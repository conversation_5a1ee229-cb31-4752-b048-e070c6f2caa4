

CREATE TABLE country( country_name  VARCHAR(30), home_node    VARCHAR(30), development FLOAT, PRIMARY KEY (country_name));

CREATE TABLE trade_node(    trade_node    VARCHAR(30), local_value FLOAT,    is_inland BOOLEAN,    total_power FLOAT,    outgoing FLOAT,    ingoing FLOAT,   PRIMARY KEY (trade_node));

CREATE TABLE flow(    source    VARCHAR(30),    dest VARCHAR(30),    flow FLOAT,   PRIMARY KEY (source, dest));

CREATE TABLE node_country(    trade_node     VARCHAR(30), country_name    VARCHAR(30),    is_home BOOLEAN, has_merchant BOOLEAN,    base_trading_power FLOAT,    calculated_trading_power FLOAT,   PRIMARY KEY (trade_node, country_name));
    
INSERT INTO country(country_name,home_node,development) VALUES ("SWE", "baltic_sea", 314.924);
INSERT INTO country(country_name,home_node,development) VALUES ("<PERSON>AN", "lubeck", 166.977);
INSERT INTO country(country_name,home_node,development) VALUES ("SHL", "lubeck", 22.784);
INSERT INTO country(country_name,home_node,development) VALUES ("KNI", "genua", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MOL", "crimea", 27.83);
INSERT INTO country(country_name,home_node,development) VALUES ("MON", "ragusa", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RAG", "ragusa", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WAL", "pest", 45.67);
INSERT INTO country(country_name,home_node,development) VALUES ("TUR", "constantinople", 1185.917);
INSERT INTO country(country_name,home_node,development) VALUES ("ENG", "english_channel", 453.009);
INSERT INTO country(country_name,home_node,development) VALUES ("SCO", "north_sea", 89.362);
INSERT INTO country(country_name,home_node,development) VALUES ("PRU", "saxony", 182.62);
INSERT INTO country(country_name,home_node,development) VALUES ("KUR", "baltic_sea", 12.307);
INSERT INTO country(country_name,home_node,development) VALUES ("PLC", "krakow", 437.677);
INSERT INTO country(country_name,home_node,development) VALUES ("FRA", "champagne", 797.677);
INSERT INTO country(country_name,home_node,development) VALUES ("AAC", "rheinland", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANH", "saxony", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANS", "rheinland", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AUG", "wien", 17.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BAD", "rheinland", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BAV", "wien", 87.806);
INSERT INTO country(country_name,home_node,development) VALUES ("BRE", "lubeck", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BRU", "saxony", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("EFR", "english_channel", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("FRN", "rheinland", 24.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HAB", "wien", 535.967);
INSERT INTO country(country_name,home_node,development) VALUES ("HAM", "lubeck", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HAN", "saxony", 25.134);
INSERT INTO country(country_name,home_node,development) VALUES ("HES", "rheinland", 42.69);
INSERT INTO country(country_name,home_node,development) VALUES ("KOL", "rheinland", 23.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LOR", "champagne", 24.7);
INSERT INTO country(country_name,home_node,development) VALUES ("LUN", "lubeck", 19.9);
INSERT INTO country(country_name,home_node,development) VALUES ("MAI", "rheinland", 16.664);
INSERT INTO country(country_name,home_node,development) VALUES ("MKL", "lubeck", 17.92);
INSERT INTO country(country_name,home_node,development) VALUES ("MUN", "rheinland", 36.162);
INSERT INTO country(country_name,home_node,development) VALUES ("PAL", "rheinland", 56.417);
INSERT INTO country(country_name,home_node,development) VALUES ("SAX", "saxony", 58.8);
INSERT INTO country(country_name,home_node,development) VALUES ("SLZ", "wien", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SWI", "champagne", 69.01);
INSERT INTO country(country_name,home_node,development) VALUES ("THU", "saxony", 25.206);
INSERT INTO country(country_name,home_node,development) VALUES ("TRI", "rheinland", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ULM", "wien", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WBG", "rheinland", 15.51);
INSERT INTO country(country_name,home_node,development) VALUES ("WUR", "wien", 18.856);
INSERT INTO country(country_name,home_node,development) VALUES ("NUM", "rheinland", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MEM", "wien", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NSA", "rheinland", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RVA", "rheinland", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("POR", "sevilla", 212.084);
INSERT INTO country(country_name,home_node,development) VALUES ("SPA", "sevilla", 811.636);
INSERT INTO country(country_name,home_node,development) VALUES ("GEN", "genua", 45.056);
INSERT INTO country(country_name,home_node,development) VALUES ("MAN", "venice", 29.572);
INSERT INTO country(country_name,home_node,development) VALUES ("MOD", "venice", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PAP", "genua", 116.898);
INSERT INTO country(country_name,home_node,development) VALUES ("PAR", "venice", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SAV", "genua", 69.503);
INSERT INTO country(country_name,home_node,development) VALUES ("TUS", "genua", 77.212);
INSERT INTO country(country_name,home_node,development) VALUES ("VEN", "venice", 149.347);
INSERT INTO country(country_name,home_node,development) VALUES ("LUC", "genua", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LIE", "champagne", 26.424);
INSERT INTO country(country_name,home_node,development) VALUES ("NED", "english_channel", 376.545);
INSERT INTO country(country_name,home_node,development) VALUES ("CRI", "crimea", 56.298);
INSERT INTO country(country_name,home_node,development) VALUES ("GEO", "crimea", 20.986);
INSERT INTO country(country_name,home_node,development) VALUES ("RUS", "novgorod", 1165.369);
INSERT INTO country(country_name,home_node,development) VALUES ("ZAZ", "crimea", 8.19);
INSERT INTO country(country_name,home_node,development) VALUES ("ADE", "gulf_of_aden", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANZ", "basra", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ARD", "basra", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DAW", "basra", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HDR", "gulf_of_aden", 14.92);
INSERT INTO country(country_name,home_node,development) VALUES ("HED", "alexandria", 48.715);
INSERT INTO country(country_name,home_node,development) VALUES ("MHR", "gulf_of_aden", 9.784);
INSERT INTO country(country_name,home_node,development) VALUES ("NAJ", "basra", 8.306);
INSERT INTO country(country_name,home_node,development) VALUES ("NJR", "gulf_of_aden", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OMA", "hormuz", 68.139);
INSERT INTO country(country_name,home_node,development) VALUES ("RAS", "gulf_of_aden", 82.482);
INSERT INTO country(country_name,home_node,development) VALUES ("SHM", "basra", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AVR", "astrakhan", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MSY", "basra", 27.452);
INSERT INTO country(country_name,home_node,development) VALUES ("ALG", "safi", 58.258);
INSERT INTO country(country_name,home_node,development) VALUES ("MOR", "safi", 163.975);
INSERT INTO country(country_name,home_node,development) VALUES ("TRP", "tunis", 13.088);
INSERT INTO country(country_name,home_node,development) VALUES ("TUN", "tunis", 67.558);
INSERT INTO country(country_name,home_node,development) VALUES ("KBA", "tunis", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TGT", "tunis", 5.706);
INSERT INTO country(country_name,home_node,development) VALUES ("GHD", "tunis", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("FZA", "tunis", 7.512);
INSERT INTO country(country_name,home_node,development) VALUES ("MZB", "tunis", 5.703);
INSERT INTO country(country_name,home_node,development) VALUES ("KZH", "samarkand", 157.854);
INSERT INTO country(country_name,home_node,development) VALUES ("KHI", "samarkand", 59.658);
INSERT INTO country(country_name,home_node,development) VALUES ("BUK", "samarkand", 116.286);
INSERT INTO country(country_name,home_node,development) VALUES ("PER", "persia", 506.504);
INSERT INTO country(country_name,home_node,development) VALUES ("CIR", "crimea", 12.94);
INSERT INTO country(country_name,home_node,development) VALUES ("GAZ", "astrakhan", 38.588);
INSERT INTO country(country_name,home_node,development) VALUES ("IME", "crimea", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MGR", "persia", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHE", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ASH", "timbuktu", 13.95);
INSERT INTO country(country_name,home_node,development) VALUES ("BEN", "ivory_coast", 23.87);
INSERT INTO country(country_name,home_node,development) VALUES ("ETH", "ethiopia", 91.81);
INSERT INTO country(country_name,home_node,development) VALUES ("KON", "kongo", 34.598);
INSERT INTO country(country_name,home_node,development) VALUES ("MAL", "timbuktu", 9.76);
INSERT INTO country(country_name,home_node,development) VALUES ("NUB", "ethiopia", 46.728);
INSERT INTO country(country_name,home_node,development) VALUES ("SON", "timbuktu", 23.76);
INSERT INTO country(country_name,home_node,development) VALUES ("ZAN", "zanzibar", 27.33);
INSERT INTO country(country_name,home_node,development) VALUES ("ZIM", "zambezi", 9.95);
INSERT INTO country(country_name,home_node,development) VALUES ("HAU", "katsina", 20.498);
INSERT INTO country(country_name,home_node,development) VALUES ("KBO", "katsina", 59.548);
INSERT INTO country(country_name,home_node,development) VALUES ("LOA", "ivory_coast", 18.092);
INSERT INTO country(country_name,home_node,development) VALUES ("OYO", "katsina", 28.652);
INSERT INTO country(country_name,home_node,development) VALUES ("SOF", "timbuktu", 66.874);
INSERT INTO country(country_name,home_node,development) VALUES ("JOL", "ivory_coast", 20.376);
INSERT INTO country(country_name,home_node,development) VALUES ("MLI", "zanzibar", 14.55);
INSERT INTO country(country_name,home_node,development) VALUES ("AJU", "gulf_of_aden", 24.726);
INSERT INTO country(country_name,home_node,development) VALUES ("MDI", "gulf_of_aden", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ENA", "ethiopia", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WGD", "timbuktu", 18.9);
INSERT INTO country(country_name,home_node,development) VALUES ("GUR", "timbuktu", 5.856);
INSERT INTO country(country_name,home_node,development) VALUES ("OGD", "ethiopia", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RZI", "zambezi", 34.31);
INSERT INTO country(country_name,home_node,development) VALUES ("WAD", "ethiopia", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AFA", "gulf_of_aden", 10.952);
INSERT INTO country(country_name,home_node,development) VALUES ("DAR", "ethiopia", 11.91);
INSERT INTO country(country_name,home_node,development) VALUES ("HAR", "gulf_of_aden", 18.97);
INSERT INTO country(country_name,home_node,development) VALUES ("HOB", "gulf_of_aden", 12.76);
INSERT INTO country(country_name,home_node,development) VALUES ("KAF", "ethiopia", 13.106);
INSERT INTO country(country_name,home_node,development) VALUES ("MED", "ethiopia", 16.89);
INSERT INTO country(country_name,home_node,development) VALUES ("MJE", "gulf_of_aden", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MRE", "ethiopia", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PTE", "zanzibar", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WAR", "gulf_of_aden", 38.578);
INSERT INTO country(country_name,home_node,development) VALUES ("WLY", "ethiopia", 13.616);
INSERT INTO country(country_name,home_node,development) VALUES ("JJI", "ethiopia", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ABB", "ethiopia", 10.166);
INSERT INTO country(country_name,home_node,development) VALUES ("SYO", "ivory_coast", 17.594);
INSERT INTO country(country_name,home_node,development) VALUES ("KSJ", "kongo", 8.97);
INSERT INTO country(country_name,home_node,development) VALUES ("LUB", "kongo", 29.79);
INSERT INTO country(country_name,home_node,development) VALUES ("LND", "kongo", 55.736);
INSERT INTO country(country_name,home_node,development) VALUES ("KZB", "kongo", 20.87);
INSERT INTO country(country_name,home_node,development) VALUES ("YAK", "kongo", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KUB", "kongo", 9.96);
INSERT INTO country(country_name,home_node,development) VALUES ("RWA", "african_great_lakes", 17.93);
INSERT INTO country(country_name,home_node,development) VALUES ("BUU", "african_great_lakes", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BUG", "african_great_lakes", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NKO", "african_great_lakes", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KRW", "african_great_lakes", 13.712);
INSERT INTO country(country_name,home_node,development) VALUES ("BNY", "african_great_lakes", 13.908);
INSERT INTO country(country_name,home_node,development) VALUES ("BSG", "african_great_lakes", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("UBH", "african_great_lakes", 19.86);
INSERT INTO country(country_name,home_node,development) VALUES ("MRA", "zambezi", 20.362);
INSERT INTO country(country_name,home_node,development) VALUES ("LDU", "zambezi", 11.4);
INSERT INTO country(country_name,home_node,development) VALUES ("TBK", "zambezi", 10.95);
INSERT INTO country(country_name,home_node,development) VALUES ("MKU", "zambezi", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MIR", "zanzibar", 11.01);
INSERT INTO country(country_name,home_node,development) VALUES ("SKA", "zanzibar", 21.975);
INSERT INTO country(country_name,home_node,development) VALUES ("BTS", "zanzibar", 14.174);
INSERT INTO country(country_name,home_node,development) VALUES ("MFY", "zanzibar", 7.743);
INSERT INTO country(country_name,home_node,development) VALUES ("ANT", "zanzibar", 10.95);
INSERT INTO country(country_name,home_node,development) VALUES ("ANN", "canton", 77.43);
INSERT INTO country(country_name,home_node,development) VALUES ("ARK", "ganges_delta", 19.52);
INSERT INTO country(country_name,home_node,development) VALUES ("ATJ", "malacca", 71.787);
INSERT INTO country(country_name,home_node,development) VALUES ("AYU", "gulf_of_siam", 170.594);
INSERT INTO country(country_name,home_node,development) VALUES ("BLI", "the_moluccas", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BAN", "the_moluccas", 28.066);
INSERT INTO country(country_name,home_node,development) VALUES ("BEI", "malacca", 74.616);
INSERT INTO country(country_name,home_node,development) VALUES ("CHA", "gulf_of_siam", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DAI", "canton", 19.594);
INSERT INTO country(country_name,home_node,development) VALUES ("JAP", "nippon", 266.911);
INSERT INTO country(country_name,home_node,development) VALUES ("DTE", "nippon", 15.307);
INSERT INTO country(country_name,home_node,development) VALUES ("HSK", "nippon", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("IKE", "nippon", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAE", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MRI", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SMZ", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("UES", "nippon", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RFR", "nippon", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ITO", "nippon", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SHN", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("STK", "nippon", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KHM", "gulf_of_siam", 51.638);
INSERT INTO country(country_name,home_node,development) VALUES ("KOR", "nippon", 126.698);
INSERT INTO country(country_name,home_node,development) VALUES ("LNA", "gulf_of_siam", 54.278);
INSERT INTO country(country_name,home_node,development) VALUES ("LXA", "gulf_of_siam", 138.432);
INSERT INTO country(country_name,home_node,development) VALUES ("MKS", "the_moluccas", 22.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MTR", "the_moluccas", 87.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PAT", "malacca", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("QNG", "beijing", 1532.695);
INSERT INTO country(country_name,home_node,development) VALUES ("RYU", "nippon", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SUK", "gulf_of_siam", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SUL", "philippines", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAU", "burma", 238.238);
INSERT INTO country(country_name,home_node,development) VALUES ("TOK", "canton", 76.472);
INSERT INTO country(country_name,home_node,development) VALUES ("ZUN", "yumen", 129.476);
INSERT INTO country(country_name,home_node,development) VALUES ("KSD", "lhasa", 67.996);
INSERT INTO country(country_name,home_node,development) VALUES ("KAM", "chengdu", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BAL", "lahore", 31.278);
INSERT INTO country(country_name,home_node,development) VALUES ("MAR", "deccan", 60.435);
INSERT INTO country(country_name,home_node,development) VALUES ("MUG", "deccan", 1089.249);
INSERT INTO country(country_name,home_node,development) VALUES ("MYS", "comorin_cape", 59.086);
INSERT INTO country(country_name,home_node,development) VALUES ("ASS", "burma", 43.596);
INSERT INTO country(country_name,home_node,development) VALUES ("GUJ", "gujarat", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAD", "comorin_cape", 26.84);
INSERT INTO country(country_name,home_node,development) VALUES ("MER", "gujarat", 24.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JAN", "gujarat", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GDW", "deccan", 13.842);
INSERT INTO country(country_name,home_node,development) VALUES ("GRJ", "ganges_delta", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DHU", "doab", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KLN", "comorin_cape", 19.292);
INSERT INTO country(country_name,home_node,development) VALUES ("MAB", "comorin_cape", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BST", "ganges_delta", 6.97);
INSERT INTO country(country_name,home_node,development) VALUES ("BHU", "lhasa", 6.476);
INSERT INTO country(country_name,home_node,development) VALUES ("BND", "doab", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JSL", "gujarat", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAC", "burma", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KMT", "ganges_delta", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLB", "burma", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HAD", "doab", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LDK", "lahore", 8.712);
INSERT INTO country(country_name,home_node,development) VALUES ("BGL", "doab", 9.95);
INSERT INTO country(country_name,home_node,development) VALUES ("GHR", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHD", "deccan", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JAJ", "gujarat", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TPR", "burma", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SKK", "lhasa", 6.968);
INSERT INTO country(country_name,home_node,development) VALUES ("RJK", "gujarat", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PAN", "doab", 9.905);
INSERT INTO country(country_name,home_node,development) VALUES ("SBP", "ganges_delta", 11.924);
INSERT INTO country(country_name,home_node,development) VALUES ("PTT", "ganges_delta", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RTT", "ganges_delta", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KLH", "ganges_delta", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KJH", "ganges_delta", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PRD", "ganges_delta", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JPR", "ganges_delta", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SRG", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KND", "comorin_cape", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DNG", "doab", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DTI", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GRK", "lhasa", 12.95);
INSERT INTO country(country_name,home_node,development) VALUES ("JML", "doab", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MKP", "ganges_delta", 11.652);
INSERT INTO country(country_name,home_node,development) VALUES ("SRM", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KTU", "lhasa", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KMN", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HSA", "lubeck", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ABE", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("APA", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BLA", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CAD", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHI", "mississippi_river", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHO", "mississippi_river", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COM", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("FOX", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LEN", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAH", "st_lawrence", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MIK", "st_lawrence", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MMI", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NAH", "rio_grande", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OJI", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OSA", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OTT", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PAW", "mississippi_river", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PIM", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("POT", "ohio", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SHO", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SIO", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WCR", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AIR", "katsina", 23.88);
INSERT INTO country(country_name,home_node,development) VALUES ("BON", "timbuktu", 11.95);
INSERT INTO country(country_name,home_node,development) VALUES ("DAH", "timbuktu", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DGB", "timbuktu", 5.97);
INSERT INTO country(country_name,home_node,development) VALUES ("FUL", "timbuktu", 44.702);
INSERT INTO country(country_name,home_node,development) VALUES ("JNN", "timbuktu", 23.77);
INSERT INTO country(country_name,home_node,development) VALUES ("KAN", "katsina", 24.52);
INSERT INTO country(country_name,home_node,development) VALUES ("KBU", "ivory_coast", 10.106);
INSERT INTO country(country_name,home_node,development) VALUES ("KNG", "timbuktu", 13.616);
INSERT INTO country(country_name,home_node,development) VALUES ("KTS", "katsina", 21.93);
INSERT INTO country(country_name,home_node,development) VALUES ("NUP", "katsina", 5.826);
INSERT INTO country(country_name,home_node,development) VALUES ("TMB", "timbuktu", 45.766);
INSERT INTO country(country_name,home_node,development) VALUES ("YAO", "katsina", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YAT", "timbuktu", 16.622);
INSERT INTO country(country_name,home_node,development) VALUES ("ZZZ", "katsina", 11.97);
INSERT INTO country(country_name,home_node,development) VALUES ("NDO", "kongo", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JOH", "malacca", 49.756);
INSERT INTO country(country_name,home_node,development) VALUES ("KED", "malacca", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PRK", "malacca", 19.92);
INSERT INTO country(country_name,home_node,development) VALUES ("CHU", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HOD", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHV", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KMC", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ARP", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CNK", "california", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HDA", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KIO", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SAL", "california", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WIC", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BLM", "the_moluccas", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BTN", "the_moluccas", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CRB", "the_moluccas", 28.136);
INSERT INTO country(country_name,home_node,development) VALUES ("PGR", "malacca", 19.812);
INSERT INTO country(country_name,home_node,development) VALUES ("PLB", "malacca", 19.93);
INSERT INTO country(country_name,home_node,development) VALUES ("SAK", "malacca", 26.86);
INSERT INTO country(country_name,home_node,development) VALUES ("KUT", "malacca", 28.232);
INSERT INTO country(country_name,home_node,development) VALUES ("BNJ", "malacca", 30.04);
INSERT INTO country(country_name,home_node,development) VALUES ("LFA", "malacca", 33.82);
INSERT INTO country(country_name,home_node,development) VALUES ("LUW", "the_moluccas", 17.91);
INSERT INTO country(country_name,home_node,development) VALUES ("MGD", "philippines", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TER", "the_moluccas", 35.55);
INSERT INTO country(country_name,home_node,development) VALUES ("TID", "the_moluccas", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GUA", "laplata", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ZNI", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MSC", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LIP", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MIS", "panama", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YAQ", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YKT", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PSS", "wien", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ROT", "rheinland", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BYT", "rheinland", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("REG", "wien", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TTL", "rheinland", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GOS", "saxony", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TNT", "wien", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLH", "rheinland", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BAM", "rheinland", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BNE", "the_moluccas", 16.216);
INSERT INTO country(country_name,home_node,development) VALUES ("BEU", "malacca", 17.713);
INSERT INTO country(country_name,home_node,development) VALUES ("BRS", "malacca", 16.886);
INSERT INTO country(country_name,home_node,development) VALUES ("JMB", "malacca", 11.97);
INSERT INTO country(country_name,home_node,development) VALUES ("IND", "malacca", 11.94);
INSERT INTO country(country_name,home_node,development) VALUES ("TIW", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LAR", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YOL", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YNU", "australia", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AWN", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GMI", "australia", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MIA", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("EOR", "australia", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAU", "australia", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PLW", "australia", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WRU", "australia", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NOO", "australia", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLG", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAA", "polynesia_node", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAN", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAK", "polynesia_node", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TNK", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TEA", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TTT", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WAI", "polynesia_node", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HAW", "polynesia_node", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAU", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OAH", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAA", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TOG", "polynesia_node", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SAM", "polynesia_node", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("VIL", "polynesia_node", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("VNL", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LAI", "polynesia_node", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ALT", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ICH", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COF", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JOA", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ABI", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COW", "mississippi_river", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NTZ", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PCH", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("QUI", "mississippi_river", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CCA", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KSI", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OEO", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NTC", "mississippi_river", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HNI", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MOH", "ohio", 17.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ONE", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ONO", "ohio", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CAY", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SEN", "ohio", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WEN", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TSC", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KSK", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PEN", "st_lawrence", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLS", "st_lawrence", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NEH", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NAK", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HWK", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CLG", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KSP", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MSG", "ohio", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WCY", "mississippi_river", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LAK", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("INN", "st_lawrence", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AGQ", "st_lawrence", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("C00", "james_bay", 24.0);
INSERT INTO country(country_name,home_node,development) VALUES ("C01", "chesapeake_bay", 145.696);
INSERT INTO country(country_name,home_node,development) VALUES ("C02", "st_lawrence", 141.194);
INSERT INTO country(country_name,home_node,development) VALUES ("C03", "mississippi_river", 46.772);
INSERT INTO country(country_name,home_node,development) VALUES ("C04", "carribean_trade", 69.81);
INSERT INTO country(country_name,home_node,development) VALUES ("C05", "brazil", 240.149);
INSERT INTO country(country_name,home_node,development) VALUES ("C06", "chesapeake_bay", 31.0);
INSERT INTO country(country_name,home_node,development) VALUES ("C07", "california", 14.88);
INSERT INTO country(country_name,home_node,development) VALUES ("C08", "mexico", 508.256);
INSERT INTO country(country_name,home_node,development) VALUES ("C09", "carribean_trade", 94.612);
INSERT INTO country(country_name,home_node,development) VALUES ("C10", "lima", 323.32);
INSERT INTO country(country_name,home_node,development) VALUES ("C11", "lima", 218.523);
INSERT INTO country(country_name,home_node,development) VALUES ("C12", "cuiaba", 133.084);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("african_great_lakes", 1.923, TRUE, 758.204, 0.6346094784517095, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("kongo", 3.349, TRUE, 790.712, 0.5766068664968647, 0.3331699761871475);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("zambezi", 2.161, TRUE, 560.83, 0.4658366788213039, 0.30271860491085395);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("patagonia", 0.255, FALSE, 224.888, 0.1275, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("amazonas_node", 2.878, FALSE, 544.622, 1.4390000000000003, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("rio_grande", 2.889, FALSE, 491.27599999999984, 1.1426309141908015, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("james_bay", 1.958, FALSE, 369.3, 0.3035668778770647, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("california", 1.984, FALSE, 617.234, 0.5663213504598946, 0.5592934308522395);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("girin", 6.628, FALSE, 948.1360000000001, 2.7590698809431555, 0.14865935449572235);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("mississippi_river", 4.947, FALSE, 799.928, 1.0862236173806792, 0.5485801744625028);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ohio", 7.458, TRUE, 1241.2079999999999, 2.5936725008577723, 0.5702673991248566);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("mexico", 14.124, FALSE, 917.488, 2.0622801050019035, 0.5485801744625028);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lhasa", 1.626, TRUE, 771.254, 0.49408006960093553, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("chengdu", 2.694, TRUE, 1125.5160000000003, 1.3772292096595389, 0.17292802436032745);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("burma", 6.001, FALSE, 850.3180000000003, 1.5648451770138032, 0.4820302233808386);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("gulf_of_siam", 9.257, FALSE, 1166.5800000000004, 2.1085735847666967, 0.8215437179322467);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("canton", 6.793, FALSE, 990.8620000000001, 2.8490525101426645, 1.5890313553833544);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("philippines", 3.509, FALSE, 525.6219999999998, 1.539097291712129, 0.9971683785499326);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("cuiaba", 3.109, FALSE, 668.688, 1.3113745153569378, 0.6375250000000001);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lima", 3.6, FALSE, 659.1279999999999, 0.30040480007350584, 0.45898108037492824);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("polynesia_node", 2.43, FALSE, 1148.3640000000003, 0.23060990769834827, 1.836195989433847);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("australia", 2.456, FALSE, 614.586, 0.0, 0.0807134676944219);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("nippon", 9.075, FALSE, 1174.91, 0.32440887090775594, 1.0463879260245266);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("hangzhou", 10.989, FALSE, 965.5000000000003, 6.163398846501536, 1.3377976930030764);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("xian", 5.079, TRUE, 737.462, 3.859109909828188, 2.6392198196563763);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("beijing", 5.313, FALSE, 403.116, 0.25231089878381063, 5.14889675726544);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("the_moluccas", 13.345, FALSE, 1214.438, 2.0078744374190753, 0.8080260781488678);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("siberia", 4.377, TRUE, 419.63399999999996, 2.671337229165052, 0.9656744583301046);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("yumen", 3.149, TRUE, 781.4060000000001, 1.6561116262935847, 2.2909591463827996);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("malacca", 12.531, FALSE, 1619.9700000000005, 1.1354003730947326, 6.369627266118014);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ganges_delta", 12.328, FALSE, 991.0439999999998, 4.628086888233043, 1.5905569381673088);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("doab", 11.791, TRUE, 1390.356, 5.553896492088549, 2.429745616322348);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lahore", 7.376, TRUE, 953.3719999999998, 4.42067980940248, 3.088723682706816);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("deccan", 11.8, TRUE, 1093.0219999999997, 2.123999918442997, 2.9157956583464886);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("comorin_cape", 14.287, FALSE, 1079.25, 5.408423133046053, 3.5448455735049214);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("gujarat", 8.659, FALSE, 988.2710000000003, 4.643587952099204, 4.555285987039561);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("katsina", 4.417, TRUE, 1463.7109999999998, 1.3011327420508558, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ethiopia", 3.253, TRUE, 964.438, 0.70532505748987, 0.4553964597177996);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("gulf_of_aden", 5.617, FALSE, 1455.5239999999997, 1.1025479277515031, 3.888499534983022);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("hormuz", 3.748, FALSE, 346.38199999999995, 1.6381502857953352, 2.0111475579477474);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("zanzibar", 5.944, FALSE, 793.152, 0.6173578395719997, 2.833446046897264);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("cape_of_good_hope", 0.4, FALSE, 78.642, 1.768629511995727, 3.137259023991453);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("basra", 4.036, FALSE, 589.306, 2.0777467312976663, 1.720057800085102);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("samarkand", 5.381, TRUE, 1188.31, 1.5399683445879209, 4.688607186210785);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("persia", 9.427, TRUE, 974.7040000000001, 1.6305226301439122, 3.446538348130802);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("aleppo", 5.981, FALSE, 600.552, 3.9639207073784144, 1.946841414756829);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("alexandria", 7.61, FALSE, 1066.2560000000003, 4.428411929506506, 2.8372458012688755);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("astrakhan", 3.789, TRUE, 450.772, 1.8982603962866074, 1.6645077617342126);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("crimea", 5.369, FALSE, 808.3360000000002, 2.1719835191645545, 0.9965867080504689);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("constantinople", 7.76, FALSE, 655.882, 1.067126303872239, 4.391196778408538);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("kiev", 5.433, TRUE, 902.6939999999998, 3.0965971158537977, 0.7601942317075941);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("kazan", 3.204, TRUE, 539.85, 2.8015193766810604, 2.399038753362121);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("novgorod", 8.418, FALSE, 741.43, 1.9970190570834148, 4.567308831338357);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("laplata", 1.673, FALSE, 244.504, 1.0560477353957618, 0.5928560803749283);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("brazil", 7.538, FALSE, 534.712, 0.4114354318791457, 2.0714812025404785);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("timbuktu", 5.806, TRUE, 1302.7019999999998, 1.0142466172158109, 0.4553964597177996);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ivory_coast", 3.116, FALSE, 1244.81, 1.986552354937784, 3.1242662700177712);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("tunis", 3.008, FALSE, 863.04, 0.8840401329030095, 0.4553964597177996);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ragusa", 6.09, FALSE, 1012.7960000000002, 2.9027290731902244, 1.120482619065851);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("safi", 4.14, FALSE, 518.116, 0.7274269455775877, 0.5324794740383008);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("pest", 4.361, TRUE, 1163.324, 2.7709451884857397, 1.7761494073241728);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("krakow", 7.324, TRUE, 1835.63, 3.882507265627556, 3.080459709778257);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("wien", 9.087, TRUE, 2059.584, 3.621068527858107, 2.8136237669246578);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("saxony", 9.551, TRUE, 1670.1520000000003, 3.368096217723976, 2.6262515277199823);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("baltic_sea", 7.814, FALSE, 558.55, 2.75694537984006, 2.407312547938437);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("rheinland", 12.04, TRUE, 1734.3760000000002, 2.9858824426162642, 3.035624499055425);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("panama", 5.146, FALSE, 441.082, 2.6803416559197224, 0.9602240244836787);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("carribean_trade", 12.711, FALSE, 1193.2459999999999, 4.47903564319502, 5.131544167762399);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("chesapeake_bay", 7.412, FALSE, 994.972, 1.7860153606137963, 2.9293405380685877);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("st_lawrence", 5.933, FALSE, 716.776, 0.9834011725825923, 2.4587087381580326);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("white_sea", 1.9, FALSE, 155.43, 1.4742175024843962, 1.0484350049687927);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("north_sea", 5.24, FALSE, 757.878, 2.9250279576826443, 2.064213993214477);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lubeck", 7.383, FALSE, 1601.8359999999998, 1.284197681921703, 7.766271123294077);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("bordeaux", 6.834, FALSE, 702.946, 4.719709041947642, 2.605418083895286);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("sevilla", 9.688, FALSE, 879.41, 0.3544690921689127, 3.162344807661946);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("champagne", 13.422, TRUE, 1923.838, 5.3018071994797324, 6.523282776418562);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("valencia", 4.972, FALSE, 365.89000000000004, 2.8268032966467063, 0.6816065932934117);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("genua", 12.898, FALSE, 1174.162, 1.8095291669986122, 8.626905638665809);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("venice", 9.412, FALSE, 597.3870000000001, 1.475481061138661, 3.8332733356941935);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("english_channel", 17.084, FALSE, 873.4599999999999, 2.0783839276752585, 7.126624081021448);
INSERT INTO flow(source, dest, flow) VALUES ("african_great_lakes", "zanzibar", 0.3173047392258547);
INSERT INTO flow(source, dest, flow) VALUES ("african_great_lakes", "kongo", 0.3173047392258547);
INSERT INTO flow(source, dest, flow) VALUES ("kongo", "ivory_coast", 0.28830343324843233);
INSERT INTO flow(source, dest, flow) VALUES ("kongo", "zambezi", 0.28830343324843233);
INSERT INTO flow(source, dest, flow) VALUES ("zambezi", "zanzibar", 0.4658366788213039);
INSERT INTO flow(source, dest, flow) VALUES ("patagonia", "laplata", 0.06375);
INSERT INTO flow(source, dest, flow) VALUES ("patagonia", "cuiaba", 0.06375);
INSERT INTO flow(source, dest, flow) VALUES ("amazonas_node", "brazil", 0.47966666666666674);
INSERT INTO flow(source, dest, flow) VALUES ("amazonas_node", "carribean_trade", 0.47966666666666674);
INSERT INTO flow(source, dest, flow) VALUES ("amazonas_node", "cuiaba", 0.47966666666666674);
INSERT INTO flow(source, dest, flow) VALUES ("rio_grande", "mississippi_river", 0.3808769713969338);
INSERT INTO flow(source, dest, flow) VALUES ("rio_grande", "mexico", 0.3808769713969338);
INSERT INTO flow(source, dest, flow) VALUES ("rio_grande", "california", 0.3808769713969338);
INSERT INTO flow(source, dest, flow) VALUES ("james_bay", "st_lawrence", 0.15178343893853236);
INSERT INTO flow(source, dest, flow) VALUES ("james_bay", "california", 0.15178343893853236);
INSERT INTO flow(source, dest, flow) VALUES ("california", "mexico", 0.14158033761497366);
INSERT INTO flow(source, dest, flow) VALUES ("california", "mississippi_river", 0.14158033761497366);
INSERT INTO flow(source, dest, flow) VALUES ("california", "girin", 0.14158033761497366);
INSERT INTO flow(source, dest, flow) VALUES ("california", "polynesia_node", 0.14158033761497366);
INSERT INTO flow(source, dest, flow) VALUES ("girin", "siberia", 0.9196899603143852);
INSERT INTO flow(source, dest, flow) VALUES ("girin", "beijing", 0.9196899603143852);
INSERT INTO flow(source, dest, flow) VALUES ("girin", "nippon", 0.9196899603143852);
INSERT INTO flow(source, dest, flow) VALUES ("mississippi_river", "carribean_trade", 0.5431118086903396);
INSERT INTO flow(source, dest, flow) VALUES ("mississippi_river", "ohio", 0.5431118086903396);
INSERT INTO flow(source, dest, flow) VALUES ("ohio", "chesapeake_bay", 1.2968362504288862);
INSERT INTO flow(source, dest, flow) VALUES ("ohio", "st_lawrence", 1.2968362504288862);
INSERT INTO flow(source, dest, flow) VALUES ("mexico", "carribean_trade", 0.6874267016673011);
INSERT INTO flow(source, dest, flow) VALUES ("mexico", "panama", 0.6874267016673011);
INSERT INTO flow(source, dest, flow) VALUES ("mexico", "polynesia_node", 0.6874267016673011);
INSERT INTO flow(source, dest, flow) VALUES ("lhasa", "lahore", 0.16469335653364517);
INSERT INTO flow(source, dest, flow) VALUES ("lhasa", "chengdu", 0.16469335653364517);
INSERT INTO flow(source, dest, flow) VALUES ("lhasa", "ganges_delta", 0.16469335653364517);
INSERT INTO flow(source, dest, flow) VALUES ("chengdu", "canton", 0.4590764032198463);
INSERT INTO flow(source, dest, flow) VALUES ("chengdu", "xian", 0.4590764032198463);
INSERT INTO flow(source, dest, flow) VALUES ("chengdu", "burma", 0.4590764032198463);
INSERT INTO flow(source, dest, flow) VALUES ("burma", "ganges_delta", 0.7824225885069016);
INSERT INTO flow(source, dest, flow) VALUES ("burma", "gulf_of_siam", 0.7824225885069016);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_siam", "malacca", 1.0542867923833483);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_siam", "canton", 1.0542867923833483);
INSERT INTO flow(source, dest, flow) VALUES ("canton", "malacca", 0.9496841700475548);
INSERT INTO flow(source, dest, flow) VALUES ("canton", "hangzhou", 0.9496841700475548);
INSERT INTO flow(source, dest, flow) VALUES ("canton", "philippines", 0.9496841700475548);
INSERT INTO flow(source, dest, flow) VALUES ("philippines", "the_moluccas", 0.7695486458560645);
INSERT INTO flow(source, dest, flow) VALUES ("philippines", "polynesia_node", 0.7695486458560645);
INSERT INTO flow(source, dest, flow) VALUES ("cuiaba", "laplata", 0.4371248384523126);
INSERT INTO flow(source, dest, flow) VALUES ("cuiaba", "lima", 0.4371248384523126);
INSERT INTO flow(source, dest, flow) VALUES ("cuiaba", "brazil", 0.4371248384523126);
INSERT INTO flow(source, dest, flow) VALUES ("lima", "panama", 0.15020240003675292);
INSERT INTO flow(source, dest, flow) VALUES ("lima", "polynesia_node", 0.15020240003675292);
INSERT INTO flow(source, dest, flow) VALUES ("polynesia_node", "nippon", 0.07686996923278276);
INSERT INTO flow(source, dest, flow) VALUES ("polynesia_node", "australia", 0.07686996923278276);
INSERT INTO flow(source, dest, flow) VALUES ("polynesia_node", "panama", 0.07686996923278276);
INSERT INTO flow(source, dest, flow) VALUES ("australia", "the_moluccas", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("nippon", "hangzhou", 0.32440887090775594);
INSERT INTO flow(source, dest, flow) VALUES ("hangzhou", "xian", 2.0544662821671786);
INSERT INTO flow(source, dest, flow) VALUES ("hangzhou", "beijing", 2.0544662821671786);
INSERT INTO flow(source, dest, flow) VALUES ("hangzhou", "malacca", 2.0544662821671786);
INSERT INTO flow(source, dest, flow) VALUES ("xian", "beijing", 1.929554954914094);
INSERT INTO flow(source, dest, flow) VALUES ("xian", "yumen", 1.929554954914094);
INSERT INTO flow(source, dest, flow) VALUES ("beijing", "yumen", 0.25231089878381063);
INSERT INTO flow(source, dest, flow) VALUES ("the_moluccas", "malacca", 2.0078744374190753);
INSERT INTO flow(source, dest, flow) VALUES ("siberia", "kazan", 1.335668614582526);
INSERT INTO flow(source, dest, flow) VALUES ("siberia", "samarkand", 1.335668614582526);
INSERT INTO flow(source, dest, flow) VALUES ("yumen", "samarkand", 1.6561116262935847);
INSERT INTO flow(source, dest, flow) VALUES ("malacca", "ganges_delta", 0.5677001865473663);
INSERT INTO flow(source, dest, flow) VALUES ("malacca", "cape_of_good_hope", 0.5677001865473663);
INSERT INTO flow(source, dest, flow) VALUES ("ganges_delta", "comorin_cape", 2.3140434441165216);
INSERT INTO flow(source, dest, flow) VALUES ("ganges_delta", "doab", 2.3140434441165216);
INSERT INTO flow(source, dest, flow) VALUES ("doab", "deccan", 2.7769482460442747);
INSERT INTO flow(source, dest, flow) VALUES ("doab", "lahore", 2.7769482460442747);
INSERT INTO flow(source, dest, flow) VALUES ("lahore", "samarkand", 1.4735599364674934);
INSERT INTO flow(source, dest, flow) VALUES ("lahore", "persia", 1.4735599364674934);
INSERT INTO flow(source, dest, flow) VALUES ("lahore", "gujarat", 1.4735599364674934);
INSERT INTO flow(source, dest, flow) VALUES ("deccan", "gujarat", 1.0619999592214986);
INSERT INTO flow(source, dest, flow) VALUES ("deccan", "comorin_cape", 1.0619999592214986);
INSERT INTO flow(source, dest, flow) VALUES ("comorin_cape", "gulf_of_aden", 1.802807711015351);
INSERT INTO flow(source, dest, flow) VALUES ("comorin_cape", "gujarat", 1.802807711015351);
INSERT INTO flow(source, dest, flow) VALUES ("comorin_cape", "cape_of_good_hope", 1.802807711015351);
INSERT INTO flow(source, dest, flow) VALUES ("gujarat", "gulf_of_aden", 1.5478626506997346);
INSERT INTO flow(source, dest, flow) VALUES ("gujarat", "hormuz", 1.5478626506997346);
INSERT INTO flow(source, dest, flow) VALUES ("gujarat", "zanzibar", 1.5478626506997346);
INSERT INTO flow(source, dest, flow) VALUES ("katsina", "timbuktu", 0.43371091401695194);
INSERT INTO flow(source, dest, flow) VALUES ("katsina", "tunis", 0.43371091401695194);
INSERT INTO flow(source, dest, flow) VALUES ("katsina", "ethiopia", 0.43371091401695194);
INSERT INTO flow(source, dest, flow) VALUES ("ethiopia", "alexandria", 0.352662528744935);
INSERT INTO flow(source, dest, flow) VALUES ("ethiopia", "gulf_of_aden", 0.352662528744935);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_aden", "zanzibar", 0.3675159759171677);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_aden", "alexandria", 0.3675159759171677);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_aden", "hormuz", 0.3675159759171677);
INSERT INTO flow(source, dest, flow) VALUES ("hormuz", "basra", 1.6381502857953352);
INSERT INTO flow(source, dest, flow) VALUES ("zanzibar", "cape_of_good_hope", 0.6173578395719997);
INSERT INTO flow(source, dest, flow) VALUES ("cape_of_good_hope", "ivory_coast", 1.768629511995727);
INSERT INTO flow(source, dest, flow) VALUES ("basra", "aleppo", 1.0388733656488331);
INSERT INTO flow(source, dest, flow) VALUES ("basra", "persia", 1.0388733656488331);
INSERT INTO flow(source, dest, flow) VALUES ("samarkand", "persia", 0.7699841722939604);
INSERT INTO flow(source, dest, flow) VALUES ("samarkand", "astrakhan", 0.7699841722939604);
INSERT INTO flow(source, dest, flow) VALUES ("persia", "aleppo", 0.8152613150719561);
INSERT INTO flow(source, dest, flow) VALUES ("persia", "astrakhan", 0.8152613150719561);
INSERT INTO flow(source, dest, flow) VALUES ("aleppo", "constantinople", 1.9819603536892072);
INSERT INTO flow(source, dest, flow) VALUES ("aleppo", "alexandria", 1.9819603536892072);
INSERT INTO flow(source, dest, flow) VALUES ("alexandria", "constantinople", 1.476137309835502);
INSERT INTO flow(source, dest, flow) VALUES ("alexandria", "venice", 1.476137309835502);
INSERT INTO flow(source, dest, flow) VALUES ("alexandria", "genua", 1.476137309835502);
INSERT INTO flow(source, dest, flow) VALUES ("astrakhan", "kazan", 0.9491301981433037);
INSERT INTO flow(source, dest, flow) VALUES ("astrakhan", "crimea", 0.9491301981433037);
INSERT INTO flow(source, dest, flow) VALUES ("crimea", "kiev", 0.7239945063881849);
INSERT INTO flow(source, dest, flow) VALUES ("crimea", "constantinople", 0.7239945063881849);
INSERT INTO flow(source, dest, flow) VALUES ("crimea", "pest", 0.7239945063881849);
INSERT INTO flow(source, dest, flow) VALUES ("constantinople", "ragusa", 1.067126303872239);
INSERT INTO flow(source, dest, flow) VALUES ("kiev", "novgorod", 1.5482985579268989);
INSERT INTO flow(source, dest, flow) VALUES ("kiev", "krakow", 1.5482985579268989);
INSERT INTO flow(source, dest, flow) VALUES ("kazan", "novgorod", 2.8015193766810604);
INSERT INTO flow(source, dest, flow) VALUES ("novgorod", "baltic_sea", 0.9985095285417074);
INSERT INTO flow(source, dest, flow) VALUES ("novgorod", "white_sea", 0.9985095285417074);
INSERT INTO flow(source, dest, flow) VALUES ("laplata", "brazil", 1.0560477353957618);
INSERT INTO flow(source, dest, flow) VALUES ("brazil", "ivory_coast", 0.4114354318791457);
INSERT INTO flow(source, dest, flow) VALUES ("timbuktu", "safi", 0.5071233086079054);
INSERT INTO flow(source, dest, flow) VALUES ("timbuktu", "ivory_coast", 0.5071233086079054);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "carribean_trade", 0.496638088734446);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "bordeaux", 0.496638088734446);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "english_channel", 0.496638088734446);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "sevilla", 0.496638088734446);
INSERT INTO flow(source, dest, flow) VALUES ("tunis", "sevilla", 0.2946800443010032);
INSERT INTO flow(source, dest, flow) VALUES ("tunis", "valencia", 0.2946800443010032);
INSERT INTO flow(source, dest, flow) VALUES ("tunis", "genua", 0.2946800443010032);
INSERT INTO flow(source, dest, flow) VALUES ("ragusa", "pest", 0.9675763577300748);
INSERT INTO flow(source, dest, flow) VALUES ("ragusa", "venice", 0.9675763577300748);
INSERT INTO flow(source, dest, flow) VALUES ("ragusa", "genua", 0.9675763577300748);
INSERT INTO flow(source, dest, flow) VALUES ("safi", "sevilla", 0.7274269455775877);
INSERT INTO flow(source, dest, flow) VALUES ("pest", "wien", 1.3854725942428698);
INSERT INTO flow(source, dest, flow) VALUES ("pest", "krakow", 1.3854725942428698);
INSERT INTO flow(source, dest, flow) VALUES ("krakow", "wien", 1.2941690885425186);
INSERT INTO flow(source, dest, flow) VALUES ("krakow", "saxony", 1.2941690885425186);
INSERT INTO flow(source, dest, flow) VALUES ("krakow", "baltic_sea", 1.2941690885425186);
INSERT INTO flow(source, dest, flow) VALUES ("wien", "venice", 1.207022842619369);
INSERT INTO flow(source, dest, flow) VALUES ("wien", "rheinland", 1.207022842619369);
INSERT INTO flow(source, dest, flow) VALUES ("wien", "saxony", 1.207022842619369);
INSERT INTO flow(source, dest, flow) VALUES ("saxony", "rheinland", 1.684048108861988);
INSERT INTO flow(source, dest, flow) VALUES ("saxony", "lubeck", 1.684048108861988);
INSERT INTO flow(source, dest, flow) VALUES ("baltic_sea", "lubeck", 2.75694537984006);
INSERT INTO flow(source, dest, flow) VALUES ("rheinland", "champagne", 1.4929412213081321);
INSERT INTO flow(source, dest, flow) VALUES ("rheinland", "lubeck", 1.4929412213081321);
INSERT INTO flow(source, dest, flow) VALUES ("panama", "carribean_trade", 2.6803416559197224);
INSERT INTO flow(source, dest, flow) VALUES ("carribean_trade", "chesapeake_bay", 1.4930118810650066);
INSERT INTO flow(source, dest, flow) VALUES ("carribean_trade", "bordeaux", 1.4930118810650066);
INSERT INTO flow(source, dest, flow) VALUES ("carribean_trade", "sevilla", 1.4930118810650066);
INSERT INTO flow(source, dest, flow) VALUES ("chesapeake_bay", "st_lawrence", 0.8930076803068981);
INSERT INTO flow(source, dest, flow) VALUES ("chesapeake_bay", "english_channel", 0.8930076803068981);
INSERT INTO flow(source, dest, flow) VALUES ("st_lawrence", "north_sea", 0.49170058629129615);
INSERT INTO flow(source, dest, flow) VALUES ("st_lawrence", "bordeaux", 0.49170058629129615);
INSERT INTO flow(source, dest, flow) VALUES ("white_sea", "north_sea", 1.4742175024843962);
INSERT INTO flow(source, dest, flow) VALUES ("north_sea", "english_channel", 1.4625139788413222);
INSERT INTO flow(source, dest, flow) VALUES ("north_sea", "lubeck", 1.4625139788413222);
INSERT INTO flow(source, dest, flow) VALUES ("lubeck", "english_channel", 1.284197681921703);
INSERT INTO flow(source, dest, flow) VALUES ("bordeaux", "champagne", 4.719709041947642);
INSERT INTO flow(source, dest, flow) VALUES ("sevilla", "valencia", 0.3544690921689127);
INSERT INTO flow(source, dest, flow) VALUES ("champagne", "genua", 2.6509035997398662);
INSERT INTO flow(source, dest, flow) VALUES ("champagne", "english_channel", 2.6509035997398662);
INSERT INTO flow(source, dest, flow) VALUES ("valencia", "genua", 2.8268032966467063);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "POR", FALSE, FALSE, 8.992, 8.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "OMA", FALSE, FALSE, 4.166, 4.166);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KON", FALSE, FALSE, 45.347, 45.347);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "ZAN", FALSE, FALSE, 47.067, 47.067);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "MLI", FALSE, FALSE, 2.583, 2.583);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "RZI", FALSE, FALSE, 39.19, 39.19);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KSJ", FALSE, FALSE, 5.045, 5.045);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "LUB", FALSE, FALSE, 14.769, 14.769);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "LND", FALSE, FALSE, 54.517, 54.517);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KZB", FALSE, FALSE, 9.081, 9.081);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "YAK", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KUB", FALSE, FALSE, 5.38, 5.38);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "RWA", TRUE, FALSE, 17.475, 17.475);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BUU", TRUE, FALSE, 12.269, 12.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BUG", TRUE, FALSE, 26.112, 26.112);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "NKO", TRUE, FALSE, 11.69, 11.69);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KRW", TRUE, FALSE, 15.124, 15.124);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BNY", TRUE, FALSE, 15.194, 15.194);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BSG", TRUE, FALSE, 12.391, 12.391);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "UBH", TRUE, FALSE, 18.632, 18.632);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "SKA", FALSE, FALSE, 2.926, 2.926);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BTS", FALSE, FALSE, 2.409, 2.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "NDO", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "POR", FALSE, FALSE, 10.289, 10.289);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "BEN", FALSE, FALSE, 6.057, 6.057);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KON", TRUE, FALSE, 71.986, 71.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "ZIM", FALSE, FALSE, 5.295, 5.295);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "LOA", FALSE, FALSE, 9.586, 9.586);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "JOL", FALSE, FALSE, 4.341, 4.341);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "RZI", FALSE, FALSE, 42.7, 42.7);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "SYO", FALSE, FALSE, 11.949, 11.949);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KSJ", TRUE, FALSE, 13.485, 13.485);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "LUB", TRUE, FALSE, 33.48, 33.48);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "LND", TRUE, FALSE, 86.136, 86.136);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KZB", TRUE, FALSE, 21.115, 21.115);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "YAK", TRUE, FALSE, 12.848, 12.848);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KUB", TRUE, FALSE, 20.774, 20.774);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "MRA", FALSE, FALSE, 14.772, 14.772);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "LDU", FALSE, FALSE, 6.012, 6.012);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "TBK", FALSE, FALSE, 6.456, 6.456);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "MKU", FALSE, FALSE, 3.98, 3.98);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KBU", FALSE, FALSE, 2.384, 2.384);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "NDO", TRUE, FALSE, 11.711, 11.711);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "POR", FALSE, FALSE, 29.261, 29.261);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "OMA", FALSE, FALSE, 4.166, 4.166);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "ZAN", FALSE, FALSE, 47.067, 47.067);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "ZIM", TRUE, FALSE, 16.463, 16.463);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "MLI", FALSE, FALSE, 2.583, 2.583);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "RZI", TRUE, FALSE, 73.176, 73.176);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "MRA", TRUE, FALSE, 37.05, 37.05);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "LDU", TRUE, FALSE, 18.72, 18.72);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "TBK", TRUE, FALSE, 15.569, 15.569);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "MKU", TRUE, FALSE, 13.396, 13.396);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "MIR", FALSE, FALSE, 6.301, 6.301);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "SKA", FALSE, FALSE, 2.926, 2.926);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "BTS", FALSE, FALSE, 2.409, 2.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "MFY", FALSE, FALSE, 5.045, 5.045);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "ANT", FALSE, FALSE, 6.283, 6.283);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("patagonia", "C05", FALSE, FALSE, 23.681, 23.681);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("patagonia", "C11", FALSE, FALSE, 14.239, 14.239);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("patagonia", "C12", FALSE, FALSE, 74.524, 74.524);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "ENG", FALSE, FALSE, 5.288, 5.288);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "FRA", FALSE, FALSE, 1.126, 1.126);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "NED", FALSE, FALSE, 6.148, 6.148);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "C04", FALSE, FALSE, 34.005, 34.005);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "C05", FALSE, FALSE, 94.99, 94.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "C09", FALSE, FALSE, 43.45, 43.45);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "C10", FALSE, FALSE, 38.61, 38.61);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "C11", FALSE, FALSE, 14.239, 14.239);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "C12", FALSE, FALSE, 34.455, 34.455);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "APA", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "CAD", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "CHI", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "CHO", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "NAH", TRUE, FALSE, 8.758, 8.758);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "OSA", FALSE, FALSE, 1.99, 1.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "CNK", FALSE, FALSE, 3.628, 3.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "WIC", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "ZNI", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "MSC", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "LIP", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "YAQ", FALSE, FALSE, 2.014, 2.014);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "ABI", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "COW", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "NTZ", FALSE, FALSE, 2.048, 2.048);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "PCH", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "QUI", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "NTC", FALSE, FALSE, 2.032, 2.032);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "HNI", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "C03", FALSE, FALSE, 33.414, 33.414);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "C07", FALSE, FALSE, 2.386, 2.386);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "C08", FALSE, FALSE, 130.645, 130.645);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "BLA", TRUE, FALSE, 8.619, 8.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "WCR", TRUE, FALSE, 8.722, 8.722);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "ARP", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "CNK", FALSE, FALSE, 3.628, 3.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "KIO", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "NEH", TRUE, FALSE, 8.499, 8.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "NAK", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "INN", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "C00", TRUE, FALSE, 75.887, 75.887);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "C02", FALSE, FALSE, 51.61, 51.61);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "RUS", FALSE, FALSE, 5.611, 5.611);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "QNG", FALSE, FALSE, 27.045, 27.045);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "APA", TRUE, FALSE, 9.359, 9.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "PIM", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "SHO", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "CNK", TRUE, FALSE, 41.348, 41.348);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "HDA", TRUE, FALSE, 24.913, 24.913);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "SAL", TRUE, FALSE, 25.709, 25.709);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "YAQ", TRUE, FALSE, 27.387, 27.387);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "YKT", TRUE, FALSE, 8.499, 8.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "C03", FALSE, FALSE, 9.716, 9.716);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "C07", TRUE, FALSE, 16.809, 16.809);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "C08", FALSE, FALSE, 95.069, 95.069);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "RUS", FALSE, FALSE, 47.903, 47.903);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "JAP", FALSE, FALSE, 42.981, 42.981);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "HSK", FALSE, FALSE, 2.194, 2.194);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "IKE", FALSE, FALSE, 1.994, 1.994);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "MAE", FALSE, FALSE, 1.99, 1.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "MRI", FALSE, FALSE, 4.687, 4.687);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "SMZ", FALSE, FALSE, 1.99, 1.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "UES", FALSE, FALSE, 1.99, 1.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "ITO", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "SHN", FALSE, FALSE, 1.994, 1.994);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "STK", FALSE, FALSE, 1.99, 1.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "KOR", FALSE, FALSE, 37.07, 37.07);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "QNG", FALSE, FALSE, 235.208, 235.208);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "RYU", FALSE, FALSE, 2.014, 2.014);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "CHU", TRUE, FALSE, 15.381, 15.381);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "HOD", TRUE, FALSE, 8.628, 8.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "CHV", TRUE, FALSE, 32.016, 32.016);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "KMC", TRUE, FALSE, 32.016, 32.016);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "ENG", FALSE, FALSE, 5.288, 5.288);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "FRA", FALSE, FALSE, 1.877, 1.877);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "NED", FALSE, FALSE, 2.593, 2.593);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CAD", TRUE, FALSE, 9.359, 9.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CHI", TRUE, FALSE, 12.843, 12.843);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CHO", TRUE, FALSE, 11.223, 11.223);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "COM", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "MMI", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "OSA", TRUE, FALSE, 9.241, 9.241);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "PAW", TRUE, FALSE, 8.74, 8.74);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "ALT", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "ICH", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "ABI", TRUE, FALSE, 9.493, 9.493);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "COW", TRUE, FALSE, 10.158, 10.158);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "NTZ", TRUE, FALSE, 9.486, 9.486);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "PCH", TRUE, FALSE, 9.359, 9.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "QUI", TRUE, FALSE, 9.892, 9.892);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CCA", TRUE, FALSE, 8.397, 8.397);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "KSI", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "OEO", TRUE, FALSE, 8.517, 8.517);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "NTC", TRUE, FALSE, 10.133, 10.133);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "KSK", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "HWK", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CLG", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "KSP", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "WCY", TRUE, FALSE, 9.424, 9.424);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "LAK", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "C02", FALSE, FALSE, 13.387, 13.387);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "C03", TRUE, FALSE, 88.316, 88.316);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "C04", FALSE, FALSE, 34.005, 34.005);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "C06", FALSE, FALSE, 13.563, 13.563);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "C09", FALSE, FALSE, 43.45, 43.45);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "C10", FALSE, FALSE, 16.37, 16.37);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CHE", FALSE, FALSE, 4.502, 4.502);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "FOX", TRUE, FALSE, 12.278, 12.278);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "LEN", TRUE, FALSE, 12.475, 12.475);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "MMI", TRUE, FALSE, 11.711, 11.711);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "OJI", TRUE, FALSE, 12.475, 12.475);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "OTT", TRUE, FALSE, 10.655, 10.655);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "POT", TRUE, FALSE, 11.253, 11.253);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "SIO", TRUE, FALSE, 10.655, 10.655);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ALT", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ICH", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "COF", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "JOA", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "KSI", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "MOH", TRUE, FALSE, 17.179, 17.179);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ONE", TRUE, FALSE, 11.851, 11.851);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ONO", TRUE, FALSE, 13.647, 13.647);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CAY", TRUE, FALSE, 11.6, 11.6);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "SEN", TRUE, FALSE, 11.253, 11.253);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "WEN", TRUE, FALSE, 11.851, 11.851);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "TSC", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "KSK", TRUE, FALSE, 13.026, 13.026);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "HWK", TRUE, FALSE, 13.026, 13.026);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CLG", TRUE, FALSE, 11.711, 11.711);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "KSP", TRUE, FALSE, 11.711, 11.711);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "MSG", TRUE, FALSE, 11.253, 11.253);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "C00", FALSE, FALSE, 3.312, 3.312);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "C01", FALSE, FALSE, 164.587, 164.587);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "C02", FALSE, FALSE, 167.175, 167.175);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "C03", FALSE, FALSE, 34.518, 34.518);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "ENG", FALSE, FALSE, 10.591, 10.591);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "NED", FALSE, FALSE, 2.593, 2.593);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "C04", FALSE, FALSE, 34.005, 34.005);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "C08", TRUE, FALSE, 329.788, 329.788);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "C09", FALSE, FALSE, 43.45, 43.45);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "C10", FALSE, FALSE, 38.317, 38.317);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "ENG", FALSE, FALSE, 12.765, 12.765);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "ARK", FALSE, FALSE, 2.793, 2.793);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "QNG", FALSE, FALSE, 11.633, 11.633);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "TAU", FALSE, FALSE, 96.115, 96.115);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "KSD", TRUE, FALSE, 97.194, 97.194);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "KAM", FALSE, FALSE, 6.332, 6.332);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MUG", FALSE, FALSE, 40.201, 40.201);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "ASS", FALSE, FALSE, 41.684, 41.684);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "BST", FALSE, FALSE, 4.38, 4.38);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "BHU", TRUE, FALSE, 11.675, 11.675);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "KMT", FALSE, FALSE, 4.98, 4.98);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "SKK", TRUE, FALSE, 9.375, 9.375);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "RTT", FALSE, FALSE, 5.892, 5.892);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "GRK", TRUE, FALSE, 14.874, 14.874);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MKP", FALSE, FALSE, 7.58, 7.58);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "KTU", TRUE, FALSE, 18.154, 18.154);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "DAI", FALSE, FALSE, 3.918, 3.918);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "KHM", FALSE, FALSE, 47.25, 47.25);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "LNA", FALSE, FALSE, 20.19, 20.19);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "LXA", FALSE, FALSE, 93.665, 93.665);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "QNG", FALSE, FALSE, 136.053, 136.053);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "TAU", FALSE, FALSE, 119.409, 119.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "TOK", FALSE, FALSE, 51.833, 51.833);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "KSD", FALSE, FALSE, 11.401, 11.401);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "KAM", TRUE, FALSE, 22.077, 22.077);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "ASS", FALSE, FALSE, 44.91, 44.91);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "KAC", FALSE, FALSE, 3.98, 3.98);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "MLB", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "TPR", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "ENG", FALSE, FALSE, 12.765, 12.765);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "ANN", FALSE, FALSE, 23.697, 23.697);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "ARK", FALSE, FALSE, 4.807, 4.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "AYU", FALSE, FALSE, 27.186, 27.186);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "CHA", FALSE, FALSE, 2.186, 2.186);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "DAI", FALSE, FALSE, 1.84, 1.84);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KHM", FALSE, FALSE, 29.294, 29.294);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "LNA", FALSE, FALSE, 5.022, 5.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "LXA", FALSE, FALSE, 31.585, 31.585);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "SUK", FALSE, FALSE, 1.994, 1.994);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "TAU", TRUE, FALSE, 143.07, 143.07);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "TOK", FALSE, FALSE, 15.657, 15.657);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MUG", FALSE, FALSE, 27.173, 27.173);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "ASS", TRUE, FALSE, 50.492, 50.492);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "GRJ", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "BST", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KAC", TRUE, FALSE, 8.519, 8.519);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KMT", FALSE, FALSE, 1.992, 1.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MLB", TRUE, FALSE, 9.204, 9.204);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "TPR", TRUE, FALSE, 8.628, 8.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "SBP", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "PTT", FALSE, FALSE, 1.964, 1.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "RTT", FALSE, FALSE, 1.964, 1.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KLH", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KJH", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "PRD", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "JPR", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MKP", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "NED", FALSE, FALSE, 7.443, 7.443);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "ANN", FALSE, FALSE, 46.95, 46.95);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "ARK", FALSE, FALSE, 2.014, 2.014);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "ATJ", FALSE, FALSE, 34.898, 34.898);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "AYU", TRUE, FALSE, 91.148, 91.148);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "BEI", FALSE, FALSE, 30.243, 30.243);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "CHA", TRUE, FALSE, 15.628, 15.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "DAI", FALSE, FALSE, 5.758, 5.758);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "KHM", TRUE, FALSE, 86.163, 86.163);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "LNA", TRUE, FALSE, 26.594, 26.594);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "LXA", TRUE, FALSE, 108.876, 108.876);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "PAT", FALSE, FALSE, 4.968, 4.968);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "QNG", FALSE, FALSE, 27.085, 27.085);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "SUK", TRUE, FALSE, 10.816, 10.816);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "SUL", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "TAU", FALSE, FALSE, 13.922, 13.922);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "TOK", FALSE, FALSE, 22.1, 22.1);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "JOH", FALSE, FALSE, 20.503, 20.503);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "PGR", FALSE, FALSE, 2.222, 2.222);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "PLB", FALSE, FALSE, 5.979, 5.979);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "SAK", FALSE, FALSE, 5.513, 5.513);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "KUT", FALSE, FALSE, 2.513, 2.513);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "BNJ", FALSE, FALSE, 2.458, 2.458);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "LFA", FALSE, FALSE, 3.238, 3.238);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "BEU", FALSE, FALSE, 2.218, 2.218);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "IND", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "POR", FALSE, FALSE, 12.615, 12.615);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "SPA", FALSE, FALSE, 29.019, 29.019);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "NED", FALSE, FALSE, 7.443, 7.443);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "ANN", TRUE, FALSE, 40.101, 40.101);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "ATJ", FALSE, FALSE, 34.898, 34.898);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "BEI", FALSE, FALSE, 30.243, 30.243);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "DAI", TRUE, FALSE, 36.17, 36.17);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "MKS", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "MTR", FALSE, FALSE, 20.587, 20.587);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "PAT", FALSE, FALSE, 4.968, 4.968);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "QNG", FALSE, FALSE, 145.585, 145.585);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "SUL", FALSE, FALSE, 5.329, 5.329);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "TOK", TRUE, FALSE, 82.366, 82.366);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "JOH", FALSE, FALSE, 20.503, 20.503);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "PLB", FALSE, FALSE, 3.657, 3.657);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "SAK", FALSE, FALSE, 3.491, 3.491);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "KUT", FALSE, FALSE, 2.513, 2.513);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "BNJ", FALSE, FALSE, 2.458, 2.458);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "LFA", FALSE, FALSE, 3.238, 3.238);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "MGD", FALSE, FALSE, 3.985, 3.985);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "TER", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "BEU", FALSE, FALSE, 2.218, 2.218);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "SPA", FALSE, FALSE, 75.876, 75.876);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "NED", FALSE, FALSE, 13.742, 13.742);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BLI", FALSE, FALSE, 2.024, 2.024);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BAN", FALSE, FALSE, 5.296, 5.296);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BEI", FALSE, FALSE, 2.926, 2.926);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "JAP", FALSE, FALSE, 17.595, 17.595);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "MKS", FALSE, FALSE, 7.748, 7.748);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "MTR", FALSE, FALSE, 29.831, 29.831);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "SUL", TRUE, FALSE, 44.926, 44.926);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BLM", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BTN", FALSE, FALSE, 2.038, 2.038);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "CRB", FALSE, FALSE, 4.529, 4.529);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "LUW", FALSE, FALSE, 4.406, 4.406);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "MGD", TRUE, FALSE, 38.357, 38.357);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "TER", FALSE, FALSE, 4.43, 4.43);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "TID", FALSE, FALSE, 2.038, 2.038);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BNE", FALSE, FALSE, 5.031, 5.031);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "C04", FALSE, FALSE, 23.681, 23.681);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "C05", FALSE, FALSE, 55.536, 55.536);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "C10", FALSE, FALSE, 37.771, 37.771);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "C11", FALSE, FALSE, 117.069, 117.069);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "C12", TRUE, FALSE, 100.287, 100.287);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "SPA", FALSE, FALSE, 1.403, 1.403);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "C08", FALSE, FALSE, 23.698, 23.698);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "C09", FALSE, FALSE, 23.681, 23.681);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "C10", TRUE, FALSE, 129.022, 129.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "C11", TRUE, FALSE, 151.76, 151.76);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "SPA", FALSE, FALSE, 1.403, 1.403);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "JAP", FALSE, FALSE, 41.19, 41.19);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "MRI", FALSE, FALSE, 2.693, 2.693);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "KOR", FALSE, FALSE, 16.789, 16.789);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "MAA", TRUE, FALSE, 29.877, 29.877);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TAN", TRUE, FALSE, 30.468, 30.468);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TAK", TRUE, FALSE, 29.581, 29.581);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TNK", TRUE, FALSE, 30.468, 30.468);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TEA", TRUE, FALSE, 30.468, 30.468);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TTT", TRUE, FALSE, 30.468, 30.468);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "WAI", TRUE, FALSE, 26.147, 26.147);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "HAW", TRUE, FALSE, 34.336, 34.336);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "MAU", TRUE, FALSE, 33.751, 33.751);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "OAH", TRUE, FALSE, 41.066, 41.066);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "KAA", TRUE, FALSE, 33.476, 33.476);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TOG", TRUE, FALSE, 41.445, 41.445);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "SAM", TRUE, FALSE, 36.709, 36.709);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "VIL", TRUE, FALSE, 28.534, 28.534);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "VNL", TRUE, FALSE, 27.949, 27.949);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "LAI", TRUE, FALSE, 27.364, 27.364);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "TIW", TRUE, FALSE, 11.535, 11.535);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "LAR", TRUE, FALSE, 19.108, 19.108);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "YOL", TRUE, FALSE, 13.573, 13.573);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "YNU", TRUE, FALSE, 12.854, 12.854);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "AWN", TRUE, FALSE, 13.413, 13.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "GMI", TRUE, FALSE, 10.19, 10.19);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "MIA", TRUE, FALSE, 27.511, 27.511);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "EOR", TRUE, FALSE, 36.564, 36.564);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "KAU", TRUE, FALSE, 35.396, 35.396);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "PLW", TRUE, FALSE, 26.392, 26.392);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "WRU", TRUE, FALSE, 36.636, 36.636);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "NOO", TRUE, FALSE, 36.337, 36.337);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "MLG", TRUE, FALSE, 27.784, 27.784);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "JAP", TRUE, FALSE, 215.112, 215.112);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "DTE", TRUE, FALSE, 13.232, 13.232);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "HSK", TRUE, FALSE, 13.515, 13.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "IKE", TRUE, FALSE, 13.196, 13.196);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "MAE", TRUE, FALSE, 13.807, 13.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "MRI", TRUE, FALSE, 24.967, 24.967);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "SMZ", TRUE, FALSE, 17.092, 17.092);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "UES", TRUE, FALSE, 16.457, 16.457);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "RFR", TRUE, FALSE, 30.168, 30.168);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "ITO", TRUE, FALSE, 13.365, 13.365);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "SHN", TRUE, FALSE, 15.094, 15.094);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "STK", TRUE, FALSE, 9.57, 9.57);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "KOR", TRUE, FALSE, 134.905, 134.905);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "QNG", FALSE, FALSE, 37.658, 37.658);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "RYU", TRUE, FALSE, 19.317, 19.317);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "NED", FALSE, FALSE, 7.443, 7.443);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "ATJ", FALSE, FALSE, 34.898, 34.898);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "AYU", FALSE, FALSE, 16.439, 16.439);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "BAN", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "BEI", FALSE, FALSE, 30.243, 30.243);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "PAT", FALSE, FALSE, 4.968, 4.968);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "QNG", FALSE, FALSE, 328.175, 328.175);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "JOH", FALSE, FALSE, 20.503, 20.503);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "KED", FALSE, FALSE, 1.99, 1.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "PRK", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "PLB", FALSE, FALSE, 5.979, 5.979);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "SAK", FALSE, FALSE, 5.513, 5.513);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "KUT", FALSE, FALSE, 4.535, 4.535);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "BNJ", FALSE, FALSE, 4.48, 4.48);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "LFA", FALSE, FALSE, 5.256, 5.256);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "BEU", FALSE, FALSE, 4.24, 4.24);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "BRS", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "JMB", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("xian", "QNG", FALSE, FALSE, 266.924, 266.924);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("xian", "ZUN", FALSE, FALSE, 101.807, 101.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("beijing", "QNG", TRUE, FALSE, 191.836, 191.836);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("beijing", "ZUN", FALSE, FALSE, 9.722, 9.722);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "POR", FALSE, FALSE, 3.678, 3.678);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "NED", FALSE, FALSE, 58.404, 58.404);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "ATJ", FALSE, FALSE, 34.898, 34.898);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BLI", TRUE, FALSE, 17.558, 17.558);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BAN", TRUE, FALSE, 33.843, 33.843);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BEI", FALSE, FALSE, 30.243, 30.243);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "MKS", TRUE, FALSE, 64.57, 64.57);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "MTR", TRUE, FALSE, 100.773, 100.773);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "PAT", FALSE, FALSE, 4.968, 4.968);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "JOH", FALSE, FALSE, 20.503, 20.503);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BLM", TRUE, FALSE, 21.525, 21.525);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BTN", TRUE, FALSE, 37.464, 37.464);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "CRB", TRUE, FALSE, 31.328, 31.328);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "PLB", FALSE, FALSE, 3.657, 3.657);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "SAK", FALSE, FALSE, 5.513, 5.513);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "KUT", FALSE, FALSE, 2.513, 2.513);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BNJ", FALSE, FALSE, 2.458, 2.458);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "LFA", FALSE, FALSE, 3.238, 3.238);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "LUW", TRUE, FALSE, 41.006, 41.006);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "TER", TRUE, FALSE, 22.9, 22.9);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "TID", TRUE, FALSE, 12.924, 12.924);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BNE", TRUE, FALSE, 51.037, 51.037);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BEU", FALSE, FALSE, 2.218, 2.218);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "RUS", FALSE, FALSE, 76.207, 76.207);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "KZH", FALSE, FALSE, 110.189, 110.189);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "KHI", FALSE, FALSE, 5.134, 5.134);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "BUK", FALSE, FALSE, 9.357, 9.357);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "QNG", FALSE, FALSE, 1.987, 1.987);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "ZUN", FALSE, FALSE, 6.943, 6.943);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "KZH", FALSE, FALSE, 102.645, 102.645);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "KHI", FALSE, FALSE, 5.134, 5.134);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "BUK", FALSE, FALSE, 87.412, 87.412);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "QNG", FALSE, FALSE, 42.696, 42.696);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "ZUN", TRUE, FALSE, 152.816, 152.816);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "ENG", FALSE, FALSE, 18.36, 18.36);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "NED", FALSE, FALSE, 28.127, 28.127);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "ARK", FALSE, FALSE, 4.807, 4.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "ATJ", TRUE, FALSE, 111.864, 111.864);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "AYU", FALSE, FALSE, 12.155, 12.155);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BAN", FALSE, FALSE, 2.127, 2.127);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BEI", TRUE, FALSE, 90.409, 90.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PAT", TRUE, FALSE, 43.269, 43.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "MUG", FALSE, FALSE, 25.575, 25.575);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "MAD", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "KLN", FALSE, FALSE, 1.96, 1.96);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "MAB", FALSE, FALSE, 2.186, 2.186);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "JOH", TRUE, FALSE, 80.176, 80.176);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "KED", TRUE, FALSE, 34.054, 34.054);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PRK", TRUE, FALSE, 27.982, 27.982);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PGR", TRUE, FALSE, 24.064, 24.064);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PLB", TRUE, FALSE, 35.59, 35.59);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "SAK", TRUE, FALSE, 35.095, 35.095);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "KUT", TRUE, FALSE, 37.276, 37.276);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BNJ", TRUE, FALSE, 31.285, 31.285);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "LFA", TRUE, FALSE, 43.307, 43.307);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BEU", TRUE, FALSE, 44.209, 44.209);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BRS", TRUE, FALSE, 30.274, 30.274);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "JMB", TRUE, FALSE, 18.575, 18.575);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "IND", TRUE, FALSE, 25.241, 25.241);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "ENG", FALSE, FALSE, 72.675, 72.675);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "FRA", FALSE, FALSE, 7.534, 7.534);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "POR", FALSE, FALSE, 8.142, 8.142);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "NED", FALSE, FALSE, 9.367, 9.367);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "ARK", TRUE, FALSE, 50.805, 50.805);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MUG", FALSE, FALSE, 126.688, 126.688);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MYS", FALSE, FALSE, 24.362, 24.362);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MAD", FALSE, FALSE, 5.81, 5.81);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "GDW", FALSE, FALSE, 1.99, 1.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "GRJ", TRUE, FALSE, 9.071, 9.071);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "DHU", FALSE, FALSE, 7.148, 7.148);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KLN", FALSE, FALSE, 4.625, 4.625);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MAB", FALSE, FALSE, 6.422, 6.422);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "BST", TRUE, FALSE, 10.681, 10.681);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "BND", FALSE, FALSE, 4.633, 4.633);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KMT", TRUE, FALSE, 11.17, 11.17);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "HAD", FALSE, FALSE, 5.295, 5.295);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "LDK", FALSE, FALSE, 2.024, 2.024);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "BGL", FALSE, FALSE, 5.295, 5.295);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "GHR", FALSE, FALSE, 3.896, 3.896);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "JAJ", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "PAN", FALSE, FALSE, 5.38, 5.38);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "SBP", TRUE, FALSE, 18.452, 18.452);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "PTT", TRUE, FALSE, 8.699, 8.699);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "RTT", TRUE, FALSE, 11.961, 11.961);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KLH", TRUE, FALSE, 8.628, 8.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KJH", TRUE, FALSE, 8.916, 8.916);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "PRD", TRUE, FALSE, 8.628, 8.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "JPR", TRUE, FALSE, 8.628, 8.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "SRG", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KND", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "DNG", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "DTI", FALSE, FALSE, 4.096, 4.096);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "JML", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MKP", TRUE, FALSE, 10.349, 10.349);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "SRM", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KMN", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "KHI", FALSE, FALSE, 54.33, 54.33);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "BUK", FALSE, FALSE, 78.055, 78.055);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "MAR", FALSE, FALSE, 54.292, 54.292);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "MUG", FALSE, FALSE, 288.481, 288.481);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "MYS", FALSE, FALSE, 51.783, 51.783);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "GDW", FALSE, FALSE, 6.965, 6.965);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "DHU", TRUE, FALSE, 24.132, 24.132);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "BND", TRUE, FALSE, 10.652, 10.652);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "HAD", TRUE, FALSE, 14.135, 14.135);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "LDK", FALSE, FALSE, 5.06, 5.06);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "BGL", TRUE, FALSE, 14.135, 14.135);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "GHR", TRUE, FALSE, 10.638, 10.638);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "CHD", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "PAN", TRUE, FALSE, 14.328, 14.328);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "SRG", TRUE, FALSE, 8.628, 8.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "DNG", TRUE, FALSE, 11.422, 11.422);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "DTI", TRUE, FALSE, 10.992, 10.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "JML", TRUE, FALSE, 11.422, 11.422);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "SRM", TRUE, FALSE, 10.846, 10.846);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "KMN", TRUE, FALSE, 10.846, 10.846);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "ENG", FALSE, FALSE, 2.901, 2.901);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "KZH", FALSE, FALSE, 102.645, 102.645);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "KHI", FALSE, FALSE, 59.464, 59.464);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "BUK", FALSE, FALSE, 94.423, 94.423);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "PER", FALSE, FALSE, 45.679, 45.679);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "ZUN", FALSE, FALSE, 6.071, 6.071);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "BAL", TRUE, FALSE, 56.886, 56.886);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "MUG", FALSE, FALSE, 76.495, 76.495);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "MER", FALSE, FALSE, 2.53, 2.53);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "JAN", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "JSL", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "LDK", TRUE, FALSE, 14.907, 14.907);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "JAJ", FALSE, FALSE, 4.587, 4.587);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "ENG", FALSE, FALSE, 11.743, 11.743);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "FRA", FALSE, FALSE, 7.534, 7.534);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "POR", FALSE, FALSE, 8.142, 8.142);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "NED", FALSE, FALSE, 9.367, 9.367);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MAR", TRUE, FALSE, 68.885, 68.885);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MUG", TRUE, FALSE, 289.626, 289.626);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MYS", FALSE, FALSE, 55.593, 55.593);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "GUJ", FALSE, FALSE, 5.37, 5.37);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MAD", FALSE, FALSE, 14.891, 14.891);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MER", FALSE, FALSE, 12.46, 12.46);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "JAN", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "GDW", TRUE, FALSE, 18.817, 18.817);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "KLN", FALSE, FALSE, 11.158, 11.158);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MAB", FALSE, FALSE, 8.972, 8.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "JSL", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "CHD", TRUE, FALSE, 11.422, 11.422);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "JAJ", FALSE, FALSE, 4.587, 4.587);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "DAN", FALSE, FALSE, 4.269, 4.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "ENG", FALSE, FALSE, 47.11, 47.11);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "FRA", FALSE, FALSE, 37.674, 37.674);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "POR", FALSE, FALSE, 21.724, 21.724);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "NED", FALSE, FALSE, 32.942, 32.942);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "ADE", FALSE, FALSE, 8.878, 8.878);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "HDR", FALSE, FALSE, 6.483, 6.483);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "RAS", FALSE, FALSE, 34.409, 34.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MDI", FALSE, FALSE, 6.274, 6.274);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "HAR", FALSE, FALSE, 2.307, 2.307);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "HOB", FALSE, FALSE, 4.051, 4.051);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "WAR", FALSE, FALSE, 4.244, 4.244);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MAR", FALSE, FALSE, 18.819, 18.819);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MUG", FALSE, FALSE, 82.697, 82.697);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MYS", TRUE, FALSE, 56.972, 56.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "GUJ", FALSE, FALSE, 2.37, 2.37);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MAD", TRUE, FALSE, 58.636, 58.636);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MER", FALSE, FALSE, 4.516, 4.516);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "KLN", TRUE, FALSE, 49.41, 49.41);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MAB", TRUE, FALSE, 36.747, 36.747);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "JAJ", FALSE, FALSE, 6.573, 6.573);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "RJK", FALSE, FALSE, 1.998, 1.998);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "KND", TRUE, FALSE, 10.522, 10.522);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "ENG", FALSE, FALSE, 14.509, 14.509);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "POR", FALSE, FALSE, 17.173, 17.173);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "ADE", FALSE, FALSE, 8.878, 8.878);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "HDR", FALSE, FALSE, 6.483, 6.483);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "HED", FALSE, FALSE, 20.281, 20.281);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MHR", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "OMA", FALSE, FALSE, 31.422, 31.422);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "RAS", FALSE, FALSE, 34.409, 34.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MSY", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "PER", FALSE, FALSE, 15.499, 15.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "ZAN", FALSE, FALSE, 29.829, 29.829);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MLI", FALSE, FALSE, 4.769, 4.769);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "AJU", FALSE, FALSE, 1.98, 1.98);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MDI", FALSE, FALSE, 6.274, 6.274);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "AFA", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "HAR", FALSE, FALSE, 2.307, 2.307);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "HOB", FALSE, FALSE, 4.051, 4.051);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MED", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MJE", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "PTE", FALSE, FALSE, 2.222, 2.222);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "WAR", FALSE, FALSE, 4.244, 4.244);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "SKA", FALSE, FALSE, 2.926, 2.926);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "BTS", FALSE, FALSE, 2.409, 2.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "BAL", FALSE, FALSE, 32.862, 32.862);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MAR", FALSE, FALSE, 12.017, 12.017);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MUG", FALSE, FALSE, 82.675, 82.675);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "GUJ", TRUE, FALSE, 13.019, 13.019);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MER", TRUE, FALSE, 21.163, 21.163);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "JAN", TRUE, FALSE, 7.826, 7.826);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "JSL", TRUE, FALSE, 9.281, 9.281);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "JAJ", TRUE, FALSE, 49.628, 49.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "RJK", TRUE, FALSE, 10.071, 10.071);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "ALG", FALSE, FALSE, 61.4, 61.4);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "TRP", FALSE, FALSE, 12.809, 12.809);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "TUN", FALSE, FALSE, 60.923, 60.923);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "FZA", FALSE, FALSE, 6.132, 6.132);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "ASH", FALSE, FALSE, 6.725, 6.725);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "ETH", FALSE, FALSE, 7.53, 7.53);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "MAL", FALSE, FALSE, 4.826, 4.826);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "NUB", FALSE, FALSE, 53.7, 53.7);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "SON", FALSE, FALSE, 12.029, 12.029);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "HAU", TRUE, FALSE, 23.097, 23.097);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "KBO", TRUE, FALSE, 101.292, 101.292);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "OYO", TRUE, FALSE, 24.485, 24.485);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "SOF", FALSE, FALSE, 62.793, 62.793);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "WGD", FALSE, FALSE, 8.424, 8.424);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "GUR", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "WAD", FALSE, FALSE, 4.286, 4.286);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "DAR", FALSE, FALSE, 6.084, 6.084);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "AIR", TRUE, FALSE, 47.279, 47.279);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "BON", FALSE, FALSE, 6.654, 6.654);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "DAH", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "DGB", FALSE, FALSE, 4.444, 4.444);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "FUL", FALSE, FALSE, 47.341, 47.341);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "JNN", FALSE, FALSE, 16.336, 16.336);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "KAN", TRUE, FALSE, 26.537, 26.537);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "KNG", FALSE, FALSE, 7.405, 7.405);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "KTS", TRUE, FALSE, 37.951, 37.951);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "NUP", TRUE, FALSE, 11.684, 11.684);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "TMB", FALSE, FALSE, 25.5, 25.5);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "YAO", TRUE, FALSE, 11.877, 11.877);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "YAT", FALSE, FALSE, 7.75, 7.75);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "ZZZ", TRUE, FALSE, 16.143, 16.143);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "TUR", FALSE, FALSE, 28.473, 28.473);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ADE", FALSE, FALSE, 6.66, 6.66);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "HDR", FALSE, FALSE, 4.323, 4.323);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "HED", FALSE, FALSE, 6.906, 6.906);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "NJR", FALSE, FALSE, 4.42, 4.42);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "RAS", FALSE, FALSE, 81.86, 81.86);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ETH", TRUE, FALSE, 77.931, 77.931);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "NUB", TRUE, FALSE, 90.818, 90.818);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "AJU", FALSE, FALSE, 14.231, 14.231);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MDI", FALSE, FALSE, 4.08, 4.08);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ENA", TRUE, FALSE, 11.253, 11.253);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "OGD", TRUE, FALSE, 9.244, 9.244);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "WAD", TRUE, FALSE, 11.606, 11.606);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "DAR", TRUE, FALSE, 23.456, 23.456);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "HAR", FALSE, FALSE, 10.715, 10.715);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "HOB", FALSE, FALSE, 3.606, 3.606);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "KAF", TRUE, FALSE, 15.325, 15.325);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MED", TRUE, FALSE, 14.169, 14.169);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MRE", TRUE, FALSE, 9.67, 9.67);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "WAR", FALSE, FALSE, 18.159, 18.159);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "WLY", TRUE, FALSE, 15.39, 15.39);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "JJI", TRUE, FALSE, 10.13, 10.13);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ABB", TRUE, FALSE, 9.794, 9.794);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "TUR", FALSE, FALSE, 31.818, 31.818);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "POR", FALSE, FALSE, 8.992, 8.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "ADE", TRUE, FALSE, 89.705, 89.705);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "HDR", TRUE, FALSE, 57.399, 57.399);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "HED", FALSE, FALSE, 27.187, 27.187);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MHR", TRUE, FALSE, 30.622, 30.622);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "NJR", TRUE, FALSE, 10.314, 10.314);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "OMA", FALSE, FALSE, 35.861, 35.861);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "RAS", TRUE, FALSE, 121.378, 121.378);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "PER", FALSE, FALSE, 15.499, 15.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "ETH", FALSE, FALSE, 1.406, 1.406);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "ZAN", FALSE, FALSE, 29.829, 29.829);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MLI", FALSE, FALSE, 4.769, 4.769);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "AJU", TRUE, FALSE, 27.739, 27.739);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MDI", TRUE, FALSE, 56.549, 56.549);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "AFA", TRUE, FALSE, 29.0, 29.0);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "HAR", TRUE, FALSE, 20.444, 20.444);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "HOB", TRUE, FALSE, 38.795, 38.795);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MED", FALSE, FALSE, 4.095, 4.095);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MJE", TRUE, FALSE, 32.43, 32.43);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "WAR", TRUE, FALSE, 44.56, 44.56);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "SKA", FALSE, FALSE, 4.944, 4.944);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "BTS", FALSE, FALSE, 4.427, 4.427);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "TUR", FALSE, FALSE, 15.43, 15.43);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "ANZ", FALSE, FALSE, 2.218, 2.218);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "ARD", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "DAW", FALSE, FALSE, 2.218, 2.218);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "NAJ", FALSE, FALSE, 2.218, 2.218);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "OMA", TRUE, FALSE, 74.665, 74.665);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "SHM", FALSE, FALSE, 2.198, 2.198);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "MSY", FALSE, FALSE, 4.191, 4.191);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "PER", FALSE, FALSE, 54.778, 54.778);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "BAL", FALSE, FALSE, 13.289, 13.289);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "FRA", FALSE, FALSE, 4.129, 4.129);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "POR", FALSE, FALSE, 23.853, 23.853);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "NED", FALSE, FALSE, 5.866, 5.866);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "OMA", FALSE, FALSE, 20.328, 20.328);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "ZAN", TRUE, FALSE, 88.628, 88.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "MLI", TRUE, FALSE, 21.753, 21.753);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "AJU", FALSE, FALSE, 1.61, 1.61);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "PTE", TRUE, FALSE, 30.965, 30.965);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "MIR", TRUE, FALSE, 10.818, 10.818);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "SKA", TRUE, FALSE, 57.113, 57.113);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "BTS", TRUE, FALSE, 44.297, 44.297);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "MFY", TRUE, FALSE, 40.265, 40.265);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "ANT", TRUE, FALSE, 37.708, 37.708);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cape_of_good_hope", "POR", FALSE, FALSE, 4.474, 4.474);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cape_of_good_hope", "NED", FALSE, FALSE, 18.188, 18.188);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cape_of_good_hope", "BEN", FALSE, FALSE, 6.057, 6.057);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cape_of_good_hope", "JOL", FALSE, FALSE, 4.341, 4.341);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cape_of_good_hope", "SYO", FALSE, FALSE, 3.877, 3.877);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cape_of_good_hope", "KBU", FALSE, FALSE, 2.384, 2.384);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "TUR", FALSE, FALSE, 64.432, 64.432);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "ANZ", TRUE, FALSE, 6.698, 6.698);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "ARD", TRUE, FALSE, 9.64, 9.64);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "DAW", TRUE, FALSE, 12.863, 12.863);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "NAJ", TRUE, FALSE, 11.396, 11.396);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "SHM", TRUE, FALSE, 18.992, 18.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "MSY", TRUE, FALSE, 22.344, 22.344);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "PER", FALSE, FALSE, 144.316, 144.316);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "MGR", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "RUS", FALSE, FALSE, 15.867, 15.867);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "KZH", TRUE, FALSE, 142.611, 142.611);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "KHI", TRUE, FALSE, 105.493, 105.493);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "BUK", TRUE, FALSE, 164.32, 164.32);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "PER", FALSE, FALSE, 140.387, 140.387);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "ZUN", FALSE, FALSE, 25.477, 25.477);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "TUR", FALSE, FALSE, 31.993, 31.993);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "CRI", FALSE, FALSE, 20.853, 20.853);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "GEO", FALSE, FALSE, 11.17, 11.17);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "RUS", FALSE, FALSE, 15.867, 15.867);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "AVR", FALSE, FALSE, 4.302, 4.302);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "PER", TRUE, FALSE, 352.357, 352.357);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "GAZ", FALSE, FALSE, 39.268, 39.268);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "MGR", TRUE, FALSE, 11.542, 11.542);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "TUR", FALSE, FALSE, 246.363, 246.363);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "SAV", FALSE, FALSE, 20.57, 20.57);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "LUC", FALSE, FALSE, 2.042, 2.042);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "ANZ", FALSE, FALSE, 2.218, 2.218);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "HED", FALSE, FALSE, 27.187, 27.187);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "ABB", FALSE, FALSE, 1.896, 1.896);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "KNI", FALSE, FALSE, 2.014, 2.014);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "RAG", FALSE, FALSE, 22.032, 22.032);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "TUR", FALSE, FALSE, 207.883, 207.883);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "FRA", FALSE, FALSE, 8.314, 8.314);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "HAB", FALSE, FALSE, 2.112, 2.112);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "SPA", FALSE, FALSE, 32.134, 32.134);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "GEN", FALSE, FALSE, 42.77, 42.77);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "MAN", FALSE, FALSE, 2.313, 2.313);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "PAP", FALSE, FALSE, 37.32, 37.32);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "SAV", FALSE, FALSE, 24.023, 24.023);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "TUS", FALSE, FALSE, 12.863, 12.863);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "VEN", FALSE, FALSE, 49.866, 49.866);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "LUC", FALSE, FALSE, 5.147, 5.147);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "ANZ", FALSE, FALSE, 1.542, 1.542);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "HED", TRUE, FALSE, 81.16, 81.16);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "ABB", FALSE, FALSE, 1.635, 1.635);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "MOL", FALSE, FALSE, 11.253, 11.253);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "TUR", FALSE, FALSE, 19.063, 19.063);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "CRI", FALSE, FALSE, 24.248, 24.248);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "GEO", FALSE, FALSE, 14.186, 14.186);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "RUS", FALSE, FALSE, 68.561, 68.561);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "ZAZ", FALSE, FALSE, 4.89, 4.89);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "AVR", TRUE, FALSE, 12.19, 12.19);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "KZH", FALSE, FALSE, 6.093, 6.093);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "PER", FALSE, FALSE, 2.31, 2.31);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "CIR", FALSE, FALSE, 6.301, 6.301);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "GAZ", TRUE, FALSE, 56.291, 56.291);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "MOL", TRUE, FALSE, 15.608, 15.608);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "WAL", FALSE, FALSE, 21.441, 21.441);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "TUR", FALSE, FALSE, 160.836, 160.836);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "PLC", FALSE, FALSE, 10.817, 10.817);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "HAB", FALSE, FALSE, 14.129, 14.129);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "SLZ", FALSE, FALSE, 6.725, 6.725);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "CRI", TRUE, FALSE, 36.393, 36.393);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "GEO", TRUE, FALSE, 34.887, 34.887);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "RUS", FALSE, FALSE, 32.816, 32.816);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "ZAZ", TRUE, FALSE, 10.094, 10.094);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "PER", FALSE, FALSE, 11.551, 11.551);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "CIR", TRUE, FALSE, 19.786, 19.786);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "GAZ", FALSE, FALSE, 11.093, 11.093);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "IME", TRUE, FALSE, 11.59, 11.59);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "REG", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "MON", FALSE, FALSE, 1.972, 1.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "RAG", FALSE, FALSE, 37.922, 37.922);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "WAL", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "TUR", TRUE, FALSE, 270.341, 270.341);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "HAB", FALSE, FALSE, 2.964, 2.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "VEN", FALSE, FALSE, 12.756, 12.756);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "SWE", FALSE, FALSE, 6.51, 6.51);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "PLC", FALSE, FALSE, 172.791, 172.791);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "BAV", FALSE, FALSE, 32.837, 32.837);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "HAB", FALSE, FALSE, 4.049, 4.049);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "RUS", FALSE, FALSE, 235.16, 235.16);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kazan", "SWE", FALSE, FALSE, 6.51, 6.51);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kazan", "RUS", FALSE, FALSE, 263.415, 263.415);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "SWE", FALSE, FALSE, 56.385, 56.385);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "SHL", FALSE, FALSE, 1.924, 1.924);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "SCO", FALSE, FALSE, 2.012, 2.012);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "PRU", FALSE, FALSE, 8.075, 8.075);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "KUR", FALSE, FALSE, 0.694, 0.694);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "PLC", FALSE, FALSE, 16.533, 16.533);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "BRE", FALSE, FALSE, 2.042, 2.042);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "HAM", FALSE, FALSE, 2.322, 2.322);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "RUS", TRUE, FALSE, 256.69, 256.69);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "HSA", FALSE, FALSE, 24.038, 24.038);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("laplata", "POR", FALSE, FALSE, 5.33, 5.33);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("laplata", "GUA", TRUE, FALSE, 8.296, 8.296);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("laplata", "C05", FALSE, FALSE, 57.333, 57.333);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("laplata", "C12", FALSE, FALSE, 51.293, 51.293);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "POR", FALSE, FALSE, 6.235, 6.235);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "BEN", FALSE, FALSE, 6.057, 6.057);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "JOL", FALSE, FALSE, 4.341, 4.341);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "SYO", FALSE, FALSE, 3.877, 3.877);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "KBU", FALSE, FALSE, 2.384, 2.384);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "C05", TRUE, FALSE, 244.462, 244.462);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "POR", FALSE, FALSE, 6.235, 6.235);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "ALG", FALSE, FALSE, 69.647, 69.647);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "MOR", FALSE, FALSE, 80.205, 80.205);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "ASH", TRUE, FALSE, 14.132, 14.132);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "BEN", FALSE, FALSE, 16.007, 16.007);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "MAL", TRUE, FALSE, 12.964, 12.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "SON", TRUE, FALSE, 25.931, 25.931);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "OYO", FALSE, FALSE, 2.264, 2.264);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "SOF", TRUE, FALSE, 106.237, 106.237);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "JOL", FALSE, FALSE, 13.422, 13.422);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "WGD", TRUE, FALSE, 20.29, 20.29);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "GUR", TRUE, FALSE, 11.709, 11.709);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "SYO", FALSE, FALSE, 3.877, 3.877);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "AIR", FALSE, FALSE, 10.771, 10.771);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "BON", TRUE, FALSE, 17.071, 17.071);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "DAH", TRUE, FALSE, 10.846, 10.846);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "DGB", TRUE, FALSE, 12.86, 12.86);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "FUL", TRUE, FALSE, 65.313, 65.313);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "JNN", TRUE, FALSE, 42.537, 42.537);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "KBU", FALSE, FALSE, 8.589, 8.589);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "KNG", TRUE, FALSE, 18.542, 18.542);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "TMB", TRUE, FALSE, 62.979, 62.979);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "YAT", TRUE, FALSE, 18.923, 18.923);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "DAN", FALSE, FALSE, 11.065, 11.065);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "ENG", FALSE, FALSE, 44.007, 44.007);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "PRU", FALSE, FALSE, 1.078, 1.078);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "FRA", FALSE, FALSE, 45.669, 45.669);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "EFR", FALSE, FALSE, 3.572, 3.572);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "POR", FALSE, FALSE, 58.981, 58.981);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "SPA", FALSE, FALSE, 65.291, 65.291);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "NED", FALSE, FALSE, 46.095, 46.095);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "MOR", FALSE, FALSE, 5.384, 5.384);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "ASH", FALSE, FALSE, 1.666, 1.666);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "BEN", TRUE, FALSE, 63.991, 63.991);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "KON", FALSE, FALSE, 20.274, 20.274);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "LOA", TRUE, FALSE, 35.865, 35.865);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "JOL", TRUE, FALSE, 54.915, 54.915);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "SYO", TRUE, FALSE, 52.364, 52.364);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "FUL", FALSE, FALSE, 23.052, 23.052);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "KBU", TRUE, FALSE, 18.992, 18.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "C04", FALSE, FALSE, 10.324, 10.324);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "C09", FALSE, FALSE, 43.45, 43.45);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "C10", FALSE, FALSE, 16.37, 16.37);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "FRA", FALSE, FALSE, 12.734, 12.734);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "POR", FALSE, FALSE, 25.095, 25.095);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "SPA", FALSE, FALSE, 97.061, 97.061);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "GEN", FALSE, FALSE, 42.77, 42.77);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "PAP", FALSE, FALSE, 5.964, 5.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "SAV", FALSE, FALSE, 3.453, 3.453);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "TUS", FALSE, FALSE, 10.747, 10.747);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "LUC", FALSE, FALSE, 3.105, 3.105);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "ALG", FALSE, FALSE, 13.98, 13.98);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "MOR", FALSE, FALSE, 5.384, 5.384);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "TRP", TRUE, FALSE, 44.0, 44.0);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "TUN", TRUE, FALSE, 107.315, 107.315);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "KBA", TRUE, FALSE, 21.838, 21.838);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "TGT", TRUE, FALSE, 9.313, 9.313);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "GHD", TRUE, FALSE, 8.402, 8.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "FZA", TRUE, FALSE, 10.927, 10.927);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "MZB", TRUE, FALSE, 9.432, 9.432);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "MON", TRUE, FALSE, 9.013, 9.013);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "RAG", TRUE, FALSE, 89.663, 89.663);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "WAL", FALSE, FALSE, 21.441, 21.441);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "TUR", FALSE, FALSE, 105.42, 105.42);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "FRA", FALSE, FALSE, 8.314, 8.314);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "HAB", FALSE, FALSE, 31.062, 31.062);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "ULM", FALSE, FALSE, 6.104, 6.104);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "SPA", FALSE, FALSE, 32.134, 32.134);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "GEN", FALSE, FALSE, 42.77, 42.77);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "MAN", FALSE, FALSE, 4.335, 4.335);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "MOD", FALSE, FALSE, 2.048, 2.048);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "PAP", FALSE, FALSE, 37.32, 37.32);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "PAR", FALSE, FALSE, 2.028, 2.028);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "SAV", FALSE, FALSE, 24.023, 24.023);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "TUS", FALSE, FALSE, 12.863, 12.863);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "VEN", FALSE, FALSE, 72.713, 72.713);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "LUC", FALSE, FALSE, 5.147, 5.147);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "POR", FALSE, FALSE, 25.095, 25.095);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "SPA", FALSE, FALSE, 55.567, 55.567);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "ALG", TRUE, FALSE, 75.385, 75.385);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "MOR", TRUE, FALSE, 103.011, 103.011);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "WAL", TRUE, FALSE, 56.417, 56.417);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "TUR", FALSE, FALSE, 6.421, 6.421);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "PLC", FALSE, FALSE, 118.704, 118.704);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "BAV", FALSE, FALSE, 37.506, 37.506);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "HAB", FALSE, FALSE, 142.779, 142.779);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "SAX", FALSE, FALSE, 50.04, 50.04);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "SLZ", FALSE, FALSE, 8.772, 8.772);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "THU", FALSE, FALSE, 9.194, 9.194);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "ULM", FALSE, FALSE, 8.336, 8.336);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "WUR", FALSE, FALSE, 8.466, 8.466);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "MEM", FALSE, FALSE, 5.522, 5.522);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "VEN", FALSE, FALSE, 113.344, 113.344);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "PSS", FALSE, FALSE, 5.682, 5.682);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "REG", FALSE, FALSE, 10.479, 10.479);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "SWE", FALSE, FALSE, 74.481, 74.481);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "PRU", FALSE, FALSE, 117.72, 117.72);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "PLC", TRUE, FALSE, 232.835, 232.835);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "ANH", FALSE, FALSE, 4.436, 4.436);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "AUG", FALSE, FALSE, 7.75, 7.75);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "BAV", FALSE, FALSE, 37.506, 37.506);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "BRU", FALSE, FALSE, 6.874, 6.874);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "FRN", FALSE, FALSE, 10.12, 10.12);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "HAB", FALSE, FALSE, 95.359, 95.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "HAN", FALSE, FALSE, 11.807, 11.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "HES", FALSE, FALSE, 16.659, 16.659);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "KOL", FALSE, FALSE, 9.733, 9.733);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "MAI", FALSE, FALSE, 7.65, 7.65);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "PAL", FALSE, FALSE, 21.168, 21.168);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "SAX", FALSE, FALSE, 55.207, 55.207);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "SLZ", FALSE, FALSE, 8.772, 8.772);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "THU", FALSE, FALSE, 11.338, 11.338);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "ULM", FALSE, FALSE, 8.336, 8.336);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "WUR", FALSE, FALSE, 8.466, 8.466);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "NUM", FALSE, FALSE, 8.432, 8.432);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "MEM", FALSE, FALSE, 5.522, 5.522);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "RVA", FALSE, FALSE, 6.066, 6.066);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "MAN", FALSE, FALSE, 12.468, 12.468);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "VEN", FALSE, FALSE, 113.344, 113.344);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "PSS", FALSE, FALSE, 5.682, 5.682);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "REG", FALSE, FALSE, 10.479, 10.479);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "GOS", FALSE, FALSE, 4.884, 4.884);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "TNT", FALSE, FALSE, 4.721, 4.721);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "PRU", FALSE, FALSE, 109.644, 109.644);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "FRA", FALSE, FALSE, 3.05, 3.05);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "AAC", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ANH", FALSE, FALSE, 4.436, 4.436);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ANS", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "AUG", TRUE, FALSE, 18.982, 18.982);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BAD", FALSE, FALSE, 8.415, 8.415);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BAV", TRUE, FALSE, 79.118, 79.118);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BRU", FALSE, FALSE, 6.874, 6.874);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "FRN", FALSE, FALSE, 14.912, 14.912);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "HAB", TRUE, FALSE, 137.803, 137.803);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "HES", FALSE, FALSE, 18.94, 18.94);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "KOL", FALSE, FALSE, 13.556, 13.556);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MAI", FALSE, FALSE, 9.82, 9.82);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MUN", FALSE, FALSE, 15.923, 15.923);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "PAL", FALSE, FALSE, 24.146, 24.146);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "SAX", FALSE, FALSE, 55.207, 55.207);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "SLZ", TRUE, FALSE, 28.544, 28.544);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "SWI", FALSE, FALSE, 62.552, 62.552);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "THU", FALSE, FALSE, 11.338, 11.338);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "TRI", FALSE, FALSE, 8.761, 8.761);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ULM", TRUE, FALSE, 29.065, 29.065);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "WBG", FALSE, FALSE, 7.413, 7.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "WUR", TRUE, FALSE, 22.194, 22.194);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "NUM", FALSE, FALSE, 12.733, 12.733);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MEM", TRUE, FALSE, 16.608, 16.608);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "NSA", FALSE, FALSE, 4.31, 4.31);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "RVA", FALSE, FALSE, 8.609, 8.609);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "SPA", FALSE, FALSE, 3.952, 3.952);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MAN", FALSE, FALSE, 14.781, 14.781);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MOD", FALSE, FALSE, 6.144, 6.144);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "PAP", FALSE, FALSE, 10.259, 10.259);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "PAR", FALSE, FALSE, 7.098, 7.098);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "VEN", FALSE, FALSE, 142.266, 142.266);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "PSS", TRUE, FALSE, 16.271, 16.271);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ROT", FALSE, FALSE, 5.391, 5.391);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BYT", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "REG", TRUE, FALSE, 41.287, 41.287);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "TTL", FALSE, FALSE, 7.497, 7.497);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "GOS", FALSE, FALSE, 4.884, 4.884);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "TNT", TRUE, FALSE, 13.239, 13.239);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MLH", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BAM", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "SWE", FALSE, FALSE, 12.789, 12.789);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "DAN", FALSE, FALSE, 85.413, 85.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "SHL", FALSE, FALSE, 12.138, 12.138);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "PRU", TRUE, FALSE, 166.052, 166.052);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "FRA", FALSE, FALSE, 3.05, 3.05);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "AAC", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "ANH", TRUE, FALSE, 13.823, 13.823);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "ANS", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BAD", FALSE, FALSE, 8.415, 8.415);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BRE", FALSE, FALSE, 7.247, 7.247);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BRU", TRUE, FALSE, 18.793, 18.793);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "FRN", FALSE, FALSE, 14.912, 14.912);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "HAB", FALSE, FALSE, 18.634, 18.634);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "HAM", FALSE, FALSE, 10.223, 10.223);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "HAN", TRUE, FALSE, 20.134, 20.134);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "HES", FALSE, FALSE, 18.94, 18.94);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "KOL", FALSE, FALSE, 13.556, 13.556);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "LUN", FALSE, FALSE, 8.839, 8.839);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "MAI", FALSE, FALSE, 9.82, 9.82);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "MKL", FALSE, FALSE, 8.888, 8.888);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "MUN", FALSE, FALSE, 15.923, 15.923);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "PAL", FALSE, FALSE, 24.146, 24.146);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "SAX", TRUE, FALSE, 106.37, 106.37);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "SWI", FALSE, FALSE, 62.552, 62.552);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "THU", TRUE, FALSE, 32.659, 32.659);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "TRI", FALSE, FALSE, 8.761, 8.761);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "WBG", FALSE, FALSE, 7.413, 7.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "NUM", FALSE, FALSE, 12.733, 12.733);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "NSA", FALSE, FALSE, 4.31, 4.31);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "RVA", FALSE, FALSE, 8.609, 8.609);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "HSA", FALSE, FALSE, 47.977, 47.977);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "ROT", FALSE, FALSE, 5.391, 5.391);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BYT", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "TTL", FALSE, FALSE, 7.497, 7.497);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "GOS", TRUE, FALSE, 15.299, 15.299);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "MLH", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BAM", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "SWE", TRUE, FALSE, 126.885, 126.885);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "DAN", FALSE, FALSE, 25.613, 25.613);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "SHL", FALSE, FALSE, 4.763, 4.763);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "PRU", FALSE, FALSE, 30.595, 30.595);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "KUR", TRUE, FALSE, 1.735, 1.735);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "PLC", FALSE, FALSE, 37.221, 37.221);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "BRE", FALSE, FALSE, 7.247, 7.247);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "HAM", FALSE, FALSE, 10.223, 10.223);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "HSA", FALSE, FALSE, 34.993, 34.993);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "SWE", FALSE, FALSE, 14.832, 14.832);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "DAN", FALSE, FALSE, 85.413, 85.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "SHL", FALSE, FALSE, 12.138, 12.138);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "PRU", FALSE, FALSE, 1.523, 1.523);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "FRA", FALSE, FALSE, 49.652, 49.652);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "AAC", TRUE, FALSE, 18.762, 18.762);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "ANS", TRUE, FALSE, 14.224, 14.224);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BAD", TRUE, FALSE, 21.105, 21.105);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BRE", FALSE, FALSE, 14.734, 14.734);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "FRN", TRUE, FALSE, 50.748, 50.748);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HAB", FALSE, FALSE, 2.929, 2.929);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HAM", FALSE, FALSE, 19.898, 19.898);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HES", TRUE, FALSE, 42.917, 42.917);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "KOL", TRUE, FALSE, 43.975, 43.975);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "LOR", FALSE, FALSE, 12.082, 12.082);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "MAI", TRUE, FALSE, 30.553, 30.553);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "MUN", TRUE, FALSE, 37.898, 37.898);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "PAL", TRUE, FALSE, 53.333, 53.333);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "SWI", FALSE, FALSE, 77.68, 77.68);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "THU", FALSE, FALSE, 1.263, 1.263);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "TRI", TRUE, FALSE, 22.846, 22.846);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "WBG", TRUE, FALSE, 20.275, 20.275);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "NUM", TRUE, FALSE, 45.375, 45.375);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "NSA", TRUE, FALSE, 14.339, 14.339);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "RVA", TRUE, FALSE, 30.913, 30.913);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "SPA", FALSE, FALSE, 3.39, 3.39);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HSA", FALSE, FALSE, 47.977, 47.977);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "ROT", TRUE, FALSE, 16.63, 16.63);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BYT", TRUE, FALSE, 12.598, 12.598);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "TTL", TRUE, FALSE, 20.388, 20.388);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "MLH", TRUE, FALSE, 14.2, 14.2);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BAM", TRUE, FALSE, 12.598, 12.598);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "ENG", FALSE, FALSE, 5.288, 5.288);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "SPA", FALSE, FALSE, 1.51, 1.51);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "NED", FALSE, FALSE, 2.593, 2.593);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "MIS", TRUE, FALSE, 26.927, 26.927);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "C04", FALSE, FALSE, 34.005, 34.005);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "C08", FALSE, FALSE, 16.021, 16.021);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "C09", FALSE, FALSE, 43.45, 43.45);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "C10", FALSE, FALSE, 90.747, 90.747);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "DAN", FALSE, FALSE, 3.336, 3.336);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "ENG", FALSE, FALSE, 14.459, 14.459);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "FRA", FALSE, FALSE, 30.577, 30.577);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "POR", FALSE, FALSE, 27.801, 27.801);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "SPA", FALSE, FALSE, 52.401, 52.401);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "NED", FALSE, FALSE, 12.968, 12.968);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "MOR", FALSE, FALSE, 5.384, 5.384);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "C01", FALSE, FALSE, 62.424, 62.424);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "C02", FALSE, FALSE, 23.698, 23.698);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "C04", TRUE, FALSE, 111.804, 111.804);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "C06", FALSE, FALSE, 32.986, 32.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "C09", TRUE, FALSE, 185.277, 185.277);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "C10", FALSE, FALSE, 33.508, 33.508);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ENG", FALSE, FALSE, 37.357, 37.357);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "FRA", FALSE, FALSE, 15.091, 15.091);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "EFR", FALSE, FALSE, 3.572, 3.572);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "SPA", FALSE, FALSE, 15.907, 15.907);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "NED", FALSE, FALSE, 34.471, 34.471);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "CHE", TRUE, FALSE, 7.54, 7.54);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ABE", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "MAH", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "MIK", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ALT", TRUE, FALSE, 7.785, 7.785);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ICH", TRUE, FALSE, 7.785, 7.785);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "COF", TRUE, FALSE, 7.851, 7.851);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "JOA", TRUE, FALSE, 7.851, 7.851);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "KSI", TRUE, FALSE, 8.366, 8.366);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "TSC", TRUE, FALSE, 7.851, 7.851);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "PEN", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "MLS", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "INN", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "AGQ", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "C00", FALSE, FALSE, 3.312, 3.312);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "C01", TRUE, FALSE, 226.196, 226.196);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "C02", FALSE, FALSE, 50.02, 50.02);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "C06", TRUE, FALSE, 35.786, 35.786);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "DAN", FALSE, FALSE, 8.398, 8.398);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "ENG", FALSE, FALSE, 12.676, 12.676);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "SCO", FALSE, FALSE, 12.225, 12.225);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "FRA", FALSE, FALSE, 16.513, 16.513);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "MAH", TRUE, FALSE, 8.206, 8.206);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "MIK", TRUE, FALSE, 39.169, 39.169);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "PEN", TRUE, FALSE, 39.169, 39.169);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "MLS", TRUE, FALSE, 9.493, 9.493);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "INN", TRUE, FALSE, 7.931, 7.931);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "AGQ", TRUE, FALSE, 8.14, 8.14);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "C00", FALSE, FALSE, 20.136, 20.136);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "C01", FALSE, FALSE, 14.049, 14.049);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "C02", TRUE, FALSE, 162.283, 162.283);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "SWE", FALSE, FALSE, 0.441, 0.441);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "DAN", FALSE, FALSE, 9.095, 9.095);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "ENG", FALSE, FALSE, 12.676, 12.676);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "SCO", FALSE, FALSE, 12.225, 12.225);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "RUS", FALSE, FALSE, 43.278, 43.278);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SWE", FALSE, FALSE, 13.524, 13.524);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "DAN", FALSE, FALSE, 67.606, 67.606);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SHL", FALSE, FALSE, 4.763, 4.763);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "ENG", FALSE, FALSE, 100.74, 100.74);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SCO", TRUE, FALSE, 75.441, 75.441);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "FRA", FALSE, FALSE, 15.091, 15.091);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "BRE", FALSE, FALSE, 9.289, 9.289);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "EFR", FALSE, FALSE, 5.594, 5.594);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "HAM", FALSE, FALSE, 12.545, 12.545);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "HAN", FALSE, FALSE, 2.214, 2.214);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "LUN", FALSE, FALSE, 2.04, 2.04);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "MKL", FALSE, FALSE, 2.222, 2.222);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SPA", FALSE, FALSE, 12.889, 12.889);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "LIE", FALSE, FALSE, 2.024, 2.024);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "NED", FALSE, FALSE, 17.964, 17.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "HSA", FALSE, FALSE, 34.993, 34.993);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "SWE", FALSE, FALSE, 33.019, 33.019);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "DAN", TRUE, FALSE, 183.339, 183.339);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "SHL", TRUE, FALSE, 69.826, 69.826);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "ENG", FALSE, FALSE, 34.903, 34.903);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "PRU", FALSE, FALSE, 13.936, 13.936);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "FRA", FALSE, FALSE, 15.091, 15.091);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "BRE", TRUE, FALSE, 88.493, 88.493);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "EFR", FALSE, FALSE, 5.594, 5.594);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "HAM", TRUE, FALSE, 120.576, 120.576);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "HAN", FALSE, FALSE, 2.391, 2.391);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "LUN", TRUE, FALSE, 16.714, 16.714);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "MKL", TRUE, FALSE, 29.733, 29.733);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "SPA", FALSE, FALSE, 12.889, 12.889);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "NED", FALSE, FALSE, 17.964, 17.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "HSA", TRUE, FALSE, 156.45, 156.45);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "FRA", FALSE, FALSE, 250.257, 250.257);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "LOR", FALSE, FALSE, 12.082, 12.082);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "SWI", FALSE, FALSE, 67.292, 67.292);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "SPA", FALSE, FALSE, 10.71, 10.71);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "LIE", FALSE, FALSE, 11.132, 11.132);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "FRA", FALSE, FALSE, 4.42, 4.42);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "POR", TRUE, FALSE, 134.948, 134.948);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "SPA", TRUE, FALSE, 280.499, 280.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "MOR", FALSE, FALSE, 19.838, 19.838);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "ENG", FALSE, FALSE, 98.707, 98.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "FRA", TRUE, FALSE, 261.998, 261.998);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "EFR", FALSE, FALSE, 7.953, 7.953);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "LOR", TRUE, FALSE, 27.712, 27.712);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "SWI", TRUE, FALSE, 140.681, 140.681);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "SPA", FALSE, FALSE, 30.542, 30.542);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "GEN", FALSE, FALSE, 72.047, 72.047);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "PAP", FALSE, FALSE, 89.111, 89.111);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "SAV", FALSE, FALSE, 67.59, 67.59);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "TUS", FALSE, FALSE, 40.371, 40.371);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "LUC", FALSE, FALSE, 9.911, 9.911);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "LIE", TRUE, FALSE, 20.138, 20.138);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "NED", FALSE, FALSE, 95.158, 95.158);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "FRA", FALSE, FALSE, 30.415, 30.415);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "SPA", FALSE, FALSE, 65.921, 65.921);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "GEN", FALSE, FALSE, 42.77, 42.77);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "PAP", FALSE, FALSE, 5.964, 5.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "SAV", FALSE, FALSE, 24.023, 24.023);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "TUS", FALSE, FALSE, 10.747, 10.747);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "LUC", FALSE, FALSE, 3.105, 3.105);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "KNI", TRUE, FALSE, 30.21, 30.21);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "FRA", FALSE, FALSE, 22.015, 22.015);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "SPA", FALSE, FALSE, 73.729, 73.729);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "GEN", TRUE, FALSE, 232.744, 232.744);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "MAN", FALSE, FALSE, 2.964, 2.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "PAP", TRUE, FALSE, 65.815, 65.815);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "SAV", TRUE, FALSE, 32.955, 32.955);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "TUS", TRUE, FALSE, 87.984, 87.984);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "LUC", TRUE, FALSE, 38.665, 38.665);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "HAB", FALSE, FALSE, 10.56, 10.56);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "SPA", FALSE, FALSE, 19.76, 19.76);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "MAN", TRUE, FALSE, 24.174, 24.174);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "MOD", TRUE, FALSE, 12.386, 12.386);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "PAP", FALSE, FALSE, 36.227, 36.227);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "PAR", TRUE, FALSE, 13.232, 13.232);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "VEN", TRUE, FALSE, 178.186, 178.186);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "ENG", TRUE, FALSE, 180.656, 180.656);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "FRA", FALSE, FALSE, 38.971, 38.971);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "EFR", TRUE, FALSE, 61.839, 61.839);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "SPA", FALSE, FALSE, 33.758, 33.758);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "LIE", FALSE, FALSE, 2.254, 2.254);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "NED", TRUE, FALSE, 119.252, 119.252);
