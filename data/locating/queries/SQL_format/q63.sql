

CREATE TABLE country( country_name  VARCHAR(30), home_node    VARCHAR(30), development FLOAT, PRIMARY KEY (country_name));

CREATE TABLE trade_node(    trade_node    VARCHAR(30), local_value FLOAT,    is_inland BOOLEAN,    total_power FLOAT,    outgoing FLOAT,    ingoing FLOAT,   PRIMARY KEY (trade_node));

CREATE TABLE flow(    source    VARCHAR(30),    dest VARCHAR(30),    flow FLOAT,   PRIMARY KEY (source, dest));

CREATE TABLE node_country(    trade_node     VARCHAR(30), country_name    VARCHAR(30),    is_home BOOLEAN, has_merchant BOOLEAN,    base_trading_power FLOAT,    calculated_trading_power FLOAT,   PRIMARY KEY (trade_node, country_name));
    
INSERT INTO country(country_name,home_node,development) VALUES ("SWE", "baltic_sea", 107.373);
INSERT INTO country(country_name,home_node,development) VALUES ("DAN", "lubeck", 118.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GOT", "baltic_sea", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NOR", "lubeck", 78.015);
INSERT INTO country(country_name,home_node,development) VALUES ("SHL", "lubeck", 19.713);
INSERT INTO country(country_name,home_node,development) VALUES ("ALB", "ragusa", 10.915);
INSERT INTO country(country_name,home_node,development) VALUES ("ATH", "ragusa", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BOS", "ragusa", 22.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BYZ", "constantinople", 37.79);
INSERT INTO country(country_name,home_node,development) VALUES ("CRO", "ragusa", 40.808);
INSERT INTO country(country_name,home_node,development) VALUES ("CYP", "aleppo", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("EPI", "ragusa", 10.765);
INSERT INTO country(country_name,home_node,development) VALUES ("KNI", "constantinople", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MOL", "crimea", 39.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NAX", "constantinople", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RAG", "ragusa", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SER", "ragusa", 46.98);
INSERT INTO country(country_name,home_node,development) VALUES ("WAL", "pest", 45.703);
INSERT INTO country(country_name,home_node,development) VALUES ("HUN", "pest", 172.224);
INSERT INTO country(country_name,home_node,development) VALUES ("TUR", "constantinople", 312.922);
INSERT INTO country(country_name,home_node,development) VALUES ("CNN", "north_sea", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ENG", "english_channel", 315.602);
INSERT INTO country(country_name,home_node,development) VALUES ("LEI", "north_sea", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MNS", "north_sea", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SCO", "north_sea", 78.039);
INSERT INTO country(country_name,home_node,development) VALUES ("TYR", "north_sea", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ULS", "north_sea", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DMS", "north_sea", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SLN", "north_sea", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KID", "north_sea", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ORD", "north_sea", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TRY", "north_sea", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("FLY", "north_sea", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MCM", "north_sea", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LOI", "north_sea", 5.949);
INSERT INTO country(country_name,home_node,development) VALUES ("LIT", "kiev", 220.82);
INSERT INTO country(country_name,home_node,development) VALUES ("LIV", "baltic_sea", 54.75);
INSERT INTO country(country_name,home_node,development) VALUES ("MAZ", "krakow", 35.947);
INSERT INTO country(country_name,home_node,development) VALUES ("POL", "krakow", 181.717);
INSERT INTO country(country_name,home_node,development) VALUES ("RIG", "baltic_sea", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TEU", "baltic_sea", 92.818);
INSERT INTO country(country_name,home_node,development) VALUES ("OKA", "kiev", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ALS", "rheinland", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AMG", "bordeaux", 25.788);
INSERT INTO country(country_name,home_node,development) VALUES ("AUV", "bordeaux", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BOU", "bordeaux", 16.564);
INSERT INTO country(country_name,home_node,development) VALUES ("BRI", "bordeaux", 55.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BUR", "champagne", 139.637);
INSERT INTO country(country_name,home_node,development) VALUES ("FOI", "bordeaux", 17.772);
INSERT INTO country(country_name,home_node,development) VALUES ("FRA", "champagne", 245.966);
INSERT INTO country(country_name,home_node,development) VALUES ("NEV", "champagne", 25.362);
INSERT INTO country(country_name,home_node,development) VALUES ("ORL", "champagne", 49.468);
INSERT INTO country(country_name,home_node,development) VALUES ("PRO", "bordeaux", 70.758);
INSERT INTO country(country_name,home_node,development) VALUES ("AAC", "rheinland", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANH", "saxony", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANS", "rheinland", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AUG", "wien", 17.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BAD", "rheinland", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BOH", "saxony", 118.409);
INSERT INTO country(country_name,home_node,development) VALUES ("BRA", "saxony", 64.005);
INSERT INTO country(country_name,home_node,development) VALUES ("BRE", "lubeck", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BRU", "saxony", 31.211);
INSERT INTO country(country_name,home_node,development) VALUES ("EFR", "english_channel", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("FRN", "rheinland", 18.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HAB", "wien", 168.835);
INSERT INTO country(country_name,home_node,development) VALUES ("HAM", "lubeck", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HES", "rheinland", 23.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KLE", "rheinland", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KOL", "rheinland", 26.398);
INSERT INTO country(country_name,home_node,development) VALUES ("LAU", "lubeck", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LOR", "champagne", 32.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LUN", "lubeck", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAG", "saxony", 16.93);
INSERT INTO country(country_name,home_node,development) VALUES ("MAI", "rheinland", 16.678);
INSERT INTO country(country_name,home_node,development) VALUES ("MKL", "lubeck", 23.86);
INSERT INTO country(country_name,home_node,development) VALUES ("MUN", "rheinland", 25.551);
INSERT INTO country(country_name,home_node,development) VALUES ("OLD", "lubeck", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PAL", "rheinland", 39.577);
INSERT INTO country(country_name,home_node,development) VALUES ("SAX", "saxony", 41.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SLZ", "wien", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SWI", "champagne", 51.0);
INSERT INTO country(country_name,home_node,development) VALUES ("THU", "saxony", 33.977);
INSERT INTO country(country_name,home_node,development) VALUES ("TRI", "rheinland", 22.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ULM", "wien", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WBG", "rheinland", 15.515);
INSERT INTO country(country_name,home_node,development) VALUES ("WUR", "wien", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NUM", "rheinland", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MEM", "wien", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("VER", "lubeck", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NSA", "rheinland", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RVA", "rheinland", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DTT", "lubeck", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ARA", "valencia", 196.943);
INSERT INTO country(country_name,home_node,development) VALUES ("CAS", "sevilla", 275.487);
INSERT INTO country(country_name,home_node,development) VALUES ("GRA", "sevilla", 30.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NAV", "bordeaux", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("POR", "sevilla", 135.181);
INSERT INTO country(country_name,home_node,development) VALUES ("FER", "venice", 25.796);
INSERT INTO country(country_name,home_node,development) VALUES ("GEN", "genua", 75.916);
INSERT INTO country(country_name,home_node,development) VALUES ("MAN", "venice", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLO", "genua", 81.513);
INSERT INTO country(country_name,home_node,development) VALUES ("NAP", "genua", 101.724);
INSERT INTO country(country_name,home_node,development) VALUES ("PAP", "genua", 62.238);
INSERT INTO country(country_name,home_node,development) VALUES ("SAV", "champagne", 72.343);
INSERT INTO country(country_name,home_node,development) VALUES ("SIE", "genua", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("URB", "venice", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("VEN", "venice", 170.281);
INSERT INTO country(country_name,home_node,development) VALUES ("MFA", "genua", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LUC", "genua", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LAN", "genua", 56.745);
INSERT INTO country(country_name,home_node,development) VALUES ("BRB", "english_channel", 53.986);
INSERT INTO country(country_name,home_node,development) VALUES ("FLA", "english_channel", 40.933);
INSERT INTO country(country_name,home_node,development) VALUES ("FRI", "english_channel", 24.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GEL", "english_channel", 27.642);
INSERT INTO country(country_name,home_node,development) VALUES ("HOL", "english_channel", 42.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LIE", "champagne", 26.436);
INSERT INTO country(country_name,home_node,development) VALUES ("UTR", "english_channel", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CRI", "crimea", 73.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GEO", "crimea", 29.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAZ", "kazan", 105.938);
INSERT INTO country(country_name,home_node,development) VALUES ("MOS", "novgorod", 191.788);
INSERT INTO country(country_name,home_node,development) VALUES ("NOV", "novgorod", 124.214);
INSERT INTO country(country_name,home_node,development) VALUES ("PSK", "novgorod", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RYA", "novgorod", 32.621);
INSERT INTO country(country_name,home_node,development) VALUES ("TVE", "novgorod", 23.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YAR", "novgorod", 17.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NOG", "astrakhan", 90.454);
INSERT INTO country(country_name,home_node,development) VALUES ("PRM", "white_sea", 24.785);
INSERT INTO country(country_name,home_node,development) VALUES ("FEO", "crimea", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BLO", "novgorod", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RSO", "novgorod", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GOL", "astrakhan", 128.11);
INSERT INTO country(country_name,home_node,development) VALUES ("ADE", "gulf_of_aden", 27.491);
INSERT INTO country(country_name,home_node,development) VALUES ("ALH", "basra", 18.796);
INSERT INTO country(country_name,home_node,development) VALUES ("ANZ", "basra", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ARD", "basra", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DAW", "basra", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("FAD", "aleppo", 10.881);
INSERT INTO country(country_name,home_node,development) VALUES ("HDR", "gulf_of_aden", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HED", "alexandria", 34.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAK", "alexandria", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MDA", "alexandria", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MFL", "gulf_of_aden", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MHR", "gulf_of_aden", 13.728);
INSERT INTO country(country_name,home_node,development) VALUES ("NAJ", "basra", 8.312);
INSERT INTO country(country_name,home_node,development) VALUES ("NJR", "gulf_of_aden", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OMA", "hormuz", 19.915);
INSERT INTO country(country_name,home_node,development) VALUES ("RAS", "gulf_of_aden", 19.841);
INSERT INTO country(country_name,home_node,development) VALUES ("SHM", "basra", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SRV", "persia", 25.678);
INSERT INTO country(country_name,home_node,development) VALUES ("YAS", "hormuz", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YEM", "gulf_of_aden", 26.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HSN", "aleppo", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BTL", "aleppo", 16.179);
INSERT INTO country(country_name,home_node,development) VALUES ("AKK", "aleppo", 52.378);
INSERT INTO country(country_name,home_node,development) VALUES ("CND", "constantinople", 18.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DUL", "aleppo", 23.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAR", "aleppo", 42.732);
INSERT INTO country(country_name,home_node,development) VALUES ("TRE", "crimea", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RAM", "aleppo", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AVR", "astrakhan", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLK", "persia", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SME", "crimea", 15.72);
INSERT INTO country(country_name,home_node,development) VALUES ("ARL", "persia", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MSY", "basra", 39.992);
INSERT INTO country(country_name,home_node,development) VALUES ("MAM", "alexandria", 318.025);
INSERT INTO country(country_name,home_node,development) VALUES ("MOR", "safi", 86.675);
INSERT INTO country(country_name,home_node,development) VALUES ("TUN", "tunis", 108.956);
INSERT INTO country(country_name,home_node,development) VALUES ("TFL", "safi", 19.499);
INSERT INTO country(country_name,home_node,development) VALUES ("SOS", "safi", 27.628);
INSERT INTO country(country_name,home_node,development) VALUES ("TLC", "safi", 55.43);
INSERT INTO country(country_name,home_node,development) VALUES ("TGT", "tunis", 7.668);
INSERT INTO country(country_name,home_node,development) VALUES ("GHD", "tunis", 7.97);
INSERT INTO country(country_name,home_node,development) VALUES ("FZA", "tunis", 9.717);
INSERT INTO country(country_name,home_node,development) VALUES ("MZB", "tunis", 5.709);
INSERT INTO country(country_name,home_node,development) VALUES ("MRK", "safi", 19.364);
INSERT INTO country(country_name,home_node,development) VALUES ("SHY", "siberia", 177.948);
INSERT INTO country(country_name,home_node,development) VALUES ("AFG", "lahore", 46.435);
INSERT INTO country(country_name,home_node,development) VALUES ("KHO", "persia", 19.779);
INSERT INTO country(country_name,home_node,development) VALUES ("QAR", "persia", 153.492);
INSERT INTO country(country_name,home_node,development) VALUES ("TIM", "persia", 136.62);
INSERT INTO country(country_name,home_node,development) VALUES ("TRS", "samarkand", 121.518);
INSERT INTO country(country_name,home_node,development) VALUES ("KRY", "persia", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CIR", "crimea", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GAZ", "astrakhan", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("IME", "crimea", 28.8);
INSERT INTO country(country_name,home_node,development) VALUES ("TAB", "persia", 21.752);
INSERT INTO country(country_name,home_node,development) VALUES ("ORM", "hormuz", 42.466);
INSERT INTO country(country_name,home_node,development) VALUES ("LRI", "basra", 13.898);
INSERT INTO country(country_name,home_node,development) VALUES ("SIS", "persia", 13.264);
INSERT INTO country(country_name,home_node,development) VALUES ("BPI", "persia", 12.649);
INSERT INTO country(country_name,home_node,development) VALUES ("FRS", "hormuz", 55.309);
INSERT INTO country(country_name,home_node,development) VALUES ("QOM", "persia", 88.105);
INSERT INTO country(country_name,home_node,development) VALUES ("AZT", "mexico", 50.77);
INSERT INTO country(country_name,home_node,development) VALUES ("CHE", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHM", "lima", 24.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ZAP", "mexico", 20.278);
INSERT INTO country(country_name,home_node,development) VALUES ("BEN", "ivory_coast", 24.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ETH", "ethiopia", 66.426);
INSERT INTO country(country_name,home_node,development) VALUES ("KON", "kongo", 55.18);
INSERT INTO country(country_name,home_node,development) VALUES ("MAL", "timbuktu", 92.318);
INSERT INTO country(country_name,home_node,development) VALUES ("SON", "timbuktu", 52.494);
INSERT INTO country(country_name,home_node,development) VALUES ("ZAN", "zanzibar", 38.066);
INSERT INTO country(country_name,home_node,development) VALUES ("ZIM", "zambezi", 46.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ADA", "gulf_of_aden", 56.262);
INSERT INTO country(country_name,home_node,development) VALUES ("KBO", "katsina", 27.724);
INSERT INTO country(country_name,home_node,development) VALUES ("LOA", "ivory_coast", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OYO", "katsina", 23.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JOL", "ivory_coast", 23.402);
INSERT INTO country(country_name,home_node,development) VALUES ("SFA", "zanzibar", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MBA", "zanzibar", 15.624);
INSERT INTO country(country_name,home_node,development) VALUES ("MLI", "zanzibar", 14.578);
INSERT INTO country(country_name,home_node,development) VALUES ("AJU", "gulf_of_aden", 34.156);
INSERT INTO country(country_name,home_node,development) VALUES ("MDI", "gulf_of_aden", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ENA", "ethiopia", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WGD", "timbuktu", 18.958);
INSERT INTO country(country_name,home_node,development) VALUES ("GUR", "timbuktu", 5.862);
INSERT INTO country(country_name,home_node,development) VALUES ("OGD", "ethiopia", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WAD", "ethiopia", 5.862);
INSERT INTO country(country_name,home_node,development) VALUES ("ALO", "ethiopia", 17.394);
INSERT INTO country(country_name,home_node,development) VALUES ("KAF", "ethiopia", 13.112);
INSERT INTO country(country_name,home_node,development) VALUES ("MED", "ethiopia", 17.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MRE", "gulf_of_aden", 16.916);
INSERT INTO country(country_name,home_node,development) VALUES ("PTE", "zanzibar", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WAR", "gulf_of_aden", 30.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BEJ", "ethiopia", 10.409);
INSERT INTO country(country_name,home_node,development) VALUES ("WLY", "ethiopia", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DAM", "ethiopia", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HDY", "ethiopia", 10.612);
INSERT INTO country(country_name,home_node,development) VALUES ("JJI", "ethiopia", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ABB", "ethiopia", 12.932);
INSERT INTO country(country_name,home_node,development) VALUES ("TYO", "kongo", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KSJ", "kongo", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LUB", "kongo", 22.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LND", "kongo", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CKW", "kongo", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KIK", "kongo", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KZB", "kongo", 21.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YAK", "kongo", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KLD", "kongo", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KUB", "kongo", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RWA", "african_great_lakes", 18.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BUU", "african_great_lakes", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BUG", "african_great_lakes", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NKO", "african_great_lakes", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KRW", "african_great_lakes", 13.712);
INSERT INTO country(country_name,home_node,development) VALUES ("BNY", "african_great_lakes", 13.916);
INSERT INTO country(country_name,home_node,development) VALUES ("BSG", "african_great_lakes", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("UBH", "african_great_lakes", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MRA", "zambezi", 18.662);
INSERT INTO country(country_name,home_node,development) VALUES ("LDU", "zambezi", 11.412);
INSERT INTO country(country_name,home_node,development) VALUES ("TBK", "zambezi", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MKU", "zambezi", 5.662);
INSERT INTO country(country_name,home_node,development) VALUES ("RZW", "zambezi", 9.505);
INSERT INTO country(country_name,home_node,development) VALUES ("MIR", "zanzibar", 12.814);
INSERT INTO country(country_name,home_node,development) VALUES ("SKA", "zanzibar", 22.045);
INSERT INTO country(country_name,home_node,development) VALUES ("BTS", "zanzibar", 14.176);
INSERT INTO country(country_name,home_node,development) VALUES ("MFY", "zanzibar", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANT", "zanzibar", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ARK", "ganges_delta", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ATJ", "malacca", 18.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AYU", "gulf_of_siam", 132.466);
INSERT INTO country(country_name,home_node,development) VALUES ("BLI", "the_moluccas", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BEI", "malacca", 74.632);
INSERT INTO country(country_name,home_node,development) VALUES ("CHA", "gulf_of_siam", 41.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHG", "yumen", 80.286);
INSERT INTO country(country_name,home_node,development) VALUES ("DAI", "canton", 115.962);
INSERT INTO country(country_name,home_node,development) VALUES ("AMA", "nippon", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DTE", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HSK", "nippon", 40.474);
INSERT INTO country(country_name,home_node,development) VALUES ("HTK", "nippon", 15.321);
INSERT INTO country(country_name,home_node,development) VALUES ("IMG", "nippon", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ODA", "nippon", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OTM", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OUC", "nippon", 18.06);
INSERT INTO country(country_name,home_node,development) VALUES ("SBA", "nippon", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SMZ", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TKD", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TKG", "nippon", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("UES", "nippon", 33.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YMN", "nippon", 28.585);
INSERT INTO country(country_name,home_node,development) VALUES ("RFR", "nippon", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ASK", "nippon", 20.832);
INSERT INTO country(country_name,home_node,development) VALUES ("KTB", "nippon", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANU", "nippon", 14.892);
INSERT INTO country(country_name,home_node,development) VALUES ("AKT", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CBA", "nippon", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ISK", "nippon", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ITO", "nippon", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KKC", "nippon", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KNO", "nippon", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OGS", "nippon", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SHN", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("STK", "nippon", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TKI", "nippon", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("UTN", "nippon", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TTI", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KHA", "yumen", 88.417);
INSERT INTO country(country_name,home_node,development) VALUES ("KHM", "gulf_of_siam", 110.918);
INSERT INTO country(country_name,home_node,development) VALUES ("KOR", "nippon", 129.372);
INSERT INTO country(country_name,home_node,development) VALUES ("LNA", "gulf_of_siam", 55.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LXA", "gulf_of_siam", 99.348);
INSERT INTO country(country_name,home_node,development) VALUES ("MAJ", "the_moluccas", 86.928);
INSERT INTO country(country_name,home_node,development) VALUES ("MKS", "the_moluccas", 22.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLC", "malacca", 60.772);
INSERT INTO country(country_name,home_node,development) VALUES ("MNG", "beijing", 1066.159);
INSERT INTO country(country_name,home_node,development) VALUES ("OIR", "yumen", 113.725);
INSERT INTO country(country_name,home_node,development) VALUES ("PAT", "malacca", 19.95);
INSERT INTO country(country_name,home_node,development) VALUES ("PEG", "burma", 47.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RYU", "nippon", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SUK", "gulf_of_siam", 38.592);
INSERT INTO country(country_name,home_node,development) VALUES ("SUL", "philippines", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAU", "burma", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SOO", "nippon", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NVK", "girin", 23.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SOL", "girin", 33.0);
INSERT INTO country(country_name,home_node,development) VALUES ("EJZ", "girin", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NHX", "girin", 43.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MYR", "girin", 37.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MHX", "girin", 40.988);
INSERT INTO country(country_name,home_node,development) VALUES ("MJZ", "girin", 72.838);
INSERT INTO country(country_name,home_node,development) VALUES ("KRC", "girin", 60.32);
INSERT INTO country(country_name,home_node,development) VALUES ("HMI", "yumen", 12.515);
INSERT INTO country(country_name,home_node,development) VALUES ("KAS", "samarkand", 58.836);
INSERT INTO country(country_name,home_node,development) VALUES ("SYG", "lhasa", 6.816);
INSERT INTO country(country_name,home_node,development) VALUES ("UTS", "lhasa", 35.677);
INSERT INTO country(country_name,home_node,development) VALUES ("KAM", "chengdu", 30.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GUG", "lhasa", 10.982);
INSERT INTO country(country_name,home_node,development) VALUES ("PHA", "lhasa", 9.864);
INSERT INTO country(country_name,home_node,development) VALUES ("BAL", "lahore", 34.015);
INSERT INTO country(country_name,home_node,development) VALUES ("BNG", "ganges_delta", 166.634);
INSERT INTO country(country_name,home_node,development) VALUES ("BAH", "deccan", 186.512);
INSERT INTO country(country_name,home_node,development) VALUES ("DLH", "doab", 55.634);
INSERT INTO country(country_name,home_node,development) VALUES ("MYS", "comorin_cape", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("VIJ", "comorin_cape", 200.238);
INSERT INTO country(country_name,home_node,development) VALUES ("ASS", "burma", 27.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GUJ", "gujarat", 71.93);
INSERT INTO country(country_name,home_node,development) VALUES ("JNP", "doab", 194.842);
INSERT INTO country(country_name,home_node,development) VALUES ("MAD", "comorin_cape", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLW", "deccan", 53.524);
INSERT INTO country(country_name,home_node,development) VALUES ("MER", "gujarat", 54.724);
INSERT INTO country(country_name,home_node,development) VALUES ("MUL", "lahore", 37.934);
INSERT INTO country(country_name,home_node,development) VALUES ("ORI", "ganges_delta", 54.936);
INSERT INTO country(country_name,home_node,development) VALUES ("SND", "gujarat", 48.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JAN", "gujarat", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GDW", "deccan", 20.828);
INSERT INTO country(country_name,home_node,development) VALUES ("GRJ", "ganges_delta", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GWA", "doab", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DHU", "doab", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KSH", "lahore", 17.958);
INSERT INTO country(country_name,home_node,development) VALUES ("KHD", "deccan", 24.642);
INSERT INTO country(country_name,home_node,development) VALUES ("VND", "comorin_cape", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAB", "comorin_cape", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MEW", "doab", 9.418);
INSERT INTO country(country_name,home_node,development) VALUES ("BST", "ganges_delta", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BND", "doab", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CEY", "comorin_cape", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JSL", "gujarat", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAC", "burma", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KMT", "ganges_delta", 14.418);
INSERT INTO country(country_name,home_node,development) VALUES ("KGR", "doab", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAT", "gujarat", 12.83);
INSERT INTO country(country_name,home_node,development) VALUES ("KOC", "comorin_cape", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLB", "burma", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HAD", "doab", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NGA", "gujarat", 9.77);
INSERT INTO country(country_name,home_node,development) VALUES ("LDK", "lahore", 8.724);
INSERT INTO country(country_name,home_node,development) VALUES ("BGL", "doab", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JFN", "comorin_cape", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GHR", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHD", "deccan", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NGP", "ganges_delta", 11.889);
INSERT INTO country(country_name,home_node,development) VALUES ("TRT", "ganges_delta", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CMP", "deccan", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BGA", "gujarat", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TPR", "burma", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SDY", "burma", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YOR", "comorin_cape", 33.728);
INSERT INTO country(country_name,home_node,development) VALUES ("DGL", "comorin_cape", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MBL", "ganges_delta", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("IDR", "gujarat", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JLV", "gujarat", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PTL", "gujarat", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JGD", "gujarat", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PRB", "gujarat", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PAN", "doab", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KLP", "doab", 16.064);
INSERT INTO country(country_name,home_node,development) VALUES ("PTT", "ganges_delta", 15.864);
INSERT INTO country(country_name,home_node,development) VALUES ("RTT", "ganges_delta", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KLH", "ganges_delta", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KJH", "ganges_delta", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PRD", "ganges_delta", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JPR", "ganges_delta", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KND", "comorin_cape", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TLG", "deccan", 13.704);
INSERT INTO country(country_name,home_node,development) VALUES ("KLT", "comorin_cape", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DNG", "doab", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DTI", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GRK", "lhasa", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JML", "doab", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LWA", "lhasa", 12.982);
INSERT INTO country(country_name,home_node,development) VALUES ("SRM", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KTU", "lhasa", 13.952);
INSERT INTO country(country_name,home_node,development) VALUES ("KMN", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SRH", "lahore", 86.745);
INSERT INTO country(country_name,home_node,development) VALUES ("HSA", "lubeck", 17.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ABE", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("APA", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ASI", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BLA", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CAD", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHI", "mississippi_river", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHO", "mississippi_river", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHY", "james_bay", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COM", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("FOX", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LEN", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAH", "st_lawrence", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MIK", "st_lawrence", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MMI", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NAH", "rio_grande", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OJI", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OSA", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OTT", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PAW", "mississippi_river", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PEQ", "chesapeake_bay", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PIM", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("POT", "ohio", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("POW", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SHO", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SIO", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SUS", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WCR", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AIR", "katsina", 26.964);
INSERT INTO country(country_name,home_node,development) VALUES ("BON", "timbuktu", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DAH", "timbuktu", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DGB", "timbuktu", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JNN", "timbuktu", 39.89);
INSERT INTO country(country_name,home_node,development) VALUES ("KAN", "katsina", 24.54);
INSERT INTO country(country_name,home_node,development) VALUES ("KNG", "timbuktu", 12.882);
INSERT INTO country(country_name,home_node,development) VALUES ("KTS", "katsina", 31.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NUP", "katsina", 11.724);
INSERT INTO country(country_name,home_node,development) VALUES ("TMB", "timbuktu", 38.44);
INSERT INTO country(country_name,home_node,development) VALUES ("YAO", "katsina", 32.308);
INSERT INTO country(country_name,home_node,development) VALUES ("YAT", "timbuktu", 16.706);
INSERT INTO country(country_name,home_node,development) VALUES ("ZZZ", "katsina", 23.85);
INSERT INTO country(country_name,home_node,development) VALUES ("NDO", "ivory_coast", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AVA", "burma", 62.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HSE", "burma", 12.816);
INSERT INTO country(country_name,home_node,development) VALUES ("KED", "malacca", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LIG", "malacca", 27.03);
INSERT INTO country(country_name,home_node,development) VALUES ("MPH", "gulf_of_siam", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MYA", "burma", 18.816);
INSERT INTO country(country_name,home_node,development) VALUES ("MMA", "chengdu", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MKA", "burma", 14.432);
INSERT INTO country(country_name,home_node,development) VALUES ("MPA", "burma", 10.77);
INSERT INTO country(country_name,home_node,development) VALUES ("MNI", "burma", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAL", "burma", 11.645);
INSERT INTO country(country_name,home_node,development) VALUES ("HSI", "burma", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BPR", "burma", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHU", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HOD", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHV", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KMC", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ARP", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CLM", "mexico", 31.77);
INSERT INTO country(country_name,home_node,development) VALUES ("CNK", "california", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COC", "mexico", 23.278);
INSERT INTO country(country_name,home_node,development) VALUES ("HDA", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ITZ", "mexico", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KIC", "mexico", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KIO", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MIX", "mexico", 11.95);
INSERT INTO country(country_name,home_node,development) VALUES ("SAL", "california", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAR", "mexico", 30.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TLA", "mexico", 23.721);
INSERT INTO country(country_name,home_node,development) VALUES ("TLX", "mexico", 27.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TOT", "mexico", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WIC", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("XIU", "mexico", 21.064);
INSERT INTO country(country_name,home_node,development) VALUES ("BLM", "the_moluccas", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BTN", "the_moluccas", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PGR", "malacca", 19.836);
INSERT INTO country(country_name,home_node,development) VALUES ("PLB", "malacca", 27.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PSA", "malacca", 37.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SAK", "malacca", 27.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SUN", "the_moluccas", 69.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KUT", "malacca", 29.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BNJ", "malacca", 31.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LUW", "the_moluccas", 18.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MGD", "philippines", 20.864);
INSERT INTO country(country_name,home_node,development) VALUES ("TER", "the_moluccas", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TID", "the_moluccas", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAS", "philippines", 13.515);
INSERT INTO country(country_name,home_node,development) VALUES ("PGS", "philippines", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TDO", "philippines", 23.448);
INSERT INTO country(country_name,home_node,development) VALUES ("MNA", "philippines", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CEB", "philippines", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BTU", "philippines", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CSU", "lima", 32.549);
INSERT INTO country(country_name,home_node,development) VALUES ("CCQ", "cuiaba", 16.692);
INSERT INTO country(country_name,home_node,development) VALUES ("MPC", "patagonia", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MCA", "panama", 29.012);
INSERT INTO country(country_name,home_node,development) VALUES ("QTO", "lima", 35.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CJA", "lima", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HJA", "lima", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PTG", "brazil", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TPQ", "brazil", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TPA", "amazonas_node", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TUA", "brazil", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GUA", "laplata", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CUA", "laplata", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WKA", "lima", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CYA", "lima", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CLA", "cuiaba", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CRA", "cuiaba", 29.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PCJ", "cuiaba", 24.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ARW", "amazonas_node", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CAB", "carribean_trade", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ICM", "lima", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAT", "mexico", 21.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COI", "mexico", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TEO", "mexico", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("XAL", "mexico", 17.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GAM", "mexico", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HST", "mexico", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CCM", "mexico", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OTO", "mexico", 8.262);
INSERT INTO country(country_name,home_node,development) VALUES ("YOK", "mexico", 10.04);
INSERT INTO country(country_name,home_node,development) VALUES ("LAC", "mexico", 11.29);
INSERT INTO country(country_name,home_node,development) VALUES ("KAQ", "mexico", 9.52);
INSERT INTO country(country_name,home_node,development) VALUES ("CTM", "mexico", 19.048);
INSERT INTO country(country_name,home_node,development) VALUES ("KER", "rio_grande", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ZNI", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MSC", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LIP", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHT", "panama", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MIS", "panama", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAI", "panama", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CNP", "mexico", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TON", "mexico", 9.501);
INSERT INTO country(country_name,home_node,development) VALUES ("YAQ", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YKT", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("UBV", "wien", 24.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LBV", "wien", 30.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ING", "wien", 24.298);
INSERT INTO country(country_name,home_node,development) VALUES ("PSS", "wien", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MBZ", "wien", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KNZ", "wien", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ROT", "rheinland", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BYT", "rheinland", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("REG", "wien", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GNV", "champagne", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TTL", "rheinland", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OPL", "krakow", 29.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GLG", "krakow", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BLG", "venice", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SZO", "genua", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WOL", "lubeck", 27.94);
INSERT INTO country(country_name,home_node,development) VALUES ("STE", "lubeck", 21.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GOS", "saxony", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CLI", "venice", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HRZ", "ragusa", 14.864);
INSERT INTO country(country_name,home_node,development) VALUES ("TNT", "wien", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BRG", "rheinland", 21.415);
INSERT INTO country(country_name,home_node,development) VALUES ("MLH", "rheinland", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BAM", "rheinland", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PGA", "genua", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BNE", "the_moluccas", 17.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BEU", "malacca", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SMB", "malacca", 31.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BRS", "malacca", 17.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DLI", "malacca", 27.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JMB", "malacca", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PAH", "malacca", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KEL", "malacca", 10.975);
INSERT INTO country(country_name,home_node,development) VALUES ("IND", "malacca", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JAR", "gulf_of_siam", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RHA", "gulf_of_siam", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KOH", "gulf_of_siam", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TIW", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LAR", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YOL", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YNU", "australia", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AWN", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GMI", "australia", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MIA", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("EOR", "australia", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAU", "australia", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PLW", "australia", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WRU", "australia", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NOO", "australia", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLG", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAA", "polynesia_node", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAN", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAK", "polynesia_node", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TNK", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TEA", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TTT", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WAI", "polynesia_node", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HAW", "polynesia_node", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAU", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OAH", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAA", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TOG", "polynesia_node", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SAM", "polynesia_node", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("VIL", "polynesia_node", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("VNL", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LAI", "polynesia_node", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ALT", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ICH", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COF", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JOA", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ETO", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SAT", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CIA", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COO", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ABI", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COW", "mississippi_river", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NTZ", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CAQ", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PCH", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("QUI", "mississippi_river", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CCA", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ATA", "mississippi_river", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KSI", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OEO", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANL", "mississippi_river", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NTC", "mississippi_river", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HNI", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MOH", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ONE", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ONO", "ohio", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CAY", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SEN", "ohio", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAH", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ATT", "ohio", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AGG", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ATW", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ARN", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TIO", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OSH", "st_lawrence", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("STA", "st_lawrence", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ERI", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WEN", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TSC", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OHK", "rio_grande", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ISL", "rio_grande", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ACO", "rio_grande", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CAO", "ohio", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PEO", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KSK", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PEN", "st_lawrence", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLS", "st_lawrence", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NEH", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NAK", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HWK", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CLG", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KSP", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MSG", "ohio", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WCY", "mississippi_river", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LAK", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("INN", "st_lawrence", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WAM", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AGQ", "st_lawrence", 9.0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("african_great_lakes", 1.637, TRUE, 347.522, 0.21111488193553213, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("kongo", 2.875, TRUE, 606.7359999999998, 0.35759986032744384, 0.11083531301615437);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("zambezi", 1.974, TRUE, 416.1120000000001, 0.307502662743773, 0.18773992667190803);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("patagonia", 0.082, FALSE, 27.53, 0.014785615691972395, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("amazonas_node", 0.297, FALSE, 35.788, 0.0, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("rio_grande", 1.711, FALSE, 221.768, 0.13362847660618304, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("james_bay", 1.115, FALSE, 153.526, 0.05317685603741385, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("california", 1.184, FALSE, 174.86599999999996, 0.006327053803871296, 0.07468781623180634);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("girin", 4.45, FALSE, 638.3280000000001, 0.5007776735041073, 0.0016608516235162153);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("mississippi_river", 2.888, FALSE, 288.08000000000004, 0.031303037501443955, 0.048430818435680284);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ohio", 5.065, TRUE, 813.2559999999999, 0.2657384538781044, 0.016434094688258075);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("mexico", 8.985, FALSE, 692.52, 0.0482247353661363, 0.048430818435680284);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lhasa", 1.569, TRUE, 580.2039999999998, 0.4490710974071191, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("chengdu", 2.705, TRUE, 554.0659999999999, 1.189128163916911, 0.15717488409249167);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("burma", 6.637, FALSE, 712.75, 1.1124701686015872, 0.4161948573709188);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("gulf_of_siam", 10.956, FALSE, 816.6820000000005, 1.7511244823687961, 0.5840468385158333);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("canton", 5.411, FALSE, 670.2200000000003, 2.7017345908187145, 1.3355352106145368);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("philippines", 3.084, FALSE, 550.718, 0.2591903161757923, 0.9456071067865501);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("cuiaba", 1.183, FALSE, 176.138, 0.13543607591473547, 0.015524896476571015);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lima", 2.225, FALSE, 290.912, 0.022082561823898072, 0.04740262657015742);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("polynesia_node", 1.791, FALSE, 868.6760000000002, 0.0, 0.16620776995150136);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("australia", 2.045, FALSE, 333.57599999999996, 0.0, 0.0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("nippon", 8.027, FALSE, 1135.5399999999997, 0.28490605511176, 0.17527218572643757);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("hangzhou", 8.561, FALSE, 534.2459999999999, 4.90287923232695, 1.244758464653898);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("xian", 4.221, TRUE, 549.226, 3.176601294342676, 2.1322025886853515);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("beijing", 4.373, FALSE, 326.54400000000004, 0.4738637675103589, 3.5589955965707754);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("the_moluccas", 7.742, FALSE, 680.6840000000002, 0.5010166024739675, 0.13607491599229096);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("siberia", 1.481, TRUE, 257.976, 0.22686619668042532, 0.17527218572643757);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("yumen", 2.848, TRUE, 500.152, 0.4024434498151434, 2.1652726354157816);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("malacca", 10.652, FALSE, 1231.2240000000006, 0.6183516752706555, 4.1070226239422665);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ganges_delta", 8.306, FALSE, 1172.4080000000001, 1.6467463883528162, 1.0658563521254192);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("doab", 6.978, TRUE, 1243.2320000000002, 1.564903871845784, 0.8645418538852285);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lahore", 5.742, TRUE, 929.6479999999996, 1.5057648618408148, 0.9787494168115283);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("deccan", 8.906, TRUE, 1128.1840000000004, 2.1710250448223505, 0.8215745327190366);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("comorin_cape", 10.339, FALSE, 1268.8820000000005, 0.798208141772303, 2.0043300024169626);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("gujarat", 5.57, FALSE, 881.0209999999998, 1.0119811372811092, 1.9461786997963253);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("katsina", 3.697, TRUE, 674.194, 0.6802880521036972, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ethiopia", 2.512, TRUE, 616.1080000000001, 0.35567389710684544, 0.23810081823629406);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("gulf_of_aden", 5.534, FALSE, 937.5630000000001, 0.7232904530770788, 0.8202950436497882);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("hormuz", 3.583, FALSE, 392.6979999999999, 0.49269617441281366, 0.6073450566253658);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("zanzibar", 3.341, FALSE, 759.1819999999999, 0.008889001023344367, 1.041058165522482);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("cape_of_good_hope", 0.0, FALSE, 0.0, 0, 0.6133409302119117);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("basra", 3.202, FALSE, 671.3940000000001, 1.1302014443336117, 0.5173309831334544);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("samarkand", 4.509, TRUE, 677.8619999999999, 1.395512276124377, 1.068688077207409);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("persia", 6.085, TRUE, 1291.696, 1.0057966863626304, 1.8530174048847292);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("aleppo", 4.268, FALSE, 524.6519999999999, 1.4096308976779548, 1.1213990186155272);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("alexandria", 6.764, FALSE, 694.4360000000001, 1.8074035719766977, 1.1799366758389978);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("astrakhan", 2.908, TRUE, 696.7979999999999, 0.9392974233304527, 1.260687205305679);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("crimea", 4.205, FALSE, 816.0979999999998, 1.240370684791908, 0.4931311472484877);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("constantinople", 6.006, FALSE, 588.1240000000001, 0.528579696172331, 1.8067772111499383);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("kiev", 4.514, TRUE, 867.582, 1.5652862865462849, 0.43412973967716784);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("kazan", 2.813, TRUE, 309.832, 0.7593332937273595, 0.612235900505711);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("novgorod", 5.857, FALSE, 608.9419999999999, 0.7581511465127987, 1.619075258850527);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("laplata", 0.148, FALSE, 80.74, 0.0, 0.06292752304672844);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("brazil", 0.793, FALSE, 73.326, 0.0, 0.04740262657015742);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("timbuktu", 4.55, TRUE, 736.042, 0.2957784582993468, 0.23810081823629406);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ivory_coast", 1.876, FALSE, 322.01400000000007, 0.20656695656573082, 0.3430236172790651);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("tunis", 2.404, FALSE, 464.75399999999985, 0.7388893256402872, 0.23810081823629406);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ragusa", 4.88, FALSE, 958.096, 1.788256361131945, 0.5550086809809476);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("safi", 3.426, FALSE, 412.68200000000013, 0.46814721740588067, 0.15528369060715708);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("pest", 3.309, TRUE, 771.854, 1.1667092290622842, 1.0600194660733486);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("krakow", 4.777, TRUE, 1013.1360000000001, 1.6728708999506772, 1.4342976456944987);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("wien", 6.243, TRUE, 1654.5179999999998, 1.9649225021204768, 1.1980271602404362);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("saxony", 5.283, TRUE, 1227.2720000000006, 1.6157706450639817, 1.2732276907249038);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("baltic_sea", 5.665, FALSE, 616.1300000000001, 0.6469946465504153, 0.9835341669019564);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("rheinland", 7.552, TRUE, 1439.46, 1.5796643578879517, 1.5360024644007573);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("panama", 1.131, FALSE, 95.85, 0.0, 0.028472002335694194);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("carribean_trade", 0.099, FALSE, 16.998, 0.0, 0.08753657816491012);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("chesapeake_bay", 4.647, FALSE, 349.5319999999999, 0.13359926354931242, 0.13951268828600483);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("st_lawrence", 2.925, FALSE, 197.89, 0.0, 0.23757015106903612);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("white_sea", 1.476, FALSE, 117.41399999999999, 0.6434145096374161, 0.39802935191921934);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("north_sea", 4.317, FALSE, 1066.7179999999996, 0.9671373506398264, 0.6755852351192869);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lubeck", 5.818, FALSE, 1206.6480000000001, 0.5510622303390725, 2.86469486451361);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("bordeaux", 4.182, FALSE, 441.022, 0.9417395421506174, 0.05422382609850434);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("sevilla", 7.256, FALSE, 632.968, 0.35981972306477283, 0.8043896683487796);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("champagne", 7.497, TRUE, 1388.8780000000002, 1.571132396114675, 1.818150307149323);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("valencia", 3.217, FALSE, 288.9649999999999, 0.8367472481161577, 0.6364219731921119);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("genua", 9.344, FALSE, 736.0519999999998, 0.7226820608550747, 3.2205213590442954);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("venice", 6.346, FALSE, 519.232, 0.3577149984753327, 1.9462038523301919);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("english_channel", 9.62, FALSE, 1091.278, 0.12936416629400133, 2.0355703983640328);
INSERT INTO flow(source, dest, flow) VALUES ("african_great_lakes", "zanzibar", 0.10555744096776606);
INSERT INTO flow(source, dest, flow) VALUES ("african_great_lakes", "kongo", 0.10555744096776606);
INSERT INTO flow(source, dest, flow) VALUES ("kongo", "ivory_coast", 0.17879993016372192);
INSERT INTO flow(source, dest, flow) VALUES ("kongo", "zambezi", 0.17879993016372192);
INSERT INTO flow(source, dest, flow) VALUES ("zambezi", "zanzibar", 0.307502662743773);
INSERT INTO flow(source, dest, flow) VALUES ("patagonia", "laplata", 0.007392807845986197);
INSERT INTO flow(source, dest, flow) VALUES ("patagonia", "cuiaba", 0.007392807845986197);
INSERT INTO flow(source, dest, flow) VALUES ("amazonas_node", "brazil", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("amazonas_node", "carribean_trade", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("amazonas_node", "cuiaba", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("rio_grande", "mississippi_river", 0.04454282553539435);
INSERT INTO flow(source, dest, flow) VALUES ("rio_grande", "mexico", 0.04454282553539435);
INSERT INTO flow(source, dest, flow) VALUES ("rio_grande", "california", 0.04454282553539435);
INSERT INTO flow(source, dest, flow) VALUES ("james_bay", "st_lawrence", 0.026588428018706926);
INSERT INTO flow(source, dest, flow) VALUES ("james_bay", "california", 0.026588428018706926);
INSERT INTO flow(source, dest, flow) VALUES ("california", "mexico", 0.001581763450967824);
INSERT INTO flow(source, dest, flow) VALUES ("california", "mississippi_river", 0.001581763450967824);
INSERT INTO flow(source, dest, flow) VALUES ("california", "girin", 0.001581763450967824);
INSERT INTO flow(source, dest, flow) VALUES ("california", "polynesia_node", 0.001581763450967824);
INSERT INTO flow(source, dest, flow) VALUES ("girin", "siberia", 0.16692589116803577);
INSERT INTO flow(source, dest, flow) VALUES ("girin", "beijing", 0.16692589116803577);
INSERT INTO flow(source, dest, flow) VALUES ("girin", "nippon", 0.16692589116803577);
INSERT INTO flow(source, dest, flow) VALUES ("mississippi_river", "carribean_trade", 0.015651518750721977);
INSERT INTO flow(source, dest, flow) VALUES ("mississippi_river", "ohio", 0.015651518750721977);
INSERT INTO flow(source, dest, flow) VALUES ("ohio", "chesapeake_bay", 0.1328692269390522);
INSERT INTO flow(source, dest, flow) VALUES ("ohio", "st_lawrence", 0.1328692269390522);
INSERT INTO flow(source, dest, flow) VALUES ("mexico", "carribean_trade", 0.0160749117887121);
INSERT INTO flow(source, dest, flow) VALUES ("mexico", "panama", 0.0160749117887121);
INSERT INTO flow(source, dest, flow) VALUES ("mexico", "polynesia_node", 0.0160749117887121);
INSERT INTO flow(source, dest, flow) VALUES ("lhasa", "lahore", 0.14969036580237302);
INSERT INTO flow(source, dest, flow) VALUES ("lhasa", "chengdu", 0.14969036580237302);
INSERT INTO flow(source, dest, flow) VALUES ("lhasa", "ganges_delta", 0.14969036580237302);
INSERT INTO flow(source, dest, flow) VALUES ("chengdu", "canton", 0.3963760546389703);
INSERT INTO flow(source, dest, flow) VALUES ("chengdu", "xian", 0.3963760546389703);
INSERT INTO flow(source, dest, flow) VALUES ("chengdu", "burma", 0.3963760546389703);
INSERT INTO flow(source, dest, flow) VALUES ("burma", "ganges_delta", 0.5562350843007936);
INSERT INTO flow(source, dest, flow) VALUES ("burma", "gulf_of_siam", 0.5562350843007936);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_siam", "malacca", 0.8755622411843981);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_siam", "canton", 0.8755622411843981);
INSERT INTO flow(source, dest, flow) VALUES ("canton", "malacca", 0.9005781969395715);
INSERT INTO flow(source, dest, flow) VALUES ("canton", "hangzhou", 0.9005781969395715);
INSERT INTO flow(source, dest, flow) VALUES ("canton", "philippines", 0.9005781969395715);
INSERT INTO flow(source, dest, flow) VALUES ("philippines", "the_moluccas", 0.12959515808789615);
INSERT INTO flow(source, dest, flow) VALUES ("philippines", "polynesia_node", 0.12959515808789615);
INSERT INTO flow(source, dest, flow) VALUES ("cuiaba", "laplata", 0.045145358638245155);
INSERT INTO flow(source, dest, flow) VALUES ("cuiaba", "lima", 0.045145358638245155);
INSERT INTO flow(source, dest, flow) VALUES ("cuiaba", "brazil", 0.045145358638245155);
INSERT INTO flow(source, dest, flow) VALUES ("lima", "panama", 0.011041280911949036);
INSERT INTO flow(source, dest, flow) VALUES ("lima", "polynesia_node", 0.011041280911949036);
INSERT INTO flow(source, dest, flow) VALUES ("polynesia_node", "nippon", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("polynesia_node", "australia", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("polynesia_node", "panama", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("australia", "the_moluccas", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("nippon", "hangzhou", 0.28490605511176);
INSERT INTO flow(source, dest, flow) VALUES ("hangzhou", "xian", 1.6342930774423168);
INSERT INTO flow(source, dest, flow) VALUES ("hangzhou", "beijing", 1.6342930774423168);
INSERT INTO flow(source, dest, flow) VALUES ("hangzhou", "malacca", 1.6342930774423168);
INSERT INTO flow(source, dest, flow) VALUES ("xian", "beijing", 1.588300647171338);
INSERT INTO flow(source, dest, flow) VALUES ("xian", "yumen", 1.588300647171338);
INSERT INTO flow(source, dest, flow) VALUES ("beijing", "yumen", 0.4738637675103589);
INSERT INTO flow(source, dest, flow) VALUES ("the_moluccas", "malacca", 0.5010166024739675);
INSERT INTO flow(source, dest, flow) VALUES ("siberia", "kazan", 0.11343309834021266);
INSERT INTO flow(source, dest, flow) VALUES ("siberia", "samarkand", 0.11343309834021266);
INSERT INTO flow(source, dest, flow) VALUES ("yumen", "samarkand", 0.4024434498151434);
INSERT INTO flow(source, dest, flow) VALUES ("malacca", "ganges_delta", 0.30917583763532774);
INSERT INTO flow(source, dest, flow) VALUES ("malacca", "cape_of_good_hope", 0.30917583763532774);
INSERT INTO flow(source, dest, flow) VALUES ("ganges_delta", "comorin_cape", 0.8233731941764081);
INSERT INTO flow(source, dest, flow) VALUES ("ganges_delta", "doab", 0.8233731941764081);
INSERT INTO flow(source, dest, flow) VALUES ("doab", "deccan", 0.782451935922892);
INSERT INTO flow(source, dest, flow) VALUES ("doab", "lahore", 0.782451935922892);
INSERT INTO flow(source, dest, flow) VALUES ("lahore", "samarkand", 0.5019216206136049);
INSERT INTO flow(source, dest, flow) VALUES ("lahore", "persia", 0.5019216206136049);
INSERT INTO flow(source, dest, flow) VALUES ("lahore", "gujarat", 0.5019216206136049);
INSERT INTO flow(source, dest, flow) VALUES ("deccan", "gujarat", 1.0855125224111752);
INSERT INTO flow(source, dest, flow) VALUES ("deccan", "comorin_cape", 1.0855125224111752);
INSERT INTO flow(source, dest, flow) VALUES ("comorin_cape", "gulf_of_aden", 0.2660693805907677);
INSERT INTO flow(source, dest, flow) VALUES ("comorin_cape", "gujarat", 0.2660693805907677);
INSERT INTO flow(source, dest, flow) VALUES ("comorin_cape", "cape_of_good_hope", 0.2660693805907677);
INSERT INTO flow(source, dest, flow) VALUES ("gujarat", "gulf_of_aden", 0.3373270457603697);
INSERT INTO flow(source, dest, flow) VALUES ("gujarat", "hormuz", 0.3373270457603697);
INSERT INTO flow(source, dest, flow) VALUES ("gujarat", "zanzibar", 0.3373270457603697);
INSERT INTO flow(source, dest, flow) VALUES ("katsina", "timbuktu", 0.22676268403456576);
INSERT INTO flow(source, dest, flow) VALUES ("katsina", "tunis", 0.22676268403456576);
INSERT INTO flow(source, dest, flow) VALUES ("katsina", "ethiopia", 0.22676268403456576);
INSERT INTO flow(source, dest, flow) VALUES ("ethiopia", "alexandria", 0.17783694855342272);
INSERT INTO flow(source, dest, flow) VALUES ("ethiopia", "gulf_of_aden", 0.17783694855342272);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_aden", "zanzibar", 0.2410968176923596);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_aden", "alexandria", 0.2410968176923596);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_aden", "hormuz", 0.2410968176923596);
INSERT INTO flow(source, dest, flow) VALUES ("hormuz", "basra", 0.49269617441281366);
INSERT INTO flow(source, dest, flow) VALUES ("zanzibar", "cape_of_good_hope", 0.008889001023344367);
INSERT INTO flow(source, dest, flow) VALUES ("cape_of_good_hope", "ivory_coast", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("basra", "aleppo", 0.5651007221668058);
INSERT INTO flow(source, dest, flow) VALUES ("basra", "persia", 0.5651007221668058);
INSERT INTO flow(source, dest, flow) VALUES ("samarkand", "persia", 0.6977561380621885);
INSERT INTO flow(source, dest, flow) VALUES ("samarkand", "astrakhan", 0.6977561380621885);
INSERT INTO flow(source, dest, flow) VALUES ("persia", "aleppo", 0.5028983431813152);
INSERT INTO flow(source, dest, flow) VALUES ("persia", "astrakhan", 0.5028983431813152);
INSERT INTO flow(source, dest, flow) VALUES ("aleppo", "constantinople", 0.7048154488389774);
INSERT INTO flow(source, dest, flow) VALUES ("aleppo", "alexandria", 0.7048154488389774);
INSERT INTO flow(source, dest, flow) VALUES ("alexandria", "constantinople", 0.6024678573255658);
INSERT INTO flow(source, dest, flow) VALUES ("alexandria", "venice", 0.6024678573255658);
INSERT INTO flow(source, dest, flow) VALUES ("alexandria", "genua", 0.6024678573255658);
INSERT INTO flow(source, dest, flow) VALUES ("astrakhan", "kazan", 0.46964871166522637);
INSERT INTO flow(source, dest, flow) VALUES ("astrakhan", "crimea", 0.46964871166522637);
INSERT INTO flow(source, dest, flow) VALUES ("crimea", "kiev", 0.41345689493063603);
INSERT INTO flow(source, dest, flow) VALUES ("crimea", "constantinople", 0.41345689493063603);
INSERT INTO flow(source, dest, flow) VALUES ("crimea", "pest", 0.41345689493063603);
INSERT INTO flow(source, dest, flow) VALUES ("constantinople", "ragusa", 0.528579696172331);
INSERT INTO flow(source, dest, flow) VALUES ("kiev", "novgorod", 0.7826431432731424);
INSERT INTO flow(source, dest, flow) VALUES ("kiev", "krakow", 0.7826431432731424);
INSERT INTO flow(source, dest, flow) VALUES ("kazan", "novgorod", 0.7593332937273595);
INSERT INTO flow(source, dest, flow) VALUES ("novgorod", "baltic_sea", 0.37907557325639935);
INSERT INTO flow(source, dest, flow) VALUES ("novgorod", "white_sea", 0.37907557325639935);
INSERT INTO flow(source, dest, flow) VALUES ("laplata", "brazil", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("brazil", "ivory_coast", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("timbuktu", "safi", 0.1478892291496734);
INSERT INTO flow(source, dest, flow) VALUES ("timbuktu", "ivory_coast", 0.1478892291496734);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "carribean_trade", 0.051641739141432705);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "bordeaux", 0.051641739141432705);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "english_channel", 0.051641739141432705);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "sevilla", 0.051641739141432705);
INSERT INTO flow(source, dest, flow) VALUES ("tunis", "sevilla", 0.24629644188009572);
INSERT INTO flow(source, dest, flow) VALUES ("tunis", "valencia", 0.24629644188009572);
INSERT INTO flow(source, dest, flow) VALUES ("tunis", "genua", 0.24629644188009572);
INSERT INTO flow(source, dest, flow) VALUES ("ragusa", "pest", 0.5960854537106484);
INSERT INTO flow(source, dest, flow) VALUES ("ragusa", "venice", 0.5960854537106484);
INSERT INTO flow(source, dest, flow) VALUES ("ragusa", "genua", 0.5960854537106484);
INSERT INTO flow(source, dest, flow) VALUES ("safi", "sevilla", 0.46814721740588067);
INSERT INTO flow(source, dest, flow) VALUES ("pest", "wien", 0.5833546145311421);
INSERT INTO flow(source, dest, flow) VALUES ("pest", "krakow", 0.5833546145311421);
INSERT INTO flow(source, dest, flow) VALUES ("krakow", "wien", 0.5576236333168924);
INSERT INTO flow(source, dest, flow) VALUES ("krakow", "saxony", 0.5576236333168924);
INSERT INTO flow(source, dest, flow) VALUES ("krakow", "baltic_sea", 0.5576236333168924);
INSERT INTO flow(source, dest, flow) VALUES ("wien", "venice", 0.6549741673734922);
INSERT INTO flow(source, dest, flow) VALUES ("wien", "rheinland", 0.6549741673734922);
INSERT INTO flow(source, dest, flow) VALUES ("wien", "saxony", 0.6549741673734922);
INSERT INTO flow(source, dest, flow) VALUES ("saxony", "rheinland", 0.8078853225319909);
INSERT INTO flow(source, dest, flow) VALUES ("saxony", "lubeck", 0.8078853225319909);
INSERT INTO flow(source, dest, flow) VALUES ("baltic_sea", "lubeck", 0.6469946465504153);
INSERT INTO flow(source, dest, flow) VALUES ("rheinland", "champagne", 0.7898321789439758);
INSERT INTO flow(source, dest, flow) VALUES ("rheinland", "lubeck", 0.7898321789439758);
INSERT INTO flow(source, dest, flow) VALUES ("panama", "carribean_trade", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("carribean_trade", "chesapeake_bay", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("carribean_trade", "bordeaux", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("carribean_trade", "sevilla", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("chesapeake_bay", "st_lawrence", 0.06679963177465621);
INSERT INTO flow(source, dest, flow) VALUES ("chesapeake_bay", "english_channel", 0.06679963177465621);
INSERT INTO flow(source, dest, flow) VALUES ("st_lawrence", "north_sea", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("st_lawrence", "bordeaux", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("white_sea", "north_sea", 0.6434145096374161);
INSERT INTO flow(source, dest, flow) VALUES ("north_sea", "english_channel", 0.4835686753199132);
INSERT INTO flow(source, dest, flow) VALUES ("north_sea", "lubeck", 0.4835686753199132);
INSERT INTO flow(source, dest, flow) VALUES ("lubeck", "english_channel", 0.5510622303390725);
INSERT INTO flow(source, dest, flow) VALUES ("bordeaux", "champagne", 0.9417395421506174);
INSERT INTO flow(source, dest, flow) VALUES ("sevilla", "valencia", 0.35981972306477283);
INSERT INTO flow(source, dest, flow) VALUES ("champagne", "genua", 0.7855661980573375);
INSERT INTO flow(source, dest, flow) VALUES ("champagne", "english_channel", 0.7855661980573375);
INSERT INTO flow(source, dest, flow) VALUES ("valencia", "genua", 0.8367472481161577);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KON", FALSE, FALSE, 4.017, 4.017);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "LUB", FALSE, FALSE, 11.693, 11.693);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "LND", FALSE, FALSE, 2.183, 2.183);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KIK", FALSE, FALSE, 5.391, 5.391);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KZB", FALSE, FALSE, 9.081, 9.081);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KLD", FALSE, FALSE, 5.055, 5.055);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KUB", FALSE, FALSE, 7.398, 7.398);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "RWA", TRUE, FALSE, 17.51, 17.51);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BUU", TRUE, FALSE, 12.269, 12.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BUG", TRUE, FALSE, 26.164, 26.164);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "NKO", TRUE, FALSE, 11.713, 11.713);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KRW", TRUE, FALSE, 15.155, 15.155);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BNY", TRUE, FALSE, 15.194, 15.194);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BSG", TRUE, FALSE, 12.269, 12.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "UBH", TRUE, FALSE, 18.669, 18.669);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "BEN", FALSE, FALSE, 5.743, 5.743);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KON", TRUE, FALSE, 50.521, 50.521);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "ZIM", FALSE, FALSE, 23.349, 23.349);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "JOL", FALSE, FALSE, 4.16, 4.16);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "TYO", TRUE, FALSE, 10.513, 10.513);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KSJ", TRUE, FALSE, 16.318, 16.318);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "LUB", TRUE, FALSE, 28.404, 28.404);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "LND", TRUE, FALSE, 24.735, 24.735);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "CKW", TRUE, FALSE, 12.269, 12.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KIK", TRUE, FALSE, 14.146, 14.146);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KZB", TRUE, FALSE, 21.115, 21.115);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "YAK", TRUE, FALSE, 14.583, 14.583);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KLD", TRUE, FALSE, 13.509, 13.509);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KUB", TRUE, FALSE, 24.589, 24.589);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "MRA", FALSE, FALSE, 13.645, 13.645);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "LDU", FALSE, FALSE, 6.377, 6.377);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "TBK", FALSE, FALSE, 5.716, 5.716);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "RZW", FALSE, FALSE, 5.958, 5.958);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "NDO", FALSE, FALSE, 7.718, 7.718);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "ZAN", FALSE, FALSE, 23.604, 23.604);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "ZIM", TRUE, FALSE, 56.972, 56.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "SFA", FALSE, FALSE, 12.522, 12.522);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "MBA", FALSE, FALSE, 3.908, 3.908);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "MLI", FALSE, FALSE, 2.386, 2.386);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "MRA", TRUE, FALSE, 35.559, 35.559);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "LDU", TRUE, FALSE, 15.786, 15.786);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "TBK", TRUE, FALSE, 14.756, 14.756);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "MKU", TRUE, FALSE, 10.948, 10.948);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "RZW", TRUE, FALSE, 14.844, 14.844);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "SKA", FALSE, FALSE, 14.361, 14.361);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "BTS", FALSE, FALSE, 2.41, 2.41);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("patagonia", "MPC", TRUE, FALSE, 8.801, 8.801);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("patagonia", "CUA", FALSE, FALSE, 4.964, 4.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "TPA", TRUE, FALSE, 9.093, 9.093);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "ARW", TRUE, FALSE, 8.801, 8.801);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "AZT", FALSE, FALSE, 3.706, 3.706);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "NAH", TRUE, FALSE, 8.758, 8.758);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "CNK", FALSE, FALSE, 3.628, 3.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "TLA", FALSE, FALSE, 3.01, 3.01);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "TOT", FALSE, FALSE, 3.811, 3.811);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "WIC", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "XAL", FALSE, FALSE, 2.286, 2.286);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "HST", FALSE, FALSE, 0.879, 0.879);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "KER", TRUE, FALSE, 9.018, 9.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "ZNI", TRUE, FALSE, 8.532, 8.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "MSC", TRUE, FALSE, 8.532, 8.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "LIP", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "HNI", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "OHK", TRUE, FALSE, 15.054, 15.054);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "ISL", TRUE, FALSE, 9.107, 9.107);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "ACO", TRUE, FALSE, 9.018, 9.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "ASI", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "BLA", TRUE, FALSE, 8.619, 8.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "CHY", TRUE, FALSE, 9.26, 9.26);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "WCR", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "ARP", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "CNK", FALSE, FALSE, 3.628, 3.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "KIO", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "STA", FALSE, FALSE, 3.694, 3.694);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "NEH", TRUE, FALSE, 8.499, 8.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "NAK", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "APA", TRUE, FALSE, 8.532, 8.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "PIM", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "SHO", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "CNK", TRUE, FALSE, 25.204, 25.204);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "HDA", TRUE, FALSE, 8.801, 8.801);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "SAL", TRUE, FALSE, 9.565, 9.565);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "XAL", FALSE, FALSE, 0.879, 0.879);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "YAQ", TRUE, FALSE, 8.801, 8.801);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "YKT", TRUE, FALSE, 8.499, 8.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "SHY", FALSE, FALSE, 6.384, 6.384);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "AMA", FALSE, FALSE, 1.994, 1.994);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "HSK", FALSE, FALSE, 5.793, 5.793);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "OUC", FALSE, FALSE, 7.835, 7.835);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "UES", FALSE, FALSE, 4.729, 4.729);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "YMN", FALSE, FALSE, 1.994, 1.994);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "ANU", FALSE, FALSE, 0.886, 0.886);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "KHA", FALSE, FALSE, 4.566, 4.566);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "KOR", FALSE, FALSE, 15.045, 15.045);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "MNG", FALSE, FALSE, 20.587, 20.587);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "SOO", FALSE, FALSE, 1.994, 1.994);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "NVK", TRUE, FALSE, 33.787, 33.787);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "SOL", TRUE, FALSE, 17.864, 17.864);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "EJZ", TRUE, FALSE, 11.811, 11.811);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "NHX", TRUE, FALSE, 18.464, 18.464);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "MYR", TRUE, FALSE, 16.297, 16.297);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "MHX", TRUE, FALSE, 29.159, 29.159);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "MJZ", TRUE, FALSE, 37.326, 37.326);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "KRC", TRUE, FALSE, 18.057, 18.057);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "CHU", TRUE, FALSE, 12.095, 12.095);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "HOD", TRUE, FALSE, 7.927, 7.927);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "CHV", TRUE, FALSE, 22.285, 22.285);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "KMC", TRUE, FALSE, 22.285, 22.285);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CAD", TRUE, FALSE, 8.532, 8.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CHI", TRUE, FALSE, 9.05, 9.05);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CHO", TRUE, FALSE, 9.018, 9.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "COM", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "FOX", FALSE, FALSE, 3.071, 3.071);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "PAW", TRUE, FALSE, 8.74, 8.74);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "ABI", TRUE, FALSE, 8.654, 8.654);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "COW", TRUE, FALSE, 9.26, 9.26);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "NTZ", TRUE, FALSE, 8.532, 8.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CAQ", TRUE, FALSE, 8.414, 8.414);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "PCH", TRUE, FALSE, 8.532, 8.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "QUI", TRUE, FALSE, 9.018, 9.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "ATA", TRUE, FALSE, 8.893, 8.893);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "ANL", TRUE, FALSE, 9.018, 9.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "NTC", TRUE, FALSE, 9.018, 9.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "WCY", TRUE, FALSE, 9.26, 9.26);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "LAK", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CHE", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "FOX", TRUE, FALSE, 25.215, 25.215);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "LEN", FALSE, FALSE, 3.049, 3.049);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "MMI", TRUE, FALSE, 10.676, 10.676);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "OJI", TRUE, FALSE, 12.599, 12.599);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "OSA", TRUE, FALSE, 10.554, 10.554);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "OTT", TRUE, FALSE, 10.655, 10.655);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "POT", TRUE, FALSE, 11.253, 11.253);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "SIO", TRUE, FALSE, 10.655, 10.655);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "SUS", TRUE, FALSE, 10.554, 10.554);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ALT", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "COF", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "JOA", FALSE, FALSE, 4.787, 4.787);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ETO", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "SAT", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CIA", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "COO", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CCA", TRUE, FALSE, 10.676, 10.676);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "OEO", TRUE, FALSE, 12.475, 12.475);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "MOH", TRUE, FALSE, 12.475, 12.475);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ONE", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ONO", TRUE, FALSE, 13.674, 13.674);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CAY", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "SEN", TRUE, FALSE, 11.275, 11.275);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "TAH", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ATT", TRUE, FALSE, 13.674, 13.674);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "AGG", TRUE, FALSE, 12.293, 12.293);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ATW", TRUE, FALSE, 12.302, 12.302);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ARN", TRUE, FALSE, 11.851, 11.851);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "TIO", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "STA", FALSE, FALSE, 3.694, 3.694);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ERI", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "WEN", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CAO", TRUE, FALSE, 12.847, 12.847);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "PEO", TRUE, FALSE, 10.533, 10.533);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "KSK", TRUE, FALSE, 12.11, 12.11);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "HWK", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CLG", TRUE, FALSE, 10.676, 10.676);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "KSP", TRUE, FALSE, 10.676, 10.676);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "MSG", TRUE, FALSE, 11.275, 11.275);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "AZT", TRUE, FALSE, 25.609, 25.609);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "ZAP", TRUE, FALSE, 13.019, 13.019);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "CLM", TRUE, FALSE, 15.722, 15.722);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "COC", TRUE, FALSE, 13.697, 13.697);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "ITZ", TRUE, FALSE, 10.716, 10.716);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "KIC", TRUE, FALSE, 11.898, 11.898);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "MIX", TRUE, FALSE, 10.336, 10.336);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "TAR", TRUE, FALSE, 14.356, 14.356);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "TLA", TRUE, FALSE, 22.203, 22.203);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "TLX", TRUE, FALSE, 13.628, 13.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "TOT", TRUE, FALSE, 26.134, 26.134);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "XIU", TRUE, FALSE, 13.567, 13.567);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "MCA", FALSE, FALSE, 2.827, 2.827);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "MAT", TRUE, FALSE, 12.172, 12.172);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "COI", TRUE, FALSE, 10.757, 10.757);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "TEO", TRUE, FALSE, 10.13, 10.13);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "XAL", TRUE, FALSE, 18.511, 18.511);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "GAM", TRUE, FALSE, 8.532, 8.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "HST", TRUE, FALSE, 7.956, 7.956);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "CCM", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "OTO", TRUE, FALSE, 9.169, 9.169);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "YOK", TRUE, FALSE, 10.849, 10.849);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "LAC", TRUE, FALSE, 10.263, 10.263);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "KAQ", TRUE, FALSE, 10.33, 10.33);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "CTM", TRUE, FALSE, 12.943, 12.943);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "CHT", FALSE, FALSE, 0.87, 0.87);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "CNP", TRUE, FALSE, 11.651, 11.651);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "TON", TRUE, FALSE, 9.9, 9.9);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "AFG", FALSE, FALSE, 5.8, 5.8);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "ARK", FALSE, FALSE, 2.638, 2.638);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MNG", FALSE, FALSE, 12.085, 12.085);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "SYG", TRUE, FALSE, 9.252, 9.252);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "UTS", TRUE, FALSE, 40.032, 40.032);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "KAM", FALSE, FALSE, 16.353, 16.353);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "GUG", TRUE, FALSE, 13.263, 13.263);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "PHA", TRUE, FALSE, 14.687, 14.687);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "BNG", FALSE, FALSE, 27.728, 27.728);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "JNP", FALSE, FALSE, 4.798, 4.798);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MUL", FALSE, FALSE, 3.039, 3.039);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "ORI", FALSE, FALSE, 3.968, 3.968);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "KMT", FALSE, FALSE, 7.084, 7.084);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "TRT", FALSE, FALSE, 2.337, 2.337);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MBL", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "GRK", TRUE, FALSE, 14.611, 14.611);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "LWA", TRUE, FALSE, 14.661, 14.661);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "KTU", TRUE, FALSE, 17.533, 17.533);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "SRH", FALSE, FALSE, 8.889, 8.889);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "AVA", FALSE, FALSE, 20.084, 20.084);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "HSE", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MYA", FALSE, FALSE, 8.424, 8.424);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MMA", FALSE, FALSE, 6.066, 6.066);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MKA", FALSE, FALSE, 7.098, 7.098);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MNI", FALSE, FALSE, 5.716, 5.716);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "KAL", FALSE, FALSE, 6.108, 6.108);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "HSI", FALSE, FALSE, 6.739, 6.739);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "DAI", FALSE, FALSE, 9.932, 9.932);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "LXA", FALSE, FALSE, 36.224, 36.224);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "MNG", FALSE, FALSE, 106.551, 106.551);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "PEG", FALSE, FALSE, 7.647, 7.647);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "KAM", TRUE, FALSE, 31.419, 31.419);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "AVA", FALSE, FALSE, 25.325, 25.325);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "HSE", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "MPH", FALSE, FALSE, 4.028, 4.028);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "MYA", FALSE, FALSE, 8.424, 8.424);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "MMA", TRUE, FALSE, 15.42, 15.42);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "MKA", FALSE, FALSE, 7.098, 7.098);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "MNI", FALSE, FALSE, 5.716, 5.716);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "KAL", FALSE, FALSE, 6.108, 6.108);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "HSI", FALSE, FALSE, 6.739, 6.739);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "ARK", FALSE, FALSE, 4.66, 4.66);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "AYU", FALSE, FALSE, 11.8, 11.8);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "CHA", FALSE, FALSE, 3.808, 3.808);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KHM", FALSE, FALSE, 11.86, 11.86);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "LNA", FALSE, FALSE, 4.903, 4.903);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "LXA", FALSE, FALSE, 8.033, 8.033);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "PEG", TRUE, FALSE, 49.357, 49.357);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "SUK", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "TAU", TRUE, FALSE, 10.225, 10.225);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "BNG", FALSE, FALSE, 30.787, 30.787);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "ASS", TRUE, FALSE, 16.054, 16.054);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "JNP", FALSE, FALSE, 4.774, 4.774);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "ORI", FALSE, FALSE, 5.99, 5.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "GRJ", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KAC", TRUE, FALSE, 9.26, 9.26);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KMT", FALSE, FALSE, 3.409, 3.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MLB", TRUE, FALSE, 8.29, 8.29);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "NGP", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "TRT", FALSE, FALSE, 4.359, 4.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "TPR", TRUE, FALSE, 7.789, 7.789);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "SDY", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MBL", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "PTT", FALSE, FALSE, 2.048, 2.048);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KJH", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "PRD", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KTU", FALSE, FALSE, 1.994, 1.994);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "AVA", TRUE, FALSE, 39.809, 39.809);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "HSE", TRUE, FALSE, 12.226, 12.226);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MPH", FALSE, FALSE, 2.014, 2.014);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MYA", TRUE, FALSE, 13.97, 13.97);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MKA", TRUE, FALSE, 12.782, 12.782);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MPA", TRUE, FALSE, 9.716, 9.716);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MNI", TRUE, FALSE, 11.654, 11.654);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KAL", TRUE, FALSE, 11.98, 11.98);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "HSI", TRUE, FALSE, 12.545, 12.545);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "BPR", TRUE, FALSE, 9.784, 9.784);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "ARK", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "ATJ", FALSE, FALSE, 4.543, 4.543);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "AYU", TRUE, FALSE, 66.019, 66.019);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "BEI", FALSE, FALSE, 8.852, 8.852);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "CHA", TRUE, FALSE, 29.989, 29.989);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "DAI", FALSE, FALSE, 14.645, 14.645);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "KHM", TRUE, FALSE, 66.35, 66.35);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "LNA", TRUE, FALSE, 23.621, 23.621);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "LXA", TRUE, FALSE, 44.639, 44.639);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "MLC", FALSE, FALSE, 9.362, 9.362);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "MNG", FALSE, FALSE, 29.712, 29.712);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "PAT", FALSE, FALSE, 4.608, 4.608);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "SUK", TRUE, FALSE, 17.822, 17.822);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "SUL", FALSE, FALSE, 1.974, 1.974);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "KED", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "LIG", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "MPH", TRUE, FALSE, 10.187, 10.187);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "PGR", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "PLB", FALSE, FALSE, 5.823, 5.823);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "PSA", FALSE, FALSE, 7.123, 7.123);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "SAK", FALSE, FALSE, 5.0, 5.0);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "MGD", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "MAS", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "TDO", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "MNA", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "CEB", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "SMB", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "BRS", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "DLI", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "JMB", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "PAH", FALSE, FALSE, 2.002, 2.002);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "KEL", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "IND", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "JAR", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "RHA", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "KOH", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "ATJ", FALSE, FALSE, 2.521, 2.521);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "BEI", FALSE, FALSE, 6.83, 6.83);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "DAI", TRUE, FALSE, 66.712, 66.712);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "MLC", FALSE, FALSE, 9.362, 9.362);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "MNG", FALSE, FALSE, 188.011, 188.011);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "PAT", FALSE, FALSE, 4.608, 4.608);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "SUL", FALSE, FALSE, 4.034, 4.034);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "LIG", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "PLB", FALSE, FALSE, 3.501, 3.501);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "PSA", FALSE, FALSE, 5.101, 5.101);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "SAK", FALSE, FALSE, 2.978, 2.978);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "KUT", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "BNJ", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "MGD", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "MAS", FALSE, FALSE, 4.293, 4.293);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "PGS", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "TDO", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "MNA", FALSE, FALSE, 5.646, 5.646);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "CEB", FALSE, FALSE, 5.295, 5.295);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "BTU", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "BEU", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "SMB", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "DLI", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "JMB", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "PAH", FALSE, FALSE, 2.002, 2.002);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "KEL", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BEI", FALSE, FALSE, 2.288, 2.288);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "MAJ", FALSE, FALSE, 8.524, 8.524);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "MKS", FALSE, FALSE, 6.243, 6.243);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "SUL", TRUE, FALSE, 30.197, 30.197);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BTN", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "SUN", FALSE, FALSE, 8.25, 8.25);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "LUW", FALSE, FALSE, 2.042, 2.042);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "MGD", TRUE, FALSE, 27.934, 27.934);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "TER", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "TID", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "MAS", TRUE, FALSE, 24.508, 24.508);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "PGS", TRUE, FALSE, 26.604, 26.604);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "TDO", TRUE, FALSE, 28.866, 28.866);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "MNA", TRUE, FALSE, 39.872, 39.872);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "CEB", TRUE, FALSE, 35.351, 35.351);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BTU", TRUE, FALSE, 26.604, 26.604);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BNE", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "CHM", FALSE, FALSE, 2.873, 2.873);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "CSU", FALSE, FALSE, 2.824, 2.824);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "CCQ", TRUE, FALSE, 17.192, 17.192);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "QTO", FALSE, FALSE, 5.963, 5.963);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "CUA", FALSE, FALSE, 4.964, 4.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "CLA", TRUE, FALSE, 24.014, 24.014);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "CRA", TRUE, FALSE, 14.085, 14.085);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "PCJ", TRUE, FALSE, 12.874, 12.874);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "ICM", FALSE, FALSE, 3.28, 3.28);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "CHM", TRUE, FALSE, 21.443, 21.443);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "CSU", TRUE, FALSE, 21.258, 21.258);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "MCA", FALSE, FALSE, 2.827, 2.827);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "QTO", TRUE, FALSE, 36.878, 36.878);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "CJA", TRUE, FALSE, 10.959, 10.959);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "HJA", TRUE, FALSE, 10.231, 10.231);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "WKA", TRUE, FALSE, 9.746, 9.746);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "CYA", TRUE, FALSE, 8.642, 8.642);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "ICM", TRUE, FALSE, 23.472, 23.472);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "MAA", TRUE, FALSE, 25.598, 25.598);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TAN", TRUE, FALSE, 26.184, 26.184);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TAK", TRUE, FALSE, 25.305, 25.305);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TNK", TRUE, FALSE, 26.184, 26.184);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TEA", TRUE, FALSE, 26.184, 26.184);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TTT", TRUE, FALSE, 26.133, 26.133);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "WAI", TRUE, FALSE, 22.359, 22.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "HAW", TRUE, FALSE, 29.291, 29.291);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "MAU", TRUE, FALSE, 28.706, 28.706);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "OAH", TRUE, FALSE, 36.021, 36.021);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "KAA", TRUE, FALSE, 28.706, 28.706);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TOG", TRUE, FALSE, 36.825, 36.825);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "SAM", TRUE, FALSE, 29.525, 29.525);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "VIL", TRUE, FALSE, 22.914, 22.914);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "VNL", TRUE, FALSE, 22.337, 22.337);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "LAI", TRUE, FALSE, 22.066, 22.066);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "TIW", TRUE, FALSE, 9.403, 9.403);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "LAR", TRUE, FALSE, 19.108, 19.108);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "YOL", TRUE, FALSE, 9.273, 9.273);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "YNU", TRUE, FALSE, 8.818, 8.818);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "AWN", TRUE, FALSE, 9.403, 9.403);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "GMI", TRUE, FALSE, 10.211, 10.211);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "MIA", TRUE, FALSE, 9.385, 9.385);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "EOR", TRUE, FALSE, 18.474, 18.474);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "KAU", TRUE, FALSE, 17.304, 17.304);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "PLW", TRUE, FALSE, 9.696, 9.696);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "WRU", TRUE, FALSE, 18.474, 18.474);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "NOO", TRUE, FALSE, 17.854, 17.854);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "MLG", TRUE, FALSE, 9.385, 9.385);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "AMA", TRUE, FALSE, 12.418, 12.418);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "DTE", TRUE, FALSE, 15.563, 15.563);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "HSK", TRUE, FALSE, 28.585, 28.585);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "HTK", TRUE, FALSE, 11.504, 11.504);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "IMG", TRUE, FALSE, 10.159, 10.159);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "ODA", TRUE, FALSE, 10.069, 10.069);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "OTM", TRUE, FALSE, 11.575, 11.575);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "OUC", TRUE, FALSE, 42.01, 42.01);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "SBA", TRUE, FALSE, 11.605, 11.605);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "SMZ", TRUE, FALSE, 11.575, 11.575);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "TKD", TRUE, FALSE, 9.132, 9.132);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "TKG", TRUE, FALSE, 9.002, 9.002);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "UES", TRUE, FALSE, 30.627, 30.627);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "YMN", TRUE, FALSE, 16.833, 16.833);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "RFR", TRUE, FALSE, 15.274, 15.274);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "ASK", TRUE, FALSE, 24.367, 24.367);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "KTB", TRUE, FALSE, 10.707, 10.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "ANU", TRUE, FALSE, 12.717, 12.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "AKT", TRUE, FALSE, 11.575, 11.575);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "CBA", TRUE, FALSE, 10.042, 10.042);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "ISK", TRUE, FALSE, 10.996, 10.996);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "ITO", TRUE, FALSE, 10.996, 10.996);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "KKC", TRUE, FALSE, 11.286, 11.286);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "KNO", TRUE, FALSE, 12.099, 12.099);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "OGS", TRUE, FALSE, 8.414, 8.414);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "SHN", TRUE, FALSE, 11.575, 11.575);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "STK", TRUE, FALSE, 10.707, 10.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "TKI", TRUE, FALSE, 9.611, 9.611);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "UTN", TRUE, FALSE, 8.414, 8.414);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "TTI", TRUE, FALSE, 9.132, 9.132);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "KOR", TRUE, FALSE, 86.076, 86.076);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "MNG", FALSE, FALSE, 39.443, 39.443);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "RYU", TRUE, FALSE, 12.537, 12.537);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "SOO", TRUE, FALSE, 11.145, 11.145);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "ATJ", FALSE, FALSE, 2.521, 2.521);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "BEI", FALSE, FALSE, 6.83, 6.83);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "MLC", FALSE, FALSE, 7.14, 7.14);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "MNG", FALSE, FALSE, 236.466, 236.466);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "PAT", FALSE, FALSE, 2.586, 2.586);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "PLB", FALSE, FALSE, 3.501, 3.501);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "PSA", FALSE, FALSE, 5.101, 5.101);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "SAK", FALSE, FALSE, 2.978, 2.978);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("xian", "KHA", FALSE, FALSE, 36.623, 36.623);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("xian", "MNG", FALSE, FALSE, 161.183, 161.183);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("xian", "OIR", FALSE, FALSE, 49.517, 49.517);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("xian", "KRC", FALSE, FALSE, 21.653, 21.653);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("xian", "HMI", FALSE, FALSE, 2.92, 2.92);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("xian", "KAS", FALSE, FALSE, 2.717, 2.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("beijing", "KHA", FALSE, FALSE, 6.82, 6.82);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("beijing", "MNG", TRUE, FALSE, 143.764, 143.764);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("beijing", "OIR", FALSE, FALSE, 5.232, 5.232);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("beijing", "KRC", FALSE, FALSE, 1.819, 1.819);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("beijing", "HMI", FALSE, FALSE, 2.92, 2.92);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("beijing", "KAS", FALSE, FALSE, 2.717, 2.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "ATJ", FALSE, FALSE, 2.521, 2.521);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BLI", TRUE, FALSE, 13.582, 13.582);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BEI", FALSE, FALSE, 8.852, 8.852);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "MAJ", TRUE, FALSE, 50.177, 50.177);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "MKS", TRUE, FALSE, 44.306, 44.306);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "MLC", FALSE, FALSE, 9.362, 9.362);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "PAT", FALSE, FALSE, 2.586, 2.586);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BLM", TRUE, FALSE, 14.317, 14.317);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BTN", TRUE, FALSE, 23.643, 23.643);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "PLB", FALSE, FALSE, 5.823, 5.823);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "PSA", FALSE, FALSE, 5.101, 5.101);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "SAK", FALSE, FALSE, 5.0, 5.0);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "SUN", TRUE, FALSE, 52.262, 52.262);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "KUT", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "LUW", TRUE, FALSE, 29.392, 29.392);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "TER", TRUE, FALSE, 21.392, 21.392);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "TID", TRUE, FALSE, 19.174, 19.174);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BNE", TRUE, FALSE, 28.808, 28.808);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BEU", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "KAZ", FALSE, FALSE, 6.945, 6.945);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "MOS", FALSE, FALSE, 4.858, 4.858);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "SHY", TRUE, FALSE, 93.652, 93.652);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "TRS", FALSE, FALSE, 11.046, 11.046);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "CHG", FALSE, FALSE, 2.207, 2.207);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "OIR", FALSE, FALSE, 5.478, 5.478);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "KAS", FALSE, FALSE, 4.802, 4.802);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "SHY", FALSE, FALSE, 4.102, 4.102);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "TRS", FALSE, FALSE, 11.046, 11.046);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "CHG", TRUE, FALSE, 46.06, 46.06);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "KHA", TRUE, FALSE, 57.929, 57.929);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "MNG", FALSE, FALSE, 6.611, 6.611);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "OIR", TRUE, FALSE, 79.901, 79.901);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "HMI", TRUE, FALSE, 26.036, 26.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "KAS", FALSE, FALSE, 18.391, 18.391);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "ARK", FALSE, FALSE, 6.682, 6.682);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "ATJ", TRUE, FALSE, 26.075, 26.075);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BEI", TRUE, FALSE, 54.228, 54.228);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "MLC", TRUE, FALSE, 91.584, 91.584);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PAT", TRUE, FALSE, 33.658, 33.658);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BNG", FALSE, FALSE, 31.796, 31.796);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "JNP", FALSE, FALSE, 4.779, 4.779);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "ORI", FALSE, FALSE, 5.99, 5.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "TRT", FALSE, FALSE, 2.337, 2.337);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "KED", TRUE, FALSE, 21.497, 21.497);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "LIG", TRUE, FALSE, 20.426, 20.426);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PGR", TRUE, FALSE, 18.293, 18.293);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PLB", TRUE, FALSE, 32.772, 32.772);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PSA", TRUE, FALSE, 40.251, 40.251);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "SAK", TRUE, FALSE, 28.737, 28.737);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "KUT", TRUE, FALSE, 24.207, 24.207);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BNJ", TRUE, FALSE, 22.564, 22.564);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BEU", TRUE, FALSE, 24.873, 24.873);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "SMB", TRUE, FALSE, 29.257, 29.257);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BRS", TRUE, FALSE, 17.698, 17.698);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "DLI", TRUE, FALSE, 20.381, 20.381);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "JMB", TRUE, FALSE, 12.691, 12.691);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PAH", TRUE, FALSE, 13.991, 13.991);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "KEL", TRUE, FALSE, 14.758, 14.758);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "IND", TRUE, FALSE, 16.087, 16.087);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "ARK", TRUE, FALSE, 42.017, 42.017);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "BNG", TRUE, FALSE, 169.032, 169.032);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "DLH", FALSE, FALSE, 5.329, 5.329);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "VIJ", FALSE, FALSE, 27.764, 27.764);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "JNP", FALSE, FALSE, 76.584, 76.584);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MAD", FALSE, FALSE, 2.02, 2.02);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "ORI", TRUE, FALSE, 37.176, 37.176);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "GDW", FALSE, FALSE, 9.027, 9.027);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "GRJ", TRUE, FALSE, 8.962, 8.962);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "GWA", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "VND", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MAB", FALSE, FALSE, 5.311, 5.311);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "BST", TRUE, FALSE, 8.775, 8.775);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "BND", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "CEY", FALSE, FALSE, 4.592, 4.592);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KMT", TRUE, FALSE, 11.101, 11.101);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KOC", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "BGL", FALSE, FALSE, 5.295, 5.295);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "JFN", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "CHD", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "NGP", TRUE, FALSE, 10.158, 10.158);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "TRT", TRUE, FALSE, 20.62, 20.62);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "YOR", FALSE, FALSE, 11.209, 11.209);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "DGL", FALSE, FALSE, 2.014, 2.014);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MBL", TRUE, FALSE, 10.784, 10.784);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "PAN", FALSE, FALSE, 4.028, 4.028);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KLP", FALSE, FALSE, 7.612, 7.612);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "PTT", TRUE, FALSE, 18.846, 18.846);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "RTT", TRUE, FALSE, 10.136, 10.136);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KLH", TRUE, FALSE, 7.789, 7.789);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KJH", TRUE, FALSE, 8.7, 8.7);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "PRD", TRUE, FALSE, 8.437, 8.437);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "JPR", TRUE, FALSE, 7.665, 7.665);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KND", FALSE, FALSE, 4.006, 4.006);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "TLG", FALSE, FALSE, 6.619, 6.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KLT", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "DNG", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "DTI", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "JML", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KTU", FALSE, FALSE, 1.713, 1.713);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "AFG", FALSE, FALSE, 23.342, 23.342);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "BAH", FALSE, FALSE, 70.621, 70.621);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "DLH", TRUE, FALSE, 53.192, 53.192);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "JNP", TRUE, FALSE, 143.732, 143.732);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "MLW", FALSE, FALSE, 28.271, 28.271);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "MER", FALSE, FALSE, 1.299, 1.299);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "MUL", FALSE, FALSE, 17.793, 17.793);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "GDW", FALSE, FALSE, 9.989, 9.989);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "GWA", TRUE, FALSE, 13.026, 13.026);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "DHU", TRUE, FALSE, 21.553, 21.553);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "KSH", FALSE, FALSE, 8.407, 8.407);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "KHD", FALSE, FALSE, 14.18, 14.18);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "MEW", TRUE, FALSE, 12.775, 12.775);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "BND", TRUE, FALSE, 11.148, 11.148);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "KGR", TRUE, FALSE, 10.291, 10.291);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "HAD", TRUE, FALSE, 12.643, 12.643);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "LDK", FALSE, FALSE, 5.06, 5.06);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "BGL", TRUE, FALSE, 13.917, 13.917);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "GHR", TRUE, FALSE, 9.807, 9.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "CHD", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "NGP", FALSE, FALSE, 0.712, 0.712);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "CMP", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "IDR", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "JLV", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "PAN", TRUE, FALSE, 0.243, 0.243);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "KLP", TRUE, FALSE, 18.178, 18.178);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "TLG", FALSE, FALSE, 6.619, 6.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "DNG", TRUE, FALSE, 11.311, 11.311);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "DTI", TRUE, FALSE, 10.779, 10.779);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "JML", TRUE, FALSE, 11.332, 11.332);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "SRM", TRUE, FALSE, 9.807, 9.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "KMN", TRUE, FALSE, 9.807, 9.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "SRH", FALSE, FALSE, 45.63, 45.63);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "SRV", FALSE, FALSE, 4.153, 4.153);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "SHY", FALSE, FALSE, 4.102, 4.102);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "AFG", TRUE, FALSE, 61.883, 61.883);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "QAR", FALSE, FALSE, 6.887, 6.887);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "TIM", FALSE, FALSE, 18.531, 18.531);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "TRS", FALSE, FALSE, 55.068, 55.068);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "TAB", FALSE, FALSE, 4.329, 4.329);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "QOM", FALSE, FALSE, 8.909, 8.909);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "CHG", FALSE, FALSE, 2.207, 2.207);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "KAS", FALSE, FALSE, 25.445, 25.445);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "BAL", TRUE, FALSE, 25.415, 25.415);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "DLH", FALSE, FALSE, 1.808, 1.808);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "VIJ", FALSE, FALSE, 2.716, 2.716);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "GUJ", FALSE, FALSE, 16.634, 16.634);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "MER", FALSE, FALSE, 24.932, 24.932);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "MUL", TRUE, FALSE, 38.635, 38.635);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "SND", FALSE, FALSE, 7.06, 7.06);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "JAN", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "KSH", TRUE, FALSE, 22.297, 22.297);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "JSL", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "NGA", FALSE, FALSE, 5.38, 5.38);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "LDK", TRUE, FALSE, 13.481, 13.481);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "BGA", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "IDR", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "JLV", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "SRH", TRUE, FALSE, 94.828, 94.828);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "BAH", TRUE, FALSE, 160.085, 160.085);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MYS", FALSE, FALSE, 8.366, 8.366);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "VIJ", FALSE, FALSE, 72.827, 72.827);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "GUJ", FALSE, FALSE, 16.634, 16.634);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MAD", FALSE, FALSE, 4.712, 4.712);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MLW", TRUE, FALSE, 56.502, 56.502);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MER", FALSE, FALSE, 24.932, 24.932);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "ORI", FALSE, FALSE, 2.17, 2.17);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "SND", FALSE, FALSE, 7.06, 7.06);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "JAN", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "GDW", TRUE, FALSE, 19.322, 19.322);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "KHD", TRUE, FALSE, 36.877, 36.877);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "VND", FALSE, FALSE, 6.066, 6.066);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MAB", FALSE, FALSE, 7.529, 7.529);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "CEY", FALSE, FALSE, 10.982, 10.982);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "JSL", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "KAT", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "KOC", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "NGA", FALSE, FALSE, 5.38, 5.38);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "JFN", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "CHD", TRUE, FALSE, 9.913, 9.913);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "CMP", TRUE, FALSE, 11.577, 11.577);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "BGA", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "YOR", FALSE, FALSE, 22.633, 22.633);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "IDR", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "JLV", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "PTL", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "JGD", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "PRB", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "KND", FALSE, FALSE, 7.742, 7.742);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "TLG", TRUE, FALSE, 18.025, 18.025);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "KLT", FALSE, FALSE, 4.38, 4.38);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "ADE", FALSE, FALSE, 4.388, 4.388);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "HDR", FALSE, FALSE, 2.533, 2.533);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MHR", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "YEM", FALSE, FALSE, 2.889, 2.889);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "ORM", FALSE, FALSE, 2.222, 2.222);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "ADA", FALSE, FALSE, 6.946, 6.946);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "AJU", FALSE, FALSE, 2.233, 2.233);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MDI", FALSE, FALSE, 3.743, 3.743);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MYS", TRUE, FALSE, 12.82, 12.82);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "VIJ", TRUE, FALSE, 177.931, 177.931);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "GUJ", FALSE, FALSE, 20.881, 20.881);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MAD", TRUE, FALSE, 28.217, 28.217);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MER", FALSE, FALSE, 4.518, 4.518);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "ORI", FALSE, FALSE, 6.444, 6.444);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "SND", FALSE, FALSE, 9.082, 9.082);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "VND", TRUE, FALSE, 32.067, 32.067);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MAB", TRUE, FALSE, 45.073, 45.073);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "CEY", TRUE, FALSE, 38.397, 38.397);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "KAT", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "KOC", TRUE, FALSE, 28.24, 28.24);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "JFN", TRUE, FALSE, 17.584, 17.584);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "YOR", TRUE, FALSE, 82.878, 82.878);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "DGL", TRUE, FALSE, 24.852, 24.852);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "PTL", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "JGD", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "PRB", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "KND", TRUE, FALSE, 27.658, 27.658);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "KLT", TRUE, FALSE, 36.669, 36.669);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "ADE", FALSE, FALSE, 8.432, 8.432);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "ALH", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "HDR", FALSE, FALSE, 6.977, 6.977);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MHR", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "OMA", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "YEM", FALSE, FALSE, 4.911, 4.911);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MSY", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "TIM", FALSE, FALSE, 3.04, 3.04);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "ORM", FALSE, FALSE, 11.169, 11.169);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "FRS", FALSE, FALSE, 9.661, 9.661);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "ZAN", FALSE, FALSE, 6.788, 6.788);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "ADA", FALSE, FALSE, 8.974, 8.974);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "SFA", FALSE, FALSE, 5.241, 5.241);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MBA", FALSE, FALSE, 3.908, 3.908);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MLI", FALSE, FALSE, 2.386, 2.386);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "AJU", FALSE, FALSE, 4.255, 4.255);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MDI", FALSE, FALSE, 3.743, 3.743);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MRE", FALSE, FALSE, 2.026, 2.026);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "WAR", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "SKA", FALSE, FALSE, 2.926, 2.926);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "BTS", FALSE, FALSE, 2.41, 2.41);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "BAL", FALSE, FALSE, 8.088, 8.088);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "VIJ", FALSE, FALSE, 13.584, 13.584);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "GUJ", TRUE, FALSE, 113.848, 113.848);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MER", TRUE, FALSE, 33.114, 33.114);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "SND", TRUE, FALSE, 57.683, 57.683);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "JAN", TRUE, FALSE, 7.682, 7.682);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "JSL", TRUE, FALSE, 6.915, 6.915);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "KAT", TRUE, FALSE, 29.783, 29.783);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "NGA", TRUE, FALSE, 8.911, 8.911);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "BGA", TRUE, FALSE, 10.203, 10.203);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "PTL", TRUE, FALSE, 13.708, 13.708);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "JGD", TRUE, FALSE, 14.98, 14.98);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "PRB", TRUE, FALSE, 24.764, 24.764);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "ETH", FALSE, FALSE, 5.406, 5.406);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "MAL", FALSE, FALSE, 5.318, 5.318);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "SON", FALSE, FALSE, 23.654, 23.654);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "KBO", TRUE, FALSE, 35.402, 35.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "OYO", TRUE, FALSE, 18.466, 18.466);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "WGD", FALSE, FALSE, 8.424, 8.424);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "GUR", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "WAD", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "ALO", FALSE, FALSE, 3.35, 3.35);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "AIR", TRUE, FALSE, 24.541, 24.541);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "BON", FALSE, FALSE, 6.054, 6.054);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "DAH", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "DGB", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "JNN", FALSE, FALSE, 21.735, 21.735);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "KAN", TRUE, FALSE, 24.185, 24.185);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "KNG", FALSE, FALSE, 6.739, 6.739);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "KTS", TRUE, FALSE, 41.01, 41.01);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "NUP", TRUE, FALSE, 13.996, 13.996);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "TMB", FALSE, FALSE, 19.469, 19.469);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "YAO", TRUE, FALSE, 32.395, 32.395);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "YAT", FALSE, FALSE, 7.75, 7.75);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "ZZZ", TRUE, FALSE, 23.043, 23.043);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ADE", FALSE, FALSE, 4.388, 4.388);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "HDR", FALSE, FALSE, 2.533, 2.533);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "HED", FALSE, FALSE, 4.619, 4.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MAK", FALSE, FALSE, 5.045, 5.045);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MFL", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "NJR", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "RAS", FALSE, FALSE, 8.7, 8.7);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "YEM", FALSE, FALSE, 2.889, 2.889);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MAM", FALSE, FALSE, 22.444, 22.444);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ETH", TRUE, FALSE, 57.511, 57.511);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ADA", FALSE, FALSE, 8.3, 8.3);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "AJU", FALSE, FALSE, 5.857, 5.857);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MDI", FALSE, FALSE, 3.743, 3.743);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ENA", TRUE, FALSE, 11.112, 11.112);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "OGD", TRUE, FALSE, 10.533, 10.533);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "WAD", TRUE, FALSE, 9.339, 9.339);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ALO", TRUE, FALSE, 30.88, 30.88);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "KAF", TRUE, FALSE, 15.052, 15.052);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MED", TRUE, FALSE, 14.261, 14.261);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MRE", FALSE, FALSE, 0.726, 0.726);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "BEJ", TRUE, FALSE, 12.724, 12.724);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "WLY", TRUE, FALSE, 14.032, 14.032);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "DAM", TRUE, FALSE, 14.351, 14.351);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "HDY", TRUE, FALSE, 13.616, 13.616);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "JJI", TRUE, FALSE, 10.049, 10.049);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ABB", TRUE, FALSE, 14.718, 14.718);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "ADE", TRUE, FALSE, 45.223, 45.223);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "DAW", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "HDR", TRUE, FALSE, 35.996, 35.996);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "HED", FALSE, FALSE, 6.605, 6.605);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MDA", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MFL", TRUE, FALSE, 16.076, 16.076);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MHR", TRUE, FALSE, 30.646, 30.646);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "NAJ", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "NJR", TRUE, FALSE, 9.359, 9.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "OMA", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "RAS", TRUE, FALSE, 11.888, 11.888);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "YAS", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "YEM", TRUE, FALSE, 39.207, 39.207);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MAM", FALSE, FALSE, 24.236, 24.236);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "TIM", FALSE, FALSE, 3.04, 3.04);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "ORM", FALSE, FALSE, 12.107, 12.107);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "FRS", FALSE, FALSE, 9.661, 9.661);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "ETH", FALSE, FALSE, 0.704, 0.704);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "ZAN", FALSE, FALSE, 6.788, 6.788);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "ADA", TRUE, FALSE, 59.326, 59.326);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "SFA", FALSE, FALSE, 5.241, 5.241);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MBA", FALSE, FALSE, 7.944, 7.944);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MLI", FALSE, FALSE, 6.422, 6.422);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "AJU", TRUE, FALSE, 26.718, 26.718);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MDI", TRUE, FALSE, 33.613, 33.613);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MED", FALSE, FALSE, 4.498, 4.498);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MRE", TRUE, FALSE, 21.031, 21.031);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "PTE", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "WAR", TRUE, FALSE, 32.83, 32.83);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "SKA", FALSE, FALSE, 2.926, 2.926);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "BTS", FALSE, FALSE, 2.41, 2.41);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "ALH", FALSE, FALSE, 4.594, 4.594);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "DAW", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "NAJ", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "OMA", TRUE, FALSE, 11.834, 11.834);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "SHM", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "YAS", TRUE, FALSE, 8.561, 8.561);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "MSY", FALSE, FALSE, 6.963, 6.963);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "QAR", FALSE, FALSE, 5.015, 5.015);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "TIM", FALSE, FALSE, 15.202, 15.202);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "ORM", TRUE, FALSE, 77.702, 77.702);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "LRI", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "FRS", TRUE, FALSE, 52.079, 52.079);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "BAL", FALSE, FALSE, 6.347, 6.347);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "ZAN", TRUE, FALSE, 77.008, 77.008);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "SFA", TRUE, FALSE, 45.239, 45.239);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "MBA", TRUE, FALSE, 44.773, 44.773);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "MLI", TRUE, FALSE, 36.402, 36.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "AJU", FALSE, FALSE, 1.54, 1.54);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "PTE", TRUE, FALSE, 31.684, 31.684);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "MIR", TRUE, FALSE, 10.572, 10.572);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "SKA", TRUE, FALSE, 41.59, 41.59);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "BTS", TRUE, FALSE, 35.259, 35.259);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "MFY", TRUE, FALSE, 22.944, 22.944);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "ANT", TRUE, FALSE, 26.478, 26.478);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "ALH", TRUE, FALSE, 23.886, 23.886);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "ANZ", TRUE, FALSE, 6.013, 6.013);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "ARD", TRUE, FALSE, 8.619, 8.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "DAW", TRUE, FALSE, 10.123, 10.123);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "NAJ", TRUE, FALSE, 8.859, 8.859);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "SHM", TRUE, FALSE, 16.842, 16.842);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "SRV", FALSE, FALSE, 4.153, 4.153);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "AKK", FALSE, FALSE, 2.899, 2.899);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "KAR", FALSE, FALSE, 2.139, 2.139);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "RAM", FALSE, FALSE, 2.048, 2.048);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "MSY", TRUE, FALSE, 46.029, 46.029);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "MAM", FALSE, FALSE, 12.288, 12.288);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "KHO", FALSE, FALSE, 8.631, 8.631);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "QAR", FALSE, FALSE, 84.691, 84.691);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "TIM", FALSE, FALSE, 9.22, 9.22);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "KRY", FALSE, FALSE, 6.054, 6.054);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "TAB", FALSE, FALSE, 14.082, 14.082);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "LRI", TRUE, FALSE, 11.308, 11.308);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "SIS", FALSE, FALSE, 6.619, 6.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "BPI", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "QOM", FALSE, FALSE, 44.792, 44.792);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "NOG", FALSE, FALSE, 39.601, 39.601);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "GOL", FALSE, FALSE, 9.501, 9.501);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "SRV", FALSE, FALSE, 4.153, 4.153);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "SHY", FALSE, FALSE, 11.28, 11.28);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "KHO", FALSE, FALSE, 8.631, 8.631);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "QAR", FALSE, FALSE, 6.887, 6.887);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "TIM", FALSE, FALSE, 63.157, 63.157);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "TRS", TRUE, FALSE, 114.716, 114.716);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "TAB", FALSE, FALSE, 4.329, 4.329);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "SIS", FALSE, FALSE, 6.619, 6.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "QOM", FALSE, FALSE, 8.909, 8.909);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "CHG", FALSE, FALSE, 6.531, 6.531);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "KAS", TRUE, FALSE, 54.617, 54.617);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "NOG", FALSE, FALSE, 35.249, 35.249);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "GOL", FALSE, FALSE, 55.852, 55.852);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "SRV", TRUE, FALSE, 36.604, 36.604);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "HSN", FALSE, FALSE, 4.048, 4.048);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "BTL", FALSE, FALSE, 9.336, 9.336);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "AKK", FALSE, FALSE, 23.578, 23.578);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "KAR", FALSE, FALSE, 2.139, 2.139);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "RAM", FALSE, FALSE, 2.048, 2.048);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "AVR", FALSE, FALSE, 4.302, 4.302);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "MLK", TRUE, FALSE, 12.074, 12.074);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "SME", FALSE, FALSE, 7.413, 7.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "ARL", TRUE, FALSE, 13.722, 13.722);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "MAM", FALSE, FALSE, 12.288, 12.288);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "KHO", TRUE, FALSE, 19.658, 19.658);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "QAR", TRUE, FALSE, 101.331, 101.331);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "TIM", TRUE, FALSE, 112.378, 112.378);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "KRY", TRUE, FALSE, 19.518, 19.518);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "GAZ", FALSE, FALSE, 7.413, 7.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "TAB", TRUE, FALSE, 40.061, 40.061);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "SIS", TRUE, FALSE, 15.432, 15.432);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "BPI", TRUE, FALSE, 20.196, 20.196);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "QOM", TRUE, FALSE, 91.208, 91.208);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "BYZ", FALSE, FALSE, 9.049, 9.049);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "CYP", TRUE, FALSE, 15.534, 15.534);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "KNI", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "NAX", FALSE, FALSE, 1.992, 1.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "TUR", FALSE, FALSE, 18.511, 18.511);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "ANZ", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "FAD", TRUE, FALSE, 9.556, 9.556);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "HED", FALSE, FALSE, 4.619, 4.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "MDA", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "HSN", TRUE, FALSE, 9.385, 9.385);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "BTL", TRUE, FALSE, 11.093, 11.093);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "AKK", TRUE, FALSE, 23.742, 23.742);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "CND", FALSE, FALSE, 6.009, 6.009);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "DUL", TRUE, FALSE, 12.632, 12.632);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "KAR", TRUE, FALSE, 21.818, 21.818);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "RAM", TRUE, FALSE, 21.34, 21.34);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "MAM", FALSE, FALSE, 86.01, 86.01);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "QAR", FALSE, FALSE, 4.978, 4.978);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "ALB", FALSE, FALSE, 2.042, 2.042);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "ATH", FALSE, FALSE, 1.992, 1.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "BYZ", FALSE, FALSE, 9.049, 9.049);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "CRO", FALSE, FALSE, 1.992, 1.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "EPI", FALSE, FALSE, 2.024, 2.024);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "KNI", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "NAX", FALSE, FALSE, 1.992, 1.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "RAG", FALSE, FALSE, 2.228, 2.228);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "TUR", FALSE, FALSE, 17.684, 17.684);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "PRO", FALSE, FALSE, 8.532, 8.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "ARA", FALSE, FALSE, 6.392, 6.392);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "FER", FALSE, FALSE, 6.319, 6.319);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "GEN", FALSE, FALSE, 14.172, 14.172);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "MAN", FALSE, FALSE, 2.156, 2.156);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "MLO", FALSE, FALSE, 5.804, 5.804);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "NAP", FALSE, FALSE, 10.622, 10.622);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "PAP", FALSE, FALSE, 6.352, 6.352);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "SAV", FALSE, FALSE, 2.028, 2.028);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "SIE", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "URB", FALSE, FALSE, 4.116, 4.116);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "VEN", FALSE, FALSE, 24.696, 24.696);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "LUC", FALSE, FALSE, 4.782, 4.782);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "LAN", FALSE, FALSE, 10.263, 10.263);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "ANZ", FALSE, FALSE, 1.373, 1.373);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "HED", TRUE, FALSE, 33.074, 33.074);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "MAK", TRUE, FALSE, 10.158, 10.158);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "MDA", TRUE, FALSE, 15.512, 15.512);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "CND", FALSE, FALSE, 6.009, 6.009);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "MAM", TRUE, FALSE, 130.476, 130.476);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "BEJ", FALSE, FALSE, 1.339, 1.339);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "MOL", FALSE, FALSE, 4.925, 4.925);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "LIT", FALSE, FALSE, 3.026, 3.026);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "GEN", FALSE, FALSE, 8.291, 8.291);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "CRI", FALSE, FALSE, 30.65, 30.65);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "GEO", FALSE, FALSE, 15.041, 15.041);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "KAZ", FALSE, FALSE, 49.014, 49.014);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "MOS", FALSE, FALSE, 4.858, 4.858);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "NOG", TRUE, FALSE, 52.706, 52.706);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "GOL", TRUE, FALSE, 108.684, 108.684);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "SRV", FALSE, FALSE, 1.91, 1.91);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "TRE", FALSE, FALSE, 8.26, 8.26);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "AVR", TRUE, FALSE, 12.037, 12.037);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "SME", FALSE, FALSE, 7.413, 7.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "CIR", FALSE, FALSE, 8.761, 8.761);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "GAZ", TRUE, FALSE, 17.968, 17.968);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "IME", FALSE, FALSE, 14.855, 14.855);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "BYZ", FALSE, FALSE, 7.029, 7.029);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "EPI", FALSE, FALSE, 2.024, 2.024);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "MOL", TRUE, FALSE, 35.728, 35.728);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "SER", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "WAL", FALSE, FALSE, 21.127, 21.127);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "HUN", FALSE, FALSE, 12.859, 12.859);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "TUR", FALSE, FALSE, 22.206, 22.206);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "LIT", FALSE, FALSE, 85.019, 85.019);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "MAZ", FALSE, FALSE, 14.232, 14.232);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "OKA", FALSE, FALSE, 5.045, 5.045);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "GEN", FALSE, FALSE, 21.767, 21.767);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "CRI", TRUE, FALSE, 34.352, 34.352);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "GEO", TRUE, FALSE, 25.619, 25.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "RYA", FALSE, FALSE, 13.452, 13.452);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "FEO", TRUE, FALSE, 15.093, 15.093);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "AKK", FALSE, FALSE, 4.692, 4.692);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "CND", FALSE, FALSE, 3.987, 3.987);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "TRE", TRUE, FALSE, 24.207, 24.207);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "SME", TRUE, FALSE, 10.66, 10.66);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "CIR", TRUE, FALSE, 17.887, 17.887);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "IME", TRUE, FALSE, 29.042, 29.042);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "ALB", FALSE, FALSE, 2.042, 2.042);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "ATH", FALSE, FALSE, 4.071, 4.071);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "BOS", FALSE, FALSE, 2.024, 2.024);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "BYZ", TRUE, FALSE, 52.998, 52.998);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "CRO", FALSE, FALSE, 4.685, 4.685);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "EPI", FALSE, FALSE, 2.024, 2.024);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "KNI", TRUE, FALSE, 14.737, 14.737);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "NAX", TRUE, FALSE, 10.453, 10.453);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "RAG", FALSE, FALSE, 7.218, 7.218);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "SER", FALSE, FALSE, 4.301, 4.301);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "TUR", TRUE, FALSE, 141.305, 141.305);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "GEN", FALSE, FALSE, 3.474, 3.474);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "VEN", FALSE, FALSE, 7.929, 7.929);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "CND", TRUE, FALSE, 34.779, 34.779);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "HRZ", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "LIT", TRUE, FALSE, 145.856, 145.856);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "MAZ", FALSE, FALSE, 17.162, 17.162);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "POL", FALSE, FALSE, 69.923, 69.923);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "OKA", TRUE, FALSE, 13.485, 13.485);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "MOS", FALSE, FALSE, 64.386, 64.386);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "NOV", FALSE, FALSE, 61.279, 61.279);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "PSK", FALSE, FALSE, 12.278, 12.278);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "RYA", FALSE, FALSE, 16.916, 16.916);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "TVE", FALSE, FALSE, 9.752, 9.752);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "YAR", FALSE, FALSE, 2.001, 2.001);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "RSO", FALSE, FALSE, 4.633, 4.633);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "GOL", FALSE, FALSE, 1.948, 1.948);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "OPL", FALSE, FALSE, 14.172, 14.172);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kazan", "KAZ", TRUE, FALSE, 86.23, 86.23);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kazan", "MOS", FALSE, FALSE, 33.738, 33.738);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kazan", "NOV", FALSE, FALSE, 14.973, 14.973);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kazan", "PSK", FALSE, FALSE, 2.681, 2.681);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kazan", "YAR", FALSE, FALSE, 9.614, 9.614);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kazan", "NOG", FALSE, FALSE, 6.318, 6.318);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kazan", "PRM", FALSE, FALSE, 1.362, 1.362);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "SWE", FALSE, FALSE, 11.795, 11.795);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "GOT", FALSE, FALSE, 4.481, 4.481);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "NOR", FALSE, FALSE, 1.992, 1.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "LIT", FALSE, FALSE, 2.445, 2.445);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "LIV", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "RIG", FALSE, FALSE, 7.036, 7.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "TEU", FALSE, FALSE, 16.215, 16.215);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "MOS", TRUE, FALSE, 59.676, 59.676);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "NOV", TRUE, FALSE, 96.027, 96.027);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "PSK", TRUE, FALSE, 22.406, 22.406);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "RYA", TRUE, FALSE, 11.558, 11.558);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "TVE", TRUE, FALSE, 14.649, 14.649);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "YAR", TRUE, FALSE, 18.668, 18.668);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "PRM", FALSE, FALSE, 2.288, 2.288);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "BLO", TRUE, FALSE, 9.81, 9.81);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "RSO", TRUE, FALSE, 9.924, 9.924);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "HSA", FALSE, FALSE, 7.077, 7.077);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "STE", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("laplata", "GUA", TRUE, FALSE, 8.499, 8.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("laplata", "CUA", TRUE, FALSE, 31.871, 31.871);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "PTG", TRUE, FALSE, 16.978, 16.978);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "TPQ", TRUE, FALSE, 9.989, 9.989);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "TUA", TRUE, FALSE, 9.696, 9.696);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "MOR", FALSE, FALSE, 4.406, 4.406);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "TUN", FALSE, FALSE, 2.161, 2.161);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "TFL", FALSE, FALSE, 4.425, 4.425);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "SOS", FALSE, FALSE, 2.861, 2.861);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "TLC", FALSE, FALSE, 5.392, 5.392);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "BEN", FALSE, FALSE, 5.743, 5.743);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "KON", FALSE, FALSE, 4.247, 4.247);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "MAL", TRUE, FALSE, 70.284, 70.284);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "SON", TRUE, FALSE, 48.129, 48.129);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "OYO", FALSE, FALSE, 1.983, 1.983);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "JOL", FALSE, FALSE, 14.25, 14.25);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "WGD", TRUE, FALSE, 19.871, 19.871);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "GUR", TRUE, FALSE, 11.578, 11.578);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "BON", TRUE, FALSE, 15.392, 15.392);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "DAH", TRUE, FALSE, 10.779, 10.779);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "DGB", TRUE, FALSE, 11.665, 11.665);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "JNN", TRUE, FALSE, 53.88, 53.88);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "KNG", TRUE, FALSE, 16.541, 16.541);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "TMB", TRUE, FALSE, 45.873, 45.873);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "YAT", TRUE, FALSE, 18.561, 18.561);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "BEN", TRUE, FALSE, 49.905, 49.905);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "KON", FALSE, FALSE, 21.237, 21.237);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "MAL", FALSE, FALSE, 8.739, 8.739);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "LOA", TRUE, FALSE, 18.926, 18.926);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "JOL", TRUE, FALSE, 46.155, 46.155);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "NDO", TRUE, FALSE, 16.045, 16.045);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "FRA", FALSE, FALSE, 3.362, 3.362);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "PRO", FALSE, FALSE, 6.532, 6.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "ARA", FALSE, FALSE, 18.385, 18.385);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "CAS", FALSE, FALSE, 27.293, 27.293);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "GRA", FALSE, FALSE, 4.328, 4.328);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "POR", FALSE, FALSE, 19.929, 19.929);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "GEN", FALSE, FALSE, 12.138, 12.138);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "MLO", FALSE, FALSE, 5.804, 5.804);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "NAP", FALSE, FALSE, 6.159, 6.159);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "PAP", FALSE, FALSE, 4.27, 4.27);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "MFA", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "LUC", FALSE, FALSE, 2.76, 2.76);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "LAN", FALSE, FALSE, 8.269, 8.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "MOR", FALSE, FALSE, 4.754, 4.754);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "TUN", TRUE, FALSE, 70.856, 70.856);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "GHD", TRUE, FALSE, 9.129, 9.129);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "FZA", TRUE, FALSE, 13.856, 13.856);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "MZB", TRUE, FALSE, 8.563, 8.563);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "SZO", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "PGA", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "ALB", TRUE, FALSE, 14.565, 14.565);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "ATH", TRUE, FALSE, 20.858, 20.858);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "BOS", TRUE, FALSE, 13.655, 13.655);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "BYZ", FALSE, FALSE, 5.229, 5.229);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "CRO", TRUE, FALSE, 24.541, 24.541);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "EPI", TRUE, FALSE, 13.323, 13.323);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "RAG", TRUE, FALSE, 45.403, 45.403);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "SER", TRUE, FALSE, 19.709, 19.709);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "WAL", FALSE, FALSE, 21.127, 21.127);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "HUN", FALSE, FALSE, 74.037, 74.037);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "TUR", FALSE, FALSE, 31.449, 31.449);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "PRO", FALSE, FALSE, 6.532, 6.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "AUG", FALSE, FALSE, 7.75, 7.75);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "MEM", FALSE, FALSE, 6.56, 6.56);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "ARA", FALSE, FALSE, 6.392, 6.392);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "FER", FALSE, FALSE, 6.319, 6.319);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "GEN", FALSE, FALSE, 12.138, 12.138);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "MAN", FALSE, FALSE, 4.198, 4.198);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "MLO", FALSE, FALSE, 7.826, 7.826);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "NAP", FALSE, FALSE, 8.636, 8.636);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "PAP", FALSE, FALSE, 6.352, 6.352);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "SIE", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "URB", FALSE, FALSE, 4.116, 4.116);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "VEN", FALSE, FALSE, 45.939, 45.939);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "MFA", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "LUC", FALSE, FALSE, 4.782, 4.782);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "LAN", FALSE, FALSE, 8.269, 8.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "LBV", FALSE, FALSE, 12.132, 12.132);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "ING", FALSE, FALSE, 10.446, 10.446);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "GLG", FALSE, FALSE, 8.283, 8.283);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "BLG", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "SZO", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "CLI", FALSE, FALSE, 1.994, 1.994);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "HRZ", TRUE, FALSE, 11.756, 11.756);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "TNT", FALSE, FALSE, 4.698, 4.698);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "PGA", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "CAS", FALSE, FALSE, 27.293, 27.293);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "GRA", FALSE, FALSE, 2.324, 2.324);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "POR", FALSE, FALSE, 17.907, 17.907);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "MOR", TRUE, FALSE, 44.026, 44.026);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "TUN", FALSE, FALSE, 6.422, 6.422);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "TFL", TRUE, FALSE, 29.083, 29.083);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "SOS", TRUE, FALSE, 27.23, 27.23);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "TLC", TRUE, FALSE, 36.064, 36.064);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "MRK", TRUE, FALSE, 15.992, 15.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "CRO", FALSE, FALSE, 4.288, 4.288);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "WAL", TRUE, FALSE, 45.517, 45.517);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "HUN", TRUE, FALSE, 134.293, 134.293);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "MAZ", FALSE, FALSE, 2.929, 2.929);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "POL", FALSE, FALSE, 14.556, 14.556);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "AUG", FALSE, FALSE, 7.75, 7.75);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "HAB", FALSE, FALSE, 62.891, 62.891);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "SLZ", FALSE, FALSE, 6.739, 6.739);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "ULM", FALSE, FALSE, 9.644, 9.644);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "WUR", FALSE, FALSE, 8.761, 8.761);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "MEM", FALSE, FALSE, 6.56, 6.56);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "UBV", FALSE, FALSE, 10.11, 10.11);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "LBV", FALSE, FALSE, 12.132, 12.132);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "ING", FALSE, FALSE, 10.446, 10.446);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "PSS", FALSE, FALSE, 5.586, 5.586);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "MBZ", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "KNZ", FALSE, FALSE, 8.707, 8.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "REG", FALSE, FALSE, 3.821, 3.821);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "OPL", FALSE, FALSE, 14.172, 14.172);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "GLG", FALSE, FALSE, 8.283, 8.283);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "TNT", FALSE, FALSE, 4.698, 4.698);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "SWE", FALSE, FALSE, 7.98, 7.98);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "GOT", FALSE, FALSE, 2.467, 2.467);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "LIV", FALSE, FALSE, 4.372, 4.372);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "MAZ", TRUE, FALSE, 38.277, 38.277);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "POL", TRUE, FALSE, 143.465, 143.465);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "RIG", FALSE, FALSE, 5.022, 5.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "TEU", FALSE, FALSE, 22.268, 22.268);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "ANH", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "BOH", FALSE, FALSE, 7.191, 7.191);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "BRA", FALSE, FALSE, 28.025, 28.025);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "BRU", FALSE, FALSE, 12.805, 12.805);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "HAB", FALSE, FALSE, 62.891, 62.891);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "MAG", FALSE, FALSE, 10.999, 10.999);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "SAX", FALSE, FALSE, 19.041, 19.041);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "SLZ", FALSE, FALSE, 6.739, 6.739);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "THU", FALSE, FALSE, 16.225, 16.225);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "ULM", FALSE, FALSE, 9.644, 9.644);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "WUR", FALSE, FALSE, 8.761, 8.761);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "UBV", FALSE, FALSE, 10.11, 10.11);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "PSS", FALSE, FALSE, 5.586, 5.586);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "MBZ", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "KNZ", FALSE, FALSE, 8.707, 8.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "REG", FALSE, FALSE, 10.224, 10.224);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "OPL", TRUE, FALSE, 32.985, 32.985);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "GLG", TRUE, FALSE, 18.976, 18.976);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "GOS", FALSE, FALSE, 5.728, 5.728);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "AAC", FALSE, FALSE, 6.692, 6.692);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ANH", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ANS", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "AUG", TRUE, FALSE, 17.857, 17.857);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BAD", FALSE, FALSE, 7.743, 7.743);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BOH", FALSE, FALSE, 54.529, 54.529);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BRA", FALSE, FALSE, 28.025, 28.025);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BRU", FALSE, FALSE, 12.805, 12.805);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "FRN", FALSE, FALSE, 14.024, 14.024);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "HAB", TRUE, FALSE, 119.68, 119.68);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "HES", FALSE, FALSE, 9.772, 9.772);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "KLE", FALSE, FALSE, 6.739, 6.739);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "KOL", FALSE, FALSE, 14.841, 14.841);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MAG", FALSE, FALSE, 10.999, 10.999);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MAI", FALSE, FALSE, 9.398, 9.398);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MUN", FALSE, FALSE, 12.59, 12.59);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "PAL", FALSE, FALSE, 17.684, 17.684);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "SAX", FALSE, FALSE, 19.041, 19.041);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "SLZ", TRUE, FALSE, 23.04, 23.04);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "THU", FALSE, FALSE, 16.225, 16.225);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "TRI", FALSE, FALSE, 9.435, 9.435);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ULM", TRUE, FALSE, 26.972, 26.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "WBG", FALSE, FALSE, 7.376, 7.376);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "WUR", TRUE, FALSE, 19.94, 19.94);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "NUM", FALSE, FALSE, 12.61, 12.61);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MEM", TRUE, FALSE, 16.355, 16.355);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "NSA", FALSE, FALSE, 5.055, 5.055);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "RVA", FALSE, FALSE, 8.195, 8.195);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "FER", FALSE, FALSE, 4.307, 4.307);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MAN", FALSE, FALSE, 10.664, 10.664);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "NAP", FALSE, FALSE, 2.476, 2.476);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "URB", FALSE, FALSE, 2.13, 2.13);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "VEN", FALSE, FALSE, 74.232, 74.232);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "UBV", TRUE, FALSE, 22.718, 22.718);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "LBV", TRUE, FALSE, 26.884, 26.884);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ING", TRUE, FALSE, 23.308, 23.308);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "PSS", TRUE, FALSE, 13.408, 13.408);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MBZ", TRUE, FALSE, 9.93, 9.93);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "KNZ", TRUE, FALSE, 25.107, 25.107);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ROT", FALSE, FALSE, 5.391, 5.391);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BYT", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "REG", TRUE, FALSE, 33.588, 33.588);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "TTL", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BLG", FALSE, FALSE, 6.066, 6.066);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "GOS", FALSE, FALSE, 5.728, 5.728);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "CLI", FALSE, FALSE, 4.835, 4.835);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "TNT", TRUE, FALSE, 11.57, 11.57);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BRG", FALSE, FALSE, 9.435, 9.435);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MLH", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BAM", FALSE, FALSE, 4.016, 4.016);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "SWE", FALSE, FALSE, 2.105, 2.105);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "DAN", FALSE, FALSE, 58.968, 58.968);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "NOR", FALSE, FALSE, 3.147, 3.147);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "SHL", FALSE, FALSE, 11.548, 11.548);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "TEU", FALSE, FALSE, 2.173, 2.173);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "ALS", FALSE, FALSE, 7.523, 7.523);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "AAC", FALSE, FALSE, 6.692, 6.692);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "ANH", TRUE, FALSE, 10.203, 10.203);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "ANS", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BAD", FALSE, FALSE, 7.743, 7.743);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BOH", TRUE, FALSE, 91.488, 91.488);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BRA", TRUE, FALSE, 56.845, 56.845);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BRE", FALSE, FALSE, 6.44, 6.44);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BRU", TRUE, FALSE, 28.153, 28.153);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "FRN", FALSE, FALSE, 14.024, 14.024);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "HAM", FALSE, FALSE, 8.421, 8.421);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "HES", FALSE, FALSE, 9.772, 9.772);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "KLE", FALSE, FALSE, 6.739, 6.739);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "KOL", FALSE, FALSE, 14.841, 14.841);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "MAG", TRUE, FALSE, 32.378, 32.378);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "MAI", FALSE, FALSE, 9.398, 9.398);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "MUN", FALSE, FALSE, 12.59, 12.59);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "PAL", FALSE, FALSE, 15.547, 15.547);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "SAX", TRUE, FALSE, 41.786, 41.786);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "THU", TRUE, FALSE, 35.834, 35.834);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "TRI", FALSE, FALSE, 9.435, 9.435);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "WBG", FALSE, FALSE, 7.376, 7.376);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "NUM", FALSE, FALSE, 12.61, 12.61);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "NSA", FALSE, FALSE, 5.055, 5.055);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "RVA", FALSE, FALSE, 8.195, 8.195);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "HSA", FALSE, FALSE, 4.741, 4.741);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "ROT", FALSE, FALSE, 5.391, 5.391);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BYT", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "TTL", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "WOL", FALSE, FALSE, 14.563, 14.563);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "STE", FALSE, FALSE, 4.163, 4.163);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "GOS", TRUE, FALSE, 14.49, 14.49);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BRG", FALSE, FALSE, 9.435, 9.435);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "MLH", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BAM", FALSE, FALSE, 4.016, 4.016);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "SWE", TRUE, FALSE, 56.092, 56.092);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "DAN", FALSE, FALSE, 17.18, 17.18);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "GOT", TRUE, FALSE, 25.737, 25.737);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "NOR", FALSE, FALSE, 3.147, 3.147);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "SHL", FALSE, FALSE, 2.611, 2.611);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "LIT", FALSE, FALSE, 6.102, 6.102);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "LIV", TRUE, FALSE, 36.286, 36.286);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "RIG", TRUE, FALSE, 39.785, 39.785);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "TEU", TRUE, FALSE, 90.207, 90.207);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "BRE", FALSE, FALSE, 6.44, 6.44);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "HAM", FALSE, FALSE, 8.421, 8.421);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "OLD", FALSE, FALSE, 2.026, 2.026);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "DTT", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "HSA", FALSE, FALSE, 4.741, 4.741);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "WOL", FALSE, FALSE, 3.105, 3.105);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "STE", FALSE, FALSE, 4.163, 4.163);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "SWE", FALSE, FALSE, 2.105, 2.105);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "DAN", FALSE, FALSE, 17.18, 17.18);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "NOR", FALSE, FALSE, 3.147, 3.147);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "SHL", FALSE, FALSE, 2.611, 2.611);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "ALS", TRUE, FALSE, 24.966, 24.966);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BUR", FALSE, FALSE, 7.429, 7.429);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "FRA", FALSE, FALSE, 69.179, 69.179);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "NEV", FALSE, FALSE, 10.923, 10.923);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "ORL", FALSE, FALSE, 23.946, 23.946);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "AAC", TRUE, FALSE, 16.694, 16.694);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "ANS", TRUE, FALSE, 11.608, 11.608);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BAD", TRUE, FALSE, 17.098, 17.098);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BRE", FALSE, FALSE, 6.44, 6.44);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "FRN", TRUE, FALSE, 42.735, 42.735);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HAB", FALSE, FALSE, 4.927, 4.927);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HAM", FALSE, FALSE, 8.421, 8.421);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HES", TRUE, FALSE, 22.023, 22.023);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "KLE", TRUE, FALSE, 15.774, 15.774);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "KOL", TRUE, FALSE, 39.241, 39.241);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "LAU", FALSE, FALSE, 4.016, 4.016);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "LOR", FALSE, FALSE, 17.412, 17.412);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "LUN", FALSE, FALSE, 6.739, 6.739);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "MAI", TRUE, FALSE, 24.525, 24.525);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "MKL", FALSE, FALSE, 10.11, 10.11);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "MUN", TRUE, FALSE, 27.209, 27.209);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "PAL", TRUE, FALSE, 31.288, 31.288);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "SWI", FALSE, FALSE, 28.72, 28.72);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "THU", FALSE, FALSE, 1.568, 1.568);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "TRI", TRUE, FALSE, 21.329, 21.329);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "WBG", TRUE, FALSE, 17.473, 17.473);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "NUM", TRUE, FALSE, 38.791, 38.791);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "VER", FALSE, FALSE, 6.402, 6.402);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "NSA", TRUE, FALSE, 12.303, 12.303);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "RVA", TRUE, FALSE, 24.059, 24.059);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HSA", FALSE, FALSE, 4.741, 4.741);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "ROT", TRUE, FALSE, 13.724, 13.724);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BYT", TRUE, FALSE, 10.068, 10.068);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "GNV", FALSE, FALSE, 6.921, 6.921);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "TTL", TRUE, FALSE, 15.08, 15.08);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "WOL", FALSE, FALSE, 3.105, 3.105);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "STE", FALSE, FALSE, 4.163, 4.163);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BRG", TRUE, FALSE, 21.768, 21.768);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "MLH", TRUE, FALSE, 11.608, 11.608);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BAM", TRUE, FALSE, 10.161, 10.161);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "MCA", TRUE, FALSE, 26.449, 26.449);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "CHT", TRUE, FALSE, 5.875, 5.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "MIS", TRUE, FALSE, 6.8, 6.8);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "TAI", TRUE, FALSE, 8.801, 8.801);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "CAB", TRUE, FALSE, 8.499, 8.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "CHE", TRUE, FALSE, 10.087, 10.087);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ABE", TRUE, FALSE, 8.868, 8.868);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "LEN", TRUE, FALSE, 22.322, 22.322);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "MAH", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "MIK", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "PEQ", TRUE, FALSE, 9.715, 9.715);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "POW", TRUE, FALSE, 9.584, 9.584);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ALT", TRUE, FALSE, 9.376, 9.376);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ICH", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "COF", TRUE, FALSE, 10.087, 10.087);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "JOA", TRUE, FALSE, 10.224, 10.224);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ETO", TRUE, FALSE, 9.51, 9.51);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "SAT", TRUE, FALSE, 9.51, 9.51);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "CIA", TRUE, FALSE, 9.91, 9.91);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "COO", TRUE, FALSE, 10.087, 10.087);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "KSI", TRUE, FALSE, 9.018, 9.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "STA", FALSE, FALSE, 3.694, 3.694);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "TSC", TRUE, FALSE, 9.179, 9.179);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "PEN", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "WAM", TRUE, FALSE, 9.018, 9.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "MAH", TRUE, FALSE, 10.443, 10.443);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "MIK", TRUE, FALSE, 10.335, 10.335);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "OSH", TRUE, FALSE, 15.533, 15.533);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "STA", TRUE, FALSE, 25.547, 25.547);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "PEN", TRUE, FALSE, 10.354, 10.354);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "MLS", TRUE, FALSE, 8.654, 8.654);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "INN", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "AGQ", TRUE, FALSE, 9.442, 9.442);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "SWE", FALSE, FALSE, 0.404, 0.404);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "NOR", FALSE, FALSE, 7.692, 7.692);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "ENG", FALSE, FALSE, 2.759, 2.759);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "SCO", FALSE, FALSE, 8.908, 8.908);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "MOS", FALSE, FALSE, 4.672, 4.672);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "NOV", FALSE, FALSE, 15.877, 15.877);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "PRM", TRUE, FALSE, 18.395, 18.395);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SWE", FALSE, FALSE, 2.105, 2.105);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "DAN", FALSE, FALSE, 17.18, 17.18);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "NOR", FALSE, FALSE, 40.911, 40.911);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SHL", FALSE, FALSE, 4.597, 4.597);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "CNN", TRUE, FALSE, 22.99, 22.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "ENG", FALSE, FALSE, 51.979, 51.979);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "LEI", TRUE, FALSE, 22.99, 22.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "MNS", TRUE, FALSE, 23.576, 23.576);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SCO", TRUE, FALSE, 67.822, 67.822);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "TYR", TRUE, FALSE, 23.576, 23.576);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "ULS", TRUE, FALSE, 23.576, 23.576);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "DMS", TRUE, FALSE, 30.319, 30.319);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SLN", TRUE, FALSE, 25.598, 25.598);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "KID", TRUE, FALSE, 9.26, 9.26);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "ORD", TRUE, FALSE, 8.775, 8.775);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "TRY", TRUE, FALSE, 22.696, 22.696);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "FLY", TRUE, FALSE, 8.29, 8.29);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "MCM", TRUE, FALSE, 22.625, 22.625);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "LOI", TRUE, FALSE, 14.627, 14.627);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "BUR", FALSE, FALSE, 2.231, 2.231);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "BRE", FALSE, FALSE, 8.462, 8.462);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "EFR", FALSE, FALSE, 5.364, 5.364);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "HAM", FALSE, FALSE, 10.443, 10.443);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "LAU", FALSE, FALSE, 2.008, 2.008);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "LUN", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "MKL", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "OLD", FALSE, FALSE, 2.026, 2.026);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "VER", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "DTT", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "BRB", FALSE, FALSE, 7.733, 7.733);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "FLA", FALSE, FALSE, 7.149, 7.149);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "FRI", FALSE, FALSE, 2.052, 2.052);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "GEL", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "HOL", FALSE, FALSE, 10.164, 10.164);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "LIE", FALSE, FALSE, 2.028, 2.028);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "UTR", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "HSA", FALSE, FALSE, 6.763, 6.763);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "WOL", FALSE, FALSE, 5.127, 5.127);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "STE", FALSE, FALSE, 6.185, 6.185);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "SWE", FALSE, FALSE, 10.527, 10.527);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "DAN", TRUE, FALSE, 108.844, 108.844);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "NOR", TRUE, FALSE, 34.444, 34.444);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "SHL", TRUE, FALSE, 31.196, 31.196);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "ENG", FALSE, FALSE, 38.179, 38.179);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "BUR", FALSE, FALSE, 2.231, 2.231);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "BRA", FALSE, FALSE, 1.213, 1.213);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "BRE", TRUE, FALSE, 60.938, 60.938);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "EFR", FALSE, FALSE, 5.364, 5.364);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "HAM", TRUE, FALSE, 71.826, 71.826);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "LAU", TRUE, FALSE, 7.464, 7.464);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "LUN", TRUE, FALSE, 10.123, 10.123);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "MKL", TRUE, FALSE, 18.818, 18.818);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "OLD", TRUE, FALSE, 15.805, 15.805);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "VER", TRUE, FALSE, 27.15, 27.15);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "DTT", TRUE, FALSE, 25.818, 25.818);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "BRB", FALSE, FALSE, 5.727, 5.727);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "FLA", FALSE, FALSE, 5.163, 5.163);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "HOL", FALSE, FALSE, 8.178, 8.178);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "HSA", TRUE, FALSE, 48.984, 48.984);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "WOL", TRUE, FALSE, 29.499, 29.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "STE", TRUE, FALSE, 35.833, 35.833);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "ENG", FALSE, FALSE, 35.703, 35.703);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "AMG", TRUE, FALSE, 13.532, 13.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "AUV", TRUE, FALSE, 9.353, 9.353);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "BOU", TRUE, FALSE, 11.21, 11.21);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "BRI", TRUE, FALSE, 59.799, 59.799);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "BUR", FALSE, FALSE, 7.429, 7.429);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "FOI", TRUE, FALSE, 11.235, 11.235);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "FRA", FALSE, FALSE, 41.324, 41.324);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "ORL", FALSE, FALSE, 5.609, 5.609);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "PRO", TRUE, FALSE, 8.6, 8.6);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "LOR", FALSE, FALSE, 2.716, 2.716);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "SWI", FALSE, FALSE, 2.232, 2.232);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "CAS", FALSE, FALSE, 3.029, 3.029);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "NAV", TRUE, FALSE, 8.74, 8.74);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "FRA", FALSE, FALSE, 3.362, 3.362);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "ARA", FALSE, FALSE, 11.993, 11.993);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "CAS", TRUE, FALSE, 141.718, 141.718);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "GRA", TRUE, FALSE, 40.333, 40.333);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "POR", TRUE, FALSE, 106.177, 106.177);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "MOR", FALSE, FALSE, 12.901, 12.901);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "ENG", FALSE, FALSE, 87.799, 87.799);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "BUR", TRUE, FALSE, 96.722, 96.722);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "FRA", TRUE, FALSE, 142.112, 142.112);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "NEV", TRUE, FALSE, 24.353, 24.353);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "ORL", TRUE, FALSE, 47.418, 47.418);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "PRO", FALSE, FALSE, 10.822, 10.822);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "EFR", FALSE, FALSE, 3.342, 3.342);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "LOR", TRUE, FALSE, 36.592, 36.592);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "SWI", TRUE, FALSE, 38.929, 38.929);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "ARA", FALSE, FALSE, 6.392, 6.392);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "GEN", FALSE, FALSE, 12.138, 12.138);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "MLO", FALSE, FALSE, 5.804, 5.804);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "NAP", FALSE, FALSE, 6.159, 6.159);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "PAP", FALSE, FALSE, 4.27, 4.27);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "SAV", TRUE, FALSE, 38.971, 38.971);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "LUC", FALSE, FALSE, 2.76, 2.76);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "LAN", FALSE, FALSE, 8.269, 8.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "BRB", FALSE, FALSE, 26.455, 26.455);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "FLA", FALSE, FALSE, 21.051, 21.051);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "FRI", FALSE, FALSE, 10.26, 10.26);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "GEL", FALSE, FALSE, 11.794, 11.794);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "HOL", FALSE, FALSE, 8.178, 8.178);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "LIE", TRUE, FALSE, 17.846, 17.846);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "UTR", FALSE, FALSE, 8.761, 8.761);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "GNV", TRUE, FALSE, 17.242, 17.242);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "FRA", FALSE, FALSE, 16.815, 16.815);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "PRO", FALSE, FALSE, 6.532, 6.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "ARA", TRUE, FALSE, 77.833, 77.833);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "GEN", FALSE, FALSE, 12.138, 12.138);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "MLO", FALSE, FALSE, 5.804, 5.804);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "NAP", FALSE, FALSE, 6.159, 6.159);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "PAP", FALSE, FALSE, 4.27, 4.27);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "LUC", FALSE, FALSE, 2.76, 2.76);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "LAN", FALSE, FALSE, 8.269, 8.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "PRO", FALSE, FALSE, 17.33, 17.33);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "ARA", FALSE, FALSE, 18.052, 18.052);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "GEN", TRUE, FALSE, 80.02, 80.02);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "MLO", TRUE, FALSE, 39.672, 39.672);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "NAP", TRUE, FALSE, 39.737, 39.737);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "PAP", TRUE, FALSE, 36.625, 36.625);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "SAV", FALSE, FALSE, 6.954, 6.954);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "SIE", TRUE, FALSE, 19.376, 19.376);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "MFA", TRUE, FALSE, 9.4, 9.4);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "LUC", TRUE, FALSE, 27.429, 27.429);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "LAN", TRUE, FALSE, 55.367, 55.367);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "SZO", TRUE, FALSE, 9.236, 9.236);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "PGA", TRUE, FALSE, 8.828, 8.828);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "HAB", FALSE, FALSE, 7.558, 7.558);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "FER", TRUE, FALSE, 36.83, 36.83);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "MAN", TRUE, FALSE, 18.998, 18.998);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "MLO", FALSE, FALSE, 3.424, 3.424);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "NAP", FALSE, FALSE, 7.191, 7.191);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "PAP", FALSE, FALSE, 4.226, 4.226);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "URB", TRUE, FALSE, 21.151, 21.151);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "VEN", TRUE, FALSE, 141.254, 141.254);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "BLG", TRUE, FALSE, 10.414, 10.414);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "CLI", TRUE, FALSE, 8.57, 8.57);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "ENG", TRUE, FALSE, 216.863, 216.863);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "BUR", FALSE, FALSE, 9.674, 9.674);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "EFR", TRUE, FALSE, 43.026, 43.026);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "BRB", TRUE, FALSE, 47.583, 47.583);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "FLA", TRUE, FALSE, 46.527, 46.527);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "FRI", TRUE, FALSE, 34.278, 34.278);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "GEL", TRUE, FALSE, 32.144, 32.144);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "HOL", TRUE, FALSE, 74.52, 74.52);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "LIE", FALSE, FALSE, 2.438, 2.438);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "UTR", TRUE, FALSE, 32.454, 32.454);
