

CREATE TABLE country( country_name  VARCHAR(30), home_node    VARCHAR(30), development FLOAT, PRIMARY KEY (country_name));

CREATE TABLE trade_node(    trade_node    VARCHAR(30), local_value FLOAT,    is_inland BOOLEAN,    total_power FLOAT,    outgoing FLOAT,    ingoing FLOAT,   PRIMARY KEY (trade_node));

CREATE TABLE flow(    source    VARCHAR(30),    dest VARCHAR(30),    flow FLOAT,   PRIMARY KEY (source, dest));

CREATE TABLE node_country(    trade_node     VARCHAR(30), country_name    VARCHAR(30),    is_home BOOLEAN, has_merchant BOOLEAN,    base_trading_power FLOAT,    calculated_trading_power FLOAT,   PRIMARY KEY (trade_node, country_name));
    
INSERT INTO country(country_name,home_node,development) VALUES ("SWE", "baltic_sea", 153.648);
INSERT INTO country(country_name,home_node,development) VALUES ("DAN", "lubeck", 218.135);
INSERT INTO country(country_name,home_node,development) VALUES ("SHL", "lubeck", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KNI", "genua", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MOL", "crimea", 27.83);
INSERT INTO country(country_name,home_node,development) VALUES ("MON", "ragusa", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RAG", "ragusa", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TRA", "pest", 41.3);
INSERT INTO country(country_name,home_node,development) VALUES ("WAL", "pest", 45.67);
INSERT INTO country(country_name,home_node,development) VALUES ("TUR", "constantinople", 1041.201);
INSERT INTO country(country_name,home_node,development) VALUES ("CNN", "north_sea", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ENG", "english_channel", 335.822);
INSERT INTO country(country_name,home_node,development) VALUES ("LEI", "north_sea", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SCO", "north_sea", 89.779);
INSERT INTO country(country_name,home_node,development) VALUES ("SLN", "north_sea", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ORD", "north_sea", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PRU", "baltic_sea", 31.999);
INSERT INTO country(country_name,home_node,development) VALUES ("KUR", "baltic_sea", 12.3);
INSERT INTO country(country_name,home_node,development) VALUES ("PLC", "krakow", 487.075);
INSERT INTO country(country_name,home_node,development) VALUES ("ALS", "rheinland", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("FRA", "champagne", 698.702);
INSERT INTO country(country_name,home_node,development) VALUES ("AAC", "rheinland", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANH", "saxony", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANS", "rheinland", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AUG", "wien", 17.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BAD", "rheinland", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BAV", "wien", 86.733);
INSERT INTO country(country_name,home_node,development) VALUES ("BOH", "saxony", 147.984);
INSERT INTO country(country_name,home_node,development) VALUES ("BRA", "saxony", 75.669);
INSERT INTO country(country_name,home_node,development) VALUES ("BRE", "lubeck", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BRU", "saxony", 31.177);
INSERT INTO country(country_name,home_node,development) VALUES ("EFR", "english_channel", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("FRN", "rheinland", 18.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HAB", "wien", 295.952);
INSERT INTO country(country_name,home_node,development) VALUES ("HAM", "lubeck", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HES", "rheinland", 44.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KOL", "rheinland", 52.54);
INSERT INTO country(country_name,home_node,development) VALUES ("LAU", "lubeck", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LOR", "champagne", 25.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LUN", "lubeck", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAG", "saxony", 16.93);
INSERT INTO country(country_name,home_node,development) VALUES ("MAI", "rheinland", 16.664);
INSERT INTO country(country_name,home_node,development) VALUES ("MKL", "lubeck", 23.86);
INSERT INTO country(country_name,home_node,development) VALUES ("OLD", "lubeck", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PAL", "rheinland", 61.677);
INSERT INTO country(country_name,home_node,development) VALUES ("SAX", "saxony", 45.907);
INSERT INTO country(country_name,home_node,development) VALUES ("SLZ", "wien", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SWI", "champagne", 69.01);
INSERT INTO country(country_name,home_node,development) VALUES ("THU", "saxony", 25.292);
INSERT INTO country(country_name,home_node,development) VALUES ("TRI", "rheinland", 22.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ULM", "wien", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WBG", "rheinland", 15.505);
INSERT INTO country(country_name,home_node,development) VALUES ("WUR", "wien", 18.848);
INSERT INTO country(country_name,home_node,development) VALUES ("NUM", "rheinland", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MEM", "wien", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("VER", "lubeck", 12.94);
INSERT INTO country(country_name,home_node,development) VALUES ("NSA", "rheinland", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RVA", "rheinland", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("POR", "sevilla", 252.001);
INSERT INTO country(country_name,home_node,development) VALUES ("SPA", "sevilla", 657.606);
INSERT INTO country(country_name,home_node,development) VALUES ("GEN", "genua", 45.056);
INSERT INTO country(country_name,home_node,development) VALUES ("MAN", "venice", 29.572);
INSERT INTO country(country_name,home_node,development) VALUES ("MOD", "venice", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PAP", "genua", 105.104);
INSERT INTO country(country_name,home_node,development) VALUES ("PAR", "venice", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SAV", "genua", 70.546);
INSERT INTO country(country_name,home_node,development) VALUES ("TUS", "genua", 76.37);
INSERT INTO country(country_name,home_node,development) VALUES ("URB", "venice", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("VEN", "venice", 136.622);
INSERT INTO country(country_name,home_node,development) VALUES ("LUC", "genua", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LIE", "champagne", 26.424);
INSERT INTO country(country_name,home_node,development) VALUES ("NED", "english_channel", 222.818);
INSERT INTO country(country_name,home_node,development) VALUES ("CRI", "crimea", 56.259);
INSERT INTO country(country_name,home_node,development) VALUES ("GEO", "crimea", 17.236);
INSERT INTO country(country_name,home_node,development) VALUES ("QAS", "novgorod", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RUS", "novgorod", 970.066);
INSERT INTO country(country_name,home_node,development) VALUES ("ZAZ", "crimea", 21.06);
INSERT INTO country(country_name,home_node,development) VALUES ("NOG", "astrakhan", 24.54);
INSERT INTO country(country_name,home_node,development) VALUES ("ANZ", "basra", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ARD", "basra", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DAW", "basra", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HDR", "gulf_of_aden", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HED", "alexandria", 48.7);
INSERT INTO country(country_name,home_node,development) VALUES ("MFL", "gulf_of_aden", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MHR", "gulf_of_aden", 9.784);
INSERT INTO country(country_name,home_node,development) VALUES ("NAJ", "basra", 8.306);
INSERT INTO country(country_name,home_node,development) VALUES ("OMA", "hormuz", 13.905);
INSERT INTO country(country_name,home_node,development) VALUES ("RAS", "gulf_of_aden", 11.892);
INSERT INTO country(country_name,home_node,development) VALUES ("SHM", "basra", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YAS", "hormuz", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AVR", "astrakhan", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MSY", "basra", 27.44);
INSERT INTO country(country_name,home_node,development) VALUES ("ALG", "safi", 56.32);
INSERT INTO country(country_name,home_node,development) VALUES ("MOR", "safi", 132.6);
INSERT INTO country(country_name,home_node,development) VALUES ("TRP", "tunis", 13.07);
INSERT INTO country(country_name,home_node,development) VALUES ("TUN", "tunis", 67.54);
INSERT INTO country(country_name,home_node,development) VALUES ("KBA", "tunis", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SOS", "safi", 22.62);
INSERT INTO country(country_name,home_node,development) VALUES ("TGT", "tunis", 7.662);
INSERT INTO country(country_name,home_node,development) VALUES ("GHD", "tunis", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("FZA", "tunis", 7.512);
INSERT INTO country(country_name,home_node,development) VALUES ("MZB", "tunis", 5.703);
INSERT INTO country(country_name,home_node,development) VALUES ("KZH", "samarkand", 180.144);
INSERT INTO country(country_name,home_node,development) VALUES ("KHI", "samarkand", 48.18);
INSERT INTO country(country_name,home_node,development) VALUES ("BUK", "samarkand", 116.286);
INSERT INTO country(country_name,home_node,development) VALUES ("PER", "persia", 475.449);
INSERT INTO country(country_name,home_node,development) VALUES ("CIR", "crimea", 12.94);
INSERT INTO country(country_name,home_node,development) VALUES ("GAZ", "astrakhan", 37.588);
INSERT INTO country(country_name,home_node,development) VALUES ("IME", "crimea", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ORM", "hormuz", 12.88);
INSERT INTO country(country_name,home_node,development) VALUES ("BSR", "basra", 30.203);
INSERT INTO country(country_name,home_node,development) VALUES ("MGR", "persia", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHE", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ASH", "timbuktu", 13.95);
INSERT INTO country(country_name,home_node,development) VALUES ("BEN", "ivory_coast", 23.87);
INSERT INTO country(country_name,home_node,development) VALUES ("ETH", "ethiopia", 73.331);
INSERT INTO country(country_name,home_node,development) VALUES ("KON", "kongo", 33.1);
INSERT INTO country(country_name,home_node,development) VALUES ("MAL", "timbuktu", 14.52);
INSERT INTO country(country_name,home_node,development) VALUES ("NUB", "ethiopia", 42.488);
INSERT INTO country(country_name,home_node,development) VALUES ("SON", "timbuktu", 23.76);
INSERT INTO country(country_name,home_node,development) VALUES ("ZAN", "zanzibar", 27.33);
INSERT INTO country(country_name,home_node,development) VALUES ("ZIM", "zambezi", 35.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HAU", "katsina", 20.254);
INSERT INTO country(country_name,home_node,development) VALUES ("KBO", "katsina", 59.548);
INSERT INTO country(country_name,home_node,development) VALUES ("LOA", "ivory_coast", 18.086);
INSERT INTO country(country_name,home_node,development) VALUES ("OYO", "katsina", 23.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JOL", "ivory_coast", 12.76);
INSERT INTO country(country_name,home_node,development) VALUES ("MLI", "zanzibar", 14.55);
INSERT INTO country(country_name,home_node,development) VALUES ("AJU", "gulf_of_aden", 47.468);
INSERT INTO country(country_name,home_node,development) VALUES ("ENA", "ethiopia", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WGD", "timbuktu", 18.9);
INSERT INTO country(country_name,home_node,development) VALUES ("GUR", "timbuktu", 5.856);
INSERT INTO country(country_name,home_node,development) VALUES ("OGD", "ethiopia", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WAD", "ethiopia", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AFA", "gulf_of_aden", 25.81);
INSERT INTO country(country_name,home_node,development) VALUES ("DAR", "ethiopia", 11.538);
INSERT INTO country(country_name,home_node,development) VALUES ("KAF", "ethiopia", 13.106);
INSERT INTO country(country_name,home_node,development) VALUES ("MED", "ethiopia", 16.89);
INSERT INTO country(country_name,home_node,development) VALUES ("MRE", "gulf_of_aden", 16.86);
INSERT INTO country(country_name,home_node,development) VALUES ("PTE", "zanzibar", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WAR", "gulf_of_aden", 38.769);
INSERT INTO country(country_name,home_node,development) VALUES ("BTI", "ethiopia", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WLY", "ethiopia", 13.616);
INSERT INTO country(country_name,home_node,development) VALUES ("DAM", "ethiopia", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JJI", "ethiopia", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ABB", "ethiopia", 10.16);
INSERT INTO country(country_name,home_node,development) VALUES ("SYO", "ivory_coast", 15.417);
INSERT INTO country(country_name,home_node,development) VALUES ("KSJ", "kongo", 15.9);
INSERT INTO country(country_name,home_node,development) VALUES ("LUB", "kongo", 35.888);
INSERT INTO country(country_name,home_node,development) VALUES ("LND", "kongo", 19.89);
INSERT INTO country(country_name,home_node,development) VALUES ("CKW", "kongo", 8.96);
INSERT INTO country(country_name,home_node,development) VALUES ("KIK", "kongo", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KZB", "kongo", 20.87);
INSERT INTO country(country_name,home_node,development) VALUES ("YAK", "kongo", 12.95);
INSERT INTO country(country_name,home_node,development) VALUES ("KUB", "kongo", 15.9);
INSERT INTO country(country_name,home_node,development) VALUES ("RWA", "african_great_lakes", 17.93);
INSERT INTO country(country_name,home_node,development) VALUES ("BUU", "african_great_lakes", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BUG", "african_great_lakes", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NKO", "african_great_lakes", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KRW", "african_great_lakes", 13.712);
INSERT INTO country(country_name,home_node,development) VALUES ("BNY", "african_great_lakes", 13.908);
INSERT INTO country(country_name,home_node,development) VALUES ("BSG", "african_great_lakes", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("UBH", "african_great_lakes", 20.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MRA", "zambezi", 18.586);
INSERT INTO country(country_name,home_node,development) VALUES ("LDU", "zambezi", 16.671);
INSERT INTO country(country_name,home_node,development) VALUES ("TBK", "zambezi", 10.95);
INSERT INTO country(country_name,home_node,development) VALUES ("RZW", "zambezi", 9.505);
INSERT INTO country(country_name,home_node,development) VALUES ("MIR", "zanzibar", 12.816);
INSERT INTO country(country_name,home_node,development) VALUES ("SKA", "zanzibar", 21.975);
INSERT INTO country(country_name,home_node,development) VALUES ("BTS", "zanzibar", 14.174);
INSERT INTO country(country_name,home_node,development) VALUES ("MFY", "zanzibar", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANT", "zanzibar", 10.95);
INSERT INTO country(country_name,home_node,development) VALUES ("ANN", "canton", 50.48);
INSERT INTO country(country_name,home_node,development) VALUES ("ARK", "ganges_delta", 31.116);
INSERT INTO country(country_name,home_node,development) VALUES ("ATJ", "malacca", 78.787);
INSERT INTO country(country_name,home_node,development) VALUES ("AYU", "gulf_of_siam", 170.638);
INSERT INTO country(country_name,home_node,development) VALUES ("BLI", "the_moluccas", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BAN", "the_moluccas", 52.818);
INSERT INTO country(country_name,home_node,development) VALUES ("BEI", "malacca", 72.616);
INSERT INTO country(country_name,home_node,development) VALUES ("CHA", "gulf_of_siam", 28.088);
INSERT INTO country(country_name,home_node,development) VALUES ("DAI", "canton", 19.594);
INSERT INTO country(country_name,home_node,development) VALUES ("DTE", "nippon", 16.208);
INSERT INTO country(country_name,home_node,development) VALUES ("HSK", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("IKE", "nippon", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAE", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MRI", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SMZ", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TKG", "nippon", 249.453);
INSERT INTO country(country_name,home_node,development) VALUES ("UES", "nippon", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RFR", "nippon", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ANU", "nippon", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ITO", "nippon", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SHN", "nippon", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("STK", "nippon", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KHA", "yumen", 212.782);
INSERT INTO country(country_name,home_node,development) VALUES ("KHM", "gulf_of_siam", 62.16);
INSERT INTO country(country_name,home_node,development) VALUES ("KOR", "nippon", 129.284);
INSERT INTO country(country_name,home_node,development) VALUES ("LNA", "gulf_of_siam", 54.278);
INSERT INTO country(country_name,home_node,development) VALUES ("LXA", "gulf_of_siam", 139.742);
INSERT INTO country(country_name,home_node,development) VALUES ("MCH", "girin", 182.768);
INSERT INTO country(country_name,home_node,development) VALUES ("MKS", "the_moluccas", 22.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MNG", "beijing", 1067.734);
INSERT INTO country(country_name,home_node,development) VALUES ("MTR", "the_moluccas", 57.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OIR", "yumen", 32.436);
INSERT INTO country(country_name,home_node,development) VALUES ("PAT", "malacca", 15.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RYU", "nippon", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SUK", "gulf_of_siam", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SUL", "philippines", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAU", "burma", 238.388);
INSERT INTO country(country_name,home_node,development) VALUES ("TOK", "canton", 76.472);
INSERT INTO country(country_name,home_node,development) VALUES ("NVK", "girin", 22.88);
INSERT INTO country(country_name,home_node,development) VALUES ("SOL", "girin", 29.75);
INSERT INTO country(country_name,home_node,development) VALUES ("EJZ", "girin", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NHX", "girin", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MYR", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MHX", "girin", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KRC", "girin", 30.76);
INSERT INTO country(country_name,home_node,development) VALUES ("KAS", "samarkand", 99.461);
INSERT INTO country(country_name,home_node,development) VALUES ("UTS", "lhasa", 41.525);
INSERT INTO country(country_name,home_node,development) VALUES ("KAM", "chengdu", 30.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GUG", "lhasa", 10.976);
INSERT INTO country(country_name,home_node,development) VALUES ("BAL", "hormuz", 9.764);
INSERT INTO country(country_name,home_node,development) VALUES ("BIJ", "deccan", 97.219);
INSERT INTO country(country_name,home_node,development) VALUES ("GOC", "deccan", 78.907);
INSERT INTO country(country_name,home_node,development) VALUES ("MUG", "doab", 911.563);
INSERT INTO country(country_name,home_node,development) VALUES ("MYS", "comorin_cape", 27.886);
INSERT INTO country(country_name,home_node,development) VALUES ("VIJ", "comorin_cape", 91.747);
INSERT INTO country(country_name,home_node,development) VALUES ("AHM", "deccan", 26.118);
INSERT INTO country(country_name,home_node,development) VALUES ("ASS", "burma", 37.692);
INSERT INTO country(country_name,home_node,development) VALUES ("GUJ", "gujarat", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAD", "comorin_cape", 30.808);
INSERT INTO country(country_name,home_node,development) VALUES ("MAW", "gujarat", 18.81);
INSERT INTO country(country_name,home_node,development) VALUES ("MER", "gujarat", 24.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JAN", "gujarat", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GDW", "deccan", 5.193);
INSERT INTO country(country_name,home_node,development) VALUES ("GRJ", "ganges_delta", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DHU", "doab", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KLN", "comorin_cape", 17.488);
INSERT INTO country(country_name,home_node,development) VALUES ("VND", "comorin_cape", 11.93);
INSERT INTO country(country_name,home_node,development) VALUES ("MAB", "comorin_cape", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BST", "ganges_delta", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BHU", "lhasa", 6.476);
INSERT INTO country(country_name,home_node,development) VALUES ("BND", "doab", 17.7);
INSERT INTO country(country_name,home_node,development) VALUES ("CEY", "comorin_cape", 12.952);
INSERT INTO country(country_name,home_node,development) VALUES ("JSL", "gujarat", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAC", "burma", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KMT", "ganges_delta", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KGR", "doab", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KOC", "comorin_cape", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLB", "burma", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HAD", "doab", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LDK", "lahore", 8.712);
INSERT INTO country(country_name,home_node,development) VALUES ("BGL", "doab", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JFN", "comorin_cape", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GHR", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHD", "deccan", 13.829);
INSERT INTO country(country_name,home_node,development) VALUES ("NGP", "ganges_delta", 10.4);
INSERT INTO country(country_name,home_node,development) VALUES ("JAJ", "gujarat", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TPR", "burma", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DGL", "comorin_cape", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SKK", "lhasa", 6.968);
INSERT INTO country(country_name,home_node,development) VALUES ("IDR", "gujarat", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SBP", "ganges_delta", 11.924);
INSERT INTO country(country_name,home_node,development) VALUES ("PTT", "ganges_delta", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("RTT", "ganges_delta", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KLH", "ganges_delta", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KJH", "ganges_delta", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PRD", "ganges_delta", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JPR", "ganges_delta", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SRG", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KND", "comorin_cape", 17.671);
INSERT INTO country(country_name,home_node,development) VALUES ("DNG", "doab", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DTI", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GRK", "lhasa", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JML", "doab", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MKP", "ganges_delta", 11.652);
INSERT INTO country(country_name,home_node,development) VALUES ("SRM", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KTU", "lhasa", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KMN", "doab", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GNG", "comorin_cape", 15.502);
INSERT INTO country(country_name,home_node,development) VALUES ("TNJ", "comorin_cape", 21.712);
INSERT INTO country(country_name,home_node,development) VALUES ("HSA", "lubeck", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ABE", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("APA", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ASI", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BLA", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CAD", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHI", "mississippi_river", 19.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHO", "mississippi_river", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHY", "james_bay", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COM", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("FOX", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LEN", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAH", "st_lawrence", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MIK", "st_lawrence", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MMI", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NAH", "rio_grande", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OJI", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OSA", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OTT", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PAW", "mississippi_river", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PEQ", "chesapeake_bay", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PIM", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("POT", "ohio", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("POW", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SHO", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SIO", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SUS", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WCR", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AIR", "katsina", 26.856);
INSERT INTO country(country_name,home_node,development) VALUES ("BON", "timbuktu", 11.95);
INSERT INTO country(country_name,home_node,development) VALUES ("DAH", "timbuktu", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("DGB", "timbuktu", 5.97);
INSERT INTO country(country_name,home_node,development) VALUES ("FUL", "timbuktu", 59.464);
INSERT INTO country(country_name,home_node,development) VALUES ("JNN", "timbuktu", 38.582);
INSERT INTO country(country_name,home_node,development) VALUES ("KAN", "katsina", 24.52);
INSERT INTO country(country_name,home_node,development) VALUES ("KBU", "ivory_coast", 9.356);
INSERT INTO country(country_name,home_node,development) VALUES ("KNG", "timbuktu", 12.866);
INSERT INTO country(country_name,home_node,development) VALUES ("KTS", "katsina", 21.93);
INSERT INTO country(country_name,home_node,development) VALUES ("NUP", "katsina", 11.682);
INSERT INTO country(country_name,home_node,development) VALUES ("TMB", "timbuktu", 47.586);
INSERT INTO country(country_name,home_node,development) VALUES ("YAO", "katsina", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YAT", "timbuktu", 16.628);
INSERT INTO country(country_name,home_node,development) VALUES ("ZAF", "timbuktu", 28.532);
INSERT INTO country(country_name,home_node,development) VALUES ("ZZZ", "katsina", 11.97);
INSERT INTO country(country_name,home_node,development) VALUES ("NDO", "ivory_coast", 12.94);
INSERT INTO country(country_name,home_node,development) VALUES ("JOH", "malacca", 49.756);
INSERT INTO country(country_name,home_node,development) VALUES ("KED", "malacca", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PRK", "malacca", 19.936);
INSERT INTO country(country_name,home_node,development) VALUES ("CHU", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HOD", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CHV", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KMC", "girin", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ARP", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CNK", "california", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HDA", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ITZ", "mexico", 26.188);
INSERT INTO country(country_name,home_node,development) VALUES ("KIO", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SAL", "california", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WIC", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BLM", "the_moluccas", 39.56);
INSERT INTO country(country_name,home_node,development) VALUES ("BTN", "the_moluccas", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CRB", "the_moluccas", 28.136);
INSERT INTO country(country_name,home_node,development) VALUES ("PGR", "malacca", 19.812);
INSERT INTO country(country_name,home_node,development) VALUES ("PLB", "malacca", 19.664);
INSERT INTO country(country_name,home_node,development) VALUES ("SAK", "malacca", 27.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KUT", "malacca", 28.84);
INSERT INTO country(country_name,home_node,development) VALUES ("BNJ", "malacca", 30.04);
INSERT INTO country(country_name,home_node,development) VALUES ("LNO", "philippines", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LUW", "the_moluccas", 18.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MGD", "philippines", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TER", "the_moluccas", 35.525);
INSERT INTO country(country_name,home_node,development) VALUES ("TID", "the_moluccas", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GUA", "laplata", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ZNI", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MSC", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LIP", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MIS", "panama", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YAQ", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YKT", "california", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PSS", "wien", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ROT", "rheinland", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BYT", "rheinland", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("REG", "wien", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TTL", "rheinland", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OPL", "krakow", 16.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WOL", "lubeck", 31.55);
INSERT INTO country(country_name,home_node,development) VALUES ("STE", "lubeck", 20.91);
INSERT INTO country(country_name,home_node,development) VALUES ("GOS", "saxony", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TNT", "wien", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLH", "rheinland", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BAM", "rheinland", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("BNE", "the_moluccas", 16.208);
INSERT INTO country(country_name,home_node,development) VALUES ("BEU", "malacca", 18.87);
INSERT INTO country(country_name,home_node,development) VALUES ("SMB", "malacca", 30.78);
INSERT INTO country(country_name,home_node,development) VALUES ("BRS", "malacca", 16.886);
INSERT INTO country(country_name,home_node,development) VALUES ("JMB", "malacca", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("IND", "malacca", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TIW", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LAR", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YOL", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("YNU", "australia", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AWN", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("GMI", "australia", 13.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MIA", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("EOR", "australia", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAU", "australia", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PLW", "australia", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WRU", "australia", 14.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NOO", "australia", 12.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLG", "australia", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAA", "polynesia_node", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAN", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAK", "polynesia_node", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TNK", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TEA", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TTT", "polynesia_node", 10.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WAI", "polynesia_node", 4.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HAW", "polynesia_node", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MAU", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OAH", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KAA", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TOG", "polynesia_node", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SAM", "polynesia_node", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("VIL", "polynesia_node", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("VNL", "polynesia_node", 5.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LAI", "polynesia_node", 3.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ALT", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ICH", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COF", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("JOA", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SAT", "chesapeake_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ABI", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("COW", "mississippi_river", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NTZ", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PCH", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("QUI", "mississippi_river", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CCA", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KSI", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OEO", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NTC", "mississippi_river", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HNI", "rio_grande", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MOH", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ONE", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ONO", "ohio", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CAY", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("SEN", "ohio", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TAH", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ATT", "ohio", 11.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AGG", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ATW", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ARN", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TIO", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("OSH", "st_lawrence", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("ERI", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WEN", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("TSC", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CAO", "ohio", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PEO", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KSK", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("PEN", "st_lawrence", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MLS", "st_lawrence", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NEH", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("NAK", "james_bay", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("HWK", "ohio", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("CLG", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("KSP", "ohio", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("MSG", "ohio", 7.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WCY", "mississippi_river", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("LAK", "mississippi_river", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("INN", "st_lawrence", 6.0);
INSERT INTO country(country_name,home_node,development) VALUES ("WAM", "chesapeake_bay", 8.0);
INSERT INTO country(country_name,home_node,development) VALUES ("AGQ", "st_lawrence", 9.0);
INSERT INTO country(country_name,home_node,development) VALUES ("C00", "brazil", 132.806);
INSERT INTO country(country_name,home_node,development) VALUES ("C01", "chesapeake_bay", 18.0);
INSERT INTO country(country_name,home_node,development) VALUES ("C02", "mexico", 476.541);
INSERT INTO country(country_name,home_node,development) VALUES ("C03", "carribean_trade", 158.422);
INSERT INTO country(country_name,home_node,development) VALUES ("C04", "lima", 313.32);
INSERT INTO country(country_name,home_node,development) VALUES ("C05", "lima", 206.523);
INSERT INTO country(country_name,home_node,development) VALUES ("C06", "cuiaba", 128.781);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("african_great_lakes", 1.63, TRUE, 549.896, 0.4298826505375561, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("kongo", 3.309, TRUE, 756.4940000000001, 0.5308896431885539, 0.22568839153221695);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("zambezi", 1.96, TRUE, 515.202, 0.45575444272634597, 0.2787170626739908);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("patagonia", 0.213, FALSE, 185.166, 0.1065, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("amazonas_node", 1.368, FALSE, 412.73800000000006, 0.6839999999999999, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("rio_grande", 1.887, FALSE, 370.28999999999996, 0.6819067325609657, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("james_bay", 1.713, FALSE, 150.558, 0.06596775993304907, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("california", 1.583, FALSE, 362.562, 0.4850848350741127, 0.27330043036118873);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("girin", 4.736, FALSE, 747.2580000000002, 0.64462169543269, 0.12733476920695458);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("mississippi_river", 3.257, FALSE, 410.94399999999996, 0.7255984293271156, 0.3660021256032926);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ohio", 7.592, TRUE, 837.044, 0.515413421182032, 0.3809391753967357);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("mexico", 9.379, FALSE, 865.264, 1.0828743705674002, 0.3660021256032926);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lhasa", 1.197, TRUE, 542.1120000000001, 0.29699339250929696, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("chengdu", 2.615, TRUE, 680.974, 1.1332735139657597, 0.10394768737825394);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("burma", 5.582, FALSE, 605.548, 1.1791561332318092, 0.39664572988801594);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("gulf_of_siam", 8.491, FALSE, 936.755, 1.8851663384121975, 0.6190569699466999);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("canton", 5.209, FALSE, 672.4299999999997, 2.4555529376397467, 1.3863580575544197);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("philippines", 3.815, FALSE, 430.18799999999993, 1.3716973222546658, 0.8594435281739115);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("cuiaba", 2.314, FALSE, 594.06, 0.8857164587752081, 0.351225);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lima", 2.685, FALSE, 487.712, 0.0038872028603799936, 0.31000076057132286);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("polynesia_node", 1.975, FALSE, 392.41999999999996, 0.2927106391624609, 1.2285226745909439);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("australia", 2.108, FALSE, 374.76199999999994, 0.0, 0.10244872370686131);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("nippon", 8.371, FALSE, 844.362, 0.3353680136653585, 0.3280663171083028);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("hangzhou", 9.684, FALSE, 796.4839999999997, 5.447789971261272, 1.211579942522538);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("xian", 4.563, TRUE, 655.8760000000001, 3.4331861099147294, 2.303372219829461);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("beijing", 4.506, FALSE, 486.984, 2.1042812336076686, 3.9347667910481197);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("the_moluccas", 9.368, FALSE, 1046.734, 1.214884226200091, 0.7201410941836995);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("siberia", 3.662, TRUE, 544.544, 1.9438087967007207, 0.2256175934014415);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("yumen", 3.079, TRUE, 843.265, 2.1205020444188114, 4.011918002993285);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("malacca", 12.867, FALSE, 1415.865, 1.4035868891311516, 5.031510783291855);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ganges_delta", 8.926, FALSE, 1156.1240000000003, 3.5461756809352845, 1.4598877741188083);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("doab", 8.376, TRUE, 1046.6380000000001, 1.3054652648123963, 1.8617422324910244);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lahore", 6.274, TRUE, 1012.592, 3.438800863498156, 0.789316951404762);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("deccan", 8.405, TRUE, 903.0880000000001, 2.1983665672945065, 0.6853692640265081);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("comorin_cape", 11.101, FALSE, 925.39, 2.8656182112884205, 3.0158846803206405);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("gujarat", 6.696, FALSE, 611.4979999999998, 3.533881133851508, 3.360689124004918);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("katsina", 3.951, TRUE, 850.9439999999998, 0.6222886288639442, 0);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ethiopia", 3.07, TRUE, 727.9239999999999, 0.34462733147256, 0.21780102010238048);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("gulf_of_aden", 6.069, FALSE, 634.6580000000001, 2.1825074818556915, 2.4207541198220692);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("hormuz", 4.433, FALSE, 309.55799999999994, 1.8552291062979176, 2.00073601549752);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("zanzibar", 4.543, FALSE, 464.3620000000001, 0.6510904802286137, 2.7049665718924003);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("cape_of_good_hope", 0.0, FALSE, 23.352, 0.0, 2.423494494984846);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("basra", 3.558, FALSE, 550.746, 1.5549850057072716, 1.9479905616128135);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("samarkand", 4.195, TRUE, 1102.168, 0.39791969581903247, 4.450607067131985);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("persia", 7.622, TRUE, 620.392, 0.7560835333412107, 2.2288552705256643);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("aleppo", 4.408, FALSE, 537.03, 2.810655491500227, 1.2133109830004534);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("alexandria", 6.969, FALSE, 826.028, 3.8300751445268233, 2.420401100710205);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("astrakhan", 2.601, TRUE, 456.1720000000001, 1.1616517487694586, 0.6058516953091277);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("crimea", 4.231, FALSE, 695.002, 1.6266311666774478, 0.6098671681039658);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("constantinople", 6.85, FALSE, 558.624, 0.6066061546017363, 3.385441341959114);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("kiev", 3.633, TRUE, 696.5239999999999, 2.101160454168554, 0.5693209083371068);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("kazan", 3.055, TRUE, 460.46799999999996, 2.342683393185922, 1.6303667863718443);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("novgorod", 6.418, FALSE, 645.44, 1.3804340521563514, 3.5629268012837088);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("laplata", 1.156, FALSE, 212.92, 0.7259317552664258, 0.42182576057132287);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("brazil", 3.266, FALSE, 415.124, 0.26381321888050796, 1.31162910360107);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("timbuktu", 5.139, TRUE, 1209.262, 0.943053412385038, 0.21780102010238048);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ivory_coast", 2.903, FALSE, 886.1960000000001, 1.2778868549637499, 1.0508239840006692);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("tunis", 2.421, FALSE, 702.868, 0.6962449392759471, 0.21780102010238048);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("ragusa", 5.129, FALSE, 835.5280000000001, 2.280225507411606, 0.6369364623318231);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("safi", 3.418, FALSE, 428.86, 0.5860530132272567, 0.495103041502145);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("pest", 3.378, TRUE, 802.514, 1.7598887869488957, 1.367399835931169);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("krakow", 5.99, TRUE, 1494.342, 2.5482753833075655, 2.027050851586661);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("wien", 6.809, TRUE, 1886.8679999999995, 2.6514341644679003, 1.8158379973058183);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("saxony", 6.423, TRUE, 1325.0579999999995, 1.921836272747858, 1.8198983417214132);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("baltic_sea", 7.288, FALSE, 639.0319999999999, 2.3056108775685162, 1.6166242615397326);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("rheinland", 9.031, TRUE, 1636.6120000000003, 1.5846202183393807, 1.9369660007563905);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("panama", 5.251, FALSE, 342.74, 2.7536922408286855, 0.4834955349071509);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("carribean_trade", 7.527, FALSE, 849.1220000000001, 1.9485861798883226, 4.226167357393431);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("chesapeake_bay", 6.651, FALSE, 494.4299999999999, 1.423527252100068, 0.9525972090814798);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("st_lawrence", 3.754, FALSE, 243.97000000000003, 0.9256753573622668, 1.0525769274379533);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("white_sea", 1.753, FALSE, 116.866, 1.2388639386910425, 0.7247278773820846);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("north_sea", 3.821, FALSE, 723.4680000000002, 1.9447052211499776, 1.7867866982407847);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("lubeck", 5.32, FALSE, 1404.298, 0.5468318456119889, 5.282751320371481);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("bordeaux", 4.405, FALSE, 577.4219999999999, 2.8248566361739833, 1.5034300250040875);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("sevilla", 7.019, FALSE, 707.826, 0.08573441133127971, 1.8764918550240983);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("champagne", 9.853, TRUE, 1295.64, 3.0338877493461753, 3.7980250826108577);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("valencia", 3.593, FALSE, 388.68399999999997, 1.963353430322213, 0.33370686064442523);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("genua", 10.184, FALSE, 1063.496, 1.3700550985657758, 6.036603127170098);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("venice", 7.791, FALSE, 605.5620000000001, 0.6144184791004293, 3.0666071857422157);
INSERT INTO trade_node(trade_node, local_value, is_inland, total_power, outgoing, ingoing) VALUES ("english_channel", 10.555, FALSE, 629.488, 1.5372186481564518, 4.270731854183589);
INSERT INTO flow(source, dest, flow) VALUES ("african_great_lakes", "zanzibar", 0.21494132526877804);
INSERT INTO flow(source, dest, flow) VALUES ("african_great_lakes", "kongo", 0.21494132526877804);
INSERT INTO flow(source, dest, flow) VALUES ("kongo", "ivory_coast", 0.26544482159427696);
INSERT INTO flow(source, dest, flow) VALUES ("kongo", "zambezi", 0.26544482159427696);
INSERT INTO flow(source, dest, flow) VALUES ("zambezi", "zanzibar", 0.45575444272634597);
INSERT INTO flow(source, dest, flow) VALUES ("patagonia", "laplata", 0.05325);
INSERT INTO flow(source, dest, flow) VALUES ("patagonia", "cuiaba", 0.05325);
INSERT INTO flow(source, dest, flow) VALUES ("amazonas_node", "brazil", 0.22799999999999998);
INSERT INTO flow(source, dest, flow) VALUES ("amazonas_node", "carribean_trade", 0.22799999999999998);
INSERT INTO flow(source, dest, flow) VALUES ("amazonas_node", "cuiaba", 0.22799999999999998);
INSERT INTO flow(source, dest, flow) VALUES ("rio_grande", "mississippi_river", 0.22730224418698855);
INSERT INTO flow(source, dest, flow) VALUES ("rio_grande", "mexico", 0.22730224418698855);
INSERT INTO flow(source, dest, flow) VALUES ("rio_grande", "california", 0.22730224418698855);
INSERT INTO flow(source, dest, flow) VALUES ("james_bay", "st_lawrence", 0.032983879966524535);
INSERT INTO flow(source, dest, flow) VALUES ("james_bay", "california", 0.032983879966524535);
INSERT INTO flow(source, dest, flow) VALUES ("california", "mexico", 0.12127120876852818);
INSERT INTO flow(source, dest, flow) VALUES ("california", "mississippi_river", 0.12127120876852818);
INSERT INTO flow(source, dest, flow) VALUES ("california", "girin", 0.12127120876852818);
INSERT INTO flow(source, dest, flow) VALUES ("california", "polynesia_node", 0.12127120876852818);
INSERT INTO flow(source, dest, flow) VALUES ("girin", "siberia", 0.21487389847756333);
INSERT INTO flow(source, dest, flow) VALUES ("girin", "beijing", 0.21487389847756333);
INSERT INTO flow(source, dest, flow) VALUES ("girin", "nippon", 0.21487389847756333);
INSERT INTO flow(source, dest, flow) VALUES ("mississippi_river", "carribean_trade", 0.3627992146635578);
INSERT INTO flow(source, dest, flow) VALUES ("mississippi_river", "ohio", 0.3627992146635578);
INSERT INTO flow(source, dest, flow) VALUES ("ohio", "chesapeake_bay", 0.257706710591016);
INSERT INTO flow(source, dest, flow) VALUES ("ohio", "st_lawrence", 0.257706710591016);
INSERT INTO flow(source, dest, flow) VALUES ("mexico", "carribean_trade", 0.36095812352246676);
INSERT INTO flow(source, dest, flow) VALUES ("mexico", "panama", 0.36095812352246676);
INSERT INTO flow(source, dest, flow) VALUES ("mexico", "polynesia_node", 0.36095812352246676);
INSERT INTO flow(source, dest, flow) VALUES ("lhasa", "lahore", 0.09899779750309899);
INSERT INTO flow(source, dest, flow) VALUES ("lhasa", "chengdu", 0.09899779750309899);
INSERT INTO flow(source, dest, flow) VALUES ("lhasa", "ganges_delta", 0.09899779750309899);
INSERT INTO flow(source, dest, flow) VALUES ("chengdu", "canton", 0.3777578379885866);
INSERT INTO flow(source, dest, flow) VALUES ("chengdu", "xian", 0.3777578379885866);
INSERT INTO flow(source, dest, flow) VALUES ("chengdu", "burma", 0.3777578379885866);
INSERT INTO flow(source, dest, flow) VALUES ("burma", "ganges_delta", 0.5895780666159046);
INSERT INTO flow(source, dest, flow) VALUES ("burma", "gulf_of_siam", 0.5895780666159046);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_siam", "malacca", 0.9425831692060987);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_siam", "canton", 0.9425831692060987);
INSERT INTO flow(source, dest, flow) VALUES ("canton", "malacca", 0.8185176458799156);
INSERT INTO flow(source, dest, flow) VALUES ("canton", "hangzhou", 0.8185176458799156);
INSERT INTO flow(source, dest, flow) VALUES ("canton", "philippines", 0.8185176458799156);
INSERT INTO flow(source, dest, flow) VALUES ("philippines", "the_moluccas", 0.6858486611273329);
INSERT INTO flow(source, dest, flow) VALUES ("philippines", "polynesia_node", 0.6858486611273329);
INSERT INTO flow(source, dest, flow) VALUES ("cuiaba", "laplata", 0.29523881959173603);
INSERT INTO flow(source, dest, flow) VALUES ("cuiaba", "lima", 0.29523881959173603);
INSERT INTO flow(source, dest, flow) VALUES ("cuiaba", "brazil", 0.29523881959173603);
INSERT INTO flow(source, dest, flow) VALUES ("lima", "panama", 0.0019436014301899968);
INSERT INTO flow(source, dest, flow) VALUES ("lima", "polynesia_node", 0.0019436014301899968);
INSERT INTO flow(source, dest, flow) VALUES ("polynesia_node", "nippon", 0.09757021305415363);
INSERT INTO flow(source, dest, flow) VALUES ("polynesia_node", "australia", 0.09757021305415363);
INSERT INTO flow(source, dest, flow) VALUES ("polynesia_node", "panama", 0.09757021305415363);
INSERT INTO flow(source, dest, flow) VALUES ("australia", "the_moluccas", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("nippon", "hangzhou", 0.3353680136653585);
INSERT INTO flow(source, dest, flow) VALUES ("hangzhou", "xian", 1.8159299904204238);
INSERT INTO flow(source, dest, flow) VALUES ("hangzhou", "beijing", 1.8159299904204238);
INSERT INTO flow(source, dest, flow) VALUES ("hangzhou", "malacca", 1.8159299904204238);
INSERT INTO flow(source, dest, flow) VALUES ("xian", "beijing", 1.7165930549573647);
INSERT INTO flow(source, dest, flow) VALUES ("xian", "yumen", 1.7165930549573647);
INSERT INTO flow(source, dest, flow) VALUES ("beijing", "yumen", 2.1042812336076686);
INSERT INTO flow(source, dest, flow) VALUES ("the_moluccas", "malacca", 1.214884226200091);
INSERT INTO flow(source, dest, flow) VALUES ("siberia", "kazan", 0.9719043983503604);
INSERT INTO flow(source, dest, flow) VALUES ("siberia", "samarkand", 0.9719043983503604);
INSERT INTO flow(source, dest, flow) VALUES ("yumen", "samarkand", 2.1205020444188114);
INSERT INTO flow(source, dest, flow) VALUES ("malacca", "ganges_delta", 0.7017934445655758);
INSERT INTO flow(source, dest, flow) VALUES ("malacca", "cape_of_good_hope", 0.7017934445655758);
INSERT INTO flow(source, dest, flow) VALUES ("ganges_delta", "comorin_cape", 1.7730878404676422);
INSERT INTO flow(source, dest, flow) VALUES ("ganges_delta", "doab", 1.7730878404676422);
INSERT INTO flow(source, dest, flow) VALUES ("doab", "deccan", 0.6527326324061982);
INSERT INTO flow(source, dest, flow) VALUES ("doab", "lahore", 0.6527326324061982);
INSERT INTO flow(source, dest, flow) VALUES ("lahore", "samarkand", 1.1462669544993853);
INSERT INTO flow(source, dest, flow) VALUES ("lahore", "persia", 1.1462669544993853);
INSERT INTO flow(source, dest, flow) VALUES ("lahore", "gujarat", 1.1462669544993853);
INSERT INTO flow(source, dest, flow) VALUES ("deccan", "gujarat", 1.0991832836472533);
INSERT INTO flow(source, dest, flow) VALUES ("deccan", "comorin_cape", 1.0991832836472533);
INSERT INTO flow(source, dest, flow) VALUES ("comorin_cape", "gulf_of_aden", 0.9552060704294735);
INSERT INTO flow(source, dest, flow) VALUES ("comorin_cape", "gujarat", 0.9552060704294735);
INSERT INTO flow(source, dest, flow) VALUES ("comorin_cape", "cape_of_good_hope", 0.9552060704294735);
INSERT INTO flow(source, dest, flow) VALUES ("gujarat", "gulf_of_aden", 1.1779603779505028);
INSERT INTO flow(source, dest, flow) VALUES ("gujarat", "hormuz", 1.1779603779505028);
INSERT INTO flow(source, dest, flow) VALUES ("gujarat", "zanzibar", 1.1779603779505028);
INSERT INTO flow(source, dest, flow) VALUES ("katsina", "timbuktu", 0.20742954295464808);
INSERT INTO flow(source, dest, flow) VALUES ("katsina", "tunis", 0.20742954295464808);
INSERT INTO flow(source, dest, flow) VALUES ("katsina", "ethiopia", 0.20742954295464808);
INSERT INTO flow(source, dest, flow) VALUES ("ethiopia", "alexandria", 0.17231366573628);
INSERT INTO flow(source, dest, flow) VALUES ("ethiopia", "gulf_of_aden", 0.17231366573628);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_aden", "zanzibar", 0.7275024939518971);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_aden", "alexandria", 0.7275024939518971);
INSERT INTO flow(source, dest, flow) VALUES ("gulf_of_aden", "hormuz", 0.7275024939518971);
INSERT INTO flow(source, dest, flow) VALUES ("hormuz", "basra", 1.8552291062979176);
INSERT INTO flow(source, dest, flow) VALUES ("zanzibar", "cape_of_good_hope", 0.6510904802286137);
INSERT INTO flow(source, dest, flow) VALUES ("cape_of_good_hope", "ivory_coast", 0.0);
INSERT INTO flow(source, dest, flow) VALUES ("basra", "aleppo", 0.7774925028536358);
INSERT INTO flow(source, dest, flow) VALUES ("basra", "persia", 0.7774925028536358);
INSERT INTO flow(source, dest, flow) VALUES ("samarkand", "persia", 0.19895984790951624);
INSERT INTO flow(source, dest, flow) VALUES ("samarkand", "astrakhan", 0.19895984790951624);
INSERT INTO flow(source, dest, flow) VALUES ("persia", "aleppo", 0.37804176667060535);
INSERT INTO flow(source, dest, flow) VALUES ("persia", "astrakhan", 0.37804176667060535);
INSERT INTO flow(source, dest, flow) VALUES ("aleppo", "constantinople", 1.4053277457501134);
INSERT INTO flow(source, dest, flow) VALUES ("aleppo", "alexandria", 1.4053277457501134);
INSERT INTO flow(source, dest, flow) VALUES ("alexandria", "constantinople", 1.2766917148422745);
INSERT INTO flow(source, dest, flow) VALUES ("alexandria", "venice", 1.2766917148422745);
INSERT INTO flow(source, dest, flow) VALUES ("alexandria", "genua", 1.2766917148422745);
INSERT INTO flow(source, dest, flow) VALUES ("astrakhan", "kazan", 0.5808258743847293);
INSERT INTO flow(source, dest, flow) VALUES ("astrakhan", "crimea", 0.5808258743847293);
INSERT INTO flow(source, dest, flow) VALUES ("crimea", "kiev", 0.5422103888924826);
INSERT INTO flow(source, dest, flow) VALUES ("crimea", "constantinople", 0.5422103888924826);
INSERT INTO flow(source, dest, flow) VALUES ("crimea", "pest", 0.5422103888924826);
INSERT INTO flow(source, dest, flow) VALUES ("constantinople", "ragusa", 0.6066061546017363);
INSERT INTO flow(source, dest, flow) VALUES ("kiev", "novgorod", 1.050580227084277);
INSERT INTO flow(source, dest, flow) VALUES ("kiev", "krakow", 1.050580227084277);
INSERT INTO flow(source, dest, flow) VALUES ("kazan", "novgorod", 2.342683393185922);
INSERT INTO flow(source, dest, flow) VALUES ("novgorod", "baltic_sea", 0.6902170260781757);
INSERT INTO flow(source, dest, flow) VALUES ("novgorod", "white_sea", 0.6902170260781757);
INSERT INTO flow(source, dest, flow) VALUES ("laplata", "brazil", 0.7259317552664258);
INSERT INTO flow(source, dest, flow) VALUES ("brazil", "ivory_coast", 0.26381321888050796);
INSERT INTO flow(source, dest, flow) VALUES ("timbuktu", "safi", 0.471526706192519);
INSERT INTO flow(source, dest, flow) VALUES ("timbuktu", "ivory_coast", 0.471526706192519);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "carribean_trade", 0.31947171374093747);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "bordeaux", 0.31947171374093747);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "english_channel", 0.31947171374093747);
INSERT INTO flow(source, dest, flow) VALUES ("ivory_coast", "sevilla", 0.31947171374093747);
INSERT INTO flow(source, dest, flow) VALUES ("tunis", "sevilla", 0.2320816464253157);
INSERT INTO flow(source, dest, flow) VALUES ("tunis", "valencia", 0.2320816464253157);
INSERT INTO flow(source, dest, flow) VALUES ("tunis", "genua", 0.2320816464253157);
INSERT INTO flow(source, dest, flow) VALUES ("ragusa", "pest", 0.760075169137202);
INSERT INTO flow(source, dest, flow) VALUES ("ragusa", "venice", 0.760075169137202);
INSERT INTO flow(source, dest, flow) VALUES ("ragusa", "genua", 0.760075169137202);
INSERT INTO flow(source, dest, flow) VALUES ("safi", "sevilla", 0.5860530132272567);
INSERT INTO flow(source, dest, flow) VALUES ("pest", "wien", 0.8799443934744479);
INSERT INTO flow(source, dest, flow) VALUES ("pest", "krakow", 0.8799443934744479);
INSERT INTO flow(source, dest, flow) VALUES ("krakow", "wien", 0.8494251277691885);
INSERT INTO flow(source, dest, flow) VALUES ("krakow", "saxony", 0.8494251277691885);
INSERT INTO flow(source, dest, flow) VALUES ("krakow", "baltic_sea", 0.8494251277691885);
INSERT INTO flow(source, dest, flow) VALUES ("wien", "venice", 0.8838113881559667);
INSERT INTO flow(source, dest, flow) VALUES ("wien", "rheinland", 0.8838113881559667);
INSERT INTO flow(source, dest, flow) VALUES ("wien", "saxony", 0.8838113881559667);
INSERT INTO flow(source, dest, flow) VALUES ("saxony", "rheinland", 0.960918136373929);
INSERT INTO flow(source, dest, flow) VALUES ("saxony", "lubeck", 0.960918136373929);
INSERT INTO flow(source, dest, flow) VALUES ("baltic_sea", "lubeck", 2.3056108775685162);
INSERT INTO flow(source, dest, flow) VALUES ("rheinland", "champagne", 0.7923101091696904);
INSERT INTO flow(source, dest, flow) VALUES ("rheinland", "lubeck", 0.7923101091696904);
INSERT INTO flow(source, dest, flow) VALUES ("panama", "carribean_trade", 2.7536922408286855);
INSERT INTO flow(source, dest, flow) VALUES ("carribean_trade", "chesapeake_bay", 0.6495287266294408);
INSERT INTO flow(source, dest, flow) VALUES ("carribean_trade", "bordeaux", 0.6495287266294408);
INSERT INTO flow(source, dest, flow) VALUES ("carribean_trade", "sevilla", 0.6495287266294408);
INSERT INTO flow(source, dest, flow) VALUES ("chesapeake_bay", "st_lawrence", 0.711763626050034);
INSERT INTO flow(source, dest, flow) VALUES ("chesapeake_bay", "english_channel", 0.711763626050034);
INSERT INTO flow(source, dest, flow) VALUES ("st_lawrence", "north_sea", 0.4628376786811334);
INSERT INTO flow(source, dest, flow) VALUES ("st_lawrence", "bordeaux", 0.4628376786811334);
INSERT INTO flow(source, dest, flow) VALUES ("white_sea", "north_sea", 1.2388639386910425);
INSERT INTO flow(source, dest, flow) VALUES ("north_sea", "english_channel", 0.9723526105749888);
INSERT INTO flow(source, dest, flow) VALUES ("north_sea", "lubeck", 0.9723526105749888);
INSERT INTO flow(source, dest, flow) VALUES ("lubeck", "english_channel", 0.5468318456119889);
INSERT INTO flow(source, dest, flow) VALUES ("bordeaux", "champagne", 2.8248566361739833);
INSERT INTO flow(source, dest, flow) VALUES ("sevilla", "valencia", 0.08573441133127971);
INSERT INTO flow(source, dest, flow) VALUES ("champagne", "genua", 1.5169438746730877);
INSERT INTO flow(source, dest, flow) VALUES ("champagne", "english_channel", 1.5169438746730877);
INSERT INTO flow(source, dest, flow) VALUES ("valencia", "genua", 1.963353430322213);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "POR", FALSE, FALSE, 15.745, 15.745);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KON", FALSE, FALSE, 44.71, 44.71);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "ZAN", FALSE, FALSE, 7.241, 7.241);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "MLI", FALSE, FALSE, 2.347, 2.347);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "LUB", FALSE, FALSE, 42.223, 42.223);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "LND", FALSE, FALSE, 10.945, 10.945);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KZB", FALSE, FALSE, 9.081, 9.081);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KUB", FALSE, FALSE, 7.398, 7.398);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "RWA", TRUE, FALSE, 17.475, 17.475);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BUU", TRUE, FALSE, 12.269, 12.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BUG", TRUE, FALSE, 26.691, 26.691);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "NKO", TRUE, FALSE, 12.269, 12.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "KRW", TRUE, FALSE, 15.124, 15.124);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BNY", TRUE, FALSE, 15.194, 15.194);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BSG", TRUE, FALSE, 12.269, 12.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "UBH", TRUE, FALSE, 18.632, 18.632);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "SKA", FALSE, FALSE, 2.926, 2.926);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("african_great_lakes", "BTS", FALSE, FALSE, 2.409, 2.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "POR", FALSE, FALSE, 12.053, 12.053);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "BEN", FALSE, FALSE, 5.743, 5.743);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KON", TRUE, FALSE, 70.849, 70.849);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "ZIM", FALSE, FALSE, 41.604, 41.604);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "LOA", FALSE, FALSE, 9.566, 9.566);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "JOL", FALSE, FALSE, 3.678, 3.678);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "SYO", FALSE, FALSE, 11.25, 11.25);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KSJ", TRUE, FALSE, 16.318, 16.318);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "LUB", TRUE, FALSE, 67.573, 67.573);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "LND", TRUE, FALSE, 27.181, 27.181);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "CKW", TRUE, FALSE, 12.269, 12.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KIK", TRUE, FALSE, 10.149, 10.149);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KZB", TRUE, FALSE, 21.115, 21.115);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "YAK", TRUE, FALSE, 14.583, 14.583);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KUB", TRUE, FALSE, 24.589, 24.589);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "MRA", FALSE, FALSE, 13.445, 13.445);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "TBK", FALSE, FALSE, 6.109, 6.109);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "KBU", FALSE, FALSE, 2.209, 2.209);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kongo", "NDO", FALSE, FALSE, 7.964, 7.964);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "POR", FALSE, FALSE, 33.932, 33.932);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "ZAN", FALSE, FALSE, 43.427, 43.427);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "ZIM", TRUE, FALSE, 66.408, 66.408);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "MLI", FALSE, FALSE, 2.347, 2.347);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "MRA", TRUE, FALSE, 35.085, 35.085);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "LDU", TRUE, FALSE, 22.742, 22.742);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "TBK", TRUE, FALSE, 14.998, 14.998);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "RZW", TRUE, FALSE, 13.484, 13.484);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "SKA", FALSE, FALSE, 14.361, 14.361);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zambezi", "BTS", FALSE, FALSE, 10.817, 10.817);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("patagonia", "C05", FALSE, FALSE, 18.547, 18.547);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("patagonia", "C06", FALSE, FALSE, 74.036, 74.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "FRA", FALSE, FALSE, 0.5, 0.5);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "NED", FALSE, FALSE, 2.902, 2.902);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "C00", FALSE, FALSE, 58.96, 58.96);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "C03", FALSE, FALSE, 59.316, 59.316);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "C04", FALSE, FALSE, 36.897, 36.897);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "C05", FALSE, FALSE, 13.386, 13.386);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("amazonas_node", "C06", FALSE, FALSE, 34.408, 34.408);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "CAD", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "CHO", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "NAH", TRUE, FALSE, 8.758, 8.758);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "CNK", FALSE, FALSE, 3.628, 3.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "WIC", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "ZNI", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "MSC", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "LIP", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "NTZ", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "QUI", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "NTC", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "HNI", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rio_grande", "C02", FALSE, FALSE, 120.094, 120.094);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "FRA", FALSE, FALSE, 2.17, 2.17);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "ASI", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "BLA", TRUE, FALSE, 8.619, 8.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "CHY", TRUE, FALSE, 9.424, 9.424);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "WCR", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "ARP", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "CNK", FALSE, FALSE, 3.628, 3.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "KIO", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "NEH", TRUE, FALSE, 8.619, 8.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("james_bay", "NAK", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "SPA", FALSE, FALSE, 1.165, 1.165);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "APA", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "PIM", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "SHO", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "CNK", TRUE, FALSE, 25.204, 25.204);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "HDA", TRUE, FALSE, 8.801, 8.801);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "SAL", TRUE, FALSE, 9.565, 9.565);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "YAQ", TRUE, FALSE, 8.801, 8.801);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "YKT", TRUE, FALSE, 8.499, 8.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("california", "C02", FALSE, FALSE, 93.579, 93.579);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "RUS", FALSE, FALSE, 14.757, 14.757);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "MRI", FALSE, FALSE, 4.538, 4.538);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "TKG", FALSE, FALSE, 21.014, 21.014);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "ANU", FALSE, FALSE, 2.986, 2.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "KHA", FALSE, FALSE, 16.52, 16.52);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "KOR", FALSE, FALSE, 16.066, 16.066);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "MCH", TRUE, FALSE, 105.503, 105.503);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "MNG", FALSE, FALSE, 23.166, 23.166);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "NVK", TRUE, FALSE, 38.515, 38.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "SOL", TRUE, FALSE, 39.556, 39.556);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "EJZ", TRUE, FALSE, 8.506, 8.506);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "NHX", TRUE, FALSE, 11.322, 11.322);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "MYR", TRUE, FALSE, 7.274, 7.274);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "MHX", TRUE, FALSE, 9.013, 9.013);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "KRC", TRUE, FALSE, 14.539, 14.539);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "CHU", TRUE, FALSE, 14.272, 14.272);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "HOD", TRUE, FALSE, 8.628, 8.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "CHV", TRUE, FALSE, 8.727, 8.727);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("girin", "KMC", TRUE, FALSE, 8.727, 8.727);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CAD", TRUE, FALSE, 9.359, 9.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CHI", TRUE, FALSE, 11.808, 11.808);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CHO", TRUE, FALSE, 11.223, 11.223);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "COM", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "FOX", FALSE, FALSE, 3.095, 3.095);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "PAW", TRUE, FALSE, 8.74, 8.74);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "ABI", TRUE, FALSE, 8.654, 8.654);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "COW", TRUE, FALSE, 9.334, 9.334);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "NTZ", TRUE, FALSE, 9.359, 9.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "PCH", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "QUI", TRUE, FALSE, 9.892, 9.892);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "CCA", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "NTC", TRUE, FALSE, 9.892, 9.892);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "WCY", TRUE, FALSE, 9.242, 9.242);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "LAK", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "C03", FALSE, FALSE, 59.316, 59.316);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mississippi_river", "C04", FALSE, FALSE, 15.847, 15.847);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ENG", FALSE, FALSE, 3.805, 3.805);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "FRA", FALSE, FALSE, 2.17, 2.17);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CHE", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "FOX", TRUE, FALSE, 25.568, 25.568);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "LEN", FALSE, FALSE, 7.766, 7.766);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "MMI", TRUE, FALSE, 10.655, 10.655);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "OJI", TRUE, FALSE, 12.475, 12.475);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "OSA", TRUE, FALSE, 10.554, 10.554);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "OTT", TRUE, FALSE, 10.655, 10.655);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "POT", TRUE, FALSE, 11.253, 11.253);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "POW", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "SIO", TRUE, FALSE, 10.655, 10.655);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "SUS", TRUE, FALSE, 10.554, 10.554);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ALT", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ICH", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "COF", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "JOA", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "SAT", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CCA", TRUE, FALSE, 11.732, 11.732);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "KSI", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "OEO", TRUE, FALSE, 12.451, 12.451);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "MOH", TRUE, FALSE, 12.451, 12.451);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ONE", TRUE, FALSE, 11.851, 11.851);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ONO", TRUE, FALSE, 13.647, 13.647);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CAY", TRUE, FALSE, 11.851, 11.851);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "SEN", TRUE, FALSE, 11.253, 11.253);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "TAH", TRUE, FALSE, 11.851, 11.851);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ATT", TRUE, FALSE, 13.647, 13.647);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "AGG", TRUE, FALSE, 12.269, 12.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ATW", TRUE, FALSE, 12.475, 12.475);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ARN", TRUE, FALSE, 11.851, 11.851);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "TIO", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "ERI", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "WEN", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "TSC", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CAO", TRUE, FALSE, 12.245, 12.245);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "PEO", TRUE, FALSE, 10.533, 10.533);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "KSK", TRUE, FALSE, 11.851, 11.851);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "HWK", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "CLG", TRUE, FALSE, 10.676, 10.676);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "KSP", TRUE, FALSE, 10.655, 10.655);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ohio", "MSG", TRUE, FALSE, 11.253, 11.253);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "ITZ", TRUE, FALSE, 14.147, 14.147);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "C02", TRUE, FALSE, 322.336, 322.336);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "C03", FALSE, FALSE, 59.316, 59.316);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("mexico", "C04", FALSE, FALSE, 36.833, 36.833);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "ARK", FALSE, FALSE, 10.228, 10.228);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MNG", FALSE, FALSE, 14.173, 14.173);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "KAS", FALSE, FALSE, 1.806, 1.806);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "UTS", TRUE, FALSE, 70.259, 70.259);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "KAM", FALSE, FALSE, 37.691, 37.691);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "GUG", TRUE, FALSE, 13.421, 13.421);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MUG", FALSE, FALSE, 63.146, 63.146);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "BHU", TRUE, FALSE, 11.504, 11.504);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "SKK", TRUE, FALSE, 9.257, 9.257);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "GRK", TRUE, FALSE, 14.611, 14.611);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "MKP", FALSE, FALSE, 7.462, 7.462);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lhasa", "KTU", TRUE, FALSE, 17.498, 17.498);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "DAI", FALSE, FALSE, 3.6, 3.6);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "MNG", FALSE, FALSE, 121.693, 121.693);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "TAU", FALSE, FALSE, 117.728, 117.728);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "TOK", FALSE, FALSE, 24.204, 24.204);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "KAM", TRUE, FALSE, 56.653, 56.653);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chengdu", "ASS", FALSE, FALSE, 16.609, 16.609);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "ANN", FALSE, FALSE, 2.982, 2.982);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "ARK", FALSE, FALSE, 12.242, 12.242);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "AYU", FALSE, FALSE, 9.418, 9.418);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KHM", FALSE, FALSE, 9.318, 9.318);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "LNA", FALSE, FALSE, 4.806, 4.806);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "LXA", FALSE, FALSE, 29.941, 29.941);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "SUK", FALSE, FALSE, 2.014, 2.014);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "TAU", TRUE, FALSE, 137.691, 137.691);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MUG", FALSE, FALSE, 40.728, 40.728);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "ASS", TRUE, FALSE, 20.59, 20.59);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "GRJ", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KAC", TRUE, FALSE, 7.789, 7.789);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KMT", FALSE, FALSE, 1.992, 1.992);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "MLB", TRUE, FALSE, 8.273, 8.273);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "NGP", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "TPR", TRUE, FALSE, 9.0, 9.0);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("burma", "KJH", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "POR", FALSE, FALSE, 8.296, 8.296);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "ANN", FALSE, FALSE, 16.615, 16.615);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "ATJ", FALSE, FALSE, 34.919, 34.919);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "AYU", TRUE, FALSE, 67.585, 67.585);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "BEI", FALSE, FALSE, 29.525, 29.525);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "CHA", TRUE, FALSE, 21.586, 21.586);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "DAI", FALSE, FALSE, 5.41, 5.41);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "KHM", TRUE, FALSE, 50.721, 50.721);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "LNA", TRUE, FALSE, 23.171, 23.171);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "LXA", TRUE, FALSE, 91.221, 91.221);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "MNG", FALSE, FALSE, 33.18, 33.18);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "PAT", FALSE, FALSE, 4.713, 4.713);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "SUK", TRUE, FALSE, 10.671, 10.671);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "TAU", FALSE, FALSE, 13.687, 13.687);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "TOK", FALSE, FALSE, 6.02, 6.02);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "JOH", FALSE, FALSE, 19.206, 19.206);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "KED", FALSE, FALSE, 1.994, 1.994);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "PLB", FALSE, FALSE, 3.714, 3.714);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "SAK", FALSE, FALSE, 5.0, 5.0);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "KUT", FALSE, FALSE, 2.323, 2.323);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "BNJ", FALSE, FALSE, 2.233, 2.233);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "BEU", FALSE, FALSE, 2.131, 2.131);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "SMB", FALSE, FALSE, 2.857, 2.857);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_siam", "BRS", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "POR", FALSE, FALSE, 8.296, 8.296);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "SPA", FALSE, FALSE, 16.883, 16.883);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "ANN", TRUE, FALSE, 13.081, 13.081);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "ATJ", FALSE, FALSE, 34.919, 34.919);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "BEI", FALSE, FALSE, 29.525, 29.525);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "DAI", TRUE, FALSE, 30.048, 30.048);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "MNG", FALSE, FALSE, 115.571, 115.571);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "PAT", FALSE, FALSE, 4.713, 4.713);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "SUL", FALSE, FALSE, 5.007, 5.007);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "TOK", TRUE, FALSE, 42.73, 42.73);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "JOH", FALSE, FALSE, 19.206, 19.206);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "PLB", FALSE, FALSE, 3.714, 3.714);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "SAK", FALSE, FALSE, 2.978, 2.978);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "KUT", FALSE, FALSE, 2.323, 2.323);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "BNJ", FALSE, FALSE, 2.233, 2.233);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "BEU", FALSE, FALSE, 2.131, 2.131);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("canton", "SMB", FALSE, FALSE, 2.857, 2.857);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "SPA", FALSE, FALSE, 46.253, 46.253);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BAN", FALSE, FALSE, 29.561, 29.561);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BEI", FALSE, FALSE, 2.386, 2.386);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "MKS", FALSE, FALSE, 7.254, 7.254);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "MTR", FALSE, FALSE, 26.986, 26.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "SUL", TRUE, FALSE, 38.748, 38.748);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BLM", FALSE, FALSE, 4.876, 4.876);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "CRB", FALSE, FALSE, 2.174, 2.174);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "LNO", TRUE, FALSE, 19.493, 19.493);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "MGD", TRUE, FALSE, 30.616, 30.616);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "TER", FALSE, FALSE, 4.29, 4.29);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("philippines", "BNE", FALSE, FALSE, 2.457, 2.457);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "C00", FALSE, FALSE, 47.77, 47.77);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "C04", FALSE, FALSE, 37.0, 37.0);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "C05", FALSE, FALSE, 112.65, 112.65);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("cuiaba", "C06", TRUE, FALSE, 99.61, 99.61);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "SPA", FALSE, FALSE, 0.633, 0.633);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "C04", TRUE, FALSE, 102.409, 102.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lima", "C05", TRUE, FALSE, 140.814, 140.814);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "MRI", FALSE, FALSE, 2.544, 2.544);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TKG", FALSE, FALSE, 19.236, 19.236);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "KOR", FALSE, FALSE, 14.076, 14.076);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "MAA", TRUE, FALSE, 9.403, 9.403);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TAN", TRUE, FALSE, 9.989, 9.989);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TAK", TRUE, FALSE, 9.111, 9.111);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TNK", TRUE, FALSE, 9.989, 9.989);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TEA", TRUE, FALSE, 9.989, 9.989);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TTT", TRUE, FALSE, 9.989, 9.989);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "WAI", TRUE, FALSE, 8.233, 8.233);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "HAW", TRUE, FALSE, 9.111, 9.111);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "MAU", TRUE, FALSE, 8.526, 8.526);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "OAH", TRUE, FALSE, 15.841, 15.841);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "KAA", TRUE, FALSE, 8.526, 8.526);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "TOG", TRUE, FALSE, 16.685, 16.685);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "SAM", TRUE, FALSE, 9.385, 9.385);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "VIL", TRUE, FALSE, 9.111, 9.111);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "VNL", TRUE, FALSE, 8.526, 8.526);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("polynesia_node", "LAI", TRUE, FALSE, 7.94, 7.94);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "TIW", TRUE, FALSE, 13.439, 13.439);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "LAR", TRUE, FALSE, 23.544, 23.544);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "YOL", TRUE, FALSE, 13.439, 13.439);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "YNU", TRUE, FALSE, 12.854, 12.854);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "AWN", TRUE, FALSE, 13.413, 13.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "GMI", TRUE, FALSE, 10.19, 10.19);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "MIA", TRUE, FALSE, 9.385, 9.385);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "EOR", TRUE, FALSE, 18.438, 18.438);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "KAU", TRUE, FALSE, 17.27, 17.27);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "PLW", TRUE, FALSE, 9.696, 9.696);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "WRU", TRUE, FALSE, 18.474, 18.474);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "NOO", TRUE, FALSE, 17.854, 17.854);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("australia", "MLG", TRUE, FALSE, 9.385, 9.385);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "DTE", TRUE, FALSE, 11.777, 11.777);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "HSK", TRUE, FALSE, 9.581, 9.581);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "IKE", TRUE, FALSE, 11.495, 11.495);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "MAE", TRUE, FALSE, 12.049, 12.049);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "MRI", TRUE, FALSE, 21.676, 21.676);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "SMZ", TRUE, FALSE, 14.536, 14.536);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "TKG", TRUE, FALSE, 127.75, 127.75);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "UES", TRUE, FALSE, 13.959, 13.959);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "RFR", TRUE, FALSE, 9.273, 9.273);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "ANU", TRUE, FALSE, 12.021, 12.021);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "ITO", TRUE, FALSE, 11.241, 11.241);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "SHN", TRUE, FALSE, 12.073, 12.073);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "STK", TRUE, FALSE, 8.696, 8.696);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "KOR", TRUE, FALSE, 97.441, 97.441);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "MNG", FALSE, FALSE, 32.552, 32.552);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("nippon", "RYU", TRUE, FALSE, 16.061, 16.061);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "POR", FALSE, FALSE, 8.296, 8.296);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "ATJ", FALSE, FALSE, 34.919, 34.919);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "BEI", FALSE, FALSE, 29.525, 29.525);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "MNG", FALSE, FALSE, 281.003, 281.003);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "PAT", FALSE, FALSE, 4.713, 4.713);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "JOH", FALSE, FALSE, 19.206, 19.206);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "PLB", FALSE, FALSE, 6.036, 6.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "SAK", FALSE, FALSE, 5.0, 5.0);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "KUT", FALSE, FALSE, 2.323, 2.323);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "BNJ", FALSE, FALSE, 2.233, 2.233);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "BEU", FALSE, FALSE, 2.131, 2.131);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hangzhou", "SMB", FALSE, FALSE, 2.857, 2.857);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("xian", "KHA", FALSE, FALSE, 109.542, 109.542);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("xian", "MNG", FALSE, FALSE, 210.834, 210.834);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("xian", "KAS", FALSE, FALSE, 7.562, 7.562);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("beijing", "KHA", FALSE, FALSE, 113.843, 113.843);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("beijing", "MNG", TRUE, FALSE, 122.087, 122.087);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("beijing", "KAS", FALSE, FALSE, 7.562, 7.562);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "POR", FALSE, FALSE, 11.597, 11.597);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "NED", FALSE, FALSE, 7.837, 7.837);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "ATJ", FALSE, FALSE, 34.919, 34.919);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BLI", TRUE, FALSE, 19.845, 19.845);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BAN", TRUE, FALSE, 76.606, 76.606);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BEI", FALSE, FALSE, 29.525, 29.525);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "MKS", TRUE, FALSE, 53.194, 53.194);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "MTR", TRUE, FALSE, 75.895, 75.895);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "PAT", FALSE, FALSE, 4.713, 4.713);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "JOH", FALSE, FALSE, 19.206, 19.206);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BLM", TRUE, FALSE, 31.79, 31.79);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BTN", TRUE, FALSE, 9.403, 9.403);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "CRB", TRUE, FALSE, 25.503, 25.503);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "PLB", FALSE, FALSE, 3.714, 3.714);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "SAK", FALSE, FALSE, 5.0, 5.0);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "KUT", FALSE, FALSE, 2.323, 2.323);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BNJ", FALSE, FALSE, 2.233, 2.233);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "LUW", TRUE, FALSE, 12.378, 12.378);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "TER", TRUE, FALSE, 20.228, 20.228);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "TID", TRUE, FALSE, 31.571, 31.571);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BNE", TRUE, FALSE, 40.899, 40.899);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "BEU", FALSE, FALSE, 2.131, 2.131);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("the_moluccas", "SMB", FALSE, FALSE, 2.857, 2.857);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "RUS", FALSE, FALSE, 54.733, 54.733);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "KZH", FALSE, FALSE, 110.885, 110.885);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "KHI", FALSE, FALSE, 4.068, 4.068);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "BUK", FALSE, FALSE, 8.862, 8.862);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "KHA", FALSE, FALSE, 2.972, 2.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("siberia", "KAS", FALSE, FALSE, 90.752, 90.752);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "KZH", FALSE, FALSE, 103.341, 103.341);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "KHI", FALSE, FALSE, 4.068, 4.068);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "BUK", FALSE, FALSE, 8.862, 8.862);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "KHA", TRUE, FALSE, 122.172, 122.172);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "MNG", FALSE, FALSE, 7.337, 7.337);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "OIR", TRUE, FALSE, 47.142, 47.142);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("yumen", "KAS", FALSE, FALSE, 128.566, 128.566);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "POR", FALSE, FALSE, 41.482, 41.482);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "ARK", FALSE, FALSE, 12.242, 12.242);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "ATJ", TRUE, FALSE, 107.036, 107.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "AYU", FALSE, FALSE, 5.791, 5.791);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BAN", FALSE, FALSE, 13.182, 13.182);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BEI", TRUE, FALSE, 82.612, 82.612);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PAT", TRUE, FALSE, 39.41, 39.41);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "MUG", FALSE, FALSE, 38.334, 38.334);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "JOH", TRUE, FALSE, 59.155, 59.155);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "KED", TRUE, FALSE, 10.86, 10.86);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PRK", TRUE, FALSE, 23.037, 23.037);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PGR", TRUE, FALSE, 22.18, 22.18);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "PLB", TRUE, FALSE, 37.827, 37.827);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "SAK", TRUE, FALSE, 25.866, 25.866);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "KUT", TRUE, FALSE, 26.275, 26.275);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BNJ", TRUE, FALSE, 25.83, 25.83);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BEU", TRUE, FALSE, 32.897, 32.897);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "SMB", TRUE, FALSE, 36.485, 36.485);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "BRS", TRUE, FALSE, 24.344, 24.344);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "JMB", TRUE, FALSE, 10.595, 10.595);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("malacca", "IND", TRUE, FALSE, 18.177, 18.177);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "POR", FALSE, FALSE, 10.879, 10.879);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "NED", FALSE, FALSE, 5.32, 5.32);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "ARK", TRUE, FALSE, 69.75, 69.75);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "GOC", FALSE, FALSE, 18.121, 18.121);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MUG", FALSE, FALSE, 303.58, 303.58);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "VIJ", FALSE, FALSE, 7.519, 7.519);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MAD", FALSE, FALSE, 5.86, 5.86);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "GRJ", TRUE, FALSE, 8.962, 8.962);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MAB", FALSE, FALSE, 5.608, 5.608);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "BST", TRUE, FALSE, 8.758, 8.758);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "CEY", FALSE, FALSE, 4.248, 4.248);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KMT", TRUE, FALSE, 10.039, 10.039);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "BGL", FALSE, FALSE, 5.295, 5.295);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "NGP", TRUE, FALSE, 10.593, 10.593);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "SBP", TRUE, FALSE, 16.044, 16.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "PTT", TRUE, FALSE, 7.816, 7.816);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "RTT", TRUE, FALSE, 9.682, 9.682);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KLH", TRUE, FALSE, 7.905, 7.905);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KJH", TRUE, FALSE, 8.827, 8.827);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "PRD", TRUE, FALSE, 7.789, 7.789);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "JPR", TRUE, FALSE, 7.789, 7.789);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "SRG", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "KND", FALSE, FALSE, 4.447, 4.447);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "DNG", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "JML", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "MKP", TRUE, FALSE, 9.359, 9.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "GNG", FALSE, FALSE, 5.413, 5.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ganges_delta", "TNJ", FALSE, FALSE, 6.351, 6.351);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "BIJ", FALSE, FALSE, 35.742, 35.742);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "GOC", FALSE, FALSE, 66.007, 66.007);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "MUG", TRUE, FALSE, 241.742, 241.742);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "AHM", FALSE, FALSE, 15.962, 15.962);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "GDW", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "DHU", TRUE, FALSE, 21.169, 21.169);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "BND", TRUE, FALSE, 17.163, 17.163);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "KGR", TRUE, FALSE, 10.594, 10.594);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "HAD", TRUE, FALSE, 12.643, 12.643);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "LDK", FALSE, FALSE, 4.99, 4.99);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "BGL", TRUE, FALSE, 13.917, 13.917);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "GHR", TRUE, FALSE, 9.807, 9.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "CHD", FALSE, FALSE, 6.725, 6.725);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "SRG", TRUE, FALSE, 10.779, 10.779);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "DNG", TRUE, FALSE, 11.311, 11.311);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "DTI", TRUE, FALSE, 9.807, 9.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "JML", TRUE, FALSE, 11.311, 11.311);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "SRM", TRUE, FALSE, 9.807, 9.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("doab", "KMN", TRUE, FALSE, 9.807, 9.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "POR", FALSE, FALSE, 3.884, 3.884);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "KZH", FALSE, FALSE, 103.341, 103.341);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "KHI", FALSE, FALSE, 51.424, 51.424);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "BUK", FALSE, FALSE, 95.566, 95.566);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "PER", FALSE, FALSE, 36.048, 36.048);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "KAS", FALSE, FALSE, 90.752, 90.752);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "MUG", FALSE, FALSE, 87.975, 87.975);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "MAW", FALSE, FALSE, 8.274, 8.274);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "MER", FALSE, FALSE, 12.265, 12.265);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "LDK", TRUE, FALSE, 13.312, 13.312);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lahore", "JAJ", FALSE, FALSE, 3.455, 3.455);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "POR", FALSE, FALSE, 14.764, 14.764);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "NED", FALSE, FALSE, 5.32, 5.32);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "BIJ", TRUE, FALSE, 82.269, 82.269);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "GOC", TRUE, FALSE, 77.544, 77.544);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MUG", FALSE, FALSE, 73.221, 73.221);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "VIJ", FALSE, FALSE, 37.724, 37.724);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "AHM", TRUE, FALSE, 45.225, 45.225);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MAD", FALSE, FALSE, 17.655, 17.655);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MER", FALSE, FALSE, 12.265, 12.265);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "GDW", TRUE, FALSE, 11.469, 11.469);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "MAB", FALSE, FALSE, 7.925, 7.925);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "CEY", FALSE, FALSE, 8.628, 8.628);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "CHD", TRUE, FALSE, 16.639, 16.639);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "JAJ", FALSE, FALSE, 3.455, 3.455);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "KND", FALSE, FALSE, 10.513, 10.513);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "GNG", FALSE, FALSE, 11.816, 11.816);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("deccan", "TNJ", FALSE, FALSE, 15.112, 15.112);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "TUR", FALSE, FALSE, 9.33, 9.33);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "POR", FALSE, FALSE, 58.284, 58.284);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "NED", FALSE, FALSE, 14.131, 14.131);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "HDR", FALSE, FALSE, 4.923, 4.923);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "AJU", FALSE, FALSE, 6.928, 6.928);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "AFA", FALSE, FALSE, 2.112, 2.112);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "WAR", FALSE, FALSE, 4.09, 4.09);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "BIJ", FALSE, FALSE, 6.787, 6.787);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "GOC", FALSE, FALSE, 42.916, 42.916);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MUG", FALSE, FALSE, 30.57, 30.57);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MYS", TRUE, FALSE, 14.351, 14.351);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "VIJ", TRUE, FALSE, 48.306, 48.306);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MAD", TRUE, FALSE, 29.048, 29.048);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MER", FALSE, FALSE, 2.335, 2.335);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "KLN", TRUE, FALSE, 11.893, 11.893);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "VND", TRUE, FALSE, 10.959, 10.959);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "MAB", TRUE, FALSE, 30.111, 30.111);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "CEY", TRUE, FALSE, 21.81, 21.81);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "KOC", TRUE, FALSE, 9.665, 9.665);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "JFN", TRUE, FALSE, 8.836, 8.836);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "JAJ", FALSE, FALSE, 5.441, 5.441);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "DGL", TRUE, FALSE, 8.509, 8.509);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "KND", TRUE, FALSE, 23.002, 23.002);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "GNG", TRUE, FALSE, 26.374, 26.374);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("comorin_cape", "TNJ", TRUE, FALSE, 31.984, 31.984);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "TUR", FALSE, FALSE, 9.33, 9.33);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "POR", FALSE, FALSE, 43.82, 43.82);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "HDR", FALSE, FALSE, 4.923, 4.923);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "PER", FALSE, FALSE, 9.125, 9.125);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "ORM", FALSE, FALSE, 6.775, 6.775);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "ZAN", FALSE, FALSE, 27.522, 27.522);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MLI", FALSE, FALSE, 4.333, 4.333);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "AJU", FALSE, FALSE, 6.928, 6.928);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "AFA", FALSE, FALSE, 3.826, 3.826);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "WAR", FALSE, FALSE, 4.09, 4.09);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "SKA", FALSE, FALSE, 2.926, 2.926);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "BTS", FALSE, FALSE, 2.409, 2.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "BIJ", FALSE, FALSE, 1.787, 1.787);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MUG", FALSE, FALSE, 87.084, 87.084);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "GUJ", TRUE, FALSE, 9.304, 9.304);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MAW", TRUE, FALSE, 12.608, 12.608);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "MER", TRUE, FALSE, 19.994, 19.994);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "JAN", TRUE, FALSE, 8.38, 8.38);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "JSL", TRUE, FALSE, 7.665, 7.665);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "JAJ", TRUE, FALSE, 24.483, 24.483);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gujarat", "IDR", TRUE, FALSE, 8.437, 8.437);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "TRP", FALSE, FALSE, 4.026, 4.026);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "TUN", FALSE, FALSE, 9.017, 9.017);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "ETH", FALSE, FALSE, 5.991, 5.991);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "NUB", FALSE, FALSE, 5.509, 5.509);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "SON", FALSE, FALSE, 10.67, 10.67);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "HAU", TRUE, FALSE, 22.605, 22.605);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "KBO", TRUE, FALSE, 96.526, 96.526);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "OYO", TRUE, FALSE, 18.466, 18.466);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "WGD", FALSE, FALSE, 8.424, 8.424);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "GUR", FALSE, FALSE, 4.044, 4.044);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "AIR", TRUE, FALSE, 48.53, 48.53);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "BON", FALSE, FALSE, 6.654, 6.654);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "DAH", FALSE, FALSE, 4.036, 4.036);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "DGB", FALSE, FALSE, 4.444, 4.444);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "JNN", FALSE, FALSE, 22.654, 22.654);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "KAN", TRUE, FALSE, 25.987, 25.987);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "KTS", TRUE, FALSE, 36.374, 36.374);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "NUP", TRUE, FALSE, 15.348, 15.348);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "TMB", FALSE, FALSE, 25.836, 25.836);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "YAO", TRUE, FALSE, 11.728, 11.728);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "YAT", FALSE, FALSE, 7.75, 7.75);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "ZAF", FALSE, FALSE, 14.97, 14.97);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("katsina", "ZZZ", TRUE, FALSE, 15.883, 15.883);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "TUR", FALSE, FALSE, 34.128, 34.128);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "HDR", FALSE, FALSE, 2.705, 2.705);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "HED", FALSE, FALSE, 6.906, 6.906);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MFL", FALSE, FALSE, 4.38, 4.38);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "RAS", FALSE, FALSE, 6.48, 6.48);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ETH", TRUE, FALSE, 63.929, 63.929);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "NUB", TRUE, FALSE, 80.26, 80.26);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "AJU", FALSE, FALSE, 8.35, 8.35);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ENA", TRUE, FALSE, 11.112, 11.112);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "OGD", TRUE, FALSE, 10.231, 10.231);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "WAD", TRUE, FALSE, 10.779, 10.779);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "AFA", FALSE, FALSE, 2.112, 2.112);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "DAR", TRUE, FALSE, 20.991, 20.991);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "KAF", TRUE, FALSE, 15.052, 15.052);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MED", TRUE, FALSE, 14.03, 14.03);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "MRE", FALSE, FALSE, 8.457, 8.457);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "WAR", FALSE, FALSE, 2.783, 2.783);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "BTI", TRUE, FALSE, 9.89, 9.89);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "WLY", TRUE, FALSE, 15.112, 15.112);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "DAM", TRUE, FALSE, 14.351, 14.351);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "JJI", TRUE, FALSE, 10.049, 10.049);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ethiopia", "ABB", TRUE, FALSE, 11.875, 11.875);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "TUR", FALSE, FALSE, 46.42, 46.42);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "POR", FALSE, FALSE, 27.192, 27.192);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "HDR", TRUE, FALSE, 20.794, 20.794);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "HED", FALSE, FALSE, 27.187, 27.187);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MFL", TRUE, FALSE, 10.229, 10.229);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MHR", TRUE, FALSE, 10.153, 10.153);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "OMA", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "RAS", TRUE, FALSE, 11.922, 11.922);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "YAS", FALSE, FALSE, 2.16, 2.16);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "PER", FALSE, FALSE, 9.125, 9.125);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "ORM", FALSE, FALSE, 6.775, 6.775);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "ETH", FALSE, FALSE, 1.357, 1.357);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "ZAN", FALSE, FALSE, 27.522, 27.522);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MLI", FALSE, FALSE, 4.333, 4.333);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "AJU", TRUE, FALSE, 51.318, 51.318);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "AFA", TRUE, FALSE, 19.215, 19.215);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MED", FALSE, FALSE, 3.731, 3.731);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "MRE", TRUE, FALSE, 12.08, 12.08);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "WAR", TRUE, FALSE, 18.463, 18.463);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "SKA", FALSE, FALSE, 2.926, 2.926);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("gulf_of_aden", "BTS", FALSE, FALSE, 2.409, 2.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "TUR", FALSE, FALSE, 5.949, 5.949);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "POR", FALSE, FALSE, 43.247, 43.247);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "NAJ", FALSE, FALSE, 2.218, 2.218);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "OMA", TRUE, FALSE, 11.785, 11.785);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "YAS", TRUE, FALSE, 9.18, 9.18);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "MSY", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "PER", FALSE, FALSE, 27.905, 27.905);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "ORM", TRUE, FALSE, 33.085, 33.085);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "BSR", FALSE, FALSE, 7.025, 7.025);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "BAL", TRUE, FALSE, 11.465, 11.465);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("hormuz", "MUG", FALSE, FALSE, 0.934, 0.934);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "POR", FALSE, FALSE, 40.58, 40.58);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "ZAN", TRUE, FALSE, 78.158, 78.158);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "MLI", TRUE, FALSE, 20.071, 20.071);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "AJU", FALSE, FALSE, 1.134, 1.134);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "PTE", TRUE, FALSE, 16.719, 16.719);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "MIR", TRUE, FALSE, 10.55, 10.55);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "SKA", TRUE, FALSE, 23.843, 23.843);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "BTS", TRUE, FALSE, 21.008, 21.008);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "MFY", TRUE, FALSE, 8.818, 8.818);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("zanzibar", "ANT", TRUE, FALSE, 11.3, 11.3);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "TUR", FALSE, FALSE, 59.536, 59.536);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "POR", FALSE, FALSE, 1.768, 1.768);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "ANZ", TRUE, FALSE, 9.72, 9.72);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "ARD", TRUE, FALSE, 8.619, 8.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "DAW", TRUE, FALSE, 11.799, 11.799);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "NAJ", TRUE, FALSE, 11.496, 11.496);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "SHM", TRUE, FALSE, 17.408, 17.408);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "MSY", TRUE, FALSE, 18.009, 18.009);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "PER", FALSE, FALSE, 90.264, 90.264);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "BSR", TRUE, FALSE, 42.782, 42.782);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("basra", "MGR", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "RUS", FALSE, FALSE, 12.625, 12.625);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "KZH", TRUE, FALSE, 146.956, 146.956);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "KHI", TRUE, FALSE, 84.486, 84.486);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "BUK", TRUE, FALSE, 141.261, 141.261);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "PER", FALSE, FALSE, 38.103, 38.103);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("samarkand", "KAS", TRUE, FALSE, 127.653, 127.653);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "TUR", FALSE, FALSE, 30.69, 30.69);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "RUS", FALSE, FALSE, 12.625, 12.625);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "AVR", FALSE, FALSE, 4.302, 4.302);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "PER", TRUE, FALSE, 251.169, 251.169);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("persia", "MGR", TRUE, FALSE, 11.41, 11.41);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "TUR", FALSE, FALSE, 239.11, 239.11);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "ANZ", FALSE, FALSE, 2.218, 2.218);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("aleppo", "HED", FALSE, FALSE, 27.187, 27.187);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "TUR", FALSE, FALSE, 189.349, 189.349);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "FRA", FALSE, FALSE, 6.728, 6.728);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "HAB", FALSE, FALSE, 2.132, 2.132);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "SPA", FALSE, FALSE, 19.497, 19.497);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "GEN", FALSE, FALSE, 43.622, 43.622);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "MAN", FALSE, FALSE, 2.322, 2.322);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "PAP", FALSE, FALSE, 13.318, 13.318);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "SAV", FALSE, FALSE, 5.003, 5.003);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "TUS", FALSE, FALSE, 12.503, 12.503);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "URB", FALSE, FALSE, 4.263, 4.263);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "VEN", FALSE, FALSE, 30.588, 30.588);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "LUC", FALSE, FALSE, 5.097, 5.097);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "ANZ", FALSE, FALSE, 0.865, 0.865);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "HED", TRUE, FALSE, 76.065, 76.065);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("alexandria", "ABB", FALSE, FALSE, 1.662, 1.662);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "TUR", FALSE, FALSE, 14.571, 14.571);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "CRI", FALSE, FALSE, 24.248, 24.248);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "GEO", FALSE, FALSE, 14.711, 14.711);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "RUS", FALSE, FALSE, 94.28, 94.28);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "NOG", TRUE, FALSE, 22.088, 22.088);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "AVR", TRUE, FALSE, 12.19, 12.19);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "KZH", FALSE, FALSE, 8.704, 8.704);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "PER", FALSE, FALSE, 2.429, 2.429);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "CIR", FALSE, FALSE, 6.301, 6.301);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("astrakhan", "GAZ", TRUE, FALSE, 28.564, 28.564);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "MOL", TRUE, FALSE, 14.18, 14.18);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "TRA", FALSE, FALSE, 23.678, 23.678);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "WAL", FALSE, FALSE, 21.441, 21.441);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "TUR", FALSE, FALSE, 144.761, 144.761);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "PLC", FALSE, FALSE, 19.045, 19.045);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "HAB", FALSE, FALSE, 4.97, 4.97);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "CRI", TRUE, FALSE, 29.955, 29.955);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "GEO", TRUE, FALSE, 28.358, 28.358);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "RUS", FALSE, FALSE, 5.892, 5.892);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "ZAZ", TRUE, FALSE, 11.192, 11.192);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "PER", FALSE, FALSE, 12.146, 12.146);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "CIR", TRUE, FALSE, 18.691, 18.691);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "GAZ", FALSE, FALSE, 1.602, 1.602);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("crimea", "IME", TRUE, FALSE, 11.59, 11.59);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "MON", FALSE, FALSE, 1.986, 1.986);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "RAG", FALSE, FALSE, 28.169, 28.169);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "TUR", TRUE, FALSE, 246.205, 246.205);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("constantinople", "HAB", FALSE, FALSE, 2.952, 2.952);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "SWE", FALSE, FALSE, 6.634, 6.634);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "PLC", FALSE, FALSE, 220.033, 220.033);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "BOH", FALSE, FALSE, 2.557, 2.557);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "RUS", FALSE, FALSE, 117.767, 117.767);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kiev", "ZAZ", FALSE, FALSE, 1.271, 1.271);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kazan", "SWE", FALSE, FALSE, 6.634, 6.634);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("kazan", "RUS", FALSE, FALSE, 223.6, 223.6);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "SWE", FALSE, FALSE, 47.623, 47.623);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "DAN", FALSE, FALSE, 4.29, 4.29);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "PRU", FALSE, FALSE, 8.409, 8.409);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "KUR", FALSE, FALSE, 2.186, 2.186);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "PLC", FALSE, FALSE, 22.357, 22.357);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "QAS", TRUE, FALSE, 10.565, 10.565);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "RUS", TRUE, FALSE, 222.886, 222.886);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "WOL", FALSE, FALSE, 2.218, 2.218);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("novgorod", "STE", FALSE, FALSE, 2.186, 2.186);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("laplata", "GUA", TRUE, FALSE, 8.499, 8.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("laplata", "C00", FALSE, FALSE, 47.77, 47.77);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("laplata", "C06", FALSE, FALSE, 50.191, 50.191);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "POR", FALSE, FALSE, 8.416, 8.416);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "SPA", FALSE, FALSE, 0.572, 0.572);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "BEN", FALSE, FALSE, 5.743, 5.743);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "JOL", FALSE, FALSE, 3.678, 3.678);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "SYO", FALSE, FALSE, 3.306, 3.306);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "KBU", FALSE, FALSE, 2.209, 2.209);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("brazil", "C00", TRUE, FALSE, 183.638, 183.638);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "POR", FALSE, FALSE, 8.416, 8.416);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "ALG", FALSE, FALSE, 67.83, 67.83);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "MOR", FALSE, FALSE, 70.553, 70.553);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "SOS", FALSE, FALSE, 15.317, 15.317);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "ASH", TRUE, FALSE, 13.95, 13.95);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "BEN", FALSE, FALSE, 15.833, 15.833);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "MAL", TRUE, FALSE, 19.373, 19.373);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "SON", TRUE, FALSE, 24.191, 24.191);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "OYO", FALSE, FALSE, 1.983, 1.983);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "JOL", FALSE, FALSE, 10.068, 10.068);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "WGD", TRUE, FALSE, 19.869, 19.869);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "GUR", TRUE, FALSE, 11.578, 11.578);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "SYO", FALSE, FALSE, 3.306, 3.306);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "AIR", FALSE, FALSE, 11.169, 11.169);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "BON", TRUE, FALSE, 16.78, 16.78);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "DAH", TRUE, FALSE, 10.779, 10.779);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "DGB", TRUE, FALSE, 12.715, 12.715);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "FUL", TRUE, FALSE, 75.665, 75.665);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "JNN", TRUE, FALSE, 51.522, 51.522);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "KBU", FALSE, FALSE, 8.413, 8.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "KNG", TRUE, FALSE, 16.597, 16.597);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "TMB", TRUE, FALSE, 61.549, 61.549);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "YAT", TRUE, FALSE, 18.551, 18.551);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("timbuktu", "ZAF", TRUE, FALSE, 38.624, 38.624);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "ENG", FALSE, FALSE, 23.983, 23.983);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "FRA", FALSE, FALSE, 35.16, 35.16);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "EFR", FALSE, FALSE, 3.572, 3.572);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "POR", FALSE, FALSE, 71.593, 71.593);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "SPA", FALSE, FALSE, 46.548, 46.548);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "NED", FALSE, FALSE, 11.844, 11.844);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "ASH", FALSE, FALSE, 1.21, 1.21);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "BEN", TRUE, FALSE, 39.325, 39.325);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "KON", FALSE, FALSE, 11.027, 11.027);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "LOA", TRUE, FALSE, 12.553, 12.553);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "JOL", TRUE, FALSE, 27.983, 27.983);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "SYO", TRUE, FALSE, 25.852, 25.852);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "FUL", FALSE, FALSE, 6.321, 6.321);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "KBU", TRUE, FALSE, 18.034, 18.034);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "NDO", TRUE, FALSE, 16.989, 16.989);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "C03", FALSE, FALSE, 59.316, 59.316);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ivory_coast", "C04", FALSE, FALSE, 15.847, 15.847);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "FRA", FALSE, FALSE, 10.057, 10.057);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "POR", FALSE, FALSE, 27.077, 27.077);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "SPA", FALSE, FALSE, 69.678, 69.678);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "GEN", FALSE, FALSE, 43.622, 43.622);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "PAP", FALSE, FALSE, 5.006, 5.006);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "SAV", FALSE, FALSE, 2.965, 2.965);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "TUS", FALSE, FALSE, 10.467, 10.467);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "LUC", FALSE, FALSE, 3.075, 3.075);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "ALG", FALSE, FALSE, 13.504, 13.504);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "TRP", TRUE, FALSE, 27.788, 27.788);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "TUN", TRUE, FALSE, 82.153, 82.153);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "KBA", TRUE, FALSE, 18.619, 18.619);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "TGT", TRUE, FALSE, 9.834, 9.834);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "GHD", TRUE, FALSE, 8.453, 8.453);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "FZA", TRUE, FALSE, 9.833, 9.833);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("tunis", "MZB", TRUE, FALSE, 9.303, 9.303);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "MON", TRUE, FALSE, 9.071, 9.071);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "RAG", TRUE, FALSE, 77.313, 77.313);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "TRA", FALSE, FALSE, 23.678, 23.678);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "WAL", FALSE, FALSE, 21.441, 21.441);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "TUR", FALSE, FALSE, 106.807, 106.807);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "FRA", FALSE, FALSE, 6.728, 6.728);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "HAB", FALSE, FALSE, 21.869, 21.869);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "SLZ", FALSE, FALSE, 6.825, 6.825);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "SPA", FALSE, FALSE, 19.497, 19.497);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "GEN", FALSE, FALSE, 43.622, 43.622);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "MAN", FALSE, FALSE, 4.352, 4.352);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "MOD", FALSE, FALSE, 2.028, 2.028);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "PAP", FALSE, FALSE, 11.236, 11.236);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "PAR", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "SAV", FALSE, FALSE, 2.965, 2.965);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "TUS", FALSE, FALSE, 10.467, 10.467);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "URB", FALSE, FALSE, 4.263, 4.263);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "VEN", FALSE, FALSE, 39.551, 39.551);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("ragusa", "LUC", FALSE, FALSE, 3.075, 3.075);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "POR", FALSE, FALSE, 27.077, 27.077);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "SPA", FALSE, FALSE, 37.152, 37.152);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "ALG", TRUE, FALSE, 67.098, 67.098);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "MOR", TRUE, FALSE, 58.732, 58.732);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("safi", "SOS", TRUE, FALSE, 24.371, 24.371);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "TRA", TRUE, FALSE, 53.967, 53.967);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "WAL", TRUE, FALSE, 49.668, 49.668);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "TUR", FALSE, FALSE, 32.708, 32.708);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "PLC", FALSE, FALSE, 25.588, 25.588);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "BAV", FALSE, FALSE, 36.302, 36.302);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "BOH", FALSE, FALSE, 2.557, 2.557);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "HAB", FALSE, FALSE, 92.141, 92.141);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "SLZ", FALSE, FALSE, 8.902, 8.902);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "ULM", FALSE, FALSE, 8.24, 8.24);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "VEN", FALSE, FALSE, 73.424, 73.424);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "REG", FALSE, FALSE, 10.479, 10.479);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("pest", "OPL", FALSE, FALSE, 7.281, 7.281);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "SWE", FALSE, FALSE, 65.297, 65.297);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "DAN", FALSE, FALSE, 2.282, 2.282);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "PRU", FALSE, FALSE, 19.42, 19.42);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "PLC", TRUE, FALSE, 253.808, 253.808);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "ANH", FALSE, FALSE, 4.436, 4.436);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "AUG", FALSE, FALSE, 7.75, 7.75);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "BAV", FALSE, FALSE, 36.302, 36.302);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "BOH", FALSE, FALSE, 73.225, 73.225);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "BRA", FALSE, FALSE, 34.381, 34.381);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "BRU", FALSE, FALSE, 12.144, 12.144);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "HAB", FALSE, FALSE, 67.286, 67.286);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "MAG", FALSE, FALSE, 11.439, 11.439);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "PAL", FALSE, FALSE, 51.741, 51.741);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "SAX", FALSE, FALSE, 20.849, 20.849);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "SLZ", FALSE, FALSE, 8.902, 8.902);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "THU", FALSE, FALSE, 11.338, 11.338);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "ULM", FALSE, FALSE, 8.24, 8.24);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "WUR", FALSE, FALSE, 8.499, 8.499);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "MEM", FALSE, FALSE, 5.746, 5.746);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "PSS", FALSE, FALSE, 5.626, 5.626);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "REG", FALSE, FALSE, 10.479, 10.479);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "OPL", TRUE, FALSE, 18.376, 18.376);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "GOS", FALSE, FALSE, 4.884, 4.884);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("krakow", "TNT", FALSE, FALSE, 4.721, 4.721);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ALS", FALSE, FALSE, 9.569, 9.569);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "AAC", FALSE, FALSE, 6.739, 6.739);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ANS", FALSE, FALSE, 4.717, 4.717);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "AUG", TRUE, FALSE, 18.982, 18.982);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BAD", FALSE, FALSE, 8.415, 8.415);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BAV", TRUE, FALSE, 69.121, 69.121);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BOH", FALSE, FALSE, 65.91, 65.91);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BRA", FALSE, FALSE, 34.381, 34.381);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "FRN", FALSE, FALSE, 12.5, 12.5);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "HAB", TRUE, FALSE, 122.703, 122.703);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "HES", FALSE, FALSE, 19.162, 19.162);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "KOL", FALSE, FALSE, 54.869, 54.869);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MAG", FALSE, FALSE, 11.439, 11.439);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MAI", FALSE, FALSE, 9.82, 9.82);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "PAL", FALSE, FALSE, 55.624, 55.624);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "SAX", FALSE, FALSE, 20.849, 20.849);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "SLZ", TRUE, FALSE, 26.223, 26.223);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "SWI", FALSE, FALSE, 55.664, 55.664);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "THU", FALSE, FALSE, 11.338, 11.338);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "TRI", FALSE, FALSE, 9.435, 9.435);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ULM", TRUE, FALSE, 24.213, 24.213);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "WBG", FALSE, FALSE, 7.413, 7.413);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "WUR", TRUE, FALSE, 20.442, 20.442);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "NUM", FALSE, FALSE, 12.901, 12.901);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MEM", TRUE, FALSE, 15.532, 15.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "NSA", FALSE, FALSE, 4.31, 4.31);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "RVA", FALSE, FALSE, 8.609, 8.609);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MAN", FALSE, FALSE, 14.84, 14.84);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "PAP", FALSE, FALSE, 6.23, 6.23);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "URB", FALSE, FALSE, 8.235, 8.235);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "VEN", FALSE, FALSE, 101.578, 101.578);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "PSS", TRUE, FALSE, 14.783, 14.783);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "ROT", FALSE, FALSE, 5.391, 5.391);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BYT", FALSE, FALSE, 3.972, 3.972);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "REG", TRUE, FALSE, 38.138, 38.138);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "TTL", FALSE, FALSE, 7.497, 7.497);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "TNT", TRUE, FALSE, 13.239, 13.239);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "MLH", FALSE, FALSE, 4.707, 4.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("wien", "BAM", FALSE, FALSE, 3.944, 3.944);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "SWE", FALSE, FALSE, 2.304, 2.304);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "DAN", FALSE, FALSE, 22.733, 22.733);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "SHL", FALSE, FALSE, 2.188, 2.188);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "ALS", FALSE, FALSE, 9.569, 9.569);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "ANH", TRUE, FALSE, 12.767, 12.767);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BOH", TRUE, FALSE, 115.603, 115.603);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BRA", TRUE, FALSE, 68.485, 68.485);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BRE", FALSE, FALSE, 7.176, 7.176);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "BRU", TRUE, FALSE, 27.901, 27.901);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "FRN", FALSE, FALSE, 12.5, 12.5);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "HAM", FALSE, FALSE, 10.355, 10.355);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "HES", FALSE, FALSE, 19.162, 19.162);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "KOL", FALSE, FALSE, 54.869, 54.869);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "MAG", TRUE, FALSE, 36.885, 36.885);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "MAI", FALSE, FALSE, 9.82, 9.82);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "PAL", FALSE, FALSE, 55.624, 55.624);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "SAX", TRUE, FALSE, 48.244, 48.244);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "THU", TRUE, FALSE, 29.848, 29.848);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "NUM", FALSE, FALSE, 12.901, 12.901);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "RVA", FALSE, FALSE, 8.609, 8.609);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "HSA", FALSE, FALSE, 48.551, 48.551);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "WOL", FALSE, FALSE, 17.929, 17.929);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "STE", FALSE, FALSE, 14.648, 14.648);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("saxony", "GOS", TRUE, FALSE, 13.858, 13.858);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "SWE", TRUE, FALSE, 94.071, 94.071);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "DAN", FALSE, FALSE, 34.145, 34.145);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "SHL", FALSE, FALSE, 4.096, 4.096);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "PRU", TRUE, FALSE, 48.05, 48.05);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "KUR", TRUE, FALSE, 11.935, 11.935);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "PLC", FALSE, FALSE, 61.616, 61.616);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "BRE", FALSE, FALSE, 9.198, 9.198);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "HAM", FALSE, FALSE, 12.707, 12.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "HSA", FALSE, FALSE, 35.005, 35.005);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "WOL", FALSE, FALSE, 3.882, 3.882);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("baltic_sea", "STE", FALSE, FALSE, 4.811, 4.811);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "SWE", FALSE, FALSE, 2.304, 2.304);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "DAN", FALSE, FALSE, 22.733, 22.733);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "SHL", FALSE, FALSE, 7.912, 7.912);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "ALS", TRUE, FALSE, 27.844, 27.844);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "FRA", FALSE, FALSE, 24.111, 24.111);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "AAC", TRUE, FALSE, 17.938, 17.938);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "ANS", TRUE, FALSE, 13.049, 13.049);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BAD", TRUE, FALSE, 19.35, 19.35);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BRA", FALSE, FALSE, 1.213, 1.213);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BRE", FALSE, FALSE, 7.176, 7.176);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "FRN", TRUE, FALSE, 42.129, 42.129);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HAB", FALSE, FALSE, 5.377, 5.377);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HAM", FALSE, FALSE, 10.355, 10.355);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HES", TRUE, FALSE, 40.091, 40.091);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "KOL", TRUE, FALSE, 101.426, 101.426);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "MAI", TRUE, FALSE, 28.199, 28.199);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "PAL", TRUE, FALSE, 96.371, 96.371);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "SWI", FALSE, FALSE, 69.126, 69.126);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "THU", FALSE, FALSE, 1.263, 1.263);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "TRI", TRUE, FALSE, 22.278, 22.278);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "WBG", TRUE, FALSE, 18.601, 18.601);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "NUM", TRUE, FALSE, 42.92, 42.92);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "NSA", TRUE, FALSE, 13.105, 13.105);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "RVA", TRUE, FALSE, 28.555, 28.555);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "SPA", FALSE, FALSE, 3.755, 3.755);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "HSA", FALSE, FALSE, 48.551, 48.551);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "ROT", TRUE, FALSE, 15.257, 15.257);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BYT", TRUE, FALSE, 11.542, 11.542);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "TTL", TRUE, FALSE, 18.704, 18.704);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "WOL", FALSE, FALSE, 17.929, 17.929);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "STE", FALSE, FALSE, 14.648, 14.648);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "MLH", TRUE, FALSE, 13.026, 13.026);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("rheinland", "BAM", TRUE, FALSE, 11.468, 11.468);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "SPA", FALSE, FALSE, 1.24, 1.24);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "MIS", TRUE, FALSE, 6.787, 6.787);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "C02", FALSE, FALSE, 16.021, 16.021);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "C03", FALSE, FALSE, 59.316, 59.316);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("panama", "C04", FALSE, FALSE, 88.006, 88.006);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "ENG", FALSE, FALSE, 7.928, 7.928);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "FRA", FALSE, FALSE, 24.844, 24.844);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "POR", FALSE, FALSE, 29.505, 29.505);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "SPA", FALSE, FALSE, 36.676, 36.676);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "LEN", FALSE, FALSE, 3.049, 3.049);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "C01", FALSE, FALSE, 6.931, 6.931);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "C03", TRUE, FALSE, 283.783, 283.783);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("carribean_trade", "C04", FALSE, FALSE, 31.845, 31.845);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ENG", FALSE, FALSE, 45.057, 45.057);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "FRA", FALSE, FALSE, 12.486, 12.486);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "EFR", FALSE, FALSE, 3.572, 3.572);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "SPA", FALSE, FALSE, 9.871, 9.871);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "NED", FALSE, FALSE, 13.5, 13.5);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "CHE", TRUE, FALSE, 10.069, 10.069);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ABE", TRUE, FALSE, 8.654, 8.654);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "LEN", TRUE, FALSE, 24.53, 24.53);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "MAH", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "PEQ", TRUE, FALSE, 9.696, 9.696);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "POW", TRUE, FALSE, 10.532, 10.532);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ALT", TRUE, FALSE, 9.359, 9.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "ICH", TRUE, FALSE, 9.359, 9.359);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "COF", TRUE, FALSE, 10.069, 10.069);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "JOA", TRUE, FALSE, 10.087, 10.087);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "SAT", TRUE, FALSE, 9.493, 9.493);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "KSI", TRUE, FALSE, 9.892, 9.892);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "OSH", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "TSC", TRUE, FALSE, 10.069, 10.069);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "PEN", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "WAM", TRUE, FALSE, 9.018, 9.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "AGQ", FALSE, FALSE, 2.018, 2.018);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("chesapeake_bay", "C01", TRUE, FALSE, 13.822, 13.822);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "DAN", FALSE, FALSE, 7.204, 7.204);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "ENG", FALSE, FALSE, 8.848, 8.848);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "SCO", FALSE, FALSE, 12.104, 12.104);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "FRA", FALSE, FALSE, 18.829, 18.829);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "MAH", TRUE, FALSE, 10.443, 10.443);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "MIK", TRUE, FALSE, 9.403, 9.403);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "OSH", TRUE, FALSE, 17.309, 17.309);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "PEN", TRUE, FALSE, 10.335, 10.335);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "MLS", TRUE, FALSE, 8.515, 8.515);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "INN", TRUE, FALSE, 8.637, 8.637);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("st_lawrence", "AGQ", TRUE, FALSE, 10.358, 10.358);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "SWE", FALSE, FALSE, 0.443, 0.443);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "DAN", FALSE, FALSE, 7.79, 7.79);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "ENG", FALSE, FALSE, 7.263, 7.263);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "SCO", FALSE, FALSE, 10.112, 10.112);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("white_sea", "RUS", FALSE, FALSE, 32.825, 32.825);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SWE", FALSE, FALSE, 2.304, 2.304);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "DAN", FALSE, FALSE, 60.768, 60.768);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SHL", FALSE, FALSE, 4.096, 4.096);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "CNN", TRUE, FALSE, 8.939, 8.939);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "ENG", FALSE, FALSE, 62.348, 62.348);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "LEI", TRUE, FALSE, 10.268, 10.268);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SCO", TRUE, FALSE, 73.174, 73.174);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SLN", TRUE, FALSE, 9.565, 9.565);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "ORD", TRUE, FALSE, 8.899, 8.899);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "FRA", FALSE, FALSE, 10.315, 10.315);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "BRE", FALSE, FALSE, 9.198, 9.198);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "EFR", FALSE, FALSE, 5.594, 5.594);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "HAM", FALSE, FALSE, 12.707, 12.707);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "LAU", FALSE, FALSE, 1.724, 1.724);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "LUN", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "MKL", FALSE, FALSE, 2.222, 2.222);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "OLD", FALSE, FALSE, 2.022, 2.022);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "VER", FALSE, FALSE, 2.042, 2.042);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "SPA", FALSE, FALSE, 9.871, 9.871);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "LIE", FALSE, FALSE, 2.054, 2.054);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "NED", FALSE, FALSE, 13.5, 13.5);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "HSA", FALSE, FALSE, 35.005, 35.005);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "WOL", FALSE, FALSE, 6.1, 6.1);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("north_sea", "STE", FALSE, FALSE, 6.997, 6.997);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "SWE", FALSE, FALSE, 11.522, 11.522);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "DAN", TRUE, FALSE, 165.976, 165.976);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "SHL", TRUE, FALSE, 28.177, 28.177);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "ENG", FALSE, FALSE, 23.983, 23.983);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "FRA", FALSE, FALSE, 10.315, 10.315);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "BRA", FALSE, FALSE, 1.319, 1.319);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "BRE", TRUE, FALSE, 49.033, 49.033);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "EFR", FALSE, FALSE, 3.572, 3.572);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "HAM", TRUE, FALSE, 67.465, 67.465);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "LAU", TRUE, FALSE, 9.158, 9.158);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "LUN", TRUE, FALSE, 15.384, 15.384);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "MKL", TRUE, FALSE, 27.901, 27.901);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "OLD", TRUE, FALSE, 11.104, 11.104);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "VER", TRUE, FALSE, 12.263, 12.263);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "SPA", FALSE, FALSE, 9.871, 9.871);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "NED", FALSE, FALSE, 11.844, 11.844);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "HSA", TRUE, FALSE, 141.953, 141.953);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "WOL", TRUE, FALSE, 47.547, 47.547);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("lubeck", "STE", TRUE, FALSE, 53.762, 53.762);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "FRA", FALSE, FALSE, 196.846, 196.846);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "LOR", FALSE, FALSE, 12.19, 12.19);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "SWI", FALSE, FALSE, 59.882, 59.882);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("bordeaux", "SPA", FALSE, FALSE, 7.151, 7.151);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "FRA", FALSE, FALSE, 3.329, 3.329);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "POR", TRUE, FALSE, 141.457, 141.457);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "SPA", TRUE, FALSE, 205.634, 205.634);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("sevilla", "MOR", FALSE, FALSE, 3.493, 3.493);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "ENG", FALSE, FALSE, 23.983, 23.983);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "FRA", TRUE, FALSE, 189.053, 189.053);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "EFR", FALSE, FALSE, 7.953, 7.953);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "LOR", TRUE, FALSE, 26.224, 26.224);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "SWI", TRUE, FALSE, 124.155, 124.155);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "SPA", FALSE, FALSE, 25.317, 25.317);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "GEN", FALSE, FALSE, 73.912, 73.912);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "MAN", FALSE, FALSE, 12.517, 12.517);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "PAP", FALSE, FALSE, 5.006, 5.006);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "SAV", FALSE, FALSE, 35.918, 35.918);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "TUS", FALSE, FALSE, 38.631, 38.631);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "LUC", FALSE, FALSE, 9.814, 9.814);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "LIE", TRUE, FALSE, 20.437, 20.437);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("champagne", "NED", FALSE, FALSE, 54.9, 54.9);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "FRA", FALSE, FALSE, 23.374, 23.374);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "SPA", FALSE, FALSE, 105.833, 105.833);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "GEN", FALSE, FALSE, 43.622, 43.622);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "PAP", FALSE, FALSE, 5.006, 5.006);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "SAV", FALSE, FALSE, 2.965, 2.965);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "TUS", FALSE, FALSE, 10.467, 10.467);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("valencia", "LUC", FALSE, FALSE, 3.075, 3.075);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "KNI", TRUE, FALSE, 24.651, 24.651);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "FRA", FALSE, FALSE, 33.643, 33.643);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "SPA", FALSE, FALSE, 53.209, 53.209);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "GEN", TRUE, FALSE, 222.554, 222.554);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "MAN", FALSE, FALSE, 2.975, 2.975);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "PAP", TRUE, FALSE, 36.483, 36.483);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "SAV", TRUE, FALSE, 27.49, 27.49);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "TUS", TRUE, FALSE, 76.017, 76.017);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("genua", "LUC", TRUE, FALSE, 31.74, 31.74);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "HAB", FALSE, FALSE, 6.389, 6.389);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "SPA", FALSE, FALSE, 11.245, 11.245);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "MAN", TRUE, FALSE, 21.618, 21.618);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "MOD", TRUE, FALSE, 11.273, 11.273);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "PAP", FALSE, FALSE, 16.634, 16.634);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "PAR", TRUE, FALSE, 12.088, 12.088);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "URB", TRUE, FALSE, 25.174, 25.174);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("venice", "VEN", TRUE, FALSE, 198.36, 198.36);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "ENG", TRUE, FALSE, 149.475, 149.475);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "FRA", FALSE, FALSE, 37.059, 37.059);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "EFR", TRUE, FALSE, 27.453, 27.453);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "SPA", FALSE, FALSE, 25.921, 25.921);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "LIE", FALSE, FALSE, 2.289, 2.289);
INSERT INTO node_country(trade_node,country_name, is_home, has_merchant, base_trading_power, calculated_trading_power) VALUES ("english_channel", "NED", TRUE, FALSE, 72.547, 72.547);
