CREATE (african_great_lakes:Trade_node {name:"african_great_lakes", local_value:1.637, is_inland:true, total_power:347.522, outgoing:0.21111488193553213, ingoing:0});
CREATE (kongo:Trade_node {name:"kongo", local_value:2.875, is_inland:true, total_power:606.7359999999998, outgoing:0.35759986032744384, ingoing:0.11083531301615437});
CREATE (zambezi:Trade_node {name:"zambezi", local_value:1.974, is_inland:true, total_power:416.1120000000001, outgoing:0.307502662743773, ingoing:0.18773992667190803});
CREATE (patagonia:Trade_node {name:"patagonia", local_value:0.082, is_inland:false, total_power:27.53, outgoing:0.014785615691972395, ingoing:0});
CREATE (amazonas_node:Trade_node {name:"amazonas_node", local_value:0.297, is_inland:false, total_power:35.788, outgoing:0.0, ingoing:0});
CREATE (rio_grande:Trade_node {name:"rio_grande", local_value:1.711, is_inland:false, total_power:221.768, outgoing:0.13362847660618304, ingoing:0});
CREATE (james_bay:Trade_node {name:"james_bay", local_value:1.115, is_inland:false, total_power:153.526, outgoing:0.05317685603741385, ingoing:0});
CREATE (california:Trade_node {name:"california", local_value:1.184, is_inland:false, total_power:174.86599999999996, outgoing:0.006327053803871296, ingoing:0.07468781623180634});
CREATE (girin:Trade_node {name:"girin", local_value:4.45, is_inland:false, total_power:638.3280000000001, outgoing:0.5007776735041073, ingoing:0.0016608516235162153});
CREATE (mississippi_river:Trade_node {name:"mississippi_river", local_value:2.888, is_inland:false, total_power:288.08000000000004, outgoing:0.031303037501443955, ingoing:0.048430818435680284});
CREATE (ohio:Trade_node {name:"ohio", local_value:5.065, is_inland:true, total_power:813.2559999999999, outgoing:0.2657384538781044, ingoing:0.016434094688258075});
CREATE (mexico:Trade_node {name:"mexico", local_value:8.985, is_inland:false, total_power:692.52, outgoing:0.0482247353661363, ingoing:0.048430818435680284});
CREATE (lhasa:Trade_node {name:"lhasa", local_value:1.569, is_inland:true, total_power:580.2039999999998, outgoing:0.4490710974071191, ingoing:0});
CREATE (chengdu:Trade_node {name:"chengdu", local_value:2.705, is_inland:true, total_power:554.0659999999999, outgoing:1.189128163916911, ingoing:0.15717488409249167});
CREATE (burma:Trade_node {name:"burma", local_value:6.637, is_inland:false, total_power:712.75, outgoing:1.1124701686015872, ingoing:0.4161948573709188});
CREATE (gulf_of_siam:Trade_node {name:"gulf_of_siam", local_value:10.956, is_inland:false, total_power:816.6820000000005, outgoing:1.7511244823687961, ingoing:0.5840468385158333});
CREATE (canton:Trade_node {name:"canton", local_value:5.411, is_inland:false, total_power:670.2200000000003, outgoing:2.7017345908187145, ingoing:1.3355352106145368});
CREATE (philippines:Trade_node {name:"philippines", local_value:3.084, is_inland:false, total_power:550.718, outgoing:0.2591903161757923, ingoing:0.9456071067865501});
CREATE (cuiaba:Trade_node {name:"cuiaba", local_value:1.183, is_inland:false, total_power:176.138, outgoing:0.13543607591473547, ingoing:0.015524896476571015});
CREATE (lima:Trade_node {name:"lima", local_value:2.225, is_inland:false, total_power:290.912, outgoing:0.022082561823898072, ingoing:0.04740262657015742});
CREATE (polynesia_node:Trade_node {name:"polynesia_node", local_value:1.791, is_inland:false, total_power:868.6760000000002, outgoing:0.0, ingoing:0.16620776995150136});
CREATE (australia:Trade_node {name:"australia", local_value:2.045, is_inland:false, total_power:333.57599999999996, outgoing:0.0, ingoing:0.0});
CREATE (nippon:Trade_node {name:"nippon", local_value:8.027, is_inland:false, total_power:1135.5399999999997, outgoing:0.28490605511176, ingoing:0.17527218572643757});
CREATE (hangzhou:Trade_node {name:"hangzhou", local_value:8.561, is_inland:false, total_power:534.2459999999999, outgoing:4.90287923232695, ingoing:1.244758464653898});
CREATE (xian:Trade_node {name:"xian", local_value:4.221, is_inland:true, total_power:549.226, outgoing:3.176601294342676, ingoing:2.1322025886853515});
CREATE (beijing:Trade_node {name:"beijing", local_value:4.373, is_inland:false, total_power:326.54400000000004, outgoing:0.4738637675103589, ingoing:3.5589955965707754});
CREATE (the_moluccas:Trade_node {name:"the_moluccas", local_value:7.742, is_inland:false, total_power:680.6840000000002, outgoing:0.5010166024739675, ingoing:0.13607491599229096});
CREATE (siberia:Trade_node {name:"siberia", local_value:1.481, is_inland:true, total_power:257.976, outgoing:0.22686619668042532, ingoing:0.17527218572643757});
CREATE (yumen:Trade_node {name:"yumen", local_value:2.848, is_inland:true, total_power:500.152, outgoing:0.4024434498151434, ingoing:2.1652726354157816});
CREATE (malacca:Trade_node {name:"malacca", local_value:10.652, is_inland:false, total_power:1231.2240000000006, outgoing:0.6183516752706555, ingoing:4.1070226239422665});
CREATE (ganges_delta:Trade_node {name:"ganges_delta", local_value:8.306, is_inland:false, total_power:1172.4080000000001, outgoing:1.6467463883528162, ingoing:1.0658563521254192});
CREATE (doab:Trade_node {name:"doab", local_value:6.978, is_inland:true, total_power:1243.2320000000002, outgoing:1.564903871845784, ingoing:0.8645418538852285});
CREATE (lahore:Trade_node {name:"lahore", local_value:5.742, is_inland:true, total_power:929.6479999999996, outgoing:1.5057648618408148, ingoing:0.9787494168115283});
CREATE (deccan:Trade_node {name:"deccan", local_value:8.906, is_inland:true, total_power:1128.1840000000004, outgoing:2.1710250448223505, ingoing:0.8215745327190366});
CREATE (comorin_cape:Trade_node {name:"comorin_cape", local_value:10.339, is_inland:false, total_power:1268.8820000000005, outgoing:0.798208141772303, ingoing:2.0043300024169626});
CREATE (gujarat:Trade_node {name:"gujarat", local_value:5.57, is_inland:false, total_power:881.0209999999998, outgoing:1.0119811372811092, ingoing:1.9461786997963253});
CREATE (katsina:Trade_node {name:"katsina", local_value:3.697, is_inland:true, total_power:674.194, outgoing:0.6802880521036972, ingoing:0});
CREATE (ethiopia:Trade_node {name:"ethiopia", local_value:2.512, is_inland:true, total_power:616.1080000000001, outgoing:0.35567389710684544, ingoing:0.23810081823629406});
CREATE (gulf_of_aden:Trade_node {name:"gulf_of_aden", local_value:5.534, is_inland:false, total_power:937.5630000000001, outgoing:0.7232904530770788, ingoing:0.8202950436497882});
CREATE (hormuz:Trade_node {name:"hormuz", local_value:3.583, is_inland:false, total_power:392.6979999999999, outgoing:0.49269617441281366, ingoing:0.6073450566253658});
CREATE (zanzibar:Trade_node {name:"zanzibar", local_value:3.341, is_inland:false, total_power:759.1819999999999, outgoing:0.008889001023344367, ingoing:1.041058165522482});
CREATE (cape_of_good_hope:Trade_node {name:"cape_of_good_hope", local_value:0.0, is_inland:false, total_power:0.0, outgoing:0, ingoing:0.6133409302119117});
CREATE (basra:Trade_node {name:"basra", local_value:3.202, is_inland:false, total_power:671.3940000000001, outgoing:1.1302014443336117, ingoing:0.5173309831334544});
CREATE (samarkand:Trade_node {name:"samarkand", local_value:4.509, is_inland:true, total_power:677.8619999999999, outgoing:1.395512276124377, ingoing:1.068688077207409});
CREATE (persia:Trade_node {name:"persia", local_value:6.085, is_inland:true, total_power:1291.696, outgoing:1.0057966863626304, ingoing:1.8530174048847292});
CREATE (aleppo:Trade_node {name:"aleppo", local_value:4.268, is_inland:false, total_power:524.6519999999999, outgoing:1.4096308976779548, ingoing:1.1213990186155272});
CREATE (alexandria:Trade_node {name:"alexandria", local_value:6.764, is_inland:false, total_power:694.4360000000001, outgoing:1.8074035719766977, ingoing:1.1799366758389978});
CREATE (astrakhan:Trade_node {name:"astrakhan", local_value:2.908, is_inland:true, total_power:696.7979999999999, outgoing:0.9392974233304527, ingoing:1.260687205305679});
CREATE (crimea:Trade_node {name:"crimea", local_value:4.205, is_inland:false, total_power:816.0979999999998, outgoing:1.240370684791908, ingoing:0.4931311472484877});
CREATE (constantinople:Trade_node {name:"constantinople", local_value:6.006, is_inland:false, total_power:588.1240000000001, outgoing:0.528579696172331, ingoing:1.8067772111499383});
CREATE (kiev:Trade_node {name:"kiev", local_value:4.514, is_inland:true, total_power:867.582, outgoing:1.5652862865462849, ingoing:0.43412973967716784});
CREATE (kazan:Trade_node {name:"kazan", local_value:2.813, is_inland:true, total_power:309.832, outgoing:0.7593332937273595, ingoing:0.612235900505711});
CREATE (novgorod:Trade_node {name:"novgorod", local_value:5.857, is_inland:false, total_power:608.9419999999999, outgoing:0.7581511465127987, ingoing:1.619075258850527});
CREATE (laplata:Trade_node {name:"laplata", local_value:0.148, is_inland:false, total_power:80.74, outgoing:0.0, ingoing:0.06292752304672844});
CREATE (brazil:Trade_node {name:"brazil", local_value:0.793, is_inland:false, total_power:73.326, outgoing:0.0, ingoing:0.04740262657015742});
CREATE (timbuktu:Trade_node {name:"timbuktu", local_value:4.55, is_inland:true, total_power:736.042, outgoing:0.2957784582993468, ingoing:0.23810081823629406});
CREATE (ivory_coast:Trade_node {name:"ivory_coast", local_value:1.876, is_inland:false, total_power:322.01400000000007, outgoing:0.20656695656573082, ingoing:0.3430236172790651});
CREATE (tunis:Trade_node {name:"tunis", local_value:2.404, is_inland:false, total_power:464.75399999999985, outgoing:0.7388893256402872, ingoing:0.23810081823629406});
CREATE (ragusa:Trade_node {name:"ragusa", local_value:4.88, is_inland:false, total_power:958.096, outgoing:1.788256361131945, ingoing:0.5550086809809476});
CREATE (safi:Trade_node {name:"safi", local_value:3.426, is_inland:false, total_power:412.68200000000013, outgoing:0.46814721740588067, ingoing:0.15528369060715708});
CREATE (pest:Trade_node {name:"pest", local_value:3.309, is_inland:true, total_power:771.854, outgoing:1.1667092290622842, ingoing:1.0600194660733486});
CREATE (krakow:Trade_node {name:"krakow", local_value:4.777, is_inland:true, total_power:1013.1360000000001, outgoing:1.6728708999506772, ingoing:1.4342976456944987});
CREATE (wien:Trade_node {name:"wien", local_value:6.243, is_inland:true, total_power:1654.5179999999998, outgoing:1.9649225021204768, ingoing:1.1980271602404362});
CREATE (saxony:Trade_node {name:"saxony", local_value:5.283, is_inland:true, total_power:1227.2720000000006, outgoing:1.6157706450639817, ingoing:1.2732276907249038});
CREATE (baltic_sea:Trade_node {name:"baltic_sea", local_value:5.665, is_inland:false, total_power:616.1300000000001, outgoing:0.6469946465504153, ingoing:0.9835341669019564});
CREATE (rheinland:Trade_node {name:"rheinland", local_value:7.552, is_inland:true, total_power:1439.46, outgoing:1.5796643578879517, ingoing:1.5360024644007573});
CREATE (panama:Trade_node {name:"panama", local_value:1.131, is_inland:false, total_power:95.85, outgoing:0.0, ingoing:0.028472002335694194});
CREATE (carribean_trade:Trade_node {name:"carribean_trade", local_value:0.099, is_inland:false, total_power:16.998, outgoing:0.0, ingoing:0.08753657816491012});
CREATE (chesapeake_bay:Trade_node {name:"chesapeake_bay", local_value:4.647, is_inland:false, total_power:349.5319999999999, outgoing:0.13359926354931242, ingoing:0.13951268828600483});
CREATE (st_lawrence:Trade_node {name:"st_lawrence", local_value:2.925, is_inland:false, total_power:197.89, outgoing:0.0, ingoing:0.23757015106903612});
CREATE (white_sea:Trade_node {name:"white_sea", local_value:1.476, is_inland:false, total_power:117.41399999999999, outgoing:0.6434145096374161, ingoing:0.39802935191921934});
CREATE (north_sea:Trade_node {name:"north_sea", local_value:4.317, is_inland:false, total_power:1066.7179999999996, outgoing:0.9671373506398264, ingoing:0.6755852351192869});
CREATE (lubeck:Trade_node {name:"lubeck", local_value:5.818, is_inland:false, total_power:1206.6480000000001, outgoing:0.5510622303390725, ingoing:2.86469486451361});
CREATE (bordeaux:Trade_node {name:"bordeaux", local_value:4.182, is_inland:false, total_power:441.022, outgoing:0.9417395421506174, ingoing:0.05422382609850434});
CREATE (sevilla:Trade_node {name:"sevilla", local_value:7.256, is_inland:false, total_power:632.968, outgoing:0.35981972306477283, ingoing:0.8043896683487796});
CREATE (champagne:Trade_node {name:"champagne", local_value:7.497, is_inland:true, total_power:1388.8780000000002, outgoing:1.571132396114675, ingoing:1.818150307149323});
CREATE (valencia:Trade_node {name:"valencia", local_value:3.217, is_inland:false, total_power:288.9649999999999, outgoing:0.8367472481161577, ingoing:0.6364219731921119});
CREATE (genua:Trade_node {name:"genua", local_value:9.344, is_inland:false, total_power:736.0519999999998, outgoing:0.7226820608550747, ingoing:3.2205213590442954});
CREATE (venice:Trade_node {name:"venice", local_value:6.346, is_inland:false, total_power:519.232, outgoing:0.3577149984753327, ingoing:1.9462038523301919});
CREATE (english_channel:Trade_node {name:"english_channel", local_value:9.62, is_inland:false, total_power:1091.278, outgoing:0.12936416629400133, ingoing:2.0355703983640328});
CREATE (SWE:Country {name:"SWE", home_node:"baltic_sea", development:107.373});
CREATE (DAN:Country {name:"DAN", home_node:"lubeck", development:118.0});
CREATE (GOT:Country {name:"GOT", home_node:"baltic_sea", development:10.0});
CREATE (NOR:Country {name:"NOR", home_node:"lubeck", development:78.015});
CREATE (SHL:Country {name:"SHL", home_node:"lubeck", development:19.713});
CREATE (ALB:Country {name:"ALB", home_node:"ragusa", development:10.915});
CREATE (ATH:Country {name:"ATH", home_node:"ragusa", development:11.0});
CREATE (BOS:Country {name:"BOS", home_node:"ragusa", development:22.0});
CREATE (BYZ:Country {name:"BYZ", home_node:"constantinople", development:37.79});
CREATE (CRO:Country {name:"CRO", home_node:"ragusa", development:40.808});
CREATE (CYP:Country {name:"CYP", home_node:"aleppo", development:15.0});
CREATE (EPI:Country {name:"EPI", home_node:"ragusa", development:10.765});
CREATE (KNI:Country {name:"KNI", home_node:"constantinople", development:11.0});
CREATE (MOL:Country {name:"MOL", home_node:"crimea", development:39.0});
CREATE (NAX:Country {name:"NAX", home_node:"constantinople", development:6.0});
CREATE (RAG:Country {name:"RAG", home_node:"ragusa", development:14.0});
CREATE (SER:Country {name:"SER", home_node:"ragusa", development:46.98});
CREATE (WAL:Country {name:"WAL", home_node:"pest", development:45.703});
CREATE (HUN:Country {name:"HUN", home_node:"pest", development:172.224});
CREATE (TUR:Country {name:"TUR", home_node:"constantinople", development:312.922});
CREATE (CNN:Country {name:"CNN", home_node:"north_sea", development:6.0});
CREATE (ENG:Country {name:"ENG", home_node:"english_channel", development:315.602});
CREATE (LEI:Country {name:"LEI", home_node:"north_sea", development:6.0});
CREATE (MNS:Country {name:"MNS", home_node:"north_sea", development:8.0});
CREATE (SCO:Country {name:"SCO", home_node:"north_sea", development:78.039});
CREATE (TYR:Country {name:"TYR", home_node:"north_sea", development:8.0});
CREATE (ULS:Country {name:"ULS", home_node:"north_sea", development:8.0});
CREATE (DMS:Country {name:"DMS", home_node:"north_sea", development:6.0});
CREATE (SLN:Country {name:"SLN", home_node:"north_sea", development:8.0});
CREATE (KID:Country {name:"KID", home_node:"north_sea", development:9.0});
CREATE (ORD:Country {name:"ORD", home_node:"north_sea", development:7.0});
CREATE (TRY:Country {name:"TRY", home_node:"north_sea", development:5.0});
CREATE (FLY:Country {name:"FLY", home_node:"north_sea", development:5.0});
CREATE (MCM:Country {name:"MCM", home_node:"north_sea", development:4.0});
CREATE (LOI:Country {name:"LOI", home_node:"north_sea", development:5.949});
CREATE (LIT:Country {name:"LIT", home_node:"kiev", development:220.82});
CREATE (LIV:Country {name:"LIV", home_node:"baltic_sea", development:54.75});
CREATE (MAZ:Country {name:"MAZ", home_node:"krakow", development:35.947});
CREATE (POL:Country {name:"POL", home_node:"krakow", development:181.717});
CREATE (RIG:Country {name:"RIG", home_node:"baltic_sea", development:11.0});
CREATE (TEU:Country {name:"TEU", home_node:"baltic_sea", development:92.818});
CREATE (OKA:Country {name:"OKA", home_node:"kiev", development:9.0});
CREATE (ALS:Country {name:"ALS", home_node:"rheinland", development:16.0});
CREATE (AMG:Country {name:"AMG", home_node:"bordeaux", development:25.788});
CREATE (AUV:Country {name:"AUV", home_node:"bordeaux", development:10.0});
CREATE (BOU:Country {name:"BOU", home_node:"bordeaux", development:16.564});
CREATE (BRI:Country {name:"BRI", home_node:"bordeaux", development:55.0});
CREATE (BUR:Country {name:"BUR", home_node:"champagne", development:139.637});
CREATE (FOI:Country {name:"FOI", home_node:"bordeaux", development:17.772});
CREATE (FRA:Country {name:"FRA", home_node:"champagne", development:245.966});
CREATE (NEV:Country {name:"NEV", home_node:"champagne", development:25.362});
CREATE (ORL:Country {name:"ORL", home_node:"champagne", development:49.468});
CREATE (PRO:Country {name:"PRO", home_node:"bordeaux", development:70.758});
CREATE (AAC:Country {name:"AAC", home_node:"rheinland", development:14.0});
CREATE (ANH:Country {name:"ANH", home_node:"saxony", development:6.0});
CREATE (ANS:Country {name:"ANS", home_node:"rheinland", development:8.0});
CREATE (AUG:Country {name:"AUG", home_node:"wien", development:17.0});
CREATE (BAD:Country {name:"BAD", home_node:"rheinland", development:16.0});
CREATE (BOH:Country {name:"BOH", home_node:"saxony", development:118.409});
CREATE (BRA:Country {name:"BRA", home_node:"saxony", development:64.005});
CREATE (BRE:Country {name:"BRE", home_node:"lubeck", development:16.0});
CREATE (BRU:Country {name:"BRU", home_node:"saxony", development:31.211});
CREATE (EFR:Country {name:"EFR", home_node:"english_channel", development:7.0});
CREATE (FRN:Country {name:"FRN", home_node:"rheinland", development:18.0});
CREATE (HAB:Country {name:"HAB", home_node:"wien", development:168.835});
CREATE (HAM:Country {name:"HAM", home_node:"lubeck", development:19.0});
CREATE (HES:Country {name:"HES", home_node:"rheinland", development:23.0});
CREATE (KLE:Country {name:"KLE", home_node:"rheinland", development:14.0});
CREATE (KOL:Country {name:"KOL", home_node:"rheinland", development:26.398});
CREATE (LAU:Country {name:"LAU", home_node:"lubeck", development:5.0});
CREATE (LOR:Country {name:"LOR", home_node:"champagne", development:32.0});
CREATE (LUN:Country {name:"LUN", home_node:"lubeck", development:14.0});
CREATE (MAG:Country {name:"MAG", home_node:"saxony", development:16.93});
CREATE (MAI:Country {name:"MAI", home_node:"rheinland", development:16.678});
CREATE (MKL:Country {name:"MKL", home_node:"lubeck", development:23.86});
CREATE (MUN:Country {name:"MUN", home_node:"rheinland", development:25.551});
CREATE (OLD:Country {name:"OLD", home_node:"lubeck", development:7.0});
CREATE (PAL:Country {name:"PAL", home_node:"rheinland", development:39.577});
CREATE (SAX:Country {name:"SAX", home_node:"saxony", development:41.0});
CREATE (SLZ:Country {name:"SLZ", home_node:"wien", development:14.0});
CREATE (SWI:Country {name:"SWI", home_node:"champagne", development:51.0});
CREATE (THU:Country {name:"THU", home_node:"saxony", development:33.977});
CREATE (TRI:Country {name:"TRI", home_node:"rheinland", development:22.0});
CREATE (ULM:Country {name:"ULM", home_node:"wien", development:15.0});
CREATE (WBG:Country {name:"WBG", home_node:"rheinland", development:15.515});
CREATE (WUR:Country {name:"WUR", home_node:"wien", development:20.0});
CREATE (NUM:Country {name:"NUM", home_node:"rheinland", development:19.0});
CREATE (MEM:Country {name:"MEM", home_node:"wien", development:13.0});
CREATE (VER:Country {name:"VER", home_node:"lubeck", development:13.0});
CREATE (NSA:Country {name:"NSA", home_node:"rheinland", development:9.0});
CREATE (RVA:Country {name:"RVA", home_node:"rheinland", development:12.0});
CREATE (DTT:Country {name:"DTT", home_node:"lubeck", development:8.0});
CREATE (ARA:Country {name:"ARA", home_node:"valencia", development:196.943});
CREATE (CAS:Country {name:"CAS", home_node:"sevilla", development:275.487});
CREATE (GRA:Country {name:"GRA", home_node:"sevilla", development:30.0});
CREATE (NAV:Country {name:"NAV", home_node:"bordeaux", development:7.0});
CREATE (POR:Country {name:"POR", home_node:"sevilla", development:135.181});
CREATE (FER:Country {name:"FER", home_node:"venice", development:25.796});
CREATE (GEN:Country {name:"GEN", home_node:"genua", development:75.916});
CREATE (MAN:Country {name:"MAN", home_node:"venice", development:19.0});
CREATE (MLO:Country {name:"MLO", home_node:"genua", development:81.513});
CREATE (NAP:Country {name:"NAP", home_node:"genua", development:101.724});
CREATE (PAP:Country {name:"PAP", home_node:"genua", development:62.238});
CREATE (SAV:Country {name:"SAV", home_node:"champagne", development:72.343});
CREATE (SIE:Country {name:"SIE", home_node:"genua", development:20.0});
CREATE (URB:Country {name:"URB", home_node:"venice", development:12.0});
CREATE (VEN:Country {name:"VEN", home_node:"venice", development:170.281});
CREATE (MFA:Country {name:"MFA", home_node:"genua", development:12.0});
CREATE (LUC:Country {name:"LUC", home_node:"genua", development:14.0});
CREATE (LAN:Country {name:"LAN", home_node:"genua", development:56.745});
CREATE (BRB:Country {name:"BRB", home_node:"english_channel", development:53.986});
CREATE (FLA:Country {name:"FLA", home_node:"english_channel", development:40.933});
CREATE (FRI:Country {name:"FRI", home_node:"english_channel", development:24.0});
CREATE (GEL:Country {name:"GEL", home_node:"english_channel", development:27.642});
CREATE (HOL:Country {name:"HOL", home_node:"english_channel", development:42.0});
CREATE (LIE:Country {name:"LIE", home_node:"champagne", development:26.436});
CREATE (UTR:Country {name:"UTR", home_node:"english_channel", development:20.0});
CREATE (CRI:Country {name:"CRI", home_node:"crimea", development:73.0});
CREATE (GEO:Country {name:"GEO", home_node:"crimea", development:29.0});
CREATE (KAZ:Country {name:"KAZ", home_node:"kazan", development:105.938});
CREATE (MOS:Country {name:"MOS", home_node:"novgorod", development:191.788});
CREATE (NOV:Country {name:"NOV", home_node:"novgorod", development:124.214});
CREATE (PSK:Country {name:"PSK", home_node:"novgorod", development:20.0});
CREATE (RYA:Country {name:"RYA", home_node:"novgorod", development:32.621});
CREATE (TVE:Country {name:"TVE", home_node:"novgorod", development:23.0});
CREATE (YAR:Country {name:"YAR", home_node:"novgorod", development:17.0});
CREATE (NOG:Country {name:"NOG", home_node:"astrakhan", development:90.454});
CREATE (PRM:Country {name:"PRM", home_node:"white_sea", development:24.785});
CREATE (FEO:Country {name:"FEO", home_node:"crimea", development:9.0});
CREATE (BLO:Country {name:"BLO", home_node:"novgorod", development:12.0});
CREATE (RSO:Country {name:"RSO", home_node:"novgorod", development:8.0});
CREATE (GOL:Country {name:"GOL", home_node:"astrakhan", development:128.11});
CREATE (ADE:Country {name:"ADE", home_node:"gulf_of_aden", development:27.491});
CREATE (ALH:Country {name:"ALH", home_node:"basra", development:18.796});
CREATE (ANZ:Country {name:"ANZ", home_node:"basra", development:7.0});
CREATE (ARD:Country {name:"ARD", home_node:"basra", development:7.0});
CREATE (DAW:Country {name:"DAW", home_node:"basra", development:14.0});
CREATE (FAD:Country {name:"FAD", home_node:"aleppo", development:10.881});
CREATE (HDR:Country {name:"HDR", home_node:"gulf_of_aden", development:15.0});
CREATE (HED:Country {name:"HED", home_node:"alexandria", development:34.0});
CREATE (MAK:Country {name:"MAK", home_node:"alexandria", development:9.0});
CREATE (MDA:Country {name:"MDA", home_node:"alexandria", development:15.0});
CREATE (MFL:Country {name:"MFL", home_node:"gulf_of_aden", development:13.0});
CREATE (MHR:Country {name:"MHR", home_node:"gulf_of_aden", development:13.728});
CREATE (NAJ:Country {name:"NAJ", home_node:"basra", development:8.312});
CREATE (NJR:Country {name:"NJR", home_node:"gulf_of_aden", development:6.0});
CREATE (OMA:Country {name:"OMA", home_node:"hormuz", development:19.915});
CREATE (RAS:Country {name:"RAS", home_node:"gulf_of_aden", development:19.841});
CREATE (SHM:Country {name:"SHM", home_node:"basra", development:9.0});
CREATE (SRV:Country {name:"SRV", home_node:"persia", development:25.678});
CREATE (YAS:Country {name:"YAS", home_node:"hormuz", development:3.0});
CREATE (YEM:Country {name:"YEM", home_node:"gulf_of_aden", development:26.0});
CREATE (HSN:Country {name:"HSN", home_node:"aleppo", development:6.0});
CREATE (BTL:Country {name:"BTL", home_node:"aleppo", development:16.179});
CREATE (AKK:Country {name:"AKK", home_node:"aleppo", development:52.378});
CREATE (CND:Country {name:"CND", home_node:"constantinople", development:18.0});
CREATE (DUL:Country {name:"DUL", home_node:"aleppo", development:23.0});
CREATE (KAR:Country {name:"KAR", home_node:"aleppo", development:42.732});
CREATE (TRE:Country {name:"TRE", home_node:"crimea", development:12.0});
CREATE (RAM:Country {name:"RAM", home_node:"aleppo", development:10.0});
CREATE (AVR:Country {name:"AVR", home_node:"astrakhan", development:7.0});
CREATE (MLK:Country {name:"MLK", home_node:"persia", development:9.0});
CREATE (SME:Country {name:"SME", home_node:"crimea", development:15.72});
CREATE (ARL:Country {name:"ARL", home_node:"persia", development:9.0});
CREATE (MSY:Country {name:"MSY", home_node:"basra", development:39.992});
CREATE (MAM:Country {name:"MAM", home_node:"alexandria", development:318.025});
CREATE (MOR:Country {name:"MOR", home_node:"safi", development:86.675});
CREATE (TUN:Country {name:"TUN", home_node:"tunis", development:108.956});
CREATE (TFL:Country {name:"TFL", home_node:"safi", development:19.499});
CREATE (SOS:Country {name:"SOS", home_node:"safi", development:27.628});
CREATE (TLC:Country {name:"TLC", home_node:"safi", development:55.43});
CREATE (TGT:Country {name:"TGT", home_node:"tunis", development:7.668});
CREATE (GHD:Country {name:"GHD", home_node:"tunis", development:7.97});
CREATE (FZA:Country {name:"FZA", home_node:"tunis", development:9.717});
CREATE (MZB:Country {name:"MZB", home_node:"tunis", development:5.709});
CREATE (MRK:Country {name:"MRK", home_node:"safi", development:19.364});
CREATE (SHY:Country {name:"SHY", home_node:"siberia", development:177.948});
CREATE (AFG:Country {name:"AFG", home_node:"lahore", development:46.435});
CREATE (KHO:Country {name:"KHO", home_node:"persia", development:19.779});
CREATE (QAR:Country {name:"QAR", home_node:"persia", development:153.492});
CREATE (TIM:Country {name:"TIM", home_node:"persia", development:136.62});
CREATE (TRS:Country {name:"TRS", home_node:"samarkand", development:121.518});
CREATE (KRY:Country {name:"KRY", home_node:"persia", development:12.0});
CREATE (CIR:Country {name:"CIR", home_node:"crimea", development:20.0});
CREATE (GAZ:Country {name:"GAZ", home_node:"astrakhan", development:16.0});
CREATE (IME:Country {name:"IME", home_node:"crimea", development:28.8});
CREATE (TAB:Country {name:"TAB", home_node:"persia", development:21.752});
CREATE (ORM:Country {name:"ORM", home_node:"hormuz", development:42.466});
CREATE (LRI:Country {name:"LRI", home_node:"basra", development:13.898});
CREATE (SIS:Country {name:"SIS", home_node:"persia", development:13.264});
CREATE (BPI:Country {name:"BPI", home_node:"persia", development:12.649});
CREATE (FRS:Country {name:"FRS", home_node:"hormuz", development:55.309});
CREATE (QOM:Country {name:"QOM", home_node:"persia", development:88.105});
CREATE (AZT:Country {name:"AZT", home_node:"mexico", development:50.77});
CREATE (CHE:Country {name:"CHE", home_node:"chesapeake_bay", development:8.0});
CREATE (CHM:Country {name:"CHM", home_node:"lima", development:24.0});
CREATE (ZAP:Country {name:"ZAP", home_node:"mexico", development:20.278});
CREATE (BEN:Country {name:"BEN", home_node:"ivory_coast", development:24.0});
CREATE (ETH:Country {name:"ETH", home_node:"ethiopia", development:66.426});
CREATE (KON:Country {name:"KON", home_node:"kongo", development:55.18});
CREATE (MAL:Country {name:"MAL", home_node:"timbuktu", development:92.318});
CREATE (SON:Country {name:"SON", home_node:"timbuktu", development:52.494});
CREATE (ZAN:Country {name:"ZAN", home_node:"zanzibar", development:38.066});
CREATE (ZIM:Country {name:"ZIM", home_node:"zambezi", development:46.0});
CREATE (ADA:Country {name:"ADA", home_node:"gulf_of_aden", development:56.262});
CREATE (KBO:Country {name:"KBO", home_node:"katsina", development:27.724});
CREATE (LOA:Country {name:"LOA", home_node:"ivory_coast", development:14.0});
CREATE (OYO:Country {name:"OYO", home_node:"katsina", development:23.0});
CREATE (JOL:Country {name:"JOL", home_node:"ivory_coast", development:23.402});
CREATE (SFA:Country {name:"SFA", home_node:"zanzibar", development:16.0});
CREATE (MBA:Country {name:"MBA", home_node:"zanzibar", development:15.624});
CREATE (MLI:Country {name:"MLI", home_node:"zanzibar", development:14.578});
CREATE (AJU:Country {name:"AJU", home_node:"gulf_of_aden", development:34.156});
CREATE (MDI:Country {name:"MDI", home_node:"gulf_of_aden", development:15.0});
CREATE (ENA:Country {name:"ENA", home_node:"ethiopia", development:7.0});
CREATE (WGD:Country {name:"WGD", home_node:"timbuktu", development:18.958});
CREATE (GUR:Country {name:"GUR", home_node:"timbuktu", development:5.862});
CREATE (OGD:Country {name:"OGD", home_node:"ethiopia", development:6.0});
CREATE (WAD:Country {name:"WAD", home_node:"ethiopia", development:5.862});
CREATE (ALO:Country {name:"ALO", home_node:"ethiopia", development:17.394});
CREATE (KAF:Country {name:"KAF", home_node:"ethiopia", development:13.112});
CREATE (MED:Country {name:"MED", home_node:"ethiopia", development:17.0});
CREATE (MRE:Country {name:"MRE", home_node:"gulf_of_aden", development:16.916});
CREATE (PTE:Country {name:"PTE", home_node:"zanzibar", development:8.0});
CREATE (WAR:Country {name:"WAR", home_node:"gulf_of_aden", development:30.0});
CREATE (BEJ:Country {name:"BEJ", home_node:"ethiopia", development:10.409});
CREATE (WLY:Country {name:"WLY", home_node:"ethiopia", development:12.0});
CREATE (DAM:Country {name:"DAM", home_node:"ethiopia", development:13.0});
CREATE (HDY:Country {name:"HDY", home_node:"ethiopia", development:10.612});
CREATE (JJI:Country {name:"JJI", home_node:"ethiopia", development:4.0});
CREATE (ABB:Country {name:"ABB", home_node:"ethiopia", development:12.932});
CREATE (TYO:Country {name:"TYO", home_node:"kongo", development:6.0});
CREATE (KSJ:Country {name:"KSJ", home_node:"kongo", development:16.0});
CREATE (LUB:Country {name:"LUB", home_node:"kongo", development:22.0});
CREATE (LND:Country {name:"LND", home_node:"kongo", development:20.0});
CREATE (CKW:Country {name:"CKW", home_node:"kongo", development:9.0});
CREATE (KIK:Country {name:"KIK", home_node:"kongo", development:10.0});
CREATE (KZB:Country {name:"KZB", home_node:"kongo", development:21.0});
CREATE (YAK:Country {name:"YAK", home_node:"kongo", development:13.0});
CREATE (KLD:Country {name:"KLD", home_node:"kongo", development:9.0});
CREATE (KUB:Country {name:"KUB", home_node:"kongo", development:16.0});
CREATE (RWA:Country {name:"RWA", home_node:"african_great_lakes", development:18.0});
CREATE (BUU:Country {name:"BUU", home_node:"african_great_lakes", development:9.0});
CREATE (BUG:Country {name:"BUG", home_node:"african_great_lakes", development:12.0});
CREATE (NKO:Country {name:"NKO", home_node:"african_great_lakes", development:8.0});
CREATE (KRW:Country {name:"KRW", home_node:"african_great_lakes", development:13.712});
CREATE (BNY:Country {name:"BNY", home_node:"african_great_lakes", development:13.916});
CREATE (BSG:Country {name:"BSG", home_node:"african_great_lakes", development:9.0});
CREATE (UBH:Country {name:"UBH", home_node:"african_great_lakes", development:20.0});
CREATE (MRA:Country {name:"MRA", home_node:"zambezi", development:18.662});
CREATE (LDU:Country {name:"LDU", home_node:"zambezi", development:11.412});
CREATE (TBK:Country {name:"TBK", home_node:"zambezi", development:11.0});
CREATE (MKU:Country {name:"MKU", home_node:"zambezi", development:5.662});
CREATE (RZW:Country {name:"RZW", home_node:"zambezi", development:9.505});
CREATE (MIR:Country {name:"MIR", home_node:"zanzibar", development:12.814});
CREATE (SKA:Country {name:"SKA", home_node:"zanzibar", development:22.045});
CREATE (BTS:Country {name:"BTS", home_node:"zanzibar", development:14.176});
CREATE (MFY:Country {name:"MFY", home_node:"zanzibar", development:6.0});
CREATE (ANT:Country {name:"ANT", home_node:"zanzibar", development:11.0});
CREATE (ARK:Country {name:"ARK", home_node:"ganges_delta", development:20.0});
CREATE (ATJ:Country {name:"ATJ", home_node:"malacca", development:18.0});
CREATE (AYU:Country {name:"AYU", home_node:"gulf_of_siam", development:132.466});
CREATE (BLI:Country {name:"BLI", home_node:"the_moluccas", development:9.0});
CREATE (BEI:Country {name:"BEI", home_node:"malacca", development:74.632});
CREATE (CHA:Country {name:"CHA", home_node:"gulf_of_siam", development:41.0});
CREATE (CHG:Country {name:"CHG", home_node:"yumen", development:80.286});
CREATE (DAI:Country {name:"DAI", home_node:"canton", development:115.962});
CREATE (AMA:Country {name:"AMA", home_node:"nippon", development:8.0});
CREATE (DTE:Country {name:"DTE", home_node:"nippon", development:9.0});
CREATE (HSK:Country {name:"HSK", home_node:"nippon", development:40.474});
CREATE (HTK:Country {name:"HTK", home_node:"nippon", development:15.321});
CREATE (IMG:Country {name:"IMG", home_node:"nippon", development:11.0});
CREATE (ODA:Country {name:"ODA", home_node:"nippon", development:10.0});
CREATE (OTM:Country {name:"OTM", home_node:"nippon", development:9.0});
CREATE (OUC:Country {name:"OUC", home_node:"nippon", development:18.06});
CREATE (SBA:Country {name:"SBA", home_node:"nippon", development:16.0});
CREATE (SMZ:Country {name:"SMZ", home_node:"nippon", development:9.0});
CREATE (TKD:Country {name:"TKD", home_node:"nippon", development:9.0});
CREATE (TKG:Country {name:"TKG", home_node:"nippon", development:7.0});
CREATE (UES:Country {name:"UES", home_node:"nippon", development:33.0});
CREATE (YMN:Country {name:"YMN", home_node:"nippon", development:28.585});
CREATE (RFR:Country {name:"RFR", home_node:"nippon", development:8.0});
CREATE (ASK:Country {name:"ASK", home_node:"nippon", development:20.832});
CREATE (KTB:Country {name:"KTB", home_node:"nippon", development:6.0});
CREATE (ANU:Country {name:"ANU", home_node:"nippon", development:14.892});
CREATE (AKT:Country {name:"AKT", home_node:"nippon", development:9.0});
CREATE (CBA:Country {name:"CBA", home_node:"nippon", development:5.0});
CREATE (ISK:Country {name:"ISK", home_node:"nippon", development:7.0});
CREATE (ITO:Country {name:"ITO", home_node:"nippon", development:7.0});
CREATE (KKC:Country {name:"KKC", home_node:"nippon", development:8.0});
CREATE (KNO:Country {name:"KNO", home_node:"nippon", development:7.0});
CREATE (OGS:Country {name:"OGS", home_node:"nippon", development:6.0});
CREATE (SHN:Country {name:"SHN", home_node:"nippon", development:9.0});
CREATE (STK:Country {name:"STK", home_node:"nippon", development:6.0});
CREATE (TKI:Country {name:"TKI", home_node:"nippon", development:11.0});
CREATE (UTN:Country {name:"UTN", home_node:"nippon", development:6.0});
CREATE (TTI:Country {name:"TTI", home_node:"nippon", development:9.0});
CREATE (KHA:Country {name:"KHA", home_node:"yumen", development:88.417});
CREATE (KHM:Country {name:"KHM", home_node:"gulf_of_siam", development:110.918});
CREATE (KOR:Country {name:"KOR", home_node:"nippon", development:129.372});
CREATE (LNA:Country {name:"LNA", home_node:"gulf_of_siam", development:55.0});
CREATE (LXA:Country {name:"LXA", home_node:"gulf_of_siam", development:99.348});
CREATE (MAJ:Country {name:"MAJ", home_node:"the_moluccas", development:86.928});
CREATE (MKS:Country {name:"MKS", home_node:"the_moluccas", development:22.0});
CREATE (MLC:Country {name:"MLC", home_node:"malacca", development:60.772});
CREATE (MNG:Country {name:"MNG", home_node:"beijing", development:1066.159});
CREATE (OIR:Country {name:"OIR", home_node:"yumen", development:113.725});
CREATE (PAT:Country {name:"PAT", home_node:"malacca", development:19.95});
CREATE (PEG:Country {name:"PEG", home_node:"burma", development:47.0});
CREATE (RYU:Country {name:"RYU", home_node:"nippon", development:5.0});
CREATE (SUK:Country {name:"SUK", home_node:"gulf_of_siam", development:38.592});
CREATE (SUL:Country {name:"SUL", home_node:"philippines", development:11.0});
CREATE (TAU:Country {name:"TAU", home_node:"burma", development:14.0});
CREATE (SOO:Country {name:"SOO", home_node:"nippon", development:4.0});
CREATE (NVK:Country {name:"NVK", home_node:"girin", development:23.0});
CREATE (SOL:Country {name:"SOL", home_node:"girin", development:33.0});
CREATE (EJZ:Country {name:"EJZ", home_node:"girin", development:20.0});
CREATE (NHX:Country {name:"NHX", home_node:"girin", development:43.0});
CREATE (MYR:Country {name:"MYR", home_node:"girin", development:37.0});
CREATE (MHX:Country {name:"MHX", home_node:"girin", development:40.988});
CREATE (MJZ:Country {name:"MJZ", home_node:"girin", development:72.838});
CREATE (KRC:Country {name:"KRC", home_node:"girin", development:60.32});
CREATE (HMI:Country {name:"HMI", home_node:"yumen", development:12.515});
CREATE (KAS:Country {name:"KAS", home_node:"samarkand", development:58.836});
CREATE (SYG:Country {name:"SYG", home_node:"lhasa", development:6.816});
CREATE (UTS:Country {name:"UTS", home_node:"lhasa", development:35.677});
CREATE (KAM:Country {name:"KAM", home_node:"chengdu", development:30.0});
CREATE (GUG:Country {name:"GUG", home_node:"lhasa", development:10.982});
CREATE (PHA:Country {name:"PHA", home_node:"lhasa", development:9.864});
CREATE (BAL:Country {name:"BAL", home_node:"lahore", development:34.015});
CREATE (BNG:Country {name:"BNG", home_node:"ganges_delta", development:166.634});
CREATE (BAH:Country {name:"BAH", home_node:"deccan", development:186.512});
CREATE (DLH:Country {name:"DLH", home_node:"doab", development:55.634});
CREATE (MYS:Country {name:"MYS", home_node:"comorin_cape", development:19.0});
CREATE (VIJ:Country {name:"VIJ", home_node:"comorin_cape", development:200.238});
CREATE (ASS:Country {name:"ASS", home_node:"burma", development:27.0});
CREATE (GUJ:Country {name:"GUJ", home_node:"gujarat", development:71.93});
CREATE (JNP:Country {name:"JNP", home_node:"doab", development:194.842});
CREATE (MAD:Country {name:"MAD", home_node:"comorin_cape", development:8.0});
CREATE (MLW:Country {name:"MLW", home_node:"deccan", development:53.524});
CREATE (MER:Country {name:"MER", home_node:"gujarat", development:54.724});
CREATE (MUL:Country {name:"MUL", home_node:"lahore", development:37.934});
CREATE (ORI:Country {name:"ORI", home_node:"ganges_delta", development:54.936});
CREATE (SND:Country {name:"SND", home_node:"gujarat", development:48.0});
CREATE (JAN:Country {name:"JAN", home_node:"gujarat", development:6.0});
CREATE (GDW:Country {name:"GDW", home_node:"deccan", development:20.828});
CREATE (GRJ:Country {name:"GRJ", home_node:"ganges_delta", development:5.0});
CREATE (GWA:Country {name:"GWA", home_node:"doab", development:8.0});
CREATE (DHU:Country {name:"DHU", home_node:"doab", development:13.0});
CREATE (KSH:Country {name:"KSH", home_node:"lahore", development:17.958});
CREATE (KHD:Country {name:"KHD", home_node:"deccan", development:24.642});
CREATE (VND:Country {name:"VND", home_node:"comorin_cape", development:12.0});
CREATE (MAB:Country {name:"MAB", home_node:"comorin_cape", development:7.0});
CREATE (MEW:Country {name:"MEW", home_node:"doab", development:9.418});
CREATE (BST:Country {name:"BST", home_node:"ganges_delta", development:7.0});
CREATE (BND:Country {name:"BND", home_node:"doab", development:5.0});
CREATE (CEY:Country {name:"CEY", home_node:"comorin_cape", development:19.0});
CREATE (JSL:Country {name:"JSL", home_node:"gujarat", development:3.0});
CREATE (KAC:Country {name:"KAC", home_node:"burma", development:9.0});
CREATE (KMT:Country {name:"KMT", home_node:"ganges_delta", development:14.418});
CREATE (KGR:Country {name:"KGR", home_node:"doab", development:5.0});
CREATE (KAT:Country {name:"KAT", home_node:"gujarat", development:12.83});
CREATE (KOC:Country {name:"KOC", home_node:"comorin_cape", development:8.0});
CREATE (MLB:Country {name:"MLB", home_node:"burma", development:5.0});
CREATE (HAD:Country {name:"HAD", home_node:"doab", development:10.0});
CREATE (NGA:Country {name:"NGA", home_node:"gujarat", development:9.77});
CREATE (LDK:Country {name:"LDK", home_node:"lahore", development:8.724});
CREATE (BGL:Country {name:"BGL", home_node:"doab", development:10.0});
CREATE (JFN:Country {name:"JFN", home_node:"comorin_cape", development:6.0});
CREATE (GHR:Country {name:"GHR", home_node:"doab", development:3.0});
CREATE (CHD:Country {name:"CHD", home_node:"deccan", development:5.0});
CREATE (NGP:Country {name:"NGP", home_node:"ganges_delta", development:11.889});
CREATE (TRT:Country {name:"TRT", home_node:"ganges_delta", development:20.0});
CREATE (CMP:Country {name:"CMP", home_node:"deccan", development:6.0});
CREATE (BGA:Country {name:"BGA", home_node:"gujarat", development:6.0});
CREATE (TPR:Country {name:"TPR", home_node:"burma", development:3.0});
CREATE (SDY:Country {name:"SDY", home_node:"burma", development:6.0});
CREATE (YOR:Country {name:"YOR", home_node:"comorin_cape", development:33.728});
CREATE (DGL:Country {name:"DGL", home_node:"comorin_cape", development:5.0});
CREATE (MBL:Country {name:"MBL", home_node:"ganges_delta", development:8.0});
CREATE (IDR:Country {name:"IDR", home_node:"gujarat", development:3.0});
CREATE (JLV:Country {name:"JLV", home_node:"gujarat", development:5.0});
CREATE (PTL:Country {name:"PTL", home_node:"gujarat", development:8.0});
CREATE (JGD:Country {name:"JGD", home_node:"gujarat", development:13.0});
CREATE (PRB:Country {name:"PRB", home_node:"gujarat", development:5.0});
CREATE (PAN:Country {name:"PAN", home_node:"doab", development:5.0});
CREATE (KLP:Country {name:"KLP", home_node:"doab", development:16.064});
CREATE (PTT:Country {name:"PTT", home_node:"ganges_delta", development:15.864});
CREATE (RTT:Country {name:"RTT", home_node:"ganges_delta", development:12.0});
CREATE (KLH:Country {name:"KLH", home_node:"ganges_delta", development:3.0});
CREATE (KJH:Country {name:"KJH", home_node:"ganges_delta", development:4.0});
CREATE (PRD:Country {name:"PRD", home_node:"ganges_delta", development:3.0});
CREATE (JPR:Country {name:"JPR", home_node:"ganges_delta", development:3.0});
CREATE (KND:Country {name:"KND", home_node:"comorin_cape", development:12.0});
CREATE (TLG:Country {name:"TLG", home_node:"deccan", development:13.704});
CREATE (KLT:Country {name:"KLT", home_node:"comorin_cape", development:7.0});
CREATE (DNG:Country {name:"DNG", home_node:"doab", development:5.0});
CREATE (DTI:Country {name:"DTI", home_node:"doab", development:3.0});
CREATE (GRK:Country {name:"GRK", home_node:"lhasa", development:13.0});
CREATE (JML:Country {name:"JML", home_node:"doab", development:5.0});
CREATE (LWA:Country {name:"LWA", home_node:"lhasa", development:12.982});
CREATE (SRM:Country {name:"SRM", home_node:"doab", development:3.0});
CREATE (KTU:Country {name:"KTU", home_node:"lhasa", development:13.952});
CREATE (KMN:Country {name:"KMN", home_node:"doab", development:3.0});
CREATE (SRH:Country {name:"SRH", home_node:"lahore", development:86.745});
CREATE (HSA:Country {name:"HSA", home_node:"lubeck", development:17.0});
CREATE (ABE:Country {name:"ABE", home_node:"chesapeake_bay", development:6.0});
CREATE (APA:Country {name:"APA", home_node:"california", development:6.0});
CREATE (ASI:Country {name:"ASI", home_node:"james_bay", development:6.0});
CREATE (BLA:Country {name:"BLA", home_node:"james_bay", development:6.0});
CREATE (CAD:Country {name:"CAD", home_node:"mississippi_river", development:6.0});
CREATE (CHI:Country {name:"CHI", home_node:"mississippi_river", development:8.0});
CREATE (CHO:Country {name:"CHO", home_node:"mississippi_river", development:8.0});
CREATE (CHY:Country {name:"CHY", home_node:"james_bay", development:9.0});
CREATE (COM:Country {name:"COM", home_node:"mississippi_river", development:6.0});
CREATE (FOX:Country {name:"FOX", home_node:"ohio", development:9.0});
CREATE (LEN:Country {name:"LEN", home_node:"chesapeake_bay", development:8.0});
CREATE (MAH:Country {name:"MAH", home_node:"st_lawrence", development:10.0});
CREATE (MIK:Country {name:"MIK", home_node:"st_lawrence", development:8.0});
CREATE (MMI:Country {name:"MMI", home_node:"ohio", development:6.0});
CREATE (NAH:Country {name:"NAH", home_node:"rio_grande", development:7.0});
CREATE (OJI:Country {name:"OJI", home_node:"ohio", development:9.0});
CREATE (OSA:Country {name:"OSA", home_node:"ohio", development:6.0});
CREATE (OTT:Country {name:"OTT", home_node:"ohio", development:6.0});
CREATE (PAW:Country {name:"PAW", home_node:"mississippi_river", development:7.0});
CREATE (PEQ:Country {name:"PEQ", home_node:"chesapeake_bay", development:9.0});
CREATE (PIM:Country {name:"PIM", home_node:"california", development:6.0});
CREATE (POT:Country {name:"POT", home_node:"ohio", development:7.0});
CREATE (POW:Country {name:"POW", home_node:"chesapeake_bay", development:8.0});
CREATE (SHO:Country {name:"SHO", home_node:"california", development:6.0});
CREATE (SIO:Country {name:"SIO", home_node:"ohio", development:6.0});
CREATE (SUS:Country {name:"SUS", home_node:"ohio", development:6.0});
CREATE (WCR:Country {name:"WCR", home_node:"james_bay", development:6.0});
CREATE (AIR:Country {name:"AIR", home_node:"katsina", development:26.964});
CREATE (BON:Country {name:"BON", home_node:"timbuktu", development:12.0});
CREATE (DAH:Country {name:"DAH", home_node:"timbuktu", development:3.0});
CREATE (DGB:Country {name:"DGB", home_node:"timbuktu", development:6.0});
CREATE (JNN:Country {name:"JNN", home_node:"timbuktu", development:39.89});
CREATE (KAN:Country {name:"KAN", home_node:"katsina", development:24.54});
CREATE (KNG:Country {name:"KNG", home_node:"timbuktu", development:12.882});
CREATE (KTS:Country {name:"KTS", home_node:"katsina", development:31.0});
CREATE (NUP:Country {name:"NUP", home_node:"katsina", development:11.724});
CREATE (TMB:Country {name:"TMB", home_node:"timbuktu", development:38.44});
CREATE (YAO:Country {name:"YAO", home_node:"katsina", development:32.308});
CREATE (YAT:Country {name:"YAT", home_node:"timbuktu", development:16.706});
CREATE (ZZZ:Country {name:"ZZZ", home_node:"katsina", development:23.85});
CREATE (NDO:Country {name:"NDO", home_node:"ivory_coast", development:13.0});
CREATE (AVA:Country {name:"AVA", home_node:"burma", development:62.0});
CREATE (HSE:Country {name:"HSE", home_node:"burma", development:12.816});
CREATE (KED:Country {name:"KED", home_node:"malacca", development:15.0});
CREATE (LIG:Country {name:"LIG", home_node:"malacca", development:27.03});
CREATE (MPH:Country {name:"MPH", home_node:"gulf_of_siam", development:6.0});
CREATE (MYA:Country {name:"MYA", home_node:"burma", development:18.816});
CREATE (MMA:Country {name:"MMA", home_node:"chengdu", development:12.0});
CREATE (MKA:Country {name:"MKA", home_node:"burma", development:14.432});
CREATE (MPA:Country {name:"MPA", home_node:"burma", development:10.77});
CREATE (MNI:Country {name:"MNI", home_node:"burma", development:11.0});
CREATE (KAL:Country {name:"KAL", home_node:"burma", development:11.645});
CREATE (HSI:Country {name:"HSI", home_node:"burma", development:14.0});
CREATE (BPR:Country {name:"BPR", home_node:"burma", development:11.0});
CREATE (CHU:Country {name:"CHU", home_node:"girin", development:3.0});
CREATE (HOD:Country {name:"HOD", home_node:"girin", development:3.0});
CREATE (CHV:Country {name:"CHV", home_node:"girin", development:3.0});
CREATE (KMC:Country {name:"KMC", home_node:"girin", development:3.0});
CREATE (ARP:Country {name:"ARP", home_node:"james_bay", development:6.0});
CREATE (CLM:Country {name:"CLM", home_node:"mexico", development:31.77});
CREATE (CNK:Country {name:"CNK", home_node:"california", development:8.0});
CREATE (COC:Country {name:"COC", home_node:"mexico", development:23.278});
CREATE (HDA:Country {name:"HDA", home_node:"california", development:6.0});
CREATE (ITZ:Country {name:"ITZ", home_node:"mexico", development:15.0});
CREATE (KIC:Country {name:"KIC", home_node:"mexico", development:14.0});
CREATE (KIO:Country {name:"KIO", home_node:"james_bay", development:6.0});
CREATE (MIX:Country {name:"MIX", home_node:"mexico", development:11.95});
CREATE (SAL:Country {name:"SAL", home_node:"california", development:8.0});
CREATE (TAR:Country {name:"TAR", home_node:"mexico", development:30.0});
CREATE (TLA:Country {name:"TLA", home_node:"mexico", development:23.721});
CREATE (TLX:Country {name:"TLX", home_node:"mexico", development:27.0});
CREATE (TOT:Country {name:"TOT", home_node:"mexico", development:15.0});
CREATE (WIC:Country {name:"WIC", home_node:"rio_grande", development:6.0});
CREATE (XIU:Country {name:"XIU", home_node:"mexico", development:21.064});
CREATE (BLM:Country {name:"BLM", home_node:"the_moluccas", development:11.0});
CREATE (BTN:Country {name:"BTN", home_node:"the_moluccas", development:8.0});
CREATE (PGR:Country {name:"PGR", home_node:"malacca", development:19.836});
CREATE (PLB:Country {name:"PLB", home_node:"malacca", development:27.0});
CREATE (PSA:Country {name:"PSA", home_node:"malacca", development:37.0});
CREATE (SAK:Country {name:"SAK", home_node:"malacca", development:27.0});
CREATE (SUN:Country {name:"SUN", home_node:"the_moluccas", development:69.0});
CREATE (KUT:Country {name:"KUT", home_node:"malacca", development:29.0});
CREATE (BNJ:Country {name:"BNJ", home_node:"malacca", development:31.0});
CREATE (LUW:Country {name:"LUW", home_node:"the_moluccas", development:18.0});
CREATE (MGD:Country {name:"MGD", home_node:"philippines", development:20.864});
CREATE (TER:Country {name:"TER", home_node:"the_moluccas", development:13.0});
CREATE (TID:Country {name:"TID", home_node:"the_moluccas", development:13.0});
CREATE (MAS:Country {name:"MAS", home_node:"philippines", development:13.515});
CREATE (PGS:Country {name:"PGS", home_node:"philippines", development:6.0});
CREATE (TDO:Country {name:"TDO", home_node:"philippines", development:23.448});
CREATE (MNA:Country {name:"MNA", home_node:"philippines", development:12.0});
CREATE (CEB:Country {name:"CEB", home_node:"philippines", development:6.0});
CREATE (BTU:Country {name:"BTU", home_node:"philippines", development:6.0});
CREATE (CSU:Country {name:"CSU", home_node:"lima", development:32.549});
CREATE (CCQ:Country {name:"CCQ", home_node:"cuiaba", development:16.692});
CREATE (MPC:Country {name:"MPC", home_node:"patagonia", development:6.0});
CREATE (MCA:Country {name:"MCA", home_node:"panama", development:29.012});
CREATE (QTO:Country {name:"QTO", home_node:"lima", development:35.0});
CREATE (CJA:Country {name:"CJA", home_node:"lima", development:16.0});
CREATE (HJA:Country {name:"HJA", home_node:"lima", development:13.0});
CREATE (PTG:Country {name:"PTG", home_node:"brazil", development:9.0});
CREATE (TPQ:Country {name:"TPQ", home_node:"brazil", development:10.0});
CREATE (TPA:Country {name:"TPA", home_node:"amazonas_node", development:7.0});
CREATE (TUA:Country {name:"TUA", home_node:"brazil", development:9.0});
CREATE (GUA:Country {name:"GUA", home_node:"laplata", development:6.0});
CREATE (CUA:Country {name:"CUA", home_node:"laplata", development:10.0});
CREATE (WKA:Country {name:"WKA", home_node:"lima", development:11.0});
CREATE (CYA:Country {name:"CYA", home_node:"lima", development:6.0});
CREATE (CLA:Country {name:"CLA", home_node:"cuiaba", development:20.0});
CREATE (CRA:Country {name:"CRA", home_node:"cuiaba", development:29.0});
CREATE (PCJ:Country {name:"PCJ", home_node:"cuiaba", development:24.0});
CREATE (ARW:Country {name:"ARW", home_node:"amazonas_node", development:6.0});
CREATE (CAB:Country {name:"CAB", home_node:"carribean_trade", development:6.0});
CREATE (ICM:Country {name:"ICM", home_node:"lima", development:6.0});
CREATE (MAT:Country {name:"MAT", home_node:"mexico", development:21.0});
CREATE (COI:Country {name:"COI", home_node:"mexico", development:14.0});
CREATE (TEO:Country {name:"TEO", home_node:"mexico", development:12.0});
CREATE (XAL:Country {name:"XAL", home_node:"mexico", development:17.0});
CREATE (GAM:Country {name:"GAM", home_node:"mexico", development:6.0});
CREATE (HST:Country {name:"HST", home_node:"mexico", development:6.0});
CREATE (CCM:Country {name:"CCM", home_node:"mexico", development:6.0});
CREATE (OTO:Country {name:"OTO", home_node:"mexico", development:8.262});
CREATE (YOK:Country {name:"YOK", home_node:"mexico", development:10.04});
CREATE (LAC:Country {name:"LAC", home_node:"mexico", development:11.29});
CREATE (KAQ:Country {name:"KAQ", home_node:"mexico", development:9.52});
CREATE (CTM:Country {name:"CTM", home_node:"mexico", development:19.048});
CREATE (KER:Country {name:"KER", home_node:"rio_grande", development:8.0});
CREATE (ZNI:Country {name:"ZNI", home_node:"rio_grande", development:6.0});
CREATE (MSC:Country {name:"MSC", home_node:"rio_grande", development:6.0});
CREATE (LIP:Country {name:"LIP", home_node:"rio_grande", development:6.0});
CREATE (CHT:Country {name:"CHT", home_node:"panama", development:6.0});
CREATE (MIS:Country {name:"MIS", home_node:"panama", development:6.0});
CREATE (TAI:Country {name:"TAI", home_node:"panama", development:6.0});
CREATE (CNP:Country {name:"CNP", home_node:"mexico", development:16.0});
CREATE (TON:Country {name:"TON", home_node:"mexico", development:9.501});
CREATE (YAQ:Country {name:"YAQ", home_node:"california", development:6.0});
CREATE (YKT:Country {name:"YKT", home_node:"california", development:6.0});
CREATE (UBV:Country {name:"UBV", home_node:"wien", development:24.0});
CREATE (LBV:Country {name:"LBV", home_node:"wien", development:30.0});
CREATE (ING:Country {name:"ING", home_node:"wien", development:24.298});
CREATE (PSS:Country {name:"PSS", home_node:"wien", development:11.0});
CREATE (MBZ:Country {name:"MBZ", home_node:"wien", development:5.0});
CREATE (KNZ:Country {name:"KNZ", home_node:"wien", development:13.0});
CREATE (ROT:Country {name:"ROT", home_node:"rheinland", development:10.0});
CREATE (BYT:Country {name:"BYT", home_node:"rheinland", development:6.0});
CREATE (REG:Country {name:"REG", home_node:"wien", development:13.0});
CREATE (GNV:Country {name:"GNV", home_node:"champagne", development:13.0});
CREATE (TTL:Country {name:"TTL", home_node:"rheinland", development:13.0});
CREATE (OPL:Country {name:"OPL", home_node:"krakow", development:29.0});
CREATE (GLG:Country {name:"GLG", home_node:"krakow", development:19.0});
CREATE (BLG:Country {name:"BLG", home_node:"venice", development:12.0});
CREATE (SZO:Country {name:"SZO", home_node:"genua", development:11.0});
CREATE (WOL:Country {name:"WOL", home_node:"lubeck", development:27.94});
CREATE (STE:Country {name:"STE", home_node:"lubeck", development:21.0});
CREATE (GOS:Country {name:"GOS", home_node:"saxony", development:11.0});
CREATE (CLI:Country {name:"CLI", home_node:"venice", development:9.0});
CREATE (HRZ:Country {name:"HRZ", home_node:"ragusa", development:14.864});
CREATE (TNT:Country {name:"TNT", home_node:"wien", development:8.0});
CREATE (BRG:Country {name:"BRG", home_node:"rheinland", development:21.415});
CREATE (MLH:Country {name:"MLH", home_node:"rheinland", development:8.0});
CREATE (BAM:Country {name:"BAM", home_node:"rheinland", development:6.0});
CREATE (PGA:Country {name:"PGA", home_node:"genua", development:8.0});
CREATE (BNE:Country {name:"BNE", home_node:"the_moluccas", development:17.0});
CREATE (BEU:Country {name:"BEU", home_node:"malacca", development:19.0});
CREATE (SMB:Country {name:"SMB", home_node:"malacca", development:31.0});
CREATE (BRS:Country {name:"BRS", home_node:"malacca", development:17.0});
CREATE (DLI:Country {name:"DLI", home_node:"malacca", development:27.0});
CREATE (JMB:Country {name:"JMB", home_node:"malacca", development:12.0});
CREATE (PAH:Country {name:"PAH", home_node:"malacca", development:10.0});
CREATE (KEL:Country {name:"KEL", home_node:"malacca", development:10.975});
CREATE (IND:Country {name:"IND", home_node:"malacca", development:12.0});
CREATE (JAR:Country {name:"JAR", home_node:"gulf_of_siam", development:6.0});
CREATE (RHA:Country {name:"RHA", home_node:"gulf_of_siam", development:6.0});
CREATE (KOH:Country {name:"KOH", home_node:"gulf_of_siam", development:6.0});
CREATE (TIW:Country {name:"TIW", home_node:"australia", development:8.0});
CREATE (LAR:Country {name:"LAR", home_node:"australia", development:8.0});
CREATE (YOL:Country {name:"YOL", home_node:"australia", development:8.0});
CREATE (YNU:Country {name:"YNU", home_node:"australia", development:6.0});
CREATE (AWN:Country {name:"AWN", home_node:"australia", development:8.0});
CREATE (GMI:Country {name:"GMI", home_node:"australia", development:13.0});
CREATE (MIA:Country {name:"MIA", home_node:"australia", development:8.0});
CREATE (EOR:Country {name:"EOR", home_node:"australia", development:14.0});
CREATE (KAU:Country {name:"KAU", home_node:"australia", development:10.0});
CREATE (PLW:Country {name:"PLW", home_node:"australia", development:9.0});
CREATE (WRU:Country {name:"WRU", home_node:"australia", development:14.0});
CREATE (NOO:Country {name:"NOO", home_node:"australia", development:12.0});
CREATE (MLG:Country {name:"MLG", home_node:"australia", development:8.0});
CREATE (MAA:Country {name:"MAA", home_node:"polynesia_node", development:8.0});
CREATE (TAN:Country {name:"TAN", home_node:"polynesia_node", development:10.0});
CREATE (TAK:Country {name:"TAK", home_node:"polynesia_node", development:7.0});
CREATE (TNK:Country {name:"TNK", home_node:"polynesia_node", development:10.0});
CREATE (TEA:Country {name:"TEA", home_node:"polynesia_node", development:10.0});
CREATE (TTT:Country {name:"TTT", home_node:"polynesia_node", development:10.0});
CREATE (WAI:Country {name:"WAI", home_node:"polynesia_node", development:4.0});
CREATE (HAW:Country {name:"HAW", home_node:"polynesia_node", development:7.0});
CREATE (MAU:Country {name:"MAU", home_node:"polynesia_node", development:5.0});
CREATE (OAH:Country {name:"OAH", home_node:"polynesia_node", development:5.0});
CREATE (KAA:Country {name:"KAA", home_node:"polynesia_node", development:5.0});
CREATE (TOG:Country {name:"TOG", home_node:"polynesia_node", development:8.0});
CREATE (SAM:Country {name:"SAM", home_node:"polynesia_node", development:8.0});
CREATE (VIL:Country {name:"VIL", home_node:"polynesia_node", development:7.0});
CREATE (VNL:Country {name:"VNL", home_node:"polynesia_node", development:5.0});
CREATE (LAI:Country {name:"LAI", home_node:"polynesia_node", development:3.0});
CREATE (ALT:Country {name:"ALT", home_node:"chesapeake_bay", development:6.0});
CREATE (ICH:Country {name:"ICH", home_node:"chesapeake_bay", development:6.0});
CREATE (COF:Country {name:"COF", home_node:"chesapeake_bay", development:8.0});
CREATE (JOA:Country {name:"JOA", home_node:"chesapeake_bay", development:8.0});
CREATE (ETO:Country {name:"ETO", home_node:"chesapeake_bay", development:6.0});
CREATE (SAT:Country {name:"SAT", home_node:"chesapeake_bay", development:6.0});
CREATE (CIA:Country {name:"CIA", home_node:"chesapeake_bay", development:8.0});
CREATE (COO:Country {name:"COO", home_node:"chesapeake_bay", development:8.0});
CREATE (ABI:Country {name:"ABI", home_node:"mississippi_river", development:6.0});
CREATE (COW:Country {name:"COW", home_node:"mississippi_river", development:9.0});
CREATE (NTZ:Country {name:"NTZ", home_node:"mississippi_river", development:6.0});
CREATE (CAQ:Country {name:"CAQ", home_node:"mississippi_river", development:6.0});
CREATE (PCH:Country {name:"PCH", home_node:"mississippi_river", development:6.0});
CREATE (QUI:Country {name:"QUI", home_node:"mississippi_river", development:8.0});
CREATE (CCA:Country {name:"CCA", home_node:"ohio", development:6.0});
CREATE (ATA:Country {name:"ATA", home_node:"mississippi_river", development:8.0});
CREATE (KSI:Country {name:"KSI", home_node:"chesapeake_bay", development:8.0});
CREATE (OEO:Country {name:"OEO", home_node:"ohio", development:9.0});
CREATE (ANL:Country {name:"ANL", home_node:"mississippi_river", development:8.0});
CREATE (NTC:Country {name:"NTC", home_node:"mississippi_river", development:8.0});
CREATE (HNI:Country {name:"HNI", home_node:"rio_grande", development:6.0});
CREATE (MOH:Country {name:"MOH", home_node:"ohio", development:9.0});
CREATE (ONE:Country {name:"ONE", home_node:"ohio", development:8.0});
CREATE (ONO:Country {name:"ONO", home_node:"ohio", development:11.0});
CREATE (CAY:Country {name:"CAY", home_node:"ohio", development:8.0});
CREATE (SEN:Country {name:"SEN", home_node:"ohio", development:7.0});
CREATE (TAH:Country {name:"TAH", home_node:"ohio", development:8.0});
CREATE (ATT:Country {name:"ATT", home_node:"ohio", development:11.0});
CREATE (AGG:Country {name:"AGG", home_node:"ohio", development:9.0});
CREATE (ATW:Country {name:"ATW", home_node:"ohio", development:9.0});
CREATE (ARN:Country {name:"ARN", home_node:"ohio", development:8.0});
CREATE (TIO:Country {name:"TIO", home_node:"ohio", development:8.0});
CREATE (OSH:Country {name:"OSH", home_node:"st_lawrence", development:8.0});
CREATE (STA:Country {name:"STA", home_node:"st_lawrence", development:13.0});
CREATE (ERI:Country {name:"ERI", home_node:"ohio", development:8.0});
CREATE (WEN:Country {name:"WEN", home_node:"ohio", development:8.0});
CREATE (TSC:Country {name:"TSC", home_node:"chesapeake_bay", development:8.0});
CREATE (OHK:Country {name:"OHK", home_node:"rio_grande", development:8.0});
CREATE (ISL:Country {name:"ISL", home_node:"rio_grande", development:8.0});
CREATE (ACO:Country {name:"ACO", home_node:"rio_grande", development:8.0});
CREATE (CAO:Country {name:"CAO", home_node:"ohio", development:10.0});
CREATE (PEO:Country {name:"PEO", home_node:"ohio", development:6.0});
CREATE (KSK:Country {name:"KSK", home_node:"ohio", development:8.0});
CREATE (PEN:Country {name:"PEN", home_node:"st_lawrence", development:8.0});
CREATE (MLS:Country {name:"MLS", home_node:"st_lawrence", development:6.0});
CREATE (NEH:Country {name:"NEH", home_node:"james_bay", development:6.0});
CREATE (NAK:Country {name:"NAK", home_node:"james_bay", development:6.0});
CREATE (HWK:Country {name:"HWK", home_node:"ohio", development:8.0});
CREATE (CLG:Country {name:"CLG", home_node:"ohio", development:6.0});
CREATE (KSP:Country {name:"KSP", home_node:"ohio", development:6.0});
CREATE (MSG:Country {name:"MSG", home_node:"ohio", development:7.0});
CREATE (WCY:Country {name:"WCY", home_node:"mississippi_river", development:9.0});
CREATE (LAK:Country {name:"LAK", home_node:"mississippi_river", development:6.0});
CREATE (INN:Country {name:"INN", home_node:"st_lawrence", development:6.0});
CREATE (WAM:Country {name:"WAM", home_node:"chesapeake_bay", development:8.0});
CREATE (AGQ:Country {name:"AGQ", home_node:"st_lawrence", development:9.0});
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (african_great_lakes)-[r:source{flow:0.10555744096776606}]->(zanzibar);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (kongo:Trade_node {name:"kongo"}) CREATE (african_great_lakes)-[r:source{flow:0.10555744096776606}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (kongo)-[r:source{flow:0.17879993016372192}]->(ivory_coast);
MATCH (kongo:Trade_node {name:"kongo"}), (zambezi:Trade_node {name:"zambezi"}) CREATE (kongo)-[r:source{flow:0.17879993016372192}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (zambezi)-[r:source{flow:0.307502662743773}]->(zanzibar);
MATCH (patagonia:Trade_node {name:"patagonia"}), (laplata:Trade_node {name:"laplata"}) CREATE (patagonia)-[r:source{flow:0.007392807845986197}]->(laplata);
MATCH (patagonia:Trade_node {name:"patagonia"}), (cuiaba:Trade_node {name:"cuiaba"}) CREATE (patagonia)-[r:source{flow:0.007392807845986197}]->(cuiaba);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (brazil:Trade_node {name:"brazil"}) CREATE (amazonas_node)-[r:source{flow:0.0}]->(brazil);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (amazonas_node)-[r:source{flow:0.0}]->(carribean_trade);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (cuiaba:Trade_node {name:"cuiaba"}) CREATE (amazonas_node)-[r:source{flow:0.0}]->(cuiaba);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (mississippi_river:Trade_node {name:"mississippi_river"}) CREATE (rio_grande)-[r:source{flow:0.04454282553539435}]->(mississippi_river);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (mexico:Trade_node {name:"mexico"}) CREATE (rio_grande)-[r:source{flow:0.04454282553539435}]->(mexico);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (california:Trade_node {name:"california"}) CREATE (rio_grande)-[r:source{flow:0.04454282553539435}]->(california);
MATCH (james_bay:Trade_node {name:"james_bay"}), (st_lawrence:Trade_node {name:"st_lawrence"}) CREATE (james_bay)-[r:source{flow:0.026588428018706926}]->(st_lawrence);
MATCH (james_bay:Trade_node {name:"james_bay"}), (california:Trade_node {name:"california"}) CREATE (james_bay)-[r:source{flow:0.026588428018706926}]->(california);
MATCH (california:Trade_node {name:"california"}), (mexico:Trade_node {name:"mexico"}) CREATE (california)-[r:source{flow:0.001581763450967824}]->(mexico);
MATCH (california:Trade_node {name:"california"}), (mississippi_river:Trade_node {name:"mississippi_river"}) CREATE (california)-[r:source{flow:0.001581763450967824}]->(mississippi_river);
MATCH (california:Trade_node {name:"california"}), (girin:Trade_node {name:"girin"}) CREATE (california)-[r:source{flow:0.001581763450967824}]->(girin);
MATCH (california:Trade_node {name:"california"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (california)-[r:source{flow:0.001581763450967824}]->(polynesia_node);
MATCH (girin:Trade_node {name:"girin"}), (siberia:Trade_node {name:"siberia"}) CREATE (girin)-[r:source{flow:0.16692589116803577}]->(siberia);
MATCH (girin:Trade_node {name:"girin"}), (beijing:Trade_node {name:"beijing"}) CREATE (girin)-[r:source{flow:0.16692589116803577}]->(beijing);
MATCH (girin:Trade_node {name:"girin"}), (nippon:Trade_node {name:"nippon"}) CREATE (girin)-[r:source{flow:0.16692589116803577}]->(nippon);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (mississippi_river)-[r:source{flow:0.015651518750721977}]->(carribean_trade);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (ohio:Trade_node {name:"ohio"}) CREATE (mississippi_river)-[r:source{flow:0.015651518750721977}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (chesapeake_bay:Trade_node {name:"chesapeake_bay"}) CREATE (ohio)-[r:source{flow:0.1328692269390522}]->(chesapeake_bay);
MATCH (ohio:Trade_node {name:"ohio"}), (st_lawrence:Trade_node {name:"st_lawrence"}) CREATE (ohio)-[r:source{flow:0.1328692269390522}]->(st_lawrence);
MATCH (mexico:Trade_node {name:"mexico"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (mexico)-[r:source{flow:0.0160749117887121}]->(carribean_trade);
MATCH (mexico:Trade_node {name:"mexico"}), (panama:Trade_node {name:"panama"}) CREATE (mexico)-[r:source{flow:0.0160749117887121}]->(panama);
MATCH (mexico:Trade_node {name:"mexico"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (mexico)-[r:source{flow:0.0160749117887121}]->(polynesia_node);
MATCH (lhasa:Trade_node {name:"lhasa"}), (lahore:Trade_node {name:"lahore"}) CREATE (lhasa)-[r:source{flow:0.14969036580237302}]->(lahore);
MATCH (lhasa:Trade_node {name:"lhasa"}), (chengdu:Trade_node {name:"chengdu"}) CREATE (lhasa)-[r:source{flow:0.14969036580237302}]->(chengdu);
MATCH (lhasa:Trade_node {name:"lhasa"}), (ganges_delta:Trade_node {name:"ganges_delta"}) CREATE (lhasa)-[r:source{flow:0.14969036580237302}]->(ganges_delta);
MATCH (chengdu:Trade_node {name:"chengdu"}), (canton:Trade_node {name:"canton"}) CREATE (chengdu)-[r:source{flow:0.3963760546389703}]->(canton);
MATCH (chengdu:Trade_node {name:"chengdu"}), (xian:Trade_node {name:"xian"}) CREATE (chengdu)-[r:source{flow:0.3963760546389703}]->(xian);
MATCH (chengdu:Trade_node {name:"chengdu"}), (burma:Trade_node {name:"burma"}) CREATE (chengdu)-[r:source{flow:0.3963760546389703}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (ganges_delta:Trade_node {name:"ganges_delta"}) CREATE (burma)-[r:source{flow:0.5562350843007936}]->(ganges_delta);
MATCH (burma:Trade_node {name:"burma"}), (gulf_of_siam:Trade_node {name:"gulf_of_siam"}) CREATE (burma)-[r:source{flow:0.5562350843007936}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (malacca:Trade_node {name:"malacca"}) CREATE (gulf_of_siam)-[r:source{flow:0.8755622411843981}]->(malacca);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (canton:Trade_node {name:"canton"}) CREATE (gulf_of_siam)-[r:source{flow:0.8755622411843981}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (malacca:Trade_node {name:"malacca"}) CREATE (canton)-[r:source{flow:0.9005781969395715}]->(malacca);
MATCH (canton:Trade_node {name:"canton"}), (hangzhou:Trade_node {name:"hangzhou"}) CREATE (canton)-[r:source{flow:0.9005781969395715}]->(hangzhou);
MATCH (canton:Trade_node {name:"canton"}), (philippines:Trade_node {name:"philippines"}) CREATE (canton)-[r:source{flow:0.9005781969395715}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (the_moluccas:Trade_node {name:"the_moluccas"}) CREATE (philippines)-[r:source{flow:0.12959515808789615}]->(the_moluccas);
MATCH (philippines:Trade_node {name:"philippines"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (philippines)-[r:source{flow:0.12959515808789615}]->(polynesia_node);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (laplata:Trade_node {name:"laplata"}) CREATE (cuiaba)-[r:source{flow:0.045145358638245155}]->(laplata);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (lima:Trade_node {name:"lima"}) CREATE (cuiaba)-[r:source{flow:0.045145358638245155}]->(lima);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (brazil:Trade_node {name:"brazil"}) CREATE (cuiaba)-[r:source{flow:0.045145358638245155}]->(brazil);
MATCH (lima:Trade_node {name:"lima"}), (panama:Trade_node {name:"panama"}) CREATE (lima)-[r:source{flow:0.011041280911949036}]->(panama);
MATCH (lima:Trade_node {name:"lima"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (lima)-[r:source{flow:0.011041280911949036}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (nippon:Trade_node {name:"nippon"}) CREATE (polynesia_node)-[r:source{flow:0.0}]->(nippon);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (australia:Trade_node {name:"australia"}) CREATE (polynesia_node)-[r:source{flow:0.0}]->(australia);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (panama:Trade_node {name:"panama"}) CREATE (polynesia_node)-[r:source{flow:0.0}]->(panama);
MATCH (australia:Trade_node {name:"australia"}), (the_moluccas:Trade_node {name:"the_moluccas"}) CREATE (australia)-[r:source{flow:0.0}]->(the_moluccas);
MATCH (nippon:Trade_node {name:"nippon"}), (hangzhou:Trade_node {name:"hangzhou"}) CREATE (nippon)-[r:source{flow:0.28490605511176}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (xian:Trade_node {name:"xian"}) CREATE (hangzhou)-[r:source{flow:1.6342930774423168}]->(xian);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (beijing:Trade_node {name:"beijing"}) CREATE (hangzhou)-[r:source{flow:1.6342930774423168}]->(beijing);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (malacca:Trade_node {name:"malacca"}) CREATE (hangzhou)-[r:source{flow:1.6342930774423168}]->(malacca);
MATCH (xian:Trade_node {name:"xian"}), (beijing:Trade_node {name:"beijing"}) CREATE (xian)-[r:source{flow:1.588300647171338}]->(beijing);
MATCH (xian:Trade_node {name:"xian"}), (yumen:Trade_node {name:"yumen"}) CREATE (xian)-[r:source{flow:1.588300647171338}]->(yumen);
MATCH (beijing:Trade_node {name:"beijing"}), (yumen:Trade_node {name:"yumen"}) CREATE (beijing)-[r:source{flow:0.4738637675103589}]->(yumen);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (malacca:Trade_node {name:"malacca"}) CREATE (the_moluccas)-[r:source{flow:0.5010166024739675}]->(malacca);
MATCH (siberia:Trade_node {name:"siberia"}), (kazan:Trade_node {name:"kazan"}) CREATE (siberia)-[r:source{flow:0.11343309834021266}]->(kazan);
MATCH (siberia:Trade_node {name:"siberia"}), (samarkand:Trade_node {name:"samarkand"}) CREATE (siberia)-[r:source{flow:0.11343309834021266}]->(samarkand);
MATCH (yumen:Trade_node {name:"yumen"}), (samarkand:Trade_node {name:"samarkand"}) CREATE (yumen)-[r:source{flow:0.4024434498151434}]->(samarkand);
MATCH (malacca:Trade_node {name:"malacca"}), (ganges_delta:Trade_node {name:"ganges_delta"}) CREATE (malacca)-[r:source{flow:0.30917583763532774}]->(ganges_delta);
MATCH (malacca:Trade_node {name:"malacca"}), (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}) CREATE (malacca)-[r:source{flow:0.30917583763532774}]->(cape_of_good_hope);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (comorin_cape:Trade_node {name:"comorin_cape"}) CREATE (ganges_delta)-[r:source{flow:0.8233731941764081}]->(comorin_cape);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (doab:Trade_node {name:"doab"}) CREATE (ganges_delta)-[r:source{flow:0.8233731941764081}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (deccan:Trade_node {name:"deccan"}) CREATE (doab)-[r:source{flow:0.782451935922892}]->(deccan);
MATCH (doab:Trade_node {name:"doab"}), (lahore:Trade_node {name:"lahore"}) CREATE (doab)-[r:source{flow:0.782451935922892}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (samarkand:Trade_node {name:"samarkand"}) CREATE (lahore)-[r:source{flow:0.5019216206136049}]->(samarkand);
MATCH (lahore:Trade_node {name:"lahore"}), (persia:Trade_node {name:"persia"}) CREATE (lahore)-[r:source{flow:0.5019216206136049}]->(persia);
MATCH (lahore:Trade_node {name:"lahore"}), (gujarat:Trade_node {name:"gujarat"}) CREATE (lahore)-[r:source{flow:0.5019216206136049}]->(gujarat);
MATCH (deccan:Trade_node {name:"deccan"}), (gujarat:Trade_node {name:"gujarat"}) CREATE (deccan)-[r:source{flow:1.0855125224111752}]->(gujarat);
MATCH (deccan:Trade_node {name:"deccan"}), (comorin_cape:Trade_node {name:"comorin_cape"}) CREATE (deccan)-[r:source{flow:1.0855125224111752}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (gulf_of_aden:Trade_node {name:"gulf_of_aden"}) CREATE (comorin_cape)-[r:source{flow:0.2660693805907677}]->(gulf_of_aden);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (gujarat:Trade_node {name:"gujarat"}) CREATE (comorin_cape)-[r:source{flow:0.2660693805907677}]->(gujarat);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}) CREATE (comorin_cape)-[r:source{flow:0.2660693805907677}]->(cape_of_good_hope);
MATCH (gujarat:Trade_node {name:"gujarat"}), (gulf_of_aden:Trade_node {name:"gulf_of_aden"}) CREATE (gujarat)-[r:source{flow:0.3373270457603697}]->(gulf_of_aden);
MATCH (gujarat:Trade_node {name:"gujarat"}), (hormuz:Trade_node {name:"hormuz"}) CREATE (gujarat)-[r:source{flow:0.3373270457603697}]->(hormuz);
MATCH (gujarat:Trade_node {name:"gujarat"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (gujarat)-[r:source{flow:0.3373270457603697}]->(zanzibar);
MATCH (katsina:Trade_node {name:"katsina"}), (timbuktu:Trade_node {name:"timbuktu"}) CREATE (katsina)-[r:source{flow:0.22676268403456576}]->(timbuktu);
MATCH (katsina:Trade_node {name:"katsina"}), (tunis:Trade_node {name:"tunis"}) CREATE (katsina)-[r:source{flow:0.22676268403456576}]->(tunis);
MATCH (katsina:Trade_node {name:"katsina"}), (ethiopia:Trade_node {name:"ethiopia"}) CREATE (katsina)-[r:source{flow:0.22676268403456576}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (alexandria:Trade_node {name:"alexandria"}) CREATE (ethiopia)-[r:source{flow:0.17783694855342272}]->(alexandria);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (gulf_of_aden:Trade_node {name:"gulf_of_aden"}) CREATE (ethiopia)-[r:source{flow:0.17783694855342272}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (gulf_of_aden)-[r:source{flow:0.2410968176923596}]->(zanzibar);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (alexandria:Trade_node {name:"alexandria"}) CREATE (gulf_of_aden)-[r:source{flow:0.2410968176923596}]->(alexandria);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (hormuz:Trade_node {name:"hormuz"}) CREATE (gulf_of_aden)-[r:source{flow:0.2410968176923596}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (basra:Trade_node {name:"basra"}) CREATE (hormuz)-[r:source{flow:0.49269617441281366}]->(basra);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}) CREATE (zanzibar)-[r:source{flow:0.008889001023344367}]->(cape_of_good_hope);
MATCH (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (cape_of_good_hope)-[r:source{flow:0.0}]->(ivory_coast);
MATCH (basra:Trade_node {name:"basra"}), (aleppo:Trade_node {name:"aleppo"}) CREATE (basra)-[r:source{flow:0.5651007221668058}]->(aleppo);
MATCH (basra:Trade_node {name:"basra"}), (persia:Trade_node {name:"persia"}) CREATE (basra)-[r:source{flow:0.5651007221668058}]->(persia);
MATCH (samarkand:Trade_node {name:"samarkand"}), (persia:Trade_node {name:"persia"}) CREATE (samarkand)-[r:source{flow:0.6977561380621885}]->(persia);
MATCH (samarkand:Trade_node {name:"samarkand"}), (astrakhan:Trade_node {name:"astrakhan"}) CREATE (samarkand)-[r:source{flow:0.6977561380621885}]->(astrakhan);
MATCH (persia:Trade_node {name:"persia"}), (aleppo:Trade_node {name:"aleppo"}) CREATE (persia)-[r:source{flow:0.5028983431813152}]->(aleppo);
MATCH (persia:Trade_node {name:"persia"}), (astrakhan:Trade_node {name:"astrakhan"}) CREATE (persia)-[r:source{flow:0.5028983431813152}]->(astrakhan);
MATCH (aleppo:Trade_node {name:"aleppo"}), (constantinople:Trade_node {name:"constantinople"}) CREATE (aleppo)-[r:source{flow:0.7048154488389774}]->(constantinople);
MATCH (aleppo:Trade_node {name:"aleppo"}), (alexandria:Trade_node {name:"alexandria"}) CREATE (aleppo)-[r:source{flow:0.7048154488389774}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (constantinople:Trade_node {name:"constantinople"}) CREATE (alexandria)-[r:source{flow:0.6024678573255658}]->(constantinople);
MATCH (alexandria:Trade_node {name:"alexandria"}), (venice:Trade_node {name:"venice"}) CREATE (alexandria)-[r:source{flow:0.6024678573255658}]->(venice);
MATCH (alexandria:Trade_node {name:"alexandria"}), (genua:Trade_node {name:"genua"}) CREATE (alexandria)-[r:source{flow:0.6024678573255658}]->(genua);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (kazan:Trade_node {name:"kazan"}) CREATE (astrakhan)-[r:source{flow:0.46964871166522637}]->(kazan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (crimea:Trade_node {name:"crimea"}) CREATE (astrakhan)-[r:source{flow:0.46964871166522637}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (kiev:Trade_node {name:"kiev"}) CREATE (crimea)-[r:source{flow:0.41345689493063603}]->(kiev);
MATCH (crimea:Trade_node {name:"crimea"}), (constantinople:Trade_node {name:"constantinople"}) CREATE (crimea)-[r:source{flow:0.41345689493063603}]->(constantinople);
MATCH (crimea:Trade_node {name:"crimea"}), (pest:Trade_node {name:"pest"}) CREATE (crimea)-[r:source{flow:0.41345689493063603}]->(pest);
MATCH (constantinople:Trade_node {name:"constantinople"}), (ragusa:Trade_node {name:"ragusa"}) CREATE (constantinople)-[r:source{flow:0.528579696172331}]->(ragusa);
MATCH (kiev:Trade_node {name:"kiev"}), (novgorod:Trade_node {name:"novgorod"}) CREATE (kiev)-[r:source{flow:0.7826431432731424}]->(novgorod);
MATCH (kiev:Trade_node {name:"kiev"}), (krakow:Trade_node {name:"krakow"}) CREATE (kiev)-[r:source{flow:0.7826431432731424}]->(krakow);
MATCH (kazan:Trade_node {name:"kazan"}), (novgorod:Trade_node {name:"novgorod"}) CREATE (kazan)-[r:source{flow:0.7593332937273595}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (baltic_sea:Trade_node {name:"baltic_sea"}) CREATE (novgorod)-[r:source{flow:0.37907557325639935}]->(baltic_sea);
MATCH (novgorod:Trade_node {name:"novgorod"}), (white_sea:Trade_node {name:"white_sea"}) CREATE (novgorod)-[r:source{flow:0.37907557325639935}]->(white_sea);
MATCH (laplata:Trade_node {name:"laplata"}), (brazil:Trade_node {name:"brazil"}) CREATE (laplata)-[r:source{flow:0.0}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (brazil)-[r:source{flow:0.0}]->(ivory_coast);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (safi:Trade_node {name:"safi"}) CREATE (timbuktu)-[r:source{flow:0.1478892291496734}]->(safi);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (timbuktu)-[r:source{flow:0.1478892291496734}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (ivory_coast)-[r:source{flow:0.051641739141432705}]->(carribean_trade);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (bordeaux:Trade_node {name:"bordeaux"}) CREATE (ivory_coast)-[r:source{flow:0.051641739141432705}]->(bordeaux);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (ivory_coast)-[r:source{flow:0.051641739141432705}]->(english_channel);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (ivory_coast)-[r:source{flow:0.051641739141432705}]->(sevilla);
MATCH (tunis:Trade_node {name:"tunis"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (tunis)-[r:source{flow:0.24629644188009572}]->(sevilla);
MATCH (tunis:Trade_node {name:"tunis"}), (valencia:Trade_node {name:"valencia"}) CREATE (tunis)-[r:source{flow:0.24629644188009572}]->(valencia);
MATCH (tunis:Trade_node {name:"tunis"}), (genua:Trade_node {name:"genua"}) CREATE (tunis)-[r:source{flow:0.24629644188009572}]->(genua);
MATCH (ragusa:Trade_node {name:"ragusa"}), (pest:Trade_node {name:"pest"}) CREATE (ragusa)-[r:source{flow:0.5960854537106484}]->(pest);
MATCH (ragusa:Trade_node {name:"ragusa"}), (venice:Trade_node {name:"venice"}) CREATE (ragusa)-[r:source{flow:0.5960854537106484}]->(venice);
MATCH (ragusa:Trade_node {name:"ragusa"}), (genua:Trade_node {name:"genua"}) CREATE (ragusa)-[r:source{flow:0.5960854537106484}]->(genua);
MATCH (safi:Trade_node {name:"safi"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (safi)-[r:source{flow:0.46814721740588067}]->(sevilla);
MATCH (pest:Trade_node {name:"pest"}), (wien:Trade_node {name:"wien"}) CREATE (pest)-[r:source{flow:0.5833546145311421}]->(wien);
MATCH (pest:Trade_node {name:"pest"}), (krakow:Trade_node {name:"krakow"}) CREATE (pest)-[r:source{flow:0.5833546145311421}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (wien:Trade_node {name:"wien"}) CREATE (krakow)-[r:source{flow:0.5576236333168924}]->(wien);
MATCH (krakow:Trade_node {name:"krakow"}), (saxony:Trade_node {name:"saxony"}) CREATE (krakow)-[r:source{flow:0.5576236333168924}]->(saxony);
MATCH (krakow:Trade_node {name:"krakow"}), (baltic_sea:Trade_node {name:"baltic_sea"}) CREATE (krakow)-[r:source{flow:0.5576236333168924}]->(baltic_sea);
MATCH (wien:Trade_node {name:"wien"}), (venice:Trade_node {name:"venice"}) CREATE (wien)-[r:source{flow:0.6549741673734922}]->(venice);
MATCH (wien:Trade_node {name:"wien"}), (rheinland:Trade_node {name:"rheinland"}) CREATE (wien)-[r:source{flow:0.6549741673734922}]->(rheinland);
MATCH (wien:Trade_node {name:"wien"}), (saxony:Trade_node {name:"saxony"}) CREATE (wien)-[r:source{flow:0.6549741673734922}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (rheinland:Trade_node {name:"rheinland"}) CREATE (saxony)-[r:source{flow:0.8078853225319909}]->(rheinland);
MATCH (saxony:Trade_node {name:"saxony"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (saxony)-[r:source{flow:0.8078853225319909}]->(lubeck);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (baltic_sea)-[r:source{flow:0.6469946465504153}]->(lubeck);
MATCH (rheinland:Trade_node {name:"rheinland"}), (champagne:Trade_node {name:"champagne"}) CREATE (rheinland)-[r:source{flow:0.7898321789439758}]->(champagne);
MATCH (rheinland:Trade_node {name:"rheinland"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (rheinland)-[r:source{flow:0.7898321789439758}]->(lubeck);
MATCH (panama:Trade_node {name:"panama"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (panama)-[r:source{flow:0.0}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (chesapeake_bay:Trade_node {name:"chesapeake_bay"}) CREATE (carribean_trade)-[r:source{flow:0.0}]->(chesapeake_bay);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (bordeaux:Trade_node {name:"bordeaux"}) CREATE (carribean_trade)-[r:source{flow:0.0}]->(bordeaux);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (carribean_trade)-[r:source{flow:0.0}]->(sevilla);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (st_lawrence:Trade_node {name:"st_lawrence"}) CREATE (chesapeake_bay)-[r:source{flow:0.06679963177465621}]->(st_lawrence);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (chesapeake_bay)-[r:source{flow:0.06679963177465621}]->(english_channel);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (north_sea:Trade_node {name:"north_sea"}) CREATE (st_lawrence)-[r:source{flow:0.0}]->(north_sea);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (bordeaux:Trade_node {name:"bordeaux"}) CREATE (st_lawrence)-[r:source{flow:0.0}]->(bordeaux);
MATCH (white_sea:Trade_node {name:"white_sea"}), (north_sea:Trade_node {name:"north_sea"}) CREATE (white_sea)-[r:source{flow:0.6434145096374161}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (north_sea)-[r:source{flow:0.4835686753199132}]->(english_channel);
MATCH (north_sea:Trade_node {name:"north_sea"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (north_sea)-[r:source{flow:0.4835686753199132}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (lubeck)-[r:source{flow:0.5510622303390725}]->(english_channel);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (champagne:Trade_node {name:"champagne"}) CREATE (bordeaux)-[r:source{flow:0.9417395421506174}]->(champagne);
MATCH (sevilla:Trade_node {name:"sevilla"}), (valencia:Trade_node {name:"valencia"}) CREATE (sevilla)-[r:source{flow:0.35981972306477283}]->(valencia);
MATCH (champagne:Trade_node {name:"champagne"}), (genua:Trade_node {name:"genua"}) CREATE (champagne)-[r:source{flow:0.7855661980573375}]->(genua);
MATCH (champagne:Trade_node {name:"champagne"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (champagne)-[r:source{flow:0.7855661980573375}]->(english_channel);
MATCH (valencia:Trade_node {name:"valencia"}), (genua:Trade_node {name:"genua"}) CREATE (valencia)-[r:source{flow:0.8367472481161577}]->(genua);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KON:Country {name:"KON"}) CREATE (KON)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.017,calculated_trading_power: 4.017}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (LUB:Country {name:"LUB"}) CREATE (LUB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.693,calculated_trading_power: 11.693}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (LND:Country {name:"LND"}) CREATE (LND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.183,calculated_trading_power: 2.183}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KIK:Country {name:"KIK"}) CREATE (KIK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.391,calculated_trading_power: 5.391}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KZB:Country {name:"KZB"}) CREATE (KZB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.081,calculated_trading_power: 9.081}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KLD:Country {name:"KLD"}) CREATE (KLD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.055,calculated_trading_power: 5.055}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KUB:Country {name:"KUB"}) CREATE (KUB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.398,calculated_trading_power: 7.398}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (RWA:Country {name:"RWA"}) CREATE (RWA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.51,calculated_trading_power: 17.51}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BUU:Country {name:"BUU"}) CREATE (BUU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.269,calculated_trading_power: 12.269}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BUG:Country {name:"BUG"}) CREATE (BUG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.164,calculated_trading_power: 26.164}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (NKO:Country {name:"NKO"}) CREATE (NKO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.713,calculated_trading_power: 11.713}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KRW:Country {name:"KRW"}) CREATE (KRW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.155,calculated_trading_power: 15.155}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BNY:Country {name:"BNY"}) CREATE (BNY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.194,calculated_trading_power: 15.194}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BSG:Country {name:"BSG"}) CREATE (BSG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.269,calculated_trading_power: 12.269}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (UBH:Country {name:"UBH"}) CREATE (UBH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.669,calculated_trading_power: 18.669}]->(african_great_lakes);
MATCH (kongo:Trade_node {name:"kongo"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.743,calculated_trading_power: 5.743}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KON:Country {name:"KON"}) CREATE (KON)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 50.521,calculated_trading_power: 50.521}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (ZIM:Country {name:"ZIM"}) CREATE (ZIM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.349,calculated_trading_power: 23.349}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.16,calculated_trading_power: 4.16}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (TYO:Country {name:"TYO"}) CREATE (TYO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.513,calculated_trading_power: 10.513}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KSJ:Country {name:"KSJ"}) CREATE (KSJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.318,calculated_trading_power: 16.318}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (LUB:Country {name:"LUB"}) CREATE (LUB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.404,calculated_trading_power: 28.404}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (LND:Country {name:"LND"}) CREATE (LND)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.735,calculated_trading_power: 24.735}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (CKW:Country {name:"CKW"}) CREATE (CKW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.269,calculated_trading_power: 12.269}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KIK:Country {name:"KIK"}) CREATE (KIK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.146,calculated_trading_power: 14.146}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KZB:Country {name:"KZB"}) CREATE (KZB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.115,calculated_trading_power: 21.115}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (YAK:Country {name:"YAK"}) CREATE (YAK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.583,calculated_trading_power: 14.583}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KLD:Country {name:"KLD"}) CREATE (KLD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.509,calculated_trading_power: 13.509}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KUB:Country {name:"KUB"}) CREATE (KUB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.589,calculated_trading_power: 24.589}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (MRA:Country {name:"MRA"}) CREATE (MRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.645,calculated_trading_power: 13.645}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (LDU:Country {name:"LDU"}) CREATE (LDU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.377,calculated_trading_power: 6.377}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (TBK:Country {name:"TBK"}) CREATE (TBK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.716,calculated_trading_power: 5.716}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (RZW:Country {name:"RZW"}) CREATE (RZW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.958,calculated_trading_power: 5.958}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (NDO:Country {name:"NDO"}) CREATE (NDO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.718,calculated_trading_power: 7.718}]->(kongo);
MATCH (zambezi:Trade_node {name:"zambezi"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.604,calculated_trading_power: 23.604}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (ZIM:Country {name:"ZIM"}) CREATE (ZIM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 56.972,calculated_trading_power: 56.972}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (SFA:Country {name:"SFA"}) CREATE (SFA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.522,calculated_trading_power: 12.522}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (MBA:Country {name:"MBA"}) CREATE (MBA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.908,calculated_trading_power: 3.908}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.386,calculated_trading_power: 2.386}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (MRA:Country {name:"MRA"}) CREATE (MRA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.559,calculated_trading_power: 35.559}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (LDU:Country {name:"LDU"}) CREATE (LDU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.786,calculated_trading_power: 15.786}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (TBK:Country {name:"TBK"}) CREATE (TBK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.756,calculated_trading_power: 14.756}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (MKU:Country {name:"MKU"}) CREATE (MKU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.948,calculated_trading_power: 10.948}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (RZW:Country {name:"RZW"}) CREATE (RZW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.844,calculated_trading_power: 14.844}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.361,calculated_trading_power: 14.361}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.41,calculated_trading_power: 2.41}]->(zambezi);
MATCH (patagonia:Trade_node {name:"patagonia"}), (MPC:Country {name:"MPC"}) CREATE (MPC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.801,calculated_trading_power: 8.801}]->(patagonia);
MATCH (patagonia:Trade_node {name:"patagonia"}), (CUA:Country {name:"CUA"}) CREATE (CUA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.964,calculated_trading_power: 4.964}]->(patagonia);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (TPA:Country {name:"TPA"}) CREATE (TPA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.093,calculated_trading_power: 9.093}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (ARW:Country {name:"ARW"}) CREATE (ARW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.801,calculated_trading_power: 8.801}]->(amazonas_node);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (AZT:Country {name:"AZT"}) CREATE (AZT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.706,calculated_trading_power: 3.706}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (NAH:Country {name:"NAH"}) CREATE (NAH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.758,calculated_trading_power: 8.758}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (CNK:Country {name:"CNK"}) CREATE (CNK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.628,calculated_trading_power: 3.628}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (TLA:Country {name:"TLA"}) CREATE (TLA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.01,calculated_trading_power: 3.01}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (TOT:Country {name:"TOT"}) CREATE (TOT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.811,calculated_trading_power: 3.811}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (WIC:Country {name:"WIC"}) CREATE (WIC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (XAL:Country {name:"XAL"}) CREATE (XAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.286,calculated_trading_power: 2.286}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (HST:Country {name:"HST"}) CREATE (HST)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 0.879,calculated_trading_power: 0.879}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (KER:Country {name:"KER"}) CREATE (KER)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.018,calculated_trading_power: 9.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (ZNI:Country {name:"ZNI"}) CREATE (ZNI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.532,calculated_trading_power: 8.532}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (MSC:Country {name:"MSC"}) CREATE (MSC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.532,calculated_trading_power: 8.532}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (LIP:Country {name:"LIP"}) CREATE (LIP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (HNI:Country {name:"HNI"}) CREATE (HNI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (OHK:Country {name:"OHK"}) CREATE (OHK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.054,calculated_trading_power: 15.054}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (ISL:Country {name:"ISL"}) CREATE (ISL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.107,calculated_trading_power: 9.107}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (ACO:Country {name:"ACO"}) CREATE (ACO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.018,calculated_trading_power: 9.018}]->(rio_grande);
MATCH (james_bay:Trade_node {name:"james_bay"}), (ASI:Country {name:"ASI"}) CREATE (ASI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (BLA:Country {name:"BLA"}) CREATE (BLA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.619,calculated_trading_power: 8.619}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (CHY:Country {name:"CHY"}) CREATE (CHY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.26,calculated_trading_power: 9.26}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (WCR:Country {name:"WCR"}) CREATE (WCR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (ARP:Country {name:"ARP"}) CREATE (ARP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (CNK:Country {name:"CNK"}) CREATE (CNK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.628,calculated_trading_power: 3.628}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (KIO:Country {name:"KIO"}) CREATE (KIO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (STA:Country {name:"STA"}) CREATE (STA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.694,calculated_trading_power: 3.694}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (NEH:Country {name:"NEH"}) CREATE (NEH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.499,calculated_trading_power: 8.499}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (NAK:Country {name:"NAK"}) CREATE (NAK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(james_bay);
MATCH (california:Trade_node {name:"california"}), (APA:Country {name:"APA"}) CREATE (APA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.532,calculated_trading_power: 8.532}]->(california);
MATCH (california:Trade_node {name:"california"}), (PIM:Country {name:"PIM"}) CREATE (PIM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(california);
MATCH (california:Trade_node {name:"california"}), (SHO:Country {name:"SHO"}) CREATE (SHO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(california);
MATCH (california:Trade_node {name:"california"}), (CNK:Country {name:"CNK"}) CREATE (CNK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.204,calculated_trading_power: 25.204}]->(california);
MATCH (california:Trade_node {name:"california"}), (HDA:Country {name:"HDA"}) CREATE (HDA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.801,calculated_trading_power: 8.801}]->(california);
MATCH (california:Trade_node {name:"california"}), (SAL:Country {name:"SAL"}) CREATE (SAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.565,calculated_trading_power: 9.565}]->(california);
MATCH (california:Trade_node {name:"california"}), (XAL:Country {name:"XAL"}) CREATE (XAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 0.879,calculated_trading_power: 0.879}]->(california);
MATCH (california:Trade_node {name:"california"}), (YAQ:Country {name:"YAQ"}) CREATE (YAQ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.801,calculated_trading_power: 8.801}]->(california);
MATCH (california:Trade_node {name:"california"}), (YKT:Country {name:"YKT"}) CREATE (YKT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.499,calculated_trading_power: 8.499}]->(california);
MATCH (girin:Trade_node {name:"girin"}), (SHY:Country {name:"SHY"}) CREATE (SHY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.384,calculated_trading_power: 6.384}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (AMA:Country {name:"AMA"}) CREATE (AMA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.994,calculated_trading_power: 1.994}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (HSK:Country {name:"HSK"}) CREATE (HSK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.793,calculated_trading_power: 5.793}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (OUC:Country {name:"OUC"}) CREATE (OUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.835,calculated_trading_power: 7.835}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (UES:Country {name:"UES"}) CREATE (UES)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.729,calculated_trading_power: 4.729}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (YMN:Country {name:"YMN"}) CREATE (YMN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.994,calculated_trading_power: 1.994}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (ANU:Country {name:"ANU"}) CREATE (ANU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 0.886,calculated_trading_power: 0.886}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (KHA:Country {name:"KHA"}) CREATE (KHA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.566,calculated_trading_power: 4.566}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (KOR:Country {name:"KOR"}) CREATE (KOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.045,calculated_trading_power: 15.045}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.587,calculated_trading_power: 20.587}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (SOO:Country {name:"SOO"}) CREATE (SOO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.994,calculated_trading_power: 1.994}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (NVK:Country {name:"NVK"}) CREATE (NVK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 33.787,calculated_trading_power: 33.787}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (SOL:Country {name:"SOL"}) CREATE (SOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.864,calculated_trading_power: 17.864}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (EJZ:Country {name:"EJZ"}) CREATE (EJZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.811,calculated_trading_power: 11.811}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (NHX:Country {name:"NHX"}) CREATE (NHX)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.464,calculated_trading_power: 18.464}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (MYR:Country {name:"MYR"}) CREATE (MYR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.297,calculated_trading_power: 16.297}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (MHX:Country {name:"MHX"}) CREATE (MHX)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.159,calculated_trading_power: 29.159}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (MJZ:Country {name:"MJZ"}) CREATE (MJZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 37.326,calculated_trading_power: 37.326}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (KRC:Country {name:"KRC"}) CREATE (KRC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.057,calculated_trading_power: 18.057}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (CHU:Country {name:"CHU"}) CREATE (CHU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.095,calculated_trading_power: 12.095}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (HOD:Country {name:"HOD"}) CREATE (HOD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.927,calculated_trading_power: 7.927}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (CHV:Country {name:"CHV"}) CREATE (CHV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.285,calculated_trading_power: 22.285}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (KMC:Country {name:"KMC"}) CREATE (KMC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.285,calculated_trading_power: 22.285}]->(girin);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CAD:Country {name:"CAD"}) CREATE (CAD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.532,calculated_trading_power: 8.532}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CHI:Country {name:"CHI"}) CREATE (CHI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.05,calculated_trading_power: 9.05}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CHO:Country {name:"CHO"}) CREATE (CHO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.018,calculated_trading_power: 9.018}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (COM:Country {name:"COM"}) CREATE (COM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (FOX:Country {name:"FOX"}) CREATE (FOX)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.071,calculated_trading_power: 3.071}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (PAW:Country {name:"PAW"}) CREATE (PAW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.74,calculated_trading_power: 8.74}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (ABI:Country {name:"ABI"}) CREATE (ABI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.654,calculated_trading_power: 8.654}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (COW:Country {name:"COW"}) CREATE (COW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.26,calculated_trading_power: 9.26}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (NTZ:Country {name:"NTZ"}) CREATE (NTZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.532,calculated_trading_power: 8.532}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CAQ:Country {name:"CAQ"}) CREATE (CAQ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.414,calculated_trading_power: 8.414}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (PCH:Country {name:"PCH"}) CREATE (PCH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.532,calculated_trading_power: 8.532}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (QUI:Country {name:"QUI"}) CREATE (QUI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.018,calculated_trading_power: 9.018}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (ATA:Country {name:"ATA"}) CREATE (ATA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.893,calculated_trading_power: 8.893}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (ANL:Country {name:"ANL"}) CREATE (ANL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.018,calculated_trading_power: 9.018}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (NTC:Country {name:"NTC"}) CREATE (NTC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.018,calculated_trading_power: 9.018}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (WCY:Country {name:"WCY"}) CREATE (WCY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.26,calculated_trading_power: 9.26}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (LAK:Country {name:"LAK"}) CREATE (LAK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(mississippi_river);
MATCH (ohio:Trade_node {name:"ohio"}), (CHE:Country {name:"CHE"}) CREATE (CHE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (FOX:Country {name:"FOX"}) CREATE (FOX)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.215,calculated_trading_power: 25.215}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (LEN:Country {name:"LEN"}) CREATE (LEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.049,calculated_trading_power: 3.049}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (MMI:Country {name:"MMI"}) CREATE (MMI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.676,calculated_trading_power: 10.676}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (OJI:Country {name:"OJI"}) CREATE (OJI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.599,calculated_trading_power: 12.599}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (OSA:Country {name:"OSA"}) CREATE (OSA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.554,calculated_trading_power: 10.554}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (OTT:Country {name:"OTT"}) CREATE (OTT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.655,calculated_trading_power: 10.655}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (POT:Country {name:"POT"}) CREATE (POT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.253,calculated_trading_power: 11.253}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (SIO:Country {name:"SIO"}) CREATE (SIO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.655,calculated_trading_power: 10.655}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (SUS:Country {name:"SUS"}) CREATE (SUS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.554,calculated_trading_power: 10.554}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ALT:Country {name:"ALT"}) CREATE (ALT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (COF:Country {name:"COF"}) CREATE (COF)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (JOA:Country {name:"JOA"}) CREATE (JOA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.787,calculated_trading_power: 4.787}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ETO:Country {name:"ETO"}) CREATE (ETO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (SAT:Country {name:"SAT"}) CREATE (SAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CIA:Country {name:"CIA"}) CREATE (CIA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (COO:Country {name:"COO"}) CREATE (COO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CCA:Country {name:"CCA"}) CREATE (CCA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.676,calculated_trading_power: 10.676}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (OEO:Country {name:"OEO"}) CREATE (OEO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.475,calculated_trading_power: 12.475}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (MOH:Country {name:"MOH"}) CREATE (MOH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.475,calculated_trading_power: 12.475}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ONE:Country {name:"ONE"}) CREATE (ONE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ONO:Country {name:"ONO"}) CREATE (ONO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.674,calculated_trading_power: 13.674}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CAY:Country {name:"CAY"}) CREATE (CAY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (SEN:Country {name:"SEN"}) CREATE (SEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.275,calculated_trading_power: 11.275}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (TAH:Country {name:"TAH"}) CREATE (TAH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ATT:Country {name:"ATT"}) CREATE (ATT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.674,calculated_trading_power: 13.674}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (AGG:Country {name:"AGG"}) CREATE (AGG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.293,calculated_trading_power: 12.293}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ATW:Country {name:"ATW"}) CREATE (ATW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.302,calculated_trading_power: 12.302}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ARN:Country {name:"ARN"}) CREATE (ARN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.851,calculated_trading_power: 11.851}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (TIO:Country {name:"TIO"}) CREATE (TIO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (STA:Country {name:"STA"}) CREATE (STA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.694,calculated_trading_power: 3.694}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ERI:Country {name:"ERI"}) CREATE (ERI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (WEN:Country {name:"WEN"}) CREATE (WEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CAO:Country {name:"CAO"}) CREATE (CAO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.847,calculated_trading_power: 12.847}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (PEO:Country {name:"PEO"}) CREATE (PEO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.533,calculated_trading_power: 10.533}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (KSK:Country {name:"KSK"}) CREATE (KSK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.11,calculated_trading_power: 12.11}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (HWK:Country {name:"HWK"}) CREATE (HWK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CLG:Country {name:"CLG"}) CREATE (CLG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.676,calculated_trading_power: 10.676}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (KSP:Country {name:"KSP"}) CREATE (KSP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.676,calculated_trading_power: 10.676}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (MSG:Country {name:"MSG"}) CREATE (MSG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.275,calculated_trading_power: 11.275}]->(ohio);
MATCH (mexico:Trade_node {name:"mexico"}), (AZT:Country {name:"AZT"}) CREATE (AZT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.609,calculated_trading_power: 25.609}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (ZAP:Country {name:"ZAP"}) CREATE (ZAP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.019,calculated_trading_power: 13.019}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (CLM:Country {name:"CLM"}) CREATE (CLM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.722,calculated_trading_power: 15.722}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (COC:Country {name:"COC"}) CREATE (COC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.697,calculated_trading_power: 13.697}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (ITZ:Country {name:"ITZ"}) CREATE (ITZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.716,calculated_trading_power: 10.716}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (KIC:Country {name:"KIC"}) CREATE (KIC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.898,calculated_trading_power: 11.898}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (MIX:Country {name:"MIX"}) CREATE (MIX)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.336,calculated_trading_power: 10.336}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (TAR:Country {name:"TAR"}) CREATE (TAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.356,calculated_trading_power: 14.356}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (TLA:Country {name:"TLA"}) CREATE (TLA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.203,calculated_trading_power: 22.203}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (TLX:Country {name:"TLX"}) CREATE (TLX)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.628,calculated_trading_power: 13.628}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (TOT:Country {name:"TOT"}) CREATE (TOT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.134,calculated_trading_power: 26.134}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (XIU:Country {name:"XIU"}) CREATE (XIU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.567,calculated_trading_power: 13.567}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (MCA:Country {name:"MCA"}) CREATE (MCA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.827,calculated_trading_power: 2.827}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (MAT:Country {name:"MAT"}) CREATE (MAT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.172,calculated_trading_power: 12.172}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (COI:Country {name:"COI"}) CREATE (COI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.757,calculated_trading_power: 10.757}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (TEO:Country {name:"TEO"}) CREATE (TEO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.13,calculated_trading_power: 10.13}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (XAL:Country {name:"XAL"}) CREATE (XAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.511,calculated_trading_power: 18.511}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (GAM:Country {name:"GAM"}) CREATE (GAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.532,calculated_trading_power: 8.532}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (HST:Country {name:"HST"}) CREATE (HST)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.956,calculated_trading_power: 7.956}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (CCM:Country {name:"CCM"}) CREATE (CCM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (OTO:Country {name:"OTO"}) CREATE (OTO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.169,calculated_trading_power: 9.169}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (YOK:Country {name:"YOK"}) CREATE (YOK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.849,calculated_trading_power: 10.849}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (LAC:Country {name:"LAC"}) CREATE (LAC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.263,calculated_trading_power: 10.263}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (KAQ:Country {name:"KAQ"}) CREATE (KAQ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.33,calculated_trading_power: 10.33}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (CTM:Country {name:"CTM"}) CREATE (CTM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.943,calculated_trading_power: 12.943}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (CHT:Country {name:"CHT"}) CREATE (CHT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 0.87,calculated_trading_power: 0.87}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (CNP:Country {name:"CNP"}) CREATE (CNP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.651,calculated_trading_power: 11.651}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (TON:Country {name:"TON"}) CREATE (TON)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.9,calculated_trading_power: 9.9}]->(mexico);
MATCH (lhasa:Trade_node {name:"lhasa"}), (AFG:Country {name:"AFG"}) CREATE (AFG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.8,calculated_trading_power: 5.8}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.638,calculated_trading_power: 2.638}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.085,calculated_trading_power: 12.085}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (SYG:Country {name:"SYG"}) CREATE (SYG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.252,calculated_trading_power: 9.252}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (UTS:Country {name:"UTS"}) CREATE (UTS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 40.032,calculated_trading_power: 40.032}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (KAM:Country {name:"KAM"}) CREATE (KAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.353,calculated_trading_power: 16.353}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (GUG:Country {name:"GUG"}) CREATE (GUG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.263,calculated_trading_power: 13.263}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (PHA:Country {name:"PHA"}) CREATE (PHA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.687,calculated_trading_power: 14.687}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (BNG:Country {name:"BNG"}) CREATE (BNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 27.728,calculated_trading_power: 27.728}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (JNP:Country {name:"JNP"}) CREATE (JNP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.798,calculated_trading_power: 4.798}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MUL:Country {name:"MUL"}) CREATE (MUL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.039,calculated_trading_power: 3.039}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (ORI:Country {name:"ORI"}) CREATE (ORI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.968,calculated_trading_power: 3.968}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (KMT:Country {name:"KMT"}) CREATE (KMT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.084,calculated_trading_power: 7.084}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (TRT:Country {name:"TRT"}) CREATE (TRT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.337,calculated_trading_power: 2.337}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MBL:Country {name:"MBL"}) CREATE (MBL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (GRK:Country {name:"GRK"}) CREATE (GRK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.611,calculated_trading_power: 14.611}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (LWA:Country {name:"LWA"}) CREATE (LWA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.661,calculated_trading_power: 14.661}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (KTU:Country {name:"KTU"}) CREATE (KTU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.533,calculated_trading_power: 17.533}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (SRH:Country {name:"SRH"}) CREATE (SRH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.889,calculated_trading_power: 8.889}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (AVA:Country {name:"AVA"}) CREATE (AVA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.084,calculated_trading_power: 20.084}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (HSE:Country {name:"HSE"}) CREATE (HSE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MYA:Country {name:"MYA"}) CREATE (MYA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.424,calculated_trading_power: 8.424}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MMA:Country {name:"MMA"}) CREATE (MMA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.066,calculated_trading_power: 6.066}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MKA:Country {name:"MKA"}) CREATE (MKA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.098,calculated_trading_power: 7.098}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MNI:Country {name:"MNI"}) CREATE (MNI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.716,calculated_trading_power: 5.716}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (KAL:Country {name:"KAL"}) CREATE (KAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.108,calculated_trading_power: 6.108}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (HSI:Country {name:"HSI"}) CREATE (HSI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.739,calculated_trading_power: 6.739}]->(lhasa);
MATCH (chengdu:Trade_node {name:"chengdu"}), (DAI:Country {name:"DAI"}) CREATE (DAI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.932,calculated_trading_power: 9.932}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (LXA:Country {name:"LXA"}) CREATE (LXA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 36.224,calculated_trading_power: 36.224}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 106.551,calculated_trading_power: 106.551}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (PEG:Country {name:"PEG"}) CREATE (PEG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.647,calculated_trading_power: 7.647}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (KAM:Country {name:"KAM"}) CREATE (KAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 31.419,calculated_trading_power: 31.419}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (AVA:Country {name:"AVA"}) CREATE (AVA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 25.325,calculated_trading_power: 25.325}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (HSE:Country {name:"HSE"}) CREATE (HSE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (MPH:Country {name:"MPH"}) CREATE (MPH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.028,calculated_trading_power: 4.028}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (MYA:Country {name:"MYA"}) CREATE (MYA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.424,calculated_trading_power: 8.424}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (MMA:Country {name:"MMA"}) CREATE (MMA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.42,calculated_trading_power: 15.42}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (MKA:Country {name:"MKA"}) CREATE (MKA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.098,calculated_trading_power: 7.098}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (MNI:Country {name:"MNI"}) CREATE (MNI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.716,calculated_trading_power: 5.716}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (KAL:Country {name:"KAL"}) CREATE (KAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.108,calculated_trading_power: 6.108}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (HSI:Country {name:"HSI"}) CREATE (HSI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.739,calculated_trading_power: 6.739}]->(chengdu);
MATCH (burma:Trade_node {name:"burma"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.66,calculated_trading_power: 4.66}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (AYU:Country {name:"AYU"}) CREATE (AYU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.8,calculated_trading_power: 11.8}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (CHA:Country {name:"CHA"}) CREATE (CHA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.808,calculated_trading_power: 3.808}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KHM:Country {name:"KHM"}) CREATE (KHM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.86,calculated_trading_power: 11.86}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (LNA:Country {name:"LNA"}) CREATE (LNA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.903,calculated_trading_power: 4.903}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (LXA:Country {name:"LXA"}) CREATE (LXA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.033,calculated_trading_power: 8.033}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (PEG:Country {name:"PEG"}) CREATE (PEG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 49.357,calculated_trading_power: 49.357}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (SUK:Country {name:"SUK"}) CREATE (SUK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (TAU:Country {name:"TAU"}) CREATE (TAU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.225,calculated_trading_power: 10.225}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (BNG:Country {name:"BNG"}) CREATE (BNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 30.787,calculated_trading_power: 30.787}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (ASS:Country {name:"ASS"}) CREATE (ASS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.054,calculated_trading_power: 16.054}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (JNP:Country {name:"JNP"}) CREATE (JNP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.774,calculated_trading_power: 4.774}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (ORI:Country {name:"ORI"}) CREATE (ORI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.99,calculated_trading_power: 5.99}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (GRJ:Country {name:"GRJ"}) CREATE (GRJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KAC:Country {name:"KAC"}) CREATE (KAC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.26,calculated_trading_power: 9.26}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KMT:Country {name:"KMT"}) CREATE (KMT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.409,calculated_trading_power: 3.409}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MLB:Country {name:"MLB"}) CREATE (MLB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.29,calculated_trading_power: 8.29}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (NGP:Country {name:"NGP"}) CREATE (NGP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (TRT:Country {name:"TRT"}) CREATE (TRT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.359,calculated_trading_power: 4.359}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (TPR:Country {name:"TPR"}) CREATE (TPR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.789,calculated_trading_power: 7.789}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (SDY:Country {name:"SDY"}) CREATE (SDY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MBL:Country {name:"MBL"}) CREATE (MBL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (PTT:Country {name:"PTT"}) CREATE (PTT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.048,calculated_trading_power: 2.048}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KJH:Country {name:"KJH"}) CREATE (KJH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (PRD:Country {name:"PRD"}) CREATE (PRD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KTU:Country {name:"KTU"}) CREATE (KTU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.994,calculated_trading_power: 1.994}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (AVA:Country {name:"AVA"}) CREATE (AVA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 39.809,calculated_trading_power: 39.809}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (HSE:Country {name:"HSE"}) CREATE (HSE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.226,calculated_trading_power: 12.226}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MPH:Country {name:"MPH"}) CREATE (MPH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.014,calculated_trading_power: 2.014}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MYA:Country {name:"MYA"}) CREATE (MYA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.97,calculated_trading_power: 13.97}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MKA:Country {name:"MKA"}) CREATE (MKA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.782,calculated_trading_power: 12.782}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MPA:Country {name:"MPA"}) CREATE (MPA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.716,calculated_trading_power: 9.716}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MNI:Country {name:"MNI"}) CREATE (MNI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.654,calculated_trading_power: 11.654}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KAL:Country {name:"KAL"}) CREATE (KAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.98,calculated_trading_power: 11.98}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (HSI:Country {name:"HSI"}) CREATE (HSI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.545,calculated_trading_power: 12.545}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (BPR:Country {name:"BPR"}) CREATE (BPR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.784,calculated_trading_power: 9.784}]->(burma);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.543,calculated_trading_power: 4.543}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (AYU:Country {name:"AYU"}) CREATE (AYU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 66.019,calculated_trading_power: 66.019}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.852,calculated_trading_power: 8.852}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (CHA:Country {name:"CHA"}) CREATE (CHA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.989,calculated_trading_power: 29.989}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (DAI:Country {name:"DAI"}) CREATE (DAI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.645,calculated_trading_power: 14.645}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (KHM:Country {name:"KHM"}) CREATE (KHM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 66.35,calculated_trading_power: 66.35}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (LNA:Country {name:"LNA"}) CREATE (LNA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.621,calculated_trading_power: 23.621}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (LXA:Country {name:"LXA"}) CREATE (LXA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 44.639,calculated_trading_power: 44.639}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (MLC:Country {name:"MLC"}) CREATE (MLC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.362,calculated_trading_power: 9.362}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 29.712,calculated_trading_power: 29.712}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.608,calculated_trading_power: 4.608}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (SUK:Country {name:"SUK"}) CREATE (SUK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.822,calculated_trading_power: 17.822}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (SUL:Country {name:"SUL"}) CREATE (SUL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.974,calculated_trading_power: 1.974}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (KED:Country {name:"KED"}) CREATE (KED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (LIG:Country {name:"LIG"}) CREATE (LIG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (MPH:Country {name:"MPH"}) CREATE (MPH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.187,calculated_trading_power: 10.187}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (PGR:Country {name:"PGR"}) CREATE (PGR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.823,calculated_trading_power: 5.823}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (PSA:Country {name:"PSA"}) CREATE (PSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.123,calculated_trading_power: 7.123}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.0,calculated_trading_power: 5.0}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (MGD:Country {name:"MGD"}) CREATE (MGD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (MAS:Country {name:"MAS"}) CREATE (MAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (TDO:Country {name:"TDO"}) CREATE (TDO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (MNA:Country {name:"MNA"}) CREATE (MNA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (CEB:Country {name:"CEB"}) CREATE (CEB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (SMB:Country {name:"SMB"}) CREATE (SMB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (BRS:Country {name:"BRS"}) CREATE (BRS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (DLI:Country {name:"DLI"}) CREATE (DLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (JMB:Country {name:"JMB"}) CREATE (JMB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (PAH:Country {name:"PAH"}) CREATE (PAH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.002,calculated_trading_power: 2.002}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (KEL:Country {name:"KEL"}) CREATE (KEL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (IND:Country {name:"IND"}) CREATE (IND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (JAR:Country {name:"JAR"}) CREATE (JAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (RHA:Country {name:"RHA"}) CREATE (RHA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (KOH:Country {name:"KOH"}) CREATE (KOH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(gulf_of_siam);
MATCH (canton:Trade_node {name:"canton"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.521,calculated_trading_power: 2.521}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.83,calculated_trading_power: 6.83}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (DAI:Country {name:"DAI"}) CREATE (DAI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 66.712,calculated_trading_power: 66.712}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (MLC:Country {name:"MLC"}) CREATE (MLC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.362,calculated_trading_power: 9.362}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 188.011,calculated_trading_power: 188.011}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.608,calculated_trading_power: 4.608}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (SUL:Country {name:"SUL"}) CREATE (SUL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.034,calculated_trading_power: 4.034}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (LIG:Country {name:"LIG"}) CREATE (LIG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.501,calculated_trading_power: 3.501}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (PSA:Country {name:"PSA"}) CREATE (PSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.101,calculated_trading_power: 5.101}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.978,calculated_trading_power: 2.978}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (MGD:Country {name:"MGD"}) CREATE (MGD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (MAS:Country {name:"MAS"}) CREATE (MAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.293,calculated_trading_power: 4.293}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (PGS:Country {name:"PGS"}) CREATE (PGS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (TDO:Country {name:"TDO"}) CREATE (TDO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (MNA:Country {name:"MNA"}) CREATE (MNA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.646,calculated_trading_power: 5.646}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (CEB:Country {name:"CEB"}) CREATE (CEB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.295,calculated_trading_power: 5.295}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (BTU:Country {name:"BTU"}) CREATE (BTU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (SMB:Country {name:"SMB"}) CREATE (SMB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (DLI:Country {name:"DLI"}) CREATE (DLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (JMB:Country {name:"JMB"}) CREATE (JMB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (PAH:Country {name:"PAH"}) CREATE (PAH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.002,calculated_trading_power: 2.002}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (KEL:Country {name:"KEL"}) CREATE (KEL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(canton);
MATCH (philippines:Trade_node {name:"philippines"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.288,calculated_trading_power: 2.288}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (MAJ:Country {name:"MAJ"}) CREATE (MAJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.524,calculated_trading_power: 8.524}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (MKS:Country {name:"MKS"}) CREATE (MKS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.243,calculated_trading_power: 6.243}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (SUL:Country {name:"SUL"}) CREATE (SUL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.197,calculated_trading_power: 30.197}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BTN:Country {name:"BTN"}) CREATE (BTN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (SUN:Country {name:"SUN"}) CREATE (SUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.25,calculated_trading_power: 8.25}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (LUW:Country {name:"LUW"}) CREATE (LUW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.042,calculated_trading_power: 2.042}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (MGD:Country {name:"MGD"}) CREATE (MGD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.934,calculated_trading_power: 27.934}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (TER:Country {name:"TER"}) CREATE (TER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (TID:Country {name:"TID"}) CREATE (TID)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (MAS:Country {name:"MAS"}) CREATE (MAS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.508,calculated_trading_power: 24.508}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (PGS:Country {name:"PGS"}) CREATE (PGS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.604,calculated_trading_power: 26.604}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (TDO:Country {name:"TDO"}) CREATE (TDO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.866,calculated_trading_power: 28.866}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (MNA:Country {name:"MNA"}) CREATE (MNA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 39.872,calculated_trading_power: 39.872}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (CEB:Country {name:"CEB"}) CREATE (CEB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.351,calculated_trading_power: 35.351}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BTU:Country {name:"BTU"}) CREATE (BTU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.604,calculated_trading_power: 26.604}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BNE:Country {name:"BNE"}) CREATE (BNE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(philippines);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (CHM:Country {name:"CHM"}) CREATE (CHM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.873,calculated_trading_power: 2.873}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (CSU:Country {name:"CSU"}) CREATE (CSU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.824,calculated_trading_power: 2.824}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (CCQ:Country {name:"CCQ"}) CREATE (CCQ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.192,calculated_trading_power: 17.192}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (QTO:Country {name:"QTO"}) CREATE (QTO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.963,calculated_trading_power: 5.963}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (CUA:Country {name:"CUA"}) CREATE (CUA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.964,calculated_trading_power: 4.964}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (CLA:Country {name:"CLA"}) CREATE (CLA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.014,calculated_trading_power: 24.014}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (CRA:Country {name:"CRA"}) CREATE (CRA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.085,calculated_trading_power: 14.085}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (PCJ:Country {name:"PCJ"}) CREATE (PCJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.874,calculated_trading_power: 12.874}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (ICM:Country {name:"ICM"}) CREATE (ICM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.28,calculated_trading_power: 3.28}]->(cuiaba);
MATCH (lima:Trade_node {name:"lima"}), (CHM:Country {name:"CHM"}) CREATE (CHM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.443,calculated_trading_power: 21.443}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (CSU:Country {name:"CSU"}) CREATE (CSU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.258,calculated_trading_power: 21.258}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (MCA:Country {name:"MCA"}) CREATE (MCA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.827,calculated_trading_power: 2.827}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (QTO:Country {name:"QTO"}) CREATE (QTO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.878,calculated_trading_power: 36.878}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (CJA:Country {name:"CJA"}) CREATE (CJA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.959,calculated_trading_power: 10.959}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (HJA:Country {name:"HJA"}) CREATE (HJA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.231,calculated_trading_power: 10.231}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (WKA:Country {name:"WKA"}) CREATE (WKA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.746,calculated_trading_power: 9.746}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (CYA:Country {name:"CYA"}) CREATE (CYA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.642,calculated_trading_power: 8.642}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (ICM:Country {name:"ICM"}) CREATE (ICM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.472,calculated_trading_power: 23.472}]->(lima);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (MAA:Country {name:"MAA"}) CREATE (MAA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.598,calculated_trading_power: 25.598}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TAN:Country {name:"TAN"}) CREATE (TAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.184,calculated_trading_power: 26.184}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TAK:Country {name:"TAK"}) CREATE (TAK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.305,calculated_trading_power: 25.305}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TNK:Country {name:"TNK"}) CREATE (TNK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.184,calculated_trading_power: 26.184}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TEA:Country {name:"TEA"}) CREATE (TEA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.184,calculated_trading_power: 26.184}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TTT:Country {name:"TTT"}) CREATE (TTT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.133,calculated_trading_power: 26.133}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (WAI:Country {name:"WAI"}) CREATE (WAI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.359,calculated_trading_power: 22.359}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (HAW:Country {name:"HAW"}) CREATE (HAW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.291,calculated_trading_power: 29.291}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (MAU:Country {name:"MAU"}) CREATE (MAU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.706,calculated_trading_power: 28.706}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (OAH:Country {name:"OAH"}) CREATE (OAH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.021,calculated_trading_power: 36.021}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (KAA:Country {name:"KAA"}) CREATE (KAA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.706,calculated_trading_power: 28.706}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TOG:Country {name:"TOG"}) CREATE (TOG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.825,calculated_trading_power: 36.825}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (SAM:Country {name:"SAM"}) CREATE (SAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.525,calculated_trading_power: 29.525}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (VIL:Country {name:"VIL"}) CREATE (VIL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.914,calculated_trading_power: 22.914}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (VNL:Country {name:"VNL"}) CREATE (VNL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.337,calculated_trading_power: 22.337}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (LAI:Country {name:"LAI"}) CREATE (LAI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.066,calculated_trading_power: 22.066}]->(polynesia_node);
MATCH (australia:Trade_node {name:"australia"}), (TIW:Country {name:"TIW"}) CREATE (TIW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.403,calculated_trading_power: 9.403}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (LAR:Country {name:"LAR"}) CREATE (LAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.108,calculated_trading_power: 19.108}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (YOL:Country {name:"YOL"}) CREATE (YOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.273,calculated_trading_power: 9.273}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (YNU:Country {name:"YNU"}) CREATE (YNU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.818,calculated_trading_power: 8.818}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (AWN:Country {name:"AWN"}) CREATE (AWN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.403,calculated_trading_power: 9.403}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (GMI:Country {name:"GMI"}) CREATE (GMI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.211,calculated_trading_power: 10.211}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (MIA:Country {name:"MIA"}) CREATE (MIA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.385,calculated_trading_power: 9.385}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (EOR:Country {name:"EOR"}) CREATE (EOR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.474,calculated_trading_power: 18.474}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (KAU:Country {name:"KAU"}) CREATE (KAU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.304,calculated_trading_power: 17.304}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (PLW:Country {name:"PLW"}) CREATE (PLW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.696,calculated_trading_power: 9.696}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (WRU:Country {name:"WRU"}) CREATE (WRU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.474,calculated_trading_power: 18.474}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (NOO:Country {name:"NOO"}) CREATE (NOO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.854,calculated_trading_power: 17.854}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (MLG:Country {name:"MLG"}) CREATE (MLG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.385,calculated_trading_power: 9.385}]->(australia);
MATCH (nippon:Trade_node {name:"nippon"}), (AMA:Country {name:"AMA"}) CREATE (AMA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.418,calculated_trading_power: 12.418}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (DTE:Country {name:"DTE"}) CREATE (DTE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.563,calculated_trading_power: 15.563}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (HSK:Country {name:"HSK"}) CREATE (HSK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.585,calculated_trading_power: 28.585}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (HTK:Country {name:"HTK"}) CREATE (HTK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.504,calculated_trading_power: 11.504}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (IMG:Country {name:"IMG"}) CREATE (IMG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.159,calculated_trading_power: 10.159}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (ODA:Country {name:"ODA"}) CREATE (ODA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.069,calculated_trading_power: 10.069}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (OTM:Country {name:"OTM"}) CREATE (OTM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.575,calculated_trading_power: 11.575}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (OUC:Country {name:"OUC"}) CREATE (OUC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 42.01,calculated_trading_power: 42.01}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (SBA:Country {name:"SBA"}) CREATE (SBA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.605,calculated_trading_power: 11.605}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (SMZ:Country {name:"SMZ"}) CREATE (SMZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.575,calculated_trading_power: 11.575}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (TKD:Country {name:"TKD"}) CREATE (TKD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.132,calculated_trading_power: 9.132}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (TKG:Country {name:"TKG"}) CREATE (TKG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.002,calculated_trading_power: 9.002}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (UES:Country {name:"UES"}) CREATE (UES)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.627,calculated_trading_power: 30.627}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (YMN:Country {name:"YMN"}) CREATE (YMN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.833,calculated_trading_power: 16.833}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (RFR:Country {name:"RFR"}) CREATE (RFR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.274,calculated_trading_power: 15.274}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (ASK:Country {name:"ASK"}) CREATE (ASK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.367,calculated_trading_power: 24.367}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (KTB:Country {name:"KTB"}) CREATE (KTB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.707,calculated_trading_power: 10.707}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (ANU:Country {name:"ANU"}) CREATE (ANU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.717,calculated_trading_power: 12.717}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (AKT:Country {name:"AKT"}) CREATE (AKT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.575,calculated_trading_power: 11.575}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (CBA:Country {name:"CBA"}) CREATE (CBA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.042,calculated_trading_power: 10.042}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (ISK:Country {name:"ISK"}) CREATE (ISK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.996,calculated_trading_power: 10.996}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (ITO:Country {name:"ITO"}) CREATE (ITO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.996,calculated_trading_power: 10.996}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (KKC:Country {name:"KKC"}) CREATE (KKC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.286,calculated_trading_power: 11.286}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (KNO:Country {name:"KNO"}) CREATE (KNO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.099,calculated_trading_power: 12.099}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (OGS:Country {name:"OGS"}) CREATE (OGS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.414,calculated_trading_power: 8.414}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (SHN:Country {name:"SHN"}) CREATE (SHN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.575,calculated_trading_power: 11.575}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (STK:Country {name:"STK"}) CREATE (STK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.707,calculated_trading_power: 10.707}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (TKI:Country {name:"TKI"}) CREATE (TKI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.611,calculated_trading_power: 9.611}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (UTN:Country {name:"UTN"}) CREATE (UTN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.414,calculated_trading_power: 8.414}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (TTI:Country {name:"TTI"}) CREATE (TTI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.132,calculated_trading_power: 9.132}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (KOR:Country {name:"KOR"}) CREATE (KOR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 86.076,calculated_trading_power: 86.076}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 39.443,calculated_trading_power: 39.443}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (RYU:Country {name:"RYU"}) CREATE (RYU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.537,calculated_trading_power: 12.537}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (SOO:Country {name:"SOO"}) CREATE (SOO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.145,calculated_trading_power: 11.145}]->(nippon);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.521,calculated_trading_power: 2.521}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.83,calculated_trading_power: 6.83}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (MLC:Country {name:"MLC"}) CREATE (MLC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.14,calculated_trading_power: 7.14}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 236.466,calculated_trading_power: 236.466}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.586,calculated_trading_power: 2.586}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.501,calculated_trading_power: 3.501}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (PSA:Country {name:"PSA"}) CREATE (PSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.101,calculated_trading_power: 5.101}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.978,calculated_trading_power: 2.978}]->(hangzhou);
MATCH (xian:Trade_node {name:"xian"}), (KHA:Country {name:"KHA"}) CREATE (KHA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 36.623,calculated_trading_power: 36.623}]->(xian);
MATCH (xian:Trade_node {name:"xian"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 161.183,calculated_trading_power: 161.183}]->(xian);
MATCH (xian:Trade_node {name:"xian"}), (OIR:Country {name:"OIR"}) CREATE (OIR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 49.517,calculated_trading_power: 49.517}]->(xian);
MATCH (xian:Trade_node {name:"xian"}), (KRC:Country {name:"KRC"}) CREATE (KRC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 21.653,calculated_trading_power: 21.653}]->(xian);
MATCH (xian:Trade_node {name:"xian"}), (HMI:Country {name:"HMI"}) CREATE (HMI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.92,calculated_trading_power: 2.92}]->(xian);
MATCH (xian:Trade_node {name:"xian"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.717,calculated_trading_power: 2.717}]->(xian);
MATCH (beijing:Trade_node {name:"beijing"}), (KHA:Country {name:"KHA"}) CREATE (KHA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.82,calculated_trading_power: 6.82}]->(beijing);
MATCH (beijing:Trade_node {name:"beijing"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 143.764,calculated_trading_power: 143.764}]->(beijing);
MATCH (beijing:Trade_node {name:"beijing"}), (OIR:Country {name:"OIR"}) CREATE (OIR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.232,calculated_trading_power: 5.232}]->(beijing);
MATCH (beijing:Trade_node {name:"beijing"}), (KRC:Country {name:"KRC"}) CREATE (KRC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.819,calculated_trading_power: 1.819}]->(beijing);
MATCH (beijing:Trade_node {name:"beijing"}), (HMI:Country {name:"HMI"}) CREATE (HMI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.92,calculated_trading_power: 2.92}]->(beijing);
MATCH (beijing:Trade_node {name:"beijing"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.717,calculated_trading_power: 2.717}]->(beijing);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.521,calculated_trading_power: 2.521}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BLI:Country {name:"BLI"}) CREATE (BLI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.582,calculated_trading_power: 13.582}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.852,calculated_trading_power: 8.852}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (MAJ:Country {name:"MAJ"}) CREATE (MAJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 50.177,calculated_trading_power: 50.177}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (MKS:Country {name:"MKS"}) CREATE (MKS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 44.306,calculated_trading_power: 44.306}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (MLC:Country {name:"MLC"}) CREATE (MLC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.362,calculated_trading_power: 9.362}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.586,calculated_trading_power: 2.586}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BLM:Country {name:"BLM"}) CREATE (BLM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.317,calculated_trading_power: 14.317}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BTN:Country {name:"BTN"}) CREATE (BTN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.643,calculated_trading_power: 23.643}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.823,calculated_trading_power: 5.823}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (PSA:Country {name:"PSA"}) CREATE (PSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.101,calculated_trading_power: 5.101}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.0,calculated_trading_power: 5.0}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (SUN:Country {name:"SUN"}) CREATE (SUN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 52.262,calculated_trading_power: 52.262}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (LUW:Country {name:"LUW"}) CREATE (LUW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.392,calculated_trading_power: 29.392}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (TER:Country {name:"TER"}) CREATE (TER)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.392,calculated_trading_power: 21.392}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (TID:Country {name:"TID"}) CREATE (TID)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.174,calculated_trading_power: 19.174}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BNE:Country {name:"BNE"}) CREATE (BNE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.808,calculated_trading_power: 28.808}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(the_moluccas);
MATCH (siberia:Trade_node {name:"siberia"}), (KAZ:Country {name:"KAZ"}) CREATE (KAZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.945,calculated_trading_power: 6.945}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (MOS:Country {name:"MOS"}) CREATE (MOS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.858,calculated_trading_power: 4.858}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (SHY:Country {name:"SHY"}) CREATE (SHY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 93.652,calculated_trading_power: 93.652}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (TRS:Country {name:"TRS"}) CREATE (TRS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.046,calculated_trading_power: 11.046}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (CHG:Country {name:"CHG"}) CREATE (CHG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.207,calculated_trading_power: 2.207}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (OIR:Country {name:"OIR"}) CREATE (OIR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.478,calculated_trading_power: 5.478}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.802,calculated_trading_power: 4.802}]->(siberia);
MATCH (yumen:Trade_node {name:"yumen"}), (SHY:Country {name:"SHY"}) CREATE (SHY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.102,calculated_trading_power: 4.102}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (TRS:Country {name:"TRS"}) CREATE (TRS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.046,calculated_trading_power: 11.046}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (CHG:Country {name:"CHG"}) CREATE (CHG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 46.06,calculated_trading_power: 46.06}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (KHA:Country {name:"KHA"}) CREATE (KHA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 57.929,calculated_trading_power: 57.929}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.611,calculated_trading_power: 6.611}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (OIR:Country {name:"OIR"}) CREATE (OIR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 79.901,calculated_trading_power: 79.901}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (HMI:Country {name:"HMI"}) CREATE (HMI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.036,calculated_trading_power: 26.036}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.391,calculated_trading_power: 18.391}]->(yumen);
MATCH (malacca:Trade_node {name:"malacca"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.682,calculated_trading_power: 6.682}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.075,calculated_trading_power: 26.075}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 54.228,calculated_trading_power: 54.228}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (MLC:Country {name:"MLC"}) CREATE (MLC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 91.584,calculated_trading_power: 91.584}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 33.658,calculated_trading_power: 33.658}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BNG:Country {name:"BNG"}) CREATE (BNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 31.796,calculated_trading_power: 31.796}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (JNP:Country {name:"JNP"}) CREATE (JNP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.779,calculated_trading_power: 4.779}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (ORI:Country {name:"ORI"}) CREATE (ORI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.99,calculated_trading_power: 5.99}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (TRT:Country {name:"TRT"}) CREATE (TRT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.337,calculated_trading_power: 2.337}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (KED:Country {name:"KED"}) CREATE (KED)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.497,calculated_trading_power: 21.497}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (LIG:Country {name:"LIG"}) CREATE (LIG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.426,calculated_trading_power: 20.426}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PGR:Country {name:"PGR"}) CREATE (PGR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.293,calculated_trading_power: 18.293}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.772,calculated_trading_power: 32.772}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PSA:Country {name:"PSA"}) CREATE (PSA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 40.251,calculated_trading_power: 40.251}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.737,calculated_trading_power: 28.737}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.207,calculated_trading_power: 24.207}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.564,calculated_trading_power: 22.564}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.873,calculated_trading_power: 24.873}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (SMB:Country {name:"SMB"}) CREATE (SMB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.257,calculated_trading_power: 29.257}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BRS:Country {name:"BRS"}) CREATE (BRS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.698,calculated_trading_power: 17.698}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (DLI:Country {name:"DLI"}) CREATE (DLI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.381,calculated_trading_power: 20.381}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (JMB:Country {name:"JMB"}) CREATE (JMB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.691,calculated_trading_power: 12.691}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PAH:Country {name:"PAH"}) CREATE (PAH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.991,calculated_trading_power: 13.991}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (KEL:Country {name:"KEL"}) CREATE (KEL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.758,calculated_trading_power: 14.758}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (IND:Country {name:"IND"}) CREATE (IND)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.087,calculated_trading_power: 16.087}]->(malacca);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 42.017,calculated_trading_power: 42.017}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (BNG:Country {name:"BNG"}) CREATE (BNG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 169.032,calculated_trading_power: 169.032}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (DLH:Country {name:"DLH"}) CREATE (DLH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.329,calculated_trading_power: 5.329}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (VIJ:Country {name:"VIJ"}) CREATE (VIJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 27.764,calculated_trading_power: 27.764}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (JNP:Country {name:"JNP"}) CREATE (JNP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 76.584,calculated_trading_power: 76.584}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MAD:Country {name:"MAD"}) CREATE (MAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.02,calculated_trading_power: 2.02}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (ORI:Country {name:"ORI"}) CREATE (ORI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 37.176,calculated_trading_power: 37.176}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (GDW:Country {name:"GDW"}) CREATE (GDW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.027,calculated_trading_power: 9.027}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (GRJ:Country {name:"GRJ"}) CREATE (GRJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.962,calculated_trading_power: 8.962}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (GWA:Country {name:"GWA"}) CREATE (GWA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (VND:Country {name:"VND"}) CREATE (VND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MAB:Country {name:"MAB"}) CREATE (MAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.311,calculated_trading_power: 5.311}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (BST:Country {name:"BST"}) CREATE (BST)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.775,calculated_trading_power: 8.775}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (BND:Country {name:"BND"}) CREATE (BND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (CEY:Country {name:"CEY"}) CREATE (CEY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.592,calculated_trading_power: 4.592}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KMT:Country {name:"KMT"}) CREATE (KMT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.101,calculated_trading_power: 11.101}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KOC:Country {name:"KOC"}) CREATE (KOC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (BGL:Country {name:"BGL"}) CREATE (BGL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.295,calculated_trading_power: 5.295}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (JFN:Country {name:"JFN"}) CREATE (JFN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (CHD:Country {name:"CHD"}) CREATE (CHD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (NGP:Country {name:"NGP"}) CREATE (NGP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.158,calculated_trading_power: 10.158}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (TRT:Country {name:"TRT"}) CREATE (TRT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.62,calculated_trading_power: 20.62}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (YOR:Country {name:"YOR"}) CREATE (YOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.209,calculated_trading_power: 11.209}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (DGL:Country {name:"DGL"}) CREATE (DGL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.014,calculated_trading_power: 2.014}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MBL:Country {name:"MBL"}) CREATE (MBL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.784,calculated_trading_power: 10.784}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (PAN:Country {name:"PAN"}) CREATE (PAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.028,calculated_trading_power: 4.028}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KLP:Country {name:"KLP"}) CREATE (KLP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.612,calculated_trading_power: 7.612}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (PTT:Country {name:"PTT"}) CREATE (PTT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.846,calculated_trading_power: 18.846}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (RTT:Country {name:"RTT"}) CREATE (RTT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.136,calculated_trading_power: 10.136}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KLH:Country {name:"KLH"}) CREATE (KLH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.789,calculated_trading_power: 7.789}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KJH:Country {name:"KJH"}) CREATE (KJH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.7,calculated_trading_power: 8.7}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (PRD:Country {name:"PRD"}) CREATE (PRD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.437,calculated_trading_power: 8.437}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (JPR:Country {name:"JPR"}) CREATE (JPR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.665,calculated_trading_power: 7.665}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KND:Country {name:"KND"}) CREATE (KND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.006,calculated_trading_power: 4.006}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (TLG:Country {name:"TLG"}) CREATE (TLG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.619,calculated_trading_power: 6.619}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KLT:Country {name:"KLT"}) CREATE (KLT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (DNG:Country {name:"DNG"}) CREATE (DNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (DTI:Country {name:"DTI"}) CREATE (DTI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (JML:Country {name:"JML"}) CREATE (JML)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KTU:Country {name:"KTU"}) CREATE (KTU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.713,calculated_trading_power: 1.713}]->(ganges_delta);
MATCH (doab:Trade_node {name:"doab"}), (AFG:Country {name:"AFG"}) CREATE (AFG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.342,calculated_trading_power: 23.342}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (BAH:Country {name:"BAH"}) CREATE (BAH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 70.621,calculated_trading_power: 70.621}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (DLH:Country {name:"DLH"}) CREATE (DLH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 53.192,calculated_trading_power: 53.192}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (JNP:Country {name:"JNP"}) CREATE (JNP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 143.732,calculated_trading_power: 143.732}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (MLW:Country {name:"MLW"}) CREATE (MLW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 28.271,calculated_trading_power: 28.271}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.299,calculated_trading_power: 1.299}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (MUL:Country {name:"MUL"}) CREATE (MUL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.793,calculated_trading_power: 17.793}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (GDW:Country {name:"GDW"}) CREATE (GDW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.989,calculated_trading_power: 9.989}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (GWA:Country {name:"GWA"}) CREATE (GWA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.026,calculated_trading_power: 13.026}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (DHU:Country {name:"DHU"}) CREATE (DHU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.553,calculated_trading_power: 21.553}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (KSH:Country {name:"KSH"}) CREATE (KSH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.407,calculated_trading_power: 8.407}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (KHD:Country {name:"KHD"}) CREATE (KHD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.18,calculated_trading_power: 14.18}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (MEW:Country {name:"MEW"}) CREATE (MEW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.775,calculated_trading_power: 12.775}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (BND:Country {name:"BND"}) CREATE (BND)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.148,calculated_trading_power: 11.148}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (KGR:Country {name:"KGR"}) CREATE (KGR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.291,calculated_trading_power: 10.291}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (HAD:Country {name:"HAD"}) CREATE (HAD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.643,calculated_trading_power: 12.643}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (LDK:Country {name:"LDK"}) CREATE (LDK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.06,calculated_trading_power: 5.06}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (BGL:Country {name:"BGL"}) CREATE (BGL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.917,calculated_trading_power: 13.917}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (GHR:Country {name:"GHR"}) CREATE (GHR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.807,calculated_trading_power: 9.807}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (CHD:Country {name:"CHD"}) CREATE (CHD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (NGP:Country {name:"NGP"}) CREATE (NGP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 0.712,calculated_trading_power: 0.712}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (CMP:Country {name:"CMP"}) CREATE (CMP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (IDR:Country {name:"IDR"}) CREATE (IDR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (JLV:Country {name:"JLV"}) CREATE (JLV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (PAN:Country {name:"PAN"}) CREATE (PAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 0.243,calculated_trading_power: 0.243}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (KLP:Country {name:"KLP"}) CREATE (KLP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.178,calculated_trading_power: 18.178}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (TLG:Country {name:"TLG"}) CREATE (TLG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.619,calculated_trading_power: 6.619}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (DNG:Country {name:"DNG"}) CREATE (DNG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.311,calculated_trading_power: 11.311}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (DTI:Country {name:"DTI"}) CREATE (DTI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.779,calculated_trading_power: 10.779}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (JML:Country {name:"JML"}) CREATE (JML)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.332,calculated_trading_power: 11.332}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (SRM:Country {name:"SRM"}) CREATE (SRM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.807,calculated_trading_power: 9.807}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (KMN:Country {name:"KMN"}) CREATE (KMN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.807,calculated_trading_power: 9.807}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (SRH:Country {name:"SRH"}) CREATE (SRH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 45.63,calculated_trading_power: 45.63}]->(doab);
MATCH (lahore:Trade_node {name:"lahore"}), (SRV:Country {name:"SRV"}) CREATE (SRV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.153,calculated_trading_power: 4.153}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (SHY:Country {name:"SHY"}) CREATE (SHY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.102,calculated_trading_power: 4.102}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (AFG:Country {name:"AFG"}) CREATE (AFG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 61.883,calculated_trading_power: 61.883}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (QAR:Country {name:"QAR"}) CREATE (QAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.887,calculated_trading_power: 6.887}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (TIM:Country {name:"TIM"}) CREATE (TIM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.531,calculated_trading_power: 18.531}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (TRS:Country {name:"TRS"}) CREATE (TRS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 55.068,calculated_trading_power: 55.068}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (TAB:Country {name:"TAB"}) CREATE (TAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.329,calculated_trading_power: 4.329}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (QOM:Country {name:"QOM"}) CREATE (QOM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.909,calculated_trading_power: 8.909}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (CHG:Country {name:"CHG"}) CREATE (CHG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.207,calculated_trading_power: 2.207}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 25.445,calculated_trading_power: 25.445}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (BAL:Country {name:"BAL"}) CREATE (BAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.415,calculated_trading_power: 25.415}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (DLH:Country {name:"DLH"}) CREATE (DLH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.808,calculated_trading_power: 1.808}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (VIJ:Country {name:"VIJ"}) CREATE (VIJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.716,calculated_trading_power: 2.716}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (GUJ:Country {name:"GUJ"}) CREATE (GUJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.634,calculated_trading_power: 16.634}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.932,calculated_trading_power: 24.932}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (MUL:Country {name:"MUL"}) CREATE (MUL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 38.635,calculated_trading_power: 38.635}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (SND:Country {name:"SND"}) CREATE (SND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.06,calculated_trading_power: 7.06}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (JAN:Country {name:"JAN"}) CREATE (JAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (KSH:Country {name:"KSH"}) CREATE (KSH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.297,calculated_trading_power: 22.297}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (JSL:Country {name:"JSL"}) CREATE (JSL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (NGA:Country {name:"NGA"}) CREATE (NGA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.38,calculated_trading_power: 5.38}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (LDK:Country {name:"LDK"}) CREATE (LDK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.481,calculated_trading_power: 13.481}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (BGA:Country {name:"BGA"}) CREATE (BGA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (IDR:Country {name:"IDR"}) CREATE (IDR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (JLV:Country {name:"JLV"}) CREATE (JLV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (SRH:Country {name:"SRH"}) CREATE (SRH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 94.828,calculated_trading_power: 94.828}]->(lahore);
MATCH (deccan:Trade_node {name:"deccan"}), (BAH:Country {name:"BAH"}) CREATE (BAH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 160.085,calculated_trading_power: 160.085}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MYS:Country {name:"MYS"}) CREATE (MYS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.366,calculated_trading_power: 8.366}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (VIJ:Country {name:"VIJ"}) CREATE (VIJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 72.827,calculated_trading_power: 72.827}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (GUJ:Country {name:"GUJ"}) CREATE (GUJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.634,calculated_trading_power: 16.634}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MAD:Country {name:"MAD"}) CREATE (MAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.712,calculated_trading_power: 4.712}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MLW:Country {name:"MLW"}) CREATE (MLW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 56.502,calculated_trading_power: 56.502}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.932,calculated_trading_power: 24.932}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (ORI:Country {name:"ORI"}) CREATE (ORI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.17,calculated_trading_power: 2.17}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (SND:Country {name:"SND"}) CREATE (SND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.06,calculated_trading_power: 7.06}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (JAN:Country {name:"JAN"}) CREATE (JAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (GDW:Country {name:"GDW"}) CREATE (GDW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.322,calculated_trading_power: 19.322}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (KHD:Country {name:"KHD"}) CREATE (KHD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.877,calculated_trading_power: 36.877}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (VND:Country {name:"VND"}) CREATE (VND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.066,calculated_trading_power: 6.066}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MAB:Country {name:"MAB"}) CREATE (MAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.529,calculated_trading_power: 7.529}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (CEY:Country {name:"CEY"}) CREATE (CEY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.982,calculated_trading_power: 10.982}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (JSL:Country {name:"JSL"}) CREATE (JSL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (KAT:Country {name:"KAT"}) CREATE (KAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (KOC:Country {name:"KOC"}) CREATE (KOC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (NGA:Country {name:"NGA"}) CREATE (NGA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.38,calculated_trading_power: 5.38}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (JFN:Country {name:"JFN"}) CREATE (JFN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (CHD:Country {name:"CHD"}) CREATE (CHD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.913,calculated_trading_power: 9.913}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (CMP:Country {name:"CMP"}) CREATE (CMP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.577,calculated_trading_power: 11.577}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (BGA:Country {name:"BGA"}) CREATE (BGA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (YOR:Country {name:"YOR"}) CREATE (YOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 22.633,calculated_trading_power: 22.633}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (IDR:Country {name:"IDR"}) CREATE (IDR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (JLV:Country {name:"JLV"}) CREATE (JLV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (PTL:Country {name:"PTL"}) CREATE (PTL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (JGD:Country {name:"JGD"}) CREATE (JGD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (PRB:Country {name:"PRB"}) CREATE (PRB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (KND:Country {name:"KND"}) CREATE (KND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.742,calculated_trading_power: 7.742}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (TLG:Country {name:"TLG"}) CREATE (TLG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.025,calculated_trading_power: 18.025}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (KLT:Country {name:"KLT"}) CREATE (KLT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.38,calculated_trading_power: 4.38}]->(deccan);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (ADE:Country {name:"ADE"}) CREATE (ADE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.388,calculated_trading_power: 4.388}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.533,calculated_trading_power: 2.533}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MHR:Country {name:"MHR"}) CREATE (MHR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (YEM:Country {name:"YEM"}) CREATE (YEM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.889,calculated_trading_power: 2.889}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (ORM:Country {name:"ORM"}) CREATE (ORM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.222,calculated_trading_power: 2.222}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (ADA:Country {name:"ADA"}) CREATE (ADA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.946,calculated_trading_power: 6.946}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.233,calculated_trading_power: 2.233}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MDI:Country {name:"MDI"}) CREATE (MDI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.743,calculated_trading_power: 3.743}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MYS:Country {name:"MYS"}) CREATE (MYS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.82,calculated_trading_power: 12.82}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (VIJ:Country {name:"VIJ"}) CREATE (VIJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 177.931,calculated_trading_power: 177.931}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (GUJ:Country {name:"GUJ"}) CREATE (GUJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.881,calculated_trading_power: 20.881}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MAD:Country {name:"MAD"}) CREATE (MAD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.217,calculated_trading_power: 28.217}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.518,calculated_trading_power: 4.518}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (ORI:Country {name:"ORI"}) CREATE (ORI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.444,calculated_trading_power: 6.444}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (SND:Country {name:"SND"}) CREATE (SND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.082,calculated_trading_power: 9.082}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (VND:Country {name:"VND"}) CREATE (VND)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.067,calculated_trading_power: 32.067}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MAB:Country {name:"MAB"}) CREATE (MAB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 45.073,calculated_trading_power: 45.073}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (CEY:Country {name:"CEY"}) CREATE (CEY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 38.397,calculated_trading_power: 38.397}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (KAT:Country {name:"KAT"}) CREATE (KAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (KOC:Country {name:"KOC"}) CREATE (KOC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.24,calculated_trading_power: 28.24}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (JFN:Country {name:"JFN"}) CREATE (JFN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.584,calculated_trading_power: 17.584}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (YOR:Country {name:"YOR"}) CREATE (YOR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 82.878,calculated_trading_power: 82.878}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (DGL:Country {name:"DGL"}) CREATE (DGL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.852,calculated_trading_power: 24.852}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (PTL:Country {name:"PTL"}) CREATE (PTL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (JGD:Country {name:"JGD"}) CREATE (JGD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (PRB:Country {name:"PRB"}) CREATE (PRB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (KND:Country {name:"KND"}) CREATE (KND)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.658,calculated_trading_power: 27.658}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (KLT:Country {name:"KLT"}) CREATE (KLT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.669,calculated_trading_power: 36.669}]->(comorin_cape);
MATCH (gujarat:Trade_node {name:"gujarat"}), (ADE:Country {name:"ADE"}) CREATE (ADE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.432,calculated_trading_power: 8.432}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (ALH:Country {name:"ALH"}) CREATE (ALH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.977,calculated_trading_power: 6.977}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MHR:Country {name:"MHR"}) CREATE (MHR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (OMA:Country {name:"OMA"}) CREATE (OMA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (YEM:Country {name:"YEM"}) CREATE (YEM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.911,calculated_trading_power: 4.911}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MSY:Country {name:"MSY"}) CREATE (MSY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (TIM:Country {name:"TIM"}) CREATE (TIM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.04,calculated_trading_power: 3.04}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (ORM:Country {name:"ORM"}) CREATE (ORM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.169,calculated_trading_power: 11.169}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (FRS:Country {name:"FRS"}) CREATE (FRS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.661,calculated_trading_power: 9.661}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.788,calculated_trading_power: 6.788}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (ADA:Country {name:"ADA"}) CREATE (ADA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.974,calculated_trading_power: 8.974}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (SFA:Country {name:"SFA"}) CREATE (SFA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.241,calculated_trading_power: 5.241}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MBA:Country {name:"MBA"}) CREATE (MBA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.908,calculated_trading_power: 3.908}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.386,calculated_trading_power: 2.386}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.255,calculated_trading_power: 4.255}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MDI:Country {name:"MDI"}) CREATE (MDI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.743,calculated_trading_power: 3.743}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MRE:Country {name:"MRE"}) CREATE (MRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.026,calculated_trading_power: 2.026}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (WAR:Country {name:"WAR"}) CREATE (WAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.926,calculated_trading_power: 2.926}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.41,calculated_trading_power: 2.41}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (BAL:Country {name:"BAL"}) CREATE (BAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.088,calculated_trading_power: 8.088}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (VIJ:Country {name:"VIJ"}) CREATE (VIJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.584,calculated_trading_power: 13.584}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (GUJ:Country {name:"GUJ"}) CREATE (GUJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 113.848,calculated_trading_power: 113.848}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 33.114,calculated_trading_power: 33.114}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (SND:Country {name:"SND"}) CREATE (SND)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 57.683,calculated_trading_power: 57.683}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (JAN:Country {name:"JAN"}) CREATE (JAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.682,calculated_trading_power: 7.682}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (JSL:Country {name:"JSL"}) CREATE (JSL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 6.915,calculated_trading_power: 6.915}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (KAT:Country {name:"KAT"}) CREATE (KAT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.783,calculated_trading_power: 29.783}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (NGA:Country {name:"NGA"}) CREATE (NGA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.911,calculated_trading_power: 8.911}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (BGA:Country {name:"BGA"}) CREATE (BGA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.203,calculated_trading_power: 10.203}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (PTL:Country {name:"PTL"}) CREATE (PTL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.708,calculated_trading_power: 13.708}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (JGD:Country {name:"JGD"}) CREATE (JGD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.98,calculated_trading_power: 14.98}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (PRB:Country {name:"PRB"}) CREATE (PRB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.764,calculated_trading_power: 24.764}]->(gujarat);
MATCH (katsina:Trade_node {name:"katsina"}), (ETH:Country {name:"ETH"}) CREATE (ETH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.406,calculated_trading_power: 5.406}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (MAL:Country {name:"MAL"}) CREATE (MAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.318,calculated_trading_power: 5.318}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (SON:Country {name:"SON"}) CREATE (SON)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.654,calculated_trading_power: 23.654}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (KBO:Country {name:"KBO"}) CREATE (KBO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.402,calculated_trading_power: 35.402}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (OYO:Country {name:"OYO"}) CREATE (OYO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.466,calculated_trading_power: 18.466}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (WGD:Country {name:"WGD"}) CREATE (WGD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.424,calculated_trading_power: 8.424}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (GUR:Country {name:"GUR"}) CREATE (GUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (WAD:Country {name:"WAD"}) CREATE (WAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (ALO:Country {name:"ALO"}) CREATE (ALO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.35,calculated_trading_power: 3.35}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (AIR:Country {name:"AIR"}) CREATE (AIR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.541,calculated_trading_power: 24.541}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (BON:Country {name:"BON"}) CREATE (BON)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.054,calculated_trading_power: 6.054}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (DAH:Country {name:"DAH"}) CREATE (DAH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (DGB:Country {name:"DGB"}) CREATE (DGB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (JNN:Country {name:"JNN"}) CREATE (JNN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 21.735,calculated_trading_power: 21.735}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (KAN:Country {name:"KAN"}) CREATE (KAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.185,calculated_trading_power: 24.185}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (KNG:Country {name:"KNG"}) CREATE (KNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.739,calculated_trading_power: 6.739}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (KTS:Country {name:"KTS"}) CREATE (KTS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 41.01,calculated_trading_power: 41.01}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (NUP:Country {name:"NUP"}) CREATE (NUP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.996,calculated_trading_power: 13.996}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (TMB:Country {name:"TMB"}) CREATE (TMB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 19.469,calculated_trading_power: 19.469}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (YAO:Country {name:"YAO"}) CREATE (YAO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.395,calculated_trading_power: 32.395}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (YAT:Country {name:"YAT"}) CREATE (YAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.75,calculated_trading_power: 7.75}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (ZZZ:Country {name:"ZZZ"}) CREATE (ZZZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.043,calculated_trading_power: 23.043}]->(katsina);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ADE:Country {name:"ADE"}) CREATE (ADE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.388,calculated_trading_power: 4.388}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.533,calculated_trading_power: 2.533}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.619,calculated_trading_power: 4.619}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MAK:Country {name:"MAK"}) CREATE (MAK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.045,calculated_trading_power: 5.045}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MFL:Country {name:"MFL"}) CREATE (MFL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (NJR:Country {name:"NJR"}) CREATE (NJR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (RAS:Country {name:"RAS"}) CREATE (RAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.7,calculated_trading_power: 8.7}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (YEM:Country {name:"YEM"}) CREATE (YEM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.889,calculated_trading_power: 2.889}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MAM:Country {name:"MAM"}) CREATE (MAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 22.444,calculated_trading_power: 22.444}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ETH:Country {name:"ETH"}) CREATE (ETH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 57.511,calculated_trading_power: 57.511}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ADA:Country {name:"ADA"}) CREATE (ADA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.3,calculated_trading_power: 8.3}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.857,calculated_trading_power: 5.857}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MDI:Country {name:"MDI"}) CREATE (MDI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.743,calculated_trading_power: 3.743}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ENA:Country {name:"ENA"}) CREATE (ENA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.112,calculated_trading_power: 11.112}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (OGD:Country {name:"OGD"}) CREATE (OGD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.533,calculated_trading_power: 10.533}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (WAD:Country {name:"WAD"}) CREATE (WAD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.339,calculated_trading_power: 9.339}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ALO:Country {name:"ALO"}) CREATE (ALO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.88,calculated_trading_power: 30.88}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (KAF:Country {name:"KAF"}) CREATE (KAF)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.052,calculated_trading_power: 15.052}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MED:Country {name:"MED"}) CREATE (MED)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.261,calculated_trading_power: 14.261}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MRE:Country {name:"MRE"}) CREATE (MRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 0.726,calculated_trading_power: 0.726}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (BEJ:Country {name:"BEJ"}) CREATE (BEJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.724,calculated_trading_power: 12.724}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (WLY:Country {name:"WLY"}) CREATE (WLY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.032,calculated_trading_power: 14.032}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (DAM:Country {name:"DAM"}) CREATE (DAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.351,calculated_trading_power: 14.351}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (HDY:Country {name:"HDY"}) CREATE (HDY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.616,calculated_trading_power: 13.616}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (JJI:Country {name:"JJI"}) CREATE (JJI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.049,calculated_trading_power: 10.049}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ABB:Country {name:"ABB"}) CREATE (ABB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.718,calculated_trading_power: 14.718}]->(ethiopia);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (ADE:Country {name:"ADE"}) CREATE (ADE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 45.223,calculated_trading_power: 45.223}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (DAW:Country {name:"DAW"}) CREATE (DAW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.996,calculated_trading_power: 35.996}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.605,calculated_trading_power: 6.605}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MDA:Country {name:"MDA"}) CREATE (MDA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MFL:Country {name:"MFL"}) CREATE (MFL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.076,calculated_trading_power: 16.076}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MHR:Country {name:"MHR"}) CREATE (MHR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.646,calculated_trading_power: 30.646}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (NAJ:Country {name:"NAJ"}) CREATE (NAJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (NJR:Country {name:"NJR"}) CREATE (NJR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.359,calculated_trading_power: 9.359}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (OMA:Country {name:"OMA"}) CREATE (OMA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (RAS:Country {name:"RAS"}) CREATE (RAS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.888,calculated_trading_power: 11.888}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (YAS:Country {name:"YAS"}) CREATE (YAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (YEM:Country {name:"YEM"}) CREATE (YEM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 39.207,calculated_trading_power: 39.207}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MAM:Country {name:"MAM"}) CREATE (MAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.236,calculated_trading_power: 24.236}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (TIM:Country {name:"TIM"}) CREATE (TIM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.04,calculated_trading_power: 3.04}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (ORM:Country {name:"ORM"}) CREATE (ORM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.107,calculated_trading_power: 12.107}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (FRS:Country {name:"FRS"}) CREATE (FRS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.661,calculated_trading_power: 9.661}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (ETH:Country {name:"ETH"}) CREATE (ETH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 0.704,calculated_trading_power: 0.704}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.788,calculated_trading_power: 6.788}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (ADA:Country {name:"ADA"}) CREATE (ADA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 59.326,calculated_trading_power: 59.326}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (SFA:Country {name:"SFA"}) CREATE (SFA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.241,calculated_trading_power: 5.241}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MBA:Country {name:"MBA"}) CREATE (MBA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.944,calculated_trading_power: 7.944}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.422,calculated_trading_power: 6.422}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.718,calculated_trading_power: 26.718}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MDI:Country {name:"MDI"}) CREATE (MDI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 33.613,calculated_trading_power: 33.613}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MED:Country {name:"MED"}) CREATE (MED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.498,calculated_trading_power: 4.498}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MRE:Country {name:"MRE"}) CREATE (MRE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.031,calculated_trading_power: 21.031}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (PTE:Country {name:"PTE"}) CREATE (PTE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (WAR:Country {name:"WAR"}) CREATE (WAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.83,calculated_trading_power: 32.83}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.926,calculated_trading_power: 2.926}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.41,calculated_trading_power: 2.41}]->(gulf_of_aden);
MATCH (hormuz:Trade_node {name:"hormuz"}), (ALH:Country {name:"ALH"}) CREATE (ALH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.594,calculated_trading_power: 4.594}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (DAW:Country {name:"DAW"}) CREATE (DAW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (NAJ:Country {name:"NAJ"}) CREATE (NAJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (OMA:Country {name:"OMA"}) CREATE (OMA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.834,calculated_trading_power: 11.834}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (SHM:Country {name:"SHM"}) CREATE (SHM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (YAS:Country {name:"YAS"}) CREATE (YAS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.561,calculated_trading_power: 8.561}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (MSY:Country {name:"MSY"}) CREATE (MSY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.963,calculated_trading_power: 6.963}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (QAR:Country {name:"QAR"}) CREATE (QAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.015,calculated_trading_power: 5.015}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (TIM:Country {name:"TIM"}) CREATE (TIM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.202,calculated_trading_power: 15.202}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (ORM:Country {name:"ORM"}) CREATE (ORM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 77.702,calculated_trading_power: 77.702}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (LRI:Country {name:"LRI"}) CREATE (LRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (FRS:Country {name:"FRS"}) CREATE (FRS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 52.079,calculated_trading_power: 52.079}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (BAL:Country {name:"BAL"}) CREATE (BAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.347,calculated_trading_power: 6.347}]->(hormuz);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 77.008,calculated_trading_power: 77.008}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (SFA:Country {name:"SFA"}) CREATE (SFA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 45.239,calculated_trading_power: 45.239}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (MBA:Country {name:"MBA"}) CREATE (MBA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 44.773,calculated_trading_power: 44.773}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.402,calculated_trading_power: 36.402}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.54,calculated_trading_power: 1.54}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (PTE:Country {name:"PTE"}) CREATE (PTE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 31.684,calculated_trading_power: 31.684}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (MIR:Country {name:"MIR"}) CREATE (MIR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.572,calculated_trading_power: 10.572}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 41.59,calculated_trading_power: 41.59}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.259,calculated_trading_power: 35.259}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (MFY:Country {name:"MFY"}) CREATE (MFY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.944,calculated_trading_power: 22.944}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (ANT:Country {name:"ANT"}) CREATE (ANT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.478,calculated_trading_power: 26.478}]->(zanzibar);
MATCH (basra:Trade_node {name:"basra"}), (ALH:Country {name:"ALH"}) CREATE (ALH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.886,calculated_trading_power: 23.886}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (ANZ:Country {name:"ANZ"}) CREATE (ANZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 6.013,calculated_trading_power: 6.013}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (ARD:Country {name:"ARD"}) CREATE (ARD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.619,calculated_trading_power: 8.619}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (DAW:Country {name:"DAW"}) CREATE (DAW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.123,calculated_trading_power: 10.123}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (NAJ:Country {name:"NAJ"}) CREATE (NAJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.859,calculated_trading_power: 8.859}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (SHM:Country {name:"SHM"}) CREATE (SHM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.842,calculated_trading_power: 16.842}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (SRV:Country {name:"SRV"}) CREATE (SRV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.153,calculated_trading_power: 4.153}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (AKK:Country {name:"AKK"}) CREATE (AKK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.899,calculated_trading_power: 2.899}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (KAR:Country {name:"KAR"}) CREATE (KAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.139,calculated_trading_power: 2.139}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (RAM:Country {name:"RAM"}) CREATE (RAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.048,calculated_trading_power: 2.048}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (MSY:Country {name:"MSY"}) CREATE (MSY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 46.029,calculated_trading_power: 46.029}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (MAM:Country {name:"MAM"}) CREATE (MAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.288,calculated_trading_power: 12.288}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (KHO:Country {name:"KHO"}) CREATE (KHO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.631,calculated_trading_power: 8.631}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (QAR:Country {name:"QAR"}) CREATE (QAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 84.691,calculated_trading_power: 84.691}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (TIM:Country {name:"TIM"}) CREATE (TIM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.22,calculated_trading_power: 9.22}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (KRY:Country {name:"KRY"}) CREATE (KRY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.054,calculated_trading_power: 6.054}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (TAB:Country {name:"TAB"}) CREATE (TAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.082,calculated_trading_power: 14.082}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (LRI:Country {name:"LRI"}) CREATE (LRI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.308,calculated_trading_power: 11.308}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (SIS:Country {name:"SIS"}) CREATE (SIS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.619,calculated_trading_power: 6.619}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (BPI:Country {name:"BPI"}) CREATE (BPI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (QOM:Country {name:"QOM"}) CREATE (QOM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 44.792,calculated_trading_power: 44.792}]->(basra);
MATCH (samarkand:Trade_node {name:"samarkand"}), (NOG:Country {name:"NOG"}) CREATE (NOG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 39.601,calculated_trading_power: 39.601}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (GOL:Country {name:"GOL"}) CREATE (GOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.501,calculated_trading_power: 9.501}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (SRV:Country {name:"SRV"}) CREATE (SRV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.153,calculated_trading_power: 4.153}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (SHY:Country {name:"SHY"}) CREATE (SHY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.28,calculated_trading_power: 11.28}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (KHO:Country {name:"KHO"}) CREATE (KHO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.631,calculated_trading_power: 8.631}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (QAR:Country {name:"QAR"}) CREATE (QAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.887,calculated_trading_power: 6.887}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (TIM:Country {name:"TIM"}) CREATE (TIM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 63.157,calculated_trading_power: 63.157}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (TRS:Country {name:"TRS"}) CREATE (TRS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 114.716,calculated_trading_power: 114.716}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (TAB:Country {name:"TAB"}) CREATE (TAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.329,calculated_trading_power: 4.329}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (SIS:Country {name:"SIS"}) CREATE (SIS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.619,calculated_trading_power: 6.619}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (QOM:Country {name:"QOM"}) CREATE (QOM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.909,calculated_trading_power: 8.909}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (CHG:Country {name:"CHG"}) CREATE (CHG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.531,calculated_trading_power: 6.531}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 54.617,calculated_trading_power: 54.617}]->(samarkand);
MATCH (persia:Trade_node {name:"persia"}), (NOG:Country {name:"NOG"}) CREATE (NOG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 35.249,calculated_trading_power: 35.249}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (GOL:Country {name:"GOL"}) CREATE (GOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 55.852,calculated_trading_power: 55.852}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (SRV:Country {name:"SRV"}) CREATE (SRV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.604,calculated_trading_power: 36.604}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (HSN:Country {name:"HSN"}) CREATE (HSN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.048,calculated_trading_power: 4.048}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (BTL:Country {name:"BTL"}) CREATE (BTL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.336,calculated_trading_power: 9.336}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (AKK:Country {name:"AKK"}) CREATE (AKK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.578,calculated_trading_power: 23.578}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (KAR:Country {name:"KAR"}) CREATE (KAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.139,calculated_trading_power: 2.139}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (RAM:Country {name:"RAM"}) CREATE (RAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.048,calculated_trading_power: 2.048}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (AVR:Country {name:"AVR"}) CREATE (AVR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.302,calculated_trading_power: 4.302}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (MLK:Country {name:"MLK"}) CREATE (MLK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.074,calculated_trading_power: 12.074}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (SME:Country {name:"SME"}) CREATE (SME)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.413,calculated_trading_power: 7.413}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (ARL:Country {name:"ARL"}) CREATE (ARL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.722,calculated_trading_power: 13.722}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (MAM:Country {name:"MAM"}) CREATE (MAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.288,calculated_trading_power: 12.288}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (KHO:Country {name:"KHO"}) CREATE (KHO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.658,calculated_trading_power: 19.658}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (QAR:Country {name:"QAR"}) CREATE (QAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 101.331,calculated_trading_power: 101.331}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (TIM:Country {name:"TIM"}) CREATE (TIM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 112.378,calculated_trading_power: 112.378}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (KRY:Country {name:"KRY"}) CREATE (KRY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.518,calculated_trading_power: 19.518}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (GAZ:Country {name:"GAZ"}) CREATE (GAZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.413,calculated_trading_power: 7.413}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (TAB:Country {name:"TAB"}) CREATE (TAB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 40.061,calculated_trading_power: 40.061}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (SIS:Country {name:"SIS"}) CREATE (SIS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.432,calculated_trading_power: 15.432}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (BPI:Country {name:"BPI"}) CREATE (BPI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.196,calculated_trading_power: 20.196}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (QOM:Country {name:"QOM"}) CREATE (QOM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 91.208,calculated_trading_power: 91.208}]->(persia);
MATCH (aleppo:Trade_node {name:"aleppo"}), (BYZ:Country {name:"BYZ"}) CREATE (BYZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.049,calculated_trading_power: 9.049}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (CYP:Country {name:"CYP"}) CREATE (CYP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.534,calculated_trading_power: 15.534}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (KNI:Country {name:"KNI"}) CREATE (KNI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (NAX:Country {name:"NAX"}) CREATE (NAX)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.992,calculated_trading_power: 1.992}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.511,calculated_trading_power: 18.511}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (ANZ:Country {name:"ANZ"}) CREATE (ANZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (FAD:Country {name:"FAD"}) CREATE (FAD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.556,calculated_trading_power: 9.556}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.619,calculated_trading_power: 4.619}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (MDA:Country {name:"MDA"}) CREATE (MDA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (HSN:Country {name:"HSN"}) CREATE (HSN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.385,calculated_trading_power: 9.385}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (BTL:Country {name:"BTL"}) CREATE (BTL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.093,calculated_trading_power: 11.093}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (AKK:Country {name:"AKK"}) CREATE (AKK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.742,calculated_trading_power: 23.742}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (CND:Country {name:"CND"}) CREATE (CND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.009,calculated_trading_power: 6.009}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (DUL:Country {name:"DUL"}) CREATE (DUL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.632,calculated_trading_power: 12.632}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (KAR:Country {name:"KAR"}) CREATE (KAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.818,calculated_trading_power: 21.818}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (RAM:Country {name:"RAM"}) CREATE (RAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.34,calculated_trading_power: 21.34}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (MAM:Country {name:"MAM"}) CREATE (MAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 86.01,calculated_trading_power: 86.01}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (QAR:Country {name:"QAR"}) CREATE (QAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.978,calculated_trading_power: 4.978}]->(aleppo);
MATCH (alexandria:Trade_node {name:"alexandria"}), (ALB:Country {name:"ALB"}) CREATE (ALB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.042,calculated_trading_power: 2.042}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (ATH:Country {name:"ATH"}) CREATE (ATH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.992,calculated_trading_power: 1.992}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (BYZ:Country {name:"BYZ"}) CREATE (BYZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.049,calculated_trading_power: 9.049}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (CRO:Country {name:"CRO"}) CREATE (CRO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.992,calculated_trading_power: 1.992}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (EPI:Country {name:"EPI"}) CREATE (EPI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.024,calculated_trading_power: 2.024}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (KNI:Country {name:"KNI"}) CREATE (KNI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (NAX:Country {name:"NAX"}) CREATE (NAX)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.992,calculated_trading_power: 1.992}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (RAG:Country {name:"RAG"}) CREATE (RAG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.228,calculated_trading_power: 2.228}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.684,calculated_trading_power: 17.684}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (PRO:Country {name:"PRO"}) CREATE (PRO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.532,calculated_trading_power: 8.532}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (ARA:Country {name:"ARA"}) CREATE (ARA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.392,calculated_trading_power: 6.392}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (FER:Country {name:"FER"}) CREATE (FER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.319,calculated_trading_power: 6.319}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.172,calculated_trading_power: 14.172}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.156,calculated_trading_power: 2.156}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (MLO:Country {name:"MLO"}) CREATE (MLO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.804,calculated_trading_power: 5.804}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (NAP:Country {name:"NAP"}) CREATE (NAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.622,calculated_trading_power: 10.622}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.352,calculated_trading_power: 6.352}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.028,calculated_trading_power: 2.028}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (SIE:Country {name:"SIE"}) CREATE (SIE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (URB:Country {name:"URB"}) CREATE (URB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.116,calculated_trading_power: 4.116}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.696,calculated_trading_power: 24.696}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.782,calculated_trading_power: 4.782}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (LAN:Country {name:"LAN"}) CREATE (LAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.263,calculated_trading_power: 10.263}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (ANZ:Country {name:"ANZ"}) CREATE (ANZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.373,calculated_trading_power: 1.373}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 33.074,calculated_trading_power: 33.074}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (MAK:Country {name:"MAK"}) CREATE (MAK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.158,calculated_trading_power: 10.158}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (MDA:Country {name:"MDA"}) CREATE (MDA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.512,calculated_trading_power: 15.512}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (CND:Country {name:"CND"}) CREATE (CND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.009,calculated_trading_power: 6.009}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (MAM:Country {name:"MAM"}) CREATE (MAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 130.476,calculated_trading_power: 130.476}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (BEJ:Country {name:"BEJ"}) CREATE (BEJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.339,calculated_trading_power: 1.339}]->(alexandria);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (MOL:Country {name:"MOL"}) CREATE (MOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.925,calculated_trading_power: 4.925}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (LIT:Country {name:"LIT"}) CREATE (LIT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.026,calculated_trading_power: 3.026}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.291,calculated_trading_power: 8.291}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (CRI:Country {name:"CRI"}) CREATE (CRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 30.65,calculated_trading_power: 30.65}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (GEO:Country {name:"GEO"}) CREATE (GEO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.041,calculated_trading_power: 15.041}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (KAZ:Country {name:"KAZ"}) CREATE (KAZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 49.014,calculated_trading_power: 49.014}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (MOS:Country {name:"MOS"}) CREATE (MOS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.858,calculated_trading_power: 4.858}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (NOG:Country {name:"NOG"}) CREATE (NOG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 52.706,calculated_trading_power: 52.706}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (GOL:Country {name:"GOL"}) CREATE (GOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 108.684,calculated_trading_power: 108.684}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (SRV:Country {name:"SRV"}) CREATE (SRV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.91,calculated_trading_power: 1.91}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (TRE:Country {name:"TRE"}) CREATE (TRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.26,calculated_trading_power: 8.26}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (AVR:Country {name:"AVR"}) CREATE (AVR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.037,calculated_trading_power: 12.037}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (SME:Country {name:"SME"}) CREATE (SME)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.413,calculated_trading_power: 7.413}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (CIR:Country {name:"CIR"}) CREATE (CIR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.761,calculated_trading_power: 8.761}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (GAZ:Country {name:"GAZ"}) CREATE (GAZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.968,calculated_trading_power: 17.968}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (IME:Country {name:"IME"}) CREATE (IME)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.855,calculated_trading_power: 14.855}]->(astrakhan);
MATCH (crimea:Trade_node {name:"crimea"}), (BYZ:Country {name:"BYZ"}) CREATE (BYZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.029,calculated_trading_power: 7.029}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (EPI:Country {name:"EPI"}) CREATE (EPI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.024,calculated_trading_power: 2.024}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (MOL:Country {name:"MOL"}) CREATE (MOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.728,calculated_trading_power: 35.728}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (SER:Country {name:"SER"}) CREATE (SER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (WAL:Country {name:"WAL"}) CREATE (WAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 21.127,calculated_trading_power: 21.127}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (HUN:Country {name:"HUN"}) CREATE (HUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.859,calculated_trading_power: 12.859}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 22.206,calculated_trading_power: 22.206}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (LIT:Country {name:"LIT"}) CREATE (LIT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 85.019,calculated_trading_power: 85.019}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (MAZ:Country {name:"MAZ"}) CREATE (MAZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.232,calculated_trading_power: 14.232}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (OKA:Country {name:"OKA"}) CREATE (OKA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.045,calculated_trading_power: 5.045}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 21.767,calculated_trading_power: 21.767}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (CRI:Country {name:"CRI"}) CREATE (CRI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 34.352,calculated_trading_power: 34.352}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (GEO:Country {name:"GEO"}) CREATE (GEO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.619,calculated_trading_power: 25.619}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (RYA:Country {name:"RYA"}) CREATE (RYA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.452,calculated_trading_power: 13.452}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (FEO:Country {name:"FEO"}) CREATE (FEO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.093,calculated_trading_power: 15.093}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (AKK:Country {name:"AKK"}) CREATE (AKK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.692,calculated_trading_power: 4.692}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (CND:Country {name:"CND"}) CREATE (CND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.987,calculated_trading_power: 3.987}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (TRE:Country {name:"TRE"}) CREATE (TRE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.207,calculated_trading_power: 24.207}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (SME:Country {name:"SME"}) CREATE (SME)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.66,calculated_trading_power: 10.66}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (CIR:Country {name:"CIR"}) CREATE (CIR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.887,calculated_trading_power: 17.887}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (IME:Country {name:"IME"}) CREATE (IME)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.042,calculated_trading_power: 29.042}]->(crimea);
MATCH (constantinople:Trade_node {name:"constantinople"}), (ALB:Country {name:"ALB"}) CREATE (ALB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.042,calculated_trading_power: 2.042}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (ATH:Country {name:"ATH"}) CREATE (ATH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.071,calculated_trading_power: 4.071}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (BOS:Country {name:"BOS"}) CREATE (BOS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.024,calculated_trading_power: 2.024}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (BYZ:Country {name:"BYZ"}) CREATE (BYZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 52.998,calculated_trading_power: 52.998}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (CRO:Country {name:"CRO"}) CREATE (CRO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.685,calculated_trading_power: 4.685}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (EPI:Country {name:"EPI"}) CREATE (EPI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.024,calculated_trading_power: 2.024}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (KNI:Country {name:"KNI"}) CREATE (KNI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.737,calculated_trading_power: 14.737}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (NAX:Country {name:"NAX"}) CREATE (NAX)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.453,calculated_trading_power: 10.453}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (RAG:Country {name:"RAG"}) CREATE (RAG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.218,calculated_trading_power: 7.218}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (SER:Country {name:"SER"}) CREATE (SER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.301,calculated_trading_power: 4.301}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 141.305,calculated_trading_power: 141.305}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.474,calculated_trading_power: 3.474}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.929,calculated_trading_power: 7.929}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (CND:Country {name:"CND"}) CREATE (CND)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 34.779,calculated_trading_power: 34.779}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (HRZ:Country {name:"HRZ"}) CREATE (HRZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(constantinople);
MATCH (kiev:Trade_node {name:"kiev"}), (LIT:Country {name:"LIT"}) CREATE (LIT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 145.856,calculated_trading_power: 145.856}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (MAZ:Country {name:"MAZ"}) CREATE (MAZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.162,calculated_trading_power: 17.162}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (POL:Country {name:"POL"}) CREATE (POL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 69.923,calculated_trading_power: 69.923}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (OKA:Country {name:"OKA"}) CREATE (OKA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.485,calculated_trading_power: 13.485}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (MOS:Country {name:"MOS"}) CREATE (MOS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 64.386,calculated_trading_power: 64.386}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (NOV:Country {name:"NOV"}) CREATE (NOV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 61.279,calculated_trading_power: 61.279}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (PSK:Country {name:"PSK"}) CREATE (PSK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.278,calculated_trading_power: 12.278}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (RYA:Country {name:"RYA"}) CREATE (RYA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.916,calculated_trading_power: 16.916}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (TVE:Country {name:"TVE"}) CREATE (TVE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.752,calculated_trading_power: 9.752}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (YAR:Country {name:"YAR"}) CREATE (YAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.001,calculated_trading_power: 2.001}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (RSO:Country {name:"RSO"}) CREATE (RSO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.633,calculated_trading_power: 4.633}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (GOL:Country {name:"GOL"}) CREATE (GOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.948,calculated_trading_power: 1.948}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (OPL:Country {name:"OPL"}) CREATE (OPL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.172,calculated_trading_power: 14.172}]->(kiev);
MATCH (kazan:Trade_node {name:"kazan"}), (KAZ:Country {name:"KAZ"}) CREATE (KAZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 86.23,calculated_trading_power: 86.23}]->(kazan);
MATCH (kazan:Trade_node {name:"kazan"}), (MOS:Country {name:"MOS"}) CREATE (MOS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 33.738,calculated_trading_power: 33.738}]->(kazan);
MATCH (kazan:Trade_node {name:"kazan"}), (NOV:Country {name:"NOV"}) CREATE (NOV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.973,calculated_trading_power: 14.973}]->(kazan);
MATCH (kazan:Trade_node {name:"kazan"}), (PSK:Country {name:"PSK"}) CREATE (PSK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.681,calculated_trading_power: 2.681}]->(kazan);
MATCH (kazan:Trade_node {name:"kazan"}), (YAR:Country {name:"YAR"}) CREATE (YAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.614,calculated_trading_power: 9.614}]->(kazan);
MATCH (kazan:Trade_node {name:"kazan"}), (NOG:Country {name:"NOG"}) CREATE (NOG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.318,calculated_trading_power: 6.318}]->(kazan);
MATCH (kazan:Trade_node {name:"kazan"}), (PRM:Country {name:"PRM"}) CREATE (PRM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.362,calculated_trading_power: 1.362}]->(kazan);
MATCH (novgorod:Trade_node {name:"novgorod"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.795,calculated_trading_power: 11.795}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (GOT:Country {name:"GOT"}) CREATE (GOT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.481,calculated_trading_power: 4.481}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (NOR:Country {name:"NOR"}) CREATE (NOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.992,calculated_trading_power: 1.992}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (LIT:Country {name:"LIT"}) CREATE (LIT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.445,calculated_trading_power: 2.445}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (LIV:Country {name:"LIV"}) CREATE (LIV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (RIG:Country {name:"RIG"}) CREATE (RIG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.036,calculated_trading_power: 7.036}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (TEU:Country {name:"TEU"}) CREATE (TEU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.215,calculated_trading_power: 16.215}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (MOS:Country {name:"MOS"}) CREATE (MOS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 59.676,calculated_trading_power: 59.676}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (NOV:Country {name:"NOV"}) CREATE (NOV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 96.027,calculated_trading_power: 96.027}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (PSK:Country {name:"PSK"}) CREATE (PSK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.406,calculated_trading_power: 22.406}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (RYA:Country {name:"RYA"}) CREATE (RYA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.558,calculated_trading_power: 11.558}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (TVE:Country {name:"TVE"}) CREATE (TVE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.649,calculated_trading_power: 14.649}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (YAR:Country {name:"YAR"}) CREATE (YAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.668,calculated_trading_power: 18.668}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (PRM:Country {name:"PRM"}) CREATE (PRM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.288,calculated_trading_power: 2.288}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (BLO:Country {name:"BLO"}) CREATE (BLO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.81,calculated_trading_power: 9.81}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (RSO:Country {name:"RSO"}) CREATE (RSO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.924,calculated_trading_power: 9.924}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.077,calculated_trading_power: 7.077}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(novgorod);
MATCH (laplata:Trade_node {name:"laplata"}), (GUA:Country {name:"GUA"}) CREATE (GUA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.499,calculated_trading_power: 8.499}]->(laplata);
MATCH (laplata:Trade_node {name:"laplata"}), (CUA:Country {name:"CUA"}) CREATE (CUA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 31.871,calculated_trading_power: 31.871}]->(laplata);
MATCH (brazil:Trade_node {name:"brazil"}), (PTG:Country {name:"PTG"}) CREATE (PTG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.978,calculated_trading_power: 16.978}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (TPQ:Country {name:"TPQ"}) CREATE (TPQ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.989,calculated_trading_power: 9.989}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (TUA:Country {name:"TUA"}) CREATE (TUA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.696,calculated_trading_power: 9.696}]->(brazil);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.406,calculated_trading_power: 4.406}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (TUN:Country {name:"TUN"}) CREATE (TUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.161,calculated_trading_power: 2.161}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (TFL:Country {name:"TFL"}) CREATE (TFL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.425,calculated_trading_power: 4.425}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (SOS:Country {name:"SOS"}) CREATE (SOS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.861,calculated_trading_power: 2.861}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (TLC:Country {name:"TLC"}) CREATE (TLC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.392,calculated_trading_power: 5.392}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.743,calculated_trading_power: 5.743}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (KON:Country {name:"KON"}) CREATE (KON)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.247,calculated_trading_power: 4.247}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (MAL:Country {name:"MAL"}) CREATE (MAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 70.284,calculated_trading_power: 70.284}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (SON:Country {name:"SON"}) CREATE (SON)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 48.129,calculated_trading_power: 48.129}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (OYO:Country {name:"OYO"}) CREATE (OYO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.983,calculated_trading_power: 1.983}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.25,calculated_trading_power: 14.25}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (WGD:Country {name:"WGD"}) CREATE (WGD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.871,calculated_trading_power: 19.871}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (GUR:Country {name:"GUR"}) CREATE (GUR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.578,calculated_trading_power: 11.578}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (BON:Country {name:"BON"}) CREATE (BON)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.392,calculated_trading_power: 15.392}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (DAH:Country {name:"DAH"}) CREATE (DAH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.779,calculated_trading_power: 10.779}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (DGB:Country {name:"DGB"}) CREATE (DGB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.665,calculated_trading_power: 11.665}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (JNN:Country {name:"JNN"}) CREATE (JNN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 53.88,calculated_trading_power: 53.88}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (KNG:Country {name:"KNG"}) CREATE (KNG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.541,calculated_trading_power: 16.541}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (TMB:Country {name:"TMB"}) CREATE (TMB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 45.873,calculated_trading_power: 45.873}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (YAT:Country {name:"YAT"}) CREATE (YAT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.561,calculated_trading_power: 18.561}]->(timbuktu);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 49.905,calculated_trading_power: 49.905}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (KON:Country {name:"KON"}) CREATE (KON)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 21.237,calculated_trading_power: 21.237}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (MAL:Country {name:"MAL"}) CREATE (MAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.739,calculated_trading_power: 8.739}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (LOA:Country {name:"LOA"}) CREATE (LOA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.926,calculated_trading_power: 18.926}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 46.155,calculated_trading_power: 46.155}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (NDO:Country {name:"NDO"}) CREATE (NDO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.045,calculated_trading_power: 16.045}]->(ivory_coast);
MATCH (tunis:Trade_node {name:"tunis"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.362,calculated_trading_power: 3.362}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (PRO:Country {name:"PRO"}) CREATE (PRO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.532,calculated_trading_power: 6.532}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (ARA:Country {name:"ARA"}) CREATE (ARA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.385,calculated_trading_power: 18.385}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (CAS:Country {name:"CAS"}) CREATE (CAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 27.293,calculated_trading_power: 27.293}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (GRA:Country {name:"GRA"}) CREATE (GRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.328,calculated_trading_power: 4.328}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 19.929,calculated_trading_power: 19.929}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.138,calculated_trading_power: 12.138}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (MLO:Country {name:"MLO"}) CREATE (MLO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.804,calculated_trading_power: 5.804}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (NAP:Country {name:"NAP"}) CREATE (NAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.159,calculated_trading_power: 6.159}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.27,calculated_trading_power: 4.27}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (MFA:Country {name:"MFA"}) CREATE (MFA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.76,calculated_trading_power: 2.76}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (LAN:Country {name:"LAN"}) CREATE (LAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.269,calculated_trading_power: 8.269}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.754,calculated_trading_power: 4.754}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (TUN:Country {name:"TUN"}) CREATE (TUN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 70.856,calculated_trading_power: 70.856}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (GHD:Country {name:"GHD"}) CREATE (GHD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.129,calculated_trading_power: 9.129}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (FZA:Country {name:"FZA"}) CREATE (FZA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.856,calculated_trading_power: 13.856}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (MZB:Country {name:"MZB"}) CREATE (MZB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.563,calculated_trading_power: 8.563}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (SZO:Country {name:"SZO"}) CREATE (SZO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (PGA:Country {name:"PGA"}) CREATE (PGA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(tunis);
MATCH (ragusa:Trade_node {name:"ragusa"}), (ALB:Country {name:"ALB"}) CREATE (ALB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.565,calculated_trading_power: 14.565}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (ATH:Country {name:"ATH"}) CREATE (ATH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.858,calculated_trading_power: 20.858}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (BOS:Country {name:"BOS"}) CREATE (BOS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.655,calculated_trading_power: 13.655}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (BYZ:Country {name:"BYZ"}) CREATE (BYZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.229,calculated_trading_power: 5.229}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (CRO:Country {name:"CRO"}) CREATE (CRO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.541,calculated_trading_power: 24.541}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (EPI:Country {name:"EPI"}) CREATE (EPI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.323,calculated_trading_power: 13.323}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (RAG:Country {name:"RAG"}) CREATE (RAG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 45.403,calculated_trading_power: 45.403}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (SER:Country {name:"SER"}) CREATE (SER)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.709,calculated_trading_power: 19.709}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (WAL:Country {name:"WAL"}) CREATE (WAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 21.127,calculated_trading_power: 21.127}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (HUN:Country {name:"HUN"}) CREATE (HUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 74.037,calculated_trading_power: 74.037}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 31.449,calculated_trading_power: 31.449}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (PRO:Country {name:"PRO"}) CREATE (PRO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.532,calculated_trading_power: 6.532}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (AUG:Country {name:"AUG"}) CREATE (AUG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.75,calculated_trading_power: 7.75}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (MEM:Country {name:"MEM"}) CREATE (MEM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.56,calculated_trading_power: 6.56}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (ARA:Country {name:"ARA"}) CREATE (ARA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.392,calculated_trading_power: 6.392}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (FER:Country {name:"FER"}) CREATE (FER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.319,calculated_trading_power: 6.319}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.138,calculated_trading_power: 12.138}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.198,calculated_trading_power: 4.198}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (MLO:Country {name:"MLO"}) CREATE (MLO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.826,calculated_trading_power: 7.826}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (NAP:Country {name:"NAP"}) CREATE (NAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.636,calculated_trading_power: 8.636}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.352,calculated_trading_power: 6.352}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (SIE:Country {name:"SIE"}) CREATE (SIE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (URB:Country {name:"URB"}) CREATE (URB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.116,calculated_trading_power: 4.116}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 45.939,calculated_trading_power: 45.939}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (MFA:Country {name:"MFA"}) CREATE (MFA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.782,calculated_trading_power: 4.782}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (LAN:Country {name:"LAN"}) CREATE (LAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.269,calculated_trading_power: 8.269}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (LBV:Country {name:"LBV"}) CREATE (LBV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.132,calculated_trading_power: 12.132}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (ING:Country {name:"ING"}) CREATE (ING)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.446,calculated_trading_power: 10.446}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (GLG:Country {name:"GLG"}) CREATE (GLG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.283,calculated_trading_power: 8.283}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (BLG:Country {name:"BLG"}) CREATE (BLG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (SZO:Country {name:"SZO"}) CREATE (SZO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (CLI:Country {name:"CLI"}) CREATE (CLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.994,calculated_trading_power: 1.994}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (HRZ:Country {name:"HRZ"}) CREATE (HRZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.756,calculated_trading_power: 11.756}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (TNT:Country {name:"TNT"}) CREATE (TNT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.698,calculated_trading_power: 4.698}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (PGA:Country {name:"PGA"}) CREATE (PGA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(ragusa);
MATCH (safi:Trade_node {name:"safi"}), (CAS:Country {name:"CAS"}) CREATE (CAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 27.293,calculated_trading_power: 27.293}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (GRA:Country {name:"GRA"}) CREATE (GRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.324,calculated_trading_power: 2.324}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.907,calculated_trading_power: 17.907}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 44.026,calculated_trading_power: 44.026}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (TUN:Country {name:"TUN"}) CREATE (TUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.422,calculated_trading_power: 6.422}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (TFL:Country {name:"TFL"}) CREATE (TFL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.083,calculated_trading_power: 29.083}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (SOS:Country {name:"SOS"}) CREATE (SOS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.23,calculated_trading_power: 27.23}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (TLC:Country {name:"TLC"}) CREATE (TLC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.064,calculated_trading_power: 36.064}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (MRK:Country {name:"MRK"}) CREATE (MRK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.992,calculated_trading_power: 15.992}]->(safi);
MATCH (pest:Trade_node {name:"pest"}), (CRO:Country {name:"CRO"}) CREATE (CRO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.288,calculated_trading_power: 4.288}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (WAL:Country {name:"WAL"}) CREATE (WAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 45.517,calculated_trading_power: 45.517}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (HUN:Country {name:"HUN"}) CREATE (HUN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 134.293,calculated_trading_power: 134.293}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (MAZ:Country {name:"MAZ"}) CREATE (MAZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.929,calculated_trading_power: 2.929}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (POL:Country {name:"POL"}) CREATE (POL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.556,calculated_trading_power: 14.556}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (AUG:Country {name:"AUG"}) CREATE (AUG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.75,calculated_trading_power: 7.75}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 62.891,calculated_trading_power: 62.891}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (SLZ:Country {name:"SLZ"}) CREATE (SLZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.739,calculated_trading_power: 6.739}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (ULM:Country {name:"ULM"}) CREATE (ULM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.644,calculated_trading_power: 9.644}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (WUR:Country {name:"WUR"}) CREATE (WUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.761,calculated_trading_power: 8.761}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (MEM:Country {name:"MEM"}) CREATE (MEM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.56,calculated_trading_power: 6.56}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (UBV:Country {name:"UBV"}) CREATE (UBV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.11,calculated_trading_power: 10.11}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (LBV:Country {name:"LBV"}) CREATE (LBV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.132,calculated_trading_power: 12.132}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (ING:Country {name:"ING"}) CREATE (ING)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.446,calculated_trading_power: 10.446}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (PSS:Country {name:"PSS"}) CREATE (PSS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.586,calculated_trading_power: 5.586}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (MBZ:Country {name:"MBZ"}) CREATE (MBZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (KNZ:Country {name:"KNZ"}) CREATE (KNZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.707,calculated_trading_power: 8.707}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (REG:Country {name:"REG"}) CREATE (REG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.821,calculated_trading_power: 3.821}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (OPL:Country {name:"OPL"}) CREATE (OPL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.172,calculated_trading_power: 14.172}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (GLG:Country {name:"GLG"}) CREATE (GLG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.283,calculated_trading_power: 8.283}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (TNT:Country {name:"TNT"}) CREATE (TNT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.698,calculated_trading_power: 4.698}]->(pest);
MATCH (krakow:Trade_node {name:"krakow"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.98,calculated_trading_power: 7.98}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (GOT:Country {name:"GOT"}) CREATE (GOT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.467,calculated_trading_power: 2.467}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (LIV:Country {name:"LIV"}) CREATE (LIV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.372,calculated_trading_power: 4.372}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (MAZ:Country {name:"MAZ"}) CREATE (MAZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 38.277,calculated_trading_power: 38.277}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (POL:Country {name:"POL"}) CREATE (POL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 143.465,calculated_trading_power: 143.465}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (RIG:Country {name:"RIG"}) CREATE (RIG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.022,calculated_trading_power: 5.022}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (TEU:Country {name:"TEU"}) CREATE (TEU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 22.268,calculated_trading_power: 22.268}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (ANH:Country {name:"ANH"}) CREATE (ANH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (BOH:Country {name:"BOH"}) CREATE (BOH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.191,calculated_trading_power: 7.191}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (BRA:Country {name:"BRA"}) CREATE (BRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 28.025,calculated_trading_power: 28.025}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (BRU:Country {name:"BRU"}) CREATE (BRU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.805,calculated_trading_power: 12.805}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 62.891,calculated_trading_power: 62.891}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (MAG:Country {name:"MAG"}) CREATE (MAG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.999,calculated_trading_power: 10.999}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (SAX:Country {name:"SAX"}) CREATE (SAX)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 19.041,calculated_trading_power: 19.041}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (SLZ:Country {name:"SLZ"}) CREATE (SLZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.739,calculated_trading_power: 6.739}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.225,calculated_trading_power: 16.225}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (ULM:Country {name:"ULM"}) CREATE (ULM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.644,calculated_trading_power: 9.644}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (WUR:Country {name:"WUR"}) CREATE (WUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.761,calculated_trading_power: 8.761}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (UBV:Country {name:"UBV"}) CREATE (UBV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.11,calculated_trading_power: 10.11}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (PSS:Country {name:"PSS"}) CREATE (PSS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.586,calculated_trading_power: 5.586}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (MBZ:Country {name:"MBZ"}) CREATE (MBZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (KNZ:Country {name:"KNZ"}) CREATE (KNZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.707,calculated_trading_power: 8.707}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (REG:Country {name:"REG"}) CREATE (REG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.224,calculated_trading_power: 10.224}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (OPL:Country {name:"OPL"}) CREATE (OPL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.985,calculated_trading_power: 32.985}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (GLG:Country {name:"GLG"}) CREATE (GLG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.976,calculated_trading_power: 18.976}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (GOS:Country {name:"GOS"}) CREATE (GOS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.728,calculated_trading_power: 5.728}]->(krakow);
MATCH (wien:Trade_node {name:"wien"}), (AAC:Country {name:"AAC"}) CREATE (AAC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.692,calculated_trading_power: 6.692}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ANH:Country {name:"ANH"}) CREATE (ANH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ANS:Country {name:"ANS"}) CREATE (ANS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (AUG:Country {name:"AUG"}) CREATE (AUG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.857,calculated_trading_power: 17.857}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BAD:Country {name:"BAD"}) CREATE (BAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.743,calculated_trading_power: 7.743}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BOH:Country {name:"BOH"}) CREATE (BOH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 54.529,calculated_trading_power: 54.529}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BRA:Country {name:"BRA"}) CREATE (BRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 28.025,calculated_trading_power: 28.025}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BRU:Country {name:"BRU"}) CREATE (BRU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.805,calculated_trading_power: 12.805}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (FRN:Country {name:"FRN"}) CREATE (FRN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.024,calculated_trading_power: 14.024}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 119.68,calculated_trading_power: 119.68}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (HES:Country {name:"HES"}) CREATE (HES)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.772,calculated_trading_power: 9.772}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (KLE:Country {name:"KLE"}) CREATE (KLE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.739,calculated_trading_power: 6.739}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (KOL:Country {name:"KOL"}) CREATE (KOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.841,calculated_trading_power: 14.841}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MAG:Country {name:"MAG"}) CREATE (MAG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.999,calculated_trading_power: 10.999}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MAI:Country {name:"MAI"}) CREATE (MAI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.398,calculated_trading_power: 9.398}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MUN:Country {name:"MUN"}) CREATE (MUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.59,calculated_trading_power: 12.59}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (PAL:Country {name:"PAL"}) CREATE (PAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.684,calculated_trading_power: 17.684}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (SAX:Country {name:"SAX"}) CREATE (SAX)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 19.041,calculated_trading_power: 19.041}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (SLZ:Country {name:"SLZ"}) CREATE (SLZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.04,calculated_trading_power: 23.04}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.225,calculated_trading_power: 16.225}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (TRI:Country {name:"TRI"}) CREATE (TRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.435,calculated_trading_power: 9.435}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ULM:Country {name:"ULM"}) CREATE (ULM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.972,calculated_trading_power: 26.972}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (WBG:Country {name:"WBG"}) CREATE (WBG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.376,calculated_trading_power: 7.376}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (WUR:Country {name:"WUR"}) CREATE (WUR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.94,calculated_trading_power: 19.94}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (NUM:Country {name:"NUM"}) CREATE (NUM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.61,calculated_trading_power: 12.61}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MEM:Country {name:"MEM"}) CREATE (MEM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.355,calculated_trading_power: 16.355}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (NSA:Country {name:"NSA"}) CREATE (NSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.055,calculated_trading_power: 5.055}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (RVA:Country {name:"RVA"}) CREATE (RVA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.195,calculated_trading_power: 8.195}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (FER:Country {name:"FER"}) CREATE (FER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.307,calculated_trading_power: 4.307}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.664,calculated_trading_power: 10.664}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (NAP:Country {name:"NAP"}) CREATE (NAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.476,calculated_trading_power: 2.476}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (URB:Country {name:"URB"}) CREATE (URB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.13,calculated_trading_power: 2.13}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 74.232,calculated_trading_power: 74.232}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (UBV:Country {name:"UBV"}) CREATE (UBV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.718,calculated_trading_power: 22.718}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (LBV:Country {name:"LBV"}) CREATE (LBV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.884,calculated_trading_power: 26.884}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ING:Country {name:"ING"}) CREATE (ING)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.308,calculated_trading_power: 23.308}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (PSS:Country {name:"PSS"}) CREATE (PSS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.408,calculated_trading_power: 13.408}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MBZ:Country {name:"MBZ"}) CREATE (MBZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.93,calculated_trading_power: 9.93}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (KNZ:Country {name:"KNZ"}) CREATE (KNZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.107,calculated_trading_power: 25.107}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ROT:Country {name:"ROT"}) CREATE (ROT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.391,calculated_trading_power: 5.391}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BYT:Country {name:"BYT"}) CREATE (BYT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (REG:Country {name:"REG"}) CREATE (REG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 33.588,calculated_trading_power: 33.588}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (TTL:Country {name:"TTL"}) CREATE (TTL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BLG:Country {name:"BLG"}) CREATE (BLG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.066,calculated_trading_power: 6.066}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (GOS:Country {name:"GOS"}) CREATE (GOS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.728,calculated_trading_power: 5.728}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (CLI:Country {name:"CLI"}) CREATE (CLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.835,calculated_trading_power: 4.835}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (TNT:Country {name:"TNT"}) CREATE (TNT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.57,calculated_trading_power: 11.57}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BRG:Country {name:"BRG"}) CREATE (BRG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.435,calculated_trading_power: 9.435}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MLH:Country {name:"MLH"}) CREATE (MLH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BAM:Country {name:"BAM"}) CREATE (BAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.016,calculated_trading_power: 4.016}]->(wien);
MATCH (saxony:Trade_node {name:"saxony"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.105,calculated_trading_power: 2.105}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 58.968,calculated_trading_power: 58.968}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (NOR:Country {name:"NOR"}) CREATE (NOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.147,calculated_trading_power: 3.147}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.548,calculated_trading_power: 11.548}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (TEU:Country {name:"TEU"}) CREATE (TEU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.173,calculated_trading_power: 2.173}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (ALS:Country {name:"ALS"}) CREATE (ALS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.523,calculated_trading_power: 7.523}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (AAC:Country {name:"AAC"}) CREATE (AAC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.692,calculated_trading_power: 6.692}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (ANH:Country {name:"ANH"}) CREATE (ANH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.203,calculated_trading_power: 10.203}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (ANS:Country {name:"ANS"}) CREATE (ANS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BAD:Country {name:"BAD"}) CREATE (BAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.743,calculated_trading_power: 7.743}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BOH:Country {name:"BOH"}) CREATE (BOH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 91.488,calculated_trading_power: 91.488}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BRA:Country {name:"BRA"}) CREATE (BRA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 56.845,calculated_trading_power: 56.845}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.44,calculated_trading_power: 6.44}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BRU:Country {name:"BRU"}) CREATE (BRU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.153,calculated_trading_power: 28.153}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (FRN:Country {name:"FRN"}) CREATE (FRN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.024,calculated_trading_power: 14.024}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.421,calculated_trading_power: 8.421}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (HES:Country {name:"HES"}) CREATE (HES)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.772,calculated_trading_power: 9.772}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (KLE:Country {name:"KLE"}) CREATE (KLE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.739,calculated_trading_power: 6.739}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (KOL:Country {name:"KOL"}) CREATE (KOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.841,calculated_trading_power: 14.841}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (MAG:Country {name:"MAG"}) CREATE (MAG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.378,calculated_trading_power: 32.378}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (MAI:Country {name:"MAI"}) CREATE (MAI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.398,calculated_trading_power: 9.398}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (MUN:Country {name:"MUN"}) CREATE (MUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.59,calculated_trading_power: 12.59}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (PAL:Country {name:"PAL"}) CREATE (PAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.547,calculated_trading_power: 15.547}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (SAX:Country {name:"SAX"}) CREATE (SAX)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 41.786,calculated_trading_power: 41.786}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.834,calculated_trading_power: 35.834}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (TRI:Country {name:"TRI"}) CREATE (TRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.435,calculated_trading_power: 9.435}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (WBG:Country {name:"WBG"}) CREATE (WBG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.376,calculated_trading_power: 7.376}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (NUM:Country {name:"NUM"}) CREATE (NUM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.61,calculated_trading_power: 12.61}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (NSA:Country {name:"NSA"}) CREATE (NSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.055,calculated_trading_power: 5.055}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (RVA:Country {name:"RVA"}) CREATE (RVA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.195,calculated_trading_power: 8.195}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.741,calculated_trading_power: 4.741}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (ROT:Country {name:"ROT"}) CREATE (ROT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.391,calculated_trading_power: 5.391}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BYT:Country {name:"BYT"}) CREATE (BYT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (TTL:Country {name:"TTL"}) CREATE (TTL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (WOL:Country {name:"WOL"}) CREATE (WOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.563,calculated_trading_power: 14.563}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.163,calculated_trading_power: 4.163}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (GOS:Country {name:"GOS"}) CREATE (GOS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.49,calculated_trading_power: 14.49}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BRG:Country {name:"BRG"}) CREATE (BRG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.435,calculated_trading_power: 9.435}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (MLH:Country {name:"MLH"}) CREATE (MLH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BAM:Country {name:"BAM"}) CREATE (BAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.016,calculated_trading_power: 4.016}]->(saxony);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 56.092,calculated_trading_power: 56.092}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.18,calculated_trading_power: 17.18}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (GOT:Country {name:"GOT"}) CREATE (GOT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.737,calculated_trading_power: 25.737}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (NOR:Country {name:"NOR"}) CREATE (NOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.147,calculated_trading_power: 3.147}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.611,calculated_trading_power: 2.611}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (LIT:Country {name:"LIT"}) CREATE (LIT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.102,calculated_trading_power: 6.102}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (LIV:Country {name:"LIV"}) CREATE (LIV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.286,calculated_trading_power: 36.286}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (RIG:Country {name:"RIG"}) CREATE (RIG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 39.785,calculated_trading_power: 39.785}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (TEU:Country {name:"TEU"}) CREATE (TEU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 90.207,calculated_trading_power: 90.207}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.44,calculated_trading_power: 6.44}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.421,calculated_trading_power: 8.421}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (OLD:Country {name:"OLD"}) CREATE (OLD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.026,calculated_trading_power: 2.026}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (DTT:Country {name:"DTT"}) CREATE (DTT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.741,calculated_trading_power: 4.741}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (WOL:Country {name:"WOL"}) CREATE (WOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.105,calculated_trading_power: 3.105}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.163,calculated_trading_power: 4.163}]->(baltic_sea);
MATCH (rheinland:Trade_node {name:"rheinland"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.105,calculated_trading_power: 2.105}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.18,calculated_trading_power: 17.18}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (NOR:Country {name:"NOR"}) CREATE (NOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.147,calculated_trading_power: 3.147}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.611,calculated_trading_power: 2.611}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (ALS:Country {name:"ALS"}) CREATE (ALS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.966,calculated_trading_power: 24.966}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BUR:Country {name:"BUR"}) CREATE (BUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.429,calculated_trading_power: 7.429}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 69.179,calculated_trading_power: 69.179}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (NEV:Country {name:"NEV"}) CREATE (NEV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.923,calculated_trading_power: 10.923}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (ORL:Country {name:"ORL"}) CREATE (ORL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.946,calculated_trading_power: 23.946}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (AAC:Country {name:"AAC"}) CREATE (AAC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.694,calculated_trading_power: 16.694}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (ANS:Country {name:"ANS"}) CREATE (ANS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.608,calculated_trading_power: 11.608}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BAD:Country {name:"BAD"}) CREATE (BAD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.098,calculated_trading_power: 17.098}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.44,calculated_trading_power: 6.44}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (FRN:Country {name:"FRN"}) CREATE (FRN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 42.735,calculated_trading_power: 42.735}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.927,calculated_trading_power: 4.927}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.421,calculated_trading_power: 8.421}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HES:Country {name:"HES"}) CREATE (HES)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.023,calculated_trading_power: 22.023}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (KLE:Country {name:"KLE"}) CREATE (KLE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.774,calculated_trading_power: 15.774}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (KOL:Country {name:"KOL"}) CREATE (KOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 39.241,calculated_trading_power: 39.241}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (LAU:Country {name:"LAU"}) CREATE (LAU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.016,calculated_trading_power: 4.016}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (LOR:Country {name:"LOR"}) CREATE (LOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.412,calculated_trading_power: 17.412}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (LUN:Country {name:"LUN"}) CREATE (LUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.739,calculated_trading_power: 6.739}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (MAI:Country {name:"MAI"}) CREATE (MAI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.525,calculated_trading_power: 24.525}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (MKL:Country {name:"MKL"}) CREATE (MKL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.11,calculated_trading_power: 10.11}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (MUN:Country {name:"MUN"}) CREATE (MUN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.209,calculated_trading_power: 27.209}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (PAL:Country {name:"PAL"}) CREATE (PAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 31.288,calculated_trading_power: 31.288}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 28.72,calculated_trading_power: 28.72}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.568,calculated_trading_power: 1.568}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (TRI:Country {name:"TRI"}) CREATE (TRI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.329,calculated_trading_power: 21.329}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (WBG:Country {name:"WBG"}) CREATE (WBG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.473,calculated_trading_power: 17.473}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (NUM:Country {name:"NUM"}) CREATE (NUM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 38.791,calculated_trading_power: 38.791}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (VER:Country {name:"VER"}) CREATE (VER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (NSA:Country {name:"NSA"}) CREATE (NSA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.303,calculated_trading_power: 12.303}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (RVA:Country {name:"RVA"}) CREATE (RVA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.059,calculated_trading_power: 24.059}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.741,calculated_trading_power: 4.741}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (ROT:Country {name:"ROT"}) CREATE (ROT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.724,calculated_trading_power: 13.724}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BYT:Country {name:"BYT"}) CREATE (BYT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.068,calculated_trading_power: 10.068}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (GNV:Country {name:"GNV"}) CREATE (GNV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.921,calculated_trading_power: 6.921}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (TTL:Country {name:"TTL"}) CREATE (TTL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.08,calculated_trading_power: 15.08}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (WOL:Country {name:"WOL"}) CREATE (WOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.105,calculated_trading_power: 3.105}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.163,calculated_trading_power: 4.163}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BRG:Country {name:"BRG"}) CREATE (BRG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.768,calculated_trading_power: 21.768}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (MLH:Country {name:"MLH"}) CREATE (MLH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.608,calculated_trading_power: 11.608}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BAM:Country {name:"BAM"}) CREATE (BAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.161,calculated_trading_power: 10.161}]->(rheinland);
MATCH (panama:Trade_node {name:"panama"}), (MCA:Country {name:"MCA"}) CREATE (MCA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.449,calculated_trading_power: 26.449}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (CHT:Country {name:"CHT"}) CREATE (CHT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 5.875,calculated_trading_power: 5.875}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (MIS:Country {name:"MIS"}) CREATE (MIS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 6.8,calculated_trading_power: 6.8}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (TAI:Country {name:"TAI"}) CREATE (TAI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.801,calculated_trading_power: 8.801}]->(panama);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (CAB:Country {name:"CAB"}) CREATE (CAB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.499,calculated_trading_power: 8.499}]->(carribean_trade);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (CHE:Country {name:"CHE"}) CREATE (CHE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.087,calculated_trading_power: 10.087}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ABE:Country {name:"ABE"}) CREATE (ABE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.868,calculated_trading_power: 8.868}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (LEN:Country {name:"LEN"}) CREATE (LEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.322,calculated_trading_power: 22.322}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (MAH:Country {name:"MAH"}) CREATE (MAH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (MIK:Country {name:"MIK"}) CREATE (MIK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (PEQ:Country {name:"PEQ"}) CREATE (PEQ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.715,calculated_trading_power: 9.715}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (POW:Country {name:"POW"}) CREATE (POW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.584,calculated_trading_power: 9.584}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ALT:Country {name:"ALT"}) CREATE (ALT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.376,calculated_trading_power: 9.376}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ICH:Country {name:"ICH"}) CREATE (ICH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (COF:Country {name:"COF"}) CREATE (COF)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.087,calculated_trading_power: 10.087}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (JOA:Country {name:"JOA"}) CREATE (JOA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.224,calculated_trading_power: 10.224}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ETO:Country {name:"ETO"}) CREATE (ETO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.51,calculated_trading_power: 9.51}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (SAT:Country {name:"SAT"}) CREATE (SAT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.51,calculated_trading_power: 9.51}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (CIA:Country {name:"CIA"}) CREATE (CIA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.91,calculated_trading_power: 9.91}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (COO:Country {name:"COO"}) CREATE (COO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.087,calculated_trading_power: 10.087}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (KSI:Country {name:"KSI"}) CREATE (KSI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.018,calculated_trading_power: 9.018}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (STA:Country {name:"STA"}) CREATE (STA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.694,calculated_trading_power: 3.694}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (TSC:Country {name:"TSC"}) CREATE (TSC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.179,calculated_trading_power: 9.179}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (PEN:Country {name:"PEN"}) CREATE (PEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (WAM:Country {name:"WAM"}) CREATE (WAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.018,calculated_trading_power: 9.018}]->(chesapeake_bay);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (MAH:Country {name:"MAH"}) CREATE (MAH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.443,calculated_trading_power: 10.443}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (MIK:Country {name:"MIK"}) CREATE (MIK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.335,calculated_trading_power: 10.335}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (OSH:Country {name:"OSH"}) CREATE (OSH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.533,calculated_trading_power: 15.533}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (STA:Country {name:"STA"}) CREATE (STA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.547,calculated_trading_power: 25.547}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (PEN:Country {name:"PEN"}) CREATE (PEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.354,calculated_trading_power: 10.354}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (MLS:Country {name:"MLS"}) CREATE (MLS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.654,calculated_trading_power: 8.654}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (INN:Country {name:"INN"}) CREATE (INN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (AGQ:Country {name:"AGQ"}) CREATE (AGQ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.442,calculated_trading_power: 9.442}]->(st_lawrence);
MATCH (white_sea:Trade_node {name:"white_sea"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 0.404,calculated_trading_power: 0.404}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (NOR:Country {name:"NOR"}) CREATE (NOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.692,calculated_trading_power: 7.692}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.759,calculated_trading_power: 2.759}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (SCO:Country {name:"SCO"}) CREATE (SCO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.908,calculated_trading_power: 8.908}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (MOS:Country {name:"MOS"}) CREATE (MOS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.672,calculated_trading_power: 4.672}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (NOV:Country {name:"NOV"}) CREATE (NOV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.877,calculated_trading_power: 15.877}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (PRM:Country {name:"PRM"}) CREATE (PRM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.395,calculated_trading_power: 18.395}]->(white_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.105,calculated_trading_power: 2.105}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.18,calculated_trading_power: 17.18}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (NOR:Country {name:"NOR"}) CREATE (NOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 40.911,calculated_trading_power: 40.911}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.597,calculated_trading_power: 4.597}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (CNN:Country {name:"CNN"}) CREATE (CNN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.99,calculated_trading_power: 22.99}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 51.979,calculated_trading_power: 51.979}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (LEI:Country {name:"LEI"}) CREATE (LEI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.99,calculated_trading_power: 22.99}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (MNS:Country {name:"MNS"}) CREATE (MNS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.576,calculated_trading_power: 23.576}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SCO:Country {name:"SCO"}) CREATE (SCO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 67.822,calculated_trading_power: 67.822}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (TYR:Country {name:"TYR"}) CREATE (TYR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.576,calculated_trading_power: 23.576}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (ULS:Country {name:"ULS"}) CREATE (ULS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.576,calculated_trading_power: 23.576}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (DMS:Country {name:"DMS"}) CREATE (DMS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.319,calculated_trading_power: 30.319}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SLN:Country {name:"SLN"}) CREATE (SLN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.598,calculated_trading_power: 25.598}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (KID:Country {name:"KID"}) CREATE (KID)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.26,calculated_trading_power: 9.26}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (ORD:Country {name:"ORD"}) CREATE (ORD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.775,calculated_trading_power: 8.775}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (TRY:Country {name:"TRY"}) CREATE (TRY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.696,calculated_trading_power: 22.696}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (FLY:Country {name:"FLY"}) CREATE (FLY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.29,calculated_trading_power: 8.29}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (MCM:Country {name:"MCM"}) CREATE (MCM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.625,calculated_trading_power: 22.625}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (LOI:Country {name:"LOI"}) CREATE (LOI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.627,calculated_trading_power: 14.627}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (BUR:Country {name:"BUR"}) CREATE (BUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.231,calculated_trading_power: 2.231}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.462,calculated_trading_power: 8.462}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.364,calculated_trading_power: 5.364}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.443,calculated_trading_power: 10.443}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (LAU:Country {name:"LAU"}) CREATE (LAU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.008,calculated_trading_power: 2.008}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (LUN:Country {name:"LUN"}) CREATE (LUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (MKL:Country {name:"MKL"}) CREATE (MKL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (OLD:Country {name:"OLD"}) CREATE (OLD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.026,calculated_trading_power: 2.026}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (VER:Country {name:"VER"}) CREATE (VER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (DTT:Country {name:"DTT"}) CREATE (DTT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (BRB:Country {name:"BRB"}) CREATE (BRB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.733,calculated_trading_power: 7.733}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (FLA:Country {name:"FLA"}) CREATE (FLA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.149,calculated_trading_power: 7.149}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (FRI:Country {name:"FRI"}) CREATE (FRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.052,calculated_trading_power: 2.052}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (GEL:Country {name:"GEL"}) CREATE (GEL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (HOL:Country {name:"HOL"}) CREATE (HOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.164,calculated_trading_power: 10.164}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (LIE:Country {name:"LIE"}) CREATE (LIE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.028,calculated_trading_power: 2.028}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (UTR:Country {name:"UTR"}) CREATE (UTR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.763,calculated_trading_power: 6.763}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (WOL:Country {name:"WOL"}) CREATE (WOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.127,calculated_trading_power: 5.127}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.185,calculated_trading_power: 6.185}]->(north_sea);
MATCH (lubeck:Trade_node {name:"lubeck"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.527,calculated_trading_power: 10.527}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 108.844,calculated_trading_power: 108.844}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (NOR:Country {name:"NOR"}) CREATE (NOR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 34.444,calculated_trading_power: 34.444}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 31.196,calculated_trading_power: 31.196}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 38.179,calculated_trading_power: 38.179}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (BUR:Country {name:"BUR"}) CREATE (BUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.231,calculated_trading_power: 2.231}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (BRA:Country {name:"BRA"}) CREATE (BRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.213,calculated_trading_power: 1.213}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 60.938,calculated_trading_power: 60.938}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.364,calculated_trading_power: 5.364}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 71.826,calculated_trading_power: 71.826}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (LAU:Country {name:"LAU"}) CREATE (LAU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.464,calculated_trading_power: 7.464}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (LUN:Country {name:"LUN"}) CREATE (LUN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.123,calculated_trading_power: 10.123}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (MKL:Country {name:"MKL"}) CREATE (MKL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.818,calculated_trading_power: 18.818}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (OLD:Country {name:"OLD"}) CREATE (OLD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.805,calculated_trading_power: 15.805}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (VER:Country {name:"VER"}) CREATE (VER)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.15,calculated_trading_power: 27.15}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (DTT:Country {name:"DTT"}) CREATE (DTT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.818,calculated_trading_power: 25.818}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (BRB:Country {name:"BRB"}) CREATE (BRB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.727,calculated_trading_power: 5.727}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (FLA:Country {name:"FLA"}) CREATE (FLA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.163,calculated_trading_power: 5.163}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (HOL:Country {name:"HOL"}) CREATE (HOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.178,calculated_trading_power: 8.178}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 48.984,calculated_trading_power: 48.984}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (WOL:Country {name:"WOL"}) CREATE (WOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.499,calculated_trading_power: 29.499}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.833,calculated_trading_power: 35.833}]->(lubeck);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 35.703,calculated_trading_power: 35.703}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (AMG:Country {name:"AMG"}) CREATE (AMG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.532,calculated_trading_power: 13.532}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (AUV:Country {name:"AUV"}) CREATE (AUV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.353,calculated_trading_power: 9.353}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (BOU:Country {name:"BOU"}) CREATE (BOU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.21,calculated_trading_power: 11.21}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (BRI:Country {name:"BRI"}) CREATE (BRI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 59.799,calculated_trading_power: 59.799}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (BUR:Country {name:"BUR"}) CREATE (BUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.429,calculated_trading_power: 7.429}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (FOI:Country {name:"FOI"}) CREATE (FOI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.235,calculated_trading_power: 11.235}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 41.324,calculated_trading_power: 41.324}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (ORL:Country {name:"ORL"}) CREATE (ORL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.609,calculated_trading_power: 5.609}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (PRO:Country {name:"PRO"}) CREATE (PRO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.6,calculated_trading_power: 8.6}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (LOR:Country {name:"LOR"}) CREATE (LOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.716,calculated_trading_power: 2.716}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.232,calculated_trading_power: 2.232}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (CAS:Country {name:"CAS"}) CREATE (CAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.029,calculated_trading_power: 3.029}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (NAV:Country {name:"NAV"}) CREATE (NAV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.74,calculated_trading_power: 8.74}]->(bordeaux);
MATCH (sevilla:Trade_node {name:"sevilla"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.362,calculated_trading_power: 3.362}]->(sevilla);
MATCH (sevilla:Trade_node {name:"sevilla"}), (ARA:Country {name:"ARA"}) CREATE (ARA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.993,calculated_trading_power: 11.993}]->(sevilla);
MATCH (sevilla:Trade_node {name:"sevilla"}), (CAS:Country {name:"CAS"}) CREATE (CAS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 141.718,calculated_trading_power: 141.718}]->(sevilla);
MATCH (sevilla:Trade_node {name:"sevilla"}), (GRA:Country {name:"GRA"}) CREATE (GRA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 40.333,calculated_trading_power: 40.333}]->(sevilla);
MATCH (sevilla:Trade_node {name:"sevilla"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 106.177,calculated_trading_power: 106.177}]->(sevilla);
MATCH (sevilla:Trade_node {name:"sevilla"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.901,calculated_trading_power: 12.901}]->(sevilla);
MATCH (champagne:Trade_node {name:"champagne"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 87.799,calculated_trading_power: 87.799}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (BUR:Country {name:"BUR"}) CREATE (BUR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 96.722,calculated_trading_power: 96.722}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 142.112,calculated_trading_power: 142.112}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (NEV:Country {name:"NEV"}) CREATE (NEV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.353,calculated_trading_power: 24.353}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (ORL:Country {name:"ORL"}) CREATE (ORL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 47.418,calculated_trading_power: 47.418}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (PRO:Country {name:"PRO"}) CREATE (PRO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.822,calculated_trading_power: 10.822}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.342,calculated_trading_power: 3.342}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (LOR:Country {name:"LOR"}) CREATE (LOR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.592,calculated_trading_power: 36.592}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 38.929,calculated_trading_power: 38.929}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (ARA:Country {name:"ARA"}) CREATE (ARA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.392,calculated_trading_power: 6.392}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.138,calculated_trading_power: 12.138}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (MLO:Country {name:"MLO"}) CREATE (MLO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.804,calculated_trading_power: 5.804}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (NAP:Country {name:"NAP"}) CREATE (NAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.159,calculated_trading_power: 6.159}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.27,calculated_trading_power: 4.27}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 38.971,calculated_trading_power: 38.971}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.76,calculated_trading_power: 2.76}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (LAN:Country {name:"LAN"}) CREATE (LAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.269,calculated_trading_power: 8.269}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (BRB:Country {name:"BRB"}) CREATE (BRB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 26.455,calculated_trading_power: 26.455}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (FLA:Country {name:"FLA"}) CREATE (FLA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 21.051,calculated_trading_power: 21.051}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (FRI:Country {name:"FRI"}) CREATE (FRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.26,calculated_trading_power: 10.26}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (GEL:Country {name:"GEL"}) CREATE (GEL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.794,calculated_trading_power: 11.794}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (HOL:Country {name:"HOL"}) CREATE (HOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.178,calculated_trading_power: 8.178}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (LIE:Country {name:"LIE"}) CREATE (LIE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.846,calculated_trading_power: 17.846}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (UTR:Country {name:"UTR"}) CREATE (UTR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.761,calculated_trading_power: 8.761}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (GNV:Country {name:"GNV"}) CREATE (GNV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.242,calculated_trading_power: 17.242}]->(champagne);
MATCH (valencia:Trade_node {name:"valencia"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.815,calculated_trading_power: 16.815}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (PRO:Country {name:"PRO"}) CREATE (PRO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.532,calculated_trading_power: 6.532}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (ARA:Country {name:"ARA"}) CREATE (ARA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 77.833,calculated_trading_power: 77.833}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.138,calculated_trading_power: 12.138}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (MLO:Country {name:"MLO"}) CREATE (MLO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.804,calculated_trading_power: 5.804}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (NAP:Country {name:"NAP"}) CREATE (NAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.159,calculated_trading_power: 6.159}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.27,calculated_trading_power: 4.27}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.76,calculated_trading_power: 2.76}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (LAN:Country {name:"LAN"}) CREATE (LAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.269,calculated_trading_power: 8.269}]->(valencia);
MATCH (genua:Trade_node {name:"genua"}), (PRO:Country {name:"PRO"}) CREATE (PRO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.33,calculated_trading_power: 17.33}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (ARA:Country {name:"ARA"}) CREATE (ARA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.052,calculated_trading_power: 18.052}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 80.02,calculated_trading_power: 80.02}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (MLO:Country {name:"MLO"}) CREATE (MLO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 39.672,calculated_trading_power: 39.672}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (NAP:Country {name:"NAP"}) CREATE (NAP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 39.737,calculated_trading_power: 39.737}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.625,calculated_trading_power: 36.625}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.954,calculated_trading_power: 6.954}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (SIE:Country {name:"SIE"}) CREATE (SIE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.376,calculated_trading_power: 19.376}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (MFA:Country {name:"MFA"}) CREATE (MFA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.4,calculated_trading_power: 9.4}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.429,calculated_trading_power: 27.429}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (LAN:Country {name:"LAN"}) CREATE (LAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 55.367,calculated_trading_power: 55.367}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (SZO:Country {name:"SZO"}) CREATE (SZO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.236,calculated_trading_power: 9.236}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (PGA:Country {name:"PGA"}) CREATE (PGA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.828,calculated_trading_power: 8.828}]->(genua);
MATCH (venice:Trade_node {name:"venice"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.558,calculated_trading_power: 7.558}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (FER:Country {name:"FER"}) CREATE (FER)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.83,calculated_trading_power: 36.83}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.998,calculated_trading_power: 18.998}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (MLO:Country {name:"MLO"}) CREATE (MLO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.424,calculated_trading_power: 3.424}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (NAP:Country {name:"NAP"}) CREATE (NAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.191,calculated_trading_power: 7.191}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.226,calculated_trading_power: 4.226}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (URB:Country {name:"URB"}) CREATE (URB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.151,calculated_trading_power: 21.151}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 141.254,calculated_trading_power: 141.254}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (BLG:Country {name:"BLG"}) CREATE (BLG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.414,calculated_trading_power: 10.414}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (CLI:Country {name:"CLI"}) CREATE (CLI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.57,calculated_trading_power: 8.57}]->(venice);
MATCH (english_channel:Trade_node {name:"english_channel"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 216.863,calculated_trading_power: 216.863}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (BUR:Country {name:"BUR"}) CREATE (BUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.674,calculated_trading_power: 9.674}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 43.026,calculated_trading_power: 43.026}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (BRB:Country {name:"BRB"}) CREATE (BRB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 47.583,calculated_trading_power: 47.583}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (FLA:Country {name:"FLA"}) CREATE (FLA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 46.527,calculated_trading_power: 46.527}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (FRI:Country {name:"FRI"}) CREATE (FRI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 34.278,calculated_trading_power: 34.278}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (GEL:Country {name:"GEL"}) CREATE (GEL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.144,calculated_trading_power: 32.144}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (HOL:Country {name:"HOL"}) CREATE (HOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 74.52,calculated_trading_power: 74.52}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (LIE:Country {name:"LIE"}) CREATE (LIE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.438,calculated_trading_power: 2.438}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (UTR:Country {name:"UTR"}) CREATE (UTR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.454,calculated_trading_power: 32.454}]->(english_channel);
