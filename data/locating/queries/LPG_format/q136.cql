CREATE (african_great_lakes:Trade_node {name:"african_great_lakes", local_value:1.63, is_inland:true, total_power:549.896, outgoing:0.4298826505375561, ingoing:0});
CREATE (kongo:Trade_node {name:"kongo", local_value:3.309, is_inland:true, total_power:756.4940000000001, outgoing:0.5308896431885539, ingoing:0.22568839153221695});
CREATE (zambezi:Trade_node {name:"zambezi", local_value:1.96, is_inland:true, total_power:515.202, outgoing:0.45575444272634597, ingoing:0.2787170626739908});
CREATE (patagonia:Trade_node {name:"patagonia", local_value:0.213, is_inland:false, total_power:185.166, outgoing:0.1065, ingoing:0});
CREATE (amazonas_node:Trade_node {name:"amazonas_node", local_value:1.368, is_inland:false, total_power:412.73800000000006, outgoing:0.6839999999999999, ingoing:0});
CREATE (rio_grande:Trade_node {name:"rio_grande", local_value:1.887, is_inland:false, total_power:370.28999999999996, outgoing:0.6819067325609657, ingoing:0});
CREATE (james_bay:Trade_node {name:"james_bay", local_value:1.713, is_inland:false, total_power:150.558, outgoing:0.06596775993304907, ingoing:0});
CREATE (california:Trade_node {name:"california", local_value:1.583, is_inland:false, total_power:362.562, outgoing:0.4850848350741127, ingoing:0.27330043036118873});
CREATE (girin:Trade_node {name:"girin", local_value:4.736, is_inland:false, total_power:747.2580000000002, outgoing:0.64462169543269, ingoing:0.12733476920695458});
CREATE (mississippi_river:Trade_node {name:"mississippi_river", local_value:3.257, is_inland:false, total_power:410.94399999999996, outgoing:0.7255984293271156, ingoing:0.3660021256032926});
CREATE (ohio:Trade_node {name:"ohio", local_value:7.592, is_inland:true, total_power:837.044, outgoing:0.515413421182032, ingoing:0.3809391753967357});
CREATE (mexico:Trade_node {name:"mexico", local_value:9.379, is_inland:false, total_power:865.264, outgoing:1.0828743705674002, ingoing:0.3660021256032926});
CREATE (lhasa:Trade_node {name:"lhasa", local_value:1.197, is_inland:true, total_power:542.1120000000001, outgoing:0.29699339250929696, ingoing:0});
CREATE (chengdu:Trade_node {name:"chengdu", local_value:2.615, is_inland:true, total_power:680.974, outgoing:1.1332735139657597, ingoing:0.10394768737825394});
CREATE (burma:Trade_node {name:"burma", local_value:5.582, is_inland:false, total_power:605.548, outgoing:1.1791561332318092, ingoing:0.39664572988801594});
CREATE (gulf_of_siam:Trade_node {name:"gulf_of_siam", local_value:8.491, is_inland:false, total_power:936.755, outgoing:1.8851663384121975, ingoing:0.6190569699466999});
CREATE (canton:Trade_node {name:"canton", local_value:5.209, is_inland:false, total_power:672.4299999999997, outgoing:2.4555529376397467, ingoing:1.3863580575544197});
CREATE (philippines:Trade_node {name:"philippines", local_value:3.815, is_inland:false, total_power:430.18799999999993, outgoing:1.3716973222546658, ingoing:0.859443**********});
CREATE (cuiaba:Trade_node {name:"cuiaba", local_value:2.314, is_inland:false, total_power:594.06, outgoing:0.8857164587752081, ingoing:0.351225});
CREATE (lima:Trade_node {name:"lima", local_value:2.685, is_inland:false, total_power:487.712, outgoing:0.0038872028603799936, ingoing:0.31000076057132286});
CREATE (polynesia_node:Trade_node {name:"polynesia_node", local_value:1.975, is_inland:false, total_power:392.41999999999996, outgoing:0.2927106391624609, ingoing:1.2285226745909439});
CREATE (australia:Trade_node {name:"australia", local_value:2.108, is_inland:false, total_power:374.76199999999994, outgoing:0.0, ingoing:0.10244872370686131});
CREATE (nippon:Trade_node {name:"nippon", local_value:8.371, is_inland:false, total_power:844.362, outgoing:0.3353680136653585, ingoing:0.3280663171083028});
CREATE (hangzhou:Trade_node {name:"hangzhou", local_value:9.684, is_inland:false, total_power:796.4839999999997, outgoing:5.447789971261272, ingoing:1.211579942522538});
CREATE (xian:Trade_node {name:"xian", local_value:4.563, is_inland:true, total_power:655.8760000000001, outgoing:3.4331861099147294, ingoing:2.303372219829461});
CREATE (beijing:Trade_node {name:"beijing", local_value:4.506, is_inland:false, total_power:486.984, outgoing:2.1042812336076686, ingoing:3.9347667910481197});
CREATE (the_moluccas:Trade_node {name:"the_moluccas", local_value:9.368, is_inland:false, total_power:1046.734, outgoing:1.214884226200091, ingoing:0.7201410941836995});
CREATE (siberia:Trade_node {name:"siberia", local_value:3.662, is_inland:true, total_power:544.544, outgoing:1.9438087967007207, ingoing:0.2256175934014415});
CREATE (yumen:Trade_node {name:"yumen", local_value:3.079, is_inland:true, total_power:843.265, outgoing:2.1205020444188114, ingoing:4.011918002993285});
CREATE (malacca:Trade_node {name:"malacca", local_value:12.867, is_inland:false, total_power:1415.865, outgoing:1.4035868891311516, ingoing:5.031510783291855});
CREATE (ganges_delta:Trade_node {name:"ganges_delta", local_value:8.926, is_inland:false, total_power:1156.1240000000003, outgoing:3.5461756809352845, ingoing:1.4598877741188083});
CREATE (doab:Trade_node {name:"doab", local_value:8.376, is_inland:true, total_power:1046.6380000000001, outgoing:1.3054652648123963, ingoing:1.8617422324910244});
CREATE (lahore:Trade_node {name:"lahore", local_value:6.274, is_inland:true, total_power:1012.592, outgoing:3.438800863498156, ingoing:0.789316951404762});
CREATE (deccan:Trade_node {name:"deccan", local_value:8.405, is_inland:true, total_power:903.0880000000001, outgoing:2.1983665672945065, ingoing:0.6853692640265081});
CREATE (comorin_cape:Trade_node {name:"comorin_cape", local_value:11.101, is_inland:false, total_power:925.39, outgoing:2.8656182112884205, ingoing:3.0158846803206405});
CREATE (gujarat:Trade_node {name:"gujarat", local_value:6.696, is_inland:false, total_power:611.4979999999998, outgoing:3.533881133851508, ingoing:3.360689124004918});
CREATE (katsina:Trade_node {name:"katsina", local_value:3.951, is_inland:true, total_power:850.9439999999998, outgoing:0.6222886288639442, ingoing:0});
CREATE (ethiopia:Trade_node {name:"ethiopia", local_value:3.07, is_inland:true, total_power:727.9239999999999, outgoing:0.34462733147256, ingoing:0.21780102010238048});
CREATE (gulf_of_aden:Trade_node {name:"gulf_of_aden", local_value:6.069, is_inland:false, total_power:634.6580000000001, outgoing:2.1825074818556915, ingoing:2.4207541198220692});
CREATE (hormuz:Trade_node {name:"hormuz", local_value:4.433, is_inland:false, total_power:309.55799999999994, outgoing:1.8552291062979176, ingoing:2.00073601549752});
CREATE (zanzibar:Trade_node {name:"zanzibar", local_value:4.543, is_inland:false, total_power:464.3620000000001, outgoing:0.6510904802286137, ingoing:2.7049665718924003});
CREATE (cape_of_good_hope:Trade_node {name:"cape_of_good_hope", local_value:0.0, is_inland:false, total_power:23.352, outgoing:0.0, ingoing:2.423494494984846});
CREATE (basra:Trade_node {name:"basra", local_value:3.558, is_inland:false, total_power:550.746, outgoing:1.5549850057072716, ingoing:1.9479905616128135});
CREATE (samarkand:Trade_node {name:"samarkand", local_value:4.195, is_inland:true, total_power:1102.168, outgoing:0.39791969581903247, ingoing:4.450607067131985});
CREATE (persia:Trade_node {name:"persia", local_value:7.622, is_inland:true, total_power:620.392, outgoing:0.7560835333412107, ingoing:2.2288552705256643});
CREATE (aleppo:Trade_node {name:"aleppo", local_value:4.408, is_inland:false, total_power:537.03, outgoing:2.810655491500227, ingoing:1.2133109830004534});
CREATE (alexandria:Trade_node {name:"alexandria", local_value:6.969, is_inland:false, total_power:826.028, outgoing:3.8300751445268233, ingoing:2.420401100710205});
CREATE (astrakhan:Trade_node {name:"astrakhan", local_value:2.601, is_inland:true, total_power:456.1720000000001, outgoing:1.1616517487694586, ingoing:0.6058516953091277});
CREATE (crimea:Trade_node {name:"crimea", local_value:4.231, is_inland:false, total_power:695.002, outgoing:1.6266311666774478, ingoing:0.6098671681039658});
CREATE (constantinople:Trade_node {name:"constantinople", local_value:6.85, is_inland:false, total_power:558.624, outgoing:0.6066061546017363, ingoing:3.385441341959114});
CREATE (kiev:Trade_node {name:"kiev", local_value:3.633, is_inland:true, total_power:696.5239999999999, outgoing:2.101160454168554, ingoing:0.5693209083371068});
CREATE (kazan:Trade_node {name:"kazan", local_value:3.055, is_inland:true, total_power:460.46799999999996, outgoing:2.342683393185922, ingoing:1.6303667863718443});
CREATE (novgorod:Trade_node {name:"novgorod", local_value:6.418, is_inland:false, total_power:645.44, outgoing:1.3804340521563514, ingoing:3.5629268012837088});
CREATE (laplata:Trade_node {name:"laplata", local_value:1.156, is_inland:false, total_power:212.92, outgoing:0.7259317552664258, ingoing:0.42182576057132287});
CREATE (brazil:Trade_node {name:"brazil", local_value:3.266, is_inland:false, total_power:415.124, outgoing:0.26381321888050796, ingoing:1.31162910360107});
CREATE (timbuktu:Trade_node {name:"timbuktu", local_value:5.139, is_inland:true, total_power:1209.262, outgoing:0.943053412385038, ingoing:0.21780102010238048});
CREATE (ivory_coast:Trade_node {name:"ivory_coast", local_value:2.903, is_inland:false, total_power:886.1960000000001, outgoing:1.2778868549637499, ingoing:1.0508239840006692});
CREATE (tunis:Trade_node {name:"tunis", local_value:2.421, is_inland:false, total_power:702.868, outgoing:0.6962449392759471, ingoing:0.21780102010238048});
CREATE (ragusa:Trade_node {name:"ragusa", local_value:5.129, is_inland:false, total_power:835.5280000000001, outgoing:2.280225507411606, ingoing:0.6369364623318231});
CREATE (safi:Trade_node {name:"safi", local_value:3.418, is_inland:false, total_power:428.86, outgoing:0.5860530132272567, ingoing:0.495103041502145});
CREATE (pest:Trade_node {name:"pest", local_value:3.378, is_inland:true, total_power:802.514, outgoing:1.7598887869488957, ingoing:1.367399835931169});
CREATE (krakow:Trade_node {name:"krakow", local_value:5.99, is_inland:true, total_power:1494.342, outgoing:2.5482753833075655, ingoing:2.027050851586661});
CREATE (wien:Trade_node {name:"wien", local_value:6.809, is_inland:true, total_power:1886.8679999999995, outgoing:2.6514341644679003, ingoing:1.8158379973058183});
CREATE (saxony:Trade_node {name:"saxony", local_value:6.423, is_inland:true, total_power:1325.0579999999995, outgoing:1.921836272747858, ingoing:1.8198983417214132});
CREATE (baltic_sea:Trade_node {name:"baltic_sea", local_value:7.288, is_inland:false, total_power:639.0319999999999, outgoing:2.3056108775685162, ingoing:1.6166242615397326});
CREATE (rheinland:Trade_node {name:"rheinland", local_value:9.031, is_inland:true, total_power:1636.6120000000003, outgoing:1.5846202183393807, ingoing:1.9369660007563905});
CREATE (panama:Trade_node {name:"panama", local_value:5.251, is_inland:false, total_power:342.74, outgoing:2.7536922408286855, ingoing:0.4834955349071509});
CREATE (carribean_trade:Trade_node {name:"carribean_trade", local_value:7.527, is_inland:false, total_power:849.1220000000001, outgoing:1.9485861798883226, ingoing:4.226167357393431});
CREATE (chesapeake_bay:Trade_node {name:"chesapeake_bay", local_value:6.651, is_inland:false, total_power:494.4299999999999, outgoing:1.423527252100068, ingoing:0.9525972090814798});
CREATE (st_lawrence:Trade_node {name:"st_lawrence", local_value:3.754, is_inland:false, total_power:243.97000000000003, outgoing:0.9256753573622668, ingoing:1.0525769274379533});
CREATE (white_sea:Trade_node {name:"white_sea", local_value:1.753, is_inland:false, total_power:116.866, outgoing:1.2388639386910425, ingoing:0.7247278773820846});
CREATE (north_sea:Trade_node {name:"north_sea", local_value:3.821, is_inland:false, total_power:723.4680000000002, outgoing:1.9447052211499776, ingoing:1.7867866982407847});
CREATE (lubeck:Trade_node {name:"lubeck", local_value:5.32, is_inland:false, total_power:1404.298, outgoing:0.5468318456119889, ingoing:5.282751320371481});
CREATE (bordeaux:Trade_node {name:"bordeaux", local_value:4.405, is_inland:false, total_power:577.4219999999999, outgoing:2.8248566361739833, ingoing:1.5034300250040875});
CREATE (sevilla:Trade_node {name:"sevilla", local_value:7.019, is_inland:false, total_power:707.826, outgoing:0.08573441133127971, ingoing:1.8764918550240983});
CREATE (champagne:Trade_node {name:"champagne", local_value:9.853, is_inland:true, total_power:1295.64, outgoing:3.0338877493461753, ingoing:3.7980250826108577});
CREATE (valencia:Trade_node {name:"valencia", local_value:3.593, is_inland:false, total_power:388.68399999999997, outgoing:1.963353430322213, ingoing:0.33370686064442523});
CREATE (genua:Trade_node {name:"genua", local_value:10.184, is_inland:false, total_power:1063.496, outgoing:1.3700550985657758, ingoing:6.036603127170098});
CREATE (venice:Trade_node {name:"venice", local_value:7.791, is_inland:false, total_power:605.5620000000001, outgoing:0.6144184791004293, ingoing:3.0666071857422157});
CREATE (english_channel:Trade_node {name:"english_channel", local_value:10.555, is_inland:false, total_power:629.488, outgoing:1.5372186481564518, ingoing:4.270731854183589});
CREATE (SWE:Country {name:"SWE", home_node:"baltic_sea", development:153.648});
CREATE (DAN:Country {name:"DAN", home_node:"lubeck", development:218.135});
CREATE (SHL:Country {name:"SHL", home_node:"lubeck", development:12.0});
CREATE (KNI:Country {name:"KNI", home_node:"genua", development:8.0});
CREATE (MOL:Country {name:"MOL", home_node:"crimea", development:27.83});
CREATE (MON:Country {name:"MON", home_node:"ragusa", development:5.0});
CREATE (RAG:Country {name:"RAG", home_node:"ragusa", development:14.0});
CREATE (TRA:Country {name:"TRA", home_node:"pest", development:41.3});
CREATE (WAL:Country {name:"WAL", home_node:"pest", development:45.67});
CREATE (TUR:Country {name:"TUR", home_node:"constantinople", development:1041.201});
CREATE (CNN:Country {name:"CNN", home_node:"north_sea", development:6.0});
CREATE (ENG:Country {name:"ENG", home_node:"english_channel", development:335.822});
CREATE (LEI:Country {name:"LEI", home_node:"north_sea", development:6.0});
CREATE (SCO:Country {name:"SCO", home_node:"north_sea", development:89.779});
CREATE (SLN:Country {name:"SLN", home_node:"north_sea", development:8.0});
CREATE (ORD:Country {name:"ORD", home_node:"north_sea", development:7.0});
CREATE (PRU:Country {name:"PRU", home_node:"baltic_sea", development:31.999});
CREATE (KUR:Country {name:"KUR", home_node:"baltic_sea", development:12.3});
CREATE (PLC:Country {name:"PLC", home_node:"krakow", development:487.075});
CREATE (ALS:Country {name:"ALS", home_node:"rheinland", development:16.0});
CREATE (FRA:Country {name:"FRA", home_node:"champagne", development:698.702});
CREATE (AAC:Country {name:"AAC", home_node:"rheinland", development:14.0});
CREATE (ANH:Country {name:"ANH", home_node:"saxony", development:6.0});
CREATE (ANS:Country {name:"ANS", home_node:"rheinland", development:8.0});
CREATE (AUG:Country {name:"AUG", home_node:"wien", development:17.0});
CREATE (BAD:Country {name:"BAD", home_node:"rheinland", development:16.0});
CREATE (BAV:Country {name:"BAV", home_node:"wien", development:86.733});
CREATE (BOH:Country {name:"BOH", home_node:"saxony", development:147.984});
CREATE (BRA:Country {name:"BRA", home_node:"saxony", development:75.669});
CREATE (BRE:Country {name:"BRE", home_node:"lubeck", development:16.0});
CREATE (BRU:Country {name:"BRU", home_node:"saxony", development:31.177});
CREATE (EFR:Country {name:"EFR", home_node:"english_channel", development:7.0});
CREATE (FRN:Country {name:"FRN", home_node:"rheinland", development:18.0});
CREATE (HAB:Country {name:"HAB", home_node:"wien", development:295.952});
CREATE (HAM:Country {name:"HAM", home_node:"lubeck", development:19.0});
CREATE (HES:Country {name:"HES", home_node:"rheinland", development:44.0});
CREATE (KOL:Country {name:"KOL", home_node:"rheinland", development:52.54});
CREATE (LAU:Country {name:"LAU", home_node:"lubeck", development:9.0});
CREATE (LOR:Country {name:"LOR", home_node:"champagne", development:25.0});
CREATE (LUN:Country {name:"LUN", home_node:"lubeck", development:20.0});
CREATE (MAG:Country {name:"MAG", home_node:"saxony", development:16.93});
CREATE (MAI:Country {name:"MAI", home_node:"rheinland", development:16.664});
CREATE (MKL:Country {name:"MKL", home_node:"lubeck", development:23.86});
CREATE (OLD:Country {name:"OLD", home_node:"lubeck", development:7.0});
CREATE (PAL:Country {name:"PAL", home_node:"rheinland", development:61.677});
CREATE (SAX:Country {name:"SAX", home_node:"saxony", development:45.907});
CREATE (SLZ:Country {name:"SLZ", home_node:"wien", development:14.0});
CREATE (SWI:Country {name:"SWI", home_node:"champagne", development:69.01});
CREATE (THU:Country {name:"THU", home_node:"saxony", development:25.292});
CREATE (TRI:Country {name:"TRI", home_node:"rheinland", development:22.0});
CREATE (ULM:Country {name:"ULM", home_node:"wien", development:15.0});
CREATE (WBG:Country {name:"WBG", home_node:"rheinland", development:15.505});
CREATE (WUR:Country {name:"WUR", home_node:"wien", development:18.848});
CREATE (NUM:Country {name:"NUM", home_node:"rheinland", development:19.0});
CREATE (MEM:Country {name:"MEM", home_node:"wien", development:14.0});
CREATE (VER:Country {name:"VER", home_node:"lubeck", development:12.94});
CREATE (NSA:Country {name:"NSA", home_node:"rheinland", development:9.0});
CREATE (RVA:Country {name:"RVA", home_node:"rheinland", development:12.0});
CREATE (POR:Country {name:"POR", home_node:"sevilla", development:252.001});
CREATE (SPA:Country {name:"SPA", home_node:"sevilla", development:657.606});
CREATE (GEN:Country {name:"GEN", home_node:"genua", development:45.056});
CREATE (MAN:Country {name:"MAN", home_node:"venice", development:29.572});
CREATE (MOD:Country {name:"MOD", home_node:"venice", development:12.0});
CREATE (PAP:Country {name:"PAP", home_node:"genua", development:105.104});
CREATE (PAR:Country {name:"PAR", home_node:"venice", development:15.0});
CREATE (SAV:Country {name:"SAV", home_node:"genua", development:70.546});
CREATE (TUS:Country {name:"TUS", home_node:"genua", development:76.37});
CREATE (URB:Country {name:"URB", home_node:"venice", development:12.0});
CREATE (VEN:Country {name:"VEN", home_node:"venice", development:136.622});
CREATE (LUC:Country {name:"LUC", home_node:"genua", development:14.0});
CREATE (LIE:Country {name:"LIE", home_node:"champagne", development:26.424});
CREATE (NED:Country {name:"NED", home_node:"english_channel", development:222.818});
CREATE (CRI:Country {name:"CRI", home_node:"crimea", development:56.259});
CREATE (GEO:Country {name:"GEO", home_node:"crimea", development:17.236});
CREATE (QAS:Country {name:"QAS", home_node:"novgorod", development:14.0});
CREATE (RUS:Country {name:"RUS", home_node:"novgorod", development:970.066});
CREATE (ZAZ:Country {name:"ZAZ", home_node:"crimea", development:21.06});
CREATE (NOG:Country {name:"NOG", home_node:"astrakhan", development:24.54});
CREATE (ANZ:Country {name:"ANZ", home_node:"basra", development:7.0});
CREATE (ARD:Country {name:"ARD", home_node:"basra", development:7.0});
CREATE (DAW:Country {name:"DAW", home_node:"basra", development:14.0});
CREATE (HDR:Country {name:"HDR", home_node:"gulf_of_aden", development:15.0});
CREATE (HED:Country {name:"HED", home_node:"alexandria", development:48.7});
CREATE (MFL:Country {name:"MFL", home_node:"gulf_of_aden", development:6.0});
CREATE (MHR:Country {name:"MHR", home_node:"gulf_of_aden", development:9.784});
CREATE (NAJ:Country {name:"NAJ", home_node:"basra", development:8.306});
CREATE (OMA:Country {name:"OMA", home_node:"hormuz", development:13.905});
CREATE (RAS:Country {name:"RAS", home_node:"gulf_of_aden", development:11.892});
CREATE (SHM:Country {name:"SHM", home_node:"basra", development:9.0});
CREATE (YAS:Country {name:"YAS", home_node:"hormuz", development:3.0});
CREATE (AVR:Country {name:"AVR", home_node:"astrakhan", development:7.0});
CREATE (MSY:Country {name:"MSY", home_node:"basra", development:27.44});
CREATE (ALG:Country {name:"ALG", home_node:"safi", development:56.32});
CREATE (MOR:Country {name:"MOR", home_node:"safi", development:132.6});
CREATE (TRP:Country {name:"TRP", home_node:"tunis", development:13.07});
CREATE (TUN:Country {name:"TUN", home_node:"tunis", development:67.54});
CREATE (KBA:Country {name:"KBA", home_node:"tunis", development:8.0});
CREATE (SOS:Country {name:"SOS", home_node:"safi", development:22.62});
CREATE (TGT:Country {name:"TGT", home_node:"tunis", development:7.662});
CREATE (GHD:Country {name:"GHD", home_node:"tunis", development:3.0});
CREATE (FZA:Country {name:"FZA", home_node:"tunis", development:7.512});
CREATE (MZB:Country {name:"MZB", home_node:"tunis", development:5.703});
CREATE (KZH:Country {name:"KZH", home_node:"samarkand", development:180.144});
CREATE (KHI:Country {name:"KHI", home_node:"samarkand", development:48.18});
CREATE (BUK:Country {name:"BUK", home_node:"samarkand", development:116.286});
CREATE (PER:Country {name:"PER", home_node:"persia", development:475.449});
CREATE (CIR:Country {name:"CIR", home_node:"crimea", development:12.94});
CREATE (GAZ:Country {name:"GAZ", home_node:"astrakhan", development:37.588});
CREATE (IME:Country {name:"IME", home_node:"crimea", development:10.0});
CREATE (ORM:Country {name:"ORM", home_node:"hormuz", development:12.88});
CREATE (BSR:Country {name:"BSR", home_node:"basra", development:30.203});
CREATE (MGR:Country {name:"MGR", home_node:"persia", development:6.0});
CREATE (CHE:Country {name:"CHE", home_node:"chesapeake_bay", development:8.0});
CREATE (ASH:Country {name:"ASH", home_node:"timbuktu", development:13.95});
CREATE (BEN:Country {name:"BEN", home_node:"ivory_coast", development:23.87});
CREATE (ETH:Country {name:"ETH", home_node:"ethiopia", development:73.331});
CREATE (KON:Country {name:"KON", home_node:"kongo", development:33.1});
CREATE (MAL:Country {name:"MAL", home_node:"timbuktu", development:14.52});
CREATE (NUB:Country {name:"NUB", home_node:"ethiopia", development:42.488});
CREATE (SON:Country {name:"SON", home_node:"timbuktu", development:23.76});
CREATE (ZAN:Country {name:"ZAN", home_node:"zanzibar", development:27.33});
CREATE (ZIM:Country {name:"ZIM", home_node:"zambezi", development:35.0});
CREATE (HAU:Country {name:"HAU", home_node:"katsina", development:20.254});
CREATE (KBO:Country {name:"KBO", home_node:"katsina", development:59.548});
CREATE (LOA:Country {name:"LOA", home_node:"ivory_coast", development:18.086});
CREATE (OYO:Country {name:"OYO", home_node:"katsina", development:23.0});
CREATE (JOL:Country {name:"JOL", home_node:"ivory_coast", development:12.76});
CREATE (MLI:Country {name:"MLI", home_node:"zanzibar", development:14.55});
CREATE (AJU:Country {name:"AJU", home_node:"gulf_of_aden", development:47.468});
CREATE (ENA:Country {name:"ENA", home_node:"ethiopia", development:7.0});
CREATE (WGD:Country {name:"WGD", home_node:"timbuktu", development:18.9});
CREATE (GUR:Country {name:"GUR", home_node:"timbuktu", development:5.856});
CREATE (OGD:Country {name:"OGD", home_node:"ethiopia", development:6.0});
CREATE (WAD:Country {name:"WAD", home_node:"ethiopia", development:3.0});
CREATE (AFA:Country {name:"AFA", home_node:"gulf_of_aden", development:25.81});
CREATE (DAR:Country {name:"DAR", home_node:"ethiopia", development:11.538});
CREATE (KAF:Country {name:"KAF", home_node:"ethiopia", development:13.106});
CREATE (MED:Country {name:"MED", home_node:"ethiopia", development:16.89});
CREATE (MRE:Country {name:"MRE", home_node:"gulf_of_aden", development:16.86});
CREATE (PTE:Country {name:"PTE", home_node:"zanzibar", development:8.0});
CREATE (WAR:Country {name:"WAR", home_node:"gulf_of_aden", development:38.769});
CREATE (BTI:Country {name:"BTI", home_node:"ethiopia", development:4.0});
CREATE (WLY:Country {name:"WLY", home_node:"ethiopia", development:13.616});
CREATE (DAM:Country {name:"DAM", home_node:"ethiopia", development:13.0});
CREATE (JJI:Country {name:"JJI", home_node:"ethiopia", development:4.0});
CREATE (ABB:Country {name:"ABB", home_node:"ethiopia", development:10.16});
CREATE (SYO:Country {name:"SYO", home_node:"ivory_coast", development:15.417});
CREATE (KSJ:Country {name:"KSJ", home_node:"kongo", development:15.9});
CREATE (LUB:Country {name:"LUB", home_node:"kongo", development:35.888});
CREATE (LND:Country {name:"LND", home_node:"kongo", development:19.89});
CREATE (CKW:Country {name:"CKW", home_node:"kongo", development:8.96});
CREATE (KIK:Country {name:"KIK", home_node:"kongo", development:5.0});
CREATE (KZB:Country {name:"KZB", home_node:"kongo", development:20.87});
CREATE (YAK:Country {name:"YAK", home_node:"kongo", development:12.95});
CREATE (KUB:Country {name:"KUB", home_node:"kongo", development:15.9});
CREATE (RWA:Country {name:"RWA", home_node:"african_great_lakes", development:17.93});
CREATE (BUU:Country {name:"BUU", home_node:"african_great_lakes", development:9.0});
CREATE (BUG:Country {name:"BUG", home_node:"african_great_lakes", development:13.0});
CREATE (NKO:Country {name:"NKO", home_node:"african_great_lakes", development:9.0});
CREATE (KRW:Country {name:"KRW", home_node:"african_great_lakes", development:13.712});
CREATE (BNY:Country {name:"BNY", home_node:"african_great_lakes", development:13.908});
CREATE (BSG:Country {name:"BSG", home_node:"african_great_lakes", development:9.0});
CREATE (UBH:Country {name:"UBH", home_node:"african_great_lakes", development:20.0});
CREATE (MRA:Country {name:"MRA", home_node:"zambezi", development:18.586});
CREATE (LDU:Country {name:"LDU", home_node:"zambezi", development:16.671});
CREATE (TBK:Country {name:"TBK", home_node:"zambezi", development:10.95});
CREATE (RZW:Country {name:"RZW", home_node:"zambezi", development:9.505});
CREATE (MIR:Country {name:"MIR", home_node:"zanzibar", development:12.816});
CREATE (SKA:Country {name:"SKA", home_node:"zanzibar", development:21.975});
CREATE (BTS:Country {name:"BTS", home_node:"zanzibar", development:14.174});
CREATE (MFY:Country {name:"MFY", home_node:"zanzibar", development:6.0});
CREATE (ANT:Country {name:"ANT", home_node:"zanzibar", development:10.95});
CREATE (ANN:Country {name:"ANN", home_node:"canton", development:50.48});
CREATE (ARK:Country {name:"ARK", home_node:"ganges_delta", development:31.116});
CREATE (ATJ:Country {name:"ATJ", home_node:"malacca", development:78.787});
CREATE (AYU:Country {name:"AYU", home_node:"gulf_of_siam", development:170.638});
CREATE (BLI:Country {name:"BLI", home_node:"the_moluccas", development:9.0});
CREATE (BAN:Country {name:"BAN", home_node:"the_moluccas", development:52.818});
CREATE (BEI:Country {name:"BEI", home_node:"malacca", development:72.616});
CREATE (CHA:Country {name:"CHA", home_node:"gulf_of_siam", development:28.088});
CREATE (DAI:Country {name:"DAI", home_node:"canton", development:19.594});
CREATE (DTE:Country {name:"DTE", home_node:"nippon", development:16.208});
CREATE (HSK:Country {name:"HSK", home_node:"nippon", development:9.0});
CREATE (IKE:Country {name:"IKE", home_node:"nippon", development:7.0});
CREATE (MAE:Country {name:"MAE", home_node:"nippon", development:9.0});
CREATE (MRI:Country {name:"MRI", home_node:"nippon", development:9.0});
CREATE (SMZ:Country {name:"SMZ", home_node:"nippon", development:9.0});
CREATE (TKG:Country {name:"TKG", home_node:"nippon", development:249.453});
CREATE (UES:Country {name:"UES", home_node:"nippon", development:7.0});
CREATE (RFR:Country {name:"RFR", home_node:"nippon", development:8.0});
CREATE (ANU:Country {name:"ANU", home_node:"nippon", development:15.0});
CREATE (ITO:Country {name:"ITO", home_node:"nippon", development:7.0});
CREATE (SHN:Country {name:"SHN", home_node:"nippon", development:9.0});
CREATE (STK:Country {name:"STK", home_node:"nippon", development:6.0});
CREATE (KHA:Country {name:"KHA", home_node:"yumen", development:212.782});
CREATE (KHM:Country {name:"KHM", home_node:"gulf_of_siam", development:62.16});
CREATE (KOR:Country {name:"KOR", home_node:"nippon", development:129.284});
CREATE (LNA:Country {name:"LNA", home_node:"gulf_of_siam", development:54.278});
CREATE (LXA:Country {name:"LXA", home_node:"gulf_of_siam", development:139.742});
CREATE (MCH:Country {name:"MCH", home_node:"girin", development:182.768});
CREATE (MKS:Country {name:"MKS", home_node:"the_moluccas", development:22.0});
CREATE (MNG:Country {name:"MNG", home_node:"beijing", development:1067.734});
CREATE (MTR:Country {name:"MTR", home_node:"the_moluccas", development:57.0});
CREATE (OIR:Country {name:"OIR", home_node:"yumen", development:32.436});
CREATE (PAT:Country {name:"PAT", home_node:"malacca", development:15.0});
CREATE (RYU:Country {name:"RYU", home_node:"nippon", development:7.0});
CREATE (SUK:Country {name:"SUK", home_node:"gulf_of_siam", development:11.0});
CREATE (SUL:Country {name:"SUL", home_node:"philippines", development:11.0});
CREATE (TAU:Country {name:"TAU", home_node:"burma", development:238.388});
CREATE (TOK:Country {name:"TOK", home_node:"canton", development:76.472});
CREATE (NVK:Country {name:"NVK", home_node:"girin", development:22.88});
CREATE (SOL:Country {name:"SOL", home_node:"girin", development:29.75});
CREATE (EJZ:Country {name:"EJZ", home_node:"girin", development:7.0});
CREATE (NHX:Country {name:"NHX", home_node:"girin", development:9.0});
CREATE (MYR:Country {name:"MYR", home_node:"girin", development:3.0});
CREATE (MHX:Country {name:"MHX", home_node:"girin", development:9.0});
CREATE (KRC:Country {name:"KRC", home_node:"girin", development:30.76});
CREATE (KAS:Country {name:"KAS", home_node:"samarkand", development:99.461});
CREATE (UTS:Country {name:"UTS", home_node:"lhasa", development:41.525});
CREATE (KAM:Country {name:"KAM", home_node:"chengdu", development:30.0});
CREATE (GUG:Country {name:"GUG", home_node:"lhasa", development:10.976});
CREATE (BAL:Country {name:"BAL", home_node:"hormuz", development:9.764});
CREATE (BIJ:Country {name:"BIJ", home_node:"deccan", development:97.219});
CREATE (GOC:Country {name:"GOC", home_node:"deccan", development:78.907});
CREATE (MUG:Country {name:"MUG", home_node:"doab", development:911.563});
CREATE (MYS:Country {name:"MYS", home_node:"comorin_cape", development:27.886});
CREATE (VIJ:Country {name:"VIJ", home_node:"comorin_cape", development:91.747});
CREATE (AHM:Country {name:"AHM", home_node:"deccan", development:26.118});
CREATE (ASS:Country {name:"ASS", home_node:"burma", development:37.692});
CREATE (GUJ:Country {name:"GUJ", home_node:"gujarat", development:8.0});
CREATE (MAD:Country {name:"MAD", home_node:"comorin_cape", development:30.808});
CREATE (MAW:Country {name:"MAW", home_node:"gujarat", development:18.81});
CREATE (MER:Country {name:"MER", home_node:"gujarat", development:24.0});
CREATE (JAN:Country {name:"JAN", home_node:"gujarat", development:6.0});
CREATE (GDW:Country {name:"GDW", home_node:"deccan", development:5.193});
CREATE (GRJ:Country {name:"GRJ", home_node:"ganges_delta", development:5.0});
CREATE (DHU:Country {name:"DHU", home_node:"doab", development:13.0});
CREATE (KLN:Country {name:"KLN", home_node:"comorin_cape", development:17.488});
CREATE (VND:Country {name:"VND", home_node:"comorin_cape", development:11.93});
CREATE (MAB:Country {name:"MAB", home_node:"comorin_cape", development:7.0});
CREATE (BST:Country {name:"BST", home_node:"ganges_delta", development:7.0});
CREATE (BHU:Country {name:"BHU", home_node:"lhasa", development:6.476});
CREATE (BND:Country {name:"BND", home_node:"doab", development:17.7});
CREATE (CEY:Country {name:"CEY", home_node:"comorin_cape", development:12.952});
CREATE (JSL:Country {name:"JSL", home_node:"gujarat", development:3.0});
CREATE (KAC:Country {name:"KAC", home_node:"burma", development:3.0});
CREATE (KMT:Country {name:"KMT", home_node:"ganges_delta", development:9.0});
CREATE (KGR:Country {name:"KGR", home_node:"doab", development:5.0});
CREATE (KOC:Country {name:"KOC", home_node:"comorin_cape", development:8.0});
CREATE (MLB:Country {name:"MLB", home_node:"burma", development:5.0});
CREATE (HAD:Country {name:"HAD", home_node:"doab", development:10.0});
CREATE (LDK:Country {name:"LDK", home_node:"lahore", development:8.712});
CREATE (BGL:Country {name:"BGL", home_node:"doab", development:10.0});
CREATE (JFN:Country {name:"JFN", home_node:"comorin_cape", development:6.0});
CREATE (GHR:Country {name:"GHR", home_node:"doab", development:3.0});
CREATE (CHD:Country {name:"CHD", home_node:"deccan", development:13.829});
CREATE (NGP:Country {name:"NGP", home_node:"ganges_delta", development:10.4});
CREATE (JAJ:Country {name:"JAJ", home_node:"gujarat", development:10.0});
CREATE (TPR:Country {name:"TPR", home_node:"burma", development:8.0});
CREATE (DGL:Country {name:"DGL", home_node:"comorin_cape", development:5.0});
CREATE (SKK:Country {name:"SKK", home_node:"lhasa", development:6.968});
CREATE (IDR:Country {name:"IDR", home_node:"gujarat", development:3.0});
CREATE (SBP:Country {name:"SBP", home_node:"ganges_delta", development:11.924});
CREATE (PTT:Country {name:"PTT", home_node:"ganges_delta", development:4.0});
CREATE (RTT:Country {name:"RTT", home_node:"ganges_delta", development:12.0});
CREATE (KLH:Country {name:"KLH", home_node:"ganges_delta", development:3.0});
CREATE (KJH:Country {name:"KJH", home_node:"ganges_delta", development:4.0});
CREATE (PRD:Country {name:"PRD", home_node:"ganges_delta", development:3.0});
CREATE (JPR:Country {name:"JPR", home_node:"ganges_delta", development:3.0});
CREATE (SRG:Country {name:"SRG", home_node:"doab", development:3.0});
CREATE (KND:Country {name:"KND", home_node:"comorin_cape", development:17.671});
CREATE (DNG:Country {name:"DNG", home_node:"doab", development:5.0});
CREATE (DTI:Country {name:"DTI", home_node:"doab", development:3.0});
CREATE (GRK:Country {name:"GRK", home_node:"lhasa", development:13.0});
CREATE (JML:Country {name:"JML", home_node:"doab", development:5.0});
CREATE (MKP:Country {name:"MKP", home_node:"ganges_delta", development:11.652});
CREATE (SRM:Country {name:"SRM", home_node:"doab", development:3.0});
CREATE (KTU:Country {name:"KTU", home_node:"lhasa", development:8.0});
CREATE (KMN:Country {name:"KMN", home_node:"doab", development:3.0});
CREATE (GNG:Country {name:"GNG", home_node:"comorin_cape", development:15.502});
CREATE (TNJ:Country {name:"TNJ", home_node:"comorin_cape", development:21.712});
CREATE (HSA:Country {name:"HSA", home_node:"lubeck", development:19.0});
CREATE (ABE:Country {name:"ABE", home_node:"chesapeake_bay", development:6.0});
CREATE (APA:Country {name:"APA", home_node:"california", development:6.0});
CREATE (ASI:Country {name:"ASI", home_node:"james_bay", development:6.0});
CREATE (BLA:Country {name:"BLA", home_node:"james_bay", development:6.0});
CREATE (CAD:Country {name:"CAD", home_node:"mississippi_river", development:6.0});
CREATE (CHI:Country {name:"CHI", home_node:"mississippi_river", development:19.0});
CREATE (CHO:Country {name:"CHO", home_node:"mississippi_river", development:13.0});
CREATE (CHY:Country {name:"CHY", home_node:"james_bay", development:9.0});
CREATE (COM:Country {name:"COM", home_node:"mississippi_river", development:6.0});
CREATE (FOX:Country {name:"FOX", home_node:"ohio", development:9.0});
CREATE (LEN:Country {name:"LEN", home_node:"chesapeake_bay", development:8.0});
CREATE (MAH:Country {name:"MAH", home_node:"st_lawrence", development:10.0});
CREATE (MIK:Country {name:"MIK", home_node:"st_lawrence", development:8.0});
CREATE (MMI:Country {name:"MMI", home_node:"ohio", development:6.0});
CREATE (NAH:Country {name:"NAH", home_node:"rio_grande", development:7.0});
CREATE (OJI:Country {name:"OJI", home_node:"ohio", development:9.0});
CREATE (OSA:Country {name:"OSA", home_node:"ohio", development:6.0});
CREATE (OTT:Country {name:"OTT", home_node:"ohio", development:6.0});
CREATE (PAW:Country {name:"PAW", home_node:"mississippi_river", development:7.0});
CREATE (PEQ:Country {name:"PEQ", home_node:"chesapeake_bay", development:9.0});
CREATE (PIM:Country {name:"PIM", home_node:"california", development:6.0});
CREATE (POT:Country {name:"POT", home_node:"ohio", development:7.0});
CREATE (POW:Country {name:"POW", home_node:"chesapeake_bay", development:8.0});
CREATE (SHO:Country {name:"SHO", home_node:"california", development:6.0});
CREATE (SIO:Country {name:"SIO", home_node:"ohio", development:6.0});
CREATE (SUS:Country {name:"SUS", home_node:"ohio", development:6.0});
CREATE (WCR:Country {name:"WCR", home_node:"james_bay", development:6.0});
CREATE (AIR:Country {name:"AIR", home_node:"katsina", development:26.856});
CREATE (BON:Country {name:"BON", home_node:"timbuktu", development:11.95});
CREATE (DAH:Country {name:"DAH", home_node:"timbuktu", development:3.0});
CREATE (DGB:Country {name:"DGB", home_node:"timbuktu", development:5.97});
CREATE (FUL:Country {name:"FUL", home_node:"timbuktu", development:59.464});
CREATE (JNN:Country {name:"JNN", home_node:"timbuktu", development:38.582});
CREATE (KAN:Country {name:"KAN", home_node:"katsina", development:24.52});
CREATE (KBU:Country {name:"KBU", home_node:"ivory_coast", development:9.356});
CREATE (KNG:Country {name:"KNG", home_node:"timbuktu", development:12.866});
CREATE (KTS:Country {name:"KTS", home_node:"katsina", development:21.93});
CREATE (NUP:Country {name:"NUP", home_node:"katsina", development:11.682});
CREATE (TMB:Country {name:"TMB", home_node:"timbuktu", development:47.586});
CREATE (YAO:Country {name:"YAO", home_node:"katsina", development:7.0});
CREATE (YAT:Country {name:"YAT", home_node:"timbuktu", development:16.628});
CREATE (ZAF:Country {name:"ZAF", home_node:"timbuktu", development:28.532});
CREATE (ZZZ:Country {name:"ZZZ", home_node:"katsina", development:11.97});
CREATE (NDO:Country {name:"NDO", home_node:"ivory_coast", development:12.94});
CREATE (JOH:Country {name:"JOH", home_node:"malacca", development:49.756});
CREATE (KED:Country {name:"KED", home_node:"malacca", development:10.0});
CREATE (PRK:Country {name:"PRK", home_node:"malacca", development:19.936});
CREATE (CHU:Country {name:"CHU", home_node:"girin", development:3.0});
CREATE (HOD:Country {name:"HOD", home_node:"girin", development:3.0});
CREATE (CHV:Country {name:"CHV", home_node:"girin", development:3.0});
CREATE (KMC:Country {name:"KMC", home_node:"girin", development:3.0});
CREATE (ARP:Country {name:"ARP", home_node:"james_bay", development:6.0});
CREATE (CNK:Country {name:"CNK", home_node:"california", development:8.0});
CREATE (HDA:Country {name:"HDA", home_node:"california", development:6.0});
CREATE (ITZ:Country {name:"ITZ", home_node:"mexico", development:26.188});
CREATE (KIO:Country {name:"KIO", home_node:"james_bay", development:6.0});
CREATE (SAL:Country {name:"SAL", home_node:"california", development:8.0});
CREATE (WIC:Country {name:"WIC", home_node:"rio_grande", development:6.0});
CREATE (BLM:Country {name:"BLM", home_node:"the_moluccas", development:39.56});
CREATE (BTN:Country {name:"BTN", home_node:"the_moluccas", development:8.0});
CREATE (CRB:Country {name:"CRB", home_node:"the_moluccas", development:28.136});
CREATE (PGR:Country {name:"PGR", home_node:"malacca", development:19.812});
CREATE (PLB:Country {name:"PLB", home_node:"malacca", development:19.664});
CREATE (SAK:Country {name:"SAK", home_node:"malacca", development:27.0});
CREATE (KUT:Country {name:"KUT", home_node:"malacca", development:28.84});
CREATE (BNJ:Country {name:"BNJ", home_node:"malacca", development:30.04});
CREATE (LNO:Country {name:"LNO", home_node:"philippines", development:8.0});
CREATE (LUW:Country {name:"LUW", home_node:"the_moluccas", development:18.0});
CREATE (MGD:Country {name:"MGD", home_node:"philippines", development:13.0});
CREATE (TER:Country {name:"TER", home_node:"the_moluccas", development:35.525});
CREATE (TID:Country {name:"TID", home_node:"the_moluccas", development:13.0});
CREATE (GUA:Country {name:"GUA", home_node:"laplata", development:6.0});
CREATE (ZNI:Country {name:"ZNI", home_node:"rio_grande", development:6.0});
CREATE (MSC:Country {name:"MSC", home_node:"rio_grande", development:6.0});
CREATE (LIP:Country {name:"LIP", home_node:"rio_grande", development:6.0});
CREATE (MIS:Country {name:"MIS", home_node:"panama", development:6.0});
CREATE (YAQ:Country {name:"YAQ", home_node:"california", development:6.0});
CREATE (YKT:Country {name:"YKT", home_node:"california", development:6.0});
CREATE (PSS:Country {name:"PSS", home_node:"wien", development:11.0});
CREATE (ROT:Country {name:"ROT", home_node:"rheinland", development:10.0});
CREATE (BYT:Country {name:"BYT", home_node:"rheinland", development:6.0});
CREATE (REG:Country {name:"REG", home_node:"wien", development:13.0});
CREATE (TTL:Country {name:"TTL", home_node:"rheinland", development:13.0});
CREATE (OPL:Country {name:"OPL", home_node:"krakow", development:16.0});
CREATE (WOL:Country {name:"WOL", home_node:"lubeck", development:31.55});
CREATE (STE:Country {name:"STE", home_node:"lubeck", development:20.91});
CREATE (GOS:Country {name:"GOS", home_node:"saxony", development:11.0});
CREATE (TNT:Country {name:"TNT", home_node:"wien", development:8.0});
CREATE (MLH:Country {name:"MLH", home_node:"rheinland", development:8.0});
CREATE (BAM:Country {name:"BAM", home_node:"rheinland", development:6.0});
CREATE (BNE:Country {name:"BNE", home_node:"the_moluccas", development:16.208});
CREATE (BEU:Country {name:"BEU", home_node:"malacca", development:18.87});
CREATE (SMB:Country {name:"SMB", home_node:"malacca", development:30.78});
CREATE (BRS:Country {name:"BRS", home_node:"malacca", development:16.886});
CREATE (JMB:Country {name:"JMB", home_node:"malacca", development:12.0});
CREATE (IND:Country {name:"IND", home_node:"malacca", development:12.0});
CREATE (TIW:Country {name:"TIW", home_node:"australia", development:8.0});
CREATE (LAR:Country {name:"LAR", home_node:"australia", development:8.0});
CREATE (YOL:Country {name:"YOL", home_node:"australia", development:8.0});
CREATE (YNU:Country {name:"YNU", home_node:"australia", development:6.0});
CREATE (AWN:Country {name:"AWN", home_node:"australia", development:8.0});
CREATE (GMI:Country {name:"GMI", home_node:"australia", development:13.0});
CREATE (MIA:Country {name:"MIA", home_node:"australia", development:8.0});
CREATE (EOR:Country {name:"EOR", home_node:"australia", development:14.0});
CREATE (KAU:Country {name:"KAU", home_node:"australia", development:10.0});
CREATE (PLW:Country {name:"PLW", home_node:"australia", development:9.0});
CREATE (WRU:Country {name:"WRU", home_node:"australia", development:14.0});
CREATE (NOO:Country {name:"NOO", home_node:"australia", development:12.0});
CREATE (MLG:Country {name:"MLG", home_node:"australia", development:8.0});
CREATE (MAA:Country {name:"MAA", home_node:"polynesia_node", development:8.0});
CREATE (TAN:Country {name:"TAN", home_node:"polynesia_node", development:10.0});
CREATE (TAK:Country {name:"TAK", home_node:"polynesia_node", development:7.0});
CREATE (TNK:Country {name:"TNK", home_node:"polynesia_node", development:10.0});
CREATE (TEA:Country {name:"TEA", home_node:"polynesia_node", development:10.0});
CREATE (TTT:Country {name:"TTT", home_node:"polynesia_node", development:10.0});
CREATE (WAI:Country {name:"WAI", home_node:"polynesia_node", development:4.0});
CREATE (HAW:Country {name:"HAW", home_node:"polynesia_node", development:7.0});
CREATE (MAU:Country {name:"MAU", home_node:"polynesia_node", development:5.0});
CREATE (OAH:Country {name:"OAH", home_node:"polynesia_node", development:5.0});
CREATE (KAA:Country {name:"KAA", home_node:"polynesia_node", development:5.0});
CREATE (TOG:Country {name:"TOG", home_node:"polynesia_node", development:8.0});
CREATE (SAM:Country {name:"SAM", home_node:"polynesia_node", development:8.0});
CREATE (VIL:Country {name:"VIL", home_node:"polynesia_node", development:7.0});
CREATE (VNL:Country {name:"VNL", home_node:"polynesia_node", development:5.0});
CREATE (LAI:Country {name:"LAI", home_node:"polynesia_node", development:3.0});
CREATE (ALT:Country {name:"ALT", home_node:"chesapeake_bay", development:6.0});
CREATE (ICH:Country {name:"ICH", home_node:"chesapeake_bay", development:6.0});
CREATE (COF:Country {name:"COF", home_node:"chesapeake_bay", development:8.0});
CREATE (JOA:Country {name:"JOA", home_node:"chesapeake_bay", development:8.0});
CREATE (SAT:Country {name:"SAT", home_node:"chesapeake_bay", development:6.0});
CREATE (ABI:Country {name:"ABI", home_node:"mississippi_river", development:6.0});
CREATE (COW:Country {name:"COW", home_node:"mississippi_river", development:9.0});
CREATE (NTZ:Country {name:"NTZ", home_node:"mississippi_river", development:6.0});
CREATE (PCH:Country {name:"PCH", home_node:"mississippi_river", development:6.0});
CREATE (QUI:Country {name:"QUI", home_node:"mississippi_river", development:8.0});
CREATE (CCA:Country {name:"CCA", home_node:"ohio", development:6.0});
CREATE (KSI:Country {name:"KSI", home_node:"chesapeake_bay", development:8.0});
CREATE (OEO:Country {name:"OEO", home_node:"ohio", development:9.0});
CREATE (NTC:Country {name:"NTC", home_node:"mississippi_river", development:8.0});
CREATE (HNI:Country {name:"HNI", home_node:"rio_grande", development:6.0});
CREATE (MOH:Country {name:"MOH", home_node:"ohio", development:9.0});
CREATE (ONE:Country {name:"ONE", home_node:"ohio", development:8.0});
CREATE (ONO:Country {name:"ONO", home_node:"ohio", development:11.0});
CREATE (CAY:Country {name:"CAY", home_node:"ohio", development:8.0});
CREATE (SEN:Country {name:"SEN", home_node:"ohio", development:7.0});
CREATE (TAH:Country {name:"TAH", home_node:"ohio", development:8.0});
CREATE (ATT:Country {name:"ATT", home_node:"ohio", development:11.0});
CREATE (AGG:Country {name:"AGG", home_node:"ohio", development:9.0});
CREATE (ATW:Country {name:"ATW", home_node:"ohio", development:9.0});
CREATE (ARN:Country {name:"ARN", home_node:"ohio", development:8.0});
CREATE (TIO:Country {name:"TIO", home_node:"ohio", development:8.0});
CREATE (OSH:Country {name:"OSH", home_node:"st_lawrence", development:8.0});
CREATE (ERI:Country {name:"ERI", home_node:"ohio", development:8.0});
CREATE (WEN:Country {name:"WEN", home_node:"ohio", development:8.0});
CREATE (TSC:Country {name:"TSC", home_node:"chesapeake_bay", development:8.0});
CREATE (CAO:Country {name:"CAO", home_node:"ohio", development:9.0});
CREATE (PEO:Country {name:"PEO", home_node:"ohio", development:6.0});
CREATE (KSK:Country {name:"KSK", home_node:"ohio", development:8.0});
CREATE (PEN:Country {name:"PEN", home_node:"st_lawrence", development:8.0});
CREATE (MLS:Country {name:"MLS", home_node:"st_lawrence", development:6.0});
CREATE (NEH:Country {name:"NEH", home_node:"james_bay", development:6.0});
CREATE (NAK:Country {name:"NAK", home_node:"james_bay", development:6.0});
CREATE (HWK:Country {name:"HWK", home_node:"ohio", development:8.0});
CREATE (CLG:Country {name:"CLG", home_node:"ohio", development:6.0});
CREATE (KSP:Country {name:"KSP", home_node:"ohio", development:6.0});
CREATE (MSG:Country {name:"MSG", home_node:"ohio", development:7.0});
CREATE (WCY:Country {name:"WCY", home_node:"mississippi_river", development:9.0});
CREATE (LAK:Country {name:"LAK", home_node:"mississippi_river", development:6.0});
CREATE (INN:Country {name:"INN", home_node:"st_lawrence", development:6.0});
CREATE (WAM:Country {name:"WAM", home_node:"chesapeake_bay", development:8.0});
CREATE (AGQ:Country {name:"AGQ", home_node:"st_lawrence", development:9.0});
CREATE (C00:Country {name:"C00", home_node:"brazil", development:132.806});
CREATE (C01:Country {name:"C01", home_node:"chesapeake_bay", development:18.0});
CREATE (C02:Country {name:"C02", home_node:"mexico", development:476.541});
CREATE (C03:Country {name:"C03", home_node:"carribean_trade", development:158.422});
CREATE (C04:Country {name:"C04", home_node:"lima", development:313.32});
CREATE (C05:Country {name:"C05", home_node:"lima", development:206.523});
CREATE (C06:Country {name:"C06", home_node:"cuiaba", development:128.781});
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (african_great_lakes)-[r:source{flow:0.21494132526877804}]->(zanzibar);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (kongo:Trade_node {name:"kongo"}) CREATE (african_great_lakes)-[r:source{flow:0.21494132526877804}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (kongo)-[r:source{flow:0.26544482159427696}]->(ivory_coast);
MATCH (kongo:Trade_node {name:"kongo"}), (zambezi:Trade_node {name:"zambezi"}) CREATE (kongo)-[r:source{flow:0.26544482159427696}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (zambezi)-[r:source{flow:0.45575444272634597}]->(zanzibar);
MATCH (patagonia:Trade_node {name:"patagonia"}), (laplata:Trade_node {name:"laplata"}) CREATE (patagonia)-[r:source{flow:0.05325}]->(laplata);
MATCH (patagonia:Trade_node {name:"patagonia"}), (cuiaba:Trade_node {name:"cuiaba"}) CREATE (patagonia)-[r:source{flow:0.05325}]->(cuiaba);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (brazil:Trade_node {name:"brazil"}) CREATE (amazonas_node)-[r:source{flow:0.22799999999999998}]->(brazil);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (amazonas_node)-[r:source{flow:0.22799999999999998}]->(carribean_trade);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (cuiaba:Trade_node {name:"cuiaba"}) CREATE (amazonas_node)-[r:source{flow:0.22799999999999998}]->(cuiaba);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (mississippi_river:Trade_node {name:"mississippi_river"}) CREATE (rio_grande)-[r:source{flow:0.22730224418698855}]->(mississippi_river);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (mexico:Trade_node {name:"mexico"}) CREATE (rio_grande)-[r:source{flow:0.22730224418698855}]->(mexico);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (california:Trade_node {name:"california"}) CREATE (rio_grande)-[r:source{flow:0.22730224418698855}]->(california);
MATCH (james_bay:Trade_node {name:"james_bay"}), (st_lawrence:Trade_node {name:"st_lawrence"}) CREATE (james_bay)-[r:source{flow:0.032983879966524535}]->(st_lawrence);
MATCH (james_bay:Trade_node {name:"james_bay"}), (california:Trade_node {name:"california"}) CREATE (james_bay)-[r:source{flow:0.032983879966524535}]->(california);
MATCH (california:Trade_node {name:"california"}), (mexico:Trade_node {name:"mexico"}) CREATE (california)-[r:source{flow:0.12127120876852818}]->(mexico);
MATCH (california:Trade_node {name:"california"}), (mississippi_river:Trade_node {name:"mississippi_river"}) CREATE (california)-[r:source{flow:0.12127120876852818}]->(mississippi_river);
MATCH (california:Trade_node {name:"california"}), (girin:Trade_node {name:"girin"}) CREATE (california)-[r:source{flow:0.12127120876852818}]->(girin);
MATCH (california:Trade_node {name:"california"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (california)-[r:source{flow:0.12127120876852818}]->(polynesia_node);
MATCH (girin:Trade_node {name:"girin"}), (siberia:Trade_node {name:"siberia"}) CREATE (girin)-[r:source{flow:0.21487389847756333}]->(siberia);
MATCH (girin:Trade_node {name:"girin"}), (beijing:Trade_node {name:"beijing"}) CREATE (girin)-[r:source{flow:0.21487389847756333}]->(beijing);
MATCH (girin:Trade_node {name:"girin"}), (nippon:Trade_node {name:"nippon"}) CREATE (girin)-[r:source{flow:0.21487389847756333}]->(nippon);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (mississippi_river)-[r:source{flow:0.3627992146635578}]->(carribean_trade);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (ohio:Trade_node {name:"ohio"}) CREATE (mississippi_river)-[r:source{flow:0.3627992146635578}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (chesapeake_bay:Trade_node {name:"chesapeake_bay"}) CREATE (ohio)-[r:source{flow:0.257706710591016}]->(chesapeake_bay);
MATCH (ohio:Trade_node {name:"ohio"}), (st_lawrence:Trade_node {name:"st_lawrence"}) CREATE (ohio)-[r:source{flow:0.257706710591016}]->(st_lawrence);
MATCH (mexico:Trade_node {name:"mexico"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (mexico)-[r:source{flow:0.36095812352246676}]->(carribean_trade);
MATCH (mexico:Trade_node {name:"mexico"}), (panama:Trade_node {name:"panama"}) CREATE (mexico)-[r:source{flow:0.36095812352246676}]->(panama);
MATCH (mexico:Trade_node {name:"mexico"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (mexico)-[r:source{flow:0.36095812352246676}]->(polynesia_node);
MATCH (lhasa:Trade_node {name:"lhasa"}), (lahore:Trade_node {name:"lahore"}) CREATE (lhasa)-[r:source{flow:0.09899779750309899}]->(lahore);
MATCH (lhasa:Trade_node {name:"lhasa"}), (chengdu:Trade_node {name:"chengdu"}) CREATE (lhasa)-[r:source{flow:0.09899779750309899}]->(chengdu);
MATCH (lhasa:Trade_node {name:"lhasa"}), (ganges_delta:Trade_node {name:"ganges_delta"}) CREATE (lhasa)-[r:source{flow:0.09899779750309899}]->(ganges_delta);
MATCH (chengdu:Trade_node {name:"chengdu"}), (canton:Trade_node {name:"canton"}) CREATE (chengdu)-[r:source{flow:0.3777578379885866}]->(canton);
MATCH (chengdu:Trade_node {name:"chengdu"}), (xian:Trade_node {name:"xian"}) CREATE (chengdu)-[r:source{flow:0.3777578379885866}]->(xian);
MATCH (chengdu:Trade_node {name:"chengdu"}), (burma:Trade_node {name:"burma"}) CREATE (chengdu)-[r:source{flow:0.3777578379885866}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (ganges_delta:Trade_node {name:"ganges_delta"}) CREATE (burma)-[r:source{flow:0.5895780666159046}]->(ganges_delta);
MATCH (burma:Trade_node {name:"burma"}), (gulf_of_siam:Trade_node {name:"gulf_of_siam"}) CREATE (burma)-[r:source{flow:0.5895780666159046}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (malacca:Trade_node {name:"malacca"}) CREATE (gulf_of_siam)-[r:source{flow:0.9425831692060987}]->(malacca);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (canton:Trade_node {name:"canton"}) CREATE (gulf_of_siam)-[r:source{flow:0.9425831692060987}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (malacca:Trade_node {name:"malacca"}) CREATE (canton)-[r:source{flow:0.8185176458799156}]->(malacca);
MATCH (canton:Trade_node {name:"canton"}), (hangzhou:Trade_node {name:"hangzhou"}) CREATE (canton)-[r:source{flow:0.8185176458799156}]->(hangzhou);
MATCH (canton:Trade_node {name:"canton"}), (philippines:Trade_node {name:"philippines"}) CREATE (canton)-[r:source{flow:0.8185176458799156}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (the_moluccas:Trade_node {name:"the_moluccas"}) CREATE (philippines)-[r:source{flow:0.6858486611273329}]->(the_moluccas);
MATCH (philippines:Trade_node {name:"philippines"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (philippines)-[r:source{flow:0.6858486611273329}]->(polynesia_node);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (laplata:Trade_node {name:"laplata"}) CREATE (cuiaba)-[r:source{flow:0.29523881959173603}]->(laplata);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (lima:Trade_node {name:"lima"}) CREATE (cuiaba)-[r:source{flow:0.29523881959173603}]->(lima);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (brazil:Trade_node {name:"brazil"}) CREATE (cuiaba)-[r:source{flow:0.29523881959173603}]->(brazil);
MATCH (lima:Trade_node {name:"lima"}), (panama:Trade_node {name:"panama"}) CREATE (lima)-[r:source{flow:0.0019436014301899968}]->(panama);
MATCH (lima:Trade_node {name:"lima"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (lima)-[r:source{flow:0.0019436014301899968}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (nippon:Trade_node {name:"nippon"}) CREATE (polynesia_node)-[r:source{flow:0.09757021305415363}]->(nippon);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (australia:Trade_node {name:"australia"}) CREATE (polynesia_node)-[r:source{flow:0.09757021305415363}]->(australia);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (panama:Trade_node {name:"panama"}) CREATE (polynesia_node)-[r:source{flow:0.09757021305415363}]->(panama);
MATCH (australia:Trade_node {name:"australia"}), (the_moluccas:Trade_node {name:"the_moluccas"}) CREATE (australia)-[r:source{flow:0.0}]->(the_moluccas);
MATCH (nippon:Trade_node {name:"nippon"}), (hangzhou:Trade_node {name:"hangzhou"}) CREATE (nippon)-[r:source{flow:0.3353680136653585}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (xian:Trade_node {name:"xian"}) CREATE (hangzhou)-[r:source{flow:1.8159299904204238}]->(xian);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (beijing:Trade_node {name:"beijing"}) CREATE (hangzhou)-[r:source{flow:1.8159299904204238}]->(beijing);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (malacca:Trade_node {name:"malacca"}) CREATE (hangzhou)-[r:source{flow:1.8159299904204238}]->(malacca);
MATCH (xian:Trade_node {name:"xian"}), (beijing:Trade_node {name:"beijing"}) CREATE (xian)-[r:source{flow:1.7165930549573647}]->(beijing);
MATCH (xian:Trade_node {name:"xian"}), (yumen:Trade_node {name:"yumen"}) CREATE (xian)-[r:source{flow:1.7165930549573647}]->(yumen);
MATCH (beijing:Trade_node {name:"beijing"}), (yumen:Trade_node {name:"yumen"}) CREATE (beijing)-[r:source{flow:2.1042812336076686}]->(yumen);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (malacca:Trade_node {name:"malacca"}) CREATE (the_moluccas)-[r:source{flow:1.214884226200091}]->(malacca);
MATCH (siberia:Trade_node {name:"siberia"}), (kazan:Trade_node {name:"kazan"}) CREATE (siberia)-[r:source{flow:0.9719043983503604}]->(kazan);
MATCH (siberia:Trade_node {name:"siberia"}), (samarkand:Trade_node {name:"samarkand"}) CREATE (siberia)-[r:source{flow:0.9719043983503604}]->(samarkand);
MATCH (yumen:Trade_node {name:"yumen"}), (samarkand:Trade_node {name:"samarkand"}) CREATE (yumen)-[r:source{flow:2.1205020444188114}]->(samarkand);
MATCH (malacca:Trade_node {name:"malacca"}), (ganges_delta:Trade_node {name:"ganges_delta"}) CREATE (malacca)-[r:source{flow:0.7017934445655758}]->(ganges_delta);
MATCH (malacca:Trade_node {name:"malacca"}), (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}) CREATE (malacca)-[r:source{flow:0.7017934445655758}]->(cape_of_good_hope);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (comorin_cape:Trade_node {name:"comorin_cape"}) CREATE (ganges_delta)-[r:source{flow:1.7730878404676422}]->(comorin_cape);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (doab:Trade_node {name:"doab"}) CREATE (ganges_delta)-[r:source{flow:1.7730878404676422}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (deccan:Trade_node {name:"deccan"}) CREATE (doab)-[r:source{flow:0.6527326324061982}]->(deccan);
MATCH (doab:Trade_node {name:"doab"}), (lahore:Trade_node {name:"lahore"}) CREATE (doab)-[r:source{flow:0.6527326324061982}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (samarkand:Trade_node {name:"samarkand"}) CREATE (lahore)-[r:source{flow:1.1462669544993853}]->(samarkand);
MATCH (lahore:Trade_node {name:"lahore"}), (persia:Trade_node {name:"persia"}) CREATE (lahore)-[r:source{flow:1.1462669544993853}]->(persia);
MATCH (lahore:Trade_node {name:"lahore"}), (gujarat:Trade_node {name:"gujarat"}) CREATE (lahore)-[r:source{flow:1.1462669544993853}]->(gujarat);
MATCH (deccan:Trade_node {name:"deccan"}), (gujarat:Trade_node {name:"gujarat"}) CREATE (deccan)-[r:source{flow:1.0991832836472533}]->(gujarat);
MATCH (deccan:Trade_node {name:"deccan"}), (comorin_cape:Trade_node {name:"comorin_cape"}) CREATE (deccan)-[r:source{flow:1.0991832836472533}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (gulf_of_aden:Trade_node {name:"gulf_of_aden"}) CREATE (comorin_cape)-[r:source{flow:0.9552060704294735}]->(gulf_of_aden);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (gujarat:Trade_node {name:"gujarat"}) CREATE (comorin_cape)-[r:source{flow:0.9552060704294735}]->(gujarat);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}) CREATE (comorin_cape)-[r:source{flow:0.9552060704294735}]->(cape_of_good_hope);
MATCH (gujarat:Trade_node {name:"gujarat"}), (gulf_of_aden:Trade_node {name:"gulf_of_aden"}) CREATE (gujarat)-[r:source{flow:1.1779603779505028}]->(gulf_of_aden);
MATCH (gujarat:Trade_node {name:"gujarat"}), (hormuz:Trade_node {name:"hormuz"}) CREATE (gujarat)-[r:source{flow:1.1779603779505028}]->(hormuz);
MATCH (gujarat:Trade_node {name:"gujarat"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (gujarat)-[r:source{flow:1.1779603779505028}]->(zanzibar);
MATCH (katsina:Trade_node {name:"katsina"}), (timbuktu:Trade_node {name:"timbuktu"}) CREATE (katsina)-[r:source{flow:0.20742954295464808}]->(timbuktu);
MATCH (katsina:Trade_node {name:"katsina"}), (tunis:Trade_node {name:"tunis"}) CREATE (katsina)-[r:source{flow:0.20742954295464808}]->(tunis);
MATCH (katsina:Trade_node {name:"katsina"}), (ethiopia:Trade_node {name:"ethiopia"}) CREATE (katsina)-[r:source{flow:0.20742954295464808}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (alexandria:Trade_node {name:"alexandria"}) CREATE (ethiopia)-[r:source{flow:0.17231366573628}]->(alexandria);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (gulf_of_aden:Trade_node {name:"gulf_of_aden"}) CREATE (ethiopia)-[r:source{flow:0.17231366573628}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (gulf_of_aden)-[r:source{flow:0.7275024939518971}]->(zanzibar);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (alexandria:Trade_node {name:"alexandria"}) CREATE (gulf_of_aden)-[r:source{flow:0.7275024939518971}]->(alexandria);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (hormuz:Trade_node {name:"hormuz"}) CREATE (gulf_of_aden)-[r:source{flow:0.7275024939518971}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (basra:Trade_node {name:"basra"}) CREATE (hormuz)-[r:source{flow:1.8552291062979176}]->(basra);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}) CREATE (zanzibar)-[r:source{flow:0.6510904802286137}]->(cape_of_good_hope);
MATCH (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (cape_of_good_hope)-[r:source{flow:0.0}]->(ivory_coast);
MATCH (basra:Trade_node {name:"basra"}), (aleppo:Trade_node {name:"aleppo"}) CREATE (basra)-[r:source{flow:0.7774925028536358}]->(aleppo);
MATCH (basra:Trade_node {name:"basra"}), (persia:Trade_node {name:"persia"}) CREATE (basra)-[r:source{flow:0.7774925028536358}]->(persia);
MATCH (samarkand:Trade_node {name:"samarkand"}), (persia:Trade_node {name:"persia"}) CREATE (samarkand)-[r:source{flow:0.19895984790951624}]->(persia);
MATCH (samarkand:Trade_node {name:"samarkand"}), (astrakhan:Trade_node {name:"astrakhan"}) CREATE (samarkand)-[r:source{flow:0.19895984790951624}]->(astrakhan);
MATCH (persia:Trade_node {name:"persia"}), (aleppo:Trade_node {name:"aleppo"}) CREATE (persia)-[r:source{flow:0.37804176667060535}]->(aleppo);
MATCH (persia:Trade_node {name:"persia"}), (astrakhan:Trade_node {name:"astrakhan"}) CREATE (persia)-[r:source{flow:0.37804176667060535}]->(astrakhan);
MATCH (aleppo:Trade_node {name:"aleppo"}), (constantinople:Trade_node {name:"constantinople"}) CREATE (aleppo)-[r:source{flow:1.4053277457501134}]->(constantinople);
MATCH (aleppo:Trade_node {name:"aleppo"}), (alexandria:Trade_node {name:"alexandria"}) CREATE (aleppo)-[r:source{flow:1.4053277457501134}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (constantinople:Trade_node {name:"constantinople"}) CREATE (alexandria)-[r:source{flow:1.2766917148422745}]->(constantinople);
MATCH (alexandria:Trade_node {name:"alexandria"}), (venice:Trade_node {name:"venice"}) CREATE (alexandria)-[r:source{flow:1.2766917148422745}]->(venice);
MATCH (alexandria:Trade_node {name:"alexandria"}), (genua:Trade_node {name:"genua"}) CREATE (alexandria)-[r:source{flow:1.2766917148422745}]->(genua);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (kazan:Trade_node {name:"kazan"}) CREATE (astrakhan)-[r:source{flow:0.5808258743847293}]->(kazan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (crimea:Trade_node {name:"crimea"}) CREATE (astrakhan)-[r:source{flow:0.5808258743847293}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (kiev:Trade_node {name:"kiev"}) CREATE (crimea)-[r:source{flow:0.5422103888924826}]->(kiev);
MATCH (crimea:Trade_node {name:"crimea"}), (constantinople:Trade_node {name:"constantinople"}) CREATE (crimea)-[r:source{flow:0.5422103888924826}]->(constantinople);
MATCH (crimea:Trade_node {name:"crimea"}), (pest:Trade_node {name:"pest"}) CREATE (crimea)-[r:source{flow:0.5422103888924826}]->(pest);
MATCH (constantinople:Trade_node {name:"constantinople"}), (ragusa:Trade_node {name:"ragusa"}) CREATE (constantinople)-[r:source{flow:0.6066061546017363}]->(ragusa);
MATCH (kiev:Trade_node {name:"kiev"}), (novgorod:Trade_node {name:"novgorod"}) CREATE (kiev)-[r:source{flow:1.050580227084277}]->(novgorod);
MATCH (kiev:Trade_node {name:"kiev"}), (krakow:Trade_node {name:"krakow"}) CREATE (kiev)-[r:source{flow:1.050580227084277}]->(krakow);
MATCH (kazan:Trade_node {name:"kazan"}), (novgorod:Trade_node {name:"novgorod"}) CREATE (kazan)-[r:source{flow:2.342683393185922}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (baltic_sea:Trade_node {name:"baltic_sea"}) CREATE (novgorod)-[r:source{flow:0.6902170260781757}]->(baltic_sea);
MATCH (novgorod:Trade_node {name:"novgorod"}), (white_sea:Trade_node {name:"white_sea"}) CREATE (novgorod)-[r:source{flow:0.6902170260781757}]->(white_sea);
MATCH (laplata:Trade_node {name:"laplata"}), (brazil:Trade_node {name:"brazil"}) CREATE (laplata)-[r:source{flow:0.7259317552664258}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (brazil)-[r:source{flow:0.26381321888050796}]->(ivory_coast);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (safi:Trade_node {name:"safi"}) CREATE (timbuktu)-[r:source{flow:0.471526706192519}]->(safi);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (timbuktu)-[r:source{flow:0.471526706192519}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (ivory_coast)-[r:source{flow:0.31947171374093747}]->(carribean_trade);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (bordeaux:Trade_node {name:"bordeaux"}) CREATE (ivory_coast)-[r:source{flow:0.31947171374093747}]->(bordeaux);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (ivory_coast)-[r:source{flow:0.31947171374093747}]->(english_channel);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (ivory_coast)-[r:source{flow:0.31947171374093747}]->(sevilla);
MATCH (tunis:Trade_node {name:"tunis"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (tunis)-[r:source{flow:0.2320816464253157}]->(sevilla);
MATCH (tunis:Trade_node {name:"tunis"}), (valencia:Trade_node {name:"valencia"}) CREATE (tunis)-[r:source{flow:0.2320816464253157}]->(valencia);
MATCH (tunis:Trade_node {name:"tunis"}), (genua:Trade_node {name:"genua"}) CREATE (tunis)-[r:source{flow:0.2320816464253157}]->(genua);
MATCH (ragusa:Trade_node {name:"ragusa"}), (pest:Trade_node {name:"pest"}) CREATE (ragusa)-[r:source{flow:0.760075169137202}]->(pest);
MATCH (ragusa:Trade_node {name:"ragusa"}), (venice:Trade_node {name:"venice"}) CREATE (ragusa)-[r:source{flow:0.760075169137202}]->(venice);
MATCH (ragusa:Trade_node {name:"ragusa"}), (genua:Trade_node {name:"genua"}) CREATE (ragusa)-[r:source{flow:0.760075169137202}]->(genua);
MATCH (safi:Trade_node {name:"safi"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (safi)-[r:source{flow:0.5860530132272567}]->(sevilla);
MATCH (pest:Trade_node {name:"pest"}), (wien:Trade_node {name:"wien"}) CREATE (pest)-[r:source{flow:0.8799443934744479}]->(wien);
MATCH (pest:Trade_node {name:"pest"}), (krakow:Trade_node {name:"krakow"}) CREATE (pest)-[r:source{flow:0.8799443934744479}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (wien:Trade_node {name:"wien"}) CREATE (krakow)-[r:source{flow:0.8494251277691885}]->(wien);
MATCH (krakow:Trade_node {name:"krakow"}), (saxony:Trade_node {name:"saxony"}) CREATE (krakow)-[r:source{flow:0.8494251277691885}]->(saxony);
MATCH (krakow:Trade_node {name:"krakow"}), (baltic_sea:Trade_node {name:"baltic_sea"}) CREATE (krakow)-[r:source{flow:0.8494251277691885}]->(baltic_sea);
MATCH (wien:Trade_node {name:"wien"}), (venice:Trade_node {name:"venice"}) CREATE (wien)-[r:source{flow:0.8838113881559667}]->(venice);
MATCH (wien:Trade_node {name:"wien"}), (rheinland:Trade_node {name:"rheinland"}) CREATE (wien)-[r:source{flow:0.8838113881559667}]->(rheinland);
MATCH (wien:Trade_node {name:"wien"}), (saxony:Trade_node {name:"saxony"}) CREATE (wien)-[r:source{flow:0.8838113881559667}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (rheinland:Trade_node {name:"rheinland"}) CREATE (saxony)-[r:source{flow:0.960918136373929}]->(rheinland);
MATCH (saxony:Trade_node {name:"saxony"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (saxony)-[r:source{flow:0.960918136373929}]->(lubeck);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (baltic_sea)-[r:source{flow:2.3056108775685162}]->(lubeck);
MATCH (rheinland:Trade_node {name:"rheinland"}), (champagne:Trade_node {name:"champagne"}) CREATE (rheinland)-[r:source{flow:0.7923101091696904}]->(champagne);
MATCH (rheinland:Trade_node {name:"rheinland"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (rheinland)-[r:source{flow:0.7923101091696904}]->(lubeck);
MATCH (panama:Trade_node {name:"panama"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (panama)-[r:source{flow:2.7536922408286855}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (chesapeake_bay:Trade_node {name:"chesapeake_bay"}) CREATE (carribean_trade)-[r:source{flow:0.6495287266294408}]->(chesapeake_bay);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (bordeaux:Trade_node {name:"bordeaux"}) CREATE (carribean_trade)-[r:source{flow:0.6495287266294408}]->(bordeaux);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (carribean_trade)-[r:source{flow:0.6495287266294408}]->(sevilla);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (st_lawrence:Trade_node {name:"st_lawrence"}) CREATE (chesapeake_bay)-[r:source{flow:0.711763626050034}]->(st_lawrence);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (chesapeake_bay)-[r:source{flow:0.711763626050034}]->(english_channel);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (north_sea:Trade_node {name:"north_sea"}) CREATE (st_lawrence)-[r:source{flow:0.4628376786811334}]->(north_sea);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (bordeaux:Trade_node {name:"bordeaux"}) CREATE (st_lawrence)-[r:source{flow:0.4628376786811334}]->(bordeaux);
MATCH (white_sea:Trade_node {name:"white_sea"}), (north_sea:Trade_node {name:"north_sea"}) CREATE (white_sea)-[r:source{flow:1.2388639386910425}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (north_sea)-[r:source{flow:0.9723526105749888}]->(english_channel);
MATCH (north_sea:Trade_node {name:"north_sea"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (north_sea)-[r:source{flow:0.9723526105749888}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (lubeck)-[r:source{flow:0.5468318456119889}]->(english_channel);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (champagne:Trade_node {name:"champagne"}) CREATE (bordeaux)-[r:source{flow:2.8248566361739833}]->(champagne);
MATCH (sevilla:Trade_node {name:"sevilla"}), (valencia:Trade_node {name:"valencia"}) CREATE (sevilla)-[r:source{flow:0.08573441133127971}]->(valencia);
MATCH (champagne:Trade_node {name:"champagne"}), (genua:Trade_node {name:"genua"}) CREATE (champagne)-[r:source{flow:1.5169438746730877}]->(genua);
MATCH (champagne:Trade_node {name:"champagne"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (champagne)-[r:source{flow:1.5169438746730877}]->(english_channel);
MATCH (valencia:Trade_node {name:"valencia"}), (genua:Trade_node {name:"genua"}) CREATE (valencia)-[r:source{flow:1.963353430322213}]->(genua);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 15.745,calculated_trading_power: 15.745}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KON:Country {name:"KON"}) CREATE (KON)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 44.71,calculated_trading_power: 44.71}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.241,calculated_trading_power: 7.241}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.347,calculated_trading_power: 2.347}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (LUB:Country {name:"LUB"}) CREATE (LUB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 42.223,calculated_trading_power: 42.223}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (LND:Country {name:"LND"}) CREATE (LND)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.945,calculated_trading_power: 10.945}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KZB:Country {name:"KZB"}) CREATE (KZB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.081,calculated_trading_power: 9.081}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KUB:Country {name:"KUB"}) CREATE (KUB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.398,calculated_trading_power: 7.398}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (RWA:Country {name:"RWA"}) CREATE (RWA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 17.475,calculated_trading_power: 17.475}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BUU:Country {name:"BUU"}) CREATE (BUU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.269,calculated_trading_power: 12.269}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BUG:Country {name:"BUG"}) CREATE (BUG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 26.691,calculated_trading_power: 26.691}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (NKO:Country {name:"NKO"}) CREATE (NKO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.269,calculated_trading_power: 12.269}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KRW:Country {name:"KRW"}) CREATE (KRW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 15.124,calculated_trading_power: 15.124}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BNY:Country {name:"BNY"}) CREATE (BNY)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 15.194,calculated_trading_power: 15.194}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BSG:Country {name:"BSG"}) CREATE (BSG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.269,calculated_trading_power: 12.269}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (UBH:Country {name:"UBH"}) CREATE (UBH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.632,calculated_trading_power: 18.632}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.926,calculated_trading_power: 2.926}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.409,calculated_trading_power: 2.409}]->(african_great_lakes);
MATCH (kongo:Trade_node {name:"kongo"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.053,calculated_trading_power: 12.053}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.743,calculated_trading_power: 5.743}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KON:Country {name:"KON"}) CREATE (KON)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 70.849,calculated_trading_power: 70.849}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (ZIM:Country {name:"ZIM"}) CREATE (ZIM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 41.604,calculated_trading_power: 41.604}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (LOA:Country {name:"LOA"}) CREATE (LOA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.566,calculated_trading_power: 9.566}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.678,calculated_trading_power: 3.678}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (SYO:Country {name:"SYO"}) CREATE (SYO)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.25,calculated_trading_power: 11.25}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KSJ:Country {name:"KSJ"}) CREATE (KSJ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 16.318,calculated_trading_power: 16.318}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (LUB:Country {name:"LUB"}) CREATE (LUB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 67.573,calculated_trading_power: 67.573}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (LND:Country {name:"LND"}) CREATE (LND)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 27.181,calculated_trading_power: 27.181}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (CKW:Country {name:"CKW"}) CREATE (CKW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.269,calculated_trading_power: 12.269}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KIK:Country {name:"KIK"}) CREATE (KIK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.149,calculated_trading_power: 10.149}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KZB:Country {name:"KZB"}) CREATE (KZB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 21.115,calculated_trading_power: 21.115}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (YAK:Country {name:"YAK"}) CREATE (YAK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.583,calculated_trading_power: 14.583}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KUB:Country {name:"KUB"}) CREATE (KUB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 24.589,calculated_trading_power: 24.589}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (MRA:Country {name:"MRA"}) CREATE (MRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 13.445,calculated_trading_power: 13.445}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (TBK:Country {name:"TBK"}) CREATE (TBK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.109,calculated_trading_power: 6.109}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KBU:Country {name:"KBU"}) CREATE (KBU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.209,calculated_trading_power: 2.209}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (NDO:Country {name:"NDO"}) CREATE (NDO)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.964,calculated_trading_power: 7.964}]->(kongo);
MATCH (zambezi:Trade_node {name:"zambezi"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 33.932,calculated_trading_power: 33.932}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 43.427,calculated_trading_power: 43.427}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (ZIM:Country {name:"ZIM"}) CREATE (ZIM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 66.408,calculated_trading_power: 66.408}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.347,calculated_trading_power: 2.347}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (MRA:Country {name:"MRA"}) CREATE (MRA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 35.085,calculated_trading_power: 35.085}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (LDU:Country {name:"LDU"}) CREATE (LDU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 22.742,calculated_trading_power: 22.742}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (TBK:Country {name:"TBK"}) CREATE (TBK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.998,calculated_trading_power: 14.998}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (RZW:Country {name:"RZW"}) CREATE (RZW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.484,calculated_trading_power: 13.484}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.361,calculated_trading_power: 14.361}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.817,calculated_trading_power: 10.817}]->(zambezi);
MATCH (patagonia:Trade_node {name:"patagonia"}), (C05:Country {name:"C05"}) CREATE (C05)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 18.547,calculated_trading_power: 18.547}]->(patagonia);
MATCH (patagonia:Trade_node {name:"patagonia"}), (C06:Country {name:"C06"}) CREATE (C06)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 74.036,calculated_trading_power: 74.036}]->(patagonia);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 0.5,calculated_trading_power: 0.5}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.902,calculated_trading_power: 2.902}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (C00:Country {name:"C00"}) CREATE (C00)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 58.96,calculated_trading_power: 58.96}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (C03:Country {name:"C03"}) CREATE (C03)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 59.316,calculated_trading_power: 59.316}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 36.897,calculated_trading_power: 36.897}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (C05:Country {name:"C05"}) CREATE (C05)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 13.386,calculated_trading_power: 13.386}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (C06:Country {name:"C06"}) CREATE (C06)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 34.408,calculated_trading_power: 34.408}]->(amazonas_node);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (CAD:Country {name:"CAD"}) CREATE (CAD)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (CHO:Country {name:"CHO"}) CREATE (CHO)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (NAH:Country {name:"NAH"}) CREATE (NAH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.758,calculated_trading_power: 8.758}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (CNK:Country {name:"CNK"}) CREATE (CNK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.628,calculated_trading_power: 3.628}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (WIC:Country {name:"WIC"}) CREATE (WIC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (ZNI:Country {name:"ZNI"}) CREATE (ZNI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (MSC:Country {name:"MSC"}) CREATE (MSC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (LIP:Country {name:"LIP"}) CREATE (LIP)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (NTZ:Country {name:"NTZ"}) CREATE (NTZ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (QUI:Country {name:"QUI"}) CREATE (QUI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (NTC:Country {name:"NTC"}) CREATE (NTC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (HNI:Country {name:"HNI"}) CREATE (HNI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (C02:Country {name:"C02"}) CREATE (C02)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 120.094,calculated_trading_power: 120.094}]->(rio_grande);
MATCH (james_bay:Trade_node {name:"james_bay"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.17,calculated_trading_power: 2.17}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (ASI:Country {name:"ASI"}) CREATE (ASI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (BLA:Country {name:"BLA"}) CREATE (BLA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.619,calculated_trading_power: 8.619}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (CHY:Country {name:"CHY"}) CREATE (CHY)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.424,calculated_trading_power: 9.424}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (WCR:Country {name:"WCR"}) CREATE (WCR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (ARP:Country {name:"ARP"}) CREATE (ARP)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (CNK:Country {name:"CNK"}) CREATE (CNK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.628,calculated_trading_power: 3.628}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (KIO:Country {name:"KIO"}) CREATE (KIO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (NEH:Country {name:"NEH"}) CREATE (NEH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.619,calculated_trading_power: 8.619}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (NAK:Country {name:"NAK"}) CREATE (NAK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(james_bay);
MATCH (california:Trade_node {name:"california"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.165,calculated_trading_power: 1.165}]->(california);
MATCH (california:Trade_node {name:"california"}), (APA:Country {name:"APA"}) CREATE (APA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(california);
MATCH (california:Trade_node {name:"california"}), (PIM:Country {name:"PIM"}) CREATE (PIM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(california);
MATCH (california:Trade_node {name:"california"}), (SHO:Country {name:"SHO"}) CREATE (SHO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(california);
MATCH (california:Trade_node {name:"california"}), (CNK:Country {name:"CNK"}) CREATE (CNK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 25.204,calculated_trading_power: 25.204}]->(california);
MATCH (california:Trade_node {name:"california"}), (HDA:Country {name:"HDA"}) CREATE (HDA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.801,calculated_trading_power: 8.801}]->(california);
MATCH (california:Trade_node {name:"california"}), (SAL:Country {name:"SAL"}) CREATE (SAL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.565,calculated_trading_power: 9.565}]->(california);
MATCH (california:Trade_node {name:"california"}), (YAQ:Country {name:"YAQ"}) CREATE (YAQ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.801,calculated_trading_power: 8.801}]->(california);
MATCH (california:Trade_node {name:"california"}), (YKT:Country {name:"YKT"}) CREATE (YKT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.499,calculated_trading_power: 8.499}]->(california);
MATCH (california:Trade_node {name:"california"}), (C02:Country {name:"C02"}) CREATE (C02)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 93.579,calculated_trading_power: 93.579}]->(california);
MATCH (girin:Trade_node {name:"girin"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.757,calculated_trading_power: 14.757}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (MRI:Country {name:"MRI"}) CREATE (MRI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.538,calculated_trading_power: 4.538}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (TKG:Country {name:"TKG"}) CREATE (TKG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 21.014,calculated_trading_power: 21.014}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (ANU:Country {name:"ANU"}) CREATE (ANU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.986,calculated_trading_power: 2.986}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (KHA:Country {name:"KHA"}) CREATE (KHA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 16.52,calculated_trading_power: 16.52}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (KOR:Country {name:"KOR"}) CREATE (KOR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 16.066,calculated_trading_power: 16.066}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (MCH:Country {name:"MCH"}) CREATE (MCH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 105.503,calculated_trading_power: 105.503}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 23.166,calculated_trading_power: 23.166}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (NVK:Country {name:"NVK"}) CREATE (NVK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 38.515,calculated_trading_power: 38.515}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (SOL:Country {name:"SOL"}) CREATE (SOL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 39.556,calculated_trading_power: 39.556}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (EJZ:Country {name:"EJZ"}) CREATE (EJZ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.506,calculated_trading_power: 8.506}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (NHX:Country {name:"NHX"}) CREATE (NHX)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.322,calculated_trading_power: 11.322}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (MYR:Country {name:"MYR"}) CREATE (MYR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 7.274,calculated_trading_power: 7.274}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (MHX:Country {name:"MHX"}) CREATE (MHX)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.013,calculated_trading_power: 9.013}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (KRC:Country {name:"KRC"}) CREATE (KRC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.539,calculated_trading_power: 14.539}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (CHU:Country {name:"CHU"}) CREATE (CHU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.272,calculated_trading_power: 14.272}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (HOD:Country {name:"HOD"}) CREATE (HOD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.628,calculated_trading_power: 8.628}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (CHV:Country {name:"CHV"}) CREATE (CHV)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.727,calculated_trading_power: 8.727}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (KMC:Country {name:"KMC"}) CREATE (KMC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.727,calculated_trading_power: 8.727}]->(girin);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CAD:Country {name:"CAD"}) CREATE (CAD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.359,calculated_trading_power: 9.359}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CHI:Country {name:"CHI"}) CREATE (CHI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.808,calculated_trading_power: 11.808}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CHO:Country {name:"CHO"}) CREATE (CHO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.223,calculated_trading_power: 11.223}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (COM:Country {name:"COM"}) CREATE (COM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (FOX:Country {name:"FOX"}) CREATE (FOX)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.095,calculated_trading_power: 3.095}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (PAW:Country {name:"PAW"}) CREATE (PAW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.74,calculated_trading_power: 8.74}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (ABI:Country {name:"ABI"}) CREATE (ABI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.654,calculated_trading_power: 8.654}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (COW:Country {name:"COW"}) CREATE (COW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.334,calculated_trading_power: 9.334}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (NTZ:Country {name:"NTZ"}) CREATE (NTZ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.359,calculated_trading_power: 9.359}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (PCH:Country {name:"PCH"}) CREATE (PCH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (QUI:Country {name:"QUI"}) CREATE (QUI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.892,calculated_trading_power: 9.892}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CCA:Country {name:"CCA"}) CREATE (CCA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (NTC:Country {name:"NTC"}) CREATE (NTC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.892,calculated_trading_power: 9.892}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (WCY:Country {name:"WCY"}) CREATE (WCY)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.242,calculated_trading_power: 9.242}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (LAK:Country {name:"LAK"}) CREATE (LAK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (C03:Country {name:"C03"}) CREATE (C03)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 59.316,calculated_trading_power: 59.316}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 15.847,calculated_trading_power: 15.847}]->(mississippi_river);
MATCH (ohio:Trade_node {name:"ohio"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.805,calculated_trading_power: 3.805}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.17,calculated_trading_power: 2.17}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CHE:Country {name:"CHE"}) CREATE (CHE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (FOX:Country {name:"FOX"}) CREATE (FOX)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 25.568,calculated_trading_power: 25.568}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (LEN:Country {name:"LEN"}) CREATE (LEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.766,calculated_trading_power: 7.766}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (MMI:Country {name:"MMI"}) CREATE (MMI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.655,calculated_trading_power: 10.655}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (OJI:Country {name:"OJI"}) CREATE (OJI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.475,calculated_trading_power: 12.475}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (OSA:Country {name:"OSA"}) CREATE (OSA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.554,calculated_trading_power: 10.554}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (OTT:Country {name:"OTT"}) CREATE (OTT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.655,calculated_trading_power: 10.655}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (POT:Country {name:"POT"}) CREATE (POT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.253,calculated_trading_power: 11.253}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (POW:Country {name:"POW"}) CREATE (POW)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (SIO:Country {name:"SIO"}) CREATE (SIO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.655,calculated_trading_power: 10.655}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (SUS:Country {name:"SUS"}) CREATE (SUS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.554,calculated_trading_power: 10.554}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ALT:Country {name:"ALT"}) CREATE (ALT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ICH:Country {name:"ICH"}) CREATE (ICH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (COF:Country {name:"COF"}) CREATE (COF)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (JOA:Country {name:"JOA"}) CREATE (JOA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (SAT:Country {name:"SAT"}) CREATE (SAT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CCA:Country {name:"CCA"}) CREATE (CCA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.732,calculated_trading_power: 11.732}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (KSI:Country {name:"KSI"}) CREATE (KSI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (OEO:Country {name:"OEO"}) CREATE (OEO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.451,calculated_trading_power: 12.451}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (MOH:Country {name:"MOH"}) CREATE (MOH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.451,calculated_trading_power: 12.451}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ONE:Country {name:"ONE"}) CREATE (ONE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.851,calculated_trading_power: 11.851}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ONO:Country {name:"ONO"}) CREATE (ONO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.647,calculated_trading_power: 13.647}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CAY:Country {name:"CAY"}) CREATE (CAY)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.851,calculated_trading_power: 11.851}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (SEN:Country {name:"SEN"}) CREATE (SEN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.253,calculated_trading_power: 11.253}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (TAH:Country {name:"TAH"}) CREATE (TAH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.851,calculated_trading_power: 11.851}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ATT:Country {name:"ATT"}) CREATE (ATT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.647,calculated_trading_power: 13.647}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (AGG:Country {name:"AGG"}) CREATE (AGG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.269,calculated_trading_power: 12.269}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ATW:Country {name:"ATW"}) CREATE (ATW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.475,calculated_trading_power: 12.475}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ARN:Country {name:"ARN"}) CREATE (ARN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.851,calculated_trading_power: 11.851}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (TIO:Country {name:"TIO"}) CREATE (TIO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ERI:Country {name:"ERI"}) CREATE (ERI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (WEN:Country {name:"WEN"}) CREATE (WEN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (TSC:Country {name:"TSC"}) CREATE (TSC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CAO:Country {name:"CAO"}) CREATE (CAO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.245,calculated_trading_power: 12.245}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (PEO:Country {name:"PEO"}) CREATE (PEO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.533,calculated_trading_power: 10.533}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (KSK:Country {name:"KSK"}) CREATE (KSK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.851,calculated_trading_power: 11.851}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (HWK:Country {name:"HWK"}) CREATE (HWK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CLG:Country {name:"CLG"}) CREATE (CLG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.676,calculated_trading_power: 10.676}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (KSP:Country {name:"KSP"}) CREATE (KSP)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.655,calculated_trading_power: 10.655}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (MSG:Country {name:"MSG"}) CREATE (MSG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.253,calculated_trading_power: 11.253}]->(ohio);
MATCH (mexico:Trade_node {name:"mexico"}), (ITZ:Country {name:"ITZ"}) CREATE (ITZ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.147,calculated_trading_power: 14.147}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (C02:Country {name:"C02"}) CREATE (C02)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 322.336,calculated_trading_power: 322.336}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (C03:Country {name:"C03"}) CREATE (C03)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 59.316,calculated_trading_power: 59.316}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 36.833,calculated_trading_power: 36.833}]->(mexico);
MATCH (lhasa:Trade_node {name:"lhasa"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.228,calculated_trading_power: 10.228}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.173,calculated_trading_power: 14.173}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.806,calculated_trading_power: 1.806}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (UTS:Country {name:"UTS"}) CREATE (UTS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 70.259,calculated_trading_power: 70.259}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (KAM:Country {name:"KAM"}) CREATE (KAM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 37.691,calculated_trading_power: 37.691}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (GUG:Country {name:"GUG"}) CREATE (GUG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.421,calculated_trading_power: 13.421}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 63.146,calculated_trading_power: 63.146}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (BHU:Country {name:"BHU"}) CREATE (BHU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.504,calculated_trading_power: 11.504}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (SKK:Country {name:"SKK"}) CREATE (SKK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.257,calculated_trading_power: 9.257}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (GRK:Country {name:"GRK"}) CREATE (GRK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.611,calculated_trading_power: 14.611}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MKP:Country {name:"MKP"}) CREATE (MKP)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.462,calculated_trading_power: 7.462}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (KTU:Country {name:"KTU"}) CREATE (KTU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 17.498,calculated_trading_power: 17.498}]->(lhasa);
MATCH (chengdu:Trade_node {name:"chengdu"}), (DAI:Country {name:"DAI"}) CREATE (DAI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.6,calculated_trading_power: 3.6}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 121.693,calculated_trading_power: 121.693}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (TAU:Country {name:"TAU"}) CREATE (TAU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 117.728,calculated_trading_power: 117.728}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (TOK:Country {name:"TOK"}) CREATE (TOK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 24.204,calculated_trading_power: 24.204}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (KAM:Country {name:"KAM"}) CREATE (KAM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 56.653,calculated_trading_power: 56.653}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (ASS:Country {name:"ASS"}) CREATE (ASS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 16.609,calculated_trading_power: 16.609}]->(chengdu);
MATCH (burma:Trade_node {name:"burma"}), (ANN:Country {name:"ANN"}) CREATE (ANN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.982,calculated_trading_power: 2.982}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.242,calculated_trading_power: 12.242}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (AYU:Country {name:"AYU"}) CREATE (AYU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.418,calculated_trading_power: 9.418}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KHM:Country {name:"KHM"}) CREATE (KHM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.318,calculated_trading_power: 9.318}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (LNA:Country {name:"LNA"}) CREATE (LNA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.806,calculated_trading_power: 4.806}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (LXA:Country {name:"LXA"}) CREATE (LXA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 29.941,calculated_trading_power: 29.941}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (SUK:Country {name:"SUK"}) CREATE (SUK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.014,calculated_trading_power: 2.014}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (TAU:Country {name:"TAU"}) CREATE (TAU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 137.691,calculated_trading_power: 137.691}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 40.728,calculated_trading_power: 40.728}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (ASS:Country {name:"ASS"}) CREATE (ASS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 20.59,calculated_trading_power: 20.59}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (GRJ:Country {name:"GRJ"}) CREATE (GRJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KAC:Country {name:"KAC"}) CREATE (KAC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 7.789,calculated_trading_power: 7.789}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KMT:Country {name:"KMT"}) CREATE (KMT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.992,calculated_trading_power: 1.992}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MLB:Country {name:"MLB"}) CREATE (MLB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.273,calculated_trading_power: 8.273}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (NGP:Country {name:"NGP"}) CREATE (NGP)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (TPR:Country {name:"TPR"}) CREATE (TPR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.0,calculated_trading_power: 9.0}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KJH:Country {name:"KJH"}) CREATE (KJH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(burma);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.296,calculated_trading_power: 8.296}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (ANN:Country {name:"ANN"}) CREATE (ANN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 16.615,calculated_trading_power: 16.615}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 34.919,calculated_trading_power: 34.919}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (AYU:Country {name:"AYU"}) CREATE (AYU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 67.585,calculated_trading_power: 67.585}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 29.525,calculated_trading_power: 29.525}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (CHA:Country {name:"CHA"}) CREATE (CHA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 21.586,calculated_trading_power: 21.586}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (DAI:Country {name:"DAI"}) CREATE (DAI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.41,calculated_trading_power: 5.41}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (KHM:Country {name:"KHM"}) CREATE (KHM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 50.721,calculated_trading_power: 50.721}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (LNA:Country {name:"LNA"}) CREATE (LNA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 23.171,calculated_trading_power: 23.171}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (LXA:Country {name:"LXA"}) CREATE (LXA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 91.221,calculated_trading_power: 91.221}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 33.18,calculated_trading_power: 33.18}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.713,calculated_trading_power: 4.713}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (SUK:Country {name:"SUK"}) CREATE (SUK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.671,calculated_trading_power: 10.671}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (TAU:Country {name:"TAU"}) CREATE (TAU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 13.687,calculated_trading_power: 13.687}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (TOK:Country {name:"TOK"}) CREATE (TOK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.02,calculated_trading_power: 6.02}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (JOH:Country {name:"JOH"}) CREATE (JOH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 19.206,calculated_trading_power: 19.206}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (KED:Country {name:"KED"}) CREATE (KED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.994,calculated_trading_power: 1.994}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.714,calculated_trading_power: 3.714}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.0,calculated_trading_power: 5.0}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.323,calculated_trading_power: 2.323}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.233,calculated_trading_power: 2.233}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.131,calculated_trading_power: 2.131}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (SMB:Country {name:"SMB"}) CREATE (SMB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.857,calculated_trading_power: 2.857}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (BRS:Country {name:"BRS"}) CREATE (BRS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (canton:Trade_node {name:"canton"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.296,calculated_trading_power: 8.296}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 16.883,calculated_trading_power: 16.883}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (ANN:Country {name:"ANN"}) CREATE (ANN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.081,calculated_trading_power: 13.081}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 34.919,calculated_trading_power: 34.919}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 29.525,calculated_trading_power: 29.525}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (DAI:Country {name:"DAI"}) CREATE (DAI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 30.048,calculated_trading_power: 30.048}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 115.571,calculated_trading_power: 115.571}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.713,calculated_trading_power: 4.713}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (SUL:Country {name:"SUL"}) CREATE (SUL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.007,calculated_trading_power: 5.007}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (TOK:Country {name:"TOK"}) CREATE (TOK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 42.73,calculated_trading_power: 42.73}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (JOH:Country {name:"JOH"}) CREATE (JOH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 19.206,calculated_trading_power: 19.206}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.714,calculated_trading_power: 3.714}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.978,calculated_trading_power: 2.978}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.323,calculated_trading_power: 2.323}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.233,calculated_trading_power: 2.233}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.131,calculated_trading_power: 2.131}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (SMB:Country {name:"SMB"}) CREATE (SMB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.857,calculated_trading_power: 2.857}]->(canton);
MATCH (philippines:Trade_node {name:"philippines"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 46.253,calculated_trading_power: 46.253}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BAN:Country {name:"BAN"}) CREATE (BAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 29.561,calculated_trading_power: 29.561}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.386,calculated_trading_power: 2.386}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (MKS:Country {name:"MKS"}) CREATE (MKS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.254,calculated_trading_power: 7.254}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (MTR:Country {name:"MTR"}) CREATE (MTR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 26.986,calculated_trading_power: 26.986}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (SUL:Country {name:"SUL"}) CREATE (SUL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 38.748,calculated_trading_power: 38.748}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BLM:Country {name:"BLM"}) CREATE (BLM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.876,calculated_trading_power: 4.876}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (CRB:Country {name:"CRB"}) CREATE (CRB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.174,calculated_trading_power: 2.174}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (LNO:Country {name:"LNO"}) CREATE (LNO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 19.493,calculated_trading_power: 19.493}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (MGD:Country {name:"MGD"}) CREATE (MGD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 30.616,calculated_trading_power: 30.616}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (TER:Country {name:"TER"}) CREATE (TER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.29,calculated_trading_power: 4.29}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BNE:Country {name:"BNE"}) CREATE (BNE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.457,calculated_trading_power: 2.457}]->(philippines);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (C00:Country {name:"C00"}) CREATE (C00)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 47.77,calculated_trading_power: 47.77}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 37.0,calculated_trading_power: 37.0}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (C05:Country {name:"C05"}) CREATE (C05)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 112.65,calculated_trading_power: 112.65}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (C06:Country {name:"C06"}) CREATE (C06)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 99.61,calculated_trading_power: 99.61}]->(cuiaba);
MATCH (lima:Trade_node {name:"lima"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 0.633,calculated_trading_power: 0.633}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 102.409,calculated_trading_power: 102.409}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (C05:Country {name:"C05"}) CREATE (C05)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 140.814,calculated_trading_power: 140.814}]->(lima);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (MRI:Country {name:"MRI"}) CREATE (MRI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.544,calculated_trading_power: 2.544}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TKG:Country {name:"TKG"}) CREATE (TKG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 19.236,calculated_trading_power: 19.236}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (KOR:Country {name:"KOR"}) CREATE (KOR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.076,calculated_trading_power: 14.076}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (MAA:Country {name:"MAA"}) CREATE (MAA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.403,calculated_trading_power: 9.403}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TAN:Country {name:"TAN"}) CREATE (TAN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.989,calculated_trading_power: 9.989}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TAK:Country {name:"TAK"}) CREATE (TAK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.111,calculated_trading_power: 9.111}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TNK:Country {name:"TNK"}) CREATE (TNK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.989,calculated_trading_power: 9.989}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TEA:Country {name:"TEA"}) CREATE (TEA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.989,calculated_trading_power: 9.989}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TTT:Country {name:"TTT"}) CREATE (TTT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.989,calculated_trading_power: 9.989}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (WAI:Country {name:"WAI"}) CREATE (WAI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.233,calculated_trading_power: 8.233}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (HAW:Country {name:"HAW"}) CREATE (HAW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.111,calculated_trading_power: 9.111}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (MAU:Country {name:"MAU"}) CREATE (MAU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.526,calculated_trading_power: 8.526}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (OAH:Country {name:"OAH"}) CREATE (OAH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 15.841,calculated_trading_power: 15.841}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (KAA:Country {name:"KAA"}) CREATE (KAA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.526,calculated_trading_power: 8.526}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TOG:Country {name:"TOG"}) CREATE (TOG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 16.685,calculated_trading_power: 16.685}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (SAM:Country {name:"SAM"}) CREATE (SAM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.385,calculated_trading_power: 9.385}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (VIL:Country {name:"VIL"}) CREATE (VIL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.111,calculated_trading_power: 9.111}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (VNL:Country {name:"VNL"}) CREATE (VNL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.526,calculated_trading_power: 8.526}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (LAI:Country {name:"LAI"}) CREATE (LAI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 7.94,calculated_trading_power: 7.94}]->(polynesia_node);
MATCH (australia:Trade_node {name:"australia"}), (TIW:Country {name:"TIW"}) CREATE (TIW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.439,calculated_trading_power: 13.439}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (LAR:Country {name:"LAR"}) CREATE (LAR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 23.544,calculated_trading_power: 23.544}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (YOL:Country {name:"YOL"}) CREATE (YOL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.439,calculated_trading_power: 13.439}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (YNU:Country {name:"YNU"}) CREATE (YNU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.854,calculated_trading_power: 12.854}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (AWN:Country {name:"AWN"}) CREATE (AWN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.413,calculated_trading_power: 13.413}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (GMI:Country {name:"GMI"}) CREATE (GMI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.19,calculated_trading_power: 10.19}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (MIA:Country {name:"MIA"}) CREATE (MIA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.385,calculated_trading_power: 9.385}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (EOR:Country {name:"EOR"}) CREATE (EOR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.438,calculated_trading_power: 18.438}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (KAU:Country {name:"KAU"}) CREATE (KAU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 17.27,calculated_trading_power: 17.27}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (PLW:Country {name:"PLW"}) CREATE (PLW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.696,calculated_trading_power: 9.696}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (WRU:Country {name:"WRU"}) CREATE (WRU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.474,calculated_trading_power: 18.474}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (NOO:Country {name:"NOO"}) CREATE (NOO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 17.854,calculated_trading_power: 17.854}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (MLG:Country {name:"MLG"}) CREATE (MLG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.385,calculated_trading_power: 9.385}]->(australia);
MATCH (nippon:Trade_node {name:"nippon"}), (DTE:Country {name:"DTE"}) CREATE (DTE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.777,calculated_trading_power: 11.777}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (HSK:Country {name:"HSK"}) CREATE (HSK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.581,calculated_trading_power: 9.581}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (IKE:Country {name:"IKE"}) CREATE (IKE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.495,calculated_trading_power: 11.495}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (MAE:Country {name:"MAE"}) CREATE (MAE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.049,calculated_trading_power: 12.049}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (MRI:Country {name:"MRI"}) CREATE (MRI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 21.676,calculated_trading_power: 21.676}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (SMZ:Country {name:"SMZ"}) CREATE (SMZ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.536,calculated_trading_power: 14.536}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (TKG:Country {name:"TKG"}) CREATE (TKG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 127.75,calculated_trading_power: 127.75}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (UES:Country {name:"UES"}) CREATE (UES)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.959,calculated_trading_power: 13.959}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (RFR:Country {name:"RFR"}) CREATE (RFR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.273,calculated_trading_power: 9.273}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (ANU:Country {name:"ANU"}) CREATE (ANU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.021,calculated_trading_power: 12.021}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (ITO:Country {name:"ITO"}) CREATE (ITO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.241,calculated_trading_power: 11.241}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (SHN:Country {name:"SHN"}) CREATE (SHN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.073,calculated_trading_power: 12.073}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (STK:Country {name:"STK"}) CREATE (STK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.696,calculated_trading_power: 8.696}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (KOR:Country {name:"KOR"}) CREATE (KOR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 97.441,calculated_trading_power: 97.441}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 32.552,calculated_trading_power: 32.552}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (RYU:Country {name:"RYU"}) CREATE (RYU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 16.061,calculated_trading_power: 16.061}]->(nippon);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.296,calculated_trading_power: 8.296}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 34.919,calculated_trading_power: 34.919}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 29.525,calculated_trading_power: 29.525}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 281.003,calculated_trading_power: 281.003}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.713,calculated_trading_power: 4.713}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (JOH:Country {name:"JOH"}) CREATE (JOH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 19.206,calculated_trading_power: 19.206}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.036,calculated_trading_power: 6.036}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.0,calculated_trading_power: 5.0}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.323,calculated_trading_power: 2.323}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.233,calculated_trading_power: 2.233}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.131,calculated_trading_power: 2.131}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (SMB:Country {name:"SMB"}) CREATE (SMB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.857,calculated_trading_power: 2.857}]->(hangzhou);
MATCH (xian:Trade_node {name:"xian"}), (KHA:Country {name:"KHA"}) CREATE (KHA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 109.542,calculated_trading_power: 109.542}]->(xian);
MATCH (xian:Trade_node {name:"xian"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 210.834,calculated_trading_power: 210.834}]->(xian);
MATCH (xian:Trade_node {name:"xian"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.562,calculated_trading_power: 7.562}]->(xian);
MATCH (beijing:Trade_node {name:"beijing"}), (KHA:Country {name:"KHA"}) CREATE (KHA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 113.843,calculated_trading_power: 113.843}]->(beijing);
MATCH (beijing:Trade_node {name:"beijing"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 122.087,calculated_trading_power: 122.087}]->(beijing);
MATCH (beijing:Trade_node {name:"beijing"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.562,calculated_trading_power: 7.562}]->(beijing);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.597,calculated_trading_power: 11.597}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.837,calculated_trading_power: 7.837}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 34.919,calculated_trading_power: 34.919}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BLI:Country {name:"BLI"}) CREATE (BLI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 19.845,calculated_trading_power: 19.845}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BAN:Country {name:"BAN"}) CREATE (BAN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 76.606,calculated_trading_power: 76.606}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 29.525,calculated_trading_power: 29.525}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (MKS:Country {name:"MKS"}) CREATE (MKS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 53.194,calculated_trading_power: 53.194}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (MTR:Country {name:"MTR"}) CREATE (MTR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 75.895,calculated_trading_power: 75.895}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.713,calculated_trading_power: 4.713}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (JOH:Country {name:"JOH"}) CREATE (JOH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 19.206,calculated_trading_power: 19.206}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BLM:Country {name:"BLM"}) CREATE (BLM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 31.79,calculated_trading_power: 31.79}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BTN:Country {name:"BTN"}) CREATE (BTN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.403,calculated_trading_power: 9.403}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (CRB:Country {name:"CRB"}) CREATE (CRB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 25.503,calculated_trading_power: 25.503}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.714,calculated_trading_power: 3.714}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.0,calculated_trading_power: 5.0}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.323,calculated_trading_power: 2.323}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.233,calculated_trading_power: 2.233}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (LUW:Country {name:"LUW"}) CREATE (LUW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.378,calculated_trading_power: 12.378}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (TER:Country {name:"TER"}) CREATE (TER)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 20.228,calculated_trading_power: 20.228}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (TID:Country {name:"TID"}) CREATE (TID)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 31.571,calculated_trading_power: 31.571}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BNE:Country {name:"BNE"}) CREATE (BNE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 40.899,calculated_trading_power: 40.899}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.131,calculated_trading_power: 2.131}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (SMB:Country {name:"SMB"}) CREATE (SMB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.857,calculated_trading_power: 2.857}]->(the_moluccas);
MATCH (siberia:Trade_node {name:"siberia"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 54.733,calculated_trading_power: 54.733}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (KZH:Country {name:"KZH"}) CREATE (KZH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 110.885,calculated_trading_power: 110.885}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (KHI:Country {name:"KHI"}) CREATE (KHI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.068,calculated_trading_power: 4.068}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (BUK:Country {name:"BUK"}) CREATE (BUK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.862,calculated_trading_power: 8.862}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (KHA:Country {name:"KHA"}) CREATE (KHA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.972,calculated_trading_power: 2.972}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 90.752,calculated_trading_power: 90.752}]->(siberia);
MATCH (yumen:Trade_node {name:"yumen"}), (KZH:Country {name:"KZH"}) CREATE (KZH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 103.341,calculated_trading_power: 103.341}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (KHI:Country {name:"KHI"}) CREATE (KHI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.068,calculated_trading_power: 4.068}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (BUK:Country {name:"BUK"}) CREATE (BUK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.862,calculated_trading_power: 8.862}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (KHA:Country {name:"KHA"}) CREATE (KHA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 122.172,calculated_trading_power: 122.172}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (MNG:Country {name:"MNG"}) CREATE (MNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.337,calculated_trading_power: 7.337}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (OIR:Country {name:"OIR"}) CREATE (OIR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 47.142,calculated_trading_power: 47.142}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 128.566,calculated_trading_power: 128.566}]->(yumen);
MATCH (malacca:Trade_node {name:"malacca"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 41.482,calculated_trading_power: 41.482}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.242,calculated_trading_power: 12.242}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 107.036,calculated_trading_power: 107.036}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (AYU:Country {name:"AYU"}) CREATE (AYU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.791,calculated_trading_power: 5.791}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BAN:Country {name:"BAN"}) CREATE (BAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 13.182,calculated_trading_power: 13.182}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 82.612,calculated_trading_power: 82.612}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 39.41,calculated_trading_power: 39.41}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 38.334,calculated_trading_power: 38.334}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (JOH:Country {name:"JOH"}) CREATE (JOH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 59.155,calculated_trading_power: 59.155}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (KED:Country {name:"KED"}) CREATE (KED)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.86,calculated_trading_power: 10.86}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PRK:Country {name:"PRK"}) CREATE (PRK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 23.037,calculated_trading_power: 23.037}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PGR:Country {name:"PGR"}) CREATE (PGR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 22.18,calculated_trading_power: 22.18}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 37.827,calculated_trading_power: 37.827}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 25.866,calculated_trading_power: 25.866}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 26.275,calculated_trading_power: 26.275}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 25.83,calculated_trading_power: 25.83}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 32.897,calculated_trading_power: 32.897}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (SMB:Country {name:"SMB"}) CREATE (SMB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 36.485,calculated_trading_power: 36.485}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BRS:Country {name:"BRS"}) CREATE (BRS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 24.344,calculated_trading_power: 24.344}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (JMB:Country {name:"JMB"}) CREATE (JMB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.595,calculated_trading_power: 10.595}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (IND:Country {name:"IND"}) CREATE (IND)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.177,calculated_trading_power: 18.177}]->(malacca);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.879,calculated_trading_power: 10.879}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.32,calculated_trading_power: 5.32}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 69.75,calculated_trading_power: 69.75}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (GOC:Country {name:"GOC"}) CREATE (GOC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 18.121,calculated_trading_power: 18.121}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 303.58,calculated_trading_power: 303.58}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (VIJ:Country {name:"VIJ"}) CREATE (VIJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.519,calculated_trading_power: 7.519}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MAD:Country {name:"MAD"}) CREATE (MAD)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.86,calculated_trading_power: 5.86}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (GRJ:Country {name:"GRJ"}) CREATE (GRJ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.962,calculated_trading_power: 8.962}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MAB:Country {name:"MAB"}) CREATE (MAB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.608,calculated_trading_power: 5.608}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (BST:Country {name:"BST"}) CREATE (BST)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.758,calculated_trading_power: 8.758}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (CEY:Country {name:"CEY"}) CREATE (CEY)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.248,calculated_trading_power: 4.248}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KMT:Country {name:"KMT"}) CREATE (KMT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.039,calculated_trading_power: 10.039}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (BGL:Country {name:"BGL"}) CREATE (BGL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.295,calculated_trading_power: 5.295}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (NGP:Country {name:"NGP"}) CREATE (NGP)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.593,calculated_trading_power: 10.593}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (SBP:Country {name:"SBP"}) CREATE (SBP)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 16.044,calculated_trading_power: 16.044}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (PTT:Country {name:"PTT"}) CREATE (PTT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 7.816,calculated_trading_power: 7.816}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (RTT:Country {name:"RTT"}) CREATE (RTT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.682,calculated_trading_power: 9.682}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KLH:Country {name:"KLH"}) CREATE (KLH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 7.905,calculated_trading_power: 7.905}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KJH:Country {name:"KJH"}) CREATE (KJH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.827,calculated_trading_power: 8.827}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (PRD:Country {name:"PRD"}) CREATE (PRD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 7.789,calculated_trading_power: 7.789}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (JPR:Country {name:"JPR"}) CREATE (JPR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 7.789,calculated_trading_power: 7.789}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (SRG:Country {name:"SRG"}) CREATE (SRG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KND:Country {name:"KND"}) CREATE (KND)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.447,calculated_trading_power: 4.447}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (DNG:Country {name:"DNG"}) CREATE (DNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (JML:Country {name:"JML"}) CREATE (JML)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MKP:Country {name:"MKP"}) CREATE (MKP)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.359,calculated_trading_power: 9.359}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (GNG:Country {name:"GNG"}) CREATE (GNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.413,calculated_trading_power: 5.413}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (TNJ:Country {name:"TNJ"}) CREATE (TNJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.351,calculated_trading_power: 6.351}]->(ganges_delta);
MATCH (doab:Trade_node {name:"doab"}), (BIJ:Country {name:"BIJ"}) CREATE (BIJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 35.742,calculated_trading_power: 35.742}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (GOC:Country {name:"GOC"}) CREATE (GOC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 66.007,calculated_trading_power: 66.007}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 241.742,calculated_trading_power: 241.742}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (AHM:Country {name:"AHM"}) CREATE (AHM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 15.962,calculated_trading_power: 15.962}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (GDW:Country {name:"GDW"}) CREATE (GDW)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (DHU:Country {name:"DHU"}) CREATE (DHU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 21.169,calculated_trading_power: 21.169}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (BND:Country {name:"BND"}) CREATE (BND)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 17.163,calculated_trading_power: 17.163}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (KGR:Country {name:"KGR"}) CREATE (KGR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.594,calculated_trading_power: 10.594}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (HAD:Country {name:"HAD"}) CREATE (HAD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.643,calculated_trading_power: 12.643}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (LDK:Country {name:"LDK"}) CREATE (LDK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.99,calculated_trading_power: 4.99}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (BGL:Country {name:"BGL"}) CREATE (BGL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.917,calculated_trading_power: 13.917}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (GHR:Country {name:"GHR"}) CREATE (GHR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.807,calculated_trading_power: 9.807}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (CHD:Country {name:"CHD"}) CREATE (CHD)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.725,calculated_trading_power: 6.725}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (SRG:Country {name:"SRG"}) CREATE (SRG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.779,calculated_trading_power: 10.779}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (DNG:Country {name:"DNG"}) CREATE (DNG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.311,calculated_trading_power: 11.311}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (DTI:Country {name:"DTI"}) CREATE (DTI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.807,calculated_trading_power: 9.807}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (JML:Country {name:"JML"}) CREATE (JML)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.311,calculated_trading_power: 11.311}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (SRM:Country {name:"SRM"}) CREATE (SRM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.807,calculated_trading_power: 9.807}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (KMN:Country {name:"KMN"}) CREATE (KMN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.807,calculated_trading_power: 9.807}]->(doab);
MATCH (lahore:Trade_node {name:"lahore"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.884,calculated_trading_power: 3.884}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (KZH:Country {name:"KZH"}) CREATE (KZH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 103.341,calculated_trading_power: 103.341}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (KHI:Country {name:"KHI"}) CREATE (KHI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 51.424,calculated_trading_power: 51.424}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (BUK:Country {name:"BUK"}) CREATE (BUK)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 95.566,calculated_trading_power: 95.566}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 36.048,calculated_trading_power: 36.048}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 90.752,calculated_trading_power: 90.752}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 87.975,calculated_trading_power: 87.975}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (MAW:Country {name:"MAW"}) CREATE (MAW)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.274,calculated_trading_power: 8.274}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.265,calculated_trading_power: 12.265}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (LDK:Country {name:"LDK"}) CREATE (LDK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.312,calculated_trading_power: 13.312}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (JAJ:Country {name:"JAJ"}) CREATE (JAJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.455,calculated_trading_power: 3.455}]->(lahore);
MATCH (deccan:Trade_node {name:"deccan"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.764,calculated_trading_power: 14.764}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.32,calculated_trading_power: 5.32}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (BIJ:Country {name:"BIJ"}) CREATE (BIJ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 82.269,calculated_trading_power: 82.269}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (GOC:Country {name:"GOC"}) CREATE (GOC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 77.544,calculated_trading_power: 77.544}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 73.221,calculated_trading_power: 73.221}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (VIJ:Country {name:"VIJ"}) CREATE (VIJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 37.724,calculated_trading_power: 37.724}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (AHM:Country {name:"AHM"}) CREATE (AHM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 45.225,calculated_trading_power: 45.225}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MAD:Country {name:"MAD"}) CREATE (MAD)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 17.655,calculated_trading_power: 17.655}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.265,calculated_trading_power: 12.265}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (GDW:Country {name:"GDW"}) CREATE (GDW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.469,calculated_trading_power: 11.469}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MAB:Country {name:"MAB"}) CREATE (MAB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.925,calculated_trading_power: 7.925}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (CEY:Country {name:"CEY"}) CREATE (CEY)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.628,calculated_trading_power: 8.628}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (CHD:Country {name:"CHD"}) CREATE (CHD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 16.639,calculated_trading_power: 16.639}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (JAJ:Country {name:"JAJ"}) CREATE (JAJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.455,calculated_trading_power: 3.455}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (KND:Country {name:"KND"}) CREATE (KND)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.513,calculated_trading_power: 10.513}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (GNG:Country {name:"GNG"}) CREATE (GNG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.816,calculated_trading_power: 11.816}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (TNJ:Country {name:"TNJ"}) CREATE (TNJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 15.112,calculated_trading_power: 15.112}]->(deccan);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.33,calculated_trading_power: 9.33}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 58.284,calculated_trading_power: 58.284}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.131,calculated_trading_power: 14.131}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.923,calculated_trading_power: 4.923}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.928,calculated_trading_power: 6.928}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (AFA:Country {name:"AFA"}) CREATE (AFA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.112,calculated_trading_power: 2.112}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (WAR:Country {name:"WAR"}) CREATE (WAR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.09,calculated_trading_power: 4.09}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (BIJ:Country {name:"BIJ"}) CREATE (BIJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.787,calculated_trading_power: 6.787}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (GOC:Country {name:"GOC"}) CREATE (GOC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 42.916,calculated_trading_power: 42.916}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 30.57,calculated_trading_power: 30.57}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MYS:Country {name:"MYS"}) CREATE (MYS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.351,calculated_trading_power: 14.351}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (VIJ:Country {name:"VIJ"}) CREATE (VIJ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 48.306,calculated_trading_power: 48.306}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MAD:Country {name:"MAD"}) CREATE (MAD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 29.048,calculated_trading_power: 29.048}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.335,calculated_trading_power: 2.335}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (KLN:Country {name:"KLN"}) CREATE (KLN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.893,calculated_trading_power: 11.893}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (VND:Country {name:"VND"}) CREATE (VND)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.959,calculated_trading_power: 10.959}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MAB:Country {name:"MAB"}) CREATE (MAB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 30.111,calculated_trading_power: 30.111}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (CEY:Country {name:"CEY"}) CREATE (CEY)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 21.81,calculated_trading_power: 21.81}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (KOC:Country {name:"KOC"}) CREATE (KOC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.665,calculated_trading_power: 9.665}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (JFN:Country {name:"JFN"}) CREATE (JFN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.836,calculated_trading_power: 8.836}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (JAJ:Country {name:"JAJ"}) CREATE (JAJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.441,calculated_trading_power: 5.441}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (DGL:Country {name:"DGL"}) CREATE (DGL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.509,calculated_trading_power: 8.509}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (KND:Country {name:"KND"}) CREATE (KND)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 23.002,calculated_trading_power: 23.002}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (GNG:Country {name:"GNG"}) CREATE (GNG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 26.374,calculated_trading_power: 26.374}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (TNJ:Country {name:"TNJ"}) CREATE (TNJ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 31.984,calculated_trading_power: 31.984}]->(comorin_cape);
MATCH (gujarat:Trade_node {name:"gujarat"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.33,calculated_trading_power: 9.33}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 43.82,calculated_trading_power: 43.82}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.923,calculated_trading_power: 4.923}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.125,calculated_trading_power: 9.125}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (ORM:Country {name:"ORM"}) CREATE (ORM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.775,calculated_trading_power: 6.775}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 27.522,calculated_trading_power: 27.522}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.333,calculated_trading_power: 4.333}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.928,calculated_trading_power: 6.928}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (AFA:Country {name:"AFA"}) CREATE (AFA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.826,calculated_trading_power: 3.826}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (WAR:Country {name:"WAR"}) CREATE (WAR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.09,calculated_trading_power: 4.09}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.926,calculated_trading_power: 2.926}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.409,calculated_trading_power: 2.409}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (BIJ:Country {name:"BIJ"}) CREATE (BIJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.787,calculated_trading_power: 1.787}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 87.084,calculated_trading_power: 87.084}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (GUJ:Country {name:"GUJ"}) CREATE (GUJ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.304,calculated_trading_power: 9.304}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MAW:Country {name:"MAW"}) CREATE (MAW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.608,calculated_trading_power: 12.608}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 19.994,calculated_trading_power: 19.994}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (JAN:Country {name:"JAN"}) CREATE (JAN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.38,calculated_trading_power: 8.38}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (JSL:Country {name:"JSL"}) CREATE (JSL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 7.665,calculated_trading_power: 7.665}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (JAJ:Country {name:"JAJ"}) CREATE (JAJ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 24.483,calculated_trading_power: 24.483}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (IDR:Country {name:"IDR"}) CREATE (IDR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.437,calculated_trading_power: 8.437}]->(gujarat);
MATCH (katsina:Trade_node {name:"katsina"}), (TRP:Country {name:"TRP"}) CREATE (TRP)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.026,calculated_trading_power: 4.026}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (TUN:Country {name:"TUN"}) CREATE (TUN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.017,calculated_trading_power: 9.017}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (ETH:Country {name:"ETH"}) CREATE (ETH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.991,calculated_trading_power: 5.991}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (NUB:Country {name:"NUB"}) CREATE (NUB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.509,calculated_trading_power: 5.509}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (SON:Country {name:"SON"}) CREATE (SON)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.67,calculated_trading_power: 10.67}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (HAU:Country {name:"HAU"}) CREATE (HAU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 22.605,calculated_trading_power: 22.605}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (KBO:Country {name:"KBO"}) CREATE (KBO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 96.526,calculated_trading_power: 96.526}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (OYO:Country {name:"OYO"}) CREATE (OYO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.466,calculated_trading_power: 18.466}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (WGD:Country {name:"WGD"}) CREATE (WGD)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.424,calculated_trading_power: 8.424}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (GUR:Country {name:"GUR"}) CREATE (GUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (AIR:Country {name:"AIR"}) CREATE (AIR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 48.53,calculated_trading_power: 48.53}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (BON:Country {name:"BON"}) CREATE (BON)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.654,calculated_trading_power: 6.654}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (DAH:Country {name:"DAH"}) CREATE (DAH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (DGB:Country {name:"DGB"}) CREATE (DGB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.444,calculated_trading_power: 4.444}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (JNN:Country {name:"JNN"}) CREATE (JNN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 22.654,calculated_trading_power: 22.654}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (KAN:Country {name:"KAN"}) CREATE (KAN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 25.987,calculated_trading_power: 25.987}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (KTS:Country {name:"KTS"}) CREATE (KTS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 36.374,calculated_trading_power: 36.374}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (NUP:Country {name:"NUP"}) CREATE (NUP)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 15.348,calculated_trading_power: 15.348}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (TMB:Country {name:"TMB"}) CREATE (TMB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 25.836,calculated_trading_power: 25.836}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (YAO:Country {name:"YAO"}) CREATE (YAO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.728,calculated_trading_power: 11.728}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (YAT:Country {name:"YAT"}) CREATE (YAT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.75,calculated_trading_power: 7.75}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (ZAF:Country {name:"ZAF"}) CREATE (ZAF)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.97,calculated_trading_power: 14.97}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (ZZZ:Country {name:"ZZZ"}) CREATE (ZZZ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 15.883,calculated_trading_power: 15.883}]->(katsina);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 34.128,calculated_trading_power: 34.128}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.705,calculated_trading_power: 2.705}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.906,calculated_trading_power: 6.906}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MFL:Country {name:"MFL"}) CREATE (MFL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.38,calculated_trading_power: 4.38}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (RAS:Country {name:"RAS"}) CREATE (RAS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.48,calculated_trading_power: 6.48}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ETH:Country {name:"ETH"}) CREATE (ETH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 63.929,calculated_trading_power: 63.929}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (NUB:Country {name:"NUB"}) CREATE (NUB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 80.26,calculated_trading_power: 80.26}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.35,calculated_trading_power: 8.35}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ENA:Country {name:"ENA"}) CREATE (ENA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.112,calculated_trading_power: 11.112}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (OGD:Country {name:"OGD"}) CREATE (OGD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.231,calculated_trading_power: 10.231}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (WAD:Country {name:"WAD"}) CREATE (WAD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.779,calculated_trading_power: 10.779}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (AFA:Country {name:"AFA"}) CREATE (AFA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.112,calculated_trading_power: 2.112}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (DAR:Country {name:"DAR"}) CREATE (DAR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 20.991,calculated_trading_power: 20.991}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (KAF:Country {name:"KAF"}) CREATE (KAF)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 15.052,calculated_trading_power: 15.052}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MED:Country {name:"MED"}) CREATE (MED)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.03,calculated_trading_power: 14.03}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MRE:Country {name:"MRE"}) CREATE (MRE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.457,calculated_trading_power: 8.457}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (WAR:Country {name:"WAR"}) CREATE (WAR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.783,calculated_trading_power: 2.783}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (BTI:Country {name:"BTI"}) CREATE (BTI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.89,calculated_trading_power: 9.89}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (WLY:Country {name:"WLY"}) CREATE (WLY)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 15.112,calculated_trading_power: 15.112}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (DAM:Country {name:"DAM"}) CREATE (DAM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.351,calculated_trading_power: 14.351}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (JJI:Country {name:"JJI"}) CREATE (JJI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.049,calculated_trading_power: 10.049}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ABB:Country {name:"ABB"}) CREATE (ABB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.875,calculated_trading_power: 11.875}]->(ethiopia);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 46.42,calculated_trading_power: 46.42}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 27.192,calculated_trading_power: 27.192}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 20.794,calculated_trading_power: 20.794}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 27.187,calculated_trading_power: 27.187}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MFL:Country {name:"MFL"}) CREATE (MFL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.229,calculated_trading_power: 10.229}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MHR:Country {name:"MHR"}) CREATE (MHR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.153,calculated_trading_power: 10.153}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (OMA:Country {name:"OMA"}) CREATE (OMA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (RAS:Country {name:"RAS"}) CREATE (RAS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.922,calculated_trading_power: 11.922}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (YAS:Country {name:"YAS"}) CREATE (YAS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.16,calculated_trading_power: 2.16}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.125,calculated_trading_power: 9.125}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (ORM:Country {name:"ORM"}) CREATE (ORM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.775,calculated_trading_power: 6.775}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (ETH:Country {name:"ETH"}) CREATE (ETH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.357,calculated_trading_power: 1.357}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 27.522,calculated_trading_power: 27.522}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.333,calculated_trading_power: 4.333}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 51.318,calculated_trading_power: 51.318}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (AFA:Country {name:"AFA"}) CREATE (AFA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 19.215,calculated_trading_power: 19.215}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MED:Country {name:"MED"}) CREATE (MED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.731,calculated_trading_power: 3.731}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MRE:Country {name:"MRE"}) CREATE (MRE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.08,calculated_trading_power: 12.08}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (WAR:Country {name:"WAR"}) CREATE (WAR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.463,calculated_trading_power: 18.463}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.926,calculated_trading_power: 2.926}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.409,calculated_trading_power: 2.409}]->(gulf_of_aden);
MATCH (hormuz:Trade_node {name:"hormuz"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.949,calculated_trading_power: 5.949}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 43.247,calculated_trading_power: 43.247}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (NAJ:Country {name:"NAJ"}) CREATE (NAJ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.218,calculated_trading_power: 2.218}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (OMA:Country {name:"OMA"}) CREATE (OMA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.785,calculated_trading_power: 11.785}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (YAS:Country {name:"YAS"}) CREATE (YAS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.18,calculated_trading_power: 9.18}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (MSY:Country {name:"MSY"}) CREATE (MSY)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 27.905,calculated_trading_power: 27.905}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (ORM:Country {name:"ORM"}) CREATE (ORM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 33.085,calculated_trading_power: 33.085}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (BSR:Country {name:"BSR"}) CREATE (BSR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.025,calculated_trading_power: 7.025}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (BAL:Country {name:"BAL"}) CREATE (BAL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.465,calculated_trading_power: 11.465}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 0.934,calculated_trading_power: 0.934}]->(hormuz);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 40.58,calculated_trading_power: 40.58}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 78.158,calculated_trading_power: 78.158}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 20.071,calculated_trading_power: 20.071}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.134,calculated_trading_power: 1.134}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (PTE:Country {name:"PTE"}) CREATE (PTE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 16.719,calculated_trading_power: 16.719}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (MIR:Country {name:"MIR"}) CREATE (MIR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.55,calculated_trading_power: 10.55}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 23.843,calculated_trading_power: 23.843}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 21.008,calculated_trading_power: 21.008}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (MFY:Country {name:"MFY"}) CREATE (MFY)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.818,calculated_trading_power: 8.818}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (ANT:Country {name:"ANT"}) CREATE (ANT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.3,calculated_trading_power: 11.3}]->(zanzibar);
MATCH (basra:Trade_node {name:"basra"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 59.536,calculated_trading_power: 59.536}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.768,calculated_trading_power: 1.768}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (ANZ:Country {name:"ANZ"}) CREATE (ANZ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.72,calculated_trading_power: 9.72}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (ARD:Country {name:"ARD"}) CREATE (ARD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.619,calculated_trading_power: 8.619}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (DAW:Country {name:"DAW"}) CREATE (DAW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.799,calculated_trading_power: 11.799}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (NAJ:Country {name:"NAJ"}) CREATE (NAJ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.496,calculated_trading_power: 11.496}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (SHM:Country {name:"SHM"}) CREATE (SHM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 17.408,calculated_trading_power: 17.408}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (MSY:Country {name:"MSY"}) CREATE (MSY)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.009,calculated_trading_power: 18.009}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 90.264,calculated_trading_power: 90.264}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (BSR:Country {name:"BSR"}) CREATE (BSR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 42.782,calculated_trading_power: 42.782}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (MGR:Country {name:"MGR"}) CREATE (MGR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(basra);
MATCH (samarkand:Trade_node {name:"samarkand"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.625,calculated_trading_power: 12.625}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (KZH:Country {name:"KZH"}) CREATE (KZH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 146.956,calculated_trading_power: 146.956}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (KHI:Country {name:"KHI"}) CREATE (KHI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 84.486,calculated_trading_power: 84.486}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (BUK:Country {name:"BUK"}) CREATE (BUK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 141.261,calculated_trading_power: 141.261}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 38.103,calculated_trading_power: 38.103}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (KAS:Country {name:"KAS"}) CREATE (KAS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 127.653,calculated_trading_power: 127.653}]->(samarkand);
MATCH (persia:Trade_node {name:"persia"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 30.69,calculated_trading_power: 30.69}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.625,calculated_trading_power: 12.625}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (AVR:Country {name:"AVR"}) CREATE (AVR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.302,calculated_trading_power: 4.302}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 251.169,calculated_trading_power: 251.169}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (MGR:Country {name:"MGR"}) CREATE (MGR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.41,calculated_trading_power: 11.41}]->(persia);
MATCH (aleppo:Trade_node {name:"aleppo"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 239.11,calculated_trading_power: 239.11}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (ANZ:Country {name:"ANZ"}) CREATE (ANZ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.218,calculated_trading_power: 2.218}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 27.187,calculated_trading_power: 27.187}]->(aleppo);
MATCH (alexandria:Trade_node {name:"alexandria"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 189.349,calculated_trading_power: 189.349}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.728,calculated_trading_power: 6.728}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.132,calculated_trading_power: 2.132}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 19.497,calculated_trading_power: 19.497}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 43.622,calculated_trading_power: 43.622}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.322,calculated_trading_power: 2.322}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 13.318,calculated_trading_power: 13.318}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.003,calculated_trading_power: 5.003}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.503,calculated_trading_power: 12.503}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (URB:Country {name:"URB"}) CREATE (URB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.263,calculated_trading_power: 4.263}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 30.588,calculated_trading_power: 30.588}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.097,calculated_trading_power: 5.097}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (ANZ:Country {name:"ANZ"}) CREATE (ANZ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 0.865,calculated_trading_power: 0.865}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 76.065,calculated_trading_power: 76.065}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (ABB:Country {name:"ABB"}) CREATE (ABB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.662,calculated_trading_power: 1.662}]->(alexandria);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.571,calculated_trading_power: 14.571}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (CRI:Country {name:"CRI"}) CREATE (CRI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 24.248,calculated_trading_power: 24.248}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (GEO:Country {name:"GEO"}) CREATE (GEO)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.711,calculated_trading_power: 14.711}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 94.28,calculated_trading_power: 94.28}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (NOG:Country {name:"NOG"}) CREATE (NOG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 22.088,calculated_trading_power: 22.088}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (AVR:Country {name:"AVR"}) CREATE (AVR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.19,calculated_trading_power: 12.19}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (KZH:Country {name:"KZH"}) CREATE (KZH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.704,calculated_trading_power: 8.704}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.429,calculated_trading_power: 2.429}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (CIR:Country {name:"CIR"}) CREATE (CIR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.301,calculated_trading_power: 6.301}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (GAZ:Country {name:"GAZ"}) CREATE (GAZ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 28.564,calculated_trading_power: 28.564}]->(astrakhan);
MATCH (crimea:Trade_node {name:"crimea"}), (MOL:Country {name:"MOL"}) CREATE (MOL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.18,calculated_trading_power: 14.18}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (TRA:Country {name:"TRA"}) CREATE (TRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 23.678,calculated_trading_power: 23.678}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (WAL:Country {name:"WAL"}) CREATE (WAL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 21.441,calculated_trading_power: 21.441}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 144.761,calculated_trading_power: 144.761}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 19.045,calculated_trading_power: 19.045}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.97,calculated_trading_power: 4.97}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (CRI:Country {name:"CRI"}) CREATE (CRI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 29.955,calculated_trading_power: 29.955}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (GEO:Country {name:"GEO"}) CREATE (GEO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 28.358,calculated_trading_power: 28.358}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.892,calculated_trading_power: 5.892}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (ZAZ:Country {name:"ZAZ"}) CREATE (ZAZ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.192,calculated_trading_power: 11.192}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.146,calculated_trading_power: 12.146}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (CIR:Country {name:"CIR"}) CREATE (CIR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.691,calculated_trading_power: 18.691}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (GAZ:Country {name:"GAZ"}) CREATE (GAZ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.602,calculated_trading_power: 1.602}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (IME:Country {name:"IME"}) CREATE (IME)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.59,calculated_trading_power: 11.59}]->(crimea);
MATCH (constantinople:Trade_node {name:"constantinople"}), (MON:Country {name:"MON"}) CREATE (MON)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (RAG:Country {name:"RAG"}) CREATE (RAG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 28.169,calculated_trading_power: 28.169}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 246.205,calculated_trading_power: 246.205}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.952,calculated_trading_power: 2.952}]->(constantinople);
MATCH (kiev:Trade_node {name:"kiev"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.634,calculated_trading_power: 6.634}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 220.033,calculated_trading_power: 220.033}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (BOH:Country {name:"BOH"}) CREATE (BOH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.557,calculated_trading_power: 2.557}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 117.767,calculated_trading_power: 117.767}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (ZAZ:Country {name:"ZAZ"}) CREATE (ZAZ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.271,calculated_trading_power: 1.271}]->(kiev);
MATCH (kazan:Trade_node {name:"kazan"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.634,calculated_trading_power: 6.634}]->(kazan);
MATCH (kazan:Trade_node {name:"kazan"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 223.6,calculated_trading_power: 223.6}]->(kazan);
MATCH (novgorod:Trade_node {name:"novgorod"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 47.623,calculated_trading_power: 47.623}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.29,calculated_trading_power: 4.29}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (PRU:Country {name:"PRU"}) CREATE (PRU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.409,calculated_trading_power: 8.409}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (KUR:Country {name:"KUR"}) CREATE (KUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.186,calculated_trading_power: 2.186}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 22.357,calculated_trading_power: 22.357}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (QAS:Country {name:"QAS"}) CREATE (QAS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.565,calculated_trading_power: 10.565}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 222.886,calculated_trading_power: 222.886}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (WOL:Country {name:"WOL"}) CREATE (WOL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.218,calculated_trading_power: 2.218}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.186,calculated_trading_power: 2.186}]->(novgorod);
MATCH (laplata:Trade_node {name:"laplata"}), (GUA:Country {name:"GUA"}) CREATE (GUA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.499,calculated_trading_power: 8.499}]->(laplata);
MATCH (laplata:Trade_node {name:"laplata"}), (C00:Country {name:"C00"}) CREATE (C00)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 47.77,calculated_trading_power: 47.77}]->(laplata);
MATCH (laplata:Trade_node {name:"laplata"}), (C06:Country {name:"C06"}) CREATE (C06)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 50.191,calculated_trading_power: 50.191}]->(laplata);
MATCH (brazil:Trade_node {name:"brazil"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.416,calculated_trading_power: 8.416}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 0.572,calculated_trading_power: 0.572}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.743,calculated_trading_power: 5.743}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.678,calculated_trading_power: 3.678}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (SYO:Country {name:"SYO"}) CREATE (SYO)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.306,calculated_trading_power: 3.306}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (KBU:Country {name:"KBU"}) CREATE (KBU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.209,calculated_trading_power: 2.209}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (C00:Country {name:"C00"}) CREATE (C00)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 183.638,calculated_trading_power: 183.638}]->(brazil);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.416,calculated_trading_power: 8.416}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (ALG:Country {name:"ALG"}) CREATE (ALG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 67.83,calculated_trading_power: 67.83}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 70.553,calculated_trading_power: 70.553}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (SOS:Country {name:"SOS"}) CREATE (SOS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 15.317,calculated_trading_power: 15.317}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (ASH:Country {name:"ASH"}) CREATE (ASH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.95,calculated_trading_power: 13.95}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 15.833,calculated_trading_power: 15.833}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (MAL:Country {name:"MAL"}) CREATE (MAL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 19.373,calculated_trading_power: 19.373}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (SON:Country {name:"SON"}) CREATE (SON)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 24.191,calculated_trading_power: 24.191}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (OYO:Country {name:"OYO"}) CREATE (OYO)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.983,calculated_trading_power: 1.983}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.068,calculated_trading_power: 10.068}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (WGD:Country {name:"WGD"}) CREATE (WGD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 19.869,calculated_trading_power: 19.869}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (GUR:Country {name:"GUR"}) CREATE (GUR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.578,calculated_trading_power: 11.578}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (SYO:Country {name:"SYO"}) CREATE (SYO)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.306,calculated_trading_power: 3.306}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (AIR:Country {name:"AIR"}) CREATE (AIR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.169,calculated_trading_power: 11.169}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (BON:Country {name:"BON"}) CREATE (BON)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 16.78,calculated_trading_power: 16.78}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (DAH:Country {name:"DAH"}) CREATE (DAH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.779,calculated_trading_power: 10.779}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (DGB:Country {name:"DGB"}) CREATE (DGB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.715,calculated_trading_power: 12.715}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (FUL:Country {name:"FUL"}) CREATE (FUL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 75.665,calculated_trading_power: 75.665}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (JNN:Country {name:"JNN"}) CREATE (JNN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 51.522,calculated_trading_power: 51.522}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (KBU:Country {name:"KBU"}) CREATE (KBU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.413,calculated_trading_power: 8.413}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (KNG:Country {name:"KNG"}) CREATE (KNG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 16.597,calculated_trading_power: 16.597}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (TMB:Country {name:"TMB"}) CREATE (TMB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 61.549,calculated_trading_power: 61.549}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (YAT:Country {name:"YAT"}) CREATE (YAT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.551,calculated_trading_power: 18.551}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (ZAF:Country {name:"ZAF"}) CREATE (ZAF)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 38.624,calculated_trading_power: 38.624}]->(timbuktu);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 23.983,calculated_trading_power: 23.983}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 35.16,calculated_trading_power: 35.16}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.572,calculated_trading_power: 3.572}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 71.593,calculated_trading_power: 71.593}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 46.548,calculated_trading_power: 46.548}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.844,calculated_trading_power: 11.844}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (ASH:Country {name:"ASH"}) CREATE (ASH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.21,calculated_trading_power: 1.21}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 39.325,calculated_trading_power: 39.325}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (KON:Country {name:"KON"}) CREATE (KON)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.027,calculated_trading_power: 11.027}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (LOA:Country {name:"LOA"}) CREATE (LOA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.553,calculated_trading_power: 12.553}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 27.983,calculated_trading_power: 27.983}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (SYO:Country {name:"SYO"}) CREATE (SYO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 25.852,calculated_trading_power: 25.852}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (FUL:Country {name:"FUL"}) CREATE (FUL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.321,calculated_trading_power: 6.321}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (KBU:Country {name:"KBU"}) CREATE (KBU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.034,calculated_trading_power: 18.034}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (NDO:Country {name:"NDO"}) CREATE (NDO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 16.989,calculated_trading_power: 16.989}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (C03:Country {name:"C03"}) CREATE (C03)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 59.316,calculated_trading_power: 59.316}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 15.847,calculated_trading_power: 15.847}]->(ivory_coast);
MATCH (tunis:Trade_node {name:"tunis"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.057,calculated_trading_power: 10.057}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 27.077,calculated_trading_power: 27.077}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 69.678,calculated_trading_power: 69.678}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 43.622,calculated_trading_power: 43.622}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.006,calculated_trading_power: 5.006}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.965,calculated_trading_power: 2.965}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.467,calculated_trading_power: 10.467}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.075,calculated_trading_power: 3.075}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (ALG:Country {name:"ALG"}) CREATE (ALG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 13.504,calculated_trading_power: 13.504}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (TRP:Country {name:"TRP"}) CREATE (TRP)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 27.788,calculated_trading_power: 27.788}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (TUN:Country {name:"TUN"}) CREATE (TUN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 82.153,calculated_trading_power: 82.153}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (KBA:Country {name:"KBA"}) CREATE (KBA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.619,calculated_trading_power: 18.619}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (TGT:Country {name:"TGT"}) CREATE (TGT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.834,calculated_trading_power: 9.834}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (GHD:Country {name:"GHD"}) CREATE (GHD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.453,calculated_trading_power: 8.453}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (FZA:Country {name:"FZA"}) CREATE (FZA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.833,calculated_trading_power: 9.833}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (MZB:Country {name:"MZB"}) CREATE (MZB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.303,calculated_trading_power: 9.303}]->(tunis);
MATCH (ragusa:Trade_node {name:"ragusa"}), (MON:Country {name:"MON"}) CREATE (MON)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.071,calculated_trading_power: 9.071}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (RAG:Country {name:"RAG"}) CREATE (RAG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 77.313,calculated_trading_power: 77.313}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (TRA:Country {name:"TRA"}) CREATE (TRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 23.678,calculated_trading_power: 23.678}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (WAL:Country {name:"WAL"}) CREATE (WAL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 21.441,calculated_trading_power: 21.441}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 106.807,calculated_trading_power: 106.807}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.728,calculated_trading_power: 6.728}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 21.869,calculated_trading_power: 21.869}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (SLZ:Country {name:"SLZ"}) CREATE (SLZ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.825,calculated_trading_power: 6.825}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 19.497,calculated_trading_power: 19.497}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 43.622,calculated_trading_power: 43.622}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.352,calculated_trading_power: 4.352}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (MOD:Country {name:"MOD"}) CREATE (MOD)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.028,calculated_trading_power: 2.028}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.236,calculated_trading_power: 11.236}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (PAR:Country {name:"PAR"}) CREATE (PAR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.965,calculated_trading_power: 2.965}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.467,calculated_trading_power: 10.467}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (URB:Country {name:"URB"}) CREATE (URB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.263,calculated_trading_power: 4.263}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 39.551,calculated_trading_power: 39.551}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.075,calculated_trading_power: 3.075}]->(ragusa);
MATCH (safi:Trade_node {name:"safi"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 27.077,calculated_trading_power: 27.077}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 37.152,calculated_trading_power: 37.152}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (ALG:Country {name:"ALG"}) CREATE (ALG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 67.098,calculated_trading_power: 67.098}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 58.732,calculated_trading_power: 58.732}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (SOS:Country {name:"SOS"}) CREATE (SOS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 24.371,calculated_trading_power: 24.371}]->(safi);
MATCH (pest:Trade_node {name:"pest"}), (TRA:Country {name:"TRA"}) CREATE (TRA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 53.967,calculated_trading_power: 53.967}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (WAL:Country {name:"WAL"}) CREATE (WAL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 49.668,calculated_trading_power: 49.668}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 32.708,calculated_trading_power: 32.708}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 25.588,calculated_trading_power: 25.588}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (BAV:Country {name:"BAV"}) CREATE (BAV)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 36.302,calculated_trading_power: 36.302}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (BOH:Country {name:"BOH"}) CREATE (BOH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.557,calculated_trading_power: 2.557}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 92.141,calculated_trading_power: 92.141}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (SLZ:Country {name:"SLZ"}) CREATE (SLZ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.902,calculated_trading_power: 8.902}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (ULM:Country {name:"ULM"}) CREATE (ULM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.24,calculated_trading_power: 8.24}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 73.424,calculated_trading_power: 73.424}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (REG:Country {name:"REG"}) CREATE (REG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.479,calculated_trading_power: 10.479}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (OPL:Country {name:"OPL"}) CREATE (OPL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.281,calculated_trading_power: 7.281}]->(pest);
MATCH (krakow:Trade_node {name:"krakow"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 65.297,calculated_trading_power: 65.297}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.282,calculated_trading_power: 2.282}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (PRU:Country {name:"PRU"}) CREATE (PRU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 19.42,calculated_trading_power: 19.42}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 253.808,calculated_trading_power: 253.808}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (ANH:Country {name:"ANH"}) CREATE (ANH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.436,calculated_trading_power: 4.436}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (AUG:Country {name:"AUG"}) CREATE (AUG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.75,calculated_trading_power: 7.75}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (BAV:Country {name:"BAV"}) CREATE (BAV)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 36.302,calculated_trading_power: 36.302}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (BOH:Country {name:"BOH"}) CREATE (BOH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 73.225,calculated_trading_power: 73.225}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (BRA:Country {name:"BRA"}) CREATE (BRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 34.381,calculated_trading_power: 34.381}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (BRU:Country {name:"BRU"}) CREATE (BRU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.144,calculated_trading_power: 12.144}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 67.286,calculated_trading_power: 67.286}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (MAG:Country {name:"MAG"}) CREATE (MAG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.439,calculated_trading_power: 11.439}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (PAL:Country {name:"PAL"}) CREATE (PAL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 51.741,calculated_trading_power: 51.741}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (SAX:Country {name:"SAX"}) CREATE (SAX)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 20.849,calculated_trading_power: 20.849}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (SLZ:Country {name:"SLZ"}) CREATE (SLZ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.902,calculated_trading_power: 8.902}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.338,calculated_trading_power: 11.338}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (ULM:Country {name:"ULM"}) CREATE (ULM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.24,calculated_trading_power: 8.24}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (WUR:Country {name:"WUR"}) CREATE (WUR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.499,calculated_trading_power: 8.499}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (MEM:Country {name:"MEM"}) CREATE (MEM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.746,calculated_trading_power: 5.746}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (PSS:Country {name:"PSS"}) CREATE (PSS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.626,calculated_trading_power: 5.626}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (REG:Country {name:"REG"}) CREATE (REG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.479,calculated_trading_power: 10.479}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (OPL:Country {name:"OPL"}) CREATE (OPL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.376,calculated_trading_power: 18.376}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (GOS:Country {name:"GOS"}) CREATE (GOS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.884,calculated_trading_power: 4.884}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (TNT:Country {name:"TNT"}) CREATE (TNT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.721,calculated_trading_power: 4.721}]->(krakow);
MATCH (wien:Trade_node {name:"wien"}), (ALS:Country {name:"ALS"}) CREATE (ALS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.569,calculated_trading_power: 9.569}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (AAC:Country {name:"AAC"}) CREATE (AAC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.739,calculated_trading_power: 6.739}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ANS:Country {name:"ANS"}) CREATE (ANS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (AUG:Country {name:"AUG"}) CREATE (AUG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.982,calculated_trading_power: 18.982}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BAD:Country {name:"BAD"}) CREATE (BAD)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.415,calculated_trading_power: 8.415}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BAV:Country {name:"BAV"}) CREATE (BAV)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 69.121,calculated_trading_power: 69.121}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BOH:Country {name:"BOH"}) CREATE (BOH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 65.91,calculated_trading_power: 65.91}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BRA:Country {name:"BRA"}) CREATE (BRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 34.381,calculated_trading_power: 34.381}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (FRN:Country {name:"FRN"}) CREATE (FRN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.5,calculated_trading_power: 12.5}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 122.703,calculated_trading_power: 122.703}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (HES:Country {name:"HES"}) CREATE (HES)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 19.162,calculated_trading_power: 19.162}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (KOL:Country {name:"KOL"}) CREATE (KOL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 54.869,calculated_trading_power: 54.869}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MAG:Country {name:"MAG"}) CREATE (MAG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.439,calculated_trading_power: 11.439}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MAI:Country {name:"MAI"}) CREATE (MAI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.82,calculated_trading_power: 9.82}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (PAL:Country {name:"PAL"}) CREATE (PAL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 55.624,calculated_trading_power: 55.624}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (SAX:Country {name:"SAX"}) CREATE (SAX)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 20.849,calculated_trading_power: 20.849}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (SLZ:Country {name:"SLZ"}) CREATE (SLZ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 26.223,calculated_trading_power: 26.223}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 55.664,calculated_trading_power: 55.664}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.338,calculated_trading_power: 11.338}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (TRI:Country {name:"TRI"}) CREATE (TRI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.435,calculated_trading_power: 9.435}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ULM:Country {name:"ULM"}) CREATE (ULM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 24.213,calculated_trading_power: 24.213}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (WBG:Country {name:"WBG"}) CREATE (WBG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.413,calculated_trading_power: 7.413}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (WUR:Country {name:"WUR"}) CREATE (WUR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 20.442,calculated_trading_power: 20.442}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (NUM:Country {name:"NUM"}) CREATE (NUM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.901,calculated_trading_power: 12.901}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MEM:Country {name:"MEM"}) CREATE (MEM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 15.532,calculated_trading_power: 15.532}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (NSA:Country {name:"NSA"}) CREATE (NSA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.31,calculated_trading_power: 4.31}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (RVA:Country {name:"RVA"}) CREATE (RVA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.609,calculated_trading_power: 8.609}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.84,calculated_trading_power: 14.84}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.23,calculated_trading_power: 6.23}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (URB:Country {name:"URB"}) CREATE (URB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.235,calculated_trading_power: 8.235}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 101.578,calculated_trading_power: 101.578}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (PSS:Country {name:"PSS"}) CREATE (PSS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 14.783,calculated_trading_power: 14.783}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ROT:Country {name:"ROT"}) CREATE (ROT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.391,calculated_trading_power: 5.391}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BYT:Country {name:"BYT"}) CREATE (BYT)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (REG:Country {name:"REG"}) CREATE (REG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 38.138,calculated_trading_power: 38.138}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (TTL:Country {name:"TTL"}) CREATE (TTL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.497,calculated_trading_power: 7.497}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (TNT:Country {name:"TNT"}) CREATE (TNT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.239,calculated_trading_power: 13.239}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MLH:Country {name:"MLH"}) CREATE (MLH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BAM:Country {name:"BAM"}) CREATE (BAM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.944,calculated_trading_power: 3.944}]->(wien);
MATCH (saxony:Trade_node {name:"saxony"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.304,calculated_trading_power: 2.304}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 22.733,calculated_trading_power: 22.733}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.188,calculated_trading_power: 2.188}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (ALS:Country {name:"ALS"}) CREATE (ALS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.569,calculated_trading_power: 9.569}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (ANH:Country {name:"ANH"}) CREATE (ANH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.767,calculated_trading_power: 12.767}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BOH:Country {name:"BOH"}) CREATE (BOH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 115.603,calculated_trading_power: 115.603}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BRA:Country {name:"BRA"}) CREATE (BRA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 68.485,calculated_trading_power: 68.485}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.176,calculated_trading_power: 7.176}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BRU:Country {name:"BRU"}) CREATE (BRU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 27.901,calculated_trading_power: 27.901}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (FRN:Country {name:"FRN"}) CREATE (FRN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.5,calculated_trading_power: 12.5}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.355,calculated_trading_power: 10.355}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (HES:Country {name:"HES"}) CREATE (HES)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 19.162,calculated_trading_power: 19.162}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (KOL:Country {name:"KOL"}) CREATE (KOL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 54.869,calculated_trading_power: 54.869}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (MAG:Country {name:"MAG"}) CREATE (MAG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 36.885,calculated_trading_power: 36.885}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (MAI:Country {name:"MAI"}) CREATE (MAI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.82,calculated_trading_power: 9.82}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (PAL:Country {name:"PAL"}) CREATE (PAL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 55.624,calculated_trading_power: 55.624}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (SAX:Country {name:"SAX"}) CREATE (SAX)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 48.244,calculated_trading_power: 48.244}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 29.848,calculated_trading_power: 29.848}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (NUM:Country {name:"NUM"}) CREATE (NUM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.901,calculated_trading_power: 12.901}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (RVA:Country {name:"RVA"}) CREATE (RVA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.609,calculated_trading_power: 8.609}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 48.551,calculated_trading_power: 48.551}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (WOL:Country {name:"WOL"}) CREATE (WOL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 17.929,calculated_trading_power: 17.929}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.648,calculated_trading_power: 14.648}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (GOS:Country {name:"GOS"}) CREATE (GOS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.858,calculated_trading_power: 13.858}]->(saxony);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 94.071,calculated_trading_power: 94.071}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 34.145,calculated_trading_power: 34.145}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.096,calculated_trading_power: 4.096}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (PRU:Country {name:"PRU"}) CREATE (PRU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 48.05,calculated_trading_power: 48.05}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (KUR:Country {name:"KUR"}) CREATE (KUR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.935,calculated_trading_power: 11.935}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 61.616,calculated_trading_power: 61.616}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.198,calculated_trading_power: 9.198}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.707,calculated_trading_power: 12.707}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 35.005,calculated_trading_power: 35.005}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (WOL:Country {name:"WOL"}) CREATE (WOL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.882,calculated_trading_power: 3.882}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.811,calculated_trading_power: 4.811}]->(baltic_sea);
MATCH (rheinland:Trade_node {name:"rheinland"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.304,calculated_trading_power: 2.304}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 22.733,calculated_trading_power: 22.733}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.912,calculated_trading_power: 7.912}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (ALS:Country {name:"ALS"}) CREATE (ALS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 27.844,calculated_trading_power: 27.844}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 24.111,calculated_trading_power: 24.111}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (AAC:Country {name:"AAC"}) CREATE (AAC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 17.938,calculated_trading_power: 17.938}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (ANS:Country {name:"ANS"}) CREATE (ANS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.049,calculated_trading_power: 13.049}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BAD:Country {name:"BAD"}) CREATE (BAD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 19.35,calculated_trading_power: 19.35}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BRA:Country {name:"BRA"}) CREATE (BRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.213,calculated_trading_power: 1.213}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.176,calculated_trading_power: 7.176}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (FRN:Country {name:"FRN"}) CREATE (FRN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 42.129,calculated_trading_power: 42.129}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.377,calculated_trading_power: 5.377}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.355,calculated_trading_power: 10.355}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HES:Country {name:"HES"}) CREATE (HES)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 40.091,calculated_trading_power: 40.091}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (KOL:Country {name:"KOL"}) CREATE (KOL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 101.426,calculated_trading_power: 101.426}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (MAI:Country {name:"MAI"}) CREATE (MAI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 28.199,calculated_trading_power: 28.199}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (PAL:Country {name:"PAL"}) CREATE (PAL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 96.371,calculated_trading_power: 96.371}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 69.126,calculated_trading_power: 69.126}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.263,calculated_trading_power: 1.263}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (TRI:Country {name:"TRI"}) CREATE (TRI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 22.278,calculated_trading_power: 22.278}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (WBG:Country {name:"WBG"}) CREATE (WBG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.601,calculated_trading_power: 18.601}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (NUM:Country {name:"NUM"}) CREATE (NUM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 42.92,calculated_trading_power: 42.92}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (NSA:Country {name:"NSA"}) CREATE (NSA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.105,calculated_trading_power: 13.105}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (RVA:Country {name:"RVA"}) CREATE (RVA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 28.555,calculated_trading_power: 28.555}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.755,calculated_trading_power: 3.755}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 48.551,calculated_trading_power: 48.551}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (ROT:Country {name:"ROT"}) CREATE (ROT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 15.257,calculated_trading_power: 15.257}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BYT:Country {name:"BYT"}) CREATE (BYT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.542,calculated_trading_power: 11.542}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (TTL:Country {name:"TTL"}) CREATE (TTL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 18.704,calculated_trading_power: 18.704}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (WOL:Country {name:"WOL"}) CREATE (WOL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 17.929,calculated_trading_power: 17.929}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 14.648,calculated_trading_power: 14.648}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (MLH:Country {name:"MLH"}) CREATE (MLH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.026,calculated_trading_power: 13.026}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BAM:Country {name:"BAM"}) CREATE (BAM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.468,calculated_trading_power: 11.468}]->(rheinland);
MATCH (panama:Trade_node {name:"panama"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.24,calculated_trading_power: 1.24}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (MIS:Country {name:"MIS"}) CREATE (MIS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 6.787,calculated_trading_power: 6.787}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (C02:Country {name:"C02"}) CREATE (C02)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 16.021,calculated_trading_power: 16.021}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (C03:Country {name:"C03"}) CREATE (C03)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 59.316,calculated_trading_power: 59.316}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 88.006,calculated_trading_power: 88.006}]->(panama);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.928,calculated_trading_power: 7.928}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 24.844,calculated_trading_power: 24.844}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 29.505,calculated_trading_power: 29.505}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 36.676,calculated_trading_power: 36.676}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (LEN:Country {name:"LEN"}) CREATE (LEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.049,calculated_trading_power: 3.049}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (C01:Country {name:"C01"}) CREATE (C01)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.931,calculated_trading_power: 6.931}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (C03:Country {name:"C03"}) CREATE (C03)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 283.783,calculated_trading_power: 283.783}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 31.845,calculated_trading_power: 31.845}]->(carribean_trade);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 45.057,calculated_trading_power: 45.057}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.486,calculated_trading_power: 12.486}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.572,calculated_trading_power: 3.572}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.871,calculated_trading_power: 9.871}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 13.5,calculated_trading_power: 13.5}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (CHE:Country {name:"CHE"}) CREATE (CHE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.069,calculated_trading_power: 10.069}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ABE:Country {name:"ABE"}) CREATE (ABE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.654,calculated_trading_power: 8.654}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (LEN:Country {name:"LEN"}) CREATE (LEN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 24.53,calculated_trading_power: 24.53}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (MAH:Country {name:"MAH"}) CREATE (MAH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (PEQ:Country {name:"PEQ"}) CREATE (PEQ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.696,calculated_trading_power: 9.696}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (POW:Country {name:"POW"}) CREATE (POW)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.532,calculated_trading_power: 10.532}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ALT:Country {name:"ALT"}) CREATE (ALT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.359,calculated_trading_power: 9.359}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ICH:Country {name:"ICH"}) CREATE (ICH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.359,calculated_trading_power: 9.359}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (COF:Country {name:"COF"}) CREATE (COF)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.069,calculated_trading_power: 10.069}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (JOA:Country {name:"JOA"}) CREATE (JOA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.087,calculated_trading_power: 10.087}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (SAT:Country {name:"SAT"}) CREATE (SAT)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.493,calculated_trading_power: 9.493}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (KSI:Country {name:"KSI"}) CREATE (KSI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.892,calculated_trading_power: 9.892}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (OSH:Country {name:"OSH"}) CREATE (OSH)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (TSC:Country {name:"TSC"}) CREATE (TSC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.069,calculated_trading_power: 10.069}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (PEN:Country {name:"PEN"}) CREATE (PEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (WAM:Country {name:"WAM"}) CREATE (WAM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.018,calculated_trading_power: 9.018}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (AGQ:Country {name:"AGQ"}) CREATE (AGQ)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (C01:Country {name:"C01"}) CREATE (C01)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 13.822,calculated_trading_power: 13.822}]->(chesapeake_bay);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.204,calculated_trading_power: 7.204}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 8.848,calculated_trading_power: 8.848}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (SCO:Country {name:"SCO"}) CREATE (SCO)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.104,calculated_trading_power: 12.104}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 18.829,calculated_trading_power: 18.829}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (MAH:Country {name:"MAH"}) CREATE (MAH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.443,calculated_trading_power: 10.443}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (MIK:Country {name:"MIK"}) CREATE (MIK)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.403,calculated_trading_power: 9.403}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (OSH:Country {name:"OSH"}) CREATE (OSH)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 17.309,calculated_trading_power: 17.309}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (PEN:Country {name:"PEN"}) CREATE (PEN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.335,calculated_trading_power: 10.335}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (MLS:Country {name:"MLS"}) CREATE (MLS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (INN:Country {name:"INN"}) CREATE (INN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (AGQ:Country {name:"AGQ"}) CREATE (AGQ)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.358,calculated_trading_power: 10.358}]->(st_lawrence);
MATCH (white_sea:Trade_node {name:"white_sea"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 0.443,calculated_trading_power: 0.443}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.79,calculated_trading_power: 7.79}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.263,calculated_trading_power: 7.263}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (SCO:Country {name:"SCO"}) CREATE (SCO)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.112,calculated_trading_power: 10.112}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 32.825,calculated_trading_power: 32.825}]->(white_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.304,calculated_trading_power: 2.304}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 60.768,calculated_trading_power: 60.768}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 4.096,calculated_trading_power: 4.096}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (CNN:Country {name:"CNN"}) CREATE (CNN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.939,calculated_trading_power: 8.939}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 62.348,calculated_trading_power: 62.348}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (LEI:Country {name:"LEI"}) CREATE (LEI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 10.268,calculated_trading_power: 10.268}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SCO:Country {name:"SCO"}) CREATE (SCO)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 73.174,calculated_trading_power: 73.174}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SLN:Country {name:"SLN"}) CREATE (SLN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.565,calculated_trading_power: 9.565}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (ORD:Country {name:"ORD"}) CREATE (ORD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 8.899,calculated_trading_power: 8.899}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.315,calculated_trading_power: 10.315}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.198,calculated_trading_power: 9.198}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.594,calculated_trading_power: 5.594}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.707,calculated_trading_power: 12.707}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (LAU:Country {name:"LAU"}) CREATE (LAU)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.724,calculated_trading_power: 1.724}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (LUN:Country {name:"LUN"}) CREATE (LUN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (MKL:Country {name:"MKL"}) CREATE (MKL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.222,calculated_trading_power: 2.222}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (OLD:Country {name:"OLD"}) CREATE (OLD)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (VER:Country {name:"VER"}) CREATE (VER)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.042,calculated_trading_power: 2.042}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.871,calculated_trading_power: 9.871}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (LIE:Country {name:"LIE"}) CREATE (LIE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.054,calculated_trading_power: 2.054}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 13.5,calculated_trading_power: 13.5}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 35.005,calculated_trading_power: 35.005}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (WOL:Country {name:"WOL"}) CREATE (WOL)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.1,calculated_trading_power: 6.1}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.997,calculated_trading_power: 6.997}]->(north_sea);
MATCH (lubeck:Trade_node {name:"lubeck"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.522,calculated_trading_power: 11.522}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 165.976,calculated_trading_power: 165.976}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 28.177,calculated_trading_power: 28.177}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 23.983,calculated_trading_power: 23.983}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.315,calculated_trading_power: 10.315}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (BRA:Country {name:"BRA"}) CREATE (BRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 1.319,calculated_trading_power: 1.319}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 49.033,calculated_trading_power: 49.033}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.572,calculated_trading_power: 3.572}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 67.465,calculated_trading_power: 67.465}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (LAU:Country {name:"LAU"}) CREATE (LAU)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 9.158,calculated_trading_power: 9.158}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (LUN:Country {name:"LUN"}) CREATE (LUN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 15.384,calculated_trading_power: 15.384}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (MKL:Country {name:"MKL"}) CREATE (MKL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 27.901,calculated_trading_power: 27.901}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (OLD:Country {name:"OLD"}) CREATE (OLD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.104,calculated_trading_power: 11.104}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (VER:Country {name:"VER"}) CREATE (VER)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.263,calculated_trading_power: 12.263}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.871,calculated_trading_power: 9.871}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.844,calculated_trading_power: 11.844}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 141.953,calculated_trading_power: 141.953}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (WOL:Country {name:"WOL"}) CREATE (WOL)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 47.547,calculated_trading_power: 47.547}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (STE:Country {name:"STE"}) CREATE (STE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 53.762,calculated_trading_power: 53.762}]->(lubeck);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 196.846,calculated_trading_power: 196.846}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (LOR:Country {name:"LOR"}) CREATE (LOR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.19,calculated_trading_power: 12.19}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 59.882,calculated_trading_power: 59.882}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.151,calculated_trading_power: 7.151}]->(bordeaux);
MATCH (sevilla:Trade_node {name:"sevilla"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.329,calculated_trading_power: 3.329}]->(sevilla);
MATCH (sevilla:Trade_node {name:"sevilla"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 141.457,calculated_trading_power: 141.457}]->(sevilla);
MATCH (sevilla:Trade_node {name:"sevilla"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 205.634,calculated_trading_power: 205.634}]->(sevilla);
MATCH (sevilla:Trade_node {name:"sevilla"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.493,calculated_trading_power: 3.493}]->(sevilla);
MATCH (champagne:Trade_node {name:"champagne"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 23.983,calculated_trading_power: 23.983}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 189.053,calculated_trading_power: 189.053}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 7.953,calculated_trading_power: 7.953}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (LOR:Country {name:"LOR"}) CREATE (LOR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 26.224,calculated_trading_power: 26.224}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 124.155,calculated_trading_power: 124.155}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 25.317,calculated_trading_power: 25.317}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 73.912,calculated_trading_power: 73.912}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 12.517,calculated_trading_power: 12.517}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.006,calculated_trading_power: 5.006}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 35.918,calculated_trading_power: 35.918}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 38.631,calculated_trading_power: 38.631}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 9.814,calculated_trading_power: 9.814}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (LIE:Country {name:"LIE"}) CREATE (LIE)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 20.437,calculated_trading_power: 20.437}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 54.9,calculated_trading_power: 54.9}]->(champagne);
MATCH (valencia:Trade_node {name:"valencia"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 23.374,calculated_trading_power: 23.374}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 105.833,calculated_trading_power: 105.833}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 43.622,calculated_trading_power: 43.622}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 5.006,calculated_trading_power: 5.006}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.965,calculated_trading_power: 2.965}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 10.467,calculated_trading_power: 10.467}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 3.075,calculated_trading_power: 3.075}]->(valencia);
MATCH (genua:Trade_node {name:"genua"}), (KNI:Country {name:"KNI"}) CREATE (KNI)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 24.651,calculated_trading_power: 24.651}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 33.643,calculated_trading_power: 33.643}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 53.209,calculated_trading_power: 53.209}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 222.554,calculated_trading_power: 222.554}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.975,calculated_trading_power: 2.975}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 36.483,calculated_trading_power: 36.483}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 27.49,calculated_trading_power: 27.49}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 76.017,calculated_trading_power: 76.017}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 31.74,calculated_trading_power: 31.74}]->(genua);
MATCH (venice:Trade_node {name:"venice"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 6.389,calculated_trading_power: 6.389}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 11.245,calculated_trading_power: 11.245}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 21.618,calculated_trading_power: 21.618}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (MOD:Country {name:"MOD"}) CREATE (MOD)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 11.273,calculated_trading_power: 11.273}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 16.634,calculated_trading_power: 16.634}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (PAR:Country {name:"PAR"}) CREATE (PAR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 12.088,calculated_trading_power: 12.088}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (URB:Country {name:"URB"}) CREATE (URB)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 25.174,calculated_trading_power: 25.174}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 198.36,calculated_trading_power: 198.36}]->(venice);
MATCH (english_channel:Trade_node {name:"english_channel"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 149.475,calculated_trading_power: 149.475}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 37.059,calculated_trading_power: 37.059}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 27.453,calculated_trading_power: 27.453}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 25.921,calculated_trading_power: 25.921}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (LIE:Country {name:"LIE"}) CREATE (LIE)-[r:NodeCountry{is_home: false, has_merchant:  "False",base_trading_power: 2.289,calculated_trading_power: 2.289}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: true, has_merchant:  "False",base_trading_power: 72.547,calculated_trading_power: 72.547}]->(english_channel);
