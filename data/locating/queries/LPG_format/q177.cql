CREATE (african_great_lakes:Trade_node {name:"african_great_lakes", local_value:1.923, is_inland:true, total_power:758.204, outgoing:0.6346094784517095, ingoing:0});
CREATE (kongo:Trade_node {name:"kongo", local_value:3.349, is_inland:true, total_power:790.712, outgoing:0.5766068664968647, ingoing:0.3331699761871475});
CREATE (zambezi:Trade_node {name:"zambezi", local_value:2.161, is_inland:true, total_power:560.83, outgoing:0.4658366788213039, ingoing:0.30271860491085395});
CREATE (patagonia:Trade_node {name:"patagonia", local_value:0.255, is_inland:false, total_power:224.888, outgoing:0.1275, ingoing:0});
CREATE (amazonas_node:Trade_node {name:"amazonas_node", local_value:2.878, is_inland:false, total_power:544.622, outgoing:1.4390000000000003, ingoing:0});
CREATE (rio_grande:Trade_node {name:"rio_grande", local_value:2.889, is_inland:false, total_power:491.27599999999984, outgoing:1.1426309141908015, ingoing:0});
CREATE (james_bay:Trade_node {name:"james_bay", local_value:1.958, is_inland:false, total_power:369.3, outgoing:0.3035668778770647, ingoing:0});
CREATE (california:Trade_node {name:"california", local_value:1.984, is_inland:false, total_power:617.234, outgoing:0.5663213504598946, ingoing:0.5592934308522395});
CREATE (girin:Trade_node {name:"girin", local_value:6.628, is_inland:false, total_power:948.1360000000001, outgoing:2.7590698809431555, ingoing:0.14865935449572235});
CREATE (mississippi_river:Trade_node {name:"mississippi_river", local_value:4.947, is_inland:false, total_power:799.928, outgoing:1.0862236173806792, ingoing:0.5485801744625028});
CREATE (ohio:Trade_node {name:"ohio", local_value:7.458, is_inland:true, total_power:1241.2079999999999, outgoing:2.5936725008577723, ingoing:0.5702673991248566});
CREATE (mexico:Trade_node {name:"mexico", local_value:14.124, is_inland:false, total_power:917.488, outgoing:2.0622801050019035, ingoing:0.5485801744625028});
CREATE (lhasa:Trade_node {name:"lhasa", local_value:1.626, is_inland:true, total_power:771.254, outgoing:0.49408006960093553, ingoing:0});
CREATE (chengdu:Trade_node {name:"chengdu", local_value:2.694, is_inland:true, total_power:1125.5160000000003, outgoing:1.3772292096595389, ingoing:0.17292802436032745});
CREATE (burma:Trade_node {name:"burma", local_value:6.001, is_inland:false, total_power:850.3180000000003, outgoing:1.5648451770138032, ingoing:0.4820302233808386});
CREATE (gulf_of_siam:Trade_node {name:"gulf_of_siam", local_value:9.257, is_inland:false, total_power:1166.5800000000004, outgoing:2.1085735847666967, ingoing:0.8215437179322467});
CREATE (canton:Trade_node {name:"canton", local_value:6.793, is_inland:false, total_power:990.8620000000001, outgoing:2.8490525101426645, ingoing:1.5890313553833544});
CREATE (philippines:Trade_node {name:"philippines", local_value:3.509, is_inland:false, total_power:525.6219999999998, outgoing:1.539097291712129, ingoing:0.9971683785499326});
CREATE (cuiaba:Trade_node {name:"cuiaba", local_value:3.109, is_inland:false, total_power:668.688, outgoing:1.3113745153569378, ingoing:0.6375250000000001});
CREATE (lima:Trade_node {name:"lima", local_value:3.6, is_inland:false, total_power:659.1279999999999, outgoing:0.30040480007350584, ingoing:0.45898108037492824});
CREATE (polynesia_node:Trade_node {name:"polynesia_node", local_value:2.43, is_inland:false, total_power:1148.3640000000003, outgoing:0.23060990769834827, ingoing:1.836195989433847});
CREATE (australia:Trade_node {name:"australia", local_value:2.456, is_inland:false, total_power:614.586, outgoing:0.0, ingoing:0.0807134676944219});
CREATE (nippon:Trade_node {name:"nippon", local_value:9.075, is_inland:false, total_power:1174.91, outgoing:0.32440887090775594, ingoing:1.0463879260245266});
CREATE (hangzhou:Trade_node {name:"hangzhou", local_value:10.989, is_inland:false, total_power:965.5000000000003, outgoing:6.163398846501536, ingoing:1.3377976930030764});
CREATE (xian:Trade_node {name:"xian", local_value:5.079, is_inland:true, total_power:737.462, outgoing:3.859109909828188, ingoing:2.6392198196563763});
CREATE (beijing:Trade_node {name:"beijing", local_value:5.313, is_inland:false, total_power:403.116, outgoing:0.25231089878381063, ingoing:5.14889675726544});
CREATE (the_moluccas:Trade_node {name:"the_moluccas", local_value:13.345, is_inland:false, total_power:1214.438, outgoing:2.0078744374190753, ingoing:0.8080260781488678});
CREATE (siberia:Trade_node {name:"siberia", local_value:4.377, is_inland:true, total_power:419.63399999999996, outgoing:2.671337229165052, ingoing:0.9656744583301046});
CREATE (yumen:Trade_node {name:"yumen", local_value:3.149, is_inland:true, total_power:781.4060000000001, outgoing:1.6561116262935847, ingoing:2.2909591463827996});
CREATE (malacca:Trade_node {name:"malacca", local_value:12.531, is_inland:false, total_power:1619.9700000000005, outgoing:1.1354003730947326, ingoing:6.369627266118014});
CREATE (ganges_delta:Trade_node {name:"ganges_delta", local_value:12.328, is_inland:false, total_power:991.0439999999998, outgoing:4.628086888233043, ingoing:1.5905569381673088});
CREATE (doab:Trade_node {name:"doab", local_value:11.791, is_inland:true, total_power:1390.356, outgoing:5.553896492088549, ingoing:2.429745616322348});
CREATE (lahore:Trade_node {name:"lahore", local_value:7.376, is_inland:true, total_power:953.3719999999998, outgoing:4.42067980940248, ingoing:3.088723682706816});
CREATE (deccan:Trade_node {name:"deccan", local_value:11.8, is_inland:true, total_power:1093.0219999999997, outgoing:2.123999918442997, ingoing:2.9157956583464886});
CREATE (comorin_cape:Trade_node {name:"comorin_cape", local_value:14.287, is_inland:false, total_power:1079.25, outgoing:5.408423133046053, ingoing:3.5448455735049214});
CREATE (gujarat:Trade_node {name:"gujarat", local_value:8.659, is_inland:false, total_power:988.2710000000003, outgoing:4.643587952099204, ingoing:4.555285987039561});
CREATE (katsina:Trade_node {name:"katsina", local_value:4.417, is_inland:true, total_power:1463.7109999999998, outgoing:1.3011327420508558, ingoing:0});
CREATE (ethiopia:Trade_node {name:"ethiopia", local_value:3.253, is_inland:true, total_power:964.438, outgoing:0.70532505748987, ingoing:0.4553964597177996});
CREATE (gulf_of_aden:Trade_node {name:"gulf_of_aden", local_value:5.617, is_inland:false, total_power:1455.5239999999997, outgoing:1.1025479277515031, ingoing:3.888499534983022});
CREATE (hormuz:Trade_node {name:"hormuz", local_value:3.748, is_inland:false, total_power:346.38199999999995, outgoing:1.6381502857953352, ingoing:2.0111475579477474});
CREATE (zanzibar:Trade_node {name:"zanzibar", local_value:5.944, is_inland:false, total_power:793.152, outgoing:0.6173578395719997, ingoing:2.833446046897264});
CREATE (cape_of_good_hope:Trade_node {name:"cape_of_good_hope", local_value:0.4, is_inland:false, total_power:78.642, outgoing:1.768629511995727, ingoing:3.137259023991453});
CREATE (basra:Trade_node {name:"basra", local_value:4.036, is_inland:false, total_power:589.306, outgoing:2.0777467312976663, ingoing:1.720057800085102});
CREATE (samarkand:Trade_node {name:"samarkand", local_value:5.381, is_inland:true, total_power:1188.31, outgoing:1.5399683445879209, ingoing:4.688607186210785});
CREATE (persia:Trade_node {name:"persia", local_value:9.427, is_inland:true, total_power:974.7040000000001, outgoing:1.6305226301439122, ingoing:3.446538348130802});
CREATE (aleppo:Trade_node {name:"aleppo", local_value:5.981, is_inland:false, total_power:600.552, outgoing:3.9639207073784144, ingoing:1.946841414756829});
CREATE (alexandria:Trade_node {name:"alexandria", local_value:7.61, is_inland:false, total_power:1066.2560000000003, outgoing:4.428411929506506, ingoing:2.8372458012688755});
CREATE (astrakhan:Trade_node {name:"astrakhan", local_value:3.789, is_inland:true, total_power:450.772, outgoing:1.8982603962866074, ingoing:1.6645077617342126});
CREATE (crimea:Trade_node {name:"crimea", local_value:5.369, is_inland:false, total_power:808.3360000000002, outgoing:2.1719835191645545, ingoing:0.9965867080504689});
CREATE (constantinople:Trade_node {name:"constantinople", local_value:7.76, is_inland:false, total_power:655.882, outgoing:1.067126303872239, ingoing:4.391196778408538});
CREATE (kiev:Trade_node {name:"kiev", local_value:5.433, is_inland:true, total_power:902.6939999999998, outgoing:3.0965971158537977, ingoing:0.7601942317075941});
CREATE (kazan:Trade_node {name:"kazan", local_value:3.204, is_inland:true, total_power:539.85, outgoing:2.8015193766810604, ingoing:2.399038753362121});
CREATE (novgorod:Trade_node {name:"novgorod", local_value:8.418, is_inland:false, total_power:741.43, outgoing:1.9970190570834148, ingoing:4.567308831338357});
CREATE (laplata:Trade_node {name:"laplata", local_value:1.673, is_inland:false, total_power:244.504, outgoing:1.0560477353957618, ingoing:0.5928560803749283});
CREATE (brazil:Trade_node {name:"brazil", local_value:7.538, is_inland:false, total_power:534.712, outgoing:0.4114354318791457, ingoing:2.0714812025404785});
CREATE (timbuktu:Trade_node {name:"timbuktu", local_value:5.806, is_inland:true, total_power:1302.7019999999998, outgoing:1.0142466172158109, ingoing:0.4553964597177996});
CREATE (ivory_coast:Trade_node {name:"ivory_coast", local_value:3.116, is_inland:false, total_power:1244.81, outgoing:1.986552354937784, ingoing:3.1242662700177712});
CREATE (tunis:Trade_node {name:"tunis", local_value:3.008, is_inland:false, total_power:863.04, outgoing:0.8840401329030095, ingoing:0.4553964597177996});
CREATE (ragusa:Trade_node {name:"ragusa", local_value:6.09, is_inland:false, total_power:1012.7960000000002, outgoing:2.9027290731902244, ingoing:1.120482619065851});
CREATE (safi:Trade_node {name:"safi", local_value:4.14, is_inland:false, total_power:518.116, outgoing:0.7274269455775877, ingoing:0.5324794740383008});
CREATE (pest:Trade_node {name:"pest", local_value:4.361, is_inland:true, total_power:1163.324, outgoing:2.7709451884857397, ingoing:1.7761494073241728});
CREATE (krakow:Trade_node {name:"krakow", local_value:7.324, is_inland:true, total_power:1835.63, outgoing:3.882507265627556, ingoing:3.080459709778257});
CREATE (wien:Trade_node {name:"wien", local_value:9.087, is_inland:true, total_power:2059.584, outgoing:3.621068527858107, ingoing:2.8136237669246578});
CREATE (saxony:Trade_node {name:"saxony", local_value:9.551, is_inland:true, total_power:1670.1520000000003, outgoing:3.368096217723976, ingoing:2.6262515277199823});
CREATE (baltic_sea:Trade_node {name:"baltic_sea", local_value:7.814, is_inland:false, total_power:558.55, outgoing:2.75694537984006, ingoing:2.407312547938437});
CREATE (rheinland:Trade_node {name:"rheinland", local_value:12.04, is_inland:true, total_power:1734.3760000000002, outgoing:2.9858824426162642, ingoing:3.035624499055425});
CREATE (panama:Trade_node {name:"panama", local_value:5.146, is_inland:false, total_power:441.082, outgoing:2.6803416559197224, ingoing:0.9602240244836787});
CREATE (carribean_trade:Trade_node {name:"carribean_trade", local_value:12.711, is_inland:false, total_power:1193.2459999999999, outgoing:4.47903564319502, ingoing:5.131544167762399});
CREATE (chesapeake_bay:Trade_node {name:"chesapeake_bay", local_value:7.412, is_inland:false, total_power:994.972, outgoing:1.7860153606137963, ingoing:2.9293405380685877});
CREATE (st_lawrence:Trade_node {name:"st_lawrence", local_value:5.933, is_inland:false, total_power:716.776, outgoing:0.9834011725825923, ingoing:2.4587087381580326});
CREATE (white_sea:Trade_node {name:"white_sea", local_value:1.9, is_inland:false, total_power:155.43, outgoing:1.4742175024843962, ingoing:1.0484350049687927});
CREATE (north_sea:Trade_node {name:"north_sea", local_value:5.24, is_inland:false, total_power:757.878, outgoing:2.9250279576826443, ingoing:2.064213993214477});
CREATE (lubeck:Trade_node {name:"lubeck", local_value:7.383, is_inland:false, total_power:1601.8359999999998, outgoing:1.284197681921703, ingoing:7.766271123294077});
CREATE (bordeaux:Trade_node {name:"bordeaux", local_value:6.834, is_inland:false, total_power:702.946, outgoing:4.719709041947642, ingoing:2.605418083895286});
CREATE (sevilla:Trade_node {name:"sevilla", local_value:9.688, is_inland:false, total_power:879.41, outgoing:0.3544690921689127, ingoing:3.162344807661946});
CREATE (champagne:Trade_node {name:"champagne", local_value:13.422, is_inland:true, total_power:1923.838, outgoing:5.3018071994797324, ingoing:6.523282776418562});
CREATE (valencia:Trade_node {name:"valencia", local_value:4.972, is_inland:false, total_power:365.89000000000004, outgoing:2.8268032966467063, ingoing:0.6816065932934117});
CREATE (genua:Trade_node {name:"genua", local_value:12.898, is_inland:false, total_power:1174.162, outgoing:1.8095291669986122, ingoing:8.626905638665809});
CREATE (venice:Trade_node {name:"venice", local_value:9.412, is_inland:false, total_power:597.3870000000001, outgoing:1.475481061138661, ingoing:3.8332733356941935});
CREATE (english_channel:Trade_node {name:"english_channel", local_value:17.084, is_inland:false, total_power:873.4599999999999, outgoing:2.0783839276752585, ingoing:7.126624081021448});
CREATE (SWE:Country {name:"SWE", home_node:"baltic_sea", development:314.924});
CREATE (DAN:Country {name:"DAN", home_node:"lubeck", development:166.977});
CREATE (SHL:Country {name:"SHL", home_node:"lubeck", development:22.784});
CREATE (KNI:Country {name:"KNI", home_node:"genua", development:9.0});
CREATE (MOL:Country {name:"MOL", home_node:"crimea", development:27.83});
CREATE (MON:Country {name:"MON", home_node:"ragusa", development:5.0});
CREATE (RAG:Country {name:"RAG", home_node:"ragusa", development:14.0});
CREATE (WAL:Country {name:"WAL", home_node:"pest", development:45.67});
CREATE (TUR:Country {name:"TUR", home_node:"constantinople", development:1185.917});
CREATE (ENG:Country {name:"ENG", home_node:"english_channel", development:453.009});
CREATE (SCO:Country {name:"SCO", home_node:"north_sea", development:89.362});
CREATE (PRU:Country {name:"PRU", home_node:"saxony", development:182.62});
CREATE (KUR:Country {name:"KUR", home_node:"baltic_sea", development:12.307});
CREATE (PLC:Country {name:"PLC", home_node:"krakow", development:437.677});
CREATE (FRA:Country {name:"FRA", home_node:"champagne", development:797.677});
CREATE (AAC:Country {name:"AAC", home_node:"rheinland", development:13.0});
CREATE (ANH:Country {name:"ANH", home_node:"saxony", development:6.0});
CREATE (ANS:Country {name:"ANS", home_node:"rheinland", development:8.0});
CREATE (AUG:Country {name:"AUG", home_node:"wien", development:17.0});
CREATE (BAD:Country {name:"BAD", home_node:"rheinland", development:16.0});
CREATE (BAV:Country {name:"BAV", home_node:"wien", development:87.806});
CREATE (BRE:Country {name:"BRE", home_node:"lubeck", development:16.0});
CREATE (BRU:Country {name:"BRU", home_node:"saxony", development:15.0});
CREATE (EFR:Country {name:"EFR", home_node:"english_channel", development:7.0});
CREATE (FRN:Country {name:"FRN", home_node:"rheinland", development:24.0});
CREATE (HAB:Country {name:"HAB", home_node:"wien", development:535.967});
CREATE (HAM:Country {name:"HAM", home_node:"lubeck", development:19.0});
CREATE (HAN:Country {name:"HAN", home_node:"saxony", development:25.134});
CREATE (HES:Country {name:"HES", home_node:"rheinland", development:42.69});
CREATE (KOL:Country {name:"KOL", home_node:"rheinland", development:23.0});
CREATE (LOR:Country {name:"LOR", home_node:"champagne", development:24.7});
CREATE (LUN:Country {name:"LUN", home_node:"lubeck", development:19.9});
CREATE (MAI:Country {name:"MAI", home_node:"rheinland", development:16.664});
CREATE (MKL:Country {name:"MKL", home_node:"lubeck", development:17.92});
CREATE (MUN:Country {name:"MUN", home_node:"rheinland", development:36.162});
CREATE (PAL:Country {name:"PAL", home_node:"rheinland", development:56.417});
CREATE (SAX:Country {name:"SAX", home_node:"saxony", development:58.8});
CREATE (SLZ:Country {name:"SLZ", home_node:"wien", development:14.0});
CREATE (SWI:Country {name:"SWI", home_node:"champagne", development:69.01});
CREATE (THU:Country {name:"THU", home_node:"saxony", development:25.206});
CREATE (TRI:Country {name:"TRI", home_node:"rheinland", development:20.0});
CREATE (ULM:Country {name:"ULM", home_node:"wien", development:15.0});
CREATE (WBG:Country {name:"WBG", home_node:"rheinland", development:15.51});
CREATE (WUR:Country {name:"WUR", home_node:"wien", development:18.856});
CREATE (NUM:Country {name:"NUM", home_node:"rheinland", development:19.0});
CREATE (MEM:Country {name:"MEM", home_node:"wien", development:13.0});
CREATE (NSA:Country {name:"NSA", home_node:"rheinland", development:9.0});
CREATE (RVA:Country {name:"RVA", home_node:"rheinland", development:12.0});
CREATE (POR:Country {name:"POR", home_node:"sevilla", development:212.084});
CREATE (SPA:Country {name:"SPA", home_node:"sevilla", development:811.636});
CREATE (GEN:Country {name:"GEN", home_node:"genua", development:45.056});
CREATE (MAN:Country {name:"MAN", home_node:"venice", development:29.572});
CREATE (MOD:Country {name:"MOD", home_node:"venice", development:12.0});
CREATE (PAP:Country {name:"PAP", home_node:"genua", development:116.898});
CREATE (PAR:Country {name:"PAR", home_node:"venice", development:15.0});
CREATE (SAV:Country {name:"SAV", home_node:"genua", development:69.503});
CREATE (TUS:Country {name:"TUS", home_node:"genua", development:77.212});
CREATE (VEN:Country {name:"VEN", home_node:"venice", development:149.347});
CREATE (LUC:Country {name:"LUC", home_node:"genua", development:14.0});
CREATE (LIE:Country {name:"LIE", home_node:"champagne", development:26.424});
CREATE (NED:Country {name:"NED", home_node:"english_channel", development:376.545});
CREATE (CRI:Country {name:"CRI", home_node:"crimea", development:56.298});
CREATE (GEO:Country {name:"GEO", home_node:"crimea", development:20.986});
CREATE (RUS:Country {name:"RUS", home_node:"novgorod", development:1165.369});
CREATE (ZAZ:Country {name:"ZAZ", home_node:"crimea", development:8.19});
CREATE (ADE:Country {name:"ADE", home_node:"gulf_of_aden", development:13.0});
CREATE (ANZ:Country {name:"ANZ", home_node:"basra", development:7.0});
CREATE (ARD:Country {name:"ARD", home_node:"basra", development:7.0});
CREATE (DAW:Country {name:"DAW", home_node:"basra", development:14.0});
CREATE (HDR:Country {name:"HDR", home_node:"gulf_of_aden", development:14.92});
CREATE (HED:Country {name:"HED", home_node:"alexandria", development:48.715});
CREATE (MHR:Country {name:"MHR", home_node:"gulf_of_aden", development:9.784});
CREATE (NAJ:Country {name:"NAJ", home_node:"basra", development:8.306});
CREATE (NJR:Country {name:"NJR", home_node:"gulf_of_aden", development:6.0});
CREATE (OMA:Country {name:"OMA", home_node:"hormuz", development:68.139});
CREATE (RAS:Country {name:"RAS", home_node:"gulf_of_aden", development:82.482});
CREATE (SHM:Country {name:"SHM", home_node:"basra", development:9.0});
CREATE (AVR:Country {name:"AVR", home_node:"astrakhan", development:7.0});
CREATE (MSY:Country {name:"MSY", home_node:"basra", development:27.452});
CREATE (ALG:Country {name:"ALG", home_node:"safi", development:58.258});
CREATE (MOR:Country {name:"MOR", home_node:"safi", development:163.975});
CREATE (TRP:Country {name:"TRP", home_node:"tunis", development:13.088});
CREATE (TUN:Country {name:"TUN", home_node:"tunis", development:67.558});
CREATE (KBA:Country {name:"KBA", home_node:"tunis", development:8.0});
CREATE (TGT:Country {name:"TGT", home_node:"tunis", development:5.706});
CREATE (GHD:Country {name:"GHD", home_node:"tunis", development:3.0});
CREATE (FZA:Country {name:"FZA", home_node:"tunis", development:7.512});
CREATE (MZB:Country {name:"MZB", home_node:"tunis", development:5.703});
CREATE (KZH:Country {name:"KZH", home_node:"samarkand", development:157.854});
CREATE (KHI:Country {name:"KHI", home_node:"samarkand", development:59.658});
CREATE (BUK:Country {name:"BUK", home_node:"samarkand", development:116.286});
CREATE (PER:Country {name:"PER", home_node:"persia", development:506.504});
CREATE (CIR:Country {name:"CIR", home_node:"crimea", development:12.94});
CREATE (GAZ:Country {name:"GAZ", home_node:"astrakhan", development:38.588});
CREATE (IME:Country {name:"IME", home_node:"crimea", development:10.0});
CREATE (MGR:Country {name:"MGR", home_node:"persia", development:6.0});
CREATE (CHE:Country {name:"CHE", home_node:"chesapeake_bay", development:8.0});
CREATE (ASH:Country {name:"ASH", home_node:"timbuktu", development:13.95});
CREATE (BEN:Country {name:"BEN", home_node:"ivory_coast", development:23.87});
CREATE (ETH:Country {name:"ETH", home_node:"ethiopia", development:91.81});
CREATE (KON:Country {name:"KON", home_node:"kongo", development:34.598});
CREATE (MAL:Country {name:"MAL", home_node:"timbuktu", development:9.76});
CREATE (NUB:Country {name:"NUB", home_node:"ethiopia", development:46.728});
CREATE (SON:Country {name:"SON", home_node:"timbuktu", development:23.76});
CREATE (ZAN:Country {name:"ZAN", home_node:"zanzibar", development:27.33});
CREATE (ZIM:Country {name:"ZIM", home_node:"zambezi", development:9.95});
CREATE (HAU:Country {name:"HAU", home_node:"katsina", development:20.498});
CREATE (KBO:Country {name:"KBO", home_node:"katsina", development:59.548});
CREATE (LOA:Country {name:"LOA", home_node:"ivory_coast", development:18.092});
CREATE (OYO:Country {name:"OYO", home_node:"katsina", development:28.652});
CREATE (SOF:Country {name:"SOF", home_node:"timbuktu", development:66.874});
CREATE (JOL:Country {name:"JOL", home_node:"ivory_coast", development:20.376});
CREATE (MLI:Country {name:"MLI", home_node:"zanzibar", development:14.55});
CREATE (AJU:Country {name:"AJU", home_node:"gulf_of_aden", development:24.726});
CREATE (MDI:Country {name:"MDI", home_node:"gulf_of_aden", development:10.0});
CREATE (ENA:Country {name:"ENA", home_node:"ethiopia", development:7.0});
CREATE (WGD:Country {name:"WGD", home_node:"timbuktu", development:18.9});
CREATE (GUR:Country {name:"GUR", home_node:"timbuktu", development:5.856});
CREATE (OGD:Country {name:"OGD", home_node:"ethiopia", development:3.0});
CREATE (RZI:Country {name:"RZI", home_node:"zambezi", development:34.31});
CREATE (WAD:Country {name:"WAD", home_node:"ethiopia", development:3.0});
CREATE (AFA:Country {name:"AFA", home_node:"gulf_of_aden", development:10.952});
CREATE (DAR:Country {name:"DAR", home_node:"ethiopia", development:11.91});
CREATE (HAR:Country {name:"HAR", home_node:"gulf_of_aden", development:18.97});
CREATE (HOB:Country {name:"HOB", home_node:"gulf_of_aden", development:12.76});
CREATE (KAF:Country {name:"KAF", home_node:"ethiopia", development:13.106});
CREATE (MED:Country {name:"MED", home_node:"ethiopia", development:16.89});
CREATE (MJE:Country {name:"MJE", home_node:"gulf_of_aden", development:14.0});
CREATE (MRE:Country {name:"MRE", home_node:"ethiopia", development:3.0});
CREATE (PTE:Country {name:"PTE", home_node:"zanzibar", development:8.0});
CREATE (WAR:Country {name:"WAR", home_node:"gulf_of_aden", development:38.578});
CREATE (WLY:Country {name:"WLY", home_node:"ethiopia", development:13.616});
CREATE (JJI:Country {name:"JJI", home_node:"ethiopia", development:4.0});
CREATE (ABB:Country {name:"ABB", home_node:"ethiopia", development:10.166});
CREATE (SYO:Country {name:"SYO", home_node:"ivory_coast", development:17.594});
CREATE (KSJ:Country {name:"KSJ", home_node:"kongo", development:8.97});
CREATE (LUB:Country {name:"LUB", home_node:"kongo", development:29.79});
CREATE (LND:Country {name:"LND", home_node:"kongo", development:55.736});
CREATE (KZB:Country {name:"KZB", home_node:"kongo", development:20.87});
CREATE (YAK:Country {name:"YAK", home_node:"kongo", development:8.0});
CREATE (KUB:Country {name:"KUB", home_node:"kongo", development:9.96});
CREATE (RWA:Country {name:"RWA", home_node:"african_great_lakes", development:17.93});
CREATE (BUU:Country {name:"BUU", home_node:"african_great_lakes", development:9.0});
CREATE (BUG:Country {name:"BUG", home_node:"african_great_lakes", development:12.0});
CREATE (NKO:Country {name:"NKO", home_node:"african_great_lakes", development:8.0});
CREATE (KRW:Country {name:"KRW", home_node:"african_great_lakes", development:13.712});
CREATE (BNY:Country {name:"BNY", home_node:"african_great_lakes", development:13.908});
CREATE (BSG:Country {name:"BSG", home_node:"african_great_lakes", development:9.0});
CREATE (UBH:Country {name:"UBH", home_node:"african_great_lakes", development:19.86});
CREATE (MRA:Country {name:"MRA", home_node:"zambezi", development:20.362});
CREATE (LDU:Country {name:"LDU", home_node:"zambezi", development:11.4});
CREATE (TBK:Country {name:"TBK", home_node:"zambezi", development:10.95});
CREATE (MKU:Country {name:"MKU", home_node:"zambezi", development:4.0});
CREATE (MIR:Country {name:"MIR", home_node:"zanzibar", development:11.01});
CREATE (SKA:Country {name:"SKA", home_node:"zanzibar", development:21.975});
CREATE (BTS:Country {name:"BTS", home_node:"zanzibar", development:14.174});
CREATE (MFY:Country {name:"MFY", home_node:"zanzibar", development:7.743});
CREATE (ANT:Country {name:"ANT", home_node:"zanzibar", development:10.95});
CREATE (ANN:Country {name:"ANN", home_node:"canton", development:77.43});
CREATE (ARK:Country {name:"ARK", home_node:"ganges_delta", development:19.52});
CREATE (ATJ:Country {name:"ATJ", home_node:"malacca", development:71.787});
CREATE (AYU:Country {name:"AYU", home_node:"gulf_of_siam", development:170.594});
CREATE (BLI:Country {name:"BLI", home_node:"the_moluccas", development:9.0});
CREATE (BAN:Country {name:"BAN", home_node:"the_moluccas", development:28.066});
CREATE (BEI:Country {name:"BEI", home_node:"malacca", development:74.616});
CREATE (CHA:Country {name:"CHA", home_node:"gulf_of_siam", development:10.0});
CREATE (DAI:Country {name:"DAI", home_node:"canton", development:19.594});
CREATE (JAP:Country {name:"JAP", home_node:"nippon", development:266.911});
CREATE (DTE:Country {name:"DTE", home_node:"nippon", development:15.307});
CREATE (HSK:Country {name:"HSK", home_node:"nippon", development:8.0});
CREATE (IKE:Country {name:"IKE", home_node:"nippon", development:7.0});
CREATE (MAE:Country {name:"MAE", home_node:"nippon", development:9.0});
CREATE (MRI:Country {name:"MRI", home_node:"nippon", development:9.0});
CREATE (SMZ:Country {name:"SMZ", home_node:"nippon", development:9.0});
CREATE (UES:Country {name:"UES", home_node:"nippon", development:7.0});
CREATE (RFR:Country {name:"RFR", home_node:"nippon", development:8.0});
CREATE (ITO:Country {name:"ITO", home_node:"nippon", development:7.0});
CREATE (SHN:Country {name:"SHN", home_node:"nippon", development:9.0});
CREATE (STK:Country {name:"STK", home_node:"nippon", development:6.0});
CREATE (KHM:Country {name:"KHM", home_node:"gulf_of_siam", development:51.638});
CREATE (KOR:Country {name:"KOR", home_node:"nippon", development:126.698});
CREATE (LNA:Country {name:"LNA", home_node:"gulf_of_siam", development:54.278});
CREATE (LXA:Country {name:"LXA", home_node:"gulf_of_siam", development:138.432});
CREATE (MKS:Country {name:"MKS", home_node:"the_moluccas", development:22.0});
CREATE (MTR:Country {name:"MTR", home_node:"the_moluccas", development:87.0});
CREATE (PAT:Country {name:"PAT", home_node:"malacca", development:15.0});
CREATE (QNG:Country {name:"QNG", home_node:"beijing", development:1532.695});
CREATE (RYU:Country {name:"RYU", home_node:"nippon", development:5.0});
CREATE (SUK:Country {name:"SUK", home_node:"gulf_of_siam", development:11.0});
CREATE (SUL:Country {name:"SUL", home_node:"philippines", development:11.0});
CREATE (TAU:Country {name:"TAU", home_node:"burma", development:238.238});
CREATE (TOK:Country {name:"TOK", home_node:"canton", development:76.472});
CREATE (ZUN:Country {name:"ZUN", home_node:"yumen", development:129.476});
CREATE (KSD:Country {name:"KSD", home_node:"lhasa", development:67.996});
CREATE (KAM:Country {name:"KAM", home_node:"chengdu", development:14.0});
CREATE (BAL:Country {name:"BAL", home_node:"lahore", development:31.278});
CREATE (MAR:Country {name:"MAR", home_node:"deccan", development:60.435});
CREATE (MUG:Country {name:"MUG", home_node:"deccan", development:1089.249});
CREATE (MYS:Country {name:"MYS", home_node:"comorin_cape", development:59.086});
CREATE (ASS:Country {name:"ASS", home_node:"burma", development:43.596});
CREATE (GUJ:Country {name:"GUJ", home_node:"gujarat", development:8.0});
CREATE (MAD:Country {name:"MAD", home_node:"comorin_cape", development:26.84});
CREATE (MER:Country {name:"MER", home_node:"gujarat", development:24.0});
CREATE (JAN:Country {name:"JAN", home_node:"gujarat", development:6.0});
CREATE (GDW:Country {name:"GDW", home_node:"deccan", development:13.842});
CREATE (GRJ:Country {name:"GRJ", home_node:"ganges_delta", development:5.0});
CREATE (DHU:Country {name:"DHU", home_node:"doab", development:13.0});
CREATE (KLN:Country {name:"KLN", home_node:"comorin_cape", development:19.292});
CREATE (MAB:Country {name:"MAB", home_node:"comorin_cape", development:7.0});
CREATE (BST:Country {name:"BST", home_node:"ganges_delta", development:6.97});
CREATE (BHU:Country {name:"BHU", home_node:"lhasa", development:6.476});
CREATE (BND:Country {name:"BND", home_node:"doab", development:8.0});
CREATE (JSL:Country {name:"JSL", home_node:"gujarat", development:3.0});
CREATE (KAC:Country {name:"KAC", home_node:"burma", development:3.0});
CREATE (KMT:Country {name:"KMT", home_node:"ganges_delta", development:9.0});
CREATE (MLB:Country {name:"MLB", home_node:"burma", development:5.0});
CREATE (HAD:Country {name:"HAD", home_node:"doab", development:10.0});
CREATE (LDK:Country {name:"LDK", home_node:"lahore", development:8.712});
CREATE (BGL:Country {name:"BGL", home_node:"doab", development:9.95});
CREATE (GHR:Country {name:"GHR", home_node:"doab", development:3.0});
CREATE (CHD:Country {name:"CHD", home_node:"deccan", development:5.0});
CREATE (JAJ:Country {name:"JAJ", home_node:"gujarat", development:10.0});
CREATE (TPR:Country {name:"TPR", home_node:"burma", development:3.0});
CREATE (SKK:Country {name:"SKK", home_node:"lhasa", development:6.968});
CREATE (RJK:Country {name:"RJK", home_node:"gujarat", development:5.0});
CREATE (PAN:Country {name:"PAN", home_node:"doab", development:9.905});
CREATE (SBP:Country {name:"SBP", home_node:"ganges_delta", development:11.924});
CREATE (PTT:Country {name:"PTT", home_node:"ganges_delta", development:4.0});
CREATE (RTT:Country {name:"RTT", home_node:"ganges_delta", development:12.0});
CREATE (KLH:Country {name:"KLH", home_node:"ganges_delta", development:3.0});
CREATE (KJH:Country {name:"KJH", home_node:"ganges_delta", development:4.0});
CREATE (PRD:Country {name:"PRD", home_node:"ganges_delta", development:3.0});
CREATE (JPR:Country {name:"JPR", home_node:"ganges_delta", development:3.0});
CREATE (SRG:Country {name:"SRG", home_node:"doab", development:3.0});
CREATE (KND:Country {name:"KND", home_node:"comorin_cape", development:7.0});
CREATE (DNG:Country {name:"DNG", home_node:"doab", development:5.0});
CREATE (DTI:Country {name:"DTI", home_node:"doab", development:3.0});
CREATE (GRK:Country {name:"GRK", home_node:"lhasa", development:12.95});
CREATE (JML:Country {name:"JML", home_node:"doab", development:5.0});
CREATE (MKP:Country {name:"MKP", home_node:"ganges_delta", development:11.652});
CREATE (SRM:Country {name:"SRM", home_node:"doab", development:3.0});
CREATE (KTU:Country {name:"KTU", home_node:"lhasa", development:8.0});
CREATE (KMN:Country {name:"KMN", home_node:"doab", development:3.0});
CREATE (HSA:Country {name:"HSA", home_node:"lubeck", development:19.0});
CREATE (ABE:Country {name:"ABE", home_node:"chesapeake_bay", development:6.0});
CREATE (APA:Country {name:"APA", home_node:"california", development:6.0});
CREATE (BLA:Country {name:"BLA", home_node:"james_bay", development:6.0});
CREATE (CAD:Country {name:"CAD", home_node:"mississippi_river", development:6.0});
CREATE (CHI:Country {name:"CHI", home_node:"mississippi_river", development:19.0});
CREATE (CHO:Country {name:"CHO", home_node:"mississippi_river", development:13.0});
CREATE (COM:Country {name:"COM", home_node:"mississippi_river", development:6.0});
CREATE (FOX:Country {name:"FOX", home_node:"ohio", development:9.0});
CREATE (LEN:Country {name:"LEN", home_node:"ohio", development:9.0});
CREATE (MAH:Country {name:"MAH", home_node:"st_lawrence", development:10.0});
CREATE (MIK:Country {name:"MIK", home_node:"st_lawrence", development:8.0});
CREATE (MMI:Country {name:"MMI", home_node:"ohio", development:6.0});
CREATE (NAH:Country {name:"NAH", home_node:"rio_grande", development:7.0});
CREATE (OJI:Country {name:"OJI", home_node:"ohio", development:9.0});
CREATE (OSA:Country {name:"OSA", home_node:"mississippi_river", development:6.0});
CREATE (OTT:Country {name:"OTT", home_node:"ohio", development:6.0});
CREATE (PAW:Country {name:"PAW", home_node:"mississippi_river", development:7.0});
CREATE (PIM:Country {name:"PIM", home_node:"california", development:6.0});
CREATE (POT:Country {name:"POT", home_node:"ohio", development:7.0});
CREATE (SHO:Country {name:"SHO", home_node:"california", development:6.0});
CREATE (SIO:Country {name:"SIO", home_node:"ohio", development:6.0});
CREATE (WCR:Country {name:"WCR", home_node:"james_bay", development:6.0});
CREATE (AIR:Country {name:"AIR", home_node:"katsina", development:23.88});
CREATE (BON:Country {name:"BON", home_node:"timbuktu", development:11.95});
CREATE (DAH:Country {name:"DAH", home_node:"timbuktu", development:3.0});
CREATE (DGB:Country {name:"DGB", home_node:"timbuktu", development:5.97});
CREATE (FUL:Country {name:"FUL", home_node:"timbuktu", development:44.702});
CREATE (JNN:Country {name:"JNN", home_node:"timbuktu", development:23.77});
CREATE (KAN:Country {name:"KAN", home_node:"katsina", development:24.52});
CREATE (KBU:Country {name:"KBU", home_node:"ivory_coast", development:10.106});
CREATE (KNG:Country {name:"KNG", home_node:"timbuktu", development:13.616});
CREATE (KTS:Country {name:"KTS", home_node:"katsina", development:21.93});
CREATE (NUP:Country {name:"NUP", home_node:"katsina", development:5.826});
CREATE (TMB:Country {name:"TMB", home_node:"timbuktu", development:45.766});
CREATE (YAO:Country {name:"YAO", home_node:"katsina", development:7.0});
CREATE (YAT:Country {name:"YAT", home_node:"timbuktu", development:16.622});
CREATE (ZZZ:Country {name:"ZZZ", home_node:"katsina", development:11.97});
CREATE (NDO:Country {name:"NDO", home_node:"kongo", development:6.0});
CREATE (JOH:Country {name:"JOH", home_node:"malacca", development:49.756});
CREATE (KED:Country {name:"KED", home_node:"malacca", development:10.0});
CREATE (PRK:Country {name:"PRK", home_node:"malacca", development:19.92});
CREATE (CHU:Country {name:"CHU", home_node:"girin", development:3.0});
CREATE (HOD:Country {name:"HOD", home_node:"girin", development:3.0});
CREATE (CHV:Country {name:"CHV", home_node:"girin", development:3.0});
CREATE (KMC:Country {name:"KMC", home_node:"girin", development:3.0});
CREATE (ARP:Country {name:"ARP", home_node:"james_bay", development:6.0});
CREATE (CNK:Country {name:"CNK", home_node:"california", development:8.0});
CREATE (HDA:Country {name:"HDA", home_node:"california", development:6.0});
CREATE (KIO:Country {name:"KIO", home_node:"james_bay", development:6.0});
CREATE (SAL:Country {name:"SAL", home_node:"california", development:8.0});
CREATE (WIC:Country {name:"WIC", home_node:"rio_grande", development:6.0});
CREATE (BLM:Country {name:"BLM", home_node:"the_moluccas", development:11.0});
CREATE (BTN:Country {name:"BTN", home_node:"the_moluccas", development:8.0});
CREATE (CRB:Country {name:"CRB", home_node:"the_moluccas", development:28.136});
CREATE (PGR:Country {name:"PGR", home_node:"malacca", development:19.812});
CREATE (PLB:Country {name:"PLB", home_node:"malacca", development:19.93});
CREATE (SAK:Country {name:"SAK", home_node:"malacca", development:26.86});
CREATE (KUT:Country {name:"KUT", home_node:"malacca", development:28.232});
CREATE (BNJ:Country {name:"BNJ", home_node:"malacca", development:30.04});
CREATE (LFA:Country {name:"LFA", home_node:"malacca", development:33.82});
CREATE (LUW:Country {name:"LUW", home_node:"the_moluccas", development:17.91});
CREATE (MGD:Country {name:"MGD", home_node:"philippines", development:13.0});
CREATE (TER:Country {name:"TER", home_node:"the_moluccas", development:35.55});
CREATE (TID:Country {name:"TID", home_node:"the_moluccas", development:13.0});
CREATE (GUA:Country {name:"GUA", home_node:"laplata", development:6.0});
CREATE (ZNI:Country {name:"ZNI", home_node:"rio_grande", development:6.0});
CREATE (MSC:Country {name:"MSC", home_node:"rio_grande", development:6.0});
CREATE (LIP:Country {name:"LIP", home_node:"rio_grande", development:6.0});
CREATE (MIS:Country {name:"MIS", home_node:"panama", development:6.0});
CREATE (YAQ:Country {name:"YAQ", home_node:"california", development:6.0});
CREATE (YKT:Country {name:"YKT", home_node:"california", development:6.0});
CREATE (PSS:Country {name:"PSS", home_node:"wien", development:11.0});
CREATE (ROT:Country {name:"ROT", home_node:"rheinland", development:10.0});
CREATE (BYT:Country {name:"BYT", home_node:"rheinland", development:6.0});
CREATE (REG:Country {name:"REG", home_node:"wien", development:13.0});
CREATE (TTL:Country {name:"TTL", home_node:"rheinland", development:13.0});
CREATE (GOS:Country {name:"GOS", home_node:"saxony", development:11.0});
CREATE (TNT:Country {name:"TNT", home_node:"wien", development:8.0});
CREATE (MLH:Country {name:"MLH", home_node:"rheinland", development:8.0});
CREATE (BAM:Country {name:"BAM", home_node:"rheinland", development:6.0});
CREATE (BNE:Country {name:"BNE", home_node:"the_moluccas", development:16.216});
CREATE (BEU:Country {name:"BEU", home_node:"malacca", development:17.713});
CREATE (BRS:Country {name:"BRS", home_node:"malacca", development:16.886});
CREATE (JMB:Country {name:"JMB", home_node:"malacca", development:11.97});
CREATE (IND:Country {name:"IND", home_node:"malacca", development:11.94});
CREATE (TIW:Country {name:"TIW", home_node:"australia", development:8.0});
CREATE (LAR:Country {name:"LAR", home_node:"australia", development:8.0});
CREATE (YOL:Country {name:"YOL", home_node:"australia", development:8.0});
CREATE (YNU:Country {name:"YNU", home_node:"australia", development:6.0});
CREATE (AWN:Country {name:"AWN", home_node:"australia", development:8.0});
CREATE (GMI:Country {name:"GMI", home_node:"australia", development:13.0});
CREATE (MIA:Country {name:"MIA", home_node:"australia", development:8.0});
CREATE (EOR:Country {name:"EOR", home_node:"australia", development:14.0});
CREATE (KAU:Country {name:"KAU", home_node:"australia", development:10.0});
CREATE (PLW:Country {name:"PLW", home_node:"australia", development:10.0});
CREATE (WRU:Country {name:"WRU", home_node:"australia", development:14.0});
CREATE (NOO:Country {name:"NOO", home_node:"australia", development:12.0});
CREATE (MLG:Country {name:"MLG", home_node:"australia", development:8.0});
CREATE (MAA:Country {name:"MAA", home_node:"polynesia_node", development:8.0});
CREATE (TAN:Country {name:"TAN", home_node:"polynesia_node", development:10.0});
CREATE (TAK:Country {name:"TAK", home_node:"polynesia_node", development:7.0});
CREATE (TNK:Country {name:"TNK", home_node:"polynesia_node", development:10.0});
CREATE (TEA:Country {name:"TEA", home_node:"polynesia_node", development:10.0});
CREATE (TTT:Country {name:"TTT", home_node:"polynesia_node", development:10.0});
CREATE (WAI:Country {name:"WAI", home_node:"polynesia_node", development:4.0});
CREATE (HAW:Country {name:"HAW", home_node:"polynesia_node", development:7.0});
CREATE (MAU:Country {name:"MAU", home_node:"polynesia_node", development:5.0});
CREATE (OAH:Country {name:"OAH", home_node:"polynesia_node", development:5.0});
CREATE (KAA:Country {name:"KAA", home_node:"polynesia_node", development:5.0});
CREATE (TOG:Country {name:"TOG", home_node:"polynesia_node", development:8.0});
CREATE (SAM:Country {name:"SAM", home_node:"polynesia_node", development:8.0});
CREATE (VIL:Country {name:"VIL", home_node:"polynesia_node", development:7.0});
CREATE (VNL:Country {name:"VNL", home_node:"polynesia_node", development:5.0});
CREATE (LAI:Country {name:"LAI", home_node:"polynesia_node", development:3.0});
CREATE (ALT:Country {name:"ALT", home_node:"chesapeake_bay", development:6.0});
CREATE (ICH:Country {name:"ICH", home_node:"chesapeake_bay", development:6.0});
CREATE (COF:Country {name:"COF", home_node:"chesapeake_bay", development:8.0});
CREATE (JOA:Country {name:"JOA", home_node:"chesapeake_bay", development:8.0});
CREATE (ABI:Country {name:"ABI", home_node:"mississippi_river", development:6.0});
CREATE (COW:Country {name:"COW", home_node:"mississippi_river", development:9.0});
CREATE (NTZ:Country {name:"NTZ", home_node:"mississippi_river", development:6.0});
CREATE (PCH:Country {name:"PCH", home_node:"mississippi_river", development:6.0});
CREATE (QUI:Country {name:"QUI", home_node:"mississippi_river", development:8.0});
CREATE (CCA:Country {name:"CCA", home_node:"mississippi_river", development:6.0});
CREATE (KSI:Country {name:"KSI", home_node:"chesapeake_bay", development:8.0});
CREATE (OEO:Country {name:"OEO", home_node:"mississippi_river", development:6.0});
CREATE (NTC:Country {name:"NTC", home_node:"mississippi_river", development:8.0});
CREATE (HNI:Country {name:"HNI", home_node:"rio_grande", development:6.0});
CREATE (MOH:Country {name:"MOH", home_node:"ohio", development:17.0});
CREATE (ONE:Country {name:"ONE", home_node:"ohio", development:8.0});
CREATE (ONO:Country {name:"ONO", home_node:"ohio", development:11.0});
CREATE (CAY:Country {name:"CAY", home_node:"ohio", development:8.0});
CREATE (SEN:Country {name:"SEN", home_node:"ohio", development:7.0});
CREATE (WEN:Country {name:"WEN", home_node:"ohio", development:8.0});
CREATE (TSC:Country {name:"TSC", home_node:"chesapeake_bay", development:8.0});
CREATE (KSK:Country {name:"KSK", home_node:"ohio", development:8.0});
CREATE (PEN:Country {name:"PEN", home_node:"st_lawrence", development:8.0});
CREATE (MLS:Country {name:"MLS", home_node:"st_lawrence", development:6.0});
CREATE (NEH:Country {name:"NEH", home_node:"james_bay", development:6.0});
CREATE (NAK:Country {name:"NAK", home_node:"james_bay", development:6.0});
CREATE (HWK:Country {name:"HWK", home_node:"ohio", development:8.0});
CREATE (CLG:Country {name:"CLG", home_node:"ohio", development:6.0});
CREATE (KSP:Country {name:"KSP", home_node:"ohio", development:6.0});
CREATE (MSG:Country {name:"MSG", home_node:"ohio", development:7.0});
CREATE (WCY:Country {name:"WCY", home_node:"mississippi_river", development:9.0});
CREATE (LAK:Country {name:"LAK", home_node:"mississippi_river", development:6.0});
CREATE (INN:Country {name:"INN", home_node:"st_lawrence", development:6.0});
CREATE (AGQ:Country {name:"AGQ", home_node:"st_lawrence", development:9.0});
CREATE (C00:Country {name:"C00", home_node:"james_bay", development:24.0});
CREATE (C01:Country {name:"C01", home_node:"chesapeake_bay", development:145.696});
CREATE (C02:Country {name:"C02", home_node:"st_lawrence", development:141.194});
CREATE (C03:Country {name:"C03", home_node:"mississippi_river", development:46.772});
CREATE (C04:Country {name:"C04", home_node:"carribean_trade", development:69.81});
CREATE (C05:Country {name:"C05", home_node:"brazil", development:240.149});
CREATE (C06:Country {name:"C06", home_node:"chesapeake_bay", development:31.0});
CREATE (C07:Country {name:"C07", home_node:"california", development:14.88});
CREATE (C08:Country {name:"C08", home_node:"mexico", development:508.256});
CREATE (C09:Country {name:"C09", home_node:"carribean_trade", development:94.612});
CREATE (C10:Country {name:"C10", home_node:"lima", development:323.32});
CREATE (C11:Country {name:"C11", home_node:"lima", development:218.523});
CREATE (C12:Country {name:"C12", home_node:"cuiaba", development:133.084});
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (african_great_lakes)-[r:source{flow:0.3173047392258547}]->(zanzibar);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (kongo:Trade_node {name:"kongo"}) CREATE (african_great_lakes)-[r:source{flow:0.3173047392258547}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (kongo)-[r:source{flow:0.28830343324843233}]->(ivory_coast);
MATCH (kongo:Trade_node {name:"kongo"}), (zambezi:Trade_node {name:"zambezi"}) CREATE (kongo)-[r:source{flow:0.28830343324843233}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (zambezi)-[r:source{flow:0.4658366788213039}]->(zanzibar);
MATCH (patagonia:Trade_node {name:"patagonia"}), (laplata:Trade_node {name:"laplata"}) CREATE (patagonia)-[r:source{flow:0.06375}]->(laplata);
MATCH (patagonia:Trade_node {name:"patagonia"}), (cuiaba:Trade_node {name:"cuiaba"}) CREATE (patagonia)-[r:source{flow:0.06375}]->(cuiaba);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (brazil:Trade_node {name:"brazil"}) CREATE (amazonas_node)-[r:source{flow:0.47966666666666674}]->(brazil);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (amazonas_node)-[r:source{flow:0.47966666666666674}]->(carribean_trade);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (cuiaba:Trade_node {name:"cuiaba"}) CREATE (amazonas_node)-[r:source{flow:0.47966666666666674}]->(cuiaba);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (mississippi_river:Trade_node {name:"mississippi_river"}) CREATE (rio_grande)-[r:source{flow:0.3808769713969338}]->(mississippi_river);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (mexico:Trade_node {name:"mexico"}) CREATE (rio_grande)-[r:source{flow:0.3808769713969338}]->(mexico);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (california:Trade_node {name:"california"}) CREATE (rio_grande)-[r:source{flow:0.3808769713969338}]->(california);
MATCH (james_bay:Trade_node {name:"james_bay"}), (st_lawrence:Trade_node {name:"st_lawrence"}) CREATE (james_bay)-[r:source{flow:0.15178343893853236}]->(st_lawrence);
MATCH (james_bay:Trade_node {name:"james_bay"}), (california:Trade_node {name:"california"}) CREATE (james_bay)-[r:source{flow:0.15178343893853236}]->(california);
MATCH (california:Trade_node {name:"california"}), (mexico:Trade_node {name:"mexico"}) CREATE (california)-[r:source{flow:0.14158033761497366}]->(mexico);
MATCH (california:Trade_node {name:"california"}), (mississippi_river:Trade_node {name:"mississippi_river"}) CREATE (california)-[r:source{flow:0.14158033761497366}]->(mississippi_river);
MATCH (california:Trade_node {name:"california"}), (girin:Trade_node {name:"girin"}) CREATE (california)-[r:source{flow:0.14158033761497366}]->(girin);
MATCH (california:Trade_node {name:"california"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (california)-[r:source{flow:0.14158033761497366}]->(polynesia_node);
MATCH (girin:Trade_node {name:"girin"}), (siberia:Trade_node {name:"siberia"}) CREATE (girin)-[r:source{flow:0.9196899603143852}]->(siberia);
MATCH (girin:Trade_node {name:"girin"}), (beijing:Trade_node {name:"beijing"}) CREATE (girin)-[r:source{flow:0.9196899603143852}]->(beijing);
MATCH (girin:Trade_node {name:"girin"}), (nippon:Trade_node {name:"nippon"}) CREATE (girin)-[r:source{flow:0.9196899603143852}]->(nippon);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (mississippi_river)-[r:source{flow:0.5431118086903396}]->(carribean_trade);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (ohio:Trade_node {name:"ohio"}) CREATE (mississippi_river)-[r:source{flow:0.5431118086903396}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (chesapeake_bay:Trade_node {name:"chesapeake_bay"}) CREATE (ohio)-[r:source{flow:1.2968362504288862}]->(chesapeake_bay);
MATCH (ohio:Trade_node {name:"ohio"}), (st_lawrence:Trade_node {name:"st_lawrence"}) CREATE (ohio)-[r:source{flow:1.2968362504288862}]->(st_lawrence);
MATCH (mexico:Trade_node {name:"mexico"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (mexico)-[r:source{flow:0.6874267016673011}]->(carribean_trade);
MATCH (mexico:Trade_node {name:"mexico"}), (panama:Trade_node {name:"panama"}) CREATE (mexico)-[r:source{flow:0.6874267016673011}]->(panama);
MATCH (mexico:Trade_node {name:"mexico"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (mexico)-[r:source{flow:0.6874267016673011}]->(polynesia_node);
MATCH (lhasa:Trade_node {name:"lhasa"}), (lahore:Trade_node {name:"lahore"}) CREATE (lhasa)-[r:source{flow:0.16469335653364517}]->(lahore);
MATCH (lhasa:Trade_node {name:"lhasa"}), (chengdu:Trade_node {name:"chengdu"}) CREATE (lhasa)-[r:source{flow:0.16469335653364517}]->(chengdu);
MATCH (lhasa:Trade_node {name:"lhasa"}), (ganges_delta:Trade_node {name:"ganges_delta"}) CREATE (lhasa)-[r:source{flow:0.16469335653364517}]->(ganges_delta);
MATCH (chengdu:Trade_node {name:"chengdu"}), (canton:Trade_node {name:"canton"}) CREATE (chengdu)-[r:source{flow:0.4590764032198463}]->(canton);
MATCH (chengdu:Trade_node {name:"chengdu"}), (xian:Trade_node {name:"xian"}) CREATE (chengdu)-[r:source{flow:0.4590764032198463}]->(xian);
MATCH (chengdu:Trade_node {name:"chengdu"}), (burma:Trade_node {name:"burma"}) CREATE (chengdu)-[r:source{flow:0.4590764032198463}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (ganges_delta:Trade_node {name:"ganges_delta"}) CREATE (burma)-[r:source{flow:0.7824225885069016}]->(ganges_delta);
MATCH (burma:Trade_node {name:"burma"}), (gulf_of_siam:Trade_node {name:"gulf_of_siam"}) CREATE (burma)-[r:source{flow:0.7824225885069016}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (malacca:Trade_node {name:"malacca"}) CREATE (gulf_of_siam)-[r:source{flow:1.0542867923833483}]->(malacca);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (canton:Trade_node {name:"canton"}) CREATE (gulf_of_siam)-[r:source{flow:1.0542867923833483}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (malacca:Trade_node {name:"malacca"}) CREATE (canton)-[r:source{flow:0.9496841700475548}]->(malacca);
MATCH (canton:Trade_node {name:"canton"}), (hangzhou:Trade_node {name:"hangzhou"}) CREATE (canton)-[r:source{flow:0.9496841700475548}]->(hangzhou);
MATCH (canton:Trade_node {name:"canton"}), (philippines:Trade_node {name:"philippines"}) CREATE (canton)-[r:source{flow:0.9496841700475548}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (the_moluccas:Trade_node {name:"the_moluccas"}) CREATE (philippines)-[r:source{flow:0.7695486458560645}]->(the_moluccas);
MATCH (philippines:Trade_node {name:"philippines"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (philippines)-[r:source{flow:0.7695486458560645}]->(polynesia_node);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (laplata:Trade_node {name:"laplata"}) CREATE (cuiaba)-[r:source{flow:0.4371248384523126}]->(laplata);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (lima:Trade_node {name:"lima"}) CREATE (cuiaba)-[r:source{flow:0.4371248384523126}]->(lima);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (brazil:Trade_node {name:"brazil"}) CREATE (cuiaba)-[r:source{flow:0.4371248384523126}]->(brazil);
MATCH (lima:Trade_node {name:"lima"}), (panama:Trade_node {name:"panama"}) CREATE (lima)-[r:source{flow:0.15020240003675292}]->(panama);
MATCH (lima:Trade_node {name:"lima"}), (polynesia_node:Trade_node {name:"polynesia_node"}) CREATE (lima)-[r:source{flow:0.15020240003675292}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (nippon:Trade_node {name:"nippon"}) CREATE (polynesia_node)-[r:source{flow:0.07686996923278276}]->(nippon);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (australia:Trade_node {name:"australia"}) CREATE (polynesia_node)-[r:source{flow:0.07686996923278276}]->(australia);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (panama:Trade_node {name:"panama"}) CREATE (polynesia_node)-[r:source{flow:0.07686996923278276}]->(panama);
MATCH (australia:Trade_node {name:"australia"}), (the_moluccas:Trade_node {name:"the_moluccas"}) CREATE (australia)-[r:source{flow:0.0}]->(the_moluccas);
MATCH (nippon:Trade_node {name:"nippon"}), (hangzhou:Trade_node {name:"hangzhou"}) CREATE (nippon)-[r:source{flow:0.32440887090775594}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (xian:Trade_node {name:"xian"}) CREATE (hangzhou)-[r:source{flow:2.0544662821671786}]->(xian);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (beijing:Trade_node {name:"beijing"}) CREATE (hangzhou)-[r:source{flow:2.0544662821671786}]->(beijing);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (malacca:Trade_node {name:"malacca"}) CREATE (hangzhou)-[r:source{flow:2.0544662821671786}]->(malacca);
MATCH (xian:Trade_node {name:"xian"}), (beijing:Trade_node {name:"beijing"}) CREATE (xian)-[r:source{flow:1.929554954914094}]->(beijing);
MATCH (xian:Trade_node {name:"xian"}), (yumen:Trade_node {name:"yumen"}) CREATE (xian)-[r:source{flow:1.929554954914094}]->(yumen);
MATCH (beijing:Trade_node {name:"beijing"}), (yumen:Trade_node {name:"yumen"}) CREATE (beijing)-[r:source{flow:0.25231089878381063}]->(yumen);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (malacca:Trade_node {name:"malacca"}) CREATE (the_moluccas)-[r:source{flow:2.0078744374190753}]->(malacca);
MATCH (siberia:Trade_node {name:"siberia"}), (kazan:Trade_node {name:"kazan"}) CREATE (siberia)-[r:source{flow:1.335668614582526}]->(kazan);
MATCH (siberia:Trade_node {name:"siberia"}), (samarkand:Trade_node {name:"samarkand"}) CREATE (siberia)-[r:source{flow:1.335668614582526}]->(samarkand);
MATCH (yumen:Trade_node {name:"yumen"}), (samarkand:Trade_node {name:"samarkand"}) CREATE (yumen)-[r:source{flow:1.6561116262935847}]->(samarkand);
MATCH (malacca:Trade_node {name:"malacca"}), (ganges_delta:Trade_node {name:"ganges_delta"}) CREATE (malacca)-[r:source{flow:0.5677001865473663}]->(ganges_delta);
MATCH (malacca:Trade_node {name:"malacca"}), (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}) CREATE (malacca)-[r:source{flow:0.5677001865473663}]->(cape_of_good_hope);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (comorin_cape:Trade_node {name:"comorin_cape"}) CREATE (ganges_delta)-[r:source{flow:2.3140434441165216}]->(comorin_cape);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (doab:Trade_node {name:"doab"}) CREATE (ganges_delta)-[r:source{flow:2.3140434441165216}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (deccan:Trade_node {name:"deccan"}) CREATE (doab)-[r:source{flow:2.7769482460442747}]->(deccan);
MATCH (doab:Trade_node {name:"doab"}), (lahore:Trade_node {name:"lahore"}) CREATE (doab)-[r:source{flow:2.7769482460442747}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (samarkand:Trade_node {name:"samarkand"}) CREATE (lahore)-[r:source{flow:1.4735599364674934}]->(samarkand);
MATCH (lahore:Trade_node {name:"lahore"}), (persia:Trade_node {name:"persia"}) CREATE (lahore)-[r:source{flow:1.4735599364674934}]->(persia);
MATCH (lahore:Trade_node {name:"lahore"}), (gujarat:Trade_node {name:"gujarat"}) CREATE (lahore)-[r:source{flow:1.4735599364674934}]->(gujarat);
MATCH (deccan:Trade_node {name:"deccan"}), (gujarat:Trade_node {name:"gujarat"}) CREATE (deccan)-[r:source{flow:1.0619999592214986}]->(gujarat);
MATCH (deccan:Trade_node {name:"deccan"}), (comorin_cape:Trade_node {name:"comorin_cape"}) CREATE (deccan)-[r:source{flow:1.0619999592214986}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (gulf_of_aden:Trade_node {name:"gulf_of_aden"}) CREATE (comorin_cape)-[r:source{flow:1.802807711015351}]->(gulf_of_aden);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (gujarat:Trade_node {name:"gujarat"}) CREATE (comorin_cape)-[r:source{flow:1.802807711015351}]->(gujarat);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}) CREATE (comorin_cape)-[r:source{flow:1.802807711015351}]->(cape_of_good_hope);
MATCH (gujarat:Trade_node {name:"gujarat"}), (gulf_of_aden:Trade_node {name:"gulf_of_aden"}) CREATE (gujarat)-[r:source{flow:1.5478626506997346}]->(gulf_of_aden);
MATCH (gujarat:Trade_node {name:"gujarat"}), (hormuz:Trade_node {name:"hormuz"}) CREATE (gujarat)-[r:source{flow:1.5478626506997346}]->(hormuz);
MATCH (gujarat:Trade_node {name:"gujarat"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (gujarat)-[r:source{flow:1.5478626506997346}]->(zanzibar);
MATCH (katsina:Trade_node {name:"katsina"}), (timbuktu:Trade_node {name:"timbuktu"}) CREATE (katsina)-[r:source{flow:0.43371091401695194}]->(timbuktu);
MATCH (katsina:Trade_node {name:"katsina"}), (tunis:Trade_node {name:"tunis"}) CREATE (katsina)-[r:source{flow:0.43371091401695194}]->(tunis);
MATCH (katsina:Trade_node {name:"katsina"}), (ethiopia:Trade_node {name:"ethiopia"}) CREATE (katsina)-[r:source{flow:0.43371091401695194}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (alexandria:Trade_node {name:"alexandria"}) CREATE (ethiopia)-[r:source{flow:0.352662528744935}]->(alexandria);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (gulf_of_aden:Trade_node {name:"gulf_of_aden"}) CREATE (ethiopia)-[r:source{flow:0.352662528744935}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (zanzibar:Trade_node {name:"zanzibar"}) CREATE (gulf_of_aden)-[r:source{flow:0.3675159759171677}]->(zanzibar);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (alexandria:Trade_node {name:"alexandria"}) CREATE (gulf_of_aden)-[r:source{flow:0.3675159759171677}]->(alexandria);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (hormuz:Trade_node {name:"hormuz"}) CREATE (gulf_of_aden)-[r:source{flow:0.3675159759171677}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (basra:Trade_node {name:"basra"}) CREATE (hormuz)-[r:source{flow:1.6381502857953352}]->(basra);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}) CREATE (zanzibar)-[r:source{flow:0.6173578395719997}]->(cape_of_good_hope);
MATCH (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (cape_of_good_hope)-[r:source{flow:1.768629511995727}]->(ivory_coast);
MATCH (basra:Trade_node {name:"basra"}), (aleppo:Trade_node {name:"aleppo"}) CREATE (basra)-[r:source{flow:1.0388733656488331}]->(aleppo);
MATCH (basra:Trade_node {name:"basra"}), (persia:Trade_node {name:"persia"}) CREATE (basra)-[r:source{flow:1.0388733656488331}]->(persia);
MATCH (samarkand:Trade_node {name:"samarkand"}), (persia:Trade_node {name:"persia"}) CREATE (samarkand)-[r:source{flow:0.7699841722939604}]->(persia);
MATCH (samarkand:Trade_node {name:"samarkand"}), (astrakhan:Trade_node {name:"astrakhan"}) CREATE (samarkand)-[r:source{flow:0.7699841722939604}]->(astrakhan);
MATCH (persia:Trade_node {name:"persia"}), (aleppo:Trade_node {name:"aleppo"}) CREATE (persia)-[r:source{flow:0.8152613150719561}]->(aleppo);
MATCH (persia:Trade_node {name:"persia"}), (astrakhan:Trade_node {name:"astrakhan"}) CREATE (persia)-[r:source{flow:0.8152613150719561}]->(astrakhan);
MATCH (aleppo:Trade_node {name:"aleppo"}), (constantinople:Trade_node {name:"constantinople"}) CREATE (aleppo)-[r:source{flow:1.9819603536892072}]->(constantinople);
MATCH (aleppo:Trade_node {name:"aleppo"}), (alexandria:Trade_node {name:"alexandria"}) CREATE (aleppo)-[r:source{flow:1.9819603536892072}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (constantinople:Trade_node {name:"constantinople"}) CREATE (alexandria)-[r:source{flow:1.476137309835502}]->(constantinople);
MATCH (alexandria:Trade_node {name:"alexandria"}), (venice:Trade_node {name:"venice"}) CREATE (alexandria)-[r:source{flow:1.476137309835502}]->(venice);
MATCH (alexandria:Trade_node {name:"alexandria"}), (genua:Trade_node {name:"genua"}) CREATE (alexandria)-[r:source{flow:1.476137309835502}]->(genua);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (kazan:Trade_node {name:"kazan"}) CREATE (astrakhan)-[r:source{flow:0.9491301981433037}]->(kazan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (crimea:Trade_node {name:"crimea"}) CREATE (astrakhan)-[r:source{flow:0.9491301981433037}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (kiev:Trade_node {name:"kiev"}) CREATE (crimea)-[r:source{flow:0.7239945063881849}]->(kiev);
MATCH (crimea:Trade_node {name:"crimea"}), (constantinople:Trade_node {name:"constantinople"}) CREATE (crimea)-[r:source{flow:0.7239945063881849}]->(constantinople);
MATCH (crimea:Trade_node {name:"crimea"}), (pest:Trade_node {name:"pest"}) CREATE (crimea)-[r:source{flow:0.7239945063881849}]->(pest);
MATCH (constantinople:Trade_node {name:"constantinople"}), (ragusa:Trade_node {name:"ragusa"}) CREATE (constantinople)-[r:source{flow:1.067126303872239}]->(ragusa);
MATCH (kiev:Trade_node {name:"kiev"}), (novgorod:Trade_node {name:"novgorod"}) CREATE (kiev)-[r:source{flow:1.5482985579268989}]->(novgorod);
MATCH (kiev:Trade_node {name:"kiev"}), (krakow:Trade_node {name:"krakow"}) CREATE (kiev)-[r:source{flow:1.5482985579268989}]->(krakow);
MATCH (kazan:Trade_node {name:"kazan"}), (novgorod:Trade_node {name:"novgorod"}) CREATE (kazan)-[r:source{flow:2.8015193766810604}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (baltic_sea:Trade_node {name:"baltic_sea"}) CREATE (novgorod)-[r:source{flow:0.9985095285417074}]->(baltic_sea);
MATCH (novgorod:Trade_node {name:"novgorod"}), (white_sea:Trade_node {name:"white_sea"}) CREATE (novgorod)-[r:source{flow:0.9985095285417074}]->(white_sea);
MATCH (laplata:Trade_node {name:"laplata"}), (brazil:Trade_node {name:"brazil"}) CREATE (laplata)-[r:source{flow:1.0560477353957618}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (brazil)-[r:source{flow:0.4114354318791457}]->(ivory_coast);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (safi:Trade_node {name:"safi"}) CREATE (timbuktu)-[r:source{flow:0.5071233086079054}]->(safi);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (ivory_coast:Trade_node {name:"ivory_coast"}) CREATE (timbuktu)-[r:source{flow:0.5071233086079054}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (ivory_coast)-[r:source{flow:0.496638088734446}]->(carribean_trade);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (bordeaux:Trade_node {name:"bordeaux"}) CREATE (ivory_coast)-[r:source{flow:0.496638088734446}]->(bordeaux);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (ivory_coast)-[r:source{flow:0.496638088734446}]->(english_channel);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (ivory_coast)-[r:source{flow:0.496638088734446}]->(sevilla);
MATCH (tunis:Trade_node {name:"tunis"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (tunis)-[r:source{flow:0.2946800443010032}]->(sevilla);
MATCH (tunis:Trade_node {name:"tunis"}), (valencia:Trade_node {name:"valencia"}) CREATE (tunis)-[r:source{flow:0.2946800443010032}]->(valencia);
MATCH (tunis:Trade_node {name:"tunis"}), (genua:Trade_node {name:"genua"}) CREATE (tunis)-[r:source{flow:0.2946800443010032}]->(genua);
MATCH (ragusa:Trade_node {name:"ragusa"}), (pest:Trade_node {name:"pest"}) CREATE (ragusa)-[r:source{flow:0.9675763577300748}]->(pest);
MATCH (ragusa:Trade_node {name:"ragusa"}), (venice:Trade_node {name:"venice"}) CREATE (ragusa)-[r:source{flow:0.9675763577300748}]->(venice);
MATCH (ragusa:Trade_node {name:"ragusa"}), (genua:Trade_node {name:"genua"}) CREATE (ragusa)-[r:source{flow:0.9675763577300748}]->(genua);
MATCH (safi:Trade_node {name:"safi"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (safi)-[r:source{flow:0.7274269455775877}]->(sevilla);
MATCH (pest:Trade_node {name:"pest"}), (wien:Trade_node {name:"wien"}) CREATE (pest)-[r:source{flow:1.3854725942428698}]->(wien);
MATCH (pest:Trade_node {name:"pest"}), (krakow:Trade_node {name:"krakow"}) CREATE (pest)-[r:source{flow:1.3854725942428698}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (wien:Trade_node {name:"wien"}) CREATE (krakow)-[r:source{flow:1.2941690885425186}]->(wien);
MATCH (krakow:Trade_node {name:"krakow"}), (saxony:Trade_node {name:"saxony"}) CREATE (krakow)-[r:source{flow:1.2941690885425186}]->(saxony);
MATCH (krakow:Trade_node {name:"krakow"}), (baltic_sea:Trade_node {name:"baltic_sea"}) CREATE (krakow)-[r:source{flow:1.2941690885425186}]->(baltic_sea);
MATCH (wien:Trade_node {name:"wien"}), (venice:Trade_node {name:"venice"}) CREATE (wien)-[r:source{flow:1.207022842619369}]->(venice);
MATCH (wien:Trade_node {name:"wien"}), (rheinland:Trade_node {name:"rheinland"}) CREATE (wien)-[r:source{flow:1.207022842619369}]->(rheinland);
MATCH (wien:Trade_node {name:"wien"}), (saxony:Trade_node {name:"saxony"}) CREATE (wien)-[r:source{flow:1.207022842619369}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (rheinland:Trade_node {name:"rheinland"}) CREATE (saxony)-[r:source{flow:1.684048108861988}]->(rheinland);
MATCH (saxony:Trade_node {name:"saxony"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (saxony)-[r:source{flow:1.684048108861988}]->(lubeck);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (baltic_sea)-[r:source{flow:2.75694537984006}]->(lubeck);
MATCH (rheinland:Trade_node {name:"rheinland"}), (champagne:Trade_node {name:"champagne"}) CREATE (rheinland)-[r:source{flow:1.4929412213081321}]->(champagne);
MATCH (rheinland:Trade_node {name:"rheinland"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (rheinland)-[r:source{flow:1.4929412213081321}]->(lubeck);
MATCH (panama:Trade_node {name:"panama"}), (carribean_trade:Trade_node {name:"carribean_trade"}) CREATE (panama)-[r:source{flow:2.6803416559197224}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (chesapeake_bay:Trade_node {name:"chesapeake_bay"}) CREATE (carribean_trade)-[r:source{flow:1.4930118810650066}]->(chesapeake_bay);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (bordeaux:Trade_node {name:"bordeaux"}) CREATE (carribean_trade)-[r:source{flow:1.4930118810650066}]->(bordeaux);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (sevilla:Trade_node {name:"sevilla"}) CREATE (carribean_trade)-[r:source{flow:1.4930118810650066}]->(sevilla);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (st_lawrence:Trade_node {name:"st_lawrence"}) CREATE (chesapeake_bay)-[r:source{flow:0.8930076803068981}]->(st_lawrence);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (chesapeake_bay)-[r:source{flow:0.8930076803068981}]->(english_channel);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (north_sea:Trade_node {name:"north_sea"}) CREATE (st_lawrence)-[r:source{flow:0.49170058629129615}]->(north_sea);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (bordeaux:Trade_node {name:"bordeaux"}) CREATE (st_lawrence)-[r:source{flow:0.49170058629129615}]->(bordeaux);
MATCH (white_sea:Trade_node {name:"white_sea"}), (north_sea:Trade_node {name:"north_sea"}) CREATE (white_sea)-[r:source{flow:1.4742175024843962}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (north_sea)-[r:source{flow:1.4625139788413222}]->(english_channel);
MATCH (north_sea:Trade_node {name:"north_sea"}), (lubeck:Trade_node {name:"lubeck"}) CREATE (north_sea)-[r:source{flow:1.4625139788413222}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (lubeck)-[r:source{flow:1.284197681921703}]->(english_channel);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (champagne:Trade_node {name:"champagne"}) CREATE (bordeaux)-[r:source{flow:4.719709041947642}]->(champagne);
MATCH (sevilla:Trade_node {name:"sevilla"}), (valencia:Trade_node {name:"valencia"}) CREATE (sevilla)-[r:source{flow:0.3544690921689127}]->(valencia);
MATCH (champagne:Trade_node {name:"champagne"}), (genua:Trade_node {name:"genua"}) CREATE (champagne)-[r:source{flow:2.6509035997398662}]->(genua);
MATCH (champagne:Trade_node {name:"champagne"}), (english_channel:Trade_node {name:"english_channel"}) CREATE (champagne)-[r:source{flow:2.6509035997398662}]->(english_channel);
MATCH (valencia:Trade_node {name:"valencia"}), (genua:Trade_node {name:"genua"}) CREATE (valencia)-[r:source{flow:2.8268032966467063}]->(genua);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.992,calculated_trading_power: 8.992}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (OMA:Country {name:"OMA"}) CREATE (OMA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.166,calculated_trading_power: 4.166}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KON:Country {name:"KON"}) CREATE (KON)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 45.347,calculated_trading_power: 45.347}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 47.067,calculated_trading_power: 47.067}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.583,calculated_trading_power: 2.583}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (RZI:Country {name:"RZI"}) CREATE (RZI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 39.19,calculated_trading_power: 39.19}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KSJ:Country {name:"KSJ"}) CREATE (KSJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.045,calculated_trading_power: 5.045}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (LUB:Country {name:"LUB"}) CREATE (LUB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.769,calculated_trading_power: 14.769}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (LND:Country {name:"LND"}) CREATE (LND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 54.517,calculated_trading_power: 54.517}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KZB:Country {name:"KZB"}) CREATE (KZB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.081,calculated_trading_power: 9.081}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (YAK:Country {name:"YAK"}) CREATE (YAK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KUB:Country {name:"KUB"}) CREATE (KUB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.38,calculated_trading_power: 5.38}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (RWA:Country {name:"RWA"}) CREATE (RWA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.475,calculated_trading_power: 17.475}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BUU:Country {name:"BUU"}) CREATE (BUU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.269,calculated_trading_power: 12.269}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BUG:Country {name:"BUG"}) CREATE (BUG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.112,calculated_trading_power: 26.112}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (NKO:Country {name:"NKO"}) CREATE (NKO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.69,calculated_trading_power: 11.69}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (KRW:Country {name:"KRW"}) CREATE (KRW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.124,calculated_trading_power: 15.124}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BNY:Country {name:"BNY"}) CREATE (BNY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.194,calculated_trading_power: 15.194}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BSG:Country {name:"BSG"}) CREATE (BSG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.391,calculated_trading_power: 12.391}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (UBH:Country {name:"UBH"}) CREATE (UBH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.632,calculated_trading_power: 18.632}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.926,calculated_trading_power: 2.926}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.409,calculated_trading_power: 2.409}]->(african_great_lakes);
MATCH (african_great_lakes:Trade_node {name:"african_great_lakes"}), (NDO:Country {name:"NDO"}) CREATE (NDO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(african_great_lakes);
MATCH (kongo:Trade_node {name:"kongo"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.289,calculated_trading_power: 10.289}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.057,calculated_trading_power: 6.057}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KON:Country {name:"KON"}) CREATE (KON)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 71.986,calculated_trading_power: 71.986}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (ZIM:Country {name:"ZIM"}) CREATE (ZIM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.295,calculated_trading_power: 5.295}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (LOA:Country {name:"LOA"}) CREATE (LOA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.586,calculated_trading_power: 9.586}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.341,calculated_trading_power: 4.341}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (RZI:Country {name:"RZI"}) CREATE (RZI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 42.7,calculated_trading_power: 42.7}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (SYO:Country {name:"SYO"}) CREATE (SYO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.949,calculated_trading_power: 11.949}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KSJ:Country {name:"KSJ"}) CREATE (KSJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.485,calculated_trading_power: 13.485}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (LUB:Country {name:"LUB"}) CREATE (LUB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 33.48,calculated_trading_power: 33.48}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (LND:Country {name:"LND"}) CREATE (LND)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 86.136,calculated_trading_power: 86.136}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KZB:Country {name:"KZB"}) CREATE (KZB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.115,calculated_trading_power: 21.115}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (YAK:Country {name:"YAK"}) CREATE (YAK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.848,calculated_trading_power: 12.848}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KUB:Country {name:"KUB"}) CREATE (KUB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.774,calculated_trading_power: 20.774}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (MRA:Country {name:"MRA"}) CREATE (MRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.772,calculated_trading_power: 14.772}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (LDU:Country {name:"LDU"}) CREATE (LDU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.012,calculated_trading_power: 6.012}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (TBK:Country {name:"TBK"}) CREATE (TBK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.456,calculated_trading_power: 6.456}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (MKU:Country {name:"MKU"}) CREATE (MKU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.98,calculated_trading_power: 3.98}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (KBU:Country {name:"KBU"}) CREATE (KBU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.384,calculated_trading_power: 2.384}]->(kongo);
MATCH (kongo:Trade_node {name:"kongo"}), (NDO:Country {name:"NDO"}) CREATE (NDO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.711,calculated_trading_power: 11.711}]->(kongo);
MATCH (zambezi:Trade_node {name:"zambezi"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 29.261,calculated_trading_power: 29.261}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (OMA:Country {name:"OMA"}) CREATE (OMA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.166,calculated_trading_power: 4.166}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 47.067,calculated_trading_power: 47.067}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (ZIM:Country {name:"ZIM"}) CREATE (ZIM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.463,calculated_trading_power: 16.463}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.583,calculated_trading_power: 2.583}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (RZI:Country {name:"RZI"}) CREATE (RZI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 73.176,calculated_trading_power: 73.176}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (MRA:Country {name:"MRA"}) CREATE (MRA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 37.05,calculated_trading_power: 37.05}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (LDU:Country {name:"LDU"}) CREATE (LDU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.72,calculated_trading_power: 18.72}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (TBK:Country {name:"TBK"}) CREATE (TBK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.569,calculated_trading_power: 15.569}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (MKU:Country {name:"MKU"}) CREATE (MKU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.396,calculated_trading_power: 13.396}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (MIR:Country {name:"MIR"}) CREATE (MIR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.301,calculated_trading_power: 6.301}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.926,calculated_trading_power: 2.926}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.409,calculated_trading_power: 2.409}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (MFY:Country {name:"MFY"}) CREATE (MFY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.045,calculated_trading_power: 5.045}]->(zambezi);
MATCH (zambezi:Trade_node {name:"zambezi"}), (ANT:Country {name:"ANT"}) CREATE (ANT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.283,calculated_trading_power: 6.283}]->(zambezi);
MATCH (patagonia:Trade_node {name:"patagonia"}), (C05:Country {name:"C05"}) CREATE (C05)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.681,calculated_trading_power: 23.681}]->(patagonia);
MATCH (patagonia:Trade_node {name:"patagonia"}), (C11:Country {name:"C11"}) CREATE (C11)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.239,calculated_trading_power: 14.239}]->(patagonia);
MATCH (patagonia:Trade_node {name:"patagonia"}), (C12:Country {name:"C12"}) CREATE (C12)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 74.524,calculated_trading_power: 74.524}]->(patagonia);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.288,calculated_trading_power: 5.288}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.126,calculated_trading_power: 1.126}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.148,calculated_trading_power: 6.148}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.005,calculated_trading_power: 34.005}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (C05:Country {name:"C05"}) CREATE (C05)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 94.99,calculated_trading_power: 94.99}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (C09:Country {name:"C09"}) CREATE (C09)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 43.45,calculated_trading_power: 43.45}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (C10:Country {name:"C10"}) CREATE (C10)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 38.61,calculated_trading_power: 38.61}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (C11:Country {name:"C11"}) CREATE (C11)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.239,calculated_trading_power: 14.239}]->(amazonas_node);
MATCH (amazonas_node:Trade_node {name:"amazonas_node"}), (C12:Country {name:"C12"}) CREATE (C12)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.455,calculated_trading_power: 34.455}]->(amazonas_node);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (APA:Country {name:"APA"}) CREATE (APA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (CAD:Country {name:"CAD"}) CREATE (CAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (CHI:Country {name:"CHI"}) CREATE (CHI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (CHO:Country {name:"CHO"}) CREATE (CHO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (NAH:Country {name:"NAH"}) CREATE (NAH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.758,calculated_trading_power: 8.758}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (OSA:Country {name:"OSA"}) CREATE (OSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.99,calculated_trading_power: 1.99}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (CNK:Country {name:"CNK"}) CREATE (CNK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.628,calculated_trading_power: 3.628}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (WIC:Country {name:"WIC"}) CREATE (WIC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (ZNI:Country {name:"ZNI"}) CREATE (ZNI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (MSC:Country {name:"MSC"}) CREATE (MSC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (LIP:Country {name:"LIP"}) CREATE (LIP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (YAQ:Country {name:"YAQ"}) CREATE (YAQ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.014,calculated_trading_power: 2.014}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (ABI:Country {name:"ABI"}) CREATE (ABI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (COW:Country {name:"COW"}) CREATE (COW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (NTZ:Country {name:"NTZ"}) CREATE (NTZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.048,calculated_trading_power: 2.048}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (PCH:Country {name:"PCH"}) CREATE (PCH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (QUI:Country {name:"QUI"}) CREATE (QUI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (NTC:Country {name:"NTC"}) CREATE (NTC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.032,calculated_trading_power: 2.032}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (HNI:Country {name:"HNI"}) CREATE (HNI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (C03:Country {name:"C03"}) CREATE (C03)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 33.414,calculated_trading_power: 33.414}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (C07:Country {name:"C07"}) CREATE (C07)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.386,calculated_trading_power: 2.386}]->(rio_grande);
MATCH (rio_grande:Trade_node {name:"rio_grande"}), (C08:Country {name:"C08"}) CREATE (C08)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 130.645,calculated_trading_power: 130.645}]->(rio_grande);
MATCH (james_bay:Trade_node {name:"james_bay"}), (BLA:Country {name:"BLA"}) CREATE (BLA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.619,calculated_trading_power: 8.619}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (WCR:Country {name:"WCR"}) CREATE (WCR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.722,calculated_trading_power: 8.722}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (ARP:Country {name:"ARP"}) CREATE (ARP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (CNK:Country {name:"CNK"}) CREATE (CNK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.628,calculated_trading_power: 3.628}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (KIO:Country {name:"KIO"}) CREATE (KIO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (NEH:Country {name:"NEH"}) CREATE (NEH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.499,calculated_trading_power: 8.499}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (NAK:Country {name:"NAK"}) CREATE (NAK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (INN:Country {name:"INN"}) CREATE (INN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (C00:Country {name:"C00"}) CREATE (C00)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 75.887,calculated_trading_power: 75.887}]->(james_bay);
MATCH (james_bay:Trade_node {name:"james_bay"}), (C02:Country {name:"C02"}) CREATE (C02)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 51.61,calculated_trading_power: 51.61}]->(james_bay);
MATCH (california:Trade_node {name:"california"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.611,calculated_trading_power: 5.611}]->(california);
MATCH (california:Trade_node {name:"california"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 27.045,calculated_trading_power: 27.045}]->(california);
MATCH (california:Trade_node {name:"california"}), (APA:Country {name:"APA"}) CREATE (APA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.359,calculated_trading_power: 9.359}]->(california);
MATCH (california:Trade_node {name:"california"}), (PIM:Country {name:"PIM"}) CREATE (PIM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.515,calculated_trading_power: 8.515}]->(california);
MATCH (california:Trade_node {name:"california"}), (SHO:Country {name:"SHO"}) CREATE (SHO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(california);
MATCH (california:Trade_node {name:"california"}), (CNK:Country {name:"CNK"}) CREATE (CNK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 41.348,calculated_trading_power: 41.348}]->(california);
MATCH (california:Trade_node {name:"california"}), (HDA:Country {name:"HDA"}) CREATE (HDA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.913,calculated_trading_power: 24.913}]->(california);
MATCH (california:Trade_node {name:"california"}), (SAL:Country {name:"SAL"}) CREATE (SAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.709,calculated_trading_power: 25.709}]->(california);
MATCH (california:Trade_node {name:"california"}), (YAQ:Country {name:"YAQ"}) CREATE (YAQ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.387,calculated_trading_power: 27.387}]->(california);
MATCH (california:Trade_node {name:"california"}), (YKT:Country {name:"YKT"}) CREATE (YKT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.499,calculated_trading_power: 8.499}]->(california);
MATCH (california:Trade_node {name:"california"}), (C03:Country {name:"C03"}) CREATE (C03)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.716,calculated_trading_power: 9.716}]->(california);
MATCH (california:Trade_node {name:"california"}), (C07:Country {name:"C07"}) CREATE (C07)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.809,calculated_trading_power: 16.809}]->(california);
MATCH (california:Trade_node {name:"california"}), (C08:Country {name:"C08"}) CREATE (C08)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 95.069,calculated_trading_power: 95.069}]->(california);
MATCH (girin:Trade_node {name:"girin"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 47.903,calculated_trading_power: 47.903}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (JAP:Country {name:"JAP"}) CREATE (JAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 42.981,calculated_trading_power: 42.981}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (HSK:Country {name:"HSK"}) CREATE (HSK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.194,calculated_trading_power: 2.194}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (IKE:Country {name:"IKE"}) CREATE (IKE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.994,calculated_trading_power: 1.994}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (MAE:Country {name:"MAE"}) CREATE (MAE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.99,calculated_trading_power: 1.99}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (MRI:Country {name:"MRI"}) CREATE (MRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.687,calculated_trading_power: 4.687}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (SMZ:Country {name:"SMZ"}) CREATE (SMZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.99,calculated_trading_power: 1.99}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (UES:Country {name:"UES"}) CREATE (UES)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.99,calculated_trading_power: 1.99}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (ITO:Country {name:"ITO"}) CREATE (ITO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (SHN:Country {name:"SHN"}) CREATE (SHN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.994,calculated_trading_power: 1.994}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (STK:Country {name:"STK"}) CREATE (STK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.99,calculated_trading_power: 1.99}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (KOR:Country {name:"KOR"}) CREATE (KOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 37.07,calculated_trading_power: 37.07}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 235.208,calculated_trading_power: 235.208}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (RYU:Country {name:"RYU"}) CREATE (RYU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.014,calculated_trading_power: 2.014}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (CHU:Country {name:"CHU"}) CREATE (CHU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.381,calculated_trading_power: 15.381}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (HOD:Country {name:"HOD"}) CREATE (HOD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.628,calculated_trading_power: 8.628}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (CHV:Country {name:"CHV"}) CREATE (CHV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.016,calculated_trading_power: 32.016}]->(girin);
MATCH (girin:Trade_node {name:"girin"}), (KMC:Country {name:"KMC"}) CREATE (KMC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.016,calculated_trading_power: 32.016}]->(girin);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.288,calculated_trading_power: 5.288}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.877,calculated_trading_power: 1.877}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.593,calculated_trading_power: 2.593}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CAD:Country {name:"CAD"}) CREATE (CAD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.359,calculated_trading_power: 9.359}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CHI:Country {name:"CHI"}) CREATE (CHI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.843,calculated_trading_power: 12.843}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CHO:Country {name:"CHO"}) CREATE (CHO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.223,calculated_trading_power: 11.223}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (COM:Country {name:"COM"}) CREATE (COM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (MMI:Country {name:"MMI"}) CREATE (MMI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (OSA:Country {name:"OSA"}) CREATE (OSA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.241,calculated_trading_power: 9.241}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (PAW:Country {name:"PAW"}) CREATE (PAW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.74,calculated_trading_power: 8.74}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (ALT:Country {name:"ALT"}) CREATE (ALT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (ICH:Country {name:"ICH"}) CREATE (ICH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (ABI:Country {name:"ABI"}) CREATE (ABI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.493,calculated_trading_power: 9.493}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (COW:Country {name:"COW"}) CREATE (COW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.158,calculated_trading_power: 10.158}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (NTZ:Country {name:"NTZ"}) CREATE (NTZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.486,calculated_trading_power: 9.486}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (PCH:Country {name:"PCH"}) CREATE (PCH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.359,calculated_trading_power: 9.359}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (QUI:Country {name:"QUI"}) CREATE (QUI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.892,calculated_trading_power: 9.892}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CCA:Country {name:"CCA"}) CREATE (CCA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.397,calculated_trading_power: 8.397}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (KSI:Country {name:"KSI"}) CREATE (KSI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (OEO:Country {name:"OEO"}) CREATE (OEO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.517,calculated_trading_power: 8.517}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (NTC:Country {name:"NTC"}) CREATE (NTC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.133,calculated_trading_power: 10.133}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (KSK:Country {name:"KSK"}) CREATE (KSK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (HWK:Country {name:"HWK"}) CREATE (HWK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (CLG:Country {name:"CLG"}) CREATE (CLG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (KSP:Country {name:"KSP"}) CREATE (KSP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (WCY:Country {name:"WCY"}) CREATE (WCY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.424,calculated_trading_power: 9.424}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (LAK:Country {name:"LAK"}) CREATE (LAK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (C02:Country {name:"C02"}) CREATE (C02)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.387,calculated_trading_power: 13.387}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (C03:Country {name:"C03"}) CREATE (C03)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 88.316,calculated_trading_power: 88.316}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.005,calculated_trading_power: 34.005}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (C06:Country {name:"C06"}) CREATE (C06)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.563,calculated_trading_power: 13.563}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (C09:Country {name:"C09"}) CREATE (C09)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 43.45,calculated_trading_power: 43.45}]->(mississippi_river);
MATCH (mississippi_river:Trade_node {name:"mississippi_river"}), (C10:Country {name:"C10"}) CREATE (C10)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.37,calculated_trading_power: 16.37}]->(mississippi_river);
MATCH (ohio:Trade_node {name:"ohio"}), (CHE:Country {name:"CHE"}) CREATE (CHE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.502,calculated_trading_power: 4.502}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (FOX:Country {name:"FOX"}) CREATE (FOX)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.278,calculated_trading_power: 12.278}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (LEN:Country {name:"LEN"}) CREATE (LEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.475,calculated_trading_power: 12.475}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (MMI:Country {name:"MMI"}) CREATE (MMI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.711,calculated_trading_power: 11.711}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (OJI:Country {name:"OJI"}) CREATE (OJI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.475,calculated_trading_power: 12.475}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (OTT:Country {name:"OTT"}) CREATE (OTT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.655,calculated_trading_power: 10.655}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (POT:Country {name:"POT"}) CREATE (POT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.253,calculated_trading_power: 11.253}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (SIO:Country {name:"SIO"}) CREATE (SIO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.655,calculated_trading_power: 10.655}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ALT:Country {name:"ALT"}) CREATE (ALT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ICH:Country {name:"ICH"}) CREATE (ICH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (COF:Country {name:"COF"}) CREATE (COF)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (JOA:Country {name:"JOA"}) CREATE (JOA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (KSI:Country {name:"KSI"}) CREATE (KSI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (MOH:Country {name:"MOH"}) CREATE (MOH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.179,calculated_trading_power: 17.179}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ONE:Country {name:"ONE"}) CREATE (ONE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.851,calculated_trading_power: 11.851}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (ONO:Country {name:"ONO"}) CREATE (ONO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.647,calculated_trading_power: 13.647}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CAY:Country {name:"CAY"}) CREATE (CAY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.6,calculated_trading_power: 11.6}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (SEN:Country {name:"SEN"}) CREATE (SEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.253,calculated_trading_power: 11.253}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (WEN:Country {name:"WEN"}) CREATE (WEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.851,calculated_trading_power: 11.851}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (TSC:Country {name:"TSC"}) CREATE (TSC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (KSK:Country {name:"KSK"}) CREATE (KSK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.026,calculated_trading_power: 13.026}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (HWK:Country {name:"HWK"}) CREATE (HWK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.026,calculated_trading_power: 13.026}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (CLG:Country {name:"CLG"}) CREATE (CLG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.711,calculated_trading_power: 11.711}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (KSP:Country {name:"KSP"}) CREATE (KSP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.711,calculated_trading_power: 11.711}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (MSG:Country {name:"MSG"}) CREATE (MSG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.253,calculated_trading_power: 11.253}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (C00:Country {name:"C00"}) CREATE (C00)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.312,calculated_trading_power: 3.312}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (C01:Country {name:"C01"}) CREATE (C01)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 164.587,calculated_trading_power: 164.587}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (C02:Country {name:"C02"}) CREATE (C02)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 167.175,calculated_trading_power: 167.175}]->(ohio);
MATCH (ohio:Trade_node {name:"ohio"}), (C03:Country {name:"C03"}) CREATE (C03)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.518,calculated_trading_power: 34.518}]->(ohio);
MATCH (mexico:Trade_node {name:"mexico"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.591,calculated_trading_power: 10.591}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.593,calculated_trading_power: 2.593}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.005,calculated_trading_power: 34.005}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (C08:Country {name:"C08"}) CREATE (C08)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 329.788,calculated_trading_power: 329.788}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (C09:Country {name:"C09"}) CREATE (C09)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 43.45,calculated_trading_power: 43.45}]->(mexico);
MATCH (mexico:Trade_node {name:"mexico"}), (C10:Country {name:"C10"}) CREATE (C10)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 38.317,calculated_trading_power: 38.317}]->(mexico);
MATCH (lhasa:Trade_node {name:"lhasa"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.765,calculated_trading_power: 12.765}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.793,calculated_trading_power: 2.793}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.633,calculated_trading_power: 11.633}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (TAU:Country {name:"TAU"}) CREATE (TAU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 96.115,calculated_trading_power: 96.115}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (KSD:Country {name:"KSD"}) CREATE (KSD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 97.194,calculated_trading_power: 97.194}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (KAM:Country {name:"KAM"}) CREATE (KAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.332,calculated_trading_power: 6.332}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 40.201,calculated_trading_power: 40.201}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (ASS:Country {name:"ASS"}) CREATE (ASS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 41.684,calculated_trading_power: 41.684}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (BST:Country {name:"BST"}) CREATE (BST)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.38,calculated_trading_power: 4.38}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (BHU:Country {name:"BHU"}) CREATE (BHU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.675,calculated_trading_power: 11.675}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (KMT:Country {name:"KMT"}) CREATE (KMT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.98,calculated_trading_power: 4.98}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (SKK:Country {name:"SKK"}) CREATE (SKK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.375,calculated_trading_power: 9.375}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (RTT:Country {name:"RTT"}) CREATE (RTT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.892,calculated_trading_power: 5.892}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (GRK:Country {name:"GRK"}) CREATE (GRK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.874,calculated_trading_power: 14.874}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (MKP:Country {name:"MKP"}) CREATE (MKP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.58,calculated_trading_power: 7.58}]->(lhasa);
MATCH (lhasa:Trade_node {name:"lhasa"}), (KTU:Country {name:"KTU"}) CREATE (KTU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.154,calculated_trading_power: 18.154}]->(lhasa);
MATCH (chengdu:Trade_node {name:"chengdu"}), (DAI:Country {name:"DAI"}) CREATE (DAI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.918,calculated_trading_power: 3.918}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (KHM:Country {name:"KHM"}) CREATE (KHM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 47.25,calculated_trading_power: 47.25}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (LNA:Country {name:"LNA"}) CREATE (LNA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.19,calculated_trading_power: 20.19}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (LXA:Country {name:"LXA"}) CREATE (LXA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 93.665,calculated_trading_power: 93.665}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 136.053,calculated_trading_power: 136.053}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (TAU:Country {name:"TAU"}) CREATE (TAU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 119.409,calculated_trading_power: 119.409}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (TOK:Country {name:"TOK"}) CREATE (TOK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 51.833,calculated_trading_power: 51.833}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (KSD:Country {name:"KSD"}) CREATE (KSD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.401,calculated_trading_power: 11.401}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (KAM:Country {name:"KAM"}) CREATE (KAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.077,calculated_trading_power: 22.077}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (ASS:Country {name:"ASS"}) CREATE (ASS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 44.91,calculated_trading_power: 44.91}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (KAC:Country {name:"KAC"}) CREATE (KAC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.98,calculated_trading_power: 3.98}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (MLB:Country {name:"MLB"}) CREATE (MLB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(chengdu);
MATCH (chengdu:Trade_node {name:"chengdu"}), (TPR:Country {name:"TPR"}) CREATE (TPR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(chengdu);
MATCH (burma:Trade_node {name:"burma"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.765,calculated_trading_power: 12.765}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (ANN:Country {name:"ANN"}) CREATE (ANN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.697,calculated_trading_power: 23.697}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.807,calculated_trading_power: 4.807}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (AYU:Country {name:"AYU"}) CREATE (AYU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 27.186,calculated_trading_power: 27.186}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (CHA:Country {name:"CHA"}) CREATE (CHA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.186,calculated_trading_power: 2.186}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (DAI:Country {name:"DAI"}) CREATE (DAI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.84,calculated_trading_power: 1.84}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KHM:Country {name:"KHM"}) CREATE (KHM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 29.294,calculated_trading_power: 29.294}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (LNA:Country {name:"LNA"}) CREATE (LNA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.022,calculated_trading_power: 5.022}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (LXA:Country {name:"LXA"}) CREATE (LXA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 31.585,calculated_trading_power: 31.585}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (SUK:Country {name:"SUK"}) CREATE (SUK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.994,calculated_trading_power: 1.994}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (TAU:Country {name:"TAU"}) CREATE (TAU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 143.07,calculated_trading_power: 143.07}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (TOK:Country {name:"TOK"}) CREATE (TOK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.657,calculated_trading_power: 15.657}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 27.173,calculated_trading_power: 27.173}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (ASS:Country {name:"ASS"}) CREATE (ASS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 50.492,calculated_trading_power: 50.492}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (GRJ:Country {name:"GRJ"}) CREATE (GRJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (BST:Country {name:"BST"}) CREATE (BST)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KAC:Country {name:"KAC"}) CREATE (KAC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.519,calculated_trading_power: 8.519}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KMT:Country {name:"KMT"}) CREATE (KMT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.992,calculated_trading_power: 1.992}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MLB:Country {name:"MLB"}) CREATE (MLB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.204,calculated_trading_power: 9.204}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (TPR:Country {name:"TPR"}) CREATE (TPR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.628,calculated_trading_power: 8.628}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (SBP:Country {name:"SBP"}) CREATE (SBP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (PTT:Country {name:"PTT"}) CREATE (PTT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.964,calculated_trading_power: 1.964}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (RTT:Country {name:"RTT"}) CREATE (RTT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.964,calculated_trading_power: 1.964}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KLH:Country {name:"KLH"}) CREATE (KLH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (KJH:Country {name:"KJH"}) CREATE (KJH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (PRD:Country {name:"PRD"}) CREATE (PRD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (JPR:Country {name:"JPR"}) CREATE (JPR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(burma);
MATCH (burma:Trade_node {name:"burma"}), (MKP:Country {name:"MKP"}) CREATE (MKP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(burma);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.443,calculated_trading_power: 7.443}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (ANN:Country {name:"ANN"}) CREATE (ANN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 46.95,calculated_trading_power: 46.95}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.014,calculated_trading_power: 2.014}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.898,calculated_trading_power: 34.898}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (AYU:Country {name:"AYU"}) CREATE (AYU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 91.148,calculated_trading_power: 91.148}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 30.243,calculated_trading_power: 30.243}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (CHA:Country {name:"CHA"}) CREATE (CHA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.628,calculated_trading_power: 15.628}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (DAI:Country {name:"DAI"}) CREATE (DAI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.758,calculated_trading_power: 5.758}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (KHM:Country {name:"KHM"}) CREATE (KHM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 86.163,calculated_trading_power: 86.163}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (LNA:Country {name:"LNA"}) CREATE (LNA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.594,calculated_trading_power: 26.594}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (LXA:Country {name:"LXA"}) CREATE (LXA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 108.876,calculated_trading_power: 108.876}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.968,calculated_trading_power: 4.968}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 27.085,calculated_trading_power: 27.085}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (SUK:Country {name:"SUK"}) CREATE (SUK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.816,calculated_trading_power: 10.816}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (SUL:Country {name:"SUL"}) CREATE (SUL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (TAU:Country {name:"TAU"}) CREATE (TAU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.922,calculated_trading_power: 13.922}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (TOK:Country {name:"TOK"}) CREATE (TOK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 22.1,calculated_trading_power: 22.1}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (JOH:Country {name:"JOH"}) CREATE (JOH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.503,calculated_trading_power: 20.503}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (PGR:Country {name:"PGR"}) CREATE (PGR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.222,calculated_trading_power: 2.222}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.979,calculated_trading_power: 5.979}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.513,calculated_trading_power: 5.513}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.513,calculated_trading_power: 2.513}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.458,calculated_trading_power: 2.458}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (LFA:Country {name:"LFA"}) CREATE (LFA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.238,calculated_trading_power: 3.238}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.218,calculated_trading_power: 2.218}]->(gulf_of_siam);
MATCH (gulf_of_siam:Trade_node {name:"gulf_of_siam"}), (IND:Country {name:"IND"}) CREATE (IND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gulf_of_siam);
MATCH (canton:Trade_node {name:"canton"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.615,calculated_trading_power: 12.615}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 29.019,calculated_trading_power: 29.019}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.443,calculated_trading_power: 7.443}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (ANN:Country {name:"ANN"}) CREATE (ANN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 40.101,calculated_trading_power: 40.101}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.898,calculated_trading_power: 34.898}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 30.243,calculated_trading_power: 30.243}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (DAI:Country {name:"DAI"}) CREATE (DAI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.17,calculated_trading_power: 36.17}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (MKS:Country {name:"MKS"}) CREATE (MKS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (MTR:Country {name:"MTR"}) CREATE (MTR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.587,calculated_trading_power: 20.587}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.968,calculated_trading_power: 4.968}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 145.585,calculated_trading_power: 145.585}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (SUL:Country {name:"SUL"}) CREATE (SUL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.329,calculated_trading_power: 5.329}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (TOK:Country {name:"TOK"}) CREATE (TOK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 82.366,calculated_trading_power: 82.366}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (JOH:Country {name:"JOH"}) CREATE (JOH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.503,calculated_trading_power: 20.503}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.657,calculated_trading_power: 3.657}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.491,calculated_trading_power: 3.491}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.513,calculated_trading_power: 2.513}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.458,calculated_trading_power: 2.458}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (LFA:Country {name:"LFA"}) CREATE (LFA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.238,calculated_trading_power: 3.238}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (MGD:Country {name:"MGD"}) CREATE (MGD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.985,calculated_trading_power: 3.985}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (TER:Country {name:"TER"}) CREATE (TER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(canton);
MATCH (canton:Trade_node {name:"canton"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.218,calculated_trading_power: 2.218}]->(canton);
MATCH (philippines:Trade_node {name:"philippines"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 75.876,calculated_trading_power: 75.876}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.742,calculated_trading_power: 13.742}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BLI:Country {name:"BLI"}) CREATE (BLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.024,calculated_trading_power: 2.024}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BAN:Country {name:"BAN"}) CREATE (BAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.296,calculated_trading_power: 5.296}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.926,calculated_trading_power: 2.926}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (JAP:Country {name:"JAP"}) CREATE (JAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.595,calculated_trading_power: 17.595}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (MKS:Country {name:"MKS"}) CREATE (MKS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.748,calculated_trading_power: 7.748}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (MTR:Country {name:"MTR"}) CREATE (MTR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 29.831,calculated_trading_power: 29.831}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (SUL:Country {name:"SUL"}) CREATE (SUL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 44.926,calculated_trading_power: 44.926}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BLM:Country {name:"BLM"}) CREATE (BLM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BTN:Country {name:"BTN"}) CREATE (BTN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.038,calculated_trading_power: 2.038}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (CRB:Country {name:"CRB"}) CREATE (CRB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.529,calculated_trading_power: 4.529}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (LUW:Country {name:"LUW"}) CREATE (LUW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.406,calculated_trading_power: 4.406}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (MGD:Country {name:"MGD"}) CREATE (MGD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 38.357,calculated_trading_power: 38.357}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (TER:Country {name:"TER"}) CREATE (TER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.43,calculated_trading_power: 4.43}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (TID:Country {name:"TID"}) CREATE (TID)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.038,calculated_trading_power: 2.038}]->(philippines);
MATCH (philippines:Trade_node {name:"philippines"}), (BNE:Country {name:"BNE"}) CREATE (BNE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.031,calculated_trading_power: 5.031}]->(philippines);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.681,calculated_trading_power: 23.681}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (C05:Country {name:"C05"}) CREATE (C05)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 55.536,calculated_trading_power: 55.536}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (C10:Country {name:"C10"}) CREATE (C10)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 37.771,calculated_trading_power: 37.771}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (C11:Country {name:"C11"}) CREATE (C11)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 117.069,calculated_trading_power: 117.069}]->(cuiaba);
MATCH (cuiaba:Trade_node {name:"cuiaba"}), (C12:Country {name:"C12"}) CREATE (C12)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 100.287,calculated_trading_power: 100.287}]->(cuiaba);
MATCH (lima:Trade_node {name:"lima"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.403,calculated_trading_power: 1.403}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (C08:Country {name:"C08"}) CREATE (C08)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.698,calculated_trading_power: 23.698}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (C09:Country {name:"C09"}) CREATE (C09)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.681,calculated_trading_power: 23.681}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (C10:Country {name:"C10"}) CREATE (C10)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 129.022,calculated_trading_power: 129.022}]->(lima);
MATCH (lima:Trade_node {name:"lima"}), (C11:Country {name:"C11"}) CREATE (C11)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 151.76,calculated_trading_power: 151.76}]->(lima);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.403,calculated_trading_power: 1.403}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (JAP:Country {name:"JAP"}) CREATE (JAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 41.19,calculated_trading_power: 41.19}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (MRI:Country {name:"MRI"}) CREATE (MRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.693,calculated_trading_power: 2.693}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (KOR:Country {name:"KOR"}) CREATE (KOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.789,calculated_trading_power: 16.789}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (MAA:Country {name:"MAA"}) CREATE (MAA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.877,calculated_trading_power: 29.877}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TAN:Country {name:"TAN"}) CREATE (TAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.468,calculated_trading_power: 30.468}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TAK:Country {name:"TAK"}) CREATE (TAK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.581,calculated_trading_power: 29.581}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TNK:Country {name:"TNK"}) CREATE (TNK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.468,calculated_trading_power: 30.468}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TEA:Country {name:"TEA"}) CREATE (TEA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.468,calculated_trading_power: 30.468}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TTT:Country {name:"TTT"}) CREATE (TTT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.468,calculated_trading_power: 30.468}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (WAI:Country {name:"WAI"}) CREATE (WAI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.147,calculated_trading_power: 26.147}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (HAW:Country {name:"HAW"}) CREATE (HAW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 34.336,calculated_trading_power: 34.336}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (MAU:Country {name:"MAU"}) CREATE (MAU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 33.751,calculated_trading_power: 33.751}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (OAH:Country {name:"OAH"}) CREATE (OAH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 41.066,calculated_trading_power: 41.066}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (KAA:Country {name:"KAA"}) CREATE (KAA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 33.476,calculated_trading_power: 33.476}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (TOG:Country {name:"TOG"}) CREATE (TOG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 41.445,calculated_trading_power: 41.445}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (SAM:Country {name:"SAM"}) CREATE (SAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.709,calculated_trading_power: 36.709}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (VIL:Country {name:"VIL"}) CREATE (VIL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.534,calculated_trading_power: 28.534}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (VNL:Country {name:"VNL"}) CREATE (VNL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.949,calculated_trading_power: 27.949}]->(polynesia_node);
MATCH (polynesia_node:Trade_node {name:"polynesia_node"}), (LAI:Country {name:"LAI"}) CREATE (LAI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.364,calculated_trading_power: 27.364}]->(polynesia_node);
MATCH (australia:Trade_node {name:"australia"}), (TIW:Country {name:"TIW"}) CREATE (TIW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.535,calculated_trading_power: 11.535}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (LAR:Country {name:"LAR"}) CREATE (LAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.108,calculated_trading_power: 19.108}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (YOL:Country {name:"YOL"}) CREATE (YOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.573,calculated_trading_power: 13.573}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (YNU:Country {name:"YNU"}) CREATE (YNU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.854,calculated_trading_power: 12.854}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (AWN:Country {name:"AWN"}) CREATE (AWN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.413,calculated_trading_power: 13.413}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (GMI:Country {name:"GMI"}) CREATE (GMI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.19,calculated_trading_power: 10.19}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (MIA:Country {name:"MIA"}) CREATE (MIA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.511,calculated_trading_power: 27.511}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (EOR:Country {name:"EOR"}) CREATE (EOR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.564,calculated_trading_power: 36.564}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (KAU:Country {name:"KAU"}) CREATE (KAU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.396,calculated_trading_power: 35.396}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (PLW:Country {name:"PLW"}) CREATE (PLW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.392,calculated_trading_power: 26.392}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (WRU:Country {name:"WRU"}) CREATE (WRU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.636,calculated_trading_power: 36.636}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (NOO:Country {name:"NOO"}) CREATE (NOO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.337,calculated_trading_power: 36.337}]->(australia);
MATCH (australia:Trade_node {name:"australia"}), (MLG:Country {name:"MLG"}) CREATE (MLG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.784,calculated_trading_power: 27.784}]->(australia);
MATCH (nippon:Trade_node {name:"nippon"}), (JAP:Country {name:"JAP"}) CREATE (JAP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 215.112,calculated_trading_power: 215.112}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (DTE:Country {name:"DTE"}) CREATE (DTE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.232,calculated_trading_power: 13.232}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (HSK:Country {name:"HSK"}) CREATE (HSK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.515,calculated_trading_power: 13.515}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (IKE:Country {name:"IKE"}) CREATE (IKE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.196,calculated_trading_power: 13.196}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (MAE:Country {name:"MAE"}) CREATE (MAE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.807,calculated_trading_power: 13.807}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (MRI:Country {name:"MRI"}) CREATE (MRI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.967,calculated_trading_power: 24.967}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (SMZ:Country {name:"SMZ"}) CREATE (SMZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.092,calculated_trading_power: 17.092}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (UES:Country {name:"UES"}) CREATE (UES)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.457,calculated_trading_power: 16.457}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (RFR:Country {name:"RFR"}) CREATE (RFR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.168,calculated_trading_power: 30.168}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (ITO:Country {name:"ITO"}) CREATE (ITO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.365,calculated_trading_power: 13.365}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (SHN:Country {name:"SHN"}) CREATE (SHN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.094,calculated_trading_power: 15.094}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (STK:Country {name:"STK"}) CREATE (STK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.57,calculated_trading_power: 9.57}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (KOR:Country {name:"KOR"}) CREATE (KOR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 134.905,calculated_trading_power: 134.905}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 37.658,calculated_trading_power: 37.658}]->(nippon);
MATCH (nippon:Trade_node {name:"nippon"}), (RYU:Country {name:"RYU"}) CREATE (RYU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.317,calculated_trading_power: 19.317}]->(nippon);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.443,calculated_trading_power: 7.443}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.898,calculated_trading_power: 34.898}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (AYU:Country {name:"AYU"}) CREATE (AYU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.439,calculated_trading_power: 16.439}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (BAN:Country {name:"BAN"}) CREATE (BAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 30.243,calculated_trading_power: 30.243}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.968,calculated_trading_power: 4.968}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 328.175,calculated_trading_power: 328.175}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (JOH:Country {name:"JOH"}) CREATE (JOH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.503,calculated_trading_power: 20.503}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (KED:Country {name:"KED"}) CREATE (KED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.99,calculated_trading_power: 1.99}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (PRK:Country {name:"PRK"}) CREATE (PRK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.979,calculated_trading_power: 5.979}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.513,calculated_trading_power: 5.513}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.535,calculated_trading_power: 4.535}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.48,calculated_trading_power: 4.48}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (LFA:Country {name:"LFA"}) CREATE (LFA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.256,calculated_trading_power: 5.256}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.24,calculated_trading_power: 4.24}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (BRS:Country {name:"BRS"}) CREATE (BRS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(hangzhou);
MATCH (hangzhou:Trade_node {name:"hangzhou"}), (JMB:Country {name:"JMB"}) CREATE (JMB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(hangzhou);
MATCH (xian:Trade_node {name:"xian"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 266.924,calculated_trading_power: 266.924}]->(xian);
MATCH (xian:Trade_node {name:"xian"}), (ZUN:Country {name:"ZUN"}) CREATE (ZUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 101.807,calculated_trading_power: 101.807}]->(xian);
MATCH (beijing:Trade_node {name:"beijing"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 191.836,calculated_trading_power: 191.836}]->(beijing);
MATCH (beijing:Trade_node {name:"beijing"}), (ZUN:Country {name:"ZUN"}) CREATE (ZUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.722,calculated_trading_power: 9.722}]->(beijing);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.678,calculated_trading_power: 3.678}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 58.404,calculated_trading_power: 58.404}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.898,calculated_trading_power: 34.898}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BLI:Country {name:"BLI"}) CREATE (BLI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.558,calculated_trading_power: 17.558}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BAN:Country {name:"BAN"}) CREATE (BAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 33.843,calculated_trading_power: 33.843}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 30.243,calculated_trading_power: 30.243}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (MKS:Country {name:"MKS"}) CREATE (MKS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 64.57,calculated_trading_power: 64.57}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (MTR:Country {name:"MTR"}) CREATE (MTR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 100.773,calculated_trading_power: 100.773}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.968,calculated_trading_power: 4.968}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (JOH:Country {name:"JOH"}) CREATE (JOH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.503,calculated_trading_power: 20.503}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BLM:Country {name:"BLM"}) CREATE (BLM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.525,calculated_trading_power: 21.525}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BTN:Country {name:"BTN"}) CREATE (BTN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 37.464,calculated_trading_power: 37.464}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (CRB:Country {name:"CRB"}) CREATE (CRB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 31.328,calculated_trading_power: 31.328}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.657,calculated_trading_power: 3.657}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.513,calculated_trading_power: 5.513}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.513,calculated_trading_power: 2.513}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.458,calculated_trading_power: 2.458}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (LFA:Country {name:"LFA"}) CREATE (LFA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.238,calculated_trading_power: 3.238}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (LUW:Country {name:"LUW"}) CREATE (LUW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 41.006,calculated_trading_power: 41.006}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (TER:Country {name:"TER"}) CREATE (TER)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.9,calculated_trading_power: 22.9}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (TID:Country {name:"TID"}) CREATE (TID)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.924,calculated_trading_power: 12.924}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BNE:Country {name:"BNE"}) CREATE (BNE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 51.037,calculated_trading_power: 51.037}]->(the_moluccas);
MATCH (the_moluccas:Trade_node {name:"the_moluccas"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.218,calculated_trading_power: 2.218}]->(the_moluccas);
MATCH (siberia:Trade_node {name:"siberia"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 76.207,calculated_trading_power: 76.207}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (KZH:Country {name:"KZH"}) CREATE (KZH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 110.189,calculated_trading_power: 110.189}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (KHI:Country {name:"KHI"}) CREATE (KHI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.134,calculated_trading_power: 5.134}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (BUK:Country {name:"BUK"}) CREATE (BUK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.357,calculated_trading_power: 9.357}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.987,calculated_trading_power: 1.987}]->(siberia);
MATCH (siberia:Trade_node {name:"siberia"}), (ZUN:Country {name:"ZUN"}) CREATE (ZUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.943,calculated_trading_power: 6.943}]->(siberia);
MATCH (yumen:Trade_node {name:"yumen"}), (KZH:Country {name:"KZH"}) CREATE (KZH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 102.645,calculated_trading_power: 102.645}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (KHI:Country {name:"KHI"}) CREATE (KHI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.134,calculated_trading_power: 5.134}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (BUK:Country {name:"BUK"}) CREATE (BUK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 87.412,calculated_trading_power: 87.412}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (QNG:Country {name:"QNG"}) CREATE (QNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 42.696,calculated_trading_power: 42.696}]->(yumen);
MATCH (yumen:Trade_node {name:"yumen"}), (ZUN:Country {name:"ZUN"}) CREATE (ZUN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 152.816,calculated_trading_power: 152.816}]->(yumen);
MATCH (malacca:Trade_node {name:"malacca"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.36,calculated_trading_power: 18.36}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 28.127,calculated_trading_power: 28.127}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.807,calculated_trading_power: 4.807}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (ATJ:Country {name:"ATJ"}) CREATE (ATJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 111.864,calculated_trading_power: 111.864}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (AYU:Country {name:"AYU"}) CREATE (AYU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.155,calculated_trading_power: 12.155}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BAN:Country {name:"BAN"}) CREATE (BAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.127,calculated_trading_power: 2.127}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BEI:Country {name:"BEI"}) CREATE (BEI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 90.409,calculated_trading_power: 90.409}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PAT:Country {name:"PAT"}) CREATE (PAT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 43.269,calculated_trading_power: 43.269}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 25.575,calculated_trading_power: 25.575}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (MAD:Country {name:"MAD"}) CREATE (MAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (KLN:Country {name:"KLN"}) CREATE (KLN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.96,calculated_trading_power: 1.96}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (MAB:Country {name:"MAB"}) CREATE (MAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.186,calculated_trading_power: 2.186}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (JOH:Country {name:"JOH"}) CREATE (JOH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 80.176,calculated_trading_power: 80.176}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (KED:Country {name:"KED"}) CREATE (KED)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 34.054,calculated_trading_power: 34.054}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PRK:Country {name:"PRK"}) CREATE (PRK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.982,calculated_trading_power: 27.982}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PGR:Country {name:"PGR"}) CREATE (PGR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.064,calculated_trading_power: 24.064}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (PLB:Country {name:"PLB"}) CREATE (PLB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.59,calculated_trading_power: 35.59}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (SAK:Country {name:"SAK"}) CREATE (SAK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.095,calculated_trading_power: 35.095}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (KUT:Country {name:"KUT"}) CREATE (KUT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 37.276,calculated_trading_power: 37.276}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BNJ:Country {name:"BNJ"}) CREATE (BNJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 31.285,calculated_trading_power: 31.285}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (LFA:Country {name:"LFA"}) CREATE (LFA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 43.307,calculated_trading_power: 43.307}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BEU:Country {name:"BEU"}) CREATE (BEU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 44.209,calculated_trading_power: 44.209}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (BRS:Country {name:"BRS"}) CREATE (BRS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.274,calculated_trading_power: 30.274}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (JMB:Country {name:"JMB"}) CREATE (JMB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.575,calculated_trading_power: 18.575}]->(malacca);
MATCH (malacca:Trade_node {name:"malacca"}), (IND:Country {name:"IND"}) CREATE (IND)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.241,calculated_trading_power: 25.241}]->(malacca);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 72.675,calculated_trading_power: 72.675}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.534,calculated_trading_power: 7.534}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.142,calculated_trading_power: 8.142}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.367,calculated_trading_power: 9.367}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (ARK:Country {name:"ARK"}) CREATE (ARK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 50.805,calculated_trading_power: 50.805}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 126.688,calculated_trading_power: 126.688}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MYS:Country {name:"MYS"}) CREATE (MYS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.362,calculated_trading_power: 24.362}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MAD:Country {name:"MAD"}) CREATE (MAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.81,calculated_trading_power: 5.81}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (GDW:Country {name:"GDW"}) CREATE (GDW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.99,calculated_trading_power: 1.99}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (GRJ:Country {name:"GRJ"}) CREATE (GRJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.071,calculated_trading_power: 9.071}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (DHU:Country {name:"DHU"}) CREATE (DHU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.148,calculated_trading_power: 7.148}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KLN:Country {name:"KLN"}) CREATE (KLN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.625,calculated_trading_power: 4.625}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MAB:Country {name:"MAB"}) CREATE (MAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.422,calculated_trading_power: 6.422}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (BST:Country {name:"BST"}) CREATE (BST)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.681,calculated_trading_power: 10.681}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (BND:Country {name:"BND"}) CREATE (BND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.633,calculated_trading_power: 4.633}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KMT:Country {name:"KMT"}) CREATE (KMT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.17,calculated_trading_power: 11.17}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (HAD:Country {name:"HAD"}) CREATE (HAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.295,calculated_trading_power: 5.295}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (LDK:Country {name:"LDK"}) CREATE (LDK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.024,calculated_trading_power: 2.024}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (BGL:Country {name:"BGL"}) CREATE (BGL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.295,calculated_trading_power: 5.295}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (GHR:Country {name:"GHR"}) CREATE (GHR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.896,calculated_trading_power: 3.896}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (JAJ:Country {name:"JAJ"}) CREATE (JAJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (PAN:Country {name:"PAN"}) CREATE (PAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.38,calculated_trading_power: 5.38}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (SBP:Country {name:"SBP"}) CREATE (SBP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.452,calculated_trading_power: 18.452}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (PTT:Country {name:"PTT"}) CREATE (PTT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.699,calculated_trading_power: 8.699}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (RTT:Country {name:"RTT"}) CREATE (RTT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.961,calculated_trading_power: 11.961}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KLH:Country {name:"KLH"}) CREATE (KLH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.628,calculated_trading_power: 8.628}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KJH:Country {name:"KJH"}) CREATE (KJH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.916,calculated_trading_power: 8.916}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (PRD:Country {name:"PRD"}) CREATE (PRD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.628,calculated_trading_power: 8.628}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (JPR:Country {name:"JPR"}) CREATE (JPR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.628,calculated_trading_power: 8.628}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (SRG:Country {name:"SRG"}) CREATE (SRG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KND:Country {name:"KND"}) CREATE (KND)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (DNG:Country {name:"DNG"}) CREATE (DNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (DTI:Country {name:"DTI"}) CREATE (DTI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.096,calculated_trading_power: 4.096}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (JML:Country {name:"JML"}) CREATE (JML)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (MKP:Country {name:"MKP"}) CREATE (MKP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.349,calculated_trading_power: 10.349}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (SRM:Country {name:"SRM"}) CREATE (SRM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ganges_delta);
MATCH (ganges_delta:Trade_node {name:"ganges_delta"}), (KMN:Country {name:"KMN"}) CREATE (KMN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(ganges_delta);
MATCH (doab:Trade_node {name:"doab"}), (KHI:Country {name:"KHI"}) CREATE (KHI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 54.33,calculated_trading_power: 54.33}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (BUK:Country {name:"BUK"}) CREATE (BUK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 78.055,calculated_trading_power: 78.055}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (MAR:Country {name:"MAR"}) CREATE (MAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 54.292,calculated_trading_power: 54.292}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 288.481,calculated_trading_power: 288.481}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (MYS:Country {name:"MYS"}) CREATE (MYS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 51.783,calculated_trading_power: 51.783}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (GDW:Country {name:"GDW"}) CREATE (GDW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.965,calculated_trading_power: 6.965}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (DHU:Country {name:"DHU"}) CREATE (DHU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.132,calculated_trading_power: 24.132}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (BND:Country {name:"BND"}) CREATE (BND)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.652,calculated_trading_power: 10.652}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (HAD:Country {name:"HAD"}) CREATE (HAD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.135,calculated_trading_power: 14.135}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (LDK:Country {name:"LDK"}) CREATE (LDK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.06,calculated_trading_power: 5.06}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (BGL:Country {name:"BGL"}) CREATE (BGL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.135,calculated_trading_power: 14.135}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (GHR:Country {name:"GHR"}) CREATE (GHR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.638,calculated_trading_power: 10.638}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (CHD:Country {name:"CHD"}) CREATE (CHD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (PAN:Country {name:"PAN"}) CREATE (PAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.328,calculated_trading_power: 14.328}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (SRG:Country {name:"SRG"}) CREATE (SRG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.628,calculated_trading_power: 8.628}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (DNG:Country {name:"DNG"}) CREATE (DNG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.422,calculated_trading_power: 11.422}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (DTI:Country {name:"DTI"}) CREATE (DTI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.992,calculated_trading_power: 10.992}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (JML:Country {name:"JML"}) CREATE (JML)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.422,calculated_trading_power: 11.422}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (SRM:Country {name:"SRM"}) CREATE (SRM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.846,calculated_trading_power: 10.846}]->(doab);
MATCH (doab:Trade_node {name:"doab"}), (KMN:Country {name:"KMN"}) CREATE (KMN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.846,calculated_trading_power: 10.846}]->(doab);
MATCH (lahore:Trade_node {name:"lahore"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.901,calculated_trading_power: 2.901}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (KZH:Country {name:"KZH"}) CREATE (KZH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 102.645,calculated_trading_power: 102.645}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (KHI:Country {name:"KHI"}) CREATE (KHI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 59.464,calculated_trading_power: 59.464}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (BUK:Country {name:"BUK"}) CREATE (BUK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 94.423,calculated_trading_power: 94.423}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 45.679,calculated_trading_power: 45.679}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (ZUN:Country {name:"ZUN"}) CREATE (ZUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.071,calculated_trading_power: 6.071}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (BAL:Country {name:"BAL"}) CREATE (BAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 56.886,calculated_trading_power: 56.886}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 76.495,calculated_trading_power: 76.495}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.53,calculated_trading_power: 2.53}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (JAN:Country {name:"JAN"}) CREATE (JAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (JSL:Country {name:"JSL"}) CREATE (JSL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (LDK:Country {name:"LDK"}) CREATE (LDK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.907,calculated_trading_power: 14.907}]->(lahore);
MATCH (lahore:Trade_node {name:"lahore"}), (JAJ:Country {name:"JAJ"}) CREATE (JAJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.587,calculated_trading_power: 4.587}]->(lahore);
MATCH (deccan:Trade_node {name:"deccan"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.743,calculated_trading_power: 11.743}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.534,calculated_trading_power: 7.534}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.142,calculated_trading_power: 8.142}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.367,calculated_trading_power: 9.367}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MAR:Country {name:"MAR"}) CREATE (MAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 68.885,calculated_trading_power: 68.885}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 289.626,calculated_trading_power: 289.626}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MYS:Country {name:"MYS"}) CREATE (MYS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 55.593,calculated_trading_power: 55.593}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (GUJ:Country {name:"GUJ"}) CREATE (GUJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.37,calculated_trading_power: 5.37}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MAD:Country {name:"MAD"}) CREATE (MAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.891,calculated_trading_power: 14.891}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.46,calculated_trading_power: 12.46}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (JAN:Country {name:"JAN"}) CREATE (JAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (GDW:Country {name:"GDW"}) CREATE (GDW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.817,calculated_trading_power: 18.817}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (KLN:Country {name:"KLN"}) CREATE (KLN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.158,calculated_trading_power: 11.158}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (MAB:Country {name:"MAB"}) CREATE (MAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.972,calculated_trading_power: 8.972}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (JSL:Country {name:"JSL"}) CREATE (JSL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (CHD:Country {name:"CHD"}) CREATE (CHD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.422,calculated_trading_power: 11.422}]->(deccan);
MATCH (deccan:Trade_node {name:"deccan"}), (JAJ:Country {name:"JAJ"}) CREATE (JAJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.587,calculated_trading_power: 4.587}]->(deccan);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.269,calculated_trading_power: 4.269}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 47.11,calculated_trading_power: 47.11}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 37.674,calculated_trading_power: 37.674}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 21.724,calculated_trading_power: 21.724}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 32.942,calculated_trading_power: 32.942}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (ADE:Country {name:"ADE"}) CREATE (ADE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.878,calculated_trading_power: 8.878}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.483,calculated_trading_power: 6.483}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (RAS:Country {name:"RAS"}) CREATE (RAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.409,calculated_trading_power: 34.409}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MDI:Country {name:"MDI"}) CREATE (MDI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.274,calculated_trading_power: 6.274}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (HAR:Country {name:"HAR"}) CREATE (HAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.307,calculated_trading_power: 2.307}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (HOB:Country {name:"HOB"}) CREATE (HOB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.051,calculated_trading_power: 4.051}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (WAR:Country {name:"WAR"}) CREATE (WAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.244,calculated_trading_power: 4.244}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MAR:Country {name:"MAR"}) CREATE (MAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.819,calculated_trading_power: 18.819}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 82.697,calculated_trading_power: 82.697}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MYS:Country {name:"MYS"}) CREATE (MYS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 56.972,calculated_trading_power: 56.972}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (GUJ:Country {name:"GUJ"}) CREATE (GUJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.37,calculated_trading_power: 2.37}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MAD:Country {name:"MAD"}) CREATE (MAD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 58.636,calculated_trading_power: 58.636}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.516,calculated_trading_power: 4.516}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (KLN:Country {name:"KLN"}) CREATE (KLN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 49.41,calculated_trading_power: 49.41}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (MAB:Country {name:"MAB"}) CREATE (MAB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.747,calculated_trading_power: 36.747}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (JAJ:Country {name:"JAJ"}) CREATE (JAJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.573,calculated_trading_power: 6.573}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (RJK:Country {name:"RJK"}) CREATE (RJK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.998,calculated_trading_power: 1.998}]->(comorin_cape);
MATCH (comorin_cape:Trade_node {name:"comorin_cape"}), (KND:Country {name:"KND"}) CREATE (KND)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.522,calculated_trading_power: 10.522}]->(comorin_cape);
MATCH (gujarat:Trade_node {name:"gujarat"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.509,calculated_trading_power: 14.509}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.173,calculated_trading_power: 17.173}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (ADE:Country {name:"ADE"}) CREATE (ADE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.878,calculated_trading_power: 8.878}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.483,calculated_trading_power: 6.483}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.281,calculated_trading_power: 20.281}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MHR:Country {name:"MHR"}) CREATE (MHR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (OMA:Country {name:"OMA"}) CREATE (OMA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 31.422,calculated_trading_power: 31.422}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (RAS:Country {name:"RAS"}) CREATE (RAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.409,calculated_trading_power: 34.409}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MSY:Country {name:"MSY"}) CREATE (MSY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.499,calculated_trading_power: 15.499}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 29.829,calculated_trading_power: 29.829}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.769,calculated_trading_power: 4.769}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.98,calculated_trading_power: 1.98}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MDI:Country {name:"MDI"}) CREATE (MDI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.274,calculated_trading_power: 6.274}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (AFA:Country {name:"AFA"}) CREATE (AFA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (HAR:Country {name:"HAR"}) CREATE (HAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.307,calculated_trading_power: 2.307}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (HOB:Country {name:"HOB"}) CREATE (HOB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.051,calculated_trading_power: 4.051}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MED:Country {name:"MED"}) CREATE (MED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MJE:Country {name:"MJE"}) CREATE (MJE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.022,calculated_trading_power: 2.022}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (PTE:Country {name:"PTE"}) CREATE (PTE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.222,calculated_trading_power: 2.222}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (WAR:Country {name:"WAR"}) CREATE (WAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.244,calculated_trading_power: 4.244}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.926,calculated_trading_power: 2.926}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.409,calculated_trading_power: 2.409}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (BAL:Country {name:"BAL"}) CREATE (BAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 32.862,calculated_trading_power: 32.862}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MAR:Country {name:"MAR"}) CREATE (MAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.017,calculated_trading_power: 12.017}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MUG:Country {name:"MUG"}) CREATE (MUG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 82.675,calculated_trading_power: 82.675}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (GUJ:Country {name:"GUJ"}) CREATE (GUJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.019,calculated_trading_power: 13.019}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (MER:Country {name:"MER"}) CREATE (MER)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.163,calculated_trading_power: 21.163}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (JAN:Country {name:"JAN"}) CREATE (JAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.826,calculated_trading_power: 7.826}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (JSL:Country {name:"JSL"}) CREATE (JSL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.281,calculated_trading_power: 9.281}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (JAJ:Country {name:"JAJ"}) CREATE (JAJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 49.628,calculated_trading_power: 49.628}]->(gujarat);
MATCH (gujarat:Trade_node {name:"gujarat"}), (RJK:Country {name:"RJK"}) CREATE (RJK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.071,calculated_trading_power: 10.071}]->(gujarat);
MATCH (katsina:Trade_node {name:"katsina"}), (ALG:Country {name:"ALG"}) CREATE (ALG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 61.4,calculated_trading_power: 61.4}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (TRP:Country {name:"TRP"}) CREATE (TRP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.809,calculated_trading_power: 12.809}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (TUN:Country {name:"TUN"}) CREATE (TUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 60.923,calculated_trading_power: 60.923}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (FZA:Country {name:"FZA"}) CREATE (FZA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.132,calculated_trading_power: 6.132}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (ASH:Country {name:"ASH"}) CREATE (ASH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.725,calculated_trading_power: 6.725}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (ETH:Country {name:"ETH"}) CREATE (ETH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.53,calculated_trading_power: 7.53}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (MAL:Country {name:"MAL"}) CREATE (MAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.826,calculated_trading_power: 4.826}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (NUB:Country {name:"NUB"}) CREATE (NUB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 53.7,calculated_trading_power: 53.7}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (SON:Country {name:"SON"}) CREATE (SON)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.029,calculated_trading_power: 12.029}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (HAU:Country {name:"HAU"}) CREATE (HAU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.097,calculated_trading_power: 23.097}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (KBO:Country {name:"KBO"}) CREATE (KBO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 101.292,calculated_trading_power: 101.292}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (OYO:Country {name:"OYO"}) CREATE (OYO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.485,calculated_trading_power: 24.485}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (SOF:Country {name:"SOF"}) CREATE (SOF)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 62.793,calculated_trading_power: 62.793}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (WGD:Country {name:"WGD"}) CREATE (WGD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.424,calculated_trading_power: 8.424}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (GUR:Country {name:"GUR"}) CREATE (GUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.044,calculated_trading_power: 4.044}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (WAD:Country {name:"WAD"}) CREATE (WAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.286,calculated_trading_power: 4.286}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (DAR:Country {name:"DAR"}) CREATE (DAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.084,calculated_trading_power: 6.084}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (AIR:Country {name:"AIR"}) CREATE (AIR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 47.279,calculated_trading_power: 47.279}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (BON:Country {name:"BON"}) CREATE (BON)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.654,calculated_trading_power: 6.654}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (DAH:Country {name:"DAH"}) CREATE (DAH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.036,calculated_trading_power: 4.036}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (DGB:Country {name:"DGB"}) CREATE (DGB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.444,calculated_trading_power: 4.444}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (FUL:Country {name:"FUL"}) CREATE (FUL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 47.341,calculated_trading_power: 47.341}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (JNN:Country {name:"JNN"}) CREATE (JNN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.336,calculated_trading_power: 16.336}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (KAN:Country {name:"KAN"}) CREATE (KAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.537,calculated_trading_power: 26.537}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (KNG:Country {name:"KNG"}) CREATE (KNG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.405,calculated_trading_power: 7.405}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (KTS:Country {name:"KTS"}) CREATE (KTS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 37.951,calculated_trading_power: 37.951}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (NUP:Country {name:"NUP"}) CREATE (NUP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.684,calculated_trading_power: 11.684}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (TMB:Country {name:"TMB"}) CREATE (TMB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 25.5,calculated_trading_power: 25.5}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (YAO:Country {name:"YAO"}) CREATE (YAO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.877,calculated_trading_power: 11.877}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (YAT:Country {name:"YAT"}) CREATE (YAT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.75,calculated_trading_power: 7.75}]->(katsina);
MATCH (katsina:Trade_node {name:"katsina"}), (ZZZ:Country {name:"ZZZ"}) CREATE (ZZZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.143,calculated_trading_power: 16.143}]->(katsina);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 28.473,calculated_trading_power: 28.473}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ADE:Country {name:"ADE"}) CREATE (ADE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.66,calculated_trading_power: 6.66}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.323,calculated_trading_power: 4.323}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.906,calculated_trading_power: 6.906}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (NJR:Country {name:"NJR"}) CREATE (NJR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.42,calculated_trading_power: 4.42}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (RAS:Country {name:"RAS"}) CREATE (RAS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 81.86,calculated_trading_power: 81.86}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ETH:Country {name:"ETH"}) CREATE (ETH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 77.931,calculated_trading_power: 77.931}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (NUB:Country {name:"NUB"}) CREATE (NUB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 90.818,calculated_trading_power: 90.818}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.231,calculated_trading_power: 14.231}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MDI:Country {name:"MDI"}) CREATE (MDI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.08,calculated_trading_power: 4.08}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ENA:Country {name:"ENA"}) CREATE (ENA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.253,calculated_trading_power: 11.253}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (OGD:Country {name:"OGD"}) CREATE (OGD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.244,calculated_trading_power: 9.244}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (WAD:Country {name:"WAD"}) CREATE (WAD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.606,calculated_trading_power: 11.606}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (DAR:Country {name:"DAR"}) CREATE (DAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 23.456,calculated_trading_power: 23.456}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (HAR:Country {name:"HAR"}) CREATE (HAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.715,calculated_trading_power: 10.715}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (HOB:Country {name:"HOB"}) CREATE (HOB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.606,calculated_trading_power: 3.606}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (KAF:Country {name:"KAF"}) CREATE (KAF)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.325,calculated_trading_power: 15.325}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MED:Country {name:"MED"}) CREATE (MED)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.169,calculated_trading_power: 14.169}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (MRE:Country {name:"MRE"}) CREATE (MRE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.67,calculated_trading_power: 9.67}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (WAR:Country {name:"WAR"}) CREATE (WAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.159,calculated_trading_power: 18.159}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (WLY:Country {name:"WLY"}) CREATE (WLY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.39,calculated_trading_power: 15.39}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (JJI:Country {name:"JJI"}) CREATE (JJI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.13,calculated_trading_power: 10.13}]->(ethiopia);
MATCH (ethiopia:Trade_node {name:"ethiopia"}), (ABB:Country {name:"ABB"}) CREATE (ABB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.794,calculated_trading_power: 9.794}]->(ethiopia);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 31.818,calculated_trading_power: 31.818}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.992,calculated_trading_power: 8.992}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (ADE:Country {name:"ADE"}) CREATE (ADE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 89.705,calculated_trading_power: 89.705}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (HDR:Country {name:"HDR"}) CREATE (HDR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 57.399,calculated_trading_power: 57.399}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 27.187,calculated_trading_power: 27.187}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MHR:Country {name:"MHR"}) CREATE (MHR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.622,calculated_trading_power: 30.622}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (NJR:Country {name:"NJR"}) CREATE (NJR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.314,calculated_trading_power: 10.314}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (OMA:Country {name:"OMA"}) CREATE (OMA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 35.861,calculated_trading_power: 35.861}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (RAS:Country {name:"RAS"}) CREATE (RAS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 121.378,calculated_trading_power: 121.378}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.499,calculated_trading_power: 15.499}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (ETH:Country {name:"ETH"}) CREATE (ETH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.406,calculated_trading_power: 1.406}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 29.829,calculated_trading_power: 29.829}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.769,calculated_trading_power: 4.769}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.739,calculated_trading_power: 27.739}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MDI:Country {name:"MDI"}) CREATE (MDI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 56.549,calculated_trading_power: 56.549}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (AFA:Country {name:"AFA"}) CREATE (AFA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.0,calculated_trading_power: 29.0}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (HAR:Country {name:"HAR"}) CREATE (HAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.444,calculated_trading_power: 20.444}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (HOB:Country {name:"HOB"}) CREATE (HOB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 38.795,calculated_trading_power: 38.795}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MED:Country {name:"MED"}) CREATE (MED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.095,calculated_trading_power: 4.095}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (MJE:Country {name:"MJE"}) CREATE (MJE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.43,calculated_trading_power: 32.43}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (WAR:Country {name:"WAR"}) CREATE (WAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 44.56,calculated_trading_power: 44.56}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.944,calculated_trading_power: 4.944}]->(gulf_of_aden);
MATCH (gulf_of_aden:Trade_node {name:"gulf_of_aden"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.427,calculated_trading_power: 4.427}]->(gulf_of_aden);
MATCH (hormuz:Trade_node {name:"hormuz"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.43,calculated_trading_power: 15.43}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (ANZ:Country {name:"ANZ"}) CREATE (ANZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.218,calculated_trading_power: 2.218}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (ARD:Country {name:"ARD"}) CREATE (ARD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (DAW:Country {name:"DAW"}) CREATE (DAW)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.218,calculated_trading_power: 2.218}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (NAJ:Country {name:"NAJ"}) CREATE (NAJ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.218,calculated_trading_power: 2.218}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (OMA:Country {name:"OMA"}) CREATE (OMA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 74.665,calculated_trading_power: 74.665}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (SHM:Country {name:"SHM"}) CREATE (SHM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.198,calculated_trading_power: 2.198}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (MSY:Country {name:"MSY"}) CREATE (MSY)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.191,calculated_trading_power: 4.191}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 54.778,calculated_trading_power: 54.778}]->(hormuz);
MATCH (hormuz:Trade_node {name:"hormuz"}), (BAL:Country {name:"BAL"}) CREATE (BAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.289,calculated_trading_power: 13.289}]->(hormuz);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.129,calculated_trading_power: 4.129}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.853,calculated_trading_power: 23.853}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.866,calculated_trading_power: 5.866}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (OMA:Country {name:"OMA"}) CREATE (OMA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.328,calculated_trading_power: 20.328}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (ZAN:Country {name:"ZAN"}) CREATE (ZAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 88.628,calculated_trading_power: 88.628}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (MLI:Country {name:"MLI"}) CREATE (MLI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.753,calculated_trading_power: 21.753}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (AJU:Country {name:"AJU"}) CREATE (AJU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.61,calculated_trading_power: 1.61}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (PTE:Country {name:"PTE"}) CREATE (PTE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.965,calculated_trading_power: 30.965}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (MIR:Country {name:"MIR"}) CREATE (MIR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.818,calculated_trading_power: 10.818}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (SKA:Country {name:"SKA"}) CREATE (SKA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 57.113,calculated_trading_power: 57.113}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (BTS:Country {name:"BTS"}) CREATE (BTS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 44.297,calculated_trading_power: 44.297}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (MFY:Country {name:"MFY"}) CREATE (MFY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 40.265,calculated_trading_power: 40.265}]->(zanzibar);
MATCH (zanzibar:Trade_node {name:"zanzibar"}), (ANT:Country {name:"ANT"}) CREATE (ANT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 37.708,calculated_trading_power: 37.708}]->(zanzibar);
MATCH (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.474,calculated_trading_power: 4.474}]->(cape_of_good_hope);
MATCH (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.188,calculated_trading_power: 18.188}]->(cape_of_good_hope);
MATCH (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.057,calculated_trading_power: 6.057}]->(cape_of_good_hope);
MATCH (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.341,calculated_trading_power: 4.341}]->(cape_of_good_hope);
MATCH (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}), (SYO:Country {name:"SYO"}) CREATE (SYO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.877,calculated_trading_power: 3.877}]->(cape_of_good_hope);
MATCH (cape_of_good_hope:Trade_node {name:"cape_of_good_hope"}), (KBU:Country {name:"KBU"}) CREATE (KBU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.384,calculated_trading_power: 2.384}]->(cape_of_good_hope);
MATCH (basra:Trade_node {name:"basra"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 64.432,calculated_trading_power: 64.432}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (ANZ:Country {name:"ANZ"}) CREATE (ANZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 6.698,calculated_trading_power: 6.698}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (ARD:Country {name:"ARD"}) CREATE (ARD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.64,calculated_trading_power: 9.64}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (DAW:Country {name:"DAW"}) CREATE (DAW)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.863,calculated_trading_power: 12.863}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (NAJ:Country {name:"NAJ"}) CREATE (NAJ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.396,calculated_trading_power: 11.396}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (SHM:Country {name:"SHM"}) CREATE (SHM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.992,calculated_trading_power: 18.992}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (MSY:Country {name:"MSY"}) CREATE (MSY)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.344,calculated_trading_power: 22.344}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 144.316,calculated_trading_power: 144.316}]->(basra);
MATCH (basra:Trade_node {name:"basra"}), (MGR:Country {name:"MGR"}) CREATE (MGR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(basra);
MATCH (samarkand:Trade_node {name:"samarkand"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.867,calculated_trading_power: 15.867}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (KZH:Country {name:"KZH"}) CREATE (KZH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 142.611,calculated_trading_power: 142.611}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (KHI:Country {name:"KHI"}) CREATE (KHI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 105.493,calculated_trading_power: 105.493}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (BUK:Country {name:"BUK"}) CREATE (BUK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 164.32,calculated_trading_power: 164.32}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 140.387,calculated_trading_power: 140.387}]->(samarkand);
MATCH (samarkand:Trade_node {name:"samarkand"}), (ZUN:Country {name:"ZUN"}) CREATE (ZUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 25.477,calculated_trading_power: 25.477}]->(samarkand);
MATCH (persia:Trade_node {name:"persia"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 31.993,calculated_trading_power: 31.993}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (CRI:Country {name:"CRI"}) CREATE (CRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.853,calculated_trading_power: 20.853}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (GEO:Country {name:"GEO"}) CREATE (GEO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.17,calculated_trading_power: 11.17}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.867,calculated_trading_power: 15.867}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (AVR:Country {name:"AVR"}) CREATE (AVR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.302,calculated_trading_power: 4.302}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 352.357,calculated_trading_power: 352.357}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (GAZ:Country {name:"GAZ"}) CREATE (GAZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 39.268,calculated_trading_power: 39.268}]->(persia);
MATCH (persia:Trade_node {name:"persia"}), (MGR:Country {name:"MGR"}) CREATE (MGR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.542,calculated_trading_power: 11.542}]->(persia);
MATCH (aleppo:Trade_node {name:"aleppo"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 246.363,calculated_trading_power: 246.363}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.57,calculated_trading_power: 20.57}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.042,calculated_trading_power: 2.042}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (ANZ:Country {name:"ANZ"}) CREATE (ANZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.218,calculated_trading_power: 2.218}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 27.187,calculated_trading_power: 27.187}]->(aleppo);
MATCH (aleppo:Trade_node {name:"aleppo"}), (ABB:Country {name:"ABB"}) CREATE (ABB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.896,calculated_trading_power: 1.896}]->(aleppo);
MATCH (alexandria:Trade_node {name:"alexandria"}), (KNI:Country {name:"KNI"}) CREATE (KNI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.014,calculated_trading_power: 2.014}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (RAG:Country {name:"RAG"}) CREATE (RAG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 22.032,calculated_trading_power: 22.032}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 207.883,calculated_trading_power: 207.883}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.314,calculated_trading_power: 8.314}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.112,calculated_trading_power: 2.112}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 32.134,calculated_trading_power: 32.134}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 42.77,calculated_trading_power: 42.77}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.313,calculated_trading_power: 2.313}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 37.32,calculated_trading_power: 37.32}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.023,calculated_trading_power: 24.023}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.863,calculated_trading_power: 12.863}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 49.866,calculated_trading_power: 49.866}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.147,calculated_trading_power: 5.147}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (ANZ:Country {name:"ANZ"}) CREATE (ANZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.542,calculated_trading_power: 1.542}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (HED:Country {name:"HED"}) CREATE (HED)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 81.16,calculated_trading_power: 81.16}]->(alexandria);
MATCH (alexandria:Trade_node {name:"alexandria"}), (ABB:Country {name:"ABB"}) CREATE (ABB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.635,calculated_trading_power: 1.635}]->(alexandria);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (MOL:Country {name:"MOL"}) CREATE (MOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.253,calculated_trading_power: 11.253}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 19.063,calculated_trading_power: 19.063}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (CRI:Country {name:"CRI"}) CREATE (CRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.248,calculated_trading_power: 24.248}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (GEO:Country {name:"GEO"}) CREATE (GEO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.186,calculated_trading_power: 14.186}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 68.561,calculated_trading_power: 68.561}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (ZAZ:Country {name:"ZAZ"}) CREATE (ZAZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.89,calculated_trading_power: 4.89}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (AVR:Country {name:"AVR"}) CREATE (AVR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.19,calculated_trading_power: 12.19}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (KZH:Country {name:"KZH"}) CREATE (KZH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.093,calculated_trading_power: 6.093}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.31,calculated_trading_power: 2.31}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (CIR:Country {name:"CIR"}) CREATE (CIR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.301,calculated_trading_power: 6.301}]->(astrakhan);
MATCH (astrakhan:Trade_node {name:"astrakhan"}), (GAZ:Country {name:"GAZ"}) CREATE (GAZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 56.291,calculated_trading_power: 56.291}]->(astrakhan);
MATCH (crimea:Trade_node {name:"crimea"}), (MOL:Country {name:"MOL"}) CREATE (MOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.608,calculated_trading_power: 15.608}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (WAL:Country {name:"WAL"}) CREATE (WAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 21.441,calculated_trading_power: 21.441}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 160.836,calculated_trading_power: 160.836}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.817,calculated_trading_power: 10.817}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.129,calculated_trading_power: 14.129}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (SLZ:Country {name:"SLZ"}) CREATE (SLZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.725,calculated_trading_power: 6.725}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (CRI:Country {name:"CRI"}) CREATE (CRI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 36.393,calculated_trading_power: 36.393}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (GEO:Country {name:"GEO"}) CREATE (GEO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 34.887,calculated_trading_power: 34.887}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 32.816,calculated_trading_power: 32.816}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (ZAZ:Country {name:"ZAZ"}) CREATE (ZAZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.094,calculated_trading_power: 10.094}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (PER:Country {name:"PER"}) CREATE (PER)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.551,calculated_trading_power: 11.551}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (CIR:Country {name:"CIR"}) CREATE (CIR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 19.786,calculated_trading_power: 19.786}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (GAZ:Country {name:"GAZ"}) CREATE (GAZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.093,calculated_trading_power: 11.093}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (IME:Country {name:"IME"}) CREATE (IME)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.59,calculated_trading_power: 11.59}]->(crimea);
MATCH (crimea:Trade_node {name:"crimea"}), (REG:Country {name:"REG"}) CREATE (REG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(crimea);
MATCH (constantinople:Trade_node {name:"constantinople"}), (MON:Country {name:"MON"}) CREATE (MON)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.972,calculated_trading_power: 1.972}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (RAG:Country {name:"RAG"}) CREATE (RAG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 37.922,calculated_trading_power: 37.922}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (WAL:Country {name:"WAL"}) CREATE (WAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.986,calculated_trading_power: 1.986}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 270.341,calculated_trading_power: 270.341}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.964,calculated_trading_power: 2.964}]->(constantinople);
MATCH (constantinople:Trade_node {name:"constantinople"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.756,calculated_trading_power: 12.756}]->(constantinople);
MATCH (kiev:Trade_node {name:"kiev"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.51,calculated_trading_power: 6.51}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 172.791,calculated_trading_power: 172.791}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (BAV:Country {name:"BAV"}) CREATE (BAV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 32.837,calculated_trading_power: 32.837}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.049,calculated_trading_power: 4.049}]->(kiev);
MATCH (kiev:Trade_node {name:"kiev"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 235.16,calculated_trading_power: 235.16}]->(kiev);
MATCH (kazan:Trade_node {name:"kazan"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.51,calculated_trading_power: 6.51}]->(kazan);
MATCH (kazan:Trade_node {name:"kazan"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 263.415,calculated_trading_power: 263.415}]->(kazan);
MATCH (novgorod:Trade_node {name:"novgorod"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 56.385,calculated_trading_power: 56.385}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.924,calculated_trading_power: 1.924}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (SCO:Country {name:"SCO"}) CREATE (SCO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.012,calculated_trading_power: 2.012}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (PRU:Country {name:"PRU"}) CREATE (PRU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.075,calculated_trading_power: 8.075}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (KUR:Country {name:"KUR"}) CREATE (KUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 0.694,calculated_trading_power: 0.694}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.533,calculated_trading_power: 16.533}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.042,calculated_trading_power: 2.042}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.322,calculated_trading_power: 2.322}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 256.69,calculated_trading_power: 256.69}]->(novgorod);
MATCH (novgorod:Trade_node {name:"novgorod"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.038,calculated_trading_power: 24.038}]->(novgorod);
MATCH (laplata:Trade_node {name:"laplata"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.33,calculated_trading_power: 5.33}]->(laplata);
MATCH (laplata:Trade_node {name:"laplata"}), (GUA:Country {name:"GUA"}) CREATE (GUA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.296,calculated_trading_power: 8.296}]->(laplata);
MATCH (laplata:Trade_node {name:"laplata"}), (C05:Country {name:"C05"}) CREATE (C05)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 57.333,calculated_trading_power: 57.333}]->(laplata);
MATCH (laplata:Trade_node {name:"laplata"}), (C12:Country {name:"C12"}) CREATE (C12)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 51.293,calculated_trading_power: 51.293}]->(laplata);
MATCH (brazil:Trade_node {name:"brazil"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.235,calculated_trading_power: 6.235}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.057,calculated_trading_power: 6.057}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.341,calculated_trading_power: 4.341}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (SYO:Country {name:"SYO"}) CREATE (SYO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.877,calculated_trading_power: 3.877}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (KBU:Country {name:"KBU"}) CREATE (KBU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.384,calculated_trading_power: 2.384}]->(brazil);
MATCH (brazil:Trade_node {name:"brazil"}), (C05:Country {name:"C05"}) CREATE (C05)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 244.462,calculated_trading_power: 244.462}]->(brazil);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.235,calculated_trading_power: 6.235}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (ALG:Country {name:"ALG"}) CREATE (ALG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 69.647,calculated_trading_power: 69.647}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 80.205,calculated_trading_power: 80.205}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (ASH:Country {name:"ASH"}) CREATE (ASH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.132,calculated_trading_power: 14.132}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.007,calculated_trading_power: 16.007}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (MAL:Country {name:"MAL"}) CREATE (MAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.964,calculated_trading_power: 12.964}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (SON:Country {name:"SON"}) CREATE (SON)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 25.931,calculated_trading_power: 25.931}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (OYO:Country {name:"OYO"}) CREATE (OYO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.264,calculated_trading_power: 2.264}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (SOF:Country {name:"SOF"}) CREATE (SOF)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 106.237,calculated_trading_power: 106.237}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.422,calculated_trading_power: 13.422}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (WGD:Country {name:"WGD"}) CREATE (WGD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.29,calculated_trading_power: 20.29}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (GUR:Country {name:"GUR"}) CREATE (GUR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 11.709,calculated_trading_power: 11.709}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (SYO:Country {name:"SYO"}) CREATE (SYO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.877,calculated_trading_power: 3.877}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (AIR:Country {name:"AIR"}) CREATE (AIR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.771,calculated_trading_power: 10.771}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (BON:Country {name:"BON"}) CREATE (BON)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 17.071,calculated_trading_power: 17.071}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (DAH:Country {name:"DAH"}) CREATE (DAH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.846,calculated_trading_power: 10.846}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (DGB:Country {name:"DGB"}) CREATE (DGB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.86,calculated_trading_power: 12.86}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (FUL:Country {name:"FUL"}) CREATE (FUL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 65.313,calculated_trading_power: 65.313}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (JNN:Country {name:"JNN"}) CREATE (JNN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 42.537,calculated_trading_power: 42.537}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (KBU:Country {name:"KBU"}) CREATE (KBU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.589,calculated_trading_power: 8.589}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (KNG:Country {name:"KNG"}) CREATE (KNG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.542,calculated_trading_power: 18.542}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (TMB:Country {name:"TMB"}) CREATE (TMB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 62.979,calculated_trading_power: 62.979}]->(timbuktu);
MATCH (timbuktu:Trade_node {name:"timbuktu"}), (YAT:Country {name:"YAT"}) CREATE (YAT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.923,calculated_trading_power: 18.923}]->(timbuktu);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.065,calculated_trading_power: 11.065}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 44.007,calculated_trading_power: 44.007}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (PRU:Country {name:"PRU"}) CREATE (PRU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.078,calculated_trading_power: 1.078}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 45.669,calculated_trading_power: 45.669}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.572,calculated_trading_power: 3.572}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 58.981,calculated_trading_power: 58.981}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 65.291,calculated_trading_power: 65.291}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 46.095,calculated_trading_power: 46.095}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.384,calculated_trading_power: 5.384}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (ASH:Country {name:"ASH"}) CREATE (ASH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.666,calculated_trading_power: 1.666}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (BEN:Country {name:"BEN"}) CREATE (BEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 63.991,calculated_trading_power: 63.991}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (KON:Country {name:"KON"}) CREATE (KON)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.274,calculated_trading_power: 20.274}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (LOA:Country {name:"LOA"}) CREATE (LOA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.865,calculated_trading_power: 35.865}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (JOL:Country {name:"JOL"}) CREATE (JOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 54.915,calculated_trading_power: 54.915}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (SYO:Country {name:"SYO"}) CREATE (SYO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 52.364,calculated_trading_power: 52.364}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (FUL:Country {name:"FUL"}) CREATE (FUL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.052,calculated_trading_power: 23.052}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (KBU:Country {name:"KBU"}) CREATE (KBU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.992,calculated_trading_power: 18.992}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.324,calculated_trading_power: 10.324}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (C09:Country {name:"C09"}) CREATE (C09)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 43.45,calculated_trading_power: 43.45}]->(ivory_coast);
MATCH (ivory_coast:Trade_node {name:"ivory_coast"}), (C10:Country {name:"C10"}) CREATE (C10)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.37,calculated_trading_power: 16.37}]->(ivory_coast);
MATCH (tunis:Trade_node {name:"tunis"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.734,calculated_trading_power: 12.734}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 25.095,calculated_trading_power: 25.095}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 97.061,calculated_trading_power: 97.061}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 42.77,calculated_trading_power: 42.77}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.964,calculated_trading_power: 5.964}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.453,calculated_trading_power: 3.453}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.747,calculated_trading_power: 10.747}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.105,calculated_trading_power: 3.105}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (ALG:Country {name:"ALG"}) CREATE (ALG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.98,calculated_trading_power: 13.98}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.384,calculated_trading_power: 5.384}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (TRP:Country {name:"TRP"}) CREATE (TRP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 44.0,calculated_trading_power: 44.0}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (TUN:Country {name:"TUN"}) CREATE (TUN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 107.315,calculated_trading_power: 107.315}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (KBA:Country {name:"KBA"}) CREATE (KBA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.838,calculated_trading_power: 21.838}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (TGT:Country {name:"TGT"}) CREATE (TGT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.313,calculated_trading_power: 9.313}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (GHD:Country {name:"GHD"}) CREATE (GHD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.402,calculated_trading_power: 8.402}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (FZA:Country {name:"FZA"}) CREATE (FZA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 10.927,calculated_trading_power: 10.927}]->(tunis);
MATCH (tunis:Trade_node {name:"tunis"}), (MZB:Country {name:"MZB"}) CREATE (MZB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.432,calculated_trading_power: 9.432}]->(tunis);
MATCH (ragusa:Trade_node {name:"ragusa"}), (MON:Country {name:"MON"}) CREATE (MON)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.013,calculated_trading_power: 9.013}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (RAG:Country {name:"RAG"}) CREATE (RAG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 89.663,calculated_trading_power: 89.663}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (WAL:Country {name:"WAL"}) CREATE (WAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 21.441,calculated_trading_power: 21.441}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 105.42,calculated_trading_power: 105.42}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.314,calculated_trading_power: 8.314}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 31.062,calculated_trading_power: 31.062}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (ULM:Country {name:"ULM"}) CREATE (ULM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.104,calculated_trading_power: 6.104}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 32.134,calculated_trading_power: 32.134}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 42.77,calculated_trading_power: 42.77}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.335,calculated_trading_power: 4.335}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (MOD:Country {name:"MOD"}) CREATE (MOD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.048,calculated_trading_power: 2.048}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 37.32,calculated_trading_power: 37.32}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (PAR:Country {name:"PAR"}) CREATE (PAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.028,calculated_trading_power: 2.028}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.023,calculated_trading_power: 24.023}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.863,calculated_trading_power: 12.863}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 72.713,calculated_trading_power: 72.713}]->(ragusa);
MATCH (ragusa:Trade_node {name:"ragusa"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.147,calculated_trading_power: 5.147}]->(ragusa);
MATCH (safi:Trade_node {name:"safi"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 25.095,calculated_trading_power: 25.095}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 55.567,calculated_trading_power: 55.567}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (ALG:Country {name:"ALG"}) CREATE (ALG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 75.385,calculated_trading_power: 75.385}]->(safi);
MATCH (safi:Trade_node {name:"safi"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 103.011,calculated_trading_power: 103.011}]->(safi);
MATCH (pest:Trade_node {name:"pest"}), (WAL:Country {name:"WAL"}) CREATE (WAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 56.417,calculated_trading_power: 56.417}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (TUR:Country {name:"TUR"}) CREATE (TUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.421,calculated_trading_power: 6.421}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 118.704,calculated_trading_power: 118.704}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (BAV:Country {name:"BAV"}) CREATE (BAV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 37.506,calculated_trading_power: 37.506}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 142.779,calculated_trading_power: 142.779}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (SAX:Country {name:"SAX"}) CREATE (SAX)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 50.04,calculated_trading_power: 50.04}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (SLZ:Country {name:"SLZ"}) CREATE (SLZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.772,calculated_trading_power: 8.772}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.194,calculated_trading_power: 9.194}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (ULM:Country {name:"ULM"}) CREATE (ULM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.336,calculated_trading_power: 8.336}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (WUR:Country {name:"WUR"}) CREATE (WUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.466,calculated_trading_power: 8.466}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (MEM:Country {name:"MEM"}) CREATE (MEM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.522,calculated_trading_power: 5.522}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 113.344,calculated_trading_power: 113.344}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (PSS:Country {name:"PSS"}) CREATE (PSS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.682,calculated_trading_power: 5.682}]->(pest);
MATCH (pest:Trade_node {name:"pest"}), (REG:Country {name:"REG"}) CREATE (REG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.479,calculated_trading_power: 10.479}]->(pest);
MATCH (krakow:Trade_node {name:"krakow"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 74.481,calculated_trading_power: 74.481}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (PRU:Country {name:"PRU"}) CREATE (PRU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 117.72,calculated_trading_power: 117.72}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 232.835,calculated_trading_power: 232.835}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (ANH:Country {name:"ANH"}) CREATE (ANH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.436,calculated_trading_power: 4.436}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (AUG:Country {name:"AUG"}) CREATE (AUG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.75,calculated_trading_power: 7.75}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (BAV:Country {name:"BAV"}) CREATE (BAV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 37.506,calculated_trading_power: 37.506}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (BRU:Country {name:"BRU"}) CREATE (BRU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.874,calculated_trading_power: 6.874}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (FRN:Country {name:"FRN"}) CREATE (FRN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.12,calculated_trading_power: 10.12}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 95.359,calculated_trading_power: 95.359}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (HAN:Country {name:"HAN"}) CREATE (HAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.807,calculated_trading_power: 11.807}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (HES:Country {name:"HES"}) CREATE (HES)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.659,calculated_trading_power: 16.659}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (KOL:Country {name:"KOL"}) CREATE (KOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.733,calculated_trading_power: 9.733}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (MAI:Country {name:"MAI"}) CREATE (MAI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.65,calculated_trading_power: 7.65}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (PAL:Country {name:"PAL"}) CREATE (PAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 21.168,calculated_trading_power: 21.168}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (SAX:Country {name:"SAX"}) CREATE (SAX)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 55.207,calculated_trading_power: 55.207}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (SLZ:Country {name:"SLZ"}) CREATE (SLZ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.772,calculated_trading_power: 8.772}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.338,calculated_trading_power: 11.338}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (ULM:Country {name:"ULM"}) CREATE (ULM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.336,calculated_trading_power: 8.336}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (WUR:Country {name:"WUR"}) CREATE (WUR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.466,calculated_trading_power: 8.466}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (NUM:Country {name:"NUM"}) CREATE (NUM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.432,calculated_trading_power: 8.432}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (MEM:Country {name:"MEM"}) CREATE (MEM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.522,calculated_trading_power: 5.522}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (RVA:Country {name:"RVA"}) CREATE (RVA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.066,calculated_trading_power: 6.066}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.468,calculated_trading_power: 12.468}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 113.344,calculated_trading_power: 113.344}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (PSS:Country {name:"PSS"}) CREATE (PSS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.682,calculated_trading_power: 5.682}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (REG:Country {name:"REG"}) CREATE (REG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.479,calculated_trading_power: 10.479}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (GOS:Country {name:"GOS"}) CREATE (GOS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.884,calculated_trading_power: 4.884}]->(krakow);
MATCH (krakow:Trade_node {name:"krakow"}), (TNT:Country {name:"TNT"}) CREATE (TNT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.721,calculated_trading_power: 4.721}]->(krakow);
MATCH (wien:Trade_node {name:"wien"}), (PRU:Country {name:"PRU"}) CREATE (PRU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 109.644,calculated_trading_power: 109.644}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.05,calculated_trading_power: 3.05}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (AAC:Country {name:"AAC"}) CREATE (AAC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ANH:Country {name:"ANH"}) CREATE (ANH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.436,calculated_trading_power: 4.436}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ANS:Country {name:"ANS"}) CREATE (ANS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (AUG:Country {name:"AUG"}) CREATE (AUG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.982,calculated_trading_power: 18.982}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BAD:Country {name:"BAD"}) CREATE (BAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.415,calculated_trading_power: 8.415}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BAV:Country {name:"BAV"}) CREATE (BAV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 79.118,calculated_trading_power: 79.118}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BRU:Country {name:"BRU"}) CREATE (BRU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.874,calculated_trading_power: 6.874}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (FRN:Country {name:"FRN"}) CREATE (FRN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.912,calculated_trading_power: 14.912}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 137.803,calculated_trading_power: 137.803}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (HES:Country {name:"HES"}) CREATE (HES)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.94,calculated_trading_power: 18.94}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (KOL:Country {name:"KOL"}) CREATE (KOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.556,calculated_trading_power: 13.556}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MAI:Country {name:"MAI"}) CREATE (MAI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.82,calculated_trading_power: 9.82}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MUN:Country {name:"MUN"}) CREATE (MUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.923,calculated_trading_power: 15.923}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (PAL:Country {name:"PAL"}) CREATE (PAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.146,calculated_trading_power: 24.146}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (SAX:Country {name:"SAX"}) CREATE (SAX)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 55.207,calculated_trading_power: 55.207}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (SLZ:Country {name:"SLZ"}) CREATE (SLZ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 28.544,calculated_trading_power: 28.544}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 62.552,calculated_trading_power: 62.552}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.338,calculated_trading_power: 11.338}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (TRI:Country {name:"TRI"}) CREATE (TRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.761,calculated_trading_power: 8.761}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ULM:Country {name:"ULM"}) CREATE (ULM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.065,calculated_trading_power: 29.065}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (WBG:Country {name:"WBG"}) CREATE (WBG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.413,calculated_trading_power: 7.413}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (WUR:Country {name:"WUR"}) CREATE (WUR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.194,calculated_trading_power: 22.194}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (NUM:Country {name:"NUM"}) CREATE (NUM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.733,calculated_trading_power: 12.733}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MEM:Country {name:"MEM"}) CREATE (MEM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.608,calculated_trading_power: 16.608}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (NSA:Country {name:"NSA"}) CREATE (NSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.31,calculated_trading_power: 4.31}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (RVA:Country {name:"RVA"}) CREATE (RVA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.609,calculated_trading_power: 8.609}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.952,calculated_trading_power: 3.952}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.781,calculated_trading_power: 14.781}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MOD:Country {name:"MOD"}) CREATE (MOD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.144,calculated_trading_power: 6.144}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.259,calculated_trading_power: 10.259}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (PAR:Country {name:"PAR"}) CREATE (PAR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.098,calculated_trading_power: 7.098}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 142.266,calculated_trading_power: 142.266}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (PSS:Country {name:"PSS"}) CREATE (PSS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.271,calculated_trading_power: 16.271}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (ROT:Country {name:"ROT"}) CREATE (ROT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.391,calculated_trading_power: 5.391}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BYT:Country {name:"BYT"}) CREATE (BYT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (REG:Country {name:"REG"}) CREATE (REG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 41.287,calculated_trading_power: 41.287}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (TTL:Country {name:"TTL"}) CREATE (TTL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.497,calculated_trading_power: 7.497}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (GOS:Country {name:"GOS"}) CREATE (GOS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.884,calculated_trading_power: 4.884}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (TNT:Country {name:"TNT"}) CREATE (TNT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.239,calculated_trading_power: 13.239}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (MLH:Country {name:"MLH"}) CREATE (MLH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(wien);
MATCH (wien:Trade_node {name:"wien"}), (BAM:Country {name:"BAM"}) CREATE (BAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(wien);
MATCH (saxony:Trade_node {name:"saxony"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.789,calculated_trading_power: 12.789}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 85.413,calculated_trading_power: 85.413}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.138,calculated_trading_power: 12.138}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (PRU:Country {name:"PRU"}) CREATE (PRU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 166.052,calculated_trading_power: 166.052}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.05,calculated_trading_power: 3.05}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (AAC:Country {name:"AAC"}) CREATE (AAC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 6.402,calculated_trading_power: 6.402}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (ANH:Country {name:"ANH"}) CREATE (ANH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.823,calculated_trading_power: 13.823}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (ANS:Country {name:"ANS"}) CREATE (ANS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.717,calculated_trading_power: 4.717}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BAD:Country {name:"BAD"}) CREATE (BAD)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.415,calculated_trading_power: 8.415}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.247,calculated_trading_power: 7.247}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BRU:Country {name:"BRU"}) CREATE (BRU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.793,calculated_trading_power: 18.793}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (FRN:Country {name:"FRN"}) CREATE (FRN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.912,calculated_trading_power: 14.912}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.634,calculated_trading_power: 18.634}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.223,calculated_trading_power: 10.223}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (HAN:Country {name:"HAN"}) CREATE (HAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.134,calculated_trading_power: 20.134}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (HES:Country {name:"HES"}) CREATE (HES)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 18.94,calculated_trading_power: 18.94}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (KOL:Country {name:"KOL"}) CREATE (KOL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.556,calculated_trading_power: 13.556}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (LUN:Country {name:"LUN"}) CREATE (LUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.839,calculated_trading_power: 8.839}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (MAI:Country {name:"MAI"}) CREATE (MAI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.82,calculated_trading_power: 9.82}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (MKL:Country {name:"MKL"}) CREATE (MKL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.888,calculated_trading_power: 8.888}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (MUN:Country {name:"MUN"}) CREATE (MUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.923,calculated_trading_power: 15.923}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (PAL:Country {name:"PAL"}) CREATE (PAL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.146,calculated_trading_power: 24.146}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (SAX:Country {name:"SAX"}) CREATE (SAX)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 106.37,calculated_trading_power: 106.37}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 62.552,calculated_trading_power: 62.552}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.659,calculated_trading_power: 32.659}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (TRI:Country {name:"TRI"}) CREATE (TRI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.761,calculated_trading_power: 8.761}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (WBG:Country {name:"WBG"}) CREATE (WBG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.413,calculated_trading_power: 7.413}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (NUM:Country {name:"NUM"}) CREATE (NUM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.733,calculated_trading_power: 12.733}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (NSA:Country {name:"NSA"}) CREATE (NSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.31,calculated_trading_power: 4.31}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (RVA:Country {name:"RVA"}) CREATE (RVA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.609,calculated_trading_power: 8.609}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 47.977,calculated_trading_power: 47.977}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (ROT:Country {name:"ROT"}) CREATE (ROT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.391,calculated_trading_power: 5.391}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BYT:Country {name:"BYT"}) CREATE (BYT)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (TTL:Country {name:"TTL"}) CREATE (TTL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.497,calculated_trading_power: 7.497}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (GOS:Country {name:"GOS"}) CREATE (GOS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 15.299,calculated_trading_power: 15.299}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (MLH:Country {name:"MLH"}) CREATE (MLH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.707,calculated_trading_power: 4.707}]->(saxony);
MATCH (saxony:Trade_node {name:"saxony"}), (BAM:Country {name:"BAM"}) CREATE (BAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.972,calculated_trading_power: 3.972}]->(saxony);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 126.885,calculated_trading_power: 126.885}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 25.613,calculated_trading_power: 25.613}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.763,calculated_trading_power: 4.763}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (PRU:Country {name:"PRU"}) CREATE (PRU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 30.595,calculated_trading_power: 30.595}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (KUR:Country {name:"KUR"}) CREATE (KUR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 1.735,calculated_trading_power: 1.735}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (PLC:Country {name:"PLC"}) CREATE (PLC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 37.221,calculated_trading_power: 37.221}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.247,calculated_trading_power: 7.247}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.223,calculated_trading_power: 10.223}]->(baltic_sea);
MATCH (baltic_sea:Trade_node {name:"baltic_sea"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.993,calculated_trading_power: 34.993}]->(baltic_sea);
MATCH (rheinland:Trade_node {name:"rheinland"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.832,calculated_trading_power: 14.832}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 85.413,calculated_trading_power: 85.413}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.138,calculated_trading_power: 12.138}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (PRU:Country {name:"PRU"}) CREATE (PRU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.523,calculated_trading_power: 1.523}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 49.652,calculated_trading_power: 49.652}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (AAC:Country {name:"AAC"}) CREATE (AAC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 18.762,calculated_trading_power: 18.762}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (ANS:Country {name:"ANS"}) CREATE (ANS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.224,calculated_trading_power: 14.224}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BAD:Country {name:"BAD"}) CREATE (BAD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 21.105,calculated_trading_power: 21.105}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.734,calculated_trading_power: 14.734}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (FRN:Country {name:"FRN"}) CREATE (FRN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 50.748,calculated_trading_power: 50.748}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.929,calculated_trading_power: 2.929}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 19.898,calculated_trading_power: 19.898}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HES:Country {name:"HES"}) CREATE (HES)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 42.917,calculated_trading_power: 42.917}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (KOL:Country {name:"KOL"}) CREATE (KOL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 43.975,calculated_trading_power: 43.975}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (LOR:Country {name:"LOR"}) CREATE (LOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.082,calculated_trading_power: 12.082}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (MAI:Country {name:"MAI"}) CREATE (MAI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.553,calculated_trading_power: 30.553}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (MUN:Country {name:"MUN"}) CREATE (MUN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 37.898,calculated_trading_power: 37.898}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (PAL:Country {name:"PAL"}) CREATE (PAL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 53.333,calculated_trading_power: 53.333}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 77.68,calculated_trading_power: 77.68}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (THU:Country {name:"THU"}) CREATE (THU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.263,calculated_trading_power: 1.263}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (TRI:Country {name:"TRI"}) CREATE (TRI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 22.846,calculated_trading_power: 22.846}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (WBG:Country {name:"WBG"}) CREATE (WBG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.275,calculated_trading_power: 20.275}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (NUM:Country {name:"NUM"}) CREATE (NUM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 45.375,calculated_trading_power: 45.375}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (NSA:Country {name:"NSA"}) CREATE (NSA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.339,calculated_trading_power: 14.339}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (RVA:Country {name:"RVA"}) CREATE (RVA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.913,calculated_trading_power: 30.913}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.39,calculated_trading_power: 3.39}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 47.977,calculated_trading_power: 47.977}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (ROT:Country {name:"ROT"}) CREATE (ROT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.63,calculated_trading_power: 16.63}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BYT:Country {name:"BYT"}) CREATE (BYT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.598,calculated_trading_power: 12.598}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (TTL:Country {name:"TTL"}) CREATE (TTL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.388,calculated_trading_power: 20.388}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (MLH:Country {name:"MLH"}) CREATE (MLH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 14.2,calculated_trading_power: 14.2}]->(rheinland);
MATCH (rheinland:Trade_node {name:"rheinland"}), (BAM:Country {name:"BAM"}) CREATE (BAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.598,calculated_trading_power: 12.598}]->(rheinland);
MATCH (panama:Trade_node {name:"panama"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.288,calculated_trading_power: 5.288}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 1.51,calculated_trading_power: 1.51}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.593,calculated_trading_power: 2.593}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (MIS:Country {name:"MIS"}) CREATE (MIS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 26.927,calculated_trading_power: 26.927}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.005,calculated_trading_power: 34.005}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (C08:Country {name:"C08"}) CREATE (C08)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.021,calculated_trading_power: 16.021}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (C09:Country {name:"C09"}) CREATE (C09)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 43.45,calculated_trading_power: 43.45}]->(panama);
MATCH (panama:Trade_node {name:"panama"}), (C10:Country {name:"C10"}) CREATE (C10)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 90.747,calculated_trading_power: 90.747}]->(panama);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.336,calculated_trading_power: 3.336}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.459,calculated_trading_power: 14.459}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 30.577,calculated_trading_power: 30.577}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 27.801,calculated_trading_power: 27.801}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 52.401,calculated_trading_power: 52.401}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.968,calculated_trading_power: 12.968}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.384,calculated_trading_power: 5.384}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (C01:Country {name:"C01"}) CREATE (C01)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 62.424,calculated_trading_power: 62.424}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (C02:Country {name:"C02"}) CREATE (C02)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 23.698,calculated_trading_power: 23.698}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (C04:Country {name:"C04"}) CREATE (C04)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 111.804,calculated_trading_power: 111.804}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (C06:Country {name:"C06"}) CREATE (C06)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 32.986,calculated_trading_power: 32.986}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (C09:Country {name:"C09"}) CREATE (C09)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 185.277,calculated_trading_power: 185.277}]->(carribean_trade);
MATCH (carribean_trade:Trade_node {name:"carribean_trade"}), (C10:Country {name:"C10"}) CREATE (C10)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 33.508,calculated_trading_power: 33.508}]->(carribean_trade);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 37.357,calculated_trading_power: 37.357}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.091,calculated_trading_power: 15.091}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.572,calculated_trading_power: 3.572}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.907,calculated_trading_power: 15.907}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.471,calculated_trading_power: 34.471}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (CHE:Country {name:"CHE"}) CREATE (CHE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.54,calculated_trading_power: 7.54}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ABE:Country {name:"ABE"}) CREATE (ABE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.637,calculated_trading_power: 8.637}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (MAH:Country {name:"MAH"}) CREATE (MAH)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (MIK:Country {name:"MIK"}) CREATE (MIK)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ALT:Country {name:"ALT"}) CREATE (ALT)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.785,calculated_trading_power: 7.785}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (ICH:Country {name:"ICH"}) CREATE (ICH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.785,calculated_trading_power: 7.785}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (COF:Country {name:"COF"}) CREATE (COF)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.851,calculated_trading_power: 7.851}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (JOA:Country {name:"JOA"}) CREATE (JOA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.851,calculated_trading_power: 7.851}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (KSI:Country {name:"KSI"}) CREATE (KSI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.366,calculated_trading_power: 8.366}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (TSC:Country {name:"TSC"}) CREATE (TSC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.851,calculated_trading_power: 7.851}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (PEN:Country {name:"PEN"}) CREATE (PEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (MLS:Country {name:"MLS"}) CREATE (MLS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (INN:Country {name:"INN"}) CREATE (INN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (AGQ:Country {name:"AGQ"}) CREATE (AGQ)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.018,calculated_trading_power: 2.018}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (C00:Country {name:"C00"}) CREATE (C00)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.312,calculated_trading_power: 3.312}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (C01:Country {name:"C01"}) CREATE (C01)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 226.196,calculated_trading_power: 226.196}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (C02:Country {name:"C02"}) CREATE (C02)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 50.02,calculated_trading_power: 50.02}]->(chesapeake_bay);
MATCH (chesapeake_bay:Trade_node {name:"chesapeake_bay"}), (C06:Country {name:"C06"}) CREATE (C06)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 35.786,calculated_trading_power: 35.786}]->(chesapeake_bay);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 8.398,calculated_trading_power: 8.398}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.676,calculated_trading_power: 12.676}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (SCO:Country {name:"SCO"}) CREATE (SCO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.225,calculated_trading_power: 12.225}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 16.513,calculated_trading_power: 16.513}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (MAH:Country {name:"MAH"}) CREATE (MAH)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.206,calculated_trading_power: 8.206}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (MIK:Country {name:"MIK"}) CREATE (MIK)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 39.169,calculated_trading_power: 39.169}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (PEN:Country {name:"PEN"}) CREATE (PEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 39.169,calculated_trading_power: 39.169}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (MLS:Country {name:"MLS"}) CREATE (MLS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 9.493,calculated_trading_power: 9.493}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (INN:Country {name:"INN"}) CREATE (INN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 7.931,calculated_trading_power: 7.931}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (AGQ:Country {name:"AGQ"}) CREATE (AGQ)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 8.14,calculated_trading_power: 8.14}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (C00:Country {name:"C00"}) CREATE (C00)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 20.136,calculated_trading_power: 20.136}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (C01:Country {name:"C01"}) CREATE (C01)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 14.049,calculated_trading_power: 14.049}]->(st_lawrence);
MATCH (st_lawrence:Trade_node {name:"st_lawrence"}), (C02:Country {name:"C02"}) CREATE (C02)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 162.283,calculated_trading_power: 162.283}]->(st_lawrence);
MATCH (white_sea:Trade_node {name:"white_sea"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 0.441,calculated_trading_power: 0.441}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.095,calculated_trading_power: 9.095}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.676,calculated_trading_power: 12.676}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (SCO:Country {name:"SCO"}) CREATE (SCO)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.225,calculated_trading_power: 12.225}]->(white_sea);
MATCH (white_sea:Trade_node {name:"white_sea"}), (RUS:Country {name:"RUS"}) CREATE (RUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 43.278,calculated_trading_power: 43.278}]->(white_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.524,calculated_trading_power: 13.524}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 67.606,calculated_trading_power: 67.606}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.763,calculated_trading_power: 4.763}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 100.74,calculated_trading_power: 100.74}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SCO:Country {name:"SCO"}) CREATE (SCO)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 75.441,calculated_trading_power: 75.441}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.091,calculated_trading_power: 15.091}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.289,calculated_trading_power: 9.289}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.594,calculated_trading_power: 5.594}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.545,calculated_trading_power: 12.545}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (HAN:Country {name:"HAN"}) CREATE (HAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.214,calculated_trading_power: 2.214}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (LUN:Country {name:"LUN"}) CREATE (LUN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.04,calculated_trading_power: 2.04}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (MKL:Country {name:"MKL"}) CREATE (MKL)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.222,calculated_trading_power: 2.222}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.889,calculated_trading_power: 12.889}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (LIE:Country {name:"LIE"}) CREATE (LIE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.024,calculated_trading_power: 2.024}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.964,calculated_trading_power: 17.964}]->(north_sea);
MATCH (north_sea:Trade_node {name:"north_sea"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.993,calculated_trading_power: 34.993}]->(north_sea);
MATCH (lubeck:Trade_node {name:"lubeck"}), (SWE:Country {name:"SWE"}) CREATE (SWE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 33.019,calculated_trading_power: 33.019}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (DAN:Country {name:"DAN"}) CREATE (DAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 183.339,calculated_trading_power: 183.339}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (SHL:Country {name:"SHL"}) CREATE (SHL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 69.826,calculated_trading_power: 69.826}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 34.903,calculated_trading_power: 34.903}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (PRU:Country {name:"PRU"}) CREATE (PRU)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 13.936,calculated_trading_power: 13.936}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 15.091,calculated_trading_power: 15.091}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (BRE:Country {name:"BRE"}) CREATE (BRE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 88.493,calculated_trading_power: 88.493}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.594,calculated_trading_power: 5.594}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (HAM:Country {name:"HAM"}) CREATE (HAM)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 120.576,calculated_trading_power: 120.576}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (HAN:Country {name:"HAN"}) CREATE (HAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.391,calculated_trading_power: 2.391}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (LUN:Country {name:"LUN"}) CREATE (LUN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 16.714,calculated_trading_power: 16.714}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (MKL:Country {name:"MKL"}) CREATE (MKL)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 29.733,calculated_trading_power: 29.733}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.889,calculated_trading_power: 12.889}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 17.964,calculated_trading_power: 17.964}]->(lubeck);
MATCH (lubeck:Trade_node {name:"lubeck"}), (HSA:Country {name:"HSA"}) CREATE (HSA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 156.45,calculated_trading_power: 156.45}]->(lubeck);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 250.257,calculated_trading_power: 250.257}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (LOR:Country {name:"LOR"}) CREATE (LOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 12.082,calculated_trading_power: 12.082}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 67.292,calculated_trading_power: 67.292}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.71,calculated_trading_power: 10.71}]->(bordeaux);
MATCH (bordeaux:Trade_node {name:"bordeaux"}), (LIE:Country {name:"LIE"}) CREATE (LIE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 11.132,calculated_trading_power: 11.132}]->(bordeaux);
MATCH (sevilla:Trade_node {name:"sevilla"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 4.42,calculated_trading_power: 4.42}]->(sevilla);
MATCH (sevilla:Trade_node {name:"sevilla"}), (POR:Country {name:"POR"}) CREATE (POR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 134.948,calculated_trading_power: 134.948}]->(sevilla);
MATCH (sevilla:Trade_node {name:"sevilla"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 280.499,calculated_trading_power: 280.499}]->(sevilla);
MATCH (sevilla:Trade_node {name:"sevilla"}), (MOR:Country {name:"MOR"}) CREATE (MOR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 19.838,calculated_trading_power: 19.838}]->(sevilla);
MATCH (champagne:Trade_node {name:"champagne"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 98.707,calculated_trading_power: 98.707}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 261.998,calculated_trading_power: 261.998}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 7.953,calculated_trading_power: 7.953}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (LOR:Country {name:"LOR"}) CREATE (LOR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 27.712,calculated_trading_power: 27.712}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (SWI:Country {name:"SWI"}) CREATE (SWI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 140.681,calculated_trading_power: 140.681}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 30.542,calculated_trading_power: 30.542}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 72.047,calculated_trading_power: 72.047}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 89.111,calculated_trading_power: 89.111}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 67.59,calculated_trading_power: 67.59}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 40.371,calculated_trading_power: 40.371}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 9.911,calculated_trading_power: 9.911}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (LIE:Country {name:"LIE"}) CREATE (LIE)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 20.138,calculated_trading_power: 20.138}]->(champagne);
MATCH (champagne:Trade_node {name:"champagne"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 95.158,calculated_trading_power: 95.158}]->(champagne);
MATCH (valencia:Trade_node {name:"valencia"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 30.415,calculated_trading_power: 30.415}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 65.921,calculated_trading_power: 65.921}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 42.77,calculated_trading_power: 42.77}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 5.964,calculated_trading_power: 5.964}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 24.023,calculated_trading_power: 24.023}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.747,calculated_trading_power: 10.747}]->(valencia);
MATCH (valencia:Trade_node {name:"valencia"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 3.105,calculated_trading_power: 3.105}]->(valencia);
MATCH (genua:Trade_node {name:"genua"}), (KNI:Country {name:"KNI"}) CREATE (KNI)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 30.21,calculated_trading_power: 30.21}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 22.015,calculated_trading_power: 22.015}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 73.729,calculated_trading_power: 73.729}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (GEN:Country {name:"GEN"}) CREATE (GEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 232.744,calculated_trading_power: 232.744}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.964,calculated_trading_power: 2.964}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 65.815,calculated_trading_power: 65.815}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (SAV:Country {name:"SAV"}) CREATE (SAV)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 32.955,calculated_trading_power: 32.955}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (TUS:Country {name:"TUS"}) CREATE (TUS)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 87.984,calculated_trading_power: 87.984}]->(genua);
MATCH (genua:Trade_node {name:"genua"}), (LUC:Country {name:"LUC"}) CREATE (LUC)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 38.665,calculated_trading_power: 38.665}]->(genua);
MATCH (venice:Trade_node {name:"venice"}), (HAB:Country {name:"HAB"}) CREATE (HAB)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 10.56,calculated_trading_power: 10.56}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 19.76,calculated_trading_power: 19.76}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (MAN:Country {name:"MAN"}) CREATE (MAN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 24.174,calculated_trading_power: 24.174}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (MOD:Country {name:"MOD"}) CREATE (MOD)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 12.386,calculated_trading_power: 12.386}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (PAP:Country {name:"PAP"}) CREATE (PAP)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 36.227,calculated_trading_power: 36.227}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (PAR:Country {name:"PAR"}) CREATE (PAR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 13.232,calculated_trading_power: 13.232}]->(venice);
MATCH (venice:Trade_node {name:"venice"}), (VEN:Country {name:"VEN"}) CREATE (VEN)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 178.186,calculated_trading_power: 178.186}]->(venice);
MATCH (english_channel:Trade_node {name:"english_channel"}), (ENG:Country {name:"ENG"}) CREATE (ENG)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 180.656,calculated_trading_power: 180.656}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (FRA:Country {name:"FRA"}) CREATE (FRA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 38.971,calculated_trading_power: 38.971}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (EFR:Country {name:"EFR"}) CREATE (EFR)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 61.839,calculated_trading_power: 61.839}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (SPA:Country {name:"SPA"}) CREATE (SPA)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 33.758,calculated_trading_power: 33.758}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (LIE:Country {name:"LIE"}) CREATE (LIE)-[r:NodeCountry{is_home: false, has_merchant: "False",base_trading_power: 2.254,calculated_trading_power: 2.254}]->(english_channel);
MATCH (english_channel:Trade_node {name:"english_channel"}), (NED:Country {name:"NED"}) CREATE (NED)-[r:NodeCountry{is_home: true, has_merchant: "False",base_trading_power: 119.252,calculated_trading_power: 119.252}]->(english_channel);
