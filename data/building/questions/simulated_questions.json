[{"country": "USA1836", "question_num": 2, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "furniture", "target_gdb": "gdb-2hop-001", "answer": 1485}, {"country": "USA1836", "question_num": 3, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of oil?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "oil", "target_gdb": "gdb-2hop-001", "answer": 1566}, {"country": "AUS1836", "question_num": 4, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of manowars?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "manowars", "target_gdb": "gdb-2hop-001", "answer": 893}, {"country": "AUS1836", "question_num": 6, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 893}, {"country": "PRU1836", "question_num": 10, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of groceries?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "groceries", "target_gdb": "gdb-2hop-001", "answer": 1074}, {"country": "RUS1836", "question_num": 12, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "furniture", "target_gdb": "gdb-2hop-001", "answer": 1049}, {"country": "RUS1836", "question_num": 13, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of glass?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "glass", "target_gdb": "gdb-2hop-001", "answer": 1065}, {"country": "RUS1836", "question_num": 14, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of porcelain?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "porcelain", "target_gdb": "gdb-2hop-001", "answer": 957}, {"country": "RUS1836", "question_num": 16, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of luxury_furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "luxury_furniture", "target_gdb": "gdb-2hop-001", "answer": 967}, {"country": "CHI1836", "question_num": 17, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of manowars?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "manowars", "target_gdb": "gdb-2hop-001", "answer": 2465}, {"country": "CHI1836", "question_num": 19, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 2465}, {"country": "JAP1836", "question_num": 22, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "furniture", "target_gdb": "gdb-2hop-001", "answer": 2634}, {"country": "JAP1836", "question_num": 23, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 2653}, {"country": "JAP1836", "question_num": 24, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of oil?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "oil", "target_gdb": "gdb-2hop-001", "answer": 2625}, {"country": "JAP1836", "question_num": 25, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of glass?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "glass", "target_gdb": "gdb-2hop-001", "answer": 2677}, {"country": "JAP1836", "question_num": 27, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of meat?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "meat", "target_gdb": "gdb-2hop-001", "answer": 2625}, {"country": "JAP1836", "question_num": 29, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of luxury_furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "luxury_furniture", "target_gdb": "gdb-2hop-001", "answer": 2634}, {"country": "KOR1836", "question_num": 30, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of services?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "services", "target_gdb": "gdb-2hop-001", "answer": 3677}, {"country": "KOR1836", "question_num": 31, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 2691}, {"country": "USA1839", "question_num": 32, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of manowars?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "manowars", "target_gdb": "gdb-2hop-001", "answer": 1346}, {"country": "USA1839", "question_num": 33, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clothes?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clothes", "target_gdb": "gdb-2hop-001", "answer": 1417}, {"country": "USA1839", "question_num": 35, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of oil?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "oil", "target_gdb": "gdb-2hop-001", "answer": 1449}, {"country": "AUS1839", "question_num": 37, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of manowars?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "manowars", "target_gdb": "gdb-2hop-001", "answer": 818}, {"country": "AUS1839", "question_num": 40, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of gold?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "gold", "target_gdb": "gdb-2hop-001", "answer": 381}, {"country": "AUS1839", "question_num": 41, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of fine_art?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "fine_art", "target_gdb": "gdb-2hop-001", "answer": 355}, {"country": "GBR1839", "question_num": 42, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of liquor?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "liquor", "target_gdb": "gdb-2hop-001", "answer": 5471}, {"country": "RUS1839", "question_num": 44, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 936}, {"country": "RUS1839", "question_num": 47, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of fine_art?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "fine_art", "target_gdb": "gdb-2hop-001", "answer": 873}, {"country": "CHI1839", "question_num": 48, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of manowars?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "manowars", "target_gdb": "gdb-2hop-001", "answer": 2311}, {"country": "CHI1839", "question_num": 50, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 2310}, {"country": "CHI1839", "question_num": 51, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of coal?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "coal", "target_gdb": "gdb-2hop-001", "answer": 5036}, {"country": "CHI1839", "question_num": 52, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of meat?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "meat", "target_gdb": "gdb-2hop-001", "answer": 2290}, {"country": "JAP1839", "question_num": 55, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "furniture", "target_gdb": "gdb-2hop-001", "answer": 2464}, {"country": "JAP1839", "question_num": 56, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 2483}, {"country": "KOR1839", "question_num": 61, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of fish?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "fish", "target_gdb": "gdb-2hop-001", "answer": 2523}, {"country": "KOR1839", "question_num": 62, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of services?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "services", "target_gdb": "gdb-2hop-001", "answer": 3493}, {"country": "KOR1839", "question_num": 63, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of transportation?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "transportation", "target_gdb": "gdb-2hop-001", "answer": 3493}, {"country": "KOR1839", "question_num": 64, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 2521}, {"country": "KOR1839", "question_num": 65, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of meat?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "meat", "target_gdb": "gdb-2hop-001", "answer": 2536}, {"country": "NGF1849", "question_num": 66, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of groceries?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "groceries", "target_gdb": "gdb-2hop-001", "answer": 33559493}, {"country": "NGF1849", "question_num": 67, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of luxury_clothes?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "luxury_clothes", "target_gdb": "gdb-2hop-001", "answer": 472}, {"country": "USA1849", "question_num": 68, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of manowars?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "manowars", "target_gdb": "gdb-2hop-001", "answer": 1516}, {"country": "USA1849", "question_num": 70, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of oil?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "oil", "target_gdb": "gdb-2hop-001", "answer": 1518}, {"country": "USA1849", "question_num": 71, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of luxury_furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "luxury_furniture", "target_gdb": "gdb-2hop-001", "answer": 1530}, {"country": "AUS1849", "question_num": 72, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of meat?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "meat", "target_gdb": "gdb-2hop-001", "answer": 858}, {"country": "RUS1849", "question_num": 73, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of groceries?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "groceries", "target_gdb": "gdb-2hop-001", "answer": 2081}, {"country": "RUS1849", "question_num": 75, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of glass?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "glass", "target_gdb": "gdb-2hop-001", "answer": 4782}, {"country": "RUS1849", "question_num": 77, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of liquor?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "liquor", "target_gdb": "gdb-2hop-001", "answer": 966}, {"country": "RUS1849", "question_num": 78, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of luxury_clothes?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "luxury_clothes", "target_gdb": "gdb-2hop-001", "answer": 2073}, {"country": "CHI1849", "question_num": 79, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of manowars?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "manowars", "target_gdb": "gdb-2hop-001", "answer": 2399}, {"country": "CHI1849", "question_num": 81, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 2399}, {"country": "JAP1849", "question_num": 84, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "furniture", "target_gdb": "gdb-2hop-001", "answer": 2571}, {"country": "JAP1849", "question_num": 85, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 2590}, {"country": "JAP1849", "question_num": 88, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of luxury_furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "luxury_furniture", "target_gdb": "gdb-2hop-001", "answer": 2571}, {"country": "KOR1849", "question_num": 90, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 2627}, {"country": "KOR1849", "question_num": 91, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of glass?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "glass", "target_gdb": "gdb-2hop-001", "answer": 2635}, {"country": "ARG1836", "question_num": 92, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clothes?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clothes", "target_gdb": "gdb-2hop-001", "answer": 1869}, {"country": "ARG1836", "question_num": 93, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "furniture", "target_gdb": "gdb-2hop-001", "answer": 1872}, {"country": "ARG1836", "question_num": 94, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of tools?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "tools", "target_gdb": "gdb-2hop-001", "answer": 1861}, {"country": "BRZ1836", "question_num": 95, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of manowars?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "manowars", "target_gdb": "gdb-2hop-001", "answer": 1928}, {"country": "BRZ1836", "question_num": 97, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "furniture", "target_gdb": "gdb-2hop-001", "answer": 1919}, {"country": "BRZ1836", "question_num": 98, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 1928}, {"country": "BRZ1836", "question_num": 99, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of tools?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "tools", "target_gdb": "gdb-2hop-001", "answer": 1909}, {"country": "CHL1836", "question_num": 100, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of services?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "services", "target_gdb": "gdb-2hop-001", "answer": 3197}, {"country": "CHL1836", "question_num": 101, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of iron?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "iron", "target_gdb": "gdb-2hop-001", "answer": 1882}, {"country": "CLM1836", "question_num": 102, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clothes?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clothes", "target_gdb": "gdb-2hop-001", "answer": 1788}, {"country": "CLM1836", "question_num": 103, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "furniture", "target_gdb": "gdb-2hop-001", "answer": 1793}, {"country": "FIN1836", "question_num": 105, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "furniture", "target_gdb": "gdb-2hop-001", "answer": 72}, {"country": "GRE1836", "question_num": 106, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of manowars?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "manowars", "target_gdb": "gdb-2hop-001", "answer": 940}, {"country": "GRE1836", "question_num": 107, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of services?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "services", "target_gdb": "gdb-2hop-001", "answer": 3465}, {"country": "GRE1836", "question_num": 108, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 940}, {"country": "TUR1836", "question_num": 109, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of groceries?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "groceries", "target_gdb": "gdb-2hop-001", "answer": 923}, {"country": "TUR1836", "question_num": 110, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of liquor?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "liquor", "target_gdb": "gdb-2hop-001", "answer": 923}, {"country": "ARG1839", "question_num": 111, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clothes?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clothes", "target_gdb": "gdb-2hop-001", "answer": 1748}, {"country": "ARG1839", "question_num": 112, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of tools?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "tools", "target_gdb": "gdb-2hop-001", "answer": 1741}, {"country": "BRZ1839", "question_num": 113, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of manowars?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "manowars", "target_gdb": "gdb-2hop-001", "answer": 1822}, {"country": "BRZ1839", "question_num": 114, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "furniture", "target_gdb": "gdb-2hop-001", "answer": 1812}, {"country": "BRZ1839", "question_num": 115, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 1821}, {"country": "BRZ1839", "question_num": 116, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of meat?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "meat", "target_gdb": "gdb-2hop-001", "answer": 1805}, {"country": "BRE1839", "question_num": 117, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of fabric?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "fabric", "target_gdb": "gdb-2hop-001", "answer": 5495}, {"country": "BRE1839", "question_num": 118, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of groceries?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "groceries", "target_gdb": "gdb-2hop-001", "answer": 4957}, {"country": "BRE1839", "question_num": 119, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of meat?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "meat", "target_gdb": "gdb-2hop-001", "answer": 5495}, {"country": "CHL1839", "question_num": 120, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of fabric?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "fabric", "target_gdb": "gdb-2hop-001", "answer": 1765}, {"country": "CHL1839", "question_num": 121, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of iron?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "iron", "target_gdb": "gdb-2hop-001", "answer": 1760}, {"country": "CHL1839", "question_num": 122, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of tools?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "tools", "target_gdb": "gdb-2hop-001", "answer": 1758}, {"country": "CHL1839", "question_num": 123, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of meat?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "meat", "target_gdb": "gdb-2hop-001", "answer": 1762}, {"country": "CHL1839", "question_num": 124, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of fruit?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "fruit", "target_gdb": "gdb-2hop-001", "answer": 4907}, {"country": "CHL1839", "question_num": 125, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of sugar?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "sugar", "target_gdb": "gdb-2hop-001", "answer": 4907}, {"country": "CHL1839", "question_num": 126, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of gold?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "gold", "target_gdb": "gdb-2hop-001", "answer": 1759}, {"country": "CLM1839", "question_num": 127, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clothes?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clothes", "target_gdb": "gdb-2hop-001", "answer": 1666}, {"country": "CLM1839", "question_num": 128, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of furniture?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "furniture", "target_gdb": "gdb-2hop-001", "answer": 1670}, {"country": "CLM1839", "question_num": 129, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of paper?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "paper", "target_gdb": "gdb-2hop-001", "answer": 4897}, {"country": "CLM1839", "question_num": 130, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of meat?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "meat", "target_gdb": "gdb-2hop-001", "answer": 1663}, {"country": "FIN1839", "question_num": 131, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of tools?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "tools", "target_gdb": "gdb-2hop-001", "answer": 4896}, {"country": "FIN1839", "question_num": 132, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of meat?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "meat", "target_gdb": "gdb-2hop-001", "answer": 4714}, {"country": "GRE1839", "question_num": 133, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 835}, {"country": "GRE1839", "question_num": 134, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of meat?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "meat", "target_gdb": "gdb-2hop-001", "answer": 4846}, {"country": "TUR1839", "question_num": 135, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of groceries?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "groceries", "target_gdb": "gdb-2hop-001", "answer": 851}, {"country": "TUR1839", "question_num": 137, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clippers?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clippers", "target_gdb": "gdb-2hop-001", "answer": 768}, {"country": "TUR1839", "question_num": 138, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of meat?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "meat", "target_gdb": "gdb-2hop-001", "answer": 790}, {"country": "ARG1849", "question_num": 139, "question": "Which building id should we increase a level by 5 to maximally decrease the market price of clothes?", "business_rules": "\nGoods have a \"name\", corresponding \"code\", \"base_price\", \"current_price\", and \"pop_demand\".\nBuildings have a unique \"id\" and a \"name\" and \"level\" corresponding to their type.\nThere exists a relation called Supply from Buildings to Goods. Supply has \"max_supply\", \"current_output\", and \"level\". The level here is the same as the level of the Building. Furthermore, max_supply and level have a proportional relationship.\nThere exists a relation called Demand from Goods to Buildings. Demand has \"max_demand\", \"current_input\", and \"level\". Also, max_demand and level have a proportional relationship.\nThe demand of Goods is defined as the Goods' \"pop_demand\" plus sum of the \"max_demand\" of all Demands connected to the Goods.\nThe supply of Goods is defined by the sum of \"current_output\" of all Supplies connected to Goods.\nThe \"current_input\" of Demand is determined by the ratio of connected Building's \"max_demand\" to connected Goods' demand, and multiplied by the supply of Goods.\nThe \"current_output\" of Supply is determined by the average ratio of the connected Building's \"current_input\" to connected Goods' \"max_demand\", and multiplied by the \"max_supply\" of Supply.\nThe \"current_price\" of Goods is determined by base_price*(1+0.75(demand-supply)/max(demand,supply)).", "goods": "clothes", "target_gdb": "gdb-2hop-001", "answer": 1814}]