CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:41.13776897761664, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:30.529131943659454, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:65.8128549358294, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:90.01573774908815, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:25.52604378387135, pop_demand:3363.1898951853245});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:25.155395397694107, pop_demand:807.7959505390465});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:10.997215258122841, pop_demand:678.6506425253444});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:30.440030236751078, pop_demand:229.11245691655648});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:38.935648852609894, pop_demand:436.1388591307838});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:40.89592155504187, pop_demand:915.7059024895574});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:40.43023519364419, pop_demand:741.531855376858});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:48.66690929363021, pop_demand:37.845948757830506});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:20.081486480914332, pop_demand:495.15475333333353});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:75.30010308488059, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:81.96115022177779, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:47.83139717224441, pop_demand:53.191129248597534});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:65.42733073997798, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:59.66081374731854, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:59.9087802304076, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:24.823583607739742, pop_demand:6.611188342601075});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:73.1954745533655, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:60.16242509917664, pop_demand:169.0505612510775});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:40.69949855468008, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:61.93426523242095, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:80.6012410894725, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:116.1551144965902, pop_demand:143.7430331914099});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:38.06711064964166, pop_demand:296.73478658621315});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:25.594777788994477, pop_demand:154.18935576663924});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:30.210091044969115, pop_demand:698.4282942699613});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:78.10975204726986, pop_demand:273.6939168902939});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:87.5, pop_demand:69.67334810970584});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:33.23635283002238, pop_demand:70.37133950257858});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:33.93761315136362, pop_demand:815.3917042975294});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:209.43217337084278});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:93.00597851174659, pop_demand:259.6078879058453});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 1430, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:1430}) CREATE (g)-[r:Demand{max_demand: 55.90799711815562, current_input: 10.190445356548492, level: 6}]->(b);
CREATE (n: Building {id: 1431, name:"building_maize_farmlevel", level:1});
CREATE (n: Building {id: 1432, name:"building_white_houselevel", level:1});
CREATE (n: Building {id: 1433, name:"building_capitol_hilllevel", level:1});
CREATE (n: Building {id: 1434, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1434}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.68986850704834, level: 2}]->(b);
CREATE (n: Building {id: 1435, name:"building_sugar_plantationlevel", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:1435}) CREATE (b)-[r:Supply{max_supply: 89.64, current_output: 91.4328,level: 3}]->(g);
CREATE (n: Building {id: 1436, name:"building_cotton_plantationlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1436}) CREATE (b)-[r:Supply{max_supply: 79.67999999999999, current_output: 100.3968,level: 2}]->(g);
CREATE (n: Building {id: 1437, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:1437}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 18.227169424460094, level: 10}]->(b);
CREATE (n: Building {id: 1438, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1438}) CREATE (g)-[r:Demand{max_demand: 33.399, current_input: 83.80797723084981, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1438}) CREATE (g)-[r:Demand{max_demand: 66.798, current_input: 21.375053961606497, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1438}) CREATE (g)-[r:Demand{max_demand: 83.4975, current_input: 13.843860288226878, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1438}) CREATE (g)-[r:Demand{max_demand: 16.6995, current_input: 4.86830715344446, level: 3}]->(b);
CREATE (n: Building {id: 1439, name:"building_arms_industrylevel", level:3});
MATCH (g: Goods{code: 24}), (b: Building{id:1439}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 9.947981883213423, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1439}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.963796237866273, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1439}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 11.953340384604736,level: 3}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:1439}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 11.953340384604736,level: 3}]->(g);
CREATE (n: Building {id: 1440, name:"building_munition_plantslevel", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:1440}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.49773867881194, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:1440}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.025505337086399, level: 1}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:1440}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 14.404055019872924,level: 1}]->(g);
CREATE (n: Building {id: 1441, name:"building_universitylevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:1441}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.7340754136690144, level: 3}]->(b);
CREATE (n: Building {id: 1442, name:"building_iron_minelevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:1442}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.763055578708776, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1442}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 8.745723800313412, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1442}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 31.017558758044373,level: 3}]->(g);
CREATE (n: Building {id: 1443, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1443}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 19.19972510698509, level: 2}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:1443}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.2133165766336886, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1443}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 33.64628730970051,level: 2}]->(g);
CREATE (n: Building {id: 1444, name:"building_textile_millslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:1444}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 501.85920075960246, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1444}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1444}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 150.0,level: 5}]->(g);
CREATE (n: Building {id: 1445, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1445}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 50.185920075960254, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1445}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 12.79981673799006, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1445}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.309197491910849, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1445}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.915241266771137, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1445}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 44.48203694375417,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1445}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.769794197224076,level: 2}]->(g);
CREATE (n: Building {id: 1446, name:"building_tooling_workshopslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:1446}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 47.99931276746272, level: 5}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:1446}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 41.68406055752912, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1446}) CREATE (b)-[r:Supply{max_supply: 400.0, current_output: 147.36720480500853,level: 5}]->(g);
CREATE (n: Building {id: 1447, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1447}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.915241266771137, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1447}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 23.321930134169094,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1447}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 7.288103166927843,level: 2}]->(g);
CREATE (n: Building {id: 1448, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:1448}) CREATE (g)-[r:Demand{max_demand: 19.961398058252428, current_input: 11.235373796556342, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1448}) CREATE (b)-[r:Supply{max_supply: 139.72979611650487, current_output: 78.64762204050659,level: 4}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1448}) CREATE (b)-[r:Supply{max_supply: 31.93823300970874, current_output: 17.976595888645274,level: 4}]->(g);
CREATE (n: Building {id: 1449, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1449}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.68986850704834, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1449}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 66.8986850704834,level: 2}]->(g);
CREATE (n: Building {id: 1450, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:1450}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.543503450715637, level: 10}]->(b);
CREATE (n: Building {id: 1451, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1451}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.440519697950409, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1451}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 3.1640594828182147, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1451}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1865223060568306, level: 1}]->(b);
CREATE (n: Building {id: 1452, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1452}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.03480276057251, level: 3}]->(b);
CREATE (n: Building {id: 1453, name:"building_central_parklevel", level:1});
CREATE (n: Building {id: 1454, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1454}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.6454338848920194, level: 2}]->(b);
CREATE (n: Building {id: 1455, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1455}) CREATE (g)-[r:Demand{max_demand: 11.133, current_input: 27.93599241028327, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1455}) CREATE (g)-[r:Demand{max_demand: 22.266, current_input: 7.125017987202165, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1455}) CREATE (g)-[r:Demand{max_demand: 27.8325, current_input: 4.614620096075626, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1455}) CREATE (g)-[r:Demand{max_demand: 5.5665, current_input: 1.6227690511481532, level: 1}]->(b);
CREATE (n: Building {id: 1456, name:"building_arms_industrylevel", level:3});
MATCH (g: Goods{code: 24}), (b: Building{id:1456}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 4.973990941606711, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1456}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.963796237866273, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1456}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 19.92223397434123,level: 3}]->(g);
CREATE (n: Building {id: 1457, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:1457}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.508703719139183, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1457}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.830482533542274, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1457}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 20.678372505362912,level: 2}]->(g);
CREATE (n: Building {id: 1458, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:1458}) CREATE (b)-[r:Supply{max_supply: 149.7, current_output: 155.688,level: 5}]->(g);
CREATE (n: Building {id: 1459, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1459}) CREATE (g)-[r:Demand{max_demand: 19.96059405940594, current_input: 12.877497502632268, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1459}) CREATE (g)-[r:Demand{max_demand: 19.96059405940594, current_input: 5.818994751124699, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1459}) CREATE (b)-[r:Supply{max_supply: 79.84239603960397, current_output: 37.39299378147564,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1459}) CREATE (b)-[r:Supply{max_supply: 9.98029702970297, current_output: 4.674123063439242,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1459}) CREATE (b)-[r:Supply{max_supply: 49.901495049504945, current_output: 23.370619954177055,level: 2}]->(g);
CREATE (n: Building {id: 1460, name:"building_tobacco_plantationlevel", level:4});
MATCH (g: Goods{code: 43}), (b: Building{id:1460}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 102.588,level: 4}]->(g);
CREATE (n: Building {id: 1461, name:"building_cotton_plantationlevel", level:7});
MATCH (g: Goods{code: 9}), (b: Building{id:1461}) CREATE (b)-[r:Supply{max_supply: 278.88, current_output: 365.3328,level: 7}]->(g);
CREATE (n: Building {id: 1462, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1462}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.440519697950409, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1462}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 3.1640594828182147, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1462}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1865223060568306, level: 1}]->(b);
CREATE (n: Building {id: 1463, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1463}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 100.37184015192051, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1463}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 25.59963347598012, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1463}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 19.799931276746275,level: 2}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:1463}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 26.399908368995032,level: 2}]->(g);
CREATE (n: Building {id: 1464, name:"building_naval_baselevel", level:15});
MATCH (g: Goods{code: 5}), (b: Building{id:1464}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.815255176073457, level: 15}]->(b);
CREATE (n: Building {id: 1465, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1465}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.03480276057251, level: 3}]->(b);
CREATE (n: Building {id: 1469, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1469}) CREATE (b)-[r:Supply{max_supply: 39.839999999999996, current_output: 49.8,level: 1}]->(g);
CREATE (n: Building {id: 1470, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1470}) CREATE (g)-[r:Demand{max_demand: 9.9803, current_input: 6.438750667591395, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1470}) CREATE (g)-[r:Demand{max_demand: 9.9803, current_input: 2.909498241475598, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1470}) CREATE (b)-[r:Supply{max_supply: 39.9212, current_output: 18.696497818133984,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1470}) CREATE (b)-[r:Supply{max_supply: 4.99015, current_output: 2.337062227266748,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1470}) CREATE (b)-[r:Supply{max_supply: 24.95075, current_output: 11.685311136333741,level: 1}]->(g);
CREATE (n: Building {id: 1471, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1471}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1472, name:"building_lead_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:1472}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.2543518595695917, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1472}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.915241266771137, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1472}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 10.339186252681456,level: 1}]->(g);
CREATE (n: Building {id: 1473, name:"building_maize_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1473}) CREATE (b)-[r:Supply{max_supply: 59.88, current_output: 60.4788,level: 2}]->(g);
CREATE (n: Building {id: 1474, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1474}) CREATE (g)-[r:Demand{max_demand: 9.9803, current_input: 6.438750667591395, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1474}) CREATE (g)-[r:Demand{max_demand: 9.9803, current_input: 2.909498241475598, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1474}) CREATE (b)-[r:Supply{max_supply: 39.9212, current_output: 18.696497818133984,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1474}) CREATE (b)-[r:Supply{max_supply: 4.99015, current_output: 2.337062227266748,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1474}) CREATE (b)-[r:Supply{max_supply: 24.95075, current_output: 11.685311136333741,level: 1}]->(g);
CREATE (n: Building {id: 1475, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1475}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.6454338848920194, level: 2}]->(b);
CREATE (n: Building {id: 1476, name:"building_chemical_plantslevel", level:2});
MATCH (g: Goods{code: 22}), (b: Building{id:1476}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 9.639949729901065, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1476}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.3159939610711406, level: 2}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:1476}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 29.38189741967173,level: 2}]->(g);
CREATE (n: Building {id: 1477, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1477}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.915241266771137, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1477}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 34.98289520125364,level: 2}]->(g);
CREATE (n: Building {id: 1478, name:"building_maize_farmlevel", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:1478}) CREATE (g)-[r:Demand{max_demand: 39.919999999999995, current_input: 22.46917378480431, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1478}) CREATE (b)-[r:Supply{max_supply: 399.2, current_output: 224.6917378480431,level: 8}]->(g);
CREATE (n: Building {id: 1479, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1479}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4576206333855686, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1479}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 17.49144760062682,level: 1}]->(g);
CREATE (n: Building {id: 1480, name:"building_sulfur_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:1480}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.508703719139183, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1480}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.830482533542274, level: 2}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:1480}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 20.678372505362912,level: 2}]->(g);
CREATE (n: Building {id: 1481, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1481}) CREATE (b)-[r:Supply{max_supply: 29.94, current_output: 29.94,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1481}) CREATE (b)-[r:Supply{max_supply: 4.99, current_output: 4.99,level: 1}]->(g);
CREATE (n: Building {id: 1482, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1482}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1483, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1483}) CREATE (g)-[r:Demand{max_demand: 11.133, current_input: 27.93599241028327, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1483}) CREATE (g)-[r:Demand{max_demand: 22.266, current_input: 7.125017987202165, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1483}) CREATE (g)-[r:Demand{max_demand: 27.8325, current_input: 4.614620096075626, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1483}) CREATE (g)-[r:Demand{max_demand: 5.5665, current_input: 1.6227690511481532, level: 1}]->(b);
CREATE (n: Building {id: 1484, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1484}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 19.19972510698509, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1484}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.24660801821791, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1484}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 41.69294114342091,level: 3}]->(g);
CREATE (n: Building {id: 1485, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1485}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 50.185920075960254, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1485}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 19.19972510698509, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1485}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.915241266771137, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1485}) CREATE (b)-[r:Supply{max_supply: 130.0, current_output: 69.83251362216417,level: 2}]->(g);
CREATE (n: Building {id: 1486, name:"building_chemical_plantslevel", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:1486}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 4.819974864950533, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1486}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.381527789354388, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1486}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.6579969805355703, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:1486}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 13.797517821055017,level: 1}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:1486}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 3.6793380856146713,level: 1}]->(g);
CREATE (n: Building {id: 1487, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1487}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.830482533542274, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1487}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 23.321930134169094,level: 2}]->(g);
CREATE (n: Building {id: 1488, name:"building_maize_farmlevel", level:6});
MATCH (g: Goods{code: 32}), (b: Building{id:1488}) CREATE (g)-[r:Demand{max_demand: 29.941495238095236, current_input: 16.852721940923164, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1488}) CREATE (b)-[r:Supply{max_supply: 227.5554, current_output: 128.08070712100852,level: 6}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1488}) CREATE (b)-[r:Supply{max_supply: 35.9298, current_output: 20.223269545422397,level: 6}]->(g);
CREATE (n: Building {id: 1489, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1489}) CREATE (g)-[r:Demand{max_demand: 19.96059405940594, current_input: 12.877497502632268, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1489}) CREATE (g)-[r:Demand{max_demand: 19.96059405940594, current_input: 5.818994751124699, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1489}) CREATE (b)-[r:Supply{max_supply: 79.84239603960397, current_output: 37.39299378147564,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1489}) CREATE (b)-[r:Supply{max_supply: 9.98029702970297, current_output: 4.674123063439242,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1489}) CREATE (b)-[r:Supply{max_supply: 49.901495049504945, current_output: 23.370619954177055,level: 2}]->(g);
CREATE (n: Building {id: 1490, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1490}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.440519697950409, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1490}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 3.1640594828182147, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1490}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1865223060568306, level: 1}]->(b);
CREATE (n: Building {id: 1491, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1491}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1492, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1492}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.9113584712230048, level: 1}]->(b);
CREATE (n: Building {id: 1493, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:1493}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 103.22336070204535, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1493}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 137.1704174525018, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1493}) CREATE (b)-[r:Supply{max_supply: 139.99999999999997, current_output: 105.17227794261434,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1493}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 180.29533361591032,level: 4}]->(g);
CREATE (n: Building {id: 1494, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1494}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 5.8304825335422725, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1494}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 23.32193013416909,level: 2}]->(g);
CREATE (n: Building {id: 1495, name:"building_tobacco_plantationlevel", level:8});
MATCH (g: Goods{code: 43}), (b: Building{id:1495}) CREATE (b)-[r:Supply{max_supply: 199.2, current_output: 213.144,level: 8}]->(g);
CREATE (n: Building {id: 1496, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:1496}) CREATE (g)-[r:Demand{max_demand: 24.95125, current_input: 14.043937184321107, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1496}) CREATE (b)-[r:Supply{max_supply: 189.62949999999998, current_output: 106.73392260084039,level: 5}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1496}) CREATE (b)-[r:Supply{max_supply: 29.941499999999998, current_output: 16.852724621185324,level: 5}]->(g);
CREATE (n: Building {id: 1497, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1497}) CREATE (g)-[r:Demand{max_demand: 9.9803, current_input: 6.438750667591395, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1497}) CREATE (g)-[r:Demand{max_demand: 9.9803, current_input: 2.909498241475598, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1497}) CREATE (b)-[r:Supply{max_supply: 39.9212, current_output: 18.696497818133984,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1497}) CREATE (b)-[r:Supply{max_supply: 4.99015, current_output: 2.337062227266748,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1497}) CREATE (b)-[r:Supply{max_supply: 24.95075, current_output: 11.685311136333741,level: 1}]->(g);
CREATE (n: Building {id: 1498, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1498}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.440519697950409, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1498}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 3.1640594828182147, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1498}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1865223060568306, level: 1}]->(b);
CREATE (n: Building {id: 1499, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1499}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1500, name:"building_food_industrylevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1500}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 77.41752052653402, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1500}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 102.87781308937633, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1500}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 78.87920845696077,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1500}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 135.22150021193278,level: 3}]->(g);
CREATE (n: Building {id: 1501, name:"building_maize_farmlevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:1501}) CREATE (b)-[r:Supply{max_supply: 99.79999999999998, current_output: 108.782,level: 10}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1501}) CREATE (b)-[r:Supply{max_supply: 89.82, current_output: 97.9038,level: 10}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1501}) CREATE (b)-[r:Supply{max_supply: 59.879999999999995, current_output: 65.2692,level: 10}]->(g);
CREATE (n: Building {id: 1502, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1502}) CREATE (g)-[r:Demand{max_demand: 19.96059405940594, current_input: 12.877497502632268, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1502}) CREATE (g)-[r:Demand{max_demand: 19.96059405940594, current_input: 5.818994751124699, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1502}) CREATE (b)-[r:Supply{max_supply: 79.84239603960397, current_output: 37.39299378147564,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1502}) CREATE (b)-[r:Supply{max_supply: 9.98029702970297, current_output: 4.674123063439242,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1502}) CREATE (b)-[r:Supply{max_supply: 49.901495049504945, current_output: 23.370619954177055,level: 2}]->(g);
CREATE (n: Building {id: 1503, name:"building_tobacco_plantationlevel", level:10});
MATCH (g: Goods{code: 43}), (b: Building{id:1503}) CREATE (b)-[r:Supply{max_supply: 249.0, current_output: 271.41,level: 10}]->(g);
CREATE (n: Building {id: 1504, name:"building_cotton_plantationlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1504}) CREATE (b)-[r:Supply{max_supply: 119.52, current_output: 151.7904,level: 3}]->(g);
CREATE (n: Building {id: 1505, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1505}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.440519697950409, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1505}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 3.1640594828182147, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1505}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1865223060568306, level: 1}]->(b);
CREATE (n: Building {id: 1506, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1506}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1507, name:"building_cotton_plantationlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:1507}) CREATE (b)-[r:Supply{max_supply: 159.35999999999999, current_output: 203.9808,level: 4}]->(g);
CREATE (n: Building {id: 1508, name:"building_maize_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:1508}) CREATE (b)-[r:Supply{max_supply: 119.76, current_output: 123.3528,level: 4}]->(g);
CREATE (n: Building {id: 1509, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1509}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 1510, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1510}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1511, name:"building_maize_farmlevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:1511}) CREATE (b)-[r:Supply{max_supply: 59.88, current_output: 62.874,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1511}) CREATE (b)-[r:Supply{max_supply: 53.891999999999996, current_output: 56.5866,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1511}) CREATE (b)-[r:Supply{max_supply: 35.928000000000004, current_output: 37.7244,level: 6}]->(g);
CREATE (n: Building {id: 1512, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1512}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
CREATE (n: Building {id: 1513, name:"building_cotton_plantationlevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:1513}) CREATE (b)-[r:Supply{max_supply: 398.4, current_output: 533.856,level: 10}]->(g);
CREATE (n: Building {id: 1514, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:1514}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 76.194,level: 3}]->(g);
CREATE (n: Building {id: 1515, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1515}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.440519697950409, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1515}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 3.1640594828182147, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1515}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1865223060568306, level: 1}]->(b);
CREATE (n: Building {id: 1516, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1516}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.34493425352417, level: 1}]->(b);
CREATE (n: Building {id: 1517, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1517}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.6454338848920194, level: 2}]->(b);
CREATE (n: Building {id: 1518, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:1518}) CREATE (b)-[r:Supply{max_supply: 149.7, current_output: 155.688,level: 5}]->(g);
CREATE (n: Building {id: 1519, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1519}) CREATE (g)-[r:Demand{max_demand: 9.9803, current_input: 6.438750667591395, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1519}) CREATE (g)-[r:Demand{max_demand: 9.9803, current_input: 2.909498241475598, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1519}) CREATE (b)-[r:Supply{max_supply: 39.9212, current_output: 18.696497818133984,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1519}) CREATE (b)-[r:Supply{max_supply: 4.99015, current_output: 2.337062227266748,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1519}) CREATE (b)-[r:Supply{max_supply: 24.95075, current_output: 11.685311136333741,level: 1}]->(g);
CREATE (n: Building {id: 1520, name:"building_sugar_plantationlevel", level:4});
MATCH (g: Goods{code: 42}), (b: Building{id:1520}) CREATE (b)-[r:Supply{max_supply: 119.52, current_output: 123.1056,level: 4}]->(g);
CREATE (n: Building {id: 1521, name:"building_cotton_plantationlevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:1521}) CREATE (b)-[r:Supply{max_supply: 318.71999999999997, current_output: 420.7104,level: 8}]->(g);
CREATE (n: Building {id: 1522, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:1522}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 76.194,level: 3}]->(g);
CREATE (n: Building {id: 1523, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1523}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.440519697950409, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1523}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 3.1640594828182147, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1523}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1865223060568306, level: 1}]->(b);
CREATE (n: Building {id: 1524, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1524}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.34493425352417, level: 1}]->(b);
CREATE (n: Building {id: 1525, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1525}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.915241266771137, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1525}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 11.660965067084547,level: 1}]->(g);
CREATE (n: Building {id: 1526, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1526}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4576206333855686, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1526}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 11.660965067084547,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1526}) CREATE (b)-[r:Supply{max_supply: 12.5, current_output: 3.6440515834639213,level: 1}]->(g);
CREATE (n: Building {id: 1527, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1527}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1528, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1528}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.39990836899503, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1528}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1528}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.74886933940597, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1528}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 4.632549015935656,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1528}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 5.79068626991957,level: 1}]->(g);
CREATE (n: Building {id: 1529, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1529}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.03480276057251, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1529}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 100.34802760572512,level: 3}]->(g);
CREATE (n: Building {id: 1530, name:"building_tobacco_plantationlevel", level:4});
MATCH (g: Goods{code: 43}), (b: Building{id:1530}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 102.588,level: 4}]->(g);
CREATE (n: Building {id: 1531, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:1531}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.271751725357818, level: 5}]->(b);
CREATE (n: Building {id: 1532, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1532}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.68986850704834, level: 2}]->(b);
CREATE (n: Building {id: 1533, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1533}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1534, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1534}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.34493425352417, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1534}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 33.4493425352417,level: 1}]->(g);
CREATE (n: Building {id: 1535, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1535}) CREATE (g)-[r:Demand{max_demand: 9.9803, current_input: 6.438750667591395, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1535}) CREATE (g)-[r:Demand{max_demand: 9.9803, current_input: 2.909498241475598, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1535}) CREATE (b)-[r:Supply{max_supply: 39.9212, current_output: 18.696497818133984,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1535}) CREATE (b)-[r:Supply{max_supply: 4.99015, current_output: 2.337062227266748,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1535}) CREATE (b)-[r:Supply{max_supply: 24.95075, current_output: 11.685311136333741,level: 1}]->(g);
CREATE (n: Building {id: 1536, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:1536}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.468150827338029, level: 3}]->(b);
CREATE (n: Building {id: 1537, name:"building_steel_millslevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:1537}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 20.289166736126322, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1537}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 19.895963766426846, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:1537}) CREATE (b)-[r:Supply{max_supply: 195.0, current_output: 38.14540119102533,level: 3}]->(g);
CREATE (n: Building {id: 1538, name:"building_textile_millslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1538}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 200.74368030384102, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1538}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1538}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 60.0,level: 2}]->(g);
CREATE (n: Building {id: 1539, name:"building_furniture_manufacturieslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:1539}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 100.37184015192051, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1539}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 25.59963347598012, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1539}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 14.618394983821698, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1539}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.830482533542274, level: 4}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1539}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 88.96407388750833,level: 4}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1539}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 39.53958839444815,level: 4}]->(g);
CREATE (n: Building {id: 1540, name:"building_glassworkslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1540}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 12.79981673799006, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1540}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1540}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.49773867881194, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1540}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 9.265098031871313,level: 2}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1540}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 11.58137253983914,level: 2}]->(g);
CREATE (n: Building {id: 1541, name:"building_coal_minelevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1541}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 11.66096506708455, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1541}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 46.6438602683382,level: 4}]->(g);
CREATE (n: Building {id: 1542, name:"building_maize_farmlevel", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:1542}) CREATE (g)-[r:Demand{max_demand: 19.961000000000002, current_input: 11.235149747456887, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1542}) CREATE (b)-[r:Supply{max_supply: 151.70359223300972, current_output: 85.3871337089826,level: 4}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1542}) CREATE (b)-[r:Supply{max_supply: 23.953194174757282, current_output: 13.482176418180956,level: 4}]->(g);
CREATE (n: Building {id: 1543, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1543}) CREATE (g)-[r:Demand{max_demand: 29.940892156862745, current_input: 19.316246942805524, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1543}) CREATE (g)-[r:Demand{max_demand: 29.940892156862745, current_input: 8.728492437963055, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1543}) CREATE (b)-[r:Supply{max_supply: 119.76359803921568, current_output: 56.08949253609791,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1543}) CREATE (b)-[r:Supply{max_supply: 14.970441176470588, current_output: 7.01118254943202,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1543}) CREATE (b)-[r:Supply{max_supply: 74.8522450980392, current_output: 35.055931113241094,level: 3}]->(g);
CREATE (n: Building {id: 1544, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:1544}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 7.288103166927842, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1544}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 58.30482533542274,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1544}) CREATE (b)-[r:Supply{max_supply: 62.5, current_output: 18.220257917319607,level: 5}]->(g);
CREATE (n: Building {id: 1545, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1545}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.440519697950409, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1545}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 3.1640594828182147, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1545}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1865223060568306, level: 1}]->(b);
CREATE (n: Building {id: 1546, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1546}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 28.79958766047763, level: 3}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:1546}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 4.819974864950533, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1546}) CREATE (b)-[r:Supply{max_supply: 209.99999999999997, current_output: 50.46943096455076,level: 3}]->(g);
CREATE (n: Building {id: 1547, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1547}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1548, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1548}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.34493425352417, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1548}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 33.4493425352417,level: 1}]->(g);
CREATE (n: Building {id: 1549, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1549}) CREATE (b)-[r:Supply{max_supply: 9.98, current_output: 9.98,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1549}) CREATE (b)-[r:Supply{max_supply: 8.982, current_output: 8.982,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1549}) CREATE (b)-[r:Supply{max_supply: 5.988, current_output: 5.988,level: 1}]->(g);
CREATE (n: Building {id: 1550, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1550}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.915241266771137, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1550}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 34.98289520125364,level: 2}]->(g);
CREATE (n: Building {id: 1551, name:"building_lead_minelevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:1551}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.763055578708776, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1551}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 8.745723800313412, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1551}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 31.017558758044373,level: 3}]->(g);
CREATE (n: Building {id: 1552, name:"building_maize_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1552}) CREATE (b)-[r:Supply{max_supply: 19.96, current_output: 20.1596,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1552}) CREATE (b)-[r:Supply{max_supply: 17.964000000000002, current_output: 18.14364,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1552}) CREATE (b)-[r:Supply{max_supply: 11.976, current_output: 12.09576,level: 2}]->(g);
CREATE (n: Building {id: 1553, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1553}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1554, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1554}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.39990836899503, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1554}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.74886933940597, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1554}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 13.89764704780697,level: 1}]->(g);
CREATE (n: Building {id: 1555, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1555}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.34493425352417, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1555}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 33.4493425352417,level: 1}]->(g);
CREATE (n: Building {id: 1556, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1556}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1557, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:1557}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.2543518595695917, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1557}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.915241266771137, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1557}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 10.339186252681456,level: 1}]->(g);
CREATE (n: Building {id: 1558, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1558}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.34493425352417, level: 1}]->(b);
CREATE (n: Building {id: 1559, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1559}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1560, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1560}) CREATE (g)-[r:Demand{max_demand: 11.133, current_input: 27.93599241028327, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1560}) CREATE (g)-[r:Demand{max_demand: 22.266, current_input: 7.125017987202165, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1560}) CREATE (g)-[r:Demand{max_demand: 27.8325, current_input: 4.614620096075626, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1560}) CREATE (g)-[r:Demand{max_demand: 5.5665, current_input: 1.6227690511481532, level: 1}]->(b);
CREATE (n: Building {id: 1561, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1561}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 2}]->(b);
CREATE (n: Building {id: 1562, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1562}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 301.1155204557615, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1562}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1562}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 90.0,level: 3}]->(g);
CREATE (n: Building {id: 1563, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1563}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 19.19972510698509, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1563}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.24660801821791, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1563}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 41.69294114342091,level: 3}]->(g);
CREATE (n: Building {id: 1564, name:"building_shipyardslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1564}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 150.55776022788075, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1564}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 38.39945021397018, level: 3}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1564}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 29.699896915119407,level: 3}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:1564}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 39.59986255349255,level: 3}]->(g);
CREATE (n: Building {id: 1565, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1565}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.03480276057251, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1565}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 100.34802760572512,level: 3}]->(g);
CREATE (n: Building {id: 1566, name:"building_whaling_stationlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1566}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.34493425352417, level: 1}]->(b);
MATCH (g: Goods{code: 28}), (b: Building{id:1566}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 13.37973701409668,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1566}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 6.68986850704834,level: 1}]->(g);
CREATE (n: Building {id: 1567, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1567}) CREATE (g)-[r:Demand{max_demand: 9.98, current_input: 5.617293446201078, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1567}) CREATE (b)-[r:Supply{max_supply: 59.88, current_output: 33.70376067720647,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1567}) CREATE (b)-[r:Supply{max_supply: 17.964000000000002, current_output: 10.111128203161941,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1567}) CREATE (b)-[r:Supply{max_supply: 11.976, current_output: 6.740752135441293,level: 2}]->(g);
CREATE (n: Building {id: 1568, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1568}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.440519697950409, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1568}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 3.1640594828182147, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1568}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1865223060568306, level: 1}]->(b);
CREATE (n: Building {id: 1569, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:1569}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.543503450715637, level: 10}]->(b);
CREATE (n: Building {id: 1570, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1570}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.03480276057251, level: 3}]->(b);
CREATE (n: Building {id: 1571, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1571}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4576206333855686, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1571}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 11.660965067084547,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1571}) CREATE (b)-[r:Supply{max_supply: 12.5, current_output: 3.6440515834639213,level: 1}]->(g);
CREATE (n: Building {id: 1572, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1572}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1573, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1573}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4576206333855686, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1573}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 17.49144760062682,level: 1}]->(g);
CREATE (n: Building {id: 1574, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1574}) CREATE (b)-[r:Supply{max_supply: 29.94, current_output: 29.94,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1574}) CREATE (b)-[r:Supply{max_supply: 4.99, current_output: 4.99,level: 1}]->(g);
CREATE (n: Building {id: 1575, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1575}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 51.61168035102268, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1575}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 68.5852087262509, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1575}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 52.586138971307186,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1575}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 90.14766680795518,level: 2}]->(g);
CREATE (n: Building {id: 1576, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1576}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4576206333855686, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1576}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 17.49144760062682,level: 1}]->(g);
CREATE (n: Building {id: 1577, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1577}) CREATE (b)-[r:Supply{max_supply: 59.88, current_output: 60.4788,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1577}) CREATE (b)-[r:Supply{max_supply: 9.98, current_output: 10.0798,level: 2}]->(g);
CREATE (n: Building {id: 1578, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1578}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8227169424460097, level: 1}]->(b);
CREATE (n: Building {id: 1579, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1579}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 25.092960037980127, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1579}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.39990836899503, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1579}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.6545987459554246, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1579}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4576206333855686, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1579}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 22.241018471877084,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1579}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 9.884897098612038,level: 1}]->(g);
CREATE (n: Building {id: 1580, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1580}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 4.372861900156706, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1580}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 34.98289520125365,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1580}) CREATE (b)-[r:Supply{max_supply: 37.5, current_output: 10.932154750391765,level: 3}]->(g);
CREATE (n: Building {id: 1581, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1581}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 100.37184015192051, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1581}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 25.59963347598012, level: 2}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:1581}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 46.19983964574131,level: 2}]->(g);
CREATE (n: Building {id: 1582, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1582}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.03480276057251, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1582}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 100.34802760572512,level: 3}]->(g);
CREATE (n: Building {id: 1583, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1583}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.34493425352417, level: 1}]->(b);
CREATE (n: Building {id: 1584, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1584}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.6454338848920194, level: 2}]->(b);
CREATE (n: Building {id: 1585, name:"building_food_industrylevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1585}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 25.80584017551134, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1585}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 34.29260436312545, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1585}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 26.293069485653593,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1585}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 45.07383340397759,level: 1}]->(g);
CREATE (n: Building {id: 1586, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1586}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 5.8304825335422725, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1586}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 23.32193013416909,level: 2}]->(g);
CREATE (n: Building {id: 1587, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1587}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4576206333855686, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1587}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 17.49144760062682,level: 1}]->(g);
CREATE (n: Building {id: 1588, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:1588}) CREATE (b)-[r:Supply{max_supply: 149.7, current_output: 155.688,level: 5}]->(g);
CREATE (n: Building {id: 1589, name:"building_cotton_plantationlevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:1589}) CREATE (b)-[r:Supply{max_supply: 398.4, current_output: 533.856,level: 10}]->(g);
CREATE (n: Building {id: 1590, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1590}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.440519697950409, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1590}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 3.1640594828182147, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1590}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1865223060568306, level: 1}]->(b);
CREATE (n: Building {id: 1591, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1591}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.34493425352417, level: 1}]->(b);
CREATE (n: Building {id: 1592, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1592}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.6454338848920194, level: 2}]->(b);
CREATE (n: Building {id: 1593, name:"building_cotton_plantationlevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:1593}) CREATE (b)-[r:Supply{max_supply: 398.4, current_output: 533.856,level: 10}]->(g);
CREATE (n: Building {id: 1594, name:"building_tobacco_plantationlevel", level:5});
MATCH (g: Goods{code: 43}), (b: Building{id:1594}) CREATE (b)-[r:Supply{max_supply: 124.49999999999999, current_output: 129.48,level: 5}]->(g);
CREATE (n: Building {id: 1595, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:1595}) CREATE (b)-[r:Supply{max_supply: 149.7, current_output: 155.688,level: 5}]->(g);
CREATE (n: Building {id: 1596, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1596}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.440519697950409, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1596}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 3.1640594828182147, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1596}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1865223060568306, level: 1}]->(b);
CREATE (n: Building {id: 1597, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1597}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.34493425352417, level: 1}]->(b);
CREATE (n: Building {id: 2866, name:"building_subsistence_farmslevel", level:115});
MATCH (g: Goods{code: 7}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 19.31425, current_output: 19.31425,level: 115}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 3.86285, current_output: 3.86285,level: 115}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 3.86285, current_output: 3.86285,level: 115}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 3.86285, current_output: 3.86285,level: 115}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 3.86285, current_output: 3.86285,level: 115}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 3.86285, current_output: 3.86285,level: 115}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 5.40799, current_output: 5.40799,level: 115}]->(g);
CREATE (n: Building {id: 2867, name:"building_subsistence_farmslevel", level:116});
MATCH (g: Goods{code: 7}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 118.3519, current_output: 118.3519,level: 116}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 23.67038, current_output: 23.67038,level: 116}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 23.67038, current_output: 23.67038,level: 116}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 23.67038, current_output: 23.67038,level: 116}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 23.67038, current_output: 23.67038,level: 116}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 23.67038, current_output: 23.67038,level: 116}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 33.13853, current_output: 33.13853,level: 116}]->(g);
CREATE (n: Building {id: 2868, name:"building_urban_centerlevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2868}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 15.999770922487569, level: 10}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2868}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 22.543518595695915, level: 10}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2868}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 17.680744852191598, level: 10}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2868}) CREATE (b)-[r:Supply{max_supply: 700.0, current_output: 209.77728367179333,level: 10}]->(g);
CREATE (n: Building {id: 2869, name:"building_subsistence_farmslevel", level:162});
MATCH (g: Goods{code: 7}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 8.13645, current_output: 8.13645,level: 162}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 1.62729, current_output: 1.62729,level: 162}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 1.62729, current_output: 1.62729,level: 162}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 1.62729, current_output: 1.62729,level: 162}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 1.62729, current_output: 1.62729,level: 162}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 1.62729, current_output: 1.62729,level: 162}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 2.2782, current_output: 2.2782,level: 162}]->(g);
CREATE (n: Building {id: 2870, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2870}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 4.799931276746272, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2870}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.763055578708776, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2870}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.30422345565748, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2870}) CREATE (b)-[r:Supply{max_supply: 209.99999999999997, current_output: 62.93318510153799,level: 3}]->(g);
CREATE (n: Building {id: 2915, name:"building_subsistence_farmslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:2915}) CREATE (b)-[r:Supply{max_supply: 0.224, current_output: 0.224,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2915}) CREATE (b)-[r:Supply{max_supply: 0.0448, current_output: 0.0448,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2915}) CREATE (b)-[r:Supply{max_supply: 0.0448, current_output: 0.0448,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2915}) CREATE (b)-[r:Supply{max_supply: 0.0448, current_output: 0.0448,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2915}) CREATE (b)-[r:Supply{max_supply: 0.0448, current_output: 0.0448,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2915}) CREATE (b)-[r:Supply{max_supply: 0.0448, current_output: 0.0448,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2915}) CREATE (b)-[r:Supply{max_supply: 0.06272, current_output: 0.06272,level: 20}]->(g);
CREATE (n: Building {id: 2916, name:"building_subsistence_farmslevel", level:103});
MATCH (g: Goods{code: 7}), (b: Building{id:2916}) CREATE (b)-[r:Supply{max_supply: 2.4926, current_output: 2.4926,level: 103}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2916}) CREATE (b)-[r:Supply{max_supply: 0.49852, current_output: 0.49852,level: 103}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2916}) CREATE (b)-[r:Supply{max_supply: 0.49852, current_output: 0.49852,level: 103}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2916}) CREATE (b)-[r:Supply{max_supply: 0.49852, current_output: 0.49852,level: 103}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2916}) CREATE (b)-[r:Supply{max_supply: 0.49852, current_output: 0.49852,level: 103}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2916}) CREATE (b)-[r:Supply{max_supply: 0.49852, current_output: 0.49852,level: 103}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2916}) CREATE (b)-[r:Supply{max_supply: 0.69792, current_output: 0.69792,level: 103}]->(g);
CREATE (n: Building {id: 2919, name:"building_subsistence_farmslevel", level:126});
MATCH (g: Goods{code: 7}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 1.4931, current_output: 1.4931,level: 126}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.29862, current_output: 0.29862,level: 126}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.29862, current_output: 0.29862,level: 126}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.29862, current_output: 0.29862,level: 126}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.29862, current_output: 0.29862,level: 126}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.29862, current_output: 0.29862,level: 126}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.41806, current_output: 0.41806,level: 126}]->(g);
CREATE (n: Building {id: 2923, name:"building_subsistence_farmslevel", level:129});
MATCH (g: Goods{code: 7}), (b: Building{id:2923}) CREATE (b)-[r:Supply{max_supply: 0.744975, current_output: 0.89397,level: 129}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2923}) CREATE (b)-[r:Supply{max_supply: 0.1489916666666667, current_output: 0.17879,level: 129}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2923}) CREATE (b)-[r:Supply{max_supply: 0.1489916666666667, current_output: 0.17879,level: 129}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2923}) CREATE (b)-[r:Supply{max_supply: 0.1489916666666667, current_output: 0.17879,level: 129}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2923}) CREATE (b)-[r:Supply{max_supply: 0.1489916666666667, current_output: 0.17879,level: 129}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2923}) CREATE (b)-[r:Supply{max_supply: 0.1489916666666667, current_output: 0.17879,level: 129}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2923}) CREATE (b)-[r:Supply{max_supply: 0.20859166666666665, current_output: 0.25031,level: 129}]->(g);
CREATE (n: Building {id: 2926, name:"building_subsistence_farmslevel", level:17});
MATCH (g: Goods{code: 7}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 0.49725, current_output: 0.5967,level: 17}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 0.09945000000000001, current_output: 0.11934,level: 17}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 0.09945000000000001, current_output: 0.11934,level: 17}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 0.09945000000000001, current_output: 0.11934,level: 17}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 0.09945000000000001, current_output: 0.11934,level: 17}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 0.09945000000000001, current_output: 0.11934,level: 17}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 0.13922500000000002, current_output: 0.16707,level: 17}]->(g);
CREATE (n: Building {id: 2927, name:"building_subsistence_farmslevel", level:39});
MATCH (g: Goods{code: 7}), (b: Building{id:2927}) CREATE (b)-[r:Supply{max_supply: 0.496275, current_output: 0.59553,level: 39}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2927}) CREATE (b)-[r:Supply{max_supply: 0.09925, current_output: 0.1191,level: 39}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2927}) CREATE (b)-[r:Supply{max_supply: 0.09925, current_output: 0.1191,level: 39}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2927}) CREATE (b)-[r:Supply{max_supply: 0.09925, current_output: 0.1191,level: 39}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2927}) CREATE (b)-[r:Supply{max_supply: 0.09925, current_output: 0.1191,level: 39}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2927}) CREATE (b)-[r:Supply{max_supply: 0.09925, current_output: 0.1191,level: 39}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2927}) CREATE (b)-[r:Supply{max_supply: 0.13895000000000002, current_output: 0.16674,level: 39}]->(g);
CREATE (n: Building {id: 2930, name:"building_subsistence_farmslevel", level:148});
MATCH (g: Goods{code: 7}), (b: Building{id:2930}) CREATE (b)-[r:Supply{max_supply: 1.4948000000000001, current_output: 1.79376,level: 148}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2930}) CREATE (b)-[r:Supply{max_supply: 0.2989583333333334, current_output: 0.35875,level: 148}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2930}) CREATE (b)-[r:Supply{max_supply: 0.2989583333333334, current_output: 0.35875,level: 148}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2930}) CREATE (b)-[r:Supply{max_supply: 0.2989583333333334, current_output: 0.35875,level: 148}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2930}) CREATE (b)-[r:Supply{max_supply: 0.2989583333333334, current_output: 0.35875,level: 148}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2930}) CREATE (b)-[r:Supply{max_supply: 0.2989583333333334, current_output: 0.35875,level: 148}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2930}) CREATE (b)-[r:Supply{max_supply: 0.41854166666666665, current_output: 0.50225,level: 148}]->(g);
CREATE (n: Building {id: 2935, name:"building_subsistence_farmslevel", level:178});
MATCH (g: Goods{code: 7}), (b: Building{id:2935}) CREATE (b)-[r:Supply{max_supply: 6.5148, current_output: 6.5148,level: 178}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2935}) CREATE (b)-[r:Supply{max_supply: 1.30296, current_output: 1.30296,level: 178}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2935}) CREATE (b)-[r:Supply{max_supply: 1.30296, current_output: 1.30296,level: 178}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2935}) CREATE (b)-[r:Supply{max_supply: 1.30296, current_output: 1.30296,level: 178}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2935}) CREATE (b)-[r:Supply{max_supply: 1.30296, current_output: 1.30296,level: 178}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2935}) CREATE (b)-[r:Supply{max_supply: 1.30296, current_output: 1.30296,level: 178}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2935}) CREATE (b)-[r:Supply{max_supply: 1.82414, current_output: 1.82414,level: 178}]->(g);
CREATE (n: Building {id: 2936, name:"building_subsistence_farmslevel", level:227});
MATCH (g: Goods{code: 7}), (b: Building{id:2936}) CREATE (b)-[r:Supply{max_supply: 33.5506, current_output: 33.5506,level: 227}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2936}) CREATE (b)-[r:Supply{max_supply: 6.71012, current_output: 6.71012,level: 227}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2936}) CREATE (b)-[r:Supply{max_supply: 6.71012, current_output: 6.71012,level: 227}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2936}) CREATE (b)-[r:Supply{max_supply: 6.71012, current_output: 6.71012,level: 227}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2936}) CREATE (b)-[r:Supply{max_supply: 6.71012, current_output: 6.71012,level: 227}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2936}) CREATE (b)-[r:Supply{max_supply: 6.71012, current_output: 6.71012,level: 227}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2936}) CREATE (b)-[r:Supply{max_supply: 9.39416, current_output: 9.39416,level: 227}]->(g);
CREATE (n: Building {id: 2937, name:"building_subsistence_farmslevel", level:240});
MATCH (g: Goods{code: 7}), (b: Building{id:2937}) CREATE (b)-[r:Supply{max_supply: 5.484, current_output: 5.484,level: 240}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2937}) CREATE (b)-[r:Supply{max_supply: 1.0968, current_output: 1.0968,level: 240}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2937}) CREATE (b)-[r:Supply{max_supply: 1.0968, current_output: 1.0968,level: 240}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2937}) CREATE (b)-[r:Supply{max_supply: 1.0968, current_output: 1.0968,level: 240}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2937}) CREATE (b)-[r:Supply{max_supply: 1.0968, current_output: 1.0968,level: 240}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2937}) CREATE (b)-[r:Supply{max_supply: 1.0968, current_output: 1.0968,level: 240}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2937}) CREATE (b)-[r:Supply{max_supply: 1.53552, current_output: 1.53552,level: 240}]->(g);
CREATE (n: Building {id: 2938, name:"building_subsistence_farmslevel", level:111});
MATCH (g: Goods{code: 7}), (b: Building{id:2938}) CREATE (b)-[r:Supply{max_supply: 1.99245, current_output: 1.99245,level: 111}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2938}) CREATE (b)-[r:Supply{max_supply: 0.39849, current_output: 0.39849,level: 111}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2938}) CREATE (b)-[r:Supply{max_supply: 0.39849, current_output: 0.39849,level: 111}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2938}) CREATE (b)-[r:Supply{max_supply: 0.39849, current_output: 0.39849,level: 111}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2938}) CREATE (b)-[r:Supply{max_supply: 0.39849, current_output: 0.39849,level: 111}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2938}) CREATE (b)-[r:Supply{max_supply: 0.39849, current_output: 0.39849,level: 111}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2938}) CREATE (b)-[r:Supply{max_supply: 0.55788, current_output: 0.55788,level: 111}]->(g);
CREATE (n: Building {id: 2940, name:"building_subsistence_farmslevel", level:180});
MATCH (g: Goods{code: 7}), (b: Building{id:2940}) CREATE (b)-[r:Supply{max_supply: 4.4955, current_output: 4.4955,level: 180}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2940}) CREATE (b)-[r:Supply{max_supply: 0.8991, current_output: 0.8991,level: 180}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2940}) CREATE (b)-[r:Supply{max_supply: 0.8991, current_output: 0.8991,level: 180}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2940}) CREATE (b)-[r:Supply{max_supply: 0.8991, current_output: 0.8991,level: 180}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2940}) CREATE (b)-[r:Supply{max_supply: 0.8991, current_output: 0.8991,level: 180}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2940}) CREATE (b)-[r:Supply{max_supply: 0.8991, current_output: 0.8991,level: 180}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2940}) CREATE (b)-[r:Supply{max_supply: 1.25874, current_output: 1.25874,level: 180}]->(g);
CREATE (n: Building {id: 2941, name:"building_subsistence_farmslevel", level:212});
MATCH (g: Goods{code: 7}), (b: Building{id:2941}) CREATE (b)-[r:Supply{max_supply: 45.9404, current_output: 45.9404,level: 212}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2941}) CREATE (b)-[r:Supply{max_supply: 9.18808, current_output: 9.18808,level: 212}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2941}) CREATE (b)-[r:Supply{max_supply: 9.18808, current_output: 9.18808,level: 212}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2941}) CREATE (b)-[r:Supply{max_supply: 9.18808, current_output: 9.18808,level: 212}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2941}) CREATE (b)-[r:Supply{max_supply: 9.18808, current_output: 9.18808,level: 212}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2941}) CREATE (b)-[r:Supply{max_supply: 9.18808, current_output: 9.18808,level: 212}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2941}) CREATE (b)-[r:Supply{max_supply: 12.86331, current_output: 12.86331,level: 212}]->(g);
CREATE (n: Building {id: 2942, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2942}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.5999770922487575, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2942}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.2543518595695917, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2942}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.7680744852191603, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2942}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.97772836717933,level: 1}]->(g);
CREATE (n: Building {id: 2943, name:"building_subsistence_farmslevel", level:179});
MATCH (g: Goods{code: 7}), (b: Building{id:2943}) CREATE (b)-[r:Supply{max_supply: 9.58545, current_output: 9.58545,level: 179}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2943}) CREATE (b)-[r:Supply{max_supply: 1.91709, current_output: 1.91709,level: 179}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2943}) CREATE (b)-[r:Supply{max_supply: 1.91709, current_output: 1.91709,level: 179}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2943}) CREATE (b)-[r:Supply{max_supply: 1.91709, current_output: 1.91709,level: 179}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2943}) CREATE (b)-[r:Supply{max_supply: 1.91709, current_output: 1.91709,level: 179}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2943}) CREATE (b)-[r:Supply{max_supply: 1.91709, current_output: 1.91709,level: 179}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2943}) CREATE (b)-[r:Supply{max_supply: 2.68392, current_output: 2.68392,level: 179}]->(g);
CREATE (n: Building {id: 2944, name:"building_subsistence_farmslevel", level:172});
MATCH (g: Goods{code: 7}), (b: Building{id:2944}) CREATE (b)-[r:Supply{max_supply: 117.8286, current_output: 117.8286,level: 172}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2944}) CREATE (b)-[r:Supply{max_supply: 23.56572, current_output: 23.56572,level: 172}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2944}) CREATE (b)-[r:Supply{max_supply: 23.56572, current_output: 23.56572,level: 172}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2944}) CREATE (b)-[r:Supply{max_supply: 23.56572, current_output: 23.56572,level: 172}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2944}) CREATE (b)-[r:Supply{max_supply: 23.56572, current_output: 23.56572,level: 172}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2944}) CREATE (b)-[r:Supply{max_supply: 23.56572, current_output: 23.56572,level: 172}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2944}) CREATE (b)-[r:Supply{max_supply: 32.992, current_output: 32.992,level: 172}]->(g);
CREATE (n: Building {id: 2945, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2945}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.199954184497515, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2945}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.508703719139183, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2945}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.5361489704383207, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2945}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 41.95545673435866,level: 2}]->(g);
CREATE (n: Building {id: 2946, name:"building_subsistence_farmslevel", level:166});
MATCH (g: Goods{code: 7}), (b: Building{id:2946}) CREATE (b)-[r:Supply{max_supply: 32.0878, current_output: 32.0878,level: 166}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2946}) CREATE (b)-[r:Supply{max_supply: 6.41756, current_output: 6.41756,level: 166}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2946}) CREATE (b)-[r:Supply{max_supply: 6.41756, current_output: 6.41756,level: 166}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2946}) CREATE (b)-[r:Supply{max_supply: 6.41756, current_output: 6.41756,level: 166}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2946}) CREATE (b)-[r:Supply{max_supply: 6.41756, current_output: 6.41756,level: 166}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2946}) CREATE (b)-[r:Supply{max_supply: 6.41756, current_output: 6.41756,level: 166}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2946}) CREATE (b)-[r:Supply{max_supply: 8.98458, current_output: 8.98458,level: 166}]->(g);
CREATE (n: Building {id: 2947, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2947}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.199954184497515, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2947}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.508703719139183, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2947}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.5361489704383207, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2947}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 41.95545673435866,level: 2}]->(g);
CREATE (n: Building {id: 2948, name:"building_subsistence_farmslevel", level:155});
MATCH (g: Goods{code: 7}), (b: Building{id:2948}) CREATE (b)-[r:Supply{max_supply: 17.96837, current_output: 17.96837,level: 155}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2948}) CREATE (b)-[r:Supply{max_supply: 3.59367, current_output: 3.59367,level: 155}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2948}) CREATE (b)-[r:Supply{max_supply: 3.59367, current_output: 3.59367,level: 155}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2948}) CREATE (b)-[r:Supply{max_supply: 3.59367, current_output: 3.59367,level: 155}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2948}) CREATE (b)-[r:Supply{max_supply: 3.59367, current_output: 3.59367,level: 155}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2948}) CREATE (b)-[r:Supply{max_supply: 3.59367, current_output: 3.59367,level: 155}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2948}) CREATE (b)-[r:Supply{max_supply: 5.03114, current_output: 5.03114,level: 155}]->(g);
CREATE (n: Building {id: 2949, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2949}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.199954184497515, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2949}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.508703719139183, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2949}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.5361489704383207, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2949}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 41.95545673435866,level: 2}]->(g);
CREATE (n: Building {id: 2950, name:"building_subsistence_farmslevel", level:150});
MATCH (g: Goods{code: 7}), (b: Building{id:2950}) CREATE (b)-[r:Supply{max_supply: 5.0925, current_output: 5.0925,level: 150}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2950}) CREATE (b)-[r:Supply{max_supply: 1.0185, current_output: 1.0185,level: 150}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2950}) CREATE (b)-[r:Supply{max_supply: 1.0185, current_output: 1.0185,level: 150}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2950}) CREATE (b)-[r:Supply{max_supply: 1.0185, current_output: 1.0185,level: 150}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2950}) CREATE (b)-[r:Supply{max_supply: 1.0185, current_output: 1.0185,level: 150}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2950}) CREATE (b)-[r:Supply{max_supply: 1.0185, current_output: 1.0185,level: 150}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2950}) CREATE (b)-[r:Supply{max_supply: 1.4259, current_output: 1.4259,level: 150}]->(g);
CREATE (n: Building {id: 2951, name:"building_subsistence_farmslevel", level:149});
MATCH (g: Goods{code: 7}), (b: Building{id:2951}) CREATE (b)-[r:Supply{max_supply: 14.29282, current_output: 14.29282,level: 149}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2951}) CREATE (b)-[r:Supply{max_supply: 2.85856, current_output: 2.85856,level: 149}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2951}) CREATE (b)-[r:Supply{max_supply: 2.85856, current_output: 2.85856,level: 149}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2951}) CREATE (b)-[r:Supply{max_supply: 2.85856, current_output: 2.85856,level: 149}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2951}) CREATE (b)-[r:Supply{max_supply: 2.85856, current_output: 2.85856,level: 149}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2951}) CREATE (b)-[r:Supply{max_supply: 2.85856, current_output: 2.85856,level: 149}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2951}) CREATE (b)-[r:Supply{max_supply: 4.00199, current_output: 4.00199,level: 149}]->(g);
CREATE (n: Building {id: 2952, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2952}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.5999770922487575, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2952}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.2543518595695917, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2952}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.7680744852191603, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2952}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.97772836717933,level: 1}]->(g);
CREATE (n: Building {id: 2953, name:"building_subsistence_farmslevel", level:100});
MATCH (g: Goods{code: 7}), (b: Building{id:2953}) CREATE (b)-[r:Supply{max_supply: 14.8825, current_output: 14.8825,level: 100}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2953}) CREATE (b)-[r:Supply{max_supply: 2.9765, current_output: 2.9765,level: 100}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2953}) CREATE (b)-[r:Supply{max_supply: 2.9765, current_output: 2.9765,level: 100}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2953}) CREATE (b)-[r:Supply{max_supply: 2.9765, current_output: 2.9765,level: 100}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2953}) CREATE (b)-[r:Supply{max_supply: 2.9765, current_output: 2.9765,level: 100}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2953}) CREATE (b)-[r:Supply{max_supply: 2.9765, current_output: 2.9765,level: 100}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2953}) CREATE (b)-[r:Supply{max_supply: 4.1671, current_output: 4.1671,level: 100}]->(g);
CREATE (n: Building {id: 2954, name:"building_subsistence_farmslevel", level:159});
MATCH (g: Goods{code: 7}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 11.50365, current_output: 11.50365,level: 159}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 2.30073, current_output: 2.30073,level: 159}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 2.30073, current_output: 2.30073,level: 159}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 2.30073, current_output: 2.30073,level: 159}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 2.30073, current_output: 2.30073,level: 159}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 2.30073, current_output: 2.30073,level: 159}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 3.22102, current_output: 3.22102,level: 159}]->(g);
CREATE (n: Building {id: 2955, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2955}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.5999770922487575, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2955}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.2543518595695917, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2955}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.7680744852191603, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2955}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.97772836717933,level: 1}]->(g);
CREATE (n: Building {id: 2956, name:"building_urban_centerlevel", level:1});
CREATE (n: Building {id: 2957, name:"building_subsistence_farmslevel", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 21.4775, current_output: 21.4775,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 4.2955, current_output: 4.2955,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 4.2955, current_output: 4.2955,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 4.2955, current_output: 4.2955,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 4.2955, current_output: 4.2955,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 4.2955, current_output: 4.2955,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 6.0137, current_output: 6.0137,level: 50}]->(g);
CREATE (n: Building {id: 2958, name:"building_subsistence_farmslevel", level:21});
MATCH (g: Goods{code: 7}), (b: Building{id:2958}) CREATE (b)-[r:Supply{max_supply: 14.8092, current_output: 14.8092,level: 21}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2958}) CREATE (b)-[r:Supply{max_supply: 2.96184, current_output: 2.96184,level: 21}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2958}) CREATE (b)-[r:Supply{max_supply: 2.96184, current_output: 2.96184,level: 21}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2958}) CREATE (b)-[r:Supply{max_supply: 2.96184, current_output: 2.96184,level: 21}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2958}) CREATE (b)-[r:Supply{max_supply: 2.96184, current_output: 2.96184,level: 21}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2958}) CREATE (b)-[r:Supply{max_supply: 2.96184, current_output: 2.96184,level: 21}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2958}) CREATE (b)-[r:Supply{max_supply: 4.14657, current_output: 4.14657,level: 21}]->(g);
CREATE (n: Building {id: 2959, name:"building_subsistence_farmslevel", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:2959}) CREATE (b)-[r:Supply{max_supply: 4.50975, current_output: 4.50975,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2959}) CREATE (b)-[r:Supply{max_supply: 0.90195, current_output: 0.90195,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2959}) CREATE (b)-[r:Supply{max_supply: 0.90195, current_output: 0.90195,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2959}) CREATE (b)-[r:Supply{max_supply: 0.90195, current_output: 0.90195,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2959}) CREATE (b)-[r:Supply{max_supply: 0.90195, current_output: 0.90195,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2959}) CREATE (b)-[r:Supply{max_supply: 0.90195, current_output: 0.90195,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2959}) CREATE (b)-[r:Supply{max_supply: 1.26273, current_output: 1.26273,level: 14}]->(g);
CREATE (n: Building {id: 2960, name:"building_subsistence_farmslevel", level:173});
MATCH (g: Goods{code: 7}), (b: Building{id:2960}) CREATE (b)-[r:Supply{max_supply: 102.97825, current_output: 102.97825,level: 173}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2960}) CREATE (b)-[r:Supply{max_supply: 20.59565, current_output: 20.59565,level: 173}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2960}) CREATE (b)-[r:Supply{max_supply: 20.59565, current_output: 20.59565,level: 173}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2960}) CREATE (b)-[r:Supply{max_supply: 20.59565, current_output: 20.59565,level: 173}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2960}) CREATE (b)-[r:Supply{max_supply: 20.59565, current_output: 20.59565,level: 173}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2960}) CREATE (b)-[r:Supply{max_supply: 20.59565, current_output: 20.59565,level: 173}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2960}) CREATE (b)-[r:Supply{max_supply: 28.83391, current_output: 28.83391,level: 173}]->(g);
CREATE (n: Building {id: 2961, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2961}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.39990836899503, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2961}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 9.017407438278367, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2961}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.072297940876641, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2961}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 83.91091346871731,level: 4}]->(g);
CREATE (n: Building {id: 2962, name:"building_subsistence_farmslevel", level:39});
MATCH (g: Goods{code: 7}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 21.6255, current_output: 21.6255,level: 39}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 4.3251, current_output: 4.3251,level: 39}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 4.3251, current_output: 4.3251,level: 39}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 4.3251, current_output: 4.3251,level: 39}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 4.3251, current_output: 4.3251,level: 39}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 4.3251, current_output: 4.3251,level: 39}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 6.05514, current_output: 6.05514,level: 39}]->(g);
CREATE (n: Building {id: 2963, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2963}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.5999770922487575, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2963}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.2543518595695917, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2963}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.7680744852191603, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2963}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.97772836717933,level: 1}]->(g);
CREATE (n: Building {id: 2964, name:"building_subsistence_farmslevel", level:268});
MATCH (g: Goods{code: 7}), (b: Building{id:2964}) CREATE (b)-[r:Supply{max_supply: 45.0977, current_output: 45.0977,level: 268}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2964}) CREATE (b)-[r:Supply{max_supply: 9.01954, current_output: 9.01954,level: 268}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2964}) CREATE (b)-[r:Supply{max_supply: 9.01954, current_output: 9.01954,level: 268}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2964}) CREATE (b)-[r:Supply{max_supply: 9.01954, current_output: 9.01954,level: 268}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2964}) CREATE (b)-[r:Supply{max_supply: 9.01954, current_output: 9.01954,level: 268}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2964}) CREATE (b)-[r:Supply{max_supply: 9.01954, current_output: 9.01954,level: 268}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2964}) CREATE (b)-[r:Supply{max_supply: 12.62735, current_output: 12.62735,level: 268}]->(g);
CREATE (n: Building {id: 2965, name:"building_subsistence_fishing_villageslevel", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 3.51705, current_output: 3.51705,level: 15}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 14.0682, current_output: 14.0682,level: 15}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 1.75852, current_output: 1.75852,level: 15}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 5.27557, current_output: 5.27557,level: 15}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 3.51705, current_output: 3.51705,level: 15}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 3.51705, current_output: 3.51705,level: 15}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 3.51705, current_output: 3.51705,level: 15}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 4.92387, current_output: 4.92387,level: 15}]->(g);
CREATE (n: Building {id: 2966, name:"building_subsistence_fishing_villageslevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:2966}) CREATE (b)-[r:Supply{max_supply: 1.1592, current_output: 1.1592,level: 10}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:2966}) CREATE (b)-[r:Supply{max_supply: 4.6368, current_output: 4.6368,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2966}) CREATE (b)-[r:Supply{max_supply: 0.5796, current_output: 0.5796,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2966}) CREATE (b)-[r:Supply{max_supply: 1.7388, current_output: 1.7388,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2966}) CREATE (b)-[r:Supply{max_supply: 1.1592, current_output: 1.1592,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2966}) CREATE (b)-[r:Supply{max_supply: 1.1592, current_output: 1.1592,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2966}) CREATE (b)-[r:Supply{max_supply: 1.1592, current_output: 1.1592,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2966}) CREATE (b)-[r:Supply{max_supply: 1.62288, current_output: 1.62288,level: 10}]->(g);
CREATE (n: Building {id: 2967, name:"building_subsistence_fishing_villageslevel", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:2967}) CREATE (b)-[r:Supply{max_supply: 7.20468, current_output: 7.20468,level: 18}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:2967}) CREATE (b)-[r:Supply{max_supply: 28.81872, current_output: 28.81872,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2967}) CREATE (b)-[r:Supply{max_supply: 3.60234, current_output: 3.60234,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2967}) CREATE (b)-[r:Supply{max_supply: 10.80702, current_output: 10.80702,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2967}) CREATE (b)-[r:Supply{max_supply: 7.20468, current_output: 7.20468,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2967}) CREATE (b)-[r:Supply{max_supply: 7.20468, current_output: 7.20468,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2967}) CREATE (b)-[r:Supply{max_supply: 7.20468, current_output: 7.20468,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2967}) CREATE (b)-[r:Supply{max_supply: 10.08655, current_output: 10.08655,level: 18}]->(g);
CREATE (n: Building {id: 2968, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2968}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 4.799931276746272, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2968}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.763055578708776, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2968}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.30422345565748, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2968}) CREATE (b)-[r:Supply{max_supply: 209.99999999999997, current_output: 62.93318510153799,level: 3}]->(g);
CREATE (n: Building {id: 2969, name:"building_subsistence_farmslevel", level:22});
MATCH (g: Goods{code: 7}), (b: Building{id:2969}) CREATE (b)-[r:Supply{max_supply: 23.0824, current_output: 23.0824,level: 22}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2969}) CREATE (b)-[r:Supply{max_supply: 4.61648, current_output: 4.61648,level: 22}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2969}) CREATE (b)-[r:Supply{max_supply: 4.61648, current_output: 4.61648,level: 22}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2969}) CREATE (b)-[r:Supply{max_supply: 4.61648, current_output: 4.61648,level: 22}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2969}) CREATE (b)-[r:Supply{max_supply: 4.61648, current_output: 4.61648,level: 22}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2969}) CREATE (b)-[r:Supply{max_supply: 4.61648, current_output: 4.61648,level: 22}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2969}) CREATE (b)-[r:Supply{max_supply: 6.46307, current_output: 6.46307,level: 22}]->(g);
CREATE (n: Building {id: 2970, name:"building_subsistence_farmslevel", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:2970}) CREATE (b)-[r:Supply{max_supply: 27.58385, current_output: 27.58385,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2970}) CREATE (b)-[r:Supply{max_supply: 5.51677, current_output: 5.51677,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2970}) CREATE (b)-[r:Supply{max_supply: 5.51677, current_output: 5.51677,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2970}) CREATE (b)-[r:Supply{max_supply: 5.51677, current_output: 5.51677,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2970}) CREATE (b)-[r:Supply{max_supply: 5.51677, current_output: 5.51677,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2970}) CREATE (b)-[r:Supply{max_supply: 5.51677, current_output: 5.51677,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2970}) CREATE (b)-[r:Supply{max_supply: 7.72347, current_output: 7.72347,level: 14}]->(g);
CREATE (n: Building {id: 2971, name:"building_subsistence_fishing_villageslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:2971}) CREATE (b)-[r:Supply{max_supply: 3.5869, current_output: 3.5869,level: 20}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:2971}) CREATE (b)-[r:Supply{max_supply: 14.3476, current_output: 14.3476,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2971}) CREATE (b)-[r:Supply{max_supply: 1.79345, current_output: 1.79345,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2971}) CREATE (b)-[r:Supply{max_supply: 5.38035, current_output: 5.38035,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2971}) CREATE (b)-[r:Supply{max_supply: 3.5869, current_output: 3.5869,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2971}) CREATE (b)-[r:Supply{max_supply: 3.5869, current_output: 3.5869,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2971}) CREATE (b)-[r:Supply{max_supply: 3.5869, current_output: 3.5869,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2971}) CREATE (b)-[r:Supply{max_supply: 5.02166, current_output: 5.02166,level: 20}]->(g);
CREATE (n: Building {id: 2972, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2972}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.5999770922487575, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2972}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.2543518595695917, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2972}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.7680744852191603, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2972}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.97772836717933,level: 1}]->(g);
CREATE (n: Building {id: 2973, name:"building_subsistence_farmslevel", level:165});
MATCH (g: Goods{code: 7}), (b: Building{id:2973}) CREATE (b)-[r:Supply{max_supply: 34.89337, current_output: 34.89337,level: 165}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2973}) CREATE (b)-[r:Supply{max_supply: 6.97867, current_output: 6.97867,level: 165}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2973}) CREATE (b)-[r:Supply{max_supply: 6.97867, current_output: 6.97867,level: 165}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2973}) CREATE (b)-[r:Supply{max_supply: 6.97867, current_output: 6.97867,level: 165}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2973}) CREATE (b)-[r:Supply{max_supply: 6.97867, current_output: 6.97867,level: 165}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2973}) CREATE (b)-[r:Supply{max_supply: 6.97867, current_output: 6.97867,level: 165}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2973}) CREATE (b)-[r:Supply{max_supply: 9.77014, current_output: 9.77014,level: 165}]->(g);
CREATE (n: Building {id: 2974, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2974}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.5999770922487575, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2974}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.2543518595695917, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2974}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.7680744852191603, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2974}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.97772836717933,level: 1}]->(g);
CREATE (n: Building {id: 3946, name:"building_subsistence_farmslevel", level:80});
MATCH (g: Goods{code: 7}), (b: Building{id:3946}) CREATE (b)-[r:Supply{max_supply: 12.808, current_output: 12.808,level: 80}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3946}) CREATE (b)-[r:Supply{max_supply: 2.5616, current_output: 2.5616,level: 80}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3946}) CREATE (b)-[r:Supply{max_supply: 2.5616, current_output: 2.5616,level: 80}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3946}) CREATE (b)-[r:Supply{max_supply: 2.5616, current_output: 2.5616,level: 80}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3946}) CREATE (b)-[r:Supply{max_supply: 2.5616, current_output: 2.5616,level: 80}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3946}) CREATE (b)-[r:Supply{max_supply: 2.5616, current_output: 2.5616,level: 80}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3946}) CREATE (b)-[r:Supply{max_supply: 3.58624, current_output: 3.58624,level: 80}]->(g);
CREATE (n: Building {id: 3947, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3947}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.5999770922487575, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3947}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.2543518595695917, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3947}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.7680744852191603, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3947}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.97772836717933,level: 1}]->(g);
CREATE (n: Building {id: 3956, name:"building_trade_centerlevel", level:16});
CREATE (n: Building {id: 3957, name:"building_trade_centerlevel", level:60});
CREATE (n: Building {id: 3958, name:"building_trade_centerlevel", level:17});
CREATE (n: Building {id: 3959, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3959}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.5999770922487575, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3959}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.2543518595695917, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3959}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.7680744852191603, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3959}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.97772836717933,level: 1}]->(g);
CREATE (n: Building {id: 4037, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4038, name:"building_conscription_centerlevel", level:38});
CREATE (n: Building {id: 4039, name:"building_conscription_centerlevel", level:18});
CREATE (n: Building {id: 4069, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4070, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4072, name:"building_conscription_centerlevel", level:11});
CREATE (n: Building {id: 4073, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4074, name:"building_conscription_centerlevel", level:23});
CREATE (n: Building {id: 4075, name:"building_conscription_centerlevel", level:15});
CREATE (n: Building {id: 4076, name:"building_conscription_centerlevel", level:15});
CREATE (n: Building {id: 4077, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4078, name:"building_conscription_centerlevel", level:14});
CREATE (n: Building {id: 4079, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4080, name:"building_conscription_centerlevel", level:14});
CREATE (n: Building {id: 4081, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4082, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4083, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4084, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4085, name:"building_conscription_centerlevel", level:26});
CREATE (n: Building {id: 4086, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4087, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4088, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4089, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4090, name:"building_conscription_centerlevel", level:14});
CREATE (n: Building {id: 4091, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4092, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4093, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4094, name:"building_conscription_centerlevel", level:16});
CREATE (n: Building {id: 4642, name:"building_conscription_centerlevel", level:15});
