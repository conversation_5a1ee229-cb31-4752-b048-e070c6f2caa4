CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:14.30103554957357, pop_demand:2854.945766054345});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:14.228969559384241, pop_demand:135.2441993617192});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:11.402155930377692, pop_demand:45.24826806128882});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:17.166976035136535, pop_demand:270.17363864170164});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:26.26382676641565, pop_demand:758.8813409602889});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:24.952157058860585, pop_demand:705.8323718476561});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:32.44955885616782, pop_demand:39.20755058738327});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:18.77783658506942, pop_demand:517.7712719223558});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:44.96197929706403, pop_demand:211.92565833333316});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:59.87728108562806, pop_demand:9.904551955488495});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:33.03754266211604, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:15.439145749525164, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:49.38698112700099, pop_demand:160.0929985235045});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:27.344167528027903, pop_demand:28.906945880046504});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:99.65480090554853, pop_demand:229.80758356687267});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:37.664412731451336, pop_demand:168.30767505464183});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:36.33681884980158, pop_demand:274.14477594626254});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:26.555706186729285, pop_demand:694.4632428634162});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:38.78084757194246, pop_demand:350.69163000000015});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:20.07763731336806, pop_demand:158.7153605372523});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:35.39753767431215, pop_demand:375.8726281727739});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:87.5, pop_demand:0.24874624373121867});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:81.85730233082664, pop_demand:350.00241180998523});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:70.00048236199697});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 2446, name:"building_barracks", level:2});
CREATE (n: Building {id: 2447, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2447}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 26.733921525109572, level: 3}]->(b);
CREATE (n: Building {id: 2448, name:"building_logging_camp", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2448}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 156.0,level: 5}]->(g);
CREATE (n: Building {id: 2449, name:"building_rice_farm", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2449}) CREATE (b)-[r:Supply{max_supply: 9.683, current_output: 11.6196,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2449}) CREATE (b)-[r:Supply{max_supply: 11.6196, current_output: 13.94352,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2449}) CREATE (b)-[r:Supply{max_supply: 17.4294, current_output: 20.91528,level: 1}]->(g);
CREATE (n: Building {id: 2450, name:"building_tobacco_plantation", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:2450}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 91.5,level: 3}]->(g);
CREATE (n: Building {id: 2451, name:"building_barracks", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:2451}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.6127236499690312, level: 16}]->(b);
CREATE (n: Building {id: 2452, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2452}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 26.733921525109572, level: 3}]->(b);
CREATE (n: Building {id: 2453, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2453}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 36.98536092291647, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2453}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 27.57786, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2453}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2453}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 2454, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:2454}) CREATE (b)-[r:Supply{max_supply: 74.90474509803921, current_output: 76.40284,level: 3}]->(g);
CREATE (n: Building {id: 2455, name:"building_whaling_station", level:5});
MATCH (g: Goods{code: 18}), (b: Building{id:2455}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 25.0683636081572, level: 5}]->(b);
MATCH (g: Goods{code: 28}), (b: Building{id:2455}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 100.0,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2455}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 5}]->(g);
CREATE (n: Building {id: 2456, name:"building_rice_farm", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2456}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 26.2,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2456}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 31.44,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2456}) CREATE (b)-[r:Supply{max_supply: 35.99999999999999, current_output: 47.16,level: 2}]->(g);
CREATE (n: Building {id: 2457, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2457}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 60.5,level: 2}]->(g);
CREATE (n: Building {id: 2458, name:"building_tea_plantation", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2458}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 98.4,level: 4}]->(g);
CREATE (n: Building {id: 2459, name:"building_port", level:1});
CREATE (n: Building {id: 2460, name:"building_barracks", level:20});
CREATE (n: Building {id: 2461, name:"building_government_administration", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:2461}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 44.55653587518262, level: 5}]->(b);
CREATE (n: Building {id: 2462, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2462}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 58.57401851470947, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2462}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 92.46340230729119, level: 1}]->(b);
CREATE (n: Building {id: 2463, name:"building_paper_mills", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2463}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 73.97072184583294, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2463}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 80.0,level: 2}]->(g);
CREATE (n: Building {id: 2464, name:"building_furniture_manufacturies", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2464}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 46.85921481176758, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2464}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 73.97072184583294, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2464}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 90.0,level: 2}]->(g);
CREATE (n: Building {id: 2465, name:"building_logging_camp", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2465}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 123.6,level: 4}]->(g);
CREATE (n: Building {id: 2466, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2466}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 16.127236499690312, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2466}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2466}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 2}]->(g);
CREATE (n: Building {id: 2467, name:"building_rice_farm", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2467}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 24.2,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2467}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 29.04,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2467}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 43.56,level: 2}]->(g);
CREATE (n: Building {id: 2468, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2468}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 60.5,level: 2}]->(g);
CREATE (n: Building {id: 2469, name:"building_tea_plantation", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2469}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 98.4,level: 4}]->(g);
CREATE (n: Building {id: 2470, name:"building_barracks", level:15});
CREATE (n: Building {id: 2471, name:"building_government_administration", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:2471}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 62.37915022525567, level: 7}]->(b);
CREATE (n: Building {id: 2472, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2472}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 58.57401851470947, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2472}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 92.46340230729119, level: 1}]->(b);
CREATE (n: Building {id: 2473, name:"building_textile_mills", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:2473}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 234.2960740588379, level: 4}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2473}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 78.13333333333333, level: 4}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2473}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 59.99999999999999,level: 4}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2473}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 119.99999999999999,level: 4}]->(g);
CREATE (n: Building {id: 2474, name:"building_paper_mills", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2474}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 110.95608276874943, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2474}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 3}]->(g);
CREATE (n: Building {id: 2475, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:2475}) CREATE (b)-[r:Supply{max_supply: 74.8545, current_output: 76.35159,level: 3}]->(g);
CREATE (n: Building {id: 2476, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2476}) CREATE (g)-[r:Demand{max_demand: 9.928396694214877, current_input: 16.01176015503468, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2476}) CREATE (b)-[r:Supply{max_supply: 19.856793388429754, current_output: 19.856793388429754,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2476}) CREATE (b)-[r:Supply{max_supply: 19.856793388429754, current_output: 19.856793388429754,level: 2}]->(g);
CREATE (n: Building {id: 2477, name:"building_rice_farm", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2477}) CREATE (b)-[r:Supply{max_supply: 29.78489393939394, current_output: 39.31606,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2477}) CREATE (b)-[r:Supply{max_supply: 35.74187878787878, current_output: 47.17928,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2477}) CREATE (b)-[r:Supply{max_supply: 53.61281818181818, current_output: 70.76892,level: 3}]->(g);
CREATE (n: Building {id: 2478, name:"building_silk_plantation", level:3});
MATCH (g: Goods{code: 20}), (b: Building{id:2478}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 88.2,level: 3}]->(g);
CREATE (n: Building {id: 2479, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2479}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 60.5,level: 2}]->(g);
CREATE (n: Building {id: 2480, name:"building_tea_plantation", level:5});
MATCH (g: Goods{code: 40}), (b: Building{id:2480}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 124.0,level: 5}]->(g);
CREATE (n: Building {id: 2481, name:"building_barracks", level:22});
MATCH (g: Goods{code: 7}), (b: Building{id:2481}) CREATE (g)-[r:Demand{max_demand: 1.9998, current_input: 3.2251247552080686, level: 22}]->(b);
CREATE (n: Building {id: 2482, name:"building_government_administration", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:2482}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 35.6452287001461, level: 4}]->(b);
CREATE (n: Building {id: 2483, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2483}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 46.85921481176758, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2483}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 49.31381456388863, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:2483}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 35.0,level: 1}]->(g);
CREATE (n: Building {id: 2484, name:"building_textile_mills", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2484}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 117.14803702941894, level: 2}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2484}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 39.06666666666667, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2484}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 2}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2484}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 2}]->(g);
CREATE (n: Building {id: 2485, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2485}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 36.98536092291647, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2485}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 27.57786, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2485}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2485}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 2486, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:2486}) CREATE (b)-[r:Supply{max_supply: 43.025, current_output: 43.45525,level: 2}]->(g);
CREATE (n: Building {id: 2487, name:"building_rice_farm", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2487}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.2,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2487}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 24.24,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2487}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 36.36,level: 2}]->(g);
CREATE (n: Building {id: 2488, name:"building_dye_plantation", level:2});
MATCH (g: Goods{code: 21}), (b: Building{id:2488}) CREATE (b)-[r:Supply{max_supply: 45.93, current_output: 46.3893,level: 2}]->(g);
CREATE (n: Building {id: 2489, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2489}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 2490, name:"building_tea_plantation", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2490}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 82.4,level: 4}]->(g);
CREATE (n: Building {id: 2491, name:"building_barracks", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:2491}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.6127236499690312, level: 16}]->(b);
CREATE (n: Building {id: 2492, name:"building_port", level:1});
CREATE (n: Building {id: 2493, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:2493}) CREATE (b)-[r:Supply{max_supply: 23.605, current_output: 23.605,level: 1}]->(g);
CREATE (n: Building {id: 2494, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2494}) CREATE (g)-[r:Demand{max_demand: 1.72645, current_input: 2.784286745489034, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2494}) CREATE (b)-[r:Supply{max_supply: 3.4529, current_output: 3.4529,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2494}) CREATE (b)-[r:Supply{max_supply: 3.4529, current_output: 3.4529,level: 1}]->(g);
CREATE (n: Building {id: 2495, name:"building_port", level:1});
CREATE (n: Building {id: 2496, name:"building_government_administration", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:2496}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 35.6452287001461, level: 4}]->(b);
CREATE (n: Building {id: 2497, name:"building_paper_mills", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2497}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 73.97072184583294, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2497}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 80.0,level: 2}]->(g);
CREATE (n: Building {id: 2498, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2498}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 36.98536092291647, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2498}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 27.57786, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2498}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2498}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 2499, name:"building_logging_camp", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2499}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 189.0,level: 6}]->(g);
CREATE (n: Building {id: 2500, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2500}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.063618249845156, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2500}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2500}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
CREATE (n: Building {id: 2501, name:"building_rice_farm", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2501}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 24.2,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2501}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 29.04,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2501}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 43.56,level: 2}]->(g);
CREATE (n: Building {id: 2502, name:"building_silk_plantation", level:1});
MATCH (g: Goods{code: 20}), (b: Building{id:2502}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 29.0,level: 1}]->(g);
CREATE (n: Building {id: 2503, name:"building_tobacco_plantation", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:2503}) CREATE (b)-[r:Supply{max_supply: 74.16974590163935, current_output: 90.48709,level: 3}]->(g);
CREATE (n: Building {id: 2504, name:"building_tea_plantation", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:2504}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 24.0,level: 1}]->(g);
CREATE (n: Building {id: 2505, name:"building_barracks", level:10});
CREATE (n: Building {id: 2506, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:2506}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.911307175036525, level: 1}]->(b);
CREATE (n: Building {id: 2507, name:"building_glassworks", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2507}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 73.97072184583294, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2507}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 55.15572, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2507}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 2}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2507}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 2}]->(g);
CREATE (n: Building {id: 2508, name:"building_rice_farm", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2508}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 12.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2508}) CREATE (b)-[r:Supply{max_supply: 12.0, current_output: 14.4,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2508}) CREATE (b)-[r:Supply{max_supply: 18.000000000000004, current_output: 21.6,level: 1}]->(g);
CREATE (n: Building {id: 2509, name:"building_dye_plantation", level:3});
MATCH (g: Goods{code: 21}), (b: Building{id:2509}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 91.5,level: 3}]->(g);
CREATE (n: Building {id: 2510, name:"building_tobacco_plantation", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:2510}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 2511, name:"building_tea_plantation", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:2511}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 73.2,level: 3}]->(g);
CREATE (n: Building {id: 2512, name:"building_barracks", level:5});
CREATE (n: Building {id: 3501, name:"building_subsistence_rice_paddies", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3501}) CREATE (b)-[r:Supply{max_supply: 8.9353, current_output: 8.9353,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3501}) CREATE (b)-[r:Supply{max_supply: 1.21845, current_output: 1.21845,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3501}) CREATE (b)-[r:Supply{max_supply: 1.21845, current_output: 1.21845,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3501}) CREATE (b)-[r:Supply{max_supply: 1.6246, current_output: 1.6246,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3501}) CREATE (b)-[r:Supply{max_supply: 1.6246, current_output: 1.6246,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3501}) CREATE (b)-[r:Supply{max_supply: 1.6246, current_output: 1.6246,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3501}) CREATE (b)-[r:Supply{max_supply: 1.6246, current_output: 1.6246,level: 20}]->(g);
CREATE (n: Building {id: 3502, name:"building_subsistence_rice_paddies", level:76});
MATCH (g: Goods{code: 7}), (b: Building{id:3502}) CREATE (b)-[r:Supply{max_supply: 417.05531666666667, current_output: 500.46638,level: 76}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3502}) CREATE (b)-[r:Supply{max_supply: 56.87117500000001, current_output: 68.24541,level: 76}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3502}) CREATE (b)-[r:Supply{max_supply: 56.87117500000001, current_output: 68.24541,level: 76}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3502}) CREATE (b)-[r:Supply{max_supply: 75.82823333333334, current_output: 90.99388,level: 76}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3502}) CREATE (b)-[r:Supply{max_supply: 75.82823333333334, current_output: 90.99388,level: 76}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3502}) CREATE (b)-[r:Supply{max_supply: 75.82823333333334, current_output: 90.99388,level: 76}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3502}) CREATE (b)-[r:Supply{max_supply: 75.82823333333334, current_output: 90.99388,level: 76}]->(g);
CREATE (n: Building {id: 3503, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3503}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3503}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 3504, name:"building_subsistence_rice_paddies", level:122});
MATCH (g: Goods{code: 7}), (b: Building{id:3504}) CREATE (b)-[r:Supply{max_supply: 656.3722, current_output: 853.28386,level: 122}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3504}) CREATE (b)-[r:Supply{max_supply: 89.5053, current_output: 116.35689,level: 122}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3504}) CREATE (b)-[r:Supply{max_supply: 89.5053, current_output: 116.35689,level: 122}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3504}) CREATE (b)-[r:Supply{max_supply: 119.34039999999999, current_output: 155.14252,level: 122}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3504}) CREATE (b)-[r:Supply{max_supply: 119.34039999999999, current_output: 155.14252,level: 122}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3504}) CREATE (b)-[r:Supply{max_supply: 119.34039999999999, current_output: 155.14252,level: 122}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3504}) CREATE (b)-[r:Supply{max_supply: 119.34039999999999, current_output: 155.14252,level: 122}]->(g);
CREATE (n: Building {id: 3505, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:3505}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3505}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.1,level: 2}]->(g);
CREATE (n: Building {id: 3506, name:"building_subsistence_rice_paddies", level:140});
MATCH (g: Goods{code: 7}), (b: Building{id:3506}) CREATE (b)-[r:Supply{max_supply: 745.8682, current_output: 895.04184,level: 140}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3506}) CREATE (b)-[r:Supply{max_supply: 101.7093, current_output: 122.05116,level: 140}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3506}) CREATE (b)-[r:Supply{max_supply: 101.7093, current_output: 122.05116,level: 140}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3506}) CREATE (b)-[r:Supply{max_supply: 135.6124, current_output: 162.73488,level: 140}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3506}) CREATE (b)-[r:Supply{max_supply: 135.6124, current_output: 162.73488,level: 140}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3506}) CREATE (b)-[r:Supply{max_supply: 135.6124, current_output: 162.73488,level: 140}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3506}) CREATE (b)-[r:Supply{max_supply: 135.6124, current_output: 162.73488,level: 140}]->(g);
CREATE (n: Building {id: 3507, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:3507}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3507}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.1,level: 2}]->(g);
CREATE (n: Building {id: 3508, name:"building_subsistence_rice_paddies", level:175});
MATCH (g: Goods{code: 7}), (b: Building{id:3508}) CREATE (b)-[r:Supply{max_supply: 960.09375, current_output: 1152.1125,level: 175}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3508}) CREATE (b)-[r:Supply{max_supply: 130.921875, current_output: 157.10625,level: 175}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3508}) CREATE (b)-[r:Supply{max_supply: 130.921875, current_output: 157.10625,level: 175}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3508}) CREATE (b)-[r:Supply{max_supply: 174.5625, current_output: 209.475,level: 175}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3508}) CREATE (b)-[r:Supply{max_supply: 174.5625, current_output: 209.475,level: 175}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3508}) CREATE (b)-[r:Supply{max_supply: 174.5625, current_output: 209.475,level: 175}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3508}) CREATE (b)-[r:Supply{max_supply: 174.5625, current_output: 209.475,level: 175}]->(g);
CREATE (n: Building {id: 3509, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 15}), (b: Building{id:3509}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 61.8,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3509}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.6,level: 4}]->(g);
CREATE (n: Building {id: 3510, name:"building_subsistence_rice_paddies", level:90});
MATCH (g: Goods{code: 7}), (b: Building{id:3510}) CREATE (b)-[r:Supply{max_supply: 494.15355, current_output: 494.15355,level: 90}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3510}) CREATE (b)-[r:Supply{max_supply: 67.38457, current_output: 67.38457,level: 90}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3510}) CREATE (b)-[r:Supply{max_supply: 67.38457, current_output: 67.38457,level: 90}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3510}) CREATE (b)-[r:Supply{max_supply: 89.8461, current_output: 89.8461,level: 90}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3510}) CREATE (b)-[r:Supply{max_supply: 89.8461, current_output: 89.8461,level: 90}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3510}) CREATE (b)-[r:Supply{max_supply: 89.8461, current_output: 89.8461,level: 90}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3510}) CREATE (b)-[r:Supply{max_supply: 89.8461, current_output: 89.8461,level: 90}]->(g);
CREATE (n: Building {id: 3511, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:3511}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3511}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.1,level: 2}]->(g);
CREATE (n: Building {id: 3512, name:"building_subsistence_orchards", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:3512}) CREATE (b)-[r:Supply{max_supply: 5.762675, current_output: 2.30507,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3512}) CREATE (b)-[r:Supply{max_supply: 2.881325, current_output: 1.15253,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3512}) CREATE (b)-[r:Supply{max_supply: 8.643999999999998, current_output: 3.4576,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3512}) CREATE (b)-[r:Supply{max_supply: 5.762675, current_output: 2.30507,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3512}) CREATE (b)-[r:Supply{max_supply: 5.762675, current_output: 2.30507,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3512}) CREATE (b)-[r:Supply{max_supply: 5.762675, current_output: 2.30507,level: 14}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3512}) CREATE (b)-[r:Supply{max_supply: 19.132074999999997, current_output: 7.65283,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3512}) CREATE (b)-[r:Supply{max_supply: 5.762675, current_output: 2.30507,level: 14}]->(g);
CREATE (n: Building {id: 3513, name:"building_subsistence_rice_paddies", level:67});
MATCH (g: Goods{code: 7}), (b: Building{id:3513}) CREATE (b)-[r:Supply{max_supply: 317.296925, current_output: 380.75631,level: 67}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3513}) CREATE (b)-[r:Supply{max_supply: 43.26775833333333, current_output: 51.92131,level: 67}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3513}) CREATE (b)-[r:Supply{max_supply: 43.26775833333333, current_output: 51.92131,level: 67}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3513}) CREATE (b)-[r:Supply{max_supply: 57.69035, current_output: 69.22842,level: 67}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3513}) CREATE (b)-[r:Supply{max_supply: 57.69035, current_output: 69.22842,level: 67}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3513}) CREATE (b)-[r:Supply{max_supply: 57.69035, current_output: 69.22842,level: 67}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3513}) CREATE (b)-[r:Supply{max_supply: 57.69035, current_output: 69.22842,level: 67}]->(g);
CREATE (n: Building {id: 3514, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:3514}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3514}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.1,level: 2}]->(g);
CREATE (n: Building {id: 3515, name:"building_subsistence_rice_paddies", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:3515}) CREATE (b)-[r:Supply{max_supply: 173.40223333333336, current_output: 208.08268,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3515}) CREATE (b)-[r:Supply{max_supply: 23.645758333333333, current_output: 28.37491,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3515}) CREATE (b)-[r:Supply{max_supply: 23.645758333333333, current_output: 28.37491,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3515}) CREATE (b)-[r:Supply{max_supply: 31.527675000000002, current_output: 37.83321,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3515}) CREATE (b)-[r:Supply{max_supply: 31.527675000000002, current_output: 37.83321,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3515}) CREATE (b)-[r:Supply{max_supply: 31.527675000000002, current_output: 37.83321,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3515}) CREATE (b)-[r:Supply{max_supply: 31.527675000000002, current_output: 37.83321,level: 32}]->(g);
CREATE (n: Building {id: 3516, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3516}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3516}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 4240, name:"building_whaling_station", level:1});
MATCH (g: Goods{code: 28}), (b: Building{id:4240}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:4240}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 4633, name:"building_conscription_center", level:19});
CREATE (n: Building {id: 4702, name:"building_conscription_center", level:25});
CREATE (n: Building {id: 4753, name:"building_conscription_center", level:11});
CREATE (n: Building {id: 4916, name:"building_university", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:4916}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.455653587518262, level: 1}]->(b);
CREATE (n: Building {id: 5025, name:"building_conscription_center", level:25});
CREATE (n: Building {id: 5044, name:"building_conscription_center", level:25});
CREATE (n: Building {id: 5051, name:"building_conscription_center", level:22});
CREATE (n: Building {id: 5135, name:"building_subsistence_pastures", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:5135}) CREATE (b)-[r:Supply{max_supply: 0.80288, current_output: 0.80288,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:5135}) CREATE (b)-[r:Supply{max_supply: 1.20432, current_output: 1.20432,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:5135}) CREATE (b)-[r:Supply{max_supply: 0.40144, current_output: 0.40144,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:5135}) CREATE (b)-[r:Supply{max_supply: 0.80288, current_output: 0.80288,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:5135}) CREATE (b)-[r:Supply{max_supply: 0.80288, current_output: 0.80288,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:5135}) CREATE (b)-[r:Supply{max_supply: 0.80288, current_output: 0.80288,level: 8}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:5135}) CREATE (b)-[r:Supply{max_supply: 2.66556, current_output: 2.66556,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:5135}) CREATE (b)-[r:Supply{max_supply: 0.80288, current_output: 0.80288,level: 8}]->(g);
