CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:29.24798722030073, pop_demand:198.59987168869344});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:20, pop_demand:0});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:8.392227943870793, pop_demand:16.57616029297466});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:12.398386687412195, pop_demand:25.372968457025358});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:43.179720955767095, pop_demand:31.478947530175294});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:41.678220807417404, pop_demand:27.111306724969545});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:11.760721859743237, pop_demand:11.937533333333333});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:2.259910119047619});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:37.61176764061144, pop_demand:19.05602983292313});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:41.083070963597685, pop_demand:35.977225021750215});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:57.17882966542665, pop_demand:9.855985});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:39.16607226950283, pop_demand:24.207839983687343});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:10.546247222222222});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:2.6365618055555555});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 1879, name:"building_government_administrationlevel", level:4});
CREATE (n: Building {id: 1880, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1880}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 60.82407138378445, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1880}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 20.0, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1880}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 1}]->(g);
CREATE (n: Building {id: 1881, name:"building_gold_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1881}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 30.0, level: 3}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:1881}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 3}]->(g);
CREATE (n: Building {id: 1882, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1882}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 10.0, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1882}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 1883, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1883}) CREATE (b)-[r:Supply{max_supply: 14.94225, current_output: 14.94225,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1883}) CREATE (b)-[r:Supply{max_supply: 7.9692, current_output: 7.9692,level: 1}]->(g);
CREATE (n: Building {id: 1884, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1884}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1884}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 1885, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1885}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1886, name:"building_naval_baselevel", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:1886}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 1887, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1887}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1887}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 1888, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1888}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1888}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 1889, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:1889}) CREATE (b)-[r:Supply{max_supply: 24.9, current_output: 24.9,level: 1}]->(g);
CREATE (n: Building {id: 1890, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1890}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 20.0, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1890}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 2}]->(g);
CREATE (n: Building {id: 1891, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1891}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1891}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 3196, name:"building_subsistence_farmslevel", level:38});
MATCH (g: Goods{code: 7}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 40.78635, current_output: 40.78635,level: 38}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 8.15727, current_output: 8.15727,level: 38}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 8.15727, current_output: 8.15727,level: 38}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 8.15727, current_output: 8.15727,level: 38}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 8.15727, current_output: 8.15727,level: 38}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 8.15727, current_output: 8.15727,level: 38}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 11.42017, current_output: 11.42017,level: 38}]->(g);
CREATE (n: Building {id: 3197, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3197}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 20.274690461261486, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3197}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 2}]->(g);
CREATE (n: Building {id: 3198, name:"building_subsistence_farmslevel", level:28});
MATCH (g: Goods{code: 7}), (b: Building{id:3198}) CREATE (b)-[r:Supply{max_supply: 19.432, current_output: 19.432,level: 28}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3198}) CREATE (b)-[r:Supply{max_supply: 3.8864, current_output: 3.8864,level: 28}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3198}) CREATE (b)-[r:Supply{max_supply: 3.8864, current_output: 3.8864,level: 28}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3198}) CREATE (b)-[r:Supply{max_supply: 3.8864, current_output: 3.8864,level: 28}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3198}) CREATE (b)-[r:Supply{max_supply: 3.8864, current_output: 3.8864,level: 28}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3198}) CREATE (b)-[r:Supply{max_supply: 3.8864, current_output: 3.8864,level: 28}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3198}) CREATE (b)-[r:Supply{max_supply: 5.44096, current_output: 5.44096,level: 28}]->(g);
CREATE (n: Building {id: 3199, name:"building_subsistence_pastureslevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 0.996, current_output: 0.996,level: 2}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 1.494, current_output: 1.494,level: 2}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 0.498, current_output: 0.498,level: 2}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 0.996, current_output: 0.996,level: 2}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 0.996, current_output: 0.996,level: 2}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 0.996, current_output: 0.996,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 2.64936, current_output: 2.64936,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 1.3944, current_output: 1.3944,level: 2}]->(g);
CREATE (n: Building {id: 3999, name:"building_trade_centerlevel", level:18});
CREATE (n: Building {id: 4199, name:"building_conscription_centerlevel", level:2});
