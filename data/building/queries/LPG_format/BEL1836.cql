CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:76.32884592305817, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:89.05032024356787, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:22.607015716609716, pop_demand:625.9120391316214});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:35.0, pop_demand:47.68327999174284});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:23.765514663654017, pop_demand:39.95000739014781});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:22.91539830667198, pop_demand:78.18212489499795});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:40.05979307309262, pop_demand:74.10590353752072});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:34.88252767061638, pop_demand:185.01549652511525});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:31.474612873281362, pop_demand:155.98853051221937});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:46.15502823288821, pop_demand:9.991239076626567});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:16.326366528094624, pop_demand:100.57147566514884});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:16.394726660400412, pop_demand:11.342803908058514});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:87.5, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:39.588317162078766, pop_demand:31.152828476569532});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:59.131160174276516, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:55.399932029058675, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:28.92428380743105, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:23.960674137170372, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:50.55604624483742, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:12.5, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:7.7333115595290565});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:27.52160689925915, pop_demand:81.87641114579797});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:25.95315171727482, pop_demand:36.43127396918208});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:41.53218177826278, pop_demand:301.9542033333334});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:60.53559704709743, pop_demand:64.07146000000002});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:44.74968286759414, pop_demand:5.972213699797237});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:33.439870291611584});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:79.83969219678127, pop_demand:72.17757455560451});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 363, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:363}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.65479957173265, level: 1}]->(b);
CREATE (n: Building {id: 364, name:"building_steel_millslevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:364}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 51.646731351684934, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:364}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 43.54160927869452, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:364}) CREATE (b)-[r:Supply{max_supply: 195.0, current_output: 91.32818316993132,level: 3}]->(g);
CREATE (n: Building {id: 365, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:365}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.983684374358438, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:365}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 32.225604515541384, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:365}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 18.14233719945605, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:365}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.488919098196149, level: 1}]->(b);
CREATE (n: Building {id: 366, name:"building_chemical_plantslevel", level:3});
MATCH (g: Goods{code: 22}), (b: Building{id:366}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:366}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 25.823365675842467, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:366}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.88540231967363, level: 3}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:366}) CREATE (b)-[r:Supply{max_supply: 225.0, current_output: 70.25244859225486,level: 3}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:366}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 18.73398629126796,level: 3}]->(g);
CREATE (n: Building {id: 367, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:367}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 48.33840677331207, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:367}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 91.57071991681559, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:367}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 144.45120903108275,level: 2}]->(g);
CREATE (n: Building {id: 368, name:"building_coal_minelevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:368}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 32.444595490980745, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:368}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 129.77838196392298,level: 5}]->(g);
CREATE (n: Building {id: 369, name:"building_iron_minelevel", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:369}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 22.95410282297108, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:369}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 25.955676392784596, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:369}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 97.81955843151135,level: 4}]->(g);
CREATE (n: Building {id: 370, name:"building_wheat_farmlevel", level:7});
MATCH (g: Goods{code: 32}), (b: Building{id:370}) CREATE (g)-[r:Demand{max_demand: 34.92544339622641, current_input: 47.843820454495855, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:370}) CREATE (g)-[r:Demand{max_demand: 6.9850849056603765, current_input: 4.532565084686126, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:370}) CREATE (b)-[r:Supply{max_supply: 244.47814150943398, current_output: 201.5590148313203,level: 7}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:370}) CREATE (b)-[r:Supply{max_supply: 55.88071698113207, current_output: 46.070631072554114,level: 7}]->(g);
CREATE (n: Building {id: 371, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:371}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.733378647294222, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:371}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 77.86702917835377,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:371}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 19.466757294588444,level: 3}]->(g);
CREATE (n: Building {id: 372, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:372}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.73852570574277, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:372}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:372}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 14.346314264356923,level: 1}]->(g);
CREATE (n: Building {id: 373, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:373}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.193950563640153, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:373}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.193950563640153, level: 5}]->(b);
CREATE (n: Building {id: 374, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:374}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 16.96439871519795, level: 3}]->(b);
CREATE (n: Building {id: 375, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:375}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.983684374358438, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:375}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 32.225604515541384, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:375}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 18.14233719945605, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:375}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.488919098196149, level: 1}]->(b);
CREATE (n: Building {id: 376, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:376}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.169203386656037, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:376}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:376}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 28.19740395109871,level: 1}]->(g);
CREATE (n: Building {id: 377, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:377}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 66.09591617808152, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:377}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 27.556683137443052, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:377}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 40.973012200542,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:377}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 70.23944948664344,level: 2}]->(g);
CREATE (n: Building {id: 378, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:378}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.2569348797824205, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:378}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.866689323647111, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:378}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 6.371367572653741,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:378}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 6.371367572653741,level: 1}]->(g);
CREATE (n: Building {id: 379, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:379}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.827399785866325, level: 2}]->(b);
CREATE (n: Building {id: 380, name:"building_textile_millslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:380}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 119.8694749948675, level: 4}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:380}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:380}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 89.90210624615061,level: 4}]->(g);
CREATE (n: Building {id: 381, name:"building_furniture_manufacturieslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:381}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 22.475526561537656, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:381}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 48.33840677331207, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:381}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 14.600067970941334, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:381}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.733378647294222, level: 3}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:381}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 90.8004996154389,level: 3}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:381}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 40.355777606861736,level: 3}]->(g);
CREATE (n: Building {id: 382, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:382}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.977838196392298, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:382}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 155.73405835670755,level: 4}]->(g);
CREATE (n: Building {id: 383, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:383}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:383}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 384, name:"building_wheat_farmlevel", level:6});
MATCH (g: Goods{code: 32}), (b: Building{id:384}) CREATE (g)-[r:Demand{max_demand: 29.933095238095234, current_input: 41.00488053856716, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:384}) CREATE (g)-[r:Demand{max_demand: 5.9866190476190475, current_input: 3.8846686671720074, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:384}) CREATE (b)-[r:Supply{max_supply: 179.5986, current_output: 148.06933927746454,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:384}) CREATE (b)-[r:Supply{max_supply: 53.879571428571424, current_output: 44.42079471655975,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:384}) CREATE (b)-[r:Supply{max_supply: 35.919714285714285, current_output: 29.613863144373166,level: 6}]->(g);
CREATE (n: Building {id: 385, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:385}) CREATE (g)-[r:Demand{max_demand: 49.9015, current_input: 41.22856701450669, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:385}) CREATE (g)-[r:Demand{max_demand: 49.9015, current_input: 32.38067963786351, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:385}) CREATE (b)-[r:Supply{max_supply: 199.606, current_output: 147.21849330474038,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:385}) CREATE (b)-[r:Supply{max_supply: 24.95075, current_output: 18.402311663092547,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:385}) CREATE (b)-[r:Supply{max_supply: 124.75375, current_output: 92.01155831546274,level: 5}]->(g);
CREATE (n: Building {id: 386, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:386}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.73852570574277, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:386}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:386}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 14.346314264356923,level: 1}]->(g);
CREATE (n: Building {id: 387, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:387}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.193950563640153, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:387}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.193950563640153, level: 5}]->(b);
CREATE (n: Building {id: 388, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:388}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 3331, name:"building_subsistence_farmslevel", level:45});
MATCH (g: Goods{code: 7}), (b: Building{id:3331}) CREATE (b)-[r:Supply{max_supply: 102.4965, current_output: 102.4965,level: 45}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3331}) CREATE (b)-[r:Supply{max_supply: 20.4993, current_output: 20.4993,level: 45}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3331}) CREATE (b)-[r:Supply{max_supply: 20.4993, current_output: 20.4993,level: 45}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3331}) CREATE (b)-[r:Supply{max_supply: 20.4993, current_output: 20.4993,level: 45}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3331}) CREATE (b)-[r:Supply{max_supply: 20.4993, current_output: 20.4993,level: 45}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3331}) CREATE (b)-[r:Supply{max_supply: 20.4993, current_output: 20.4993,level: 45}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3331}) CREATE (b)-[r:Supply{max_supply: 28.69902, current_output: 28.69902,level: 45}]->(g);
CREATE (n: Building {id: 3332, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3332}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.112802257770692, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3332}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 80.56401128885346,level: 4}]->(g);
CREATE (n: Building {id: 3333, name:"building_subsistence_farmslevel", level:69});
MATCH (g: Goods{code: 7}), (b: Building{id:3333}) CREATE (b)-[r:Supply{max_supply: 172.3275, current_output: 172.3275,level: 69}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3333}) CREATE (b)-[r:Supply{max_supply: 34.4655, current_output: 34.4655,level: 69}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3333}) CREATE (b)-[r:Supply{max_supply: 34.4655, current_output: 34.4655,level: 69}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3333}) CREATE (b)-[r:Supply{max_supply: 34.4655, current_output: 34.4655,level: 69}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3333}) CREATE (b)-[r:Supply{max_supply: 34.4655, current_output: 34.4655,level: 69}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3333}) CREATE (b)-[r:Supply{max_supply: 34.4655, current_output: 34.4655,level: 69}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3333}) CREATE (b)-[r:Supply{max_supply: 48.2517, current_output: 48.2517,level: 69}]->(g);
CREATE (n: Building {id: 3334, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3334}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.169203386656037, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3334}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 120.84601693328018,level: 6}]->(g);
CREATE (n: Building {id: 4009, name:"building_trade_centerlevel", level:46});
CREATE (n: Building {id: 4270, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4271, name:"building_conscription_centerlevel", level:43});
