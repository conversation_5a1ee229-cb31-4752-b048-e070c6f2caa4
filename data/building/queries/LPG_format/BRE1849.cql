CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:34.99667004949866, pop_demand:31.278783227075856});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:14.07342744111052, pop_demand:18.1217517329673});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:6.773253888989053, pop_demand:3.90141508650519});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:34.9987554591999, pop_demand:8.767224913494811});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:52.4932790125581, pop_demand:14.862180007343147});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:52.49675577227792, pop_demand:15.04980666666667});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:52.4671137851434, pop_demand:1.4846646296296295});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:52.4968804008022, pop_demand:11.877317037037036});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:52.49345733575476, pop_demand:11.030594166666665});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:52.5, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:0.14911369047619047});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:14.356416017834185, pop_demand:1.6760128043594678});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:52.49701970751087, pop_demand:22.9507675});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:1.01164});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:0.6958638888888888});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:0.1739659722222222});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 424, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:424}) CREATE (g)-[r:Demand{max_demand: 14.886, current_input: 0.0012350822900254557, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:424}) CREATE (b)-[r:Supply{max_supply: 19.848, current_output: 0.0016467763867006077,level: 1}]->(g);
CREATE (n: Building {id: 425, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:425}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 426, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:426}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:426}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 3230, name:"building_subsistence_farmslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:3230}) CREATE (b)-[r:Supply{max_supply: 0.009899999999999999, current_output: 0.01089,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3230}) CREATE (b)-[r:Supply{max_supply: 0.0019727272727272727, current_output: 0.00217,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3230}) CREATE (b)-[r:Supply{max_supply: 0.0019727272727272727, current_output: 0.00217,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3230}) CREATE (b)-[r:Supply{max_supply: 0.0019727272727272727, current_output: 0.00217,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3230}) CREATE (b)-[r:Supply{max_supply: 0.0019727272727272727, current_output: 0.00217,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3230}) CREATE (b)-[r:Supply{max_supply: 0.0019727272727272727, current_output: 0.00217,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3230}) CREATE (b)-[r:Supply{max_supply: 0.0027636363636363635, current_output: 0.00304,level: 6}]->(g);
CREATE (n: Building {id: 4745, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:4745}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4745}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 4872, name:"building_food_industrylevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:4872}) CREATE (g)-[r:Demand{max_demand: 17.776, current_input: 0.003946213340784939, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:4872}) CREATE (b)-[r:Supply{max_supply: 19.998, current_output: 0.004439490008383056,level: 1}]->(g);
CREATE (n: Building {id: 16782376, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:16782376}) CREATE (g)-[r:Demand{max_demand: 9.859, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:16782376}) CREATE (b)-[r:Supply{max_supply: 59.15399999999999, current_output: 0.0,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16782376}) CREATE (b)-[r:Supply{max_supply: 29.576999999999995, current_output: 0.0,level: 2}]->(g);
CREATE (n: Building {id: 67114922, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:67114922}) CREATE (b)-[r:Supply{max_supply: 23.044999999999998, current_output: 29.9585,level: 1}]->(g);
CREATE (n: Building {id: 6136, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:6136}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:6136}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 50337989, name:"building_trade_centerlevel", level:12});
CREATE (n: Building {id: 16783602, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16783602}) CREATE (g)-[r:Demand{max_demand: 2.501, current_input: 0.000207506436071051, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:16783602}) CREATE (b)-[r:Supply{max_supply: 12.505, current_output: 0.0010375321803552551,level: 1}]->(g);
