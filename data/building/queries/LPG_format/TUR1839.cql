CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:91.2001297409415, pop_demand:5.9497832513247095});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:52.782136551031705, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:95.07454499864178, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:22.285342021521956, pop_demand:2088.3156977566264});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:17.363458910894344, pop_demand:510.3414944617977});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:15.574890573844609, pop_demand:63.09768254590383});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:18.687046920186546, pop_demand:378.58609527542166});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:42.1568738875419, pop_demand:90.33105211604317});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:36.92471723353991, pop_demand:769.165693509338});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:31.5072724852718, pop_demand:498.15032694856063});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:37.21706081680074, pop_demand:25.5450058950265});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:21.025378942861636, pop_demand:353.2765079974053});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:37.44584282145021, pop_demand:121.1277558333333});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:79.6125423897829, pop_demand:1.7430272432360612});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:48.971479021838306, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:52.5, pop_demand:106.78619228578235});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:36.12247329959593, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:10.0, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:52.002535322840416, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:45.43897318580073, pop_demand:22.086939472243106});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:46.07574356254899, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:88.86731081353689, pop_demand:56.453200590136795});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:38.83123034877268, pop_demand:194.5733318251866});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:24.714308266216225, pop_demand:189.0295291382874});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:21.556721485595244, pop_demand:215.26100931997823});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:37.19244612222259, pop_demand:12.713646659409967});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:20.023968242467504, pop_demand:83.62835726374675});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:87.5, pop_demand:118.22411289877552});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:15.211465614052152, pop_demand:16.55521188343779});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:37.11100797543431, pop_demand:567.6753536350182});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:54.80999025340232, pop_demand:173.18948110564298});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:54.223566071534634, pop_demand:130.98502028919748});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 766, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:766}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 67.92417414755224, level: 10}]->(b);
CREATE (n: Building {id: 767, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:767}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 70.92271969745119, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:767}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 164.38900320284236, level: 2}]->(b);
CREATE (n: Building {id: 768, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:768}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 28.369087878980476, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:768}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 43.83706752075796, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:768}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 35.0,level: 1}]->(g);
CREATE (n: Building {id: 769, name:"building_military_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:769}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 28.369087878980476, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:769}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 43.83706752075796, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:769}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 11.48436430805503, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:769}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 35.0,level: 1}]->(g);
CREATE (n: Building {id: 770, name:"building_textile_mills", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:770}) CREATE (g)-[r:Demand{max_demand: 49.986000000000004, current_input: 70.90286133593591, level: 2}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:770}) CREATE (g)-[r:Demand{max_demand: 29.99159405940594, current_input: 21.022628828224253, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:770}) CREATE (b)-[r:Supply{max_supply: 29.99159405940594, current_output: 25.507111443815095,level: 2}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:770}) CREATE (b)-[r:Supply{max_supply: 59.98319801980198, current_output: 51.0142313081782,level: 2}]->(g);
CREATE (n: Building {id: 771, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:771}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 772, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:772}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 773, name:"building_silk_plantation", level:2});
MATCH (g: Goods{code: 20}), (b: Building{id:773}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 774, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:774}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 2.392425643745101, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:774}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 11.962128218725505,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:774}) CREATE (b)-[r:Supply{max_supply: 21.0, current_output: 16.74697950621571,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:774}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 11.962128218725505,level: 3}]->(g);
CREATE (n: Building {id: 775, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:775}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.462485870072367, level: 3}]->(b);
CREATE (n: Building {id: 776, name:"building_hagia_sophia", level:1});
CREATE (n: Building {id: 777, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:777}) CREATE (b)-[r:Supply{max_supply: 46.7, current_output: 47.167,level: 2}]->(g);
CREATE (n: Building {id: 778, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:778}) CREATE (g)-[r:Demand{max_demand: 1.9528712871287128, current_input: 1.5573664487534118, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:778}) CREATE (b)-[r:Supply{max_supply: 9.76439603960396, current_output: 7.786863826943875,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:778}) CREATE (b)-[r:Supply{max_supply: 13.670158415841584, current_output: 10.901612516039105,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:778}) CREATE (b)-[r:Supply{max_supply: 9.76439603960396, current_output: 7.786863826943875,level: 2}]->(g);
CREATE (n: Building {id: 779, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:779}) CREATE (g)-[r:Demand{max_demand: 4.99455, current_input: 3.9830298329890317, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:779}) CREATE (b)-[r:Supply{max_supply: 59.9346, current_output: 47.79635799586838,level: 1}]->(g);
CREATE (n: Building {id: 780, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:780}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.8208286233574555, level: 1}]->(b);
CREATE (n: Building {id: 781, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:781}) CREATE (g)-[r:Demand{max_demand: 16.83625, current_input: 23.88145279012425, level: 1}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:781}) CREATE (g)-[r:Demand{max_demand: 10.10175, current_input: 7.080828726371497, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:781}) CREATE (b)-[r:Supply{max_supply: 10.10175, current_output: 8.591289363185748,level: 1}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:781}) CREATE (b)-[r:Supply{max_supply: 20.2035, current_output: 17.182578726371496,level: 1}]->(g);
CREATE (n: Building {id: 782, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:782}) CREATE (b)-[r:Supply{max_supply: 38.62, current_output: 39.0062,level: 2}]->(g);
CREATE (n: Building {id: 783, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:783}) CREATE (g)-[r:Demand{max_demand: 9.98149504950495, current_input: 7.95999490645014, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:783}) CREATE (b)-[r:Supply{max_supply: 119.778, current_output: 95.5199862521669,level: 2}]->(g);
CREATE (n: Building {id: 784, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:784}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.8208286233574555, level: 1}]->(b);
CREATE (n: Building {id: 785, name:"building_tooling_workshops", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:785}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 65.75560128113693, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:785}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 45.93745723222012, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:785}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 2}]->(g);
CREATE (n: Building {id: 786, name:"building_arms_industry", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:786}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 22.96872861611006, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:786}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.998309784773056, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:786}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 47.997464677159584,level: 2}]->(g);
CREATE (n: Building {id: 787, name:"building_artillery_foundries", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:787}) CREATE (g)-[r:Demand{max_demand: 13.3281, current_input: 15.306475593418822, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:787}) CREATE (g)-[r:Demand{max_demand: 8.8854, current_input: 5.330489088081126, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:787}) CREATE (b)-[r:Supply{max_supply: 22.2135, current_output: 17.76986136010141,level: 1}]->(g);
CREATE (n: Building {id: 788, name:"building_iron_mine", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:788}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 15.949504291634007, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:788}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 63.79801716653603,level: 4}]->(g);
CREATE (n: Building {id: 789, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:789}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.974752145817003, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:789}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 95.69702574980404,level: 2}]->(g);
CREATE (n: Building {id: 790, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:790}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.2382193261593475, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:790}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 8.476438652318695,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:790}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 8.476438652318695,level: 1}]->(g);
CREATE (n: Building {id: 791, name:"building_furniture_manufacturies", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:791}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 28.369087878980476, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:791}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 32.877800640568466, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:791}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 17.997464677159584, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:791}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 34.66553985651537,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:791}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 43.33192482064421,level: 2}]->(g);
CREATE (n: Building {id: 792, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:792}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 56.73817575796095, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:792}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 1}]->(g);
CREATE (n: Building {id: 793, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:793}) CREATE (b)-[r:Supply{max_supply: 39.844, current_output: 40.24244,level: 2}]->(g);
CREATE (n: Building {id: 794, name:"building_tobacco_plantation", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:794}) CREATE (b)-[r:Supply{max_supply: 74.92949999999999, current_output: 76.42809,level: 3}]->(g);
CREATE (n: Building {id: 795, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:795}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 11.962128218725505, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:795}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 47.84851287490202,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:795}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 47.84851287490202,level: 3}]->(g);
CREATE (n: Building {id: 796, name:"building_furniture_manufacturies", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:796}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 42.55363181847072, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:796}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 49.31670096085271, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:796}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 26.996197015739376, level: 3}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:796}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 51.998309784773056,level: 3}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:796}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 64.99788723096631,level: 3}]->(g);
CREATE (n: Building {id: 797, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:797}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.5949504291634007, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:797}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 31.899008583268014,level: 2}]->(g);
CREATE (n: Building {id: 798, name:"building_logging_camp", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:798}) CREATE (g)-[r:Demand{max_demand: 19.99779611650485, current_input: 15.9477467491708, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:798}) CREATE (b)-[r:Supply{max_supply: 139.98459223300972, current_output: 111.63424272915125,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:798}) CREATE (b)-[r:Supply{max_supply: 39.9955922330097, current_output: 31.8954934983416,level: 4}]->(g);
CREATE (n: Building {id: 821, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:821}) CREATE (g)-[r:Demand{max_demand: 4.85095, current_input: 4.111878008046538, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:821}) CREATE (b)-[r:Supply{max_supply: 9.7019, current_output: 8.223756016093075,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:821}) CREATE (b)-[r:Supply{max_supply: 9.7019, current_output: 8.223756016093075,level: 1}]->(g);
CREATE (n: Building {id: 827, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:827}) CREATE (g)-[r:Demand{max_demand: 14.842049019607844, current_input: 12.580771798941273, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:827}) CREATE (b)-[r:Supply{max_supply: 29.684098039215687, current_output: 25.161543597882545,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:827}) CREATE (b)-[r:Supply{max_supply: 29.684098039215687, current_output: 25.161543597882545,level: 3}]->(g);
CREATE (n: Building {id: 828, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:828}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:828}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 7.0,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:828}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 833, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:833}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 843, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:843}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 13.58483482951045, level: 2}]->(b);
CREATE (n: Building {id: 844, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:844}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 56.73817575796095, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:844}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 1}]->(g);
CREATE (n: Building {id: 845, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:845}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.5949504291634007, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:845}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 31.899008583268014,level: 2}]->(g);
CREATE (n: Building {id: 846, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:846}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 847, name:"building_cotton_plantation", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:847}) CREATE (b)-[r:Supply{max_supply: 79.91199999999999, current_output: 80.71112,level: 2}]->(g);
CREATE (n: Building {id: 848, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:848}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.974752145817003, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:848}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 31.899008583268014,level: 2}]->(g);
CREATE (n: Building {id: 849, name:"building_tobacco_plantation", level:4});
MATCH (g: Goods{code: 43}), (b: Building{id:849}) CREATE (b)-[r:Supply{max_supply: 99.545, current_output: 102.53135,level: 4}]->(g);
CREATE (n: Building {id: 850, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:850}) CREATE (b)-[r:Supply{max_supply: 19.56, current_output: 19.56,level: 1}]->(g);
CREATE (n: Building {id: 851, name:"building_food_industry", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:851}) CREATE (g)-[r:Demand{max_demand: 27.6608, current_input: 23.446507427405695, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:851}) CREATE (g)-[r:Demand{max_demand: 10.3728, current_input: 30.2650639555094, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:851}) CREATE (b)-[r:Supply{max_supply: 44.9488, current_output: 41.524687284767126,level: 1}]->(g);
CREATE (n: Building {id: 852, name:"building_cotton_plantation", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:852}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 1}]->(g);
CREATE (n: Building {id: 853, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:853}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 854, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:854}) CREATE (g)-[r:Demand{max_demand: 0.99266, current_input: 0.7916217465066706, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:854}) CREATE (b)-[r:Supply{max_supply: 4.9633, current_output: 3.9581087325333537,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:854}) CREATE (b)-[r:Supply{max_supply: 6.94862, current_output: 5.541352225546695,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:854}) CREATE (b)-[r:Supply{max_supply: 4.9633, current_output: 3.9581087325333537,level: 1}]->(g);
CREATE (n: Building {id: 855, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:855}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 20.377252244265676, level: 3}]->(b);
CREATE (n: Building {id: 856, name:"building_textile_mills", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:856}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 70.92271969745119, level: 2}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:856}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.0285209781617, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:856}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 25.51426048908085,level: 2}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:856}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 51.0285209781617,level: 2}]->(g);
CREATE (n: Building {id: 857, name:"building_glassworks", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:857}) CREATE (g)-[r:Demand{max_demand: 108.49559223300972, current_input: 118.90321506057674, level: 4}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:857}) CREATE (g)-[r:Demand{max_demand: 18.08259223300971, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:857}) CREATE (b)-[r:Supply{max_supply: 36.16519417475728, current_output: 18.08259708737864,level: 4}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:857}) CREATE (b)-[r:Supply{max_supply: 72.33039805825243, current_output: 36.165199029126214,level: 4}]->(g);
CREATE (n: Building {id: 858, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:858}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 859, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:859}) CREATE (g)-[r:Demand{max_demand: 1.9026534653465348, current_input: 1.5173189805518437, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:859}) CREATE (b)-[r:Supply{max_supply: 38.053198019801975, current_output: 30.346482256361515,level: 2}]->(g);
CREATE (n: Building {id: 860, name:"building_tobacco_plantation", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:860}) CREATE (b)-[r:Supply{max_supply: 74.97, current_output: 76.4694,level: 3}]->(g);
CREATE (n: Building {id: 861, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:861}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.8208286233574555, level: 1}]->(b);
CREATE (n: Building {id: 16778323, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:16778323}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:16778323}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 1875, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1875}) CREATE (g)-[r:Demand{max_demand: 0.85644, current_input: 0.6829896727763515, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1875}) CREATE (b)-[r:Supply{max_supply: 17.1288, current_output: 13.65979345552703,level: 1}]->(g);
CREATE (n: Building {id: 1876, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:1876}) CREATE (b)-[r:Supply{max_supply: 19.308, current_output: 19.308,level: 1}]->(g);
CREATE (n: Building {id: 1877, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1877}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.8208286233574555, level: 1}]->(b);
CREATE (n: Building {id: 1894, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1894}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 35.46135984872559, level: 1}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:1894}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.51426048908085, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1894}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 12.757130244540425,level: 1}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:1894}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 25.51426048908085,level: 1}]->(g);
CREATE (n: Building {id: 1895, name:"building_rice_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1895}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 3.1899008583268014, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1895}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 15.949504291634007,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1895}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 19.139405149960808,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1895}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 28.709107724941212,level: 2}]->(g);
CREATE (n: Building {id: 1896, name:"building_tobacco_plantation", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:1896}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 91.5,level: 3}]->(g);
CREATE (n: Building {id: 1897, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1897}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.7974752145817003, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1897}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 15.949504291634007,level: 1}]->(g);
CREATE (n: Building {id: 1898, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1898}) CREATE (g)-[r:Demand{max_demand: 4.66075, current_input: 3.950656144879436, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1898}) CREATE (b)-[r:Supply{max_supply: 9.3215, current_output: 7.901312289758872,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1898}) CREATE (b)-[r:Supply{max_supply: 9.3215, current_output: 7.901312289758872,level: 1}]->(g);
CREATE (n: Building {id: 1899, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1899}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1900, name:"building_rice_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1900}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.5949504291634007, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1900}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 7.974752145817003,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1900}) CREATE (b)-[r:Supply{max_supply: 12.0, current_output: 9.569702574980404,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1900}) CREATE (b)-[r:Supply{max_supply: 18.000000000000004, current_output: 14.35455386247061,level: 1}]->(g);
CREATE (n: Building {id: 1901, name:"building_tobacco_plantation", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:1901}) CREATE (b)-[r:Supply{max_supply: 24.13, current_output: 28.956,level: 1}]->(g);
CREATE (n: Building {id: 1902, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1902}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.8208286233574555, level: 1}]->(b);
CREATE (n: Building {id: 1949, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:1949}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 20.377252244265676, level: 3}]->(b);
CREATE (n: Building {id: 1950, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1950}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 35.46135984872559, level: 1}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:1950}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.51426048908085, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1950}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 12.757130244540425,level: 1}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:1950}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 25.51426048908085,level: 1}]->(g);
CREATE (n: Building {id: 1951, name:"building_paper_mills", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:1951}) CREATE (g)-[r:Demand{max_demand: 119.99399999999999, current_input: 131.50462700214575, level: 4}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1951}) CREATE (b)-[r:Supply{max_supply: 159.99200000000002, current_output: 159.99200000000002,level: 4}]->(g);
CREATE (n: Building {id: 1952, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1952}) CREATE (g)-[r:Demand{max_demand: 14.996696078431373, current_input: 11.959493423163604, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1952}) CREATE (b)-[r:Supply{max_supply: 179.96039215686272, current_output: 143.51395235150105,level: 3}]->(g);
CREATE (n: Building {id: 1953, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1953}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1954, name:"building_tea_plantation", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:1954}) CREATE (b)-[r:Supply{max_supply: 79.992, current_output: 82.39176,level: 4}]->(g);
CREATE (n: Building {id: 1955, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1955}) CREATE (g)-[r:Demand{max_demand: 1.7048712871287128, current_input: 1.35959259553715, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1955}) CREATE (b)-[r:Supply{max_supply: 8.52439603960396, current_output: 6.7979945608625645,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1955}) CREATE (b)-[r:Supply{max_supply: 11.934158415841583, current_output: 9.517195543525272,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1955}) CREATE (b)-[r:Supply{max_supply: 8.52439603960396, current_output: 6.7979945608625645,level: 2}]->(g);
CREATE (n: Building {id: 1956, name:"building_port", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1956}) CREATE (g)-[r:Demand{max_demand: 9.998, current_input: 5.640528915265568, level: 2}]->(b);
CREATE (n: Building {id: 1957, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1957}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 35.46135984872559, level: 1}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:1957}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.51426048908085, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1957}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 12.757130244540425,level: 1}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:1957}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 25.51426048908085,level: 1}]->(g);
CREATE (n: Building {id: 1958, name:"building_furniture_manufacturies", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1958}) CREATE (g)-[r:Demand{max_demand: 19.36, current_input: 27.4612770668531, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1958}) CREATE (g)-[r:Demand{max_demand: 29.04, current_input: 31.82571102007028, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1958}) CREATE (g)-[r:Demand{max_demand: 29.04, current_input: 17.421545807490478, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1958}) CREATE (b)-[r:Supply{max_supply: 38.72, current_output: 33.55624258110688,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1958}) CREATE (b)-[r:Supply{max_supply: 48.4, current_output: 41.945303226383594,level: 2}]->(g);
CREATE (n: Building {id: 1959, name:"building_fishing_wharf", level:4});
MATCH (g: Goods{code: 8}), (b: Building{id:1959}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 103.0,level: 4}]->(g);
CREATE (n: Building {id: 1960, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1960}) CREATE (g)-[r:Demand{max_demand: 2.994862745098039, current_input: 2.388328810289799, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1960}) CREATE (b)-[r:Supply{max_supply: 59.89739215686274, current_output: 47.76668566317837,level: 3}]->(g);
CREATE (n: Building {id: 1961, name:"building_silk_plantation", level:2});
MATCH (g: Goods{code: 20}), (b: Building{id:1961}) CREATE (b)-[r:Supply{max_supply: 39.876, current_output: 40.27476,level: 2}]->(g);
CREATE (n: Building {id: 1962, name:"building_tea_plantation", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:1962}) CREATE (b)-[r:Supply{max_supply: 19.936, current_output: 19.936,level: 1}]->(g);
CREATE (n: Building {id: 1963, name:"building_tobacco_plantation", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:1963}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1964, name:"building_cotton_plantation", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1964}) CREATE (b)-[r:Supply{max_supply: 79.896, current_output: 80.69496,level: 2}]->(g);
CREATE (n: Building {id: 1965, name:"building_port", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1965}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.641657246714911, level: 2}]->(b);
CREATE (n: Building {id: 1966, name:"building_cotton_plantation", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1966}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 80.8,level: 2}]->(g);
CREATE (n: Building {id: 1967, name:"building_tobacco_plantation", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:1967}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 1968, name:"building_tea_plantation", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:1968}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 1969, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1969}) CREATE (g)-[r:Demand{max_demand: 4.9029, current_input: 4.155913106845333, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1969}) CREATE (b)-[r:Supply{max_supply: 9.8058, current_output: 8.311826213690667,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1969}) CREATE (b)-[r:Supply{max_supply: 9.8058, current_output: 8.311826213690667,level: 1}]->(g);
CREATE (n: Building {id: 1970, name:"building_tea_plantation", level:5});
MATCH (g: Goods{code: 40}), (b: Building{id:1970}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 104.0,level: 5}]->(g);
CREATE (n: Building {id: 1971, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:1971}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 1972, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1972}) CREATE (g)-[r:Demand{max_demand: 4.97055, current_input: 4.21325621432827, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1972}) CREATE (b)-[r:Supply{max_supply: 9.9411, current_output: 8.42651242865654,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1972}) CREATE (b)-[r:Supply{max_supply: 9.9411, current_output: 8.42651242865654,level: 1}]->(g);
CREATE (n: Building {id: 1973, name:"building_tea_plantation", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:1973}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 1974, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1974}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 1980, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:1980}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 1981, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1981}) CREATE (g)-[r:Demand{max_demand: 4.9843, current_input: 4.2249113174752075, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1981}) CREATE (b)-[r:Supply{max_supply: 9.9686, current_output: 8.449822634950415,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1981}) CREATE (b)-[r:Supply{max_supply: 9.9686, current_output: 8.449822634950415,level: 1}]->(g);
CREATE (n: Building {id: 1982, name:"building_tea_plantation", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:1982}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 61.2,level: 3}]->(g);
CREATE (n: Building {id: 1983, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1983}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.974752145817003, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1983}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 95.69702574980404,level: 2}]->(g);
CREATE (n: Building {id: 1984, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1984}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.8208286233574555, level: 1}]->(b);
CREATE (n: Building {id: 1985, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1985}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.974752145817003, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1985}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 95.69702574980404,level: 2}]->(g);
CREATE (n: Building {id: 1986, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1986}) CREATE (g)-[r:Demand{max_demand: 4.98135, current_input: 4.222410768072773, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1986}) CREATE (b)-[r:Supply{max_supply: 9.9627, current_output: 8.444821536145547,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1986}) CREATE (b)-[r:Supply{max_supply: 9.9627, current_output: 8.444821536145547,level: 1}]->(g);
CREATE (n: Building {id: 1987, name:"building_tea_plantation", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:1987}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 1988, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:1988}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 20.377252244265676, level: 3}]->(b);
CREATE (n: Building {id: 1989, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1989}) CREATE (g)-[r:Demand{max_demand: 4.7392, current_input: 4.017153806106877, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1989}) CREATE (b)-[r:Supply{max_supply: 9.4784, current_output: 8.034307612213754,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1989}) CREATE (b)-[r:Supply{max_supply: 9.4784, current_output: 8.034307612213754,level: 1}]->(g);
CREATE (n: Building {id: 1990, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1990}) CREATE (b)-[r:Supply{max_supply: 59.79959803921569, current_output: 60.99559,level: 3}]->(g);
CREATE (n: Building {id: 1991, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1991}) CREATE (b)-[r:Supply{max_supply: 49.29, current_output: 49.7829,level: 2}]->(g);
CREATE (n: Building {id: 1992, name:"building_tea_plantation", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:1992}) CREATE (b)-[r:Supply{max_supply: 28.2, current_output: 28.482,level: 2}]->(g);
CREATE (n: Building {id: 1993, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1993}) CREATE (g)-[r:Demand{max_demand: 4.4696, current_input: 3.7886290200403643, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1993}) CREATE (b)-[r:Supply{max_supply: 8.9392, current_output: 7.577258040080729,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1993}) CREATE (b)-[r:Supply{max_supply: 8.9392, current_output: 7.577258040080729,level: 1}]->(g);
CREATE (n: Building {id: 1994, name:"building_tea_plantation", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:1994}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 1995, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1995}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.7974752145817003, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1995}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 15.949504291634007,level: 1}]->(g);
CREATE (n: Building {id: 2715, name:"building_subsistence_farms", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:2715}) CREATE (b)-[r:Supply{max_supply: 13.9034, current_output: 13.9034,level: 7}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2715}) CREATE (b)-[r:Supply{max_supply: 2.78068, current_output: 2.78068,level: 7}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2715}) CREATE (b)-[r:Supply{max_supply: 2.78068, current_output: 2.78068,level: 7}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2715}) CREATE (b)-[r:Supply{max_supply: 2.78068, current_output: 2.78068,level: 7}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2715}) CREATE (b)-[r:Supply{max_supply: 2.78068, current_output: 2.78068,level: 7}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2715}) CREATE (b)-[r:Supply{max_supply: 2.78068, current_output: 2.78068,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2715}) CREATE (b)-[r:Supply{max_supply: 2.78068, current_output: 2.78068,level: 7}]->(g);
CREATE (n: Building {id: 2717, name:"building_subsistence_orchards", level:28});
MATCH (g: Goods{code: 7}), (b: Building{id:2717}) CREATE (b)-[r:Supply{max_supply: 8.475458333333334, current_output: 10.17055,level: 28}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2717}) CREATE (b)-[r:Supply{max_supply: 4.237725, current_output: 5.08527,level: 28}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2717}) CREATE (b)-[r:Supply{max_supply: 12.713183333333333, current_output: 15.25582,level: 28}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2717}) CREATE (b)-[r:Supply{max_supply: 8.475458333333334, current_output: 10.17055,level: 28}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2717}) CREATE (b)-[r:Supply{max_supply: 8.475458333333334, current_output: 10.17055,level: 28}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2717}) CREATE (b)-[r:Supply{max_supply: 8.475458333333334, current_output: 10.17055,level: 28}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2717}) CREATE (b)-[r:Supply{max_supply: 28.138525, current_output: 33.76623,level: 28}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2717}) CREATE (b)-[r:Supply{max_supply: 8.475458333333334, current_output: 10.17055,level: 28}]->(g);
CREATE (n: Building {id: 2718, name:"building_subsistence_farms", level:23});
MATCH (g: Goods{code: 7}), (b: Building{id:2718}) CREATE (b)-[r:Supply{max_supply: 57.49712, current_output: 57.49712,level: 23}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2718}) CREATE (b)-[r:Supply{max_supply: 11.49942, current_output: 11.49942,level: 23}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2718}) CREATE (b)-[r:Supply{max_supply: 11.49942, current_output: 11.49942,level: 23}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2718}) CREATE (b)-[r:Supply{max_supply: 11.49942, current_output: 11.49942,level: 23}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2718}) CREATE (b)-[r:Supply{max_supply: 11.49942, current_output: 11.49942,level: 23}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2718}) CREATE (b)-[r:Supply{max_supply: 11.49942, current_output: 11.49942,level: 23}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2718}) CREATE (b)-[r:Supply{max_supply: 11.49942, current_output: 11.49942,level: 23}]->(g);
CREATE (n: Building {id: 2719, name:"building_urban_center", level:5});
MATCH (g: Goods{code: 15}), (b: Building{id:2719}) CREATE (b)-[r:Supply{max_supply: 74.99399999999999, current_output: 77.99376,level: 5}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2719}) CREATE (b)-[r:Supply{max_supply: 24.998, current_output: 25.99792,level: 5}]->(g);
CREATE (n: Building {id: 2845, name:"building_subsistence_farms", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:2845}) CREATE (b)-[r:Supply{max_supply: 123.34374782608698, current_output: 141.84531,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2845}) CREATE (b)-[r:Supply{max_supply: 24.66874782608696, current_output: 28.36906,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2845}) CREATE (b)-[r:Supply{max_supply: 24.66874782608696, current_output: 28.36906,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2845}) CREATE (b)-[r:Supply{max_supply: 24.66874782608696, current_output: 28.36906,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2845}) CREATE (b)-[r:Supply{max_supply: 24.66874782608696, current_output: 28.36906,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2845}) CREATE (b)-[r:Supply{max_supply: 24.66874782608696, current_output: 28.36906,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2845}) CREATE (b)-[r:Supply{max_supply: 24.66874782608696, current_output: 28.36906,level: 50}]->(g);
CREATE (n: Building {id: 2846, name:"building_subsistence_farms", level:39});
MATCH (g: Goods{code: 7}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 70.98487, current_output: 70.98487,level: 39}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 14.19697, current_output: 14.19697,level: 39}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 14.19697, current_output: 14.19697,level: 39}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 14.19697, current_output: 14.19697,level: 39}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 14.19697, current_output: 14.19697,level: 39}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 14.19697, current_output: 14.19697,level: 39}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 14.19697, current_output: 14.19697,level: 39}]->(g);
CREATE (n: Building {id: 2847, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:2847}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2847}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 3289, name:"building_subsistence_farms", level:25});
MATCH (g: Goods{code: 7}), (b: Building{id:3289}) CREATE (b)-[r:Supply{max_supply: 61.91437, current_output: 61.91437,level: 25}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3289}) CREATE (b)-[r:Supply{max_supply: 12.38287, current_output: 12.38287,level: 25}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3289}) CREATE (b)-[r:Supply{max_supply: 12.38287, current_output: 12.38287,level: 25}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3289}) CREATE (b)-[r:Supply{max_supply: 12.38287, current_output: 12.38287,level: 25}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3289}) CREATE (b)-[r:Supply{max_supply: 12.38287, current_output: 12.38287,level: 25}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3289}) CREATE (b)-[r:Supply{max_supply: 12.38287, current_output: 12.38287,level: 25}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3289}) CREATE (b)-[r:Supply{max_supply: 12.38287, current_output: 12.38287,level: 25}]->(g);
CREATE (n: Building {id: 3290, name:"building_subsistence_farms", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 37.33875, current_output: 37.33875,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 7.46775, current_output: 7.46775,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 7.46775, current_output: 7.46775,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 7.46775, current_output: 7.46775,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 7.46775, current_output: 7.46775,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 7.46775, current_output: 7.46775,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 7.46775, current_output: 7.46775,level: 18}]->(g);
CREATE (n: Building {id: 3291, name:"building_subsistence_farms", level:46});
MATCH (g: Goods{code: 7}), (b: Building{id:3291}) CREATE (b)-[r:Supply{max_supply: 109.0637, current_output: 109.0637,level: 46}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3291}) CREATE (b)-[r:Supply{max_supply: 21.81274, current_output: 21.81274,level: 46}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3291}) CREATE (b)-[r:Supply{max_supply: 21.81274, current_output: 21.81274,level: 46}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3291}) CREATE (b)-[r:Supply{max_supply: 21.81274, current_output: 21.81274,level: 46}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3291}) CREATE (b)-[r:Supply{max_supply: 21.81274, current_output: 21.81274,level: 46}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3291}) CREATE (b)-[r:Supply{max_supply: 21.81274, current_output: 21.81274,level: 46}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3291}) CREATE (b)-[r:Supply{max_supply: 21.81274, current_output: 21.81274,level: 46}]->(g);
CREATE (n: Building {id: 3292, name:"building_subsistence_farms", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 36.62775, current_output: 36.62775,level: 15}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 7.32555, current_output: 7.32555,level: 15}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 7.32555, current_output: 7.32555,level: 15}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 7.32555, current_output: 7.32555,level: 15}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 7.32555, current_output: 7.32555,level: 15}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 7.32555, current_output: 7.32555,level: 15}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 7.32555, current_output: 7.32555,level: 15}]->(g);
CREATE (n: Building {id: 3293, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:3293}) CREATE (b)-[r:Supply{max_supply: 29.988, current_output: 30.28788,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3293}) CREATE (b)-[r:Supply{max_supply: 9.996, current_output: 10.09596,level: 2}]->(g);
CREATE (n: Building {id: 3294, name:"building_subsistence_farms", level:25});
MATCH (g: Goods{code: 7}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 62.23062, current_output: 62.23062,level: 25}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 12.44612, current_output: 12.44612,level: 25}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 12.44612, current_output: 12.44612,level: 25}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 12.44612, current_output: 12.44612,level: 25}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 12.44612, current_output: 12.44612,level: 25}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 12.44612, current_output: 12.44612,level: 25}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 12.44612, current_output: 12.44612,level: 25}]->(g);
CREATE (n: Building {id: 3295, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3295}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3295}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 3296, name:"building_subsistence_farms", level:33});
MATCH (g: Goods{code: 7}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 78.31642, current_output: 78.31642,level: 33}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 15.66328, current_output: 15.66328,level: 33}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 15.66328, current_output: 15.66328,level: 33}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 15.66328, current_output: 15.66328,level: 33}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 15.66328, current_output: 15.66328,level: 33}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 15.66328, current_output: 15.66328,level: 33}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 15.66328, current_output: 15.66328,level: 33}]->(g);
CREATE (n: Building {id: 3439, name:"building_subsistence_orchards", level:35});
MATCH (g: Goods{code: 7}), (b: Building{id:3439}) CREATE (b)-[r:Supply{max_supply: 8.355375, current_output: 10.02645,level: 35}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3439}) CREATE (b)-[r:Supply{max_supply: 4.177683333333333, current_output: 5.01322,level: 35}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3439}) CREATE (b)-[r:Supply{max_supply: 12.533058333333333, current_output: 15.03967,level: 35}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3439}) CREATE (b)-[r:Supply{max_supply: 8.355375, current_output: 10.02645,level: 35}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3439}) CREATE (b)-[r:Supply{max_supply: 8.355375, current_output: 10.02645,level: 35}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3439}) CREATE (b)-[r:Supply{max_supply: 8.355375, current_output: 10.02645,level: 35}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3439}) CREATE (b)-[r:Supply{max_supply: 27.739841666666667, current_output: 33.28781,level: 35}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3439}) CREATE (b)-[r:Supply{max_supply: 8.355375, current_output: 10.02645,level: 35}]->(g);
CREATE (n: Building {id: 3440, name:"building_subsistence_orchards", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:3440}) CREATE (b)-[r:Supply{max_supply: 14.44432, current_output: 14.44432,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3440}) CREATE (b)-[r:Supply{max_supply: 7.22216, current_output: 7.22216,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3440}) CREATE (b)-[r:Supply{max_supply: 21.66648, current_output: 21.66648,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3440}) CREATE (b)-[r:Supply{max_supply: 14.44432, current_output: 14.44432,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3440}) CREATE (b)-[r:Supply{max_supply: 14.44432, current_output: 14.44432,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3440}) CREATE (b)-[r:Supply{max_supply: 14.44432, current_output: 14.44432,level: 29}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3440}) CREATE (b)-[r:Supply{max_supply: 47.95514, current_output: 47.95514,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3440}) CREATE (b)-[r:Supply{max_supply: 14.44432, current_output: 14.44432,level: 29}]->(g);
CREATE (n: Building {id: 3457, name:"building_subsistence_farms", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:3457}) CREATE (b)-[r:Supply{max_supply: 22.410443478260873, current_output: 25.77201,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3457}) CREATE (b)-[r:Supply{max_supply: 4.482086956521739, current_output: 5.1544,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3457}) CREATE (b)-[r:Supply{max_supply: 4.482086956521739, current_output: 5.1544,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3457}) CREATE (b)-[r:Supply{max_supply: 4.482086956521739, current_output: 5.1544,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3457}) CREATE (b)-[r:Supply{max_supply: 4.482086956521739, current_output: 5.1544,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3457}) CREATE (b)-[r:Supply{max_supply: 4.482086956521739, current_output: 5.1544,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3457}) CREATE (b)-[r:Supply{max_supply: 4.482086956521739, current_output: 5.1544,level: 9}]->(g);
CREATE (n: Building {id: 3461, name:"building_subsistence_farms", level:41});
MATCH (g: Goods{code: 7}), (b: Building{id:3461}) CREATE (b)-[r:Supply{max_supply: 102.10537, current_output: 102.10537,level: 41}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3461}) CREATE (b)-[r:Supply{max_supply: 20.42107, current_output: 20.42107,level: 41}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3461}) CREATE (b)-[r:Supply{max_supply: 20.42107, current_output: 20.42107,level: 41}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3461}) CREATE (b)-[r:Supply{max_supply: 20.42107, current_output: 20.42107,level: 41}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3461}) CREATE (b)-[r:Supply{max_supply: 20.42107, current_output: 20.42107,level: 41}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3461}) CREATE (b)-[r:Supply{max_supply: 20.42107, current_output: 20.42107,level: 41}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3461}) CREATE (b)-[r:Supply{max_supply: 20.42107, current_output: 20.42107,level: 41}]->(g);
CREATE (n: Building {id: 3462, name:"building_subsistence_farms", level:34});
MATCH (g: Goods{code: 7}), (b: Building{id:3462}) CREATE (b)-[r:Supply{max_supply: 69.02255, current_output: 69.02255,level: 34}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3462}) CREATE (b)-[r:Supply{max_supply: 13.80451, current_output: 13.80451,level: 34}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3462}) CREATE (b)-[r:Supply{max_supply: 13.80451, current_output: 13.80451,level: 34}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3462}) CREATE (b)-[r:Supply{max_supply: 13.80451, current_output: 13.80451,level: 34}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3462}) CREATE (b)-[r:Supply{max_supply: 13.80451, current_output: 13.80451,level: 34}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3462}) CREATE (b)-[r:Supply{max_supply: 13.80451, current_output: 13.80451,level: 34}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3462}) CREATE (b)-[r:Supply{max_supply: 13.80451, current_output: 13.80451,level: 34}]->(g);
CREATE (n: Building {id: 3463, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:3463}) CREATE (b)-[r:Supply{max_supply: 29.553, current_output: 29.84853,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3463}) CREATE (b)-[r:Supply{max_supply: 9.851, current_output: 9.94951,level: 2}]->(g);
CREATE (n: Building {id: 3464, name:"building_subsistence_farms", level:26});
MATCH (g: Goods{code: 7}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 64.753, current_output: 64.753,level: 26}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 12.9506, current_output: 12.9506,level: 26}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 12.9506, current_output: 12.9506,level: 26}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 12.9506, current_output: 12.9506,level: 26}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 12.9506, current_output: 12.9506,level: 26}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 12.9506, current_output: 12.9506,level: 26}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 12.9506, current_output: 12.9506,level: 26}]->(g);
CREATE (n: Building {id: 3465, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3465}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3465}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 3466, name:"building_subsistence_farms", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 79.6744, current_output: 79.6744,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 15.93488, current_output: 15.93488,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 15.93488, current_output: 15.93488,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 15.93488, current_output: 15.93488,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 15.93488, current_output: 15.93488,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 15.93488, current_output: 15.93488,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 15.93488, current_output: 15.93488,level: 32}]->(g);
CREATE (n: Building {id: 3467, name:"building_subsistence_farms", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 74.805, current_output: 74.805,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 14.961, current_output: 14.961,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 14.961, current_output: 14.961,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 14.961, current_output: 14.961,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 14.961, current_output: 14.961,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 14.961, current_output: 14.961,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 14.961, current_output: 14.961,level: 30}]->(g);
CREATE (n: Building {id: 3468, name:"building_subsistence_farms", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 11.80287, current_output: 11.80287,level: 7}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 2.36057, current_output: 2.36057,level: 7}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 2.36057, current_output: 2.36057,level: 7}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 2.36057, current_output: 2.36057,level: 7}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 2.36057, current_output: 2.36057,level: 7}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 2.36057, current_output: 2.36057,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 2.36057, current_output: 2.36057,level: 7}]->(g);
CREATE (n: Building {id: 3469, name:"building_subsistence_farms", level:36});
MATCH (g: Goods{code: 7}), (b: Building{id:3469}) CREATE (b)-[r:Supply{max_supply: 89.6832, current_output: 89.6832,level: 36}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3469}) CREATE (b)-[r:Supply{max_supply: 17.93664, current_output: 17.93664,level: 36}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3469}) CREATE (b)-[r:Supply{max_supply: 17.93664, current_output: 17.93664,level: 36}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3469}) CREATE (b)-[r:Supply{max_supply: 17.93664, current_output: 17.93664,level: 36}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3469}) CREATE (b)-[r:Supply{max_supply: 17.93664, current_output: 17.93664,level: 36}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3469}) CREATE (b)-[r:Supply{max_supply: 17.93664, current_output: 17.93664,level: 36}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3469}) CREATE (b)-[r:Supply{max_supply: 17.93664, current_output: 17.93664,level: 36}]->(g);
CREATE (n: Building {id: 3470, name:"building_subsistence_farms", level:38});
MATCH (g: Goods{code: 7}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 94.63995, current_output: 94.63995,level: 38}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 18.92799, current_output: 18.92799,level: 38}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 18.92799, current_output: 18.92799,level: 38}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 18.92799, current_output: 18.92799,level: 38}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 18.92799, current_output: 18.92799,level: 38}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 18.92799, current_output: 18.92799,level: 38}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 18.92799, current_output: 18.92799,level: 38}]->(g);
CREATE (n: Building {id: 3471, name:"building_subsistence_farms", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:3471}) CREATE (b)-[r:Supply{max_supply: 9.9305, current_output: 9.9305,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3471}) CREATE (b)-[r:Supply{max_supply: 1.9861, current_output: 1.9861,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3471}) CREATE (b)-[r:Supply{max_supply: 1.9861, current_output: 1.9861,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3471}) CREATE (b)-[r:Supply{max_supply: 1.9861, current_output: 1.9861,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3471}) CREATE (b)-[r:Supply{max_supply: 1.9861, current_output: 1.9861,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3471}) CREATE (b)-[r:Supply{max_supply: 1.9861, current_output: 1.9861,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3471}) CREATE (b)-[r:Supply{max_supply: 1.9861, current_output: 1.9861,level: 4}]->(g);
CREATE (n: Building {id: 3475, name:"building_subsistence_orchards", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3475}) CREATE (b)-[r:Supply{max_supply: 1.7724, current_output: 1.7724,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3475}) CREATE (b)-[r:Supply{max_supply: 0.8862, current_output: 0.8862,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3475}) CREATE (b)-[r:Supply{max_supply: 2.6586, current_output: 2.6586,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3475}) CREATE (b)-[r:Supply{max_supply: 1.7724, current_output: 1.7724,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3475}) CREATE (b)-[r:Supply{max_supply: 1.7724, current_output: 1.7724,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3475}) CREATE (b)-[r:Supply{max_supply: 1.7724, current_output: 1.7724,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3475}) CREATE (b)-[r:Supply{max_supply: 5.88436, current_output: 5.88436,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3475}) CREATE (b)-[r:Supply{max_supply: 1.7724, current_output: 1.7724,level: 5}]->(g);
CREATE (n: Building {id: 3476, name:"building_subsistence_farms", level:57});
MATCH (g: Goods{code: 7}), (b: Building{id:3476}) CREATE (b)-[r:Supply{max_supply: 141.91575, current_output: 141.91575,level: 57}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3476}) CREATE (b)-[r:Supply{max_supply: 28.38315, current_output: 28.38315,level: 57}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3476}) CREATE (b)-[r:Supply{max_supply: 28.38315, current_output: 28.38315,level: 57}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3476}) CREATE (b)-[r:Supply{max_supply: 28.38315, current_output: 28.38315,level: 57}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3476}) CREATE (b)-[r:Supply{max_supply: 28.38315, current_output: 28.38315,level: 57}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3476}) CREATE (b)-[r:Supply{max_supply: 28.38315, current_output: 28.38315,level: 57}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3476}) CREATE (b)-[r:Supply{max_supply: 28.38315, current_output: 28.38315,level: 57}]->(g);
CREATE (n: Building {id: 3477, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3477}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3477}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 3479, name:"building_subsistence_orchards", level:35});
MATCH (g: Goods{code: 7}), (b: Building{id:3479}) CREATE (b)-[r:Supply{max_supply: 14.38605, current_output: 14.38605,level: 35}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3479}) CREATE (b)-[r:Supply{max_supply: 7.19302, current_output: 7.19302,level: 35}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3479}) CREATE (b)-[r:Supply{max_supply: 21.57907, current_output: 21.57907,level: 35}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3479}) CREATE (b)-[r:Supply{max_supply: 14.38605, current_output: 14.38605,level: 35}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3479}) CREATE (b)-[r:Supply{max_supply: 14.38605, current_output: 14.38605,level: 35}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3479}) CREATE (b)-[r:Supply{max_supply: 14.38605, current_output: 14.38605,level: 35}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3479}) CREATE (b)-[r:Supply{max_supply: 47.76168, current_output: 47.76168,level: 35}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3479}) CREATE (b)-[r:Supply{max_supply: 14.38605, current_output: 14.38605,level: 35}]->(g);
CREATE (n: Building {id: 3764, name:"building_subsistence_farms", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 6.06097, current_output: 6.06097,level: 3}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 1.21219, current_output: 1.21219,level: 3}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 1.21219, current_output: 1.21219,level: 3}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 1.21219, current_output: 1.21219,level: 3}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 1.21219, current_output: 1.21219,level: 3}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 1.21219, current_output: 1.21219,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 1.21219, current_output: 1.21219,level: 3}]->(g);
CREATE (n: Building {id: 3765, name:"building_subsistence_pastures", level:19});
MATCH (g: Goods{code: 7}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 5.78189, current_output: 5.78189,level: 19}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 8.67283, current_output: 8.67283,level: 19}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 2.89094, current_output: 2.89094,level: 19}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 5.78189, current_output: 5.78189,level: 19}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 5.78189, current_output: 5.78189,level: 19}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 5.78189, current_output: 5.78189,level: 19}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 19.19587, current_output: 19.19587,level: 19}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 5.78189, current_output: 5.78189,level: 19}]->(g);
CREATE (n: Building {id: 4055, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:4055}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.066637835346329, level: 10}]->(b);
CREATE (n: Building {id: 4056, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:4056}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.066637835346329, level: 10}]->(b);
CREATE (n: Building {id: 4057, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4057}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.066637835346329, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4057}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.2382193261593475, level: 5}]->(b);
CREATE (n: Building {id: 4058, name:"building_barracks", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:4058}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.440025623740863, level: 5}]->(b);
CREATE (n: Building {id: 4059, name:"building_barracks", level:15});
MATCH (g: Goods{code: 1}), (b: Building{id:4059}) CREATE (g)-[r:Demand{max_demand: 14.99985, current_input: 4.5999107534519625, level: 15}]->(b);
CREATE (n: Building {id: 4060, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:4060}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.066637835346329, level: 10}]->(b);
CREATE (n: Building {id: 4061, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:4061}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.066637835346329, level: 10}]->(b);
CREATE (n: Building {id: 4062, name:"building_barracks", level:12});
MATCH (g: Goods{code: 1}), (b: Building{id:4062}) CREATE (g)-[r:Demand{max_demand: 19.99992, current_input: 6.1332511375899745, level: 12}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4062}) CREATE (g)-[r:Demand{max_demand: 1.99992, current_input: 2.975891209086365, level: 12}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4062}) CREATE (g)-[r:Demand{max_demand: 9.99984, current_input: 8.47630302930026, level: 12}]->(b);
CREATE (n: Building {id: 4063, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4063}) CREATE (g)-[r:Demand{max_demand: 4.854, current_input: 1.488546005277108, level: 5}]->(b);
CREATE (n: Building {id: 4064, name:"building_barracks", level:7});
MATCH (g: Goods{code: 1}), (b: Building{id:4064}) CREATE (g)-[r:Demand{max_demand: 9.71139, current_input: 2.978131600780398, level: 7}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4064}) CREATE (g)-[r:Demand{max_demand: 1.94218, current_input: 2.889973793183406, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4064}) CREATE (g)-[r:Demand{max_demand: 4.85566, current_input: 4.115870410651779, level: 7}]->(b);
CREATE (n: Building {id: 4065, name:"building_barracks", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:4065}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.133275670692658, level: 20}]->(b);
CREATE (n: Building {id: 4066, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:4066}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.066637835346329, level: 10}]->(b);
CREATE (n: Building {id: 4067, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4067}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.5333189176731645, level: 5}]->(b);
CREATE (n: Building {id: 4068, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4068}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.5333189176731645, level: 5}]->(b);
CREATE (n: Building {id: 4069, name:"building_barracks", level:15});
CREATE (n: Building {id: 4070, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:4070}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.30666378353463286, level: 10}]->(b);
CREATE (n: Building {id: 4071, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4071}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.066637835346329, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4071}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.2382193261593475, level: 5}]->(b);
CREATE (n: Building {id: 4072, name:"building_barracks", level:3});
MATCH (g: Goods{code: 2}), (b: Building{id:4072}) CREATE (g)-[r:Demand{max_demand: 2.99997, current_input: 4.463970734090775, level: 3}]->(b);
CREATE (n: Building {id: 4073, name:"building_naval_base", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:4073}) CREATE (g)-[r:Demand{max_demand: 22.0, current_input: 11.492571619616776, level: 10}]->(b);
CREATE (n: Building {id: 4074, name:"building_naval_base", level:17});
MATCH (g: Goods{code: 5}), (b: Building{id:4074}) CREATE (g)-[r:Demand{max_demand: 36.99982, current_input: 19.32832187558769, level: 17}]->(b);
CREATE (n: Building {id: 4075, name:"building_naval_base", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:4075}) CREATE (g)-[r:Demand{max_demand: 4.99998, current_input: 2.6119376475750675, level: 3}]->(b);
CREATE (n: Building {id: 4076, name:"building_naval_base", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:4076}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.5671688572204694, level: 3}]->(b);
CREATE (n: Building {id: 4141, name:"building_trade_center", level:28});
CREATE (n: Building {id: 4157, name:"building_trade_center", level:24});
CREATE (n: Building {id: 4204, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:4204}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:4204}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 4205, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:4205}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:4205}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 4247, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:4247}) CREATE (g)-[r:Demand{max_demand: 1.516, current_input: 2.1503768612267202, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4247}) CREATE (g)-[r:Demand{max_demand: 3.032, current_input: 3.3228497180734533, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:4247}) CREATE (b)-[r:Supply{max_supply: 2.653, current_output: 2.653,level: 1}]->(g);
CREATE (n: Building {id: 4973, name:"building_lead_mine", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:4973}) CREATE (g)-[r:Demand{max_demand: 0.1, current_input: 0.07974752145817003, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:4973}) CREATE (b)-[r:Supply{max_supply: 0.4, current_output: 0.3189900858326801,level: 1}]->(g);
CREATE (n: Building {id: 5815, name:"building_trade_center", level:5});
