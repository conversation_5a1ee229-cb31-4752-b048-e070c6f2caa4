CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:24.804428886179608, pop_demand:458.1032046031077});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:25.964254895568423, pop_demand:41.5018347314912});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:14.298625577647243, pop_demand:52.64200004690045});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:28.208802795364765, pop_demand:43.6933224530995});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:38.41536085757416, pop_demand:105.58677861475282});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:32.82077846528534, pop_demand:98.86353903180903});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:52.5, pop_demand:4.860700120500845});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:32.37746192806993, pop_demand:61.555920833333374});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:10.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:3.245512738095237});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:25.42891483117357, pop_demand:8.764370726070888});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:29.895105465090474, pop_demand:26.40562095177887});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:16.702131506638857, pop_demand:12.079143158581584});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:18.99380515901235, pop_demand:18.43868699999999});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:8.515006245584274, pop_demand:4.771705859263203});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:32.88654101779905, pop_demand:119.42365700606373});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:15.145726111111115});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:3.7864315277777787});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 134218286, name:"building_coffee_plantationlevel", level:1});
MATCH (g: Goods{code: 41}), (b: Building{id:134218286}) CREATE (b)-[r:Supply{max_supply: 17.69, current_output: 17.69,level: 1}]->(g);
CREATE (n: Building {id: 1663, name:"building_coffee_plantationlevel", level:1});
MATCH (g: Goods{code: 41}), (b: Building{id:1663}) CREATE (b)-[r:Supply{max_supply: 17.6862, current_output: 17.6862,level: 1}]->(g);
CREATE (n: Building {id: 1664, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1664}) CREATE (b)-[r:Supply{max_supply: 26.5293, current_output: 26.5293,level: 1}]->(g);
CREATE (n: Building {id: 1725, name:"building_government_administrationlevel", level:1});
CREATE (n: Building {id: 1726, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1726}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1727, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:1727}) CREATE (b)-[r:Supply{max_supply: 66.32324509803921, current_output: 67.64971,level: 3}]->(g);
CREATE (n: Building {id: 1728, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1728}) CREATE (b)-[r:Supply{max_supply: 53.05859405940594, current_output: 53.58918,level: 2}]->(g);
CREATE (n: Building {id: 1729, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1729}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1729}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 1730, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1730}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1731, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1731}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 38.5,level: 1}]->(g);
CREATE (n: Building {id: 1732, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1732}) CREATE (b)-[r:Supply{max_supply: 29.994, current_output: 32.9934,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1732}) CREATE (b)-[r:Supply{max_supply: 4.999, current_output: 5.4989,level: 1}]->(g);
CREATE (n: Building {id: 1733, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:1733}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1733}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1734, name:"building_government_administrationlevel", level:4});
CREATE (n: Building {id: 1735, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1735}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 64.52566510929599, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1735}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 1}]->(g);
CREATE (n: Building {id: 1736, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1736}) CREATE (b)-[r:Supply{max_supply: 51.67019801980198, current_output: 52.1869,level: 2}]->(g);
CREATE (n: Building {id: 1737, name:"building_cotton_plantationlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1737}) CREATE (b)-[r:Supply{max_supply: 106.25879527559054, current_output: 134.94867,level: 3}]->(g);
CREATE (n: Building {id: 1738, name:"building_maize_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1738}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
CREATE (n: Building {id: 1739, name:"building_barrackslevel", level:6});
MATCH (g: Goods{code: 1}), (b: Building{id:1739}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.0, level: 6}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1739}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.0, level: 6}]->(b);
CREATE (n: Building {id: 1740, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1740}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 32.26283255464799, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1740}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 27.164788818540938, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1740}) CREATE (b)-[r:Supply{max_supply: 90.00000000000001, current_output: 65.37359161390572,level: 2}]->(g);
CREATE (n: Building {id: 1741, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1741}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
CREATE (n: Building {id: 1742, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1742}) CREATE (b)-[r:Supply{max_supply: 44.225, current_output: 44.66725,level: 2}]->(g);
CREATE (n: Building {id: 1743, name:"building_coffee_plantationlevel", level:2});
MATCH (g: Goods{code: 41}), (b: Building{id:1743}) CREATE (b)-[r:Supply{max_supply: 35.37239603960396, current_output: 35.72612,level: 2}]->(g);
CREATE (n: Building {id: 1744, name:"building_maize_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1744}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 100.8,level: 3}]->(g);
CREATE (n: Building {id: 1745, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:1745}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1745}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 2931, name:"building_subsistence_farmslevel", level:17});
MATCH (g: Goods{code: 7}), (b: Building{id:2931}) CREATE (b)-[r:Supply{max_supply: 2.3375, current_output: 2.57125,level: 17}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2931}) CREATE (b)-[r:Supply{max_supply: 0.46749999999999997, current_output: 0.51425,level: 17}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2931}) CREATE (b)-[r:Supply{max_supply: 0.46749999999999997, current_output: 0.51425,level: 17}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2931}) CREATE (b)-[r:Supply{max_supply: 0.46749999999999997, current_output: 0.51425,level: 17}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2931}) CREATE (b)-[r:Supply{max_supply: 0.46749999999999997, current_output: 0.51425,level: 17}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2931}) CREATE (b)-[r:Supply{max_supply: 0.46749999999999997, current_output: 0.51425,level: 17}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2931}) CREATE (b)-[r:Supply{max_supply: 0.6545, current_output: 0.71995,level: 17}]->(g);
CREATE (n: Building {id: 3065, name:"building_subsistence_farmslevel", level:86});
MATCH (g: Goods{code: 7}), (b: Building{id:3065}) CREATE (b)-[r:Supply{max_supply: 58.21554545454545, current_output: 64.0371,level: 86}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3065}) CREATE (b)-[r:Supply{max_supply: 11.64310909090909, current_output: 12.80742,level: 86}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3065}) CREATE (b)-[r:Supply{max_supply: 11.64310909090909, current_output: 12.80742,level: 86}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3065}) CREATE (b)-[r:Supply{max_supply: 11.64310909090909, current_output: 12.80742,level: 86}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3065}) CREATE (b)-[r:Supply{max_supply: 11.64310909090909, current_output: 12.80742,level: 86}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3065}) CREATE (b)-[r:Supply{max_supply: 11.64310909090909, current_output: 12.80742,level: 86}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3065}) CREATE (b)-[r:Supply{max_supply: 16.300345454545454, current_output: 17.93038,level: 86}]->(g);
CREATE (n: Building {id: 3066, name:"building_subsistence_farmslevel", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:3066}) CREATE (b)-[r:Supply{max_supply: 3.4747999999999997, current_output: 3.82228,level: 16}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3066}) CREATE (b)-[r:Supply{max_supply: 0.6949545454545454, current_output: 0.76445,level: 16}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3066}) CREATE (b)-[r:Supply{max_supply: 0.6949545454545454, current_output: 0.76445,level: 16}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3066}) CREATE (b)-[r:Supply{max_supply: 0.6949545454545454, current_output: 0.76445,level: 16}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3066}) CREATE (b)-[r:Supply{max_supply: 0.6949545454545454, current_output: 0.76445,level: 16}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3066}) CREATE (b)-[r:Supply{max_supply: 0.6949545454545454, current_output: 0.76445,level: 16}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3066}) CREATE (b)-[r:Supply{max_supply: 0.9729363636363636, current_output: 1.07023,level: 16}]->(g);
CREATE (n: Building {id: 3067, name:"building_subsistence_farmslevel", level:67});
MATCH (g: Goods{code: 7}), (b: Building{id:3067}) CREATE (b)-[r:Supply{max_supply: 20.61421818181818, current_output: 22.67564,level: 67}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3067}) CREATE (b)-[r:Supply{max_supply: 4.122836363636363, current_output: 4.53512,level: 67}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3067}) CREATE (b)-[r:Supply{max_supply: 4.122836363636363, current_output: 4.53512,level: 67}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3067}) CREATE (b)-[r:Supply{max_supply: 4.122836363636363, current_output: 4.53512,level: 67}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3067}) CREATE (b)-[r:Supply{max_supply: 4.122836363636363, current_output: 4.53512,level: 67}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3067}) CREATE (b)-[r:Supply{max_supply: 4.122836363636363, current_output: 4.53512,level: 67}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3067}) CREATE (b)-[r:Supply{max_supply: 5.771981818181818, current_output: 6.34918,level: 67}]->(g);
CREATE (n: Building {id: 3068, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3068}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.527464803090156, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3068}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 22.637324015450783,level: 2}]->(g);
CREATE (n: Building {id: 3069, name:"building_subsistence_farmslevel", level:38});
MATCH (g: Goods{code: 7}), (b: Building{id:3069}) CREATE (b)-[r:Supply{max_supply: 8.7856, current_output: 9.66416,level: 38}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3069}) CREATE (b)-[r:Supply{max_supply: 1.7571181818181818, current_output: 1.93283,level: 38}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3069}) CREATE (b)-[r:Supply{max_supply: 1.7571181818181818, current_output: 1.93283,level: 38}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3069}) CREATE (b)-[r:Supply{max_supply: 1.7571181818181818, current_output: 1.93283,level: 38}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3069}) CREATE (b)-[r:Supply{max_supply: 1.7571181818181818, current_output: 1.93283,level: 38}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3069}) CREATE (b)-[r:Supply{max_supply: 1.7571181818181818, current_output: 1.93283,level: 38}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3069}) CREATE (b)-[r:Supply{max_supply: 2.459963636363636, current_output: 2.70596,level: 38}]->(g);
CREATE (n: Building {id: 3079, name:"building_subsistence_farmslevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 2.4314999999999998, current_output: 2.67465,level: 3}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 0.48629999999999995, current_output: 0.53493,level: 3}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 0.48629999999999995, current_output: 0.53493,level: 3}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 0.48629999999999995, current_output: 0.53493,level: 3}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 0.48629999999999995, current_output: 0.53493,level: 3}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 0.48629999999999995, current_output: 0.53493,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 0.6808181818181818, current_output: 0.7489,level: 3}]->(g);
CREATE (n: Building {id: 268439046, name:"building_dye_plantationlevel", level:1});
MATCH (g: Goods{code: 21}), (b: Building{id:268439046}) CREATE (b)-[r:Supply{max_supply: 24.58325, current_output: 24.58325,level: 1}]->(g);
CREATE (n: Building {id: 3884, name:"building_trade_centerlevel", level:26});
CREATE (n: Building {id: 3885, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3885}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.263732401545078, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3885}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 11.318662007725392,level: 2}]->(g);
CREATE (n: Building {id: 4069, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4070, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4755, name:"building_coffee_plantationlevel", level:1});
MATCH (g: Goods{code: 41}), (b: Building{id:4755}) CREATE (b)-[r:Supply{max_supply: 17.69, current_output: 17.69,level: 1}]->(g);
CREATE (n: Building {id: 268440876, name:"building_coffee_plantationlevel", level:1});
MATCH (g: Goods{code: 41}), (b: Building{id:268440876}) CREATE (b)-[r:Supply{max_supply: 17.6862, current_output: 17.6862,level: 1}]->(g);
CREATE (n: Building {id: 33559988, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:33559988}) CREATE (b)-[r:Supply{max_supply: 22.1175, current_output: 22.1175,level: 1}]->(g);
CREATE (n: Building {id: 151000616, name:"building_dye_plantationlevel", level:1});
MATCH (g: Goods{code: 21}), (b: Building{id:151000616}) CREATE (b)-[r:Supply{max_supply: 24.60275, current_output: 24.60275,level: 1}]->(g);
CREATE (n: Building {id: 5984, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:5984}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 134223744, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:134223744}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:134223744}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 83892387, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:83892387}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 40.328540693309996, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:83892387}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 33.955986023176166, level: 1}]->(b);
CREATE (n: Building {id: 6576, name:"building_subsistence_rice_paddieslevel", level:67});
MATCH (g: Goods{code: 7}), (b: Building{id:6576}) CREATE (b)-[r:Supply{max_supply: 0.027127272727272722, current_output: 0.02984,level: 67}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6576}) CREATE (b)-[r:Supply{max_supply: 0.004518181818181817, current_output: 0.00497,level: 67}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6576}) CREATE (b)-[r:Supply{max_supply: 0.004518181818181817, current_output: 0.00497,level: 67}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6576}) CREATE (b)-[r:Supply{max_supply: 0.006027272727272727, current_output: 0.00663,level: 67}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6576}) CREATE (b)-[r:Supply{max_supply: 0.006027272727272727, current_output: 0.00663,level: 67}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6576}) CREATE (b)-[r:Supply{max_supply: 0.006027272727272727, current_output: 0.00663,level: 67}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6576}) CREATE (b)-[r:Supply{max_supply: 0.009036363636363634, current_output: 0.00994,level: 67}]->(g);
