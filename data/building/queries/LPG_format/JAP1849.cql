CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:15.182548646412524, pop_demand:3511.041611888566});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:23.77924771467431, pop_demand:462.3166052141664});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:13.776684808221313, pop_demand:400.8660020659782});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:19.035832723964884, pop_demand:277.63693176701014});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:33.88943488704233, pop_demand:1108.1221324999594});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:33.58607876559046, pop_demand:962.4672152531722});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:36.820260414778225, pop_demand:56.79238728797359});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:14.455546566677441, pop_demand:373.6752466666669});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:78.74978124817706, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:32.52084022252091, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:27.998213137399716, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:32.972972972972975, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:27.12419583839708, pop_demand:13.31759808350544});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:46.67223875515295, pop_demand:44.3010696249877});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:69.95487622560915, pop_demand:124.89256244192657});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:32.38291342237582, pop_demand:81.16115278419713});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:27.525056502390985, pop_demand:78.5780013144782});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:22.455493144799494, pop_demand:713.3413159361919});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:35.73787687033392, pop_demand:276.6776725000002});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:13.636179298657435, pop_demand:30.533628190119394});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:44.08318383389272, pop_demand:443.2668436728551});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:67.62280337765114, pop_demand:144.47311430443636});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:72.80976788388692, pop_demand:47.52994617998254});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 33555019, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:33555019}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.9166909724247705, level: 1}]->(b);
CREATE (n: Building {id: 2550, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2550}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
CREATE (n: Building {id: 2551, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2551}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.833381944849541, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2551}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 58.333819448495404,level: 2}]->(g);
CREATE (n: Building {id: 2552, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2552}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
CREATE (n: Building {id: 2553, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 2554, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2554}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 20.906319446962375, level: 3}]->(b);
CREATE (n: Building {id: 2555, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2555}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 100.8,level: 3}]->(g);
CREATE (n: Building {id: 2556, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2556}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 38.5,level: 1}]->(g);
CREATE (n: Building {id: 2557, name:"building_tobacco_plantationlevel", level:4});
MATCH (g: Goods{code: 43}), (b: Building{id:2557}) CREATE (b)-[r:Supply{max_supply: 88.431, current_output: 91.08393,level: 4}]->(g);
CREATE (n: Building {id: 2558, name:"building_barrackslevel", level:15});
CREATE (n: Building {id: 2559, name:"building_government_administrationlevel", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:2559}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 48.7814120429122, level: 7}]->(b);
CREATE (n: Building {id: 2560, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2560}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 32.060798162097406, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2560}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 25.002482, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2560}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2560}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 2561, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:2561}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.750072917274311, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2561}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 87.5007291727431,level: 3}]->(g);
CREATE (n: Building {id: 2562, name:"building_whaling_stationlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2562}) CREATE (g)-[r:Demand{max_demand: 9.999, current_input: 5.832798606655056, level: 2}]->(b);
MATCH (g: Goods{code: 28}), (b: Building{id:2562}) CREATE (b)-[r:Supply{max_supply: 39.996, current_output: 23.331194426620225,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2562}) CREATE (b)-[r:Supply{max_supply: 19.998, current_output: 11.665597213310113,level: 2}]->(g);
CREATE (n: Building {id: 2563, name:"building_rice_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2563}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 117.6,level: 3}]->(g);
CREATE (n: Building {id: 2564, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2564}) CREATE (b)-[r:Supply{max_supply: 44.23, current_output: 44.6723,level: 2}]->(g);
CREATE (n: Building {id: 2565, name:"building_tea_plantationlevel", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2565}) CREATE (b)-[r:Supply{max_supply: 78.74479611650486, current_output: 81.10714,level: 4}]->(g);
CREATE (n: Building {id: 2566, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:2566}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.750072917274311, level: 3}]->(b);
CREATE (n: Building {id: 2567, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2568, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:2568}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 34.84386574493729, level: 5}]->(b);
CREATE (n: Building {id: 2569, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2569}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 42.72683914189663, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2569}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 80.1519954052435, level: 1}]->(b);
CREATE (n: Building {id: 2570, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2570}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 64.12159632419481, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2570}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 80.0,level: 2}]->(g);
CREATE (n: Building {id: 2571, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2571}) CREATE (g)-[r:Demand{max_demand: 17.0, current_input: 29.054250616489707, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2571}) CREATE (g)-[r:Demand{max_demand: 34.0, current_input: 36.33557125037706, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2571}) CREATE (g)-[r:Demand{max_demand: 17.0, current_input: 22.2, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2571}) CREATE (b)-[r:Supply{max_supply: 42.5, current_output: 42.5,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2571}) CREATE (b)-[r:Supply{max_supply: 34.0, current_output: 34.0,level: 2}]->(g);
CREATE (n: Building {id: 2572, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2572}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 22.2,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2572}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 22.2,level: 2}]->(g);
CREATE (n: Building {id: 2573, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2573}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2573}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 11.1,level: 2}]->(g);
CREATE (n: Building {id: 2574, name:"building_rice_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:2574}) CREATE (b)-[r:Supply{max_supply: 80.00000000000001, current_output: 90.4,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2574}) CREATE (b)-[r:Supply{max_supply: 24.000000000000004, current_output: 27.12,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2574}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 40.68,level: 4}]->(g);
CREATE (n: Building {id: 2575, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2575}) CREATE (b)-[r:Supply{max_supply: 44.215495049504945, current_output: 44.65765,level: 2}]->(g);
CREATE (n: Building {id: 2576, name:"building_tea_plantationlevel", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2576}) CREATE (b)-[r:Supply{max_supply: 78.74479611650486, current_output: 81.10714,level: 4}]->(g);
CREATE (n: Building {id: 2577, name:"building_barrackslevel", level:15});
CREATE (n: Building {id: 2578, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2578}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 69.68773148987457, level: 10}]->(b);
CREATE (n: Building {id: 2579, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2579}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 42.72683914189663, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2579}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 80.1519954052435, level: 1}]->(b);
CREATE (n: Building {id: 2580, name:"building_textile_millslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:2580}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 205.08882788110378, level: 4}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2580}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 53.28398000000001, level: 4}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2580}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 100.0,level: 4}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2580}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 80.0,level: 4}]->(g);
CREATE (n: Building {id: 2581, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2581}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 96.18239448629221, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2581}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 3}]->(g);
CREATE (n: Building {id: 2582, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:2582}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.750072917274311, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2582}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 87.5007291727431,level: 3}]->(g);
CREATE (n: Building {id: 2583, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2583}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2583}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 11.1,level: 2}]->(g);
CREATE (n: Building {id: 2584, name:"building_rice_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2584}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 114.0,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2584}) CREATE (b)-[r:Supply{max_supply: 30.000000000000004, current_output: 34.2,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2584}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 51.3,level: 5}]->(g);
CREATE (n: Building {id: 2585, name:"building_silk_plantationlevel", level:3});
MATCH (g: Goods{code: 20}), (b: Building{id:2585}) CREATE (b)-[r:Supply{max_supply: 59.05859803921569, current_output: 60.23977,level: 3}]->(g);
CREATE (n: Building {id: 2586, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2586}) CREATE (b)-[r:Supply{max_supply: 44.215495049504945, current_output: 44.65765,level: 2}]->(g);
CREATE (n: Building {id: 2587, name:"building_tea_plantationlevel", level:5});
MATCH (g: Goods{code: 40}), (b: Building{id:2587}) CREATE (b)-[r:Supply{max_supply: 98.431, current_output: 102.36824,level: 5}]->(g);
CREATE (n: Building {id: 2588, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2589, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:2589}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 34.84386574493729, level: 5}]->(b);
CREATE (n: Building {id: 2590, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2590}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 68.36294262703461, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2590}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 85.49546176559308, level: 2}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:2590}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 70.0,level: 2}]->(g);
CREATE (n: Building {id: 2591, name:"building_textile_millslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2591}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 102.5444139405519, level: 2}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2591}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 26.641990000000003, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2591}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 2}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2591}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 2}]->(g);
CREATE (n: Building {id: 2592, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2592}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 32.060798162097406, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2592}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 25.002482, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2592}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2592}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 2593, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2593}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.833381944849541, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2593}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 58.333819448495404,level: 2}]->(g);
CREATE (n: Building {id: 2594, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2594}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 77.7,level: 2}]->(g);
CREATE (n: Building {id: 2595, name:"building_dye_plantationlevel", level:2});
MATCH (g: Goods{code: 21}), (b: Building{id:2595}) CREATE (b)-[r:Supply{max_supply: 49.22049504950495, current_output: 49.7127,level: 2}]->(g);
CREATE (n: Building {id: 2596, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2596}) CREATE (b)-[r:Supply{max_supply: 44.23, current_output: 44.6723,level: 2}]->(g);
CREATE (n: Building {id: 2597, name:"building_tea_plantationlevel", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2597}) CREATE (b)-[r:Supply{max_supply: 78.74879611650485, current_output: 81.11126,level: 4}]->(g);
CREATE (n: Building {id: 2598, name:"building_barrackslevel", level:15});
CREATE (n: Building {id: 2599, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2599}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.9166909724247705, level: 1}]->(b);
CREATE (n: Building {id: 2600, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:2600}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 2601, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2601}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2601}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 11.1,level: 2}]->(g);
CREATE (n: Building {id: 2602, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:2602}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.750072917274311, level: 3}]->(b);
CREATE (n: Building {id: 2603, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2603}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 20.906319446962375, level: 3}]->(b);
CREATE (n: Building {id: 2604, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2604}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 64.12159632419481, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2604}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 80.0,level: 2}]->(g);
CREATE (n: Building {id: 2605, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2605}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 32.060798162097406, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2605}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 25.002482, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2605}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2605}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 2606, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2606}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 171.0,level: 5}]->(g);
CREATE (n: Building {id: 2607, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2607}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2607}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 2608, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2608}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 44.4,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2608}) CREATE (b)-[r:Supply{max_supply: 12.0, current_output: 13.32,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2608}) CREATE (b)-[r:Supply{max_supply: 18.0, current_output: 19.98,level: 2}]->(g);
CREATE (n: Building {id: 2609, name:"building_silk_plantationlevel", level:1});
MATCH (g: Goods{code: 20}), (b: Building{id:2609}) CREATE (b)-[r:Supply{max_supply: 19.6862, current_output: 19.6862,level: 1}]->(g);
CREATE (n: Building {id: 2610, name:"building_tobacco_plantationlevel", level:4});
MATCH (g: Goods{code: 43}), (b: Building{id:2610}) CREATE (b)-[r:Supply{max_supply: 88.431, current_output: 91.08393,level: 4}]->(g);
CREATE (n: Building {id: 2611, name:"building_tea_plantationlevel", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:2611}) CREATE (b)-[r:Supply{max_supply: 19.6862, current_output: 19.6862,level: 1}]->(g);
CREATE (n: Building {id: 2612, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2613, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:2613}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.968773148987458, level: 1}]->(b);
CREATE (n: Building {id: 2614, name:"building_glassworkslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2614}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 64.12159632419481, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2614}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 50.004964, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2614}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 2}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2614}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 2}]->(g);
CREATE (n: Building {id: 2615, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2615}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 77.7,level: 2}]->(g);
CREATE (n: Building {id: 2616, name:"building_dye_plantationlevel", level:3});
MATCH (g: Goods{code: 21}), (b: Building{id:2616}) CREATE (b)-[r:Supply{max_supply: 73.82324509803922, current_output: 75.29971,level: 3}]->(g);
CREATE (n: Building {id: 2617, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:2617}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 2618, name:"building_tea_plantationlevel", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2618}) CREATE (b)-[r:Supply{max_supply: 78.74479611650486, current_output: 81.10714,level: 4}]->(g);
CREATE (n: Building {id: 2619, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 3591, name:"building_subsistence_farmslevel", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 3.0041090909090906, current_output: 3.30452,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 0.5006818181818181, current_output: 0.55075,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 0.5006818181818181, current_output: 0.55075,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 0.5006818181818181, current_output: 0.55075,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 0.5006818181818181, current_output: 0.55075,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 0.5006818181818181, current_output: 0.55075,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 0.7009545454545454, current_output: 0.77105,level: 29}]->(g);
CREATE (n: Building {id: 3592, name:"building_subsistence_farmslevel", level:180});
MATCH (g: Goods{code: 7}), (b: Building{id:3592}) CREATE (b)-[r:Supply{max_supply: 460.0422, current_output: 506.04642,level: 180}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3592}) CREATE (b)-[r:Supply{max_supply: 76.6737, current_output: 84.34107,level: 180}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3592}) CREATE (b)-[r:Supply{max_supply: 76.6737, current_output: 84.34107,level: 180}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3592}) CREATE (b)-[r:Supply{max_supply: 76.6737, current_output: 84.34107,level: 180}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3592}) CREATE (b)-[r:Supply{max_supply: 76.6737, current_output: 84.34107,level: 180}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3592}) CREATE (b)-[r:Supply{max_supply: 76.6737, current_output: 84.34107,level: 180}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3592}) CREATE (b)-[r:Supply{max_supply: 107.34317272727272, current_output: 118.07749,level: 180}]->(g);
CREATE (n: Building {id: 3593, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3593}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.343466360349567, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3593}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3594, name:"building_subsistence_farmslevel", level:269});
MATCH (g: Goods{code: 7}), (b: Building{id:3594}) CREATE (b)-[r:Supply{max_supply: 702.4934999999999, current_output: 772.74285,level: 269}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3594}) CREATE (b)-[r:Supply{max_supply: 117.08224545454544, current_output: 128.79047,level: 269}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3594}) CREATE (b)-[r:Supply{max_supply: 117.08224545454544, current_output: 128.79047,level: 269}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3594}) CREATE (b)-[r:Supply{max_supply: 117.08224545454544, current_output: 128.79047,level: 269}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3594}) CREATE (b)-[r:Supply{max_supply: 117.08224545454544, current_output: 128.79047,level: 269}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3594}) CREATE (b)-[r:Supply{max_supply: 117.08224545454544, current_output: 128.79047,level: 269}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3594}) CREATE (b)-[r:Supply{max_supply: 163.91514545454544, current_output: 180.30666,level: 269}]->(g);
CREATE (n: Building {id: 3595, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3595}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.686932720699135, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3595}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 2}]->(g);
CREATE (n: Building {id: 3596, name:"building_subsistence_farmslevel", level:362});
MATCH (g: Goods{code: 7}), (b: Building{id:3596}) CREATE (b)-[r:Supply{max_supply: 817.4864999999999, current_output: 899.23515,level: 362}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3596}) CREATE (b)-[r:Supply{max_supply: 136.24774545454545, current_output: 149.87252,level: 362}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3596}) CREATE (b)-[r:Supply{max_supply: 136.24774545454545, current_output: 149.87252,level: 362}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3596}) CREATE (b)-[r:Supply{max_supply: 136.24774545454545, current_output: 149.87252,level: 362}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3596}) CREATE (b)-[r:Supply{max_supply: 136.24774545454545, current_output: 149.87252,level: 362}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3596}) CREATE (b)-[r:Supply{max_supply: 136.24774545454545, current_output: 149.87252,level: 362}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3596}) CREATE (b)-[r:Supply{max_supply: 190.74684545454542, current_output: 209.82153,level: 362}]->(g);
CREATE (n: Building {id: 3597, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3597}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 16.030399081048703, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3597}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 75.0,level: 3}]->(g);
CREATE (n: Building {id: 3598, name:"building_subsistence_farmslevel", level:442});
MATCH (g: Goods{code: 7}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 1079.2711727272726, current_output: 1187.19829,level: 442}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 179.87852727272724, current_output: 197.86638,level: 442}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 179.87852727272724, current_output: 197.86638,level: 442}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 179.87852727272724, current_output: 197.86638,level: 442}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 179.87852727272724, current_output: 197.86638,level: 442}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 179.87852727272724, current_output: 197.86638,level: 442}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 251.82993636363634, current_output: 277.01293,level: 442}]->(g);
CREATE (n: Building {id: 3599, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3599}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 21.37386544139827, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3599}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 15.551840829898037, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3599}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 142.20736331959213,level: 4}]->(g);
CREATE (n: Building {id: 3600, name:"building_subsistence_farmslevel", level:229});
MATCH (g: Goods{code: 7}), (b: Building{id:3600}) CREATE (b)-[r:Supply{max_supply: 559.2111272727273, current_output: 615.13224,level: 229}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3600}) CREATE (b)-[r:Supply{max_supply: 93.20185454545454, current_output: 102.52204,level: 229}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3600}) CREATE (b)-[r:Supply{max_supply: 93.20185454545454, current_output: 102.52204,level: 229}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3600}) CREATE (b)-[r:Supply{max_supply: 93.20185454545454, current_output: 102.52204,level: 229}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3600}) CREATE (b)-[r:Supply{max_supply: 93.20185454545454, current_output: 102.52204,level: 229}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3600}) CREATE (b)-[r:Supply{max_supply: 93.20185454545454, current_output: 102.52204,level: 229}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3600}) CREATE (b)-[r:Supply{max_supply: 130.48259090909087, current_output: 143.53085,level: 229}]->(g);
CREATE (n: Building {id: 3601, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3601}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 16.030399081048703, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3601}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 75.0,level: 3}]->(g);
CREATE (n: Building {id: 3602, name:"building_subsistence_orchardslevel", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:3602}) CREATE (b)-[r:Supply{max_supply: 3.7375727272727266, current_output: 4.11133,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3602}) CREATE (b)-[r:Supply{max_supply: 1.868781818181818, current_output: 2.05566,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3602}) CREATE (b)-[r:Supply{max_supply: 5.6063636363636355, current_output: 6.167,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3602}) CREATE (b)-[r:Supply{max_supply: 3.7375727272727266, current_output: 4.11133,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3602}) CREATE (b)-[r:Supply{max_supply: 3.7375727272727266, current_output: 4.11133,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3602}) CREATE (b)-[r:Supply{max_supply: 3.7375727272727266, current_output: 4.11133,level: 14}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3602}) CREATE (b)-[r:Supply{max_supply: 12.408763636363636, current_output: 13.64964,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3602}) CREATE (b)-[r:Supply{max_supply: 5.23260909090909, current_output: 5.75587,level: 14}]->(g);
CREATE (n: Building {id: 3603, name:"building_subsistence_farmslevel", level:159});
MATCH (g: Goods{code: 7}), (b: Building{id:3603}) CREATE (b)-[r:Supply{max_supply: 353.23280909090903, current_output: 388.55609,level: 159}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3603}) CREATE (b)-[r:Supply{max_supply: 58.87212727272726, current_output: 64.75934,level: 159}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3603}) CREATE (b)-[r:Supply{max_supply: 58.87212727272726, current_output: 64.75934,level: 159}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3603}) CREATE (b)-[r:Supply{max_supply: 58.87212727272726, current_output: 64.75934,level: 159}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3603}) CREATE (b)-[r:Supply{max_supply: 58.87212727272726, current_output: 64.75934,level: 159}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3603}) CREATE (b)-[r:Supply{max_supply: 58.87212727272726, current_output: 64.75934,level: 159}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3603}) CREATE (b)-[r:Supply{max_supply: 82.4209818181818, current_output: 90.66308,level: 159}]->(g);
CREATE (n: Building {id: 3604, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3604}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.686932720699135, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3604}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 2}]->(g);
CREATE (n: Building {id: 3605, name:"building_subsistence_farmslevel", level:71});
MATCH (g: Goods{code: 7}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 184.0511636363636, current_output: 202.45628,level: 71}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 30.675190909090908, current_output: 33.74271,level: 71}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 30.675190909090908, current_output: 33.74271,level: 71}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 30.675190909090908, current_output: 33.74271,level: 71}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 30.675190909090908, current_output: 33.74271,level: 71}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 30.675190909090908, current_output: 33.74271,level: 71}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 42.94527272727272, current_output: 47.2398,level: 71}]->(g);
CREATE (n: Building {id: 3606, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3606}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.343466360349567, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3606}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 4400, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4401, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4402, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4403, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4404, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4405, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4406, name:"building_conscription_centerlevel", level:22});
CREATE (n: Building {id: 4407, name:"building_conscription_centerlevel", level:12});
CREATE (n: Building {id: 4684, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:4684}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.9166909724247705, level: 1}]->(b);
CREATE (n: Building {id: 67113664, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:67113664}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:67113664}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 16782058, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16782058}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:16782058}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 11.1,level: 2}]->(g);
CREATE (n: Building {id: 16782160, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782160}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 42.72683914189663, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782160}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 80.1519954052435, level: 1}]->(b);
CREATE (n: Building {id: 16782175, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:16782175}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.9166909724247705, level: 1}]->(b);
CREATE (n: Building {id: 16782346, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:16782346}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.9166909724247705, level: 1}]->(b);
CREATE (n: Building {id: 117445789, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:117445789}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.484386574493729, level: 1}]->(b);
CREATE (n: Building {id: 16782661, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782661}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:16782661}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 6151, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:6151}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.9166909724247705, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:6151}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 29.166909724247702,level: 1}]->(g);
CREATE (n: Building {id: 33560699, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33560699}) CREATE (b)-[r:Supply{max_supply: 35.3724, current_output: 44.2155,level: 1}]->(g);
CREATE (n: Building {id: 50337937, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:50337937}) CREATE (b)-[r:Supply{max_supply: 35.3724, current_output: 44.2155,level: 1}]->(g);
CREATE (n: Building {id: 6451, name:"building_subsistence_rice_paddieslevel", level:29});
CREATE (n: Building {id: 6452, name:"building_subsistence_rice_paddieslevel", level:180});
MATCH (g: Goods{code: 7}), (b: Building{id:6452}) CREATE (b)-[r:Supply{max_supply: 0.009899999999999999, current_output: 0.01089,level: 180}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6452}) CREATE (b)-[r:Supply{max_supply: 0.0013454545454545453, current_output: 0.00148,level: 180}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6452}) CREATE (b)-[r:Supply{max_supply: 0.0013454545454545453, current_output: 0.00148,level: 180}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6452}) CREATE (b)-[r:Supply{max_supply: 0.0018, current_output: 0.00198,level: 180}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6452}) CREATE (b)-[r:Supply{max_supply: 0.0018, current_output: 0.00198,level: 180}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6452}) CREATE (b)-[r:Supply{max_supply: 0.0018, current_output: 0.00198,level: 180}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6452}) CREATE (b)-[r:Supply{max_supply: 0.0026999999999999997, current_output: 0.00297,level: 180}]->(g);
CREATE (n: Building {id: 6453, name:"building_subsistence_rice_paddieslevel", level:269});
CREATE (n: Building {id: 6454, name:"building_subsistence_rice_paddieslevel", level:362});
CREATE (n: Building {id: 6455, name:"building_subsistence_rice_paddieslevel", level:442});
CREATE (n: Building {id: 6456, name:"building_subsistence_rice_paddieslevel", level:229});
CREATE (n: Building {id: 6457, name:"building_subsistence_rice_paddieslevel", level:159});
CREATE (n: Building {id: 6458, name:"building_subsistence_rice_paddieslevel", level:71});
MATCH (g: Goods{code: 7}), (b: Building{id:6458}) CREATE (b)-[r:Supply{max_supply: 0.046854545454545454, current_output: 0.05154,level: 71}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6458}) CREATE (b)-[r:Supply{max_supply: 0.006381818181818181, current_output: 0.00702,level: 71}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6458}) CREATE (b)-[r:Supply{max_supply: 0.006381818181818181, current_output: 0.00702,level: 71}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6458}) CREATE (b)-[r:Supply{max_supply: 0.008518181818181817, current_output: 0.00937,level: 71}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6458}) CREATE (b)-[r:Supply{max_supply: 0.008518181818181817, current_output: 0.00937,level: 71}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6458}) CREATE (b)-[r:Supply{max_supply: 0.008518181818181817, current_output: 0.00937,level: 71}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6458}) CREATE (b)-[r:Supply{max_supply: 0.012772727272727272, current_output: 0.01405,level: 71}]->(g);
