CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:58.006507694648725, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:58.855913385268856, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:68.66523228281366, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:24.11803417749416, pop_demand:2593.0198287234243});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:15.260311218828036, pop_demand:69.4281059140697});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:24.022625444340953, pop_demand:231.9604581400628});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:21.71647940303923, pop_demand:235.2013204863097});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:48.404068657861984, pop_demand:163.21337535255674});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:26.04753898303582, pop_demand:570.3361376101255});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:25.415724704564013, pop_demand:436.175014892174});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:38.51019336472723, pop_demand:24.56193142700652});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:16.514837775889177, pop_demand:308.1300024743806});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:31.470027811587197, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30.52194185584568, pop_demand:67.5994609157516});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:51.950932927009845, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:23.65842002434278, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:53.846153846153854, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:63.453481258559364, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:63.61265772521836, pop_demand:62.12231084674532});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:23.192335615208588, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:39.28903581625213, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:66.01301538929744, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:108.66240616073478, pop_demand:178.84670420168075});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:35.560223704591415, pop_demand:286.4777259800759});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:30.227255524923642, pop_demand:20.204066028035246});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:38.23867436003304, pop_demand:1006.7348983333329});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:71.97534225078299, pop_demand:231.88916999999992});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:50.99599461208748, pop_demand:24.600527902554795});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:26.081811029411757});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:77.77538462944848, pop_demand:208.65448823529405});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:350.0, pop_demand:0.6974256288429289});
CREATE (n: Building {id: 425, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:425}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 426, name:"building_rye_farmlevel", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:426}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.169104890349659, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:426}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.0242740726669248, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:426}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:426}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
CREATE (n: Building {id: 427, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:427}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.76393164501168, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:427}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 30.728222180007744, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:427}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 103.52786329002336,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:427}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 12.94098291125292,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:427}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 64.7049145562646,level: 3}]->(g);
CREATE (n: Building {id: 428, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:428}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.242740726669247, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:428}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 80.0,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:428}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 2}]->(g);
CREATE (n: Building {id: 429, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:429}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 430, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:430}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 453, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:453}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.435383675798024, level: 2}]->(b);
CREATE (n: Building {id: 454, name:"building_chemical_plantslevel", level:2});
MATCH (g: Goods{code: 22}), (b: Building{id:454}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 118.60815505107817, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:454}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 29.304077525539096, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:454}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.032711381993439, level: 2}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:454}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 128.9219076642154,level: 2}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:454}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 34.379175377124106,level: 2}]->(g);
CREATE (n: Building {id: 455, name:"building_steel_millslevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:455}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 58.60815505107819, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:455}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 48.130845527973754, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:455}) CREATE (b)-[r:Supply{max_supply: 130.0, current_output: 102.59847996348006,level: 2}]->(g);
CREATE (n: Building {id: 456, name:"building_iron_minelevel", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:456}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 39.07210336738546, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:456}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 40.97096290667699, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:456}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 158.1442067347709,level: 4}]->(g);
CREATE (n: Building {id: 457, name:"building_sulfur_minelevel", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:457}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 48.840129209231826, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:457}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 51.21370363334624, level: 5}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:457}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 197.68025841846364,level: 5}]->(g);
CREATE (n: Building {id: 458, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:458}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 14.338209780699318, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:458}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.0485481453338497, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:458}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:458}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 2}]->(g);
CREATE (n: Building {id: 459, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:459}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 11.797396922140512, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:459}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.39131095207634, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:459}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.39131095207634, level: 15}]->(b);
CREATE (n: Building {id: 462, name:"building_furniture_manufacturieslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:462}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.95474911131809, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:462}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 53.13408238784309, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:462}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 16.153846153846153, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:462}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.364111090003872, level: 3}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:462}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 106.5100910164715,level: 3}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:462}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 47.33781822954289,level: 3}]->(g);
CREATE (n: Building {id: 463, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:463}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 25.60685181667312, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:463}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 200.0,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:463}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 5}]->(g);
CREATE (n: Building {id: 464, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:464}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 14.338209780699318, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:464}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.0485481453338497, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:464}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:464}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 2}]->(g);
CREATE (n: Building {id: 465, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:465}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.509287763341119, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:465}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 20.485481453338494, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:465}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 69.01857552668224,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:465}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 8.62732194083528,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:465}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 43.1366097041764,level: 2}]->(g);
CREATE (n: Building {id: 466, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 0}), (b: Building{id:466}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 19.662328203567522, level: 25}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:466}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 25.652184920127237, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:466}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 25.652184920127237, level: 25}]->(b);
CREATE (n: Building {id: 469, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:469}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 62.17691837899012, level: 5}]->(b);
CREATE (n: Building {id: 470, name:"building_construction_sectorlevel", level:2});
CREATE (n: Building {id: 471, name:"building_tooling_workshopslevel", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:471}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 212.53632955137235, level: 8}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:471}) CREATE (g)-[r:Demand{max_demand: 159.99999999999997, current_input: 102.59847996348005, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:471}) CREATE (b)-[r:Supply{max_supply: 639.9999999999999, current_output: 488.5787326621231,level: 8}]->(g);
CREATE (n: Building {id: 472, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:472}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 28.676419561398635, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:472}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.097096290667699, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:472}) CREATE (b)-[r:Supply{max_supply: 139.99999999999997, current_output: 139.99999999999997,level: 4}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:472}) CREATE (b)-[r:Supply{max_supply: 32.0, current_output: 32.0,level: 4}]->(g);
CREATE (n: Building {id: 473, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:473}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.864931281427008, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:473}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.260873968050895, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:473}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.260873968050895, level: 10}]->(b);
CREATE (n: Building {id: 477, name:"building_arms_industrylevel", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:477}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 24.065422763986877, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:477}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 10.76923076923077, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:477}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 17.101456613418158,level: 2}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:477}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 17.101456613418158,level: 2}]->(g);
CREATE (n: Building {id: 478, name:"building_munition_plantslevel", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:478}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 43.92894631521414, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:478}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.459725125708037, level: 1}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:478}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 39.324656407135045,level: 1}]->(g);
CREATE (n: Building {id: 479, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:479}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 87.81899644527236, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:479}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 98.7963710009314,level: 3}]->(g);
CREATE (n: Building {id: 480, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:480}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.242740726669247, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:480}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 1}]->(g);
CREATE (n: Building {id: 481, name:"building_wheat_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:481}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 35.84552445174829, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:481}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.1213703633346235, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:481}) CREATE (b)-[r:Supply{max_supply: 175.0, current_output: 175.0,level: 5}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:481}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 5}]->(g);
CREATE (n: Building {id: 482, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:482}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.242740726669247, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:482}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 80.0,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:482}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 2}]->(g);
CREATE (n: Building {id: 483, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:483}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.864931281427008, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:483}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.260873968050895, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:483}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.260873968050895, level: 10}]->(b);
CREATE (n: Building {id: 484, name:"building_arms_industrylevel", level:6});
MATCH (g: Goods{code: 24}), (b: Building{id:484}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 72.19626829196064, level: 6}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:484}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 32.30769230769231, level: 6}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:484}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 51.30436984025447,level: 6}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:484}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 51.30436984025447,level: 6}]->(g);
CREATE (n: Building {id: 485, name:"building_munition_plantslevel", level:2});
MATCH (g: Goods{code: 25}), (b: Building{id:485}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 87.85789263042828, level: 2}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:485}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 22.919450251416073, level: 2}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:485}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 78.64931281427009,level: 2}]->(g);
CREATE (n: Building {id: 486, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:486}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 20.48548145333849, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:486}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 79.99999999999999,level: 2}]->(g);
CREATE (n: Building {id: 487, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:487}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 21.507314671048977, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:487}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.0728222180007747, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:487}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 104.99999999999999,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:487}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 24.0,level: 3}]->(g);
CREATE (n: Building {id: 488, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:488}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 11.797396922140512, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:488}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.39131095207634, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:488}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.39131095207634, level: 15}]->(b);
CREATE (n: Building {id: 489, name:"building_government_administrationlevel", level:13});
MATCH (g: Goods{code: 14}), (b: Building{id:489}) CREATE (g)-[r:Demand{max_demand: 259.99999999999994, current_input: 161.65998778537428, level: 13}]->(b);
CREATE (n: Building {id: 490, name:"building_construction_sectorlevel", level:3});
CREATE (n: Building {id: 491, name:"building_universitylevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:491}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.435383675798024, level: 4}]->(b);
CREATE (n: Building {id: 492, name:"building_arms_industrylevel", level:5});
MATCH (g: Goods{code: 24}), (b: Building{id:492}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 60.163556909967184, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:492}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 26.923076923076923, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:492}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 42.753641533545384,level: 5}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:492}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 42.753641533545384,level: 5}]->(g);
CREATE (n: Building {id: 493, name:"building_furniture_manufacturieslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:493}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 36.59124851886349, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:493}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 88.55680397973848, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:493}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 26.923076923076923, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:493}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 25.60685181667312, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:493}) CREATE (b)-[r:Supply{max_supply: 225.0, current_output: 177.51681836078586,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:493}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 78.89636371590483,level: 5}]->(g);
CREATE (n: Building {id: 494, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:494}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 53.13408238784309, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:494}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:494}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 65.89341947282121, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:494}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 37.7113607959477,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:494}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 47.13920099493462,level: 3}]->(g);
CREATE (n: Building {id: 495, name:"building_paper_millslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:495}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 106.26816477568616, level: 4}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:495}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 79.07210336738545, level: 4}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:495}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 263.9795255716338,level: 4}]->(g);
CREATE (n: Building {id: 496, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 0}), (b: Building{id:496}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 19.662328203567522, level: 25}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:496}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 25.652184920127237, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:496}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 25.652184920127237, level: 25}]->(b);
CREATE (n: Building {id: 1054, name:"building_textile_millslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:1054}) CREATE (g)-[r:Demand{max_demand: 319.99999999999994, current_input: 234.18399052072627, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1054}) CREATE (b)-[r:Supply{max_supply: 359.99999999999994, current_output: 263.45698933581707,level: 8}]->(g);
CREATE (n: Building {id: 1055, name:"building_arms_industrylevel", level:5});
MATCH (g: Goods{code: 24}), (b: Building{id:1055}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 60.163556909967184, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1055}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 26.923076923076923, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1055}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 42.753641533545384,level: 5}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:1055}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 42.753641533545384,level: 5}]->(g);
CREATE (n: Building {id: 1056, name:"building_coal_minelevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:1056}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 51.21370363334625, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1056}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 200.00000000000003,level: 5}]->(g);
CREATE (n: Building {id: 1057, name:"building_lead_minelevel", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:1057}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 48.840129209231826, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1057}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 51.21370363334624, level: 5}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1057}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 197.68025841846364,level: 5}]->(g);
CREATE (n: Building {id: 1058, name:"building_iron_minelevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:1058}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 29.304077525539096, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1058}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 30.728222180007744, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1058}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 118.60815505107819,level: 3}]->(g);
CREATE (n: Building {id: 1059, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1059}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.509287763341119, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1059}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 20.485481453338494, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1059}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 69.01857552668224,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1059}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 8.62732194083528,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1059}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 43.1366097041764,level: 2}]->(g);
CREATE (n: Building {id: 1060, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:1060}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 11.797396922140512, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1060}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.39131095207634, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1060}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.39131095207634, level: 15}]->(b);
CREATE (n: Building {id: 1066, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1066}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1067, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:1067}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 21.507314671048977, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1067}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 90.0,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1067}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 3}]->(g);
CREATE (n: Building {id: 1068, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1068}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1069, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:1069}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.864931281427008, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1069}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.260873968050895, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1069}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.260873968050895, level: 10}]->(b);
CREATE (n: Building {id: 1070, name:"building_textile_millslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1070}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 58.545997630181574, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1070}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 65.86424733395427,level: 2}]->(g);
CREATE (n: Building {id: 1071, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:1071}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 25.60685181667312, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1071}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 200.0,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1071}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 5}]->(g);
CREATE (n: Building {id: 1072, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1072}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 14.338209780699318, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1072}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1072}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 2}]->(g);
CREATE (n: Building {id: 1073, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:1073}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.864931281427008, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1073}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.260873968050895, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1073}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.260873968050895, level: 10}]->(b);
CREATE (n: Building {id: 1074, name:"building_food_industrylevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:1074}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 145.0928776334112, level: 5}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1074}) CREATE (g)-[r:Demand{max_demand: 125.0, current_input: 8.3555854884029, level: 5}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1074}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 29.711590202785473,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1074}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 118.84636081114189,level: 5}]->(g);
CREATE (n: Building {id: 1075, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1075}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 14.338209780699318, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1075}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1075}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1075}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 2}]->(g);
CREATE (n: Building {id: 1076, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1076}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.76393164501168, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1076}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 30.728222180007744, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1076}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 103.52786329002336,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1076}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 12.94098291125292,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1076}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 64.7049145562646,level: 3}]->(g);
CREATE (n: Building {id: 1077, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:1077}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 11.797396922140512, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1077}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.39131095207634, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1077}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.39131095207634, level: 15}]->(b);
CREATE (n: Building {id: 2904, name:"building_subsistence_farmslevel", level:46});
MATCH (g: Goods{code: 7}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 114.931, current_output: 114.931,level: 46}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 22.9862, current_output: 22.9862,level: 46}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 22.9862, current_output: 22.9862,level: 46}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 22.9862, current_output: 22.9862,level: 46}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 22.9862, current_output: 22.9862,level: 46}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 22.9862, current_output: 22.9862,level: 46}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 32.18068, current_output: 32.18068,level: 46}]->(g);
CREATE (n: Building {id: 3870, name:"building_subsistence_farmslevel", level:85});
MATCH (g: Goods{code: 7}), (b: Building{id:3870}) CREATE (b)-[r:Supply{max_supply: 191.44125, current_output: 191.44125,level: 85}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3870}) CREATE (b)-[r:Supply{max_supply: 38.28825, current_output: 38.28825,level: 85}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3870}) CREATE (b)-[r:Supply{max_supply: 38.28825, current_output: 38.28825,level: 85}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3870}) CREATE (b)-[r:Supply{max_supply: 38.28825, current_output: 38.28825,level: 85}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3870}) CREATE (b)-[r:Supply{max_supply: 38.28825, current_output: 38.28825,level: 85}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3870}) CREATE (b)-[r:Supply{max_supply: 38.28825, current_output: 38.28825,level: 85}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3870}) CREATE (b)-[r:Supply{max_supply: 53.60355, current_output: 53.60355,level: 85}]->(g);
CREATE (n: Building {id: 3871, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3871}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.427840198986924, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3871}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.06455704579694, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3871}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 21.96958897913546,level: 1}]->(g);
CREATE (n: Building {id: 3872, name:"building_subsistence_farmslevel", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:3872}) CREATE (b)-[r:Supply{max_supply: 124.65375, current_output: 124.65375,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3872}) CREATE (b)-[r:Supply{max_supply: 24.93075, current_output: 24.93075,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3872}) CREATE (b)-[r:Supply{max_supply: 24.93075, current_output: 24.93075,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3872}) CREATE (b)-[r:Supply{max_supply: 24.93075, current_output: 24.93075,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3872}) CREATE (b)-[r:Supply{max_supply: 24.93075, current_output: 24.93075,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3872}) CREATE (b)-[r:Supply{max_supply: 24.93075, current_output: 24.93075,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3872}) CREATE (b)-[r:Supply{max_supply: 34.90305, current_output: 34.90305,level: 50}]->(g);
CREATE (n: Building {id: 3873, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3873}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.855680397973847, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3873}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.12911409159388, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3873}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 43.93917795827092,level: 2}]->(g);
CREATE (n: Building {id: 3876, name:"building_subsistence_farmslevel", level:53});
MATCH (g: Goods{code: 7}), (b: Building{id:3876}) CREATE (b)-[r:Supply{max_supply: 132.43242, current_output: 132.43242,level: 53}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3876}) CREATE (b)-[r:Supply{max_supply: 26.48648, current_output: 26.48648,level: 53}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3876}) CREATE (b)-[r:Supply{max_supply: 26.48648, current_output: 26.48648,level: 53}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3876}) CREATE (b)-[r:Supply{max_supply: 26.48648, current_output: 26.48648,level: 53}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3876}) CREATE (b)-[r:Supply{max_supply: 26.48648, current_output: 26.48648,level: 53}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3876}) CREATE (b)-[r:Supply{max_supply: 26.48648, current_output: 26.48648,level: 53}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3876}) CREATE (b)-[r:Supply{max_supply: 37.08107, current_output: 37.08107,level: 53}]->(g);
CREATE (n: Building {id: 3877, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3877}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.427840198986924, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3877}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.06455704579694, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3877}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 21.96958897913546,level: 1}]->(g);
CREATE (n: Building {id: 3879, name:"building_subsistence_farmslevel", level:37});
MATCH (g: Goods{code: 7}), (b: Building{id:3879}) CREATE (b)-[r:Supply{max_supply: 92.19937, current_output: 92.19937,level: 37}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3879}) CREATE (b)-[r:Supply{max_supply: 18.43987, current_output: 18.43987,level: 37}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3879}) CREATE (b)-[r:Supply{max_supply: 18.43987, current_output: 18.43987,level: 37}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3879}) CREATE (b)-[r:Supply{max_supply: 18.43987, current_output: 18.43987,level: 37}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3879}) CREATE (b)-[r:Supply{max_supply: 18.43987, current_output: 18.43987,level: 37}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3879}) CREATE (b)-[r:Supply{max_supply: 18.43987, current_output: 18.43987,level: 37}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3879}) CREATE (b)-[r:Supply{max_supply: 25.81582, current_output: 25.81582,level: 37}]->(g);
CREATE (n: Building {id: 3880, name:"building_subsistence_farmslevel", level:38});
MATCH (g: Goods{code: 7}), (b: Building{id:3880}) CREATE (b)-[r:Supply{max_supply: 94.71595, current_output: 94.71595,level: 38}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3880}) CREATE (b)-[r:Supply{max_supply: 18.94319, current_output: 18.94319,level: 38}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3880}) CREATE (b)-[r:Supply{max_supply: 18.94319, current_output: 18.94319,level: 38}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3880}) CREATE (b)-[r:Supply{max_supply: 18.94319, current_output: 18.94319,level: 38}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3880}) CREATE (b)-[r:Supply{max_supply: 18.94319, current_output: 18.94319,level: 38}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3880}) CREATE (b)-[r:Supply{max_supply: 18.94319, current_output: 18.94319,level: 38}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3880}) CREATE (b)-[r:Supply{max_supply: 26.52046, current_output: 26.52046,level: 38}]->(g);
CREATE (n: Building {id: 3881, name:"building_subsistence_farmslevel", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:3881}) CREATE (b)-[r:Supply{max_supply: 43.9496, current_output: 43.9496,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3881}) CREATE (b)-[r:Supply{max_supply: 8.78992, current_output: 8.78992,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3881}) CREATE (b)-[r:Supply{max_supply: 8.78992, current_output: 8.78992,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3881}) CREATE (b)-[r:Supply{max_supply: 8.78992, current_output: 8.78992,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3881}) CREATE (b)-[r:Supply{max_supply: 8.78992, current_output: 8.78992,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3881}) CREATE (b)-[r:Supply{max_supply: 8.78992, current_output: 8.78992,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3881}) CREATE (b)-[r:Supply{max_supply: 12.30588, current_output: 12.30588,level: 32}]->(g);
CREATE (n: Building {id: 3882, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3882}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.283520596960772, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3882}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.1936711373908193, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3882}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 65.90876693740636,level: 3}]->(g);
CREATE (n: Building {id: 3884, name:"building_subsistence_farmslevel", level:72});
MATCH (g: Goods{code: 7}), (b: Building{id:3884}) CREATE (b)-[r:Supply{max_supply: 101.3778, current_output: 101.3778,level: 72}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3884}) CREATE (b)-[r:Supply{max_supply: 20.27556, current_output: 20.27556,level: 72}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3884}) CREATE (b)-[r:Supply{max_supply: 20.27556, current_output: 20.27556,level: 72}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3884}) CREATE (b)-[r:Supply{max_supply: 20.27556, current_output: 20.27556,level: 72}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3884}) CREATE (b)-[r:Supply{max_supply: 20.27556, current_output: 20.27556,level: 72}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3884}) CREATE (b)-[r:Supply{max_supply: 20.27556, current_output: 20.27556,level: 72}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3884}) CREATE (b)-[r:Supply{max_supply: 28.38578, current_output: 28.38578,level: 72}]->(g);
CREATE (n: Building {id: 3885, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3885}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.427840198986924, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3885}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.06455704579694, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3885}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 21.96958897913546,level: 1}]->(g);
CREATE (n: Building {id: 3886, name:"building_subsistence_farmslevel", level:36});
MATCH (g: Goods{code: 7}), (b: Building{id:3886}) CREATE (b)-[r:Supply{max_supply: 71.5068, current_output: 71.5068,level: 36}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3886}) CREATE (b)-[r:Supply{max_supply: 14.30136, current_output: 14.30136,level: 36}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3886}) CREATE (b)-[r:Supply{max_supply: 14.30136, current_output: 14.30136,level: 36}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3886}) CREATE (b)-[r:Supply{max_supply: 14.30136, current_output: 14.30136,level: 36}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3886}) CREATE (b)-[r:Supply{max_supply: 14.30136, current_output: 14.30136,level: 36}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3886}) CREATE (b)-[r:Supply{max_supply: 14.30136, current_output: 14.30136,level: 36}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3886}) CREATE (b)-[r:Supply{max_supply: 20.0219, current_output: 20.0219,level: 36}]->(g);
CREATE (n: Building {id: 3887, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3887}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.855680397973847, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3887}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.12911409159388, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3887}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 43.93917795827092,level: 2}]->(g);
CREATE (n: Building {id: 3888, name:"building_subsistence_farmslevel", level:98});
MATCH (g: Goods{code: 7}), (b: Building{id:3888}) CREATE (b)-[r:Supply{max_supply: 244.18905, current_output: 244.18905,level: 98}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3888}) CREATE (b)-[r:Supply{max_supply: 48.83781, current_output: 48.83781,level: 98}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3888}) CREATE (b)-[r:Supply{max_supply: 48.83781, current_output: 48.83781,level: 98}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3888}) CREATE (b)-[r:Supply{max_supply: 48.83781, current_output: 48.83781,level: 98}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3888}) CREATE (b)-[r:Supply{max_supply: 48.83781, current_output: 48.83781,level: 98}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3888}) CREATE (b)-[r:Supply{max_supply: 48.83781, current_output: 48.83781,level: 98}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3888}) CREATE (b)-[r:Supply{max_supply: 68.37293, current_output: 68.37293,level: 98}]->(g);
CREATE (n: Building {id: 3889, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3889}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.711360795947694, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3889}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.25822818318776, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3889}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 87.87835591654184,level: 4}]->(g);
CREATE (n: Building {id: 3890, name:"building_subsistence_farmslevel", level:45});
MATCH (g: Goods{code: 7}), (b: Building{id:3890}) CREATE (b)-[r:Supply{max_supply: 94.88587, current_output: 94.88587,level: 45}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3890}) CREATE (b)-[r:Supply{max_supply: 18.97717, current_output: 18.97717,level: 45}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3890}) CREATE (b)-[r:Supply{max_supply: 18.97717, current_output: 18.97717,level: 45}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3890}) CREATE (b)-[r:Supply{max_supply: 18.97717, current_output: 18.97717,level: 45}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3890}) CREATE (b)-[r:Supply{max_supply: 18.97717, current_output: 18.97717,level: 45}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3890}) CREATE (b)-[r:Supply{max_supply: 18.97717, current_output: 18.97717,level: 45}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3890}) CREATE (b)-[r:Supply{max_supply: 26.56804, current_output: 26.56804,level: 45}]->(g);
CREATE (n: Building {id: 3891, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3891}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 30.994881392908468, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3891}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 7.451899320578579, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3891}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 153.78712285394818,level: 7}]->(g);
CREATE (n: Building {id: 3915, name:"building_subsistence_farmslevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3915}) CREATE (b)-[r:Supply{max_supply: 12.5, current_output: 12.5,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3915}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3915}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3915}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3915}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3915}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3915}) CREATE (b)-[r:Supply{max_supply: 3.5, current_output: 3.5,level: 5}]->(g);
CREATE (n: Building {id: 3968, name:"building_trade_centerlevel", level:19});
CREATE (n: Building {id: 3969, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3969}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.427840198986924, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3969}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.06455704579694, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3969}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 21.96958897913546,level: 1}]->(g);
CREATE (n: Building {id: 4031, name:"building_trade_centerlevel", level:16});
CREATE (n: Building {id: 4032, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4032}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.427840198986924, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:4032}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.06455704579694, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:4032}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 21.96958897913546,level: 1}]->(g);
CREATE (n: Building {id: 4057, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4607, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4608, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4609, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4610, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4611, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4612, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4614, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4615, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4616, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4617, name:"building_conscription_centerlevel", level:6});
