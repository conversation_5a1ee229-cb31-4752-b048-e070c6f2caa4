CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:64.22697732924055, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:74.93147355078064, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:71.87652234430054, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:22.051444677435367, pop_demand:5000.628950106441});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:21.974079098289664, pop_demand:383.4661700843082});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:18.781797071147125, pop_demand:679.1854805338364});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:26.34386469636432, pop_demand:415.6965708961683});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:45.72397583010389, pop_demand:343.26834560744123});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:28.211561711413815, pop_demand:1222.1850406370784});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:29.276221948347413, pop_demand:1003.0188975963235});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:43.32644042120042, pop_demand:51.84976821306489});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:16.091313253077267, pop_demand:512.3956779343366});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:69.08964893677442, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:33.07692307692307, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:38.43409725333774, pop_demand:35.83023404666434});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:46.84831055353129, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:48.537249336468605, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:44.664437364277674, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:57.729538010735844, pop_demand:81.1582212556401});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:54.05156877486541, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:114.77948743858013, pop_demand:245.55938479999105});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:39.65341380079986, pop_demand:267.8824511537757});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:37.95433664736363, pop_demand:307.6690207592952});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:34.58267201924185, pop_demand:2046.7990883333332});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:64.25025629368145, pop_demand:466.71482219612756});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:87.5, pop_demand:44.57274780387275});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:43.72895896449816, pop_demand:65.1534650133238});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:85.064159409215, pop_demand:361.15858607577735});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:87.90501530594456, pop_demand:272.75347332423325});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:24.99999999999999, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:297.73410619945946, pop_demand:2.3441803098495764});
CREATE (n: Building {id: 389, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:389}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 40.84022637463872, level: 10}]->(b);
CREATE (n: Building {id: 390, name:"building_arts_academylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:390}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0420113187319364, level: 2}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:390}) CREATE (b)-[r:Supply{max_supply: 2.0, current_output: 0.8168045274927745,level: 2}]->(g);
CREATE (n: Building {id: 391, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:391}) CREATE (g)-[r:Demand{max_demand: 47.339, current_input: 51.52339686430286, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:391}) CREATE (g)-[r:Demand{max_demand: 142.017, current_input: 82.05474196492209, level: 2}]->(b);
CREATE (n: Building {id: 392, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:392}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.084022637463873, level: 2}]->(b);
CREATE (n: Building {id: 393, name:"building_furniture_manufacturieslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:393}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 54.41960842466345, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:393}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 57.7781124547921, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:393}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 42.305249942498435, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:393}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 13.414219164707118, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:393}) CREATE (b)-[r:Supply{max_supply: 225.0, current_output: 166.52558756172232,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:393}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 74.01137224965436,level: 5}]->(g);
CREATE (n: Building {id: 394, name:"building_paper_millslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:394}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 86.66716868218815, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:394}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 115.55622490958419,level: 5}]->(g);
CREATE (n: Building {id: 395, name:"building_wheat_farmlevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:395}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 109.0,level: 10}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:395}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 98.1,level: 10}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:395}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 65.4,level: 10}]->(g);
CREATE (n: Building {id: 396, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:396}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 91.8,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:396}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.3,level: 3}]->(g);
CREATE (n: Building {id: 397, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:397}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 18.275941536311972, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:397}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 18.275941536311972, level: 20}]->(b);
CREATE (n: Building {id: 398, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:398}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 40.84022637463872, level: 10}]->(b);
CREATE (n: Building {id: 399, name:"building_arms_industrylevel", level:5});
MATCH (g: Goods{code: 24}), (b: Building{id:399}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 78.07889427783589, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:399}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 42.305249942498435, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:399}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 61.008522811062285,level: 5}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:399}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 61.008522811062285,level: 5}]->(g);
CREATE (n: Building {id: 400, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:400}) CREATE (g)-[r:Demand{max_demand: 47.339, current_input: 51.52339686430286, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:400}) CREATE (g)-[r:Demand{max_demand: 142.017, current_input: 82.05474196492209, level: 2}]->(b);
CREATE (n: Building {id: 401, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:401}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.084022637463873, level: 2}]->(b);
CREATE (n: Building {id: 402, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:402}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 138.11792344068945, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:402}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 38.982404602230375, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:402}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 37.591832025798375,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:402}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 150.3673281031935,level: 4}]->(g);
CREATE (n: Building {id: 403, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:403}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 34.66686747287526, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:403}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.69539983132875, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:403}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 78.05766713553275,level: 3}]->(g);
CREATE (n: Building {id: 404, name:"building_iron_minelevel", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:404}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 31.596383361155343, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:404}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 26.828438329414237, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:404}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 116.84964338113917,level: 5}]->(g);
CREATE (n: Building {id: 405, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:405}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.048531498824271, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:405}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 64.38825199059417,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:405}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 16.097062997648543,level: 3}]->(g);
CREATE (n: Building {id: 406, name:"building_rye_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:406}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 74.2,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:406}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 111.3,level: 7}]->(g);
CREATE (n: Building {id: 407, name:"building_barrackslevel", level:30});
MATCH (g: Goods{code: 1}), (b: Building{id:407}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 27.413912304467956, level: 30}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:407}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 27.413912304467956, level: 30}]->(b);
CREATE (n: Building {id: 408, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:408}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.365687665882847, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:408}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 26.828438329414233,level: 2}]->(g);
CREATE (n: Building {id: 409, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:409}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 130.60706021919228, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:409}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 135.0,level: 3}]->(g);
CREATE (n: Building {id: 410, name:"building_food_industrylevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:410}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 103.58844258051708, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:410}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 29.23680345167278, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:410}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 28.193874019348787,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:410}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 112.77549607739515,level: 3}]->(g);
CREATE (n: Building {id: 411, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:411}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.048531498824271, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:411}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 64.38825199059417,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:411}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 16.097062997648543,level: 3}]->(g);
CREATE (n: Building {id: 412, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:412}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 41.2,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:412}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 37.08,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:412}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 24.72,level: 4}]->(g);
CREATE (n: Building {id: 413, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:413}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.137970768155986, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:413}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.137970768155986, level: 10}]->(b);
CREATE (n: Building {id: 414, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:414}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.365687665882847, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:414}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 26.828438329414233,level: 2}]->(g);
CREATE (n: Building {id: 415, name:"building_iron_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:415}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.048531498824271, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:415}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 32.194125995297085,level: 3}]->(g);
CREATE (n: Building {id: 416, name:"building_lead_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:416}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.365687665882847, level: 2}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:416}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 21.462750663531388,level: 2}]->(g);
CREATE (n: Building {id: 417, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:417}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:417}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 1}]->(g);
CREATE (n: Building {id: 418, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:418}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.6,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:418}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.1,level: 2}]->(g);
CREATE (n: Building {id: 419, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:419}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.137970768155986, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:419}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.137970768155986, level: 10}]->(b);
CREATE (n: Building {id: 420, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:420}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.365687665882847, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:420}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 21.462750663531388,level: 2}]->(g);
CREATE (n: Building {id: 421, name:"building_gold_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:421}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.365687665882847, level: 2}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:421}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 10.731375331765694,level: 2}]->(g);
CREATE (n: Building {id: 422, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:422}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:422}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 423, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:423}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:423}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 1}]->(g);
CREATE (n: Building {id: 424, name:"building_barrackslevel", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:424}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.8275941536311973, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:424}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.8275941536311973, level: 2}]->(b);
CREATE (n: Building {id: 600, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:600}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 12.252067912391617, level: 3}]->(b);
CREATE (n: Building {id: 601, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:601}) CREATE (g)-[r:Demand{max_demand: 71.0085, current_input: 77.28509529645429, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:601}) CREATE (g)-[r:Demand{max_demand: 213.0255, current_input: 123.08211294738315, level: 3}]->(b);
CREATE (n: Building {id: 602, name:"building_textile_millslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:602}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 217.6784336986538, level: 5}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:602}) CREATE (b)-[r:Supply{max_supply: 225.0, current_output: 225.0,level: 5}]->(g);
CREATE (n: Building {id: 603, name:"building_arms_industrylevel", level:10});
MATCH (g: Goods{code: 24}), (b: Building{id:603}) CREATE (g)-[r:Demand{max_demand: 199.99999999999997, current_input: 156.15778855567174, level: 10}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:603}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 84.61049988499686, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:603}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 122.01704562212457,level: 10}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:603}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 122.01704562212457,level: 10}]->(g);
CREATE (n: Building {id: 604, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:604}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:604}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 24.48,level: 3}]->(g);
CREATE (n: Building {id: 605, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 1}), (b: Building{id:605}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.706956152233978, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:605}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.706956152233978, level: 15}]->(b);
CREATE (n: Building {id: 708, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:708}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 12.252067912391617, level: 3}]->(b);
CREATE (n: Building {id: 709, name:"building_paper_millslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:709}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 86.66716868218815, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:709}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 115.55622490958419,level: 5}]->(g);
CREATE (n: Building {id: 710, name:"building_glassworkslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:710}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 86.66716868218815, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:710}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:710}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 14.444528113698023,level: 5}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:710}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 36.11132028424506,level: 5}]->(g);
CREATE (n: Building {id: 711, name:"building_textile_millslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:711}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 261.21412043838455, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:711}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 104.0, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:711}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 200.0,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:711}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 159.99999999999997,level: 8}]->(g);
CREATE (n: Building {id: 712, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:712}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.137970768155986, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:712}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.137970768155986, level: 10}]->(b);
CREATE (n: Building {id: 713, name:"building_fishing_wharflevel", level:4});
MATCH (g: Goods{code: 8}), (b: Building{id:713}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 103.0,level: 4}]->(g);
CREATE (n: Building {id: 714, name:"building_silk_plantationlevel", level:5});
MATCH (g: Goods{code: 20}), (b: Building{id:714}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 104.0,level: 5}]->(g);
CREATE (n: Building {id: 715, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:715}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:715}) CREATE (b)-[r:Supply{max_supply: 16.0, current_output: 16.16,level: 2}]->(g);
CREATE (n: Building {id: 716, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:716}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 11.977519318381201, level: 3}]->(b);
CREATE (n: Building {id: 873, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:873}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.6,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:873}) CREATE (b)-[r:Supply{max_supply: 27.0, current_output: 27.54,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:873}) CREATE (b)-[r:Supply{max_supply: 18.0, current_output: 18.36,level: 3}]->(g);
CREATE (n: Building {id: 874, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:874}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:874}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 875, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:875}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.2,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:875}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.2,level: 2}]->(g);
CREATE (n: Building {id: 876, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:876}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:876}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
CREATE (n: Building {id: 877, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:877}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 878, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:878}) CREATE (b)-[r:Supply{max_supply: 15.000000000000002, current_output: 17.25,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:878}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 9.2,level: 1}]->(g);
CREATE (n: Building {id: 879, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:879}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:879}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
CREATE (n: Building {id: 880, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:880}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.9925064394604006, level: 1}]->(b);
CREATE (n: Building {id: 881, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:881}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 12.252067912391617, level: 3}]->(b);
CREATE (n: Building {id: 882, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:882}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 34.8,level: 2}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:882}) CREATE (b)-[r:Supply{max_supply: 16.0, current_output: 18.56,level: 2}]->(g);
CREATE (n: Building {id: 883, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:883}) CREATE (b)-[r:Supply{max_supply: 30.000000000000004, current_output: 34.5,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:883}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.75,level: 1}]->(g);
CREATE (n: Building {id: 884, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:884}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 91.8,level: 3}]->(g);
CREATE (n: Building {id: 885, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:885}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 886, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:886}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.9925064394604006, level: 1}]->(b);
CREATE (n: Building {id: 887, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:887}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.137970768155986, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:887}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.137970768155986, level: 10}]->(b);
CREATE (n: Building {id: 888, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:888}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 8.168045274927746, level: 2}]->(b);
CREATE (n: Building {id: 889, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:889}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:889}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.0,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:889}) CREATE (b)-[r:Supply{max_supply: 6.0, current_output: 6.0,level: 1}]->(g);
CREATE (n: Building {id: 890, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:890}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:890}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 891, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:891}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:891}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
CREATE (n: Building {id: 892, name:"building_tooling_workshopslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:892}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 52.000301209312894, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:892}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 46.84733656670153, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:892}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 122.2713060593652,level: 3}]->(g);
CREATE (n: Building {id: 893, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:893}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 43.53568673973076, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:893}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 46.22248996383368, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:893}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 23.666716868218813,level: 2}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:893}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 31.55562249095842,level: 2}]->(g);
CREATE (n: Building {id: 894, name:"building_fishing_wharflevel", level:4});
MATCH (g: Goods{code: 8}), (b: Building{id:894}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 103.0,level: 4}]->(g);
CREATE (n: Building {id: 895, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:895}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 19.297060234955506, level: 10}]->(b);
CREATE (n: Building {id: 896, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:896}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.985012878920801, level: 2}]->(b);
CREATE (n: Building {id: 900, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:900}) CREATE (g)-[r:Demand{max_demand: 4.544, current_input: 4.38429208538189, level: 5}]->(b);
CREATE (n: Building {id: 901, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:901}) CREATE (g)-[r:Demand{max_demand: 4.543, current_input: 3.6275913508937205, level: 2}]->(b);
CREATE (n: Building {id: 1078, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:1078}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 20.420113187319362, level: 5}]->(b);
CREATE (n: Building {id: 1079, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1079}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.2,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1079}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
CREATE (n: Building {id: 1080, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1080}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1080}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 1081, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1081}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1081}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
CREATE (n: Building {id: 1085, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:1085}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 12.252067912391617, level: 3}]->(b);
CREATE (n: Building {id: 1086, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1086}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1086}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 24.48,level: 3}]->(g);
CREATE (n: Building {id: 1087, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1087}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.6,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1087}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.1,level: 2}]->(g);
CREATE (n: Building {id: 1088, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1088}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1088}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
CREATE (n: Building {id: 1089, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:1089}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 20.420113187319362, level: 5}]->(b);
CREATE (n: Building {id: 1090, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1090}) CREATE (g)-[r:Demand{max_demand: 23.6695, current_input: 25.76169843215143, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1090}) CREATE (g)-[r:Demand{max_demand: 71.0085, current_input: 41.02737098246104, level: 1}]->(b);
CREATE (n: Building {id: 1091, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1091}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.365687665882847, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1091}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 21.462750663531388,level: 2}]->(g);
CREATE (n: Building {id: 1092, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1092}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 69.05896172034473, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1092}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 19.491202301115187, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1092}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 18.79591601289919,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1092}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 75.18366405159676,level: 2}]->(g);
CREATE (n: Building {id: 1093, name:"building_wheat_farmlevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:1093}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 94.5,level: 6}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1093}) CREATE (b)-[r:Supply{max_supply: 48.0, current_output: 50.4,level: 6}]->(g);
CREATE (n: Building {id: 1094, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1094}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 91.8,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1094}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.3,level: 3}]->(g);
CREATE (n: Building {id: 1095, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 1}), (b: Building{id:1095}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.706956152233978, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1095}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.706956152233978, level: 15}]->(b);
CREATE (n: Building {id: 1096, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1096}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1096}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 24.48,level: 3}]->(g);
CREATE (n: Building {id: 1097, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1097}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.6,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1097}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.1,level: 2}]->(g);
CREATE (n: Building {id: 1098, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:1098}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.137970768155986, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1098}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.137970768155986, level: 10}]->(b);
CREATE (n: Building {id: 1099, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1099}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 69.05896172034473, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1099}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 19.491202301115187, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1099}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 18.79591601289919,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1099}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 75.18366405159676,level: 2}]->(g);
CREATE (n: Building {id: 1100, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1100}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1100}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 24.48,level: 3}]->(g);
CREATE (n: Building {id: 1101, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1101}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 91.8,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1101}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.3,level: 3}]->(g);
CREATE (n: Building {id: 1102, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 1}), (b: Building{id:1102}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.706956152233978, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1102}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.706956152233978, level: 15}]->(b);
CREATE (n: Building {id: 1103, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1103}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1103}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 24.48,level: 3}]->(g);
CREATE (n: Building {id: 1104, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1104}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 91.8,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1104}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.3,level: 3}]->(g);
CREATE (n: Building {id: 1105, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1105}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1105}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
CREATE (n: Building {id: 1106, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1106}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1106}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 24.48,level: 3}]->(g);
CREATE (n: Building {id: 1107, name:"building_livestock_ranchlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:1107}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 123.6,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1107}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.6,level: 4}]->(g);
CREATE (n: Building {id: 1108, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1108}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1108}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
CREATE (n: Building {id: 1109, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:1109}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 12.252067912391617, level: 3}]->(b);
CREATE (n: Building {id: 1110, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1110}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 21.76784336986538, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1110}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 23.11124498191684, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1110}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.922099976999373, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1110}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.365687665882847, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1110}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 66.61023502468892,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1110}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 29.604548899861744,level: 2}]->(g);
CREATE (n: Building {id: 1111, name:"building_iron_minelevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1111}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 10.731375331765694, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1111}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 42.925501327062776,level: 4}]->(g);
CREATE (n: Building {id: 1112, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1112}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.2,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1112}) CREATE (b)-[r:Supply{max_supply: 18.0, current_output: 18.18,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1112}) CREATE (b)-[r:Supply{max_supply: 12.0, current_output: 12.12,level: 2}]->(g);
CREATE (n: Building {id: 1113, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1113}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1113}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
CREATE (n: Building {id: 1114, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1114}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1114}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.0,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1114}) CREATE (b)-[r:Supply{max_supply: 6.0, current_output: 6.0,level: 1}]->(g);
CREATE (n: Building {id: 1115, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1115}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.6,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1115}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.6,level: 3}]->(g);
CREATE (n: Building {id: 1116, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1116}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1116}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
CREATE (n: Building {id: 1117, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1117}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1117}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
CREATE (n: Building {id: 1118, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1118}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1118}) CREATE (b)-[r:Supply{max_supply: 16.0, current_output: 16.16,level: 2}]->(g);
CREATE (n: Building {id: 1119, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:1119}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 52.0,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1119}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 52.0,level: 5}]->(g);
CREATE (n: Building {id: 1120, name:"building_iron_minelevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1120}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 10.731375331765694, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1120}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 42.925501327062776,level: 4}]->(g);
CREATE (n: Building {id: 1121, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1121}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 8.168045274927746, level: 2}]->(b);
CREATE (n: Building {id: 1122, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1122}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.88392168493269, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1122}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 17.33343373643763, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1122}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 35.50007530232822,level: 1}]->(g);
CREATE (n: Building {id: 1123, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1123}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.6,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1123}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.6,level: 3}]->(g);
CREATE (n: Building {id: 1124, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1124}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1124}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 1}]->(g);
CREATE (n: Building {id: 1125, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1125}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1125}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 1126, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1126}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1126}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.568985384077993, level: 5}]->(b);
CREATE (n: Building {id: 1127, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1127}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1127}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 1}]->(g);
CREATE (n: Building {id: 1128, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1128}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.6,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1128}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.1,level: 2}]->(g);
CREATE (n: Building {id: 1129, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1129}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.2,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1129}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.2,level: 2}]->(g);
CREATE (n: Building {id: 2902, name:"building_subsistence_farmslevel", level:53});
MATCH (g: Goods{code: 7}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 132.2085, current_output: 132.2085,level: 53}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 26.4417, current_output: 26.4417,level: 53}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 26.4417, current_output: 26.4417,level: 53}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 26.4417, current_output: 26.4417,level: 53}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 26.4417, current_output: 26.4417,level: 53}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 26.4417, current_output: 26.4417,level: 53}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 37.01838, current_output: 37.01838,level: 53}]->(g);
CREATE (n: Building {id: 2903, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2903}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 17.33343373643763, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2903}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 12.36683155519613, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2903}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 118.80106116653505,level: 6}]->(g);
CREATE (n: Building {id: 3022, name:"building_subsistence_farmslevel", level:48});
MATCH (g: Goods{code: 7}), (b: Building{id:3022}) CREATE (b)-[r:Supply{max_supply: 119.6112, current_output: 119.6112,level: 48}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3022}) CREATE (b)-[r:Supply{max_supply: 23.92224, current_output: 23.92224,level: 48}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3022}) CREATE (b)-[r:Supply{max_supply: 23.92224, current_output: 23.92224,level: 48}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3022}) CREATE (b)-[r:Supply{max_supply: 23.92224, current_output: 23.92224,level: 48}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3022}) CREATE (b)-[r:Supply{max_supply: 23.92224, current_output: 23.92224,level: 48}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3022}) CREATE (b)-[r:Supply{max_supply: 23.92224, current_output: 23.92224,level: 48}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3022}) CREATE (b)-[r:Supply{max_supply: 33.49113, current_output: 33.49113,level: 48}]->(g);
CREATE (n: Building {id: 3024, name:"building_subsistence_farmslevel", level:48});
MATCH (g: Goods{code: 7}), (b: Building{id:3024}) CREATE (b)-[r:Supply{max_supply: 119.6868, current_output: 119.6868,level: 48}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3024}) CREATE (b)-[r:Supply{max_supply: 23.93736, current_output: 23.93736,level: 48}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3024}) CREATE (b)-[r:Supply{max_supply: 23.93736, current_output: 23.93736,level: 48}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3024}) CREATE (b)-[r:Supply{max_supply: 23.93736, current_output: 23.93736,level: 48}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3024}) CREATE (b)-[r:Supply{max_supply: 23.93736, current_output: 23.93736,level: 48}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3024}) CREATE (b)-[r:Supply{max_supply: 23.93736, current_output: 23.93736,level: 48}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3024}) CREATE (b)-[r:Supply{max_supply: 33.5123, current_output: 33.5123,level: 48}]->(g);
CREATE (n: Building {id: 3025, name:"building_subsistence_farmslevel", level:24});
MATCH (g: Goods{code: 7}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 40.6068, current_output: 40.6068,level: 24}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 8.12136, current_output: 8.12136,level: 24}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 8.12136, current_output: 8.12136,level: 24}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 8.12136, current_output: 8.12136,level: 24}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 8.12136, current_output: 8.12136,level: 24}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 8.12136, current_output: 8.12136,level: 24}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 11.3699, current_output: 11.3699,level: 24}]->(g);
CREATE (n: Building {id: 3638, name:"building_subsistence_farmslevel", level:2});
CREATE (n: Building {id: 3839, name:"building_subsistence_farmslevel", level:105});
MATCH (g: Goods{code: 7}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 261.88837, current_output: 261.88837,level: 105}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 52.37767, current_output: 52.37767,level: 105}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 52.37767, current_output: 52.37767,level: 105}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 52.37767, current_output: 52.37767,level: 105}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 52.37767, current_output: 52.37767,level: 105}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 52.37767, current_output: 52.37767,level: 105}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 73.32874, current_output: 73.32874,level: 105}]->(g);
CREATE (n: Building {id: 3840, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3840}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.888905622739605, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3840}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0611385925326884, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3840}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.800176861089174,level: 1}]->(g);
CREATE (n: Building {id: 3842, name:"building_subsistence_farmslevel", level:125});
MATCH (g: Goods{code: 7}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 312.23437, current_output: 312.23437,level: 125}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 62.44687, current_output: 62.44687,level: 125}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 62.44687, current_output: 62.44687,level: 125}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 62.44687, current_output: 62.44687,level: 125}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 62.44687, current_output: 62.44687,level: 125}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 62.44687, current_output: 62.44687,level: 125}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 87.42562, current_output: 87.42562,level: 125}]->(g);
CREATE (n: Building {id: 3843, name:"building_subsistence_farmslevel", level:98});
MATCH (g: Goods{code: 7}), (b: Building{id:3843}) CREATE (b)-[r:Supply{max_supply: 244.58595, current_output: 244.58595,level: 98}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3843}) CREATE (b)-[r:Supply{max_supply: 48.91719, current_output: 48.91719,level: 98}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3843}) CREATE (b)-[r:Supply{max_supply: 48.91719, current_output: 48.91719,level: 98}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3843}) CREATE (b)-[r:Supply{max_supply: 48.91719, current_output: 48.91719,level: 98}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3843}) CREATE (b)-[r:Supply{max_supply: 48.91719, current_output: 48.91719,level: 98}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3843}) CREATE (b)-[r:Supply{max_supply: 48.91719, current_output: 48.91719,level: 98}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3843}) CREATE (b)-[r:Supply{max_supply: 68.48406, current_output: 68.48406,level: 98}]->(g);
CREATE (n: Building {id: 3844, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3844}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.888905622739605, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3844}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0611385925326884, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3844}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.800176861089174,level: 1}]->(g);
CREATE (n: Building {id: 3845, name:"building_subsistence_farmslevel", level:69});
MATCH (g: Goods{code: 7}), (b: Building{id:3845}) CREATE (b)-[r:Supply{max_supply: 169.49677, current_output: 169.49677,level: 69}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3845}) CREATE (b)-[r:Supply{max_supply: 33.89935, current_output: 33.89935,level: 69}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3845}) CREATE (b)-[r:Supply{max_supply: 33.89935, current_output: 33.89935,level: 69}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3845}) CREATE (b)-[r:Supply{max_supply: 33.89935, current_output: 33.89935,level: 69}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3845}) CREATE (b)-[r:Supply{max_supply: 33.89935, current_output: 33.89935,level: 69}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3845}) CREATE (b)-[r:Supply{max_supply: 33.89935, current_output: 33.89935,level: 69}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3845}) CREATE (b)-[r:Supply{max_supply: 47.45909, current_output: 47.45909,level: 69}]->(g);
CREATE (n: Building {id: 3846, name:"building_subsistence_farmslevel", level:19});
MATCH (g: Goods{code: 7}), (b: Building{id:3846}) CREATE (b)-[r:Supply{max_supply: 41.409547826086964, current_output: 47.62098,level: 19}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3846}) CREATE (b)-[r:Supply{max_supply: 8.281904347826089, current_output: 9.52419,level: 19}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3846}) CREATE (b)-[r:Supply{max_supply: 8.281904347826089, current_output: 9.52419,level: 19}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3846}) CREATE (b)-[r:Supply{max_supply: 8.281904347826089, current_output: 9.52419,level: 19}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3846}) CREATE (b)-[r:Supply{max_supply: 8.281904347826089, current_output: 9.52419,level: 19}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3846}) CREATE (b)-[r:Supply{max_supply: 8.281904347826089, current_output: 9.52419,level: 19}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3846}) CREATE (b)-[r:Supply{max_supply: 11.59466956521739, current_output: 13.33387,level: 19}]->(g);
CREATE (n: Building {id: 3847, name:"building_subsistence_farmslevel", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 78.6944, current_output: 90.49856,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 15.738878260869567, current_output: 18.09971,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 15.738878260869567, current_output: 18.09971,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 15.738878260869567, current_output: 18.09971,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 15.738878260869567, current_output: 18.09971,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 15.738878260869567, current_output: 18.09971,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 22.034426086956525, current_output: 25.33959,level: 32}]->(g);
CREATE (n: Building {id: 3848, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3848}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.888905622739605, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3848}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0611385925326884, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3848}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.800176861089174,level: 1}]->(g);
CREATE (n: Building {id: 3849, name:"building_subsistence_farmslevel", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3849}) CREATE (b)-[r:Supply{max_supply: 39.7998, current_output: 39.7998,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3849}) CREATE (b)-[r:Supply{max_supply: 7.95996, current_output: 7.95996,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3849}) CREATE (b)-[r:Supply{max_supply: 7.95996, current_output: 7.95996,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3849}) CREATE (b)-[r:Supply{max_supply: 7.95996, current_output: 7.95996,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3849}) CREATE (b)-[r:Supply{max_supply: 7.95996, current_output: 7.95996,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3849}) CREATE (b)-[r:Supply{max_supply: 7.95996, current_output: 7.95996,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3849}) CREATE (b)-[r:Supply{max_supply: 11.14394, current_output: 11.14394,level: 18}]->(g);
CREATE (n: Building {id: 3850, name:"building_subsistence_farmslevel", level:74});
MATCH (g: Goods{code: 7}), (b: Building{id:3850}) CREATE (b)-[r:Supply{max_supply: 184.7558, current_output: 184.7558,level: 74}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3850}) CREATE (b)-[r:Supply{max_supply: 36.95116, current_output: 36.95116,level: 74}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3850}) CREATE (b)-[r:Supply{max_supply: 36.95116, current_output: 36.95116,level: 74}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3850}) CREATE (b)-[r:Supply{max_supply: 36.95116, current_output: 36.95116,level: 74}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3850}) CREATE (b)-[r:Supply{max_supply: 36.95116, current_output: 36.95116,level: 74}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3850}) CREATE (b)-[r:Supply{max_supply: 36.95116, current_output: 36.95116,level: 74}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3850}) CREATE (b)-[r:Supply{max_supply: 51.73162, current_output: 51.73162,level: 74}]->(g);
CREATE (n: Building {id: 3851, name:"building_subsistence_farmslevel", level:114});
MATCH (g: Goods{code: 7}), (b: Building{id:3851}) CREATE (b)-[r:Supply{max_supply: 284.57535, current_output: 284.57535,level: 114}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3851}) CREATE (b)-[r:Supply{max_supply: 56.91507, current_output: 56.91507,level: 114}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3851}) CREATE (b)-[r:Supply{max_supply: 56.91507, current_output: 56.91507,level: 114}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3851}) CREATE (b)-[r:Supply{max_supply: 56.91507, current_output: 56.91507,level: 114}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3851}) CREATE (b)-[r:Supply{max_supply: 56.91507, current_output: 56.91507,level: 114}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3851}) CREATE (b)-[r:Supply{max_supply: 56.91507, current_output: 56.91507,level: 114}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3851}) CREATE (b)-[r:Supply{max_supply: 79.68109, current_output: 79.68109,level: 114}]->(g);
CREATE (n: Building {id: 3852, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3852}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.888905622739605, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3852}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0611385925326884, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3852}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.800176861089174,level: 1}]->(g);
CREATE (n: Building {id: 3853, name:"building_subsistence_farmslevel", level:25});
MATCH (g: Goods{code: 7}), (b: Building{id:3853}) CREATE (b)-[r:Supply{max_supply: 60.53061739130435, current_output: 69.61021,level: 25}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3853}) CREATE (b)-[r:Supply{max_supply: 12.106121739130437, current_output: 13.92204,level: 25}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3853}) CREATE (b)-[r:Supply{max_supply: 12.106121739130437, current_output: 13.92204,level: 25}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3853}) CREATE (b)-[r:Supply{max_supply: 12.106121739130437, current_output: 13.92204,level: 25}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3853}) CREATE (b)-[r:Supply{max_supply: 12.106121739130437, current_output: 13.92204,level: 25}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3853}) CREATE (b)-[r:Supply{max_supply: 12.106121739130437, current_output: 13.92204,level: 25}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3853}) CREATE (b)-[r:Supply{max_supply: 16.948573913043482, current_output: 19.49086,level: 25}]->(g);
CREATE (n: Building {id: 3854, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3854}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.888905622739605, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3854}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0611385925326884, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3854}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.800176861089174,level: 1}]->(g);
CREATE (n: Building {id: 3855, name:"building_subsistence_farmslevel", level:97});
MATCH (g: Goods{code: 7}), (b: Building{id:3855}) CREATE (b)-[r:Supply{max_supply: 241.93497, current_output: 241.93497,level: 97}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3855}) CREATE (b)-[r:Supply{max_supply: 48.38699, current_output: 48.38699,level: 97}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3855}) CREATE (b)-[r:Supply{max_supply: 48.38699, current_output: 48.38699,level: 97}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3855}) CREATE (b)-[r:Supply{max_supply: 48.38699, current_output: 48.38699,level: 97}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3855}) CREATE (b)-[r:Supply{max_supply: 48.38699, current_output: 48.38699,level: 97}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3855}) CREATE (b)-[r:Supply{max_supply: 48.38699, current_output: 48.38699,level: 97}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3855}) CREATE (b)-[r:Supply{max_supply: 67.74179, current_output: 67.74179,level: 97}]->(g);
CREATE (n: Building {id: 3856, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3856}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.55562249095842, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3856}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 8.244554370130754, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3856}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 79.2007074443567,level: 4}]->(g);
CREATE (n: Building {id: 3857, name:"building_subsistence_farmslevel", level:173});
MATCH (g: Goods{code: 7}), (b: Building{id:3857}) CREATE (b)-[r:Supply{max_supply: 431.45335, current_output: 431.45335,level: 173}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3857}) CREATE (b)-[r:Supply{max_supply: 86.29067, current_output: 86.29067,level: 173}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3857}) CREATE (b)-[r:Supply{max_supply: 86.29067, current_output: 86.29067,level: 173}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3857}) CREATE (b)-[r:Supply{max_supply: 86.29067, current_output: 86.29067,level: 173}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3857}) CREATE (b)-[r:Supply{max_supply: 86.29067, current_output: 86.29067,level: 173}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3857}) CREATE (b)-[r:Supply{max_supply: 86.29067, current_output: 86.29067,level: 173}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3857}) CREATE (b)-[r:Supply{max_supply: 120.80693, current_output: 120.80693,level: 173}]->(g);
CREATE (n: Building {id: 3858, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3858}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 17.33343373643763, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3858}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 12.36683155519613, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3858}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 118.80106116653505,level: 6}]->(g);
CREATE (n: Building {id: 3859, name:"building_subsistence_farmslevel", level:76});
MATCH (g: Goods{code: 7}), (b: Building{id:3859}) CREATE (b)-[r:Supply{max_supply: 189.5421, current_output: 189.5421,level: 76}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3859}) CREATE (b)-[r:Supply{max_supply: 37.90842, current_output: 37.90842,level: 76}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3859}) CREATE (b)-[r:Supply{max_supply: 37.90842, current_output: 37.90842,level: 76}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3859}) CREATE (b)-[r:Supply{max_supply: 37.90842, current_output: 37.90842,level: 76}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3859}) CREATE (b)-[r:Supply{max_supply: 37.90842, current_output: 37.90842,level: 76}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3859}) CREATE (b)-[r:Supply{max_supply: 37.90842, current_output: 37.90842,level: 76}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3859}) CREATE (b)-[r:Supply{max_supply: 53.07178, current_output: 53.07178,level: 76}]->(g);
CREATE (n: Building {id: 3860, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3860}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.888905622739605, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3860}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0611385925326884, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3860}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.800176861089174,level: 1}]->(g);
CREATE (n: Building {id: 3861, name:"building_subsistence_farmslevel", level:91});
MATCH (g: Goods{code: 7}), (b: Building{id:3861}) CREATE (b)-[r:Supply{max_supply: 227.16785, current_output: 227.16785,level: 91}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3861}) CREATE (b)-[r:Supply{max_supply: 45.43357, current_output: 45.43357,level: 91}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3861}) CREATE (b)-[r:Supply{max_supply: 45.43357, current_output: 45.43357,level: 91}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3861}) CREATE (b)-[r:Supply{max_supply: 45.43357, current_output: 45.43357,level: 91}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3861}) CREATE (b)-[r:Supply{max_supply: 45.43357, current_output: 45.43357,level: 91}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3861}) CREATE (b)-[r:Supply{max_supply: 45.43357, current_output: 45.43357,level: 91}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3861}) CREATE (b)-[r:Supply{max_supply: 63.60699, current_output: 63.60699,level: 91}]->(g);
CREATE (n: Building {id: 3862, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3862}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.77781124547921, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3862}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.122277185065377, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3862}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 39.60035372217835,level: 2}]->(g);
CREATE (n: Building {id: 3863, name:"building_subsistence_farmslevel", level:37});
MATCH (g: Goods{code: 7}), (b: Building{id:3863}) CREATE (b)-[r:Supply{max_supply: 80.12812, current_output: 80.12812,level: 37}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3863}) CREATE (b)-[r:Supply{max_supply: 16.02562, current_output: 16.02562,level: 37}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3863}) CREATE (b)-[r:Supply{max_supply: 16.02562, current_output: 16.02562,level: 37}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3863}) CREATE (b)-[r:Supply{max_supply: 16.02562, current_output: 16.02562,level: 37}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3863}) CREATE (b)-[r:Supply{max_supply: 16.02562, current_output: 16.02562,level: 37}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3863}) CREATE (b)-[r:Supply{max_supply: 16.02562, current_output: 16.02562,level: 37}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3863}) CREATE (b)-[r:Supply{max_supply: 22.43587, current_output: 22.43587,level: 37}]->(g);
CREATE (n: Building {id: 3864, name:"building_subsistence_farmslevel", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:3864}) CREATE (b)-[r:Supply{max_supply: 68.66547, current_output: 68.66547,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3864}) CREATE (b)-[r:Supply{max_supply: 13.73309, current_output: 13.73309,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3864}) CREATE (b)-[r:Supply{max_supply: 13.73309, current_output: 13.73309,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3864}) CREATE (b)-[r:Supply{max_supply: 13.73309, current_output: 13.73309,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3864}) CREATE (b)-[r:Supply{max_supply: 13.73309, current_output: 13.73309,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3864}) CREATE (b)-[r:Supply{max_supply: 13.73309, current_output: 13.73309,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3864}) CREATE (b)-[r:Supply{max_supply: 19.22633, current_output: 19.22633,level: 29}]->(g);
CREATE (n: Building {id: 3865, name:"building_subsistence_farmslevel", level:67});
MATCH (g: Goods{code: 7}), (b: Building{id:3865}) CREATE (b)-[r:Supply{max_supply: 162.11487, current_output: 162.11487,level: 67}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3865}) CREATE (b)-[r:Supply{max_supply: 32.42297, current_output: 32.42297,level: 67}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3865}) CREATE (b)-[r:Supply{max_supply: 32.42297, current_output: 32.42297,level: 67}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3865}) CREATE (b)-[r:Supply{max_supply: 32.42297, current_output: 32.42297,level: 67}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3865}) CREATE (b)-[r:Supply{max_supply: 32.42297, current_output: 32.42297,level: 67}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3865}) CREATE (b)-[r:Supply{max_supply: 32.42297, current_output: 32.42297,level: 67}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3865}) CREATE (b)-[r:Supply{max_supply: 45.39216, current_output: 45.39216,level: 67}]->(g);
CREATE (n: Building {id: 3866, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3866}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 14.444528113698025, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3866}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 10.305692962663441, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3866}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 99.00088430544587,level: 5}]->(g);
CREATE (n: Building {id: 3867, name:"building_subsistence_farmslevel", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:3867}) CREATE (b)-[r:Supply{max_supply: 22.6044, current_output: 22.6044,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3867}) CREATE (b)-[r:Supply{max_supply: 4.52088, current_output: 4.52088,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3867}) CREATE (b)-[r:Supply{max_supply: 4.52088, current_output: 4.52088,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3867}) CREATE (b)-[r:Supply{max_supply: 4.52088, current_output: 4.52088,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3867}) CREATE (b)-[r:Supply{max_supply: 4.52088, current_output: 4.52088,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3867}) CREATE (b)-[r:Supply{max_supply: 4.52088, current_output: 4.52088,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3867}) CREATE (b)-[r:Supply{max_supply: 6.32923, current_output: 6.32923,level: 14}]->(g);
CREATE (n: Building {id: 3868, name:"building_subsistence_farmslevel", level:21});
MATCH (g: Goods{code: 7}), (b: Building{id:3868}) CREATE (b)-[r:Supply{max_supply: 51.69412, current_output: 51.69412,level: 21}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3868}) CREATE (b)-[r:Supply{max_supply: 10.33882, current_output: 10.33882,level: 21}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3868}) CREATE (b)-[r:Supply{max_supply: 10.33882, current_output: 10.33882,level: 21}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3868}) CREATE (b)-[r:Supply{max_supply: 10.33882, current_output: 10.33882,level: 21}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3868}) CREATE (b)-[r:Supply{max_supply: 10.33882, current_output: 10.33882,level: 21}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3868}) CREATE (b)-[r:Supply{max_supply: 10.33882, current_output: 10.33882,level: 21}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3868}) CREATE (b)-[r:Supply{max_supply: 14.47435, current_output: 14.47435,level: 21}]->(g);
CREATE (n: Building {id: 3869, name:"building_subsistence_farmslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:3869}) CREATE (b)-[r:Supply{max_supply: 92.5441, current_output: 92.5441,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3869}) CREATE (b)-[r:Supply{max_supply: 18.50882, current_output: 18.50882,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3869}) CREATE (b)-[r:Supply{max_supply: 18.50882, current_output: 18.50882,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3869}) CREATE (b)-[r:Supply{max_supply: 18.50882, current_output: 18.50882,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3869}) CREATE (b)-[r:Supply{max_supply: 18.50882, current_output: 18.50882,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3869}) CREATE (b)-[r:Supply{max_supply: 18.50882, current_output: 18.50882,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3869}) CREATE (b)-[r:Supply{max_supply: 25.91234, current_output: 25.91234,level: 44}]->(g);
CREATE (n: Building {id: 3940, name:"building_subsistence_farmslevel", level:63});
MATCH (g: Goods{code: 7}), (b: Building{id:3940}) CREATE (b)-[r:Supply{max_supply: 153.57352, current_output: 153.57352,level: 63}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3940}) CREATE (b)-[r:Supply{max_supply: 30.7147, current_output: 30.7147,level: 63}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3940}) CREATE (b)-[r:Supply{max_supply: 30.7147, current_output: 30.7147,level: 63}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3940}) CREATE (b)-[r:Supply{max_supply: 30.7147, current_output: 30.7147,level: 63}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3940}) CREATE (b)-[r:Supply{max_supply: 30.7147, current_output: 30.7147,level: 63}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3940}) CREATE (b)-[r:Supply{max_supply: 30.7147, current_output: 30.7147,level: 63}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3940}) CREATE (b)-[r:Supply{max_supply: 43.00058, current_output: 43.00058,level: 63}]->(g);
CREATE (n: Building {id: 3967, name:"building_trade_centerlevel", level:26});
CREATE (n: Building {id: 4056, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4114, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4116, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4117, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4588, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4589, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4590, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4591, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4592, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4593, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4594, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4595, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4596, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4597, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4598, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4599, name:"building_conscription_centerlevel", level:16});
CREATE (n: Building {id: 4600, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4601, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4602, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4603, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4604, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4605, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4606, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4639, name:"building_conscription_centerlevel", level:4});
