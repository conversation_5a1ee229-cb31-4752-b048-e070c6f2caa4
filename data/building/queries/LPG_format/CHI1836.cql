CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:74.39657627550262, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:15.606327981751178, pop_demand:34005.500343081614});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:21.538481629250143, pop_demand:780.0011641194315});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:15.814875995746576, pop_demand:4548.694235441974});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:22.512329300287213, pop_demand:2689.794524558019});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:28.017245034415534, pop_demand:8062.020220600209});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:25.736935419333168, pop_demand:7607.064989856635});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:36.65948657229709, pop_demand:334.5162365575099});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:11.912800744222439, pop_demand:2307.679311666665});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:51.834696219297314, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:12.038611297983813, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:28.079520965012513, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:30.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:36.37362637362637, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:51.22165873569104, pop_demand:161.8982628129463});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:14.092744024366368, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:92.80117020878045, pop_demand:2024.6346431265415});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:42.73120715730779, pop_demand:1177.2005953225573});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:39.22122152300156, pop_demand:2259.348444735977});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:22.334912422792847, pop_demand:8335.066402881186});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:36.837109932060855, pop_demand:2756.7395950000036});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:24.59725726320241, pop_demand:692.2802566122953});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:70.0, pop_demand:176.6863427358829});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:80.73344866791328, pop_demand:2472.869735082587});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:84.60408827122642, pop_demand:706.0238439689443});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:85.96440813351157, pop_demand:1785.6096515500938});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 2166, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2166}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 2384, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2384}) CREATE (b)-[r:Supply{max_supply: 89.64, current_output: 91.4328,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2384}) CREATE (b)-[r:Supply{max_supply: 14.94, current_output: 15.2388,level: 3}]->(g);
CREATE (n: Building {id: 2385, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2385}) CREATE (b)-[r:Supply{max_supply: 89.64000000000001, current_output: 73.5048,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2385}) CREATE (b)-[r:Supply{max_supply: 14.940000000000001, current_output: 12.2508,level: 3}]->(g);
CREATE (n: Building {id: 2386, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2386}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 70.40228190090181, level: 10}]->(b);
CREATE (n: Building {id: 2387, name:"building_logging_camplevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2387}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 109.0,level: 10}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2387}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 109.0,level: 10}]->(g);
CREATE (n: Building {id: 2388, name:"building_opium_plantationlevel", level:6});
MATCH (g: Goods{code: 44}), (b: Building{id:2388}) CREATE (b)-[r:Supply{max_supply: 119.52, current_output: 125.496,level: 6}]->(g);
CREATE (n: Building {id: 2389, name:"building_paper_millslevel", level:12});
MATCH (g: Goods{code: 10}), (b: Building{id:2389}) CREATE (g)-[r:Demand{max_demand: 360.0, current_input: 299.7040967931068, level: 12}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2389}) CREATE (b)-[r:Supply{max_supply: 479.99999999999994, current_output: 399.60546239080907,level: 12}]->(g);
CREATE (n: Building {id: 2390, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2391, name:"building_government_administrationlevel", level:8});
MATCH (g: Goods{code: 14}), (b: Building{id:2391}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 56.32182552072145, level: 8}]->(b);
CREATE (n: Building {id: 2392, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2392}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 41.2,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2392}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 41.2,level: 4}]->(g);
CREATE (n: Building {id: 2393, name:"building_tea_plantationlevel", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2393}) CREATE (b)-[r:Supply{max_supply: 199.19999999999996, current_output: 217.128,level: 10}]->(g);
CREATE (n: Building {id: 2394, name:"building_opium_plantationlevel", level:10});
MATCH (g: Goods{code: 44}), (b: Building{id:2394}) CREATE (b)-[r:Supply{max_supply: 199.19999999999996, current_output: 217.128,level: 10}]->(g);
CREATE (n: Building {id: 2395, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2396, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2396}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 70.40228190090181, level: 10}]->(b);
CREATE (n: Building {id: 2397, name:"building_furniture_manufacturieslevel", level:7});
MATCH (g: Goods{code: 9}), (b: Building{id:2397}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 97.08849185260735, level: 7}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2397}) CREATE (g)-[r:Demand{max_demand: 140.0, current_input: 116.55159319731932, level: 7}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2397}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 79.625, level: 7}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2397}) CREATE (b)-[r:Supply{max_supply: 175.0, current_output: 165.22983049888305,level: 7}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2397}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 132.18386439910645,level: 7}]->(g);
CREATE (n: Building {id: 2398, name:"building_glassworkslevel", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:2398}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 199.80273119540456, level: 8}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2398}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 199.12032, level: 8}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2398}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 73.30045519923408,level: 8}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2398}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 183.25113799808523,level: 8}]->(g);
CREATE (n: Building {id: 2399, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2399}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 52.0,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2399}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 52.0,level: 5}]->(g);
CREATE (n: Building {id: 2400, name:"building_opium_plantationlevel", level:5});
MATCH (g: Goods{code: 44}), (b: Building{id:2400}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 103.584,level: 5}]->(g);
CREATE (n: Building {id: 2401, name:"building_dye_plantationlevel", level:8});
MATCH (g: Goods{code: 21}), (b: Building{id:2401}) CREATE (b)-[r:Supply{max_supply: 199.2, current_output: 213.144,level: 8}]->(g);
CREATE (n: Building {id: 2402, name:"building_tea_plantationlevel", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2402}) CREATE (b)-[r:Supply{max_supply: 79.68, current_output: 82.0704,level: 4}]->(g);
CREATE (n: Building {id: 2403, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2404, name:"building_government_administrationlevel", level:8});
MATCH (g: Goods{code: 14}), (b: Building{id:2404}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 56.32182552072145, level: 8}]->(b);
CREATE (n: Building {id: 2405, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:2405}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 36.65022759961705, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2405}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 2406, name:"building_rice_farmlevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:2406}) CREATE (b)-[r:Supply{max_supply: 159.35999999999999, current_output: 170.5152,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2406}) CREATE (b)-[r:Supply{max_supply: 47.80799999999999, current_output: 51.15456,level: 8}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2406}) CREATE (b)-[r:Supply{max_supply: 71.712, current_output: 76.73184,level: 8}]->(g);
CREATE (n: Building {id: 2407, name:"building_dye_plantationlevel", level:6});
MATCH (g: Goods{code: 21}), (b: Building{id:2407}) CREATE (b)-[r:Supply{max_supply: 149.4, current_output: 156.87,level: 6}]->(g);
CREATE (n: Building {id: 2408, name:"building_silk_plantationlevel", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2408}) CREATE (b)-[r:Supply{max_supply: 199.2, current_output: 266.928,level: 10}]->(g);
CREATE (n: Building {id: 2409, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2410, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2410}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 23.904,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2410}) CREATE (b)-[r:Supply{max_supply: 4.9799999999999995, current_output: 3.984,level: 1}]->(g);
CREATE (n: Building {id: 2411, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 2412, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2412}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 48.4056,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2412}) CREATE (b)-[r:Supply{max_supply: 9.96, current_output: 8.0676,level: 2}]->(g);
CREATE (n: Building {id: 2413, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 2414, name:"building_government_administrationlevel", level:9});
MATCH (g: Goods{code: 14}), (b: Building{id:2414}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 63.36205371081164, level: 9}]->(b);
CREATE (n: Building {id: 2415, name:"building_banana_plantationlevel", level:8});
MATCH (g: Goods{code: 37}), (b: Building{id:2415}) CREATE (b)-[r:Supply{max_supply: 239.03999999999996, current_output: 255.7728,level: 8}]->(g);
CREATE (n: Building {id: 2416, name:"building_rice_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:2416}) CREATE (b)-[r:Supply{max_supply: 139.44, current_output: 147.8064,level: 7}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2416}) CREATE (b)-[r:Supply{max_supply: 41.832, current_output: 44.34192,level: 7}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2416}) CREATE (b)-[r:Supply{max_supply: 62.74799999999999, current_output: 66.51288,level: 7}]->(g);
CREATE (n: Building {id: 2417, name:"building_tea_plantationlevel", level:8});
MATCH (g: Goods{code: 40}), (b: Building{id:2417}) CREATE (b)-[r:Supply{max_supply: 159.35999999999999, current_output: 170.5152,level: 8}]->(g);
CREATE (n: Building {id: 2418, name:"building_dye_plantationlevel", level:10});
MATCH (g: Goods{code: 21}), (b: Building{id:2418}) CREATE (b)-[r:Supply{max_supply: 249.0, current_output: 271.41,level: 10}]->(g);
CREATE (n: Building {id: 2419, name:"building_silk_plantationlevel", level:16});
MATCH (g: Goods{code: 20}), (b: Building{id:2419}) CREATE (b)-[r:Supply{max_supply: 318.72, current_output: 446.208,level: 16}]->(g);
CREATE (n: Building {id: 2420, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2421, name:"building_government_administrationlevel", level:20});
MATCH (g: Goods{code: 14}), (b: Building{id:2421}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 140.80456380180365, level: 20}]->(b);
CREATE (n: Building {id: 2422, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2422}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 104.02338412779356, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2422}) CREATE (g)-[r:Demand{max_demand: 225.0, current_input: 187.31506049569177, level: 3}]->(b);
CREATE (n: Building {id: 2423, name:"building_tooling_workshopslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2423}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 99.90136559770227, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2423}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 120.0, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2423}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 219.90136559770227,level: 4}]->(g);
CREATE (n: Building {id: 2424, name:"building_furniture_manufacturieslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:2424}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 69.34892275186239, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2424}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 83.25113799808523, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2424}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 56.875, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2424}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 118.02130749920218,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2424}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 94.41704599936175,level: 5}]->(g);
CREATE (n: Building {id: 2425, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2425}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 30.4776,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2425}) CREATE (b)-[r:Supply{max_supply: 26.892, current_output: 27.42984,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2425}) CREATE (b)-[r:Supply{max_supply: 17.928, current_output: 18.28656,level: 3}]->(g);
CREATE (n: Building {id: 2426, name:"building_tea_plantationlevel", level:5});
MATCH (g: Goods{code: 40}), (b: Building{id:2426}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 103.584,level: 5}]->(g);
CREATE (n: Building {id: 2427, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2428, name:"building_forbidden_citylevel", level:1});
CREATE (n: Building {id: 2429, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2429}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 41.2,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2429}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 41.2,level: 4}]->(g);
CREATE (n: Building {id: 2430, name:"building_livestock_ranchlevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:2430}) CREATE (b)-[r:Supply{max_supply: 298.8, current_output: 325.692,level: 10}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2430}) CREATE (b)-[r:Supply{max_supply: 49.79999999999999, current_output: 54.282,level: 10}]->(g);
CREATE (n: Building {id: 2431, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 2432, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:2432}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 42.241369140541096, level: 6}]->(b);
CREATE (n: Building {id: 2433, name:"building_iron_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:2433}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 109.95068279885113, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2433}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 3}]->(g);
CREATE (n: Building {id: 2434, name:"building_logging_camplevel", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:2434}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 85.6,level: 8}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2434}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 85.6,level: 8}]->(g);
CREATE (n: Building {id: 2435, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2435}) CREATE (b)-[r:Supply{max_supply: 89.64, current_output: 91.4328,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2435}) CREATE (b)-[r:Supply{max_supply: 14.94, current_output: 15.2388,level: 3}]->(g);
CREATE (n: Building {id: 2436, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2437, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2437}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 63.0,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2437}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 63.0,level: 6}]->(g);
CREATE (n: Building {id: 2438, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2438}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2438}) CREATE (b)-[r:Supply{max_supply: 9.959999999999999, current_output: 10.0596,level: 2}]->(g);
CREATE (n: Building {id: 2439, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2440, name:"building_government_administrationlevel", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:2440}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 49.28159733063128, level: 7}]->(b);
CREATE (n: Building {id: 2441, name:"building_wheat_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:2441}) CREATE (b)-[r:Supply{max_supply: 69.72, current_output: 84.3612,level: 7}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2441}) CREATE (b)-[r:Supply{max_supply: 62.748, current_output: 75.92508,level: 7}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2441}) CREATE (b)-[r:Supply{max_supply: 41.832, current_output: 50.61672,level: 7}]->(g);
CREATE (n: Building {id: 2442, name:"building_tea_plantationlevel", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:2442}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.9552,level: 3}]->(g);
CREATE (n: Building {id: 2443, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2444, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:2444}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 28.160912760360727, level: 4}]->(b);
CREATE (n: Building {id: 2445, name:"building_tea_plantationlevel", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:2445}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.9552,level: 3}]->(g);
CREATE (n: Building {id: 2446, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 2447, name:"building_livestock_ranchlevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:2447}) CREATE (b)-[r:Supply{max_supply: 179.28, current_output: 188.244,level: 6}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2447}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 31.374,level: 6}]->(g);
CREATE (n: Building {id: 2448, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 2449, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:2449}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 28.160912760360727, level: 4}]->(b);
CREATE (n: Building {id: 2450, name:"building_tea_plantationlevel", level:5});
MATCH (g: Goods{code: 40}), (b: Building{id:2450}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 103.584,level: 5}]->(g);
CREATE (n: Building {id: 2451, name:"building_barrackslevel", level:15});
CREATE (n: Building {id: 2452, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:2452}) CREATE (b)-[r:Supply{max_supply: 149.4, current_output: 155.376,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2452}) CREATE (b)-[r:Supply{max_supply: 24.9, current_output: 25.896,level: 5}]->(g);
CREATE (n: Building {id: 2453, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2454, name:"building_government_administrationlevel", level:12});
MATCH (g: Goods{code: 14}), (b: Building{id:2454}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 84.48273828108218, level: 12}]->(b);
CREATE (n: Building {id: 2455, name:"building_paper_millslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2455}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 99.90136559770227, level: 4}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2455}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 133.20182079693637,level: 4}]->(g);
CREATE (n: Building {id: 2456, name:"building_tea_plantationlevel", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2456}) CREATE (b)-[r:Supply{max_supply: 199.19999999999996, current_output: 217.128,level: 10}]->(g);
CREATE (n: Building {id: 2457, name:"building_silk_plantationlevel", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2457}) CREATE (b)-[r:Supply{max_supply: 199.2, current_output: 266.928,level: 10}]->(g);
CREATE (n: Building {id: 2458, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2459, name:"building_government_administrationlevel", level:13});
MATCH (g: Goods{code: 14}), (b: Building{id:2459}) CREATE (g)-[r:Demand{max_demand: 129.99999999999997, current_input: 91.52296647117234, level: 13}]->(b);
CREATE (n: Building {id: 2460, name:"building_banana_plantationlevel", level:7});
MATCH (g: Goods{code: 37}), (b: Building{id:2460}) CREATE (b)-[r:Supply{max_supply: 209.16, current_output: 221.7096,level: 7}]->(g);
CREATE (n: Building {id: 2461, name:"building_fishing_wharflevel", level:5});
MATCH (g: Goods{code: 8}), (b: Building{id:2461}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 130.0,level: 5}]->(g);
CREATE (n: Building {id: 2462, name:"building_silk_plantationlevel", level:20});
MATCH (g: Goods{code: 20}), (b: Building{id:2462}) CREATE (b)-[r:Supply{max_supply: 398.40000000000003, current_output: 573.696,level: 20}]->(g);
CREATE (n: Building {id: 2463, name:"building_rice_farmlevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:2463}) CREATE (b)-[r:Supply{max_supply: 159.35999999999999, current_output: 170.5152,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2463}) CREATE (b)-[r:Supply{max_supply: 47.80799999999999, current_output: 51.15456,level: 8}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2463}) CREATE (b)-[r:Supply{max_supply: 71.712, current_output: 76.73184,level: 8}]->(g);
CREATE (n: Building {id: 2464, name:"building_dye_plantationlevel", level:12});
MATCH (g: Goods{code: 21}), (b: Building{id:2464}) CREATE (b)-[r:Supply{max_supply: 298.79999999999995, current_output: 331.668,level: 12}]->(g);
CREATE (n: Building {id: 2465, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2465}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 55.4791382014899, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2465}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 66.60091039846819, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:2465}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 27.487670699712787,level: 2}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:2465}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 36.65022759961705,level: 2}]->(g);
CREATE (n: Building {id: 2466, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2467, name:"building_naval_baselevel", level:15});
MATCH (g: Goods{code: 5}), (b: Building{id:2467}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 27.487670699712787, level: 15}]->(b);
CREATE (n: Building {id: 2468, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:2468}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 18.325113799808523, level: 3}]->(b);
CREATE (n: Building {id: 2472, name:"building_government_administrationlevel", level:11});
MATCH (g: Goods{code: 14}), (b: Building{id:2472}) CREATE (g)-[r:Demand{max_demand: 109.99999999999999, current_input: 77.44251009099199, level: 11}]->(b);
CREATE (n: Building {id: 2473, name:"building_silk_plantationlevel", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2473}) CREATE (b)-[r:Supply{max_supply: 199.2, current_output: 266.928,level: 10}]->(g);
CREATE (n: Building {id: 2474, name:"building_rice_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2474}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 103.584,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2474}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 31.0752,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2474}) CREATE (b)-[r:Supply{max_supply: 44.82, current_output: 46.6128,level: 5}]->(g);
CREATE (n: Building {id: 2475, name:"building_dye_plantationlevel", level:10});
MATCH (g: Goods{code: 21}), (b: Building{id:2475}) CREATE (b)-[r:Supply{max_supply: 249.0, current_output: 271.41,level: 10}]->(g);
CREATE (n: Building {id: 2476, name:"building_fishing_wharflevel", level:4});
MATCH (g: Goods{code: 8}), (b: Building{id:2476}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 103.0,level: 4}]->(g);
CREATE (n: Building {id: 2477, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2478, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2478}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 70.40228190090181, level: 10}]->(b);
CREATE (n: Building {id: 2479, name:"building_textile_millslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:2479}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 332.87482920893945, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2479}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 1177.272, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2479}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 200.0,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2479}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 159.99999999999997,level: 8}]->(g);
CREATE (n: Building {id: 2480, name:"building_tea_plantationlevel", level:15});
MATCH (g: Goods{code: 40}), (b: Building{id:2480}) CREATE (b)-[r:Supply{max_supply: 298.8, current_output: 340.632,level: 15}]->(g);
CREATE (n: Building {id: 2481, name:"building_rice_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:2481}) CREATE (b)-[r:Supply{max_supply: 79.68, current_output: 82.0704,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2481}) CREATE (b)-[r:Supply{max_supply: 23.904, current_output: 24.62112,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2481}) CREATE (b)-[r:Supply{max_supply: 35.856, current_output: 36.93168,level: 4}]->(g);
CREATE (n: Building {id: 2482, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:2482}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 2483, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2484, name:"building_government_administrationlevel", level:12});
MATCH (g: Goods{code: 14}), (b: Building{id:2484}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 84.48273828108218, level: 12}]->(b);
CREATE (n: Building {id: 2485, name:"building_glassworkslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2485}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 124.87670699712784, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2485}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 124.4502, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2485}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 45.81278449952131,level: 5}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2485}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 114.53196124880327,level: 5}]->(g);
CREATE (n: Building {id: 2486, name:"building_tea_plantationlevel", level:15});
MATCH (g: Goods{code: 40}), (b: Building{id:2486}) CREATE (b)-[r:Supply{max_supply: 298.8, current_output: 340.632,level: 15}]->(g);
CREATE (n: Building {id: 2487, name:"building_rice_farmlevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:2487}) CREATE (b)-[r:Supply{max_supply: 159.35999999999999, current_output: 170.5152,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2487}) CREATE (b)-[r:Supply{max_supply: 47.80799999999999, current_output: 51.15456,level: 8}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2487}) CREATE (b)-[r:Supply{max_supply: 71.712, current_output: 76.73184,level: 8}]->(g);
CREATE (n: Building {id: 2488, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2489, name:"building_government_administrationlevel", level:17});
MATCH (g: Goods{code: 14}), (b: Building{id:2489}) CREATE (g)-[r:Demand{max_demand: 170.0, current_input: 119.6838792315331, level: 17}]->(b);
CREATE (n: Building {id: 2490, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2490}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 69.34892275186239, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2490}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 124.87670699712784, level: 2}]->(b);
CREATE (n: Building {id: 2491, name:"building_glassworkslevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2491}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 249.75341399425568, level: 10}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2491}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 248.9004, level: 10}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2491}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 91.6255689990426,level: 10}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2491}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 229.0639224976065,level: 10}]->(g);
CREATE (n: Building {id: 2492, name:"building_tea_plantationlevel", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2492}) CREATE (b)-[r:Supply{max_supply: 199.19999999999996, current_output: 217.128,level: 10}]->(g);
CREATE (n: Building {id: 2493, name:"building_rice_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2493}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 103.584,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2493}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 31.0752,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2493}) CREATE (b)-[r:Supply{max_supply: 44.82, current_output: 46.6128,level: 5}]->(g);
CREATE (n: Building {id: 2494, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2495, name:"building_government_administrationlevel", level:15});
MATCH (g: Goods{code: 14}), (b: Building{id:2495}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 105.60342285135273, level: 15}]->(b);
CREATE (n: Building {id: 2496, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2496}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 34.674461375931195, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2496}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 62.43835349856392, level: 1}]->(b);
CREATE (n: Building {id: 2497, name:"building_glassworkslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2497}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 149.8520483965534, level: 6}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2497}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 149.34024, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2497}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 54.975341399425574,level: 6}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2497}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 137.4383534985639,level: 6}]->(g);
CREATE (n: Building {id: 2498, name:"building_tea_plantationlevel", level:12});
MATCH (g: Goods{code: 40}), (b: Building{id:2498}) CREATE (b)-[r:Supply{max_supply: 239.04, current_output: 265.3344,level: 12}]->(g);
CREATE (n: Building {id: 2499, name:"building_government_administrationlevel", level:13});
MATCH (g: Goods{code: 14}), (b: Building{id:2499}) CREATE (g)-[r:Demand{max_demand: 129.99999999999997, current_input: 91.52296647117234, level: 13}]->(b);
CREATE (n: Building {id: 2500, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:2500}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 73.3004551992341, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2500}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 2}]->(g);
CREATE (n: Building {id: 2501, name:"building_paper_millslevel", level:12});
MATCH (g: Goods{code: 10}), (b: Building{id:2501}) CREATE (g)-[r:Demand{max_demand: 360.0, current_input: 299.7040967931068, level: 12}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2501}) CREATE (b)-[r:Supply{max_supply: 479.99999999999994, current_output: 399.60546239080907,level: 12}]->(g);
CREATE (n: Building {id: 2502, name:"building_glassworkslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2502}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 149.8520483965534, level: 6}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2502}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 149.34024, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2502}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 54.975341399425574,level: 6}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2502}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 137.4383534985639,level: 6}]->(g);
CREATE (n: Building {id: 2503, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:2503}) CREATE (b)-[r:Supply{max_supply: 39.84, current_output: 41.0352,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2503}) CREATE (b)-[r:Supply{max_supply: 35.856, current_output: 36.93168,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2503}) CREATE (b)-[r:Supply{max_supply: 23.904, current_output: 24.62112,level: 4}]->(g);
CREATE (n: Building {id: 2504, name:"building_tea_plantationlevel", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2504}) CREATE (b)-[r:Supply{max_supply: 199.19999999999996, current_output: 217.128,level: 10}]->(g);
CREATE (n: Building {id: 2505, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2506, name:"building_government_administrationlevel", level:12});
MATCH (g: Goods{code: 14}), (b: Building{id:2506}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 84.48273828108218, level: 12}]->(b);
CREATE (n: Building {id: 2507, name:"building_glassworkslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2507}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 124.87670699712784, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2507}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 124.4502, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2507}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 45.81278449952131,level: 5}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2507}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 114.53196124880327,level: 5}]->(g);
CREATE (n: Building {id: 2508, name:"building_rice_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2508}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 103.584,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2508}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 31.0752,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2508}) CREATE (b)-[r:Supply{max_supply: 44.82, current_output: 46.6128,level: 5}]->(g);
CREATE (n: Building {id: 2509, name:"building_silk_plantationlevel", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2509}) CREATE (b)-[r:Supply{max_supply: 199.2, current_output: 266.928,level: 10}]->(g);
CREATE (n: Building {id: 2510, name:"building_tea_plantationlevel", level:17});
MATCH (g: Goods{code: 40}), (b: Building{id:2510}) CREATE (b)-[r:Supply{max_supply: 338.64000000000004, current_output: 392.8224,level: 17}]->(g);
CREATE (n: Building {id: 2511, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2512, name:"building_government_administrationlevel", level:12});
MATCH (g: Goods{code: 14}), (b: Building{id:2512}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 84.48273828108218, level: 12}]->(b);
CREATE (n: Building {id: 2513, name:"building_furniture_manufacturieslevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:2513}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 138.69784550372475, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2513}) CREATE (g)-[r:Demand{max_demand: 199.99999999999997, current_input: 166.50227599617043, level: 10}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2513}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 113.74999999999999, level: 10}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2513}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 236.04261499840433,level: 10}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2513}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 188.83409199872347,level: 10}]->(g);
CREATE (n: Building {id: 2514, name:"building_rice_farmlevel", level:12});
MATCH (g: Goods{code: 7}), (b: Building{id:2514}) CREATE (b)-[r:Supply{max_supply: 239.04, current_output: 265.3344,level: 12}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2514}) CREATE (b)-[r:Supply{max_supply: 71.71199999999999, current_output: 79.60032,level: 12}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2514}) CREATE (b)-[r:Supply{max_supply: 107.568, current_output: 119.40048,level: 12}]->(g);
CREATE (n: Building {id: 2515, name:"building_paper_millslevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:2515}) CREATE (g)-[r:Demand{max_demand: 209.99999999999997, current_input: 174.82738979597895, level: 7}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2515}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 233.1031863946386,level: 7}]->(g);
CREATE (n: Building {id: 2516, name:"building_tea_plantationlevel", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2516}) CREATE (b)-[r:Supply{max_supply: 199.19999999999996, current_output: 217.128,level: 10}]->(g);
CREATE (n: Building {id: 2517, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2518, name:"building_government_administrationlevel", level:11});
MATCH (g: Goods{code: 14}), (b: Building{id:2518}) CREATE (g)-[r:Demand{max_demand: 109.99999999999999, current_input: 77.44251009099199, level: 11}]->(b);
CREATE (n: Building {id: 2519, name:"building_rice_farmlevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:2519}) CREATE (b)-[r:Supply{max_supply: 119.52, current_output: 125.496,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2519}) CREATE (b)-[r:Supply{max_supply: 35.856, current_output: 37.6488,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2519}) CREATE (b)-[r:Supply{max_supply: 53.784, current_output: 56.4732,level: 6}]->(g);
CREATE (n: Building {id: 2520, name:"building_tea_plantationlevel", level:15});
MATCH (g: Goods{code: 40}), (b: Building{id:2520}) CREATE (b)-[r:Supply{max_supply: 298.8, current_output: 340.632,level: 15}]->(g);
CREATE (n: Building {id: 2521, name:"building_barrackslevel", level:15});
CREATE (n: Building {id: 2522, name:"building_government_administrationlevel", level:9});
MATCH (g: Goods{code: 14}), (b: Building{id:2522}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 63.36205371081164, level: 9}]->(b);
CREATE (n: Building {id: 2523, name:"building_paper_millslevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2523}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 249.75341399425568, level: 10}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2523}) CREATE (b)-[r:Supply{max_supply: 399.99999999999994, current_output: 333.00455199234085,level: 10}]->(g);
CREATE (n: Building {id: 2524, name:"building_furniture_manufacturieslevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:2524}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 138.69784550372475, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2524}) CREATE (g)-[r:Demand{max_demand: 199.99999999999997, current_input: 166.50227599617043, level: 10}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2524}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 113.74999999999999, level: 10}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2524}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 236.04261499840433,level: 10}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2524}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 188.83409199872347,level: 10}]->(g);
CREATE (n: Building {id: 2525, name:"building_rice_farmlevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:2525}) CREATE (b)-[r:Supply{max_supply: 119.52, current_output: 125.496,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2525}) CREATE (b)-[r:Supply{max_supply: 35.856, current_output: 37.6488,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2525}) CREATE (b)-[r:Supply{max_supply: 53.784, current_output: 56.4732,level: 6}]->(g);
CREATE (n: Building {id: 2526, name:"building_tea_plantationlevel", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2526}) CREATE (b)-[r:Supply{max_supply: 199.19999999999996, current_output: 217.128,level: 10}]->(g);
CREATE (n: Building {id: 2527, name:"building_barrackslevel", level:15});
CREATE (n: Building {id: 2528, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2528}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 70.40228190090181, level: 10}]->(b);
CREATE (n: Building {id: 2529, name:"building_furniture_manufacturieslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:2529}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 110.95827640297979, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2529}) CREATE (g)-[r:Demand{max_demand: 159.99999999999997, current_input: 133.20182079693635, level: 8}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2529}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 90.99999999999999, level: 8}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2529}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 188.8340919987235,level: 8}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2529}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 151.06727359897877,level: 8}]->(g);
CREATE (n: Building {id: 2530, name:"building_rice_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:2530}) CREATE (b)-[r:Supply{max_supply: 79.68, current_output: 82.0704,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2530}) CREATE (b)-[r:Supply{max_supply: 23.904, current_output: 24.62112,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2530}) CREATE (b)-[r:Supply{max_supply: 35.856, current_output: 36.93168,level: 4}]->(g);
CREATE (n: Building {id: 2531, name:"building_paper_millslevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2531}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 249.75341399425568, level: 10}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2531}) CREATE (b)-[r:Supply{max_supply: 399.99999999999994, current_output: 333.00455199234085,level: 10}]->(g);
CREATE (n: Building {id: 2532, name:"building_tea_plantationlevel", level:6});
MATCH (g: Goods{code: 40}), (b: Building{id:2532}) CREATE (b)-[r:Supply{max_supply: 119.52, current_output: 125.496,level: 6}]->(g);
CREATE (n: Building {id: 2533, name:"building_silk_plantationlevel", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2533}) CREATE (b)-[r:Supply{max_supply: 199.2, current_output: 266.928,level: 10}]->(g);
CREATE (n: Building {id: 2534, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2535, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:2535}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 42.241369140541096, level: 6}]->(b);
CREATE (n: Building {id: 2536, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2536}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 63.0,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2536}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 63.0,level: 6}]->(g);
CREATE (n: Building {id: 2537, name:"building_rice_farmlevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:2537}) CREATE (b)-[r:Supply{max_supply: 119.52, current_output: 125.496,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2537}) CREATE (b)-[r:Supply{max_supply: 35.856, current_output: 37.6488,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2537}) CREATE (b)-[r:Supply{max_supply: 53.784, current_output: 56.4732,level: 6}]->(g);
CREATE (n: Building {id: 2538, name:"building_tea_plantationlevel", level:8});
MATCH (g: Goods{code: 40}), (b: Building{id:2538}) CREATE (b)-[r:Supply{max_supply: 159.35999999999999, current_output: 170.5152,level: 8}]->(g);
CREATE (n: Building {id: 2539, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2540, name:"building_fishing_wharflevel", level:5});
MATCH (g: Goods{code: 8}), (b: Building{id:2540}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 130.0,level: 5}]->(g);
CREATE (n: Building {id: 2541, name:"building_tea_plantationlevel", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:2541}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.9552,level: 3}]->(g);
CREATE (n: Building {id: 2542, name:"building_banana_plantationlevel", level:5});
MATCH (g: Goods{code: 37}), (b: Building{id:2542}) CREATE (b)-[r:Supply{max_supply: 149.4, current_output: 155.376,level: 5}]->(g);
CREATE (n: Building {id: 2543, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 2544, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2544}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 6.108371266602841, level: 1}]->(b);
CREATE (n: Building {id: 2545, name:"building_government_administrationlevel", level:9});
MATCH (g: Goods{code: 14}), (b: Building{id:2545}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 63.36205371081164, level: 9}]->(b);
CREATE (n: Building {id: 2546, name:"building_glassworkslevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2546}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 249.75341399425568, level: 10}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2546}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 248.9004, level: 10}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2546}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 91.6255689990426,level: 10}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2546}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 229.0639224976065,level: 10}]->(g);
CREATE (n: Building {id: 2547, name:"building_textile_millslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:2547}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 332.87482920893945, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2547}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 1177.272, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2547}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 200.0,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2547}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 159.99999999999997,level: 8}]->(g);
CREATE (n: Building {id: 2548, name:"building_tea_plantationlevel", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:2548}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.9552,level: 3}]->(g);
CREATE (n: Building {id: 2549, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2549}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 30.4776,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2549}) CREATE (b)-[r:Supply{max_supply: 26.892, current_output: 27.42984,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2549}) CREATE (b)-[r:Supply{max_supply: 17.928, current_output: 18.28656,level: 3}]->(g);
CREATE (n: Building {id: 2550, name:"building_fishing_wharflevel", level:6});
MATCH (g: Goods{code: 8}), (b: Building{id:2550}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 157.5,level: 6}]->(g);
CREATE (n: Building {id: 2551, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2552, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2552}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 6.108371266602841, level: 1}]->(b);
CREATE (n: Building {id: 2553, name:"building_government_administrationlevel", level:16});
MATCH (g: Goods{code: 14}), (b: Building{id:2553}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 112.64365104144291, level: 16}]->(b);
CREATE (n: Building {id: 2554, name:"building_paper_millslevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2554}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 249.75341399425568, level: 10}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2554}) CREATE (b)-[r:Supply{max_supply: 399.99999999999994, current_output: 333.00455199234085,level: 10}]->(g);
CREATE (n: Building {id: 2555, name:"building_tea_plantationlevel", level:12});
MATCH (g: Goods{code: 40}), (b: Building{id:2555}) CREATE (b)-[r:Supply{max_supply: 239.04, current_output: 265.3344,level: 12}]->(g);
CREATE (n: Building {id: 2556, name:"building_fishing_wharflevel", level:4});
MATCH (g: Goods{code: 8}), (b: Building{id:2556}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 103.0,level: 4}]->(g);
CREATE (n: Building {id: 2557, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2558, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2558}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 6.108371266602841, level: 1}]->(b);
CREATE (n: Building {id: 2559, name:"building_livestock_ranchlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:2559}) CREATE (b)-[r:Supply{max_supply: 119.52, current_output: 123.1056,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2559}) CREATE (b)-[r:Supply{max_supply: 19.92, current_output: 20.5176,level: 4}]->(g);
CREATE (n: Building {id: 2560, name:"building_livestock_ranchlevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:2560}) CREATE (b)-[r:Supply{max_supply: 179.28, current_output: 188.244,level: 6}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2560}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 31.374,level: 6}]->(g);
CREATE (n: Building {id: 2897, name:"building_subsistence_rice_paddieslevel", level:344});
MATCH (g: Goods{code: 7}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 1888.216, current_output: 1888.216,level: 344}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 257.484, current_output: 257.484,level: 344}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 257.484, current_output: 257.484,level: 344}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 343.312, current_output: 343.312,level: 344}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 343.312, current_output: 343.312,level: 344}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 343.312, current_output: 343.312,level: 344}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 514.968, current_output: 514.968,level: 344}]->(g);
CREATE (n: Building {id: 2898, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2898}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.97534139942557, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2898}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.778341264308956, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2898}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 175.0147306549381,level: 6}]->(g);
CREATE (n: Building {id: 2899, name:"building_subsistence_rice_paddieslevel", level:388});
MATCH (g: Goods{code: 7}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 2129.732, current_output: 2129.732,level: 388}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 290.418, current_output: 290.418,level: 388}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 290.418, current_output: 290.418,level: 388}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 387.224, current_output: 387.224,level: 388}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 387.224, current_output: 387.224,level: 388}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 387.224, current_output: 387.224,level: 388}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 580.836, current_output: 580.836,level: 388}]->(g);
CREATE (n: Building {id: 2900, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2900}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.97534139942557, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2900}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.778341264308956, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2900}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 175.0147306549381,level: 6}]->(g);
CREATE (n: Building {id: 3062, name:"building_subsistence_pastureslevel", level:34});
MATCH (g: Goods{code: 7}), (b: Building{id:3062}) CREATE (b)-[r:Supply{max_supply: 16.31167, current_output: 16.31167,level: 34}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3062}) CREATE (b)-[r:Supply{max_supply: 24.4675, current_output: 24.4675,level: 34}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3062}) CREATE (b)-[r:Supply{max_supply: 8.15583, current_output: 8.15583,level: 34}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3062}) CREATE (b)-[r:Supply{max_supply: 16.31167, current_output: 16.31167,level: 34}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3062}) CREATE (b)-[r:Supply{max_supply: 16.31167, current_output: 16.31167,level: 34}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3062}) CREATE (b)-[r:Supply{max_supply: 16.31167, current_output: 16.31167,level: 34}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3062}) CREATE (b)-[r:Supply{max_supply: 54.15474, current_output: 54.15474,level: 34}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3062}) CREATE (b)-[r:Supply{max_supply: 22.83633, current_output: 22.83633,level: 34}]->(g);
CREATE (n: Building {id: 3063, name:"building_subsistence_pastureslevel", level:26});
MATCH (g: Goods{code: 7}), (b: Building{id:3063}) CREATE (b)-[r:Supply{max_supply: 12.948, current_output: 12.948,level: 26}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3063}) CREATE (b)-[r:Supply{max_supply: 19.422, current_output: 19.422,level: 26}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3063}) CREATE (b)-[r:Supply{max_supply: 6.474, current_output: 6.474,level: 26}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3063}) CREATE (b)-[r:Supply{max_supply: 12.948, current_output: 12.948,level: 26}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3063}) CREATE (b)-[r:Supply{max_supply: 12.948, current_output: 12.948,level: 26}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3063}) CREATE (b)-[r:Supply{max_supply: 12.948, current_output: 12.948,level: 26}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3063}) CREATE (b)-[r:Supply{max_supply: 42.98736, current_output: 42.98736,level: 26}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3063}) CREATE (b)-[r:Supply{max_supply: 18.1272, current_output: 18.1272,level: 26}]->(g);
CREATE (n: Building {id: 3701, name:"building_subsistence_pastureslevel", level:27});
MATCH (g: Goods{code: 7}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 12.56404, current_output: 12.56404,level: 27}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 18.84606, current_output: 18.84606,level: 27}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 6.28202, current_output: 6.28202,level: 27}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 12.56404, current_output: 12.56404,level: 27}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 12.56404, current_output: 12.56404,level: 27}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 12.56404, current_output: 12.56404,level: 27}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 41.71262, current_output: 41.71262,level: 27}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 17.58966, current_output: 17.58966,level: 27}]->(g);
CREATE (n: Building {id: 3702, name:"building_subsistence_pastureslevel", level:37});
MATCH (g: Goods{code: 7}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 18.3514375, current_output: 14.68115,level: 37}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 27.5271625, current_output: 22.02173,level: 37}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 9.1757125, current_output: 7.34057,level: 37}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 18.3514375, current_output: 14.68115,level: 37}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 18.3514375, current_output: 14.68115,level: 37}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 18.3514375, current_output: 14.68115,level: 37}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 60.926787499999996, current_output: 48.74143,level: 37}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 25.692012499999997, current_output: 20.55361,level: 37}]->(g);
CREATE (n: Building {id: 3703, name:"building_subsistence_rice_paddieslevel", level:324});
MATCH (g: Goods{code: 7}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 1778.4360000000001, current_output: 2045.2014,level: 324}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 242.514, current_output: 278.8911,level: 324}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 242.514, current_output: 278.8911,level: 324}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 323.35200000000003, current_output: 371.8548,level: 324}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 323.35200000000003, current_output: 371.8548,level: 324}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 323.35200000000003, current_output: 371.8548,level: 324}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 485.028, current_output: 557.7822,level: 324}]->(g);
CREATE (n: Building {id: 3704, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3704}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 20.812784499521307, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3704}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 15.648617720257466, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 145.84560887911508,level: 5}]->(g);
CREATE (n: Building {id: 3705, name:"building_subsistence_rice_paddieslevel", level:130});
MATCH (g: Goods{code: 7}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 713.57, current_output: 713.57,level: 130}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 97.305, current_output: 97.305,level: 130}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 97.305, current_output: 97.305,level: 130}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 129.74, current_output: 129.74,level: 130}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 129.74, current_output: 129.74,level: 130}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 129.74, current_output: 129.74,level: 130}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 194.61, current_output: 194.61,level: 130}]->(g);
CREATE (n: Building {id: 3706, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3706}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.487670699712785, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3706}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.389170632154478, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3706}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 87.50736532746905,level: 3}]->(g);
CREATE (n: Building {id: 3707, name:"building_subsistence_rice_paddieslevel", level:93});
MATCH (g: Goods{code: 7}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 481.39822, current_output: 481.39822,level: 93}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 65.64521, current_output: 65.64521,level: 93}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 65.64521, current_output: 65.64521,level: 93}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 87.52695, current_output: 87.52695,level: 93}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 87.52695, current_output: 87.52695,level: 93}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 87.52695, current_output: 87.52695,level: 93}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 131.29042, current_output: 131.29042,level: 93}]->(g);
CREATE (n: Building {id: 3708, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3708}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.97534139942557, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3708}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.778341264308956, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3708}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 175.0147306549381,level: 6}]->(g);
CREATE (n: Building {id: 3709, name:"building_subsistence_rice_paddieslevel", level:176});
MATCH (g: Goods{code: 7}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 966.064, current_output: 966.064,level: 176}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 131.736, current_output: 131.736,level: 176}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 131.736, current_output: 131.736,level: 176}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 175.648, current_output: 175.648,level: 176}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 175.648, current_output: 175.648,level: 176}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 175.648, current_output: 175.648,level: 176}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 263.472, current_output: 263.472,level: 176}]->(g);
CREATE (n: Building {id: 3710, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3710}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.487670699712785, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3710}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.389170632154478, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3710}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 87.50736532746905,level: 3}]->(g);
CREATE (n: Building {id: 3711, name:"building_subsistence_pastureslevel", level:19});
MATCH (g: Goods{code: 7}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 9.462087499999999, current_output: 7.56967,level: 19}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 14.193137499999999, current_output: 11.35451,level: 19}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 4.731037499999999, current_output: 3.78483,level: 19}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 9.462087499999999, current_output: 7.56967,level: 19}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 9.462087499999999, current_output: 7.56967,level: 19}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 9.462087499999999, current_output: 7.56967,level: 19}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 31.414149999999996, current_output: 25.13132,level: 19}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 13.246925, current_output: 10.59754,level: 19}]->(g);
CREATE (n: Building {id: 3712, name:"building_subsistence_pastureslevel", level:23});
MATCH (g: Goods{code: 7}), (b: Building{id:3712}) CREATE (b)-[r:Supply{max_supply: 11.453999999999999, current_output: 9.1632,level: 23}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3712}) CREATE (b)-[r:Supply{max_supply: 17.180999999999997, current_output: 13.7448,level: 23}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3712}) CREATE (b)-[r:Supply{max_supply: 5.726999999999999, current_output: 4.5816,level: 23}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3712}) CREATE (b)-[r:Supply{max_supply: 11.453999999999999, current_output: 9.1632,level: 23}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3712}) CREATE (b)-[r:Supply{max_supply: 11.453999999999999, current_output: 9.1632,level: 23}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3712}) CREATE (b)-[r:Supply{max_supply: 11.453999999999999, current_output: 9.1632,level: 23}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3712}) CREATE (b)-[r:Supply{max_supply: 38.027274999999996, current_output: 30.42182,level: 23}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3712}) CREATE (b)-[r:Supply{max_supply: 16.0356, current_output: 12.82848,level: 23}]->(g);
CREATE (n: Building {id: 3713, name:"building_subsistence_rice_paddieslevel", level:351});
MATCH (g: Goods{code: 7}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 1860.09466, current_output: 1860.09466,level: 351}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 253.64927, current_output: 253.64927,level: 351}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 253.64927, current_output: 253.64927,level: 351}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 338.19903, current_output: 338.19903,level: 351}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 338.19903, current_output: 338.19903,level: 351}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 338.19903, current_output: 338.19903,level: 351}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 507.29854, current_output: 507.29854,level: 351}]->(g);
CREATE (n: Building {id: 3714, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3714}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.650227599617047, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3714}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.518894176205974, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3714}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 116.67648710329209,level: 4}]->(g);
CREATE (n: Building {id: 3715, name:"building_subsistence_rice_paddieslevel", level:332});
MATCH (g: Goods{code: 7}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 1822.348, current_output: 1822.348,level: 332}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 248.502, current_output: 248.502,level: 332}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 248.502, current_output: 248.502,level: 332}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 331.336, current_output: 331.336,level: 332}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 331.336, current_output: 331.336,level: 332}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 331.336, current_output: 331.336,level: 332}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 497.004, current_output: 497.004,level: 332}]->(g);
CREATE (n: Building {id: 3716, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3716}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.97534139942557, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3716}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.778341264308956, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3716}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 175.0147306549381,level: 6}]->(g);
CREATE (n: Building {id: 3717, name:"building_subsistence_rice_paddieslevel", level:75});
MATCH (g: Goods{code: 7}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 298.188, current_output: 298.188,level: 75}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 40.662, current_output: 40.662,level: 75}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 40.662, current_output: 40.662,level: 75}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 54.216, current_output: 54.216,level: 75}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 54.216, current_output: 54.216,level: 75}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 54.216, current_output: 54.216,level: 75}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 81.324, current_output: 81.324,level: 75}]->(g);
CREATE (n: Building {id: 3718, name:"building_subsistence_farmslevel", level:60});
MATCH (g: Goods{code: 7}), (b: Building{id:3718}) CREATE (b)-[r:Supply{max_supply: 156.6864, current_output: 156.6864,level: 60}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3718}) CREATE (b)-[r:Supply{max_supply: 26.1144, current_output: 26.1144,level: 60}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3718}) CREATE (b)-[r:Supply{max_supply: 26.1144, current_output: 26.1144,level: 60}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3718}) CREATE (b)-[r:Supply{max_supply: 26.1144, current_output: 26.1144,level: 60}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3718}) CREATE (b)-[r:Supply{max_supply: 26.1144, current_output: 26.1144,level: 60}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3718}) CREATE (b)-[r:Supply{max_supply: 26.1144, current_output: 26.1144,level: 60}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3718}) CREATE (b)-[r:Supply{max_supply: 36.56016, current_output: 36.56016,level: 60}]->(g);
CREATE (n: Building {id: 3719, name:"building_subsistence_rice_paddieslevel", level:197});
MATCH (g: Goods{code: 7}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 1081.333, current_output: 1081.333,level: 197}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 147.4545, current_output: 147.4545,level: 197}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 147.4545, current_output: 147.4545,level: 197}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 196.606, current_output: 196.606,level: 197}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 196.606, current_output: 196.606,level: 197}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 196.606, current_output: 196.606,level: 197}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 294.909, current_output: 294.909,level: 197}]->(g);
CREATE (n: Building {id: 3720, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3720}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.325113799808523, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3720}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.259447088102987, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3720}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 58.338243551646045,level: 2}]->(g);
CREATE (n: Building {id: 3721, name:"building_subsistence_farmslevel", level:118});
MATCH (g: Goods{code: 7}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 352.584, current_output: 352.584,level: 118}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 58.764, current_output: 58.764,level: 118}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 58.764, current_output: 58.764,level: 118}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 58.764, current_output: 58.764,level: 118}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 58.764, current_output: 58.764,level: 118}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 58.764, current_output: 58.764,level: 118}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 82.2696, current_output: 82.2696,level: 118}]->(g);
CREATE (n: Building {id: 3722, name:"building_subsistence_rice_paddieslevel", level:365});
MATCH (g: Goods{code: 7}), (b: Building{id:3722}) CREATE (b)-[r:Supply{max_supply: 1937.5988434782612, current_output: 2228.23867,level: 365}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3722}) CREATE (b)-[r:Supply{max_supply: 264.2180173913044, current_output: 303.85072,level: 365}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3722}) CREATE (b)-[r:Supply{max_supply: 264.2180173913044, current_output: 303.85072,level: 365}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3722}) CREATE (b)-[r:Supply{max_supply: 352.29069565217395, current_output: 405.1343,level: 365}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3722}) CREATE (b)-[r:Supply{max_supply: 352.29069565217395, current_output: 405.1343,level: 365}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3722}) CREATE (b)-[r:Supply{max_supply: 352.29069565217395, current_output: 405.1343,level: 365}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3722}) CREATE (b)-[r:Supply{max_supply: 528.4360434782609, current_output: 607.70145,level: 365}]->(g);
CREATE (n: Building {id: 3723, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3723}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.325113799808523, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3723}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.259447088102987, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3723}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 58.338243551646045,level: 2}]->(g);
CREATE (n: Building {id: 3724, name:"building_subsistence_rice_paddieslevel", level:197});
MATCH (g: Goods{code: 7}), (b: Building{id:3724}) CREATE (b)-[r:Supply{max_supply: 1081.333, current_output: 1081.333,level: 197}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3724}) CREATE (b)-[r:Supply{max_supply: 147.4545, current_output: 147.4545,level: 197}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3724}) CREATE (b)-[r:Supply{max_supply: 147.4545, current_output: 147.4545,level: 197}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3724}) CREATE (b)-[r:Supply{max_supply: 196.606, current_output: 196.606,level: 197}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3724}) CREATE (b)-[r:Supply{max_supply: 196.606, current_output: 196.606,level: 197}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3724}) CREATE (b)-[r:Supply{max_supply: 196.606, current_output: 196.606,level: 197}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3724}) CREATE (b)-[r:Supply{max_supply: 294.909, current_output: 294.909,level: 197}]->(g);
CREATE (n: Building {id: 3725, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3725}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.162556899904262, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3725}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.1297235440514934, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3725}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 29.169121775823022,level: 1}]->(g);
CREATE (n: Building {id: 3726, name:"building_subsistence_rice_paddieslevel", level:144});
MATCH (g: Goods{code: 7}), (b: Building{id:3726}) CREATE (b)-[r:Supply{max_supply: 790.416, current_output: 790.416,level: 144}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3726}) CREATE (b)-[r:Supply{max_supply: 107.784, current_output: 107.784,level: 144}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3726}) CREATE (b)-[r:Supply{max_supply: 107.784, current_output: 107.784,level: 144}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3726}) CREATE (b)-[r:Supply{max_supply: 143.712, current_output: 143.712,level: 144}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3726}) CREATE (b)-[r:Supply{max_supply: 143.712, current_output: 143.712,level: 144}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3726}) CREATE (b)-[r:Supply{max_supply: 143.712, current_output: 143.712,level: 144}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3726}) CREATE (b)-[r:Supply{max_supply: 215.568, current_output: 215.568,level: 144}]->(g);
CREATE (n: Building {id: 3727, name:"building_subsistence_rice_paddieslevel", level:245});
MATCH (g: Goods{code: 7}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 1294.8127478260872, current_output: 1489.03466,level: 245}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 176.56537391304352, current_output: 203.05018,level: 245}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 176.56537391304352, current_output: 203.05018,level: 245}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 235.4204956521739, current_output: 270.73357,level: 245}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 235.4204956521739, current_output: 270.73357,level: 245}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 235.4204956521739, current_output: 270.73357,level: 245}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 353.13074782608703, current_output: 406.10036,level: 245}]->(g);
CREATE (n: Building {id: 3728, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3728}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.162556899904262, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3728}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.1297235440514934, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3728}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 29.169121775823022,level: 1}]->(g);
CREATE (n: Building {id: 3729, name:"building_subsistence_farmslevel", level:55});
MATCH (g: Goods{code: 7}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 164.34, current_output: 164.34,level: 55}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 27.39, current_output: 27.39,level: 55}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 27.39, current_output: 27.39,level: 55}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 27.39, current_output: 27.39,level: 55}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 27.39, current_output: 27.39,level: 55}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 27.39, current_output: 27.39,level: 55}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 38.346, current_output: 38.346,level: 55}]->(g);
CREATE (n: Building {id: 3730, name:"building_subsistence_rice_paddieslevel", level:480});
MATCH (g: Goods{code: 7}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 2563.1496, current_output: 2563.1496,level: 480}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 349.5204, current_output: 349.5204,level: 480}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 349.5204, current_output: 349.5204,level: 480}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 466.0272, current_output: 466.0272,level: 480}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 466.0272, current_output: 466.0272,level: 480}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 466.0272, current_output: 466.0272,level: 480}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 699.0408, current_output: 699.0408,level: 480}]->(g);
CREATE (n: Building {id: 3731, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3731}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.650227599617047, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3731}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.518894176205974, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3731}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 116.67648710329209,level: 4}]->(g);
CREATE (n: Building {id: 3732, name:"building_subsistence_rice_paddieslevel", level:266});
MATCH (g: Goods{code: 7}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 1460.074, current_output: 1460.074,level: 266}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 199.101, current_output: 199.101,level: 266}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 199.101, current_output: 199.101,level: 266}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 265.468, current_output: 265.468,level: 266}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 265.468, current_output: 265.468,level: 266}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 265.468, current_output: 265.468,level: 266}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 398.202, current_output: 398.202,level: 266}]->(g);
CREATE (n: Building {id: 3733, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3733}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.97534139942557, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3733}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.778341264308956, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3733}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 175.0147306549381,level: 6}]->(g);
CREATE (n: Building {id: 3735, name:"building_subsistence_rice_paddieslevel", level:375});
MATCH (g: Goods{code: 7}), (b: Building{id:3735}) CREATE (b)-[r:Supply{max_supply: 2058.375, current_output: 2058.375,level: 375}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3735}) CREATE (b)-[r:Supply{max_supply: 280.6875, current_output: 280.6875,level: 375}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3735}) CREATE (b)-[r:Supply{max_supply: 280.6875, current_output: 280.6875,level: 375}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3735}) CREATE (b)-[r:Supply{max_supply: 374.25, current_output: 374.25,level: 375}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3735}) CREATE (b)-[r:Supply{max_supply: 374.25, current_output: 374.25,level: 375}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3735}) CREATE (b)-[r:Supply{max_supply: 374.25, current_output: 374.25,level: 375}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3735}) CREATE (b)-[r:Supply{max_supply: 561.375, current_output: 561.375,level: 375}]->(g);
CREATE (n: Building {id: 3736, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3736}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.650227599617047, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3736}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.518894176205974, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3736}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 116.67648710329209,level: 4}]->(g);
CREATE (n: Building {id: 3737, name:"building_subsistence_rice_paddieslevel", level:231});
MATCH (g: Goods{code: 7}), (b: Building{id:3737}) CREATE (b)-[r:Supply{max_supply: 1170.88009, current_output: 1170.88009,level: 231}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3737}) CREATE (b)-[r:Supply{max_supply: 159.66546, current_output: 159.66546,level: 231}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3737}) CREATE (b)-[r:Supply{max_supply: 159.66546, current_output: 159.66546,level: 231}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3737}) CREATE (b)-[r:Supply{max_supply: 212.88729, current_output: 212.88729,level: 231}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3737}) CREATE (b)-[r:Supply{max_supply: 212.88729, current_output: 212.88729,level: 231}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3737}) CREATE (b)-[r:Supply{max_supply: 212.88729, current_output: 212.88729,level: 231}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3737}) CREATE (b)-[r:Supply{max_supply: 319.33093, current_output: 319.33093,level: 231}]->(g);
CREATE (n: Building {id: 3738, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3738}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.650227599617047, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3738}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.518894176205974, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3738}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 116.67648710329209,level: 4}]->(g);
CREATE (n: Building {id: 3739, name:"building_subsistence_rice_paddieslevel", level:345});
MATCH (g: Goods{code: 7}), (b: Building{id:3739}) CREATE (b)-[r:Supply{max_supply: 1376.6742, current_output: 1376.6742,level: 345}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3739}) CREATE (b)-[r:Supply{max_supply: 187.7283, current_output: 187.7283,level: 345}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3739}) CREATE (b)-[r:Supply{max_supply: 187.7283, current_output: 187.7283,level: 345}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3739}) CREATE (b)-[r:Supply{max_supply: 250.3044, current_output: 250.3044,level: 345}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3739}) CREATE (b)-[r:Supply{max_supply: 250.3044, current_output: 250.3044,level: 345}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3739}) CREATE (b)-[r:Supply{max_supply: 250.3044, current_output: 250.3044,level: 345}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3739}) CREATE (b)-[r:Supply{max_supply: 375.4566, current_output: 375.4566,level: 345}]->(g);
CREATE (n: Building {id: 3740, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3740}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.97534139942557, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3740}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.778341264308956, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3740}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 175.0147306549381,level: 6}]->(g);
CREATE (n: Building {id: 3741, name:"building_subsistence_rice_paddieslevel", level:257});
MATCH (g: Goods{code: 7}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 1375.34963, current_output: 1375.34963,level: 257}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 187.54767, current_output: 187.54767,level: 257}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 187.54767, current_output: 187.54767,level: 257}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 250.06357, current_output: 250.06357,level: 257}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 250.06357, current_output: 250.06357,level: 257}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 250.06357, current_output: 250.06357,level: 257}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 375.09535, current_output: 375.09535,level: 257}]->(g);
CREATE (n: Building {id: 3742, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3742}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.650227599617047, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3742}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.518894176205974, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 116.67648710329209,level: 4}]->(g);
CREATE (n: Building {id: 3743, name:"building_subsistence_rice_paddieslevel", level:488});
MATCH (g: Goods{code: 7}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 2678.632, current_output: 2678.632,level: 488}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 365.268, current_output: 365.268,level: 488}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 365.268, current_output: 365.268,level: 488}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 487.024, current_output: 487.024,level: 488}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 487.024, current_output: 487.024,level: 488}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 487.024, current_output: 487.024,level: 488}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 730.536, current_output: 730.536,level: 488}]->(g);
CREATE (n: Building {id: 3744, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3744}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.650227599617047, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3744}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.518894176205974, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3744}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 116.67648710329209,level: 4}]->(g);
CREATE (n: Building {id: 3745, name:"building_subsistence_rice_paddieslevel", level:186});
MATCH (g: Goods{code: 7}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 1005.99774, current_output: 1005.99774,level: 186}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 137.18151, current_output: 137.18151,level: 186}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 137.18151, current_output: 137.18151,level: 186}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 182.90868, current_output: 182.90868,level: 186}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 182.90868, current_output: 182.90868,level: 186}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 182.90868, current_output: 182.90868,level: 186}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 274.36302, current_output: 274.36302,level: 186}]->(g);
CREATE (n: Building {id: 3746, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3746}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 29.13789829932983, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3746}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 21.908064808360454, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3746}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 204.1838524307611,level: 7}]->(g);
CREATE (n: Building {id: 3747, name:"building_subsistence_rice_paddieslevel", level:568});
MATCH (g: Goods{code: 7}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 3117.752, current_output: 3117.752,level: 568}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 425.148, current_output: 425.148,level: 568}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 425.148, current_output: 425.148,level: 568}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 566.864, current_output: 566.864,level: 568}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 566.864, current_output: 566.864,level: 568}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 566.864, current_output: 566.864,level: 568}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 850.296, current_output: 850.296,level: 568}]->(g);
CREATE (n: Building {id: 3748, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3748}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 20.812784499521307, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3748}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 15.648617720257466, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3748}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 145.84560887911508,level: 5}]->(g);
CREATE (n: Building {id: 3749, name:"building_subsistence_rice_paddieslevel", level:528});
MATCH (g: Goods{code: 7}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 2898.192, current_output: 2898.192,level: 528}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 395.208, current_output: 395.208,level: 528}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 395.208, current_output: 395.208,level: 528}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 526.944, current_output: 526.944,level: 528}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 526.944, current_output: 526.944,level: 528}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 526.944, current_output: 526.944,level: 528}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 790.416, current_output: 790.416,level: 528}]->(g);
CREATE (n: Building {id: 3750, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3750}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 29.13789829932983, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3750}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 21.908064808360454, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 204.1838524307611,level: 7}]->(g);
CREATE (n: Building {id: 3751, name:"building_subsistence_rice_paddieslevel", level:249});
MATCH (g: Goods{code: 7}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 1358.51661, current_output: 1358.51661,level: 249}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 185.25226, current_output: 185.25226,level: 249}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 185.25226, current_output: 185.25226,level: 249}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 247.00302, current_output: 247.00302,level: 249}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 247.00302, current_output: 247.00302,level: 249}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 247.00302, current_output: 247.00302,level: 249}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 370.50453, current_output: 370.50453,level: 249}]->(g);
CREATE (n: Building {id: 3752, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3752}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.487670699712785, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3752}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.389170632154478, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3752}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 87.50736532746905,level: 3}]->(g);
CREATE (n: Building {id: 3753, name:"building_subsistence_rice_paddieslevel", level:304});
MATCH (g: Goods{code: 7}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 1652.72184, current_output: 1652.72184,level: 304}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 225.37116, current_output: 225.37116,level: 304}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 225.37116, current_output: 225.37116,level: 304}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 300.49488, current_output: 300.49488,level: 304}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 300.49488, current_output: 300.49488,level: 304}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 300.49488, current_output: 300.49488,level: 304}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 450.74232, current_output: 450.74232,level: 304}]->(g);
CREATE (n: Building {id: 3754, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3754}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.97534139942557, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3754}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.778341264308956, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3754}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 175.0147306549381,level: 6}]->(g);
CREATE (n: Building {id: 3755, name:"building_subsistence_rice_paddieslevel", level:300});
MATCH (g: Goods{code: 7}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 1646.7, current_output: 1646.7,level: 300}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 224.55, current_output: 224.55,level: 300}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 224.55, current_output: 224.55,level: 300}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 299.4, current_output: 299.4,level: 300}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 299.4, current_output: 299.4,level: 300}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 299.4, current_output: 299.4,level: 300}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 449.1, current_output: 449.1,level: 300}]->(g);
CREATE (n: Building {id: 3756, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3756}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 29.13789829932983, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3756}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 21.908064808360454, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3756}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 204.1838524307611,level: 7}]->(g);
CREATE (n: Building {id: 3757, name:"building_subsistence_rice_paddieslevel", level:316});
MATCH (g: Goods{code: 7}), (b: Building{id:3757}) CREATE (b)-[r:Supply{max_supply: 1734.524, current_output: 1734.524,level: 316}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3757}) CREATE (b)-[r:Supply{max_supply: 236.526, current_output: 236.526,level: 316}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3757}) CREATE (b)-[r:Supply{max_supply: 236.526, current_output: 236.526,level: 316}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3757}) CREATE (b)-[r:Supply{max_supply: 315.368, current_output: 315.368,level: 316}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3757}) CREATE (b)-[r:Supply{max_supply: 315.368, current_output: 315.368,level: 316}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3757}) CREATE (b)-[r:Supply{max_supply: 315.368, current_output: 315.368,level: 316}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3757}) CREATE (b)-[r:Supply{max_supply: 473.052, current_output: 473.052,level: 316}]->(g);
CREATE (n: Building {id: 3758, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3758}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.325113799808523, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3758}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.259447088102987, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 58.338243551646045,level: 2}]->(g);
CREATE (n: Building {id: 3759, name:"building_subsistence_rice_paddieslevel", level:72});
MATCH (g: Goods{code: 7}), (b: Building{id:3759}) CREATE (b)-[r:Supply{max_supply: 356.22972, current_output: 356.22972,level: 72}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3759}) CREATE (b)-[r:Supply{max_supply: 48.57678, current_output: 48.57678,level: 72}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3759}) CREATE (b)-[r:Supply{max_supply: 48.57678, current_output: 48.57678,level: 72}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3759}) CREATE (b)-[r:Supply{max_supply: 64.76904, current_output: 64.76904,level: 72}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3759}) CREATE (b)-[r:Supply{max_supply: 64.76904, current_output: 64.76904,level: 72}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3759}) CREATE (b)-[r:Supply{max_supply: 64.76904, current_output: 64.76904,level: 72}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3759}) CREATE (b)-[r:Supply{max_supply: 97.15356, current_output: 97.15356,level: 72}]->(g);
CREATE (n: Building {id: 3777, name:"building_subsistence_pastureslevel", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:3777}) CREATE (b)-[r:Supply{max_supply: 5.6336, current_output: 5.6336,level: 16}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3777}) CREATE (b)-[r:Supply{max_supply: 8.4504, current_output: 8.4504,level: 16}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3777}) CREATE (b)-[r:Supply{max_supply: 2.8168, current_output: 2.8168,level: 16}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3777}) CREATE (b)-[r:Supply{max_supply: 5.6336, current_output: 5.6336,level: 16}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3777}) CREATE (b)-[r:Supply{max_supply: 5.6336, current_output: 5.6336,level: 16}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3777}) CREATE (b)-[r:Supply{max_supply: 5.6336, current_output: 5.6336,level: 16}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3777}) CREATE (b)-[r:Supply{max_supply: 18.70355, current_output: 18.70355,level: 16}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3777}) CREATE (b)-[r:Supply{max_supply: 7.88704, current_output: 7.88704,level: 16}]->(g);
CREATE (n: Building {id: 3785, name:"building_subsistence_pastureslevel", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:3785}) CREATE (b)-[r:Supply{max_supply: 6.3155, current_output: 6.3155,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3785}) CREATE (b)-[r:Supply{max_supply: 9.47325, current_output: 9.47325,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3785}) CREATE (b)-[r:Supply{max_supply: 3.15775, current_output: 3.15775,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3785}) CREATE (b)-[r:Supply{max_supply: 6.3155, current_output: 6.3155,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3785}) CREATE (b)-[r:Supply{max_supply: 6.3155, current_output: 6.3155,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3785}) CREATE (b)-[r:Supply{max_supply: 6.3155, current_output: 6.3155,level: 50}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3785}) CREATE (b)-[r:Supply{max_supply: 20.96746, current_output: 20.96746,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3785}) CREATE (b)-[r:Supply{max_supply: 8.8417, current_output: 8.8417,level: 50}]->(g);
CREATE (n: Building {id: 3795, name:"building_subsistence_farmslevel", level:11});
MATCH (g: Goods{code: 7}), (b: Building{id:3795}) CREATE (b)-[r:Supply{max_supply: 7.76688, current_output: 7.76688,level: 11}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3795}) CREATE (b)-[r:Supply{max_supply: 1.29448, current_output: 1.29448,level: 11}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3795}) CREATE (b)-[r:Supply{max_supply: 1.29448, current_output: 1.29448,level: 11}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3795}) CREATE (b)-[r:Supply{max_supply: 1.29448, current_output: 1.29448,level: 11}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3795}) CREATE (b)-[r:Supply{max_supply: 1.29448, current_output: 1.29448,level: 11}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3795}) CREATE (b)-[r:Supply{max_supply: 1.29448, current_output: 1.29448,level: 11}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3795}) CREATE (b)-[r:Supply{max_supply: 1.81227, current_output: 1.81227,level: 11}]->(g);
CREATE (n: Building {id: 3796, name:"building_subsistence_farmslevel", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3796}) CREATE (b)-[r:Supply{max_supply: 4.482, current_output: 4.482,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3796}) CREATE (b)-[r:Supply{max_supply: 0.747, current_output: 0.747,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3796}) CREATE (b)-[r:Supply{max_supply: 0.747, current_output: 0.747,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3796}) CREATE (b)-[r:Supply{max_supply: 0.747, current_output: 0.747,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3796}) CREATE (b)-[r:Supply{max_supply: 0.747, current_output: 0.747,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3796}) CREATE (b)-[r:Supply{max_supply: 0.747, current_output: 0.747,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3796}) CREATE (b)-[r:Supply{max_supply: 1.0458, current_output: 1.0458,level: 40}]->(g);
CREATE (n: Building {id: 4053, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4054, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4136, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4137, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4513, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4514, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4515, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4516, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4517, name:"building_conscription_centerlevel", level:22});
CREATE (n: Building {id: 4518, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4519, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4520, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4521, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4522, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4523, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4524, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4525, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4526, name:"building_conscription_centerlevel", level:15});
CREATE (n: Building {id: 4527, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4528, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4529, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4530, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4531, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4532, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4533, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4534, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4535, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4536, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4537, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4538, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4539, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4540, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4541, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4542, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4543, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4544, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4545, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4546, name:"building_conscription_centerlevel", level:14});
