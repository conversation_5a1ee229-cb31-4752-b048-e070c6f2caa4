CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:21.861486735238223, pop_demand:313.565158932364});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:21.860834514862386, pop_demand:28.54062538630645});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:17.176975927525675, pop_demand:62.65769624190795});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:25.68938667923964, pop_demand:11.86945625809209});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:29.629541451580135, pop_demand:66.35033533035329});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:25.371893481158594, pop_demand:75.77119669793954});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:16.179967449290437, pop_demand:20.63766333333334});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:3.1028621428571435});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:25.92836453199125, pop_demand:4.078811349747396});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:25.930932354437537, pop_demand:24.47627816669305});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:28.777859962834842, pop_demand:30.359354620065904});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:26.175244230232188, pop_demand:21.938154999999995});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:10.24591511058603, pop_demand:11.012657305911894});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:31.096885149991223, pop_demand:70.74207528495057});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:28.960046666666667});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:3.6200058333333334});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 1711, name:"building_coffee_plantationlevel", level:1});
MATCH (g: Goods{code: 41}), (b: Building{id:1711}) CREATE (b)-[r:Supply{max_supply: 19.92, current_output: 19.92,level: 1}]->(g);
CREATE (n: Building {id: 1712, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1712}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 1777, name:"building_government_administrationlevel", level:2});
CREATE (n: Building {id: 1778, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1778}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1779, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1779}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 1780, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1780}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
CREATE (n: Building {id: 1781, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1781}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1781}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 1782, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1782}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1783, name:"building_government_administrationlevel", level:1});
CREATE (n: Building {id: 1784, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1784}) CREATE (b)-[r:Supply{max_supply: 34.86, current_output: 34.86,level: 1}]->(g);
CREATE (n: Building {id: 1785, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1785}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1785}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 1786, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:1786}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1786}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1787, name:"building_government_administrationlevel", level:2});
CREATE (n: Building {id: 1788, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1788}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 49.27331741238962, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1788}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 1}]->(g);
CREATE (n: Building {id: 1789, name:"building_sugar_plantationlevel", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1789}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 1790, name:"building_cotton_plantationlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1790}) CREATE (b)-[r:Supply{max_supply: 79.67999999999999, current_output: 100.3968,level: 2}]->(g);
CREATE (n: Building {id: 1791, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1791}) CREATE (b)-[r:Supply{max_supply: 69.72, current_output: 70.4172,level: 2}]->(g);
CREATE (n: Building {id: 1792, name:"building_barrackslevel", level:6});
MATCH (g: Goods{code: 1}), (b: Building{id:1792}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.0, level: 6}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1792}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.0, level: 6}]->(b);
CREATE (n: Building {id: 1793, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1793}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 24.63665870619481, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1793}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 37.24245328304145, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1793}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 72.93183996228109,level: 2}]->(g);
CREATE (n: Building {id: 1794, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1794}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 1795, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1795}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 1796, name:"building_coffee_plantationlevel", level:2});
MATCH (g: Goods{code: 41}), (b: Building{id:1796}) CREATE (b)-[r:Supply{max_supply: 39.839999999999996, current_output: 40.2384,level: 2}]->(g);
CREATE (n: Building {id: 1797, name:"building_maize_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1797}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
CREATE (n: Building {id: 1798, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:1798}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1798}) CREATE (g)-[r:Demand{max_demand: 1.5, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 3000, name:"building_subsistence_farmslevel", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 10.9782, current_output: 10.9782,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 2.19564, current_output: 2.19564,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 2.19564, current_output: 2.19564,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 2.19564, current_output: 2.19564,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 2.19564, current_output: 2.19564,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 2.19564, current_output: 2.19564,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 3.07389, current_output: 3.07389,level: 18}]->(g);
CREATE (n: Building {id: 3136, name:"building_subsistence_farmslevel", level:116});
MATCH (g: Goods{code: 7}), (b: Building{id:3136}) CREATE (b)-[r:Supply{max_supply: 39.9388, current_output: 39.9388,level: 116}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3136}) CREATE (b)-[r:Supply{max_supply: 7.98776, current_output: 7.98776,level: 116}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3136}) CREATE (b)-[r:Supply{max_supply: 7.98776, current_output: 7.98776,level: 116}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3136}) CREATE (b)-[r:Supply{max_supply: 7.98776, current_output: 7.98776,level: 116}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3136}) CREATE (b)-[r:Supply{max_supply: 7.98776, current_output: 7.98776,level: 116}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3136}) CREATE (b)-[r:Supply{max_supply: 7.98776, current_output: 7.98776,level: 116}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3136}) CREATE (b)-[r:Supply{max_supply: 11.18286, current_output: 11.18286,level: 116}]->(g);
CREATE (n: Building {id: 3137, name:"building_subsistence_farmslevel", level:58});
MATCH (g: Goods{code: 7}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 8.0156, current_output: 8.0156,level: 58}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 1.60312, current_output: 1.60312,level: 58}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 1.60312, current_output: 1.60312,level: 58}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 1.60312, current_output: 1.60312,level: 58}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 1.60312, current_output: 1.60312,level: 58}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 1.60312, current_output: 1.60312,level: 58}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 2.24436, current_output: 2.24436,level: 58}]->(g);
CREATE (n: Building {id: 3138, name:"building_subsistence_rice_paddieslevel", level:45});
MATCH (g: Goods{code: 7}), (b: Building{id:3138}) CREATE (b)-[r:Supply{max_supply: 29.59335, current_output: 29.59335,level: 45}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3138}) CREATE (b)-[r:Supply{max_supply: 4.93222, current_output: 4.93222,level: 45}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3138}) CREATE (b)-[r:Supply{max_supply: 4.93222, current_output: 4.93222,level: 45}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3138}) CREATE (b)-[r:Supply{max_supply: 6.5763, current_output: 6.5763,level: 45}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3138}) CREATE (b)-[r:Supply{max_supply: 6.5763, current_output: 6.5763,level: 45}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3138}) CREATE (b)-[r:Supply{max_supply: 6.5763, current_output: 6.5763,level: 45}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3138}) CREATE (b)-[r:Supply{max_supply: 9.86445, current_output: 9.86445,level: 45}]->(g);
CREATE (n: Building {id: 3139, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3139}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.1035377735867873, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3139}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 15.517688867933938,level: 1}]->(g);
CREATE (n: Building {id: 3140, name:"building_subsistence_farmslevel", level:74});
MATCH (g: Goods{code: 7}), (b: Building{id:3140}) CREATE (b)-[r:Supply{max_supply: 18.97175, current_output: 18.97175,level: 74}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3140}) CREATE (b)-[r:Supply{max_supply: 3.79435, current_output: 3.79435,level: 74}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3140}) CREATE (b)-[r:Supply{max_supply: 3.79435, current_output: 3.79435,level: 74}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3140}) CREATE (b)-[r:Supply{max_supply: 3.79435, current_output: 3.79435,level: 74}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3140}) CREATE (b)-[r:Supply{max_supply: 3.79435, current_output: 3.79435,level: 74}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3140}) CREATE (b)-[r:Supply{max_supply: 3.79435, current_output: 3.79435,level: 74}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3140}) CREATE (b)-[r:Supply{max_supply: 5.31209, current_output: 5.31209,level: 74}]->(g);
CREATE (n: Building {id: 3150, name:"building_subsistence_farmslevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 1.5195, current_output: 1.5195,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 0.3039, current_output: 0.3039,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 0.3039, current_output: 0.3039,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 0.3039, current_output: 0.3039,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 0.3039, current_output: 0.3039,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 0.3039, current_output: 0.3039,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 0.42546, current_output: 0.42546,level: 4}]->(g);
CREATE (n: Building {id: 3991, name:"building_trade_centerlevel", level:17});
CREATE (n: Building {id: 3992, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3992}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.1035377735867873, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3992}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 15.517688867933938,level: 1}]->(g);
CREATE (n: Building {id: 4174, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4175, name:"building_conscription_centerlevel", level:1});
