CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:54.230150488161144, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:93.44478555105988, pop_demand:5.800394089629669});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:119.43013740475136, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:108.40781670629057, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:29.480765445212718, pop_demand:3540.1971368825198});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:25.111925769652366, pop_demand:734.062467421852});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:8.815581073326653, pop_demand:98.72884582947043});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:30.391334775115205, pop_demand:236.33682502256198});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:43.46382331921002, pop_demand:317.0178305193506});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:35.09281980051205, pop_demand:886.1412535345753});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:40.79877051911631, pop_demand:797.7157216911846});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:49.66898834491378, pop_demand:65.30744045294597});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:35.75016491181814, pop_demand:686.8191073647077});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:45.79066179420722, pop_demand:229.58251666666652});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:77.64780506251216, pop_demand:35.52024268329789});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:44.33169075019811, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:77.17361201684844, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:48.49870551155453, pop_demand:392.08191654604303});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:66.8574354212345, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:61.53519323723371, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:66.5541528208537, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:57.17942175251663, pop_demand:29.377785914451263});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:96.18507624959548, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:81.78874048823471, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:49.11238997202486, pop_demand:40.40761541705621});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:48.66856578526014, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:64.24046794880391, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:76.7267436870413, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:103.39841634867452, pop_demand:132.03971820598068});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:43.48090688116278, pop_demand:190.2050666397459});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:47.01889642592856, pop_demand:199.66748161419846});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:34.58669262028266, pop_demand:480.4647695622632});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:45.20899898387344, pop_demand:177.23916249394892});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:0.624691591833299});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:87.5, pop_demand:51.44670130523545});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:40.76119059194121, pop_demand:35.45529207982958});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:36.15505306140504, pop_demand:892.5121972033039});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:119.05154467528402});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:90.84915805571737, pop_demand:295.27985491773865});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:323.32752583084147, pop_demand:0.016551619424030183});
CREATE (n: Building {id: 16777662, name:"building_conscription_center", level:8});
CREATE (n: Building {id: 1318, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1318}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 5.450279525249231, level: 2}]->(b);
CREATE (n: Building {id: 1319, name:"building_white_house", level:1});
CREATE (n: Building {id: 1320, name:"building_capitol_hill", level:1});
CREATE (n: Building {id: 1321, name:"building_port", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1321}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.277336717567289, level: 2}]->(b);
CREATE (n: Building {id: 1322, name:"building_sugar_plantation", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1322}) CREATE (b)-[r:Supply{max_supply: 29.004, current_output: 29.004,level: 1}]->(g);
CREATE (n: Building {id: 1323, name:"building_cotton_plantation", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1323}) CREATE (b)-[r:Supply{max_supply: 78.584, current_output: 99.01584,level: 2}]->(g);
CREATE (n: Building {id: 1324, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:1324}) CREATE (g)-[r:Demand{max_demand: 199.99999999999997, current_input: 27.251397626246153, level: 10}]->(b);
CREATE (n: Building {id: 1325, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1325}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 157.47547218840407, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1325}) CREATE (g)-[r:Demand{max_demand: 79.9992, current_input: 25.498573650595446, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1325}) CREATE (g)-[r:Demand{max_demand: 99.999, current_input: 11.536416936193708, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1325}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 4.13353056407102, level: 2}]->(b);
CREATE (n: Building {id: 1326, name:"building_university", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:1326}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 4.087709643936924, level: 3}]->(b);
CREATE (n: Building {id: 1327, name:"building_iron_mine", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:1327}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 7.762041575548855, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1327}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 8.267143799580035, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1327}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 32.05837075025778,level: 4}]->(g);
CREATE (n: Building {id: 1328, name:"building_paper_mills", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1328}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 19.124121479161378, level: 2}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:1328}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.065400689900013, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1328}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 43.540377473671654,level: 2}]->(g);
CREATE (n: Building {id: 1329, name:"building_textile_mills", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:1329}) CREATE (g)-[r:Demand{max_demand: 164.19, current_input: 646.4039085044366, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1329}) CREATE (g)-[r:Demand{max_demand: 20.52375, current_input: 17.560332065520715, level: 5}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1329}) CREATE (b)-[r:Supply{max_supply: 246.28499999999997, current_output: 228.50449239312425,level: 5}]->(g);
CREATE (n: Building {id: 1330, name:"building_furniture_manufacturies", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1330}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 78.73852347943682, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1330}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 9.562060739580689, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1330}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 3.7600531887683784, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1330}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.0667859498950087, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1330}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 33.01498118535606,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1330}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 20.63436324084754,level: 2}]->(g);
CREATE (n: Building {id: 1331, name:"building_tooling_workshops", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:1331}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 47.81030369790344, level: 5}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:1331}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 16.978701173404488, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1331}) CREATE (b)-[r:Supply{max_supply: 400.0, current_output: 97.70447394401356,level: 5}]->(g);
CREATE (n: Building {id: 1332, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1332}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.0667859498950087, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1332}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 14.46750164926506,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1332}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 5.166964874737522,level: 2}]->(g);
CREATE (n: Building {id: 1333, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:1333}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.801010736584032, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1333}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.6200357849685026, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1333}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 23.60475864570618,level: 3}]->(g);
CREATE (n: Building {id: 1334, name:"building_vineyard_plantation", level:3});
MATCH (g: Goods{code: 39}), (b: Building{id:1334}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 61.2,level: 3}]->(g);
CREATE (n: Building {id: 1335, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1335}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.277336717567289, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1335}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 62.77336717567289,level: 2}]->(g);
CREATE (n: Building {id: 1336, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1336}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.416005076350933, level: 3}]->(b);
CREATE (n: Building {id: 1337, name:"building_central_park", level:1});
CREATE (n: Building {id: 1338, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1338}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 5.450279525249231, level: 2}]->(b);
CREATE (n: Building {id: 1339, name:"building_arms_industry", level:3});
MATCH (g: Goods{code: 24}), (b: Building{id:1339}) CREATE (g)-[r:Demand{max_demand: 21.956098039215686, current_input: 2.532972342446798, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1339}) CREATE (g)-[r:Demand{max_demand: 21.956098039215686, current_input: 2.7518698815088025, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1339}) CREATE (b)-[r:Supply{max_supply: 65.86829411764705, current_output: 7.927263335933401,level: 3}]->(g);
CREATE (n: Building {id: 1340, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:1340}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.8810207877744274, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1340}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.1335718997900175, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1340}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 16.02918537512889,level: 2}]->(g);
CREATE (n: Building {id: 1341, name:"building_maize_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:1341}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 4.668351227640053, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1341}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0333929749475044, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1341}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 39.3412644095103,level: 5}]->(g);
CREATE (n: Building {id: 1342, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1342}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.578760856689806, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1342}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.1335718997900175, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1342}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 11.712332756479821,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1342}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 2.9280831891199552,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1342}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 14.640415945599777,level: 2}]->(g);
CREATE (n: Building {id: 1343, name:"building_tobacco_plantation", level:4});
MATCH (g: Goods{code: 43}), (b: Building{id:1343}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 103.0,level: 4}]->(g);
CREATE (n: Building {id: 1344, name:"building_cotton_plantation", level:7});
MATCH (g: Goods{code: 9}), (b: Building{id:1344}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 366.8,level: 7}]->(g);
CREATE (n: Building {id: 1345, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1345}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 78.73852347943682, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1345}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 12.749414319440916, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1345}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.5067021258455853, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:1345}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1064107034925104, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:1345}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 29.14367058709407,level: 1}]->(g);
CREATE (n: Building {id: 1346, name:"building_military_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1346}) CREATE (g)-[r:Demand{max_demand: 7.3804, current_input: 29.05608993438177, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1346}) CREATE (g)-[r:Demand{max_demand: 14.7608, current_input: 4.704788872160087, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1346}) CREATE (g)-[r:Demand{max_demand: 7.3804, current_input: 0.8514422299811404, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1346}) CREATE (g)-[r:Demand{max_demand: 14.7608, current_input: 1.850046436959076, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:1346}) CREATE (g)-[r:Demand{max_demand: 3.690195744680851, current_input: 0.8165744139794817, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1346}) CREATE (b)-[r:Supply{max_supply: 33.21179574468085, current_output: 11.82816802067213,level: 1}]->(g);
CREATE (n: Building {id: 1347, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1347}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.416005076350933, level: 3}]->(b);
CREATE (n: Building {id: 1351, name:"building_cotton_plantation", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1351}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 50.0,level: 1}]->(g);
CREATE (n: Building {id: 1352, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1352}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.789380428344903, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1352}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0333929749475044, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1352}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 5.8561663782399105,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1352}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.4640415945599776,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1352}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 4.392124783679933,level: 1}]->(g);
CREATE (n: Building {id: 1353, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1353}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 1}]->(b);
CREATE (n: Building {id: 1354, name:"building_lead_mine", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:1354}) CREATE (g)-[r:Demand{max_demand: 5.5304, current_input: 1.0731798682353846, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1354}) CREATE (g)-[r:Demand{max_demand: 5.5304, current_input: 1.1430153017299356, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1354}) CREATE (b)-[r:Supply{max_supply: 22.1216, current_output: 4.43239033993064,level: 1}]->(g);
CREATE (n: Building {id: 1355, name:"building_maize_farm", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1355}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8673404910560212, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1355}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.4133571899790017, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1355}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 9.835316102377575,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1355}) CREATE (b)-[r:Supply{max_supply: 14.0, current_output: 2.753888508665721,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1355}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 1.9670632204755152,level: 2}]->(g);
CREATE (n: Building {id: 1356, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1356}) CREATE (g)-[r:Demand{max_demand: 9.4098, current_input: 3.5657311954639868, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1356}) CREATE (g)-[r:Demand{max_demand: 4.7049, current_input: 0.9724021215661027, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1356}) CREATE (b)-[r:Supply{max_supply: 18.8196, current_output: 5.510535438596191,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1356}) CREATE (b)-[r:Supply{max_supply: 4.7049, current_output: 1.3776338596490478,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1356}) CREATE (b)-[r:Supply{max_supply: 14.1147, current_output: 4.132901578947143,level: 1}]->(g);
CREATE (n: Building {id: 1357, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1357}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 5.450279525249231, level: 2}]->(b);
CREATE (n: Building {id: 1358, name:"building_chemical_plants", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:1358}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 9.098101034850018, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1358}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.1536532301516724, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:1358}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 18.838591087957557,level: 1}]->(g);
CREATE (n: Building {id: 1359, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1359}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.0667859498950087, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1359}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 24.801431398740103,level: 2}]->(g);
CREATE (n: Building {id: 1360, name:"building_maize_farm", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:1360}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 7.469361964224084, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1360}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 1.653428759916007, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1360}) CREATE (b)-[r:Supply{max_supply: 319.99999999999994, current_output: 62.94602305521647,level: 8}]->(g);
CREATE (n: Building {id: 1361, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1361}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0333929749475044, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1361}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 12.400715699370052,level: 1}]->(g);
CREATE (n: Building {id: 1362, name:"building_sulfur_mine", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:1362}) CREATE (g)-[r:Demand{max_demand: 39.94759223300971, current_input: 7.751871793892346, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1362}) CREATE (g)-[r:Demand{max_demand: 39.94759223300971, current_input: 8.256312235931944, level: 4}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:1362}) CREATE (b)-[r:Supply{max_supply: 159.79039805825244, current_output: 32.01637389551704,level: 4}]->(g);
CREATE (n: Building {id: 1363, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1363}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.789380428344903, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1363}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.578760856689806,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1363}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.8946902141724515,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1363}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.8946902141724515,level: 1}]->(g);
CREATE (n: Building {id: 1364, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1364}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 1}]->(b);
CREATE (n: Building {id: 1365, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1365}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 78.73773609420203, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1365}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 12.749286825297723, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1365}) CREATE (g)-[r:Demand{max_demand: 49.9995, current_input: 5.768208468096854, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1365}) CREATE (g)-[r:Demand{max_demand: 9.9999, current_input: 2.06676528203551, level: 1}]->(b);
CREATE (n: Building {id: 1366, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1366}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.374707159720458, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1366}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.278051541633648, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1366}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.1074784367076633, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1366}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 9.900623399890126,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1366}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 9.900623399890126,level: 1}]->(g);
CREATE (n: Building {id: 1367, name:"building_furniture_manufacturies", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1367}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 78.73852347943682, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1367}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 9.562060739580689, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1367}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 3.7600531887683784, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1367}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.0667859498950087, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1367}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 33.01498118535606,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1367}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 20.63436324084754,level: 2}]->(g);
CREATE (n: Building {id: 1368, name:"building_chemical_plants", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:1368}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 9.098101034850018, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1368}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.1536532301516724, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:1368}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 18.838591087957557,level: 1}]->(g);
CREATE (n: Building {id: 1369, name:"building_coal_mine", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1369}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.200357849685027, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1369}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 24.801431398740107,level: 3}]->(g);
CREATE (n: Building {id: 1370, name:"building_maize_farm", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:1370}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.7346809821120424, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1370}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.8267143799580035, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1370}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 19.67063220475515,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1370}) CREATE (b)-[r:Supply{max_supply: 28.0, current_output: 5.507777017331442,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1370}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 3.9341264409510304,level: 4}]->(g);
CREATE (n: Building {id: 1371, name:"building_vineyard_plantation", level:3});
MATCH (g: Goods{code: 39}), (b: Building{id:1371}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 61.2,level: 3}]->(g);
CREATE (n: Building {id: 1372, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1372}) CREATE (g)-[r:Demand{max_demand: 14.898, current_input: 5.645418962148236, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1372}) CREATE (g)-[r:Demand{max_demand: 7.449, current_input: 1.539548854076792, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1372}) CREATE (b)-[r:Supply{max_supply: 29.796, current_output: 8.72451667030182,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1372}) CREATE (b)-[r:Supply{max_supply: 7.449, current_output: 2.181129167575455,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1372}) CREATE (b)-[r:Supply{max_supply: 22.347, current_output: 6.543387502726366,level: 2}]->(g);
CREATE (n: Building {id: 1373, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1373}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 1}]->(b);
CREATE (n: Building {id: 1374, name:"building_university", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1374}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.3625698813123077, level: 1}]->(b);
CREATE (n: Building {id: 1375, name:"building_food_industry", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1375}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 30.315043426759225, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1375}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 16.10018835147721, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1375}) CREATE (b)-[r:Supply{max_supply: 130.0, current_output: 59.51471421244249,level: 2}]->(g);
CREATE (n: Building {id: 1376, name:"building_coal_mine", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1376}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 4.133571899790017, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1376}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 16.534287599160066,level: 2}]->(g);
CREATE (n: Building {id: 1377, name:"building_tobacco_plantation", level:8});
MATCH (g: Goods{code: 43}), (b: Building{id:1377}) CREATE (b)-[r:Supply{max_supply: 193.17, current_output: 206.6919,level: 8}]->(g);
CREATE (n: Building {id: 1378, name:"building_maize_farm", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:1378}) CREATE (g)-[r:Demand{max_demand: 19.987592233009707, current_input: 3.7323640295415865, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1378}) CREATE (g)-[r:Demand{max_demand: 3.997514563106796, current_input: 0.8262006933529811, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1378}) CREATE (b)-[r:Supply{max_supply: 99.93799999999999, current_output: 19.658436412788202,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1378}) CREATE (b)-[r:Supply{max_supply: 27.98263106796116, current_output: 5.5043604385921885,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1378}) CREATE (b)-[r:Supply{max_supply: 19.987592233009707, current_output: 3.9316857547415465,level: 4}]->(g);
CREATE (n: Building {id: 1379, name:"building_vineyard_plantation", level:2});
MATCH (g: Goods{code: 39}), (b: Building{id:1379}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 1380, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1380}) CREATE (g)-[r:Demand{max_demand: 9.9352, current_input: 3.7648252431692284, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1380}) CREATE (g)-[r:Demand{max_demand: 4.9676, current_input: 1.0266965884698445, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1380}) CREATE (b)-[r:Supply{max_supply: 19.8704, current_output: 5.818218420108917,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1380}) CREATE (b)-[r:Supply{max_supply: 4.9676, current_output: 1.4545546050272293,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1380}) CREATE (b)-[r:Supply{max_supply: 14.9028, current_output: 4.363663815081688,level: 1}]->(g);
CREATE (n: Building {id: 1381, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1381}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 1}]->(b);
CREATE (n: Building {id: 1382, name:"building_food_industry", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1382}) CREATE (g)-[r:Demand{max_demand: 79.96719801980198, current_input: 30.302613508581896, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1382}) CREATE (g)-[r:Demand{max_demand: 79.96719801980198, current_input: 42.916231668622906, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1382}) CREATE (b)-[r:Supply{max_supply: 69.97129702970297, current_output: 32.033244198436144,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1382}) CREATE (b)-[r:Supply{max_supply: 119.95079207920791, current_output: 54.91413161653976,level: 2}]->(g);
CREATE (n: Building {id: 1383, name:"building_maize_farm", level:10});
MATCH (g: Goods{code: 32}), (b: Building{id:1383}) CREATE (g)-[r:Demand{max_demand: 49.895495412844035, current_input: 9.317187890570363, level: 10}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1383}) CREATE (g)-[r:Demand{max_demand: 9.979091743119264, current_input: 2.0624646607392187, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1383}) CREATE (b)-[r:Supply{max_supply: 249.47749541284404, current_output: 49.07380055629545,level: 10}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1383}) CREATE (b)-[r:Supply{max_supply: 69.85369724770642, current_output: 13.740663867019501,level: 10}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1383}) CREATE (b)-[r:Supply{max_supply: 49.895495412844035, current_output: 9.814759389401027,level: 10}]->(g);
CREATE (n: Building {id: 1384, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1384}) CREATE (g)-[r:Demand{max_demand: 9.4764, current_input: 3.5909684691167643, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1384}) CREATE (g)-[r:Demand{max_demand: 4.7382, current_input: 0.9792845187792529, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1384}) CREATE (b)-[r:Supply{max_supply: 18.9528, current_output: 5.549537506675271,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1384}) CREATE (b)-[r:Supply{max_supply: 4.7382, current_output: 1.3873843766688176,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1384}) CREATE (b)-[r:Supply{max_supply: 14.2146, current_output: 4.162153130006453,level: 1}]->(g);
CREATE (n: Building {id: 1385, name:"building_tobacco_plantation", level:10});
MATCH (g: Goods{code: 43}), (b: Building{id:1385}) CREATE (b)-[r:Supply{max_supply: 249.905, current_output: 272.39645,level: 10}]->(g);
CREATE (n: Building {id: 1386, name:"building_cotton_plantation", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1386}) CREATE (b)-[r:Supply{max_supply: 119.53559842519685, current_output: 151.81021,level: 3}]->(g);
CREATE (n: Building {id: 1387, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1387}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 1}]->(b);
CREATE (n: Building {id: 1388, name:"building_cotton_plantation", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:1388}) CREATE (b)-[r:Supply{max_supply: 157.968, current_output: 202.19904,level: 4}]->(g);
CREATE (n: Building {id: 1389, name:"building_maize_farm", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:1389}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.7346809821120424, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1389}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.8267143799580035, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1389}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 31.473011527608243,level: 4}]->(g);
CREATE (n: Building {id: 1390, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1390}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 1391, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1391}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 1}]->(b);
CREATE (n: Building {id: 1392, name:"building_maize_farm", level:6});
MATCH (g: Goods{code: 32}), (b: Building{id:1392}) CREATE (g)-[r:Demand{max_demand: 28.89869523809524, current_input: 5.396370375678307, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1392}) CREATE (g)-[r:Demand{max_demand: 5.779733333333333, current_input: 1.1945471647473178, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1392}) CREATE (b)-[r:Supply{max_supply: 144.49349523809522, current_output: 28.42278400808111,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1392}) CREATE (b)-[r:Supply{max_supply: 40.458171428571426, current_output: 7.958378098483617,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1392}) CREATE (b)-[r:Supply{max_supply: 28.89869523809524, current_output: 5.684556052258805,level: 6}]->(g);
CREATE (n: Building {id: 1393, name:"building_sugar_plantation", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1393}) CREATE (b)-[r:Supply{max_supply: 29.172, current_output: 29.172,level: 1}]->(g);
CREATE (n: Building {id: 1394, name:"building_cotton_plantation", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:1394}) CREATE (b)-[r:Supply{max_supply: 400.0, current_output: 536.0,level: 10}]->(g);
CREATE (n: Building {id: 1395, name:"building_tobacco_plantation", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:1395}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 1396, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1396}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.1386683587836446, level: 1}]->(b);
CREATE (n: Building {id: 1397, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1397}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 5.450279525249231, level: 2}]->(b);
CREATE (n: Building {id: 1398, name:"building_maize_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:1398}) CREATE (g)-[r:Demand{max_demand: 23.700999999999997, current_input: 4.425783697851875, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1398}) CREATE (g)-[r:Demand{max_demand: 4.740192307692308, current_input: 0.979696286133886, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1398}) CREATE (b)-[r:Supply{max_supply: 189.60799999999998, current_output: 37.29709231079214,level: 5}]->(g);
CREATE (n: Building {id: 1399, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1399}) CREATE (g)-[r:Demand{max_demand: 9.3019, current_input: 3.5248437806421453, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1399}) CREATE (g)-[r:Demand{max_demand: 4.65095, current_input: 0.9612518113664191, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1399}) CREATE (b)-[r:Supply{max_supply: 18.6038, current_output: 5.447347403374984,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1399}) CREATE (b)-[r:Supply{max_supply: 4.65095, current_output: 1.361836850843746,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1399}) CREATE (b)-[r:Supply{max_supply: 13.95285, current_output: 4.085510552531238,level: 1}]->(g);
CREATE (n: Building {id: 1400, name:"building_sugar_plantation", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1400}) CREATE (b)-[r:Supply{max_supply: 29.538, current_output: 29.538,level: 1}]->(g);
CREATE (n: Building {id: 1401, name:"building_cotton_plantation", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:1401}) CREATE (b)-[r:Supply{max_supply: 319.62239393939393, current_output: 421.90156,level: 8}]->(g);
CREATE (n: Building {id: 1402, name:"building_tobacco_plantation", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:1402}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 1403, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1403}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.1386683587836446, level: 1}]->(b);
CREATE (n: Building {id: 1404, name:"building_coal_mine", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1404}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.200357849685027, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1404}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 24.801431398740107,level: 3}]->(g);
CREATE (n: Building {id: 1405, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1405}) CREATE (g)-[r:Demand{max_demand: 4.9891, current_input: 1.0311401782621188, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1405}) CREATE (b)-[r:Supply{max_supply: 34.9237, current_output: 7.217981247834832,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1405}) CREATE (b)-[r:Supply{max_supply: 12.47275, current_output: 2.5778504456552973,level: 1}]->(g);
CREATE (n: Building {id: 1406, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1406}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 1}]->(b);
CREATE (n: Building {id: 1407, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1407}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.374707159720458, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1407}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.278051541633648, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1407}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.1074784367076633, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1407}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 9.900623399890126,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1407}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 9.900623399890126,level: 1}]->(g);
CREATE (n: Building {id: 1408, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1408}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.416005076350933, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1408}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 94.16005076350933,level: 3}]->(g);
CREATE (n: Building {id: 1409, name:"building_tobacco_plantation", level:4});
MATCH (g: Goods{code: 43}), (b: Building{id:1409}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 103.0,level: 4}]->(g);
CREATE (n: Building {id: 1410, name:"building_port", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1410}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.277336717567289, level: 2}]->(b);
CREATE (n: Building {id: 1412, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1412}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.1386683587836446, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1412}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 31.386683587836444,level: 1}]->(g);
CREATE (n: Building {id: 1413, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1413}) CREATE (g)-[r:Demand{max_demand: 9.9941, current_input: 3.7871446938921793, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1413}) CREATE (g)-[r:Demand{max_demand: 9.9941, current_input: 2.065566546184571, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1413}) CREATE (b)-[r:Supply{max_supply: 19.9882, current_output: 5.85271124007675,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1413}) CREATE (b)-[r:Supply{max_supply: 4.99705, current_output: 1.4631778100191875,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1413}) CREATE (b)-[r:Supply{max_supply: 24.98525, current_output: 7.315889050095938,level: 1}]->(g);
CREATE (n: Building {id: 1414, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:1414}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 8.175419287873847, level: 3}]->(b);
CREATE (n: Building {id: 1415, name:"building_steel_mills", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:1415}) CREATE (g)-[r:Demand{max_demand: 50.1, current_input: 9.72195707337494, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1415}) CREATE (g)-[r:Demand{max_demand: 66.8, current_input: 7.706403577413172, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:1415}) CREATE (b)-[r:Supply{max_supply: 108.55, current_output: 16.79357306947105,level: 2}]->(g);
CREATE (n: Building {id: 1416, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1416}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 157.47547218840407, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1416}) CREATE (g)-[r:Demand{max_demand: 79.9992, current_input: 25.498573650595446, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1416}) CREATE (g)-[r:Demand{max_demand: 99.999, current_input: 11.536416936193708, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1416}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 4.13353056407102, level: 2}]->(b);
CREATE (n: Building {id: 1417, name:"building_textile_mills", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1417}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 314.9540939177473, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1417}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.556103083267296, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1417}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 111.33661849960377,level: 2}]->(g);
CREATE (n: Building {id: 1418, name:"building_furniture_manufacturies", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:1418}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 157.47704695887364, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1418}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 19.124121479161374, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1418}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 7.520106377536756, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1418}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.1335718997900175, level: 4}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1418}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 66.02996237071211,level: 4}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1418}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 41.268726481695076,level: 4}]->(g);
CREATE (n: Building {id: 1419, name:"building_munition_plants", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:1419}) CREATE (g)-[r:Demand{max_demand: 13.3606, current_input: 4.151777640147641, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:1419}) CREATE (g)-[r:Demand{max_demand: 13.3606, current_input: 4.285491683894172, level: 1}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:1419}) CREATE (b)-[r:Supply{max_supply: 33.4015, current_output: 10.546586655052266,level: 1}]->(g);
CREATE (n: Building {id: 1420, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1420}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.374707159720458, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1420}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.278051541633648, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1420}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.1074784367076633, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1420}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 9.900623399890126,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1420}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 9.900623399890126,level: 1}]->(g);
CREATE (n: Building {id: 1421, name:"building_coal_mine", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1421}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 8.267143799580037, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1421}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 33.06857519832015,level: 4}]->(g);
CREATE (n: Building {id: 1422, name:"building_maize_farm", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:1422}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.801010736584032, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1422}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.6200357849685026, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1422}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 23.60475864570618,level: 3}]->(g);
CREATE (n: Building {id: 1423, name:"building_tooling_workshops", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1423}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 19.124121479161378, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:1423}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 6.791480469361794, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1423}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 39.08178957760542,level: 2}]->(g);
CREATE (n: Building {id: 1424, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:1424}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.8810207877744274, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1424}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.1335718997900175, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1424}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 16.02918537512889,level: 2}]->(g);
CREATE (n: Building {id: 1425, name:"building_vineyard_plantation", level:2});
MATCH (g: Goods{code: 39}), (b: Building{id:1425}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 1426, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1426}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.36814128503471, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1426}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.1001789248425133, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1426}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 17.568499134719737,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1426}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 4.392124783679934,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1426}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 13.176374351039803,level: 3}]->(g);
CREATE (n: Building {id: 1427, name:"building_logging_camp", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:1427}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 5.166964874737522, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1427}) CREATE (b)-[r:Supply{max_supply: 175.0, current_output: 36.16875412316265,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1427}) CREATE (b)-[r:Supply{max_supply: 62.5, current_output: 12.917412186843803,level: 5}]->(g);
CREATE (n: Building {id: 1428, name:"building_explosives_factory", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:1428}) CREATE (g)-[r:Demand{max_demand: 6.2666, current_input: 1.900471998166371, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:1428}) CREATE (g)-[r:Demand{max_demand: 6.2666, current_input: 1.1701875921251663, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:1428}) CREATE (b)-[r:Supply{max_supply: 15.6665, current_output: 3.8383244878644214,level: 1}]->(g);
CREATE (n: Building {id: 1429, name:"building_paper_mills", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1429}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 28.686182218742065, level: 3}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:1429}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 9.098101034850018, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1429}) CREATE (b)-[r:Supply{max_supply: 209.99999999999997, current_output: 65.31056621050747,level: 3}]->(g);
CREATE (n: Building {id: 1430, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1430}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 1}]->(b);
CREATE (n: Building {id: 1431, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1431}) CREATE (g)-[r:Demand{max_demand: 3.5239, current_input: 2.212070685903537, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1431}) CREATE (b)-[r:Supply{max_supply: 35.239, current_output: 22.12070685903537,level: 1}]->(g);
CREATE (n: Building {id: 1432, name:"building_maize_farm", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:1432}) CREATE (g)-[r:Demand{max_demand: 4.0344, current_input: 0.7533598477116412, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1432}) CREATE (g)-[r:Demand{max_demand: 0.80688, current_input: 0.1667648247251285, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1432}) CREATE (b)-[r:Supply{max_supply: 20.172, current_output: 3.967959928343209,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1432}) CREATE (b)-[r:Supply{max_supply: 5.64816, current_output: 1.1110287799360985,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1432}) CREATE (b)-[r:Supply{max_supply: 4.0344, current_output: 0.7935919856686418,level: 1}]->(g);
CREATE (n: Building {id: 1433, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1433}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.0667859498950087, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1433}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 24.801431398740103,level: 2}]->(g);
CREATE (n: Building {id: 1434, name:"building_lead_mine", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:1434}) CREATE (g)-[r:Demand{max_demand: 16.25639603960396, current_input: 3.154570548199842, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1434}) CREATE (g)-[r:Demand{max_demand: 16.25639603960396, current_input: 3.3598490930582328, level: 2}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1434}) CREATE (b)-[r:Supply{max_supply: 65.02559405940593, current_output: 13.02884126632622,level: 2}]->(g);
CREATE (n: Building {id: 1435, name:"building_maize_farm", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1435}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8673404910560212, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1435}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.4133571899790017, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1435}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 9.835316102377575,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1435}) CREATE (b)-[r:Supply{max_supply: 14.0, current_output: 2.753888508665721,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1435}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 1.9670632204755152,level: 2}]->(g);
CREATE (n: Building {id: 1437, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1437}) CREATE (g)-[r:Demand{max_demand: 17.047, current_input: 5.433481647587733, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1437}) CREATE (g)-[r:Demand{max_demand: 4.26175, current_input: 3.6463972315114397, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1437}) CREATE (g)-[r:Demand{max_demand: 8.5235, current_input: 2.648659245527777, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1437}) CREATE (b)-[r:Supply{max_supply: 17.047, current_output: 8.438796354896349,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1437}) CREATE (b)-[r:Supply{max_supply: 17.047, current_output: 8.438796354896349,level: 1}]->(g);
CREATE (n: Building {id: 1438, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1438}) CREATE (g)-[r:Demand{max_demand: 3.80865, current_input: 2.3908178489362655, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1438}) CREATE (b)-[r:Supply{max_supply: 38.0865, current_output: 23.908178489362655,level: 1}]->(g);
CREATE (n: Building {id: 1439, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1439}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 1}]->(b);
CREATE (n: Building {id: 1440, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:1440}) CREATE (g)-[r:Demand{max_demand: 17.88259405940594, current_input: 3.4701359641942964, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1440}) CREATE (g)-[r:Demand{max_demand: 17.88259405940594, current_input: 3.695949414965614, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1440}) CREATE (b)-[r:Supply{max_supply: 71.53039603960396, current_output: 14.332174725939964,level: 2}]->(g);
CREATE (n: Building {id: 1441, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1441}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.1386683587836446, level: 1}]->(b);
CREATE (n: Building {id: 1442, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1442}) CREATE (g)-[r:Demand{max_demand: 19.752, current_input: 2.6913480295680703, level: 1}]->(b);
CREATE (n: Building {id: 1443, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1443}) CREATE (g)-[r:Demand{max_demand: 18.27981, current_input: 71.9662624442322, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1443}) CREATE (g)-[r:Demand{max_demand: 36.55963, current_input: 11.652846755886543, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1443}) CREATE (g)-[r:Demand{max_demand: 45.69954, current_input: 5.272142193744556, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1443}) CREATE (g)-[r:Demand{max_demand: 9.1399, current_input: 1.8890216903445394, level: 1}]->(b);
CREATE (n: Building {id: 1444, name:"building_university", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1444}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 2}]->(b);
CREATE (n: Building {id: 1445, name:"building_textile_mills", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1445}) CREATE (g)-[r:Demand{max_demand: 57.078, current_input: 224.71187215796476, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1445}) CREATE (g)-[r:Demand{max_demand: 7.1347450980392155, current_input: 6.104561453165956, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1445}) CREATE (b)-[r:Supply{max_supply: 85.617, current_output: 79.4358938840048,level: 3}]->(g);
CREATE (n: Building {id: 1446, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1446}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.374707159720458, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1446}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.278051541633648, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1446}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.1074784367076633, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1446}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 9.900623399890126,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1446}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 9.900623399890126,level: 1}]->(g);
CREATE (n: Building {id: 1447, name:"building_shipyards", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1447}) CREATE (g)-[r:Demand{max_demand: 19.44, current_input: 76.53384482201258, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1447}) CREATE (g)-[r:Demand{max_demand: 38.88, current_input: 12.392430718496572, level: 2}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:1447}) CREATE (b)-[r:Supply{max_supply: 34.02, current_output: 22.43168843934225,level: 2}]->(g);
CREATE (n: Building {id: 1448, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1448}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.416005076350933, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1448}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 94.16005076350933,level: 3}]->(g);
CREATE (n: Building {id: 1449, name:"building_whaling_station", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1449}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.1386683587836446, level: 1}]->(b);
MATCH (g: Goods{code: 28}), (b: Building{id:1449}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 12.554673435134578,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1449}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 6.277336717567289,level: 1}]->(g);
CREATE (n: Building {id: 1450, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1450}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8673404910560212, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1450}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.4133571899790017, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1450}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 9.835316102377575,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1450}) CREATE (b)-[r:Supply{max_supply: 14.0, current_output: 2.753888508665721,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1450}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 1.9670632204755152,level: 2}]->(g);
CREATE (n: Building {id: 1451, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1451}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.416005076350933, level: 3}]->(b);
CREATE (n: Building {id: 1452, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 16}), (b: Building{id:1452}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.584824210099637, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1452}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0333929749475044, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1452}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 15.709303110282848,level: 1}]->(g);
CREATE (n: Building {id: 1453, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1453}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 1}]->(b);
CREATE (n: Building {id: 1454, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1454}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0333929749475044, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1454}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 12.400715699370052,level: 1}]->(g);
CREATE (n: Building {id: 1455, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1455}) CREATE (g)-[r:Demand{max_demand: 4.1705, current_input: 1.5803611076412416, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1455}) CREATE (g)-[r:Demand{max_demand: 4.1705, current_input: 0.8619530804037133, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1455}) CREATE (b)-[r:Supply{max_supply: 8.341, current_output: 2.442314188044955,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1455}) CREATE (b)-[r:Supply{max_supply: 2.08525, current_output: 0.6105785470112387,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1455}) CREATE (b)-[r:Supply{max_supply: 10.42625, current_output: 3.0528927350561936,level: 1}]->(g);
CREATE (n: Building {id: 1456, name:"building_food_industry", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1456}) CREATE (g)-[r:Demand{max_demand: 49.29279207920792, current_input: 18.678914156342515, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1456}) CREATE (g)-[r:Demand{max_demand: 49.29279207920792, current_input: 26.454107894848384, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1456}) CREATE (b)-[r:Supply{max_supply: 43.131198019801985, current_output: 19.74569941375985,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1456}) CREATE (b)-[r:Supply{max_supply: 73.93919801980198, current_output: 33.849771071120834,level: 2}]->(g);
CREATE (n: Building {id: 1457, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1457}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0333929749475044, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1457}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 12.400715699370052,level: 1}]->(g);
CREATE (n: Building {id: 1458, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1458}) CREATE (g)-[r:Demand{max_demand: 3.0137, current_input: 1.1420055796903035, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1458}) CREATE (g)-[r:Demand{max_demand: 3.0137, current_input: 0.6228672817198588, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1458}) CREATE (b)-[r:Supply{max_supply: 6.0274, current_output: 1.7648728614101623,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1458}) CREATE (b)-[r:Supply{max_supply: 1.50685, current_output: 0.4412182153525406,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1458}) CREATE (b)-[r:Supply{max_supply: 7.53425, current_output: 2.206091076762703,level: 1}]->(g);
CREATE (n: Building {id: 1459, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1459}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.7251397626246154, level: 1}]->(b);
CREATE (n: Building {id: 1460, name:"building_furniture_manufacturies", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1460}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 39.36926173971841, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1460}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 4.7810303697903445, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1460}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.8800265943841892, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1460}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0333929749475044, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1460}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 16.50749059267803,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1460}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 10.31718162042377,level: 1}]->(g);
CREATE (n: Building {id: 1461, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1461}) CREATE (g)-[r:Demand{max_demand: 14.047794117647058, current_input: 2.9033783509370688, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1461}) CREATE (b)-[r:Supply{max_supply: 98.33459803921568, current_output: 20.323656561602423,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1461}) CREATE (b)-[r:Supply{max_supply: 35.1195, current_output: 7.2584489167337765,level: 3}]->(g);
CREATE (n: Building {id: 1462, name:"building_shipyards", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1462}) CREATE (g)-[r:Demand{max_demand: 20.212793248945147, current_input: 79.57627479085347, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1462}) CREATE (g)-[r:Demand{max_demand: 40.42559493670886, current_input: 12.885066473949855, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1462}) CREATE (g)-[r:Demand{max_demand: 20.212793248945147, current_input: 2.533372590320405, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:1462}) CREATE (g)-[r:Demand{max_demand: 5.053198312236287, current_input: 1.1181825399057033, level: 2}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:1462}) CREATE (b)-[r:Supply{max_supply: 70.74479324894514, current_output: 29.453756431419055,level: 2}]->(g);
CREATE (n: Building {id: 1463, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1463}) CREATE (g)-[r:Demand{max_demand: 14.872794117647057, current_input: 9.336153660752466, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1463}) CREATE (b)-[r:Supply{max_supply: 148.728, current_output: 93.36157353303479,level: 3}]->(g);
CREATE (n: Building {id: 1464, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1464}) CREATE (g)-[r:Demand{max_demand: 4.946, current_input: 3.1047707405087808, level: 1}]->(b);
CREATE (n: Building {id: 1465, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1465}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 5.450279525249231, level: 2}]->(b);
CREATE (n: Building {id: 1466, name:"building_food_industry", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1466}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 15.157521713379612, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1466}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 21.46691780196961, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1466}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 16.023192287965283,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1466}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 27.468329636511914,level: 1}]->(g);
CREATE (n: Building {id: 1467, name:"building_coal_mine", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1467}) CREATE (g)-[r:Demand{max_demand: 19.780396396396394, current_input: 4.088184535542593, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1467}) CREATE (b)-[r:Supply{max_supply: 79.12159459459458, current_output: 16.352740004139697,level: 2}]->(g);
CREATE (n: Building {id: 1468, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1468}) CREATE (g)-[r:Demand{max_demand: 4.93475, current_input: 1.0199071966244395, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1468}) CREATE (b)-[r:Supply{max_supply: 59.217, current_output: 12.238886359493273,level: 1}]->(g);
CREATE (n: Building {id: 1469, name:"building_maize_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:1469}) CREATE (g)-[r:Demand{max_demand: 23.8065, current_input: 4.445484140032517, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1469}) CREATE (g)-[r:Demand{max_demand: 4.761298076923077, current_input: 0.9840583968646741, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1469}) CREATE (b)-[r:Supply{max_supply: 119.0325, current_output: 23.414445279125175,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1469}) CREATE (b)-[r:Supply{max_supply: 33.32909615384616, current_output: 6.556043921592273,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1469}) CREATE (b)-[r:Supply{max_supply: 23.8065, current_output: 4.682889055825035,level: 5}]->(g);
CREATE (n: Building {id: 1470, name:"building_cotton_plantation", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:1470}) CREATE (b)-[r:Supply{max_supply: 399.8159999999999, current_output: 535.75344,level: 10}]->(g);
CREATE (n: Building {id: 1471, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1471}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.1386683587836446, level: 1}]->(b);
CREATE (n: Building {id: 1472, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1472}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 5.450279525249231, level: 2}]->(b);
CREATE (n: Building {id: 1473, name:"building_cotton_plantation", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:1473}) CREATE (b)-[r:Supply{max_supply: 390.6479999999999, current_output: 523.46832,level: 10}]->(g);
CREATE (n: Building {id: 1474, name:"building_tobacco_plantation", level:5});
MATCH (g: Goods{code: 43}), (b: Building{id:1474}) CREATE (b)-[r:Supply{max_supply: 122.085, current_output: 126.9684,level: 5}]->(g);
CREATE (n: Building {id: 1475, name:"building_dye_plantation", level:2});
MATCH (g: Goods{code: 21}), (b: Building{id:1475}) CREATE (b)-[r:Supply{max_supply: 48.809999999999995, current_output: 49.2981,level: 2}]->(g);
CREATE (n: Building {id: 1476, name:"building_maize_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:1476}) CREATE (g)-[r:Demand{max_demand: 22.73425, current_input: 4.245258555879035, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1476}) CREATE (g)-[r:Demand{max_demand: 4.546846153846154, current_input: 0.939735774710339, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1476}) CREATE (b)-[r:Supply{max_supply: 181.874, current_output: 35.775765616076384,level: 5}]->(g);
CREATE (n: Building {id: 1477, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1477}) CREATE (g)-[r:Demand{max_demand: 4.64, current_input: 2.912684236951222, level: 1}]->(b);
CREATE (n: Building {id: 16778945, name:"building_naval_base", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:16778945}) CREATE (g)-[r:Demand{max_demand: 0.065, current_input: 0.018193581948468242, level: 2}]->(b);
CREATE (n: Building {id: 2690, name:"building_subsistence_farms", level:117});
MATCH (g: Goods{code: 7}), (b: Building{id:2690}) CREATE (b)-[r:Supply{max_supply: 25.89912, current_output: 25.89912,level: 117}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2690}) CREATE (b)-[r:Supply{max_supply: 6.47478, current_output: 6.47478,level: 117}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2690}) CREATE (b)-[r:Supply{max_supply: 6.47478, current_output: 6.47478,level: 117}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2690}) CREATE (b)-[r:Supply{max_supply: 6.47478, current_output: 6.47478,level: 117}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2690}) CREATE (b)-[r:Supply{max_supply: 6.47478, current_output: 6.47478,level: 117}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2690}) CREATE (b)-[r:Supply{max_supply: 6.47478, current_output: 6.47478,level: 117}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2690}) CREATE (b)-[r:Supply{max_supply: 6.47478, current_output: 6.47478,level: 117}]->(g);
CREATE (n: Building {id: 2691, name:"building_subsistence_farms", level:114});
MATCH (g: Goods{code: 7}), (b: Building{id:2691}) CREATE (b)-[r:Supply{max_supply: 113.27952, current_output: 113.27952,level: 114}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2691}) CREATE (b)-[r:Supply{max_supply: 28.31988, current_output: 28.31988,level: 114}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2691}) CREATE (b)-[r:Supply{max_supply: 28.31988, current_output: 28.31988,level: 114}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2691}) CREATE (b)-[r:Supply{max_supply: 28.31988, current_output: 28.31988,level: 114}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2691}) CREATE (b)-[r:Supply{max_supply: 28.31988, current_output: 28.31988,level: 114}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2691}) CREATE (b)-[r:Supply{max_supply: 28.31988, current_output: 28.31988,level: 114}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2691}) CREATE (b)-[r:Supply{max_supply: 28.31988, current_output: 28.31988,level: 114}]->(g);
CREATE (n: Building {id: 2692, name:"building_urban_center", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2692}) CREATE (g)-[r:Demand{max_demand: 9.954192660550458, current_input: 3.1727531611223925, level: 10}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2692}) CREATE (g)-[r:Demand{max_demand: 9.954192660550458, current_input: 1.9316214320553977, level: 10}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2692}) CREATE (g)-[r:Demand{max_demand: 9.954192660550458, current_input: 7.175531035545295, level: 10}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2692}) CREATE (b)-[r:Supply{max_supply: 218.9923944954128, current_output: 90.05270541157263,level: 10}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2692}) CREATE (b)-[r:Supply{max_supply: 49.771, current_output: 20.466524471621618,level: 10}]->(g);
CREATE (n: Building {id: 2693, name:"building_subsistence_farms", level:162});
MATCH (g: Goods{code: 7}), (b: Building{id:2693}) CREATE (b)-[r:Supply{max_supply: 56.35008, current_output: 56.35008,level: 162}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2693}) CREATE (b)-[r:Supply{max_supply: 14.08752, current_output: 14.08752,level: 162}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2693}) CREATE (b)-[r:Supply{max_supply: 14.08752, current_output: 14.08752,level: 162}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2693}) CREATE (b)-[r:Supply{max_supply: 14.08752, current_output: 14.08752,level: 162}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2693}) CREATE (b)-[r:Supply{max_supply: 14.08752, current_output: 14.08752,level: 162}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2693}) CREATE (b)-[r:Supply{max_supply: 14.08752, current_output: 14.08752,level: 162}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2693}) CREATE (b)-[r:Supply{max_supply: 14.08752, current_output: 14.08752,level: 162}]->(g);
CREATE (n: Building {id: 2694, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2694}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.9562060739580688, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2694}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.5821531181661641, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:2694}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.6638464220955063, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2694}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 2.162565447617676, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2694}) CREATE (b)-[r:Supply{max_supply: 65.99999999999999, current_output: 24.00624084010578,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2694}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 8.72954212367483,level: 3}]->(g);
CREATE (n: Building {id: 2739, name:"building_subsistence_farms", level:37});
MATCH (g: Goods{code: 7}), (b: Building{id:2739}) CREATE (b)-[r:Supply{max_supply: 0.04958, current_output: 0.04958,level: 37}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2739}) CREATE (b)-[r:Supply{max_supply: 0.01239, current_output: 0.01239,level: 37}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2739}) CREATE (b)-[r:Supply{max_supply: 0.01239, current_output: 0.01239,level: 37}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2739}) CREATE (b)-[r:Supply{max_supply: 0.01239, current_output: 0.01239,level: 37}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2739}) CREATE (b)-[r:Supply{max_supply: 0.01239, current_output: 0.01239,level: 37}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2739}) CREATE (b)-[r:Supply{max_supply: 0.01239, current_output: 0.01239,level: 37}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2739}) CREATE (b)-[r:Supply{max_supply: 0.01239, current_output: 0.01239,level: 37}]->(g);
CREATE (n: Building {id: 2740, name:"building_subsistence_farms", level:122});
MATCH (g: Goods{code: 7}), (b: Building{id:2740}) CREATE (b)-[r:Supply{max_supply: 5.002, current_output: 5.002,level: 122}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2740}) CREATE (b)-[r:Supply{max_supply: 1.2505, current_output: 1.2505,level: 122}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2740}) CREATE (b)-[r:Supply{max_supply: 1.2505, current_output: 1.2505,level: 122}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2740}) CREATE (b)-[r:Supply{max_supply: 1.2505, current_output: 1.2505,level: 122}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2740}) CREATE (b)-[r:Supply{max_supply: 1.2505, current_output: 1.2505,level: 122}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2740}) CREATE (b)-[r:Supply{max_supply: 1.2505, current_output: 1.2505,level: 122}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2740}) CREATE (b)-[r:Supply{max_supply: 1.2505, current_output: 1.2505,level: 122}]->(g);
CREATE (n: Building {id: 2743, name:"building_subsistence_farms", level:142});
MATCH (g: Goods{code: 7}), (b: Building{id:2743}) CREATE (b)-[r:Supply{max_supply: 4.53832, current_output: 4.53832,level: 142}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2743}) CREATE (b)-[r:Supply{max_supply: 1.13458, current_output: 1.13458,level: 142}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2743}) CREATE (b)-[r:Supply{max_supply: 1.13458, current_output: 1.13458,level: 142}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2743}) CREATE (b)-[r:Supply{max_supply: 1.13458, current_output: 1.13458,level: 142}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2743}) CREATE (b)-[r:Supply{max_supply: 1.13458, current_output: 1.13458,level: 142}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2743}) CREATE (b)-[r:Supply{max_supply: 1.13458, current_output: 1.13458,level: 142}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2743}) CREATE (b)-[r:Supply{max_supply: 1.13458, current_output: 1.13458,level: 142}]->(g);
CREATE (n: Building {id: 2747, name:"building_subsistence_farms", level:160});
MATCH (g: Goods{code: 7}), (b: Building{id:2747}) CREATE (b)-[r:Supply{max_supply: 6.9536, current_output: 8.34432,level: 160}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2747}) CREATE (b)-[r:Supply{max_supply: 1.7384, current_output: 2.08608,level: 160}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2747}) CREATE (b)-[r:Supply{max_supply: 1.7384, current_output: 2.08608,level: 160}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2747}) CREATE (b)-[r:Supply{max_supply: 1.7384, current_output: 2.08608,level: 160}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2747}) CREATE (b)-[r:Supply{max_supply: 1.7384, current_output: 2.08608,level: 160}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2747}) CREATE (b)-[r:Supply{max_supply: 1.7384, current_output: 2.08608,level: 160}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2747}) CREATE (b)-[r:Supply{max_supply: 1.7384, current_output: 2.08608,level: 160}]->(g);
CREATE (n: Building {id: 2750, name:"building_subsistence_farms", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:2750}) CREATE (b)-[r:Supply{max_supply: 1.0090000000000001, current_output: 1.2108,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2750}) CREATE (b)-[r:Supply{max_supply: 0.25225000000000003, current_output: 0.3027,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2750}) CREATE (b)-[r:Supply{max_supply: 0.25225000000000003, current_output: 0.3027,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2750}) CREATE (b)-[r:Supply{max_supply: 0.25225000000000003, current_output: 0.3027,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2750}) CREATE (b)-[r:Supply{max_supply: 0.25225000000000003, current_output: 0.3027,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2750}) CREATE (b)-[r:Supply{max_supply: 0.25225000000000003, current_output: 0.3027,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2750}) CREATE (b)-[r:Supply{max_supply: 0.25225000000000003, current_output: 0.3027,level: 50}]->(g);
CREATE (n: Building {id: 2751, name:"building_subsistence_farms", level:69});
MATCH (g: Goods{code: 7}), (b: Building{id:2751}) CREATE (b)-[r:Supply{max_supply: 1.0046333333333333, current_output: 1.20556,level: 69}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2751}) CREATE (b)-[r:Supply{max_supply: 0.2511583333333333, current_output: 0.30139,level: 69}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2751}) CREATE (b)-[r:Supply{max_supply: 0.2511583333333333, current_output: 0.30139,level: 69}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2751}) CREATE (b)-[r:Supply{max_supply: 0.2511583333333333, current_output: 0.30139,level: 69}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2751}) CREATE (b)-[r:Supply{max_supply: 0.2511583333333333, current_output: 0.30139,level: 69}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2751}) CREATE (b)-[r:Supply{max_supply: 0.2511583333333333, current_output: 0.30139,level: 69}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2751}) CREATE (b)-[r:Supply{max_supply: 0.2511583333333333, current_output: 0.30139,level: 69}]->(g);
CREATE (n: Building {id: 2754, name:"building_subsistence_farms", level:183});
MATCH (g: Goods{code: 7}), (b: Building{id:2754}) CREATE (b)-[r:Supply{max_supply: 6.573358333333333, current_output: 7.88803,level: 183}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2754}) CREATE (b)-[r:Supply{max_supply: 1.6433333333333333, current_output: 1.972,level: 183}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2754}) CREATE (b)-[r:Supply{max_supply: 1.6433333333333333, current_output: 1.972,level: 183}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2754}) CREATE (b)-[r:Supply{max_supply: 1.6433333333333333, current_output: 1.972,level: 183}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2754}) CREATE (b)-[r:Supply{max_supply: 1.6433333333333333, current_output: 1.972,level: 183}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2754}) CREATE (b)-[r:Supply{max_supply: 1.6433333333333333, current_output: 1.972,level: 183}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2754}) CREATE (b)-[r:Supply{max_supply: 1.6433333333333333, current_output: 1.972,level: 183}]->(g);
CREATE (n: Building {id: 2759, name:"building_subsistence_farms", level:178});
MATCH (g: Goods{code: 7}), (b: Building{id:2759}) CREATE (b)-[r:Supply{max_supply: 18.41944, current_output: 18.41944,level: 178}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2759}) CREATE (b)-[r:Supply{max_supply: 4.60486, current_output: 4.60486,level: 178}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2759}) CREATE (b)-[r:Supply{max_supply: 4.60486, current_output: 4.60486,level: 178}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2759}) CREATE (b)-[r:Supply{max_supply: 4.60486, current_output: 4.60486,level: 178}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2759}) CREATE (b)-[r:Supply{max_supply: 4.60486, current_output: 4.60486,level: 178}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2759}) CREATE (b)-[r:Supply{max_supply: 4.60486, current_output: 4.60486,level: 178}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2759}) CREATE (b)-[r:Supply{max_supply: 4.60486, current_output: 4.60486,level: 178}]->(g);
CREATE (n: Building {id: 2760, name:"building_subsistence_farms", level:227});
MATCH (g: Goods{code: 7}), (b: Building{id:2760}) CREATE (b)-[r:Supply{max_supply: 34.65382, current_output: 34.65382,level: 227}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2760}) CREATE (b)-[r:Supply{max_supply: 8.66345, current_output: 8.66345,level: 227}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2760}) CREATE (b)-[r:Supply{max_supply: 8.66345, current_output: 8.66345,level: 227}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2760}) CREATE (b)-[r:Supply{max_supply: 8.66345, current_output: 8.66345,level: 227}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2760}) CREATE (b)-[r:Supply{max_supply: 8.66345, current_output: 8.66345,level: 227}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2760}) CREATE (b)-[r:Supply{max_supply: 8.66345, current_output: 8.66345,level: 227}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2760}) CREATE (b)-[r:Supply{max_supply: 8.66345, current_output: 8.66345,level: 227}]->(g);
CREATE (n: Building {id: 2761, name:"building_subsistence_farms", level:240});
MATCH (g: Goods{code: 7}), (b: Building{id:2761}) CREATE (b)-[r:Supply{max_supply: 15.48, current_output: 15.48,level: 240}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2761}) CREATE (b)-[r:Supply{max_supply: 3.87, current_output: 3.87,level: 240}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2761}) CREATE (b)-[r:Supply{max_supply: 3.87, current_output: 3.87,level: 240}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2761}) CREATE (b)-[r:Supply{max_supply: 3.87, current_output: 3.87,level: 240}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2761}) CREATE (b)-[r:Supply{max_supply: 3.87, current_output: 3.87,level: 240}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2761}) CREATE (b)-[r:Supply{max_supply: 3.87, current_output: 3.87,level: 240}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2761}) CREATE (b)-[r:Supply{max_supply: 3.87, current_output: 3.87,level: 240}]->(g);
CREATE (n: Building {id: 2762, name:"building_subsistence_farms", level:133});
MATCH (g: Goods{code: 7}), (b: Building{id:2762}) CREATE (b)-[r:Supply{max_supply: 16.88568, current_output: 16.88568,level: 133}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2762}) CREATE (b)-[r:Supply{max_supply: 4.22142, current_output: 4.22142,level: 133}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2762}) CREATE (b)-[r:Supply{max_supply: 4.22142, current_output: 4.22142,level: 133}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2762}) CREATE (b)-[r:Supply{max_supply: 4.22142, current_output: 4.22142,level: 133}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2762}) CREATE (b)-[r:Supply{max_supply: 4.22142, current_output: 4.22142,level: 133}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2762}) CREATE (b)-[r:Supply{max_supply: 4.22142, current_output: 4.22142,level: 133}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2762}) CREATE (b)-[r:Supply{max_supply: 4.22142, current_output: 4.22142,level: 133}]->(g);
CREATE (n: Building {id: 2764, name:"building_subsistence_farms", level:180});
MATCH (g: Goods{code: 7}), (b: Building{id:2764}) CREATE (b)-[r:Supply{max_supply: 13.8744, current_output: 13.8744,level: 180}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2764}) CREATE (b)-[r:Supply{max_supply: 3.4686, current_output: 3.4686,level: 180}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2764}) CREATE (b)-[r:Supply{max_supply: 3.4686, current_output: 3.4686,level: 180}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2764}) CREATE (b)-[r:Supply{max_supply: 3.4686, current_output: 3.4686,level: 180}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2764}) CREATE (b)-[r:Supply{max_supply: 3.4686, current_output: 3.4686,level: 180}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2764}) CREATE (b)-[r:Supply{max_supply: 3.4686, current_output: 3.4686,level: 180}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2764}) CREATE (b)-[r:Supply{max_supply: 3.4686, current_output: 3.4686,level: 180}]->(g);
CREATE (n: Building {id: 2765, name:"building_subsistence_farms", level:212});
MATCH (g: Goods{code: 7}), (b: Building{id:2765}) CREATE (b)-[r:Supply{max_supply: 44.6896, current_output: 44.6896,level: 212}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2765}) CREATE (b)-[r:Supply{max_supply: 11.1724, current_output: 11.1724,level: 212}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2765}) CREATE (b)-[r:Supply{max_supply: 11.1724, current_output: 11.1724,level: 212}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2765}) CREATE (b)-[r:Supply{max_supply: 11.1724, current_output: 11.1724,level: 212}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2765}) CREATE (b)-[r:Supply{max_supply: 11.1724, current_output: 11.1724,level: 212}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2765}) CREATE (b)-[r:Supply{max_supply: 11.1724, current_output: 11.1724,level: 212}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2765}) CREATE (b)-[r:Supply{max_supply: 11.1724, current_output: 11.1724,level: 212}]->(g);
CREATE (n: Building {id: 2766, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2766}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.3187353579860229, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2766}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.19405103938872134, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:2766}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.22128214069850208, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2766}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.720855149205892, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2766}) CREATE (b)-[r:Supply{max_supply: 22.0, current_output: 8.002080280035262,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2766}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 2.909847374558277,level: 1}]->(g);
CREATE (n: Building {id: 2767, name:"building_subsistence_farms", level:179});
MATCH (g: Goods{code: 7}), (b: Building{id:2767}) CREATE (b)-[r:Supply{max_supply: 21.73418, current_output: 21.73418,level: 179}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2767}) CREATE (b)-[r:Supply{max_supply: 5.43354, current_output: 5.43354,level: 179}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2767}) CREATE (b)-[r:Supply{max_supply: 5.43354, current_output: 5.43354,level: 179}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2767}) CREATE (b)-[r:Supply{max_supply: 5.43354, current_output: 5.43354,level: 179}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2767}) CREATE (b)-[r:Supply{max_supply: 5.43354, current_output: 5.43354,level: 179}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2767}) CREATE (b)-[r:Supply{max_supply: 5.43354, current_output: 5.43354,level: 179}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2767}) CREATE (b)-[r:Supply{max_supply: 5.43354, current_output: 5.43354,level: 179}]->(g);
CREATE (n: Building {id: 2768, name:"building_subsistence_farms", level:171});
MATCH (g: Goods{code: 7}), (b: Building{id:2768}) CREATE (b)-[r:Supply{max_supply: 100.74636, current_output: 100.74636,level: 171}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2768}) CREATE (b)-[r:Supply{max_supply: 25.18659, current_output: 25.18659,level: 171}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2768}) CREATE (b)-[r:Supply{max_supply: 25.18659, current_output: 25.18659,level: 171}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2768}) CREATE (b)-[r:Supply{max_supply: 25.18659, current_output: 25.18659,level: 171}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2768}) CREATE (b)-[r:Supply{max_supply: 25.18659, current_output: 25.18659,level: 171}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2768}) CREATE (b)-[r:Supply{max_supply: 25.18659, current_output: 25.18659,level: 171}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2768}) CREATE (b)-[r:Supply{max_supply: 25.18659, current_output: 25.18659,level: 171}]->(g);
CREATE (n: Building {id: 2769, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2769}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.3187353579860229, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2769}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.19405103938872134, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:2769}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.22128214069850208, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2769}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.720855149205892, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2769}) CREATE (b)-[r:Supply{max_supply: 22.0, current_output: 8.002080280035262,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2769}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 2.909847374558277,level: 1}]->(g);
CREATE (n: Building {id: 2770, name:"building_subsistence_farms", level:165});
MATCH (g: Goods{code: 7}), (b: Building{id:2770}) CREATE (b)-[r:Supply{max_supply: 48.5826, current_output: 48.5826,level: 165}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2770}) CREATE (b)-[r:Supply{max_supply: 12.14565, current_output: 12.14565,level: 165}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2770}) CREATE (b)-[r:Supply{max_supply: 12.14565, current_output: 12.14565,level: 165}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2770}) CREATE (b)-[r:Supply{max_supply: 12.14565, current_output: 12.14565,level: 165}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2770}) CREATE (b)-[r:Supply{max_supply: 12.14565, current_output: 12.14565,level: 165}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2770}) CREATE (b)-[r:Supply{max_supply: 12.14565, current_output: 12.14565,level: 165}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2770}) CREATE (b)-[r:Supply{max_supply: 12.14565, current_output: 12.14565,level: 165}]->(g);
CREATE (n: Building {id: 2771, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2771}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.3187353579860229, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2771}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.19405103938872134, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2771}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.720855149205892, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2771}) CREATE (b)-[r:Supply{max_supply: 22.0, current_output: 9.046704674924667,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2771}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 2.0560692443010606,level: 1}]->(g);
CREATE (n: Building {id: 2772, name:"building_subsistence_farms", level:156});
MATCH (g: Goods{code: 7}), (b: Building{id:2772}) CREATE (b)-[r:Supply{max_supply: 39.9516, current_output: 39.9516,level: 156}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2772}) CREATE (b)-[r:Supply{max_supply: 9.9879, current_output: 9.9879,level: 156}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2772}) CREATE (b)-[r:Supply{max_supply: 9.9879, current_output: 9.9879,level: 156}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2772}) CREATE (b)-[r:Supply{max_supply: 9.9879, current_output: 9.9879,level: 156}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2772}) CREATE (b)-[r:Supply{max_supply: 9.9879, current_output: 9.9879,level: 156}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2772}) CREATE (b)-[r:Supply{max_supply: 9.9879, current_output: 9.9879,level: 156}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2772}) CREATE (b)-[r:Supply{max_supply: 9.9879, current_output: 9.9879,level: 156}]->(g);
CREATE (n: Building {id: 2773, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2773}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.3187353579860229, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2773}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.19405103938872134, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2773}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.720855149205892, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2773}) CREATE (b)-[r:Supply{max_supply: 22.0, current_output: 9.046704674924667,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2773}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 2.0560692443010606,level: 1}]->(g);
CREATE (n: Building {id: 2774, name:"building_subsistence_farms", level:150});
MATCH (g: Goods{code: 7}), (b: Building{id:2774}) CREATE (b)-[r:Supply{max_supply: 16.569, current_output: 16.569,level: 150}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2774}) CREATE (b)-[r:Supply{max_supply: 4.14225, current_output: 4.14225,level: 150}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2774}) CREATE (b)-[r:Supply{max_supply: 4.14225, current_output: 4.14225,level: 150}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2774}) CREATE (b)-[r:Supply{max_supply: 4.14225, current_output: 4.14225,level: 150}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2774}) CREATE (b)-[r:Supply{max_supply: 4.14225, current_output: 4.14225,level: 150}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2774}) CREATE (b)-[r:Supply{max_supply: 4.14225, current_output: 4.14225,level: 150}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2774}) CREATE (b)-[r:Supply{max_supply: 4.14225, current_output: 4.14225,level: 150}]->(g);
CREATE (n: Building {id: 2775, name:"building_subsistence_farms", level:150});
MATCH (g: Goods{code: 7}), (b: Building{id:2775}) CREATE (b)-[r:Supply{max_supply: 40.296, current_output: 40.296,level: 150}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2775}) CREATE (b)-[r:Supply{max_supply: 10.074, current_output: 10.074,level: 150}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2775}) CREATE (b)-[r:Supply{max_supply: 10.074, current_output: 10.074,level: 150}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2775}) CREATE (b)-[r:Supply{max_supply: 10.074, current_output: 10.074,level: 150}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2775}) CREATE (b)-[r:Supply{max_supply: 10.074, current_output: 10.074,level: 150}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2775}) CREATE (b)-[r:Supply{max_supply: 10.074, current_output: 10.074,level: 150}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2775}) CREATE (b)-[r:Supply{max_supply: 10.074, current_output: 10.074,level: 150}]->(g);
CREATE (n: Building {id: 2776, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2776}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.3187353579860229, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2776}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.19405103938872134, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2776}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.720855149205892, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2776}) CREATE (b)-[r:Supply{max_supply: 22.0, current_output: 9.046704674924667,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2776}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 2.0560692443010606,level: 1}]->(g);
CREATE (n: Building {id: 2777, name:"building_subsistence_farms", level:99});
MATCH (g: Goods{code: 7}), (b: Building{id:2777}) CREATE (b)-[r:Supply{max_supply: 14.69754, current_output: 14.69754,level: 99}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2777}) CREATE (b)-[r:Supply{max_supply: 3.67438, current_output: 3.67438,level: 99}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2777}) CREATE (b)-[r:Supply{max_supply: 3.67438, current_output: 3.67438,level: 99}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2777}) CREATE (b)-[r:Supply{max_supply: 3.67438, current_output: 3.67438,level: 99}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2777}) CREATE (b)-[r:Supply{max_supply: 3.67438, current_output: 3.67438,level: 99}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2777}) CREATE (b)-[r:Supply{max_supply: 3.67438, current_output: 3.67438,level: 99}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2777}) CREATE (b)-[r:Supply{max_supply: 3.67438, current_output: 3.67438,level: 99}]->(g);
CREATE (n: Building {id: 2778, name:"building_subsistence_farms", level:162});
MATCH (g: Goods{code: 7}), (b: Building{id:2778}) CREATE (b)-[r:Supply{max_supply: 45.5058, current_output: 45.5058,level: 162}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2778}) CREATE (b)-[r:Supply{max_supply: 11.37645, current_output: 11.37645,level: 162}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2778}) CREATE (b)-[r:Supply{max_supply: 11.37645, current_output: 11.37645,level: 162}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2778}) CREATE (b)-[r:Supply{max_supply: 11.37645, current_output: 11.37645,level: 162}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2778}) CREATE (b)-[r:Supply{max_supply: 11.37645, current_output: 11.37645,level: 162}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2778}) CREATE (b)-[r:Supply{max_supply: 11.37645, current_output: 11.37645,level: 162}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2778}) CREATE (b)-[r:Supply{max_supply: 11.37645, current_output: 11.37645,level: 162}]->(g);
CREATE (n: Building {id: 2779, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2779}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.3187353579860229, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2779}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.19405103938872134, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2779}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.720855149205892, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2779}) CREATE (b)-[r:Supply{max_supply: 22.0, current_output: 9.046704674924667,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2779}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 2.0560692443010606,level: 1}]->(g);
CREATE (n: Building {id: 2780, name:"building_subsistence_farms", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2780}) CREATE (b)-[r:Supply{max_supply: 2.0, current_output: 2.0,level: 1}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2780}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2780}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2780}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2780}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2780}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2780}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
CREATE (n: Building {id: 2782, name:"building_subsistence_farms", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:2782}) CREATE (b)-[r:Supply{max_supply: 15.69, current_output: 15.69,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2782}) CREATE (b)-[r:Supply{max_supply: 3.9225, current_output: 3.9225,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2782}) CREATE (b)-[r:Supply{max_supply: 3.9225, current_output: 3.9225,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2782}) CREATE (b)-[r:Supply{max_supply: 3.9225, current_output: 3.9225,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2782}) CREATE (b)-[r:Supply{max_supply: 3.9225, current_output: 3.9225,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2782}) CREATE (b)-[r:Supply{max_supply: 3.9225, current_output: 3.9225,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2782}) CREATE (b)-[r:Supply{max_supply: 3.9225, current_output: 3.9225,level: 50}]->(g);
CREATE (n: Building {id: 2783, name:"building_subsistence_farms", level:21});
MATCH (g: Goods{code: 7}), (b: Building{id:2783}) CREATE (b)-[r:Supply{max_supply: 16.63326, current_output: 16.63326,level: 21}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2783}) CREATE (b)-[r:Supply{max_supply: 4.15831, current_output: 4.15831,level: 21}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2783}) CREATE (b)-[r:Supply{max_supply: 4.15831, current_output: 4.15831,level: 21}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2783}) CREATE (b)-[r:Supply{max_supply: 4.15831, current_output: 4.15831,level: 21}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2783}) CREATE (b)-[r:Supply{max_supply: 4.15831, current_output: 4.15831,level: 21}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2783}) CREATE (b)-[r:Supply{max_supply: 4.15831, current_output: 4.15831,level: 21}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2783}) CREATE (b)-[r:Supply{max_supply: 4.15831, current_output: 4.15831,level: 21}]->(g);
CREATE (n: Building {id: 2784, name:"building_subsistence_farms", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:2784}) CREATE (b)-[r:Supply{max_supply: 5.94552, current_output: 5.94552,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2784}) CREATE (b)-[r:Supply{max_supply: 1.48638, current_output: 1.48638,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2784}) CREATE (b)-[r:Supply{max_supply: 1.48638, current_output: 1.48638,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2784}) CREATE (b)-[r:Supply{max_supply: 1.48638, current_output: 1.48638,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2784}) CREATE (b)-[r:Supply{max_supply: 1.48638, current_output: 1.48638,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2784}) CREATE (b)-[r:Supply{max_supply: 1.48638, current_output: 1.48638,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2784}) CREATE (b)-[r:Supply{max_supply: 1.48638, current_output: 1.48638,level: 14}]->(g);
CREATE (n: Building {id: 2785, name:"building_subsistence_farms", level:172});
MATCH (g: Goods{code: 7}), (b: Building{id:2785}) CREATE (b)-[r:Supply{max_supply: 75.56648, current_output: 75.56648,level: 172}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2785}) CREATE (b)-[r:Supply{max_supply: 18.89162, current_output: 18.89162,level: 172}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2785}) CREATE (b)-[r:Supply{max_supply: 18.89162, current_output: 18.89162,level: 172}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2785}) CREATE (b)-[r:Supply{max_supply: 18.89162, current_output: 18.89162,level: 172}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2785}) CREATE (b)-[r:Supply{max_supply: 18.89162, current_output: 18.89162,level: 172}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2785}) CREATE (b)-[r:Supply{max_supply: 18.89162, current_output: 18.89162,level: 172}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2785}) CREATE (b)-[r:Supply{max_supply: 18.89162, current_output: 18.89162,level: 172}]->(g);
CREATE (n: Building {id: 2786, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2786}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.2749414319440917, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2786}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.7762041575548854, level: 4}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:2786}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.8851285627940083, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2786}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 2.883420596823568, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2786}) CREATE (b)-[r:Supply{max_supply: 88.0, current_output: 32.00832112014105,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2786}) CREATE (b)-[r:Supply{max_supply: 32.0, current_output: 11.639389498233108,level: 4}]->(g);
CREATE (n: Building {id: 2787, name:"building_subsistence_farms", level:39});
MATCH (g: Goods{code: 7}), (b: Building{id:2787}) CREATE (b)-[r:Supply{max_supply: 15.86286, current_output: 15.86286,level: 39}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2787}) CREATE (b)-[r:Supply{max_supply: 3.96571, current_output: 3.96571,level: 39}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2787}) CREATE (b)-[r:Supply{max_supply: 3.96571, current_output: 3.96571,level: 39}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2787}) CREATE (b)-[r:Supply{max_supply: 3.96571, current_output: 3.96571,level: 39}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2787}) CREATE (b)-[r:Supply{max_supply: 3.96571, current_output: 3.96571,level: 39}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2787}) CREATE (b)-[r:Supply{max_supply: 3.96571, current_output: 3.96571,level: 39}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2787}) CREATE (b)-[r:Supply{max_supply: 3.96571, current_output: 3.96571,level: 39}]->(g);
CREATE (n: Building {id: 2788, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2788}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.3187353579860229, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2788}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.19405103938872134, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:2788}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.22128214069850208, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2788}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.720855149205892, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2788}) CREATE (b)-[r:Supply{max_supply: 22.0, current_output: 8.002080280035262,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2788}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 2.909847374558277,level: 1}]->(g);
CREATE (n: Building {id: 2789, name:"building_subsistence_farms", level:268});
MATCH (g: Goods{code: 7}), (b: Building{id:2789}) CREATE (b)-[r:Supply{max_supply: 43.16944, current_output: 43.16944,level: 268}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2789}) CREATE (b)-[r:Supply{max_supply: 10.79236, current_output: 10.79236,level: 268}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2789}) CREATE (b)-[r:Supply{max_supply: 10.79236, current_output: 10.79236,level: 268}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2789}) CREATE (b)-[r:Supply{max_supply: 10.79236, current_output: 10.79236,level: 268}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2789}) CREATE (b)-[r:Supply{max_supply: 10.79236, current_output: 10.79236,level: 268}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2789}) CREATE (b)-[r:Supply{max_supply: 10.79236, current_output: 10.79236,level: 268}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2789}) CREATE (b)-[r:Supply{max_supply: 10.79236, current_output: 10.79236,level: 268}]->(g);
CREATE (n: Building {id: 2790, name:"building_subsistence_fishing_villages", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:2790}) CREATE (b)-[r:Supply{max_supply: 2.45085, current_output: 2.45085,level: 15}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:2790}) CREATE (b)-[r:Supply{max_supply: 9.8034, current_output: 9.8034,level: 15}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2790}) CREATE (b)-[r:Supply{max_supply: 1.22542, current_output: 1.22542,level: 15}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2790}) CREATE (b)-[r:Supply{max_supply: 3.67627, current_output: 3.67627,level: 15}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2790}) CREATE (b)-[r:Supply{max_supply: 2.45085, current_output: 2.45085,level: 15}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2790}) CREATE (b)-[r:Supply{max_supply: 2.45085, current_output: 2.45085,level: 15}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2790}) CREATE (b)-[r:Supply{max_supply: 2.45085, current_output: 2.45085,level: 15}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2790}) CREATE (b)-[r:Supply{max_supply: 2.45085, current_output: 2.45085,level: 15}]->(g);
CREATE (n: Building {id: 2791, name:"building_subsistence_fishing_villages", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:2791}) CREATE (b)-[r:Supply{max_supply: 1.6042, current_output: 1.6042,level: 10}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:2791}) CREATE (b)-[r:Supply{max_supply: 6.4168, current_output: 6.4168,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2791}) CREATE (b)-[r:Supply{max_supply: 0.8021, current_output: 0.8021,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2791}) CREATE (b)-[r:Supply{max_supply: 2.4063, current_output: 2.4063,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2791}) CREATE (b)-[r:Supply{max_supply: 1.6042, current_output: 1.6042,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2791}) CREATE (b)-[r:Supply{max_supply: 1.6042, current_output: 1.6042,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2791}) CREATE (b)-[r:Supply{max_supply: 1.6042, current_output: 1.6042,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2791}) CREATE (b)-[r:Supply{max_supply: 1.6042, current_output: 1.6042,level: 10}]->(g);
CREATE (n: Building {id: 2792, name:"building_subsistence_fishing_villages", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:2792}) CREATE (b)-[r:Supply{max_supply: 8.6859, current_output: 8.6859,level: 18}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:2792}) CREATE (b)-[r:Supply{max_supply: 34.7436, current_output: 34.7436,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2792}) CREATE (b)-[r:Supply{max_supply: 4.34295, current_output: 4.34295,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2792}) CREATE (b)-[r:Supply{max_supply: 13.02885, current_output: 13.02885,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2792}) CREATE (b)-[r:Supply{max_supply: 8.6859, current_output: 8.6859,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2792}) CREATE (b)-[r:Supply{max_supply: 8.6859, current_output: 8.6859,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2792}) CREATE (b)-[r:Supply{max_supply: 8.6859, current_output: 8.6859,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2792}) CREATE (b)-[r:Supply{max_supply: 8.6859, current_output: 8.6859,level: 18}]->(g);
CREATE (n: Building {id: 2793, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2793}) CREATE (g)-[r:Demand{max_demand: 1.9201782178217823, current_input: 0.6120286916543893, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2793}) CREATE (g)-[r:Demand{max_demand: 1.9201782178217823, current_input: 0.37261257897989947, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:2793}) CREATE (g)-[r:Demand{max_demand: 1.9201782178217823, current_input: 0.4249011465622386, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2793}) CREATE (g)-[r:Demand{max_demand: 1.9201782178217823, current_input: 1.3841703557098246, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2793}) CREATE (b)-[r:Supply{max_supply: 42.2439504950495, current_output: 15.365431054873703,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2793}) CREATE (b)-[r:Supply{max_supply: 15.361435643564358, current_output: 5.58742914710896,level: 2}]->(g);
CREATE (n: Building {id: 2794, name:"building_subsistence_farms", level:23});
MATCH (g: Goods{code: 7}), (b: Building{id:2794}) CREATE (b)-[r:Supply{max_supply: 20.3205, current_output: 20.3205,level: 23}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2794}) CREATE (b)-[r:Supply{max_supply: 5.08012, current_output: 5.08012,level: 23}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2794}) CREATE (b)-[r:Supply{max_supply: 5.08012, current_output: 5.08012,level: 23}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2794}) CREATE (b)-[r:Supply{max_supply: 5.08012, current_output: 5.08012,level: 23}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2794}) CREATE (b)-[r:Supply{max_supply: 5.08012, current_output: 5.08012,level: 23}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2794}) CREATE (b)-[r:Supply{max_supply: 5.08012, current_output: 5.08012,level: 23}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2794}) CREATE (b)-[r:Supply{max_supply: 5.08012, current_output: 5.08012,level: 23}]->(g);
CREATE (n: Building {id: 2795, name:"building_subsistence_farms", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:2795}) CREATE (b)-[r:Supply{max_supply: 20.6262, current_output: 20.6262,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2795}) CREATE (b)-[r:Supply{max_supply: 5.15655, current_output: 5.15655,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2795}) CREATE (b)-[r:Supply{max_supply: 5.15655, current_output: 5.15655,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2795}) CREATE (b)-[r:Supply{max_supply: 5.15655, current_output: 5.15655,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2795}) CREATE (b)-[r:Supply{max_supply: 5.15655, current_output: 5.15655,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2795}) CREATE (b)-[r:Supply{max_supply: 5.15655, current_output: 5.15655,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2795}) CREATE (b)-[r:Supply{max_supply: 5.15655, current_output: 5.15655,level: 14}]->(g);
CREATE (n: Building {id: 2796, name:"building_subsistence_fishing_villages", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:2796}) CREATE (b)-[r:Supply{max_supply: 2.7658, current_output: 2.7658,level: 20}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:2796}) CREATE (b)-[r:Supply{max_supply: 11.0632, current_output: 11.0632,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2796}) CREATE (b)-[r:Supply{max_supply: 1.3829, current_output: 1.3829,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2796}) CREATE (b)-[r:Supply{max_supply: 4.1487, current_output: 4.1487,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2796}) CREATE (b)-[r:Supply{max_supply: 2.7658, current_output: 2.7658,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2796}) CREATE (b)-[r:Supply{max_supply: 2.7658, current_output: 2.7658,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2796}) CREATE (b)-[r:Supply{max_supply: 2.7658, current_output: 2.7658,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2796}) CREATE (b)-[r:Supply{max_supply: 2.7658, current_output: 2.7658,level: 20}]->(g);
CREATE (n: Building {id: 2797, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2797}) CREATE (g)-[r:Demand{max_demand: 1.1552376237623763, current_input: 0.3682150775688235, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2797}) CREATE (g)-[r:Demand{max_demand: 1.1552376237623763, current_input: 0.22417506163204573, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:2797}) CREATE (g)-[r:Demand{max_demand: 1.1552376237623763, current_input: 0.25563345440158936, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2797}) CREATE (g)-[r:Demand{max_demand: 1.1552376237623763, current_input: 0.8327589896454878, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2797}) CREATE (b)-[r:Supply{max_supply: 25.415277227722772, current_output: 9.244322214344983,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2797}) CREATE (b)-[r:Supply{max_supply: 9.241910891089109, current_output: 3.361568767792148,level: 2}]->(g);
CREATE (n: Building {id: 2798, name:"building_subsistence_farms", level:165});
MATCH (g: Goods{code: 7}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 53.4402, current_output: 53.4402,level: 165}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 13.36005, current_output: 13.36005,level: 165}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 13.36005, current_output: 13.36005,level: 165}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 13.36005, current_output: 13.36005,level: 165}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 13.36005, current_output: 13.36005,level: 165}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 13.36005, current_output: 13.36005,level: 165}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 13.36005, current_output: 13.36005,level: 165}]->(g);
CREATE (n: Building {id: 2799, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2799}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.3187353579860229, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2799}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.19405103938872134, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2799}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.720855149205892, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2799}) CREATE (b)-[r:Supply{max_supply: 22.0, current_output: 9.046704674924667,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2799}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 2.0560692443010606,level: 1}]->(g);
CREATE (n: Building {id: 16780735, name:"building_barracks", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:16780735}) CREATE (g)-[r:Demand{max_demand: 0.4006, current_input: 0.4005174819495611, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16780735}) CREATE (g)-[r:Demand{max_demand: 0.8012, current_input: 0.22951515748524165, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16780735}) CREATE (g)-[r:Demand{max_demand: 1.6024, current_input: 0.10452868951736147, level: 5}]->(b);
CREATE (n: Building {id: 3758, name:"building_subsistence_farms", level:78});
MATCH (g: Goods{code: 7}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 38.01876, current_output: 38.01876,level: 78}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 9.50469, current_output: 9.50469,level: 78}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 9.50469, current_output: 9.50469,level: 78}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 9.50469, current_output: 9.50469,level: 78}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 9.50469, current_output: 9.50469,level: 78}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 9.50469, current_output: 9.50469,level: 78}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 9.50469, current_output: 9.50469,level: 78}]->(g);
CREATE (n: Building {id: 3759, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3759}) CREATE (g)-[r:Demand{max_demand: 0.56247, current_input: 0.17927907680639832, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3759}) CREATE (g)-[r:Demand{max_demand: 0.56247, current_input: 0.10914788812497411, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3759}) CREATE (g)-[r:Demand{max_demand: 0.56247, current_input: 0.4054593957738381, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3759}) CREATE (b)-[r:Supply{max_supply: 12.37434, current_output: 5.088499978504878,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3759}) CREATE (b)-[r:Supply{max_supply: 2.81235, current_output: 1.1564772678420174,level: 1}]->(g);
CREATE (n: Building {id: 3990, name:"building_barracks", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:3990}) CREATE (g)-[r:Demand{max_demand: 2.42796, current_input: 2.4274598738748288, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3990}) CREATE (g)-[r:Demand{max_demand: 4.85595, current_input: 1.3910560771223903, level: 3}]->(b);
CREATE (n: Building {id: 3991, name:"building_barracks", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:3991}) CREATE (g)-[r:Demand{max_demand: 0.052, current_input: 0.05198928872036239, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3991}) CREATE (g)-[r:Demand{max_demand: 0.104, current_input: 0.029792282050006398, level: 2}]->(b);
CREATE (n: Building {id: 3992, name:"building_barracks", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:3992}) CREATE (g)-[r:Demand{max_demand: 0.143, current_input: 0.1429705439809966, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3992}) CREATE (g)-[r:Demand{max_demand: 0.286, current_input: 0.08192877563751759, level: 2}]->(b);
CREATE (n: Building {id: 3993, name:"building_barracks", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:3993}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.999588027706246, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3993}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.1458570019233232, level: 2}]->(b);
CREATE (n: Building {id: 3994, name:"building_barracks", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:3994}) CREATE (g)-[r:Demand{max_demand: 0.965, current_input: 0.9648012233682637, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3994}) CREATE (g)-[r:Demand{max_demand: 1.93, current_input: 0.5528760034280034, level: 1}]->(b);
CREATE (n: Building {id: 3995, name:"building_barracks", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:3995}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.999794013853123, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3995}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.5729285009616616, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3995}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.13046516415047613, level: 2}]->(b);
CREATE (n: Building {id: 3996, name:"building_barracks", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:3996}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.999794013853123, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3996}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.5729285009616616, level: 1}]->(b);
CREATE (n: Building {id: 3997, name:"building_barracks", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:3997}) CREATE (g)-[r:Demand{max_demand: 0.671, current_input: 0.6708617832954455, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3997}) CREATE (g)-[r:Demand{max_demand: 1.342, current_input: 0.384435024145275, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3997}) CREATE (g)-[r:Demand{max_demand: 1.342, current_input: 0.08754212514496948, level: 2}]->(b);
CREATE (n: Building {id: 3998, name:"building_barracks", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:3998}) CREATE (g)-[r:Demand{max_demand: 0.99999, current_input: 0.9997840159129845, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3998}) CREATE (g)-[r:Demand{max_demand: 1.99998, current_input: 0.5729227716766521, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3998}) CREATE (g)-[r:Demand{max_demand: 3.99999, current_input: 0.2609296759751315, level: 3}]->(b);
CREATE (n: Building {id: 3999, name:"building_barracks", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:3999}) CREATE (g)-[r:Demand{max_demand: 0.969, current_input: 0.9688003994236762, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3999}) CREATE (g)-[r:Demand{max_demand: 1.938, current_input: 0.55516771743185, level: 1}]->(b);
CREATE (n: Building {id: 4000, name:"building_barracks", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:4000}) CREATE (g)-[r:Demand{max_demand: 1.29598, current_input: 0.37125193933814704, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4000}) CREATE (g)-[r:Demand{max_demand: 1.29598, current_input: 0.08454012171786703, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4000}) CREATE (g)-[r:Demand{max_demand: 1.29598, current_input: 0.4910961247526427, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4000}) CREATE (g)-[r:Demand{max_demand: 1.29598, current_input: 0.14951115132119644, level: 3}]->(b);
CREATE (n: Building {id: 4001, name:"building_naval_base", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:4001}) CREATE (g)-[r:Demand{max_demand: 9.0, current_input: 2.5191113467109876, level: 5}]->(b);
CREATE (n: Building {id: 4002, name:"building_naval_base", level:6});
MATCH (g: Goods{code: 5}), (b: Building{id:4002}) CREATE (g)-[r:Demand{max_demand: 9.99996, current_input: 2.799001411406223, level: 6}]->(b);
CREATE (n: Building {id: 4003, name:"building_naval_base", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:4003}) CREATE (g)-[r:Demand{max_demand: 2.63898, current_input: 0.7386538290825957, level: 3}]->(b);
CREATE (n: Building {id: 4004, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:4004}) CREATE (g)-[r:Demand{max_demand: 0.896, current_input: 0.2507915296281161, level: 1}]->(b);
CREATE (n: Building {id: 4005, name:"building_naval_base", level:9});
MATCH (g: Goods{code: 5}), (b: Building{id:4005}) CREATE (g)-[r:Demand{max_demand: 14.99994, current_input: 4.198502117109334, level: 9}]->(b);
CREATE (n: Building {id: 16781347, name:"building_conscription_center", level:20});
CREATE (n: Building {id: 4133, name:"building_trade_center", level:32});
CREATE (n: Building {id: 4134, name:"building_trade_center", level:65});
CREATE (n: Building {id: 4135, name:"building_trade_center", level:6});
CREATE (n: Building {id: 16781442, name:"building_conscription_center", level:12});
CREATE (n: Building {id: 4239, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:4239}) CREATE (g)-[r:Demand{max_demand: 19.9608, current_input: 78.58419597341711, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4239}) CREATE (g)-[r:Demand{max_demand: 39.9216, current_input: 12.724425467374813, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:4239}) CREATE (g)-[r:Demand{max_demand: 19.9608, current_input: 2.5017889896789276, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:4239}) CREATE (g)-[r:Demand{max_demand: 4.990195744680851, current_input: 1.1042411968875343, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:4239}) CREATE (b)-[r:Supply{max_supply: 69.8628, current_output: 29.086548992743364,level: 1}]->(g);
CREATE (n: Building {id: 4492, name:"building_conscription_center", level:8});
CREATE (n: Building {id: 4601, name:"building_artillery_foundries", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:4601}) CREATE (g)-[r:Demand{max_demand: 3.5535, current_input: 0.40995067533439683, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:4601}) CREATE (g)-[r:Demand{max_demand: 2.369, current_input: 0.2969188668064096, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4601}) CREATE (b)-[r:Supply{max_supply: 5.9225, current_output: 0.712774146286676,level: 1}]->(g);
CREATE (n: Building {id: 4676, name:"building_conscription_center", level:16});
CREATE (n: Building {id: 4831, name:"building_motor_industry", level:1});
MATCH (g: Goods{code: 30}), (b: Building{id:4831}) CREATE (g)-[r:Demand{max_demand: 0.34559999999999996, current_input: 0.05867839125528589, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:4831}) CREATE (b)-[r:Supply{max_supply: 0.4608, current_output: 0.07823785500704786,level: 1}]->(g);
CREATE (n: Building {id: 4928, name:"building_barracks", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:4928}) CREATE (g)-[r:Demand{max_demand: 0.135, current_input: 0.13497219187017162, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4928}) CREATE (g)-[r:Demand{max_demand: 0.27, current_input: 0.07734534762982433, level: 1}]->(b);
CREATE (n: Building {id: 4935, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:4935}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 157.47547218840407, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4935}) CREATE (g)-[r:Demand{max_demand: 79.9992, current_input: 25.498573650595446, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4935}) CREATE (g)-[r:Demand{max_demand: 99.999, current_input: 11.536416936193708, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4935}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 4.13353056407102, level: 2}]->(b);
CREATE (n: Building {id: 4961, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:4961}) CREATE (g)-[r:Demand{max_demand: 17.35582, current_input: 68.32858202874395, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4961}) CREATE (g)-[r:Demand{max_demand: 34.71165, current_input: 11.063830189035533, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4961}) CREATE (g)-[r:Demand{max_demand: 43.38956, current_input: 5.005650604885981, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4961}) CREATE (g)-[r:Demand{max_demand: 8.67791, current_input: 1.7935382462453398, level: 1}]->(b);
CREATE (n: Building {id: 4963, name:"building_conscription_center", level:9});
CREATE (n: Building {id: 4974, name:"building_conscription_center", level:6});
CREATE (n: Building {id: 5011, name:"building_conscription_center", level:7});
CREATE (n: Building {id: 5038, name:"building_conscription_center", level:11});
CREATE (n: Building {id: 5041, name:"building_conscription_center", level:16});
CREATE (n: Building {id: 5042, name:"building_conscription_center", level:8});
CREATE (n: Building {id: 5049, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5049}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 78.73773609420203, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5049}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 12.749286825297723, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5049}) CREATE (g)-[r:Demand{max_demand: 49.9995, current_input: 5.768208468096854, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5049}) CREATE (g)-[r:Demand{max_demand: 9.9999, current_input: 2.06676528203551, level: 1}]->(b);
CREATE (n: Building {id: 16782305, name:"building_trade_center", level:5});
CREATE (n: Building {id: 5133, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5133}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 78.73773609420203, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5133}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 12.749286825297723, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5133}) CREATE (g)-[r:Demand{max_demand: 49.9995, current_input: 5.768208468096854, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5133}) CREATE (g)-[r:Demand{max_demand: 9.9999, current_input: 2.06676528203551, level: 1}]->(b);
CREATE (n: Building {id: 5144, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5144}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 78.73773609420203, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5144}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 12.749286825297723, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5144}) CREATE (g)-[r:Demand{max_demand: 49.9995, current_input: 5.768208468096854, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5144}) CREATE (g)-[r:Demand{max_demand: 9.9999, current_input: 2.06676528203551, level: 1}]->(b);
CREATE (n: Building {id: 5149, name:"building_naval_base", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:5149}) CREATE (g)-[r:Demand{max_demand: 0.76131, current_input: 0.21309162881828247, level: 3}]->(b);
CREATE (n: Building {id: 5161, name:"building_naval_base", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:5161}) CREATE (g)-[r:Demand{max_demand: 0.08232, current_input: 0.02304147178458317, level: 3}]->(b);
CREATE (n: Building {id: 5207, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:5207}) CREATE (g)-[r:Demand{max_demand: 0.321, current_input: 0.08984830469935856, level: 1}]->(b);
CREATE (n: Building {id: 5209, name:"building_naval_base", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:5209}) CREATE (g)-[r:Demand{max_demand: 0.66798, current_input: 0.1869684441528895, level: 3}]->(b);
CREATE (n: Building {id: 5212, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:5212}) CREATE (g)-[r:Demand{max_demand: 1.194, current_input: 0.3342021053303243, level: 1}]->(b);
CREATE (n: Building {id: 5213, name:"building_naval_base", level:4});
MATCH (g: Goods{code: 5}), (b: Building{id:5213}) CREATE (g)-[r:Demand{max_demand: 0.777, current_input: 0.21748327959938193, level: 4}]->(b);
CREATE (n: Building {id: 5221, name:"building_trade_center", level:9});
CREATE (n: Building {id: 5224, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5224}) CREATE (g)-[r:Demand{max_demand: 8.8216, current_input: 34.729987936309996, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5224}) CREATE (g)-[r:Demand{max_demand: 17.6432, current_input: 5.623511668019, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:5224}) CREATE (g)-[r:Demand{max_demand: 8.8216, current_input: 1.1056561736679709, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:5224}) CREATE (g)-[r:Demand{max_demand: 2.205395744680851, current_input: 0.4880146914703459, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:5224}) CREATE (b)-[r:Supply{max_supply: 30.8756, current_output: 12.854690222555453,level: 1}]->(g);
CREATE (n: Building {id: 5226, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:5226}) CREATE (g)-[r:Demand{max_demand: 0.396, current_input: 0.11084089925528345, level: 1}]->(b);
CREATE (n: Building {id: 5227, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:5227}) CREATE (g)-[r:Demand{max_demand: 0.333, current_input: 0.09320711982830654, level: 1}]->(b);
CREATE (n: Building {id: 5229, name:"building_naval_base", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:5229}) CREATE (g)-[r:Demand{max_demand: 0.43899, current_input: 0.12287385445473961, level: 3}]->(b);
CREATE (n: Building {id: 5252, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:5252}) CREATE (g)-[r:Demand{max_demand: 0.756, current_input: 0.21160535312372294, level: 1}]->(b);
CREATE (n: Building {id: 5317, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:5317}) CREATE (g)-[r:Demand{max_demand: 0.738, current_input: 0.20656713043030098, level: 1}]->(b);
CREATE (n: Building {id: 5352, name:"building_barracks", level:4});
MATCH (g: Goods{code: 0}), (b: Building{id:5352}) CREATE (g)-[r:Demand{max_demand: 0.868, current_input: 0.8678212040245108, level: 4}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:5352}) CREATE (g)-[r:Demand{max_demand: 1.736, current_input: 0.49730193883472223, level: 4}]->(b);
CREATE (n: Building {id: 5353, name:"building_tobacco_plantation", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:5353}) CREATE (b)-[r:Supply{max_supply: 8.16, current_output: 8.16,level: 1}]->(g);
CREATE (n: Building {id: 5354, name:"building_barracks", level:2});
MATCH (g: Goods{code: 2}), (b: Building{id:5354}) CREATE (g)-[r:Demand{max_demand: 0.148, current_input: 0.009654422147135234, level: 2}]->(b);
CREATE (n: Building {id: 5356, name:"building_barracks", level:3});
MATCH (g: Goods{code: 2}), (b: Building{id:5356}) CREATE (g)-[r:Demand{max_demand: 1.72931, current_input: 0.11280735650852994, level: 3}]->(b);
CREATE (n: Building {id: 5364, name:"building_barracks", level:1});
MATCH (g: Goods{code: 2}), (b: Building{id:5364}) CREATE (g)-[r:Demand{max_demand: 0.072, current_input: 0.00469674590941714, level: 1}]->(b);
CREATE (n: Building {id: 5367, name:"building_barracks", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:5367}) CREATE (g)-[r:Demand{max_demand: 0.23, current_input: 0.2299526231862183, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:5367}) CREATE (g)-[r:Demand{max_demand: 0.46, current_input: 0.13177355522118217, level: 1}]->(b);
CREATE (n: Building {id: 5392, name:"building_barracks", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:5392}) CREATE (g)-[r:Demand{max_demand: 0.026, current_input: 0.025994644360181195, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:5392}) CREATE (g)-[r:Demand{max_demand: 0.052, current_input: 0.014896141025003199, level: 1}]->(b);
CREATE (n: Building {id: 5446, name:"building_arts_academy", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:5446}) CREATE (g)-[r:Demand{max_demand: 0.054, current_input: 0.007357877359086462, level: 1}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:5446}) CREATE (b)-[r:Supply{max_supply: 0.0216, current_output: 0.002943150943634585,level: 1}]->(g);
CREATE (n: Building {id: 5507, name:"building_motor_industry", level:1});
MATCH (g: Goods{code: 30}), (b: Building{id:5507}) CREATE (g)-[r:Demand{max_demand: 29.0868, current_input: 4.938560852905816, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:5507}) CREATE (b)-[r:Supply{max_supply: 38.782399999999996, current_output: 6.584747803874421,level: 1}]->(g);
CREATE (n: Building {id: 5621, name:"building_gold_fields", level:1});
MATCH (g: Goods{code: 50}), (b: Building{id:5621}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 5645, name:"building_steel_mills", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:5645}) CREATE (g)-[r:Demand{max_demand: 19.4151, current_input: 3.7675203348359636, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5645}) CREATE (g)-[r:Demand{max_demand: 38.8302, current_input: 4.479658565743547, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:5645}) CREATE (b)-[r:Supply{max_supply: 58.2453, current_output: 9.011024426561606,level: 1}]->(g);
CREATE (n: Building {id: 5690, name:"building_conscription_center", level:7});
CREATE (n: Building {id: 5711, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 5729, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 5776, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 16783025, name:"building_dye_plantation", level:1});
MATCH (g: Goods{code: 21}), (b: Building{id:16783025}) CREATE (b)-[r:Supply{max_supply: 3.6815, current_output: 3.6815,level: 1}]->(g);
