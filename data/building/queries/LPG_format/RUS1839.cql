CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:79.87342497654045, pop_demand:19.624740600295308});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:115.762935278352, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:95.82198289835594, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:21.462596485496434, pop_demand:7602.935267063429});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:21.35677710086855, pop_demand:120.83040706742239});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:12.497458060106265, pop_demand:252.54271142857166});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:18.213533652884998, pop_demand:1515.2562685714313});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:36.96413156314169, pop_demand:655.8951327577911});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:35.46845091240578, pop_demand:2020.2177410898205});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:32.093882451228765, pop_demand:1741.2639881190726});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:28.583043059205455, pop_demand:80.60261191018024});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:18.697102620341504, pop_demand:1010.6715529709107});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:44.03860325207872, pop_demand:524.4461000000003});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:73.90899895676797, pop_demand:10.864354498531332});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:63.83858113378089, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:30.76534957982723, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:50.52210252267003, pop_demand:30.073240742834862});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:58.81499195402688, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:106.26026369598651, pop_demand:126.2441835027407});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:30.842965859451432, pop_demand:322.46913446768195});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:44.31456492082245, pop_demand:522.8859218252315});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:38.605454824233526, pop_demand:2945.9667383268165});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:54.40792388565028, pop_demand:295.10612196873643});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:74.10416105297205, pop_demand:226.18964082665718});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:42.44447572822571, pop_demand:84.73872256484262});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:46.68849267622903, pop_demand:64.98936250489217});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:92.3825723537201, pop_demand:641.9692053782594});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:93.28648413811335, pop_demand:672.3002805352085});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:177.14902619743137, pop_demand:4.253219692321545});
CREATE (n: Building {id: 869, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:869}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 106.72083691531361, level: 10}]->(b);
CREATE (n: Building {id: 870, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:870}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 100.03432926764532, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:870}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 170.297261888143, level: 2}]->(b);
CREATE (n: Building {id: 871, name:"building_textile_mills", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:871}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 100.03432926764532, level: 2}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:871}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:871}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 15.0,level: 2}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:871}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 30.0,level: 2}]->(g);
CREATE (n: Building {id: 872, name:"building_glassworks", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:872}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 102.1783571328858, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:872}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:872}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 15.0,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:872}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 30.0,level: 3}]->(g);
CREATE (n: Building {id: 873, name:"building_arts_academy", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:873}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.672083691531363, level: 1}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:873}) CREATE (b)-[r:Supply{max_supply: 4.0, current_output: 4.0,level: 1}]->(g);
CREATE (n: Building {id: 874, name:"building_artillery_foundries", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:874}) CREATE (g)-[r:Demand{max_demand: 8.51445, current_input: 1.7490224733337625, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:874}) CREATE (g)-[r:Demand{max_demand: 5.6763, current_input: 8.202130207852978, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:874}) CREATE (b)-[r:Supply{max_supply: 14.19075, current_output: 8.552893727778136,level: 1}]->(g);
CREATE (n: Building {id: 875, name:"building_rye_farm", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:875}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.3,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:875}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
CREATE (n: Building {id: 876, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:876}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.363667014410673, level: 3}]->(b);
CREATE (n: Building {id: 877, name:"building_military_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:877}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 40.01373170705813, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:877}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 45.41260317017147, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:877}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.054181389677269, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:877}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 25.729878287956815,level: 1}]->(g);
CREATE (n: Building {id: 878, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:878}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 106.72083691531361, level: 10}]->(b);
CREATE (n: Building {id: 879, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:879}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 80.02746341411626, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:879}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 90.82520634034294, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:879}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 20.54181389677269, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:879}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.457351159849866, level: 2}]->(b);
CREATE (n: Building {id: 880, name:"building_tooling_workshops", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:880}) CREATE (g)-[r:Demand{max_demand: 209.99999999999997, current_input: 238.41616664340015, level: 7}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:880}) CREATE (g)-[r:Demand{max_demand: 140.0, current_input: 28.758539455481767, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:880}) CREATE (b)-[r:Supply{max_supply: 419.99999999999994, current_output: 253.13780918322263,level: 7}]->(g);
CREATE (n: Building {id: 881, name:"building_university", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:881}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.672083691531363, level: 2}]->(b);
CREATE (n: Building {id: 882, name:"building_textile_mills", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:882}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 150.05149390146798, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:882}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:882}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 22.5,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:882}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 45.0,level: 3}]->(g);
CREATE (n: Building {id: 883, name:"building_furniture_manufacturies", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:883}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 120.04119512117437, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:883}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 102.1783571328858, level: 6}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:883}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 130.04804515384458, level: 6}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:883}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 6}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:883}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 150.0,level: 6}]->(g);
CREATE (n: Building {id: 884, name:"building_logging_camp", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:884}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 18.643377899624664, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:884}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 223.72053479549595,level: 10}]->(g);
CREATE (n: Building {id: 885, name:"building_iron_mine", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:885}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.8643377899624665, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:885}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.457351159849866,level: 1}]->(g);
CREATE (n: Building {id: 886, name:"building_saint_basils_cathedral", level:1});
CREATE (n: Building {id: 887, name:"building_rye_farm", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:887}) CREATE (g)-[r:Demand{max_demand: 4.70775, current_input: 1.7553672461391603, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:887}) CREATE (b)-[r:Supply{max_supply: 23.53875, current_output: 8.776836230695803,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:887}) CREATE (b)-[r:Supply{max_supply: 47.0775, current_output: 17.553672461391606,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:887}) CREATE (b)-[r:Supply{max_supply: 23.53875, current_output: 8.776836230695803,level: 5}]->(g);
CREATE (n: Building {id: 888, name:"building_logging_camp", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:888}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 14.914702319699732, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:888}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 59.65880927879893,level: 8}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:888}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 59.65880927879893,level: 8}]->(g);
CREATE (n: Building {id: 889, name:"building_logging_camp", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:889}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.1860267397748, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:889}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 134.2323208772976,level: 6}]->(g);
CREATE (n: Building {id: 890, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:890}) CREATE (g)-[r:Demand{max_demand: 2.0033039215686275, current_input: 0.7469670411520793, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:890}) CREATE (b)-[r:Supply{max_supply: 10.016549019607842, current_output: 3.7348461724532784,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:890}) CREATE (b)-[r:Supply{max_supply: 14.023166666666667, current_output: 5.228783910321732,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:890}) CREATE (b)-[r:Supply{max_supply: 10.016549019607842, current_output: 3.7348461724532784,level: 3}]->(g);
CREATE (n: Building {id: 891, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:891}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 106.72083691531361, level: 10}]->(b);
CREATE (n: Building {id: 892, name:"building_food_industry", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:892}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 72.20012602653706, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:892}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 35.75950500324, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:892}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 47.232338575527464,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:892}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 80.9697232723328,level: 2}]->(g);
CREATE (n: Building {id: 893, name:"building_livestock_ranch", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:893}) CREATE (g)-[r:Demand{max_demand: 23.973499999999998, current_input: 21.63612151621483, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:893}) CREATE (b)-[r:Supply{max_supply: 47.946999999999996, current_output: 43.27224303242966,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:893}) CREATE (b)-[r:Supply{max_supply: 47.946999999999996, current_output: 43.27224303242966,level: 5}]->(g);
CREATE (n: Building {id: 894, name:"building_wheat_farm", level:45});
MATCH (g: Goods{code: 33}), (b: Building{id:894}) CREATE (g)-[r:Demand{max_demand: 43.5798, current_input: 16.24949360380126, level: 45}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:894}) CREATE (b)-[r:Supply{max_supply: 217.899, current_output: 81.24746801900629,level: 45}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:894}) CREATE (b)-[r:Supply{max_supply: 305.0586, current_output: 113.74645522660882,level: 45}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:894}) CREATE (b)-[r:Supply{max_supply: 217.899, current_output: 81.24746801900629,level: 45}]->(g);
CREATE (n: Building {id: 895, name:"building_textile_mills", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:895}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 150.05149390146798, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:895}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:895}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 22.5,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:895}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 45.0,level: 3}]->(g);
CREATE (n: Building {id: 896, name:"building_textile_mills", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:896}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 100.03432926764532, level: 2}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:896}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:896}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 15.0,level: 2}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:896}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 30.0,level: 2}]->(g);
CREATE (n: Building {id: 897, name:"building_food_industry", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:897}) CREATE (g)-[r:Demand{max_demand: 66.33039603960397, current_input: 59.863286918119016, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:897}) CREATE (g)-[r:Demand{max_demand: 24.873891089108913, current_input: 11.118475410637963, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:897}) CREATE (b)-[r:Supply{max_supply: 107.78689108910892, current_output: 72.72895619891852,level: 2}]->(g);
CREATE (n: Building {id: 898, name:"building_rye_farm", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:898}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.4914702319699733, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:898}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.457351159849867,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:898}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 22.372053479549596,level: 4}]->(g);
CREATE (n: Building {id: 899, name:"building_logging_camp", level:15});
MATCH (g: Goods{code: 33}), (b: Building{id:899}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 27.965066849436997, level: 15}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:899}) CREATE (b)-[r:Supply{max_supply: 900.0, current_output: 335.580802193244,level: 15}]->(g);
CREATE (n: Building {id: 900, name:"building_food_industry", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:900}) CREATE (g)-[r:Demand{max_demand: 107.14679411764706, current_input: 96.70015048291923, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:900}) CREATE (g)-[r:Demand{max_demand: 40.18004901960784, current_input: 17.96023329933869, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:900}) CREATE (b)-[r:Supply{max_supply: 174.11354901960783, current_output: 117.48271568401165,level: 3}]->(g);
CREATE (n: Building {id: 901, name:"building_rye_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:901}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.11860267397748, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:901}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 5.5930133698874,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:901}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 16.7790401096622,level: 3}]->(g);
CREATE (n: Building {id: 902, name:"building_logging_camp", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:902}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 14.914702319699732, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:902}) CREATE (b)-[r:Supply{max_supply: 480.0, current_output: 178.97642783639677,level: 8}]->(g);
CREATE (n: Building {id: 903, name:"building_paper_mills", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:903}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 102.1783571328858, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:903}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 3}]->(g);
CREATE (n: Building {id: 904, name:"building_logging_camp", level:12});
MATCH (g: Goods{code: 33}), (b: Building{id:904}) CREATE (g)-[r:Demand{max_demand: 59.163, current_input: 22.05996333350988, level: 12}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:904}) CREATE (b)-[r:Supply{max_supply: 236.652, current_output: 88.23985333403952,level: 12}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:904}) CREATE (b)-[r:Supply{max_supply: 236.652, current_output: 88.23985333403952,level: 12}]->(g);
CREATE (n: Building {id: 905, name:"building_rye_farm", level:7});
MATCH (g: Goods{code: 33}), (b: Building{id:905}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 2.6100729059474532, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:905}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 13.050364529737266,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:905}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 39.1510935892118,level: 7}]->(g);
CREATE (n: Building {id: 906, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:906}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.8643377899624665, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:906}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.457351159849866,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:906}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.457351159849866,level: 1}]->(g);
CREATE (n: Building {id: 907, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:907}) CREATE (b)-[r:Supply{max_supply: 49.485, current_output: 49.97985,level: 2}]->(g);
CREATE (n: Building {id: 908, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:908}) CREATE (g)-[r:Demand{max_demand: 0.05, current_input: 0.03454555671470225, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:908}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.34545556714702247,level: 1}]->(g);
CREATE (n: Building {id: 909, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:909}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.454555671470225, level: 1}]->(b);
CREATE (n: Building {id: 910, name:"building_arms_industry", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:910}) CREATE (g)-[r:Demand{max_demand: 6.7254, current_input: 1.3815191518135506, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:910}) CREATE (g)-[r:Demand{max_demand: 6.7254, current_input: 9.718056920862958, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:910}) CREATE (b)-[r:Supply{max_supply: 20.1762, current_output: 12.160378727720328,level: 1}]->(g);
CREATE (n: Building {id: 911, name:"building_logging_camp", level:15});
MATCH (g: Goods{code: 33}), (b: Building{id:911}) CREATE (g)-[r:Demand{max_demand: 73.44449999999999, current_input: 27.38507136297967, level: 15}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:911}) CREATE (b)-[r:Supply{max_supply: 881.334, current_output: 328.620856355756,level: 15}]->(g);
CREATE (n: Building {id: 912, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:912}) CREATE (b)-[r:Supply{max_supply: 48.285000000000004, current_output: 48.76785,level: 2}]->(g);
CREATE (n: Building {id: 913, name:"building_port", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:913}) CREATE (g)-[r:Demand{max_demand: 9.992, current_input: 6.903584053866097, level: 2}]->(b);
CREATE (n: Building {id: 914, name:"building_livestock_ranch", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:914}) CREATE (g)-[r:Demand{max_demand: 49.185, current_input: 44.38953998269032, level: 10}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:914}) CREATE (b)-[r:Supply{max_supply: 98.37, current_output: 88.77907996538065,level: 10}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:914}) CREATE (b)-[r:Supply{max_supply: 98.37, current_output: 88.77907996538065,level: 10}]->(g);
CREATE (n: Building {id: 915, name:"building_wheat_farm", level:20});
MATCH (g: Goods{code: 33}), (b: Building{id:915}) CREATE (g)-[r:Demand{max_demand: 19.753194029850746, current_input: 7.365325220462346, level: 20}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:915}) CREATE (b)-[r:Supply{max_supply: 395.06399999999996, current_output: 147.30654893074637,level: 20}]->(g);
CREATE (n: Building {id: 916, name:"building_vineyard_plantation", level:8});
MATCH (g: Goods{code: 39}), (b: Building{id:916}) CREATE (b)-[r:Supply{max_supply: 159.86719672131147, current_output: 195.03798,level: 8}]->(g);
CREATE (n: Building {id: 917, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:917}) CREATE (g)-[r:Demand{max_demand: 0.6846930693069307, current_input: 0.47306206515812266, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:917}) CREATE (b)-[r:Supply{max_supply: 6.8469999999999995, current_output: 4.730668536511326,level: 2}]->(g);
CREATE (n: Building {id: 918, name:"building_rye_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:918}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.11860267397748, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:918}) CREATE (b)-[r:Supply{max_supply: 15.000000000000002, current_output: 5.593013369887401,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:918}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 16.7790401096622,level: 3}]->(g);
CREATE (n: Building {id: 919, name:"building_logging_camp", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:919}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 18.643377899624664, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:919}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 223.72053479549595,level: 10}]->(g);
CREATE (n: Building {id: 920, name:"building_paper_mills", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:920}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 102.1783571328858, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:920}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 3}]->(g);
CREATE (n: Building {id: 921, name:"building_logging_camp", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:921}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 9.321688949812332, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:921}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 37.28675579924933,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:921}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 37.28675579924933,level: 5}]->(g);
CREATE (n: Building {id: 922, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:922}) CREATE (g)-[r:Demand{max_demand: 16.508, current_input: 33.027334151005775, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:922}) CREATE (g)-[r:Demand{max_demand: 33.016, current_input: 37.48356265665953, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:922}) CREATE (b)-[r:Supply{max_supply: 28.889, current_output: 28.889,level: 1}]->(g);
CREATE (n: Building {id: 923, name:"building_military_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:923}) CREATE (g)-[r:Demand{max_demand: 17.076, current_input: 34.16372413148623, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:923}) CREATE (g)-[r:Demand{max_demand: 34.152, current_input: 38.7732805866924, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:923}) CREATE (g)-[r:Demand{max_demand: 8.538, current_input: 1.7538600705064524, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:923}) CREATE (b)-[r:Supply{max_supply: 29.883, current_output: 21.96817008225753,level: 1}]->(g);
CREATE (n: Building {id: 924, name:"building_livestock_ranch", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:924}) CREATE (g)-[r:Demand{max_demand: 35.44679487179487, current_input: 31.99078821225497, level: 8}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:924}) CREATE (b)-[r:Supply{max_supply: 70.89359829059829, current_output: 63.98158413819862,level: 8}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:924}) CREATE (b)-[r:Supply{max_supply: 70.89359829059829, current_output: 63.98158413819862,level: 8}]->(g);
CREATE (n: Building {id: 925, name:"building_wheat_farm", level:15});
MATCH (g: Goods{code: 33}), (b: Building{id:925}) CREATE (g)-[r:Demand{max_demand: 14.949744186046512, current_input: 5.5742746072636375, level: 15}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:925}) CREATE (b)-[r:Supply{max_supply: 74.74874418604651, current_output: 27.871381707656745,level: 15}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:925}) CREATE (b)-[r:Supply{max_supply: 104.6482480620155, current_output: 39.01993670307639,level: 15}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:925}) CREATE (b)-[r:Supply{max_supply: 74.74874418604651, current_output: 27.871381707656745,level: 15}]->(g);
CREATE (n: Building {id: 926, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:926}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.363667014410673, level: 3}]->(b);
CREATE (n: Building {id: 927, name:"building_rye_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:927}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.11860267397748, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:927}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 5.5930133698874,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:927}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 16.7790401096622,level: 3}]->(g);
CREATE (n: Building {id: 928, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:928}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.728675579924933, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:928}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.914702319699732,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:928}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.914702319699732,level: 2}]->(g);
CREATE (n: Building {id: 934, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:934}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.5125078766585665, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:934}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 9.025015753317133,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:934}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 9.025015753317133,level: 1}]->(g);
CREATE (n: Building {id: 935, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:935}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.728675579924933, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:935}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 44.74410695909919,level: 2}]->(g);
CREATE (n: Building {id: 936, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:936}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 40.01373170705813, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:936}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 45.41260317017147, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:936}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 35.0,level: 1}]->(g);
CREATE (n: Building {id: 937, name:"building_military_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:937}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 40.01373170705813, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:937}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 45.41260317017147, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:937}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.054181389677269, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:937}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 25.729878287956815,level: 1}]->(g);
CREATE (n: Building {id: 938, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:938}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.728675579924933, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:938}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.914702319699732,level: 2}]->(g);
CREATE (n: Building {id: 939, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:939}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.728675579924933, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:939}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.914702319699732,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:939}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.914702319699732,level: 2}]->(g);
CREATE (n: Building {id: 940, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:940}) CREATE (g)-[r:Demand{max_demand: 0.4326, current_input: 0.2988881566956038, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:940}) CREATE (b)-[r:Supply{max_supply: 4.326, current_output: 2.9888815669560382,level: 1}]->(g);
CREATE (n: Building {id: 941, name:"building_rye_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:941}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.3728675579924933, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:941}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.8643377899624667,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:941}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 5.5930133698874,level: 1}]->(g);
CREATE (n: Building {id: 942, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:942}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.454555671470225, level: 1}]->(b);
CREATE (n: Building {id: 943, name:"building_rye_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:943}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.11860267397748, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:943}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 5.5930133698874,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:943}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 16.7790401096622,level: 3}]->(g);
CREATE (n: Building {id: 944, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:944}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.363667014410673, level: 3}]->(b);
CREATE (n: Building {id: 945, name:"building_rye_farm", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:945}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.4914702319699733, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:945}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.457351159849867,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:945}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 22.372053479549596,level: 4}]->(g);
CREATE (n: Building {id: 946, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:946}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.454555671470225, level: 1}]->(b);
CREATE (n: Building {id: 947, name:"building_furniture_manufacturies", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:947}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 20.006865853529064, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:947}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 17.0297261888143, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:947}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 21.674674192307428, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:947}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:947}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 948, name:"building_paper_mills", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:948}) CREATE (g)-[r:Demand{max_demand: 87.16139130434782, current_input: 98.95564187660953, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:948}) CREATE (b)-[r:Supply{max_supply: 116.2151956521739, current_output: 116.2151956521739,level: 3}]->(g);
CREATE (n: Building {id: 949, name:"building_rye_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:949}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7457351159849867, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:949}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 3.7286755799249334,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:949}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 11.1860267397748,level: 2}]->(g);
CREATE (n: Building {id: 950, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:950}) CREATE (g)-[r:Demand{max_demand: 29.982, current_input: 34.03901670620202, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:950}) CREATE (b)-[r:Supply{max_supply: 29.982, current_output: 29.982,level: 1}]->(g);
CREATE (n: Building {id: 951, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:951}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.5930133698874, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:951}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 22.3720534795496,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:951}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 22.3720534795496,level: 3}]->(g);
CREATE (n: Building {id: 959, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:959}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 50.01716463382266, level: 1}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:959}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:959}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 7.5,level: 1}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:959}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 15.0,level: 1}]->(g);
CREATE (n: Building {id: 960, name:"building_rye_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:960}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.11860267397748, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:960}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 5.5930133698874,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:960}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 16.7790401096622,level: 3}]->(g);
CREATE (n: Building {id: 961, name:"building_paper_mills", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:961}) CREATE (g)-[r:Demand{max_demand: 29.244, current_input: 33.20115417771236, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:961}) CREATE (b)-[r:Supply{max_supply: 38.992, current_output: 38.992,level: 1}]->(g);
CREATE (n: Building {id: 962, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:962}) CREATE (g)-[r:Demand{max_demand: 14.970441176470588, current_input: 13.510846745175453, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:962}) CREATE (b)-[r:Supply{max_supply: 29.940892156862745, current_output: 27.021702338405564,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:962}) CREATE (b)-[r:Supply{max_supply: 29.940892156862745, current_output: 27.021702338405564,level: 3}]->(g);
CREATE (n: Building {id: 963, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:963}) CREATE (g)-[r:Demand{max_demand: 27.1527, current_input: 30.826869752467868, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:963}) CREATE (g)-[r:Demand{max_demand: 4.525444444444444, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:963}) CREATE (b)-[r:Supply{max_supply: 9.0509, current_output: 4.52545,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:963}) CREATE (b)-[r:Supply{max_supply: 18.1018, current_output: 9.0509,level: 1}]->(g);
CREATE (n: Building {id: 1031, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1031}) CREATE (g)-[r:Demand{max_demand: 0.447, current_input: 0.3088372770294381, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1031}) CREATE (b)-[r:Supply{max_supply: 4.47, current_output: 3.088372770294381,level: 1}]->(g);
CREATE (n: Building {id: 1348, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1348}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.454555671470225, level: 1}]->(b);
CREATE (n: Building {id: 1996, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1996}) CREATE (g)-[r:Demand{max_demand: 0.95733, current_input: 0.3569572992929536, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1996}) CREATE (b)-[r:Supply{max_supply: 4.78665, current_output: 1.784786496464768,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1996}) CREATE (b)-[r:Supply{max_supply: 6.70131, current_output: 2.4987010950506754,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1996}) CREATE (b)-[r:Supply{max_supply: 4.78665, current_output: 1.784786496464768,level: 1}]->(g);
CREATE (n: Building {id: 1997, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1997}) CREATE (g)-[r:Demand{max_demand: 0.8127, current_input: 0.7334630302720834, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1997}) CREATE (g)-[r:Demand{max_demand: 0.8127, current_input: 0.30302946438049927, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1997}) CREATE (b)-[r:Supply{max_supply: 1.6254, current_output: 1.0364924946525826,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1997}) CREATE (b)-[r:Supply{max_supply: 3.2508, current_output: 2.0729849893051653,level: 1}]->(g);
CREATE (n: Building {id: 1998, name:"building_tea_plantation", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:1998}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 2001, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:2001}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.3728675579924933, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2001}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.8643377899624667,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2001}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 2.6100729059474532,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2001}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.8643377899624667,level: 1}]->(g);
CREATE (n: Building {id: 2002, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2002}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 2003, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2003}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.5125078766585665, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2003}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 9.025015753317133,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2003}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 9.025015753317133,level: 1}]->(g);
CREATE (n: Building {id: 2007, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:2007}) CREATE (g)-[r:Demand{max_demand: 1.952, current_input: 0.7278374732013468, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2007}) CREATE (b)-[r:Supply{max_supply: 9.76, current_output: 3.6391873660067344,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2007}) CREATE (b)-[r:Supply{max_supply: 13.664, current_output: 5.094862312409428,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2007}) CREATE (b)-[r:Supply{max_supply: 9.76, current_output: 3.6391873660067344,level: 2}]->(g);
CREATE (n: Building {id: 2008, name:"building_rice_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:2008}) CREATE (g)-[r:Demand{max_demand: 1.99888, current_input: 0.745317504320035, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2008}) CREATE (b)-[r:Supply{max_supply: 9.9944, current_output: 3.726587521600175,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2008}) CREATE (b)-[r:Supply{max_supply: 11.99328, current_output: 4.4719050259202096,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2008}) CREATE (b)-[r:Supply{max_supply: 17.98992, current_output: 6.707857538880315,level: 1}]->(g);
CREATE (n: Building {id: 2009, name:"building_tea_plantation", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:2009}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 2010, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2010}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 50.01716463382266, level: 1}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2010}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2010}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 7.5,level: 1}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2010}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 15.0,level: 1}]->(g);
CREATE (n: Building {id: 2011, name:"building_arms_industry", level:5});
MATCH (g: Goods{code: 24}), (b: Building{id:2011}) CREATE (g)-[r:Demand{max_demand: 45.116, current_input: 9.267644757667966, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2011}) CREATE (g)-[r:Demand{max_demand: 45.116, current_input: 65.1916400573428, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:2011}) CREATE (b)-[r:Supply{max_supply: 135.34799999999998, current_output: 81.57546713650194,level: 5}]->(g);
CREATE (n: Building {id: 2012, name:"building_food_industry", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2012}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 72.20012602653706, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:2012}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 13.409814376215, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:2012}) CREATE (b)-[r:Supply{max_supply: 130.0, current_output: 87.71720021169385,level: 2}]->(g);
CREATE (n: Building {id: 2013, name:"building_rye_farm", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:2013}) CREATE (g)-[r:Demand{max_demand: 5.566619047619048, current_input: 2.075611650560213, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2013}) CREATE (b)-[r:Supply{max_supply: 27.833095238095236, current_output: 10.378058252801065,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2013}) CREATE (b)-[r:Supply{max_supply: 83.49929523809524, current_output: 31.1341783095228,level: 6}]->(g);
CREATE (n: Building {id: 2014, name:"building_iron_mine", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:2014}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.8643377899624665, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2014}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.457351159849866,level: 1}]->(g);
CREATE (n: Building {id: 2015, name:"building_government_administration", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:2015}) CREATE (g)-[r:Demand{max_demand: 42.134, current_input: 44.96575742589824, level: 5}]->(b);
CREATE (n: Building {id: 2016, name:"building_arms_industry", level:3});
MATCH (g: Goods{code: 24}), (b: Building{id:2016}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.162544169031808, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2016}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 43.349348384614856, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:2016}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 54.24381625354771,level: 3}]->(g);
CREATE (n: Building {id: 2017, name:"building_paper_mills", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2017}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 68.1189047552572, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2017}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 80.0,level: 2}]->(g);
CREATE (n: Building {id: 2018, name:"building_food_industry", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2018}) CREATE (g)-[r:Demand{max_demand: 116.48639215686273, current_input: 105.12915242627635, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:2018}) CREATE (g)-[r:Demand{max_demand: 43.68239215686275, current_input: 19.525759011085317, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:2018}) CREATE (b)-[r:Supply{max_supply: 189.29039215686274, current_output: 127.72325559210442,level: 3}]->(g);
CREATE (n: Building {id: 2019, name:"building_rye_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:2019}) CREATE (g)-[r:Demand{max_demand: 1.6950990099009902, current_input: 0.6320474283772755, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2019}) CREATE (b)-[r:Supply{max_supply: 8.47549504950495, current_output: 3.160237141886377,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2019}) CREATE (b)-[r:Supply{max_supply: 25.42649504950495, current_output: 9.480715117417132,level: 2}]->(g);
CREATE (n: Building {id: 2020, name:"building_logging_camp", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:2020}) CREATE (g)-[r:Demand{max_demand: 22.319499999999998, current_input: 8.322217460613453, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2020}) CREATE (b)-[r:Supply{max_supply: 89.27799999999999, current_output: 33.28886984245381,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2020}) CREATE (b)-[r:Supply{max_supply: 89.27799999999999, current_output: 33.28886984245381,level: 5}]->(g);
CREATE (n: Building {id: 2021, name:"building_iron_mine", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:2021}) CREATE (g)-[r:Demand{max_demand: 39.998, current_input: 14.913956584583747, level: 8}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2021}) CREATE (b)-[r:Supply{max_supply: 159.992, current_output: 59.65582633833499,level: 8}]->(g);
CREATE (n: Building {id: 2022, name:"building_rye_farm", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:2022}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.8643377899624665, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2022}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 9.321688949812332,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2022}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 27.965066849436994,level: 5}]->(g);
CREATE (n: Building {id: 2023, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2023}) CREATE (g)-[r:Demand{max_demand: 1.5028918918918917, current_input: 1.3563622999856912, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2023}) CREATE (g)-[r:Demand{max_demand: 1.5028918918918917, current_input: 0.5603796296564478, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2023}) CREATE (b)-[r:Supply{max_supply: 3.0057927927927923, current_output: 1.9167476745481453,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2023}) CREATE (b)-[r:Supply{max_supply: 6.011594594594594, current_output: 3.8335010940022967,level: 2}]->(g);
CREATE (n: Building {id: 2024, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:2024}) CREATE (g)-[r:Demand{max_demand: 0.31999999999999995, current_input: 0.17869600176671327, level: 1}]->(b);
CREATE (n: Building {id: 2685, name:"building_iron_mine", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:2685}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.457351159849866, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2685}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 29.829404639399463,level: 4}]->(g);
CREATE (n: Building {id: 2686, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:2686}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.728675579924933, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2686}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 44.74410695909919,level: 2}]->(g);
CREATE (n: Building {id: 2687, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2687}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.454555671470225, level: 1}]->(b);
CREATE (n: Building {id: 2716, name:"building_subsistence_farms", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2716}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 1}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2716}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2716}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2716}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2716}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2716}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2716}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 1}]->(g);
CREATE (n: Building {id: 2725, name:"building_subsistence_farms", level:38});
MATCH (g: Goods{code: 7}), (b: Building{id:2725}) CREATE (b)-[r:Supply{max_supply: 63.92929565217393, current_output: 73.51869,level: 38}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2725}) CREATE (b)-[r:Supply{max_supply: 12.785852173913044, current_output: 14.70373,level: 38}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2725}) CREATE (b)-[r:Supply{max_supply: 12.785852173913044, current_output: 14.70373,level: 38}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2725}) CREATE (b)-[r:Supply{max_supply: 12.785852173913044, current_output: 14.70373,level: 38}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2725}) CREATE (b)-[r:Supply{max_supply: 12.785852173913044, current_output: 14.70373,level: 38}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2725}) CREATE (b)-[r:Supply{max_supply: 12.785852173913044, current_output: 14.70373,level: 38}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2725}) CREATE (b)-[r:Supply{max_supply: 12.785852173913044, current_output: 14.70373,level: 38}]->(g);
CREATE (n: Building {id: 2729, name:"building_subsistence_farms", level:37});
MATCH (g: Goods{code: 7}), (b: Building{id:2729}) CREATE (b)-[r:Supply{max_supply: 45.04287, current_output: 45.04287,level: 37}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2729}) CREATE (b)-[r:Supply{max_supply: 9.00857, current_output: 9.00857,level: 37}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2729}) CREATE (b)-[r:Supply{max_supply: 9.00857, current_output: 9.00857,level: 37}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2729}) CREATE (b)-[r:Supply{max_supply: 9.00857, current_output: 9.00857,level: 37}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2729}) CREATE (b)-[r:Supply{max_supply: 9.00857, current_output: 9.00857,level: 37}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2729}) CREATE (b)-[r:Supply{max_supply: 9.00857, current_output: 9.00857,level: 37}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2729}) CREATE (b)-[r:Supply{max_supply: 9.00857, current_output: 9.00857,level: 37}]->(g);
CREATE (n: Building {id: 2730, name:"building_urban_center", level:8});
MATCH (g: Goods{code: 15}), (b: Building{id:2730}) CREATE (b)-[r:Supply{max_supply: 118.818, current_output: 127.13526,level: 8}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2730}) CREATE (b)-[r:Supply{max_supply: 39.605999999999995, current_output: 42.37842,level: 8}]->(g);
CREATE (n: Building {id: 2731, name:"building_subsistence_pastures", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:2731}) CREATE (b)-[r:Supply{max_supply: 2.4006, current_output: 2.4006,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2731}) CREATE (b)-[r:Supply{max_supply: 3.6009, current_output: 3.6009,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2731}) CREATE (b)-[r:Supply{max_supply: 1.2003, current_output: 1.2003,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2731}) CREATE (b)-[r:Supply{max_supply: 2.4006, current_output: 2.4006,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2731}) CREATE (b)-[r:Supply{max_supply: 2.4006, current_output: 2.4006,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2731}) CREATE (b)-[r:Supply{max_supply: 2.4006, current_output: 2.4006,level: 20}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2731}) CREATE (b)-[r:Supply{max_supply: 7.96999, current_output: 7.96999,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2731}) CREATE (b)-[r:Supply{max_supply: 2.4006, current_output: 2.4006,level: 20}]->(g);
CREATE (n: Building {id: 3454, name:"building_subsistence_farms", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3454}) CREATE (b)-[r:Supply{max_supply: 35.0613, current_output: 35.0613,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3454}) CREATE (b)-[r:Supply{max_supply: 7.01226, current_output: 7.01226,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3454}) CREATE (b)-[r:Supply{max_supply: 7.01226, current_output: 7.01226,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3454}) CREATE (b)-[r:Supply{max_supply: 7.01226, current_output: 7.01226,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3454}) CREATE (b)-[r:Supply{max_supply: 7.01226, current_output: 7.01226,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3454}) CREATE (b)-[r:Supply{max_supply: 7.01226, current_output: 7.01226,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3454}) CREATE (b)-[r:Supply{max_supply: 7.01226, current_output: 7.01226,level: 18}]->(g);
CREATE (n: Building {id: 3455, name:"building_subsistence_farms", level:22});
MATCH (g: Goods{code: 7}), (b: Building{id:3455}) CREATE (b)-[r:Supply{max_supply: 40.79515, current_output: 40.79515,level: 22}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3455}) CREATE (b)-[r:Supply{max_supply: 8.15903, current_output: 8.15903,level: 22}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3455}) CREATE (b)-[r:Supply{max_supply: 8.15903, current_output: 8.15903,level: 22}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3455}) CREATE (b)-[r:Supply{max_supply: 8.15903, current_output: 8.15903,level: 22}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3455}) CREATE (b)-[r:Supply{max_supply: 8.15903, current_output: 8.15903,level: 22}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3455}) CREATE (b)-[r:Supply{max_supply: 8.15903, current_output: 8.15903,level: 22}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3455}) CREATE (b)-[r:Supply{max_supply: 8.15903, current_output: 8.15903,level: 22}]->(g);
CREATE (n: Building {id: 3456, name:"building_subsistence_farms", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:3456}) CREATE (b)-[r:Supply{max_supply: 94.18, current_output: 94.18,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3456}) CREATE (b)-[r:Supply{max_supply: 18.836, current_output: 18.836,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3456}) CREATE (b)-[r:Supply{max_supply: 18.836, current_output: 18.836,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3456}) CREATE (b)-[r:Supply{max_supply: 18.836, current_output: 18.836,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3456}) CREATE (b)-[r:Supply{max_supply: 18.836, current_output: 18.836,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3456}) CREATE (b)-[r:Supply{max_supply: 18.836, current_output: 18.836,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3456}) CREATE (b)-[r:Supply{max_supply: 18.836, current_output: 18.836,level: 50}]->(g);
CREATE (n: Building {id: 3472, name:"building_subsistence_farms", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 5.43547, current_output: 5.43547,level: 3}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 1.08709, current_output: 1.08709,level: 3}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 1.08709, current_output: 1.08709,level: 3}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 1.08709, current_output: 1.08709,level: 3}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 1.08709, current_output: 1.08709,level: 3}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 1.08709, current_output: 1.08709,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 1.08709, current_output: 1.08709,level: 3}]->(g);
CREATE (n: Building {id: 3584, name:"building_subsistence_farms", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 72.1433, current_output: 72.1433,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 14.42866, current_output: 14.42866,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 14.42866, current_output: 14.42866,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 14.42866, current_output: 14.42866,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 14.42866, current_output: 14.42866,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 14.42866, current_output: 14.42866,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 14.42866, current_output: 14.42866,level: 29}]->(g);
CREATE (n: Building {id: 3585, name:"building_subsistence_farms", level:57});
MATCH (g: Goods{code: 7}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 141.80317, current_output: 141.80317,level: 57}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 28.36063, current_output: 28.36063,level: 57}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 28.36063, current_output: 28.36063,level: 57}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 28.36063, current_output: 28.36063,level: 57}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 28.36063, current_output: 28.36063,level: 57}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 28.36063, current_output: 28.36063,level: 57}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 28.36063, current_output: 28.36063,level: 57}]->(g);
CREATE (n: Building {id: 3586, name:"building_subsistence_farms", level:56});
MATCH (g: Goods{code: 7}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 138.9108, current_output: 138.9108,level: 56}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 27.78216, current_output: 27.78216,level: 56}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 27.78216, current_output: 27.78216,level: 56}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 27.78216, current_output: 27.78216,level: 56}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 27.78216, current_output: 27.78216,level: 56}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 27.78216, current_output: 27.78216,level: 56}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 27.78216, current_output: 27.78216,level: 56}]->(g);
CREATE (n: Building {id: 3587, name:"building_subsistence_pastures", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 1.9671, current_output: 1.9671,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 2.95065, current_output: 2.95065,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 0.98355, current_output: 0.98355,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 1.9671, current_output: 1.9671,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 1.9671, current_output: 1.9671,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 1.9671, current_output: 1.9671,level: 20}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 6.53077, current_output: 6.53077,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 1.9671, current_output: 1.9671,level: 20}]->(g);
CREATE (n: Building {id: 3588, name:"building_subsistence_pastures", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3588}) CREATE (b)-[r:Supply{max_supply: 1.1614, current_output: 1.1614,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3588}) CREATE (b)-[r:Supply{max_supply: 1.7421, current_output: 1.7421,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3588}) CREATE (b)-[r:Supply{max_supply: 0.5807, current_output: 0.5807,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3588}) CREATE (b)-[r:Supply{max_supply: 1.1614, current_output: 1.1614,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3588}) CREATE (b)-[r:Supply{max_supply: 1.1614, current_output: 1.1614,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3588}) CREATE (b)-[r:Supply{max_supply: 1.1614, current_output: 1.1614,level: 20}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3588}) CREATE (b)-[r:Supply{max_supply: 3.85584, current_output: 3.85584,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3588}) CREATE (b)-[r:Supply{max_supply: 1.1614, current_output: 1.1614,level: 20}]->(g);
CREATE (n: Building {id: 3589, name:"building_subsistence_farms", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:3589}) CREATE (b)-[r:Supply{max_supply: 16.196, current_output: 16.196,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3589}) CREATE (b)-[r:Supply{max_supply: 3.2392, current_output: 3.2392,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3589}) CREATE (b)-[r:Supply{max_supply: 3.2392, current_output: 3.2392,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3589}) CREATE (b)-[r:Supply{max_supply: 3.2392, current_output: 3.2392,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3589}) CREATE (b)-[r:Supply{max_supply: 3.2392, current_output: 3.2392,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3589}) CREATE (b)-[r:Supply{max_supply: 3.2392, current_output: 3.2392,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3589}) CREATE (b)-[r:Supply{max_supply: 3.2392, current_output: 3.2392,level: 10}]->(g);
CREATE (n: Building {id: 3590, name:"building_subsistence_farms", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:3590}) CREATE (b)-[r:Supply{max_supply: 20.38687, current_output: 20.38687,level: 15}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3590}) CREATE (b)-[r:Supply{max_supply: 4.07737, current_output: 4.07737,level: 15}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3590}) CREATE (b)-[r:Supply{max_supply: 4.07737, current_output: 4.07737,level: 15}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3590}) CREATE (b)-[r:Supply{max_supply: 4.07737, current_output: 4.07737,level: 15}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3590}) CREATE (b)-[r:Supply{max_supply: 4.07737, current_output: 4.07737,level: 15}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3590}) CREATE (b)-[r:Supply{max_supply: 4.07737, current_output: 4.07737,level: 15}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3590}) CREATE (b)-[r:Supply{max_supply: 4.07737, current_output: 4.07737,level: 15}]->(g);
CREATE (n: Building {id: 3591, name:"building_subsistence_farms", level:145});
MATCH (g: Goods{code: 7}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 361.43062, current_output: 361.43062,level: 145}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 72.28612, current_output: 72.28612,level: 145}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 72.28612, current_output: 72.28612,level: 145}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 72.28612, current_output: 72.28612,level: 145}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 72.28612, current_output: 72.28612,level: 145}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 72.28612, current_output: 72.28612,level: 145}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 72.28612, current_output: 72.28612,level: 145}]->(g);
CREATE (n: Building {id: 3592, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3592}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3592}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 3598, name:"building_subsistence_pastures", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 2.31739, current_output: 2.31739,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 3.47608, current_output: 3.47608,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 1.15869, current_output: 1.15869,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 2.31739, current_output: 2.31739,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 2.31739, current_output: 2.31739,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 2.31739, current_output: 2.31739,level: 29}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 7.69373, current_output: 7.69373,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3598}) CREATE (b)-[r:Supply{max_supply: 2.31739, current_output: 2.31739,level: 29}]->(g);
CREATE (n: Building {id: 3604, name:"building_subsistence_pastures", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:3604}) CREATE (b)-[r:Supply{max_supply: 2.97258, current_output: 2.97258,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3604}) CREATE (b)-[r:Supply{max_supply: 4.45887, current_output: 4.45887,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3604}) CREATE (b)-[r:Supply{max_supply: 1.48629, current_output: 1.48629,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3604}) CREATE (b)-[r:Supply{max_supply: 2.97258, current_output: 2.97258,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3604}) CREATE (b)-[r:Supply{max_supply: 2.97258, current_output: 2.97258,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3604}) CREATE (b)-[r:Supply{max_supply: 2.97258, current_output: 2.97258,level: 6}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3604}) CREATE (b)-[r:Supply{max_supply: 9.86896, current_output: 9.86896,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3604}) CREATE (b)-[r:Supply{max_supply: 2.97258, current_output: 2.97258,level: 6}]->(g);
CREATE (n: Building {id: 3606, name:"building_subsistence_pastures", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:3606}) CREATE (b)-[r:Supply{max_supply: 2.77479, current_output: 2.77479,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3606}) CREATE (b)-[r:Supply{max_supply: 4.16218, current_output: 4.16218,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3606}) CREATE (b)-[r:Supply{max_supply: 1.38739, current_output: 1.38739,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3606}) CREATE (b)-[r:Supply{max_supply: 2.77479, current_output: 2.77479,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3606}) CREATE (b)-[r:Supply{max_supply: 2.77479, current_output: 2.77479,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3606}) CREATE (b)-[r:Supply{max_supply: 2.77479, current_output: 2.77479,level: 9}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3606}) CREATE (b)-[r:Supply{max_supply: 9.2123, current_output: 9.2123,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3606}) CREATE (b)-[r:Supply{max_supply: 2.77479, current_output: 2.77479,level: 9}]->(g);
CREATE (n: Building {id: 3609, name:"building_subsistence_pastures", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3609}) CREATE (b)-[r:Supply{max_supply: 0.18, current_output: 0.18,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3609}) CREATE (b)-[r:Supply{max_supply: 0.27, current_output: 0.27,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3609}) CREATE (b)-[r:Supply{max_supply: 0.09, current_output: 0.09,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3609}) CREATE (b)-[r:Supply{max_supply: 0.18, current_output: 0.18,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3609}) CREATE (b)-[r:Supply{max_supply: 0.18, current_output: 0.18,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3609}) CREATE (b)-[r:Supply{max_supply: 0.18, current_output: 0.18,level: 20}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3609}) CREATE (b)-[r:Supply{max_supply: 0.5976, current_output: 0.5976,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3609}) CREATE (b)-[r:Supply{max_supply: 0.18, current_output: 0.18,level: 20}]->(g);
CREATE (n: Building {id: 3610, name:"building_subsistence_pastures", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:3610}) CREATE (b)-[r:Supply{max_supply: 1.85625, current_output: 1.85625,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3610}) CREATE (b)-[r:Supply{max_supply: 2.78437, current_output: 2.78437,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3610}) CREATE (b)-[r:Supply{max_supply: 0.92812, current_output: 0.92812,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3610}) CREATE (b)-[r:Supply{max_supply: 1.85625, current_output: 1.85625,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3610}) CREATE (b)-[r:Supply{max_supply: 1.85625, current_output: 1.85625,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3610}) CREATE (b)-[r:Supply{max_supply: 1.85625, current_output: 1.85625,level: 50}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3610}) CREATE (b)-[r:Supply{max_supply: 6.16275, current_output: 6.16275,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3610}) CREATE (b)-[r:Supply{max_supply: 1.85625, current_output: 1.85625,level: 50}]->(g);
CREATE (n: Building {id: 3611, name:"building_subsistence_pastures", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3611}) CREATE (b)-[r:Supply{max_supply: 1.9983, current_output: 1.9983,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3611}) CREATE (b)-[r:Supply{max_supply: 2.99745, current_output: 2.99745,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3611}) CREATE (b)-[r:Supply{max_supply: 0.99915, current_output: 0.99915,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3611}) CREATE (b)-[r:Supply{max_supply: 1.9983, current_output: 1.9983,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3611}) CREATE (b)-[r:Supply{max_supply: 1.9983, current_output: 1.9983,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3611}) CREATE (b)-[r:Supply{max_supply: 1.9983, current_output: 1.9983,level: 30}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3611}) CREATE (b)-[r:Supply{max_supply: 6.63435, current_output: 6.63435,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3611}) CREATE (b)-[r:Supply{max_supply: 1.9983, current_output: 1.9983,level: 30}]->(g);
CREATE (n: Building {id: 3612, name:"building_subsistence_farms", level:60});
MATCH (g: Goods{code: 7}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 44.4165, current_output: 44.4165,level: 60}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 8.8833, current_output: 8.8833,level: 60}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 8.8833, current_output: 8.8833,level: 60}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 8.8833, current_output: 8.8833,level: 60}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 8.8833, current_output: 8.8833,level: 60}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 8.8833, current_output: 8.8833,level: 60}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 8.8833, current_output: 8.8833,level: 60}]->(g);
CREATE (n: Building {id: 3613, name:"building_subsistence_farms", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 77.122, current_output: 77.122,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 15.4244, current_output: 15.4244,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 15.4244, current_output: 15.4244,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 15.4244, current_output: 15.4244,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 15.4244, current_output: 15.4244,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 15.4244, current_output: 15.4244,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 15.4244, current_output: 15.4244,level: 40}]->(g);
CREATE (n: Building {id: 3614, name:"building_subsistence_farms", level:67});
MATCH (g: Goods{code: 7}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 167.25042, current_output: 167.25042,level: 67}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 33.45008, current_output: 33.45008,level: 67}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 33.45008, current_output: 33.45008,level: 67}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 33.45008, current_output: 33.45008,level: 67}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 33.45008, current_output: 33.45008,level: 67}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 33.45008, current_output: 33.45008,level: 67}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 33.45008, current_output: 33.45008,level: 67}]->(g);
CREATE (n: Building {id: 3617, name:"building_subsistence_farms", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3617}) CREATE (b)-[r:Supply{max_supply: 42.125, current_output: 42.125,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3617}) CREATE (b)-[r:Supply{max_supply: 8.425, current_output: 8.425,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3617}) CREATE (b)-[r:Supply{max_supply: 8.425, current_output: 8.425,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3617}) CREATE (b)-[r:Supply{max_supply: 8.425, current_output: 8.425,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3617}) CREATE (b)-[r:Supply{max_supply: 8.425, current_output: 8.425,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3617}) CREATE (b)-[r:Supply{max_supply: 8.425, current_output: 8.425,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3617}) CREATE (b)-[r:Supply{max_supply: 8.425, current_output: 8.425,level: 40}]->(g);
CREATE (n: Building {id: 3618, name:"building_subsistence_pastures", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 1.0844, current_output: 1.0844,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 1.6266, current_output: 1.6266,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 0.5422, current_output: 0.5422,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 1.0844, current_output: 1.0844,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 1.0844, current_output: 1.0844,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 1.0844, current_output: 1.0844,level: 40}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 3.6002, current_output: 3.6002,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 1.0844, current_output: 1.0844,level: 40}]->(g);
CREATE (n: Building {id: 3619, name:"building_subsistence_pastures", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 0.3721, current_output: 0.3721,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 0.55815, current_output: 0.55815,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 0.18605, current_output: 0.18605,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 0.3721, current_output: 0.3721,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 0.3721, current_output: 0.3721,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 0.3721, current_output: 0.3721,level: 20}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 1.23537, current_output: 1.23537,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 0.3721, current_output: 0.3721,level: 20}]->(g);
CREATE (n: Building {id: 3620, name:"building_subsistence_farms", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 89.177, current_output: 89.177,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 17.8354, current_output: 17.8354,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 17.8354, current_output: 17.8354,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 17.8354, current_output: 17.8354,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 17.8354, current_output: 17.8354,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 17.8354, current_output: 17.8354,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 17.8354, current_output: 17.8354,level: 40}]->(g);
CREATE (n: Building {id: 3621, name:"building_subsistence_farms", level:144});
MATCH (g: Goods{code: 7}), (b: Building{id:3621}) CREATE (b)-[r:Supply{max_supply: 344.8476, current_output: 344.8476,level: 144}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3621}) CREATE (b)-[r:Supply{max_supply: 68.96952, current_output: 68.96952,level: 144}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3621}) CREATE (b)-[r:Supply{max_supply: 68.96952, current_output: 68.96952,level: 144}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3621}) CREATE (b)-[r:Supply{max_supply: 68.96952, current_output: 68.96952,level: 144}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3621}) CREATE (b)-[r:Supply{max_supply: 68.96952, current_output: 68.96952,level: 144}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3621}) CREATE (b)-[r:Supply{max_supply: 68.96952, current_output: 68.96952,level: 144}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3621}) CREATE (b)-[r:Supply{max_supply: 68.96952, current_output: 68.96952,level: 144}]->(g);
CREATE (n: Building {id: 3622, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3622}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3622}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 3624, name:"building_subsistence_farms", level:163});
MATCH (g: Goods{code: 7}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 401.12262, current_output: 401.12262,level: 163}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 80.22452, current_output: 80.22452,level: 163}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 80.22452, current_output: 80.22452,level: 163}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 80.22452, current_output: 80.22452,level: 163}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 80.22452, current_output: 80.22452,level: 163}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 80.22452, current_output: 80.22452,level: 163}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 80.22452, current_output: 80.22452,level: 163}]->(g);
CREATE (n: Building {id: 3625, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 15}), (b: Building{id:3625}) CREATE (b)-[r:Supply{max_supply: 43.13069607843137, current_output: 43.99331,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3625}) CREATE (b)-[r:Supply{max_supply: 14.376892156862745, current_output: 14.66443,level: 3}]->(g);
CREATE (n: Building {id: 3626, name:"building_subsistence_farms", level:100});
MATCH (g: Goods{code: 7}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 249.715, current_output: 249.715,level: 100}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 49.943, current_output: 49.943,level: 100}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 49.943, current_output: 49.943,level: 100}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 49.943, current_output: 49.943,level: 100}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 49.943, current_output: 49.943,level: 100}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 49.943, current_output: 49.943,level: 100}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 49.943, current_output: 49.943,level: 100}]->(g);
CREATE (n: Building {id: 3627, name:"building_subsistence_farms", level:77});
MATCH (g: Goods{code: 7}), (b: Building{id:3627}) CREATE (b)-[r:Supply{max_supply: 155.04142, current_output: 155.04142,level: 77}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3627}) CREATE (b)-[r:Supply{max_supply: 31.00828, current_output: 31.00828,level: 77}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3627}) CREATE (b)-[r:Supply{max_supply: 31.00828, current_output: 31.00828,level: 77}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3627}) CREATE (b)-[r:Supply{max_supply: 31.00828, current_output: 31.00828,level: 77}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3627}) CREATE (b)-[r:Supply{max_supply: 31.00828, current_output: 31.00828,level: 77}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3627}) CREATE (b)-[r:Supply{max_supply: 31.00828, current_output: 31.00828,level: 77}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3627}) CREATE (b)-[r:Supply{max_supply: 31.00828, current_output: 31.00828,level: 77}]->(g);
CREATE (n: Building {id: 3628, name:"building_subsistence_farms", level:115});
MATCH (g: Goods{code: 7}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 286.87612, current_output: 286.87612,level: 115}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 57.37522, current_output: 57.37522,level: 115}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 57.37522, current_output: 57.37522,level: 115}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 57.37522, current_output: 57.37522,level: 115}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 57.37522, current_output: 57.37522,level: 115}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 57.37522, current_output: 57.37522,level: 115}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 57.37522, current_output: 57.37522,level: 115}]->(g);
CREATE (n: Building {id: 3629, name:"building_subsistence_farms", level:159});
MATCH (g: Goods{code: 7}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 396.23992, current_output: 396.23992,level: 159}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 79.24798, current_output: 79.24798,level: 159}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 79.24798, current_output: 79.24798,level: 159}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 79.24798, current_output: 79.24798,level: 159}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 79.24798, current_output: 79.24798,level: 159}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 79.24798, current_output: 79.24798,level: 159}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 79.24798, current_output: 79.24798,level: 159}]->(g);
CREATE (n: Building {id: 3630, name:"building_urban_center", level:7});
MATCH (g: Goods{code: 15}), (b: Building{id:3630}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 111.3,level: 7}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3630}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 37.1,level: 7}]->(g);
CREATE (n: Building {id: 3631, name:"building_subsistence_farms", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:3631}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 16}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3631}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 16}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3631}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 16}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3631}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 16}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3631}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 16}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3631}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 16}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3631}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 16}]->(g);
CREATE (n: Building {id: 3633, name:"building_subsistence_farms", level:53});
MATCH (g: Goods{code: 7}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 109.7206, current_output: 109.7206,level: 53}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 21.94412, current_output: 21.94412,level: 53}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 21.94412, current_output: 21.94412,level: 53}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 21.94412, current_output: 21.94412,level: 53}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 21.94412, current_output: 21.94412,level: 53}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 21.94412, current_output: 21.94412,level: 53}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 21.94412, current_output: 21.94412,level: 53}]->(g);
CREATE (n: Building {id: 3635, name:"building_subsistence_farms", level:78});
MATCH (g: Goods{code: 7}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 191.10975, current_output: 191.10975,level: 78}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 38.22195, current_output: 38.22195,level: 78}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 38.22195, current_output: 38.22195,level: 78}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 38.22195, current_output: 38.22195,level: 78}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 38.22195, current_output: 38.22195,level: 78}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 38.22195, current_output: 38.22195,level: 78}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 38.22195, current_output: 38.22195,level: 78}]->(g);
CREATE (n: Building {id: 3636, name:"building_subsistence_farms", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 23.567, current_output: 23.567,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 4.7134, current_output: 4.7134,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 4.7134, current_output: 4.7134,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 4.7134, current_output: 4.7134,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 4.7134, current_output: 4.7134,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 4.7134, current_output: 4.7134,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 4.7134, current_output: 4.7134,level: 40}]->(g);
CREATE (n: Building {id: 3637, name:"building_subsistence_farms", level:72});
MATCH (g: Goods{code: 7}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 178.3512, current_output: 205.10388,level: 72}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 35.670234782608695, current_output: 41.02077,level: 72}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 35.670234782608695, current_output: 41.02077,level: 72}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 35.670234782608695, current_output: 41.02077,level: 72}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 35.670234782608695, current_output: 41.02077,level: 72}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 35.670234782608695, current_output: 41.02077,level: 72}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 35.670234782608695, current_output: 41.02077,level: 72}]->(g);
CREATE (n: Building {id: 3638, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:3638}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3638}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.1,level: 2}]->(g);
CREATE (n: Building {id: 3639, name:"building_subsistence_farms", level:197});
MATCH (g: Goods{code: 7}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 491.24904347826094, current_output: 564.9364,level: 197}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 98.24980869565218, current_output: 112.98728,level: 197}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 98.24980869565218, current_output: 112.98728,level: 197}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 98.24980869565218, current_output: 112.98728,level: 197}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 98.24980869565218, current_output: 112.98728,level: 197}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 98.24980869565218, current_output: 112.98728,level: 197}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 98.24980869565218, current_output: 112.98728,level: 197}]->(g);
CREATE (n: Building {id: 3640, name:"building_subsistence_farms", level:60});
MATCH (g: Goods{code: 7}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 98.811, current_output: 98.811,level: 60}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 19.7622, current_output: 19.7622,level: 60}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 19.7622, current_output: 19.7622,level: 60}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 19.7622, current_output: 19.7622,level: 60}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 19.7622, current_output: 19.7622,level: 60}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 19.7622, current_output: 19.7622,level: 60}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 19.7622, current_output: 19.7622,level: 60}]->(g);
CREATE (n: Building {id: 3641, name:"building_subsistence_farms", level:36});
MATCH (g: Goods{code: 7}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 88.407, current_output: 101.66805,level: 36}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 17.6814, current_output: 20.33361,level: 36}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 17.6814, current_output: 20.33361,level: 36}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 17.6814, current_output: 20.33361,level: 36}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 17.6814, current_output: 20.33361,level: 36}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 17.6814, current_output: 20.33361,level: 36}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 17.6814, current_output: 20.33361,level: 36}]->(g);
CREATE (n: Building {id: 3642, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:3642}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3642}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.1,level: 2}]->(g);
CREATE (n: Building {id: 3643, name:"building_subsistence_farms", level:49});
MATCH (g: Goods{code: 7}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 122.5, current_output: 122.5,level: 49}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 24.5, current_output: 24.5,level: 49}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 24.5, current_output: 24.5,level: 49}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 24.5, current_output: 24.5,level: 49}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 24.5, current_output: 24.5,level: 49}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 24.5, current_output: 24.5,level: 49}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 24.5, current_output: 24.5,level: 49}]->(g);
CREATE (n: Building {id: 3644, name:"building_subsistence_farms", level:146});
MATCH (g: Goods{code: 7}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 363.6349, current_output: 363.6349,level: 146}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 72.72698, current_output: 72.72698,level: 146}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 72.72698, current_output: 72.72698,level: 146}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 72.72698, current_output: 72.72698,level: 146}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 72.72698, current_output: 72.72698,level: 146}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 72.72698, current_output: 72.72698,level: 146}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 72.72698, current_output: 72.72698,level: 146}]->(g);
CREATE (n: Building {id: 3645, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3645}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3645}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 3646, name:"building_subsistence_farms", level:77});
MATCH (g: Goods{code: 7}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 164.63562, current_output: 164.63562,level: 77}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 32.92712, current_output: 32.92712,level: 77}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 32.92712, current_output: 32.92712,level: 77}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 32.92712, current_output: 32.92712,level: 77}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 32.92712, current_output: 32.92712,level: 77}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 32.92712, current_output: 32.92712,level: 77}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 32.92712, current_output: 32.92712,level: 77}]->(g);
CREATE (n: Building {id: 3647, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3647}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3647}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 3648, name:"building_subsistence_farms", level:113});
MATCH (g: Goods{code: 7}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 281.8898, current_output: 281.8898,level: 113}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 56.37796, current_output: 56.37796,level: 113}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 56.37796, current_output: 56.37796,level: 113}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 56.37796, current_output: 56.37796,level: 113}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 56.37796, current_output: 56.37796,level: 113}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 56.37796, current_output: 56.37796,level: 113}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 56.37796, current_output: 56.37796,level: 113}]->(g);
CREATE (n: Building {id: 3649, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3649}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3649}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 3740, name:"building_subsistence_farms", level:95});
MATCH (g: Goods{code: 7}), (b: Building{id:3740}) CREATE (b)-[r:Supply{max_supply: 224.46837, current_output: 224.46837,level: 95}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3740}) CREATE (b)-[r:Supply{max_supply: 44.89367, current_output: 44.89367,level: 95}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3740}) CREATE (b)-[r:Supply{max_supply: 44.89367, current_output: 44.89367,level: 95}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3740}) CREATE (b)-[r:Supply{max_supply: 44.89367, current_output: 44.89367,level: 95}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3740}) CREATE (b)-[r:Supply{max_supply: 44.89367, current_output: 44.89367,level: 95}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3740}) CREATE (b)-[r:Supply{max_supply: 44.89367, current_output: 44.89367,level: 95}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3740}) CREATE (b)-[r:Supply{max_supply: 44.89367, current_output: 44.89367,level: 95}]->(g);
CREATE (n: Building {id: 3741, name:"building_subsistence_farms", level:57});
MATCH (g: Goods{code: 7}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 142.10527, current_output: 142.10527,level: 57}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 28.42105, current_output: 28.42105,level: 57}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 28.42105, current_output: 28.42105,level: 57}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 28.42105, current_output: 28.42105,level: 57}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 28.42105, current_output: 28.42105,level: 57}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 28.42105, current_output: 28.42105,level: 57}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3741}) CREATE (b)-[r:Supply{max_supply: 28.42105, current_output: 28.42105,level: 57}]->(g);
CREATE (n: Building {id: 3742, name:"building_subsistence_farms", level:77});
MATCH (g: Goods{code: 7}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 188.68272, current_output: 188.68272,level: 77}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 37.73654, current_output: 37.73654,level: 77}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 37.73654, current_output: 37.73654,level: 77}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 37.73654, current_output: 37.73654,level: 77}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 37.73654, current_output: 37.73654,level: 77}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 37.73654, current_output: 37.73654,level: 77}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 37.73654, current_output: 37.73654,level: 77}]->(g);
CREATE (n: Building {id: 3743, name:"building_subsistence_farms", level:98});
MATCH (g: Goods{code: 7}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 244.47325, current_output: 244.47325,level: 98}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 48.89465, current_output: 48.89465,level: 98}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 48.89465, current_output: 48.89465,level: 98}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 48.89465, current_output: 48.89465,level: 98}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 48.89465, current_output: 48.89465,level: 98}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 48.89465, current_output: 48.89465,level: 98}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 48.89465, current_output: 48.89465,level: 98}]->(g);
CREATE (n: Building {id: 3744, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3744}) CREATE (b)-[r:Supply{max_supply: 12.228, current_output: 12.228,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3744}) CREATE (b)-[r:Supply{max_supply: 4.076, current_output: 4.076,level: 1}]->(g);
CREATE (n: Building {id: 3745, name:"building_subsistence_farms", level:87});
MATCH (g: Goods{code: 7}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 205.1373, current_output: 205.1373,level: 87}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 41.02746, current_output: 41.02746,level: 87}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 41.02746, current_output: 41.02746,level: 87}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 41.02746, current_output: 41.02746,level: 87}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 41.02746, current_output: 41.02746,level: 87}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 41.02746, current_output: 41.02746,level: 87}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 41.02746, current_output: 41.02746,level: 87}]->(g);
CREATE (n: Building {id: 3746, name:"building_subsistence_farms", level:185});
MATCH (g: Goods{code: 7}), (b: Building{id:3746}) CREATE (b)-[r:Supply{max_supply: 458.16637391304357, current_output: 526.89133,level: 185}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3746}) CREATE (b)-[r:Supply{max_supply: 91.63326956521739, current_output: 105.37826,level: 185}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3746}) CREATE (b)-[r:Supply{max_supply: 91.63326956521739, current_output: 105.37826,level: 185}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3746}) CREATE (b)-[r:Supply{max_supply: 91.63326956521739, current_output: 105.37826,level: 185}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3746}) CREATE (b)-[r:Supply{max_supply: 91.63326956521739, current_output: 105.37826,level: 185}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3746}) CREATE (b)-[r:Supply{max_supply: 91.63326956521739, current_output: 105.37826,level: 185}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3746}) CREATE (b)-[r:Supply{max_supply: 91.63326956521739, current_output: 105.37826,level: 185}]->(g);
CREATE (n: Building {id: 3747, name:"building_urban_center", level:5});
MATCH (g: Goods{code: 15}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 69.915, current_output: 72.7116,level: 5}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 23.305, current_output: 24.2372,level: 5}]->(g);
CREATE (n: Building {id: 3848, name:"building_barracks", level:25});
MATCH (g: Goods{code: 1}), (b: Building{id:3848}) CREATE (g)-[r:Demand{max_demand: 23.0, current_input: 12.84377512698252, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3848}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.2833806668388563, level: 25}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3848}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 5.41500945199028, level: 25}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3848}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 1.2325088338063617, level: 25}]->(b);
CREATE (n: Building {id: 3849, name:"building_barracks", level:25});
MATCH (g: Goods{code: 1}), (b: Building{id:3849}) CREATE (g)-[r:Demand{max_demand: 27.0, current_input: 15.077475149066435, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3849}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 1.0267045334710851, level: 25}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3849}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 10.83001890398056, level: 25}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3849}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 2.4650176676127233, level: 25}]->(b);
CREATE (n: Building {id: 3850, name:"building_barracks", level:7});
MATCH (g: Goods{code: 1}), (b: Building{id:3850}) CREATE (g)-[r:Demand{max_demand: 8.999822222222223, current_input: 5.025725774132274, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3850}) CREATE (g)-[r:Demand{max_demand: 3.9999333333333333, current_input: 3.609946134555164, level: 7}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3850}) CREATE (g)-[r:Demand{max_demand: 3.9999333333333333, current_input: 0.8216588613283098, level: 7}]->(b);
CREATE (n: Building {id: 3851, name:"building_barracks", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:3851}) CREATE (g)-[r:Demand{max_demand: 2.99997, current_input: 1.6752582638127713, level: 3}]->(b);
CREATE (n: Building {id: 3852, name:"building_barracks", level:7});
MATCH (g: Goods{code: 1}), (b: Building{id:3852}) CREATE (g)-[r:Demand{max_demand: 4.47161, current_input: 2.497058838937665, level: 7}]->(b);
CREATE (n: Building {id: 3853, name:"building_barracks", level:13});
MATCH (g: Goods{code: 1}), (b: Building{id:3853}) CREATE (g)-[r:Demand{max_demand: 21.9999, current_input: 12.285294278960988, level: 13}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3853}) CREATE (g)-[r:Demand{max_demand: 17.99993, current_input: 16.244965180860568, level: 13}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3853}) CREATE (g)-[r:Demand{max_demand: 17.99993, current_input: 3.6975121221493565, level: 13}]->(b);
CREATE (n: Building {id: 3854, name:"building_barracks", level:8});
MATCH (g: Goods{code: 1}), (b: Building{id:3854}) CREATE (g)-[r:Demand{max_demand: 14.0, current_input: 7.817950077293706, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3854}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 10.83001890398056, level: 8}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3854}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 2.4650176676127233, level: 8}]->(b);
CREATE (n: Building {id: 3855, name:"building_barracks", level:4});
MATCH (g: Goods{code: 1}), (b: Building{id:3855}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 2.2337000220839163, level: 4}]->(b);
CREATE (n: Building {id: 3856, name:"building_barracks", level:16});
MATCH (g: Goods{code: 1}), (b: Building{id:3856}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 2.2337000220839163, level: 16}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3856}) CREATE (g)-[r:Demand{max_demand: 21.0, current_input: 2.695099400361598, level: 16}]->(b);
CREATE (n: Building {id: 3857, name:"building_barracks", level:19});
MATCH (g: Goods{code: 1}), (b: Building{id:3857}) CREATE (g)-[r:Demand{max_demand: 18.44468, current_input: 10.299970530832692, level: 19}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3857}) CREATE (g)-[r:Demand{max_demand: 10.06073, current_input: 9.079824673987028, level: 19}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3857}) CREATE (g)-[r:Demand{max_demand: 10.06073, current_input: 2.066656433256779, level: 19}]->(b);
CREATE (n: Building {id: 3858, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:3858}) CREATE (g)-[r:Demand{max_demand: 1.594, current_input: 0.8901294588004407, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3858}) CREATE (g)-[r:Demand{max_demand: 1.594, current_input: 1.438587511078751, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3858}) CREATE (g)-[r:Demand{max_demand: 1.594, current_input: 0.3274365135145567, level: 1}]->(b);
CREATE (n: Building {id: 3859, name:"building_barracks", level:19});
MATCH (g: Goods{code: 1}), (b: Building{id:3859}) CREATE (g)-[r:Demand{max_demand: 16.82264, current_input: 9.394182834877443, level: 19}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3859}) CREATE (g)-[r:Demand{max_demand: 7.08314, current_input: 0.9090364936512978, level: 19}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3859}) CREATE (g)-[r:Demand{max_demand: 14.16646, current_input: 12.785252466873704, level: 19}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3859}) CREATE (g)-[r:Demand{max_demand: 12.39567, current_input: 2.546295462658084, level: 19}]->(b);
CREATE (n: Building {id: 3860, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:3860}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 3.3505500331258746, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3860}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 3.610006301326853, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3860}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.6162544169031808, level: 5}]->(b);
CREATE (n: Building {id: 3861, name:"building_barracks", level:3});
MATCH (g: Goods{code: 2}), (b: Building{id:3861}) CREATE (g)-[r:Demand{max_demand: 5.99994, current_input: 0.7700206998193128, level: 3}]->(b);
CREATE (n: Building {id: 3862, name:"building_barracks", level:6});
MATCH (g: Goods{code: 1}), (b: Building{id:3862}) CREATE (g)-[r:Demand{max_demand: 7.99992, current_input: 4.4673553701673905, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3862}) CREATE (g)-[r:Demand{max_demand: 3.99996, current_input: 3.60997020126384, level: 6}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3862}) CREATE (g)-[r:Demand{max_demand: 3.99996, current_input: 0.821664339145349, level: 6}]->(b);
CREATE (n: Building {id: 3863, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:3863}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5584250055209791, level: 1}]->(b);
CREATE (n: Building {id: 3864, name:"building_barracks", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:3864}) CREATE (g)-[r:Demand{max_demand: 4.999977777777778, current_input: 2.792112618160328, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3864}) CREATE (g)-[r:Demand{max_demand: 3.999988888888889, current_input: 3.6099962735315714, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3864}) CREATE (g)-[r:Demand{max_demand: 3.999988888888889, current_input: 0.8216702734471413, level: 3}]->(b);
CREATE (n: Building {id: 3865, name:"building_barracks", level:6});
MATCH (g: Goods{code: 1}), (b: Building{id:3865}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.6752750165629373, level: 6}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3865}) CREATE (g)-[r:Demand{max_demand: 4.999911111111111, current_input: 0.6416789255912786, level: 6}]->(b);
CREATE (n: Building {id: 3866, name:"building_barracks", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:3866}) CREATE (g)-[r:Demand{max_demand: 5.9999666666666664, current_input: 3.350531418959023, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3866}) CREATE (g)-[r:Demand{max_demand: 5.9999666666666664, current_input: 5.414979368604435, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3866}) CREATE (g)-[r:Demand{max_demand: 5.9999666666666664, current_input: 1.2325019865350626, level: 3}]->(b);
CREATE (n: Building {id: 3867, name:"building_barracks", level:7});
MATCH (g: Goods{code: 1}), (b: Building{id:3867}) CREATE (g)-[r:Demand{max_demand: 6.99993, current_input: 3.9089359488964672, level: 7}]->(b);
CREATE (n: Building {id: 3868, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:3868}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5584250055209791, level: 1}]->(b);
CREATE (n: Building {id: 3869, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:3869}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.1168500110419581, level: 2}]->(b);
CREATE (n: Building {id: 3870, name:"building_barracks", level:4});
MATCH (g: Goods{code: 1}), (b: Building{id:3870}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 2.2337000220839163, level: 4}]->(b);
CREATE (n: Building {id: 3871, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:3871}) CREATE (g)-[r:Demand{max_demand: 7.8048, current_input: 4.3583954830901375, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3871}) CREATE (g)-[r:Demand{max_demand: 7.8048, current_input: 7.043844295148956, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3871}) CREATE (g)-[r:Demand{max_demand: 6.8292, current_input: 1.4028415546384005, level: 5}]->(b);
CREATE (n: Building {id: 3872, name:"building_barracks", level:7});
MATCH (g: Goods{code: 1}), (b: Building{id:3872}) CREATE (g)-[r:Demand{max_demand: 6.41893, current_input: 3.5844910206887777, level: 7}]->(b);
CREATE (n: Building {id: 3873, name:"building_barracks", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:3873}) CREATE (g)-[r:Demand{max_demand: 2.99997, current_input: 1.6752582638127713, level: 3}]->(b);
CREATE (n: Building {id: 3874, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:3874}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5584250055209791, level: 1}]->(b);
CREATE (n: Building {id: 3875, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:3875}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5584250055209791, level: 1}]->(b);
CREATE (n: Building {id: 3876, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:3876}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5584250055209791, level: 1}]->(b);
CREATE (n: Building {id: 3877, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:3877}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5584250055209791, level: 1}]->(b);
CREATE (n: Building {id: 3878, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:3878}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5584250055209791, level: 1}]->(b);
CREATE (n: Building {id: 3879, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:3879}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5584250055209791, level: 1}]->(b);
CREATE (n: Building {id: 3880, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:3880}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5584250055209791, level: 1}]->(b);
CREATE (n: Building {id: 3881, name:"building_barracks", level:11});
MATCH (g: Goods{code: 1}), (b: Building{id:3881}) CREATE (g)-[r:Demand{max_demand: 15.83977, current_input: 8.845323649701038, level: 11}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3881}) CREATE (g)-[r:Demand{max_demand: 14.07985, current_input: 12.707086805434223, level: 11}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3881}) CREATE (g)-[r:Demand{max_demand: 14.07985, current_input: 2.8922565839447496, level: 11}]->(b);
CREATE (n: Building {id: 3882, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:3882}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.1168500110419581, level: 2}]->(b);
CREATE (n: Building {id: 3884, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:3884}) CREATE (g)-[r:Demand{max_demand: 1.94, current_input: 1.0833445107106994, level: 2}]->(b);
CREATE (n: Building {id: 3885, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:3885}) CREATE (g)-[r:Demand{max_demand: 1.9789999999999999, current_input: 1.1051230859260175, level: 2}]->(b);
CREATE (n: Building {id: 3886, name:"building_barracks", level:4});
MATCH (g: Goods{code: 2}), (b: Building{id:3886}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 1.0267045334710851, level: 4}]->(b);
CREATE (n: Building {id: 3887, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:3887}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.1168500110419581, level: 2}]->(b);
CREATE (n: Building {id: 3888, name:"building_naval_base", level:20});
MATCH (g: Goods{code: 5}), (b: Building{id:3888}) CREATE (g)-[r:Demand{max_demand: 44.0, current_input: 22.35995942943361, level: 20}]->(b);
CREATE (n: Building {id: 3889, name:"building_naval_base", level:17});
MATCH (g: Goods{code: 5}), (b: Building{id:3889}) CREATE (g)-[r:Demand{max_demand: 38.99986666666666, current_input: 19.818987191363334, level: 17}]->(b);
CREATE (n: Building {id: 3890, name:"building_naval_base", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:3890}) CREATE (g)-[r:Demand{max_demand: 6.999988888888889, current_input: 3.5572606264100366, level: 3}]->(b);
CREATE (n: Building {id: 3891, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:3891}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5081808961234912, level: 1}]->(b);
CREATE (n: Building {id: 3892, name:"building_naval_base", level:13});
MATCH (g: Goods{code: 5}), (b: Building{id:3892}) CREATE (g)-[r:Demand{max_demand: 22.49991, current_input: 11.4340244264979, level: 13}]->(b);
CREATE (n: Building {id: 3893, name:"building_naval_base", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:3893}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 3.5572662728644375, level: 5}]->(b);
CREATE (n: Building {id: 3894, name:"building_naval_base", level:12});
MATCH (g: Goods{code: 5}), (b: Building{id:3894}) CREATE (g)-[r:Demand{max_demand: 21.99996, current_input: 11.17995938748096, level: 12}]->(b);
CREATE (n: Building {id: 3895, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:3895}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5081808961234912, level: 1}]->(b);
CREATE (n: Building {id: 3896, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:3896}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5081808961234912, level: 1}]->(b);
CREATE (n: Building {id: 4145, name:"building_trade_center", level:77});
CREATE (n: Building {id: 4215, name:"building_trade_center", level:34});
CREATE (n: Building {id: 4216, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:4216}) CREATE (b)-[r:Supply{max_supply: 13.271999999999998, current_output: 13.40472,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:4216}) CREATE (b)-[r:Supply{max_supply: 4.4239999999999995, current_output: 4.46824,level: 2}]->(g);
CREATE (n: Building {id: 4217, name:"building_trade_center", level:39});
CREATE (n: Building {id: 4218, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:4218}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:4218}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.1,level: 2}]->(g);
CREATE (n: Building {id: 4220, name:"building_trade_center", level:27});
CREATE (n: Building {id: 4221, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:4221}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:4221}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 4222, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:4222}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:4222}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 4237, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:4237}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 4306, name:"building_arts_academy", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:4306}) CREATE (g)-[r:Demand{max_demand: 2.544, current_input: 2.714978091125578, level: 1}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:4306}) CREATE (b)-[r:Supply{max_supply: 1.0176, current_output: 1.0176,level: 1}]->(g);
CREATE (n: Building {id: 4311, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:4311}) CREATE (g)-[r:Demand{max_demand: 0.005792792792792792, current_input: 0.00522800462106569, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4311}) CREATE (g)-[r:Demand{max_demand: 0.005792792792792792, current_input: 0.0021599445026051637, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:4311}) CREATE (b)-[r:Supply{max_supply: 0.011594594594594594, current_output: 0.007393694029676818,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:4311}) CREATE (b)-[r:Supply{max_supply: 0.023198198198198194, current_output: 0.0147931329653596,level: 2}]->(g);
CREATE (n: Building {id: 16781611, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16781611}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 40.01373170705813, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16781611}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 45.41260317017147, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16781611}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 10.270906948386346, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16781611}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.728675579924933, level: 1}]->(b);
CREATE (n: Building {id: 16781914, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:16781914}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:16781914}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 4883, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:4883}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 23.0,level: 1}]->(g);
CREATE (n: Building {id: 4956, name:"building_barracks", level:8});
MATCH (g: Goods{code: 1}), (b: Building{id:4956}) CREATE (g)-[r:Demand{max_demand: 6.26896, current_input: 3.500744022610797, level: 8}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4956}) CREATE (g)-[r:Demand{max_demand: 1.56724, current_input: 0.2011365516296529, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4956}) CREATE (g)-[r:Demand{max_demand: 3.13448, current_input: 2.828873137845749, level: 8}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4956}) CREATE (g)-[r:Demand{max_demand: 3.13448, current_input: 0.6438790482315606, level: 8}]->(b);
CREATE (n: Building {id: 5039, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:5039}) CREATE (g)-[r:Demand{max_demand: 0.3955, current_input: 0.22085708968354723, level: 2}]->(b);
CREATE (n: Building {id: 5099, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:5099}) CREATE (b)-[r:Supply{max_supply: 5.36, current_output: 5.36,level: 1}]->(g);
CREATE (n: Building {id: 5208, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:5208}) CREATE (b)-[r:Supply{max_supply: 14.8, current_output: 17.02,level: 1}]->(g);
CREATE (n: Building {id: 5257, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5257}) CREATE (g)-[r:Demand{max_demand: 7.4346, current_input: 8.440613488223919, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5257}) CREATE (g)-[r:Demand{max_demand: 4.9564, current_input: 1.018134463979642, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5257}) CREATE (b)-[r:Supply{max_supply: 14.8692, current_output: 8.961801695969463,level: 1}]->(g);
CREATE (n: Building {id: 5323, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:5323}) CREATE (g)-[r:Demand{max_demand: 0.09749999999999999, current_input: 0.05444643803829545, level: 2}]->(b);
CREATE (n: Building {id: 5328, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5328}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 40.01373170705813, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5328}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 45.41260317017147, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5328}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 10.270906948386346, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5328}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.728675579924933, level: 1}]->(b);
CREATE (n: Building {id: 5470, name:"building_trade_center", level:5});
