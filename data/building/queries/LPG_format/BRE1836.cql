CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:30.741614964904727, pop_demand:23.681512866706708});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:35.0, pop_demand:1.6565218250261342});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:26.45501967964956, pop_demand:2.3603331129933895});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:34.36893698736014, pop_demand:1.960358309749293});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:52.5, pop_demand:0.6638438943677946});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:47.32796379661235, pop_demand:5.849437012870128});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:47.1572060612167, pop_demand:5.662486771273371});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:10.403456642138542, pop_demand:0.21715722615196217});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:37.422291504201695, pop_demand:2.006505166778541});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:52.5, pop_demand:0.14849924912500267});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:52.5, pop_demand:0.5036416025814108});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:70.0, pop_demand:0.02447308669260078});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:70.0, pop_demand:0.19446391265010693});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:0.20234217500105436});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:52.5, pop_demand:0.9140452277325726});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:52.5, pop_demand:0.12095689134882705});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:44.55339657651619, pop_demand:5.32993755229219});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:87.5, pop_demand:0.0420083055969616});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:0.6888489575888757});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:87.5, pop_demand:0.393642736814163});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:52.5, pop_demand:0.18968052010210595});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:70.0, pop_demand:0.4782120786520366});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:87.5, pop_demand:1.8590503057030572});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:0.9867943184640546});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:0.5319314773680487});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:350.0, pop_demand:0.00033995734780922836});
CREATE (n: Building {id: 431, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:431}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 1.2621260252797344, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:431}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 1.682834700372979,level: 1}]->(g);
CREATE (n: Building {id: 432, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:432}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 433, name:"building_barrackslevel", level:1});
CREATE (n: Building {id: 3311, name:"building_subsistence_farmslevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3311}) CREATE (b)-[r:Supply{max_supply: 6.723, current_output: 6.723,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3311}) CREATE (b)-[r:Supply{max_supply: 1.3446, current_output: 1.3446,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3311}) CREATE (b)-[r:Supply{max_supply: 1.3446, current_output: 1.3446,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3311}) CREATE (b)-[r:Supply{max_supply: 1.3446, current_output: 1.3446,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3311}) CREATE (b)-[r:Supply{max_supply: 1.3446, current_output: 1.3446,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3311}) CREATE (b)-[r:Supply{max_supply: 1.3446, current_output: 1.3446,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3311}) CREATE (b)-[r:Supply{max_supply: 1.88244, current_output: 1.88244,level: 5}]->(g);
CREATE (n: Building {id: 4004, name:"building_trade_centerlevel", level:8});
