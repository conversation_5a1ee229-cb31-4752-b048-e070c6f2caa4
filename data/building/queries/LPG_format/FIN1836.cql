CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:24.403940145587697, pop_demand:225.80127263094843});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:6.360974887696733, pop_demand:6.963146744756876});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:18.589892620585143, pop_demand:13.572765001742994});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:28.5519727479498, pop_demand:31.250849998257003});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:52.5, pop_demand:7.136581729193065});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:37.78775778826273, pop_demand:40.02930630996308});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:18.92551976791227, pop_demand:29.628302847422507});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:52.5, pop_demand:2.191564279149287});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:27.294755461391176, pop_demand:23.027247825905448});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:70.0, pop_demand:0.9567064961333237});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:4.7642718478278585});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:52.5, pop_demand:5.462728622212168});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:52.5, pop_demand:0.9426627180084036});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:27.6706029389215, pop_demand:73.0300973774304});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:87.5, pop_demand:6.121184071331419});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:1.979590928668581});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:52.5, pop_demand:0.6682625217263221});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:70.0, pop_demand:1.7563738419272008});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:1.051911521734993});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:5.55831715579917});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:350.0, pop_demand:0.01272195111418207});
CREATE (n: Building {id: 71, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:71}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 72, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:72}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 11.03761480593225, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:72}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 12.896054504100396, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:72}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 32.1720408780753,level: 1}]->(g);
CREATE (n: Building {id: 73, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:73}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 74, name:"building_rye_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:74}) CREATE (b)-[r:Supply{max_supply: 9.96, current_output: 9.96,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:74}) CREATE (b)-[r:Supply{max_supply: 14.94, current_output: 14.94,level: 1}]->(g);
CREATE (n: Building {id: 75, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:75}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:75}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 0.0,level: 2}]->(g);
CREATE (n: Building {id: 76, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:76}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:76}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 10}]->(b);
CREATE (n: Building {id: 77, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:77}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 78, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:78}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 79, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:79}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 80, name:"building_rye_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:80}) CREATE (b)-[r:Supply{max_supply: 9.96, current_output: 9.96,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:80}) CREATE (b)-[r:Supply{max_supply: 14.94, current_output: 14.94,level: 1}]->(g);
CREATE (n: Building {id: 81, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:81}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:81}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 82, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:82}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 83, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:83}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:83}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 0.0,level: 2}]->(g);
CREATE (n: Building {id: 84, name:"building_rye_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:84}) CREATE (b)-[r:Supply{max_supply: 9.96, current_output: 9.96,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:84}) CREATE (b)-[r:Supply{max_supply: 14.94, current_output: 14.94,level: 1}]->(g);
CREATE (n: Building {id: 3760, name:"building_subsistence_farmslevel", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 72.21, current_output: 72.21,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 14.442, current_output: 14.442,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 14.442, current_output: 14.442,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 14.442, current_output: 14.442,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 14.442, current_output: 14.442,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 14.442, current_output: 14.442,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 20.2188, current_output: 20.2188,level: 29}]->(g);
CREATE (n: Building {id: 3761, name:"building_subsistence_fishing_villageslevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:3761}) CREATE (b)-[r:Supply{max_supply: 0.3111, current_output: 0.3111,level: 2}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:3761}) CREATE (b)-[r:Supply{max_supply: 1.2444, current_output: 1.2444,level: 2}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3761}) CREATE (b)-[r:Supply{max_supply: 0.15555, current_output: 0.15555,level: 2}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3761}) CREATE (b)-[r:Supply{max_supply: 0.46665, current_output: 0.46665,level: 2}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3761}) CREATE (b)-[r:Supply{max_supply: 0.3111, current_output: 0.3111,level: 2}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3761}) CREATE (b)-[r:Supply{max_supply: 0.3111, current_output: 0.3111,level: 2}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3761}) CREATE (b)-[r:Supply{max_supply: 0.3111, current_output: 0.3111,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3761}) CREATE (b)-[r:Supply{max_supply: 0.43554, current_output: 0.43554,level: 2}]->(g);
CREATE (n: Building {id: 3762, name:"building_subsistence_farmslevel", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 11.26642, current_output: 11.26642,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 2.25328, current_output: 2.25328,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 2.25328, current_output: 2.25328,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 2.25328, current_output: 2.25328,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 2.25328, current_output: 2.25328,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 2.25328, current_output: 2.25328,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 3.15459, current_output: 3.15459,level: 9}]->(g);
CREATE (n: Building {id: 3763, name:"building_subsistence_farmslevel", level:19});
MATCH (g: Goods{code: 7}), (b: Building{id:3763}) CREATE (b)-[r:Supply{max_supply: 45.8394, current_output: 45.8394,level: 19}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3763}) CREATE (b)-[r:Supply{max_supply: 9.16788, current_output: 9.16788,level: 19}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3763}) CREATE (b)-[r:Supply{max_supply: 9.16788, current_output: 9.16788,level: 19}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3763}) CREATE (b)-[r:Supply{max_supply: 9.16788, current_output: 9.16788,level: 19}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3763}) CREATE (b)-[r:Supply{max_supply: 9.16788, current_output: 9.16788,level: 19}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3763}) CREATE (b)-[r:Supply{max_supply: 9.16788, current_output: 9.16788,level: 19}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3763}) CREATE (b)-[r:Supply{max_supply: 12.83503, current_output: 12.83503,level: 19}]->(g);
CREATE (n: Building {id: 4547, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4548, name:"building_conscription_centerlevel", level:1});
