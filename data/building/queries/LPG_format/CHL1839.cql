CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0.07668667144392691});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:24.719341536149827, pop_demand:138.61333372723558});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:19.494808830530314, pop_demand:24.15801471755052});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:6.4529788006549404, pop_demand:2.805278359213546});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:10.970059326217868, pop_demand:16.85560091555491});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:42.660440170675, pop_demand:40.08348511938022});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:38.95003951508052, pop_demand:29.1073800871194});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:14.14084742243007, pop_demand:13.389621897169473});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:25.063332065219154, pop_demand:7.2430166666666675});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0.00900622536725188});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:35.97848709225535, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:42.71922579819582, pop_demand:10.99675536261577});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:29.06266502353983, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:2.3825214285714282});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:33.173656053070665, pop_demand:28.424005592912426});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:28.505494401208907, pop_demand:8.716174985456403});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:27.17629732346587, pop_demand:15.329199838261005});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:18.762742777662332, pop_demand:3.9454143855249013});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:10.857127868638795, pop_demand:1.2935377915268});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:31.74271161187036, pop_demand:37.37166262130426});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:13.898041666666666});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:2.779608333333334});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 1757, name:"building_government_administration", level:3});
CREATE (n: Building {id: 1758, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1758}) CREATE (g)-[r:Demand{max_demand: 25.9629, current_input: 65.23276884196711, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1758}) CREATE (g)-[r:Demand{max_demand: 17.3086, current_input: 19.988, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1758}) CREATE (b)-[r:Supply{max_supply: 51.9258, current_output: 51.9258,level: 1}]->(g);
CREATE (n: Building {id: 1759, name:"building_gold_mine", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1759}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 23.606353017498368, level: 3}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:1759}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 3}]->(g);
CREATE (n: Building {id: 1760, name:"building_iron_mine", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1760}) CREATE (g)-[r:Demand{max_demand: 4.997, current_input: 7.864063068562622, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1760}) CREATE (b)-[r:Supply{max_supply: 19.988, current_output: 19.988,level: 1}]->(g);
CREATE (n: Building {id: 1761, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1761}) CREATE (b)-[r:Supply{max_supply: 19.107999999999997, current_output: 25.7958,level: 1}]->(g);
CREATE (n: Building {id: 1762, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1762}) CREATE (g)-[r:Demand{max_demand: 4.14015, current_input: 2.837564542607287, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1762}) CREATE (g)-[r:Demand{max_demand: 4.14015, current_input: 6.515589496359724, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1762}) CREATE (b)-[r:Supply{max_supply: 8.2803, current_output: 6.977714542607287,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1762}) CREATE (b)-[r:Supply{max_supply: 16.5606, current_output: 13.955429085214574,level: 1}]->(g);
CREATE (n: Building {id: 1763, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1763}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1764, name:"building_vineyard_plantation", level:2});
MATCH (g: Goods{code: 39}), (b: Building{id:1764}) CREATE (b)-[r:Supply{max_supply: 15.052, current_output: 20.47072,level: 2}]->(g);
CREATE (n: Building {id: 1765, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1765}) CREATE (g)-[r:Demand{max_demand: 3.0794, current_input: 2.1105506449053486, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1765}) CREATE (b)-[r:Supply{max_supply: 6.1588, current_output: 4.221101289810697,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1765}) CREATE (b)-[r:Supply{max_supply: 6.1588, current_output: 4.221101289810697,level: 1}]->(g);
CREATE (n: Building {id: 1766, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1766}) CREATE (b)-[r:Supply{max_supply: 37.915, current_output: 51.5644,level: 2}]->(g);
CREATE (n: Building {id: 1767, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:1767}) CREATE (b)-[r:Supply{max_supply: 2.336, current_output: 3.1536,level: 1}]->(g);
CREATE (n: Building {id: 1768, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1768}) CREATE (g)-[r:Demand{max_demand: 2.61629702970297, current_input: 4.117415418786715, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1768}) CREATE (b)-[r:Supply{max_supply: 31.39559405940594, current_output: 31.39559405940594,level: 2}]->(g);
CREATE (n: Building {id: 1769, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1769}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1770, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1770}) CREATE (g)-[r:Demand{max_demand: 4.9076, current_input: 7.723369204578333, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1770}) CREATE (b)-[r:Supply{max_supply: 58.8912, current_output: 58.8912,level: 1}]->(g);
CREATE (n: Building {id: 3023, name:"building_subsistence_farms", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:3023}) CREATE (b)-[r:Supply{max_supply: 28.610555555555557, current_output: 38.62425,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3023}) CREATE (b)-[r:Supply{max_supply: 7.152637037037037, current_output: 9.65606,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3023}) CREATE (b)-[r:Supply{max_supply: 7.152637037037037, current_output: 9.65606,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3023}) CREATE (b)-[r:Supply{max_supply: 7.152637037037037, current_output: 9.65606,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3023}) CREATE (b)-[r:Supply{max_supply: 7.152637037037037, current_output: 9.65606,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3023}) CREATE (b)-[r:Supply{max_supply: 7.152637037037037, current_output: 9.65606,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3023}) CREATE (b)-[r:Supply{max_supply: 7.152637037037037, current_output: 9.65606,level: 44}]->(g);
CREATE (n: Building {id: 3024, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:3024}) CREATE (b)-[r:Supply{max_supply: 27.561, current_output: 27.83661,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3024}) CREATE (b)-[r:Supply{max_supply: 9.187, current_output: 9.27887,level: 2}]->(g);
CREATE (n: Building {id: 3025, name:"building_subsistence_farms", level:64});
MATCH (g: Goods{code: 7}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 21.94815555555555, current_output: 29.63001,level: 64}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 5.4870370370370365, current_output: 7.4075,level: 64}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 5.4870370370370365, current_output: 7.4075,level: 64}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 5.4870370370370365, current_output: 7.4075,level: 64}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 5.4870370370370365, current_output: 7.4075,level: 64}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 5.4870370370370365, current_output: 7.4075,level: 64}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3025}) CREATE (b)-[r:Supply{max_supply: 5.4870370370370365, current_output: 7.4075,level: 64}]->(g);
CREATE (n: Building {id: 3026, name:"building_subsistence_pastures", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:3026}) CREATE (b)-[r:Supply{max_supply: 0.4655, current_output: 0.4655,level: 1}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3026}) CREATE (b)-[r:Supply{max_supply: 0.69825, current_output: 0.69825,level: 1}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3026}) CREATE (b)-[r:Supply{max_supply: 0.23275, current_output: 0.23275,level: 1}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3026}) CREATE (b)-[r:Supply{max_supply: 0.4655, current_output: 0.4655,level: 1}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3026}) CREATE (b)-[r:Supply{max_supply: 0.4655, current_output: 0.4655,level: 1}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3026}) CREATE (b)-[r:Supply{max_supply: 0.4655, current_output: 0.4655,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3026}) CREATE (b)-[r:Supply{max_supply: 1.23823, current_output: 1.23823,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3026}) CREATE (b)-[r:Supply{max_supply: 0.4655, current_output: 0.4655,level: 1}]->(g);
CREATE (n: Building {id: 4023, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:4023}) CREATE (g)-[r:Demand{max_demand: 9.9011, current_input: 0.0, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4023}) CREATE (g)-[r:Demand{max_demand: 1.8002, current_input: 0.0, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4023}) CREATE (g)-[r:Demand{max_demand: 1.8002, current_input: 1.2338160911082057, level: 10}]->(b);
CREATE (n: Building {id: 4024, name:"building_barracks", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:4024}) CREATE (g)-[r:Demand{max_demand: 2.99997, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 4025, name:"building_naval_base", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:4025}) CREATE (g)-[r:Demand{max_demand: 0.63599, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 4179, name:"building_trade_center", level:15});
CREATE (n: Building {id: 4461, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 4731, name:"building_sugar_plantation", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:4731}) CREATE (b)-[r:Supply{max_supply: 1.482, current_output: 2.0007,level: 1}]->(g);
CREATE (n: Building {id: 4907, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:4907}) CREATE (g)-[r:Demand{max_demand: 1.3337573529411764, current_input: 2.099009794214238, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4907}) CREATE (b)-[r:Supply{max_supply: 6.6687941176470575, current_output: 6.6687941176470575,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:4907}) CREATE (b)-[r:Supply{max_supply: 9.336316176470588, current_output: 9.336316176470588,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:4907}) CREATE (b)-[r:Supply{max_supply: 6.6687941176470575, current_output: 6.6687941176470575,level: 2}]->(g);
CREATE (n: Building {id: 5054, name:"building_conscription_center", level:1});
CREATE (n: Building {id: 5204, name:"building_whaling_station", level:1});
MATCH (g: Goods{code: 28}), (b: Building{id:5204}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:5204}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
