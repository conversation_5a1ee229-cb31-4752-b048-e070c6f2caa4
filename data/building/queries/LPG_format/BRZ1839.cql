CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:102.09543041955122, pop_demand:2.9454424358990297});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:107.65651814744628, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:30.46011611366928, pop_demand:1286.2535136687618});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:27.93094977750499, pop_demand:189.55227015095173});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:21.726482788545688, pop_demand:47.91519671880149});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:31.955836605354673, pop_demand:334.6388757811991});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:40.55655633568723, pop_demand:410.55239969392267});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:44.00937060947529, pop_demand:252.5036724148031});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:48.91277603539679, pop_demand:12.096614457121815});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:22.408651459184856, pop_demand:114.80034671162181});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:36.64739025775499, pop_demand:49.983448333333335});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:87.77342087584269, pop_demand:5.889235458290042});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:67.26137582792347, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:66.15085026545601, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:10.879272600193117});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:44.956647194705944, pop_demand:26.911259487674396});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:24.67018807328678, pop_demand:270.67421777534986});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:49.33562211549121, pop_demand:480.70977314270147});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:18.531425754058713, pop_demand:67.17749249999999});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:9.733466500198814, pop_demand:2.734715941950101});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:51.824450771711895, pop_demand:81.73618201797382});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:63.462423501126445});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:49.665692631981614});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 1772, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1772}) CREATE (g)-[r:Demand{max_demand: 4.17065, current_input: 0.6125118403578486, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1772}) CREATE (b)-[r:Supply{max_supply: 50.0478, current_output: 7.350142084294183,level: 1}]->(g);
CREATE (n: Building {id: 1773, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1773}) CREATE (g)-[r:Demand{max_demand: 3.847, current_input: 1.5130366858432487, level: 1}]->(b);
CREATE (n: Building {id: 1774, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1774}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.7343122059605198, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1774}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 8.811746471526238,level: 1}]->(g);
CREATE (n: Building {id: 1775, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1775}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 1776, name:"building_rice_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1776}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.29372488238420796, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1776}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 5.8744976476841595,level: 1}]->(g);
CREATE (n: Building {id: 1777, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1777}) CREATE (g)-[r:Demand{max_demand: 4.9989, current_input: 0.7341506572752086, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1777}) CREATE (b)-[r:Supply{max_supply: 59.9868, current_output: 8.809807887302503,level: 1}]->(g);
CREATE (n: Building {id: 1778, name:"building_rice_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1778}) CREATE (g)-[r:Demand{max_demand: 1.9331, current_input: 0.2838997850684562, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1778}) CREATE (b)-[r:Supply{max_supply: 38.662, current_output: 5.677995701369124,level: 1}]->(g);
CREATE (n: Building {id: 1780, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1780}) CREATE (b)-[r:Supply{max_supply: 29.880000000000003, current_output: 34.362,level: 1}]->(g);
CREATE (n: Building {id: 1781, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1781}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.7343122059605198, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1781}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 8.811746471526238,level: 1}]->(g);
CREATE (n: Building {id: 1782, name:"building_maize_farm", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1782}) CREATE (b)-[r:Supply{max_supply: 19.92, current_output: 22.908,level: 1}]->(g);
CREATE (n: Building {id: 1783, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1783}) CREATE (g)-[r:Demand{max_demand: 3.259793388429752, current_input: 1.0244901715335477, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1783}) CREATE (b)-[r:Supply{max_supply: 6.519595041322314, current_output: 2.048982940428312,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1783}) CREATE (b)-[r:Supply{max_supply: 6.519595041322314, current_output: 2.048982940428312,level: 2}]->(g);
CREATE (n: Building {id: 1784, name:"building_maize_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1784}) CREATE (g)-[r:Demand{max_demand: 0.7444396551724138, current_input: 0.1093302250788288, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1784}) CREATE (b)-[r:Supply{max_supply: 14.888793103448277, current_output: 2.186604501576576,level: 2}]->(g);
CREATE (n: Building {id: 1785, name:"building_coffee_plantation", level:1});
MATCH (g: Goods{code: 41}), (b: Building{id:1785}) CREATE (b)-[r:Supply{max_supply: 17.644000000000002, current_output: 20.2906,level: 1}]->(g);
CREATE (n: Building {id: 1786, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1786}) CREATE (g)-[r:Demand{max_demand: 1.046, current_input: 0.4113949501928875, level: 1}]->(b);
CREATE (n: Building {id: 1787, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1787}) CREATE (g)-[r:Demand{max_demand: 4.3402, current_input: 0.6374123672619697, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1787}) CREATE (b)-[r:Supply{max_supply: 52.0824, current_output: 7.648948407143636,level: 1}]->(g);
CREATE (n: Building {id: 1788, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1788}) CREATE (b)-[r:Supply{max_supply: 21.45, current_output: 21.45,level: 1}]->(g);
CREATE (n: Building {id: 1789, name:"building_maize_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1789}) CREATE (g)-[r:Demand{max_demand: 1.0464356435643565, current_input: 0.15368209316429177, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1789}) CREATE (b)-[r:Supply{max_supply: 20.92879207920792, current_output: 3.0736534959544444,level: 2}]->(g);
CREATE (n: Building {id: 1790, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1790}) CREATE (g)-[r:Demand{max_demand: 4.4565, current_input: 0.6544924691726114, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1790}) CREATE (b)-[r:Supply{max_supply: 53.478, current_output: 7.853909630071337,level: 1}]->(g);
CREATE (n: Building {id: 1791, name:"building_maize_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1791}) CREATE (g)-[r:Demand{max_demand: 0.90777, current_input: 0.13331731824095622, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1791}) CREATE (b)-[r:Supply{max_supply: 18.1554, current_output: 2.6663463648191246,level: 1}]->(g);
CREATE (n: Building {id: 1792, name:"building_coffee_plantation", level:5});
MATCH (g: Goods{code: 41}), (b: Building{id:1792}) CREATE (b)-[r:Supply{max_supply: 97.284, current_output: 110.90376,level: 5}]->(g);
CREATE (n: Building {id: 1793, name:"building_sugar_plantation", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1793}) CREATE (b)-[r:Supply{max_supply: 6.390000000000001, current_output: 7.3485,level: 1}]->(g);
CREATE (n: Building {id: 1794, name:"building_arms_industry", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:1794}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.0509671858644907, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1794}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1794}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 1.576450778796736,level: 1}]->(g);
CREATE (n: Building {id: 1795, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1795}) CREATE (g)-[r:Demand{max_demand: 6.403891089108911, current_input: 2.0126194204975847, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1795}) CREATE (b)-[r:Supply{max_supply: 12.80779207920792, current_output: 4.02524195268534,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1795}) CREATE (b)-[r:Supply{max_supply: 12.80779207920792, current_output: 4.02524195268534,level: 2}]->(g);
CREATE (n: Building {id: 1796, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1796}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.7343122059605198, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1796}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 8.811746471526238,level: 1}]->(g);
CREATE (n: Building {id: 1797, name:"building_paper_mills", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1797}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 13.483847798976857, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1797}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 17.978463731969143,level: 2}]->(g);
CREATE (n: Building {id: 1798, name:"building_university", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1798}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.9007311080466722, level: 1}]->(b);
CREATE (n: Building {id: 1799, name:"building_maize_farm", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1799}) CREATE (g)-[r:Demand{max_demand: 3.9997572815533977, current_input: 0.5874141185448256, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1799}) CREATE (b)-[r:Supply{max_supply: 79.99519417475727, current_output: 11.74828950014123,level: 4}]->(g);
CREATE (n: Building {id: 1800, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1800}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8014622160933444, level: 1}]->(b);
CREATE (n: Building {id: 1801, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1801}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.7419238994884285, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1801}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.1019343717289813, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1801}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 9.8948254570819,level: 1}]->(g);
CREATE (n: Building {id: 1802, name:"building_gold_mine", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1802}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.4686244119210397, level: 2}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:1802}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 2.9372488238420793,level: 2}]->(g);
CREATE (n: Building {id: 1803, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1803}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.4686244119210397, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1803}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 5.874497647684159,level: 2}]->(g);
CREATE (n: Building {id: 1804, name:"building_logging_camp", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1804}) CREATE (g)-[r:Demand{max_demand: 19.988, current_input: 2.9354864745477744, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1804}) CREATE (b)-[r:Supply{max_supply: 239.856, current_output: 35.22583769457329,level: 4}]->(g);
CREATE (n: Building {id: 1805, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1805}) CREATE (g)-[r:Demand{max_demand: 4.69015, current_input: 1.4740236590063922, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1805}) CREATE (b)-[r:Supply{max_supply: 9.3803, current_output: 2.9480473180127844,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1805}) CREATE (b)-[r:Supply{max_supply: 9.3803, current_output: 2.9480473180127844,level: 1}]->(g);
CREATE (n: Building {id: 1806, name:"building_maize_farm", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1806}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.5874497647684159, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1806}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 11.748995295368319,level: 4}]->(g);
CREATE (n: Building {id: 1807, name:"building_coffee_plantation", level:6});
MATCH (g: Goods{code: 41}), (b: Building{id:1807}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 126.0,level: 6}]->(g);
CREATE (n: Building {id: 1808, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1808}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 1809, name:"building_government_administration", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:1809}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 9.007311080466723, level: 5}]->(b);
CREATE (n: Building {id: 1810, name:"building_university", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1810}) CREATE (g)-[r:Demand{max_demand: 9.997, current_input: 1.8009217774285164, level: 2}]->(b);
CREATE (n: Building {id: 1811, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1811}) CREATE (g)-[r:Demand{max_demand: 24.805, current_input: 21.97561273447813, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1811}) CREATE (g)-[r:Demand{max_demand: 74.415, current_input: 16.72334223268105, level: 1}]->(b);
CREATE (n: Building {id: 1812, name:"building_furniture_manufacturies", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1812}) CREATE (g)-[r:Demand{max_demand: 10.15859405940594, current_input: 8.999852004687565, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1812}) CREATE (g)-[r:Demand{max_demand: 30.47579207920792, current_input: 6.848849032488401, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1812}) CREATE (g)-[r:Demand{max_demand: 5.07929702970297, current_input: 0.7459579613219809, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1812}) CREATE (b)-[r:Supply{max_supply: 66.0308910891089, current_output: 27.678565654246697,level: 2}]->(g);
CREATE (n: Building {id: 1813, name:"building_paper_mills", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1813}) CREATE (g)-[r:Demand{max_demand: 10.283999999999999, current_input: 2.311131512744633, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1813}) CREATE (b)-[r:Supply{max_supply: 13.712, current_output: 3.081508683659511,level: 2}]->(g);
CREATE (n: Building {id: 1814, name:"building_fishing_wharf", level:2});
CREATE (n: Building {id: 1815, name:"building_coffee_plantation", level:2});
MATCH (g: Goods{code: 41}), (b: Building{id:1815}) CREATE (b)-[r:Supply{max_supply: 34.135999999999996, current_output: 34.47736,level: 2}]->(g);
CREATE (n: Building {id: 1816, name:"building_sugar_plantation", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1816}) CREATE (b)-[r:Supply{max_supply: 12.900000000000002, current_output: 14.964,level: 2}]->(g);
CREATE (n: Building {id: 1817, name:"building_maize_farm", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:1817}) CREATE (g)-[r:Demand{max_demand: 4.858619047619047, current_input: 0.7135486541557886, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1817}) CREATE (b)-[r:Supply{max_supply: 97.1724, current_output: 14.270975880495605,level: 6}]->(g);
CREATE (n: Building {id: 1818, name:"building_banana_plantation", level:3});
MATCH (g: Goods{code: 37}), (b: Building{id:1818}) CREATE (b)-[r:Supply{max_supply: 82.03139215686275, current_output: 83.67202,level: 3}]->(g);
CREATE (n: Building {id: 1819, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1819}) CREATE (g)-[r:Demand{max_demand: 14.991, current_input: 5.896005447745292, level: 3}]->(b);
CREATE (n: Building {id: 1820, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:1820}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.404386648280033, level: 3}]->(b);
CREATE (n: Building {id: 1821, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1821}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.71869601651129, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1821}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 8.989231865984571, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:1821}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 19.43664795581563,level: 1}]->(g);
CREATE (n: Building {id: 1822, name:"building_military_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1822}) CREATE (g)-[r:Demand{max_demand: 4.784, current_input: 4.238312087149501, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1822}) CREATE (g)-[r:Demand{max_demand: 9.568, current_input: 2.1502242623435097, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1822}) CREATE (g)-[r:Demand{max_demand: 2.392, current_input: 0.25139135085878617, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1822}) CREATE (b)-[r:Supply{max_supply: 8.372, current_output: 3.39278737002265,level: 1}]->(g);
CREATE (n: Building {id: 1823, name:"building_coffee_plantation", level:6});
MATCH (g: Goods{code: 41}), (b: Building{id:1823}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 126.0,level: 6}]->(g);
CREATE (n: Building {id: 1824, name:"building_sugar_plantation", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1824}) CREATE (b)-[r:Supply{max_supply: 4.554, current_output: 5.2371,level: 1}]->(g);
CREATE (n: Building {id: 1825, name:"building_maize_farm", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:1825}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.881174647152624, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1825}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 17.62349294305248,level: 6}]->(g);
CREATE (n: Building {id: 1826, name:"building_banana_plantation", level:3});
MATCH (g: Goods{code: 37}), (b: Building{id:1826}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 91.8,level: 3}]->(g);
CREATE (n: Building {id: 1827, name:"building_logging_camp", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1827}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.9372488238420793, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1827}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 35.246985886104945,level: 4}]->(g);
CREATE (n: Building {id: 1828, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1828}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.933030116566802, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1828}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 39.33030116566802,level: 2}]->(g);
CREATE (n: Building {id: 1829, name:"building_port", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1829}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.933030116566802, level: 2}]->(b);
CREATE (n: Building {id: 1830, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1830}) CREATE (g)-[r:Demand{max_demand: 24.805, current_input: 21.97561273447813, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1830}) CREATE (g)-[r:Demand{max_demand: 74.415, current_input: 16.72334223268105, level: 1}]->(b);
CREATE (n: Building {id: 1831, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1831}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.7419238994884285, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1831}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.1019343717289813, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1831}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 9.8948254570819,level: 1}]->(g);
CREATE (n: Building {id: 1832, name:"building_maize_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1832}) CREATE (g)-[r:Demand{max_demand: 0.97288, current_input: 0.14287953178697413, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1832}) CREATE (b)-[r:Supply{max_supply: 19.4576, current_output: 2.8575906357394825,level: 1}]->(g);
CREATE (n: Building {id: 1833, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1833}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.7343122059605198, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1833}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 8.811746471526238,level: 1}]->(g);
CREATE (n: Building {id: 1834, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1834}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1835, name:"building_cotton_plantation", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1835}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 50.0,level: 1}]->(g);
CREATE (n: Building {id: 1836, name:"building_maize_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1836}) CREATE (g)-[r:Demand{max_demand: 1.7953168316831682, current_input: 0.2636646126142637, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1836}) CREATE (b)-[r:Supply{max_supply: 35.906396039603955, current_output: 5.273300976786731,level: 2}]->(g);
CREATE (n: Building {id: 1837, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1837}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.4686244119210397, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1837}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 17.623492943052476,level: 2}]->(g);
CREATE (n: Building {id: 1838, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1838}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1839, name:"building_maize_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1839}) CREATE (g)-[r:Demand{max_demand: 0.99066, current_input: 0.1454907459913697, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1839}) CREATE (b)-[r:Supply{max_supply: 19.8132, current_output: 2.9098149198273937,level: 1}]->(g);
CREATE (n: Building {id: 1840, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1840}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.7343122059605198, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1840}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 8.811746471526238,level: 1}]->(g);
CREATE (n: Building {id: 1841, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1841}) CREATE (g)-[r:Demand{max_demand: 30.824, current_input: 27.308054300647203, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1841}) CREATE (b)-[r:Supply{max_supply: 34.677, current_output: 30.7215610882281,level: 1}]->(g);
CREATE (n: Building {id: 1842, name:"building_tobacco_plantation", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:1842}) CREATE (b)-[r:Supply{max_supply: 24.77, current_output: 24.77,level: 1}]->(g);
CREATE (n: Building {id: 1843, name:"building_maize_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1843}) CREATE (g)-[r:Demand{max_demand: 0.98533, current_input: 0.14470796917981582, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1843}) CREATE (b)-[r:Supply{max_supply: 19.7066, current_output: 2.8941593835963166,level: 1}]->(g);
CREATE (n: Building {id: 1844, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1844}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8014622160933444, level: 1}]->(b);
CREATE (n: Building {id: 1845, name:"building_textile_mills", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1845}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 106.31217609906774, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1845}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 119.60119811145121,level: 3}]->(g);
CREATE (n: Building {id: 1846, name:"building_cotton_plantation", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:1846}) CREATE (b)-[r:Supply{max_supply: 199.02399999999997, current_output: 256.74096,level: 5}]->(g);
CREATE (n: Building {id: 1847, name:"building_tobacco_plantation", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:1847}) CREATE (b)-[r:Supply{max_supply: 24.75, current_output: 24.75,level: 1}]->(g);
CREATE (n: Building {id: 1848, name:"building_maize_farm", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1848}) CREATE (g)-[r:Demand{max_demand: 3.678, current_input: 0.5401600587045584, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1848}) CREATE (b)-[r:Supply{max_supply: 73.56, current_output: 10.80320117409117,level: 4}]->(g);
CREATE (n: Building {id: 1849, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1849}) CREATE (b)-[r:Supply{max_supply: 29.904, current_output: 29.904,level: 1}]->(g);
CREATE (n: Building {id: 1850, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1850}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.966515058283401, level: 1}]->(b);
CREATE (n: Building {id: 1851, name:"building_university", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1851}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.9007311080466722, level: 1}]->(b);
CREATE (n: Building {id: 2974, name:"building_subsistence_farms", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:2974}) CREATE (b)-[r:Supply{max_supply: 21.02674, current_output: 21.02674,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2974}) CREATE (b)-[r:Supply{max_supply: 5.25668, current_output: 5.25668,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2974}) CREATE (b)-[r:Supply{max_supply: 5.25668, current_output: 5.25668,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2974}) CREATE (b)-[r:Supply{max_supply: 5.25668, current_output: 5.25668,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2974}) CREATE (b)-[r:Supply{max_supply: 5.25668, current_output: 5.25668,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2974}) CREATE (b)-[r:Supply{max_supply: 5.25668, current_output: 5.25668,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2974}) CREATE (b)-[r:Supply{max_supply: 5.25668, current_output: 5.25668,level: 29}]->(g);
CREATE (n: Building {id: 2975, name:"building_subsistence_rice_paddies", level:28});
MATCH (g: Goods{code: 7}), (b: Building{id:2975}) CREATE (b)-[r:Supply{max_supply: 10.54998, current_output: 10.54998,level: 28}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2975}) CREATE (b)-[r:Supply{max_supply: 1.75833, current_output: 1.75833,level: 28}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2975}) CREATE (b)-[r:Supply{max_supply: 1.75833, current_output: 1.75833,level: 28}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2975}) CREATE (b)-[r:Supply{max_supply: 2.34444, current_output: 2.34444,level: 28}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2975}) CREATE (b)-[r:Supply{max_supply: 2.34444, current_output: 2.34444,level: 28}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2975}) CREATE (b)-[r:Supply{max_supply: 2.34444, current_output: 2.34444,level: 28}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2975}) CREATE (b)-[r:Supply{max_supply: 2.34444, current_output: 2.34444,level: 28}]->(g);
CREATE (n: Building {id: 2977, name:"building_subsistence_farms", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:2977}) CREATE (b)-[r:Supply{max_supply: 2.9088, current_output: 2.9088,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2977}) CREATE (b)-[r:Supply{max_supply: 0.7272, current_output: 0.7272,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2977}) CREATE (b)-[r:Supply{max_supply: 0.7272, current_output: 0.7272,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2977}) CREATE (b)-[r:Supply{max_supply: 0.7272, current_output: 0.7272,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2977}) CREATE (b)-[r:Supply{max_supply: 0.7272, current_output: 0.7272,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2977}) CREATE (b)-[r:Supply{max_supply: 0.7272, current_output: 0.7272,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2977}) CREATE (b)-[r:Supply{max_supply: 0.7272, current_output: 0.7272,level: 32}]->(g);
CREATE (n: Building {id: 2980, name:"building_subsistence_farms", level:39});
MATCH (g: Goods{code: 7}), (b: Building{id:2980}) CREATE (b)-[r:Supply{max_supply: 2.3634, current_output: 2.3634,level: 39}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2980}) CREATE (b)-[r:Supply{max_supply: 0.59085, current_output: 0.59085,level: 39}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2980}) CREATE (b)-[r:Supply{max_supply: 0.59085, current_output: 0.59085,level: 39}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2980}) CREATE (b)-[r:Supply{max_supply: 0.59085, current_output: 0.59085,level: 39}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2980}) CREATE (b)-[r:Supply{max_supply: 0.59085, current_output: 0.59085,level: 39}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2980}) CREATE (b)-[r:Supply{max_supply: 0.59085, current_output: 0.59085,level: 39}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2980}) CREATE (b)-[r:Supply{max_supply: 0.59085, current_output: 0.59085,level: 39}]->(g);
CREATE (n: Building {id: 2982, name:"building_subsistence_farms", level:55});
MATCH (g: Goods{code: 7}), (b: Building{id:2982}) CREATE (b)-[r:Supply{max_supply: 6.2182956521739134, current_output: 7.15104,level: 55}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2982}) CREATE (b)-[r:Supply{max_supply: 1.5545739130434784, current_output: 1.78776,level: 55}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2982}) CREATE (b)-[r:Supply{max_supply: 1.5545739130434784, current_output: 1.78776,level: 55}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2982}) CREATE (b)-[r:Supply{max_supply: 1.5545739130434784, current_output: 1.78776,level: 55}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2982}) CREATE (b)-[r:Supply{max_supply: 1.5545739130434784, current_output: 1.78776,level: 55}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2982}) CREATE (b)-[r:Supply{max_supply: 1.5545739130434784, current_output: 1.78776,level: 55}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2982}) CREATE (b)-[r:Supply{max_supply: 1.5545739130434784, current_output: 1.78776,level: 55}]->(g);
CREATE (n: Building {id: 2984, name:"building_subsistence_farms", level:74});
MATCH (g: Goods{code: 7}), (b: Building{id:2984}) CREATE (b)-[r:Supply{max_supply: 11.807434782608697, current_output: 13.57855,level: 74}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2984}) CREATE (b)-[r:Supply{max_supply: 2.9518521739130437, current_output: 3.39463,level: 74}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2984}) CREATE (b)-[r:Supply{max_supply: 2.9518521739130437, current_output: 3.39463,level: 74}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2984}) CREATE (b)-[r:Supply{max_supply: 2.9518521739130437, current_output: 3.39463,level: 74}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2984}) CREATE (b)-[r:Supply{max_supply: 2.9518521739130437, current_output: 3.39463,level: 74}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2984}) CREATE (b)-[r:Supply{max_supply: 2.9518521739130437, current_output: 3.39463,level: 74}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2984}) CREATE (b)-[r:Supply{max_supply: 2.9518521739130437, current_output: 3.39463,level: 74}]->(g);
CREATE (n: Building {id: 2985, name:"building_subsistence_farms", level:37});
MATCH (g: Goods{code: 7}), (b: Building{id:2985}) CREATE (b)-[r:Supply{max_supply: 3.74588, current_output: 3.74588,level: 37}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2985}) CREATE (b)-[r:Supply{max_supply: 0.93647, current_output: 0.93647,level: 37}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2985}) CREATE (b)-[r:Supply{max_supply: 0.93647, current_output: 0.93647,level: 37}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2985}) CREATE (b)-[r:Supply{max_supply: 0.93647, current_output: 0.93647,level: 37}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2985}) CREATE (b)-[r:Supply{max_supply: 0.93647, current_output: 0.93647,level: 37}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2985}) CREATE (b)-[r:Supply{max_supply: 0.93647, current_output: 0.93647,level: 37}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2985}) CREATE (b)-[r:Supply{max_supply: 0.93647, current_output: 0.93647,level: 37}]->(g);
CREATE (n: Building {id: 2986, name:"building_subsistence_farms", level:39});
MATCH (g: Goods{code: 7}), (b: Building{id:2986}) CREATE (b)-[r:Supply{max_supply: 2.90082, current_output: 2.90082,level: 39}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2986}) CREATE (b)-[r:Supply{max_supply: 0.7252, current_output: 0.7252,level: 39}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2986}) CREATE (b)-[r:Supply{max_supply: 0.7252, current_output: 0.7252,level: 39}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2986}) CREATE (b)-[r:Supply{max_supply: 0.7252, current_output: 0.7252,level: 39}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2986}) CREATE (b)-[r:Supply{max_supply: 0.7252, current_output: 0.7252,level: 39}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2986}) CREATE (b)-[r:Supply{max_supply: 0.7252, current_output: 0.7252,level: 39}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2986}) CREATE (b)-[r:Supply{max_supply: 0.7252, current_output: 0.7252,level: 39}]->(g);
CREATE (n: Building {id: 2987, name:"building_subsistence_farms", level:118});
MATCH (g: Goods{code: 7}), (b: Building{id:2987}) CREATE (b)-[r:Supply{max_supply: 11.70796, current_output: 11.70796,level: 118}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2987}) CREATE (b)-[r:Supply{max_supply: 2.92699, current_output: 2.92699,level: 118}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2987}) CREATE (b)-[r:Supply{max_supply: 2.92699, current_output: 2.92699,level: 118}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2987}) CREATE (b)-[r:Supply{max_supply: 2.92699, current_output: 2.92699,level: 118}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2987}) CREATE (b)-[r:Supply{max_supply: 2.92699, current_output: 2.92699,level: 118}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2987}) CREATE (b)-[r:Supply{max_supply: 2.92699, current_output: 2.92699,level: 118}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2987}) CREATE (b)-[r:Supply{max_supply: 2.92699, current_output: 2.92699,level: 118}]->(g);
CREATE (n: Building {id: 2988, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:2988}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2988}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 2989, name:"building_subsistence_farms", level:138});
MATCH (g: Goods{code: 7}), (b: Building{id:2989}) CREATE (b)-[r:Supply{max_supply: 47.13252, current_output: 47.13252,level: 138}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2989}) CREATE (b)-[r:Supply{max_supply: 11.78313, current_output: 11.78313,level: 138}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2989}) CREATE (b)-[r:Supply{max_supply: 11.78313, current_output: 11.78313,level: 138}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2989}) CREATE (b)-[r:Supply{max_supply: 11.78313, current_output: 11.78313,level: 138}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2989}) CREATE (b)-[r:Supply{max_supply: 11.78313, current_output: 11.78313,level: 138}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2989}) CREATE (b)-[r:Supply{max_supply: 11.78313, current_output: 11.78313,level: 138}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2989}) CREATE (b)-[r:Supply{max_supply: 11.78313, current_output: 11.78313,level: 138}]->(g);
CREATE (n: Building {id: 2990, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:2990}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2990}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 2991, name:"building_subsistence_farms", level:67});
MATCH (g: Goods{code: 7}), (b: Building{id:2991}) CREATE (b)-[r:Supply{max_supply: 38.74744, current_output: 38.74744,level: 67}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2991}) CREATE (b)-[r:Supply{max_supply: 9.68686, current_output: 9.68686,level: 67}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2991}) CREATE (b)-[r:Supply{max_supply: 9.68686, current_output: 9.68686,level: 67}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2991}) CREATE (b)-[r:Supply{max_supply: 9.68686, current_output: 9.68686,level: 67}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2991}) CREATE (b)-[r:Supply{max_supply: 9.68686, current_output: 9.68686,level: 67}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2991}) CREATE (b)-[r:Supply{max_supply: 9.68686, current_output: 9.68686,level: 67}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2991}) CREATE (b)-[r:Supply{max_supply: 9.68686, current_output: 9.68686,level: 67}]->(g);
CREATE (n: Building {id: 2992, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 15}), (b: Building{id:2992}) CREATE (b)-[r:Supply{max_supply: 6.087, current_output: 6.26961,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2992}) CREATE (b)-[r:Supply{max_supply: 2.029, current_output: 2.08987,level: 4}]->(g);
CREATE (n: Building {id: 2993, name:"building_subsistence_farms", level:64});
MATCH (g: Goods{code: 7}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 25.62816, current_output: 25.62816,level: 64}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 6.40704, current_output: 6.40704,level: 64}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 6.40704, current_output: 6.40704,level: 64}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 6.40704, current_output: 6.40704,level: 64}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 6.40704, current_output: 6.40704,level: 64}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 6.40704, current_output: 6.40704,level: 64}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 6.40704, current_output: 6.40704,level: 64}]->(g);
CREATE (n: Building {id: 2994, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 15}), (b: Building{id:2994}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2994}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.3,level: 3}]->(g);
CREATE (n: Building {id: 2995, name:"building_subsistence_farms", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 4.65276, current_output: 4.65276,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 1.16319, current_output: 1.16319,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 1.16319, current_output: 1.16319,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 1.16319, current_output: 1.16319,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 1.16319, current_output: 1.16319,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 1.16319, current_output: 1.16319,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 1.16319, current_output: 1.16319,level: 29}]->(g);
CREATE (n: Building {id: 2996, name:"building_subsistence_farms", level:27});
MATCH (g: Goods{code: 7}), (b: Building{id:2996}) CREATE (b)-[r:Supply{max_supply: 14.53896, current_output: 14.53896,level: 27}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2996}) CREATE (b)-[r:Supply{max_supply: 3.63474, current_output: 3.63474,level: 27}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2996}) CREATE (b)-[r:Supply{max_supply: 3.63474, current_output: 3.63474,level: 27}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2996}) CREATE (b)-[r:Supply{max_supply: 3.63474, current_output: 3.63474,level: 27}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2996}) CREATE (b)-[r:Supply{max_supply: 3.63474, current_output: 3.63474,level: 27}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2996}) CREATE (b)-[r:Supply{max_supply: 3.63474, current_output: 3.63474,level: 27}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2996}) CREATE (b)-[r:Supply{max_supply: 3.63474, current_output: 3.63474,level: 27}]->(g);
CREATE (n: Building {id: 2997, name:"building_subsistence_farms", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:2997}) CREATE (b)-[r:Supply{max_supply: 8.88792, current_output: 8.88792,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2997}) CREATE (b)-[r:Supply{max_supply: 2.22198, current_output: 2.22198,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2997}) CREATE (b)-[r:Supply{max_supply: 2.22198, current_output: 2.22198,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2997}) CREATE (b)-[r:Supply{max_supply: 2.22198, current_output: 2.22198,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2997}) CREATE (b)-[r:Supply{max_supply: 2.22198, current_output: 2.22198,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2997}) CREATE (b)-[r:Supply{max_supply: 2.22198, current_output: 2.22198,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2997}) CREATE (b)-[r:Supply{max_supply: 2.22198, current_output: 2.22198,level: 29}]->(g);
CREATE (n: Building {id: 2998, name:"building_subsistence_farms", level:49});
MATCH (g: Goods{code: 7}), (b: Building{id:2998}) CREATE (b)-[r:Supply{max_supply: 45.35244, current_output: 45.35244,level: 49}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2998}) CREATE (b)-[r:Supply{max_supply: 11.33811, current_output: 11.33811,level: 49}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2998}) CREATE (b)-[r:Supply{max_supply: 11.33811, current_output: 11.33811,level: 49}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2998}) CREATE (b)-[r:Supply{max_supply: 11.33811, current_output: 11.33811,level: 49}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2998}) CREATE (b)-[r:Supply{max_supply: 11.33811, current_output: 11.33811,level: 49}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2998}) CREATE (b)-[r:Supply{max_supply: 11.33811, current_output: 11.33811,level: 49}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2998}) CREATE (b)-[r:Supply{max_supply: 11.33811, current_output: 11.33811,level: 49}]->(g);
CREATE (n: Building {id: 2999, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 15}), (b: Building{id:2999}) CREATE (b)-[r:Supply{max_supply: 23.247, current_output: 23.47947,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2999}) CREATE (b)-[r:Supply{max_supply: 7.749, current_output: 7.82649,level: 2}]->(g);
CREATE (n: Building {id: 3000, name:"building_subsistence_farms", level:48});
MATCH (g: Goods{code: 7}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 10.72512, current_output: 10.72512,level: 48}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 2.68128, current_output: 2.68128,level: 48}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 2.68128, current_output: 2.68128,level: 48}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 2.68128, current_output: 2.68128,level: 48}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 2.68128, current_output: 2.68128,level: 48}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 2.68128, current_output: 2.68128,level: 48}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3000}) CREATE (b)-[r:Supply{max_supply: 2.68128, current_output: 2.68128,level: 48}]->(g);
CREATE (n: Building {id: 4014, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:4014}) CREATE (g)-[r:Demand{max_demand: 1.501, current_input: 0.11394409863819878, level: 2}]->(b);
CREATE (n: Building {id: 4015, name:"building_barracks", level:4});
MATCH (g: Goods{code: 1}), (b: Building{id:4015}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.1518242486851416, level: 4}]->(b);
CREATE (n: Building {id: 4016, name:"building_barracks", level:4});
MATCH (g: Goods{code: 1}), (b: Building{id:4016}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.3036484973702832, level: 4}]->(b);
CREATE (n: Building {id: 4017, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:4017}) CREATE (g)-[r:Demand{max_demand: 1.5, current_input: 0.1138681865138562, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4017}) CREATE (g)-[r:Demand{max_demand: 1.125, current_input: 0.353565795631737, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4017}) CREATE (g)-[r:Demand{max_demand: 0.75, current_input: 0.0788225389398368, level: 2}]->(b);
CREATE (n: Building {id: 4018, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:4018}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.1518242486851416, level: 2}]->(b);
CREATE (n: Building {id: 4019, name:"building_barracks", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:4019}) CREATE (g)-[r:Demand{max_demand: 1.99864, current_input: 0.1517210081960357, level: 3}]->(b);
CREATE (n: Building {id: 4020, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:4020}) CREATE (g)-[r:Demand{max_demand: 0.732, current_input: 0.05556767501876183, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4020}) CREATE (g)-[r:Demand{max_demand: 0.732, current_input: 0.23005347769105022, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4020}) CREATE (g)-[r:Demand{max_demand: 0.732, current_input: 0.07693079800528071, level: 1}]->(b);
CREATE (n: Building {id: 4021, name:"building_naval_base", level:7});
MATCH (g: Goods{code: 5}), (b: Building{id:4021}) CREATE (g)-[r:Demand{max_demand: 8.99997, current_input: 2.6470243391431345, level: 7}]->(b);
CREATE (n: Building {id: 4022, name:"building_naval_base", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:4022}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.8823443875290033, level: 3}]->(b);
CREATE (n: Building {id: 4029, name:"building_barracks", level:4});
MATCH (g: Goods{code: 1}), (b: Building{id:4029}) CREATE (g)-[r:Demand{max_demand: 0.906, current_input: 0.06877638465436915, level: 4}]->(b);
CREATE (n: Building {id: 4030, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:4030}) CREATE (g)-[r:Demand{max_demand: 1.13, current_input: 0.085780700507105, level: 2}]->(b);
CREATE (n: Building {id: 4031, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4031}) CREATE (g)-[r:Demand{max_demand: 5.7106, current_input: 0.43350377727068484, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4031}) CREATE (g)-[r:Demand{max_demand: 1.6316, current_input: 0.5127804019135485, level: 5}]->(b);
CREATE (n: Building {id: 4176, name:"building_trade_center", level:15});
CREATE (n: Building {id: 4177, name:"building_trade_center", level:14});
CREATE (n: Building {id: 4494, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 4602, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 4733, name:"building_whaling_station", level:1});
CREATE (n: Building {id: 4815, name:"building_construction_sector", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:4815}) CREATE (g)-[r:Demand{max_demand: 74.415, current_input: 65.92683820343439, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4815}) CREATE (g)-[r:Demand{max_demand: 223.245, current_input: 50.170026698043145, level: 3}]->(b);
CREATE (n: Building {id: 4869, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 5013, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:5013}) CREATE (g)-[r:Demand{max_demand: 49.59015, current_input: 43.933639663159866, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5013}) CREATE (g)-[r:Demand{max_demand: 148.77046, current_input: 33.43330399372958, level: 2}]->(b);
CREATE (n: Building {id: 5058, name:"building_trade_center", level:14});
CREATE (n: Building {id: 5064, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:5064}) CREATE (b)-[r:Supply{max_supply: 29.136000000000003, current_output: 33.5064,level: 1}]->(g);
CREATE (n: Building {id: 5162, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 5337, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:5337}) CREATE (g)-[r:Demand{max_demand: 4.19105, current_input: 3.7129970469999822, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5337}) CREATE (g)-[r:Demand{max_demand: 8.3821, current_input: 1.883716010596732, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5337}) CREATE (g)-[r:Demand{max_demand: 10.47763, current_input: 1.1011645315629364, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5337}) CREATE (g)-[r:Demand{max_demand: 2.09552, current_input: 0.30775318276687774, level: 2}]->(b);
CREATE (n: Building {id: 5341, name:"building_trade_center", level:8});
CREATE (n: Building {id: 5361, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 5463, name:"building_rice_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:5463}) CREATE (g)-[r:Demand{max_demand: 0.19776, current_input: 0.029043516370150485, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:5463}) CREATE (b)-[r:Supply{max_supply: 3.9552, current_output: 0.5808703274030098,level: 1}]->(g);
CREATE (n: Building {id: 5831, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5831}) CREATE (g)-[r:Demand{max_demand: 21.08425, current_input: 18.67927082430641, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5831}) CREATE (g)-[r:Demand{max_demand: 63.25275, current_input: 14.214840897778892, level: 1}]->(b);
