CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:72.05065954625938, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:93.24863693631818, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:99.7640257102008, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:101.41924450010515, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:14.384710966176435, pop_demand:3309.384047806331});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:11.793689574414294, pop_demand:268.0445650379086});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:24.83546156658404, pop_demand:261.8474190478729});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:25.781086718500987, pop_demand:214.87789856805307});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:27.438859593590866, pop_demand:355.14919201933003});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:27.93869345711018, pop_demand:1427.8283206989565});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:16.194059369825922, pop_demand:1085.5716161559965});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:39.886154440968404, pop_demand:205.14985588816236});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:11.327403098865725, pop_demand:647.7479221382582});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:12.550352676590755, pop_demand:76.82043301320405});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:42.15458539939617, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:37.86539973803792, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:62.11923823529412, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:62.904671008188764, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:46.85201237603826, pop_demand:110.97124658938283});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:42.06984444463092, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:55.29307536381378, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:59.940499208869255, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:79.02651533456734, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:64.90962458582506, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:57.13111027150019, pop_demand:71.10404855832094});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:36.89334810791959, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:38.42261492988414, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:41.97650567954601, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:48.94409512115359});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:18.72804095430139, pop_demand:285.73920645456326});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:18.040054418012566, pop_demand:8.89487371954519});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:17.031605856012735, pop_demand:671.1855596286567});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:36.91834865503474, pop_demand:468.6121627103061});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:0.008334411476027872});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:26.691789206715576, pop_demand:12.745452878218046});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:45.843777829033066, pop_demand:74.40633430724502});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:67.48575016441386, pop_demand:532.8545640285079});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:45.57863116458612, pop_demand:298.8603669756624});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:26.992538302612562, pop_demand:436.0992007996589});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:183.7783117054393, pop_demand:2.2469257495888906});
CREATE (n: Building {id: 206, name:"building_government_administrationlevel", level:18});
MATCH (g: Goods{code: 14}), (b: Building{id:206}) CREATE (g)-[r:Demand{max_demand: 360.0, current_input: 201.82152894450556, level: 18}]->(b);
CREATE (n: Building {id: 207, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:207}) CREATE (g)-[r:Demand{max_demand: 4.932, current_input: 3.3421002369071684, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:207}) CREATE (g)-[r:Demand{max_demand: 9.864, current_input: 6.062357373913753, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:207}) CREATE (g)-[r:Demand{max_demand: 12.33, current_input: 11.479293933256693, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:207}) CREATE (g)-[r:Demand{max_demand: 2.466, current_input: 2.602856921592245, level: 3}]->(b);
CREATE (n: Building {id: 208, name:"building_universitylevel", level:9});
MATCH (g: Goods{code: 14}), (b: Building{id:208}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 50.45538223612639, level: 9}]->(b);
CREATE (n: Building {id: 209, name:"building_arts_academylevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:209}) CREATE (g)-[r:Demand{max_demand: 14.979892156862745, current_input: 8.397957607005129, level: 3}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:209}) CREATE (b)-[r:Supply{max_supply: 4.4939607843137255, current_output: 2.5193834347412376,level: 3}]->(g);
CREATE (n: Building {id: 210, name:"building_paper_millslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:210}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 92.18913281499015, level: 5}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:210}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 32.79377198908165, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:210}) CREATE (b)-[r:Supply{max_supply: 350.00000000000006, current_output: 222.33219024594095,level: 5}]->(g);
CREATE (n: Building {id: 211, name:"building_furniture_manufacturieslevel", level:15});
MATCH (g: Goods{code: 9}), (b: Building{id:211}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 101.6453843341596, level: 15}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:211}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 184.3782656299803, level: 15}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:211}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 18.826625413205807, level: 15}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:211}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 50.29750395565374, level: 15}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:211}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 158.32463026716815, level: 15}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:211}) CREATE (b)-[r:Supply{max_supply: 672.6375, current_output: 387.2466211315102,level: 15}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:211}) CREATE (b)-[r:Supply{max_supply: 298.95, current_output: 172.1096093917823,level: 15}]->(g);
CREATE (n: Building {id: 212, name:"building_wheat_farmlevel", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:212}) CREATE (g)-[r:Demand{max_demand: 37.5843937007874, current_input: 26.06962440280395, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:212}) CREATE (b)-[r:Supply{max_supply: 263.09079527559055, current_output: 182.48739812785493,level: 8}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:212}) CREATE (b)-[r:Supply{max_supply: 60.13503937007874, current_output: 41.711405598460864,level: 8}]->(g);
CREATE (n: Building {id: 213, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:213}) CREATE (g)-[r:Demand{max_demand: 27.7665, current_input: 44.38042913640115, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:213}) CREATE (g)-[r:Demand{max_demand: 27.7665, current_input: 29.3074723087555, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:213}) CREATE (b)-[r:Supply{max_supply: 111.066, current_output: 111.066,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:213}) CREATE (b)-[r:Supply{max_supply: 13.883245901639345, current_output: 13.883245901639345,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:213}) CREATE (b)-[r:Supply{max_supply: 69.41624590163934, current_output: 69.41624590163934,level: 3}]->(g);
CREATE (n: Building {id: 214, name:"building_railwaylevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:214}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.5306501652823234, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:214}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 17.315656443621773, level: 3}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:214}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 62.11576652226024,level: 3}]->(g);
CREATE (n: Building {id: 215, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:215}) CREATE (g)-[r:Demand{max_demand: 24.0, current_input: 9.887577890393997, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:215}) CREATE (g)-[r:Demand{max_demand: 24.0, current_input: 6.2673936339636365, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:215}) CREATE (g)-[r:Demand{max_demand: 24.0, current_input: 10.393588246765345, level: 15}]->(b);
CREATE (n: Building {id: 218, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:218}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 60.98723060049576, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:218}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 32.298119117647055, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:218}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.940380882352941, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:218}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 77.61311817538375,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:218}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 38.80655908769187,level: 3}]->(g);
CREATE (n: Building {id: 219, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:219}) CREATE (g)-[r:Demand{max_demand: 8.502, current_input: 14.089333141080019, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:219}) CREATE (b)-[r:Supply{max_supply: 85.02, current_output: 85.02,level: 2}]->(g);
CREATE (n: Building {id: 220, name:"building_silk_plantationlevel", level:8});
MATCH (g: Goods{code: 20}), (b: Building{id:220}) CREATE (b)-[r:Supply{max_supply: 141.55679439252336, current_output: 151.46577,level: 8}]->(g);
CREATE (n: Building {id: 221, name:"building_wheat_farmlevel", level:10});
MATCH (g: Goods{code: 32}), (b: Building{id:221}) CREATE (g)-[r:Demand{max_demand: 46.888000000000005, current_input: 32.52287528514962, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:221}) CREATE (b)-[r:Supply{max_supply: 328.216, current_output: 227.6601269960473,level: 10}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:221}) CREATE (b)-[r:Supply{max_supply: 75.02079831932774, current_output: 52.03659929047641,level: 10}]->(g);
CREATE (n: Building {id: 222, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:222}) CREATE (g)-[r:Demand{max_demand: 14.4784, current_input: 5.964846155345019, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:222}) CREATE (g)-[r:Demand{max_demand: 14.4784, current_input: 3.78090966624913, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:222}) CREATE (g)-[r:Demand{max_demand: 14.4784, current_input: 6.270105336331974, level: 10}]->(b);
CREATE (n: Building {id: 223, name:"building_naval_baselevel", level:21});
MATCH (g: Goods{code: 5}), (b: Building{id:223}) CREATE (g)-[r:Demand{max_demand: 42.0, current_input: 16.86460439991589, level: 21}]->(b);
CREATE (n: Building {id: 224, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:224}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 16.571786804375463, level: 2}]->(b);
CREATE (n: Building {id: 225, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:225}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 44.849228654334574, level: 4}]->(b);
CREATE (n: Building {id: 226, name:"building_shipyardslevel", level:7});
MATCH (g: Goods{code: 9}), (b: Building{id:226}) CREATE (g)-[r:Demand{max_demand: 128.30859482758623, current_input: 86.94650956417311, level: 7}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:226}) CREATE (g)-[r:Demand{max_demand: 256.6171982758621, current_input: 157.71544649642743, level: 7}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:226}) CREATE (g)-[r:Demand{max_demand: 128.30859482758623, current_input: 43.02401370589927, level: 7}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:226}) CREATE (g)-[r:Demand{max_demand: 64.15429310344828, current_input: 37.02912329209081, level: 7}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:226}) CREATE (b)-[r:Supply{max_supply: 95.8946379310345, current_output: 52.85557466202213,level: 7}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:226}) CREATE (b)-[r:Supply{max_supply: 351.61367241379315, current_output: 193.80377376074782,level: 7}]->(g);
CREATE (n: Building {id: 227, name:"building_textile_millslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:227}) CREATE (g)-[r:Demand{max_demand: 240.00000000000003, current_input: 162.6326149346554, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:227}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 86.12831764705882, level: 8}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:227}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 10.507682352941176, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:227}) CREATE (b)-[r:Supply{max_supply: 320.0, current_output: 206.96831513435666,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:227}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 103.48415756717833,level: 8}]->(g);
CREATE (n: Building {id: 228, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:228}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.5102167217607745, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:228}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.771885481207258, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:228}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 20.705255507420084,level: 1}]->(g);
CREATE (n: Building {id: 229, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:229}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 24.857680206563195, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:229}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 150.0,level: 3}]->(g);
CREATE (n: Building {id: 230, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:230}) CREATE (g)-[r:Demand{max_demand: 23.98737, current_input: 9.882374552529177, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:230}) CREATE (g)-[r:Demand{max_demand: 23.98737, current_input: 6.2640954180637625, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:230}) CREATE (g)-[r:Demand{max_demand: 23.98737, current_input: 10.388118620950483, level: 15}]->(b);
CREATE (n: Building {id: 231, name:"building_wheat_farmlevel", level:6});
MATCH (g: Goods{code: 32}), (b: Building{id:231}) CREATE (g)-[r:Demand{max_demand: 28.130695652173912, current_input: 19.512265534468366, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:231}) CREATE (b)-[r:Supply{max_supply: 196.91489565217393, current_output: 136.58587683594743,level: 6}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:231}) CREATE (b)-[r:Supply{max_supply: 45.009113043478266, current_output: 31.219624855149387,level: 6}]->(g);
CREATE (n: Building {id: 232, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:232}) CREATE (g)-[r:Demand{max_demand: 18.50979279279279, current_input: 29.58502322474958, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:232}) CREATE (g)-[r:Demand{max_demand: 18.50979279279279, current_input: 19.537040668272084, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:232}) CREATE (b)-[r:Supply{max_supply: 74.03919819819819, current_output: 74.03919819819819,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:232}) CREATE (b)-[r:Supply{max_supply: 9.25489189189189, current_output: 9.25489189189189,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:232}) CREATE (b)-[r:Supply{max_supply: 46.27449549549549, current_output: 46.27449549549549,level: 2}]->(g);
CREATE (n: Building {id: 233, name:"building_naval_baselevel", level:25});
MATCH (g: Goods{code: 5}), (b: Building{id:233}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 20.076909999899872, level: 25}]->(b);
CREATE (n: Building {id: 234, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:234}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 24.857680206563195, level: 3}]->(b);
CREATE (n: Building {id: 235, name:"building_universitylevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:235}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.212307163583644, level: 4}]->(b);
CREATE (n: Building {id: 236, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:236}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 16.571786804375463, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:236}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 100.0,level: 2}]->(g);
CREATE (n: Building {id: 237, name:"building_glassworkslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:237}) CREATE (g)-[r:Demand{max_demand: 100.00000000000001, current_input: 61.459421876660116, level: 5}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:237}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 24.511541060310368, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:237}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 110.48250399728084,level: 5}]->(g);
CREATE (n: Building {id: 238, name:"building_wheat_farmlevel", level:9});
MATCH (g: Goods{code: 32}), (b: Building{id:238}) CREATE (g)-[r:Demand{max_demand: 42.19784745762713, current_input: 29.269649594057643, level: 9}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:238}) CREATE (b)-[r:Supply{max_supply: 295.3849491525424, current_output: 204.88755891482674,level: 9}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:238}) CREATE (b)-[r:Supply{max_supply: 67.5165593220339, current_output: 46.831441701776875,level: 9}]->(g);
CREATE (n: Building {id: 239, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:239}) CREATE (g)-[r:Demand{max_demand: 18.50979279279279, current_input: 29.58502322474958, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:239}) CREATE (g)-[r:Demand{max_demand: 18.50979279279279, current_input: 19.537040668272084, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:239}) CREATE (b)-[r:Supply{max_supply: 74.03919819819819, current_output: 74.03919819819819,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:239}) CREATE (b)-[r:Supply{max_supply: 9.25489189189189, current_output: 9.25489189189189,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:239}) CREATE (b)-[r:Supply{max_supply: 46.27449549549549, current_output: 46.27449549549549,level: 2}]->(g);
CREATE (n: Building {id: 240, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 0}), (b: Building{id:240}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 13.183437187191997, level: 20}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:240}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 8.356524845284849, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:240}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 13.858117662353793, level: 20}]->(b);
CREATE (n: Building {id: 241, name:"building_construction_sectorlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:241}) CREATE (g)-[r:Demand{max_demand: 8.22, current_input: 5.570167061511947, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:241}) CREATE (g)-[r:Demand{max_demand: 16.44, current_input: 10.103928956522923, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:241}) CREATE (g)-[r:Demand{max_demand: 20.55, current_input: 19.132156555427823, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:241}) CREATE (g)-[r:Demand{max_demand: 4.11, current_input: 4.3380948693204076, level: 5}]->(b);
CREATE (n: Building {id: 242, name:"building_government_administrationlevel", level:8});
MATCH (g: Goods{code: 14}), (b: Building{id:242}) CREATE (g)-[r:Demand{max_demand: 159.99999999999997, current_input: 89.69845730866912, level: 8}]->(b);
CREATE (n: Building {id: 243, name:"building_textile_millslevel", level:7});
MATCH (g: Goods{code: 9}), (b: Building{id:243}) CREATE (g)-[r:Demand{max_demand: 210.0, current_input: 142.30353806782347, level: 7}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:243}) CREATE (g)-[r:Demand{max_demand: 70.00000000000001, current_input: 75.36227794117649, level: 7}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:243}) CREATE (g)-[r:Demand{max_demand: 35.00000000000001, current_input: 9.19422205882353, level: 7}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:243}) CREATE (b)-[r:Supply{max_supply: 280.00000000000006, current_output: 181.09727574256212,level: 7}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:243}) CREATE (b)-[r:Supply{max_supply: 140.00000000000003, current_output: 90.54863787128106,level: 7}]->(g);
CREATE (n: Building {id: 244, name:"building_paper_millslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:244}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 110.62695937798819, level: 6}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:244}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 39.35252638689798, level: 6}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:244}) CREATE (b)-[r:Supply{max_supply: 420.00000000000006, current_output: 266.79862829512916,level: 6}]->(g);
CREATE (n: Building {id: 245, name:"building_arms_industrylevel", level:6});
MATCH (g: Goods{code: 24}), (b: Building{id:245}) CREATE (g)-[r:Demand{max_demand: 118.99560000000001, current_input: 110.7855206134826, level: 6}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:245}) CREATE (g)-[r:Demand{max_demand: 59.497800000000005, current_input: 19.950605539017968, level: 6}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:245}) CREATE (b)-[r:Supply{max_supply: 88.93433043478261, current_output: 56.309744177057546,level: 6}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:245}) CREATE (b)-[r:Supply{max_supply: 88.93433043478261, current_output: 56.309744177057546,level: 6}]->(g);
CREATE (n: Building {id: 246, name:"building_logging_camplevel", level:7});
MATCH (g: Goods{code: 33}), (b: Building{id:246}) CREATE (g)-[r:Demand{max_demand: 35.00000000000001, current_input: 36.94241372900591, level: 7}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:246}) CREATE (b)-[r:Supply{max_supply: 280.00000000000006, current_output: 280.00000000000006,level: 7}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:246}) CREATE (b)-[r:Supply{max_supply: 70.00000000000001, current_output: 70.00000000000001,level: 7}]->(g);
CREATE (n: Building {id: 247, name:"building_coal_minelevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:247}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 94.9947781603009, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:247}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 360.0,level: 6}]->(g);
CREATE (n: Building {id: 248, name:"building_silk_plantationlevel", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:248}) CREATE (b)-[r:Supply{max_supply: 196.86199999999997, current_output: 214.57958,level: 10}]->(g);
CREATE (n: Building {id: 249, name:"building_wheat_farmlevel", level:15});
MATCH (g: Goods{code: 32}), (b: Building{id:249}) CREATE (g)-[r:Demand{max_demand: 73.98825, current_input: 51.32039386018749, level: 15}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:249}) CREATE (b)-[r:Supply{max_supply: 517.9177500000001, current_output: 359.24275702131246,level: 15}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:249}) CREATE (b)-[r:Supply{max_supply: 118.38119354838709, current_output: 82.11262570127434,level: 15}]->(g);
CREATE (n: Building {id: 250, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 0}), (b: Building{id:250}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 13.183437187191997, level: 20}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:250}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 8.356524845284849, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:250}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 13.858117662353793, level: 20}]->(b);
CREATE (n: Building {id: 251, name:"building_railwaylevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:251}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.5306501652823234, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:251}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 17.315656443621773, level: 3}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:251}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 62.11576652226024,level: 3}]->(g);
CREATE (n: Building {id: 252, name:"building_arms_industrylevel", level:4});
MATCH (g: Goods{code: 24}), (b: Building{id:252}) CREATE (g)-[r:Demand{max_demand: 39.71519469026549, current_input: 36.97505218702945, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:252}) CREATE (g)-[r:Demand{max_demand: 39.71519469026549, current_input: 13.317167746887913, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:252}) CREATE (g)-[r:Demand{max_demand: 59.572796460177, current_input: 35.887248973536806, level: 4}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:252}) CREATE (b)-[r:Supply{max_supply: 39.57619469026549, current_output: 24.652432357771293,level: 4}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:252}) CREATE (b)-[r:Supply{max_supply: 98.94048672566373, current_output: 61.631080894428244,level: 4}]->(g);
CREATE (n: Building {id: 253, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:253}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 10.554975351144543, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:253}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 79.99999999999999,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:253}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 19.999999999999996,level: 2}]->(g);
CREATE (n: Building {id: 254, name:"building_munition_plantslevel", level:5});
MATCH (g: Goods{code: 25}), (b: Building{id:254}) CREATE (g)-[r:Demand{max_demand: 97.14500000000001, current_input: 47.62347312607701, level: 5}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:254}) CREATE (g)-[r:Demand{max_demand: 97.14500000000001, current_input: 123.58783431130594, level: 5}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:254}) CREATE (b)-[r:Supply{max_supply: 242.8625, current_output: 180.96059140759627,level: 5}]->(g);
CREATE (n: Building {id: 255, name:"building_wheat_farmlevel", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:255}) CREATE (g)-[r:Demand{max_demand: 37.52, current_input: 26.024959066260315, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:255}) CREATE (b)-[r:Supply{max_supply: 262.64, current_output: 182.17471346382217,level: 8}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:255}) CREATE (b)-[r:Supply{max_supply: 60.03200000000001, current_output: 41.63993450601651,level: 8}]->(g);
CREATE (n: Building {id: 256, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:256}) CREATE (g)-[r:Demand{max_demand: 18.50979279279279, current_input: 29.58502322474958, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:256}) CREATE (g)-[r:Demand{max_demand: 18.50979279279279, current_input: 19.537040668272084, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:256}) CREATE (b)-[r:Supply{max_supply: 74.03919819819819, current_output: 74.03919819819819,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:256}) CREATE (b)-[r:Supply{max_supply: 9.25489189189189, current_output: 9.25489189189189,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:256}) CREATE (b)-[r:Supply{max_supply: 46.27449549549549, current_output: 46.27449549549549,level: 2}]->(g);
CREATE (n: Building {id: 257, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 0}), (b: Building{id:257}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 16.479296483989994, level: 25}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:257}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 10.445656056606062, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:257}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 17.322647077942243, level: 25}]->(b);
CREATE (n: Building {id: 258, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:258}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.285893402187732, level: 1}]->(b);
CREATE (n: Building {id: 259, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:259}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 44.849228654334574, level: 4}]->(b);
CREATE (n: Building {id: 260, name:"building_textile_millslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:260}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 101.6453843341596, level: 5}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:260}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 53.83019852941177, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:260}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 6.5673014705882355, level: 5}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:260}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 129.3551969589729,level: 5}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:260}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 64.67759847948645,level: 5}]->(g);
CREATE (n: Building {id: 261, name:"building_coal_minelevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:261}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 94.9947781603009, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:261}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 360.0,level: 6}]->(g);
CREATE (n: Building {id: 262, name:"building_wheat_farmlevel", level:7});
MATCH (g: Goods{code: 32}), (b: Building{id:262}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 24.27701405434731, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:262}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 7.38848274580118, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:262}) CREATE (b)-[r:Supply{max_supply: 245.0, current_output: 207.46954919021556,level: 7}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:262}) CREATE (b)-[r:Supply{max_supply: 56.0, current_output: 47.42161124347785,level: 7}]->(g);
CREATE (n: Building {id: 263, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:263}) CREATE (g)-[r:Demand{max_demand: 18.507793388429754, current_input: 29.581827488029116, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:263}) CREATE (g)-[r:Demand{max_demand: 18.507793388429754, current_input: 19.5349303018952, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:263}) CREATE (b)-[r:Supply{max_supply: 74.03119834710743, current_output: 74.03119834710743,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:263}) CREATE (b)-[r:Supply{max_supply: 9.253892561983472, current_output: 9.253892561983472,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:263}) CREATE (b)-[r:Supply{max_supply: 46.2694958677686, current_output: 46.2694958677686,level: 2}]->(g);
CREATE (n: Building {id: 264, name:"building_naval_baselevel", level:25});
MATCH (g: Goods{code: 5}), (b: Building{id:264}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 20.076909999899872, level: 25}]->(b);
CREATE (n: Building {id: 265, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:265}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 16.571786804375463, level: 2}]->(b);
CREATE (n: Building {id: 266, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:266}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.5102167217607745, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:266}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.771885481207258, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:266}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 20.705255507420084,level: 1}]->(g);
CREATE (n: Building {id: 267, name:"building_steel_millslevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:267}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 22.59195049584697, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:267}) CREATE (g)-[r:Demand{max_demand: 179.99999999999997, current_input: 167.58093333221447, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:267}) CREATE (b)-[r:Supply{max_supply: 269.99999999999994, current_output: 159.5736257429313,level: 3}]->(g);
CREATE (n: Building {id: 268, name:"building_arms_industrylevel", level:3});
MATCH (g: Goods{code: 24}), (b: Building{id:268}) CREATE (g)-[r:Demand{max_demand: 29.78879464285714, current_input: 27.733522272731506, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:268}) CREATE (g)-[r:Demand{max_demand: 29.78879464285714, current_input: 9.98868010922176, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:268}) CREATE (g)-[r:Demand{max_demand: 44.68319642857143, current_input: 26.91760485404665, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:268}) CREATE (b)-[r:Supply{max_supply: 29.68453571428571, current_output: 18.490812835734143,level: 3}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:268}) CREATE (b)-[r:Supply{max_supply: 74.21133928571427, current_output: 46.22703208933537,level: 3}]->(g);
CREATE (n: Building {id: 269, name:"building_iron_minelevel", level:10});
MATCH (g: Goods{code: 23}), (b: Building{id:269}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 37.653250826411615, level: 10}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:269}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 158.32463026716815, level: 10}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:269}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 375.3065016528232,level: 10}]->(g);
CREATE (n: Building {id: 270, name:"building_logging_camplevel", level:7});
MATCH (g: Goods{code: 33}), (b: Building{id:270}) CREATE (g)-[r:Demand{max_demand: 35.00000000000001, current_input: 36.94241372900591, level: 7}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:270}) CREATE (b)-[r:Supply{max_supply: 280.00000000000006, current_output: 280.00000000000006,level: 7}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:270}) CREATE (b)-[r:Supply{max_supply: 70.00000000000001, current_output: 70.00000000000001,level: 7}]->(g);
CREATE (n: Building {id: 271, name:"building_wheat_farmlevel", level:7});
MATCH (g: Goods{code: 32}), (b: Building{id:271}) CREATE (g)-[r:Demand{max_demand: 32.31409482758621, current_input: 22.41399240808063, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:271}) CREATE (b)-[r:Supply{max_supply: 226.19869827586209, current_output: 156.89797077480486,level: 7}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:271}) CREATE (b)-[r:Supply{max_supply: 51.70255172413794, current_output: 35.86238785292901,level: 7}]->(g);
CREATE (n: Building {id: 272, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 0}), (b: Building{id:272}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 13.183437187191997, level: 20}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:272}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 8.356524845284849, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:272}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 13.858117662353793, level: 20}]->(b);
CREATE (n: Building {id: 273, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:273}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.87565312599606, level: 2}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:273}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 13.117508795632656, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:273}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 88.93287609837638,level: 2}]->(g);
CREATE (n: Building {id: 274, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:274}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 16.571786804375463, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:274}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 100.0,level: 2}]->(g);
CREATE (n: Building {id: 275, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:275}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.285893402187732, level: 1}]->(b);
CREATE (n: Building {id: 276, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:276}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 60.98723060049576, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:276}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 32.298119117647055, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:276}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.940380882352941, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:276}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 77.61311817538375,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:276}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 38.80655908769187,level: 3}]->(g);
CREATE (n: Building {id: 277, name:"building_wheat_farmlevel", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:277}) CREATE (g)-[r:Demand{max_demand: 37.16919658119659, current_input: 25.781631651157355, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:277}) CREATE (b)-[r:Supply{max_supply: 260.1843931623932, current_output: 180.471433415007,level: 8}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:277}) CREATE (b)-[r:Supply{max_supply: 59.470717948717954, current_output: 41.25061301323287,level: 8}]->(g);
CREATE (n: Building {id: 278, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:278}) CREATE (g)-[r:Demand{max_demand: 18.539198198198196, current_input: 29.6320231891247, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:278}) CREATE (g)-[r:Demand{max_demand: 18.539198198198196, current_input: 19.568078001196533, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:278}) CREATE (b)-[r:Supply{max_supply: 74.15679279279279, current_output: 74.15679279279279,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:278}) CREATE (b)-[r:Supply{max_supply: 9.269594594594594, current_output: 9.269594594594594,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:278}) CREATE (b)-[r:Supply{max_supply: 46.348, current_output: 46.348,level: 2}]->(g);
CREATE (n: Building {id: 279, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 0}), (b: Building{id:279}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 13.183437187191997, level: 20}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:279}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 8.356524845284849, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:279}) CREATE (g)-[r:Demand{max_demand: 32.0, current_input: 13.858117662353793, level: 20}]->(b);
CREATE (n: Building {id: 280, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:280}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 60.98723060049576, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:280}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 32.298119117647055, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:280}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.940380882352941, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:280}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 77.61311817538375,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:280}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 38.80655908769187,level: 3}]->(g);
CREATE (n: Building {id: 281, name:"building_furniture_manufacturieslevel", level:9});
MATCH (g: Goods{code: 9}), (b: Building{id:281}) CREATE (g)-[r:Demand{max_demand: 90.00000000000001, current_input: 60.98723060049578, level: 9}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:281}) CREATE (g)-[r:Demand{max_demand: 90.00000000000001, current_input: 55.31347968899409, level: 9}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:281}) CREATE (g)-[r:Demand{max_demand: 180.00000000000003, current_input: 60.3570047467845, level: 9}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:281}) CREATE (g)-[r:Demand{max_demand: 135.00000000000003, current_input: 142.49216724045138, level: 9}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:281}) CREATE (b)-[r:Supply{max_supply: 403.58250000000004, current_output: 265.10797734588226,level: 9}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:281}) CREATE (b)-[r:Supply{max_supply: 358.74, current_output: 235.651535418562,level: 9}]->(g);
CREATE (n: Building {id: 282, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:282}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 31.664926053433636, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:282}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 240.00000000000003,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:282}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 60.00000000000001,level: 6}]->(g);
CREATE (n: Building {id: 283, name:"building_wheat_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:283}) CREATE (g)-[r:Demand{max_demand: 23.449, current_input: 16.264905787439716, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:283}) CREATE (b)-[r:Supply{max_supply: 164.143, current_output: 113.854340512078,level: 5}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:283}) CREATE (b)-[r:Supply{max_supply: 37.518394736842104, current_output: 26.023845609224736,level: 5}]->(g);
CREATE (n: Building {id: 284, name:"building_barrackslevel", level:30});
MATCH (g: Goods{code: 0}), (b: Building{id:284}) CREATE (g)-[r:Demand{max_demand: 48.0, current_input: 19.775155780787994, level: 30}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:284}) CREATE (g)-[r:Demand{max_demand: 48.0, current_input: 12.534787267927273, level: 30}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:284}) CREATE (g)-[r:Demand{max_demand: 48.0, current_input: 20.78717649353069, level: 30}]->(b);
CREATE (n: Building {id: 285, name:"building_government_administrationlevel", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:285}) CREATE (g)-[r:Demand{max_demand: 140.0, current_input: 78.4861501450855, level: 7}]->(b);
CREATE (n: Building {id: 286, name:"building_shipyardslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:286}) CREATE (g)-[r:Demand{max_demand: 80.99100000000001, current_input: 54.88240881738615, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:286}) CREATE (g)-[r:Demand{max_demand: 161.98200000000003, current_input: 99.55320074425158, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:286}) CREATE (g)-[r:Demand{max_demand: 80.99100000000001, current_input: 27.157634285815686, level: 5}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:286}) CREATE (g)-[r:Demand{max_demand: 40.49550000000001, current_input: 23.373538850422854, level: 5}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:286}) CREATE (b)-[r:Supply{max_supply: 60.5306403508772, current_output: 33.36351071794787,level: 5}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:286}) CREATE (b)-[r:Supply{max_supply: 221.94571052631582, current_output: 122.33288874896203,level: 5}]->(g);
CREATE (n: Building {id: 287, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:287}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 26.387438377861365, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:287}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 200.00000000000003,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:287}) CREATE (b)-[r:Supply{max_supply: 50.00000000000001, current_output: 50.00000000000001,level: 5}]->(g);
CREATE (n: Building {id: 288, name:"building_arms_industrylevel", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:288}) CREATE (g)-[r:Demand{max_demand: 19.849594594594592, current_input: 18.480075491268007, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:288}) CREATE (g)-[r:Demand{max_demand: 19.849594594594592, current_input: 6.655900417598297, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:288}) CREATE (g)-[r:Demand{max_demand: 29.774396396396394, current_input: 17.936394462001388, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:288}) CREATE (b)-[r:Supply{max_supply: 19.780126126126124, current_output: 12.321250821834328,level: 2}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:288}) CREATE (b)-[r:Supply{max_supply: 49.450315315315315, current_output: 30.803127054585822,level: 2}]->(g);
CREATE (n: Building {id: 289, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:289}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 24.857680206563195, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:289}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 150.0,level: 3}]->(g);
CREATE (n: Building {id: 290, name:"building_naval_baselevel", level:11});
MATCH (g: Goods{code: 5}), (b: Building{id:290}) CREATE (g)-[r:Demand{max_demand: 22.0, current_input: 8.833840399955943, level: 11}]->(b);
CREATE (n: Building {id: 291, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:291}) CREATE (g)-[r:Demand{max_demand: 18.76919469026549, current_input: 13.01885723669589, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:291}) CREATE (b)-[r:Supply{max_supply: 131.38439823008852, current_output: 91.13202521010922,level: 4}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:291}) CREATE (b)-[r:Supply{max_supply: 30.030716814159298, current_output: 20.83017526169912,level: 4}]->(g);
CREATE (n: Building {id: 292, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:292}) CREATE (g)-[r:Demand{max_demand: 46.270500000000006, current_input: 73.95619348336483, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:292}) CREATE (g)-[r:Demand{max_demand: 46.270500000000006, current_input: 48.838398698513366, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:292}) CREATE (b)-[r:Supply{max_supply: 185.08200000000002, current_output: 185.08200000000002,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:292}) CREATE (b)-[r:Supply{max_supply: 23.13524561403509, current_output: 23.13524561403509,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:292}) CREATE (b)-[r:Supply{max_supply: 115.67624561403511, current_output: 115.67624561403511,level: 5}]->(g);
CREATE (n: Building {id: 293, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:293}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 24.857680206563195, level: 3}]->(b);
CREATE (n: Building {id: 294, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:294}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 44.849228654334574, level: 4}]->(b);
CREATE (n: Building {id: 295, name:"building_tooling_workshopslevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:295}) CREATE (g)-[r:Demand{max_demand: 210.0, current_input: 129.0647859409862, level: 7}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:295}) CREATE (g)-[r:Demand{max_demand: 140.00000000000003, current_input: 84.33740154625312, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:295}) CREATE (b)-[r:Supply{max_supply: 560.0000000000001, current_output: 340.76118434715454,level: 7}]->(g);
CREATE (n: Building {id: 296, name:"building_furniture_manufacturieslevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:296}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 40.65815373366385, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:296}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 36.87565312599607, level: 6}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:296}) CREATE (g)-[r:Demand{max_demand: 120.00000000000001, current_input: 40.23800316452299, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:296}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 94.9947781603009, level: 6}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:296}) CREATE (b)-[r:Supply{max_supply: 269.055, current_output: 176.7386515639215,level: 6}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:296}) CREATE (b)-[r:Supply{max_supply: 239.16000000000003, current_output: 157.1010236123747,level: 6}]->(g);
CREATE (n: Building {id: 297, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:297}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 21.10995070228909, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:297}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 160.00000000000003,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:297}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 40.00000000000001,level: 4}]->(g);
CREATE (n: Building {id: 298, name:"building_iron_minelevel", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:298}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 18.826625413205807, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:298}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 79.16231513358407, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:298}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 187.6532508264116,level: 5}]->(g);
CREATE (n: Building {id: 299, name:"building_wheat_farmlevel", level:12});
MATCH (g: Goods{code: 32}), (b: Building{id:299}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 41.6177383788811, level: 12}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:299}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 12.665970421373453, level: 12}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:299}) CREATE (b)-[r:Supply{max_supply: 420.0, current_output: 355.66208432608386,level: 12}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:299}) CREATE (b)-[r:Supply{max_supply: 96.0, current_output: 81.29419070310487,level: 12}]->(g);
CREATE (n: Building {id: 300, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:300}) CREATE (g)-[r:Demand{max_demand: 24.0, current_input: 9.887577890393997, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:300}) CREATE (g)-[r:Demand{max_demand: 24.0, current_input: 6.2673936339636365, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:300}) CREATE (g)-[r:Demand{max_demand: 24.0, current_input: 10.393588246765345, level: 15}]->(b);
CREATE (n: Building {id: 301, name:"building_food_industrylevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:301}) CREATE (g)-[r:Demand{max_demand: 240.00000000000003, current_input: 383.60265041457427, level: 6}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:301}) CREATE (g)-[r:Demand{max_demand: 240.00000000000003, current_input: 70.99970315698066, level: 6}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:301}) CREATE (b)-[r:Supply{max_supply: 210.00000000000003, current_output: 136.06237013117905,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:301}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 233.24977736773548,level: 6}]->(g);
CREATE (n: Building {id: 302, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:302}) CREATE (g)-[r:Demand{max_demand: 24.0, current_input: 9.887577890393997, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:302}) CREATE (g)-[r:Demand{max_demand: 24.0, current_input: 6.2673936339636365, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:302}) CREATE (g)-[r:Demand{max_demand: 24.0, current_input: 10.393588246765345, level: 15}]->(b);
CREATE (n: Building {id: 303, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:303}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 26.387438377861365, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:303}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 200.00000000000003,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:303}) CREATE (b)-[r:Supply{max_supply: 50.00000000000001, current_output: 50.00000000000001,level: 5}]->(g);
CREATE (n: Building {id: 304, name:"building_wheat_farmlevel", level:10});
MATCH (g: Goods{code: 32}), (b: Building{id:304}) CREATE (g)-[r:Demand{max_demand: 46.537496124031, current_input: 32.279755641635255, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:304}) CREATE (b)-[r:Supply{max_supply: 325.762496124031, current_output: 225.95830562235315,level: 10}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:304}) CREATE (b)-[r:Supply{max_supply: 74.46, current_output: 51.64761332819144,level: 10}]->(g);
CREATE (n: Building {id: 305, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:305}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 67.27384298150186, level: 6}]->(b);
CREATE (n: Building {id: 306, name:"building_furniture_manufacturieslevel", level:9});
MATCH (g: Goods{code: 9}), (b: Building{id:306}) CREATE (g)-[r:Demand{max_demand: 90.00000000000001, current_input: 60.98723060049578, level: 9}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:306}) CREATE (g)-[r:Demand{max_demand: 90.00000000000001, current_input: 55.31347968899409, level: 9}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:306}) CREATE (g)-[r:Demand{max_demand: 180.00000000000003, current_input: 60.3570047467845, level: 9}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:306}) CREATE (g)-[r:Demand{max_demand: 135.00000000000003, current_input: 142.49216724045138, level: 9}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:306}) CREATE (b)-[r:Supply{max_supply: 403.58250000000004, current_output: 265.10797734588226,level: 9}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:306}) CREATE (b)-[r:Supply{max_supply: 358.74, current_output: 235.651535418562,level: 9}]->(g);
CREATE (n: Building {id: 307, name:"building_iron_minelevel", level:8});
MATCH (g: Goods{code: 23}), (b: Building{id:307}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 30.122600661129294, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:307}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 126.65970421373453, level: 8}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:307}) CREATE (b)-[r:Supply{max_supply: 480.0, current_output: 300.2452013222586,level: 8}]->(g);
CREATE (n: Building {id: 308, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:308}) CREATE (g)-[r:Demand{max_demand: 23.85264, current_input: 9.826868162146978, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:308}) CREATE (g)-[r:Demand{max_demand: 23.85264, current_input: 6.2289118370511, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:308}) CREATE (g)-[r:Demand{max_demand: 23.85264, current_input: 10.329771614930205, level: 15}]->(b);
CREATE (n: Building {id: 309, name:"building_tooling_workshopslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:309}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 110.62695937798819, level: 6}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:309}) CREATE (g)-[r:Demand{max_demand: 120.00000000000001, current_input: 72.28920132535981, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:309}) CREATE (b)-[r:Supply{max_supply: 480.00000000000006, current_output: 292.08101515470395,level: 6}]->(g);
CREATE (n: Building {id: 310, name:"building_lead_minelevel", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:310}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 12.551083608803873, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:310}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 52.774876755722715, level: 5}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:310}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 125.10216721760774,level: 5}]->(g);
CREATE (n: Building {id: 311, name:"building_sulfur_minelevel", level:6});
MATCH (g: Goods{code: 23}), (b: Building{id:311}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 22.59195049584697, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:311}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 94.9947781603009, level: 6}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:311}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 225.18390099169395,level: 6}]->(g);
CREATE (n: Building {id: 312, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:312}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 31.664926053433636, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:312}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 240.00000000000003,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:312}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 60.00000000000001,level: 6}]->(g);
CREATE (n: Building {id: 313, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:313}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.5102167217607745, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:313}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.771885481207258, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:313}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 20.705255507420084,level: 1}]->(g);
CREATE (n: Building {id: 314, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 0}), (b: Building{id:314}) CREATE (g)-[r:Demand{max_demand: 31.8528, current_input: 13.122793376130913, level: 20}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:314}) CREATE (g)-[r:Demand{max_demand: 31.8528, current_input: 8.318084830996538, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:314}) CREATE (g)-[r:Demand{max_demand: 31.8528, current_input: 13.794370321106964, level: 20}]->(b);
CREATE (n: Building {id: 315, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:315}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 44.849228654334574, level: 4}]->(b);
CREATE (n: Building {id: 316, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:316}) CREATE (g)-[r:Demand{max_demand: 160.00000000000003, current_input: 255.7351002763829, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:316}) CREATE (g)-[r:Demand{max_demand: 160.00000000000003, current_input: 47.33313543798711, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:316}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 90.70824675411934,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:316}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 155.4998515784903,level: 4}]->(g);
CREATE (n: Building {id: 317, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:317}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.832463026716816, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:317}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:317}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 3}]->(g);
CREATE (n: Building {id: 318, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:318}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 16.571786804375463, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:318}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 100.0,level: 2}]->(g);
CREATE (n: Building {id: 319, name:"building_wheat_farmlevel", level:10});
MATCH (g: Goods{code: 32}), (b: Building{id:319}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 34.68144864906758, level: 10}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:319}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.554975351144543, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:319}) CREATE (b)-[r:Supply{max_supply: 350.0, current_output: 296.38507027173654,level: 10}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:319}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 67.74515891925407,level: 10}]->(g);
CREATE (n: Building {id: 320, name:"building_naval_baselevel", level:25});
MATCH (g: Goods{code: 5}), (b: Building{id:320}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 20.076909999899872, level: 25}]->(b);
CREATE (n: Building {id: 321, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:321}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 24.857680206563195, level: 3}]->(b);
CREATE (n: Building {id: 322, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:322}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 44.849228654334574, level: 4}]->(b);
CREATE (n: Building {id: 323, name:"building_glassworkslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:323}) CREATE (g)-[r:Demand{max_demand: 80.00000000000001, current_input: 49.16753750132809, level: 4}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:323}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 19.609232848248297, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:323}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 88.38600319782468,level: 4}]->(g);
CREATE (n: Building {id: 324, name:"building_wheat_farmlevel", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:324}) CREATE (g)-[r:Demand{max_demand: 37.5103937007874, current_input: 26.018295858803327, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:324}) CREATE (b)-[r:Supply{max_supply: 262.5727952755905, current_output: 182.12809831985058,level: 8}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:324}) CREATE (b)-[r:Supply{max_supply: 60.01663779527559, current_output: 41.62927883573079,level: 8}]->(g);
CREATE (n: Building {id: 325, name:"building_livestock_ranchlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:325}) CREATE (g)-[r:Demand{max_demand: 37.01959349593496, current_input: 59.17005909296161, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:325}) CREATE (g)-[r:Demand{max_demand: 37.01959349593496, current_input: 39.07408968589844, level: 4}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:325}) CREATE (b)-[r:Supply{max_supply: 148.07839837398373, current_output: 148.07839837398373,level: 4}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:325}) CREATE (b)-[r:Supply{max_supply: 18.50979674796748, current_output: 18.50979674796748,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:325}) CREATE (b)-[r:Supply{max_supply: 92.54899999999999, current_output: 92.54899999999999,level: 4}]->(g);
CREATE (n: Building {id: 326, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:326}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 3.295859296797999, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:326}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 2.089131211321212, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:326}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 3.4645294155884483, level: 5}]->(b);
CREATE (n: Building {id: 16777777, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16777777}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 20.32907686683192, level: 1}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:16777777}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.766039705882353, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:16777777}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.313460294117647, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:16777777}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 25.871039391794582,level: 1}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:16777777}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 12.935519695897291,level: 1}]->(g);
CREATE (n: Building {id: 134218308, name:"building_railwaylevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:134218308}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.1459421876660105, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:134218308}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.020433443521549, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:134218308}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.543770962414516, level: 2}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:134218308}) CREATE (b)-[r:Supply{max_supply: 130.0, current_output: 62.52152569274753,level: 2}]->(g);
CREATE (n: Building {id: 622, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:622}) CREATE (g)-[r:Demand{max_demand: 8.225999999999999, current_input: 8.682522723851502, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:622}) CREATE (b)-[r:Supply{max_supply: 24.678, current_output: 24.678,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:622}) CREATE (b)-[r:Supply{max_supply: 20.564999999999998, current_output: 20.564999999999998,level: 1}]->(g);
CREATE (n: Building {id: 623, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:623}) CREATE (g)-[r:Demand{max_demand: 3.636, current_input: 6.025501682070918, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:623}) CREATE (b)-[r:Supply{max_supply: 19.6344, current_output: 19.6344,level: 1}]->(g);
CREATE (n: Building {id: 624, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:624}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0599120604987493, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:624}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.3057070070757577, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:624}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.1653308847427803, level: 5}]->(b);
CREATE (n: Building {id: 625, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:625}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.285893402187732, level: 1}]->(b);
CREATE (n: Building {id: 16778133, name:"building_tooling_workshopslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:16778133}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 92.18913281499015, level: 5}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16778133}) CREATE (g)-[r:Demand{max_demand: 100.00000000000001, current_input: 60.241001104466505, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16778133}) CREATE (b)-[r:Supply{max_supply: 400.00000000000006, current_output: 243.4008459622532,level: 5}]->(g);
CREATE (n: Building {id: 1182, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1182}) CREATE (g)-[r:Demand{max_demand: 3.63, current_input: 6.015558609988293, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1182}) CREATE (b)-[r:Supply{max_supply: 19.602, current_output: 19.602,level: 1}]->(g);
CREATE (n: Building {id: 1183, name:"building_lead_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1183}) CREATE (g)-[r:Demand{max_demand: 1.27, current_input: 1.340481869595357, level: 2}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1183}) CREATE (b)-[r:Supply{max_supply: 5.08, current_output: 5.08,level: 2}]->(g);
CREATE (n: Building {id: 1184, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1184}) CREATE (g)-[r:Demand{max_demand: 9.375896825396826, current_input: 6.503393685779116, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1184}) CREATE (b)-[r:Supply{max_supply: 65.63129365079365, current_output: 45.523766810437515,level: 2}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1184}) CREATE (b)-[r:Supply{max_supply: 15.001436507936509, current_output: 10.405430998244956,level: 2}]->(g);
CREATE (n: Building {id: 1185, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1185}) CREATE (g)-[r:Demand{max_demand: 8.254895999999999, current_input: 13.194166602069446, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1185}) CREATE (g)-[r:Demand{max_demand: 8.254895999999999, current_input: 8.713022380626168, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1185}) CREATE (b)-[r:Supply{max_supply: 33.019600000000004, current_output: 33.019600000000004,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1185}) CREATE (b)-[r:Supply{max_supply: 4.127447999999999, current_output: 4.127447999999999,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1185}) CREATE (b)-[r:Supply{max_supply: 20.637248, current_output: 20.637248,level: 1}]->(g);
CREATE (n: Building {id: 1186, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:1186}) CREATE (g)-[r:Demand{max_demand: 15.9968, current_input: 6.590400249877279, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1186}) CREATE (g)-[r:Demand{max_demand: 15.9968, current_input: 4.177426770157895, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1186}) CREATE (g)-[r:Demand{max_demand: 15.9968, current_input: 6.927673019410661, level: 10}]->(b);
CREATE (n: Building {id: 1187, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1187}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.285893402187732, level: 1}]->(b);
CREATE (n: Building {id: 1222, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1222}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.285893402187732, level: 1}]->(b);
CREATE (n: Building {id: 1223, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1223}) CREATE (b)-[r:Supply{max_supply: 59.05859405940594, current_output: 59.64918,level: 2}]->(g);
CREATE (n: Building {id: 1224, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1224}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.285893402187732, level: 1}]->(b);
CREATE (n: Building {id: 16778858, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:16778858}) CREATE (g)-[r:Demand{max_demand: 4.932, current_input: 3.3421002369071684, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16778858}) CREATE (g)-[r:Demand{max_demand: 9.864, current_input: 6.062357373913753, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16778858}) CREATE (g)-[r:Demand{max_demand: 12.33, current_input: 11.479293933256693, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16778858}) CREATE (g)-[r:Demand{max_demand: 2.466, current_input: 2.602856921592245, level: 3}]->(b);
CREATE (n: Building {id: 33556105, name:"building_munition_plantslevel", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:33556105}) CREATE (g)-[r:Demand{max_demand: 19.407799999999998, current_input: 9.514301731805828, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:33556105}) CREATE (g)-[r:Demand{max_demand: 19.407799999999998, current_input: 24.690596229831314, level: 1}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:33556105}) CREATE (b)-[r:Supply{max_supply: 48.5195, current_output: 36.152627164757284,level: 1}]->(g);
CREATE (n: Building {id: 1682, name:"building_sugar_plantationlevel", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:1682}) CREATE (b)-[r:Supply{max_supply: 79.58789215686274, current_output: 81.17965,level: 3}]->(g);
CREATE (n: Building {id: 1683, name:"building_coffee_plantationlevel", level:2});
MATCH (g: Goods{code: 41}), (b: Building{id:1683}) CREATE (b)-[r:Supply{max_supply: 33.34479207920792, current_output: 33.67824,level: 2}]->(g);
CREATE (n: Building {id: 1684, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1684}) CREATE (b)-[r:Supply{max_supply: 44.215495049504945, current_output: 44.65765,level: 2}]->(g);
CREATE (n: Building {id: 1685, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1685}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.285893402187732, level: 1}]->(b);
CREATE (n: Building {id: 1712, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1712}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.285893402187732, level: 1}]->(b);
CREATE (n: Building {id: 218105762, name:"building_iron_minelevel", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:218105762}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 15.061300330564645, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:218105762}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 63.329852106867264, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:218105762}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 150.12260066112927,level: 4}]->(g);
CREATE (n: Building {id: 2262, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:2262}) CREATE (b)-[r:Supply{max_supply: 59.05859405940594, current_output: 59.64918,level: 2}]->(g);
CREATE (n: Building {id: 2263, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2263}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.285893402187732, level: 1}]->(b);
CREATE (n: Building {id: 2287, name:"building_dye_plantationlevel", level:2});
MATCH (g: Goods{code: 21}), (b: Building{id:2287}) CREATE (b)-[r:Supply{max_supply: 44.215495049504945, current_output: 44.65765,level: 2}]->(g);
CREATE (n: Building {id: 2288, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2288}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.285893402187732, level: 1}]->(b);
CREATE (n: Building {id: 2809, name:"building_subsistence_farmslevel", level:73});
MATCH (g: Goods{code: 7}), (b: Building{id:2809}) CREATE (b)-[r:Supply{max_supply: 64.61777272727272, current_output: 71.07955,level: 73}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2809}) CREATE (b)-[r:Supply{max_supply: 12.923554545454543, current_output: 14.21591,level: 73}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2809}) CREATE (b)-[r:Supply{max_supply: 12.923554545454543, current_output: 14.21591,level: 73}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2809}) CREATE (b)-[r:Supply{max_supply: 12.923554545454543, current_output: 14.21591,level: 73}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2809}) CREATE (b)-[r:Supply{max_supply: 12.923554545454543, current_output: 14.21591,level: 73}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2809}) CREATE (b)-[r:Supply{max_supply: 12.923554545454543, current_output: 14.21591,level: 73}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2809}) CREATE (b)-[r:Supply{max_supply: 18.092972727272727, current_output: 19.90227,level: 73}]->(g);
CREATE (n: Building {id: 2810, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2810}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.437826562998033, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2810}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 15.061300330564647, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2810}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 12.868889728499811, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2810}) CREATE (b)-[r:Supply{max_supply: 420.0, current_output: 181.24104346497413,level: 6}]->(g);
CREATE (n: Building {id: 2811, name:"building_subsistence_farmslevel", level:140});
MATCH (g: Goods{code: 7}), (b: Building{id:2811}) CREATE (b)-[r:Supply{max_supply: 188.076, current_output: 206.8836,level: 140}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2811}) CREATE (b)-[r:Supply{max_supply: 37.615199999999994, current_output: 41.37672,level: 140}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2811}) CREATE (b)-[r:Supply{max_supply: 37.615199999999994, current_output: 41.37672,level: 140}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2811}) CREATE (b)-[r:Supply{max_supply: 37.615199999999994, current_output: 41.37672,level: 140}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2811}) CREATE (b)-[r:Supply{max_supply: 37.615199999999994, current_output: 41.37672,level: 140}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2811}) CREATE (b)-[r:Supply{max_supply: 37.615199999999994, current_output: 41.37672,level: 140}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2811}) CREATE (b)-[r:Supply{max_supply: 52.661272727272724, current_output: 57.9274,level: 140}]->(g);
CREATE (n: Building {id: 2812, name:"building_urban_centerlevel", level:11});
MATCH (g: Goods{code: 10}), (b: Building{id:2812}) CREATE (g)-[r:Demand{max_demand: 54.99999999999999, current_input: 33.802682032163055, level: 11}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2812}) CREATE (g)-[r:Demand{max_demand: 109.99999999999999, current_input: 27.612383939368513, level: 11}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2812}) CREATE (g)-[r:Demand{max_demand: 54.99999999999999, current_input: 23.59296450224965, level: 11}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2812}) CREATE (b)-[r:Supply{max_supply: 769.9999999999999, current_output: 332.2752463524525,level: 11}]->(g);
CREATE (n: Building {id: 16780214, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:16780214}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.87565312599606, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16780214}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 24.096400441786596, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16780214}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 97.36033838490127,level: 2}]->(g);
CREATE (n: Building {id: 3060, name:"building_subsistence_farmslevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:3060}) CREATE (b)-[r:Supply{max_supply: 2.177872727272727, current_output: 2.39566,level: 7}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3060}) CREATE (b)-[r:Supply{max_supply: 0.4355727272727272, current_output: 0.47913,level: 7}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3060}) CREATE (b)-[r:Supply{max_supply: 0.4355727272727272, current_output: 0.47913,level: 7}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3060}) CREATE (b)-[r:Supply{max_supply: 0.4355727272727272, current_output: 0.47913,level: 7}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3060}) CREATE (b)-[r:Supply{max_supply: 0.4355727272727272, current_output: 0.47913,level: 7}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3060}) CREATE (b)-[r:Supply{max_supply: 0.4355727272727272, current_output: 0.47913,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3060}) CREATE (b)-[r:Supply{max_supply: 0.6098, current_output: 0.67078,level: 7}]->(g);
CREATE (n: Building {id: 3143, name:"building_subsistence_farmslevel", level:69});
MATCH (g: Goods{code: 7}), (b: Building{id:3143}) CREATE (b)-[r:Supply{max_supply: 169.859024, current_output: 212.32378,level: 69}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3143}) CREATE (b)-[r:Supply{max_supply: 33.9718, current_output: 42.46475,level: 69}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3143}) CREATE (b)-[r:Supply{max_supply: 33.9718, current_output: 42.46475,level: 69}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3143}) CREATE (b)-[r:Supply{max_supply: 33.9718, current_output: 42.46475,level: 69}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3143}) CREATE (b)-[r:Supply{max_supply: 33.9718, current_output: 42.46475,level: 69}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3143}) CREATE (b)-[r:Supply{max_supply: 33.9718, current_output: 42.46475,level: 69}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3143}) CREATE (b)-[r:Supply{max_supply: 47.560520000000004, current_output: 59.45065,level: 69}]->(g);
CREATE (n: Building {id: 3168, name:"building_subsistence_farmslevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 18.121399999999998, current_output: 19.93354,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 3.624272727272727, current_output: 3.9867,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 3.624272727272727, current_output: 3.9867,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 3.624272727272727, current_output: 3.9867,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 3.624272727272727, current_output: 3.9867,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 3.624272727272727, current_output: 3.9867,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 5.0739909090909086, current_output: 5.58139,level: 8}]->(g);
CREATE (n: Building {id: 3199, name:"building_subsistence_farmslevel", level:89});
MATCH (g: Goods{code: 7}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 135.85181818181817, current_output: 149.437,level: 89}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 27.170363636363632, current_output: 29.8874,level: 89}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 27.170363636363632, current_output: 29.8874,level: 89}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 27.170363636363632, current_output: 29.8874,level: 89}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 27.170363636363632, current_output: 29.8874,level: 89}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 27.170363636363632, current_output: 29.8874,level: 89}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3199}) CREATE (b)-[r:Supply{max_supply: 38.03850909090909, current_output: 41.84236,level: 89}]->(g);
CREATE (n: Building {id: 3200, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3200}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 15.364855469165025, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3200}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 12.551083608803873, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3200}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 10.724074773749843, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3200}) CREATE (b)-[r:Supply{max_supply: 350.0, current_output: 151.03420288747844,level: 5}]->(g);
CREATE (n: Building {id: 3201, name:"building_subsistence_farmslevel", level:111});
MATCH (g: Goods{code: 7}), (b: Building{id:3201}) CREATE (b)-[r:Supply{max_supply: 134.23507272727272, current_output: 147.65858,level: 111}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3201}) CREATE (b)-[r:Supply{max_supply: 26.84700909090909, current_output: 29.53171,level: 111}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3201}) CREATE (b)-[r:Supply{max_supply: 26.84700909090909, current_output: 29.53171,level: 111}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3201}) CREATE (b)-[r:Supply{max_supply: 26.84700909090909, current_output: 29.53171,level: 111}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3201}) CREATE (b)-[r:Supply{max_supply: 26.84700909090909, current_output: 29.53171,level: 111}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3201}) CREATE (b)-[r:Supply{max_supply: 26.84700909090909, current_output: 29.53171,level: 111}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3201}) CREATE (b)-[r:Supply{max_supply: 37.585818181818176, current_output: 41.3444,level: 111}]->(g);
CREATE (n: Building {id: 3202, name:"building_urban_centerlevel", level:9});
MATCH (g: Goods{code: 10}), (b: Building{id:3202}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 27.656739844497046, level: 9}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3202}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 22.59195049584697, level: 9}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3202}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 19.303334592749714, level: 9}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3202}) CREATE (b)-[r:Supply{max_supply: 629.9999999999999, current_output: 271.8615651974611,level: 9}]->(g);
CREATE (n: Building {id: 3203, name:"building_subsistence_farmslevel", level:102});
MATCH (g: Goods{code: 7}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 83.61959999999999, current_output: 91.98156,level: 102}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 16.72391818181818, current_output: 18.39631,level: 102}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 16.72391818181818, current_output: 18.39631,level: 102}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 16.72391818181818, current_output: 18.39631,level: 102}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 16.72391818181818, current_output: 18.39631,level: 102}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 16.72391818181818, current_output: 18.39631,level: 102}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 23.413481818181815, current_output: 25.75483,level: 102}]->(g);
CREATE (n: Building {id: 3204, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3204}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.437826562998033, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3204}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 15.061300330564647, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3204}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 12.868889728499811, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3204}) CREATE (b)-[r:Supply{max_supply: 420.0, current_output: 181.24104346497413,level: 6}]->(g);
CREATE (n: Building {id: 3205, name:"building_subsistence_farmslevel", level:151});
MATCH (g: Goods{code: 7}), (b: Building{id:3205}) CREATE (b)-[r:Supply{max_supply: 250.14660000000003, current_output: 300.17592,level: 151}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3205}) CREATE (b)-[r:Supply{max_supply: 50.029316666666666, current_output: 60.03518,level: 151}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3205}) CREATE (b)-[r:Supply{max_supply: 50.029316666666666, current_output: 60.03518,level: 151}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3205}) CREATE (b)-[r:Supply{max_supply: 50.029316666666666, current_output: 60.03518,level: 151}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3205}) CREATE (b)-[r:Supply{max_supply: 50.029316666666666, current_output: 60.03518,level: 151}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3205}) CREATE (b)-[r:Supply{max_supply: 50.029316666666666, current_output: 60.03518,level: 151}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3205}) CREATE (b)-[r:Supply{max_supply: 70.04104166666667, current_output: 84.04925,level: 151}]->(g);
CREATE (n: Building {id: 3206, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3206}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 15.364855469165025, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3206}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 12.551083608803873, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3206}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 10.724074773749843, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3206}) CREATE (b)-[r:Supply{max_supply: 350.0, current_output: 151.03420288747844,level: 5}]->(g);
CREATE (n: Building {id: 3207, name:"building_subsistence_farmslevel", level:53});
MATCH (g: Goods{code: 7}), (b: Building{id:3207}) CREATE (b)-[r:Supply{max_supply: 86.19521818181818, current_output: 94.81474,level: 53}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3207}) CREATE (b)-[r:Supply{max_supply: 17.239036363636362, current_output: 18.96294,level: 53}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3207}) CREATE (b)-[r:Supply{max_supply: 17.239036363636362, current_output: 18.96294,level: 53}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3207}) CREATE (b)-[r:Supply{max_supply: 17.239036363636362, current_output: 18.96294,level: 53}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3207}) CREATE (b)-[r:Supply{max_supply: 17.239036363636362, current_output: 18.96294,level: 53}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3207}) CREATE (b)-[r:Supply{max_supply: 17.239036363636362, current_output: 18.96294,level: 53}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3207}) CREATE (b)-[r:Supply{max_supply: 24.134654545454545, current_output: 26.54812,level: 53}]->(g);
CREATE (n: Building {id: 3208, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3208}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.291884375332021, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3208}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 10.040866887043098, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3208}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 8.579259818999875, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3208}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 120.82736230998272,level: 4}]->(g);
CREATE (n: Building {id: 3209, name:"building_subsistence_farmslevel", level:92});
MATCH (g: Goods{code: 7}), (b: Building{id:3209}) CREATE (b)-[r:Supply{max_supply: 126.20789999999998, current_output: 138.82869,level: 92}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3209}) CREATE (b)-[r:Supply{max_supply: 25.241572727272725, current_output: 27.76573,level: 92}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3209}) CREATE (b)-[r:Supply{max_supply: 25.241572727272725, current_output: 27.76573,level: 92}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3209}) CREATE (b)-[r:Supply{max_supply: 25.241572727272725, current_output: 27.76573,level: 92}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3209}) CREATE (b)-[r:Supply{max_supply: 25.241572727272725, current_output: 27.76573,level: 92}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3209}) CREATE (b)-[r:Supply{max_supply: 25.241572727272725, current_output: 27.76573,level: 92}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3209}) CREATE (b)-[r:Supply{max_supply: 35.33820909090909, current_output: 38.87203,level: 92}]->(g);
CREATE (n: Building {id: 3210, name:"building_subsistence_farmslevel", level:130});
MATCH (g: Goods{code: 7}), (b: Building{id:3210}) CREATE (b)-[r:Supply{max_supply: 163.55949999999999, current_output: 179.91545,level: 130}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3210}) CREATE (b)-[r:Supply{max_supply: 32.71189999999999, current_output: 35.98309,level: 130}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3210}) CREATE (b)-[r:Supply{max_supply: 32.71189999999999, current_output: 35.98309,level: 130}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3210}) CREATE (b)-[r:Supply{max_supply: 32.71189999999999, current_output: 35.98309,level: 130}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3210}) CREATE (b)-[r:Supply{max_supply: 32.71189999999999, current_output: 35.98309,level: 130}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3210}) CREATE (b)-[r:Supply{max_supply: 32.71189999999999, current_output: 35.98309,level: 130}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3210}) CREATE (b)-[r:Supply{max_supply: 45.796654545454544, current_output: 50.37632,level: 130}]->(g);
CREATE (n: Building {id: 3211, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3211}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.218913281499017, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3211}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.5306501652823234, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3211}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 6.4344448642499055, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3211}) CREATE (b)-[r:Supply{max_supply: 209.99999999999997, current_output: 90.62052173248705,level: 3}]->(g);
CREATE (n: Building {id: 3212, name:"building_subsistence_farmslevel", level:111});
MATCH (g: Goods{code: 7}), (b: Building{id:3212}) CREATE (b)-[r:Supply{max_supply: 125.83514545454544, current_output: 138.41866,level: 111}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3212}) CREATE (b)-[r:Supply{max_supply: 25.16702727272727, current_output: 27.68373,level: 111}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3212}) CREATE (b)-[r:Supply{max_supply: 25.16702727272727, current_output: 27.68373,level: 111}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3212}) CREATE (b)-[r:Supply{max_supply: 25.16702727272727, current_output: 27.68373,level: 111}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3212}) CREATE (b)-[r:Supply{max_supply: 25.16702727272727, current_output: 27.68373,level: 111}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3212}) CREATE (b)-[r:Supply{max_supply: 25.16702727272727, current_output: 27.68373,level: 111}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3212}) CREATE (b)-[r:Supply{max_supply: 35.23383636363636, current_output: 38.75722,level: 111}]->(g);
CREATE (n: Building {id: 3213, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3213}) CREATE (g)-[r:Demand{max_demand: 24.98525, current_input: 15.355790204438218, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3213}) CREATE (g)-[r:Demand{max_demand: 49.9705, current_input: 12.54367846947468, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3213}) CREATE (g)-[r:Demand{max_demand: 24.98525, current_input: 10.71774756963333, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3213}) CREATE (b)-[r:Supply{max_supply: 349.7935, current_output: 150.94509270777482,level: 5}]->(g);
CREATE (n: Building {id: 3214, name:"building_subsistence_farmslevel", level:149});
MATCH (g: Goods{code: 7}), (b: Building{id:3214}) CREATE (b)-[r:Supply{max_supply: 267.10484545454545, current_output: 293.81533,level: 149}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3214}) CREATE (b)-[r:Supply{max_supply: 53.42096363636364, current_output: 58.76306,level: 149}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3214}) CREATE (b)-[r:Supply{max_supply: 53.42096363636364, current_output: 58.76306,level: 149}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3214}) CREATE (b)-[r:Supply{max_supply: 53.42096363636364, current_output: 58.76306,level: 149}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3214}) CREATE (b)-[r:Supply{max_supply: 53.42096363636364, current_output: 58.76306,level: 149}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3214}) CREATE (b)-[r:Supply{max_supply: 53.42096363636364, current_output: 58.76306,level: 149}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3214}) CREATE (b)-[r:Supply{max_supply: 74.78935454545453, current_output: 82.26829,level: 149}]->(g);
CREATE (n: Building {id: 3215, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3215}) CREATE (g)-[r:Demand{max_demand: 29.997895238095236, current_input: 18.436532988499483, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3215}) CREATE (g)-[r:Demand{max_demand: 59.995799999999996, current_input: 15.060246039541505, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3215}) CREATE (g)-[r:Demand{max_demand: 29.997895238095236, current_input: 12.867986863537906, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3215}) CREATE (b)-[r:Supply{max_supply: 419.9706, current_output: 181.22835659193157,level: 6}]->(g);
CREATE (n: Building {id: 3216, name:"building_subsistence_farmslevel", level:142});
MATCH (g: Goods{code: 7}), (b: Building{id:3216}) CREATE (b)-[r:Supply{max_supply: 222.21934545454545, current_output: 244.44128,level: 142}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3216}) CREATE (b)-[r:Supply{max_supply: 44.44386363636363, current_output: 48.88825,level: 142}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3216}) CREATE (b)-[r:Supply{max_supply: 44.44386363636363, current_output: 48.88825,level: 142}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3216}) CREATE (b)-[r:Supply{max_supply: 44.44386363636363, current_output: 48.88825,level: 142}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3216}) CREATE (b)-[r:Supply{max_supply: 44.44386363636363, current_output: 48.88825,level: 142}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3216}) CREATE (b)-[r:Supply{max_supply: 44.44386363636363, current_output: 48.88825,level: 142}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3216}) CREATE (b)-[r:Supply{max_supply: 62.221409090909084, current_output: 68.44355,level: 142}]->(g);
CREATE (n: Building {id: 3217, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3217}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 15.364855469165025, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3217}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 12.551083608803873, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3217}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 10.724074773749843, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3217}) CREATE (b)-[r:Supply{max_supply: 350.0, current_output: 151.03420288747844,level: 5}]->(g);
CREATE (n: Building {id: 3218, name:"building_subsistence_farmslevel", level:94});
MATCH (g: Goods{code: 7}), (b: Building{id:3218}) CREATE (b)-[r:Supply{max_supply: 182.41405, current_output: 218.89686,level: 94}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3218}) CREATE (b)-[r:Supply{max_supply: 36.48280833333334, current_output: 43.77937,level: 94}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3218}) CREATE (b)-[r:Supply{max_supply: 36.48280833333334, current_output: 43.77937,level: 94}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3218}) CREATE (b)-[r:Supply{max_supply: 36.48280833333334, current_output: 43.77937,level: 94}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3218}) CREATE (b)-[r:Supply{max_supply: 36.48280833333334, current_output: 43.77937,level: 94}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3218}) CREATE (b)-[r:Supply{max_supply: 36.48280833333334, current_output: 43.77937,level: 94}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3218}) CREATE (b)-[r:Supply{max_supply: 51.07593333333333, current_output: 61.29112,level: 94}]->(g);
CREATE (n: Building {id: 3219, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3219}) CREATE (g)-[r:Demand{max_demand: 14.990843137254902, current_input: 9.21328552659384, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3219}) CREATE (g)-[r:Demand{max_demand: 29.981696078431373, current_input: 7.526055484282787, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3219}) CREATE (g)-[r:Demand{max_demand: 14.990843137254902, current_input: 6.43051690901905, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3219}) CREATE (b)-[r:Supply{max_supply: 209.87189215686274, current_output: 90.5652398297103,level: 3}]->(g);
CREATE (n: Building {id: 3220, name:"building_subsistence_farmslevel", level:124});
MATCH (g: Goods{code: 7}), (b: Building{id:3220}) CREATE (b)-[r:Supply{max_supply: 215.00359999999998, current_output: 236.50396,level: 124}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3220}) CREATE (b)-[r:Supply{max_supply: 43.00071818181818, current_output: 47.30079,level: 124}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3220}) CREATE (b)-[r:Supply{max_supply: 43.00071818181818, current_output: 47.30079,level: 124}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3220}) CREATE (b)-[r:Supply{max_supply: 43.00071818181818, current_output: 47.30079,level: 124}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3220}) CREATE (b)-[r:Supply{max_supply: 43.00071818181818, current_output: 47.30079,level: 124}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3220}) CREATE (b)-[r:Supply{max_supply: 43.00071818181818, current_output: 47.30079,level: 124}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3220}) CREATE (b)-[r:Supply{max_supply: 60.201, current_output: 66.2211,level: 124}]->(g);
CREATE (n: Building {id: 3221, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3221}) CREATE (g)-[r:Demand{max_demand: 24.998, current_input: 15.363626280727495, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3221}) CREATE (g)-[r:Demand{max_demand: 49.996, current_input: 12.550079522115169, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3221}) CREATE (g)-[r:Demand{max_demand: 24.998, current_input: 10.723216847767944, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3221}) CREATE (b)-[r:Supply{max_supply: 349.97200000000004, current_output: 151.02212015124746,level: 5}]->(g);
CREATE (n: Building {id: 3222, name:"building_subsistence_farmslevel", level:86});
MATCH (g: Goods{code: 7}), (b: Building{id:3222}) CREATE (b)-[r:Supply{max_supply: 124.28504545454544, current_output: 136.71355,level: 86}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3222}) CREATE (b)-[r:Supply{max_supply: 24.857009090909088, current_output: 27.34271,level: 86}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3222}) CREATE (b)-[r:Supply{max_supply: 24.857009090909088, current_output: 27.34271,level: 86}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3222}) CREATE (b)-[r:Supply{max_supply: 24.857009090909088, current_output: 27.34271,level: 86}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3222}) CREATE (b)-[r:Supply{max_supply: 24.857009090909088, current_output: 27.34271,level: 86}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3222}) CREATE (b)-[r:Supply{max_supply: 24.857009090909088, current_output: 27.34271,level: 86}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3222}) CREATE (b)-[r:Supply{max_supply: 34.799809090909086, current_output: 38.27979,level: 86}]->(g);
CREATE (n: Building {id: 3223, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3223}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.291884375332021, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3223}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 10.040866887043098, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3223}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 8.579259818999875, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3223}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 120.82736230998272,level: 4}]->(g);
CREATE (n: Building {id: 3224, name:"building_subsistence_farmslevel", level:98});
MATCH (g: Goods{code: 7}), (b: Building{id:3224}) CREATE (b)-[r:Supply{max_supply: 147.2891, current_output: 162.01801,level: 98}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3224}) CREATE (b)-[r:Supply{max_supply: 29.457818181818176, current_output: 32.4036,level: 98}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3224}) CREATE (b)-[r:Supply{max_supply: 29.457818181818176, current_output: 32.4036,level: 98}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3224}) CREATE (b)-[r:Supply{max_supply: 29.457818181818176, current_output: 32.4036,level: 98}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3224}) CREATE (b)-[r:Supply{max_supply: 29.457818181818176, current_output: 32.4036,level: 98}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3224}) CREATE (b)-[r:Supply{max_supply: 29.457818181818176, current_output: 32.4036,level: 98}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3224}) CREATE (b)-[r:Supply{max_supply: 41.240945454545454, current_output: 45.36504,level: 98}]->(g);
CREATE (n: Building {id: 3225, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3225}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 21.510797656831034, level: 7}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3225}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 17.571517052325422, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3225}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 15.013704683249781, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3225}) CREATE (b)-[r:Supply{max_supply: 489.99999999999994, current_output: 211.44788404246978,level: 7}]->(g);
CREATE (n: Building {id: 3226, name:"building_subsistence_farmslevel", level:100});
MATCH (g: Goods{code: 7}), (b: Building{id:3226}) CREATE (b)-[r:Supply{max_supply: 171.10750000000002, current_output: 205.329,level: 100}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3226}) CREATE (b)-[r:Supply{max_supply: 34.221500000000006, current_output: 41.0658,level: 100}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3226}) CREATE (b)-[r:Supply{max_supply: 34.221500000000006, current_output: 41.0658,level: 100}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3226}) CREATE (b)-[r:Supply{max_supply: 34.221500000000006, current_output: 41.0658,level: 100}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3226}) CREATE (b)-[r:Supply{max_supply: 34.221500000000006, current_output: 41.0658,level: 100}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3226}) CREATE (b)-[r:Supply{max_supply: 34.221500000000006, current_output: 41.0658,level: 100}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3226}) CREATE (b)-[r:Supply{max_supply: 47.9101, current_output: 57.49212,level: 100}]->(g);
CREATE (n: Building {id: 3227, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3227}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.218913281499017, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3227}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.5306501652823234, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3227}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 6.4344448642499055, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3227}) CREATE (b)-[r:Supply{max_supply: 209.99999999999997, current_output: 90.62052173248705,level: 3}]->(g);
CREATE (n: Building {id: 3228, name:"building_subsistence_farmslevel", level:55});
MATCH (g: Goods{code: 7}), (b: Building{id:3228}) CREATE (b)-[r:Supply{max_supply: 29.869125000000004, current_output: 35.84295,level: 55}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3228}) CREATE (b)-[r:Supply{max_supply: 5.973825000000001, current_output: 7.16859,level: 55}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3228}) CREATE (b)-[r:Supply{max_supply: 5.973825000000001, current_output: 7.16859,level: 55}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3228}) CREATE (b)-[r:Supply{max_supply: 5.973825000000001, current_output: 7.16859,level: 55}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3228}) CREATE (b)-[r:Supply{max_supply: 5.973825000000001, current_output: 7.16859,level: 55}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3228}) CREATE (b)-[r:Supply{max_supply: 5.973825000000001, current_output: 7.16859,level: 55}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3228}) CREATE (b)-[r:Supply{max_supply: 8.36335, current_output: 10.03602,level: 55}]->(g);
CREATE (n: Building {id: 3229, name:"building_urban_centerlevel", level:11});
MATCH (g: Goods{code: 10}), (b: Building{id:3229}) CREATE (g)-[r:Demand{max_demand: 54.99999999999999, current_input: 33.802682032163055, level: 11}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3229}) CREATE (g)-[r:Demand{max_demand: 109.99999999999999, current_input: 27.612383939368513, level: 11}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3229}) CREATE (g)-[r:Demand{max_demand: 54.99999999999999, current_input: 23.59296450224965, level: 11}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3229}) CREATE (b)-[r:Supply{max_supply: 769.9999999999999, current_output: 332.2752463524525,level: 11}]->(g);
CREATE (n: Building {id: 3271, name:"building_subsistence_farmslevel", level:13});
MATCH (g: Goods{code: 7}), (b: Building{id:3271}) CREATE (b)-[r:Supply{max_supply: 31.377772727272724, current_output: 34.51555,level: 13}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3271}) CREATE (b)-[r:Supply{max_supply: 6.275554545454545, current_output: 6.90311,level: 13}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3271}) CREATE (b)-[r:Supply{max_supply: 6.275554545454545, current_output: 6.90311,level: 13}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3271}) CREATE (b)-[r:Supply{max_supply: 6.275554545454545, current_output: 6.90311,level: 13}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3271}) CREATE (b)-[r:Supply{max_supply: 6.275554545454545, current_output: 6.90311,level: 13}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3271}) CREATE (b)-[r:Supply{max_supply: 6.275554545454545, current_output: 6.90311,level: 13}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3271}) CREATE (b)-[r:Supply{max_supply: 8.785772727272727, current_output: 9.66435,level: 13}]->(g);
CREATE (n: Building {id: 3298, name:"building_subsistence_farmslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 17.4196, current_output: 22.64548,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 3.4839153846153845, current_output: 4.52909,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 3.4839153846153845, current_output: 4.52909,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 3.4839153846153845, current_output: 4.52909,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 3.4839153846153845, current_output: 4.52909,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 3.4839153846153845, current_output: 4.52909,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 4.877484615384615, current_output: 6.34073,level: 44}]->(g);
CREATE (n: Building {id: 3491, name:"building_subsistence_farmslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3491}) CREATE (b)-[r:Supply{max_supply: 35.248999999999995, current_output: 38.7739,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3491}) CREATE (b)-[r:Supply{max_supply: 7.049799999999999, current_output: 7.75478,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3491}) CREATE (b)-[r:Supply{max_supply: 7.049799999999999, current_output: 7.75478,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3491}) CREATE (b)-[r:Supply{max_supply: 7.049799999999999, current_output: 7.75478,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3491}) CREATE (b)-[r:Supply{max_supply: 7.049799999999999, current_output: 7.75478,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3491}) CREATE (b)-[r:Supply{max_supply: 7.049799999999999, current_output: 7.75478,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3491}) CREATE (b)-[r:Supply{max_supply: 9.86971818181818, current_output: 10.85669,level: 20}]->(g);
CREATE (n: Building {id: 3498, name:"building_subsistence_orchardslevel", level:34});
MATCH (g: Goods{code: 7}), (b: Building{id:3498}) CREATE (b)-[r:Supply{max_supply: 6.48940909090909, current_output: 7.13835,level: 34}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3498}) CREATE (b)-[r:Supply{max_supply: 3.2447, current_output: 3.56917,level: 34}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3498}) CREATE (b)-[r:Supply{max_supply: 9.73410909090909, current_output: 10.70752,level: 34}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3498}) CREATE (b)-[r:Supply{max_supply: 6.48940909090909, current_output: 7.13835,level: 34}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3498}) CREATE (b)-[r:Supply{max_supply: 6.48940909090909, current_output: 7.13835,level: 34}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3498}) CREATE (b)-[r:Supply{max_supply: 6.48940909090909, current_output: 7.13835,level: 34}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3498}) CREATE (b)-[r:Supply{max_supply: 17.26182727272727, current_output: 18.98801,level: 34}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3498}) CREATE (b)-[r:Supply{max_supply: 9.085172727272727, current_output: 9.99369,level: 34}]->(g);
CREATE (n: Building {id: 67112363, name:"building_subsistence_pastureslevel", level:11});
MATCH (g: Goods{code: 7}), (b: Building{id:67112363}) CREATE (b)-[r:Supply{max_supply: 0.9947777777777778, current_output: 0.8953,level: 11}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:67112363}) CREATE (b)-[r:Supply{max_supply: 1.4921666666666666, current_output: 1.34295,level: 11}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:67112363}) CREATE (b)-[r:Supply{max_supply: 0.4973888888888889, current_output: 0.44765,level: 11}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:67112363}) CREATE (b)-[r:Supply{max_supply: 0.9947777777777778, current_output: 0.8953,level: 11}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:67112363}) CREATE (b)-[r:Supply{max_supply: 0.9947777777777778, current_output: 0.8953,level: 11}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:67112363}) CREATE (b)-[r:Supply{max_supply: 0.9947777777777778, current_output: 0.8953,level: 11}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:67112363}) CREATE (b)-[r:Supply{max_supply: 2.646122222222222, current_output: 2.38151,level: 11}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:67112363}) CREATE (b)-[r:Supply{max_supply: 1.3926888888888889, current_output: 1.25342,level: 11}]->(g);
CREATE (n: Building {id: 3842, name:"building_subsistence_pastureslevel", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 6.748045454545454, current_output: 7.42285,level: 15}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 10.122072727272727, current_output: 11.13428,level: 15}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 3.3740181818181814, current_output: 3.71142,level: 15}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 6.748045454545454, current_output: 7.42285,level: 15}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 6.748045454545454, current_output: 7.42285,level: 15}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 6.748045454545454, current_output: 7.42285,level: 15}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 17.94980909090909, current_output: 19.74479,level: 15}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3842}) CREATE (b)-[r:Supply{max_supply: 9.447263636363635, current_output: 10.39199,level: 15}]->(g);
CREATE (n: Building {id: 3862, name:"building_trade_centerlevel", level:29});
CREATE (n: Building {id: 3863, name:"building_trade_centerlevel", level:63});
CREATE (n: Building {id: 3898, name:"building_trade_centerlevel", level:33});
CREATE (n: Building {id: 3899, name:"building_trade_centerlevel", level:44});
CREATE (n: Building {id: 33558337, name:"building_naval_baselevel", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:33558337}) CREATE (g)-[r:Demand{max_demand: 0.24, current_input: 0.09636916799951938, level: 1}]->(b);
CREATE (n: Building {id: 3910, name:"building_trade_centerlevel", level:40});
CREATE (n: Building {id: 16781139, name:"building_sulfur_minelevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:16781139}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 11.295975247923485, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16781139}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 47.49738908015045, level: 3}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:16781139}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 112.59195049584697,level: 3}]->(g);
CREATE (n: Building {id: 3936, name:"building_conscription_centerlevel", level:13});
CREATE (n: Building {id: 3937, name:"building_conscription_centerlevel", level:28});
MATCH (g: Goods{code: 0}), (b: Building{id:3937}) CREATE (g)-[r:Demand{max_demand: 19.475, current_input: 8.02335747564263, level: 28}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3937}) CREATE (g)-[r:Demand{max_demand: 19.475, current_input: 5.085728792560076, level: 28}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3937}) CREATE (g)-[r:Demand{max_demand: 19.475, current_input: 8.43396379607313, level: 28}]->(b);
CREATE (n: Building {id: 16781206, name:"building_conscription_centerlevel", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:16781206}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.8239648241994998, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16781206}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.522282802830303, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16781206}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.8661323538971121, level: 2}]->(b);
CREATE (n: Building {id: 33558522, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4106, name:"building_conscription_centerlevel", level:7});
MATCH (g: Goods{code: 0}), (b: Building{id:4106}) CREATE (g)-[r:Demand{max_demand: 9.1551, current_input: 3.7717401810144198, level: 7}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4106}) CREATE (g)-[r:Demand{max_demand: 9.1551, current_input: 2.390775644095853, level: 7}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4106}) CREATE (g)-[r:Demand{max_demand: 9.1551, current_input: 3.964764156581725, level: 7}]->(b);
CREATE (n: Building {id: 4140, name:"building_conscription_centerlevel", level:18});
CREATE (n: Building {id: 4141, name:"building_conscription_centerlevel", level:28});
MATCH (g: Goods{code: 0}), (b: Building{id:4141}) CREATE (g)-[r:Demand{max_demand: 18.75507, current_input: 7.726758977699656, level: 28}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4141}) CREATE (g)-[r:Demand{max_demand: 18.75507, current_input: 4.897725263439265, level: 28}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4141}) CREATE (g)-[r:Demand{max_demand: 18.75507, current_input: 8.122186463302555, level: 28}]->(b);
CREATE (n: Building {id: 4142, name:"building_conscription_centerlevel", level:15});
CREATE (n: Building {id: 4143, name:"building_conscription_centerlevel", level:28});
MATCH (g: Goods{code: 0}), (b: Building{id:4143}) CREATE (g)-[r:Demand{max_demand: 19.61254, current_input: 8.080021536602828, level: 28}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4143}) CREATE (g)-[r:Demand{max_demand: 19.61254, current_input: 5.121646180910715, level: 28}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4143}) CREATE (g)-[r:Demand{max_demand: 19.61254, current_input: 8.493527718050633, level: 28}]->(b);
CREATE (n: Building {id: 4144, name:"building_conscription_centerlevel", level:14});
MATCH (g: Goods{code: 0}), (b: Building{id:4144}) CREATE (g)-[r:Demand{max_demand: 9.54867, current_input: 3.9338840989445187, level: 14}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4144}) CREATE (g)-[r:Demand{max_demand: 9.54867, current_input: 2.4935530654508145, level: 14}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4144}) CREATE (g)-[r:Demand{max_demand: 9.54867, current_input: 4.135206011843368, level: 14}]->(b);
CREATE (n: Building {id: 4145, name:"building_conscription_centerlevel", level:13});
MATCH (g: Goods{code: 0}), (b: Building{id:4145}) CREATE (g)-[r:Demand{max_demand: 17.14876, current_input: 7.064987509319706, level: 13}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4145}) CREATE (g)-[r:Demand{max_demand: 17.14876, current_input: 4.478251218932093, level: 13}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4145}) CREATE (g)-[r:Demand{max_demand: 17.14876, current_input: 7.426547932608319, level: 13}]->(b);
CREATE (n: Building {id: 4146, name:"building_conscription_centerlevel", level:18});
CREATE (n: Building {id: 4147, name:"building_conscription_centerlevel", level:17});
CREATE (n: Building {id: 4148, name:"building_conscription_centerlevel", level:31});
MATCH (g: Goods{code: 0}), (b: Building{id:4148}) CREATE (g)-[r:Demand{max_demand: 44.69555, current_input: 18.413780499124975, level: 31}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4148}) CREATE (g)-[r:Demand{max_demand: 44.69555, current_input: 11.671858564020974, level: 31}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4148}) CREATE (g)-[r:Demand{max_demand: 44.69555, current_input: 19.35613096511303, level: 31}]->(b);
CREATE (n: Building {id: 4149, name:"building_conscription_centerlevel", level:26});
MATCH (g: Goods{code: 0}), (b: Building{id:4149}) CREATE (g)-[r:Demand{max_demand: 19.0399, current_input: 7.844103928138027, level: 26}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4149}) CREATE (g)-[r:Demand{max_demand: 19.0399, current_input: 4.972106168804343, level: 26}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4149}) CREATE (g)-[r:Demand{max_demand: 19.0399, current_input: 8.245536702482811, level: 26}]->(b);
CREATE (n: Building {id: 4150, name:"building_conscription_centerlevel", level:20});
CREATE (n: Building {id: 4151, name:"building_conscription_centerlevel", level:24});
MATCH (g: Goods{code: 0}), (b: Building{id:4151}) CREATE (g)-[r:Demand{max_demand: 15.69907, current_input: 6.467740726322821, level: 24}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4151}) CREATE (g)-[r:Demand{max_demand: 15.69907, current_input: 4.0996771407145625, level: 24}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4151}) CREATE (g)-[r:Demand{max_demand: 15.69907, current_input: 6.798736226547768, level: 24}]->(b);
CREATE (n: Building {id: 4152, name:"building_conscription_centerlevel", level:16});
CREATE (n: Building {id: 4153, name:"building_conscription_centerlevel", level:20});
CREATE (n: Building {id: 4154, name:"building_conscription_centerlevel", level:19});
CREATE (n: Building {id: 4155, name:"building_conscription_centerlevel", level:17});
CREATE (n: Building {id: 16781545, name:"building_chemical_plantslevel", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:16781545}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 29.514394790173473, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16781545}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 3.7653250826411613, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16781545}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.310051851789694, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:16781545}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 45.947557428417,level: 1}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:16781545}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 24.5053639618224,level: 1}]->(g);
CREATE (n: Building {id: 16781569, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16781569}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 13.552717911221277, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16781569}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.87565312599606, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16781569}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 2.510216721760774, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16781569}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 21.109950702289087, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16781569}) CREATE (b)-[r:Supply{max_supply: 130.0, current_output: 82.65568306137163,level: 2}]->(g);
CREATE (n: Building {id: 16781757, name:"building_motor_industrylevel", level:2});
MATCH (g: Goods{code: 30}), (b: Building{id:16781757}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.1446006626799, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16781757}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 48.1928008835732,level: 2}]->(g);
CREATE (n: Building {id: 67113408, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:67113408}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 13.552717911221277, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:67113408}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 12.291884375332017, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:67113408}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 13.412667721507663, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:67113408}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 31.664926053433632, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:67113408}) CREATE (b)-[r:Supply{max_supply: 89.68499999999999, current_output: 58.91288385464049,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:67113408}) CREATE (b)-[r:Supply{max_supply: 79.71999999999998, current_output: 52.36700787079155,level: 2}]->(g);
CREATE (n: Building {id: 67113438, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:67113438}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 7.530650165282323, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:67113438}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 55.86031111073816, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:67113438}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 53.191208580977104,level: 1}]->(g);
CREATE (n: Building {id: 4780, name:"building_tooling_workshopslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:4780}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 92.18913281499015, level: 5}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:4780}) CREATE (g)-[r:Demand{max_demand: 100.00000000000001, current_input: 60.241001104466505, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4780}) CREATE (b)-[r:Supply{max_supply: 400.00000000000006, current_output: 243.4008459622532,level: 5}]->(g);
CREATE (n: Building {id: 134222525, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:134222525}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 10.554975351144543, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:134222525}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 79.99999999999999,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:134222525}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 19.999999999999996,level: 2}]->(g);
CREATE (n: Building {id: 167777001, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:167777001}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.803076790895911, level: 1}]->(b);
CREATE (n: Building {id: 16782098, name:"building_tooling_workshopslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:16782098}) CREATE (g)-[r:Demand{max_demand: 119.986796460177, current_input: 73.74319143274965, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782098}) CREATE (g)-[r:Demand{max_demand: 79.9911946902655, current_input: 48.187496476838795, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782098}) CREATE (b)-[r:Supply{max_supply: 319.964796460177, current_output: 194.69925534136814,level: 4}]->(g);
CREATE (n: Building {id: 4962, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:4962}) CREATE (g)-[r:Demand{max_demand: 3.28734, current_input: 2.227619584913708, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4962}) CREATE (g)-[r:Demand{max_demand: 6.57468, current_input: 4.040760318240396, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4962}) CREATE (g)-[r:Demand{max_demand: 8.21835, current_input: 7.651326463615583, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4962}) CREATE (g)-[r:Demand{max_demand: 1.64367, current_input: 1.7348896335415753, level: 2}]->(b);
CREATE (n: Building {id: 16782183, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16782183}) CREATE (g)-[r:Demand{max_demand: 3.288, current_input: 2.228066824604779, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782183}) CREATE (g)-[r:Demand{max_demand: 6.576, current_input: 4.041571582609168, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782183}) CREATE (g)-[r:Demand{max_demand: 8.22, current_input: 7.65286262217113, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782183}) CREATE (g)-[r:Demand{max_demand: 1.644, current_input: 1.7352379477281628, level: 2}]->(b);
CREATE (n: Building {id: 33559400, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:33559400}) CREATE (g)-[r:Demand{max_demand: 3.288, current_input: 2.228066824604779, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33559400}) CREATE (g)-[r:Demand{max_demand: 6.576, current_input: 4.041571582609168, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33559400}) CREATE (g)-[r:Demand{max_demand: 8.22, current_input: 7.65286262217113, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33559400}) CREATE (g)-[r:Demand{max_demand: 1.644, current_input: 1.7352379477281628, level: 2}]->(b);
CREATE (n: Building {id: 5011, name:"building_construction_sectorlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:5011}) CREATE (g)-[r:Demand{max_demand: 8.22, current_input: 5.570167061511947, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5011}) CREATE (g)-[r:Demand{max_demand: 16.44, current_input: 10.103928956522923, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5011}) CREATE (g)-[r:Demand{max_demand: 20.55, current_input: 19.132156555427823, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5011}) CREATE (g)-[r:Demand{max_demand: 4.11, current_input: 4.3380948693204076, level: 5}]->(b);
CREATE (n: Building {id: 16782236, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16782236}) CREATE (g)-[r:Demand{max_demand: 3.288, current_input: 2.228066824604779, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782236}) CREATE (g)-[r:Demand{max_demand: 6.576, current_input: 4.041571582609168, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782236}) CREATE (g)-[r:Demand{max_demand: 8.22, current_input: 7.65286262217113, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782236}) CREATE (g)-[r:Demand{max_demand: 1.644, current_input: 1.7352379477281628, level: 2}]->(b);
CREATE (n: Building {id: 100668325, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:100668325}) CREATE (g)-[r:Demand{max_demand: 4.932, current_input: 3.3421002369071684, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:100668325}) CREATE (g)-[r:Demand{max_demand: 9.864, current_input: 6.062357373913753, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:100668325}) CREATE (g)-[r:Demand{max_demand: 12.33, current_input: 11.479293933256693, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:100668325}) CREATE (g)-[r:Demand{max_demand: 2.466, current_input: 2.602856921592245, level: 3}]->(b);
CREATE (n: Building {id: 16782249, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16782249}) CREATE (g)-[r:Demand{max_demand: 3.288, current_input: 2.228066824604779, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782249}) CREATE (g)-[r:Demand{max_demand: 6.576, current_input: 4.041571582609168, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782249}) CREATE (g)-[r:Demand{max_demand: 8.22, current_input: 7.65286262217113, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782249}) CREATE (g)-[r:Demand{max_demand: 1.644, current_input: 1.7352379477281628, level: 2}]->(b);
CREATE (n: Building {id: 5035, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:5035}) CREATE (g)-[r:Demand{max_demand: 4.932, current_input: 3.3421002369071684, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5035}) CREATE (g)-[r:Demand{max_demand: 9.864, current_input: 6.062357373913753, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5035}) CREATE (g)-[r:Demand{max_demand: 12.33, current_input: 11.479293933256693, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5035}) CREATE (g)-[r:Demand{max_demand: 2.466, current_input: 2.602856921592245, level: 3}]->(b);
CREATE (n: Building {id: 33559468, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33559468}) CREATE (g)-[r:Demand{max_demand: 1.644, current_input: 1.1140334123023894, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33559468}) CREATE (g)-[r:Demand{max_demand: 3.288, current_input: 2.020785791304584, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33559468}) CREATE (g)-[r:Demand{max_demand: 4.11, current_input: 3.826431311085565, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33559468}) CREATE (g)-[r:Demand{max_demand: 0.822, current_input: 0.8676189738640814, level: 1}]->(b);
CREATE (n: Building {id: 16782256, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782256}) CREATE (g)-[r:Demand{max_demand: 1.644, current_input: 1.1140334123023894, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782256}) CREATE (g)-[r:Demand{max_demand: 3.288, current_input: 2.020785791304584, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782256}) CREATE (g)-[r:Demand{max_demand: 4.11, current_input: 3.826431311085565, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782256}) CREATE (g)-[r:Demand{max_demand: 0.822, current_input: 0.8676189738640814, level: 1}]->(b);
CREATE (n: Building {id: 16782265, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782265}) CREATE (g)-[r:Demand{max_demand: 1.644, current_input: 1.1140334123023894, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782265}) CREATE (g)-[r:Demand{max_demand: 3.288, current_input: 2.020785791304584, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782265}) CREATE (g)-[r:Demand{max_demand: 4.11, current_input: 3.826431311085565, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782265}) CREATE (g)-[r:Demand{max_demand: 0.822, current_input: 0.8676189738640814, level: 1}]->(b);
CREATE (n: Building {id: 16782273, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16782273}) CREATE (g)-[r:Demand{max_demand: 3.288, current_input: 2.228066824604779, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782273}) CREATE (g)-[r:Demand{max_demand: 6.576, current_input: 4.041571582609168, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782273}) CREATE (g)-[r:Demand{max_demand: 8.22, current_input: 7.65286262217113, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782273}) CREATE (g)-[r:Demand{max_demand: 1.644, current_input: 1.7352379477281628, level: 2}]->(b);
CREATE (n: Building {id: 5070, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:5070}) CREATE (g)-[r:Demand{max_demand: 3.288, current_input: 2.228066824604779, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5070}) CREATE (g)-[r:Demand{max_demand: 6.576, current_input: 4.041571582609168, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5070}) CREATE (g)-[r:Demand{max_demand: 8.22, current_input: 7.65286262217113, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5070}) CREATE (g)-[r:Demand{max_demand: 1.644, current_input: 1.7352379477281628, level: 2}]->(b);
CREATE (n: Building {id: 50336719, name:"building_steel_millslevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:50336719}) CREATE (g)-[r:Demand{max_demand: 104.99999999999999, current_input: 26.357275578488128, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50336719}) CREATE (g)-[r:Demand{max_demand: 179.99999999999997, current_input: 167.58093333221447, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50336719}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.832463026716816, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:50336719}) CREATE (b)-[r:Supply{max_supply: 269.99999999999994, current_output: 196.3824171619542,level: 3}]->(g);
CREATE (n: Building {id: 16782306, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16782306}) CREATE (g)-[r:Demand{max_demand: 3.288, current_input: 2.228066824604779, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782306}) CREATE (g)-[r:Demand{max_demand: 6.576, current_input: 4.041571582609168, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782306}) CREATE (g)-[r:Demand{max_demand: 8.22, current_input: 7.65286262217113, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782306}) CREATE (g)-[r:Demand{max_demand: 1.644, current_input: 1.7352379477281628, level: 2}]->(b);
CREATE (n: Building {id: 5100, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:5100}) CREATE (g)-[r:Demand{max_demand: 2.92409900990099, current_input: 0.7340122230737589, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5100}) CREATE (g)-[r:Demand{max_demand: 2.92409900990099, current_input: 3.0863792973811113, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5100}) CREATE (b)-[r:Supply{max_supply: 11.69639603960396, current_output: 7.316222465949497,level: 2}]->(g);
CREATE (n: Building {id: 16782318, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:16782318}) CREATE (g)-[r:Demand{max_demand: 4.932, current_input: 3.3421002369071684, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782318}) CREATE (g)-[r:Demand{max_demand: 9.864, current_input: 6.062357373913753, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782318}) CREATE (g)-[r:Demand{max_demand: 12.33, current_input: 11.479293933256693, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782318}) CREATE (g)-[r:Demand{max_demand: 2.466, current_input: 2.602856921592245, level: 3}]->(b);
CREATE (n: Building {id: 33559552, name:"building_universitylevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:33559552}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.409230372687732, level: 3}]->(b);
CREATE (n: Building {id: 33559553, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:33559553}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 22.424614327167287, level: 2}]->(b);
CREATE (n: Building {id: 16782412, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:16782412}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.87565312599606, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16782412}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 5.020433443521548, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782412}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 24.096400441786596, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782412}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 78.2947147726583,level: 2}]->(g);
CREATE (n: Building {id: 16782416, name:"building_chemical_plantslevel", level:2});
MATCH (g: Goods{code: 22}), (b: Building{id:16782416}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 59.02878958034696, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16782416}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 7.530650165282323, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782416}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 18.62010370357939, level: 2}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:16782416}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 91.895114856834,level: 2}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:16782416}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 49.010727923644794,level: 2}]->(g);
CREATE (n: Building {id: 5204, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5204}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.291884375332021, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:5204}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.9023082120620725, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5204}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 22.096500799456166,level: 1}]->(g);
CREATE (n: Building {id: 16782519, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782519}) CREATE (g)-[r:Demand{max_demand: 8.514599999999998, current_input: 5.769798596344235, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782519}) CREATE (g)-[r:Demand{max_demand: 8.514599999999998, current_input: 5.2330239351101, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16782519}) CREATE (g)-[r:Demand{max_demand: 4.257299999999999, current_input: 1.0686745649552143, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782519}) CREATE (g)-[r:Demand{max_demand: 17.029199999999996, current_input: 5.710175029077456, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782519}) CREATE (g)-[r:Demand{max_demand: 17.029199999999996, current_input: 17.974278624971063, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16782519}) CREATE (b)-[r:Supply{max_supply: 38.18159090909091, current_output: 21.98166481793097,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:16782519}) CREATE (b)-[r:Supply{max_supply: 33.939190909090904, current_output: 19.53925703440965,level: 1}]->(g);
CREATE (n: Building {id: 50336954, name:"building_motor_industrylevel", level:1});
MATCH (g: Goods{code: 30}), (b: Building{id:50336954}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.07230033133995, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:50336954}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 24.096400441786603,level: 1}]->(g);
CREATE (n: Building {id: 16782526, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16782526}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.803076790895911, level: 1}]->(b);
CREATE (n: Building {id: 16782539, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16782539}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.803076790895911, level: 1}]->(b);
CREATE (n: Building {id: 16782546, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16782546}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.803076790895911, level: 1}]->(b);
CREATE (n: Building {id: 234886369, name:"building_tooling_workshopslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:234886369}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 55.313479688994086, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:234886369}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 36.1446006626799, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:234886369}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 146.04050757735192,level: 3}]->(g);
CREATE (n: Building {id: 16782593, name:"building_motor_industrylevel", level:1});
MATCH (g: Goods{code: 30}), (b: Building{id:16782593}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.07230033133995, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16782593}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 24.096400441786603,level: 1}]->(g);
CREATE (n: Building {id: 33559815, name:"building_chemical_plantslevel", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:33559815}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 29.514394790173473, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:33559815}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 3.7653250826411613, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33559815}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.310051851789694, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:33559815}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 45.947557428417,level: 1}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:33559815}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 24.5053639618224,level: 1}]->(g);
CREATE (n: Building {id: 16782622, name:"building_furniture_manufacturieslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:16782622}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 27.10543582244257, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782622}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 24.583768750664046, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782622}) CREATE (g)-[r:Demand{max_demand: 80.00000000000001, current_input: 26.825335443015334, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782622}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 63.329852106867264, level: 4}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16782622}) CREATE (b)-[r:Supply{max_supply: 179.37, current_output: 117.825767709281,level: 4}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:16782622}) CREATE (b)-[r:Supply{max_supply: 159.44000000000003, current_output: 104.73401574158312,level: 4}]->(g);
CREATE (n: Building {id: 151000382, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:151000382}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.7653250826411617, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:151000382}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.832463026716816, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:151000382}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 37.530650165282324,level: 1}]->(g);
CREATE (n: Building {id: 201332062, name:"building_tooling_workshopslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:201332062}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 73.75130625199213, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:201332062}) CREATE (g)-[r:Demand{max_demand: 80.00000000000001, current_input: 48.19280088357321, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:201332062}) CREATE (b)-[r:Supply{max_supply: 320.00000000000006, current_output: 194.72067676980262,level: 4}]->(g);
CREATE (n: Building {id: 16782699, name:"building_furniture_manufacturieslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:16782699}) CREATE (g)-[r:Demand{max_demand: 68.0, current_input: 46.07924089815236, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782699}) CREATE (g)-[r:Demand{max_demand: 68.0, current_input: 41.792406876128865, level: 8}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16782699}) CREATE (g)-[r:Demand{max_demand: 34.0, current_input: 8.534736853986633, level: 8}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782699}) CREATE (g)-[r:Demand{max_demand: 136.0, current_input: 45.603070253126056, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782699}) CREATE (g)-[r:Demand{max_demand: 136.0, current_input: 143.5476647755658, level: 8}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16782699}) CREATE (b)-[r:Supply{max_supply: 304.92900000000003, current_output: 175.551801579618,level: 8}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:16782699}) CREATE (b)-[r:Supply{max_supply: 271.04800000000006, current_output: 156.04604584854937,level: 8}]->(g);
CREATE (n: Building {id: 117446002, name:"building_steel_millslevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:117446002}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 15.061300330564645, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:117446002}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 111.72062222147632, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:117446002}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 106.38241716195422,level: 2}]->(g);
CREATE (n: Building {id: 16782717, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:16782717}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.87565312599606, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782717}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 24.096400441786596, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782717}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 97.36033838490127,level: 2}]->(g);
CREATE (n: Building {id: 16782722, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16782722}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.43782656299803, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:16782722}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.558754397816329, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:16782722}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 44.46643804918819,level: 1}]->(g);
CREATE (n: Building {id: 184554883, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:184554883}) CREATE (g)-[r:Demand{max_demand: 9.918399999999998, current_input: 9.23408182867909, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:184554883}) CREATE (g)-[r:Demand{max_demand: 9.918399999999998, current_input: 3.32580508822504, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:184554883}) CREATE (g)-[r:Demand{max_demand: 14.877599999999997, current_input: 8.962415180318107, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:184554883}) CREATE (b)-[r:Supply{max_supply: 9.883681818181817, current_output: 6.156650465649576,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:184554883}) CREATE (b)-[r:Supply{max_supply: 24.709209090909088, current_output: 15.391628995535893,level: 1}]->(g);
CREATE (n: Building {id: 117446025, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:117446025}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.87565312599606, level: 2}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:117446025}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 13.117508795632656, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:117446025}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 2.510216721760774, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:117446025}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 10.554975351144543, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:117446025}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 88.2521965753509,level: 2}]->(g);
CREATE (n: Building {id: 50337197, name:"building_iron_minelevel", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:50337197}) CREATE (g)-[r:Demand{max_demand: 45.117, current_input: 11.325344783568086, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50337197}) CREATE (g)-[r:Demand{max_demand: 45.117, current_input: 47.62088229175884, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50337197}) CREATE (b)-[r:Supply{max_supply: 180.468, current_output: 112.88468956713616,level: 4}]->(g);
CREATE (n: Building {id: 16782783, name:"building_furniture_manufacturieslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:16782783}) CREATE (g)-[r:Demand{max_demand: 34.0603982300885, current_input: 23.080548457812505, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782783}) CREATE (g)-[r:Demand{max_demand: 34.0603982300885, current_input: 20.933323841100563, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782783}) CREATE (g)-[r:Demand{max_demand: 68.120796460177, current_input: 22.84204019612024, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782783}) CREATE (g)-[r:Demand{max_demand: 51.09059292035399, current_input: 53.925994894969634, level: 4}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16782783}) CREATE (b)-[r:Supply{max_supply: 152.73534513274336, current_output: 100.32981711884642,level: 4}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:16782783}) CREATE (b)-[r:Supply{max_supply: 135.7647522123894, current_output: 89.18206030710311,level: 4}]->(g);
CREATE (n: Building {id: 16782786, name:"building_chemical_plantslevel", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:16782786}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 29.514394790173473, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16782786}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 3.7653250826411613, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782786}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.310051851789694, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:16782786}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 45.947557428417,level: 1}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:16782786}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 24.5053639618224,level: 1}]->(g);
CREATE (n: Building {id: 33560006, name:"building_iron_minelevel", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:33560006}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 18.826625413205807, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33560006}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 79.16231513358407, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33560006}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 187.6532508264116,level: 5}]->(g);
CREATE (n: Building {id: 16782813, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16782813}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.0729710938330053, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16782813}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.5102167217607745, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16782813}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.771885481207258, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:16782813}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 31.260762846373765,level: 1}]->(g);
CREATE (n: Building {id: 33560062, name:"building_steel_millslevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:33560062}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 15.061300330564645, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33560062}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 111.72062222147632, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:33560062}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 106.38241716195422,level: 2}]->(g);
CREATE (n: Building {id: 5635, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:5635}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.1459421876660105, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5635}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.020433443521549, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5635}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.289629909499937, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5635}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 60.41368115499137,level: 2}]->(g);
CREATE (n: Building {id: 16782874, name:"building_motor_industrylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:16782874}) CREATE (g)-[r:Demand{max_demand: 0.4861, current_input: 0.12202163484479124, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782874}) CREATE (g)-[r:Demand{max_demand: 2.9166, current_input: 1.75698903821287, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782874}) CREATE (g)-[r:Demand{max_demand: 0.4861, current_input: 0.5130773518191363, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16782874}) CREATE (b)-[r:Supply{max_supply: 3.8888, current_output: 2.4025417099029407,level: 1}]->(g);
CREATE (n: Building {id: 33560093, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:33560093}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.43782656299803, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:33560093}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.558754397816329, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:33560093}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 44.46643804918819,level: 1}]->(g);
CREATE (n: Building {id: 16782882, name:"building_furniture_manufacturieslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:16782882}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 33.881794778053205, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782882}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 30.729710938330058, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782882}) CREATE (g)-[r:Demand{max_demand: 100.00000000000001, current_input: 33.53166930376916, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782882}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 79.16231513358407, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16782882}) CREATE (b)-[r:Supply{max_supply: 224.2125, current_output: 147.28220963660127,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:16782882}) CREATE (b)-[r:Supply{max_supply: 199.3, current_output: 130.91751967697888,level: 5}]->(g);
CREATE (n: Building {id: 33560101, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:33560101}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.87565312599606, level: 2}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:33560101}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 13.117508795632656, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:33560101}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 88.93287609837638,level: 2}]->(g);
CREATE (n: Building {id: 16782905, name:"building_tooling_workshopslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:16782905}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 110.62695937798819, level: 6}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782905}) CREATE (g)-[r:Demand{max_demand: 120.00000000000001, current_input: 72.28920132535981, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782905}) CREATE (b)-[r:Supply{max_supply: 480.00000000000006, current_output: 292.08101515470395,level: 6}]->(g);
CREATE (n: Building {id: 16782915, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782915}) CREATE (g)-[r:Demand{max_demand: 8.498199999999999, current_input: 5.7586853676570335, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782915}) CREATE (g)-[r:Demand{max_demand: 8.498199999999999, current_input: 5.222944589922329, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782915}) CREATE (g)-[r:Demand{max_demand: 16.996399999999998, current_input: 5.699176641545821, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782915}) CREATE (g)-[r:Demand{max_demand: 12.7473, current_input: 13.454743729364484, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16782915}) CREATE (b)-[r:Supply{max_supply: 38.10804545454545, current_output: 25.032668292256194,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:16782915}) CREATE (b)-[r:Supply{max_supply: 33.87381818181818, current_output: 22.251260704227732,level: 1}]->(g);
CREATE (n: Building {id: 16782919, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16782919}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.43782656299803, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:16782919}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.558754397816329, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:16782919}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 44.46643804918819,level: 1}]->(g);
CREATE (n: Building {id: 5723, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:5723}) CREATE (g)-[r:Demand{max_demand: 9.996294117647059, current_input: 6.143664573796464, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5723}) CREATE (g)-[r:Demand{max_demand: 19.992598039215686, current_input: 5.018575390948089, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5723}) CREATE (g)-[r:Demand{max_demand: 9.996294117647059, current_input: 4.288040223121711, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5723}) CREATE (b)-[r:Supply{max_supply: 139.94819607843138, current_output: 60.39132640070405,level: 3}]->(g);
CREATE (n: Building {id: 167777888, name:"building_motor_industrylevel", level:1});
MATCH (g: Goods{code: 30}), (b: Building{id:167777888}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.07230033133995, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:167777888}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 24.096400441786603,level: 1}]->(g);
CREATE (n: Building {id: 16782969, name:"building_furniture_manufacturieslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:16782969}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 20.329076866831922, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782969}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.437826562998033, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16782969}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.7653250826411617, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782969}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 20.119001582261497, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782969}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 63.329852106867264, level: 3}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16782969}) CREATE (b)-[r:Supply{max_supply: 134.5275, current_output: 77.44932422630205,level: 3}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:16782969}) CREATE (b)-[r:Supply{max_supply: 119.57999999999998, current_output: 68.84384375671293,level: 3}]->(g);
CREATE (n: Building {id: 117446278, name:"building_textile_millslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:117446278}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 40.65815373366384, level: 2}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:117446278}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 21.532079411764702, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:117446278}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 2.6269205882352935, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:117446278}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 51.74207878358915,level: 2}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:117446278}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 25.871039391794575,level: 2}]->(g);
CREATE (n: Building {id: 16782996, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:16782996}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.87565312599606, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782996}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 24.096400441786596, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782996}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 97.36033838490127,level: 2}]->(g);
CREATE (n: Building {id: 16782998, name:"building_furniture_manufacturieslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:16782998}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 20.329076866831922, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782998}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.437826562998033, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782998}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 20.119001582261497, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782998}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 47.49738908015044, level: 3}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16782998}) CREATE (b)-[r:Supply{max_supply: 134.5275, current_output: 88.36932578196075,level: 3}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:16782998}) CREATE (b)-[r:Supply{max_supply: 119.57999999999998, current_output: 78.55051180618732,level: 3}]->(g);
CREATE (n: Building {id: 83891865, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:83891865}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.43782656299803, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:83891865}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.048200220893301, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:83891865}) CREATE (b)-[r:Supply{max_supply: 43.2, current_output: 26.28729136392335,level: 1}]->(g);
CREATE (n: Building {id: 16783023, name:"building_food_industrylevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:16783023}) CREATE (g)-[r:Demand{max_demand: 18.9564, current_input: 30.298855342995147, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:16783023}) CREATE (g)-[r:Demand{max_demand: 7.108645454545454, current_input: 2.102965488003946, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:16783023}) CREATE (b)-[r:Supply{max_supply: 16.63423636363636, current_output: 10.777588690279085,level: 1}]->(g);
CREATE (n: Building {id: 16783029, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:16783029}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 7.530650165282323, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783029}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 55.86031111073816, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16783029}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 53.191208580977104,level: 1}]->(g);
CREATE (n: Building {id: 16783051, name:"building_chemical_plantslevel", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:16783051}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 29.514394790173473, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16783051}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 3.7653250826411613, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783051}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.310051851789694, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:16783051}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 45.947557428417,level: 1}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:16783051}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 24.5053639618224,level: 1}]->(g);
CREATE (n: Building {id: 67114726, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:67114726}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.87565312599606, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:67114726}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 5.020433443521548, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:67114726}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 24.096400441786596, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:67114726}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 78.2947147726583,level: 2}]->(g);
CREATE (n: Building {id: 16783084, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783084}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 20.32907686683192, level: 1}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:16783084}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.766039705882353, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:16783084}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.313460294117647, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:16783084}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 25.871039391794582,level: 1}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:16783084}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 12.935519695897291,level: 1}]->(g);
CREATE (n: Building {id: 369104645, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 16783110, name:"building_food_industrylevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:16783110}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 63.93377506909572, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:16783110}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 11.833283859496776, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:16783110}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 22.677061688529836,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16783110}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 38.87496289462257,level: 1}]->(g);
CREATE (n: Building {id: 33560343, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:33560343}) CREATE (g)-[r:Demand{max_demand: 17.03379279279279, current_input: 11.54270943394574, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33560343}) CREATE (g)-[r:Demand{max_demand: 17.03379279279279, current_input: 10.468870574118645, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:33560343}) CREATE (g)-[r:Demand{max_demand: 34.06759459459459, current_input: 11.423433159208194, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33560343}) CREATE (g)-[r:Demand{max_demand: 25.55069369369369, current_input: 26.968694214158127, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:33560343}) CREATE (b)-[r:Supply{max_supply: 76.3838108108108, current_output: 50.17550955758633,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:33560343}) CREATE (b)-[r:Supply{max_supply: 67.89672072072071, current_output: 44.600452940076735,level: 2}]->(g);
CREATE (n: Building {id: 5917, name:"building_steel_millslevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:5917}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 15.061300330564645, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5917}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 111.72062222147632, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:5917}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 106.38241716195422,level: 2}]->(g);
CREATE (n: Building {id: 83892036, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:83892036}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 7.530650165282323, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:83892036}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 55.86031111073816, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:83892036}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 53.191208580977104,level: 1}]->(g);
CREATE (n: Building {id: 5974, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:5974}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 63.9337750690957, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:5974}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 132.47587929090676, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5974}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 18.62010370357939, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:5974}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 23.666567718993548, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:5974}) CREATE (b)-[r:Supply{max_supply: 130.0, current_output: 104.87221165415764,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:5974}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 96.80511844999167,level: 2}]->(g);
CREATE (n: Building {id: 50337638, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:50337638}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.277487675572272, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50337638}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:50337638}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
CREATE (n: Building {id: 16783213, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:16783213}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 7.530650165282323, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783213}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 55.86031111073816, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16783213}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 53.191208580977104,level: 1}]->(g);
CREATE (n: Building {id: 6022, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:6022}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 7.530650165282323, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:6022}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 55.86031111073816, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:6022}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 53.191208580977104,level: 1}]->(g);
CREATE (n: Building {id: 16783248, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:16783248}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 17.158078742217256, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:16783248}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 34.31615748443451,level: 1}]->(g);
CREATE (n: Building {id: 6035, name:"building_arms_industrylevel", level:3});
MATCH (g: Goods{code: 26}), (b: Building{id:6035}) CREATE (g)-[r:Demand{max_demand: 28.136696428571426, current_input: 9.434703999434, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:6035}) CREATE (g)-[r:Demand{max_demand: 70.34174999999999, current_input: 42.374574394401066, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:6035}) CREATE (g)-[r:Demand{max_demand: 14.068348214285713, current_input: 14.849106863310405, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:6035}) CREATE (b)-[r:Supply{max_supply: 56.0764375, current_output: 36.220270137851756,level: 3}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:6035}) CREATE (b)-[r:Supply{max_supply: 70.09555357142857, current_output: 45.275341997597515,level: 3}]->(g);
CREATE (n: Building {id: 16783268, name:"building_motor_industrylevel", level:1});
MATCH (g: Goods{code: 30}), (b: Building{id:16783268}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.07230033133995, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16783268}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 24.096400441786603,level: 1}]->(g);
CREATE (n: Building {id: 33560496, name:"building_conscription_centerlevel", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:33560496}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.8239648241994998, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:33560496}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.522282802830303, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:33560496}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.8661323538971121, level: 2}]->(b);
CREATE (n: Building {id: 16783287, name:"building_furniture_manufacturieslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:16783287}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 20.329076866831922, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783287}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.437826562998033, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16783287}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.7653250826411617, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16783287}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 20.119001582261497, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783287}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 63.329852106867264, level: 3}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16783287}) CREATE (b)-[r:Supply{max_supply: 134.5275, current_output: 77.44932422630205,level: 3}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:16783287}) CREATE (b)-[r:Supply{max_supply: 119.57999999999998, current_output: 68.84384375671293,level: 3}]->(g);
CREATE (n: Building {id: 83892159, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:83892159}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.277487675572272, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:83892159}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:83892159}) CREATE (b)-[r:Supply{max_supply: 12.499999999999998, current_output: 12.499999999999998,level: 1}]->(g);
CREATE (n: Building {id: 33560524, name:"building_chemical_plantslevel", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:33560524}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 29.514394790173473, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:33560524}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 3.7653250826411613, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33560524}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.310051851789694, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:33560524}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 45.947557428417,level: 1}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:33560524}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 24.5053639618224,level: 1}]->(g);
CREATE (n: Building {id: 6105, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6105}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.43782656299803, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:6105}) CREATE (b)-[r:Supply{max_supply: 26.249999999999996, current_output: 16.133098242623277,level: 1}]->(g);
CREATE (n: Building {id: 33560562, name:"building_railwaylevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:33560562}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.020433443521549, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:33560562}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.543770962414516, level: 2}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:33560562}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 41.41051101484017,level: 2}]->(g);
CREATE (n: Building {id: 16783355, name:"building_food_industrylevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:16783355}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 63.93377506909572, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:16783355}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 11.833283859496776, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:16783355}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 22.677061688529836,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16783355}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 38.87496289462257,level: 1}]->(g);
CREATE (n: Building {id: 33560585, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:33560585}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.832463026716816, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33560585}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:33560585}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 3}]->(g);
CREATE (n: Building {id: 6157, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6157}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.0729710938330053, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6157}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.5102167217607745, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:6157}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.1448149547499686, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6157}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 30.206840577495687,level: 1}]->(g);
CREATE (n: Building {id: 6185, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:6185}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.87565312599606, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:6185}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 24.096400441786596, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:6185}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 97.36033838490127,level: 2}]->(g);
CREATE (n: Building {id: 50337842, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:50337842}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.832463026716816, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50337842}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:50337842}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 3}]->(g);
CREATE (n: Building {id: 100669508, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:100669508}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 20.32907686683192, level: 1}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:100669508}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.766039705882353, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:100669508}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.313460294117647, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:100669508}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 25.871039391794582,level: 1}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:100669508}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 12.935519695897291,level: 1}]->(g);
CREATE (n: Building {id: 33560645, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:33560645}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.5102167217607745, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:33560645}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.771885481207258, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:33560645}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 20.705255507420084,level: 1}]->(g);
CREATE (n: Building {id: 16783468, name:"building_tooling_workshopslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:16783468}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 55.313479688994086, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16783468}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 36.1446006626799, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783468}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 146.04050757735192,level: 3}]->(g);
CREATE (n: Building {id: 33560712, name:"building_food_industrylevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:33560712}) CREATE (g)-[r:Demand{max_demand: 36.16, current_input: 57.79613266246252, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:33560712}) CREATE (g)-[r:Demand{max_demand: 9.04, current_input: 3.8778254381879425, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:33560712}) CREATE (g)-[r:Demand{max_demand: 36.16, current_input: 10.697288608985083, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:33560712}) CREATE (b)-[r:Supply{max_supply: 22.599999999999998, current_output: 12.993456325361844,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:33560712}) CREATE (b)-[r:Supply{max_supply: 90.39999999999999, current_output: 51.97382530144738,level: 1}]->(g);
CREATE (n: Building {id: 33560713, name:"building_portlevel", level:1});
CREATE (n: Building {id: 6282, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:6282}) CREATE (g)-[r:Demand{max_demand: 12.8478, current_input: 3.2250762397838075, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:6282}) CREATE (g)-[r:Demand{max_demand: 12.8478, current_input: 13.560821231643487, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:6282}) CREATE (g)-[r:Demand{max_demand: 4.2826, current_input: 5.4483221907622505, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:6282}) CREATE (b)-[r:Supply{max_supply: 61.66944, current_output: 46.27308198365409,level: 1}]->(g);
CREATE (n: Building {id: 16783503, name:"building_naval_baselevel", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:16783503}) CREATE (g)-[r:Demand{max_demand: 0.482, current_input: 0.19354141239903475, level: 1}]->(b);
CREATE (n: Building {id: 50337936, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:50337936}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.43782656299803, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:50337936}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.048200220893301, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50337936}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 48.68016919245065,level: 1}]->(g);
CREATE (n: Building {id: 33560737, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:33560737}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.43782656299803, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:33560737}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.048200220893301, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33560737}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 48.68016919245065,level: 1}]->(g);
CREATE (n: Building {id: 6315, name:"building_conscription_centerlevel", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:6315}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.8239648241994998, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:6315}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.522282802830303, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:6315}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.8661323538971121, level: 2}]->(b);
CREATE (n: Building {id: 134224056, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:134224056}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.277487675572272, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:134224056}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:134224056}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
CREATE (n: Building {id: 16783570, name:"building_munition_plantslevel", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:16783570}) CREATE (g)-[r:Demand{max_demand: 14.0, current_input: 6.863231496886902, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:16783570}) CREATE (g)-[r:Demand{max_demand: 14.0, current_input: 17.810795000857304, level: 1}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:16783570}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 26.079039371108628,level: 1}]->(g);
CREATE (n: Building {id: 33560830, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:33560830}) CREATE (g)-[r:Demand{max_demand: 3.5042999999999997, current_input: 3.698780012301582, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33560830}) CREATE (b)-[r:Supply{max_supply: 28.034399999999998, current_output: 28.034399999999998,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:33560830}) CREATE (b)-[r:Supply{max_supply: 7.0085999999999995, current_output: 7.0085999999999995,level: 1}]->(g);
CREATE (n: Building {id: 16783615, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 16783616, name:"building_subsistence_pastureslevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:16783616}) CREATE (b)-[r:Supply{max_supply: 0.07730000000000001, current_output: 0.06957,level: 2}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:16783616}) CREATE (b)-[r:Supply{max_supply: 0.11594444444444443, current_output: 0.10435,level: 2}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:16783616}) CREATE (b)-[r:Supply{max_supply: 0.03864444444444444, current_output: 0.03478,level: 2}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:16783616}) CREATE (b)-[r:Supply{max_supply: 0.07730000000000001, current_output: 0.06957,level: 2}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:16783616}) CREATE (b)-[r:Supply{max_supply: 0.07730000000000001, current_output: 0.06957,level: 2}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:16783616}) CREATE (b)-[r:Supply{max_supply: 0.07730000000000001, current_output: 0.06957,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:16783616}) CREATE (b)-[r:Supply{max_supply: 0.2056111111111111, current_output: 0.18505,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16783616}) CREATE (b)-[r:Supply{max_supply: 0.10821111111111112, current_output: 0.09739,level: 2}]->(g);
CREATE (n: Building {id: 6414, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6414}) CREATE (g)-[r:Demand{max_demand: 1.36905, current_input: 0.8414102152024152, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6414}) CREATE (g)-[r:Demand{max_demand: 2.7381, current_input: 0.6873224405853178, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6414}) CREATE (b)-[r:Supply{max_supply: 10.54168, current_output: 4.562522862616308,level: 1}]->(g);
CREATE (n: Building {id: 6443, name:"building_subsistence_fishing_villageslevel", level:13});
MATCH (g: Goods{code: 7}), (b: Building{id:6443}) CREATE (b)-[r:Supply{max_supply: 0.009872727272727273, current_output: 0.01086,level: 13}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:6443}) CREATE (b)-[r:Supply{max_supply: 0.03951818181818181, current_output: 0.04347,level: 13}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6443}) CREATE (b)-[r:Supply{max_supply: 0.004936363636363636, current_output: 0.00543,level: 13}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6443}) CREATE (b)-[r:Supply{max_supply: 0.014818181818181815, current_output: 0.0163,level: 13}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6443}) CREATE (b)-[r:Supply{max_supply: 0.009872727272727273, current_output: 0.01086,level: 13}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6443}) CREATE (b)-[r:Supply{max_supply: 0.009872727272727273, current_output: 0.01086,level: 13}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6443}) CREATE (b)-[r:Supply{max_supply: 0.009872727272727273, current_output: 0.01086,level: 13}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6443}) CREATE (b)-[r:Supply{max_supply: 0.013827272727272726, current_output: 0.01521,level: 13}]->(g);
CREATE (n: Building {id: 6444, name:"building_subsistence_rice_paddieslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:6444}) CREATE (b)-[r:Supply{max_supply: 0.029699999999999997, current_output: 0.03861,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6444}) CREATE (b)-[r:Supply{max_supply: 0.004946153846153846, current_output: 0.00643,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6444}) CREATE (b)-[r:Supply{max_supply: 0.004946153846153846, current_output: 0.00643,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6444}) CREATE (b)-[r:Supply{max_supply: 0.006600000000000001, current_output: 0.00858,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6444}) CREATE (b)-[r:Supply{max_supply: 0.006600000000000001, current_output: 0.00858,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6444}) CREATE (b)-[r:Supply{max_supply: 0.006600000000000001, current_output: 0.00858,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6444}) CREATE (b)-[r:Supply{max_supply: 0.009899999999999999, current_output: 0.01287,level: 44}]->(g);
