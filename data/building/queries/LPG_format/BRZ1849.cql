CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:85.06380821687672, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:29.941884991227028, pop_demand:2225.223218625074});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:22.88183032502158, pop_demand:130.75452337259188});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:21.557090080613015, pop_demand:162.30920176576637});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:12.99312867663967, pop_demand:265.5998719842331});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:40.32735385771238, pop_demand:591.5975915219248});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:45.70358635006128, pop_demand:505.27308097272027});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:39.417075014977804, pop_demand:40.451824024169724});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:22.593883030826095, pop_demand:256.2135025000002});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:80.61684619688165, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:51.785346383972396, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:49.11334817546142, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:42.68202770146965, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:15.749682266532684});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:47.636002442499816, pop_demand:34.3524938727937});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:35.09208175381959, pop_demand:97.5005009787665});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:47.096729945285844, pop_demand:535.2364624967825});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:28.95246798685522, pop_demand:8.287656540578537});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:25.47400688997191, pop_demand:140.62893145942164});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:13.171855512057604, pop_demand:26.884035504075847});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:57.2188781182797, pop_demand:197.13405625241327});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:53.71678530507576});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:146.99679246730278});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 16778775, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 1838, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1838}) CREATE (b)-[r:Supply{max_supply: 22.3704, current_output: 22.3704,level: 1}]->(g);
CREATE (n: Building {id: 1839, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1839}) CREATE (g)-[r:Demand{max_demand: 8.917348214285713, current_input: 8.12012904980687, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1839}) CREATE (b)-[r:Supply{max_supply: 107.00819642857142, current_output: 97.44156485838025,level: 3}]->(g);
CREATE (n: Building {id: 1840, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1840}) CREATE (g)-[r:Demand{max_demand: 16.26659523809524, current_input: 5.485287909648733, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1840}) CREATE (b)-[r:Supply{max_supply: 65.06639682539682, current_output: 21.941156991163233,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1840}) CREATE (b)-[r:Supply{max_supply: 8.13329365079365, current_output: 2.7426426166822906,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1840}) CREATE (b)-[r:Supply{max_supply: 8.13329365079365, current_output: 2.7426426166822906,level: 2}]->(g);
CREATE (n: Building {id: 1841, name:"building_coffee_plantationlevel", level:3});
MATCH (g: Goods{code: 41}), (b: Building{id:1841}) CREATE (b)-[r:Supply{max_supply: 36.73679411764706, current_output: 37.47153,level: 3}]->(g);
CREATE (n: Building {id: 1842, name:"building_barrackslevel", level:8});
MATCH (g: Goods{code: 1}), (b: Building{id:1842}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 0.0, level: 8}]->(b);
CREATE (n: Building {id: 1843, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1843}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.7092607292790682, level: 1}]->(b);
CREATE (n: Building {id: 1844, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1844}) CREATE (g)-[r:Demand{max_demand: 4.979299999999999, current_input: 4.5341459822024035, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1844}) CREATE (b)-[r:Supply{max_supply: 59.751599999999996, current_output: 54.40975178642885,level: 1}]->(g);
CREATE (n: Building {id: 1845, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1845}) CREATE (b)-[r:Supply{max_supply: 26.535, current_output: 26.535,level: 1}]->(g);
CREATE (n: Building {id: 1846, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1846}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.5529953830883905, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1846}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 54.63594459706068,level: 1}]->(g);
CREATE (n: Building {id: 1847, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1847}) CREATE (g)-[r:Demand{max_demand: 9.2686, current_input: 3.1254813177071186, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1847}) CREATE (b)-[r:Supply{max_supply: 37.0744, current_output: 12.501925270828474,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1847}) CREATE (b)-[r:Supply{max_supply: 4.6343, current_output: 1.5627406588535593,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1847}) CREATE (b)-[r:Supply{max_supply: 4.6343, current_output: 1.5627406588535593,level: 1}]->(g);
CREATE (n: Building {id: 1848, name:"building_coffee_plantationlevel", level:3});
MATCH (g: Goods{code: 41}), (b: Building{id:1848}) CREATE (b)-[r:Supply{max_supply: 53.05859803921569, current_output: 54.11977,level: 3}]->(g);
CREATE (n: Building {id: 1849, name:"building_sugar_plantationlevel", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1849}) CREATE (b)-[r:Supply{max_supply: 26.5293, current_output: 26.5293,level: 1}]->(g);
CREATE (n: Building {id: 1850, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1850}) CREATE (g)-[r:Demand{max_demand: 18.50979279279279, current_input: 6.241720601655453, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1850}) CREATE (b)-[r:Supply{max_supply: 74.03919819819819, current_output: 24.966891520454332,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1850}) CREATE (b)-[r:Supply{max_supply: 9.25489189189189, current_output: 3.1208587818556413,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1850}) CREATE (b)-[r:Supply{max_supply: 9.25489189189189, current_output: 3.1208587818556413,level: 2}]->(g);
CREATE (n: Building {id: 1851, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1851}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 18.211981532353565, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1851}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 218.54377838824274,level: 4}]->(g);
CREATE (n: Building {id: 1852, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1852}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.814633326676531, level: 1}]->(b);
CREATE (n: Building {id: 1853, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1853}) CREATE (g)-[r:Demand{max_demand: 59.98679207920792, current_input: 112.7045341016657, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1853}) CREATE (g)-[r:Demand{max_demand: 39.99119801980198, current_input: 24.311258208235255, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1853}) CREATE (b)-[r:Supply{max_supply: 119.97359405940594, current_output: 96.45368434205585,level: 2}]->(g);
CREATE (n: Building {id: 1854, name:"building_gold_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1854}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.105990766176781, level: 2}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:1854}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 18.211981532353562,level: 2}]->(g);
CREATE (n: Building {id: 1855, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1855}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.105990766176781, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1855}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 36.423963064707124,level: 2}]->(g);
CREATE (n: Building {id: 1856, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1856}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 18.211981532353565, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1856}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 218.54377838824274,level: 4}]->(g);
CREATE (n: Building {id: 1857, name:"building_maize_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1857}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 100.8,level: 3}]->(g);
CREATE (n: Building {id: 1858, name:"building_coffee_plantationlevel", level:8});
MATCH (g: Goods{code: 41}), (b: Building{id:1858}) CREATE (b)-[r:Supply{max_supply: 141.54879439252335, current_output: 151.45721,level: 8}]->(g);
CREATE (n: Building {id: 1859, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1859}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 1860, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:1860}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 34.88779996005918, level: 6}]->(b);
CREATE (n: Building {id: 1861, name:"building_universitylevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:1861}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 14.536583316691326, level: 5}]->(b);
CREATE (n: Building {id: 1862, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1862}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 44.80982970445847, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1862}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 281.8233736007622, level: 2}]->(b);
CREATE (n: Building {id: 1863, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1863}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.961965940891696, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1863}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 56.36467472015243, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1863}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.5529953830883905, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1863}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 60.81390619864837,level: 1}]->(g);
CREATE (n: Building {id: 1864, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1864}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 169.09402416045728, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1864}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 3}]->(g);
CREATE (n: Building {id: 1865, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:1865}) CREATE (b)-[r:Supply{max_supply: 48.83, current_output: 49.3183,level: 2}]->(g);
CREATE (n: Building {id: 1866, name:"building_coffee_plantationlevel", level:4});
MATCH (g: Goods{code: 41}), (b: Building{id:1866}) CREATE (b)-[r:Supply{max_supply: 70.74479611650486, current_output: 72.86714,level: 4}]->(g);
CREATE (n: Building {id: 1867, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1867}) CREATE (b)-[r:Supply{max_supply: 53.05859405940594, current_output: 53.58918,level: 2}]->(g);
CREATE (n: Building {id: 1868, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:1868}) CREATE (g)-[r:Demand{max_demand: 24.675, current_input: 3.7141105612651497, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1868}) CREATE (b)-[r:Supply{max_supply: 243.75652631578947, current_output: 36.69052436744803,level: 5}]->(g);
CREATE (n: Building {id: 1869, name:"building_barrackslevel", level:6});
MATCH (g: Goods{code: 1}), (b: Building{id:1869}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.0, level: 6}]->(b);
CREATE (n: Building {id: 1870, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1870}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.4185214585581365, level: 2}]->(b);
CREATE (n: Building {id: 1871, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:1871}) CREATE (g)-[r:Demand{max_demand: 79.836, current_input: 46.42170662685475, level: 4}]->(b);
CREATE (n: Building {id: 1872, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1872}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.923931881783393, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1872}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 75.15289962686991, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1872}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 14.221474455668773,level: 1}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:1872}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 18.961965940891695,level: 1}]->(g);
CREATE (n: Building {id: 1873, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1873}) CREATE (g)-[r:Demand{max_demand: 4.99, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1873}) CREATE (g)-[r:Demand{max_demand: 2.495, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 1874, name:"building_coffee_plantationlevel", level:4});
MATCH (g: Goods{code: 41}), (b: Building{id:1874}) CREATE (b)-[r:Supply{max_supply: 70.74879611650486, current_output: 72.87126,level: 4}]->(g);
CREATE (n: Building {id: 1875, name:"building_sugar_plantationlevel", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1875}) CREATE (b)-[r:Supply{max_supply: 26.5293, current_output: 26.5293,level: 1}]->(g);
CREATE (n: Building {id: 1876, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1876}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
CREATE (n: Building {id: 1877, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1877}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 18.211981532353565, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1877}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 218.54377838824274,level: 4}]->(g);
CREATE (n: Building {id: 1878, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:1878}) CREATE (b)-[r:Supply{max_supply: 10.04, current_output: 10.1404,level: 2}]->(g);
CREATE (n: Building {id: 1879, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:1879}) CREATE (g)-[r:Demand{max_demand: 19.944, current_input: 14.221586883168147, level: 10}]->(b);
CREATE (n: Building {id: 1880, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1880}) CREATE (g)-[r:Demand{max_demand: 9.998, current_input: 5.417437754266425, level: 2}]->(b);
CREATE (n: Building {id: 1881, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1881}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
CREATE (n: Building {id: 1882, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1882}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.5529953830883905, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1882}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 54.63594459706068,level: 1}]->(g);
CREATE (n: Building {id: 1883, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1883}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1884, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1884}) CREATE (b)-[r:Supply{max_supply: 39.3724, current_output: 49.2155,level: 1}]->(g);
CREATE (n: Building {id: 1885, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1885}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
CREATE (n: Building {id: 1886, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1886}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.658986149265171, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1886}) CREATE (b)-[r:Supply{max_supply: 179.99999999999997, current_output: 163.90783379118204,level: 3}]->(g);
CREATE (n: Building {id: 1887, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1887}) CREATE (b)-[r:Supply{max_supply: 21.175, current_output: 21.175,level: 1}]->(g);
CREATE (n: Building {id: 1888, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1888}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
CREATE (n: Building {id: 1889, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1889}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 9.10599076617678, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1889}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 109.27188919412136,level: 2}]->(g);
CREATE (n: Building {id: 1890, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1890}) CREATE (g)-[r:Demand{max_demand: 39.72, current_input: 35.59692871722181, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1890}) CREATE (b)-[r:Supply{max_supply: 44.685, current_output: 40.046544806874536,level: 1}]->(g);
CREATE (n: Building {id: 1891, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1891}) CREATE (b)-[r:Supply{max_supply: 39.348, current_output: 39.74148,level: 2}]->(g);
CREATE (n: Building {id: 1892, name:"building_maize_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1892}) CREATE (g)-[r:Demand{max_demand: 9.859, current_input: 1.4839884913277857, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1892}) CREATE (b)-[r:Supply{max_supply: 97.39403603603603, current_output: 14.659866984627355,level: 2}]->(g);
CREATE (n: Building {id: 1893, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:1893}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1893}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1894, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1894}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.629266653353062, level: 2}]->(b);
CREATE (n: Building {id: 1895, name:"building_textile_millslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:1895}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 143.39145505426714, level: 4}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1895}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 161.31538693605054,level: 4}]->(g);
CREATE (n: Building {id: 1896, name:"building_cotton_plantationlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:1896}) CREATE (b)-[r:Supply{max_supply: 196.862, current_output: 253.95198,level: 5}]->(g);
CREATE (n: Building {id: 1897, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:1897}) CREATE (b)-[r:Supply{max_supply: 22.13725, current_output: 22.13725,level: 1}]->(g);
CREATE (n: Building {id: 1898, name:"building_maize_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:1898}) CREATE (g)-[r:Demand{max_demand: 14.803946428571427, current_input: 2.228307751925456, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1898}) CREATE (b)-[r:Supply{max_supply: 146.2436339285714, current_output: 22.01276698244788,level: 3}]->(g);
CREATE (n: Building {id: 1899, name:"building_barrackslevel", level:4});
MATCH (g: Goods{code: 1}), (b: Building{id:1899}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1899}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.0, level: 4}]->(b);
CREATE (n: Building {id: 1900, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1900}) CREATE (g)-[r:Demand{max_demand: 4.997, current_input: 2.7076351728415005, level: 1}]->(b);
CREATE (n: Building {id: 1901, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1901}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.5529953830883905, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1901}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 54.63594459706068,level: 1}]->(g);
CREATE (n: Building {id: 1902, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1902}) CREATE (b)-[r:Supply{max_supply: 26.5293, current_output: 26.5293,level: 1}]->(g);
CREATE (n: Building {id: 1903, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1903}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.5529953830883905, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1903}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 54.63594459706068,level: 1}]->(g);
CREATE (n: Building {id: 1904, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1904}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.5529953830883905, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1904}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 54.63594459706068,level: 1}]->(g);
CREATE (n: Building {id: 1905, name:"building_barrackslevel", level:8});
MATCH (g: Goods{code: 1}), (b: Building{id:1905}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 0.0, level: 8}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1905}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.0, level: 8}]->(b);
CREATE (n: Building {id: 1906, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1906}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.7092607292790682, level: 1}]->(b);
CREATE (n: Building {id: 3077, name:"building_subsistence_farmslevel", level:28});
MATCH (g: Goods{code: 7}), (b: Building{id:3077}) CREATE (b)-[r:Supply{max_supply: 29.729, current_output: 32.7019,level: 28}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3077}) CREATE (b)-[r:Supply{max_supply: 5.945799999999999, current_output: 6.54038,level: 28}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3077}) CREATE (b)-[r:Supply{max_supply: 5.945799999999999, current_output: 6.54038,level: 28}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3077}) CREATE (b)-[r:Supply{max_supply: 5.945799999999999, current_output: 6.54038,level: 28}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3077}) CREATE (b)-[r:Supply{max_supply: 5.945799999999999, current_output: 6.54038,level: 28}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3077}) CREATE (b)-[r:Supply{max_supply: 5.945799999999999, current_output: 6.54038,level: 28}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3077}) CREATE (b)-[r:Supply{max_supply: 8.32411818181818, current_output: 9.15653,level: 28}]->(g);
CREATE (n: Building {id: 3078, name:"building_subsistence_farmslevel", level:73});
MATCH (g: Goods{code: 7}), (b: Building{id:3078}) CREATE (b)-[r:Supply{max_supply: 16.180445454545453, current_output: 17.79849,level: 73}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3078}) CREATE (b)-[r:Supply{max_supply: 3.236081818181818, current_output: 3.55969,level: 73}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3078}) CREATE (b)-[r:Supply{max_supply: 3.236081818181818, current_output: 3.55969,level: 73}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3078}) CREATE (b)-[r:Supply{max_supply: 3.236081818181818, current_output: 3.55969,level: 73}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3078}) CREATE (b)-[r:Supply{max_supply: 3.236081818181818, current_output: 3.55969,level: 73}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3078}) CREATE (b)-[r:Supply{max_supply: 3.236081818181818, current_output: 3.55969,level: 73}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3078}) CREATE (b)-[r:Supply{max_supply: 4.530518181818182, current_output: 4.98357,level: 73}]->(g);
CREATE (n: Building {id: 3080, name:"building_subsistence_farmslevel", level:12});
MATCH (g: Goods{code: 7}), (b: Building{id:3080}) CREATE (b)-[r:Supply{max_supply: 4.3923, current_output: 4.83153,level: 12}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3080}) CREATE (b)-[r:Supply{max_supply: 0.8784545454545454, current_output: 0.9663,level: 12}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3080}) CREATE (b)-[r:Supply{max_supply: 0.8784545454545454, current_output: 0.9663,level: 12}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3080}) CREATE (b)-[r:Supply{max_supply: 0.8784545454545454, current_output: 0.9663,level: 12}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3080}) CREATE (b)-[r:Supply{max_supply: 0.8784545454545454, current_output: 0.9663,level: 12}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3080}) CREATE (b)-[r:Supply{max_supply: 0.8784545454545454, current_output: 0.9663,level: 12}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3080}) CREATE (b)-[r:Supply{max_supply: 1.2298363636363634, current_output: 1.35282,level: 12}]->(g);
CREATE (n: Building {id: 3083, name:"building_subsistence_farmslevel", level:74});
MATCH (g: Goods{code: 7}), (b: Building{id:3083}) CREATE (b)-[r:Supply{max_supply: 8.944745454545453, current_output: 9.83922,level: 74}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3083}) CREATE (b)-[r:Supply{max_supply: 1.7889454545454544, current_output: 1.96784,level: 74}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3083}) CREATE (b)-[r:Supply{max_supply: 1.7889454545454544, current_output: 1.96784,level: 74}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3083}) CREATE (b)-[r:Supply{max_supply: 1.7889454545454544, current_output: 1.96784,level: 74}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3083}) CREATE (b)-[r:Supply{max_supply: 1.7889454545454544, current_output: 1.96784,level: 74}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3083}) CREATE (b)-[r:Supply{max_supply: 1.7889454545454544, current_output: 1.96784,level: 74}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3083}) CREATE (b)-[r:Supply{max_supply: 2.5045272727272727, current_output: 2.75498,level: 74}]->(g);
CREATE (n: Building {id: 3085, name:"building_subsistence_farmslevel", level:78});
MATCH (g: Goods{code: 7}), (b: Building{id:3085}) CREATE (b)-[r:Supply{max_supply: 11.528400000000001, current_output: 14.4105,level: 78}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3085}) CREATE (b)-[r:Supply{max_supply: 2.3056799999999997, current_output: 2.8821,level: 78}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3085}) CREATE (b)-[r:Supply{max_supply: 2.3056799999999997, current_output: 2.8821,level: 78}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3085}) CREATE (b)-[r:Supply{max_supply: 2.3056799999999997, current_output: 2.8821,level: 78}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3085}) CREATE (b)-[r:Supply{max_supply: 2.3056799999999997, current_output: 2.8821,level: 78}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3085}) CREATE (b)-[r:Supply{max_supply: 2.3056799999999997, current_output: 2.8821,level: 78}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3085}) CREATE (b)-[r:Supply{max_supply: 3.2279519999999997, current_output: 4.03494,level: 78}]->(g);
CREATE (n: Building {id: 3086, name:"building_subsistence_farmslevel", level:49});
MATCH (g: Goods{code: 7}), (b: Building{id:3086}) CREATE (b)-[r:Supply{max_supply: 20.335, current_output: 25.41875,level: 49}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3086}) CREATE (b)-[r:Supply{max_supply: 4.067, current_output: 5.08375,level: 49}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3086}) CREATE (b)-[r:Supply{max_supply: 4.067, current_output: 5.08375,level: 49}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3086}) CREATE (b)-[r:Supply{max_supply: 4.067, current_output: 5.08375,level: 49}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3086}) CREATE (b)-[r:Supply{max_supply: 4.067, current_output: 5.08375,level: 49}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3086}) CREATE (b)-[r:Supply{max_supply: 4.067, current_output: 5.08375,level: 49}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3086}) CREATE (b)-[r:Supply{max_supply: 5.6938, current_output: 7.11725,level: 49}]->(g);
CREATE (n: Building {id: 3087, name:"building_subsistence_farmslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 8.4337, current_output: 9.27707,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 1.6867363636363635, current_output: 1.85541,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 1.6867363636363635, current_output: 1.85541,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 1.6867363636363635, current_output: 1.85541,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 1.6867363636363635, current_output: 1.85541,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 1.6867363636363635, current_output: 1.85541,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 2.3614272727272727, current_output: 2.59757,level: 44}]->(g);
CREATE (n: Building {id: 3088, name:"building_subsistence_farmslevel", level:41});
MATCH (g: Goods{code: 7}), (b: Building{id:3088}) CREATE (b)-[r:Supply{max_supply: 5.4837454545454545, current_output: 6.03212,level: 41}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3088}) CREATE (b)-[r:Supply{max_supply: 1.0967454545454545, current_output: 1.20642,level: 41}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3088}) CREATE (b)-[r:Supply{max_supply: 1.0967454545454545, current_output: 1.20642,level: 41}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3088}) CREATE (b)-[r:Supply{max_supply: 1.0967454545454545, current_output: 1.20642,level: 41}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3088}) CREATE (b)-[r:Supply{max_supply: 1.0967454545454545, current_output: 1.20642,level: 41}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3088}) CREATE (b)-[r:Supply{max_supply: 1.0967454545454545, current_output: 1.20642,level: 41}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3088}) CREATE (b)-[r:Supply{max_supply: 1.5354454545454543, current_output: 1.68899,level: 41}]->(g);
CREATE (n: Building {id: 3089, name:"building_subsistence_farmslevel", level:222});
MATCH (g: Goods{code: 7}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 35.8974, current_output: 39.48714,level: 222}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 7.179472727272727, current_output: 7.89742,level: 222}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 7.179472727272727, current_output: 7.89742,level: 222}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 7.179472727272727, current_output: 7.89742,level: 222}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 7.179472727272727, current_output: 7.89742,level: 222}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 7.179472727272727, current_output: 7.89742,level: 222}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 10.051263636363636, current_output: 11.05639,level: 222}]->(g);
CREATE (n: Building {id: 3090, name:"building_subsistence_farmslevel", level:103});
MATCH (g: Goods{code: 7}), (b: Building{id:3090}) CREATE (b)-[r:Supply{max_supply: 44.90541818181818, current_output: 49.39596,level: 103}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3090}) CREATE (b)-[r:Supply{max_supply: 8.981081818181817, current_output: 9.87919,level: 103}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3090}) CREATE (b)-[r:Supply{max_supply: 8.981081818181817, current_output: 9.87919,level: 103}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3090}) CREATE (b)-[r:Supply{max_supply: 8.981081818181817, current_output: 9.87919,level: 103}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3090}) CREATE (b)-[r:Supply{max_supply: 8.981081818181817, current_output: 9.87919,level: 103}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3090}) CREATE (b)-[r:Supply{max_supply: 8.981081818181817, current_output: 9.87919,level: 103}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3090}) CREATE (b)-[r:Supply{max_supply: 12.573518181818182, current_output: 13.83087,level: 103}]->(g);
CREATE (n: Building {id: 3091, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3091}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 18.788224906717478, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3091}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 2}]->(g);
CREATE (n: Building {id: 3092, name:"building_subsistence_farmslevel", level:89});
MATCH (g: Goods{code: 7}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 70.14757272727272, current_output: 77.16233,level: 89}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 14.029509090909091, current_output: 15.43246,level: 89}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 14.029509090909091, current_output: 15.43246,level: 89}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 14.029509090909091, current_output: 15.43246,level: 89}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 14.029509090909091, current_output: 15.43246,level: 89}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 14.029509090909091, current_output: 15.43246,level: 89}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 19.641318181818182, current_output: 21.60545,level: 89}]->(g);
CREATE (n: Building {id: 3093, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3093}) CREATE (g)-[r:Demand{max_demand: 28.02389523809524, current_input: 52.651924649562226, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3093}) CREATE (b)-[r:Supply{max_supply: 140.11949523809523, current_output: 140.11949523809523,level: 6}]->(g);
CREATE (n: Building {id: 3094, name:"building_subsistence_farmslevel", level:67});
MATCH (g: Goods{code: 7}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 37.47477272727273, current_output: 41.22225,level: 67}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 7.494954545454545, current_output: 8.24445,level: 67}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 7.494954545454545, current_output: 8.24445,level: 67}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 7.494954545454545, current_output: 8.24445,level: 67}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 7.494954545454545, current_output: 8.24445,level: 67}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 7.494954545454545, current_output: 8.24445,level: 67}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 10.492936363636362, current_output: 11.54223,level: 67}]->(g);
CREATE (n: Building {id: 3095, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3095}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 28.182337360076215, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3095}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 75.0,level: 3}]->(g);
CREATE (n: Building {id: 3096, name:"building_subsistence_farmslevel", level:11});
MATCH (g: Goods{code: 7}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 2.7128727272727273, current_output: 2.98416,level: 11}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 0.5425727272727272, current_output: 0.59683,level: 11}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 0.5425727272727272, current_output: 0.59683,level: 11}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 0.5425727272727272, current_output: 0.59683,level: 11}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 0.5425727272727272, current_output: 0.59683,level: 11}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 0.5425727272727272, current_output: 0.59683,level: 11}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 0.7595999999999999, current_output: 0.83556,level: 11}]->(g);
CREATE (n: Building {id: 3097, name:"building_subsistence_farmslevel", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:3097}) CREATE (b)-[r:Supply{max_supply: 11.937445454545454, current_output: 13.13119,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3097}) CREATE (b)-[r:Supply{max_supply: 2.387481818181818, current_output: 2.62623,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3097}) CREATE (b)-[r:Supply{max_supply: 2.387481818181818, current_output: 2.62623,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3097}) CREATE (b)-[r:Supply{max_supply: 2.387481818181818, current_output: 2.62623,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3097}) CREATE (b)-[r:Supply{max_supply: 2.387481818181818, current_output: 2.62623,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3097}) CREATE (b)-[r:Supply{max_supply: 2.387481818181818, current_output: 2.62623,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3097}) CREATE (b)-[r:Supply{max_supply: 3.342481818181818, current_output: 3.67673,level: 14}]->(g);
CREATE (n: Building {id: 3098, name:"building_subsistence_farmslevel", level:23});
MATCH (g: Goods{code: 7}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 7.728572727272726, current_output: 8.50143,level: 23}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 1.5457090909090907, current_output: 1.70028,level: 23}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 1.5457090909090907, current_output: 1.70028,level: 23}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 1.5457090909090907, current_output: 1.70028,level: 23}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 1.5457090909090907, current_output: 1.70028,level: 23}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 1.5457090909090907, current_output: 1.70028,level: 23}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 2.1639999999999997, current_output: 2.3804,level: 23}]->(g);
CREATE (n: Building {id: 3099, name:"building_subsistence_farmslevel", level:51});
MATCH (g: Goods{code: 7}), (b: Building{id:3099}) CREATE (b)-[r:Supply{max_supply: 91.89434545454544, current_output: 101.08378,level: 51}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3099}) CREATE (b)-[r:Supply{max_supply: 18.378863636363636, current_output: 20.21675,level: 51}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3099}) CREATE (b)-[r:Supply{max_supply: 18.378863636363636, current_output: 20.21675,level: 51}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3099}) CREATE (b)-[r:Supply{max_supply: 18.378863636363636, current_output: 20.21675,level: 51}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3099}) CREATE (b)-[r:Supply{max_supply: 18.378863636363636, current_output: 20.21675,level: 51}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3099}) CREATE (b)-[r:Supply{max_supply: 18.378863636363636, current_output: 20.21675,level: 51}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3099}) CREATE (b)-[r:Supply{max_supply: 25.73040909090909, current_output: 28.30345,level: 51}]->(g);
CREATE (n: Building {id: 3100, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3100}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 9.394112453358739, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3100}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3101, name:"building_subsistence_farmslevel", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 5.2455, current_output: 5.77005,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 1.0491, current_output: 1.15401,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 1.0491, current_output: 1.15401,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 1.0491, current_output: 1.15401,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 1.0491, current_output: 1.15401,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 1.0491, current_output: 1.15401,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 1.4687363636363635, current_output: 1.61561,level: 30}]->(g);
CREATE (n: Building {id: 3889, name:"building_trade_centerlevel", level:40});
CREATE (n: Building {id: 3890, name:"building_trade_centerlevel", level:28});
CREATE (n: Building {id: 4074, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4076, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4077, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4078, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4079, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4080, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4081, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 33558998, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:33558998}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 9.10599076617678, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33558998}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 109.27188919412136,level: 2}]->(g);
CREATE (n: Building {id: 4652, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:4652}) CREATE (g)-[r:Demand{max_demand: 24.99, current_input: 22.395952886288345, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4652}) CREATE (g)-[r:Demand{max_demand: 74.97, current_input: 140.8553221256609, level: 1}]->(b);
CREATE (n: Building {id: 4945, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:4945}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.814633326676531, level: 2}]->(b);
CREATE (n: Building {id: 50336614, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:50336614}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:50336614}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 16782259, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16782259}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.9073166633382654, level: 1}]->(b);
CREATE (n: Building {id: 16782319, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782319}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 35.847863763566785, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:16782319}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:16782319}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 26.88589782267509,level: 1}]->(g);
CREATE (n: Building {id: 16782451, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16782451}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.814633326676531, level: 1}]->(b);
CREATE (n: Building {id: 16782620, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16782620}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.9073166633382654, level: 1}]->(b);
CREATE (n: Building {id: 16782812, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 5660, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 16782884, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:16782884}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16782884}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 16782900, name:"building_barrackslevel", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:16782900}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16782900}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 16782918, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16782918}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 56.36467472015243, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:16782918}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 1}]->(g);
CREATE (n: Building {id: 33560264, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:33560264}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 56.36467472015243, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33560264}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.158304533011155, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33560264}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 48.23745679951673,level: 1}]->(g);
CREATE (n: Building {id: 16783120, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16783120}) CREATE (g)-[r:Demand{max_demand: 4.88, current_input: 2.8375410634181466, level: 1}]->(b);
CREATE (n: Building {id: 33560366, name:"building_coffee_plantationlevel", level:1});
MATCH (g: Goods{code: 41}), (b: Building{id:33560366}) CREATE (b)-[r:Supply{max_supply: 17.6862, current_output: 17.6862,level: 1}]->(g);
CREATE (n: Building {id: 16783217, name:"building_tea_plantationlevel", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:16783217}) CREATE (b)-[r:Supply{max_supply: 18.89, current_output: 18.89,level: 1}]->(g);
CREATE (n: Building {id: 33560494, name:"building_gold_fieldslevel", level:6});
MATCH (g: Goods{code: 50}), (b: Building{id:33560494}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 126.0,level: 6}]->(g);
CREATE (n: Building {id: 6164, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:6164}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.9073166633382654, level: 1}]->(b);
CREATE (n: Building {id: 16783403, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:16783403}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 33560792, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 6573, name:"building_subsistence_rice_paddieslevel", level:73});
MATCH (g: Goods{code: 7}), (b: Building{id:6573}) CREATE (b)-[r:Supply{max_supply: 0.0032818181818181813, current_output: 0.00361,level: 73}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6573}) CREATE (b)-[r:Supply{max_supply: 0.0005454545454545454, current_output: 0.0006,level: 73}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6573}) CREATE (b)-[r:Supply{max_supply: 0.0005454545454545454, current_output: 0.0006,level: 73}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6573}) CREATE (b)-[r:Supply{max_supply: 0.0007272727272727272, current_output: 0.0008,level: 73}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6573}) CREATE (b)-[r:Supply{max_supply: 0.0007272727272727272, current_output: 0.0008,level: 73}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6573}) CREATE (b)-[r:Supply{max_supply: 0.0007272727272727272, current_output: 0.0008,level: 73}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6573}) CREATE (b)-[r:Supply{max_supply: 0.0010909090909090907, current_output: 0.0012,level: 73}]->(g);
