CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:45.94707391780537, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:53.604919570772935, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:28.64323555749042, pop_demand:1490.0615505303251});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:35.0, pop_demand:37.55505050741982});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:24.597459136175335, pop_demand:61.928927714733824});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:13.36218425918943, pop_demand:152.65963805857606});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:41.865186178746015, pop_demand:63.910015791211684});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:47.33060049509756, pop_demand:549.1578185814863});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:44.071837601205374, pop_demand:447.44183284489856});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:49.47331149555429, pop_demand:65.18534739973761});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:11.71334971356464, pop_demand:142.8804866658084});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:20.668070830646485, pop_demand:42.368210172922524});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:87.5, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:37.41098871325009, pop_demand:27.8863311511267});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:41.337550369918574, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:29.957500000000003, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:30.240150576889953, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:18.65284167903465, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:30.676902022546418, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:13.82010431514969});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:18.397703451502633, pop_demand:39.88683960220663});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:22.120783117549042, pop_demand:40.29873793463489});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:47.594405481706026, pop_demand:513.3022624999999});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:70.23784344800688, pop_demand:130.84482088018683});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:5.803279119813188});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:42.61597182796612, pop_demand:14.115376239462377});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:37.624125524261565});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:85.67168037525121, pop_demand:128.98764027473047});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:350.0, pop_demand:0.7042666666666667});
CREATE (n: Building {id: 356, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:356}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.690389781729522, level: 1}]->(b);
CREATE (n: Building {id: 357, name:"building_steel_millslevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:357}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 60.35604514699963, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:357}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 114.64979852032572, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:357}) CREATE (b)-[r:Supply{max_supply: 195.0, current_output: 158.5386768736809,level: 3}]->(g);
CREATE (n: Building {id: 358, name:"building_construction_sectorlevel", level:2});
CREATE (n: Building {id: 359, name:"building_chemical_plantslevel", level:3});
MATCH (g: Goods{code: 22}), (b: Building{id:359}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:359}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 28.66244963008143, level: 3}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:359}) CREATE (b)-[r:Supply{max_supply: 269.99999999999994, current_output: 128.9810233353664,level: 3}]->(g);
CREATE (n: Building {id: 360, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:360}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 107.62738204566179, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:360}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 84.55396099929646, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:360}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 159.99999999999997,level: 2}]->(g);
CREATE (n: Building {id: 361, name:"building_coal_minelevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:361}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 87.05366007138069, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:361}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 239.99999999999997,level: 6}]->(g);
CREATE (n: Building {id: 362, name:"building_iron_minelevel", level:6});
MATCH (g: Goods{code: 23}), (b: Building{id:362}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 40.237363431333094, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:362}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 87.0536600713807, level: 6}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:362}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 200.4747268626662,level: 6}]->(g);
CREATE (n: Building {id: 363, name:"building_wheat_farmlevel", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:363}) CREATE (g)-[r:Demand{max_demand: 37.644393162393165, current_input: 75.94466688665119, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:363}) CREATE (g)-[r:Demand{max_demand: 7.528871794871796, current_input: 10.923597432529586, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:363}) CREATE (b)-[r:Supply{max_supply: 263.51079487179493, current_output: 263.51079487179493,level: 8}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:363}) CREATE (b)-[r:Supply{max_supply: 60.23103418803419, current_output: 60.23103418803419,level: 8}]->(g);
CREATE (n: Building {id: 364, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:364}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 43.526830035690345, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:364}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 239.99999999999997,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:364}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 59.99999999999999,level: 6}]->(g);
CREATE (n: Building {id: 365, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:365}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.968948503805148, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:365}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.706227238555515, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:365}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:365}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 36.19682568353695,level: 1}]->(g);
CREATE (n: Building {id: 366, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:366}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.270477351028217, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:366}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.270477351028217, level: 5}]->(b);
CREATE (n: Building {id: 367, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:367}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 8.071169345188567, level: 6}]->(b);
CREATE (n: Building {id: 368, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 369, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:369}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 53.813691022830895, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:369}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:369}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 35.0,level: 1}]->(g);
CREATE (n: Building {id: 370, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:370}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 33.9027436933844, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:370}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 35.14321127834267, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:370}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 30.207605300130602,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:370}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 51.78446622879531,level: 2}]->(g);
CREATE (n: Building {id: 371, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:371}) CREATE (g)-[r:Demand{max_demand: 19.83, current_input: 18.945879205483823, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:371}) CREATE (g)-[r:Demand{max_demand: 9.915, current_input: 14.904171364148812, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:371}) CREATE (b)-[r:Supply{max_supply: 14.872499999999997, current_output: 14.540954702056432,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:371}) CREATE (b)-[r:Supply{max_supply: 14.872499999999997, current_output: 14.540954702056432,level: 1}]->(g);
CREATE (n: Building {id: 372, name:"building_universitylevel", level:16});
MATCH (g: Goods{code: 14}), (b: Building{id:372}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 10.761559126918089, level: 16}]->(b);
CREATE (n: Building {id: 373, name:"building_textile_millslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:373}) CREATE (g)-[r:Demand{max_demand: 160.00000000000003, current_input: 110.96043588079644, level: 4}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:373}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:373}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 83.22032691059731,level: 4}]->(g);
CREATE (n: Building {id: 374, name:"building_furniture_manufacturieslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:374}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 20.805081727649327, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:374}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 107.62738204566179, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:374}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 45.09582863585118, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:374}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 21.763415017845176, level: 3}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:374}) CREATE (b)-[r:Supply{max_supply: 134.99999999999997, current_output: 124.65571694360546,level: 3}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:374}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 55.40254086382466,level: 3}]->(g);
CREATE (n: Building {id: 375, name:"building_logging_camplevel", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:375}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 72.54471672615058, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:375}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 600.0,level: 10}]->(g);
CREATE (n: Building {id: 376, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:376}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:376}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 377, name:"building_wheat_farmlevel", level:7});
MATCH (g: Goods{code: 32}), (b: Building{id:377}) CREATE (g)-[r:Demand{max_demand: 34.45329310344828, current_input: 69.50686803748161, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:377}) CREATE (g)-[r:Demand{max_demand: 6.890655172413793, current_input: 9.997612550806858, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:377}) CREATE (b)-[r:Supply{max_supply: 206.7197931034483, current_output: 206.7197931034483,level: 7}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:377}) CREATE (b)-[r:Supply{max_supply: 62.01593965517242, current_output: 62.01593965517242,level: 7}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:377}) CREATE (b)-[r:Supply{max_supply: 41.343956896551724, current_output: 41.343956896551724,level: 7}]->(g);
CREATE (n: Building {id: 378, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:378}) CREATE (g)-[r:Demand{max_demand: 46.2725, current_input: 19.609558844401626, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:378}) CREATE (g)-[r:Demand{max_demand: 46.2725, current_input: 67.13650809421605, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:378}) CREATE (b)-[r:Supply{max_supply: 185.09, current_output: 131.76411768880325,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:378}) CREATE (b)-[r:Supply{max_supply: 23.13624561403509, current_output: 16.470511588766424,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:378}) CREATE (b)-[r:Supply{max_supply: 115.6812456140351, current_output: 82.35257043316805,level: 5}]->(g);
CREATE (n: Building {id: 379, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:379}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.968948503805148, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:379}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.706227238555515, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:379}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:379}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 36.19682568353695,level: 1}]->(g);
CREATE (n: Building {id: 380, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:380}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.270477351028217, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:380}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.270477351028217, level: 5}]->(b);
CREATE (n: Building {id: 381, name:"building_portlevel", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:381}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 4}]->(b);
CREATE (n: Building {id: 3250, name:"building_subsistence_farmslevel", level:70});
MATCH (g: Goods{code: 7}), (b: Building{id:3250}) CREATE (b)-[r:Supply{max_supply: 100.4675, current_output: 110.51425,level: 70}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3250}) CREATE (b)-[r:Supply{max_supply: 20.0935, current_output: 22.10285,level: 70}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3250}) CREATE (b)-[r:Supply{max_supply: 20.0935, current_output: 22.10285,level: 70}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3250}) CREATE (b)-[r:Supply{max_supply: 20.0935, current_output: 22.10285,level: 70}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3250}) CREATE (b)-[r:Supply{max_supply: 20.0935, current_output: 22.10285,level: 70}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3250}) CREATE (b)-[r:Supply{max_supply: 20.0935, current_output: 22.10285,level: 70}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3250}) CREATE (b)-[r:Supply{max_supply: 28.130899999999997, current_output: 30.94399,level: 70}]->(g);
CREATE (n: Building {id: 3251, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3251}) CREATE (g)-[r:Demand{max_demand: 24.571, current_input: 44.07520673739927, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3251}) CREATE (b)-[r:Supply{max_supply: 122.85499999999999, current_output: 122.85499999999999,level: 5}]->(g);
CREATE (n: Building {id: 3252, name:"building_subsistence_farmslevel", level:108});
MATCH (g: Goods{code: 7}), (b: Building{id:3252}) CREATE (b)-[r:Supply{max_supply: 94.7565, current_output: 104.23215,level: 108}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3252}) CREATE (b)-[r:Supply{max_supply: 18.9513, current_output: 20.84643,level: 108}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3252}) CREATE (b)-[r:Supply{max_supply: 18.9513, current_output: 20.84643,level: 108}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3252}) CREATE (b)-[r:Supply{max_supply: 18.9513, current_output: 20.84643,level: 108}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3252}) CREATE (b)-[r:Supply{max_supply: 18.9513, current_output: 20.84643,level: 108}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3252}) CREATE (b)-[r:Supply{max_supply: 18.9513, current_output: 20.84643,level: 108}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3252}) CREATE (b)-[r:Supply{max_supply: 26.531818181818178, current_output: 29.185,level: 108}]->(g);
CREATE (n: Building {id: 3253, name:"building_urban_centerlevel", level:14});
MATCH (g: Goods{code: 10}), (b: Building{id:3253}) CREATE (g)-[r:Demand{max_demand: 64.995, current_input: 116.58736160096313, level: 14}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3253}) CREATE (g)-[r:Demand{max_demand: 129.99, current_input: 87.17424787398316, level: 14}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3253}) CREATE (b)-[r:Supply{max_supply: 714.945, current_output: 597.2016816534538,level: 14}]->(g);
CREATE (n: Building {id: 3904, name:"building_trade_centerlevel", level:109});
CREATE (n: Building {id: 4164, name:"building_conscription_centerlevel", level:34});
CREATE (n: Building {id: 4165, name:"building_conscription_centerlevel", level:53});
CREATE (n: Building {id: 4715, name:"building_tooling_workshopslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:4715}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 161.44107306849267, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:4715}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 126.83094149894474, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4715}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 240.0,level: 3}]->(g);
CREATE (n: Building {id: 16782025, name:"building_universitylevel", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:16782025}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 4.708182118026664, level: 7}]->(b);
CREATE (n: Building {id: 50336965, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:50336965}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 20.118681715666543, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50336965}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 38.21659950677524, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:50336965}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 52.846225624560304,level: 1}]->(g);
