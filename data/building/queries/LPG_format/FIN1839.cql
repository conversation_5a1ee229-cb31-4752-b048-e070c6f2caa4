CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0.15656057085960834});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:27.65804107950114, pop_demand:224.19941066494198});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:6.095074434917054, pop_demand:3.690043121355071});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:30.4061192309726, pop_demand:7.124219285714287});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:18.927151794746425, pop_demand:42.745315714285724});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:52.5, pop_demand:11.43634278340708});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:39.23713860297489, pop_demand:42.81808487626801});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:30.03399562490553, pop_demand:33.99933160954269});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:52.5, pop_demand:1.8028688129046537});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:35.288233003553096, pop_demand:32.99430704106278});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:52.5, pop_demand:7.400241666666667});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:34.85260790578519, pop_demand:0.08491639193126083});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:70.0, pop_demand:0.6153168756945075});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:46.429489061567644, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:1.3650130529753446});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:50.95661381635877, pop_demand:6.001726962589305});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:52.5, pop_demand:9.114206707302207});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:34.767167274173026, pop_demand:79.62647959590359});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:87.5, pop_demand:5.416779041411771});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:2.6801214508145077});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:52.5, pop_demand:0.667022002783079});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:70.0, pop_demand:0.9176528030723322});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:6.746909829863262});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:43.69807808383523, pop_demand:6.942058274998835});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:350.0, pop_demand:0.03347690231220267});
CREATE (n: Building {id: 64, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:64}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 65, name:"building_furniture_manufacturies", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:65}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.062587179351597, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:65}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 16.15549276090134, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:65}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:65}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 8.708391452901065,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:65}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 10.88548931612633,level: 1}]->(g);
CREATE (n: Building {id: 66, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:66}) CREATE (b)-[r:Supply{max_supply: 26.509999999999998, current_output: 26.7751,level: 2}]->(g);
CREATE (n: Building {id: 67, name:"building_rye_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:67}) CREATE (g)-[r:Demand{max_demand: 1.1835346534653466, current_input: 0.9298838831839565, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:67}) CREATE (b)-[r:Supply{max_supply: 5.917693069306931, current_output: 4.649434974012811,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:67}) CREATE (b)-[r:Supply{max_supply: 17.75309900990099, current_output: 13.94832048013146,level: 2}]->(g);
CREATE (n: Building {id: 68, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:68}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.8568369794774515, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:68}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 94.28204375372943,level: 2}]->(g);
CREATE (n: Building {id: 69, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:69}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 11.333523588829525, level: 1}]->(b);
CREATE (n: Building {id: 70, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:70}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 71, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:71}) CREATE (b)-[r:Supply{max_supply: 19.77, current_output: 19.77,level: 1}]->(g);
CREATE (n: Building {id: 72, name:"building_rye_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:72}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.7856836979477452, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:72}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 3.9284184897387258,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:72}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 11.785255469216178,level: 1}]->(g);
CREATE (n: Building {id: 73, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:73}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.9284184897387258, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:73}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 47.14102187686471,level: 1}]->(g);
CREATE (n: Building {id: 74, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:74}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 11.333523588829525, level: 1}]->(b);
CREATE (n: Building {id: 75, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:75}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.8568369794774515, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:75}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 94.28204375372943,level: 2}]->(g);
CREATE (n: Building {id: 76, name:"building_rye_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:76}) CREATE (g)-[r:Demand{max_demand: 0.99977, current_input: 0.7855029906972172, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:76}) CREATE (b)-[r:Supply{max_supply: 4.99885, current_output: 3.927514953486086,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:76}) CREATE (b)-[r:Supply{max_supply: 14.99655, current_output: 11.782544860458257,level: 1}]->(g);
CREATE (n: Building {id: 3580, name:"building_subsistence_farms", level:28});
MATCH (g: Goods{code: 7}), (b: Building{id:3580}) CREATE (b)-[r:Supply{max_supply: 55.00264, current_output: 55.00264,level: 28}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3580}) CREATE (b)-[r:Supply{max_supply: 13.75066, current_output: 13.75066,level: 28}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3580}) CREATE (b)-[r:Supply{max_supply: 13.75066, current_output: 13.75066,level: 28}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3580}) CREATE (b)-[r:Supply{max_supply: 13.75066, current_output: 13.75066,level: 28}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3580}) CREATE (b)-[r:Supply{max_supply: 13.75066, current_output: 13.75066,level: 28}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3580}) CREATE (b)-[r:Supply{max_supply: 13.75066, current_output: 13.75066,level: 28}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3580}) CREATE (b)-[r:Supply{max_supply: 13.75066, current_output: 13.75066,level: 28}]->(g);
CREATE (n: Building {id: 3581, name:"building_subsistence_fishing_villages", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:3581}) CREATE (b)-[r:Supply{max_supply: 1.0, current_output: 1.0,level: 2}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:3581}) CREATE (b)-[r:Supply{max_supply: 4.0, current_output: 4.0,level: 2}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3581}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.5,level: 2}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3581}) CREATE (b)-[r:Supply{max_supply: 1.5, current_output: 1.5,level: 2}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3581}) CREATE (b)-[r:Supply{max_supply: 1.0, current_output: 1.0,level: 2}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3581}) CREATE (b)-[r:Supply{max_supply: 1.0, current_output: 1.0,level: 2}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3581}) CREATE (b)-[r:Supply{max_supply: 1.0, current_output: 1.0,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3581}) CREATE (b)-[r:Supply{max_supply: 1.0, current_output: 1.0,level: 2}]->(g);
CREATE (n: Building {id: 3582, name:"building_subsistence_farms", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 9.0279, current_output: 9.0279,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 2.25697, current_output: 2.25697,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 2.25697, current_output: 2.25697,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 2.25697, current_output: 2.25697,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 2.25697, current_output: 2.25697,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 2.25697, current_output: 2.25697,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 2.25697, current_output: 2.25697,level: 9}]->(g);
CREATE (n: Building {id: 3583, name:"building_subsistence_farms", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3583}) CREATE (b)-[r:Supply{max_supply: 32.92776, current_output: 32.92776,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3583}) CREATE (b)-[r:Supply{max_supply: 8.23194, current_output: 8.23194,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3583}) CREATE (b)-[r:Supply{max_supply: 8.23194, current_output: 8.23194,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3583}) CREATE (b)-[r:Supply{max_supply: 8.23194, current_output: 8.23194,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3583}) CREATE (b)-[r:Supply{max_supply: 8.23194, current_output: 8.23194,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3583}) CREATE (b)-[r:Supply{max_supply: 8.23194, current_output: 8.23194,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3583}) CREATE (b)-[r:Supply{max_supply: 8.23194, current_output: 8.23194,level: 18}]->(g);
CREATE (n: Building {id: 3934, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:3934}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 3935, name:"building_barracks", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:3935}) CREATE (g)-[r:Demand{max_demand: 1.99998, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3935}) CREATE (g)-[r:Demand{max_demand: 1.99998, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 3936, name:"building_naval_base", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:3936}) CREATE (g)-[r:Demand{max_demand: 1.165, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 4380, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 4714, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:4714}) CREATE (g)-[r:Demand{max_demand: 0.42055, current_input: 0.20584405493438637, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:4714}) CREATE (b)-[r:Supply{max_supply: 0.8411, current_output: 0.41168810986877274,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:4714}) CREATE (b)-[r:Supply{max_supply: 0.8411, current_output: 0.41168810986877274,level: 1}]->(g);
CREATE (n: Building {id: 4788, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:4788}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.125174358703194, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4788}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 43.08131402907024, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4788}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4788}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.8568369794774515, level: 1}]->(b);
CREATE (n: Building {id: 4896, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4896}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 32.31098552180268, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4896}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4896}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 5086, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5086}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.125174358703194, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5086}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 43.08131402907024, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:5086}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 22.859527563865296,level: 1}]->(g);
CREATE (n: Building {id: 5112, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5112}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 7.6564679483789915, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5112}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 80.7774638045067, level: 1}]->(b);
CREATE (n: Building {id: 5200, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:5200}) CREATE (g)-[r:Demand{max_demand: 1.063, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:5200}) CREATE (g)-[r:Demand{max_demand: 1.063, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:5200}) CREATE (g)-[r:Demand{max_demand: 1.063, current_input: 0.5203001554993525, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5200}) CREATE (g)-[r:Demand{max_demand: 1.063, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 5264, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:5264}) CREATE (g)-[r:Demand{max_demand: 0.07, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 5693, name:"building_conscription_center", level:1});
