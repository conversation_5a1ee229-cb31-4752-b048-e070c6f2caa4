CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:73.534375, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:65.00415126990885, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:76.83605659291432, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:25.45446290995452, pop_demand:4851.462248857292});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:19.837373908798618, pop_demand:125.12851996420169});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:15.455831417947213, pop_demand:283.01160990572595});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:25.882380646922417, pop_demand:496.3949688127902});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:48.85249548258672, pop_demand:366.49112707323343});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:40.783988733355166, pop_demand:1754.5187570382643});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:37.40978048626003, pop_demand:1086.3898655237108});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:37.3944123474744, pop_demand:128.1675963758767});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:14.776736104047615, pop_demand:804.9888406628133});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:18.648611413737314, pop_demand:76.86338947239767});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:44.95940204367083, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:25.44506312261931, pop_demand:101.62717835432252});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:44.57506444298794, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:38.266840470004425, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:59.72207134766754, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:78.8787815604904, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:48.73639215681785, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:58.230537865023074, pop_demand:340.4234391425969});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:18.03778948593709, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:48.87437355759645, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:87.5, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:107.31991196209306, pop_demand:370.7213818054273});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:35.89283719466227, pop_demand:488.9947925185462});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:27.904331213339574, pop_demand:17.881449016205764});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:32.97811920525915, pop_demand:1921.5376191666667});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:81.44554425482528, pop_demand:570.43139});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:51.430669185078166, pop_demand:57.44515813491803});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:94.92951831475506, pop_demand:86.24215078733594});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:89.80850696624142, pop_demand:432.5082787729983});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:350.0, pop_demand:0.34791770780113973});
CREATE (n: Building {id: 418, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:418}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 419, name:"building_rye_farmlevel", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:419}) CREATE (g)-[r:Demand{max_demand: 4.9288454545454545, current_input: 10.524413318865504, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:419}) CREATE (g)-[r:Demand{max_demand: 0.9857636363636364, current_input: 0.694191452386219, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:419}) CREATE (b)-[r:Supply{max_supply: 29.5731, current_output: 25.19948900650127,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:419}) CREATE (b)-[r:Supply{max_supply: 14.786545454545454, current_output: 12.599740630030315,level: 1}]->(g);
CREATE (n: Building {id: 420, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:420}) CREATE (g)-[r:Demand{max_demand: 25.333196428571426, current_input: 16.12139761376261, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:420}) CREATE (g)-[r:Demand{max_demand: 25.333196428571426, current_input: 17.840066090496443, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:420}) CREATE (b)-[r:Supply{max_supply: 101.33279464285712, current_output: 67.92293339330085,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:420}) CREATE (b)-[r:Supply{max_supply: 12.666598214285713, current_output: 8.490365926064763,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:420}) CREATE (b)-[r:Supply{max_supply: 63.33299999999999, current_output: 42.451835615106575,level: 3}]->(g);
CREATE (n: Building {id: 421, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:421}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 7.042169408348311, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:421}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 56.33735526678649,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:421}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 14.084338816696622,level: 2}]->(g);
CREATE (n: Building {id: 422, name:"building_naval_baselevel", level:6});
MATCH (g: Goods{code: 5}), (b: Building{id:422}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 0.0, level: 6}]->(b);
CREATE (n: Building {id: 423, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:423}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 446, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:446}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 13.427889837972034, level: 2}]->(b);
CREATE (n: Building {id: 447, name:"building_chemical_plantslevel", level:5});
MATCH (g: Goods{code: 22}), (b: Building{id:447}) CREATE (g)-[r:Demand{max_demand: 122.784, current_input: 141.85839773923718, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:447}) CREATE (g)-[r:Demand{max_demand: 40.928000000000004, current_input: 34.687663058996385, level: 5}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:447}) CREATE (b)-[r:Supply{max_supply: 368.35200000000003, current_output: 340.27048376548373,level: 5}]->(g);
CREATE (n: Building {id: 448, name:"building_steel_millslevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:448}) CREATE (g)-[r:Demand{max_demand: 53.03059459459459, current_input: 66.49473927859823, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:448}) CREATE (g)-[r:Demand{max_demand: 60.60639639639639, current_input: 51.36567282589354, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:448}) CREATE (g)-[r:Demand{max_demand: 7.575792792792791, current_input: 5.335001624939102, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:448}) CREATE (b)-[r:Supply{max_supply: 98.4853963963964, current_output: 83.76990040483275,level: 2}]->(g);
CREATE (n: Building {id: 449, name:"building_iron_minelevel", level:7});
MATCH (g: Goods{code: 23}), (b: Building{id:449}) CREATE (g)-[r:Demand{max_demand: 104.99999999999999, current_input: 131.65885990206272, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:449}) CREATE (g)-[r:Demand{max_demand: 104.99999999999999, current_input: 73.94277878765728, level: 7}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:449}) CREATE (b)-[r:Supply{max_supply: 419.99999999999994, current_output: 357.8855575753145,level: 7}]->(g);
CREATE (n: Building {id: 450, name:"building_sulfur_minelevel", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:450}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 62.69469519145844, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:450}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 35.21084704174156, level: 5}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:450}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 170.42169408348312,level: 5}]->(g);
CREATE (n: Building {id: 451, name:"building_rye_farmlevel", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:451}) CREATE (g)-[r:Demand{max_demand: 19.686592920353984, current_input: 42.03618122028624, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:451}) CREATE (g)-[r:Demand{max_demand: 3.9373185840707965, current_input: 2.7727264483664658, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:451}) CREATE (b)-[r:Supply{max_supply: 118.11959292035401, current_output: 100.65070564969065,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:451}) CREATE (b)-[r:Supply{max_supply: 59.059796460177004, current_output: 50.325352824845325,level: 4}]->(g);
CREATE (n: Building {id: 452, name:"building_barrackslevel", level:16});
MATCH (g: Goods{code: 0}), (b: Building{id:452}) CREATE (g)-[r:Demand{max_demand: 16.0, current_input: 5.958666666666666, level: 16}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:452}) CREATE (g)-[r:Demand{max_demand: 16.0, current_input: 14.222647256593175, level: 16}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:452}) CREATE (g)-[r:Demand{max_demand: 16.0, current_input: 13.918437041793892, level: 16}]->(b);
CREATE (n: Building {id: 455, name:"building_furniture_manufacturieslevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:455}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 86.07703426223732, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:455}) CREATE (g)-[r:Demand{max_demand: 120.00000000000001, current_input: 72.94571017168781, level: 6}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:455}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 20.557680076742972, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:455}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 21.126508225044944, level: 6}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:455}) CREATE (b)-[r:Supply{max_supply: 270.0, current_output: 179.19399556426134,level: 6}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:455}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 79.64177580633839,level: 6}]->(g);
CREATE (n: Building {id: 456, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:456}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 21.126508225044944, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:456}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 169.01206580035955,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:456}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 42.25301645008989,level: 6}]->(g);
CREATE (n: Building {id: 457, name:"building_rye_farmlevel", level:11});
MATCH (g: Goods{code: 32}), (b: Building{id:457}) CREATE (g)-[r:Demand{max_demand: 54.153, current_input: 115.63124867932855, level: 11}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:457}) CREATE (g)-[r:Demand{max_demand: 10.8306, current_input: 7.627091999405724, level: 11}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:457}) CREATE (b)-[r:Supply{max_supply: 324.918, current_output: 276.86537999108583,level: 11}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:457}) CREATE (b)-[r:Supply{max_supply: 162.459, current_output: 138.43268999554292,level: 11}]->(g);
CREATE (n: Building {id: 458, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:458}) CREATE (g)-[r:Demand{max_demand: 25.296, current_input: 16.097726758941636, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:458}) CREATE (g)-[r:Demand{max_demand: 25.296, current_input: 17.813871735357893, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:458}) CREATE (b)-[r:Supply{max_supply: 101.184, current_output: 67.82319698859905,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:458}) CREATE (b)-[r:Supply{max_supply: 12.648, current_output: 8.477899623574881,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:458}) CREATE (b)-[r:Supply{max_supply: 63.239999999999995, current_output: 42.3894981178744,level: 3}]->(g);
CREATE (n: Building {id: 459, name:"building_barrackslevel", level:27});
MATCH (g: Goods{code: 0}), (b: Building{id:459}) CREATE (g)-[r:Demand{max_demand: 27.0, current_input: 10.05525, level: 27}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:459}) CREATE (g)-[r:Demand{max_demand: 27.0, current_input: 24.000717245500983, level: 27}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:459}) CREATE (g)-[r:Demand{max_demand: 27.0, current_input: 23.48736250802719, level: 27}]->(b);
CREATE (n: Building {id: 462, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:462}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 67.13944918986017, level: 5}]->(b);
CREATE (n: Building {id: 463, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:463}) CREATE (g)-[r:Demand{max_demand: 19.1476, current_input: 27.469477020660253, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:463}) CREATE (g)-[r:Demand{max_demand: 38.2952, current_input: 23.27892133472349, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:463}) CREATE (g)-[r:Demand{max_demand: 47.869, current_input: 40.570361194563574, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:463}) CREATE (g)-[r:Demand{max_demand: 9.5738, current_input: 6.742032148164507, level: 2}]->(b);
CREATE (n: Building {id: 464, name:"building_tooling_workshopslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:464}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 72.9457101716878, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:464}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 50.15575615316676, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:464}) CREATE (g)-[r:Demand{max_demand: 80.00000000000001, current_input: 82.79403596854479, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:464}) CREATE (b)-[r:Supply{max_supply: 320.00000000000006, current_output: 278.17396459705583,level: 4}]->(g);
CREATE (n: Building {id: 465, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:465}) CREATE (g)-[r:Demand{max_demand: 18.762592920353985, current_input: 40.06319221174194, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:465}) CREATE (g)-[r:Demand{max_demand: 3.7525132743362835, current_input: 2.6425834184951933, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:465}) CREATE (b)-[r:Supply{max_supply: 131.3381946902655, current_output: 111.91438818490687,level: 4}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:465}) CREATE (b)-[r:Supply{max_supply: 45.0302389380531, current_output: 38.37064802403518,level: 4}]->(g);
CREATE (n: Building {id: 466, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:466}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.724166666666666, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:466}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.889154535370736, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:466}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.699023151121182, level: 10}]->(b);
CREATE (n: Building {id: 470, name:"building_arms_industrylevel", level:8});
MATCH (g: Goods{code: 24}), (b: Building{id:470}) CREATE (g)-[r:Demand{max_demand: 158.66239316239316, current_input: 134.47096447789062, level: 8}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:470}) CREATE (g)-[r:Demand{max_demand: 79.33119658119658, current_input: 27.181089323690745, level: 8}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:470}) CREATE (b)-[r:Supply{max_supply: 118.99679487179488, current_output: 70.81242867197705,level: 8}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:470}) CREATE (b)-[r:Supply{max_supply: 118.99679487179488, current_output: 70.81242867197705,level: 8}]->(g);
CREATE (n: Building {id: 471, name:"building_munition_plantslevel", level:3});
MATCH (g: Goods{code: 25}), (b: Building{id:471}) CREATE (g)-[r:Demand{max_demand: 45.583794642857136, current_input: 48.38050255690564, level: 3}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:471}) CREATE (g)-[r:Demand{max_demand: 45.583794642857136, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:471}) CREATE (b)-[r:Supply{max_supply: 113.95949999999999, current_output: 56.979749999999996,level: 3}]->(g);
CREATE (n: Building {id: 472, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:472}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 129.11555139335596, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:472}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:472}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:472}) CREATE (b)-[r:Supply{max_supply: 115.79999999999998, current_output: 38.599999999999994,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:472}) CREATE (b)-[r:Supply{max_supply: 57.89999999999999, current_output: 19.299999999999997,level: 3}]->(g);
CREATE (n: Building {id: 473, name:"building_coal_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:473}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 31.6897623375674, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:473}) CREATE (b)-[r:Supply{max_supply: 179.99999999999997, current_output: 126.75904935026959,level: 3}]->(g);
CREATE (n: Building {id: 474, name:"building_wheat_farmlevel", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:474}) CREATE (g)-[r:Demand{max_demand: 39.40719658119659, current_input: 84.14498453706219, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:474}) CREATE (g)-[r:Demand{max_demand: 7.881435897435898, current_input: 5.550240677078131, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:474}) CREATE (b)-[r:Supply{max_supply: 275.8503931623932, current_output: 235.0544565816496,level: 8}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:474}) CREATE (b)-[r:Supply{max_supply: 63.05151282051282, current_output: 53.72672814699125,level: 8}]->(g);
CREATE (n: Building {id: 475, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:475}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 7.042169408348311, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:475}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 56.33735526678649,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:475}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 14.084338816696622,level: 2}]->(g);
CREATE (n: Building {id: 476, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 0}), (b: Building{id:476}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 9.310416666666665, level: 25}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:476}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 22.222886338426832, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:476}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 21.747557877802958, level: 25}]->(b);
CREATE (n: Building {id: 477, name:"building_government_administrationlevel", level:13});
MATCH (g: Goods{code: 14}), (b: Building{id:477}) CREATE (g)-[r:Demand{max_demand: 259.99999999999994, current_input: 174.56256789363638, level: 13}]->(b);
CREATE (n: Building {id: 478, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:478}) CREATE (g)-[r:Demand{max_demand: 28.7214, current_input: 41.20421553099038, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:478}) CREATE (g)-[r:Demand{max_demand: 57.4428, current_input: 34.91838200208523, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:478}) CREATE (g)-[r:Demand{max_demand: 71.8035, current_input: 60.85554179184536, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:478}) CREATE (g)-[r:Demand{max_demand: 14.3607, current_input: 10.11304822224676, level: 3}]->(b);
CREATE (n: Building {id: 479, name:"building_universitylevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:479}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 26.855779675944067, level: 4}]->(b);
CREATE (n: Building {id: 480, name:"building_arms_industrylevel", level:5});
MATCH (g: Goods{code: 24}), (b: Building{id:480}) CREATE (g)-[r:Demand{max_demand: 99.18, current_input: 84.05791688309378, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:480}) CREATE (g)-[r:Demand{max_demand: 49.59, current_input: 16.990922583428063, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:480}) CREATE (b)-[r:Supply{max_supply: 74.385, current_output: 44.26491076873122,level: 5}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:480}) CREATE (b)-[r:Supply{max_supply: 74.385, current_output: 44.26491076873122,level: 5}]->(g);
CREATE (n: Building {id: 481, name:"building_furniture_manufacturieslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:481}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 71.73086188519778, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:481}) CREATE (g)-[r:Demand{max_demand: 100.00000000000001, current_input: 60.78809180973983, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:481}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 17.131400063952476, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:481}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 17.605423520870783, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:481}) CREATE (b)-[r:Supply{max_supply: 225.00000000000003, current_output: 149.32832963688446,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:481}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 66.36814650528198,level: 5}]->(g);
CREATE (n: Building {id: 482, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:482}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 36.4728550858439, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:482}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:482}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 31.84059352844163, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:482}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 32.15761836194797,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:482}) CREATE (b)-[r:Supply{max_supply: 74.99999999999999, current_output: 40.197022952434956,level: 3}]->(g);
CREATE (n: Building {id: 483, name:"building_paper_millslevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:483}) CREATE (g)-[r:Demand{max_demand: 210.0, current_input: 127.65499280045364, level: 7}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:483}) CREATE (g)-[r:Demand{max_demand: 70.00000000000001, current_input: 80.87444489303658, level: 7}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:483}) CREATE (g)-[r:Demand{max_demand: 35.00000000000001, current_input: 43.88628663402092, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:483}) CREATE (g)-[r:Demand{max_demand: 35.00000000000001, current_input: 24.647592929219098, level: 7}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:483}) CREATE (b)-[r:Supply{max_supply: 490.0, current_output: 405.7319877191981,level: 7}]->(g);
CREATE (n: Building {id: 484, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 0}), (b: Building{id:484}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 9.310416666666665, level: 25}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:484}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 22.222886338426832, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:484}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 21.747557877802958, level: 25}]->(b);
CREATE (n: Building {id: 490, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:490}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 491, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:491}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.8889154535370735, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:491}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.8699023151121182, level: 1}]->(b);
CREATE (n: Building {id: 543, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:543}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 20.14183475695805, level: 3}]->(b);
CREATE (n: Building {id: 544, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:544}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.713944918986017, level: 1}]->(b);
CREATE (n: Building {id: 545, name:"building_glassworkslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:545}) CREATE (g)-[r:Demand{max_demand: 100.00000000000001, current_input: 60.78809180973983, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:545}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:545}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 53.06765588073606, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:545}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 53.596030603246625,level: 5}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:545}) CREATE (b)-[r:Supply{max_supply: 125.00000000000001, current_output: 66.99503825405827,level: 5}]->(g);
CREATE (n: Building {id: 546, name:"building_coal_minelevel", level:7});
MATCH (g: Goods{code: 33}), (b: Building{id:546}) CREATE (g)-[r:Demand{max_demand: 104.99999999999999, current_input: 73.94277878765728, level: 7}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:546}) CREATE (b)-[r:Supply{max_supply: 419.99999999999994, current_output: 295.7711151506291,level: 7}]->(g);
CREATE (n: Building {id: 547, name:"building_sulfur_minelevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:547}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.084338816696624, level: 4}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:547}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 56.337355266786496,level: 4}]->(g);
CREATE (n: Building {id: 548, name:"building_rye_farmlevel", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:548}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 228.6,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:548}) CREATE (b)-[r:Supply{max_supply: 270.0, current_output: 342.9,level: 18}]->(g);
CREATE (n: Building {id: 549, name:"building_livestock_ranchlevel", level:17});
MATCH (g: Goods{code: 9}), (b: Building{id:549}) CREATE (b)-[r:Supply{max_supply: 510.0, current_output: 642.6,level: 17}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:549}) CREATE (b)-[r:Supply{max_supply: 85.0, current_output: 107.1,level: 17}]->(g);
CREATE (n: Building {id: 550, name:"building_barrackslevel", level:28});
MATCH (g: Goods{code: 1}), (b: Building{id:550}) CREATE (g)-[r:Demand{max_demand: 28.0, current_input: 24.889632699038057, level: 28}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:550}) CREATE (g)-[r:Demand{max_demand: 28.0, current_input: 24.357264823139314, level: 28}]->(b);
CREATE (n: Building {id: 563, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:563}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 172.15406852447464, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:563}) CREATE (b)-[r:Supply{max_supply: 134.99999999999997, current_output: 134.99999999999997,level: 3}]->(g);
CREATE (n: Building {id: 100663861, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:100663861}) CREATE (g)-[r:Demand{max_demand: 34.99229999999999, current_input: 37.139186697509594, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:100663861}) CREATE (b)-[r:Supply{max_supply: 69.98459999999999, current_output: 69.98459999999999,level: 1}]->(g);
CREATE (n: Building {id: 575, name:"building_rye_farmlevel", level:13});
MATCH (g: Goods{code: 7}), (b: Building{id:575}) CREATE (b)-[r:Supply{max_supply: 130.0, current_output: 158.6,level: 13}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:575}) CREATE (b)-[r:Supply{max_supply: 195.0, current_output: 237.9,level: 13}]->(g);
CREATE (n: Building {id: 576, name:"building_livestock_ranchlevel", level:12});
MATCH (g: Goods{code: 9}), (b: Building{id:576}) CREATE (b)-[r:Supply{max_supply: 360.00000000000006, current_output: 435.6,level: 12}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:576}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 72.6,level: 12}]->(g);
CREATE (n: Building {id: 577, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:577}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.56325411252247, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:577}) CREATE (b)-[r:Supply{max_supply: 179.99999999999997, current_output: 126.75904935026962,level: 3}]->(g);
CREATE (n: Building {id: 578, name:"building_barrackslevel", level:16});
MATCH (g: Goods{code: 1}), (b: Building{id:578}) CREATE (g)-[r:Demand{max_demand: 16.0, current_input: 14.222647256593175, level: 16}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:578}) CREATE (g)-[r:Demand{max_demand: 16.0, current_input: 13.918437041793892, level: 16}]->(b);
CREATE (n: Building {id: 581, name:"building_rye_farmlevel", level:11});
MATCH (g: Goods{code: 7}), (b: Building{id:581}) CREATE (b)-[r:Supply{max_supply: 110.0, current_output: 132.0,level: 11}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:581}) CREATE (b)-[r:Supply{max_supply: 165.0, current_output: 198.0,level: 11}]->(g);
CREATE (n: Building {id: 582, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:582}) CREATE (b)-[r:Supply{max_supply: 135.51000000000002, current_output: 154.4814,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:582}) CREATE (b)-[r:Supply{max_supply: 22.585, current_output: 25.7469,level: 5}]->(g);
CREATE (n: Building {id: 583, name:"building_barrackslevel", level:6});
MATCH (g: Goods{code: 1}), (b: Building{id:583}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 5.33349272122244, level: 6}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:583}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 5.219413890672709, level: 6}]->(b);
CREATE (n: Building {id: 584, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:584}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 16777802, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 67109461, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:67109461}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 14.34617237703955, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:67109461}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.236427542921945, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:67109461}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.521084704174156, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:67109461}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 50.095453610198305,level: 1}]->(g);
CREATE (n: Building {id: 1036, name:"building_textile_millslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:1036}) CREATE (g)-[r:Demand{max_demand: 320.0, current_input: 459.0775160652656, level: 8}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1036}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 0.0, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1036}) CREATE (b)-[r:Supply{max_supply: 480.00000000000006, current_output: 240.00000000000003,level: 8}]->(g);
CREATE (n: Building {id: 1037, name:"building_arms_industrylevel", level:5});
MATCH (g: Goods{code: 24}), (b: Building{id:1037}) CREATE (g)-[r:Demand{max_demand: 99.16300000000001, current_input: 84.04350889169417, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1037}) CREATE (g)-[r:Demand{max_demand: 49.581500000000005, current_input: 16.988010245417193, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1037}) CREATE (b)-[r:Supply{max_supply: 74.3722456140351, current_output: 44.25732090845499,level: 5}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:1037}) CREATE (b)-[r:Supply{max_supply: 74.3722456140351, current_output: 44.25732090845499,level: 5}]->(g);
CREATE (n: Building {id: 1038, name:"building_coal_minelevel", level:23});
MATCH (g: Goods{code: 16}), (b: Building{id:1038}) CREATE (g)-[r:Demand{max_demand: 115.0, current_input: 232.09322558964055, level: 23}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1038}) CREATE (g)-[r:Demand{max_demand: 345.0, current_input: 242.9548445880168, level: 23}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1038}) CREATE (b)-[r:Supply{max_supply: 1380.0, current_output: 1175.9096891760337,level: 23}]->(g);
CREATE (n: Building {id: 1039, name:"building_lead_minelevel", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:1039}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 62.69469519145844, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1039}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 35.21084704174156, level: 5}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1039}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 170.42169408348312,level: 5}]->(g);
CREATE (n: Building {id: 1040, name:"building_iron_minelevel", level:8});
MATCH (g: Goods{code: 23}), (b: Building{id:1040}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 150.46726845950027, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1040}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 84.50603290017976, level: 8}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1040}) CREATE (b)-[r:Supply{max_supply: 480.0, current_output: 409.0120658003595,level: 8}]->(g);
CREATE (n: Building {id: 1041, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1041}) CREATE (g)-[r:Demand{max_demand: 16.860594594594595, current_input: 10.729650726481346, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1041}) CREATE (g)-[r:Demand{max_demand: 16.860594594594595, current_input: 11.873516346061699, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1041}) CREATE (b)-[r:Supply{max_supply: 67.44239639639639, current_output: 45.20634622248553,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1041}) CREATE (b)-[r:Supply{max_supply: 8.430297297297297, current_output: 5.650791768135761,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1041}) CREATE (b)-[r:Supply{max_supply: 42.15149549549549, current_output: 28.253964879378522,level: 2}]->(g);
CREATE (n: Building {id: 1042, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:1042}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.58625, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1042}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.333731803056102, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1042}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.048534726681773, level: 15}]->(b);
CREATE (n: Building {id: 1048, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1048}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1049, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:1049}) CREATE (g)-[r:Demand{max_demand: 14.788946428571426, current_input: 31.57838609472071, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1049}) CREATE (b)-[r:Supply{max_supply: 88.73369642857142, current_output: 88.73369642857142,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1049}) CREATE (b)-[r:Supply{max_supply: 44.36684821428571, current_output: 44.36684821428571,level: 3}]->(g);
CREATE (n: Building {id: 1050, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1050}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1051, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:1051}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.724166666666666, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1051}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.889154535370736, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1051}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.699023151121182, level: 10}]->(b);
CREATE (n: Building {id: 1052, name:"building_textile_millslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1052}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 114.76937901631639, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1052}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1052}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 59.99999999999999,level: 2}]->(g);
CREATE (n: Building {id: 1053, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:1053}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 17.605423520870783, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1053}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 140.84338816696626,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1053}) CREATE (b)-[r:Supply{max_supply: 50.00000000000001, current_output: 35.210847041741566,level: 5}]->(g);
CREATE (n: Building {id: 1054, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1054}) CREATE (g)-[r:Demand{max_demand: 9.859, current_input: 21.05162189960852, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1054}) CREATE (b)-[r:Supply{max_supply: 59.15399999999999, current_output: 59.15399999999999,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1054}) CREATE (b)-[r:Supply{max_supply: 29.576999999999995, current_output: 29.576999999999995,level: 2}]->(g);
CREATE (n: Building {id: 1055, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:1055}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.724166666666666, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1055}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.889154535370736, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1055}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.699023151121182, level: 10}]->(b);
CREATE (n: Building {id: 1056, name:"building_food_industrylevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:1056}) CREATE (g)-[r:Demand{max_demand: 240.00000000000003, current_input: 152.72985539792825, level: 6}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1056}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 7.1288720994788735, level: 6}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1056}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 30.775509516955204,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1056}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 123.10203806782081,level: 6}]->(g);
CREATE (n: Building {id: 1057, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1057}) CREATE (g)-[r:Demand{max_demand: 9.859, current_input: 21.05162189960852, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1057}) CREATE (b)-[r:Supply{max_supply: 59.15399999999999, current_output: 59.15399999999999,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1057}) CREATE (b)-[r:Supply{max_supply: 19.718, current_output: 19.718,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1057}) CREATE (b)-[r:Supply{max_supply: 9.859, current_output: 9.859,level: 2}]->(g);
CREATE (n: Building {id: 1058, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1058}) CREATE (g)-[r:Demand{max_demand: 25.292098214285712, current_input: 16.09524376240857, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1058}) CREATE (g)-[r:Demand{max_demand: 25.292098214285712, current_input: 17.811124031758382, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1058}) CREATE (b)-[r:Supply{max_supply: 101.16839285714285, current_output: 67.8127355883339,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1058}) CREATE (b)-[r:Supply{max_supply: 12.646044642857142, current_output: 8.476588956150357,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1058}) CREATE (b)-[r:Supply{max_supply: 63.23025, current_output: 42.382962735100065,level: 3}]->(g);
CREATE (n: Building {id: 1059, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:1059}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.58625, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1059}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.333731803056102, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1059}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.048534726681773, level: 15}]->(b);
CREATE (n: Building {id: 218106475, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:218106475}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 21.126508225044944, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:218106475}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 253.51809870053927,level: 6}]->(g);
CREATE (n: Building {id: 2835, name:"building_subsistence_farmslevel", level:82});
MATCH (g: Goods{code: 7}), (b: Building{id:2835}) CREATE (b)-[r:Supply{max_supply: 100.66934545454544, current_output: 110.73628,level: 82}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2835}) CREATE (b)-[r:Supply{max_supply: 20.133863636363635, current_output: 22.14725,level: 82}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2835}) CREATE (b)-[r:Supply{max_supply: 20.133863636363635, current_output: 22.14725,level: 82}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2835}) CREATE (b)-[r:Supply{max_supply: 20.133863636363635, current_output: 22.14725,level: 82}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2835}) CREATE (b)-[r:Supply{max_supply: 20.133863636363635, current_output: 22.14725,level: 82}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2835}) CREATE (b)-[r:Supply{max_supply: 20.133863636363635, current_output: 22.14725,level: 82}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2835}) CREATE (b)-[r:Supply{max_supply: 28.18740909090909, current_output: 31.00615,level: 82}]->(g);
CREATE (n: Building {id: 16780076, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:16780076}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.56325411252247, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16780076}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 42.25301645008988,level: 1}]->(g);
CREATE (n: Building {id: 33557560, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:33557560}) CREATE (g)-[r:Demand{max_demand: 0.5009, current_input: 0.30448755187498683, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:33557560}) CREATE (g)-[r:Demand{max_demand: 1.0018, current_input: 1.2561509128560615, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:33557560}) CREATE (g)-[r:Demand{max_demand: 0.5009, current_input: 0.19652477718794953, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:33557560}) CREATE (b)-[r:Supply{max_supply: 7.0126, current_output: 4.67559086896037,level: 1}]->(g);
CREATE (n: Building {id: 16780573, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16780573}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.713944918986017, level: 1}]->(b);
CREATE (n: Building {id: 3773, name:"building_subsistence_farmslevel", level:127});
MATCH (g: Goods{code: 7}), (b: Building{id:3773}) CREATE (b)-[r:Supply{max_supply: 200.44091818181815, current_output: 220.48501,level: 127}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3773}) CREATE (b)-[r:Supply{max_supply: 40.088181818181816, current_output: 44.097,level: 127}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3773}) CREATE (b)-[r:Supply{max_supply: 40.088181818181816, current_output: 44.097,level: 127}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3773}) CREATE (b)-[r:Supply{max_supply: 40.088181818181816, current_output: 44.097,level: 127}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3773}) CREATE (b)-[r:Supply{max_supply: 40.088181818181816, current_output: 44.097,level: 127}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3773}) CREATE (b)-[r:Supply{max_supply: 40.088181818181816, current_output: 44.097,level: 127}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3773}) CREATE (b)-[r:Supply{max_supply: 56.123454545454535, current_output: 61.7358,level: 127}]->(g);
CREATE (n: Building {id: 3774, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3774}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.0394045904869915, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3774}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 12.53893903829169, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3774}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.9617166818521614, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3774}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 46.67189927091605,level: 1}]->(g);
CREATE (n: Building {id: 3775, name:"building_subsistence_farmslevel", level:84});
MATCH (g: Goods{code: 7}), (b: Building{id:3775}) CREATE (b)-[r:Supply{max_supply: 122.073, current_output: 134.2803,level: 84}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3775}) CREATE (b)-[r:Supply{max_supply: 24.414599999999997, current_output: 26.85606,level: 84}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3775}) CREATE (b)-[r:Supply{max_supply: 24.414599999999997, current_output: 26.85606,level: 84}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3775}) CREATE (b)-[r:Supply{max_supply: 24.414599999999997, current_output: 26.85606,level: 84}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3775}) CREATE (b)-[r:Supply{max_supply: 24.414599999999997, current_output: 26.85606,level: 84}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3775}) CREATE (b)-[r:Supply{max_supply: 24.414599999999997, current_output: 26.85606,level: 84}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3775}) CREATE (b)-[r:Supply{max_supply: 34.18043636363636, current_output: 37.59848,level: 84}]->(g);
CREATE (n: Building {id: 3776, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3776}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 15.197022952434958, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3776}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 62.69469519145844, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3776}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 9.808583409260809, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3776}) CREATE (b)-[r:Supply{max_supply: 350.0, current_output: 233.35949635458024,level: 5}]->(g);
CREATE (n: Building {id: 3779, name:"building_subsistence_farmslevel", level:78});
MATCH (g: Goods{code: 7}), (b: Building{id:3779}) CREATE (b)-[r:Supply{max_supply: 144.8421, current_output: 159.32631,level: 78}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3779}) CREATE (b)-[r:Supply{max_supply: 28.96841818181818, current_output: 31.86526,level: 78}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3779}) CREATE (b)-[r:Supply{max_supply: 28.96841818181818, current_output: 31.86526,level: 78}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3779}) CREATE (b)-[r:Supply{max_supply: 28.96841818181818, current_output: 31.86526,level: 78}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3779}) CREATE (b)-[r:Supply{max_supply: 28.96841818181818, current_output: 31.86526,level: 78}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3779}) CREATE (b)-[r:Supply{max_supply: 28.96841818181818, current_output: 31.86526,level: 78}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3779}) CREATE (b)-[r:Supply{max_supply: 40.55578181818181, current_output: 44.61136,level: 78}]->(g);
CREATE (n: Building {id: 3780, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3780}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.157618361947966, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3780}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 50.15575615316676, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3780}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.846866727408646, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3780}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 186.68759708366417,level: 4}]->(g);
CREATE (n: Building {id: 3782, name:"building_subsistence_farmslevel", level:51});
MATCH (g: Goods{code: 7}), (b: Building{id:3782}) CREATE (b)-[r:Supply{max_supply: 97.21237272727272, current_output: 106.93361,level: 51}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3782}) CREATE (b)-[r:Supply{max_supply: 19.442472727272726, current_output: 21.38672,level: 51}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3782}) CREATE (b)-[r:Supply{max_supply: 19.442472727272726, current_output: 21.38672,level: 51}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3782}) CREATE (b)-[r:Supply{max_supply: 19.442472727272726, current_output: 21.38672,level: 51}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3782}) CREATE (b)-[r:Supply{max_supply: 19.442472727272726, current_output: 21.38672,level: 51}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3782}) CREATE (b)-[r:Supply{max_supply: 19.442472727272726, current_output: 21.38672,level: 51}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3782}) CREATE (b)-[r:Supply{max_supply: 27.219463636363635, current_output: 29.94141,level: 51}]->(g);
CREATE (n: Building {id: 3783, name:"building_subsistence_farmslevel", level:46});
MATCH (g: Goods{code: 7}), (b: Building{id:3783}) CREATE (b)-[r:Supply{max_supply: 105.4067, current_output: 115.94737,level: 46}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3783}) CREATE (b)-[r:Supply{max_supply: 21.08133636363636, current_output: 23.18947,level: 46}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3783}) CREATE (b)-[r:Supply{max_supply: 21.08133636363636, current_output: 23.18947,level: 46}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3783}) CREATE (b)-[r:Supply{max_supply: 21.08133636363636, current_output: 23.18947,level: 46}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3783}) CREATE (b)-[r:Supply{max_supply: 21.08133636363636, current_output: 23.18947,level: 46}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3783}) CREATE (b)-[r:Supply{max_supply: 21.08133636363636, current_output: 23.18947,level: 46}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3783}) CREATE (b)-[r:Supply{max_supply: 29.513872727272727, current_output: 32.46526,level: 46}]->(g);
CREATE (n: Building {id: 3784, name:"building_subsistence_farmslevel", level:48});
MATCH (g: Goods{code: 7}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 75.8916, current_output: 83.48076,level: 48}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 15.17831818181818, current_output: 16.69615,level: 48}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 15.17831818181818, current_output: 16.69615,level: 48}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 15.17831818181818, current_output: 16.69615,level: 48}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 15.17831818181818, current_output: 16.69615,level: 48}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 15.17831818181818, current_output: 16.69615,level: 48}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 21.249645454545455, current_output: 23.37461,level: 48}]->(g);
CREATE (n: Building {id: 3785, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3785}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.078809180973983, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3785}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 25.07787807658338, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3785}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.923433363704323, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3785}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 93.3437985418321,level: 2}]->(g);
CREATE (n: Building {id: 3787, name:"building_subsistence_farmslevel", level:108});
MATCH (g: Goods{code: 7}), (b: Building{id:3787}) CREATE (b)-[r:Supply{max_supply: 189.7047, current_output: 208.67517,level: 108}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3787}) CREATE (b)-[r:Supply{max_supply: 37.94093636363636, current_output: 41.73503,level: 108}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3787}) CREATE (b)-[r:Supply{max_supply: 37.94093636363636, current_output: 41.73503,level: 108}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3787}) CREATE (b)-[r:Supply{max_supply: 37.94093636363636, current_output: 41.73503,level: 108}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3787}) CREATE (b)-[r:Supply{max_supply: 37.94093636363636, current_output: 41.73503,level: 108}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3787}) CREATE (b)-[r:Supply{max_supply: 37.94093636363636, current_output: 41.73503,level: 108}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3787}) CREATE (b)-[r:Supply{max_supply: 53.11730909090909, current_output: 58.42904,level: 108}]->(g);
CREATE (n: Building {id: 3788, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3788}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 15.197022952434958, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3788}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 62.69469519145844, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3788}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 9.808583409260809, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3788}) CREATE (b)-[r:Supply{max_supply: 350.0, current_output: 233.35949635458024,level: 5}]->(g);
CREATE (n: Building {id: 3789, name:"building_subsistence_farmslevel", level:124});
MATCH (g: Goods{code: 7}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 214.0736, current_output: 235.48096,level: 124}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 42.81471818181818, current_output: 47.09619,level: 124}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 42.81471818181818, current_output: 47.09619,level: 124}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 42.81471818181818, current_output: 47.09619,level: 124}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 42.81471818181818, current_output: 47.09619,level: 124}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 42.81471818181818, current_output: 47.09619,level: 124}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 59.94059999999999, current_output: 65.93466,level: 124}]->(g);
CREATE (n: Building {id: 3790, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3790}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 21.27583213340894, level: 7}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3790}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 87.77257326804184, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3790}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 13.732016772965132, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3790}) CREATE (b)-[r:Supply{max_supply: 489.99999999999994, current_output: 326.70329489641233,level: 7}]->(g);
CREATE (n: Building {id: 3791, name:"building_subsistence_farmslevel", level:72});
MATCH (g: Goods{code: 7}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 125.1144, current_output: 137.62584,level: 72}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 25.022872727272723, current_output: 27.52516,level: 72}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 25.022872727272723, current_output: 27.52516,level: 72}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 25.022872727272723, current_output: 27.52516,level: 72}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 25.022872727272723, current_output: 27.52516,level: 72}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 25.022872727272723, current_output: 27.52516,level: 72}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 35.03202727272727, current_output: 38.53523,level: 72}]->(g);
CREATE (n: Building {id: 3792, name:"building_urban_centerlevel", level:9});
MATCH (g: Goods{code: 10}), (b: Building{id:3792}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 27.354641314382924, level: 9}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3792}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 112.85045134462521, level: 9}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3792}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 17.655450136669455, level: 9}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3792}) CREATE (b)-[r:Supply{max_supply: 629.9999999999999, current_output: 420.04709343824436,level: 9}]->(g);
CREATE (n: Building {id: 3794, name:"building_subsistence_farmslevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 9.6215, current_output: 10.58365,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 1.9243, current_output: 2.11673,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 1.9243, current_output: 2.11673,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 1.9243, current_output: 2.11673,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 1.9243, current_output: 2.11673,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 1.9243, current_output: 2.11673,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 2.6940181818181816, current_output: 2.96342,level: 5}]->(g);
CREATE (n: Building {id: 3808, name:"building_subsistence_farmslevel", level:93});
MATCH (g: Goods{code: 7}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 152.055, current_output: 167.2605,level: 93}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 30.410999999999998, current_output: 33.4521,level: 93}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 30.410999999999998, current_output: 33.4521,level: 93}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 30.410999999999998, current_output: 33.4521,level: 93}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 30.410999999999998, current_output: 33.4521,level: 93}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 30.410999999999998, current_output: 33.4521,level: 93}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 42.575399999999995, current_output: 46.83294,level: 93}]->(g);
CREATE (n: Building {id: 3809, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3809}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.23642754292195, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3809}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 75.23363422975014, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3809}) CREATE (b)-[r:Supply{max_supply: 330.0, current_output: 265.3003514860707,level: 6}]->(g);
CREATE (n: Building {id: 3816, name:"building_subsistence_farmslevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 15.351999999999999, current_output: 16.8872,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 3.0704, current_output: 3.37744,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 3.0704, current_output: 3.37744,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 3.0704, current_output: 3.37744,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 3.0704, current_output: 3.37744,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 3.0704, current_output: 3.37744,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 4.298554545454546, current_output: 4.72841,level: 10}]->(g);
CREATE (n: Building {id: 3820, name:"building_subsistence_farmslevel", level:46});
MATCH (g: Goods{code: 7}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 38.438745454545455, current_output: 42.28262,level: 46}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 7.687745454545453, current_output: 8.45652,level: 46}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 7.687745454545453, current_output: 8.45652,level: 46}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 7.687745454545453, current_output: 8.45652,level: 46}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 7.687745454545453, current_output: 8.45652,level: 46}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 7.687745454545453, current_output: 8.45652,level: 46}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 10.762845454545454, current_output: 11.83913,level: 46}]->(g);
CREATE (n: Building {id: 3837, name:"building_subsistence_farmslevel", level:22});
MATCH (g: Goods{code: 7}), (b: Building{id:3837}) CREATE (b)-[r:Supply{max_supply: 0.0055, current_output: 0.00605,level: 22}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3837}) CREATE (b)-[r:Supply{max_supply: 0.0010999999999999998, current_output: 0.00121,level: 22}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3837}) CREATE (b)-[r:Supply{max_supply: 0.0010999999999999998, current_output: 0.00121,level: 22}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3837}) CREATE (b)-[r:Supply{max_supply: 0.0010999999999999998, current_output: 0.00121,level: 22}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3837}) CREATE (b)-[r:Supply{max_supply: 0.0010999999999999998, current_output: 0.00121,level: 22}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3837}) CREATE (b)-[r:Supply{max_supply: 0.0010999999999999998, current_output: 0.00121,level: 22}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3837}) CREATE (b)-[r:Supply{max_supply: 0.0015363636363636363, current_output: 0.00169,level: 22}]->(g);
CREATE (n: Building {id: 33558270, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:33558270}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3868, name:"building_trade_centerlevel", level:37});
CREATE (n: Building {id: 3869, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3869}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.078809180973983, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3869}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 25.07787807658338, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3869}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.923433363704323, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3869}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 93.3437985418321,level: 2}]->(g);
CREATE (n: Building {id: 3925, name:"building_trade_centerlevel", level:16});
CREATE (n: Building {id: 3950, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4507, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4508, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4509, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4510, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4511, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4512, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4514, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4515, name:"building_conscription_centerlevel", level:11});
CREATE (n: Building {id: 4516, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4524, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4527, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4547, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:4547}) CREATE (g)-[r:Demand{max_demand: 19.1476, current_input: 27.469477020660253, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4547}) CREATE (g)-[r:Demand{max_demand: 38.2952, current_input: 23.27892133472349, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4547}) CREATE (g)-[r:Demand{max_demand: 47.869, current_input: 40.570361194563574, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4547}) CREATE (g)-[r:Demand{max_demand: 9.5738, current_input: 6.742032148164507, level: 2}]->(b);
CREATE (n: Building {id: 4548, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:4548}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.47285508584389, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:4548}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 25.077878076583374, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:4548}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 41.397017984272374, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4548}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 139.0869822985279,level: 2}]->(g);
CREATE (n: Building {id: 4602, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:4602}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.47285508584389, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:4602}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 25.077878076583374, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:4602}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 41.397017984272374, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4602}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 139.0869822985279,level: 2}]->(g);
CREATE (n: Building {id: 4603, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:4603}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 36.47285508584389, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:4603}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 25.077878076583374, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:4603}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 41.397017984272374, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4603}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 139.0869822985279,level: 2}]->(g);
CREATE (n: Building {id: 4614, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:4614}) CREATE (g)-[r:Demand{max_demand: 9.907199999999998, current_input: 8.396638376126099, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:4614}) CREATE (g)-[r:Demand{max_demand: 9.907199999999998, current_input: 3.394484134271798, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4614}) CREATE (b)-[r:Supply{max_supply: 24.768, current_output: 14.738903137997376,level: 1}]->(g);
CREATE (n: Building {id: 4680, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:4680}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.521084704174156, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4680}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 42.253016450089866,level: 1}]->(g);
CREATE (n: Building {id: 4726, name:"building_sulfur_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:4726}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.521084704174156, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:4726}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 14.084338816696624,level: 1}]->(g);
CREATE (n: Building {id: 4734, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:4734}) CREATE (g)-[r:Demand{max_demand: 9.918399999999998, current_input: 8.406130699871719, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:4734}) CREATE (g)-[r:Demand{max_demand: 9.918399999999998, current_input: 3.398321567886124, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:4734}) CREATE (g)-[r:Demand{max_demand: 14.877599999999997, current_input: 15.397206869070267, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4734}) CREATE (b)-[r:Supply{max_supply: 9.918399999999998, current_output: 7.240950755919281,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:4734}) CREATE (b)-[r:Supply{max_supply: 24.796, current_output: 18.102376889798204,level: 1}]->(g);
CREATE (n: Building {id: 4878, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:4878}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 37.61681711487507, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4878}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.12650822504494, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4878}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 102.25301645008987,level: 2}]->(g);
CREATE (n: Building {id: 4887, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:4887}) CREATE (g)-[r:Demand{max_demand: 9.863, current_input: 21.060162977567586, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4887}) CREATE (b)-[r:Supply{max_supply: 98.62999999999998, current_output: 98.62999999999998,level: 2}]->(g);
CREATE (n: Building {id: 16782117, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:16782117}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 40.2836695139161, level: 3}]->(b);
CREATE (n: Building {id: 16782218, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:16782218}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.12650822504494, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16782218}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 84.50603290017976,level: 2}]->(g);
CREATE (n: Building {id: 5007, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5007}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.236427542921945, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:5007}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 11.553492127576652, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:5007}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 56.27583213340894,level: 1}]->(g);
CREATE (n: Building {id: 50336676, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:50336676}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 172.15406852447464, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:50336676}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:50336676}) CREATE (b)-[r:Supply{max_supply: 179.99999999999997, current_output: 89.99999999999999,level: 3}]->(g);
CREATE (n: Building {id: 33559493, name:"building_food_industrylevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:33559493}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 25.454975899654706, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:33559493}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 28.636847887111536,level: 1}]->(g);
CREATE (n: Building {id: 16782377, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:16782377}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.521084704174156, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782377}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 42.253016450089866,level: 1}]->(g);
CREATE (n: Building {id: 419435599, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:419435599}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 54.70928262876584, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:419435599}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 54.70928262876584,level: 3}]->(g);
CREATE (n: Building {id: 50336860, name:"building_tooling_workshopslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:50336860}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 54.70928262876584, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:50336860}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 37.61681711487507, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:50336860}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 62.095526976408586, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50336860}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 208.63047344779184,level: 3}]->(g);
CREATE (n: Building {id: 67114111, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:67114111}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.521084704174156, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:67114111}) CREATE (b)-[r:Supply{max_supply: 24.999999999999996, current_output: 17.60542352087078,level: 1}]->(g);
CREATE (n: Building {id: 16782763, name:"building_railwaylevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:16782763}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 25.07787807658338, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16782763}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.609509996434525, level: 2}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:16782763}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 79.02377499108631,level: 2}]->(g);
CREATE (n: Building {id: 33559997, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:33559997}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 37.61681711487506, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33559997}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 33.90115623435925, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:33559997}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 60.04468944041689,level: 1}]->(g);
CREATE (n: Building {id: 234886623, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:234886623}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 7.042169408348311, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:234886623}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 84.50603290017973,level: 2}]->(g);
CREATE (n: Building {id: 16782847, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:16782847}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 37.61681711487506, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782847}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 33.90115623435925, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782847}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 60.04468944041689,level: 1}]->(g);
CREATE (n: Building {id: 16782951, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:16782951}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.157618361947966, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16782951}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 50.15575615316676, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:16782951}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.846866727408646, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:16782951}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 186.68759708366417,level: 4}]->(g);
CREATE (n: Building {id: 5771, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5771}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.0394045904869915, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5771}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 12.53893903829169, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5771}) CREATE (b)-[r:Supply{max_supply: 55.0, current_output: 44.216725247678454,level: 1}]->(g);
CREATE (n: Building {id: 16783004, name:"building_steel_millslevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:16783004}) CREATE (g)-[r:Demand{max_demand: 92.98694642857143, current_input: 116.59576526247523, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783004}) CREATE (g)-[r:Demand{max_demand: 106.27079464285714, current_input: 90.06757030842522, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783004}) CREATE (g)-[r:Demand{max_demand: 13.283848214285713, current_input: 9.354710951978522, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16783004}) CREATE (b)-[r:Supply{max_supply: 172.69004464285712, current_output: 146.88703472759326,level: 3}]->(g);
CREATE (n: Building {id: 117446307, name:"building_motor_industrylevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:117446307}) CREATE (g)-[r:Demand{max_demand: 2.341642857142857, current_input: 2.9361717035165458, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:117446307}) CREATE (g)-[r:Demand{max_demand: 14.049892857142856, current_input: 14.540591682106074, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:117446307}) CREATE (g)-[r:Demand{max_demand: 2.341642857142857, current_input: 1.6490245693848766, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:117446307}) CREATE (b)-[r:Supply{max_supply: 18.733196428571425, current_output: 16.886209046043135,level: 3}]->(g);
CREATE (n: Building {id: 16783019, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783019}) CREATE (g)-[r:Demand{max_demand: 9.5738, current_input: 13.734738510330127, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783019}) CREATE (g)-[r:Demand{max_demand: 19.1476, current_input: 11.639460667361744, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783019}) CREATE (g)-[r:Demand{max_demand: 23.9345, current_input: 20.285180597281787, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783019}) CREATE (g)-[r:Demand{max_demand: 4.7869, current_input: 3.3710160740822537, level: 1}]->(b);
CREATE (n: Building {id: 83891885, name:"building_railwaylevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:83891885}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 25.07787807658338, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:83891885}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.609509996434525, level: 2}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:83891885}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 79.02377499108631,level: 2}]->(g);
CREATE (n: Building {id: 16783031, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16783031}) CREATE (g)-[r:Demand{max_demand: 19.1476, current_input: 27.469477020660253, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783031}) CREATE (g)-[r:Demand{max_demand: 38.2952, current_input: 23.27892133472349, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783031}) CREATE (g)-[r:Demand{max_demand: 47.869, current_input: 40.570361194563574, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783031}) CREATE (g)-[r:Demand{max_demand: 9.5738, current_input: 6.742032148164507, level: 2}]->(b);
CREATE (n: Building {id: 117446409, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:117446409}) CREATE (g)-[r:Demand{max_demand: 9.5738, current_input: 13.734738510330127, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:117446409}) CREATE (g)-[r:Demand{max_demand: 19.1476, current_input: 11.639460667361744, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:117446409}) CREATE (g)-[r:Demand{max_demand: 23.9345, current_input: 20.285180597281787, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:117446409}) CREATE (g)-[r:Demand{max_demand: 4.7869, current_input: 3.3710160740822537, level: 1}]->(b);
CREATE (n: Building {id: 5923, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5923}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 18.236427542921945, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:5923}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 20.698508992136194, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5923}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 64.31523672389594,level: 1}]->(g);
CREATE (n: Building {id: 16783243, name:"building_motor_industrylevel", level:1});
MATCH (g: Goods{code: 30}), (b: Building{id:16783243}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 31.047763488204286, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16783243}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 1}]->(g);
CREATE (n: Building {id: 6042, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:6042}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.713944918986017, level: 1}]->(b);
CREATE (n: Building {id: 16783290, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:16783290}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 12.53893903829169, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16783290}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.804754998217263, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:16783290}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 39.51188749554316,level: 1}]->(g);
CREATE (n: Building {id: 6104, name:"building_chemical_plantslevel", level:3});
MATCH (g: Goods{code: 22}), (b: Building{id:6104}) CREATE (g)-[r:Demand{max_demand: 5.686196428571428, current_input: 6.569542567335446, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:6104}) CREATE (g)-[r:Demand{max_demand: 1.895392857142857, current_input: 1.6064002343872144, level: 3}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:6104}) CREATE (b)-[r:Supply{max_supply: 17.058598214285713, current_output: 15.758126647163628,level: 3}]->(g);
CREATE (n: Building {id: 83892200, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:83892200}) CREATE (g)-[r:Demand{max_demand: 9.5738, current_input: 13.734738510330127, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:83892200}) CREATE (g)-[r:Demand{max_demand: 19.1476, current_input: 11.639460667361744, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:83892200}) CREATE (g)-[r:Demand{max_demand: 23.9345, current_input: 20.285180597281787, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:83892200}) CREATE (g)-[r:Demand{max_demand: 4.7869, current_input: 3.3710160740822537, level: 1}]->(b);
CREATE (n: Building {id: 6148, name:"building_railwaylevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:6148}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 37.61681711487507, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:6148}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 17.414264994651788, level: 3}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:6148}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 118.53566248662948,level: 3}]->(g);
CREATE (n: Building {id: 33560590, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:33560590}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 13.427889837972034, level: 1}]->(b);
CREATE (n: Building {id: 16783464, name:"building_trade_centerlevel", level:9});
CREATE (n: Building {id: 6355, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:6355}) CREATE (g)-[r:Demand{max_demand: 9.5738, current_input: 13.734738510330127, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:6355}) CREATE (g)-[r:Demand{max_demand: 19.1476, current_input: 11.639460667361744, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:6355}) CREATE (g)-[r:Demand{max_demand: 23.9345, current_input: 20.285180597281787, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:6355}) CREATE (g)-[r:Demand{max_demand: 4.7869, current_input: 3.3710160740822537, level: 1}]->(b);
CREATE (n: Building {id: 6356, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:6356}) CREATE (g)-[r:Demand{max_demand: 9.5738, current_input: 13.734738510330127, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:6356}) CREATE (g)-[r:Demand{max_demand: 19.1476, current_input: 11.639460667361744, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:6356}) CREATE (g)-[r:Demand{max_demand: 23.9345, current_input: 20.285180597281787, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:6356}) CREATE (g)-[r:Demand{max_demand: 4.7869, current_input: 3.3710160740822537, level: 1}]->(b);
CREATE (n: Building {id: 6357, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:6357}) CREATE (g)-[r:Demand{max_demand: 9.5738, current_input: 13.734738510330127, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:6357}) CREATE (g)-[r:Demand{max_demand: 19.1476, current_input: 11.639460667361744, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:6357}) CREATE (g)-[r:Demand{max_demand: 23.9345, current_input: 20.285180597281787, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:6357}) CREATE (g)-[r:Demand{max_demand: 4.7869, current_input: 3.3710160740822537, level: 1}]->(b);
CREATE (n: Building {id: 6358, name:"building_railwaylevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:6358}) CREATE (g)-[r:Demand{max_demand: 18.0, current_input: 22.570090268925043, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:6358}) CREATE (g)-[r:Demand{max_demand: 18.0, current_input: 10.448558996791073, level: 3}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:6358}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 71.12139749197769,level: 3}]->(g);
CREATE (n: Building {id: 6362, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6362}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.30394045904869915, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6362}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.253893903829169, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:6362}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.19617166818521617, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6362}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 4.667189927091605,level: 1}]->(g);
