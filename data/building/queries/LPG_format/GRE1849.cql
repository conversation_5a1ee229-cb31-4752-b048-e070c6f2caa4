CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:52.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:17.76229188491277, pop_demand:121.07354888323897});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:26.365794218006805, pop_demand:227.2576237689564});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:10.23801883965356, pop_demand:40.50237381825401});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:11.434044245253839, pop_demand:24.37744534594767});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:29.756993075327436, pop_demand:50.33990741407413});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:49.49172896483074, pop_demand:44.050028554871886});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:20.304677049889357, pop_demand:36.06595499999999});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:73.86567267443873, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:52.5, pop_demand:1.6988222238655415});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:1.4527392857142853});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:23.322498577863453, pop_demand:11.81413227147138});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:31.78087606947741, pop_demand:8.954053782491213});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:19.262771381578954, pop_demand:8.223530000000002});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:52.5, pop_demand:1.420875300358087});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:44.472755642036546, pop_demand:78.49842591313157});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:6.779449999999998});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:1.6948624999999995});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 893, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:893}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.4593697028401427, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:893}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 34.59369702840143,level: 1}]->(g);
CREATE (n: Building {id: 921, name:"building_government_administrationlevel", level:4});
CREATE (n: Building {id: 922, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:922}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 57.273562616632724, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:922}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 93.2539437294977, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:922}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:922}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 923, name:"building_wheat_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:923}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 85.5,level: 5}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:923}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 45.6,level: 5}]->(g);
CREATE (n: Building {id: 924, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:924}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.4593697028401427, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:924}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 34.59369702840143,level: 1}]->(g);
CREATE (n: Building {id: 925, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:925}) CREATE (b)-[r:Supply{max_supply: 44.220495049504954, current_output: 44.6627,level: 2}]->(g);
CREATE (n: Building {id: 926, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:926}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 171.0,level: 5}]->(g);
CREATE (n: Building {id: 927, name:"building_cotton_plantationlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:927}) CREATE (b)-[r:Supply{max_supply: 141.49759375, current_output: 181.11692,level: 4}]->(g);
CREATE (n: Building {id: 928, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:928}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.4593697028401427, level: 1}]->(b);
CREATE (n: Building {id: 929, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:929}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:929}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 930, name:"building_naval_baselevel", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:930}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 6.0, level: 2}]->(b);
CREATE (n: Building {id: 931, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:931}) CREATE (b)-[r:Supply{max_supply: 3.0599999999999996, current_output: 3.3966,level: 2}]->(g);
CREATE (n: Building {id: 932, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:932}) CREATE (g)-[r:Demand{max_demand: 8.907, current_input: 6.162521188639429, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:932}) CREATE (b)-[r:Supply{max_supply: 89.07000000000001, current_output: 61.6252118863943,level: 2}]->(g);
CREATE (n: Building {id: 933, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:933}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.4593697028401427, level: 1}]->(b);
CREATE (n: Building {id: 934, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:934}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:934}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 935, name:"building_naval_baselevel", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:935}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 9.0, level: 3}]->(b);
CREATE (n: Building {id: 2949, name:"building_subsistence_farmslevel", level:21});
MATCH (g: Goods{code: 7}), (b: Building{id:2949}) CREATE (b)-[r:Supply{max_supply: 0.0015727272727272725, current_output: 0.00173,level: 21}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2949}) CREATE (b)-[r:Supply{max_supply: 0.0003090909090909091, current_output: 0.00034,level: 21}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2949}) CREATE (b)-[r:Supply{max_supply: 0.0003090909090909091, current_output: 0.00034,level: 21}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2949}) CREATE (b)-[r:Supply{max_supply: 0.0003090909090909091, current_output: 0.00034,level: 21}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2949}) CREATE (b)-[r:Supply{max_supply: 0.0003090909090909091, current_output: 0.00034,level: 21}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2949}) CREATE (b)-[r:Supply{max_supply: 0.0003090909090909091, current_output: 0.00034,level: 21}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2949}) CREATE (b)-[r:Supply{max_supply: 0.0004363636363636363, current_output: 0.00048,level: 21}]->(g);
CREATE (n: Building {id: 3382, name:"building_subsistence_farmslevel", level:34});
MATCH (g: Goods{code: 7}), (b: Building{id:3382}) CREATE (b)-[r:Supply{max_supply: 26.769045454545452, current_output: 29.44595,level: 34}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3382}) CREATE (b)-[r:Supply{max_supply: 5.353809090909091, current_output: 5.88919,level: 34}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3382}) CREATE (b)-[r:Supply{max_supply: 5.353809090909091, current_output: 5.88919,level: 34}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3382}) CREATE (b)-[r:Supply{max_supply: 5.353809090909091, current_output: 5.88919,level: 34}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3382}) CREATE (b)-[r:Supply{max_supply: 5.353809090909091, current_output: 5.88919,level: 34}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3382}) CREATE (b)-[r:Supply{max_supply: 5.353809090909091, current_output: 5.88919,level: 34}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3382}) CREATE (b)-[r:Supply{max_supply: 7.4953272727272715, current_output: 8.24486,level: 34}]->(g);
CREATE (n: Building {id: 3383, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3383}) CREATE (g)-[r:Demand{max_demand: 11.496892156862744, current_input: 26.803263356504544, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3383}) CREATE (b)-[r:Supply{max_supply: 57.4845, current_output: 57.4845,level: 3}]->(g);
CREATE (n: Building {id: 3564, name:"building_subsistence_orchardslevel", level:7});
CREATE (n: Building {id: 3914, name:"building_trade_centerlevel", level:14});
CREATE (n: Building {id: 4236, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4697, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:4697}) CREATE (b)-[r:Supply{max_supply: 22.13225, current_output: 22.13225,level: 1}]->(g);
CREATE (n: Building {id: 4902, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:4902}) CREATE (b)-[r:Supply{max_supply: 21.779999999999998, current_output: 23.958,level: 1}]->(g);
CREATE (n: Building {id: 16782330, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:16782330}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 100.8,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:16782330}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 16.8,level: 3}]->(g);
CREATE (n: Building {id: 16783436, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783436}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 114.54712523326545, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:16783436}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 1}]->(g);
