CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:30.138481258084944, pop_demand:422.74906446013455});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:7.483523935459777, pop_demand:23.346317084810938});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:29.30453783076426, pop_demand:20.996493589031168});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:32.25480674931399, pop_demand:55.30244141096876});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:52.5, pop_demand:28.771062941814126});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:49.857891448297345, pop_demand:164.90357662224946});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:48.93073897809612, pop_demand:144.23834272763673});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:52.5, pop_demand:8.163452585006597});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:42.794484391320275, pop_demand:45.97319010901794});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:70.0, pop_demand:1.308483315052971});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:20.46557367880829});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:52.5, pop_demand:11.53283964763599});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:52.5, pop_demand:7.292957472897206});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:37.17030978166165, pop_demand:128.29484790549026});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:87.5, pop_demand:13.296093106787637});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:30.724651893212368});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:52.5, pop_demand:3.264625745347869});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:70.0, pop_demand:3.228219110421897});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:87.5, pop_demand:10.359808968368332});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:6.309962731294907});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:93.30620867998815, pop_demand:21.654197143428746});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:350.0, pop_demand:0.022218733647305075});
CREATE (n: Building {id: 70, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:70}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 71, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:71}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.796974779490494, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:71}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.8301288337906714, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:71}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:71}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:71}) CREATE (b)-[r:Supply{max_supply: 24.999999999999996, current_output: 3.5169397583007282,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:71}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 5.627103613281166,level: 1}]->(g);
CREATE (n: Building {id: 72, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:72}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 73, name:"building_rye_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:73}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:73}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
CREATE (n: Building {id: 74, name:"building_logging_camplevel", level:9});
MATCH (g: Goods{code: 33}), (b: Building{id:74}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 0.0, level: 9}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:74}) CREATE (b)-[r:Supply{max_supply: 540.0, current_output: 0.0,level: 9}]->(g);
CREATE (n: Building {id: 75, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:75}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:75}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 10}]->(b);
CREATE (n: Building {id: 76, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:76}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 77, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:77}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 78, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:78}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 79, name:"building_rye_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:79}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:79}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
CREATE (n: Building {id: 80, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:80}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:80}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 0.0,level: 5}]->(g);
CREATE (n: Building {id: 81, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:81}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 82, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:82}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 60.5,level: 2}]->(g);
CREATE (n: Building {id: 83, name:"building_logging_camplevel", level:7});
MATCH (g: Goods{code: 33}), (b: Building{id:83}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 0.0, level: 7}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:83}) CREATE (b)-[r:Supply{max_supply: 420.00000000000006, current_output: 0.0,level: 7}]->(g);
CREATE (n: Building {id: 84, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:84}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.2,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:84}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
CREATE (n: Building {id: 3669, name:"building_subsistence_farmslevel", level:57});
MATCH (g: Goods{code: 7}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 57.9405, current_output: 57.9405,level: 57}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 11.5881, current_output: 11.5881,level: 57}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 11.5881, current_output: 11.5881,level: 57}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 11.5881, current_output: 11.5881,level: 57}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 11.5881, current_output: 11.5881,level: 57}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 11.5881, current_output: 11.5881,level: 57}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 16.22334, current_output: 16.22334,level: 57}]->(g);
CREATE (n: Building {id: 3670, name:"building_subsistence_farmslevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:3670}) CREATE (b)-[r:Supply{max_supply: 3.033, current_output: 3.033,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3670}) CREATE (b)-[r:Supply{max_supply: 0.6066, current_output: 0.6066,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3670}) CREATE (b)-[r:Supply{max_supply: 0.6066, current_output: 0.6066,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3670}) CREATE (b)-[r:Supply{max_supply: 0.6066, current_output: 0.6066,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3670}) CREATE (b)-[r:Supply{max_supply: 0.6066, current_output: 0.6066,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3670}) CREATE (b)-[r:Supply{max_supply: 0.6066, current_output: 0.6066,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3670}) CREATE (b)-[r:Supply{max_supply: 0.84924, current_output: 0.84924,level: 4}]->(g);
CREATE (n: Building {id: 3671, name:"building_subsistence_farmslevel", level:33});
MATCH (g: Goods{code: 7}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 9.5238, current_output: 9.5238,level: 33}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 1.90476, current_output: 1.90476,level: 33}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 1.90476, current_output: 1.90476,level: 33}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 1.90476, current_output: 1.90476,level: 33}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 1.90476, current_output: 1.90476,level: 33}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 1.90476, current_output: 1.90476,level: 33}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 2.66666, current_output: 2.66666,level: 33}]->(g);
CREATE (n: Building {id: 3672, name:"building_subsistence_farmslevel", level:28});
MATCH (g: Goods{code: 7}), (b: Building{id:3672}) CREATE (b)-[r:Supply{max_supply: 26.3144, current_output: 26.3144,level: 28}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3672}) CREATE (b)-[r:Supply{max_supply: 5.26288, current_output: 5.26288,level: 28}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3672}) CREATE (b)-[r:Supply{max_supply: 5.26288, current_output: 5.26288,level: 28}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3672}) CREATE (b)-[r:Supply{max_supply: 5.26288, current_output: 5.26288,level: 28}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3672}) CREATE (b)-[r:Supply{max_supply: 5.26288, current_output: 5.26288,level: 28}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3672}) CREATE (b)-[r:Supply{max_supply: 5.26288, current_output: 5.26288,level: 28}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3672}) CREATE (b)-[r:Supply{max_supply: 7.36803, current_output: 7.36803,level: 28}]->(g);
CREATE (n: Building {id: 553652116, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:553652116}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:553652116}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 4445, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4446, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4447, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4750, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:4750}) CREATE (g)-[r:Demand{max_demand: 4.96, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4750}) CREATE (b)-[r:Supply{max_supply: 19.84, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 5486, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5486}) CREATE (g)-[r:Demand{max_demand: 0.51, current_input: 0.09333657052332425, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5486}) CREATE (b)-[r:Supply{max_supply: 2.55, current_output: 0.46668285261662124,level: 1}]->(g);
CREATE (n: Building {id: 50337366, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:50337366}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50337366}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 0.0,level: 2}]->(g);
CREATE (n: Building {id: 6043, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:6043}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.593949558980988, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:6043}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 7.3205153351626855, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:6043}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:6043}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 6462, name:"building_subsistence_fishing_villageslevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:6462}) CREATE (b)-[r:Supply{max_supply: 0.0018, current_output: 0.0018,level: 4}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:6462}) CREATE (b)-[r:Supply{max_supply: 0.0072, current_output: 0.0072,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6462}) CREATE (b)-[r:Supply{max_supply: 0.0009, current_output: 0.0009,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6462}) CREATE (b)-[r:Supply{max_supply: 0.0027, current_output: 0.0027,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6462}) CREATE (b)-[r:Supply{max_supply: 0.0018, current_output: 0.0018,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6462}) CREATE (b)-[r:Supply{max_supply: 0.0018, current_output: 0.0018,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6462}) CREATE (b)-[r:Supply{max_supply: 0.0018, current_output: 0.0018,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6462}) CREATE (b)-[r:Supply{max_supply: 0.00252, current_output: 0.00252,level: 4}]->(g);
