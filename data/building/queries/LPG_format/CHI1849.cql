CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:14.470752760743686, pop_demand:35693.8291011604});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:24.506049635128875, pop_demand:961.8656844222608});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:11.795618469575055, pop_demand:3735.3340512178556});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:17.144324770671343, pop_demand:2778.295288782143});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:33.01724168338546, pop_demand:10871.788764857296});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:30.348132182857555, pop_demand:10123.056100171438});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:36.011066568646925, pop_demand:326.1928258495359});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:12.592418203048766, pop_demand:2878.7631772222194});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:43.125, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:12.11384935437668, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:27.07858095462436, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:66.18448426212497, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:61.2352976789451, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:48.707737668801926, pop_demand:303.5635235347279});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:62.696081600608224, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:78.83138270595909, pop_demand:1352.5159178341962});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:39.610868245854675, pop_demand:506.069337354683});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:23.102613099199022, pop_demand:1188.0431006956665});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:20.062046326842562, pop_demand:7045.630878010201});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:33.08583285254297, pop_demand:2834.397939371947});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:87.5, pop_demand:18.718665628052822});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:11.549932533011125, pop_demand:206.65793994588807});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:52.87223529852078, pop_demand:741.2801098851786});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:81.14532439136048, pop_demand:2340.0347107857424});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:75.12380764913605, pop_demand:481.98913137549096});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:84.19677233978803, pop_demand:1209.583153234608});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:350.0, pop_demand:0.08529166666666667});
CREATE (n: Building {id: 33554984, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33554984}) CREATE (b)-[r:Supply{max_supply: 35.3724, current_output: 44.2155,level: 1}]->(g);
CREATE (n: Building {id: 16777773, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16777773}) CREATE (g)-[r:Demand{max_demand: 0.504, current_input: 0.6225129972981085, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:16777773}) CREATE (g)-[r:Demand{max_demand: 0.504, current_input: 0.35771000716412765, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:16777773}) CREATE (b)-[r:Supply{max_supply: 4.032, current_output: 3.4468400286565104,level: 1}]->(g);
CREATE (n: Building {id: 335544880, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:335544880}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 14.39250900888994, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:335544880}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 16.107276776121378, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:335544880}) CREATE (g)-[r:Demand{max_demand: 16.301, current_input: 2.073224141225753, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:335544880}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 0.7937411807528658, level: 2}]->(b);
CREATE (n: Building {id: 318768424, name:"building_construction_sectorlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:318768424}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 28.78501801777988, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:318768424}) CREATE (g)-[r:Demand{max_demand: 26.0816, current_input: 32.214553552242755, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:318768424}) CREATE (g)-[r:Demand{max_demand: 32.602, current_input: 4.146448282451506, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:318768424}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 1.5874823615057316, level: 4}]->(b);
CREATE (n: Building {id: 16778937, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:16778937}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 33.6,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:16778937}) CREATE (b)-[r:Supply{max_supply: 26.999999999999996, current_output: 30.24,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:16778937}) CREATE (b)-[r:Supply{max_supply: 18.0, current_output: 20.16,level: 3}]->(g);
CREATE (n: Building {id: 33556206, name:"building_cotton_plantationlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:33556206}) CREATE (b)-[r:Supply{max_supply: 70.74479365079365, current_output: 89.13844,level: 2}]->(g);
CREATE (n: Building {id: 2104, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2104}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
CREATE (n: Building {id: 2318, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2318}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 100.8,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2318}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 16.8,level: 3}]->(g);
CREATE (n: Building {id: 2319, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2319}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 82.8,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2319}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 13.8,level: 3}]->(g);
CREATE (n: Building {id: 2320, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2320}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 73.28414858379145, level: 10}]->(b);
CREATE (n: Building {id: 2321, name:"building_logging_camplevel", level:9});
MATCH (g: Goods{code: 33}), (b: Building{id:2321}) CREATE (g)-[r:Demand{max_demand: 45.00000000000001, current_input: 10.955877901318619, level: 9}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2321}) CREATE (b)-[r:Supply{max_supply: 360.00000000000006, current_output: 87.64702321054895,level: 9}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2321}) CREATE (b)-[r:Supply{max_supply: 90.00000000000001, current_output: 21.911755802637238,level: 9}]->(g);
CREATE (n: Building {id: 2322, name:"building_opium_plantationlevel", level:6});
MATCH (g: Goods{code: 44}), (b: Building{id:2322}) CREATE (b)-[r:Supply{max_supply: 106.1328, current_output: 111.43944,level: 6}]->(g);
CREATE (n: Building {id: 2323, name:"building_paper_millslevel", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:2323}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 296.4347606181469, level: 8}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2323}) CREATE (b)-[r:Supply{max_supply: 319.99999999999994, current_output: 319.99999999999994,level: 8}]->(g);
CREATE (n: Building {id: 2324, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 2}), (b: Building{id:2324}) CREATE (g)-[r:Demand{max_demand: 12.5, current_input: 0.0, level: 25}]->(b);
CREATE (n: Building {id: 2325, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2325}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.985244575137436, level: 3}]->(b);
CREATE (n: Building {id: 2326, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:2326}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 4.8692790672527195, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2326}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 38.954232538021756,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2326}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 9.738558134505439,level: 4}]->(g);
CREATE (n: Building {id: 2327, name:"building_tea_plantationlevel", level:12});
MATCH (g: Goods{code: 40}), (b: Building{id:2327}) CREATE (b)-[r:Supply{max_supply: 212.46239639639637, current_output: 235.83326,level: 12}]->(g);
CREATE (n: Building {id: 2328, name:"building_opium_plantationlevel", level:10});
MATCH (g: Goods{code: 44}), (b: Building{id:2328}) CREATE (b)-[r:Supply{max_supply: 177.172, current_output: 193.11748,level: 10}]->(g);
CREATE (n: Building {id: 2329, name:"building_barrackslevel", level:12});
CREATE (n: Building {id: 2330, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:2330}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 29.313659433516584, level: 4}]->(b);
CREATE (n: Building {id: 2331, name:"building_furniture_manufacturieslevel", level:7});
MATCH (g: Goods{code: 9}), (b: Building{id:2331}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 154.51132302041222, level: 7}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2331}) CREATE (g)-[r:Demand{max_demand: 140.0, current_input: 172.92027702725238, level: 7}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2331}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 20.450972803765065, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2331}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 8.52123836769226, level: 7}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2331}) CREATE (b)-[r:Supply{max_supply: 314.99999999999994, current_output: 199.68013073154327,level: 7}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2331}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 88.7467247695748,level: 7}]->(g);
CREATE (n: Building {id: 2332, name:"building_glassworkslevel", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:2332}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 296.4347606181469, level: 8}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2332}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 210.79034666666666, level: 8}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2332}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 79.99999999999999,level: 8}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2332}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 200.0,level: 8}]->(g);
CREATE (n: Building {id: 2333, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:2333}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 6.086598834065899, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2333}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 48.692790672527195,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2333}) CREATE (b)-[r:Supply{max_supply: 50.00000000000001, current_output: 12.173197668131799,level: 5}]->(g);
CREATE (n: Building {id: 2334, name:"building_opium_plantationlevel", level:5});
MATCH (g: Goods{code: 44}), (b: Building{id:2334}) CREATE (b)-[r:Supply{max_supply: 88.443, current_output: 91.98072,level: 5}]->(g);
CREATE (n: Building {id: 2335, name:"building_dye_plantationlevel", level:8});
MATCH (g: Goods{code: 21}), (b: Building{id:2335}) CREATE (b)-[r:Supply{max_supply: 176.87599999999998, current_output: 189.25732,level: 8}]->(g);
CREATE (n: Building {id: 2336, name:"building_tea_plantationlevel", level:6});
MATCH (g: Goods{code: 40}), (b: Building{id:2336}) CREATE (b)-[r:Supply{max_supply: 106.12079999999999, current_output: 111.42684,level: 6}]->(g);
CREATE (n: Building {id: 2337, name:"building_barrackslevel", level:18});
CREATE (n: Building {id: 2338, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:2338}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 36.642074291895725, level: 5}]->(b);
CREATE (n: Building {id: 2339, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:2339}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.4346395336263598, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2339}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 9.738558134505439,level: 2}]->(g);
CREATE (n: Building {id: 2340, name:"building_rice_farmlevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:2340}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 187.2,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2340}) CREATE (b)-[r:Supply{max_supply: 48.0, current_output: 56.16,level: 8}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2340}) CREATE (b)-[r:Supply{max_supply: 72.0, current_output: 84.24,level: 8}]->(g);
CREATE (n: Building {id: 2341, name:"building_dye_plantationlevel", level:6});
MATCH (g: Goods{code: 21}), (b: Building{id:2341}) CREATE (b)-[r:Supply{max_supply: 132.64649523809524, current_output: 139.27882,level: 6}]->(g);
CREATE (n: Building {id: 2342, name:"building_silk_plantationlevel", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2342}) CREATE (b)-[r:Supply{max_supply: 196.86199999999997, current_output: 263.79508,level: 10}]->(g);
CREATE (n: Building {id: 2343, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 2}), (b: Building{id:2343}) CREATE (g)-[r:Demand{max_demand: 12.5, current_input: 0.0, level: 25}]->(b);
CREATE (n: Building {id: 2344, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2344}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 27.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2344}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 4.5,level: 1}]->(g);
CREATE (n: Building {id: 2345, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 2346, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2346}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 54.6,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2346}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 9.1,level: 2}]->(g);
CREATE (n: Building {id: 2347, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:2347}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 2348, name:"building_government_administrationlevel", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:2348}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 51.29890400865402, level: 7}]->(b);
CREATE (n: Building {id: 2349, name:"building_banana_plantationlevel", level:10});
MATCH (g: Goods{code: 37}), (b: Building{id:2349}) CREATE (b)-[r:Supply{max_supply: 265.569, current_output: 289.47021,level: 10}]->(g);
CREATE (n: Building {id: 2350, name:"building_rice_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:2350}) CREATE (b)-[r:Supply{max_supply: 140.00000000000003, current_output: 162.4,level: 7}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2350}) CREATE (b)-[r:Supply{max_supply: 42.0, current_output: 48.72,level: 7}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2350}) CREATE (b)-[r:Supply{max_supply: 63.0, current_output: 73.08,level: 7}]->(g);
CREATE (n: Building {id: 2351, name:"building_tea_plantationlevel", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2351}) CREATE (b)-[r:Supply{max_supply: 177.07, current_output: 193.0063,level: 10}]->(g);
CREATE (n: Building {id: 2352, name:"building_dye_plantationlevel", level:11});
MATCH (g: Goods{code: 21}), (b: Building{id:2352}) CREATE (b)-[r:Supply{max_supply: 243.4245, current_output: 267.76695,level: 11}]->(g);
CREATE (n: Building {id: 2353, name:"building_silk_plantationlevel", level:16});
MATCH (g: Goods{code: 20}), (b: Building{id:2353}) CREATE (b)-[r:Supply{max_supply: 314.97920000000005, current_output: 440.97088,level: 16}]->(g);
CREATE (n: Building {id: 2354, name:"building_barrackslevel", level:25});
CREATE (n: Building {id: 2355, name:"building_government_administrationlevel", level:20});
MATCH (g: Goods{code: 14}), (b: Building{id:2355}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 146.5682971675829, level: 20}]->(b);
CREATE (n: Building {id: 2356, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2356}) CREATE (g)-[r:Demand{max_demand: 9.7806, current_input: 21.58876351333491, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2356}) CREATE (g)-[r:Demand{max_demand: 19.5612, current_input: 24.160915164182065, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2356}) CREATE (g)-[r:Demand{max_demand: 24.4515, current_input: 3.10983621183863, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2356}) CREATE (g)-[r:Demand{max_demand: 4.8903, current_input: 1.1906117711292987, level: 3}]->(b);
CREATE (n: Building {id: 2357, name:"building_tooling_workshopslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2357}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 148.21738030907346, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2357}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 119.99999999999999,level: 4}]->(g);
CREATE (n: Building {id: 2358, name:"building_furniture_manufacturieslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:2358}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 110.36523072886587, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2358}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 123.51448359089457, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2358}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 14.607837716975046, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2358}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 6.086598834065899, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2358}) CREATE (b)-[r:Supply{max_supply: 225.0, current_output: 142.62866480824522,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2358}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 63.39051769255343,level: 5}]->(g);
CREATE (n: Building {id: 2359, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2359}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 33.6,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2359}) CREATE (b)-[r:Supply{max_supply: 26.999999999999996, current_output: 30.24,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2359}) CREATE (b)-[r:Supply{max_supply: 18.0, current_output: 20.16,level: 3}]->(g);
CREATE (n: Building {id: 2360, name:"building_tea_plantationlevel", level:7});
MATCH (g: Goods{code: 40}), (b: Building{id:2360}) CREATE (b)-[r:Supply{max_supply: 123.80759433962264, current_output: 131.23605,level: 7}]->(g);
CREATE (n: Building {id: 2361, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 2}), (b: Building{id:2361}) CREATE (g)-[r:Demand{max_demand: 12.5, current_input: 0.0, level: 25}]->(b);
CREATE (n: Building {id: 2362, name:"building_forbidden_citylevel", level:1});
CREATE (n: Building {id: 2363, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:2363}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 4.8692790672527195, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2363}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 38.954232538021756,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2363}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 9.738558134505439,level: 4}]->(g);
CREATE (n: Building {id: 2364, name:"building_livestock_ranchlevel", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:2364}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 12.173197668131799, level: 10}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2364}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 73.0391860087908,level: 10}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2364}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 36.5195930043954,level: 10}]->(g);
CREATE (n: Building {id: 2365, name:"building_barrackslevel", level:16});
CREATE (n: Building {id: 2366, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2366}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.985244575137436, level: 3}]->(b);
CREATE (n: Building {id: 2367, name:"building_iron_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:2367}) CREATE (g)-[r:Demand{max_demand: 12.75, current_input: 3.1041654053736085, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2367}) CREATE (b)-[r:Supply{max_supply: 51.0, current_output: 12.416661621494434,level: 3}]->(g);
CREATE (n: Building {id: 2368, name:"building_logging_camplevel", level:9});
MATCH (g: Goods{code: 33}), (b: Building{id:2368}) CREATE (g)-[r:Demand{max_demand: 45.00000000000001, current_input: 10.955877901318619, level: 9}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2368}) CREATE (b)-[r:Supply{max_supply: 360.00000000000006, current_output: 87.64702321054895,level: 9}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2368}) CREATE (b)-[r:Supply{max_supply: 90.00000000000001, current_output: 21.911755802637238,level: 9}]->(g);
CREATE (n: Building {id: 2369, name:"building_livestock_ranchlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:2369}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 135.6,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2369}) CREATE (b)-[r:Supply{max_supply: 20.000000000000004, current_output: 22.6,level: 4}]->(g);
CREATE (n: Building {id: 2370, name:"building_barrackslevel", level:25});
CREATE (n: Building {id: 2371, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:2371}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 7.30391860087908, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2371}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 58.43134880703264,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2371}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 14.60783720175816,level: 6}]->(g);
CREATE (n: Building {id: 2372, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2372}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 100.8,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2372}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 16.8,level: 3}]->(g);
CREATE (n: Building {id: 2373, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:2373}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:2373}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 20}]->(b);
CREATE (n: Building {id: 2374, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2374}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.985244575137436, level: 3}]->(b);
CREATE (n: Building {id: 2375, name:"building_wheat_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:2375}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 91.7,level: 7}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2375}) CREATE (b)-[r:Supply{max_supply: 63.0, current_output: 82.53,level: 7}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2375}) CREATE (b)-[r:Supply{max_supply: 42.0, current_output: 55.02,level: 7}]->(g);
CREATE (n: Building {id: 2376, name:"building_tea_plantationlevel", level:7});
MATCH (g: Goods{code: 40}), (b: Building{id:2376}) CREATE (b)-[r:Supply{max_supply: 123.80339622641507, current_output: 131.2316,level: 7}]->(g);
CREATE (n: Building {id: 2377, name:"building_barrackslevel", level:18});
MATCH (g: Goods{code: 2}), (b: Building{id:2377}) CREATE (g)-[r:Demand{max_demand: 9.0, current_input: 0.0, level: 18}]->(b);
CREATE (n: Building {id: 2378, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:2378}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.656829716758292, level: 2}]->(b);
CREATE (n: Building {id: 2379, name:"building_tea_plantationlevel", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2379}) CREATE (b)-[r:Supply{max_supply: 70.84639805825242, current_output: 72.97179,level: 4}]->(g);
CREATE (n: Building {id: 2380, name:"building_barrackslevel", level:17});
MATCH (g: Goods{code: 2}), (b: Building{id:2380}) CREATE (g)-[r:Demand{max_demand: 17.0, current_input: 0.0, level: 17}]->(b);
CREATE (n: Building {id: 2381, name:"building_livestock_ranchlevel", level:7});
MATCH (g: Goods{code: 33}), (b: Building{id:2381}) CREATE (g)-[r:Demand{max_demand: 35.00000000000001, current_input: 8.521238367692261, level: 7}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2381}) CREATE (b)-[r:Supply{max_supply: 210.0, current_output: 51.127430206153555,level: 7}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2381}) CREATE (b)-[r:Supply{max_supply: 105.0, current_output: 25.563715103076778,level: 7}]->(g);
CREATE (n: Building {id: 2382, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 2}), (b: Building{id:2382}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 10}]->(b);
CREATE (n: Building {id: 2383, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:2383}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.656829716758292, level: 2}]->(b);
CREATE (n: Building {id: 2384, name:"building_tea_plantationlevel", level:8});
MATCH (g: Goods{code: 40}), (b: Building{id:2384}) CREATE (b)-[r:Supply{max_supply: 141.4895981308411, current_output: 151.39387,level: 8}]->(g);
CREATE (n: Building {id: 2385, name:"building_barrackslevel", level:22});
MATCH (g: Goods{code: 2}), (b: Building{id:2385}) CREATE (g)-[r:Demand{max_demand: 22.0, current_input: 0.0, level: 22}]->(b);
CREATE (n: Building {id: 2386, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:2386}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 171.0,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2386}) CREATE (b)-[r:Supply{max_supply: 25.000000000000004, current_output: 28.5,level: 5}]->(g);
CREATE (n: Building {id: 2387, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 2}), (b: Building{id:2387}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 10}]->(b);
CREATE (n: Building {id: 2388, name:"building_government_administrationlevel", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:2388}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 51.29890400865402, level: 7}]->(b);
CREATE (n: Building {id: 2389, name:"building_paper_millslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2389}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 148.21738030907346, level: 4}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2389}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 160.0,level: 4}]->(g);
CREATE (n: Building {id: 2390, name:"building_tea_plantationlevel", level:14});
MATCH (g: Goods{code: 40}), (b: Building{id:2390}) CREATE (b)-[r:Supply{max_supply: 247.609592920354, current_output: 279.79884,level: 14}]->(g);
CREATE (n: Building {id: 2391, name:"building_silk_plantationlevel", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2391}) CREATE (b)-[r:Supply{max_supply: 196.78799999999998, current_output: 263.69592,level: 10}]->(g);
CREATE (n: Building {id: 2392, name:"building_barrackslevel", level:25});
CREATE (n: Building {id: 2393, name:"building_government_administrationlevel", level:8});
MATCH (g: Goods{code: 14}), (b: Building{id:2393}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 58.62731886703315, level: 8}]->(b);
CREATE (n: Building {id: 2394, name:"building_banana_plantationlevel", level:10});
MATCH (g: Goods{code: 37}), (b: Building{id:2394}) CREATE (b)-[r:Supply{max_supply: 242.87999999999997, current_output: 264.7392,level: 10}]->(g);
CREATE (n: Building {id: 2395, name:"building_fishing_wharflevel", level:5});
MATCH (g: Goods{code: 8}), (b: Building{id:2395}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 130.0,level: 5}]->(g);
CREATE (n: Building {id: 2396, name:"building_silk_plantationlevel", level:20});
MATCH (g: Goods{code: 20}), (b: Building{id:2396}) CREATE (b)-[r:Supply{max_supply: 353.736, current_output: 509.37984,level: 20}]->(g);
CREATE (n: Building {id: 2397, name:"building_rice_farmlevel", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:2397}) CREATE (b)-[r:Supply{max_supply: 180.00000000000003, current_output: 212.4,level: 9}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2397}) CREATE (b)-[r:Supply{max_supply: 54.0, current_output: 63.72,level: 9}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2397}) CREATE (b)-[r:Supply{max_supply: 81.0, current_output: 95.58,level: 9}]->(g);
CREATE (n: Building {id: 2398, name:"building_dye_plantationlevel", level:13});
MATCH (g: Goods{code: 21}), (b: Building{id:2398}) CREATE (b)-[r:Supply{max_supply: 287.40074999999996, current_output: 321.88884,level: 13}]->(g);
CREATE (n: Building {id: 2399, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2399}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 88.29218458309269, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2399}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 98.81158687271565, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:2399}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 2}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:2399}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 2}]->(g);
CREATE (n: Building {id: 2400, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 2}), (b: Building{id:2400}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 0.0, level: 25}]->(b);
CREATE (n: Building {id: 2401, name:"building_naval_baselevel", level:15});
MATCH (g: Goods{code: 5}), (b: Building{id:2401}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 30.0, level: 15}]->(b);
CREATE (n: Building {id: 2402, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:2402}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 24.0, level: 3}]->(b);
CREATE (n: Building {id: 2406, name:"building_government_administrationlevel", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:2406}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 51.29890400865402, level: 7}]->(b);
CREATE (n: Building {id: 2407, name:"building_silk_plantationlevel", level:11});
MATCH (g: Goods{code: 20}), (b: Building{id:2407}) CREATE (b)-[r:Supply{max_supply: 216.42719999999997, current_output: 292.17672,level: 11}]->(g);
CREATE (n: Building {id: 2408, name:"building_rice_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2408}) CREATE (b)-[r:Supply{max_supply: 99.84, current_output: 113.8176,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2408}) CREATE (b)-[r:Supply{max_supply: 29.952, current_output: 34.14528,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2408}) CREATE (b)-[r:Supply{max_supply: 44.928000000000004, current_output: 51.21792,level: 5}]->(g);
CREATE (n: Building {id: 2409, name:"building_dye_plantationlevel", level:11});
MATCH (g: Goods{code: 21}), (b: Building{id:2409}) CREATE (b)-[r:Supply{max_supply: 243.18524545454542, current_output: 267.50377,level: 11}]->(g);
CREATE (n: Building {id: 2410, name:"building_fishing_wharflevel", level:4});
MATCH (g: Goods{code: 8}), (b: Building{id:2410}) CREATE (b)-[r:Supply{max_supply: 99.43499999999999, current_output: 102.41805,level: 4}]->(g);
CREATE (n: Building {id: 2411, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 2}), (b: Building{id:2411}) CREATE (g)-[r:Demand{max_demand: 12.5, current_input: 0.0, level: 25}]->(b);
CREATE (n: Building {id: 2412, name:"building_government_administrationlevel", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:2412}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 51.29890400865402, level: 7}]->(b);
CREATE (n: Building {id: 2413, name:"building_textile_millslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:2413}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 529.7531074985562, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2413}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 1135.36946, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2413}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 200.0,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2413}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 159.99999999999997,level: 8}]->(g);
CREATE (n: Building {id: 2414, name:"building_tea_plantationlevel", level:17});
MATCH (g: Goods{code: 40}), (b: Building{id:2414}) CREATE (b)-[r:Supply{max_supply: 300.66539655172414, current_output: 348.77186,level: 17}]->(g);
CREATE (n: Building {id: 2415, name:"building_rice_farmlevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:2415}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 138.0,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2415}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 41.4,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2415}) CREATE (b)-[r:Supply{max_supply: 54.00000000000001, current_output: 62.1,level: 6}]->(g);
CREATE (n: Building {id: 2416, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:2416}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 2417, name:"building_barrackslevel", level:21});
MATCH (g: Goods{code: 2}), (b: Building{id:2417}) CREATE (g)-[r:Demand{max_demand: 10.5, current_input: 0.0, level: 21}]->(b);
CREATE (n: Building {id: 2418, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2418}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 73.28414858379145, level: 10}]->(b);
CREATE (n: Building {id: 2419, name:"building_glassworkslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2419}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 185.27172538634184, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2419}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 131.74396666666667, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2419}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 5}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2419}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 125.0,level: 5}]->(g);
CREATE (n: Building {id: 2420, name:"building_tea_plantationlevel", level:16});
MATCH (g: Goods{code: 40}), (b: Building{id:2420}) CREATE (b)-[r:Supply{max_supply: 283.296, current_output: 325.7904,level: 16}]->(g);
CREATE (n: Building {id: 2421, name:"building_rice_farmlevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:2421}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 187.2,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2421}) CREATE (b)-[r:Supply{max_supply: 48.0, current_output: 56.16,level: 8}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2421}) CREATE (b)-[r:Supply{max_supply: 72.0, current_output: 84.24,level: 8}]->(g);
CREATE (n: Building {id: 2422, name:"building_barrackslevel", level:25});
CREATE (n: Building {id: 2423, name:"building_government_administrationlevel", level:15});
MATCH (g: Goods{code: 14}), (b: Building{id:2423}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 109.92622287568719, level: 15}]->(b);
CREATE (n: Building {id: 2424, name:"building_construction_sectorlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:2424}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 28.78501801777988, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2424}) CREATE (g)-[r:Demand{max_demand: 26.0816, current_input: 32.214553552242755, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2424}) CREATE (g)-[r:Demand{max_demand: 32.602, current_input: 4.146448282451506, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2424}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 1.5874823615057316, level: 4}]->(b);
CREATE (n: Building {id: 2425, name:"building_glassworkslevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2425}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 370.5434507726837, level: 10}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2425}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 263.48793333333333, level: 10}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2425}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 99.99999999999999,level: 10}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2425}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 249.99999999999997,level: 10}]->(g);
CREATE (n: Building {id: 2426, name:"building_tea_plantationlevel", level:14});
MATCH (g: Goods{code: 40}), (b: Building{id:2426}) CREATE (b)-[r:Supply{max_supply: 247.606796460177, current_output: 279.79568,level: 14}]->(g);
CREATE (n: Building {id: 2427, name:"building_rice_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2427}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 114.0,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2427}) CREATE (b)-[r:Supply{max_supply: 30.000000000000004, current_output: 34.2,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2427}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 51.3,level: 5}]->(g);
CREATE (n: Building {id: 2428, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 2}), (b: Building{id:2428}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 0.0, level: 25}]->(b);
CREATE (n: Building {id: 2429, name:"building_government_administrationlevel", level:15});
MATCH (g: Goods{code: 14}), (b: Building{id:2429}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 109.92622287568719, level: 15}]->(b);
CREATE (n: Building {id: 2430, name:"building_construction_sectorlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:2430}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 28.78501801777988, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2430}) CREATE (g)-[r:Demand{max_demand: 26.0816, current_input: 32.214553552242755, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2430}) CREATE (g)-[r:Demand{max_demand: 32.602, current_input: 4.146448282451506, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2430}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 1.5874823615057316, level: 4}]->(b);
CREATE (n: Building {id: 2431, name:"building_glassworkslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2431}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 222.32607046361022, level: 6}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2431}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 158.09276, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2431}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 6}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2431}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 150.0,level: 6}]->(g);
CREATE (n: Building {id: 2432, name:"building_tea_plantationlevel", level:16});
MATCH (g: Goods{code: 40}), (b: Building{id:2432}) CREATE (b)-[r:Supply{max_supply: 282.9952, current_output: 325.44448,level: 16}]->(g);
CREATE (n: Building {id: 2433, name:"building_government_administrationlevel", level:12});
MATCH (g: Goods{code: 14}), (b: Building{id:2433}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 87.94097830054973, level: 12}]->(b);
CREATE (n: Building {id: 2434, name:"building_iron_minelevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:2434}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.8692790672527195, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2434}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 19.477116269010878,level: 4}]->(g);
CREATE (n: Building {id: 2435, name:"building_paper_millslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2435}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 222.32607046361022, level: 6}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2435}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 240.0,level: 6}]->(g);
CREATE (n: Building {id: 2436, name:"building_glassworkslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2436}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 222.32607046361022, level: 6}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2436}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 158.09276, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2436}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 6}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2436}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 150.0,level: 6}]->(g);
CREATE (n: Building {id: 2437, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:2437}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 45.2,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2437}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 40.68,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2437}) CREATE (b)-[r:Supply{max_supply: 24.000000000000004, current_output: 27.12,level: 4}]->(g);
CREATE (n: Building {id: 2438, name:"building_tea_plantationlevel", level:11});
MATCH (g: Goods{code: 40}), (b: Building{id:2438}) CREATE (b)-[r:Supply{max_supply: 194.54819999999998, current_output: 214.00302,level: 11}]->(g);
CREATE (n: Building {id: 2439, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 2}), (b: Building{id:2439}) CREATE (g)-[r:Demand{max_demand: 12.5, current_input: 0.0, level: 25}]->(b);
CREATE (n: Building {id: 2440, name:"building_government_administrationlevel", level:11});
MATCH (g: Goods{code: 14}), (b: Building{id:2440}) CREATE (g)-[r:Demand{max_demand: 109.99999999999999, current_input: 80.6125634421706, level: 11}]->(b);
CREATE (n: Building {id: 2441, name:"building_glassworkslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2441}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 185.27172538634184, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2441}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 150.0,level: 5}]->(g);
CREATE (n: Building {id: 2442, name:"building_rice_farmlevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:2442}) CREATE (b)-[r:Supply{max_supply: 159.4751965811966, current_output: 186.58598,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2442}) CREATE (b)-[r:Supply{max_supply: 47.84255555555556, current_output: 55.97579,level: 8}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2442}) CREATE (b)-[r:Supply{max_supply: 71.76383760683761, current_output: 83.96369,level: 8}]->(g);
CREATE (n: Building {id: 2443, name:"building_silk_plantationlevel", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2443}) CREATE (b)-[r:Supply{max_supply: 196.80999999999997, current_output: 263.7254,level: 10}]->(g);
CREATE (n: Building {id: 2444, name:"building_tea_plantationlevel", level:21});
MATCH (g: Goods{code: 40}), (b: Building{id:2444}) CREATE (b)-[r:Supply{max_supply: 371.41020000000003, current_output: 445.69224,level: 21}]->(g);
CREATE (n: Building {id: 2445, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 2}), (b: Building{id:2445}) CREATE (g)-[r:Demand{max_demand: 12.5, current_input: 0.0, level: 25}]->(b);
CREATE (n: Building {id: 2446, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2446}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 73.28414858379145, level: 10}]->(b);
CREATE (n: Building {id: 2447, name:"building_furniture_manufacturieslevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:2447}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 220.7304614577317, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2447}) CREATE (g)-[r:Demand{max_demand: 199.99999999999997, current_input: 247.02896718178906, level: 10}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2447}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 29.215675433950086, level: 10}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2447}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 191.01306286162506,level: 10}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2447}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 152.81045028930004,level: 10}]->(g);
CREATE (n: Building {id: 2448, name:"building_rice_farmlevel", level:13});
MATCH (g: Goods{code: 7}), (b: Building{id:2448}) CREATE (b)-[r:Supply{max_supply: 260.0, current_output: 317.2,level: 13}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2448}) CREATE (b)-[r:Supply{max_supply: 78.0, current_output: 95.16,level: 13}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2448}) CREATE (b)-[r:Supply{max_supply: 117.00000000000001, current_output: 142.74,level: 13}]->(g);
CREATE (n: Building {id: 2449, name:"building_paper_millslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2449}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 185.27172538634184, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2449}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 200.0,level: 5}]->(g);
CREATE (n: Building {id: 2450, name:"building_tea_plantationlevel", level:11});
MATCH (g: Goods{code: 40}), (b: Building{id:2450}) CREATE (b)-[r:Supply{max_supply: 194.54819999999998, current_output: 214.00302,level: 11}]->(g);
CREATE (n: Building {id: 2451, name:"building_barrackslevel", level:25});
CREATE (n: Building {id: 2452, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2452}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 73.28414858379145, level: 10}]->(b);
CREATE (n: Building {id: 2453, name:"building_rice_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:2453}) CREATE (b)-[r:Supply{max_supply: 140.00000000000003, current_output: 162.4,level: 7}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2453}) CREATE (b)-[r:Supply{max_supply: 42.0, current_output: 48.72,level: 7}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2453}) CREATE (b)-[r:Supply{max_supply: 63.0, current_output: 73.08,level: 7}]->(g);
CREATE (n: Building {id: 2454, name:"building_tea_plantationlevel", level:20});
MATCH (g: Goods{code: 40}), (b: Building{id:2454}) CREATE (b)-[r:Supply{max_supply: 354.3, current_output: 421.617,level: 20}]->(g);
CREATE (n: Building {id: 2455, name:"building_barrackslevel", level:23});
MATCH (g: Goods{code: 2}), (b: Building{id:2455}) CREATE (g)-[r:Demand{max_demand: 23.0, current_input: 0.0, level: 23}]->(b);
CREATE (n: Building {id: 2456, name:"building_government_administrationlevel", level:8});
MATCH (g: Goods{code: 14}), (b: Building{id:2456}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 58.62731886703315, level: 8}]->(b);
CREATE (n: Building {id: 2457, name:"building_paper_millslevel", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:2457}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 296.4347606181469, level: 8}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2457}) CREATE (b)-[r:Supply{max_supply: 319.99999999999994, current_output: 319.99999999999994,level: 8}]->(g);
CREATE (n: Building {id: 2458, name:"building_furniture_manufacturieslevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:2458}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 220.7304614577317, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2458}) CREATE (g)-[r:Demand{max_demand: 199.99999999999997, current_input: 247.02896718178906, level: 10}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2458}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 29.215675433950086, level: 10}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2458}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 191.01306286162506,level: 10}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2458}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 152.81045028930004,level: 10}]->(g);
CREATE (n: Building {id: 2459, name:"building_rice_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:2459}) CREATE (b)-[r:Supply{max_supply: 140.00000000000003, current_output: 162.4,level: 7}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2459}) CREATE (b)-[r:Supply{max_supply: 42.0, current_output: 48.72,level: 7}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2459}) CREATE (b)-[r:Supply{max_supply: 63.0, current_output: 73.08,level: 7}]->(g);
CREATE (n: Building {id: 2460, name:"building_tea_plantationlevel", level:12});
MATCH (g: Goods{code: 40}), (b: Building{id:2460}) CREATE (b)-[r:Supply{max_supply: 212.23919819819818, current_output: 235.58551,level: 12}]->(g);
CREATE (n: Building {id: 2461, name:"building_barrackslevel", level:25});
CREATE (n: Building {id: 2462, name:"building_government_administrationlevel", level:9});
MATCH (g: Goods{code: 14}), (b: Building{id:2462}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 65.95573372541232, level: 9}]->(b);
CREATE (n: Building {id: 2463, name:"building_furniture_manufacturieslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:2463}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 176.58436916618535, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2463}) CREATE (g)-[r:Demand{max_demand: 159.99999999999997, current_input: 197.62317374543127, level: 8}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2463}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 23.37254034716007, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2463}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 9.738558134505437, level: 8}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2463}) CREATE (b)-[r:Supply{max_supply: 359.99999999999994, current_output: 228.2058636931923,level: 8}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2463}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 101.42482830808547,level: 8}]->(g);
CREATE (n: Building {id: 2464, name:"building_rice_farmlevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:2464}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.9738558134505438, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2464}) CREATE (b)-[r:Supply{max_supply: 80.00000000000001, current_output: 19.47711626901088,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2464}) CREATE (b)-[r:Supply{max_supply: 24.000000000000004, current_output: 5.843134880703264,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2464}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 8.764702321054894,level: 4}]->(g);
CREATE (n: Building {id: 2465, name:"building_paper_millslevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:2465}) CREATE (g)-[r:Demand{max_demand: 209.99999999999997, current_input: 259.3804155408785, level: 7}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2465}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 280.0,level: 7}]->(g);
CREATE (n: Building {id: 2466, name:"building_tea_plantationlevel", level:9});
MATCH (g: Goods{code: 40}), (b: Building{id:2466}) CREATE (b)-[r:Supply{max_supply: 159.17579629629628, current_output: 171.90986,level: 9}]->(g);
CREATE (n: Building {id: 2467, name:"building_silk_plantationlevel", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2467}) CREATE (b)-[r:Supply{max_supply: 176.862, current_output: 236.99508,level: 10}]->(g);
CREATE (n: Building {id: 2468, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 2}), (b: Building{id:2468}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 0.0, level: 25}]->(b);
CREATE (n: Building {id: 2469, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:2469}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 36.642074291895725, level: 5}]->(b);
CREATE (n: Building {id: 2470, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:2470}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 7.30391860087908, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2470}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 58.43134880703264,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2470}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 14.60783720175816,level: 6}]->(g);
CREATE (n: Building {id: 2471, name:"building_rice_farmlevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:2471}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 138.0,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2471}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 41.4,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2471}) CREATE (b)-[r:Supply{max_supply: 54.00000000000001, current_output: 62.1,level: 6}]->(g);
CREATE (n: Building {id: 2472, name:"building_tea_plantationlevel", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2472}) CREATE (b)-[r:Supply{max_supply: 176.88199999999998, current_output: 192.80138,level: 10}]->(g);
CREATE (n: Building {id: 2473, name:"building_barrackslevel", level:21});
CREATE (n: Building {id: 2474, name:"building_fishing_wharflevel", level:5});
MATCH (g: Goods{code: 8}), (b: Building{id:2474}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 130.0,level: 5}]->(g);
CREATE (n: Building {id: 2475, name:"building_tea_plantationlevel", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2475}) CREATE (b)-[r:Supply{max_supply: 70.74479611650486, current_output: 72.86714,level: 4}]->(g);
CREATE (n: Building {id: 2476, name:"building_banana_plantationlevel", level:9});
MATCH (g: Goods{code: 37}), (b: Building{id:2476}) CREATE (b)-[r:Supply{max_supply: 238.76369444444447, current_output: 257.86479,level: 9}]->(g);
CREATE (n: Building {id: 2477, name:"building_barrackslevel", level:18});
MATCH (g: Goods{code: 2}), (b: Building{id:2477}) CREATE (g)-[r:Demand{max_demand: 18.0, current_input: 0.0, level: 18}]->(b);
CREATE (n: Building {id: 2478, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2478}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.0, level: 1}]->(b);
CREATE (n: Building {id: 2479, name:"building_government_administrationlevel", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:2479}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 51.29890400865402, level: 7}]->(b);
CREATE (n: Building {id: 2480, name:"building_glassworkslevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2480}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 370.5434507726837, level: 10}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2480}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 263.48793333333333, level: 10}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2480}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 99.99999999999999,level: 10}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2480}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 249.99999999999997,level: 10}]->(g);
CREATE (n: Building {id: 2481, name:"building_textile_millslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:2481}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 529.7531074985562, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2481}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 1135.36946, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2481}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 200.0,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2481}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 159.99999999999997,level: 8}]->(g);
CREATE (n: Building {id: 2482, name:"building_tea_plantationlevel", level:8});
MATCH (g: Goods{code: 40}), (b: Building{id:2482}) CREATE (b)-[r:Supply{max_supply: 141.49279439252336, current_output: 151.39729,level: 8}]->(g);
CREATE (n: Building {id: 2483, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:2483}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 45.2,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2483}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 40.68,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2483}) CREATE (b)-[r:Supply{max_supply: 24.000000000000004, current_output: 27.12,level: 4}]->(g);
CREATE (n: Building {id: 2484, name:"building_fishing_wharflevel", level:6});
MATCH (g: Goods{code: 8}), (b: Building{id:2484}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 157.5,level: 6}]->(g);
CREATE (n: Building {id: 2485, name:"building_barrackslevel", level:22});
MATCH (g: Goods{code: 2}), (b: Building{id:2485}) CREATE (g)-[r:Demand{max_demand: 22.0, current_input: 0.0, level: 22}]->(b);
CREATE (n: Building {id: 2486, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2486}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.0, level: 1}]->(b);
CREATE (n: Building {id: 2487, name:"building_government_administrationlevel", level:13});
MATCH (g: Goods{code: 14}), (b: Building{id:2487}) CREATE (g)-[r:Demand{max_demand: 129.99999999999997, current_input: 95.26939315892886, level: 13}]->(b);
CREATE (n: Building {id: 2488, name:"building_paper_millslevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:2488}) CREATE (g)-[r:Demand{max_demand: 209.99999999999997, current_input: 259.3804155408785, level: 7}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2488}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 280.0,level: 7}]->(g);
CREATE (n: Building {id: 2489, name:"building_tea_plantationlevel", level:11});
MATCH (g: Goods{code: 40}), (b: Building{id:2489}) CREATE (b)-[r:Supply{max_supply: 194.54819999999998, current_output: 214.00302,level: 11}]->(g);
CREATE (n: Building {id: 2490, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:2490}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 2491, name:"building_barrackslevel", level:13});
CREATE (n: Building {id: 2493, name:"building_livestock_ranchlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:2493}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 135.6,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2493}) CREATE (b)-[r:Supply{max_supply: 20.000000000000004, current_output: 22.6,level: 4}]->(g);
CREATE (n: Building {id: 2494, name:"building_livestock_ranchlevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:2494}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 207.0,level: 6}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2494}) CREATE (b)-[r:Supply{max_supply: 30.000000000000004, current_output: 34.5,level: 6}]->(g);
CREATE (n: Building {id: 16780033, name:"building_barrackslevel", level:12});
CREATE (n: Building {id: 2828, name:"building_subsistence_farmslevel", level:915});
MATCH (g: Goods{code: 7}), (b: Building{id:2828}) CREATE (b)-[r:Supply{max_supply: 1970.0865, current_output: 2167.09515,level: 915}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2828}) CREATE (b)-[r:Supply{max_supply: 328.3477454545454, current_output: 361.18252,level: 915}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2828}) CREATE (b)-[r:Supply{max_supply: 328.3477454545454, current_output: 361.18252,level: 915}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2828}) CREATE (b)-[r:Supply{max_supply: 328.3477454545454, current_output: 361.18252,level: 915}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2828}) CREATE (b)-[r:Supply{max_supply: 328.3477454545454, current_output: 361.18252,level: 915}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2828}) CREATE (b)-[r:Supply{max_supply: 328.3477454545454, current_output: 361.18252,level: 915}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2828}) CREATE (b)-[r:Supply{max_supply: 459.6868454545454, current_output: 505.65553,level: 915}]->(g);
CREATE (n: Building {id: 2829, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2829}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 37.054345077268366, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2829}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.292262331198074, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2829}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 205.1690493247923,level: 6}]->(g);
CREATE (n: Building {id: 2830, name:"building_subsistence_farmslevel", level:846});
MATCH (g: Goods{code: 7}), (b: Building{id:2830}) CREATE (b)-[r:Supply{max_supply: 1951.722, current_output: 2146.8942,level: 846}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2830}) CREATE (b)-[r:Supply{max_supply: 325.287, current_output: 357.8157,level: 846}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2830}) CREATE (b)-[r:Supply{max_supply: 325.287, current_output: 357.8157,level: 846}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2830}) CREATE (b)-[r:Supply{max_supply: 325.287, current_output: 357.8157,level: 846}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2830}) CREATE (b)-[r:Supply{max_supply: 325.287, current_output: 357.8157,level: 846}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2830}) CREATE (b)-[r:Supply{max_supply: 325.287, current_output: 357.8157,level: 846}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2830}) CREATE (b)-[r:Supply{max_supply: 455.4018, current_output: 500.94198,level: 846}]->(g);
CREATE (n: Building {id: 2831, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2831}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 30.878620897723643, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2831}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 17.74355194266506, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2831}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 170.97420777066023,level: 5}]->(g);
CREATE (n: Building {id: 16780130, name:"building_tea_plantationlevel", level:7});
MATCH (g: Goods{code: 40}), (b: Building{id:16780130}) CREATE (b)-[r:Supply{max_supply: 123.82299999999998, current_output: 131.25238,level: 7}]->(g);
CREATE (n: Building {id: 2993, name:"building_subsistence_farmslevel", level:72});
MATCH (g: Goods{code: 7}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 99.89567272727271, current_output: 109.88524,level: 72}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 16.649272727272727, current_output: 18.3142,level: 72}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 16.649272727272727, current_output: 18.3142,level: 72}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 16.649272727272727, current_output: 18.3142,level: 72}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 16.649272727272727, current_output: 18.3142,level: 72}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 16.649272727272727, current_output: 18.3142,level: 72}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 23.30899090909091, current_output: 25.63989,level: 72}]->(g);
CREATE (n: Building {id: 2994, name:"building_subsistence_farmslevel", level:48});
MATCH (g: Goods{code: 7}), (b: Building{id:2994}) CREATE (b)-[r:Supply{max_supply: 73.10015454545453, current_output: 80.41017,level: 48}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2994}) CREATE (b)-[r:Supply{max_supply: 12.183354545454545, current_output: 13.40169,level: 48}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2994}) CREATE (b)-[r:Supply{max_supply: 12.183354545454545, current_output: 13.40169,level: 48}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2994}) CREATE (b)-[r:Supply{max_supply: 12.183354545454545, current_output: 13.40169,level: 48}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2994}) CREATE (b)-[r:Supply{max_supply: 12.183354545454545, current_output: 13.40169,level: 48}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2994}) CREATE (b)-[r:Supply{max_supply: 12.183354545454545, current_output: 13.40169,level: 48}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2994}) CREATE (b)-[r:Supply{max_supply: 17.0567, current_output: 18.76237,level: 48}]->(g);
CREATE (n: Building {id: 16780527, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16780527}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 7.19625450444497, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16780527}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 8.053638388060689, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16780527}) CREATE (g)-[r:Demand{max_demand: 8.1505, current_input: 1.0366120706128765, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16780527}) CREATE (g)-[r:Demand{max_demand: 1.6301, current_input: 0.3968705903764329, level: 1}]->(b);
CREATE (n: Building {id: 16780568, name:"building_barrackslevel", level:17});
MATCH (g: Goods{code: 2}), (b: Building{id:16780568}) CREATE (g)-[r:Demand{max_demand: 8.5, current_input: 0.0, level: 17}]->(b);
CREATE (n: Building {id: 33557832, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:33557832}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 14.39250900888994, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33557832}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 16.107276776121378, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33557832}) CREATE (g)-[r:Demand{max_demand: 16.301, current_input: 2.073224141225753, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33557832}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 0.7937411807528658, level: 2}]->(b);
CREATE (n: Building {id: 3612, name:"building_subsistence_farmslevel", level:35});
MATCH (g: Goods{code: 7}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 74.38724545454545, current_output: 81.82597,level: 35}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 12.397872727272727, current_output: 13.63766,level: 35}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 12.397872727272727, current_output: 13.63766,level: 35}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 12.397872727272727, current_output: 13.63766,level: 35}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 12.397872727272727, current_output: 13.63766,level: 35}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 12.397872727272727, current_output: 13.63766,level: 35}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3612}) CREATE (b)-[r:Supply{max_supply: 17.35701818181818, current_output: 19.09272,level: 35}]->(g);
CREATE (n: Building {id: 3613, name:"building_subsistence_farmslevel", level:53});
MATCH (g: Goods{code: 7}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 103.71092222222222, current_output: 93.33983,level: 53}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 17.285144444444445, current_output: 15.55663,level: 53}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 17.285144444444445, current_output: 15.55663,level: 53}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 17.285144444444445, current_output: 15.55663,level: 53}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 17.285144444444445, current_output: 15.55663,level: 53}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 17.285144444444445, current_output: 15.55663,level: 53}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3613}) CREATE (b)-[r:Supply{max_supply: 24.19921111111111, current_output: 21.77929,level: 53}]->(g);
CREATE (n: Building {id: 3614, name:"building_subsistence_farmslevel", level:876});
MATCH (g: Goods{code: 7}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 1938.62304, current_output: 2423.2788,level: 876}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 323.10384, current_output: 403.8798,level: 876}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 323.10384, current_output: 403.8798,level: 876}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 323.10384, current_output: 403.8798,level: 876}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 323.10384, current_output: 403.8798,level: 876}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 323.10384, current_output: 403.8798,level: 876}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3614}) CREATE (b)-[r:Supply{max_supply: 452.34537600000004, current_output: 565.43172,level: 876}]->(g);
CREATE (n: Building {id: 3615, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3615}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 24.702896718178913, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3615}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.19484155413205, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3615}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 136.77936621652822,level: 4}]->(g);
CREATE (n: Building {id: 3616, name:"building_subsistence_farmslevel", level:373});
MATCH (g: Goods{code: 7}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 796.1908727272727, current_output: 875.80996,level: 373}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 132.69847272727273, current_output: 145.96832,level: 373}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 132.69847272727273, current_output: 145.96832,level: 373}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 132.69847272727273, current_output: 145.96832,level: 373}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 132.69847272727273, current_output: 145.96832,level: 373}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 132.69847272727273, current_output: 145.96832,level: 373}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 185.7778636363636, current_output: 204.35565,level: 373}]->(g);
CREATE (n: Building {id: 3617, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3617}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 12.351448359089456, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3617}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.097420777066025, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3617}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 68.38968310826411,level: 2}]->(g);
CREATE (n: Building {id: 3618, name:"building_subsistence_farmslevel", level:282});
MATCH (g: Goods{code: 7}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 509.81651818181814, current_output: 560.79817,level: 282}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 84.96941818181817, current_output: 93.46636,level: 282}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 84.96941818181817, current_output: 93.46636,level: 282}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 84.96941818181817, current_output: 93.46636,level: 282}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 84.96941818181817, current_output: 93.46636,level: 282}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 84.96941818181817, current_output: 93.46636,level: 282}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 118.95718181818181, current_output: 130.8529,level: 282}]->(g);
CREATE (n: Building {id: 3619, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3619}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 30.878620897723643, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3619}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 17.74355194266506, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 170.97420777066023,level: 5}]->(g);
CREATE (n: Building {id: 3620, name:"building_subsistence_farmslevel", level:455});
MATCH (g: Goods{code: 7}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 1042.6416, current_output: 1146.90576,level: 455}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 173.7736, current_output: 191.15096,level: 455}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 173.7736, current_output: 191.15096,level: 455}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 173.7736, current_output: 191.15096,level: 455}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 173.7736, current_output: 191.15096,level: 455}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 173.7736, current_output: 191.15096,level: 455}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3620}) CREATE (b)-[r:Supply{max_supply: 243.28303636363634, current_output: 267.61134,level: 455}]->(g);
CREATE (n: Building {id: 3621, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3621}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 18.527172538634183, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3621}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.646131165599037, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3621}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 102.58452466239615,level: 3}]->(g);
CREATE (n: Building {id: 3622, name:"building_subsistence_farmslevel", level:41});
MATCH (g: Goods{code: 7}), (b: Building{id:3622}) CREATE (b)-[r:Supply{max_supply: 67.18997777777777, current_output: 60.47098,level: 41}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3622}) CREATE (b)-[r:Supply{max_supply: 11.198322222222222, current_output: 10.07849,level: 41}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3622}) CREATE (b)-[r:Supply{max_supply: 11.198322222222222, current_output: 10.07849,level: 41}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3622}) CREATE (b)-[r:Supply{max_supply: 11.198322222222222, current_output: 10.07849,level: 41}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3622}) CREATE (b)-[r:Supply{max_supply: 11.198322222222222, current_output: 10.07849,level: 41}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3622}) CREATE (b)-[r:Supply{max_supply: 11.198322222222222, current_output: 10.07849,level: 41}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3622}) CREATE (b)-[r:Supply{max_supply: 15.677655555555555, current_output: 14.10989,level: 41}]->(g);
CREATE (n: Building {id: 3623, name:"building_subsistence_farmslevel", level:42});
MATCH (g: Goods{code: 7}), (b: Building{id:3623}) CREATE (b)-[r:Supply{max_supply: 73.9368, current_output: 66.54312,level: 42}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3623}) CREATE (b)-[r:Supply{max_supply: 12.322799999999999, current_output: 11.09052,level: 42}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3623}) CREATE (b)-[r:Supply{max_supply: 12.322799999999999, current_output: 11.09052,level: 42}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3623}) CREATE (b)-[r:Supply{max_supply: 12.322799999999999, current_output: 11.09052,level: 42}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3623}) CREATE (b)-[r:Supply{max_supply: 12.322799999999999, current_output: 11.09052,level: 42}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3623}) CREATE (b)-[r:Supply{max_supply: 12.322799999999999, current_output: 11.09052,level: 42}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3623}) CREATE (b)-[r:Supply{max_supply: 17.25191111111111, current_output: 15.52672,level: 42}]->(g);
CREATE (n: Building {id: 3624, name:"building_subsistence_farmslevel", level:878});
MATCH (g: Goods{code: 7}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 1982.7961727272725, current_output: 2181.07579,level: 878}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 330.4660272727273, current_output: 363.51263,level: 878}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 330.4660272727273, current_output: 363.51263,level: 878}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 330.4660272727273, current_output: 363.51263,level: 878}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 330.4660272727273, current_output: 363.51263,level: 878}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 330.4660272727273, current_output: 363.51263,level: 878}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3624}) CREATE (b)-[r:Supply{max_supply: 462.65243636363635, current_output: 508.91768,level: 878}]->(g);
CREATE (n: Building {id: 3625, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3625}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 24.702896718178913, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3625}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.19484155413205, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3625}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 136.77936621652822,level: 4}]->(g);
CREATE (n: Building {id: 3626, name:"building_subsistence_farmslevel", level:710});
MATCH (g: Goods{code: 7}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 1999.7079, current_output: 2199.67869,level: 710}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 333.28464545454545, current_output: 366.61311,level: 710}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 333.28464545454545, current_output: 366.61311,level: 710}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 333.28464545454545, current_output: 366.61311,level: 710}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 333.28464545454545, current_output: 366.61311,level: 710}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 333.28464545454545, current_output: 366.61311,level: 710}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3626}) CREATE (b)-[r:Supply{max_supply: 466.5985090909091, current_output: 513.25836,level: 710}]->(g);
CREATE (n: Building {id: 3627, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3627}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 43.230069256813096, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3627}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 24.840972719731088, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3627}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 239.36389087892434,level: 7}]->(g);
CREATE (n: Building {id: 3628, name:"building_subsistence_farmslevel", level:394});
MATCH (g: Goods{code: 7}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 272.84105454545454, current_output: 300.12516,level: 394}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 45.47350909090908, current_output: 50.02086,level: 394}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 45.47350909090908, current_output: 50.02086,level: 394}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 45.47350909090908, current_output: 50.02086,level: 394}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 45.47350909090908, current_output: 50.02086,level: 394}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 45.47350909090908, current_output: 50.02086,level: 394}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3628}) CREATE (b)-[r:Supply{max_supply: 63.66290909090909, current_output: 70.0292,level: 394}]->(g);
CREATE (n: Building {id: 3629, name:"building_subsistence_farmslevel", level:86});
MATCH (g: Goods{code: 7}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 142.82105454545453, current_output: 157.10316,level: 86}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 23.80350909090909, current_output: 26.18386,level: 86}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 23.80350909090909, current_output: 26.18386,level: 86}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 23.80350909090909, current_output: 26.18386,level: 86}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 23.80350909090909, current_output: 26.18386,level: 86}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 23.80350909090909, current_output: 26.18386,level: 86}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3629}) CREATE (b)-[r:Supply{max_supply: 33.32490909090909, current_output: 36.6574,level: 86}]->(g);
CREATE (n: Building {id: 3630, name:"building_subsistence_farmslevel", level:514});
MATCH (g: Goods{code: 7}), (b: Building{id:3630}) CREATE (b)-[r:Supply{max_supply: 1204.8262727272727, current_output: 1325.3089,level: 514}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3630}) CREATE (b)-[r:Supply{max_supply: 200.8043727272727, current_output: 220.88481,level: 514}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3630}) CREATE (b)-[r:Supply{max_supply: 200.8043727272727, current_output: 220.88481,level: 514}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3630}) CREATE (b)-[r:Supply{max_supply: 200.8043727272727, current_output: 220.88481,level: 514}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3630}) CREATE (b)-[r:Supply{max_supply: 200.8043727272727, current_output: 220.88481,level: 514}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3630}) CREATE (b)-[r:Supply{max_supply: 200.8043727272727, current_output: 220.88481,level: 514}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3630}) CREATE (b)-[r:Supply{max_supply: 281.1261272727273, current_output: 309.23874,level: 514}]->(g);
CREATE (n: Building {id: 3631, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3631}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 12.351448359089456, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3631}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.097420777066025, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3631}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 68.38968310826411,level: 2}]->(g);
CREATE (n: Building {id: 3632, name:"building_subsistence_farmslevel", level:184});
MATCH (g: Goods{code: 7}), (b: Building{id:3632}) CREATE (b)-[r:Supply{max_supply: 404.9803181818181, current_output: 445.47835,level: 184}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3632}) CREATE (b)-[r:Supply{max_supply: 67.49671818181818, current_output: 74.24639,level: 184}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3632}) CREATE (b)-[r:Supply{max_supply: 67.49671818181818, current_output: 74.24639,level: 184}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3632}) CREATE (b)-[r:Supply{max_supply: 67.49671818181818, current_output: 74.24639,level: 184}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3632}) CREATE (b)-[r:Supply{max_supply: 67.49671818181818, current_output: 74.24639,level: 184}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3632}) CREATE (b)-[r:Supply{max_supply: 67.49671818181818, current_output: 74.24639,level: 184}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3632}) CREATE (b)-[r:Supply{max_supply: 94.49539999999999, current_output: 103.94494,level: 184}]->(g);
CREATE (n: Building {id: 3633, name:"building_subsistence_farmslevel", level:886});
MATCH (g: Goods{code: 7}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 2073.2400000000002, current_output: 2591.55,level: 886}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 345.54, current_output: 431.925,level: 886}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 345.54, current_output: 431.925,level: 886}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 345.54, current_output: 431.925,level: 886}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 345.54, current_output: 431.925,level: 886}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 345.54, current_output: 431.925,level: 886}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 483.75600000000003, current_output: 604.695,level: 886}]->(g);
CREATE (n: Building {id: 3634, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3634}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 6.175724179544728, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3634}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.5487103885330127, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3634}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 34.194841554132054,level: 1}]->(g);
CREATE (n: Building {id: 3635, name:"building_subsistence_farmslevel", level:544});
MATCH (g: Goods{code: 7}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 1223.8857545454543, current_output: 1346.27433,level: 544}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 203.98095454545452, current_output: 224.37905,level: 544}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 203.98095454545452, current_output: 224.37905,level: 544}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 203.98095454545452, current_output: 224.37905,level: 544}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 203.98095454545452, current_output: 224.37905,level: 544}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 203.98095454545452, current_output: 224.37905,level: 544}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 285.5733363636364, current_output: 314.13067,level: 544}]->(g);
CREATE (n: Building {id: 3636, name:"building_subsistence_farmslevel", level:402});
MATCH (g: Goods{code: 7}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 893.1515363636363, current_output: 982.46669,level: 402}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 148.8585818181818, current_output: 163.74444,level: 402}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 148.8585818181818, current_output: 163.74444,level: 402}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 148.8585818181818, current_output: 163.74444,level: 402}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 148.8585818181818, current_output: 163.74444,level: 402}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 148.8585818181818, current_output: 163.74444,level: 402}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 208.40201818181816, current_output: 229.24222,level: 402}]->(g);
CREATE (n: Building {id: 3637, name:"building_subsistence_farmslevel", level:599});
MATCH (g: Goods{code: 7}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 1352.27844, current_output: 1690.34805,level: 599}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 225.379736, current_output: 281.72467,level: 599}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 225.379736, current_output: 281.72467,level: 599}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 225.379736, current_output: 281.72467,level: 599}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 225.379736, current_output: 281.72467,level: 599}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 225.379736, current_output: 281.72467,level: 599}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3637}) CREATE (b)-[r:Supply{max_supply: 315.531632, current_output: 394.41454,level: 599}]->(g);
CREATE (n: Building {id: 3638, name:"building_subsistence_farmslevel", level:79});
MATCH (g: Goods{code: 7}), (b: Building{id:3638}) CREATE (b)-[r:Supply{max_supply: 175.1050727272727, current_output: 192.61558,level: 79}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3638}) CREATE (b)-[r:Supply{max_supply: 29.184172727272724, current_output: 32.10259,level: 79}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3638}) CREATE (b)-[r:Supply{max_supply: 29.184172727272724, current_output: 32.10259,level: 79}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3638}) CREATE (b)-[r:Supply{max_supply: 29.184172727272724, current_output: 32.10259,level: 79}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3638}) CREATE (b)-[r:Supply{max_supply: 29.184172727272724, current_output: 32.10259,level: 79}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3638}) CREATE (b)-[r:Supply{max_supply: 29.184172727272724, current_output: 32.10259,level: 79}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3638}) CREATE (b)-[r:Supply{max_supply: 40.85784545454545, current_output: 44.94363,level: 79}]->(g);
CREATE (n: Building {id: 3639, name:"building_subsistence_farmslevel", level:1177});
MATCH (g: Goods{code: 7}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 2668.164836363636, current_output: 2934.98132,level: 1177}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 444.6941363636363, current_output: 489.16355,level: 1177}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 444.6941363636363, current_output: 489.16355,level: 1177}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 444.6941363636363, current_output: 489.16355,level: 1177}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 444.6941363636363, current_output: 489.16355,level: 1177}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 444.6941363636363, current_output: 489.16355,level: 1177}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3639}) CREATE (b)-[r:Supply{max_supply: 622.5717909090909, current_output: 684.82897,level: 1177}]->(g);
CREATE (n: Building {id: 3640, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3640}) CREATE (g)-[r:Demand{max_demand: 19.897, current_input: 24.575676800080288, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3640}) CREATE (g)-[r:Demand{max_demand: 19.897, current_input: 14.121738120128269, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 159.176, current_output: 136.07495248051305,level: 4}]->(g);
CREATE (n: Building {id: 3641, name:"building_subsistence_farmslevel", level:666});
MATCH (g: Goods{code: 7}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 1627.0113545454544, current_output: 1789.71249,level: 666}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 271.1685545454545, current_output: 298.28541,level: 666}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 271.1685545454545, current_output: 298.28541,level: 666}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 271.1685545454545, current_output: 298.28541,level: 666}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 271.1685545454545, current_output: 298.28541,level: 666}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 271.1685545454545, current_output: 298.28541,level: 666}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 379.6359818181818, current_output: 417.59958,level: 666}]->(g);
CREATE (n: Building {id: 3642, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3642}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 43.230069256813096, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3642}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 24.840972719731088, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3642}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 239.36389087892434,level: 7}]->(g);
CREATE (n: Building {id: 3644, name:"building_subsistence_farmslevel", level:941});
MATCH (g: Goods{code: 7}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 2218.6521545454543, current_output: 2440.51737,level: 941}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 369.7753545454545, current_output: 406.75289,level: 941}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 369.7753545454545, current_output: 406.75289,level: 941}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 369.7753545454545, current_output: 406.75289,level: 941}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 369.7753545454545, current_output: 406.75289,level: 941}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 369.7753545454545, current_output: 406.75289,level: 941}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 517.6855, current_output: 569.45405,level: 941}]->(g);
CREATE (n: Building {id: 3645, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3645}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 18.527172538634183, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3645}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.646131165599037, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3645}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 102.58452466239615,level: 3}]->(g);
CREATE (n: Building {id: 3646, name:"building_subsistence_farmslevel", level:567});
MATCH (g: Goods{code: 7}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 1216.4871545454546, current_output: 1338.13587,level: 567}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 202.74785454545452, current_output: 223.02264,level: 567}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 202.74785454545452, current_output: 223.02264,level: 567}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 202.74785454545452, current_output: 223.02264,level: 567}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 202.74785454545452, current_output: 223.02264,level: 567}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 202.74785454545452, current_output: 223.02264,level: 567}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 283.847, current_output: 312.2317,level: 567}]->(g);
CREATE (n: Building {id: 3647, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3647}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 24.702896718178913, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3647}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.19484155413205, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3647}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 136.77936621652822,level: 4}]->(g);
CREATE (n: Building {id: 3648, name:"building_subsistence_farmslevel", level:618});
MATCH (g: Goods{code: 7}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 1435.422418181818, current_output: 1578.96466,level: 618}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 239.23706363636364, current_output: 263.16077,level: 618}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 239.23706363636364, current_output: 263.16077,level: 618}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 239.23706363636364, current_output: 263.16077,level: 618}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 239.23706363636364, current_output: 263.16077,level: 618}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 239.23706363636364, current_output: 263.16077,level: 618}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 334.93189090909084, current_output: 368.42508,level: 618}]->(g);
CREATE (n: Building {id: 3649, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3649}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 37.054345077268366, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3649}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.292262331198074, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3649}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 205.1690493247923,level: 6}]->(g);
CREATE (n: Building {id: 3650, name:"building_subsistence_farmslevel", level:658});
MATCH (g: Goods{code: 7}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 1425.9386363636363, current_output: 1568.5325,level: 658}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 237.65643636363635, current_output: 261.42208,level: 658}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 237.65643636363635, current_output: 261.42208,level: 658}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 237.65643636363635, current_output: 261.42208,level: 658}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 237.65643636363635, current_output: 261.42208,level: 658}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 237.65643636363635, current_output: 261.42208,level: 658}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 332.719009090909, current_output: 365.99091,level: 658}]->(g);
CREATE (n: Building {id: 3651, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3651}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 30.878620897723643, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3651}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 17.74355194266506, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3651}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 170.97420777066023,level: 5}]->(g);
CREATE (n: Building {id: 3652, name:"building_subsistence_farmslevel", level:1253});
MATCH (g: Goods{code: 7}), (b: Building{id:3652}) CREATE (b)-[r:Supply{max_supply: 2864.696309090909, current_output: 3151.16594,level: 1253}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3652}) CREATE (b)-[r:Supply{max_supply: 477.44938181818173, current_output: 525.19432,level: 1253}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3652}) CREATE (b)-[r:Supply{max_supply: 477.44938181818173, current_output: 525.19432,level: 1253}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3652}) CREATE (b)-[r:Supply{max_supply: 477.44938181818173, current_output: 525.19432,level: 1253}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3652}) CREATE (b)-[r:Supply{max_supply: 477.44938181818173, current_output: 525.19432,level: 1253}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3652}) CREATE (b)-[r:Supply{max_supply: 477.44938181818173, current_output: 525.19432,level: 1253}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3652}) CREATE (b)-[r:Supply{max_supply: 668.4291363636363, current_output: 735.27205,level: 1253}]->(g);
CREATE (n: Building {id: 3653, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3653}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 30.878620897723643, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3653}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 17.74355194266506, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3653}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 170.97420777066023,level: 5}]->(g);
CREATE (n: Building {id: 3654, name:"building_subsistence_farmslevel", level:1377});
MATCH (g: Goods{code: 7}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 1052.9918999999998, current_output: 1158.29109,level: 1377}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 175.49864545454543, current_output: 193.04851,level: 1377}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 175.49864545454543, current_output: 193.04851,level: 1377}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 175.49864545454543, current_output: 193.04851,level: 1377}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 175.49864545454543, current_output: 193.04851,level: 1377}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 175.49864545454543, current_output: 193.04851,level: 1377}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 245.69810909090907, current_output: 270.26792,level: 1377}]->(g);
CREATE (n: Building {id: 3655, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3655}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 37.054345077268366, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3655}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.292262331198074, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3655}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 205.1690493247923,level: 6}]->(g);
CREATE (n: Building {id: 3656, name:"building_subsistence_farmslevel", level:1422});
MATCH (g: Goods{code: 7}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 3305.595418181818, current_output: 3636.15496,level: 1422}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 550.9325636363635, current_output: 606.02582,level: 1422}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 550.9325636363635, current_output: 606.02582,level: 1422}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 550.9325636363635, current_output: 606.02582,level: 1422}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 550.9325636363635, current_output: 606.02582,level: 1422}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 550.9325636363635, current_output: 606.02582,level: 1422}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 771.3055909090908, current_output: 848.43615,level: 1422}]->(g);
CREATE (n: Building {id: 3657, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3657}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 30.878620897723643, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3657}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 17.74355194266506, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3657}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 170.97420777066023,level: 5}]->(g);
CREATE (n: Building {id: 3658, name:"building_subsistence_farmslevel", level:1366});
MATCH (g: Goods{code: 7}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 3105.997136363636, current_output: 3416.59685,level: 1366}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 517.6661818181818, current_output: 569.4328,level: 1366}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 517.6661818181818, current_output: 569.4328,level: 1366}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 517.6661818181818, current_output: 569.4328,level: 1366}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 517.6661818181818, current_output: 569.4328,level: 1366}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 517.6661818181818, current_output: 569.4328,level: 1366}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 724.7326636363636, current_output: 797.20593,level: 1366}]->(g);
CREATE (n: Building {id: 3659, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3659}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 37.054345077268366, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3659}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.292262331198074, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3659}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 205.1690493247923,level: 6}]->(g);
CREATE (n: Building {id: 3660, name:"building_subsistence_farmslevel", level:611});
MATCH (g: Goods{code: 7}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 1401.9150545454545, current_output: 1542.10656,level: 611}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 233.6525090909091, current_output: 257.01776,level: 611}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 233.6525090909091, current_output: 257.01776,level: 611}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 233.6525090909091, current_output: 257.01776,level: 611}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 233.6525090909091, current_output: 257.01776,level: 611}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 233.6525090909091, current_output: 257.01776,level: 611}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 327.1135090909091, current_output: 359.82486,level: 611}]->(g);
CREATE (n: Building {id: 3661, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3661}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 18.527172538634183, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3661}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.646131165599037, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3661}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 102.58452466239615,level: 3}]->(g);
CREATE (n: Building {id: 3662, name:"building_subsistence_farmslevel", level:762});
MATCH (g: Goods{code: 7}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 1725.0384545454544, current_output: 1897.5423,level: 762}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 287.5064090909091, current_output: 316.25705,level: 762}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 287.5064090909091, current_output: 316.25705,level: 762}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 287.5064090909091, current_output: 316.25705,level: 762}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 287.5064090909091, current_output: 316.25705,level: 762}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 287.5064090909091, current_output: 316.25705,level: 762}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 402.5089727272727, current_output: 442.75987,level: 762}]->(g);
CREATE (n: Building {id: 3663, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3663}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 37.054345077268366, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3663}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.292262331198074, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3663}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 205.1690493247923,level: 6}]->(g);
CREATE (n: Building {id: 3664, name:"building_subsistence_farmslevel", level:818});
MATCH (g: Goods{code: 7}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 1762.8799727272726, current_output: 1939.16797,level: 818}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 293.81332727272724, current_output: 323.19466,level: 818}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 293.81332727272724, current_output: 323.19466,level: 818}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 293.81332727272724, current_output: 323.19466,level: 818}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 293.81332727272724, current_output: 323.19466,level: 818}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 293.81332727272724, current_output: 323.19466,level: 818}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 411.3386545454545, current_output: 452.47252,level: 818}]->(g);
CREATE (n: Building {id: 3665, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3665}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 37.054345077268366, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3665}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 21.292262331198074, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3665}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 205.1690493247923,level: 6}]->(g);
CREATE (n: Building {id: 3666, name:"building_subsistence_farmslevel", level:789});
MATCH (g: Goods{code: 7}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 1811.133718181818, current_output: 1992.24709,level: 789}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 301.85561818181816, current_output: 332.04118,level: 789}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 301.85561818181816, current_output: 332.04118,level: 789}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 301.85561818181816, current_output: 332.04118,level: 789}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 301.85561818181816, current_output: 332.04118,level: 789}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 301.85561818181816, current_output: 332.04118,level: 789}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 422.59786363636357, current_output: 464.85765,level: 789}]->(g);
CREATE (n: Building {id: 3667, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3667}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 12.351448359089456, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3667}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.097420777066025, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3667}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 68.38968310826411,level: 2}]->(g);
CREATE (n: Building {id: 3668, name:"building_subsistence_farmslevel", level:156});
MATCH (g: Goods{code: 7}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 333.4593545454545, current_output: 366.80529,level: 156}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 55.57655454545454, current_output: 61.13421,level: 156}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 55.57655454545454, current_output: 61.13421,level: 156}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 55.57655454545454, current_output: 61.13421,level: 156}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 55.57655454545454, current_output: 61.13421,level: 156}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 55.57655454545454, current_output: 61.13421,level: 156}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 77.80718181818182, current_output: 85.5879,level: 156}]->(g);
CREATE (n: Building {id: 3686, name:"building_subsistence_farmslevel", level:23});
MATCH (g: Goods{code: 7}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 30.72845454545454, current_output: 33.8013,level: 23}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 5.12140909090909, current_output: 5.63355,level: 23}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 5.12140909090909, current_output: 5.63355,level: 23}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 5.12140909090909, current_output: 5.63355,level: 23}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 5.12140909090909, current_output: 5.63355,level: 23}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 5.12140909090909, current_output: 5.63355,level: 23}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 7.169972727272727, current_output: 7.88697,level: 23}]->(g);
CREATE (n: Building {id: 3691, name:"building_subsistence_pastureslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 8.030218181818181, current_output: 8.83324,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 12.045327272727272, current_output: 13.24986,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 4.015109090909091, current_output: 4.41662,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 8.030218181818181, current_output: 8.83324,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 8.030218181818181, current_output: 8.83324,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 8.030218181818181, current_output: 8.83324,level: 44}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 26.660327272727272, current_output: 29.32636,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 11.242299999999998, current_output: 12.36653,level: 44}]->(g);
CREATE (n: Building {id: 3699, name:"building_subsistence_farmslevel", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:3699}) CREATE (b)-[r:Supply{max_supply: 0.30533636363636363, current_output: 0.33587,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3699}) CREATE (b)-[r:Supply{max_supply: 0.050881818181818174, current_output: 0.05597,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3699}) CREATE (b)-[r:Supply{max_supply: 0.050881818181818174, current_output: 0.05597,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3699}) CREATE (b)-[r:Supply{max_supply: 0.050881818181818174, current_output: 0.05597,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3699}) CREATE (b)-[r:Supply{max_supply: 0.050881818181818174, current_output: 0.05597,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3699}) CREATE (b)-[r:Supply{max_supply: 0.050881818181818174, current_output: 0.05597,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3699}) CREATE (b)-[r:Supply{max_supply: 0.07124545454545453, current_output: 0.07837,level: 14}]->(g);
CREATE (n: Building {id: 3700, name:"building_subsistence_farmslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 15.091554545454544, current_output: 16.60071,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 2.515254545454545, current_output: 2.76678,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 2.515254545454545, current_output: 2.76678,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 2.515254545454545, current_output: 2.76678,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 2.515254545454545, current_output: 2.76678,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 2.515254545454545, current_output: 2.76678,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 3.521363636363636, current_output: 3.8735,level: 44}]->(g);
CREATE (n: Building {id: 33558209, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:33558209}) CREATE (b)-[r:Supply{max_supply: 44.215495049504945, current_output: 44.65765,level: 2}]->(g);
CREATE (n: Building {id: 3946, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 3947, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4031, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4032, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 16781274, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:16781274}) CREATE (g)-[r:Demand{max_demand: 4.962, current_input: 1.2080681365853996, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:16781274}) CREATE (b)-[r:Supply{max_supply: 29.772, current_output: 7.248408819512397,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:16781274}) CREATE (b)-[r:Supply{max_supply: 14.886, current_output: 3.6242044097561985,level: 1}]->(g);
CREATE (n: Building {id: 16781404, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:16781404}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.4346395336263598, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16781404}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 9.738558134505439,level: 2}]->(g);
CREATE (n: Building {id: 50335983, name:"building_tea_plantationlevel", level:6});
MATCH (g: Goods{code: 40}), (b: Building{id:50335983}) CREATE (b)-[r:Supply{max_supply: 106.1172, current_output: 111.42306,level: 6}]->(g);
CREATE (n: Building {id: 4411, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4412, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4413, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4414, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4415, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4416, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4417, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4418, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4419, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4420, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4421, name:"building_conscription_centerlevel", level:14});
CREATE (n: Building {id: 4422, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4423, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4424, name:"building_conscription_centerlevel", level:20});
CREATE (n: Building {id: 4425, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4426, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4427, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4428, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4429, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4430, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4431, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4432, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4433, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4434, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4435, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4436, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4437, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4438, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4439, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4440, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4441, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4442, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4443, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4444, name:"building_conscription_centerlevel", level:18});
CREATE (n: Building {id: 33559122, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:33559122}) CREATE (g)-[r:Demand{max_demand: 9.7806, current_input: 21.58876351333491, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33559122}) CREATE (g)-[r:Demand{max_demand: 19.5612, current_input: 24.160915164182065, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33559122}) CREATE (g)-[r:Demand{max_demand: 24.4515, current_input: 3.10983621183863, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33559122}) CREATE (g)-[r:Demand{max_demand: 4.8903, current_input: 1.1906117711292987, level: 3}]->(b);
CREATE (n: Building {id: 67113569, name:"building_cotton_plantationlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:67113569}) CREATE (b)-[r:Supply{max_supply: 70.76, current_output: 89.1576,level: 2}]->(g);
CREATE (n: Building {id: 4783, name:"building_trade_centerlevel", level:36});
CREATE (n: Building {id: 33559247, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:33559247}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 44.4,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:33559247}) CREATE (b)-[r:Supply{max_supply: 12.0, current_output: 13.32,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:33559247}) CREATE (b)-[r:Supply{max_supply: 18.0, current_output: 19.98,level: 2}]->(g);
CREATE (n: Building {id: 67113682, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:67113682}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 22.2,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:67113682}) CREATE (b)-[r:Supply{max_supply: 18.0, current_output: 19.98,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:67113682}) CREATE (b)-[r:Supply{max_supply: 12.0, current_output: 13.32,level: 2}]->(g);
CREATE (n: Building {id: 83890987, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:83890987}) CREATE (b)-[r:Supply{max_supply: 35.396, current_output: 44.245,level: 1}]->(g);
CREATE (n: Building {id: 83891623, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:83891623}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 37.054345077268366, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:83891623}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.5436772482985748, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:83891623}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 33.81551587244787,level: 1}]->(g);
CREATE (n: Building {id: 50337200, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:50337200}) CREATE (g)-[r:Demand{max_demand: 5.20197, current_input: 11.482332385892768, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50337200}) CREATE (g)-[r:Demand{max_demand: 10.40395, current_input: 12.850385115554875, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50337200}) CREATE (g)-[r:Demand{max_demand: 13.00493, current_input: 1.6540172278357792, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50337200}) CREATE (g)-[r:Demand{max_demand: 2.60098, current_input: 0.6332448734171489, level: 2}]->(b);
CREATE (n: Building {id: 50337201, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:50337201}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 7.19625450444497, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50337201}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 8.053638388060689, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50337201}) CREATE (g)-[r:Demand{max_demand: 8.1505, current_input: 1.0366120706128765, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50337201}) CREATE (g)-[r:Demand{max_demand: 1.6301, current_input: 0.3968705903764329, level: 1}]->(b);
CREATE (n: Building {id: 16782823, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782823}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 7.19625450444497, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782823}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 8.053638388060689, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782823}) CREATE (g)-[r:Demand{max_demand: 8.1505, current_input: 1.0366120706128765, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782823}) CREATE (g)-[r:Demand{max_demand: 1.6301, current_input: 0.3968705903764329, level: 1}]->(b);
CREATE (n: Building {id: 16782850, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:16782850}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.2173197668131799, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782850}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 9.738558134505439,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:16782850}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 2.4346395336263598,level: 1}]->(g);
CREATE (n: Building {id: 16782865, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:16782865}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.2173197668131799, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:16782865}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 7.303918600879078,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:16782865}) CREATE (b)-[r:Supply{max_supply: 14.999999999999998, current_output: 3.651959300439539,level: 1}]->(g);
CREATE (n: Building {id: 33560152, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:33560152}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 2.4346395336263593, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33560152}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 19.477116269010875,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:33560152}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 4.869279067252719,level: 2}]->(g);
CREATE (n: Building {id: 16782955, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:16782955}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 22.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:16782955}) CREATE (b)-[r:Supply{max_supply: 5.999999999999999, current_output: 6.6,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:16782955}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.9,level: 1}]->(g);
CREATE (n: Building {id: 16782967, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:16782967}) CREATE (b)-[r:Supply{max_supply: 19.904, current_output: 21.8944,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:16782967}) CREATE (b)-[r:Supply{max_supply: 5.9712, current_output: 6.56832,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:16782967}) CREATE (b)-[r:Supply{max_supply: 8.9568, current_output: 9.85248,level: 1}]->(g);
CREATE (n: Building {id: 16783032, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783032}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:16783032}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 5839, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5839}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:5839}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 50337489, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:50337489}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 7.19625450444497, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50337489}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 8.053638388060689, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50337489}) CREATE (g)-[r:Demand{max_demand: 8.1505, current_input: 1.0366120706128765, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50337489}) CREATE (g)-[r:Demand{max_demand: 1.6301, current_input: 0.3968705903764329, level: 1}]->(b);
CREATE (n: Building {id: 134223571, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:134223571}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 7.19625450444497, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:134223571}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 8.053638388060689, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:134223571}) CREATE (g)-[r:Demand{max_demand: 8.1505, current_input: 1.0366120706128765, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:134223571}) CREATE (g)-[r:Demand{max_demand: 1.6301, current_input: 0.3968705903764329, level: 1}]->(b);
CREATE (n: Building {id: 16783061, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:16783061}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.2173197668131799, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783061}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 9.738558134505439,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:16783061}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 2.4346395336263598,level: 1}]->(g);
CREATE (n: Building {id: 33560279, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33560279}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:33560279}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 50337508, name:"building_construction_sectorlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:50337508}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 28.78501801777988, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50337508}) CREATE (g)-[r:Demand{max_demand: 26.0816, current_input: 32.214553552242755, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50337508}) CREATE (g)-[r:Demand{max_demand: 32.602, current_input: 4.146448282451506, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50337508}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 1.5874823615057316, level: 4}]->(b);
CREATE (n: Building {id: 67114735, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:67114735}) CREATE (g)-[r:Demand{max_demand: 3.23933, current_input: 7.150188057138741, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:67114735}) CREATE (g)-[r:Demand{max_demand: 6.47866, current_input: 8.00208344260985, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:67114735}) CREATE (g)-[r:Demand{max_demand: 8.09833, current_input: 1.02997688851069, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:67114735}) CREATE (g)-[r:Demand{max_demand: 1.61966, current_input: 0.394328826703327, level: 1}]->(b);
CREATE (n: Building {id: 16783095, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:16783095}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.2173197668131799, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783095}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 9.738558134505439,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:16783095}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 2.4346395336263598,level: 1}]->(g);
CREATE (n: Building {id: 5880, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5880}) CREATE (g)-[r:Demand{max_demand: 0.414, current_input: 0.5113499620663035, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5880}) CREATE (g)-[r:Demand{max_demand: 0.414, current_input: 0.2938332201705334, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5880}) CREATE (b)-[r:Supply{max_supply: 3.312, current_output: 2.831332880682133,level: 1}]->(g);
CREATE (n: Building {id: 5883, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5883}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:5883}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 16783112, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:16783112}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 5902, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:5902}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.4346395336263598, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5902}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 9.738558134505439,level: 2}]->(g);
CREATE (n: Building {id: 5907, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5907}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 7.19625450444497, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5907}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 8.053638388060689, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5907}) CREATE (g)-[r:Demand{max_demand: 8.1505, current_input: 1.0366120706128765, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5907}) CREATE (g)-[r:Demand{max_demand: 1.6301, current_input: 0.3968705903764329, level: 1}]->(b);
CREATE (n: Building {id: 16783128, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783128}) CREATE (g)-[r:Demand{max_demand: 4.07525, current_input: 8.995318130556212, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783128}) CREATE (g)-[r:Demand{max_demand: 12.22575, current_input: 15.100571977613791, level: 1}]->(b);
CREATE (n: Building {id: 5915, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5915}) CREATE (g)-[r:Demand{max_demand: 0.483, current_input: 0.5965749557440208, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5915}) CREATE (g)-[r:Demand{max_demand: 0.483, current_input: 0.34280542353228904, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5915}) CREATE (b)-[r:Supply{max_supply: 3.864, current_output: 3.303221694129156,level: 1}]->(g);
CREATE (n: Building {id: 5920, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:5920}) CREATE (g)-[r:Demand{max_demand: 9.7806, current_input: 21.58876351333491, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5920}) CREATE (g)-[r:Demand{max_demand: 19.5612, current_input: 24.160915164182065, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5920}) CREATE (g)-[r:Demand{max_demand: 24.4515, current_input: 3.10983621183863, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5920}) CREATE (g)-[r:Demand{max_demand: 4.8903, current_input: 1.1906117711292987, level: 3}]->(b);
CREATE (n: Building {id: 16783142, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:16783142}) CREATE (g)-[r:Demand{max_demand: 9.7806, current_input: 21.58876351333491, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783142}) CREATE (g)-[r:Demand{max_demand: 19.5612, current_input: 24.160915164182065, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783142}) CREATE (g)-[r:Demand{max_demand: 24.4515, current_input: 3.10983621183863, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783142}) CREATE (g)-[r:Demand{max_demand: 4.8903, current_input: 1.1906117711292987, level: 3}]->(b);
CREATE (n: Building {id: 16783151, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16783151}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 14.39250900888994, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783151}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 16.107276776121378, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783151}) CREATE (g)-[r:Demand{max_demand: 16.301, current_input: 2.073224141225753, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783151}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 0.7937411807528658, level: 2}]->(b);
CREATE (n: Building {id: 16783155, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16783155}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 14.39250900888994, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783155}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 16.107276776121378, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783155}) CREATE (g)-[r:Demand{max_demand: 16.301, current_input: 2.073224141225753, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783155}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 0.7937411807528658, level: 2}]->(b);
CREATE (n: Building {id: 16783165, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16783165}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 14.39250900888994, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783165}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 16.107276776121378, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783165}) CREATE (g)-[r:Demand{max_demand: 16.301, current_input: 2.073224141225753, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783165}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 0.7937411807528658, level: 2}]->(b);
CREATE (n: Building {id: 5952, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:5952}) CREATE (g)-[r:Demand{max_demand: 9.7806, current_input: 21.58876351333491, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5952}) CREATE (g)-[r:Demand{max_demand: 19.5612, current_input: 24.160915164182065, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5952}) CREATE (g)-[r:Demand{max_demand: 24.4515, current_input: 3.10983621183863, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5952}) CREATE (g)-[r:Demand{max_demand: 4.8903, current_input: 1.1906117711292987, level: 3}]->(b);
CREATE (n: Building {id: 5954, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:5954}) CREATE (g)-[r:Demand{max_demand: 9.7806, current_input: 21.58876351333491, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5954}) CREATE (g)-[r:Demand{max_demand: 19.5612, current_input: 24.160915164182065, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5954}) CREATE (g)-[r:Demand{max_demand: 24.4515, current_input: 3.10983621183863, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5954}) CREATE (g)-[r:Demand{max_demand: 4.8903, current_input: 1.1906117711292987, level: 3}]->(b);
CREATE (n: Building {id: 83892037, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:83892037}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 14.39250900888994, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:83892037}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 16.107276776121378, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:83892037}) CREATE (g)-[r:Demand{max_demand: 16.301, current_input: 2.073224141225753, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:83892037}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 0.7937411807528658, level: 2}]->(b);
CREATE (n: Building {id: 16783175, name:"building_construction_sectorlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:16783175}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 28.78501801777988, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783175}) CREATE (g)-[r:Demand{max_demand: 26.0816, current_input: 32.214553552242755, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783175}) CREATE (g)-[r:Demand{max_demand: 32.602, current_input: 4.146448282451506, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783175}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 1.5874823615057316, level: 4}]->(b);
CREATE (n: Building {id: 5961, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:5961}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 14.39250900888994, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5961}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 16.107276776121378, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5961}) CREATE (g)-[r:Demand{max_demand: 16.301, current_input: 2.073224141225753, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5961}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 0.7937411807528658, level: 2}]->(b);
CREATE (n: Building {id: 16783182, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:16783182}) CREATE (g)-[r:Demand{max_demand: 9.7806, current_input: 21.58876351333491, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783182}) CREATE (g)-[r:Demand{max_demand: 19.5612, current_input: 24.160915164182065, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783182}) CREATE (g)-[r:Demand{max_demand: 24.4515, current_input: 3.10983621183863, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783182}) CREATE (g)-[r:Demand{max_demand: 4.8903, current_input: 1.1906117711292987, level: 3}]->(b);
CREATE (n: Building {id: 16783183, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:16783183}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.4346395336263598, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783183}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 9.738558134505439,level: 2}]->(g);
CREATE (n: Building {id: 5968, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5968}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 7.19625450444497, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5968}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 8.053638388060689, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5968}) CREATE (g)-[r:Demand{max_demand: 8.1505, current_input: 1.0366120706128765, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5968}) CREATE (g)-[r:Demand{max_demand: 1.6301, current_input: 0.3968705903764329, level: 1}]->(b);
CREATE (n: Building {id: 5998, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5998}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 7.19625450444497, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5998}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 8.053638388060689, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5998}) CREATE (g)-[r:Demand{max_demand: 8.1505, current_input: 1.0366120706128765, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5998}) CREATE (g)-[r:Demand{max_demand: 1.6301, current_input: 0.3968705903764329, level: 1}]->(b);
CREATE (n: Building {id: 83892083, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:83892083}) CREATE (b)-[r:Supply{max_supply: 22.1225, current_output: 22.1225,level: 1}]->(g);
CREATE (n: Building {id: 50337685, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:50337685}) CREATE (b)-[r:Supply{max_supply: 44.215495049504945, current_output: 44.65765,level: 2}]->(g);
CREATE (n: Building {id: 6045, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 6047, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6047}) CREATE (g)-[r:Demand{max_demand: 0.516, current_input: 0.6373347353290159, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:6047}) CREATE (g)-[r:Demand{max_demand: 0.516, current_input: 0.3662269120966069, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6047}) CREATE (b)-[r:Supply{max_supply: 4.128, current_output: 3.5289076483864275,level: 1}]->(g);
CREATE (n: Building {id: 6053, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:6053}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.2173197668131799, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:6053}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 4.8692790672527195,level: 1}]->(g);
CREATE (n: Building {id: 16783300, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:16783300}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 16783301, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:16783301}) CREATE (b)-[r:Supply{max_supply: 44.215495049504945, current_output: 44.65765,level: 2}]->(g);
CREATE (n: Building {id: 6089, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 16783316, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783316}) CREATE (b)-[r:Supply{max_supply: 35.4036, current_output: 44.2545,level: 1}]->(g);
CREATE (n: Building {id: 6112, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:6112}) CREATE (b)-[r:Supply{max_supply: 35.4036, current_output: 44.2545,level: 1}]->(g);
CREATE (n: Building {id: 33560565, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:33560565}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 6152, name:"building_cotton_plantationlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:6152}) CREATE (b)-[r:Supply{max_supply: 70.74479365079365, current_output: 89.13844,level: 2}]->(g);
CREATE (n: Building {id: 16783383, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:16783383}) CREATE (b)-[r:Supply{max_supply: 66.32324509803921, current_output: 67.64971,level: 3}]->(g);
CREATE (n: Building {id: 16783414, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:16783414}) CREATE (b)-[r:Supply{max_supply: 22.142, current_output: 22.142,level: 1}]->(g);
CREATE (n: Building {id: 16783421, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:16783421}) CREATE (b)-[r:Supply{max_supply: 44.23, current_output: 44.6723,level: 2}]->(g);
CREATE (n: Building {id: 16783442, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783442}) CREATE (b)-[r:Supply{max_supply: 35.38, current_output: 44.225,level: 1}]->(g);
CREATE (n: Building {id: 33560667, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:33560667}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 16783452, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:16783452}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 33560678, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:33560678}) CREATE (b)-[r:Supply{max_supply: 22.1175, current_output: 22.1175,level: 1}]->(g);
CREATE (n: Building {id: 33560730, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:33560730}) CREATE (g)-[r:Demand{max_demand: 9.7806, current_input: 21.58876351333491, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33560730}) CREATE (g)-[r:Demand{max_demand: 19.5612, current_input: 24.160915164182065, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33560730}) CREATE (g)-[r:Demand{max_demand: 24.4515, current_input: 3.10983621183863, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33560730}) CREATE (g)-[r:Demand{max_demand: 4.8903, current_input: 1.1906117711292987, level: 3}]->(b);
CREATE (n: Building {id: 16783520, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16783520}) CREATE (g)-[r:Demand{max_demand: 6.5204, current_input: 14.39250900888994, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783520}) CREATE (g)-[r:Demand{max_demand: 13.0408, current_input: 16.107276776121378, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783520}) CREATE (g)-[r:Demand{max_demand: 16.301, current_input: 2.073224141225753, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783520}) CREATE (g)-[r:Demand{max_demand: 3.2602, current_input: 0.7937411807528658, level: 2}]->(b);
CREATE (n: Building {id: 6327, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6327}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 6.175724179544728, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:6327}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.5487103885330127, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6327}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 34.194841554132054,level: 1}]->(g);
CREATE (n: Building {id: 6495, name:"building_subsistence_rice_paddieslevel", level:915});
CREATE (n: Building {id: 6496, name:"building_subsistence_rice_paddieslevel", level:846});
CREATE (n: Building {id: 6497, name:"building_subsistence_pastureslevel", level:72});
CREATE (n: Building {id: 6498, name:"building_subsistence_pastureslevel", level:48});
CREATE (n: Building {id: 6499, name:"building_subsistence_pastureslevel", level:35});
CREATE (n: Building {id: 6500, name:"building_subsistence_pastureslevel", level:53});
CREATE (n: Building {id: 6501, name:"building_subsistence_rice_paddieslevel", level:876});
CREATE (n: Building {id: 6502, name:"building_subsistence_rice_paddieslevel", level:373});
MATCH (g: Goods{code: 7}), (b: Building{id:6502}) CREATE (b)-[r:Supply{max_supply: 0.041027272727272725, current_output: 0.04513,level: 373}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6502}) CREATE (b)-[r:Supply{max_supply: 0.005590909090909091, current_output: 0.00615,level: 373}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6502}) CREATE (b)-[r:Supply{max_supply: 0.005590909090909091, current_output: 0.00615,level: 373}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6502}) CREATE (b)-[r:Supply{max_supply: 0.007454545454545454, current_output: 0.0082,level: 373}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6502}) CREATE (b)-[r:Supply{max_supply: 0.007454545454545454, current_output: 0.0082,level: 373}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6502}) CREATE (b)-[r:Supply{max_supply: 0.007454545454545454, current_output: 0.0082,level: 373}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6502}) CREATE (b)-[r:Supply{max_supply: 0.011181818181818182, current_output: 0.0123,level: 373}]->(g);
CREATE (n: Building {id: 6503, name:"building_subsistence_rice_paddieslevel", level:282});
CREATE (n: Building {id: 6504, name:"building_subsistence_rice_paddieslevel", level:455});
CREATE (n: Building {id: 6505, name:"building_subsistence_pastureslevel", level:41});
MATCH (g: Goods{code: 7}), (b: Building{id:6505}) CREATE (b)-[r:Supply{max_supply: 0.0002, current_output: 0.00018,level: 41}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6505}) CREATE (b)-[r:Supply{max_supply: 0.0003, current_output: 0.00027,level: 41}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6505}) CREATE (b)-[r:Supply{max_supply: 0.0001, current_output: 9e-05,level: 41}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6505}) CREATE (b)-[r:Supply{max_supply: 0.0002, current_output: 0.00018,level: 41}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6505}) CREATE (b)-[r:Supply{max_supply: 0.0002, current_output: 0.00018,level: 41}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6505}) CREATE (b)-[r:Supply{max_supply: 0.0002, current_output: 0.00018,level: 41}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:6505}) CREATE (b)-[r:Supply{max_supply: 0.0006777777777777777, current_output: 0.00061,level: 41}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6505}) CREATE (b)-[r:Supply{max_supply: 0.0002777777777777778, current_output: 0.00025,level: 41}]->(g);
CREATE (n: Building {id: 6506, name:"building_subsistence_pastureslevel", level:42});
CREATE (n: Building {id: 6507, name:"building_subsistence_rice_paddieslevel", level:878});
MATCH (g: Goods{code: 7}), (b: Building{id:6507}) CREATE (b)-[r:Supply{max_supply: 0.048281818181818176, current_output: 0.05311,level: 878}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6507}) CREATE (b)-[r:Supply{max_supply: 0.006581818181818181, current_output: 0.00724,level: 878}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6507}) CREATE (b)-[r:Supply{max_supply: 0.006581818181818181, current_output: 0.00724,level: 878}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6507}) CREATE (b)-[r:Supply{max_supply: 0.008772727272727272, current_output: 0.00965,level: 878}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6507}) CREATE (b)-[r:Supply{max_supply: 0.008772727272727272, current_output: 0.00965,level: 878}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6507}) CREATE (b)-[r:Supply{max_supply: 0.008772727272727272, current_output: 0.00965,level: 878}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6507}) CREATE (b)-[r:Supply{max_supply: 0.013163636363636362, current_output: 0.01448,level: 878}]->(g);
CREATE (n: Building {id: 6508, name:"building_subsistence_rice_paddieslevel", level:710});
CREATE (n: Building {id: 6509, name:"building_subsistence_rice_paddieslevel", level:394});
CREATE (n: Building {id: 6510, name:"building_subsistence_rice_paddieslevel", level:514});
CREATE (n: Building {id: 6511, name:"building_subsistence_rice_paddieslevel", level:886});
MATCH (g: Goods{code: 7}), (b: Building{id:6511}) CREATE (b)-[r:Supply{max_supply: 0.048728, current_output: 0.06091,level: 886}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6511}) CREATE (b)-[r:Supply{max_supply: 0.00664, current_output: 0.0083,level: 886}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6511}) CREATE (b)-[r:Supply{max_supply: 0.00664, current_output: 0.0083,level: 886}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6511}) CREATE (b)-[r:Supply{max_supply: 0.008856, current_output: 0.01107,level: 886}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6511}) CREATE (b)-[r:Supply{max_supply: 0.008856, current_output: 0.01107,level: 886}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6511}) CREATE (b)-[r:Supply{max_supply: 0.008856, current_output: 0.01107,level: 886}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6511}) CREATE (b)-[r:Supply{max_supply: 0.013288, current_output: 0.01661,level: 886}]->(g);
CREATE (n: Building {id: 6512, name:"building_subsistence_rice_paddieslevel", level:544});
CREATE (n: Building {id: 6513, name:"building_subsistence_rice_paddieslevel", level:402});
CREATE (n: Building {id: 6514, name:"building_subsistence_rice_paddieslevel", level:599});
CREATE (n: Building {id: 6515, name:"building_subsistence_rice_paddieslevel", level:1177});
CREATE (n: Building {id: 6516, name:"building_subsistence_rice_paddieslevel", level:666});
MATCH (g: Goods{code: 7}), (b: Building{id:6516}) CREATE (b)-[r:Supply{max_supply: 0.14651818181818183, current_output: 0.16117,level: 666}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6516}) CREATE (b)-[r:Supply{max_supply: 0.01997272727272727, current_output: 0.02197,level: 666}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6516}) CREATE (b)-[r:Supply{max_supply: 0.01997272727272727, current_output: 0.02197,level: 666}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6516}) CREATE (b)-[r:Supply{max_supply: 0.026636363636363635, current_output: 0.0293,level: 666}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6516}) CREATE (b)-[r:Supply{max_supply: 0.026636363636363635, current_output: 0.0293,level: 666}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6516}) CREATE (b)-[r:Supply{max_supply: 0.026636363636363635, current_output: 0.0293,level: 666}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6516}) CREATE (b)-[r:Supply{max_supply: 0.03995454545454545, current_output: 0.04395,level: 666}]->(g);
CREATE (n: Building {id: 6517, name:"building_subsistence_rice_paddieslevel", level:941});
CREATE (n: Building {id: 6518, name:"building_subsistence_rice_paddieslevel", level:567});
CREATE (n: Building {id: 6519, name:"building_subsistence_rice_paddieslevel", level:618});
CREATE (n: Building {id: 6520, name:"building_subsistence_rice_paddieslevel", level:658});
MATCH (g: Goods{code: 7}), (b: Building{id:6520}) CREATE (b)-[r:Supply{max_supply: 0.03618181818181818, current_output: 0.0398,level: 658}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6520}) CREATE (b)-[r:Supply{max_supply: 0.004927272727272727, current_output: 0.00542,level: 658}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6520}) CREATE (b)-[r:Supply{max_supply: 0.004927272727272727, current_output: 0.00542,level: 658}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6520}) CREATE (b)-[r:Supply{max_supply: 0.006572727272727273, current_output: 0.00723,level: 658}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6520}) CREATE (b)-[r:Supply{max_supply: 0.006572727272727273, current_output: 0.00723,level: 658}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6520}) CREATE (b)-[r:Supply{max_supply: 0.006572727272727273, current_output: 0.00723,level: 658}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6520}) CREATE (b)-[r:Supply{max_supply: 0.009863636363636363, current_output: 0.01085,level: 658}]->(g);
CREATE (n: Building {id: 6521, name:"building_subsistence_rice_paddieslevel", level:1253});
CREATE (n: Building {id: 6522, name:"building_subsistence_rice_paddieslevel", level:1377});
CREATE (n: Building {id: 6523, name:"building_subsistence_rice_paddieslevel", level:1422});
MATCH (g: Goods{code: 7}), (b: Building{id:6523}) CREATE (b)-[r:Supply{max_supply: 1.3295636363636363, current_output: 1.46252,level: 1422}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6523}) CREATE (b)-[r:Supply{max_supply: 0.1813, current_output: 0.19943,level: 1422}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6523}) CREATE (b)-[r:Supply{max_supply: 0.1813, current_output: 0.19943,level: 1422}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6523}) CREATE (b)-[r:Supply{max_supply: 0.2417363636363636, current_output: 0.26591,level: 1422}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6523}) CREATE (b)-[r:Supply{max_supply: 0.2417363636363636, current_output: 0.26591,level: 1422}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6523}) CREATE (b)-[r:Supply{max_supply: 0.2417363636363636, current_output: 0.26591,level: 1422}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6523}) CREATE (b)-[r:Supply{max_supply: 0.3626090909090909, current_output: 0.39887,level: 1422}]->(g);
CREATE (n: Building {id: 6524, name:"building_subsistence_rice_paddieslevel", level:1366});
CREATE (n: Building {id: 6525, name:"building_subsistence_rice_paddieslevel", level:611});
CREATE (n: Building {id: 6526, name:"building_subsistence_rice_paddieslevel", level:762});
CREATE (n: Building {id: 6527, name:"building_subsistence_rice_paddieslevel", level:818});
MATCH (g: Goods{code: 7}), (b: Building{id:6527}) CREATE (b)-[r:Supply{max_supply: 1.0797545454545454, current_output: 1.18773,level: 818}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6527}) CREATE (b)-[r:Supply{max_supply: 0.1472363636363636, current_output: 0.16196,level: 818}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6527}) CREATE (b)-[r:Supply{max_supply: 0.1472363636363636, current_output: 0.16196,level: 818}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6527}) CREATE (b)-[r:Supply{max_supply: 0.1963181818181818, current_output: 0.21595,level: 818}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6527}) CREATE (b)-[r:Supply{max_supply: 0.1963181818181818, current_output: 0.21595,level: 818}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6527}) CREATE (b)-[r:Supply{max_supply: 0.1963181818181818, current_output: 0.21595,level: 818}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6527}) CREATE (b)-[r:Supply{max_supply: 0.2944727272727272, current_output: 0.32392,level: 818}]->(g);
CREATE (n: Building {id: 6528, name:"building_subsistence_rice_paddieslevel", level:789});
CREATE (n: Building {id: 6529, name:"building_subsistence_rice_paddieslevel", level:156});
MATCH (g: Goods{code: 7}), (b: Building{id:6529}) CREATE (b)-[r:Supply{max_supply: 0.05147272727272727, current_output: 0.05662,level: 156}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6529}) CREATE (b)-[r:Supply{max_supply: 0.0070181818181818175, current_output: 0.00772,level: 156}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6529}) CREATE (b)-[r:Supply{max_supply: 0.0070181818181818175, current_output: 0.00772,level: 156}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6529}) CREATE (b)-[r:Supply{max_supply: 0.009354545454545454, current_output: 0.01029,level: 156}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6529}) CREATE (b)-[r:Supply{max_supply: 0.009354545454545454, current_output: 0.01029,level: 156}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6529}) CREATE (b)-[r:Supply{max_supply: 0.009354545454545454, current_output: 0.01029,level: 156}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6529}) CREATE (b)-[r:Supply{max_supply: 0.014036363636363635, current_output: 0.01544,level: 156}]->(g);
CREATE (n: Building {id: 6530, name:"building_subsistence_pastureslevel", level:23});
MATCH (g: Goods{code: 7}), (b: Building{id:6530}) CREATE (b)-[r:Supply{max_supply: 0.004254545454545454, current_output: 0.00468,level: 23}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6530}) CREATE (b)-[r:Supply{max_supply: 0.006381818181818181, current_output: 0.00702,level: 23}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6530}) CREATE (b)-[r:Supply{max_supply: 0.002127272727272727, current_output: 0.00234,level: 23}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6530}) CREATE (b)-[r:Supply{max_supply: 0.004254545454545454, current_output: 0.00468,level: 23}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6530}) CREATE (b)-[r:Supply{max_supply: 0.004254545454545454, current_output: 0.00468,level: 23}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6530}) CREATE (b)-[r:Supply{max_supply: 0.004254545454545454, current_output: 0.00468,level: 23}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:6530}) CREATE (b)-[r:Supply{max_supply: 0.014118181818181818, current_output: 0.01553,level: 23}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6530}) CREATE (b)-[r:Supply{max_supply: 0.0059545454545454546, current_output: 0.00655,level: 23}]->(g);
