CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0.004663957134013045});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:24.74768512074235, pop_demand:79.79663551034487});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:16.561051076770088, pop_demand:105.8375689241379});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:8.055017406858687, pop_demand:7.338654990109957});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:17.619960093437186, pop_demand:33.02747000989005});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:41.88624572503252, pop_demand:34.448458625270035});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:41.731073902929616, pop_demand:33.95208321649329});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:22.277928360974073, pop_demand:20.524972749002647});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:29.037712499999998, pop_demand:4.786158333333333});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:52.93390186605483, pop_demand:0.03653993367293204});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:1.9191591836734696});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:43.63519462654149, pop_demand:14.20800805351168});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:25.07520874423484, pop_demand:2.9394341344635615});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:33.9184993040167, pop_demand:19.67696156419898});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:23.859369815046993, pop_demand:7.269996681630074});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:42.94377180895212, pop_demand:33.264060076850775});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:11.19509523809524});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:2.239019047619048});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 832, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:832}) CREATE (g)-[r:Demand{max_demand: 4.247, current_input: 5.038105509810984, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:832}) CREATE (b)-[r:Supply{max_supply: 42.47, current_output: 42.47,level: 1}]->(g);
CREATE (n: Building {id: 834, name:"building_government_administration", level:2});
CREATE (n: Building {id: 835, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:835}) CREATE (g)-[r:Demand{max_demand: 16.112, current_input: 79.10920555065081, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:835}) CREATE (g)-[r:Demand{max_demand: 32.224, current_input: 38.30123046517111, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:835}) CREATE (b)-[r:Supply{max_supply: 28.195999999999998, current_output: 28.195999999999998,level: 1}]->(g);
CREATE (n: Building {id: 836, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:836}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:836}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 837, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:837}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 24.0,level: 1}]->(g);
CREATE (n: Building {id: 838, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:838}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.931369801990798, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:838}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 1}]->(g);
CREATE (n: Building {id: 839, name:"building_tobacco_plantation", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:839}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 840, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:840}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.6,level: 2}]->(g);
CREATE (n: Building {id: 841, name:"building_cotton_plantation", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:841}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 96.8,level: 2}]->(g);
CREATE (n: Building {id: 842, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:842}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.931369801990798, level: 1}]->(b);
CREATE (n: Building {id: 866, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:866}) CREATE (g)-[r:Demand{max_demand: 0.9162166666666668, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:866}) CREATE (b)-[r:Supply{max_supply: 18.3244, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 867, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:867}) CREATE (g)-[r:Demand{max_demand: 4.485, current_input: 5.320438712385746, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:867}) CREATE (b)-[r:Supply{max_supply: 44.85, current_output: 44.85,level: 1}]->(g);
CREATE (n: Building {id: 868, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:868}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.931369801990798, level: 1}]->(b);
CREATE (n: Building {id: 2844, name:"building_subsistence_farms", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:2844}) CREATE (b)-[r:Supply{max_supply: 15.121433333333334, current_output: 18.14572,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2844}) CREATE (b)-[r:Supply{max_supply: 3.7803583333333335, current_output: 4.53643,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2844}) CREATE (b)-[r:Supply{max_supply: 3.7803583333333335, current_output: 4.53643,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2844}) CREATE (b)-[r:Supply{max_supply: 3.7803583333333335, current_output: 4.53643,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2844}) CREATE (b)-[r:Supply{max_supply: 3.7803583333333335, current_output: 4.53643,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2844}) CREATE (b)-[r:Supply{max_supply: 3.7803583333333335, current_output: 4.53643,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2844}) CREATE (b)-[r:Supply{max_supply: 3.7803583333333335, current_output: 4.53643,level: 9}]->(g);
CREATE (n: Building {id: 3287, name:"building_subsistence_farms", level:19});
MATCH (g: Goods{code: 7}), (b: Building{id:3287}) CREATE (b)-[r:Supply{max_supply: 34.32995833333334, current_output: 41.19595,level: 19}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3287}) CREATE (b)-[r:Supply{max_supply: 8.582483333333334, current_output: 10.29898,level: 19}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3287}) CREATE (b)-[r:Supply{max_supply: 8.582483333333334, current_output: 10.29898,level: 19}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3287}) CREATE (b)-[r:Supply{max_supply: 8.582483333333334, current_output: 10.29898,level: 19}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3287}) CREATE (b)-[r:Supply{max_supply: 8.582483333333334, current_output: 10.29898,level: 19}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3287}) CREATE (b)-[r:Supply{max_supply: 8.582483333333334, current_output: 10.29898,level: 19}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3287}) CREATE (b)-[r:Supply{max_supply: 8.582483333333334, current_output: 10.29898,level: 19}]->(g);
CREATE (n: Building {id: 3288, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3288}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3288}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 3474, name:"building_subsistence_orchards", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 1.4147, current_output: 1.4147,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 0.70735, current_output: 0.70735,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 2.12205, current_output: 2.12205,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 1.4147, current_output: 1.4147,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 1.4147, current_output: 1.4147,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 1.4147, current_output: 1.4147,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 3.7631, current_output: 3.7631,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 1.4147, current_output: 1.4147,level: 4}]->(g);
CREATE (n: Building {id: 3937, name:"building_barracks", level:8});
MATCH (g: Goods{code: 1}), (b: Building{id:3937}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 0.0, level: 8}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3937}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.0, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3937}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 2.05046297585153, level: 8}]->(b);
CREATE (n: Building {id: 3938, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:3938}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3938}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.366975317234353, level: 5}]->(b);
CREATE (n: Building {id: 3939, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:3939}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 4156, name:"building_trade_center", level:4});
CREATE (n: Building {id: 4203, name:"building_trade_center", level:11});
CREATE (n: Building {id: 4342, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 4627, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:4627}) CREATE (g)-[r:Demand{max_demand: 0.35195, current_input: 0.24055348145031527, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4627}) CREATE (g)-[r:Demand{max_demand: 0.35195, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:4627}) CREATE (b)-[r:Supply{max_supply: 0.7039, current_output: 0.24055348145031527,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:4627}) CREATE (b)-[r:Supply{max_supply: 1.4078, current_output: 0.48110696290063054,level: 1}]->(g);
CREATE (n: Building {id: 4806, name:"building_barracks", level:1});
CREATE (n: Building {id: 4846, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:4846}) CREATE (g)-[r:Demand{max_demand: 3.7431, current_input: 2.5583626549699536, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4846}) CREATE (g)-[r:Demand{max_demand: 3.7431, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:4846}) CREATE (b)-[r:Supply{max_supply: 7.4862, current_output: 2.5583626549699536,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:4846}) CREATE (b)-[r:Supply{max_supply: 14.9724, current_output: 5.116725309939907,level: 1}]->(g);
CREATE (n: Building {id: 4847, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:4847}) CREATE (g)-[r:Demand{max_demand: 0.327, current_input: 0.0, level: 1}]->(b);
