CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:87.5, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:62.51800738458702, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:72.93767528201819, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:112.89524980156281, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:24.3265186858076, pop_demand:6513.849455111417});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:27.870470058138768, pop_demand:596.7948643788249});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:15.609831908338514, pop_demand:491.09556025200345});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:17.79610914067884, pop_demand:576.9347965867256});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:42.86188987048169, pop_demand:346.8503206057664});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:39.77108383273406, pop_demand:2285.3289070702945});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:34.9945057882764, pop_demand:1673.2179624625098});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:43.71800188872919, pop_demand:291.55474833140016});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:14.500694007486214, pop_demand:1095.7809249924603});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:78.13432835820896, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:42.08025454545455, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:12.5, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:32.300598741160776, pop_demand:135.61187877418283});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:22.234040612283867, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:30.237066083320006, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:32.13605925875232, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:63.47191889211562, pop_demand:157.1698108115073});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:45.0070601361291, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:45.54286747449574, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:87.5, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:99.70016615496242, pop_demand:328.0798940557407});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:44.389537120597, pop_demand:287.24507064298865});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:46.66728648445806, pop_demand:244.09349736079247});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:38.019516106403096, pop_demand:2469.754224748588});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:77.79212859551431, pop_demand:435.77344743625264});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:87.5, pop_demand:223.07565206374795});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:49.54388186020346, pop_demand:46.0804012050963});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:70.0, pop_demand:72.6828789385579});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:82.15145440778596, pop_demand:380.241349853899});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:66.52900621931823, pop_demand:223.1365529144044});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:297.31762955118137, pop_demand:0.09557300113049176});
CREATE (n: Building {id: 382, name:"building_government_administrationlevel", level:11});
MATCH (g: Goods{code: 14}), (b: Building{id:382}) CREATE (g)-[r:Demand{max_demand: 109.99999999999999, current_input: 42.93421298843504, level: 11}]->(b);
CREATE (n: Building {id: 383, name:"building_construction_sectorlevel", level:2});
CREATE (n: Building {id: 384, name:"building_universitylevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:384}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.70933081502774, level: 6}]->(b);
CREATE (n: Building {id: 385, name:"building_arts_academylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:385}) CREATE (g)-[r:Demand{max_demand: 0.215, current_input: 0.08391687084103214, level: 2}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:385}) CREATE (b)-[r:Supply{max_supply: 0.08600000000000001, current_output: 0.03356674833641286,level: 2}]->(g);
CREATE (n: Building {id: 386, name:"building_furniture_manufacturieslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:386}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 70.68981134531536, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:386}) CREATE (g)-[r:Demand{max_demand: 100.00000000000001, current_input: 117.22313271238828, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:386}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 67.76273872717066, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:386}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 20.380943771253552, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:386}) CREATE (b)-[r:Supply{max_supply: 225.00000000000003, current_output: 214.6071234853205,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:386}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 95.38094377125356,level: 5}]->(g);
CREATE (n: Building {id: 387, name:"building_paper_millslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:387}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 175.83469906858238, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:387}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 200.00000000000003,level: 5}]->(g);
CREATE (n: Building {id: 388, name:"building_wheat_farmlevel", level:10});
MATCH (g: Goods{code: 32}), (b: Building{id:388}) CREATE (g)-[r:Demand{max_demand: 46.364000000000004, current_input: 15.440426734299308, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:388}) CREATE (b)-[r:Supply{max_supply: 278.18399999999997, current_output: 92.64256040579583,level: 10}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:388}) CREATE (b)-[r:Supply{max_supply: 83.45519327731093, current_output: 27.792765882907158,level: 10}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:388}) CREATE (b)-[r:Supply{max_supply: 55.63679831932774, current_output: 18.52851152145127,level: 10}]->(g);
CREATE (n: Building {id: 389, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:389}) CREATE (g)-[r:Demand{max_demand: 24.80759821428571, current_input: 17.652325144576558, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:389}) CREATE (b)-[r:Supply{max_supply: 99.23039285714285, current_output: 70.60930057830623,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:389}) CREATE (b)-[r:Supply{max_supply: 12.403794642857143, current_output: 8.826159395639648,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:389}) CREATE (b)-[r:Supply{max_supply: 12.403794642857143, current_output: 8.826159395639648,level: 3}]->(g);
CREATE (n: Building {id: 390, name:"building_barrackslevel", level:34});
MATCH (g: Goods{code: 1}), (b: Building{id:390}) CREATE (g)-[r:Demand{max_demand: 34.0, current_input: 32.09750553164536, level: 34}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:390}) CREATE (g)-[r:Demand{max_demand: 34.0, current_input: 32.09750553164536, level: 34}]->(b);
CREATE (n: Building {id: 391, name:"building_government_administrationlevel", level:11});
MATCH (g: Goods{code: 14}), (b: Building{id:391}) CREATE (g)-[r:Demand{max_demand: 109.99999999999999, current_input: 42.93421298843504, level: 11}]->(b);
CREATE (n: Building {id: 392, name:"building_arms_industrylevel", level:5});
MATCH (g: Goods{code: 24}), (b: Building{id:392}) CREATE (g)-[r:Demand{max_demand: 99.16300000000001, current_input: 243.16496031678975, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:392}) CREATE (g)-[r:Demand{max_demand: 49.581500000000005, current_input: 67.19556460402424, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:392}) CREATE (b)-[r:Supply{max_supply: 74.3722456140351, current_output: 74.3722456140351,level: 5}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:392}) CREATE (b)-[r:Supply{max_supply: 74.3722456140351, current_output: 74.3722456140351,level: 5}]->(g);
CREATE (n: Building {id: 393, name:"building_construction_sectorlevel", level:2});
CREATE (n: Building {id: 394, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:394}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.9515551358379566, level: 1}]->(b);
CREATE (n: Building {id: 395, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:395}) CREATE (g)-[r:Demand{max_demand: 160.00000000000003, current_input: 113.85108702324138, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:395}) CREATE (g)-[r:Demand{max_demand: 100.00000000000001, current_input: 13.138834365755113, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:395}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 25.28872912658429,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:395}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 101.15491650633716,level: 4}]->(g);
CREATE (n: Building {id: 396, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:396}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 70.33387962743295, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:396}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:396}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 44.47284978437694, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:396}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 40.0,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:396}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 80.0,level: 3}]->(g);
CREATE (n: Building {id: 397, name:"building_iron_minelevel", level:7});
MATCH (g: Goods{code: 23}), (b: Building{id:397}) CREATE (g)-[r:Demand{max_demand: 104.99999999999999, current_input: 94.26387254124968, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:397}) CREATE (g)-[r:Demand{max_demand: 104.99999999999999, current_input: 85.59996383926489, level: 7}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:397}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 0.0, level: 7}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:397}) CREATE (b)-[r:Supply{max_supply: 478.8, current_output: 273.3930312983822,level: 7}]->(g);
CREATE (n: Building {id: 398, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:398}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 20.380943771253552, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:398}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 163.04755017002842,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:398}) CREATE (b)-[r:Supply{max_supply: 50.00000000000001, current_output: 40.761887542507104,level: 5}]->(g);
CREATE (n: Building {id: 399, name:"building_rye_farmlevel", level:7});
MATCH (g: Goods{code: 32}), (b: Building{id:399}) CREATE (g)-[r:Demand{max_demand: 34.54779310344828, current_input: 11.505320253764214, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:399}) CREATE (b)-[r:Supply{max_supply: 207.28679310344828, current_output: 69.03193300624734,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:399}) CREATE (b)-[r:Supply{max_supply: 103.64339655172414, current_output: 34.51596650312367,level: 7}]->(g);
CREATE (n: Building {id: 400, name:"building_barrackslevel", level:31});
MATCH (g: Goods{code: 1}), (b: Building{id:400}) CREATE (g)-[r:Demand{max_demand: 31.0, current_input: 29.265372690617827, level: 31}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:400}) CREATE (g)-[r:Demand{max_demand: 31.0, current_input: 29.265372690617827, level: 31}]->(b);
CREATE (n: Building {id: 401, name:"building_coal_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:401}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 36.68569878825639, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:401}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 146.74279515302555,level: 3}]->(g);
CREATE (n: Building {id: 402, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:402}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 127.2416604215676, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:402}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 27.919745454545456, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:402}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:402}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 77.22632727272727,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:402}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 38.61316363636364,level: 3}]->(g);
CREATE (n: Building {id: 403, name:"building_food_industrylevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:403}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 85.388315267431, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:403}) CREATE (g)-[r:Demand{max_demand: 74.99999999999999, current_input: 9.85412577431633, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:403}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 18.966546844938208,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:403}) CREATE (b)-[r:Supply{max_supply: 179.99999999999997, current_output: 75.86618737975283,level: 3}]->(g);
CREATE (n: Building {id: 404, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:404}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 16.30475501700284, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:404}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 130.43804013602272,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:404}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 32.60951003400568,level: 4}]->(g);
CREATE (n: Building {id: 405, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:405}) CREATE (g)-[r:Demand{max_demand: 18.761, current_input: 6.247904537188105, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:405}) CREATE (b)-[r:Supply{max_supply: 112.566, current_output: 37.487427223128634,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:405}) CREATE (b)-[r:Supply{max_supply: 33.76979646017699, current_output: 11.246226988084784,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:405}) CREATE (b)-[r:Supply{max_supply: 22.51319469026549, current_output: 7.497483676345019,level: 4}]->(g);
CREATE (n: Building {id: 406, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:406}) CREATE (g)-[r:Demand{max_demand: 9.458, current_input: 8.928770803479466, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:406}) CREATE (g)-[r:Demand{max_demand: 9.458, current_input: 8.928770803479466, level: 10}]->(b);
CREATE (n: Building {id: 407, name:"building_coal_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:407}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 36.68569878825639, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:407}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 146.74279515302555,level: 3}]->(g);
CREATE (n: Building {id: 408, name:"building_iron_minelevel", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:408}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 53.86507002357125, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:408}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 48.91426505100851, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:408}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 205.55867014915952,level: 4}]->(g);
CREATE (n: Building {id: 409, name:"building_lead_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:409}) CREATE (g)-[r:Demand{max_demand: 13.638, current_input: 11.118212446094235, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:409}) CREATE (b)-[r:Supply{max_supply: 54.552, current_output: 44.47284978437694,level: 3}]->(g);
CREATE (n: Building {id: 410, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:410}) CREATE (b)-[r:Supply{max_supply: 14.999999999999998, current_output: 16.5,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:410}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.8,level: 1}]->(g);
CREATE (n: Building {id: 411, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:411}) CREATE (g)-[r:Demand{max_demand: 16.860594594594595, current_input: 11.997481390329886, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:411}) CREATE (b)-[r:Supply{max_supply: 67.44239639639639, current_output: 47.9899383823879,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:411}) CREATE (b)-[r:Supply{max_supply: 8.430297297297297, current_output: 5.998740695164943,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:411}) CREATE (b)-[r:Supply{max_supply: 8.430297297297297, current_output: 5.998740695164943,level: 2}]->(g);
CREATE (n: Building {id: 412, name:"building_barrackslevel", level:13});
MATCH (g: Goods{code: 1}), (b: Building{id:412}) CREATE (g)-[r:Demand{max_demand: 12.298, current_input: 11.609856559652195, level: 13}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:412}) CREATE (g)-[r:Demand{max_demand: 12.298, current_input: 11.609856559652195, level: 13}]->(b);
CREATE (n: Building {id: 413, name:"building_iron_minelevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:413}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 40.39880251767844, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:413}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 36.68569878825639, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:413}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 154.16900261186964,level: 3}]->(g);
CREATE (n: Building {id: 414, name:"building_gold_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:414}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 26.932535011785628, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:414}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.457132525504257, level: 2}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:414}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 51.38966753728989,level: 2}]->(g);
CREATE (n: Building {id: 415, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:415}) CREATE (g)-[r:Demand{max_demand: 8.4294, current_input: 5.998102205960691, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:415}) CREATE (b)-[r:Supply{max_supply: 33.7176, current_output: 23.992408823842766,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:415}) CREATE (b)-[r:Supply{max_supply: 4.2147, current_output: 2.9990511029803457,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:415}) CREATE (b)-[r:Supply{max_supply: 4.2147, current_output: 2.9990511029803457,level: 1}]->(g);
CREATE (n: Building {id: 416, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:416}) CREATE (b)-[r:Supply{max_supply: 14.971045454545454, current_output: 16.46815,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:416}) CREATE (b)-[r:Supply{max_supply: 7.984554545454546, current_output: 8.78301,level: 1}]->(g);
CREATE (n: Building {id: 417, name:"building_barrackslevel", level:9});
MATCH (g: Goods{code: 1}), (b: Building{id:417}) CREATE (g)-[r:Demand{max_demand: 8.52192, current_input: 8.045069833536447, level: 9}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:417}) CREATE (g)-[r:Demand{max_demand: 8.52192, current_input: 8.045069833536447, level: 9}]->(b);
CREATE (n: Building {id: 33554985, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:33554985}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 35.166939813716475, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33554985}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 49.04348604152551, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33554985}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 59.99999999999999,level: 1}]->(g);
CREATE (n: Building {id: 588, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:588}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.70933081502774, level: 3}]->(b);
CREATE (n: Building {id: 589, name:"building_construction_sectorlevel", level:3});
CREATE (n: Building {id: 590, name:"building_textile_millslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:590}) CREATE (g)-[r:Demand{max_demand: 200.00000000000003, current_input: 282.75924538126145, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:590}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:590}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 150.0,level: 5}]->(g);
CREATE (n: Building {id: 591, name:"building_arms_industrylevel", level:10});
MATCH (g: Goods{code: 24}), (b: Building{id:591}) CREATE (g)-[r:Demand{max_demand: 198.33, current_input: 486.33972933078775, level: 10}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:591}) CREATE (g)-[r:Demand{max_demand: 99.165, current_input: 134.39383971759753, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:591}) CREATE (b)-[r:Supply{max_supply: 148.74749579831933, current_output: 148.74749579831933,level: 10}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:591}) CREATE (b)-[r:Supply{max_supply: 148.74749579831933, current_output: 148.74749579831933,level: 10}]->(g);
CREATE (n: Building {id: 592, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:592}) CREATE (g)-[r:Demand{max_demand: 14.063392857142855, current_input: 4.683478281562904, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:592}) CREATE (b)-[r:Supply{max_supply: 98.44379464285714, current_output: 32.7843628381814,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:592}) CREATE (b)-[r:Supply{max_supply: 22.501437499999998, current_output: 7.49356822394886,level: 3}]->(g);
CREATE (n: Building {id: 593, name:"building_barrackslevel", level:16});
MATCH (g: Goods{code: 1}), (b: Building{id:593}) CREATE (g)-[r:Demand{max_demand: 1.64592, current_input: 1.553821361901345, level: 16}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:593}) CREATE (g)-[r:Demand{max_demand: 1.64592, current_input: 1.553821361901345, level: 16}]->(b);
CREATE (n: Building {id: 694, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:694}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 15.612441086703653, level: 4}]->(b);
CREATE (n: Building {id: 695, name:"building_paper_millslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:695}) CREATE (g)-[r:Demand{max_demand: 149.10600000000002, current_input: 174.78672426213367, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:695}) CREATE (b)-[r:Supply{max_supply: 198.80800000000002, current_output: 198.80800000000002,level: 5}]->(g);
CREATE (n: Building {id: 696, name:"building_glassworkslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:696}) CREATE (g)-[r:Demand{max_demand: 149.9505, current_input: 175.77667361788977, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:696}) CREATE (g)-[r:Demand{max_demand: 74.97524561403509, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:696}) CREATE (b)-[r:Supply{max_supply: 49.9835, current_output: 24.99175,level: 5}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:696}) CREATE (b)-[r:Supply{max_supply: 124.9587456140351, current_output: 62.47937280701755,level: 5}]->(g);
CREATE (n: Building {id: 697, name:"building_textile_millslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:697}) CREATE (g)-[r:Demand{max_demand: 240.00000000000003, current_input: 339.3110944575137, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:697}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 74.45265454545454, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:697}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 193.06581818181817,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:697}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 154.45265454545452,level: 8}]->(g);
CREATE (n: Building {id: 698, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:698}) CREATE (g)-[r:Demand{max_demand: 4.99, current_input: 4.710780958909128, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:698}) CREATE (g)-[r:Demand{max_demand: 4.99, current_input: 4.710780958909128, level: 10}]->(b);
CREATE (n: Building {id: 699, name:"building_fishing_wharflevel", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:699}) CREATE (g)-[r:Demand{max_demand: 17.0, current_input: 10.149253731343283, level: 4}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:699}) CREATE (b)-[r:Supply{max_supply: 170.0, current_output: 101.49253731343282,level: 4}]->(g);
CREATE (n: Building {id: 700, name:"building_silk_plantationlevel", level:5});
MATCH (g: Goods{code: 20}), (b: Building{id:700}) CREATE (b)-[r:Supply{max_supply: 98.435, current_output: 102.3724,level: 5}]->(g);
CREATE (n: Building {id: 701, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:701}) CREATE (g)-[r:Demand{max_demand: 9.866297297297296, current_input: 3.2857354964570393, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:701}) CREATE (b)-[r:Supply{max_supply: 69.0640990990991, current_output: 23.000154475671348,level: 2}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:701}) CREATE (b)-[r:Supply{max_supply: 15.78607207207207, current_output: 5.257175594236848,level: 2}]->(g);
CREATE (n: Building {id: 702, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:702}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.955223880597012, level: 3}]->(b);
CREATE (n: Building {id: 857, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:857}) CREATE (g)-[r:Demand{max_demand: 14.066999999999998, current_input: 4.6846795546412805, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:857}) CREATE (b)-[r:Supply{max_supply: 84.402, current_output: 28.10807732784769,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:857}) CREATE (b)-[r:Supply{max_supply: 25.32059821428571, current_output: 8.432422603664662,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:857}) CREATE (b)-[r:Supply{max_supply: 16.880392857142855, current_output: 5.6216130868109655,level: 3}]->(g);
CREATE (n: Building {id: 858, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:858}) CREATE (g)-[r:Demand{max_demand: 9.7235, current_input: 6.918944029190545, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:858}) CREATE (g)-[r:Demand{max_demand: 4.861745454545455, current_input: 3.9634784295695376, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:858}) CREATE (b)-[r:Supply{max_supply: 38.894, current_output: 29.691816599163797,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:858}) CREATE (b)-[r:Supply{max_supply: 4.861745454545455, current_output: 3.711473604879464,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:858}) CREATE (b)-[r:Supply{max_supply: 14.585245454545452, current_output: 11.134427754670412,level: 1}]->(g);
CREATE (n: Building {id: 859, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:859}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 8.152377508501417, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:859}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 65.21902006801133,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:859}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 16.304755017002833,level: 2}]->(g);
CREATE (n: Building {id: 860, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:860}) CREATE (g)-[r:Demand{max_demand: 3.517, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:860}) CREATE (g)-[r:Demand{max_demand: 3.517, current_input: 3.3202037339646098, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:860}) CREATE (g)-[r:Demand{max_demand: 3.517, current_input: 3.3202037339646098, level: 5}]->(b);
CREATE (n: Building {id: 861, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:861}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 862, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:862}) CREATE (g)-[r:Demand{max_demand: 4.686496, current_input: 1.5607259539424287, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:862}) CREATE (b)-[r:Supply{max_supply: 32.805496, current_output: 10.9250896702258,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:862}) CREATE (b)-[r:Supply{max_supply: 7.498399999999999, current_output: 2.4971636576755656,level: 1}]->(g);
CREATE (n: Building {id: 863, name:"building_barrackslevel", level:6});
MATCH (g: Goods{code: 0}), (b: Building{id:863}) CREATE (g)-[r:Demand{max_demand: 0.64896, current_input: 0.0, level: 6}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:863}) CREATE (g)-[r:Demand{max_demand: 0.64896, current_input: 0.6126469761710757, level: 6}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:863}) CREATE (g)-[r:Demand{max_demand: 0.64896, current_input: 0.6126469761710757, level: 6}]->(b);
CREATE (n: Building {id: 864, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:864}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.9850746268656714, level: 1}]->(b);
CREATE (n: Building {id: 865, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:865}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 15.612441086703653, level: 4}]->(b);
CREATE (n: Building {id: 866, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:866}) CREATE (g)-[r:Demand{max_demand: 9.375896825396826, current_input: 3.1224192908482693, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:866}) CREATE (b)-[r:Supply{max_supply: 65.63129365079365, current_output: 21.85694032206804,level: 2}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:866}) CREATE (b)-[r:Supply{max_supply: 15.001436507936509, current_output: 4.995871393970247,level: 2}]->(g);
CREATE (n: Building {id: 867, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:867}) CREATE (g)-[r:Demand{max_demand: 8.4294, current_input: 5.998102205960691, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:867}) CREATE (b)-[r:Supply{max_supply: 33.7176, current_output: 23.992408823842766,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:867}) CREATE (b)-[r:Supply{max_supply: 4.214696, current_output: 2.9990482567031704,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:867}) CREATE (b)-[r:Supply{max_supply: 4.214696, current_output: 2.9990482567031704,level: 1}]->(g);
CREATE (n: Building {id: 868, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:868}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 16.30475501700284, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:868}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 195.65706020403405,level: 4}]->(g);
CREATE (n: Building {id: 869, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:869}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.970149253731343, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:869}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 59.70149253731343,level: 2}]->(g);
CREATE (n: Building {id: 870, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:870}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.9850746268656714, level: 1}]->(b);
CREATE (n: Building {id: 871, name:"building_barrackslevel", level:11});
MATCH (g: Goods{code: 0}), (b: Building{id:871}) CREATE (g)-[r:Demand{max_demand: 9.09392, current_input: 0.0, level: 11}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:871}) CREATE (g)-[r:Demand{max_demand: 9.09392, current_input: 8.585063161892364, level: 11}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:871}) CREATE (g)-[r:Demand{max_demand: 9.09392, current_input: 8.585063161892364, level: 11}]->(b);
CREATE (n: Building {id: 872, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:872}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.70933081502774, level: 3}]->(b);
CREATE (n: Building {id: 873, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:873}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.6651309997303196, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:873}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.8152377508501419, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:873}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 17.223959261943087,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:873}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 5.167187778582926,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:873}) CREATE (b)-[r:Supply{max_supply: 5.999999999999999, current_output: 3.444791852388617,level: 1}]->(g);
CREATE (n: Building {id: 874, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:874}) CREATE (g)-[r:Demand{max_demand: 9.2549, current_input: 6.585502658071226, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:874}) CREATE (b)-[r:Supply{max_supply: 37.0196, current_output: 26.342010632284904,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:874}) CREATE (b)-[r:Supply{max_supply: 4.6274454545454535, current_output: 3.292748094629731,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:874}) CREATE (b)-[r:Supply{max_supply: 4.6274454545454535, current_output: 3.292748094629731,level: 1}]->(g);
CREATE (n: Building {id: 875, name:"building_barrackslevel", level:7});
MATCH (g: Goods{code: 1}), (b: Building{id:875}) CREATE (g)-[r:Demand{max_demand: 6.75598, current_input: 6.377944277108395, level: 7}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:875}) CREATE (g)-[r:Demand{max_demand: 6.75598, current_input: 6.377944277108395, level: 7}]->(b);
CREATE (n: Building {id: 876, name:"building_tooling_workshopslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:876}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 105.5008194411494, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:876}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 147.13045812457653, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:876}) CREATE (b)-[r:Supply{max_supply: 179.99999999999997, current_output: 179.99999999999997,level: 3}]->(g);
CREATE (n: Building {id: 877, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:877}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 56.55184907625227, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:877}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 93.77850616991059, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:877}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 29.999999999999996,level: 2}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:877}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 39.99999999999999,level: 2}]->(g);
CREATE (n: Building {id: 878, name:"building_fishing_wharflevel", level:4});
MATCH (g: Goods{code: 8}), (b: Building{id:878}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 103.0,level: 4}]->(g);
CREATE (n: Building {id: 879, name:"building_naval_baselevel", level:30});
MATCH (g: Goods{code: 5}), (b: Building{id:879}) CREATE (g)-[r:Demand{max_demand: 44.1078, current_input: 8.06941715814529, level: 30}]->(b);
CREATE (n: Building {id: 880, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:880}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.955223880597012, level: 3}]->(b);
CREATE (n: Building {id: 1060, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:1060}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 23.41866163005548, level: 6}]->(b);
CREATE (n: Building {id: 1061, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1061}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 3.330261999460639, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1061}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.6304755017002839, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1061}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 34.44791852388617,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1061}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 17.223959261943087,level: 2}]->(g);
CREATE (n: Building {id: 1062, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1062}) CREATE (g)-[r:Demand{max_demand: 9.2549, current_input: 6.585502658071226, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1062}) CREATE (b)-[r:Supply{max_supply: 37.0196, current_output: 26.342010632284904,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1062}) CREATE (b)-[r:Supply{max_supply: 4.6274454545454535, current_output: 3.292748094629731,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1062}) CREATE (b)-[r:Supply{max_supply: 4.6274454545454535, current_output: 3.292748094629731,level: 1}]->(g);
CREATE (n: Building {id: 1063, name:"building_barrackslevel", level:11});
MATCH (g: Goods{code: 1}), (b: Building{id:1063}) CREATE (g)-[r:Demand{max_demand: 7.11194, current_input: 6.713986279139114, level: 11}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1063}) CREATE (g)-[r:Demand{max_demand: 7.11194, current_input: 6.713986279139114, level: 11}]->(b);
CREATE (n: Building {id: 1067, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:1067}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 19.515551358379568, level: 5}]->(b);
CREATE (n: Building {id: 1068, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:1068}) CREATE (g)-[r:Demand{max_demand: 14.063392857142855, current_input: 4.683478281562904, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1068}) CREATE (b)-[r:Supply{max_supply: 98.44379464285714, current_output: 32.7843628381814,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1068}) CREATE (b)-[r:Supply{max_supply: 22.501437499999998, current_output: 7.49356822394886,level: 3}]->(g);
CREATE (n: Building {id: 1069, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1069}) CREATE (g)-[r:Demand{max_demand: 18.50979279279279, current_input: 13.17100018771511, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1069}) CREATE (b)-[r:Supply{max_supply: 74.03919819819819, current_output: 52.68401998246298,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1069}) CREATE (b)-[r:Supply{max_supply: 9.25489189189189, current_output: 6.585496888590465,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1069}) CREATE (b)-[r:Supply{max_supply: 9.25489189189189, current_output: 6.585496888590465,level: 2}]->(g);
CREATE (n: Building {id: 1070, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:1070}) CREATE (g)-[r:Demand{max_demand: 6.09, current_input: 5.749229667285889, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1070}) CREATE (g)-[r:Demand{max_demand: 6.09, current_input: 5.749229667285889, level: 10}]->(b);
CREATE (n: Building {id: 1071, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:1071}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 23.41866163005548, level: 6}]->(b);
CREATE (n: Building {id: 1072, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 1073, name:"building_iron_minelevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:1073}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 40.39880251767844, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1073}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 36.68569878825639, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1073}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 154.16900261186964,level: 3}]->(g);
CREATE (n: Building {id: 1074, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1074}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 28.46277175581033, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1074}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 28.518119767444926, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1074}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 49.043486041525505, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1074}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 6.569417182877554, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1074}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 52.16084175773006,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1074}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 69.54778901030674,level: 2}]->(g);
CREATE (n: Building {id: 1075, name:"building_wheat_farmlevel", level:6});
MATCH (g: Goods{code: 32}), (b: Building{id:1075}) CREATE (g)-[r:Demand{max_demand: 28.130695652173912, current_input: 9.36825867488274, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1075}) CREATE (b)-[r:Supply{max_supply: 196.91489565217393, current_output: 65.57781941181919,level: 6}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1075}) CREATE (b)-[r:Supply{max_supply: 45.009113043478266, current_output: 14.989213879812386,level: 6}]->(g);
CREATE (n: Building {id: 1076, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1076}) CREATE (g)-[r:Demand{max_demand: 27.764696428571426, current_input: 19.756505432894773, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1076}) CREATE (b)-[r:Supply{max_supply: 111.05879464285714, current_output: 79.02602808487637,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1076}) CREATE (b)-[r:Supply{max_supply: 13.882348214285713, current_output: 9.878252716447387,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1076}) CREATE (b)-[r:Supply{max_supply: 13.882348214285713, current_output: 9.878252716447387,level: 3}]->(g);
CREATE (n: Building {id: 1077, name:"building_barrackslevel", level:27});
MATCH (g: Goods{code: 1}), (b: Building{id:1077}) CREATE (g)-[r:Demand{max_demand: 27.0, current_input: 25.489195569247784, level: 27}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1077}) CREATE (g)-[r:Demand{max_demand: 27.0, current_input: 25.489195569247784, level: 27}]->(b);
CREATE (n: Building {id: 1078, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:1078}) CREATE (g)-[r:Demand{max_demand: 13.908598214285712, current_input: 4.631927609880181, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1078}) CREATE (b)-[r:Supply{max_supply: 97.36019642857141, current_output: 32.42349624260948,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1078}) CREATE (b)-[r:Supply{max_supply: 22.253758928571425, current_output: 7.411084770497933,level: 3}]->(g);
CREATE (n: Building {id: 1079, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1079}) CREATE (g)-[r:Demand{max_demand: 18.537198198198197, current_input: 13.190501032688347, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1079}) CREATE (b)-[r:Supply{max_supply: 74.14879279279279, current_output: 52.76200413075339,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1079}) CREATE (b)-[r:Supply{max_supply: 9.268594594594594, current_output: 6.595247311077084,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1079}) CREATE (b)-[r:Supply{max_supply: 9.268594594594594, current_output: 6.595247311077084,level: 2}]->(g);
CREATE (n: Building {id: 1080, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 1}), (b: Building{id:1080}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 14.160664205137659, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1080}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 14.160664205137659, level: 15}]->(b);
CREATE (n: Building {id: 1081, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1081}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 28.46277175581033, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1081}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 28.518119767444926, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1081}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 49.043486041525505, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1081}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 6.569417182877554, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1081}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 52.16084175773006,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1081}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 69.54778901030674,level: 2}]->(g);
CREATE (n: Building {id: 1082, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:1082}) CREATE (g)-[r:Demand{max_demand: 14.090249999999997, current_input: 4.692422413790026, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1082}) CREATE (b)-[r:Supply{max_supply: 98.63175, current_output: 32.84695689653019,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1082}) CREATE (b)-[r:Supply{max_supply: 22.544392857142856, current_output: 7.507873483305472,level: 3}]->(g);
CREATE (n: Building {id: 1083, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1083}) CREATE (g)-[r:Demand{max_demand: 27.78029464285714, current_input: 19.767604643220082, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1083}) CREATE (b)-[r:Supply{max_supply: 111.12119642857142, current_output: 79.07043127947487,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1083}) CREATE (b)-[r:Supply{max_supply: 13.890142857142855, current_output: 9.883799144961408,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1083}) CREATE (b)-[r:Supply{max_supply: 13.890142857142855, current_output: 9.883799144961408,level: 3}]->(g);
CREATE (n: Building {id: 1084, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:1084}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.440442803425105, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1084}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.440442803425105, level: 10}]->(b);
CREATE (n: Building {id: 1085, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:1085}) CREATE (g)-[r:Demand{max_demand: 14.063392857142855, current_input: 4.683478281562904, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1085}) CREATE (b)-[r:Supply{max_supply: 98.44379464285714, current_output: 32.7843628381814,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1085}) CREATE (b)-[r:Supply{max_supply: 22.501437499999998, current_output: 7.49356822394886,level: 3}]->(g);
CREATE (n: Building {id: 1086, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1086}) CREATE (g)-[r:Demand{max_demand: 25.292098214285712, current_input: 17.99708046746881, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1086}) CREATE (b)-[r:Supply{max_supply: 101.16839285714285, current_output: 71.98832186987524,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1086}) CREATE (b)-[r:Supply{max_supply: 12.646044642857142, current_output: 8.998537057085771,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1086}) CREATE (b)-[r:Supply{max_supply: 12.646044642857142, current_output: 8.998537057085771,level: 3}]->(g);
CREATE (n: Building {id: 1087, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:1087}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.440442803425105, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1087}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.440442803425105, level: 10}]->(b);
CREATE (n: Building {id: 1088, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:1088}) CREATE (g)-[r:Demand{max_demand: 14.063392857142855, current_input: 4.683478281562904, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1088}) CREATE (b)-[r:Supply{max_supply: 98.44379464285714, current_output: 32.7843628381814,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1088}) CREATE (b)-[r:Supply{max_supply: 22.501437499999998, current_output: 7.49356822394886,level: 3}]->(g);
CREATE (n: Building {id: 1089, name:"building_livestock_ranchlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:1089}) CREATE (g)-[r:Demand{max_demand: 37.019592920353986, current_input: 26.34200559462619, level: 4}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1089}) CREATE (b)-[r:Supply{max_supply: 148.0783982300885, current_output: 105.36804126972496,level: 4}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1089}) CREATE (b)-[r:Supply{max_supply: 18.509796460176993, current_output: 13.171002797313095,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1089}) CREATE (b)-[r:Supply{max_supply: 18.509796460176993, current_output: 13.171002797313095,level: 4}]->(g);
CREATE (n: Building {id: 1090, name:"building_barrackslevel", level:11});
MATCH (g: Goods{code: 1}), (b: Building{id:1090}) CREATE (g)-[r:Demand{max_demand: 11.0, current_input: 10.384487083767615, level: 11}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1090}) CREATE (g)-[r:Demand{max_demand: 11.0, current_input: 10.384487083767615, level: 11}]->(b);
CREATE (n: Building {id: 1091, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:1091}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 19.515551358379568, level: 5}]->(b);
CREATE (n: Building {id: 1092, name:"building_furniture_manufacturieslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1092}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 42.41388680718921, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1092}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 70.33387962743295, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1092}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 40.65764323630239, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1092}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.228566262752128, level: 3}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1092}) CREATE (b)-[r:Supply{max_supply: 134.99999999999997, current_output: 128.76427409119225,level: 3}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1092}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 57.228566262752125,level: 3}]->(g);
CREATE (n: Building {id: 1093, name:"building_iron_minelevel", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:1093}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 53.86507002357125, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1093}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 48.91426505100851, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1093}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 205.55867014915952,level: 4}]->(g);
CREATE (n: Building {id: 1094, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1094}) CREATE (g)-[r:Demand{max_demand: 9.302999999999999, current_input: 3.0981427380982325, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1094}) CREATE (b)-[r:Supply{max_supply: 55.818, current_output: 18.588856428589395,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1094}) CREATE (b)-[r:Supply{max_supply: 16.745396396396394, current_output: 5.576655728482404,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1094}) CREATE (b)-[r:Supply{max_supply: 11.163594594594594, current_output: 3.7177694855762575,level: 2}]->(g);
CREATE (n: Building {id: 1095, name:"building_barrackslevel", level:6});
MATCH (g: Goods{code: 1}), (b: Building{id:1095}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 5.664265682055064, level: 6}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1095}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 5.664265682055064, level: 6}]->(b);
CREATE (n: Building {id: 1096, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:1096}) CREATE (g)-[r:Demand{max_demand: 4.897745454545454, current_input: 1.6310775570303802, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1096}) CREATE (g)-[r:Demand{max_demand: 0.9795454545454544, current_input: 0.7985624332191162, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1096}) CREATE (b)-[r:Supply{max_supply: 29.386499999999998, current_output: 16.871729295036353,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1096}) CREATE (b)-[r:Supply{max_supply: 8.815945454545455, current_output: 5.061516178820109,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1096}) CREATE (b)-[r:Supply{max_supply: 5.877299999999999, current_output: 3.37434585900727,level: 1}]->(g);
CREATE (n: Building {id: 1097, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:1097}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 24.45713252550426, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1097}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 195.65706020403408,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1097}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 48.91426505100852,level: 6}]->(g);
CREATE (n: Building {id: 1098, name:"building_barrackslevel", level:11});
MATCH (g: Goods{code: 1}), (b: Building{id:1098}) CREATE (g)-[r:Demand{max_demand: 11.0, current_input: 10.384487083767615, level: 11}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1098}) CREATE (g)-[r:Demand{max_demand: 11.0, current_input: 10.384487083767615, level: 11}]->(b);
CREATE (n: Building {id: 1099, name:"building_barrackslevel", level:6});
MATCH (g: Goods{code: 1}), (b: Building{id:1099}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 5.664265682055064, level: 6}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1099}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 5.664265682055064, level: 6}]->(b);
CREATE (n: Building {id: 1100, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1100}) CREATE (g)-[r:Demand{max_demand: 9.375891891891891, current_input: 3.1224176478618686, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1100}) CREATE (b)-[r:Supply{max_supply: 65.6312972972973, current_output: 21.856941536449295,level: 2}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1100}) CREATE (b)-[r:Supply{max_supply: 15.00143243243243, current_output: 4.995870036720611,level: 2}]->(g);
CREATE (n: Building {id: 1101, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:1101}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 24.45713252550426, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1101}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 195.65706020403408,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1101}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 48.91426505100852,level: 6}]->(g);
CREATE (n: Building {id: 1102, name:"building_iron_minelevel", level:6});
MATCH (g: Goods{code: 23}), (b: Building{id:1102}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 80.79760503535688, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1102}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 73.37139757651278, level: 6}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1102}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 308.3380052237393,level: 6}]->(g);
CREATE (n: Building {id: 1103, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:1103}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 19.515551358379568, level: 5}]->(b);
CREATE (n: Building {id: 1104, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1104}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 14.13796226906307, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1104}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 23.44462654247765, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1104}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 13.55254774543413, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1104}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.076188754250709, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1104}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 42.92142469706409,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1104}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 19.07618875425071,level: 1}]->(g);
CREATE (n: Building {id: 1105, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1105}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.304755017002837, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1105}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 130.4380401360227,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1105}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 32.60951003400567,level: 4}]->(g);
CREATE (n: Building {id: 1106, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1106}) CREATE (b)-[r:Supply{max_supply: 14.999999999999998, current_output: 16.5,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1106}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.8,level: 1}]->(g);
CREATE (n: Building {id: 1107, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1107}) CREATE (g)-[r:Demand{max_demand: 9.256799999999998, current_input: 6.586854639729627, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1107}) CREATE (b)-[r:Supply{max_supply: 37.02719999999999, current_output: 26.34741855891851,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1107}) CREATE (b)-[r:Supply{max_supply: 4.628399999999999, current_output: 3.2934273198648136,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1107}) CREATE (b)-[r:Supply{max_supply: 4.628399999999999, current_output: 3.2934273198648136,level: 1}]->(g);
CREATE (n: Building {id: 1108, name:"building_barrackslevel", level:13});
MATCH (g: Goods{code: 1}), (b: Building{id:1108}) CREATE (g)-[r:Demand{max_demand: 13.0, current_input: 12.272575644452639, level: 13}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1108}) CREATE (g)-[r:Demand{max_demand: 13.0, current_input: 12.272575644452639, level: 13}]->(b);
CREATE (n: Building {id: 1109, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1109}) CREATE (b)-[r:Supply{max_supply: 14.999999999999998, current_output: 16.5,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1109}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.8,level: 1}]->(g);
CREATE (n: Building {id: 1110, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1110}) CREATE (g)-[r:Demand{max_demand: 18.531198198198197, current_input: 13.186231616924976, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1110}) CREATE (b)-[r:Supply{max_supply: 74.12479279279279, current_output: 52.74492646769991,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1110}) CREATE (b)-[r:Supply{max_supply: 9.265594594594594, current_output: 6.593112603195399,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1110}) CREATE (b)-[r:Supply{max_supply: 9.265594594594594, current_output: 6.593112603195399,level: 2}]->(g);
CREATE (n: Building {id: 1111, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1111}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 16.30475501700284, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1111}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 130.43804013602272,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1111}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 32.60951003400568,level: 4}]->(g);
CREATE (n: Building {id: 2833, name:"building_subsistence_farmslevel", level:107});
MATCH (g: Goods{code: 7}), (b: Building{id:2833}) CREATE (b)-[r:Supply{max_supply: 158.94314545454543, current_output: 174.83746,level: 107}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2833}) CREATE (b)-[r:Supply{max_supply: 31.78862727272727, current_output: 34.96749,level: 107}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2833}) CREATE (b)-[r:Supply{max_supply: 31.78862727272727, current_output: 34.96749,level: 107}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2833}) CREATE (b)-[r:Supply{max_supply: 31.78862727272727, current_output: 34.96749,level: 107}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2833}) CREATE (b)-[r:Supply{max_supply: 31.78862727272727, current_output: 34.96749,level: 107}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2833}) CREATE (b)-[r:Supply{max_supply: 31.78862727272727, current_output: 34.96749,level: 107}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2833}) CREATE (b)-[r:Supply{max_supply: 44.50408181818182, current_output: 48.95449,level: 107}]->(g);
CREATE (n: Building {id: 2834, name:"building_urban_centerlevel", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:2834}) CREATE (g)-[r:Demand{max_demand: 39.993598130841114, current_input: 46.88174861337511, level: 8}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2834}) CREATE (g)-[r:Demand{max_demand: 79.98719626168223, current_input: 71.80859879374417, level: 8}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2834}) CREATE (b)-[r:Supply{max_supply: 439.9295981308411, current_output: 417.43845413841484,level: 8}]->(g);
CREATE (n: Building {id: 2952, name:"building_subsistence_farmslevel", level:86});
MATCH (g: Goods{code: 7}), (b: Building{id:2952}) CREATE (b)-[r:Supply{max_supply: 120.96114545454546, current_output: 133.05726,level: 86}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2952}) CREATE (b)-[r:Supply{max_supply: 24.192227272727273, current_output: 26.61145,level: 86}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2952}) CREATE (b)-[r:Supply{max_supply: 24.192227272727273, current_output: 26.61145,level: 86}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2952}) CREATE (b)-[r:Supply{max_supply: 24.192227272727273, current_output: 26.61145,level: 86}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2952}) CREATE (b)-[r:Supply{max_supply: 24.192227272727273, current_output: 26.61145,level: 86}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2952}) CREATE (b)-[r:Supply{max_supply: 24.192227272727273, current_output: 26.61145,level: 86}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2952}) CREATE (b)-[r:Supply{max_supply: 33.86911818181818, current_output: 37.25603,level: 86}]->(g);
CREATE (n: Building {id: 2954, name:"building_subsistence_farmslevel", level:78});
MATCH (g: Goods{code: 7}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 102.07274545454544, current_output: 112.28002,level: 78}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 20.414545454545454, current_output: 22.456,level: 78}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 20.414545454545454, current_output: 22.456,level: 78}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 20.414545454545454, current_output: 22.456,level: 78}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 20.414545454545454, current_output: 22.456,level: 78}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 20.414545454545454, current_output: 22.456,level: 78}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2954}) CREATE (b)-[r:Supply{max_supply: 28.580363636363636, current_output: 31.4384,level: 78}]->(g);
CREATE (n: Building {id: 2955, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2955}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 11.722313271238825, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2955}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.95502334119042, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2955}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.1760270359614635, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2955}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 98.71651396393115,level: 2}]->(g);
CREATE (n: Building {id: 2956, name:"building_subsistence_farmslevel", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 42.036, current_output: 46.2396,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 8.4072, current_output: 9.24792,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 8.4072, current_output: 9.24792,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 8.4072, current_output: 9.24792,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 8.4072, current_output: 9.24792,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 8.4072, current_output: 9.24792,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 11.770072727272726, current_output: 12.94708,level: 32}]->(g);
CREATE (n: Building {id: 16780908, name:"building_naval_baselevel", level:29});
MATCH (g: Goods{code: 5}), (b: Building{id:16780908}) CREATE (g)-[r:Demand{max_demand: 58.0, current_input: 10.61096212398775, level: 29}]->(b);
CREATE (n: Building {id: 3742, name:"building_subsistence_farmslevel", level:143});
MATCH (g: Goods{code: 7}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 264.8503, current_output: 291.33533,level: 143}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 52.970054545454545, current_output: 58.26706,level: 143}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 52.970054545454545, current_output: 58.26706,level: 143}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 52.970054545454545, current_output: 58.26706,level: 143}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 52.970054545454545, current_output: 58.26706,level: 143}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 52.970054545454545, current_output: 58.26706,level: 143}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3742}) CREATE (b)-[r:Supply{max_supply: 74.15808181818181, current_output: 81.57389,level: 143}]->(g);
CREATE (n: Building {id: 3743, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3743}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 11.722313271238825, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3743}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.95502334119042, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3743}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.1760270359614635, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3743}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 98.71651396393115,level: 2}]->(g);
CREATE (n: Building {id: 3745, name:"building_subsistence_farmslevel", level:189});
MATCH (g: Goods{code: 7}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 353.58591818181816, current_output: 388.94451,level: 189}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 70.71718181818181, current_output: 77.7889,level: 189}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 70.71718181818181, current_output: 77.7889,level: 189}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 70.71718181818181, current_output: 77.7889,level: 189}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 70.71718181818181, current_output: 77.7889,level: 189}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 70.71718181818181, current_output: 77.7889,level: 189}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3745}) CREATE (b)-[r:Supply{max_supply: 99.00405454545454, current_output: 108.90446,level: 189}]->(g);
CREATE (n: Building {id: 3746, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3746}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.8611566356194125, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3746}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.97751167059521, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3746}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0880135179807318, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3746}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 49.358256981965575,level: 1}]->(g);
CREATE (n: Building {id: 3747, name:"building_subsistence_farmslevel", level:154});
MATCH (g: Goods{code: 7}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 267.05139999999994, current_output: 293.75654,level: 154}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 53.41027272727273, current_output: 58.7513,level: 154}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 53.41027272727273, current_output: 58.7513,level: 154}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 53.41027272727273, current_output: 58.7513,level: 154}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 53.41027272727273, current_output: 58.7513,level: 154}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 53.41027272727273, current_output: 58.7513,level: 154}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3747}) CREATE (b)-[r:Supply{max_supply: 74.7743909090909, current_output: 82.25183,level: 154}]->(g);
CREATE (n: Building {id: 3748, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3748}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 11.722313271238825, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3748}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.95502334119042, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3748}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.1760270359614635, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3748}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 98.71651396393115,level: 2}]->(g);
CREATE (n: Building {id: 3749, name:"building_subsistence_farmslevel", level:83});
MATCH (g: Goods{code: 7}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 161.1465727272727, current_output: 177.26123,level: 83}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 32.22930909090909, current_output: 35.45224,level: 83}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 32.22930909090909, current_output: 35.45224,level: 83}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 32.22930909090909, current_output: 35.45224,level: 83}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 32.22930909090909, current_output: 35.45224,level: 83}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 32.22930909090909, current_output: 35.45224,level: 83}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3749}) CREATE (b)-[r:Supply{max_supply: 45.12103636363636, current_output: 49.63314,level: 83}]->(g);
CREATE (n: Building {id: 3750, name:"building_subsistence_farmslevel", level:41});
MATCH (g: Goods{code: 7}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 35.0632, current_output: 43.829,level: 41}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 7.01264, current_output: 8.7658,level: 41}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 7.01264, current_output: 8.7658,level: 41}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 7.01264, current_output: 8.7658,level: 41}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 7.01264, current_output: 8.7658,level: 41}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 7.01264, current_output: 8.7658,level: 41}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 9.817696, current_output: 12.27212,level: 41}]->(g);
CREATE (n: Building {id: 3751, name:"building_subsistence_farmslevel", level:57});
MATCH (g: Goods{code: 7}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 59.262896, current_output: 74.07862,level: 57}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 11.852576000000001, current_output: 14.81572,level: 57}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 11.852576000000001, current_output: 14.81572,level: 57}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 11.852576000000001, current_output: 14.81572,level: 57}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 11.852576000000001, current_output: 14.81572,level: 57}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 11.852576000000001, current_output: 14.81572,level: 57}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3751}) CREATE (b)-[r:Supply{max_supply: 16.593608, current_output: 20.74201,level: 57}]->(g);
CREATE (n: Building {id: 3752, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3752}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 11.722313271238825, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3752}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.95502334119042, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3752}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.1760270359614635, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3752}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 98.71651396393115,level: 2}]->(g);
CREATE (n: Building {id: 3753, name:"building_subsistence_farmslevel", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 30.619199999999996, current_output: 33.68112,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 6.1238363636363635, current_output: 6.73622,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 6.1238363636363635, current_output: 6.73622,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 6.1238363636363635, current_output: 6.73622,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 6.1238363636363635, current_output: 6.73622,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 6.1238363636363635, current_output: 6.73622,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 8.573372727272726, current_output: 9.43071,level: 32}]->(g);
CREATE (n: Building {id: 3754, name:"building_subsistence_farmslevel", level:123});
MATCH (g: Goods{code: 7}), (b: Building{id:3754}) CREATE (b)-[r:Supply{max_supply: 204.90569999999997, current_output: 225.39627,level: 123}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3754}) CREATE (b)-[r:Supply{max_supply: 40.98113636363636, current_output: 45.07925,level: 123}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3754}) CREATE (b)-[r:Supply{max_supply: 40.98113636363636, current_output: 45.07925,level: 123}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3754}) CREATE (b)-[r:Supply{max_supply: 40.98113636363636, current_output: 45.07925,level: 123}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3754}) CREATE (b)-[r:Supply{max_supply: 40.98113636363636, current_output: 45.07925,level: 123}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3754}) CREATE (b)-[r:Supply{max_supply: 40.98113636363636, current_output: 45.07925,level: 123}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3754}) CREATE (b)-[r:Supply{max_supply: 57.37359090909091, current_output: 63.11095,level: 123}]->(g);
CREATE (n: Building {id: 3755, name:"building_subsistence_farmslevel", level:180});
MATCH (g: Goods{code: 7}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 317.1645, current_output: 348.88095,level: 180}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 63.4329, current_output: 69.77619,level: 180}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 63.4329, current_output: 69.77619,level: 180}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 63.4329, current_output: 69.77619,level: 180}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 63.4329, current_output: 69.77619,level: 180}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 63.4329, current_output: 69.77619,level: 180}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3755}) CREATE (b)-[r:Supply{max_supply: 88.80605454545454, current_output: 97.68666,level: 180}]->(g);
CREATE (n: Building {id: 3756, name:"building_subsistence_farmslevel", level:46});
MATCH (g: Goods{code: 7}), (b: Building{id:3756}) CREATE (b)-[r:Supply{max_supply: 35.414248, current_output: 44.26781,level: 46}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3756}) CREATE (b)-[r:Supply{max_supply: 7.082848, current_output: 8.85356,level: 46}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3756}) CREATE (b)-[r:Supply{max_supply: 7.082848, current_output: 8.85356,level: 46}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3756}) CREATE (b)-[r:Supply{max_supply: 7.082848, current_output: 8.85356,level: 46}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3756}) CREATE (b)-[r:Supply{max_supply: 7.082848, current_output: 8.85356,level: 46}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3756}) CREATE (b)-[r:Supply{max_supply: 7.082848, current_output: 8.85356,level: 46}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3756}) CREATE (b)-[r:Supply{max_supply: 9.915984, current_output: 12.39498,level: 46}]->(g);
CREATE (n: Building {id: 3757, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3757}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 23.44462654247765, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3757}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 35.91004668238084, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3757}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.352054071922927, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3757}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 197.43302792786227,level: 4}]->(g);
CREATE (n: Building {id: 3758, name:"building_subsistence_farmslevel", level:157});
MATCH (g: Goods{code: 7}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 280.3666727272727, current_output: 308.40334,level: 157}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 56.07332727272727, current_output: 61.68066,level: 157}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 56.07332727272727, current_output: 61.68066,level: 157}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 56.07332727272727, current_output: 61.68066,level: 157}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 56.07332727272727, current_output: 61.68066,level: 157}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 56.07332727272727, current_output: 61.68066,level: 157}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3758}) CREATE (b)-[r:Supply{max_supply: 78.50266363636364, current_output: 86.35293,level: 157}]->(g);
CREATE (n: Building {id: 3759, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3759}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 23.44462654247765, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3759}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 35.91004668238084, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3759}) CREATE (b)-[r:Supply{max_supply: 220.0, current_output: 208.7526283765473,level: 4}]->(g);
CREATE (n: Building {id: 3760, name:"building_subsistence_farmslevel", level:265});
MATCH (g: Goods{code: 7}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 459.51661818181816, current_output: 505.46828,level: 265}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 91.90331818181816, current_output: 101.09365,level: 265}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 91.90331818181816, current_output: 101.09365,level: 265}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 91.90331818181816, current_output: 101.09365,level: 265}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 91.90331818181816, current_output: 101.09365,level: 265}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 91.90331818181816, current_output: 101.09365,level: 265}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3760}) CREATE (b)-[r:Supply{max_supply: 128.6646545454545, current_output: 141.53112,level: 265}]->(g);
CREATE (n: Building {id: 3761, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3761}) CREATE (g)-[r:Demand{max_demand: 34.991594339622644, current_input: 41.01824307091639, level: 7}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3761}) CREATE (g)-[r:Demand{max_demand: 69.98319811320755, current_input: 62.82749778068973, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3761}) CREATE (b)-[r:Supply{max_supply: 384.9075943396226, current_output: 365.2294181840457,level: 7}]->(g);
CREATE (n: Building {id: 3762, name:"building_subsistence_farmslevel", level:100});
MATCH (g: Goods{code: 7}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 196.225, current_output: 215.8475,level: 100}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 39.245, current_output: 43.1695,level: 100}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 39.245, current_output: 43.1695,level: 100}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 39.245, current_output: 43.1695,level: 100}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 39.245, current_output: 43.1695,level: 100}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 39.245, current_output: 43.1695,level: 100}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3762}) CREATE (b)-[r:Supply{max_supply: 54.943, current_output: 60.4373,level: 100}]->(g);
CREATE (n: Building {id: 3763, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3763}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 11.722313271238825, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3763}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.95502334119042, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3763}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.1760270359614635, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3763}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 98.71651396393115,level: 2}]->(g);
CREATE (n: Building {id: 3764, name:"building_subsistence_farmslevel", level:159});
MATCH (g: Goods{code: 7}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 252.85371818181818, current_output: 278.13909,level: 159}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 50.57073636363636, current_output: 55.62781,level: 159}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 50.57073636363636, current_output: 55.62781,level: 159}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 50.57073636363636, current_output: 55.62781,level: 159}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 50.57073636363636, current_output: 55.62781,level: 159}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 50.57073636363636, current_output: 55.62781,level: 159}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 70.79903636363636, current_output: 77.87894,level: 159}]->(g);
CREATE (n: Building {id: 3765, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3765}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 17.583469906858237, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3765}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 26.932535011785628, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 165.0, current_output: 156.56447128241047,level: 3}]->(g);
CREATE (n: Building {id: 3766, name:"building_subsistence_farmslevel", level:49});
MATCH (g: Goods{code: 7}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 57.76487272727272, current_output: 63.54136,level: 49}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 11.552972727272726, current_output: 12.70827,level: 49}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 11.552972727272726, current_output: 12.70827,level: 49}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 11.552972727272726, current_output: 12.70827,level: 49}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 11.552972727272726, current_output: 12.70827,level: 49}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 11.552972727272726, current_output: 12.70827,level: 49}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 16.174163636363634, current_output: 17.79158,level: 49}]->(g);
CREATE (n: Building {id: 3767, name:"building_subsistence_farmslevel", level:41});
MATCH (g: Goods{code: 7}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 55.52834545454545, current_output: 61.08118,level: 41}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 11.105663636363635, current_output: 12.21623,level: 41}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 11.105663636363635, current_output: 12.21623,level: 41}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 11.105663636363635, current_output: 12.21623,level: 41}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 11.105663636363635, current_output: 12.21623,level: 41}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 11.105663636363635, current_output: 12.21623,level: 41}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 15.547936363636364, current_output: 17.10273,level: 41}]->(g);
CREATE (n: Building {id: 3768, name:"building_subsistence_farmslevel", level:97});
MATCH (g: Goods{code: 7}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 196.5026, current_output: 216.15286,level: 97}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 39.30051818181818, current_output: 43.23057,level: 97}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 39.30051818181818, current_output: 43.23057,level: 97}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 39.30051818181818, current_output: 43.23057,level: 97}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 39.30051818181818, current_output: 43.23057,level: 97}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 39.30051818181818, current_output: 43.23057,level: 97}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 55.020727272727264, current_output: 60.5228,level: 97}]->(g);
CREATE (n: Building {id: 3769, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3769}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 41.028096449335884, level: 7}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3769}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 62.84258169416646, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3769}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 7.616094625865121, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3769}) CREATE (b)-[r:Supply{max_supply: 489.99999999999994, current_output: 345.507798873759,level: 7}]->(g);
CREATE (n: Building {id: 3770, name:"building_subsistence_farmslevel", level:31});
MATCH (g: Goods{code: 7}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 46.36281818181818, current_output: 50.9991,level: 31}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 9.272563636363635, current_output: 10.19982,level: 31}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 9.272563636363635, current_output: 10.19982,level: 31}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 9.272563636363635, current_output: 10.19982,level: 31}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 9.272563636363635, current_output: 10.19982,level: 31}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 9.272563636363635, current_output: 10.19982,level: 31}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 12.981590909090908, current_output: 14.27975,level: 31}]->(g);
CREATE (n: Building {id: 3771, name:"building_subsistence_farmslevel", level:38});
MATCH (g: Goods{code: 7}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 46.69534545454545, current_output: 51.36488,level: 38}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 9.339063636363637, current_output: 10.27297,level: 38}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 9.339063636363637, current_output: 10.27297,level: 38}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 9.339063636363637, current_output: 10.27297,level: 38}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 9.339063636363637, current_output: 10.27297,level: 38}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 9.339063636363637, current_output: 10.27297,level: 38}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 13.074690909090908, current_output: 14.38216,level: 38}]->(g);
CREATE (n: Building {id: 3772, name:"building_subsistence_farmslevel", level:56});
MATCH (g: Goods{code: 7}), (b: Building{id:3772}) CREATE (b)-[r:Supply{max_supply: 87.21999999999998, current_output: 95.942,level: 56}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3772}) CREATE (b)-[r:Supply{max_supply: 17.444, current_output: 19.1884,level: 56}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3772}) CREATE (b)-[r:Supply{max_supply: 17.444, current_output: 19.1884,level: 56}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3772}) CREATE (b)-[r:Supply{max_supply: 17.444, current_output: 19.1884,level: 56}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3772}) CREATE (b)-[r:Supply{max_supply: 17.444, current_output: 19.1884,level: 56}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3772}) CREATE (b)-[r:Supply{max_supply: 17.444, current_output: 19.1884,level: 56}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3772}) CREATE (b)-[r:Supply{max_supply: 24.421599999999998, current_output: 26.86376,level: 56}]->(g);
CREATE (n: Building {id: 16781035, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 3841, name:"building_subsistence_farmslevel", level:91});
MATCH (g: Goods{code: 7}), (b: Building{id:3841}) CREATE (b)-[r:Supply{max_supply: 116.49591818181817, current_output: 128.14551,level: 91}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3841}) CREATE (b)-[r:Supply{max_supply: 23.299181818181818, current_output: 25.6291,level: 91}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3841}) CREATE (b)-[r:Supply{max_supply: 23.299181818181818, current_output: 25.6291,level: 91}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3841}) CREATE (b)-[r:Supply{max_supply: 23.299181818181818, current_output: 25.6291,level: 91}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3841}) CREATE (b)-[r:Supply{max_supply: 23.299181818181818, current_output: 25.6291,level: 91}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3841}) CREATE (b)-[r:Supply{max_supply: 23.299181818181818, current_output: 25.6291,level: 91}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3841}) CREATE (b)-[r:Supply{max_supply: 32.618854545454546, current_output: 35.88074,level: 91}]->(g);
CREATE (n: Building {id: 3867, name:"building_trade_centerlevel", level:53});
CREATE (n: Building {id: 16781104, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:16781104}) CREATE (g)-[r:Demand{max_demand: 19.83, current_input: 48.626616410172545, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16781104}) CREATE (g)-[r:Demand{max_demand: 9.915, current_input: 13.437351089597938, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16781104}) CREATE (b)-[r:Supply{max_supply: 14.872499999999997, current_output: 14.872499999999997,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:16781104}) CREATE (b)-[r:Supply{max_supply: 14.872499999999997, current_output: 14.872499999999997,level: 1}]->(g);
CREATE (n: Building {id: 33558334, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:33558334}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.076188754250709, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33558334}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 32.60951003400567,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:33558334}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 8.152377508501418,level: 1}]->(g);
CREATE (n: Building {id: 3949, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4008, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4010, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4011, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 16781242, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 67113119, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:67113119}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.228566262752128, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:67113119}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 48.91426505100851,level: 1}]->(g);
CREATE (n: Building {id: 4487, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4489, name:"building_conscription_centerlevel", level:12});
CREATE (n: Building {id: 4490, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4491, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4492, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4493, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4494, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4495, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4496, name:"building_conscription_centerlevel", level:11});
CREATE (n: Building {id: 4497, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4498, name:"building_conscription_centerlevel", level:11});
CREATE (n: Building {id: 4499, name:"building_conscription_centerlevel", level:19});
CREATE (n: Building {id: 4500, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4501, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4502, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4503, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4504, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4505, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4506, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4539, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 33559062, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33559062}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 14.13796226906307, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33559062}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 23.44462654247765, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:33559062}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 13.55254774543413, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33559062}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.076188754250709, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:33559062}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 42.92142469706409,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:33559062}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 19.07618875425071,level: 1}]->(g);
CREATE (n: Building {id: 4666, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 50336326, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:50336326}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 8.152377508501417, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50336326}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 65.21902006801133,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:50336326}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 16.304755017002833,level: 2}]->(g);
CREATE (n: Building {id: 4691, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4691}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 35.166939813716475, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4691}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 49.04348604152551, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4691}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 59.99999999999999,level: 1}]->(g);
CREATE (n: Building {id: 16781992, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 100668094, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 16782017, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 184554198, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:184554198}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 8.152377508501417, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:184554198}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 65.21902006801133,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:184554198}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 16.304755017002833,level: 2}]->(g);
CREATE (n: Building {id: 4824, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 4826, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 4833, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 4834, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:4834}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 70.33387962743295, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4834}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 98.08697208305101, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4834}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 119.99999999999999,level: 2}]->(g);
CREATE (n: Building {id: 4838, name:"building_construction_sectorlevel", level:2});
CREATE (n: Building {id: 83890920, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:83890920}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.806220543351826, level: 2}]->(b);
CREATE (n: Building {id: 16782059, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 4845, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 16782065, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 4850, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 4853, name:"building_construction_sectorlevel", level:1});
CREATE (n: Building {id: 4854, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:4854}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.457132525504257, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:4854}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 97.82853010201703,level: 2}]->(g);
CREATE (n: Building {id: 4855, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:4855}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.903110271675913, level: 2}]->(b);
CREATE (n: Building {id: 4856, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4856}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.8611566356194125, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:4856}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.97751167059521, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:4856}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0880135179807318, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:4856}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 49.358256981965575,level: 1}]->(g);
CREATE (n: Building {id: 4857, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:4857}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.903110271675913, level: 2}]->(b);
CREATE (n: Building {id: 4906, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:4906}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.806220543351826, level: 2}]->(b);
CREATE (n: Building {id: 4938, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4938}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.8611566356194125, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:4938}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.97751167059521, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:4938}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0880135179807318, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:4938}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 49.358256981965575,level: 1}]->(g);
CREATE (n: Building {id: 4940, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:4940}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 70.33387962743295, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4940}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 98.08697208305101, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4940}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 119.99999999999999,level: 2}]->(g);
CREATE (n: Building {id: 4943, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:4943}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 70.33387962743295, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4943}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 98.08697208305101, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4943}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 119.99999999999999,level: 2}]->(g);
CREATE (n: Building {id: 4947, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4947}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 35.166939813716475, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4947}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 49.04348604152551, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4947}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 59.99999999999999,level: 1}]->(g);
CREATE (n: Building {id: 16782185, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16782185}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.9515551358379566, level: 1}]->(b);
CREATE (n: Building {id: 4980, name:"building_iron_minelevel", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:4980}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 53.86507002357125, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4980}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 48.91426505100851, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4980}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 205.55867014915952,level: 4}]->(g);
CREATE (n: Building {id: 16782280, name:"building_coal_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:16782280}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 36.68569878825639, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16782280}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 146.74279515302555,level: 3}]->(g);
CREATE (n: Building {id: 16782309, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:16782309}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.228566262752128, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782309}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 97.82853010201703,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:16782309}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 24.457132525504257,level: 3}]->(g);
CREATE (n: Building {id: 5267, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 100668646, name:"building_naval_baselevel", level:30});
MATCH (g: Goods{code: 5}), (b: Building{id:100668646}) CREATE (g)-[r:Demand{max_demand: 56.5896, current_input: 10.35293279675202, level: 30}]->(b);
CREATE (n: Building {id: 100668683, name:"building_naval_baselevel", level:22});
MATCH (g: Goods{code: 5}), (b: Building{id:100668683}) CREATE (g)-[r:Demand{max_demand: 5.28396, current_input: 0.9666879211149365, level: 22}]->(b);
CREATE (n: Building {id: 33559964, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:33559964}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 70.33387962743295, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33559964}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 98.08697208305101, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33559964}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 119.99999999999999,level: 2}]->(g);
CREATE (n: Building {id: 5564, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5564}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.8611566356194125, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5564}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.97751167059521, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5564}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0880135179807318, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5564}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 49.358256981965575,level: 1}]->(g);
CREATE (n: Building {id: 5591, name:"building_sulfur_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:5591}) CREATE (g)-[r:Demand{max_demand: 0.0913, current_input: 0.08196468155253427, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5591}) CREATE (g)-[r:Demand{max_demand: 0.0913, current_input: 0.07443120665261796, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:5591}) CREATE (b)-[r:Supply{max_supply: 0.3652, current_output: 0.3127917764103045,level: 1}]->(g);
CREATE (n: Building {id: 33560078, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:33560078}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 35.166939813716475, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33560078}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 49.04348604152551, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33560078}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 59.99999999999999,level: 1}]->(g);
CREATE (n: Building {id: 16782887, name:"building_trade_centerlevel", level:37});
CREATE (n: Building {id: 67114586, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:67114586}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 8.152377508501417, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:67114586}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 65.21902006801133,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:67114586}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 16.304755017002833,level: 2}]->(g);
CREATE (n: Building {id: 5726, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5726}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.8611566356194125, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5726}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.97751167059521, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5726}) CREATE (b)-[r:Supply{max_supply: 55.0, current_output: 52.18815709413683,level: 1}]->(g);
CREATE (n: Building {id: 16782958, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:16782958}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.457132525504257, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16782958}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 97.82853010201703,level: 2}]->(g);
CREATE (n: Building {id: 134223473, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:134223473}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 8.152377508501417, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:134223473}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 65.21902006801133,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:134223473}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 16.304755017002833,level: 2}]->(g);
CREATE (n: Building {id: 5791, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5791}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 35.166939813716475, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5791}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 49.04348604152551, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5791}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 59.99999999999999,level: 1}]->(g);
CREATE (n: Building {id: 16783022, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:16783022}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.457132525504257, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16783022}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 97.82853010201703,level: 2}]->(g);
CREATE (n: Building {id: 5817, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5817}) CREATE (g)-[r:Demand{max_demand: 1.5018, current_input: 1.760457007074647, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5817}) CREATE (g)-[r:Demand{max_demand: 3.0036, current_input: 2.6964854053799767, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5817}) CREATE (g)-[r:Demand{max_demand: 1.5018, current_input: 0.3267957402606925, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5817}) CREATE (b)-[r:Supply{max_supply: 21.0252, current_output: 14.825246067103182,level: 1}]->(g);
CREATE (n: Building {id: 16783121, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:16783121}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 26.932535011785628, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783121}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.457132525504257, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783121}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 102.77933507457978,level: 2}]->(g);
CREATE (n: Building {id: 5910, name:"building_coal_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:5910}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 36.68569878825639, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5910}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 146.74279515302555,level: 3}]->(g);
CREATE (n: Building {id: 5933, name:"building_barrackslevel", level:7});
MATCH (g: Goods{code: 1}), (b: Building{id:5933}) CREATE (g)-[r:Demand{max_demand: 6.96696, current_input: 6.577118739375058, level: 7}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:5933}) CREATE (g)-[r:Demand{max_demand: 6.96696, current_input: 6.577118739375058, level: 7}]->(b);
CREATE (n: Building {id: 16783164, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:16783164}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.9440442803425105, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16783164}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.9440442803425105, level: 1}]->(b);
CREATE (n: Building {id: 16783296, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:16783296}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.228566262752128, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16783296}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 48.91426505100851,level: 1}]->(g);
CREATE (n: Building {id: 6111, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6111}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.8611566356194125, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6111}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.97751167059521, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:6111}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0880135179807318, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6111}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 49.358256981965575,level: 1}]->(g);
CREATE (n: Building {id: 6126, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:6126}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.228566262752128, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6126}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 48.91426505100851,level: 1}]->(g);
CREATE (n: Building {id: 6129, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6129}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.8611566356194125, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6129}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.97751167059521, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:6129}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0880135179807318, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6129}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 49.358256981965575,level: 1}]->(g);
CREATE (n: Building {id: 134223882, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:134223882}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.228566262752128, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:134223882}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 48.91426505100851,level: 1}]->(g);
CREATE (n: Building {id: 6241, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6241}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.8611566356194125, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6241}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.97751167059521, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6241}) CREATE (b)-[r:Supply{max_supply: 55.0, current_output: 52.18815709413683,level: 1}]->(g);
CREATE (n: Building {id: 6254, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6254}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.8611566356194125, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6254}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.97751167059521, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:6254}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0880135179807318, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6254}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 49.358256981965575,level: 1}]->(g);
CREATE (n: Building {id: 50337913, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:50337913}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.466267505892814, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50337913}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.228566262752128, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50337913}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 51.38966753728989,level: 1}]->(g);
CREATE (n: Building {id: 6632, name:"building_trade_centerlevel", level:2});
