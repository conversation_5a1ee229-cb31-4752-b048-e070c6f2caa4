CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:85.51885158228767, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:102.74568425607899, pop_demand:25.53715903366759});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:121.0938845459035, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:117.04545149774191, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:26.002747884476758, pop_demand:4851.44140444324});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:31.692153116346926, pop_demand:840.7841355299057});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:28.597375944731365, pop_demand:106.89292125457223});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:32.34305216168679, pop_demand:598.551418899853});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:41.7010467768231, pop_demand:386.1045291302751});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:32.87270386524631, pop_demand:1494.8846824519164});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:29.76979498162618, pop_demand:1005.2677899875398});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:49.79755572400555, pop_demand:155.2250007126205});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:27.459491039315438, pop_demand:883.9334229949857});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:49.76013900641662, pop_demand:478.49526666666634});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:95.49781341061488, pop_demand:15.60545558294657});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:34.49616808740057, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:57.1071865714425, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:85.87380673581413, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:51.62088892522434, pop_demand:431.4380250059374});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:69.62097144689443, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:68.71719788342985, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:68.78389594753101, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:70.0, pop_demand:17.019106168334382});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:104.8766250508922, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:87.31071516094553, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:62.42581732178539, pop_demand:25.73138066080244});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:50.446330727483556, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:68.16940969130447, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:84.91734350688954, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:112.47374742016201, pop_demand:101.71435941681837});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:42.64279263843151, pop_demand:472.36378816727364});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:43.3715238742003, pop_demand:626.3232361026562});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:38.403963550450186, pop_demand:1818.0631470319072});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:22.196573096706363, pop_demand:339.82093958453106});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:24.984378266144695, pop_demand:13.33143833775131});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:39.527805674054015, pop_demand:84.44257986685065});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:65.6471905544617, pop_demand:378.8012134760684});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:68.39053211794257, pop_demand:593.3337632647733});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:93.1694307113155, pop_demand:462.0030757489383});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:317.13545502298024, pop_demand:0.23745924663371917});
CREATE (n: Building {id: 186, name:"building_government_administration", level:31});
MATCH (g: Goods{code: 14}), (b: Building{id:186}) CREATE (g)-[r:Demand{max_demand: 620.0, current_input: 74.66780642817774, level: 31}]->(b);
CREATE (n: Building {id: 187, name:"building_construction_sector", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:187}) CREATE (g)-[r:Demand{max_demand: 124.99875, current_input: 53.36063948483916, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:187}) CREATE (g)-[r:Demand{max_demand: 374.99625, current_input: 66.4399791586197, level: 5}]->(b);
CREATE (n: Building {id: 188, name:"building_university", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:188}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 6.021597292594979, level: 5}]->(b);
CREATE (n: Building {id: 189, name:"building_arts_academy", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:189}) CREATE (g)-[r:Demand{max_demand: 1.08, current_input: 0.13006650152005156, level: 2}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:189}) CREATE (b)-[r:Supply{max_supply: 0.432, current_output: 0.05202660060802062,level: 2}]->(g);
CREATE (n: Building {id: 190, name:"building_furniture_manufacturies", level:18});
MATCH (g: Goods{code: 9}), (b: Building{id:190}) CREATE (g)-[r:Demand{max_demand: 175.43519658119658, current_input: 74.89142313600031, level: 18}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:190}) CREATE (g)-[r:Demand{max_demand: 263.1527948717949, current_input: 46.62410945926682, level: 18}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:190}) CREATE (g)-[r:Demand{max_demand: 263.1527948717949, current_input: 10.69839760305411, level: 18}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:190}) CREATE (g)-[r:Demand{max_demand: 87.71759829059829, current_input: 5.3587328128660765, level: 18}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:190}) CREATE (b)-[r:Supply{max_supply: 701.740794871795, current_output: 123.8238949780872,level: 18}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:190}) CREATE (b)-[r:Supply{max_supply: 438.588, current_output: 77.38993492685726,level: 18}]->(g);
CREATE (n: Building {id: 191, name:"building_wheat_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:191}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 2.283592374597105, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:191}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.3054536898692331, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:191}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 15.243443295773082,level: 5}]->(g);
CREATE (n: Building {id: 192, name:"building_livestock_ranch", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:192}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 23.995705642319365, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:192}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 2.4436295189538653, level: 4}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:192}) CREATE (b)-[r:Supply{max_supply: 80.00000000000001, current_output: 26.43933516127323,level: 4}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:192}) CREATE (b)-[r:Supply{max_supply: 20.000000000000004, current_output: 6.609833790318308,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:192}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 33.04916895159154,level: 4}]->(g);
CREATE (n: Building {id: 193, name:"building_paper_mills", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:193}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 42.52201188163543, level: 8}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:193}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 3.4844861366366082, level: 8}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:193}) CREATE (b)-[r:Supply{max_supply: 560.0, current_output: 61.80471534013613,level: 8}]->(g);
CREATE (n: Building {id: 194, name:"building_railway", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:194}) CREATE (g)-[r:Demand{max_demand: 7.95272, current_input: 1.4090235597138323, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:194}) CREATE (g)-[r:Demand{max_demand: 1.98818, current_input: 0.07790730839196064, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:194}) CREATE (g)-[r:Demand{max_demand: 4.97045, current_input: 0.01390528261666962, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:194}) CREATE (b)-[r:Supply{max_supply: 29.8227, current_output: 2.1786265568354333,level: 1}]->(g);
CREATE (n: Building {id: 198, name:"building_textile_mills", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:198}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 32.016703857942076, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:198}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 55.11066037689229, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:198}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 6.446406714278752, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:198}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 55.699494971734325,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:198}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 55.699494971734325,level: 3}]->(g);
CREATE (n: Building {id: 199, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:199}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.113658718780943, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:199}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 21.13658718780943,level: 2}]->(g);
CREATE (n: Building {id: 200, name:"building_silk_plantation", level:8});
MATCH (g: Goods{code: 20}), (b: Building{id:200}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 187.2,level: 8}]->(g);
CREATE (n: Building {id: 201, name:"building_wheat_farm", level:6});
MATCH (g: Goods{code: 32}), (b: Building{id:201}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 2.740310849516526, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:201}) CREATE (g)-[r:Demand{max_demand: 6.000000000000001, current_input: 0.3665444278430798, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:201}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 11.432582471829809,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:201}) CREATE (b)-[r:Supply{max_supply: 42.0, current_output: 3.2011230921123466,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:201}) CREATE (b)-[r:Supply{max_supply: 30.000000000000004, current_output: 2.286516494365962,level: 6}]->(g);
CREATE (n: Building {id: 202, name:"building_vineyard_plantation", level:5});
MATCH (g: Goods{code: 39}), (b: Building{id:202}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 114.0,level: 5}]->(g);
CREATE (n: Building {id: 203, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:203}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.170488078171414, level: 3}]->(b);
CREATE (n: Building {id: 204, name:"building_government_administration", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:204}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 12.043194585189958, level: 5}]->(b);
CREATE (n: Building {id: 205, name:"building_shipyards", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:205}) CREATE (g)-[r:Demand{max_demand: 29.168999999999997, current_input: 12.451936464430831, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:205}) CREATE (g)-[r:Demand{max_demand: 58.337999999999994, current_input: 10.336038038128532, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:205}) CREATE (g)-[r:Demand{max_demand: 29.168999999999997, current_input: 1.185856908095991, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:205}) CREATE (g)-[r:Demand{max_demand: 7.292245098039215, current_input: 0.020400714019507077, level: 3}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:205}) CREATE (b)-[r:Supply{max_supply: 102.0915, current_output: 16.52648838970835,level: 3}]->(g);
CREATE (n: Building {id: 206, name:"building_military_shipyards", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:206}) CREATE (g)-[r:Demand{max_demand: 10.263, current_input: 4.381165755920794, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:206}) CREATE (g)-[r:Demand{max_demand: 20.526, current_input: 3.6366950661768707, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:206}) CREATE (g)-[r:Demand{max_demand: 10.263, current_input: 0.13023512422236638, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:206}) CREATE (g)-[r:Demand{max_demand: 20.526, current_input: 0.8344783467235185, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:206}) CREATE (g)-[r:Demand{max_demand: 5.1314950495049505, current_input: 0.014355820682112766, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:206}) CREATE (b)-[r:Supply{max_supply: 45.629297029702975, current_output: 6.0249516500104745,level: 2}]->(g);
CREATE (n: Building {id: 207, name:"building_textile_mills", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:207}) CREATE (g)-[r:Demand{max_demand: 200.00000000000003, current_input: 85.37787695451222, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:207}) CREATE (g)-[r:Demand{max_demand: 120.00000000000001, current_input: 146.96176100504613, level: 8}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:207}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 17.19041790474334, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:207}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 148.53198659129157,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:207}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 148.53198659129157,level: 8}]->(g);
CREATE (n: Building {id: 208, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:208}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.170488078171414, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:208}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 31.70488078171414,level: 3}]->(g);
CREATE (n: Building {id: 209, name:"building_wheat_farm", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:209}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 1.8268738996776843, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:209}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.24436295189538648, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:209}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 7.621721647886542,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:209}) CREATE (b)-[r:Supply{max_supply: 28.000000000000004, current_output: 2.1340820614082316,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:209}) CREATE (b)-[r:Supply{max_supply: 20.000000000000004, current_output: 1.5243443295773085,level: 4}]->(g);
CREATE (n: Building {id: 210, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:210}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 11.997852821159679, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:210}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 1.2218147594769322, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:210}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 13.219667580636612,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:210}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 3.304916895159153,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:210}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 16.524584475795766,level: 2}]->(g);
CREATE (n: Building {id: 211, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:211}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 212, name:"building_university", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:212}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.408638917037992, level: 2}]->(b);
CREATE (n: Building {id: 213, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:213}) CREATE (g)-[r:Demand{max_demand: 9.998, current_input: 2.113235987037186, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:213}) CREATE (b)-[r:Supply{max_supply: 99.97999999999999, current_output: 21.13235987037186,level: 2}]->(g);
CREATE (n: Building {id: 214, name:"building_glassworks", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:214}) CREATE (g)-[r:Demand{max_demand: 59.94479411764706, current_input: 10.620721865469909, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:214}) CREATE (g)-[r:Demand{max_demand: 14.986196078431373, current_input: 6.440474334766527, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:214}) CREATE (g)-[r:Demand{max_demand: 29.972392156862746, current_input: 1.2872641760465422, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:214}) CREATE (b)-[r:Supply{max_supply: 59.94479411764706, current_output: 12.985717397009626,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:214}) CREATE (b)-[r:Supply{max_supply: 59.94479411764706, current_output: 12.985717397009626,level: 3}]->(g);
CREATE (n: Building {id: 215, name:"building_wheat_farm", level:7});
MATCH (g: Goods{code: 32}), (b: Building{id:215}) CREATE (g)-[r:Demand{max_demand: 35.00000000000001, current_input: 3.1970293244359476, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:215}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 0.42763516581692634, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:215}) CREATE (b)-[r:Supply{max_supply: 175.0, current_output: 13.338012883801447,level: 7}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:215}) CREATE (b)-[r:Supply{max_supply: 49.00000000000001, current_output: 3.7346436074644056,level: 7}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:215}) CREATE (b)-[r:Supply{max_supply: 35.00000000000001, current_output: 2.66760257676029,level: 7}]->(g);
CREATE (n: Building {id: 216, name:"building_vineyard_plantation", level:4});
MATCH (g: Goods{code: 39}), (b: Building{id:216}) CREATE (b)-[r:Supply{max_supply: 80.00000000000001, current_output: 90.4,level: 4}]->(g);
CREATE (n: Building {id: 217, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:217}) CREATE (g)-[r:Demand{max_demand: 19.99, current_input: 11.9918538947491, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:217}) CREATE (g)-[r:Demand{max_demand: 19.99, current_input: 1.2212038520971937, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:217}) CREATE (b)-[r:Supply{max_supply: 39.98, current_output: 13.213057746846292,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:217}) CREATE (b)-[r:Supply{max_supply: 9.995, current_output: 3.303264436711573,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:217}) CREATE (b)-[r:Supply{max_supply: 49.974999999999994, current_output: 16.516322183557865,level: 2}]->(g);
CREATE (n: Building {id: 218, name:"building_construction_sector", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:218}) CREATE (g)-[r:Demand{max_demand: 74.99925, current_input: 32.016383690903496, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:218}) CREATE (g)-[r:Demand{max_demand: 224.99775, current_input: 39.86398749517183, level: 3}]->(b);
CREATE (n: Building {id: 219, name:"building_government_administration", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:219}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 14.45183350222795, level: 6}]->(b);
CREATE (n: Building {id: 220, name:"building_textile_mills", level:7});
MATCH (g: Goods{code: 9}), (b: Building{id:220}) CREATE (g)-[r:Demand{max_demand: 175.00000000000003, current_input: 74.70564233519819, level: 7}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:220}) CREATE (g)-[r:Demand{max_demand: 105.00000000000001, current_input: 128.59154087941536, level: 7}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:220}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 15.04161566665042, level: 7}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:220}) CREATE (b)-[r:Supply{max_supply: 210.00000000000003, current_output: 129.96548826738012,level: 7}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:220}) CREATE (b)-[r:Supply{max_supply: 210.00000000000003, current_output: 129.96548826738012,level: 7}]->(g);
CREATE (n: Building {id: 221, name:"building_paper_mills", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:221}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 15.945754455613287, level: 3}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:221}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 1.3066823012387283, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:221}) CREATE (b)-[r:Supply{max_supply: 209.99999999999997, current_output: 23.176768252551046,level: 3}]->(g);
CREATE (n: Building {id: 222, name:"building_logging_camp", level:4});
MATCH (g: Goods{code: 16}), (b: Building{id:222}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.4398721875648466, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:222}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.2218147594769324, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:222}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 21.97012168225067,level: 4}]->(g);
CREATE (n: Building {id: 223, name:"building_coal_mine", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:223}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 2.443629518953865, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:223}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 9.77451807581546,level: 4}]->(g);
CREATE (n: Building {id: 224, name:"building_silk_plantation", level:12});
MATCH (g: Goods{code: 20}), (b: Building{id:224}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 290.4,level: 12}]->(g);
CREATE (n: Building {id: 225, name:"building_wheat_farm", level:9});
MATCH (g: Goods{code: 32}), (b: Building{id:225}) CREATE (g)-[r:Demand{max_demand: 45.00000000000001, current_input: 4.110466274274789, level: 9}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:225}) CREATE (g)-[r:Demand{max_demand: 9.0, current_input: 0.5498166417646195, level: 9}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:225}) CREATE (b)-[r:Supply{max_supply: 225.0, current_output: 17.148873707744713,level: 9}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:225}) CREATE (b)-[r:Supply{max_supply: 63.00000000000001, current_output: 4.8016846381685205,level: 9}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:225}) CREATE (b)-[r:Supply{max_supply: 45.00000000000001, current_output: 3.4297747415489432,level: 9}]->(g);
CREATE (n: Building {id: 226, name:"building_vineyard_plantation", level:6});
MATCH (g: Goods{code: 39}), (b: Building{id:226}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 138.0,level: 6}]->(g);
CREATE (n: Building {id: 227, name:"building_railway", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:227}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 1.4174003960545145, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:227}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.07837047791644684, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:227}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.013987951409499764, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:227}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 2.191578787469377,level: 1}]->(g);
CREATE (n: Building {id: 228, name:"building_arms_industry", level:6});
MATCH (g: Goods{code: 26}), (b: Building{id:228}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 2.4392819255291394, level: 6}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:228}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 0.305221896052876, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:228}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 1.8327221392153983, level: 6}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:228}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 10.683246833354687,level: 6}]->(g);
CREATE (n: Building {id: 229, name:"building_artillery_foundries", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:229}) CREATE (g)-[r:Demand{max_demand: 28.54889108910891, current_input: 0.36227890260166695, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:229}) CREATE (g)-[r:Demand{max_demand: 19.032594059405938, current_input: 0.7737643780840362, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:229}) CREATE (b)-[r:Supply{max_supply: 47.01051485148515, current_output: 1.2538755041111245,level: 2}]->(g);
CREATE (n: Building {id: 230, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:230}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.6109073797384662, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:230}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 2.443629518953865,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:230}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 2.443629518953865,level: 2}]->(g);
CREATE (n: Building {id: 231, name:"building_munition_plants", level:3});
MATCH (g: Goods{code: 25}), (b: Building{id:231}) CREATE (g)-[r:Demand{max_demand: 48.96839215686275, current_input: 2.103110644363259, level: 3}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:231}) CREATE (g)-[r:Demand{max_demand: 48.96839215686275, current_input: 3.3889275065844284, level: 3}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:231}) CREATE (b)-[r:Supply{max_supply: 122.421, current_output: 6.865048788241056,level: 3}]->(g);
CREATE (n: Building {id: 232, name:"building_wheat_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:232}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 2.283592374597105, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:232}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.3054536898692331, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:232}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 9.527152059858176,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:232}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 2.6676025767602893,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:232}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 1.9054304119716352,level: 5}]->(g);
CREATE (n: Building {id: 233, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:233}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 11.997852821159679, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:233}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 1.2218147594769322, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:233}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 13.219667580636612,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:233}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 3.304916895159153,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:233}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 16.524584475795766,level: 2}]->(g);
CREATE (n: Building {id: 234, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:234}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 235, name:"building_coal_mine", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:235}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 3.6654442784307966, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:235}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 14.661777113723186,level: 6}]->(g);
CREATE (n: Building {id: 236, name:"building_explosives_factory", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:236}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.8711215341591522, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:236}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.8268738996776839, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:236}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 3.3724942922960452,level: 1}]->(g);
CREATE (n: Building {id: 237, name:"building_textile_mills", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:237}) CREATE (g)-[r:Demand{max_demand: 124.964995951417, current_input: 53.346230239805976, level: 5}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:237}) CREATE (g)-[r:Demand{max_demand: 74.97899595141699, current_input: 91.82537736175387, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:237}) CREATE (g)-[r:Demand{max_demand: 24.992995951417, current_input: 10.741001127410415, level: 5}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:237}) CREATE (b)-[r:Supply{max_supply: 149.95799999999997, current_output: 92.80649852190373,level: 5}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:237}) CREATE (b)-[r:Supply{max_supply: 149.95799999999997, current_output: 92.80649852190373,level: 5}]->(g);
CREATE (n: Building {id: 238, name:"building_wheat_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:238}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 2.283592374597105, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:238}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.3054536898692331, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:238}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 9.527152059858176,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:238}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 2.6676025767602893,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:238}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 1.9054304119716352,level: 5}]->(g);
CREATE (n: Building {id: 239, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:239}) CREATE (g)-[r:Demand{max_demand: 19.996, current_input: 11.99545325059545, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:239}) CREATE (g)-[r:Demand{max_demand: 19.996, current_input: 1.221570396525037, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:239}) CREATE (b)-[r:Supply{max_supply: 39.992, current_output: 13.217023647120488,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:239}) CREATE (b)-[r:Supply{max_supply: 9.998, current_output: 3.304255911780122,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:239}) CREATE (b)-[r:Supply{max_supply: 49.989999999999995, current_output: 16.52127955890061,level: 2}]->(g);
CREATE (n: Building {id: 240, name:"building_steel_mills", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:240}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 1.1755571687467026, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:240}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 0.5075908573413871, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:240}) CREATE (b)-[r:Supply{max_supply: 64.22, current_output: 1.6657065836793525,level: 1}]->(g);
CREATE (n: Building {id: 241, name:"building_arms_industry", level:4});
MATCH (g: Goods{code: 26}), (b: Building{id:241}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 1.6261879503527596, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:241}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 0.20348126403525063, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:241}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.2218147594769324, level: 4}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:241}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 7.122164555569792,level: 4}]->(g);
CREATE (n: Building {id: 242, name:"building_iron_mine", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:242}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 1.959261947911171, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:242}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 3.0545368986923314, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:242}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 10.027597693207005,level: 5}]->(g);
CREATE (n: Building {id: 243, name:"building_vineyard_plantation", level:4});
MATCH (g: Goods{code: 39}), (b: Building{id:243}) CREATE (b)-[r:Supply{max_supply: 80.00000000000001, current_output: 90.4,level: 4}]->(g);
CREATE (n: Building {id: 244, name:"building_logging_camp", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:244}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 1.5272684493461657, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:244}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 18.327221392153987,level: 5}]->(g);
CREATE (n: Building {id: 245, name:"building_wheat_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:245}) CREATE (g)-[r:Demand{max_demand: 24.963245614035092, current_input: 2.2802350931762065, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:245}) CREATE (g)-[r:Demand{max_demand: 4.992649122807018, current_input: 0.3050046193567587, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:245}) CREATE (b)-[r:Supply{max_supply: 199.70600000000002, current_output: 15.221035434128297,level: 5}]->(g);
CREATE (n: Building {id: 246, name:"building_paper_mills", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:246}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 21.261005940817714, level: 4}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:246}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 1.7422430683183043, level: 4}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:246}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 30.902357670068064,level: 4}]->(g);
CREATE (n: Building {id: 247, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:247}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 0.9134369498388418, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:247}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.12218147594769324, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:247}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 3.81086082394327,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:247}) CREATE (b)-[r:Supply{max_supply: 13.999999999999998, current_output: 1.0670410307041156,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:247}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 0.762172164788654,level: 2}]->(g);
CREATE (n: Building {id: 248, name:"building_vineyard_plantation", level:2});
MATCH (g: Goods{code: 39}), (b: Building{id:248}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 44.4,level: 2}]->(g);
CREATE (n: Building {id: 249, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:249}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.113658718780943, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:249}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 21.13658718780943,level: 2}]->(g);
CREATE (n: Building {id: 250, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:250}) CREATE (g)-[r:Demand{max_demand: 4.896, current_input: 1.0348473087151495, level: 1}]->(b);
CREATE (n: Building {id: 251, name:"building_textile_mills", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:251}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 32.016703857942076, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:251}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 55.11066037689229, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:251}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 6.446406714278752, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:251}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 55.699494971734325,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:251}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 55.699494971734325,level: 3}]->(g);
CREATE (n: Building {id: 252, name:"building_wheat_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:252}) CREATE (g)-[r:Demand{max_demand: 12.530000000000001, current_input: 1.144536498148069, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:252}) CREATE (g)-[r:Demand{max_demand: 2.5060000000000002, current_input: 0.15309338936245964, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:252}) CREATE (b)-[r:Supply{max_supply: 62.65000000000001, current_output: 4.775008612400918,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:252}) CREATE (b)-[r:Supply{max_supply: 17.542, current_output: 1.337002411472257,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:252}) CREATE (b)-[r:Supply{max_supply: 12.530000000000001, current_output: 0.9550017224801834,level: 5}]->(g);
CREATE (n: Building {id: 253, name:"building_vineyard_plantation", level:5});
MATCH (g: Goods{code: 39}), (b: Building{id:253}) CREATE (b)-[r:Supply{max_supply: 99.99600000000001, current_output: 113.99544,level: 5}]->(g);
CREATE (n: Building {id: 254, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:254}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 11.997852821159679, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:254}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 1.2218147594769322, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:254}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 13.219667580636612,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:254}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 3.304916895159153,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:254}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 16.524584475795766,level: 2}]->(g);
CREATE (n: Building {id: 255, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:255}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 7.225916751113975, level: 3}]->(b);
CREATE (n: Building {id: 256, name:"building_paper_mills", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:256}) CREATE (g)-[r:Demand{max_demand: 149.94, current_input: 26.565626923051738, level: 5}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:256}) CREATE (g)-[r:Demand{max_demand: 49.98, current_input: 2.176932713863721, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:256}) CREATE (b)-[r:Supply{max_supply: 349.86, current_output: 38.612495908750056,level: 5}]->(g);
CREATE (n: Building {id: 257, name:"building_furniture_manufacturies", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:257}) CREATE (g)-[r:Demand{max_demand: 19.98219801980198, current_input: 8.530188219076756, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:257}) CREATE (g)-[r:Demand{max_demand: 29.97329702970297, current_input: 5.310520385120074, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:257}) CREATE (g)-[r:Demand{max_demand: 29.97329702970297, current_input: 1.2185553615511782, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:257}) CREATE (g)-[r:Demand{max_demand: 9.99109900990099, current_input: 0.6103636116846198, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:257}) CREATE (b)-[r:Supply{max_supply: 79.92879207920792, current_output: 14.103632606893498,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:257}) CREATE (b)-[r:Supply{max_supply: 49.95549504950495, current_output: 8.814770379308436,level: 2}]->(g);
CREATE (n: Building {id: 258, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:258}) CREATE (g)-[r:Demand{max_demand: 14.993392156862745, current_input: 0.9159573915940289, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:258}) CREATE (b)-[r:Supply{max_supply: 59.97359803921569, current_output: 3.6638313631625268,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:258}) CREATE (b)-[r:Supply{max_supply: 59.97359803921569, current_output: 3.6638313631625268,level: 3}]->(g);
CREATE (n: Building {id: 259, name:"building_wheat_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:259}) CREATE (g)-[r:Demand{max_demand: 24.99424561403509, current_input: 2.2830667477207065, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:259}) CREATE (g)-[r:Demand{max_demand: 4.998842105263159, current_input: 0.30538295322526343, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:259}) CREATE (b)-[r:Supply{max_supply: 124.9712456140351, current_output: 9.52496048059837,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:259}) CREATE (b)-[r:Supply{max_supply: 34.99194736842105, current_output: 2.6669888275960116,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:259}) CREATE (b)-[r:Supply{max_supply: 24.99424561403509, current_output: 1.9049918286908445,level: 5}]->(g);
CREATE (n: Building {id: 260, name:"building_government_administration", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:260}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 12.043194585189958, level: 5}]->(b);
CREATE (n: Building {id: 261, name:"building_shipyards", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:261}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 17.07557539090244, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:261}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 14.174003960545145, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:261}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 1.6261879503527596, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:261}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.02797590281899953, level: 2}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:261}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 22.663085316203297,level: 2}]->(g);
CREATE (n: Building {id: 262, name:"building_military_shipyards", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:262}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 8.53778769545122, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:262}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 7.087001980272572, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:262}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.25379542867069355, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:262}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 1.6261879503527596, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:262}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.02797590281899953, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:262}) CREATE (b)-[r:Supply{max_supply: 88.92, current_output: 11.741112302698536,level: 2}]->(g);
CREATE (n: Building {id: 263, name:"building_logging_camp", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:263}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.2218147594769324, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:263}) CREATE (b)-[r:Supply{max_supply: 139.99999999999997, current_output: 8.552703316338524,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:263}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 2.443629518953865,level: 4}]->(g);
CREATE (n: Building {id: 264, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:264}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.170488078171414, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:264}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 31.70488078171414,level: 3}]->(g);
CREATE (n: Building {id: 265, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:265}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 0.9134369498388418, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:265}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.12218147594769324, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:265}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 6.097377318309232,level: 2}]->(g);
CREATE (n: Building {id: 266, name:"building_vineyard_plantation", level:2});
MATCH (g: Goods{code: 39}), (b: Building{id:266}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 44.4,level: 2}]->(g);
CREATE (n: Building {id: 267, name:"building_livestock_ranch", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:267}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 29.994632052899206, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:267}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 3.0545368986923314, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:267}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 33.04916895159154,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:267}) CREATE (b)-[r:Supply{max_supply: 25.000000000000004, current_output: 8.262292237897885,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:267}) CREATE (b)-[r:Supply{max_supply: 125.00000000000001, current_output: 41.31146118948942,level: 5}]->(g);
CREATE (n: Building {id: 268, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:268}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 269, name:"building_tooling_workshops", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:269}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 21.261005940817714, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:269}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 0.40696252807050126, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:269}) CREATE (b)-[r:Supply{max_supply: 320.0, current_output: 29.16193297723129,level: 4}]->(g);
CREATE (n: Building {id: 270, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:270}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.6109073797384662, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:270}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 2.443629518953865,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:270}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 2.443629518953865,level: 2}]->(g);
CREATE (n: Building {id: 271, name:"building_iron_mine", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:271}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 1.1755571687467026, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:271}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 1.8327221392153983, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:271}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 6.016558615924201,level: 3}]->(g);
CREATE (n: Building {id: 272, name:"building_wheat_farm", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:272}) CREATE (g)-[r:Demand{max_demand: 37.20879487179487, current_input: 3.3987888094871455, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:272}) CREATE (g)-[r:Demand{max_demand: 7.441752136752137, current_input: 0.45462212985263795, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:272}) CREATE (b)-[r:Supply{max_supply: 186.044, current_output: 14.179755822594037,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:272}) CREATE (b)-[r:Supply{max_supply: 52.09231623931624, current_output: 3.970331343697482,level: 8}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:272}) CREATE (b)-[r:Supply{max_supply: 37.20879487179487, current_output: 2.835950773661287,level: 8}]->(g);
CREATE (n: Building {id: 273, name:"building_vineyard_plantation", level:10});
MATCH (g: Goods{code: 39}), (b: Building{id:273}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 238.0,level: 10}]->(g);
CREATE (n: Building {id: 274, name:"building_food_industry", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:274}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 143.97423385391616, level: 6}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:274}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 138.40627827033083, level: 6}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:274}) CREATE (b)-[r:Supply{max_supply: 210.0, current_output: 123.54147405435806,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:274}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 211.78538409318526,level: 6}]->(g);
CREATE (n: Building {id: 275, name:"building_logging_camp", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:275}) CREATE (g)-[r:Demand{max_demand: 19.995592233009706, current_input: 1.2215454857386785, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:275}) CREATE (b)-[r:Supply{max_supply: 79.98239805825243, current_output: 4.886183722296598,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:275}) CREATE (b)-[r:Supply{max_supply: 79.98239805825243, current_output: 4.886183722296598,level: 4}]->(g);
CREATE (n: Building {id: 276, name:"building_vineyard_plantation", level:3});
MATCH (g: Goods{code: 39}), (b: Building{id:276}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 73.2,level: 3}]->(g);
CREATE (n: Building {id: 277, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:277}) CREATE (g)-[r:Demand{max_demand: 15.000000000000002, current_input: 1.370155424758263, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:277}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.18327221392153986, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:277}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 5.7162912359149045,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:277}) CREATE (b)-[r:Supply{max_supply: 21.0, current_output: 1.6005615460561733,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:277}) CREATE (b)-[r:Supply{max_supply: 15.000000000000002, current_output: 1.143258247182981,level: 3}]->(g);
CREATE (n: Building {id: 278, name:"building_furniture_manufacturies", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:278}) CREATE (g)-[r:Demand{max_demand: 79.92319626168224, current_input: 34.11836408120618, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:278}) CREATE (g)-[r:Demand{max_demand: 119.88479439252335, current_input: 21.24059438160958, level: 8}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:278}) CREATE (g)-[r:Demand{max_demand: 119.88479439252335, current_input: 4.873880201790988, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:278}) CREATE (g)-[r:Demand{max_demand: 39.96159813084112, current_input: 2.4412835204273735, level: 8}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:278}) CREATE (b)-[r:Supply{max_supply: 319.6927943925233, current_output: 56.410582493416456,level: 8}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:278}) CREATE (b)-[r:Supply{max_supply: 199.808, current_output: 35.256614676793475,level: 8}]->(g);
CREATE (n: Building {id: 279, name:"building_iron_mine", level:1});
MATCH (g: Goods{code: 16}), (b: Building{id:279}) CREATE (g)-[r:Demand{max_demand: 4.9986, current_input: 0.609797255838082, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:279}) CREATE (g)-[r:Demand{max_demand: 9.9972, current_input: 0.3917426709131512, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:279}) CREATE (g)-[r:Demand{max_demand: 9.9972, current_input: 0.6107363256721394, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:279}) CREATE (b)-[r:Supply{max_supply: 39.9888, current_output: 2.962764677681939,level: 1}]->(g);
CREATE (n: Building {id: 280, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:280}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.45671847491942097, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:280}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.06109073797384662, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:280}) CREATE (b)-[r:Supply{max_supply: 24.999999999999996, current_output: 1.905430411971635,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:280}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 0.5335205153520579,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:280}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.38108608239432706,level: 1}]->(g);
CREATE (n: Building {id: 281, name:"building_vineyard_plantation", level:3});
MATCH (g: Goods{code: 39}), (b: Building{id:281}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 67.2,level: 3}]->(g);
CREATE (n: Building {id: 282, name:"building_tooling_workshops", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:282}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 21.261005940817714, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:282}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 0.40696252807050126, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:282}) CREATE (b)-[r:Supply{max_supply: 320.0, current_output: 29.16193297723129,level: 4}]->(g);
CREATE (n: Building {id: 283, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:283}) CREATE (g)-[r:Demand{max_demand: 4.9544, current_input: 0.45255320242815583, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:283}) CREATE (g)-[r:Demand{max_demand: 0.9908727272727272, current_input: 0.06053314614724896, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:283}) CREATE (b)-[r:Supply{max_supply: 24.771999999999995, current_output: 1.8880528866144535,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:283}) CREATE (b)-[r:Supply{max_supply: 6.936154545454545, current_output: 0.5286543925217754,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:283}) CREATE (b)-[r:Supply{max_supply: 4.9544, current_output: 0.3776105773228908,level: 1}]->(g);
CREATE (n: Building {id: 284, name:"building_lead_mine", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:284}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.7837047791644685, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:284}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.2218147594769324, level: 2}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:284}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 4.011039077282802,level: 2}]->(g);
CREATE (n: Building {id: 285, name:"building_sulfur_mine", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:285}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 1.959261947911171, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:285}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 3.0545368986923314, level: 5}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:285}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 10.027597693207005,level: 5}]->(g);
CREATE (n: Building {id: 286, name:"building_logging_camp", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:286}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 1.5272684493461657, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:286}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 6.109073797384663,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:286}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 6.109073797384663,level: 5}]->(g);
CREATE (n: Building {id: 287, name:"building_food_industry", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:287}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 71.98711692695808, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:287}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 69.20313913516542, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:287}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 61.770737027179024,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:287}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 105.89269204659263,level: 3}]->(g);
CREATE (n: Building {id: 288, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:288}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.9163610696076991, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:288}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 3.6654442784307966,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:288}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 3.6654442784307966,level: 3}]->(g);
CREATE (n: Building {id: 289, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:289}) CREATE (g)-[r:Demand{max_demand: 9.950999999999999, current_input: 2.103301791058916, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:289}) CREATE (b)-[r:Supply{max_supply: 99.50999999999999, current_output: 21.03301791058916,level: 2}]->(g);
CREATE (n: Building {id: 290, name:"building_wheat_farm", level:6});
MATCH (g: Goods{code: 32}), (b: Building{id:290}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 2.740310849516526, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:290}) CREATE (g)-[r:Demand{max_demand: 6.000000000000001, current_input: 0.3665444278430798, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:290}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 11.432582471829809,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:290}) CREATE (b)-[r:Supply{max_supply: 42.0, current_output: 3.2011230921123466,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:290}) CREATE (b)-[r:Supply{max_supply: 30.000000000000004, current_output: 2.286516494365962,level: 6}]->(g);
CREATE (n: Building {id: 291, name:"building_vineyard_plantation", level:5});
MATCH (g: Goods{code: 39}), (b: Building{id:291}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 114.0,level: 5}]->(g);
CREATE (n: Building {id: 292, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:292}) CREATE (g)-[r:Demand{max_demand: 4.857, current_input: 1.026604039711904, level: 1}]->(b);
CREATE (n: Building {id: 293, name:"building_glassworks", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:293}) CREATE (g)-[r:Demand{max_demand: 29.72519801980198, current_input: 5.266563430758275, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:293}) CREATE (g)-[r:Demand{max_demand: 7.431297029702971, current_input: 3.1936775378717983, level: 2}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:293}) CREATE (g)-[r:Demand{max_demand: 14.862594059405941, current_input: 0.6383235877759184, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:293}) CREATE (b)-[r:Supply{max_supply: 29.72519801980198, current_output: 6.439308479360742,level: 2}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:293}) CREATE (b)-[r:Supply{max_supply: 29.72519801980198, current_output: 6.439308479360742,level: 2}]->(g);
CREATE (n: Building {id: 294, name:"building_wheat_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:294}) CREATE (g)-[r:Demand{max_demand: 22.102, current_input: 2.0188783465338087, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:294}) CREATE (g)-[r:Demand{max_demand: 4.420395161290323, current_input: 0.2700452025392466, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:294}) CREATE (b)-[r:Supply{max_supply: 110.50999999999999, current_output: 8.422764593079416,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:294}) CREATE (b)-[r:Supply{max_supply: 30.942798387096776, current_output: 2.3583739631312426,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:294}) CREATE (b)-[r:Supply{max_supply: 22.102, current_output: 1.6845529186158834,level: 5}]->(g);
CREATE (n: Building {id: 295, name:"building_vineyard_plantation", level:4});
MATCH (g: Goods{code: 39}), (b: Building{id:295}) CREATE (b)-[r:Supply{max_supply: 79.52000000000001, current_output: 97.8096,level: 4}]->(g);
CREATE (n: Building {id: 296, name:"building_livestock_ranch", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:296}) CREATE (g)-[r:Demand{max_demand: 22.398000000000003, current_input: 13.436395374416728, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:296}) CREATE (g)-[r:Demand{max_demand: 22.398000000000003, current_input: 1.368310349138217, level: 4}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:296}) CREATE (b)-[r:Supply{max_supply: 44.79600000000001, current_output: 14.804705723554946,level: 4}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:296}) CREATE (b)-[r:Supply{max_supply: 11.199000000000002, current_output: 3.7011764308887365,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:296}) CREATE (b)-[r:Supply{max_supply: 55.995000000000005, current_output: 18.505882154443682,level: 4}]->(g);
CREATE (n: Building {id: 538, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:538}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.370155424758263, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:538}) CREATE (g)-[r:Demand{max_demand: 2.9999999999999996, current_input: 0.18327221392153983, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:538}) CREATE (b)-[r:Supply{max_supply: 74.99999999999999, current_output: 5.7162912359149045,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:538}) CREATE (b)-[r:Supply{max_supply: 20.999999999999996, current_output: 1.6005615460561733,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:538}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 1.143258247182981,level: 3}]->(g);
CREATE (n: Building {id: 539, name:"building_vineyard_plantation", level:2});
MATCH (g: Goods{code: 39}), (b: Building{id:539}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 44.4,level: 2}]->(g);
CREATE (n: Building {id: 540, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:540}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.998926410579841, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:540}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.6109073797384662, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:540}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 6.609833790318307,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:540}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.6524584475795767,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:540}) CREATE (b)-[r:Supply{max_supply: 24.999999999999996, current_output: 8.262292237897883,level: 1}]->(g);
CREATE (n: Building {id: 541, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:541}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.3054536898692331, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:541}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 1.2218147594769324,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:541}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 1.2218147594769324,level: 1}]->(g);
CREATE (n: Building {id: 574, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:574}) CREATE (g)-[r:Demand{max_demand: 9.9843, current_input: 5.98950809611523, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:574}) CREATE (g)-[r:Demand{max_demand: 4.992145454545454, current_input: 0.30497384989096576, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:574}) CREATE (b)-[r:Supply{max_supply: 19.9686, current_output: 6.599456351267507,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:574}) CREATE (b)-[r:Supply{max_supply: 4.992145454545454, current_output: 1.6498625855819244,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:574}) CREATE (b)-[r:Supply{max_supply: 14.976445454545454, current_output: 4.949590761215678,level: 1}]->(g);
CREATE (n: Building {id: 575, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:575}) CREATE (g)-[r:Demand{max_demand: 4.999, current_input: 1.056617993518593, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:575}) CREATE (b)-[r:Supply{max_supply: 49.99, current_output: 10.566179935185932,level: 1}]->(g);
CREATE (n: Building {id: 576, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:576}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 1096, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1096}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 1097, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1097}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.408638917037992, level: 1}]->(b);
CREATE (n: Building {id: 1098, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:1098}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.45671847491942097, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1098}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.06109073797384662, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1098}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 1.9054304119716352,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1098}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 0.5335205153520579,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1098}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.38108608239432706,level: 1}]->(g);
CREATE (n: Building {id: 1099, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1099}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.998926410579841, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1099}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 11.997852821159682,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1099}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 2.9994632052899206,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1099}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 2.9994632052899206,level: 1}]->(g);
CREATE (n: Building {id: 1100, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1100}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 4.817277834075984, level: 2}]->(b);
CREATE (n: Building {id: 1102, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:1102}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 22.0,level: 1}]->(g);
CREATE (n: Building {id: 1103, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1103}) CREATE (g)-[r:Demand{max_demand: 1.7382, current_input: 1.0427333886869878, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1103}) CREATE (b)-[r:Supply{max_supply: 3.4764, current_output: 2.0854667773739757,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1103}) CREATE (b)-[r:Supply{max_supply: 3.4764, current_output: 2.0854667773739757,level: 1}]->(g);
CREATE (n: Building {id: 1105, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1105}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 1106, name:"building_wheat_farm", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:1106}) CREATE (g)-[r:Demand{max_demand: 17.186592920353984, current_input: 1.5698869015289978, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1106}) CREATE (g)-[r:Demand{max_demand: 3.437318584070797, current_input: 0.20998832895210254, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1106}) CREATE (b)-[r:Supply{max_supply: 85.933, current_output: 6.549574063678341,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1106}) CREATE (b)-[r:Supply{max_supply: 24.061238938053098, current_output: 1.8338806568912982,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1106}) CREATE (b)-[r:Supply{max_supply: 17.186592920353984, current_output: 1.309914273144755,level: 4}]->(g);
CREATE (n: Building {id: 1117, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1117}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 1118, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1118}) CREATE (g)-[r:Demand{max_demand: 4.887, current_input: 1.0329450158682465, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1118}) CREATE (b)-[r:Supply{max_supply: 48.87, current_output: 10.329450158682466,level: 1}]->(g);
CREATE (n: Building {id: 1119, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:1119}) CREATE (g)-[r:Demand{max_demand: 4.89, current_input: 0.4466706684711937, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1119}) CREATE (g)-[r:Demand{max_demand: 0.978, current_input: 0.059746741738421996, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1119}) CREATE (b)-[r:Supply{max_supply: 24.45, current_output: 1.8635109429082592,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1119}) CREATE (b)-[r:Supply{max_supply: 6.846, current_output: 0.5217830640143126,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1119}) CREATE (b)-[r:Supply{max_supply: 4.89, current_output: 0.3727021885816518,level: 1}]->(g);
CREATE (n: Building {id: 1146, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1146}) CREATE (g)-[r:Demand{max_demand: 2.69, current_input: 0.5685741953520735, level: 1}]->(b);
CREATE (n: Building {id: 1147, name:"building_sugar_plantation", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1147}) CREATE (b)-[r:Supply{max_supply: 59.838, current_output: 60.43638,level: 2}]->(g);
CREATE (n: Building {id: 1148, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1148}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 16778787, name:"building_cotton_plantation", level:1});
MATCH (g: Goods{code: 16}), (b: Building{id:16778787}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.6099680468912116, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:16778787}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 4.879744375129693,level: 1}]->(g);
CREATE (n: Building {id: 16778819, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16778819}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 8.537702317574267, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16778819}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 7.0869311102527695, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16778819}) CREATE (g)-[r:Demand{max_demand: 49.9995, current_input: 0.634482226791017, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16778819}) CREATE (g)-[r:Demand{max_demand: 9.9999, current_input: 0.6109012706646688, level: 1}]->(b);
CREATE (n: Building {id: 1613, name:"building_sugar_plantation", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:1613}) CREATE (b)-[r:Supply{max_supply: 89.23769642857141, current_output: 99.94622,level: 3}]->(g);
CREATE (n: Building {id: 1614, name:"building_coffee_plantation", level:2});
MATCH (g: Goods{code: 41}), (b: Building{id:1614}) CREATE (b)-[r:Supply{max_supply: 36.07599999999999, current_output: 40.04436,level: 2}]->(g);
CREATE (n: Building {id: 1615, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1615}) CREATE (b)-[r:Supply{max_supply: 49.51499999999999, current_output: 54.96165,level: 2}]->(g);
CREATE (n: Building {id: 1616, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1616}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 1641, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1641}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 16778877, name:"building_conscription_center", level:7});
CREATE (n: Building {id: 2195, name:"building_sugar_plantation", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:2195}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
CREATE (n: Building {id: 2196, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2196}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 2217, name:"building_dye_plantation", level:2});
MATCH (g: Goods{code: 21}), (b: Building{id:2217}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 65.5,level: 2}]->(g);
CREATE (n: Building {id: 2218, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2218}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0568293593904714, level: 1}]->(b);
CREATE (n: Building {id: 2702, name:"building_subsistence_farms", level:33});
MATCH (g: Goods{code: 7}), (b: Building{id:2702}) CREATE (b)-[r:Supply{max_supply: 65.69177272727272, current_output: 72.26095,level: 33}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2702}) CREATE (b)-[r:Supply{max_supply: 16.422936363636364, current_output: 18.06523,level: 33}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2702}) CREATE (b)-[r:Supply{max_supply: 16.422936363636364, current_output: 18.06523,level: 33}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2702}) CREATE (b)-[r:Supply{max_supply: 16.422936363636364, current_output: 18.06523,level: 33}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2702}) CREATE (b)-[r:Supply{max_supply: 16.422936363636364, current_output: 18.06523,level: 33}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2702}) CREATE (b)-[r:Supply{max_supply: 16.422936363636364, current_output: 18.06523,level: 33}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2702}) CREATE (b)-[r:Supply{max_supply: 16.422936363636364, current_output: 18.06523,level: 33}]->(g);
CREATE (n: Building {id: 2703, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2703}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.531525148520443, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:2703}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.008392770845699858, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2703}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.7581028546756335, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2703}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 8.653471826945175,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2703}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 3.4613887307780704,level: 3}]->(g);
CREATE (n: Building {id: 2704, name:"building_subsistence_farms", level:114});
MATCH (g: Goods{code: 7}), (b: Building{id:2704}) CREATE (b)-[r:Supply{max_supply: 226.1212727272727, current_output: 248.7334,level: 114}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2704}) CREATE (b)-[r:Supply{max_supply: 56.530318181818174, current_output: 62.18335,level: 114}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2704}) CREATE (b)-[r:Supply{max_supply: 56.530318181818174, current_output: 62.18335,level: 114}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2704}) CREATE (b)-[r:Supply{max_supply: 56.530318181818174, current_output: 62.18335,level: 114}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2704}) CREATE (b)-[r:Supply{max_supply: 56.530318181818174, current_output: 62.18335,level: 114}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2704}) CREATE (b)-[r:Supply{max_supply: 56.530318181818174, current_output: 62.18335,level: 114}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2704}) CREATE (b)-[r:Supply{max_supply: 56.530318181818174, current_output: 62.18335,level: 114}]->(g);
CREATE (n: Building {id: 2705, name:"building_urban_center", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:2705}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 1.2402253465477002, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2705}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 1.7689066609098119, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2705}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 30.09132007457512,level: 7}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2705}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 7.52283001864378,level: 7}]->(g);
CREATE (n: Building {id: 16780112, name:"building_conscription_center", level:5});
CREATE (n: Building {id: 2956, name:"building_subsistence_farms", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 9.572, current_output: 10.5292,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 2.393, current_output: 2.6323,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 2.393, current_output: 2.6323,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 2.393, current_output: 2.6323,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 2.393, current_output: 2.6323,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 2.393, current_output: 2.6323,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2956}) CREATE (b)-[r:Supply{max_supply: 2.393, current_output: 2.6323,level: 5}]->(g);
CREATE (n: Building {id: 3041, name:"building_subsistence_orchards", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3041}) CREATE (b)-[r:Supply{max_supply: 0.2986181818181818, current_output: 0.32848,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3041}) CREATE (b)-[r:Supply{max_supply: 0.1493090909090909, current_output: 0.16424,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3041}) CREATE (b)-[r:Supply{max_supply: 0.4479272727272727, current_output: 0.49272,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3041}) CREATE (b)-[r:Supply{max_supply: 0.2986181818181818, current_output: 0.32848,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3041}) CREATE (b)-[r:Supply{max_supply: 0.2986181818181818, current_output: 0.32848,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3041}) CREATE (b)-[r:Supply{max_supply: 0.2986181818181818, current_output: 0.32848,level: 18}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3041}) CREATE (b)-[r:Supply{max_supply: 0.7943272727272727, current_output: 0.87376,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3041}) CREATE (b)-[r:Supply{max_supply: 0.2986181818181818, current_output: 0.32848,level: 18}]->(g);
CREATE (n: Building {id: 3046, name:"building_subsistence_farms", level:13});
MATCH (g: Goods{code: 7}), (b: Building{id:3046}) CREATE (b)-[r:Supply{max_supply: 13.182776, current_output: 16.47847,level: 13}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3046}) CREATE (b)-[r:Supply{max_supply: 3.2956879999999997, current_output: 4.11961,level: 13}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3046}) CREATE (b)-[r:Supply{max_supply: 3.2956879999999997, current_output: 4.11961,level: 13}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3046}) CREATE (b)-[r:Supply{max_supply: 3.2956879999999997, current_output: 4.11961,level: 13}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3046}) CREATE (b)-[r:Supply{max_supply: 3.2956879999999997, current_output: 4.11961,level: 13}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3046}) CREATE (b)-[r:Supply{max_supply: 3.2956879999999997, current_output: 4.11961,level: 13}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3046}) CREATE (b)-[r:Supply{max_supply: 3.2956879999999997, current_output: 4.11961,level: 13}]->(g);
CREATE (n: Building {id: 3048, name:"building_subsistence_orchards", level:78});
MATCH (g: Goods{code: 7}), (b: Building{id:3048}) CREATE (b)-[r:Supply{max_supply: 35.64755454545455, current_output: 39.21231,level: 78}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3048}) CREATE (b)-[r:Supply{max_supply: 17.823772727272726, current_output: 19.60615,level: 78}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3048}) CREATE (b)-[r:Supply{max_supply: 53.471336363636354, current_output: 58.81847,level: 78}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3048}) CREATE (b)-[r:Supply{max_supply: 35.64755454545455, current_output: 39.21231,level: 78}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3048}) CREATE (b)-[r:Supply{max_supply: 35.64755454545455, current_output: 39.21231,level: 78}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3048}) CREATE (b)-[r:Supply{max_supply: 35.64755454545455, current_output: 39.21231,level: 78}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3048}) CREATE (b)-[r:Supply{max_supply: 94.82250909090908, current_output: 104.30476,level: 78}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3048}) CREATE (b)-[r:Supply{max_supply: 35.64755454545455, current_output: 39.21231,level: 78}]->(g);
CREATE (n: Building {id: 3072, name:"building_subsistence_farms", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3072}) CREATE (b)-[r:Supply{max_supply: 9.5544, current_output: 10.50984,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3072}) CREATE (b)-[r:Supply{max_supply: 2.3886, current_output: 2.62746,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3072}) CREATE (b)-[r:Supply{max_supply: 2.3886, current_output: 2.62746,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3072}) CREATE (b)-[r:Supply{max_supply: 2.3886, current_output: 2.62746,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3072}) CREATE (b)-[r:Supply{max_supply: 2.3886, current_output: 2.62746,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3072}) CREATE (b)-[r:Supply{max_supply: 2.3886, current_output: 2.62746,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3072}) CREATE (b)-[r:Supply{max_supply: 2.3886, current_output: 2.62746,level: 5}]->(g);
CREATE (n: Building {id: 3104, name:"building_subsistence_farms", level:107});
MATCH (g: Goods{code: 7}), (b: Building{id:3104}) CREATE (b)-[r:Supply{max_supply: 210.3662727272727, current_output: 231.4029,level: 107}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3104}) CREATE (b)-[r:Supply{max_supply: 52.59156363636363, current_output: 57.85072,level: 107}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3104}) CREATE (b)-[r:Supply{max_supply: 52.59156363636363, current_output: 57.85072,level: 107}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3104}) CREATE (b)-[r:Supply{max_supply: 52.59156363636363, current_output: 57.85072,level: 107}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3104}) CREATE (b)-[r:Supply{max_supply: 52.59156363636363, current_output: 57.85072,level: 107}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3104}) CREATE (b)-[r:Supply{max_supply: 52.59156363636363, current_output: 57.85072,level: 107}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3104}) CREATE (b)-[r:Supply{max_supply: 52.59156363636363, current_output: 57.85072,level: 107}]->(g);
CREATE (n: Building {id: 3105, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3105}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.3543500990136286, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3105}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.005595180563799905, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3105}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.5054019031170891, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3105}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 5.76898121796345,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3105}) CREATE (b)-[r:Supply{max_supply: 16.0, current_output: 2.30759248718538,level: 2}]->(g);
CREATE (n: Building {id: 3106, name:"building_subsistence_farms", level:83});
MATCH (g: Goods{code: 7}), (b: Building{id:3106}) CREATE (b)-[r:Supply{max_supply: 165.22477272727272, current_output: 181.74725,level: 83}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3106}) CREATE (b)-[r:Supply{max_supply: 41.30619090909091, current_output: 45.43681,level: 83}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3106}) CREATE (b)-[r:Supply{max_supply: 41.30619090909091, current_output: 45.43681,level: 83}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3106}) CREATE (b)-[r:Supply{max_supply: 41.30619090909091, current_output: 45.43681,level: 83}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3106}) CREATE (b)-[r:Supply{max_supply: 41.30619090909091, current_output: 45.43681,level: 83}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3106}) CREATE (b)-[r:Supply{max_supply: 41.30619090909091, current_output: 45.43681,level: 83}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3106}) CREATE (b)-[r:Supply{max_supply: 41.30619090909091, current_output: 45.43681,level: 83}]->(g);
CREATE (n: Building {id: 3107, name:"building_urban_center", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3107}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8858752475340715, level: 5}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3107}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.013987951409499764, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3107}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.2635047577927225, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3107}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 14.422453044908625,level: 5}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3107}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 5.76898121796345,level: 5}]->(g);
CREATE (n: Building {id: 3108, name:"building_subsistence_farms", level:63});
MATCH (g: Goods{code: 7}), (b: Building{id:3108}) CREATE (b)-[r:Supply{max_supply: 124.5825, current_output: 149.499,level: 63}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3108}) CREATE (b)-[r:Supply{max_supply: 31.145625, current_output: 37.37475,level: 63}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3108}) CREATE (b)-[r:Supply{max_supply: 31.145625, current_output: 37.37475,level: 63}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3108}) CREATE (b)-[r:Supply{max_supply: 31.145625, current_output: 37.37475,level: 63}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3108}) CREATE (b)-[r:Supply{max_supply: 31.145625, current_output: 37.37475,level: 63}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3108}) CREATE (b)-[r:Supply{max_supply: 31.145625, current_output: 37.37475,level: 63}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3108}) CREATE (b)-[r:Supply{max_supply: 31.145625, current_output: 37.37475,level: 63}]->(g);
CREATE (n: Building {id: 3109, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3109}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.7087001980272573, level: 4}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3109}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.01119036112759981, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3109}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.0108038062341782, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3109}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 11.5379624359269,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3109}) CREATE (b)-[r:Supply{max_supply: 32.0, current_output: 4.61518497437076,level: 4}]->(g);
CREATE (n: Building {id: 3110, name:"building_subsistence_farms", level:73});
MATCH (g: Goods{code: 7}), (b: Building{id:3110}) CREATE (b)-[r:Supply{max_supply: 144.4816, current_output: 173.37792,level: 73}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3110}) CREATE (b)-[r:Supply{max_supply: 36.1204, current_output: 43.34448,level: 73}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3110}) CREATE (b)-[r:Supply{max_supply: 36.1204, current_output: 43.34448,level: 73}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3110}) CREATE (b)-[r:Supply{max_supply: 36.1204, current_output: 43.34448,level: 73}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3110}) CREATE (b)-[r:Supply{max_supply: 36.1204, current_output: 43.34448,level: 73}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3110}) CREATE (b)-[r:Supply{max_supply: 36.1204, current_output: 43.34448,level: 73}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3110}) CREATE (b)-[r:Supply{max_supply: 36.1204, current_output: 43.34448,level: 73}]->(g);
CREATE (n: Building {id: 3111, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3111}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.1771750495068143, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3111}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0027975902818999526, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3111}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.25270095155854455, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3111}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 2.884490608981725,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3111}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 1.15379624359269,level: 1}]->(g);
CREATE (n: Building {id: 3112, name:"building_subsistence_farms", level:56});
MATCH (g: Goods{code: 7}), (b: Building{id:3112}) CREATE (b)-[r:Supply{max_supply: 106.93311818181817, current_output: 117.62643,level: 56}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3112}) CREATE (b)-[r:Supply{max_supply: 26.733272727272727, current_output: 29.4066,level: 56}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3112}) CREATE (b)-[r:Supply{max_supply: 26.733272727272727, current_output: 29.4066,level: 56}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3112}) CREATE (b)-[r:Supply{max_supply: 26.733272727272727, current_output: 29.4066,level: 56}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3112}) CREATE (b)-[r:Supply{max_supply: 26.733272727272727, current_output: 29.4066,level: 56}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3112}) CREATE (b)-[r:Supply{max_supply: 26.733272727272727, current_output: 29.4066,level: 56}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3112}) CREATE (b)-[r:Supply{max_supply: 26.733272727272727, current_output: 29.4066,level: 56}]->(g);
CREATE (n: Building {id: 3113, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3113}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.3543500990136286, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3113}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.005595180563799905, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3113}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.5054019031170891, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3113}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 5.76898121796345,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3113}) CREATE (b)-[r:Supply{max_supply: 16.0, current_output: 2.30759248718538,level: 2}]->(g);
CREATE (n: Building {id: 3114, name:"building_subsistence_farms", level:86});
MATCH (g: Goods{code: 7}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 165.54139999999998, current_output: 182.09554,level: 86}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 41.38534545454545, current_output: 45.52388,level: 86}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 41.38534545454545, current_output: 45.52388,level: 86}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 41.38534545454545, current_output: 45.52388,level: 86}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 41.38534545454545, current_output: 45.52388,level: 86}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 41.38534545454545, current_output: 45.52388,level: 86}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 41.38534545454545, current_output: 45.52388,level: 86}]->(g);
CREATE (n: Building {id: 3115, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3115}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.1771750495068143, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3115}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0027975902818999526, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3115}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.25270095155854455, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3115}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 2.884490608981725,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3115}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 1.15379624359269,level: 1}]->(g);
CREATE (n: Building {id: 3116, name:"building_subsistence_farms", level:78});
MATCH (g: Goods{code: 7}), (b: Building{id:3116}) CREATE (b)-[r:Supply{max_supply: 154.2652727272727, current_output: 169.6918,level: 78}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3116}) CREATE (b)-[r:Supply{max_supply: 38.566318181818176, current_output: 42.42295,level: 78}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3116}) CREATE (b)-[r:Supply{max_supply: 38.566318181818176, current_output: 42.42295,level: 78}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3116}) CREATE (b)-[r:Supply{max_supply: 38.566318181818176, current_output: 42.42295,level: 78}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3116}) CREATE (b)-[r:Supply{max_supply: 38.566318181818176, current_output: 42.42295,level: 78}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3116}) CREATE (b)-[r:Supply{max_supply: 38.566318181818176, current_output: 42.42295,level: 78}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3116}) CREATE (b)-[r:Supply{max_supply: 38.566318181818176, current_output: 42.42295,level: 78}]->(g);
CREATE (n: Building {id: 3117, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3117}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.1771750495068143, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3117}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.25270095155854455, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3117}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 4.298760010653588,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3117}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.074690002663397,level: 1}]->(g);
CREATE (n: Building {id: 3118, name:"building_subsistence_farms", level:104});
MATCH (g: Goods{code: 7}), (b: Building{id:3118}) CREATE (b)-[r:Supply{max_supply: 206.60223636363637, current_output: 227.26246,level: 104}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3118}) CREATE (b)-[r:Supply{max_supply: 51.65055454545454, current_output: 56.81561,level: 104}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3118}) CREATE (b)-[r:Supply{max_supply: 51.65055454545454, current_output: 56.81561,level: 104}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3118}) CREATE (b)-[r:Supply{max_supply: 51.65055454545454, current_output: 56.81561,level: 104}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3118}) CREATE (b)-[r:Supply{max_supply: 51.65055454545454, current_output: 56.81561,level: 104}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3118}) CREATE (b)-[r:Supply{max_supply: 51.65055454545454, current_output: 56.81561,level: 104}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3118}) CREATE (b)-[r:Supply{max_supply: 51.65055454545454, current_output: 56.81561,level: 104}]->(g);
CREATE (n: Building {id: 3119, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3119}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.531525148520443, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3119}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.008392770845699858, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3119}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.7581028546756335, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3119}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 8.653471826945175,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3119}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 3.4613887307780704,level: 3}]->(g);
CREATE (n: Building {id: 3120, name:"building_subsistence_farms", level:111});
MATCH (g: Goods{code: 7}), (b: Building{id:3120}) CREATE (b)-[r:Supply{max_supply: 220.8567, current_output: 242.94237,level: 111}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3120}) CREATE (b)-[r:Supply{max_supply: 55.214172727272725, current_output: 60.73559,level: 111}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3120}) CREATE (b)-[r:Supply{max_supply: 55.214172727272725, current_output: 60.73559,level: 111}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3120}) CREATE (b)-[r:Supply{max_supply: 55.214172727272725, current_output: 60.73559,level: 111}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3120}) CREATE (b)-[r:Supply{max_supply: 55.214172727272725, current_output: 60.73559,level: 111}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3120}) CREATE (b)-[r:Supply{max_supply: 55.214172727272725, current_output: 60.73559,level: 111}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3120}) CREATE (b)-[r:Supply{max_supply: 55.214172727272725, current_output: 60.73559,level: 111}]->(g);
CREATE (n: Building {id: 3121, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3121}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.531525148520443, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3121}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.008392770845699858, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3121}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.7581028546756335, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3121}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 8.653471826945175,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3121}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 3.4613887307780704,level: 3}]->(g);
CREATE (n: Building {id: 3122, name:"building_subsistence_farms", level:62});
MATCH (g: Goods{code: 7}), (b: Building{id:3122}) CREATE (b)-[r:Supply{max_supply: 118.6146727272727, current_output: 130.47614,level: 62}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3122}) CREATE (b)-[r:Supply{max_supply: 29.653663636363635, current_output: 32.61903,level: 62}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3122}) CREATE (b)-[r:Supply{max_supply: 29.653663636363635, current_output: 32.61903,level: 62}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3122}) CREATE (b)-[r:Supply{max_supply: 29.653663636363635, current_output: 32.61903,level: 62}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3122}) CREATE (b)-[r:Supply{max_supply: 29.653663636363635, current_output: 32.61903,level: 62}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3122}) CREATE (b)-[r:Supply{max_supply: 29.653663636363635, current_output: 32.61903,level: 62}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3122}) CREATE (b)-[r:Supply{max_supply: 29.653663636363635, current_output: 32.61903,level: 62}]->(g);
CREATE (n: Building {id: 3123, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3123}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.3543500990136286, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3123}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.005595180563799905, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3123}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.5054019031170891, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3123}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 5.76898121796345,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3123}) CREATE (b)-[r:Supply{max_supply: 16.0, current_output: 2.30759248718538,level: 2}]->(g);
CREATE (n: Building {id: 3124, name:"building_subsistence_farms", level:74});
MATCH (g: Goods{code: 7}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 97.21083333333334, current_output: 116.653,level: 74}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 24.302708333333335, current_output: 29.16325,level: 74}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 24.302708333333335, current_output: 29.16325,level: 74}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 24.302708333333335, current_output: 29.16325,level: 74}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 24.302708333333335, current_output: 29.16325,level: 74}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 24.302708333333335, current_output: 29.16325,level: 74}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 24.302708333333335, current_output: 29.16325,level: 74}]->(g);
CREATE (n: Building {id: 3125, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3125}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.3543500990136286, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3125}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.005595180563799905, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3125}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.5054019031170891, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3125}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 5.76898121796345,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3125}) CREATE (b)-[r:Supply{max_supply: 16.0, current_output: 2.30759248718538,level: 2}]->(g);
CREATE (n: Building {id: 3126, name:"building_subsistence_farms", level:46});
MATCH (g: Goods{code: 7}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 73.00751818181817, current_output: 80.30827,level: 46}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 18.251872727272726, current_output: 20.07706,level: 46}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 18.251872727272726, current_output: 20.07706,level: 46}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 18.251872727272726, current_output: 20.07706,level: 46}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 18.251872727272726, current_output: 20.07706,level: 46}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 18.251872727272726, current_output: 20.07706,level: 46}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 18.251872727272726, current_output: 20.07706,level: 46}]->(g);
CREATE (n: Building {id: 3127, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3127}) CREATE (g)-[r:Demand{max_demand: 1.8464356435643565, current_input: 0.32714232655966136, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3127}) CREATE (g)-[r:Demand{max_demand: 1.8464356435643565, current_input: 0.005165570412589329, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3127}) CREATE (g)-[r:Demand{max_demand: 1.8464356435643565, current_input: 0.46659604412032646, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3127}) CREATE (b)-[r:Supply{max_supply: 36.928792079207916, current_output: 5.326037697675697,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3127}) CREATE (b)-[r:Supply{max_supply: 14.771514851485149, current_output: 2.13041479347715,level: 2}]->(g);
CREATE (n: Building {id: 3128, name:"building_subsistence_farms", level:54});
MATCH (g: Goods{code: 7}), (b: Building{id:3128}) CREATE (b)-[r:Supply{max_supply: 94.39415454545453, current_output: 103.83357,level: 54}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3128}) CREATE (b)-[r:Supply{max_supply: 23.598536363636363, current_output: 25.95839,level: 54}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3128}) CREATE (b)-[r:Supply{max_supply: 23.598536363636363, current_output: 25.95839,level: 54}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3128}) CREATE (b)-[r:Supply{max_supply: 23.598536363636363, current_output: 25.95839,level: 54}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3128}) CREATE (b)-[r:Supply{max_supply: 23.598536363636363, current_output: 25.95839,level: 54}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3128}) CREATE (b)-[r:Supply{max_supply: 23.598536363636363, current_output: 25.95839,level: 54}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3128}) CREATE (b)-[r:Supply{max_supply: 23.598536363636363, current_output: 25.95839,level: 54}]->(g);
CREATE (n: Building {id: 3129, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3129}) CREATE (g)-[r:Demand{max_demand: 1.9774950495049506, current_input: 0.35036278329551984, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3129}) CREATE (g)-[r:Demand{max_demand: 1.9774950495049506, current_input: 0.005532220933000315, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3129}) CREATE (g)-[r:Demand{max_demand: 1.9774950495049506, current_input: 0.49971488071221215, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3129}) CREATE (b)-[r:Supply{max_supply: 39.550000000000004, current_output: 5.7040801792613625,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3129}) CREATE (b)-[r:Supply{max_supply: 15.819999999999999, current_output: 2.281632071704544,level: 2}]->(g);
CREATE (n: Building {id: 3130, name:"building_subsistence_farms", level:79});
MATCH (g: Goods{code: 7}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 154.90793636363634, current_output: 170.39873,level: 79}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 38.72698181818181, current_output: 42.59968,level: 79}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 38.72698181818181, current_output: 42.59968,level: 79}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 38.72698181818181, current_output: 42.59968,level: 79}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 38.72698181818181, current_output: 42.59968,level: 79}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 38.72698181818181, current_output: 42.59968,level: 79}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 38.72698181818181, current_output: 42.59968,level: 79}]->(g);
CREATE (n: Building {id: 3131, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3131}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.1771750495068143, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3131}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.25270095155854455, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3131}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 4.298760010653588,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3131}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.074690002663397,level: 1}]->(g);
CREATE (n: Building {id: 3132, name:"building_subsistence_farms", level:106});
MATCH (g: Goods{code: 7}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 182.50231818181817, current_output: 200.75255,level: 106}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 45.625572727272726, current_output: 50.18813,level: 106}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 45.625572727272726, current_output: 50.18813,level: 106}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 45.625572727272726, current_output: 50.18813,level: 106}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 45.625572727272726, current_output: 50.18813,level: 106}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 45.625572727272726, current_output: 50.18813,level: 106}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 45.625572727272726, current_output: 50.18813,level: 106}]->(g);
CREATE (n: Building {id: 3133, name:"building_subsistence_farms", level:57});
MATCH (g: Goods{code: 7}), (b: Building{id:3133}) CREATE (b)-[r:Supply{max_supply: 112.3356, current_output: 134.80272,level: 57}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3133}) CREATE (b)-[r:Supply{max_supply: 28.0839, current_output: 33.70068,level: 57}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3133}) CREATE (b)-[r:Supply{max_supply: 28.0839, current_output: 33.70068,level: 57}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3133}) CREATE (b)-[r:Supply{max_supply: 28.0839, current_output: 33.70068,level: 57}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3133}) CREATE (b)-[r:Supply{max_supply: 28.0839, current_output: 33.70068,level: 57}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3133}) CREATE (b)-[r:Supply{max_supply: 28.0839, current_output: 33.70068,level: 57}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3133}) CREATE (b)-[r:Supply{max_supply: 28.0839, current_output: 33.70068,level: 57}]->(g);
CREATE (n: Building {id: 3134, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3134}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.1771750495068143, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3134}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.25270095155854455, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3134}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 4.298760010653588,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3134}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.074690002663397,level: 1}]->(g);
CREATE (n: Building {id: 3135, name:"building_subsistence_farms", level:31});
MATCH (g: Goods{code: 7}), (b: Building{id:3135}) CREATE (b)-[r:Supply{max_supply: 51.25601666666667, current_output: 61.50722,level: 31}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3135}) CREATE (b)-[r:Supply{max_supply: 12.814, current_output: 15.3768,level: 31}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3135}) CREATE (b)-[r:Supply{max_supply: 12.814, current_output: 15.3768,level: 31}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3135}) CREATE (b)-[r:Supply{max_supply: 12.814, current_output: 15.3768,level: 31}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3135}) CREATE (b)-[r:Supply{max_supply: 12.814, current_output: 15.3768,level: 31}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3135}) CREATE (b)-[r:Supply{max_supply: 12.814, current_output: 15.3768,level: 31}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3135}) CREATE (b)-[r:Supply{max_supply: 12.814, current_output: 15.3768,level: 31}]->(g);
CREATE (n: Building {id: 3136, name:"building_urban_center", level:13});
MATCH (g: Goods{code: 10}), (b: Building{id:3136}) CREATE (g)-[r:Demand{max_demand: 9.383785714285715, current_input: 1.6625726984899085, level: 13}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3136}) CREATE (g)-[r:Demand{max_demand: 9.383785714285715, current_input: 2.371291579221477, level: 13}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3136}) CREATE (b)-[r:Supply{max_supply: 187.6757946428571, current_output: 40.338660048917454,level: 13}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3136}) CREATE (b)-[r:Supply{max_supply: 46.918946428571424, current_output: 10.084664532457042,level: 13}]->(g);
CREATE (n: Building {id: 3177, name:"building_subsistence_fishing_villages", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3177}) CREATE (b)-[r:Supply{max_supply: 2.4871, current_output: 2.73581,level: 5}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:3177}) CREATE (b)-[r:Supply{max_supply: 9.9484, current_output: 10.94324,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3177}) CREATE (b)-[r:Supply{max_supply: 1.2435454545454543, current_output: 1.3679,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3177}) CREATE (b)-[r:Supply{max_supply: 3.7306454545454546, current_output: 4.10371,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3177}) CREATE (b)-[r:Supply{max_supply: 2.4871, current_output: 2.73581,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3177}) CREATE (b)-[r:Supply{max_supply: 2.4871, current_output: 2.73581,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3177}) CREATE (b)-[r:Supply{max_supply: 2.4871, current_output: 2.73581,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3177}) CREATE (b)-[r:Supply{max_supply: 2.4871, current_output: 2.73581,level: 5}]->(g);
CREATE (n: Building {id: 3203, name:"building_subsistence_rice_paddies", level:12});
MATCH (g: Goods{code: 7}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 31.957199999999997, current_output: 41.54436,level: 12}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 5.3262, current_output: 6.92406,level: 12}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 5.3262, current_output: 6.92406,level: 12}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 7.1015999999999995, current_output: 9.23208,level: 12}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 7.1015999999999995, current_output: 9.23208,level: 12}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 7.1015999999999995, current_output: 9.23208,level: 12}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3203}) CREATE (b)-[r:Supply{max_supply: 7.1015999999999995, current_output: 9.23208,level: 12}]->(g);
CREATE (n: Building {id: 3400, name:"building_subsistence_farms", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:3400}) CREATE (b)-[r:Supply{max_supply: 3.8395999999999995, current_output: 4.22356,level: 2}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3400}) CREATE (b)-[r:Supply{max_supply: 0.9598999999999999, current_output: 1.05589,level: 2}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3400}) CREATE (b)-[r:Supply{max_supply: 0.9598999999999999, current_output: 1.05589,level: 2}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3400}) CREATE (b)-[r:Supply{max_supply: 0.9598999999999999, current_output: 1.05589,level: 2}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3400}) CREATE (b)-[r:Supply{max_supply: 0.9598999999999999, current_output: 1.05589,level: 2}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3400}) CREATE (b)-[r:Supply{max_supply: 0.9598999999999999, current_output: 1.05589,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3400}) CREATE (b)-[r:Supply{max_supply: 0.9598999999999999, current_output: 1.05589,level: 2}]->(g);
CREATE (n: Building {id: 3407, name:"building_subsistence_orchards", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3407}) CREATE (b)-[r:Supply{max_supply: 5.5843, current_output: 5.5843,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3407}) CREATE (b)-[r:Supply{max_supply: 2.79215, current_output: 2.79215,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3407}) CREATE (b)-[r:Supply{max_supply: 8.37645, current_output: 8.37645,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3407}) CREATE (b)-[r:Supply{max_supply: 5.5843, current_output: 5.5843,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3407}) CREATE (b)-[r:Supply{max_supply: 5.5843, current_output: 5.5843,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3407}) CREATE (b)-[r:Supply{max_supply: 5.5843, current_output: 5.5843,level: 20}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3407}) CREATE (b)-[r:Supply{max_supply: 14.85423, current_output: 14.85423,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3407}) CREATE (b)-[r:Supply{max_supply: 5.5843, current_output: 5.5843,level: 20}]->(g);
CREATE (n: Building {id: 3753, name:"building_subsistence_pastures", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 4.5, current_output: 4.95,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 6.749999999999999, current_output: 7.425,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 2.25, current_output: 2.475,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 4.5, current_output: 4.95,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 4.5, current_output: 4.95,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 4.5, current_output: 4.95,level: 9}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 11.969999999999999, current_output: 13.167,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3753}) CREATE (b)-[r:Supply{max_supply: 4.5, current_output: 4.95,level: 9}]->(g);
CREATE (n: Building {id: 3798, name:"building_barracks", level:20});
MATCH (g: Goods{code: 0}), (b: Building{id:3798}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.0690366660627424, level: 20}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3798}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 2.0142489070176968, level: 20}]->(b);
CREATE (n: Building {id: 3799, name:"building_barracks", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:3799}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.8017774995470568, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3799}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 1.5106866802632726, level: 15}]->(b);
CREATE (n: Building {id: 3800, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:3800}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.0071244535088484, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3800}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.997852821159682, level: 10}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3800}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.25379542867069355, level: 10}]->(b);
CREATE (n: Building {id: 3801, name:"building_barracks", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:3801}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.2699160593656063, level: 5}]->(b);
CREATE (n: Building {id: 3802, name:"building_barracks", level:25});
MATCH (g: Goods{code: 0}), (b: Building{id:3802}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 1.3362958325784282, level: 25}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3802}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 2.517811133772121, level: 25}]->(b);
CREATE (n: Building {id: 3803, name:"building_barracks", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:3803}) CREATE (g)-[r:Demand{max_demand: 14.99985, current_input: 0.8017694817720614, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3803}) CREATE (g)-[r:Demand{max_demand: 29.99985, current_input: 1.5106791268298712, level: 15}]->(b);
CREATE (n: Building {id: 3804, name:"building_barracks", level:15});
MATCH (g: Goods{code: 1}), (b: Building{id:3804}) CREATE (g)-[r:Demand{max_demand: 29.99985, current_input: 1.5106791268298712, level: 15}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3804}) CREATE (g)-[r:Demand{max_demand: 29.99985, current_input: 17.99668924784336, level: 15}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3804}) CREATE (g)-[r:Demand{max_demand: 29.99985, current_input: 0.38069123954032524, level: 15}]->(b);
CREATE (n: Building {id: 3805, name:"building_barracks", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:3805}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.2699160593656063, level: 5}]->(b);
CREATE (n: Building {id: 3806, name:"building_barracks", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:3806}) CREATE (g)-[r:Demand{max_demand: 14.99985, current_input: 0.8017694817720614, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3806}) CREATE (g)-[r:Demand{max_demand: 29.99985, current_input: 1.5106791268298712, level: 15}]->(b);
CREATE (n: Building {id: 3807, name:"building_barracks", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:3807}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.5345183330313712, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3807}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.0071244535088484, level: 10}]->(b);
CREATE (n: Building {id: 3808, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:3808}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.0071244535088484, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3808}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.997852821159682, level: 10}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3808}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.25379542867069355, level: 10}]->(b);
CREATE (n: Building {id: 3809, name:"building_barracks", level:10});
MATCH (g: Goods{code: 2}), (b: Building{id:3809}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.5398321187312126, level: 10}]->(b);
CREATE (n: Building {id: 3810, name:"building_barracks", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:3810}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.10690366660627425, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3810}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.20142489070176967, level: 2}]->(b);
CREATE (n: Building {id: 3811, name:"building_barracks", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:3811}) CREATE (g)-[r:Demand{max_demand: 14.99985, current_input: 0.8017694817720614, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3811}) CREATE (g)-[r:Demand{max_demand: 29.99985, current_input: 1.5106791268298712, level: 15}]->(b);
CREATE (n: Building {id: 3812, name:"building_barracks", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:3812}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.5345183330313712, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3812}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.0071244535088484, level: 10}]->(b);
CREATE (n: Building {id: 3813, name:"building_barracks", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:3813}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.2699160593656063, level: 5}]->(b);
CREATE (n: Building {id: 3814, name:"building_naval_base", level:17});
MATCH (g: Goods{code: 5}), (b: Building{id:3814}) CREATE (g)-[r:Demand{max_demand: 28.4862, current_input: 2.9630752195366235, level: 17}]->(b);
CREATE (n: Building {id: 3815, name:"building_naval_base", level:24});
MATCH (g: Goods{code: 5}), (b: Building{id:3815}) CREATE (g)-[r:Demand{max_demand: 36.60646, current_input: 3.8077277594399614, level: 24}]->(b);
CREATE (n: Building {id: 3816, name:"building_naval_base", level:7});
MATCH (g: Goods{code: 5}), (b: Building{id:3816}) CREATE (g)-[r:Demand{max_demand: 8.2581, current_input: 0.8589903697388698, level: 7}]->(b);
CREATE (n: Building {id: 3817, name:"building_naval_base", level:6});
MATCH (g: Goods{code: 5}), (b: Building{id:3817}) CREATE (g)-[r:Demand{max_demand: 10.62396, current_input: 1.1050822015343678, level: 6}]->(b);
CREATE (n: Building {id: 3818, name:"building_naval_base", level:27});
MATCH (g: Goods{code: 5}), (b: Building{id:3818}) CREATE (g)-[r:Demand{max_demand: 48.68355, current_input: 5.063961518351769, level: 27}]->(b);
CREATE (n: Building {id: 3819, name:"building_naval_base", level:8});
MATCH (g: Goods{code: 5}), (b: Building{id:3819}) CREATE (g)-[r:Demand{max_demand: 14.324, current_input: 1.4899526593453178, level: 8}]->(b);
CREATE (n: Building {id: 3820, name:"building_naval_base", level:16});
MATCH (g: Goods{code: 5}), (b: Building{id:3820}) CREATE (g)-[r:Demand{max_demand: 24.0, current_input: 2.496430035205783, level: 16}]->(b);
CREATE (n: Building {id: 16781190, name:"building_artillery_foundries", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:16781190}) CREATE (g)-[r:Demand{max_demand: 1.94175, current_input: 0.024640363681065956, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16781190}) CREATE (g)-[r:Demand{max_demand: 1.2945, current_input: 0.05262750754329118, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16781190}) CREATE (b)-[r:Supply{max_supply: 3.19741, current_output: 0.08528207121886677,level: 1}]->(g);
CREATE (n: Building {id: 4138, name:"building_trade_center", level:54});
CREATE (n: Building {id: 4184, name:"building_trade_center", level:16});
CREATE (n: Building {id: 4238, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:4238}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
CREATE (n: Building {id: 16781528, name:"building_motor_industry", level:1});
MATCH (g: Goods{code: 30}), (b: Building{id:16781528}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.152610948026438, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16781528}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 0.20348126403525066,level: 1}]->(g);
CREATE (n: Building {id: 4496, name:"building_conscription_center", level:14});
CREATE (n: Building {id: 16781780, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16781780}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.315251485204429, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16781780}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.10174063201762532, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16781780}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 7.290483244307822,level: 1}]->(g);
CREATE (n: Building {id: 4582, name:"building_trade_center", level:24});
CREATE (n: Building {id: 4624, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:4624}) CREATE (g)-[r:Demand{max_demand: 2.70865, current_input: 0.5725161688626, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:4624}) CREATE (b)-[r:Supply{max_supply: 27.0865, current_output: 5.725161688626001,level: 1}]->(g);
CREATE (n: Building {id: 4678, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:4678}) CREATE (b)-[r:Supply{max_supply: 24.648, current_output: 27.1128,level: 1}]->(g);
CREATE (n: Building {id: 4780, name:"building_conscription_center", level:8});
CREATE (n: Building {id: 4832, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 4870, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:4870}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
CREATE (n: Building {id: 4871, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 4919, name:"building_conscription_center", level:11});
CREATE (n: Building {id: 4925, name:"building_construction_sector", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:4925}) CREATE (g)-[r:Demand{max_demand: 59.9994, current_input: 25.6131069527228, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4925}) CREATE (g)-[r:Demand{max_demand: 119.9988, current_input: 21.260793330758307, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4925}) CREATE (g)-[r:Demand{max_demand: 149.9985, current_input: 1.9034466803730512, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4925}) CREATE (g)-[r:Demand{max_demand: 29.9997, current_input: 1.8327038119940064, level: 3}]->(b);
CREATE (n: Building {id: 4945, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:4945}) CREATE (g)-[r:Demand{max_demand: 49.9995, current_input: 21.344255793935666, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4945}) CREATE (g)-[r:Demand{max_demand: 149.9985, current_input: 26.575991663447887, level: 2}]->(b);
CREATE (n: Building {id: 4964, name:"building_conscription_center", level:9});
CREATE (n: Building {id: 4982, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 4994, name:"building_conscription_center", level:11});
CREATE (n: Building {id: 5000, name:"building_construction_sector", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:5000}) CREATE (g)-[r:Demand{max_demand: 34.19965, current_input: 14.599467547936916, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5000}) CREATE (g)-[r:Demand{max_demand: 102.59897, current_input: 18.177977589098155, level: 3}]->(b);
CREATE (n: Building {id: 5016, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:5016}) CREATE (g)-[r:Demand{max_demand: 12.11987, current_input: 5.173843847823419, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5016}) CREATE (g)-[r:Demand{max_demand: 24.23975, current_input: 4.294678906282802, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5016}) CREATE (g)-[r:Demand{max_demand: 30.29969, current_input: 0.3844961406069563, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5016}) CREATE (g)-[r:Demand{max_demand: 6.05993, current_input: 0.3702055957698523, level: 2}]->(b);
CREATE (n: Building {id: 5032, name:"building_conscription_center", level:7});
CREATE (n: Building {id: 5052, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 5062, name:"building_conscription_center", level:10});
CREATE (n: Building {id: 5102, name:"building_conscription_center", level:8});
CREATE (n: Building {id: 5131, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:5131}) CREATE (g)-[r:Demand{max_demand: 49.9995, current_input: 21.344255793935666, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5131}) CREATE (g)-[r:Demand{max_demand: 149.9985, current_input: 26.575991663447887, level: 2}]->(b);
CREATE (n: Building {id: 16782379, name:"building_trade_center", level:4});
CREATE (n: Building {id: 5173, name:"building_construction_sector", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:5173}) CREATE (g)-[r:Demand{max_demand: 99.999, current_input: 42.68851158787133, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5173}) CREATE (g)-[r:Demand{max_demand: 299.997, current_input: 53.151983326895774, level: 4}]->(b);
CREATE (n: Building {id: 5235, name:"building_construction_sector", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:5235}) CREATE (g)-[r:Demand{max_demand: 59.9994, current_input: 25.6131069527228, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5235}) CREATE (g)-[r:Demand{max_demand: 119.9988, current_input: 21.260793330758307, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5235}) CREATE (g)-[r:Demand{max_demand: 149.9985, current_input: 1.9034466803730512, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5235}) CREATE (g)-[r:Demand{max_demand: 29.9997, current_input: 1.8327038119940064, level: 3}]->(b);
CREATE (n: Building {id: 5369, name:"building_construction_sector", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:5369}) CREATE (g)-[r:Demand{max_demand: 99.999, current_input: 42.68851158787133, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5369}) CREATE (g)-[r:Demand{max_demand: 299.997, current_input: 53.151983326895774, level: 4}]->(b);
CREATE (n: Building {id: 5460, name:"building_construction_sector", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:5460}) CREATE (g)-[r:Demand{max_demand: 59.9874, current_input: 25.607984280105526, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5460}) CREATE (g)-[r:Demand{max_demand: 119.9748, current_input: 21.256541129570145, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5460}) CREATE (g)-[r:Demand{max_demand: 149.9685, current_input: 1.903065987230045, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5460}) CREATE (g)-[r:Demand{max_demand: 29.9937, current_input: 1.8323372675661633, level: 3}]->(b);
CREATE (n: Building {id: 5520, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5520}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.315251485204429, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:5520}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.10174063201762532, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5520}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 7.290483244307822,level: 1}]->(g);
CREATE (n: Building {id: 5638, name:"building_barracks", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:5638}) CREATE (g)-[r:Demand{max_demand: 2.944944444444445, current_input: 0.15741267953144422, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:5638}) CREATE (g)-[r:Demand{max_demand: 5.889922222222222, current_input: 0.2965942349632589, level: 3}]->(b);
CREATE (n: Building {id: 5647, name:"building_construction_sector", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:5647}) CREATE (g)-[r:Demand{max_demand: 79.9992, current_input: 34.15080927029707, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5647}) CREATE (g)-[r:Demand{max_demand: 159.9984, current_input: 28.347724441011078, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5647}) CREATE (g)-[r:Demand{max_demand: 199.998, current_input: 2.537928907164068, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5647}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 2.4436050826586753, level: 4}]->(b);
CREATE (n: Building {id: 5650, name:"building_conscription_center", level:1});
CREATE (n: Building {id: 5656, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5656}) CREATE (g)-[r:Demand{max_demand: 24.98975, current_input: 10.667859003120109, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5656}) CREATE (g)-[r:Demand{max_demand: 74.96925, current_input: 13.28268058023874, level: 1}]->(b);
CREATE (n: Building {id: 5662, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5662}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 8.537702317574267, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5662}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 7.0869311102527695, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5662}) CREATE (g)-[r:Demand{max_demand: 49.9995, current_input: 0.634482226791017, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5662}) CREATE (g)-[r:Demand{max_demand: 9.9999, current_input: 0.6109012706646688, level: 1}]->(b);
CREATE (n: Building {id: 5672, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:5672}) CREATE (g)-[r:Demand{max_demand: 49.9995, current_input: 21.344255793935666, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5672}) CREATE (g)-[r:Demand{max_demand: 149.9985, current_input: 26.575991663447887, level: 2}]->(b);
CREATE (n: Building {id: 5683, name:"building_construction_sector", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:5683}) CREATE (g)-[r:Demand{max_demand: 99.931, current_input: 42.65948310970679, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5683}) CREATE (g)-[r:Demand{max_demand: 199.862, current_input: 35.410559744530914, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5683}) CREATE (g)-[r:Demand{max_demand: 249.8275, current_input: 3.170253872811384, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5683}) CREATE (g)-[r:Demand{max_demand: 49.9655, current_input: 3.0524292682322334, level: 5}]->(b);
CREATE (n: Building {id: 5689, name:"building_chemical_plants", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:5689}) CREATE (g)-[r:Demand{max_demand: 11.2563, current_input: 0.4902802662477832, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5689}) CREATE (g)-[r:Demand{max_demand: 3.7521, current_input: 0.04761329139576546, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:5689}) CREATE (b)-[r:Supply{max_supply: 33.36367, current_output: 0.938283958131431,level: 1}]->(g);
CREATE (n: Building {id: 5714, name:"building_construction_sector", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:5714}) CREATE (g)-[r:Demand{max_demand: 99.931, current_input: 42.65948310970679, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5714}) CREATE (g)-[r:Demand{max_demand: 199.862, current_input: 35.410559744530914, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5714}) CREATE (g)-[r:Demand{max_demand: 249.8275, current_input: 3.170253872811384, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5714}) CREATE (g)-[r:Demand{max_demand: 49.9655, current_input: 3.0524292682322334, level: 5}]->(b);
CREATE (n: Building {id: 16782940, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:16782940}) CREATE (g)-[r:Demand{max_demand: 0.016, current_input: 0.0016642866901371886, level: 1}]->(b);
CREATE (n: Building {id: 5736, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5736}) CREATE (g)-[r:Demand{max_demand: 19.9878, current_input: 8.532579644956995, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5736}) CREATE (g)-[r:Demand{max_demand: 39.9756, current_input: 7.082678909064605, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5736}) CREATE (g)-[r:Demand{max_demand: 49.9695, current_input: 0.6341015336480109, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5736}) CREATE (g)-[r:Demand{max_demand: 9.9939, current_input: 0.6105347262368257, level: 1}]->(b);
CREATE (n: Building {id: 5779, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:5779}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 22.0,level: 1}]->(g);
