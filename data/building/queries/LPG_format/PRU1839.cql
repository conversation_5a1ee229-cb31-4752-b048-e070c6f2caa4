CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:49.53703420652127, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:89.71065833529889, pop_demand:35.428819592519936});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:79.48160278296399, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:26.149288831046775, pop_demand:2203.383040891663});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:13.933278464286193, pop_demand:60.43940566430301});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:27.807809148656013, pop_demand:22.32745764875957});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:29.642624033602942, pop_demand:154.09100961442738});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:45.04432813182943, pop_demand:205.792857612761});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:34.40416104395754, pop_demand:604.3061600320591});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:29.47015101521749, pop_demand:413.4009085373445});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:47.34977254759124, pop_demand:27.869593936779964});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:23.39838519388448, pop_demand:354.4526598495314});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:43.93314864860597, pop_demand:213.11226666666659});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:104.48261730932, pop_demand:1.2281990792073578});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:75.72507373390717, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:44.23440579970556, pop_demand:251.99884432454192});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:63.42197738807869, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:42.15895089180336, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:59.830269737191976, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:83.73277212493286, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:78.39990726640816, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:59.553219886564264, pop_demand:51.712936814531155});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:35.409546644040965, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:59.46102965361785, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:57.29779170955857, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:114.98134065777458, pop_demand:188.9615382685047});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:40.06897029205548, pop_demand:306.88809843060363});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:38.19865634317368, pop_demand:96.4536742962436});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:40.69713096765964, pop_demand:957.732789166668});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:56.55230887017078, pop_demand:126.01909407842545});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:48.70157391705287, pop_demand:44.4312001932004});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:51.408513392235214});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:91.1809753439332, pop_demand:257.042566961176});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:350.0, pop_demand:3.0043639014456907});
CREATE (n: Building {id: 384, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:384}) CREATE (b)-[r:Supply{max_supply: 74.985, current_output: 76.4847,level: 3}]->(g);
CREATE (n: Building {id: 385, name:"building_rye_farm", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:385}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.045008740466825, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:385}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.36998726308114355, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:385}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 14.737362639681356,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:385}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 8.842417583808814,level: 1}]->(g);
CREATE (n: Building {id: 386, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:386}) CREATE (g)-[r:Demand{max_demand: 29.960696078431372, current_input: 17.951166819275603, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:386}) CREATE (g)-[r:Demand{max_demand: 29.960696078431372, current_input: 11.085075942064774, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:386}) CREATE (b)-[r:Supply{max_supply: 59.921392156862744, current_output: 29.036242761340375,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:386}) CREATE (b)-[r:Supply{max_supply: 14.980343137254902, current_output: 7.259058314981013,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:386}) CREATE (b)-[r:Supply{max_supply: 74.90174509803921, current_output: 36.295305827029544,level: 3}]->(g);
CREATE (n: Building {id: 387, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:387}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.549808946217152, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:387}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 22.19923578486861,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:387}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 22.19923578486861,level: 3}]->(g);
CREATE (n: Building {id: 388, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:388}) CREATE (g)-[r:Demand{max_demand: 4.998, current_input: 0.060323150058834256, level: 1}]->(b);
CREATE (n: Building {id: 408, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:408}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.820553057845642, level: 1}]->(b);
CREATE (n: Building {id: 409, name:"building_steel_mills", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:409}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 23.331734064815375, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:409}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 18.66930628536388, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:409}) CREATE (b)-[r:Supply{max_supply: 130.0, current_output: 40.44485659374148,level: 2}]->(g);
CREATE (n: Building {id: 410, name:"building_tooling_workshops", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:410}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 54.80633961096627, level: 5}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:410}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 26.05235414695725, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:410}) CREATE (b)-[r:Supply{max_supply: 400.0, current_output: 125.17982777520287,level: 5}]->(g);
CREATE (n: Building {id: 411, name:"building_iron_mine", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:411}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 19.443111720679475, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:411}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 18.49936315405718, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:411}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 75.88494974947331,level: 5}]->(g);
CREATE (n: Building {id: 412, name:"building_sulfur_mine", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:412}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.665867032407688, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:412}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.099617892434305, level: 3}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:412}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 45.530969849683984,level: 3}]->(g);
CREATE (n: Building {id: 413, name:"building_rye_farm", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:413}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.09001748093365, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:413}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7399745261622871, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:413}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 29.474725279362712,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:413}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 17.684835167617628,level: 2}]->(g);
CREATE (n: Building {id: 415, name:"building_furniture_manufacturies", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:415}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 14.53515823593528, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:415}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 16.44190188328988, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:415}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 16.147485334059567, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:415}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.549808946217152, level: 3}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:415}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 47.361034273269226,level: 3}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:415}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 29.600646420793264,level: 3}]->(g);
CREATE (n: Building {id: 416, name:"building_logging_camp", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:416}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 9.24968157702859, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:416}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 110.99617892434307,level: 5}]->(g);
CREATE (n: Building {id: 417, name:"building_rye_farm", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:417}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.09001748093365, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:417}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7399745261622871, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:417}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 29.474725279362712,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:417}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 17.684835167617628,level: 2}]->(g);
CREATE (n: Building {id: 418, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:418}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 11.983144031288779, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:418}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.39974526162287, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:418}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.38288929291165,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:418}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 4.845722323227912,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:418}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 24.22861161613956,level: 2}]->(g);
CREATE (n: Building {id: 420, name:"building_government_administration", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:420}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 19.28221223138257, level: 4}]->(b);
CREATE (n: Building {id: 421, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:421}) CREATE (g)-[r:Demand{max_demand: 39.996, current_input: 19.37827296014892, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:421}) CREATE (g)-[r:Demand{max_demand: 79.992, current_input: 29.227124787736095, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:421}) CREATE (g)-[r:Demand{max_demand: 99.99, current_input: 23.33429919341918, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:421}) CREATE (g)-[r:Demand{max_demand: 19.998, current_input: 7.399005287096709, level: 2}]->(b);
CREATE (n: Building {id: 422, name:"building_tooling_workshops", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:422}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 65.76760753315952, level: 6}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:422}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 31.262824976348696, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:422}) CREATE (b)-[r:Supply{max_supply: 480.0, current_output: 150.2157933302434,level: 6}]->(g);
CREATE (n: Building {id: 423, name:"building_wheat_farm", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:423}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.1800349618673, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:423}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.4799490523245742, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:423}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 58.949450558725424,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:423}) CREATE (b)-[r:Supply{max_supply: 28.0, current_output: 16.50584615644312,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:423}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 11.789890111745084,level: 4}]->(g);
CREATE (n: Building {id: 424, name:"building_steel_mills", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:424}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 11.665867032407686, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:424}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 9.334653142681942, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:424}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 20.22242829687074,level: 1}]->(g);
CREATE (n: Building {id: 425, name:"building_vineyard_plantation", level:5});
MATCH (g: Goods{code: 39}), (b: Building{id:425}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 104.0,level: 5}]->(g);
CREATE (n: Building {id: 429, name:"building_textile_mills", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:429}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 58.14063294374112, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:429}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:429}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 43.60547470780584,level: 3}]->(g);
CREATE (n: Building {id: 430, name:"building_coal_mine", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:430}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 18.49936315405718, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:430}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 73.99745261622871,level: 5}]->(g);
CREATE (n: Building {id: 431, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:431}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.777244688271791, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:431}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.39974526162287, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:431}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 30.353979899789323,level: 2}]->(g);
CREATE (n: Building {id: 432, name:"building_wheat_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:432}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 20.225043702334123, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:432}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.8499363154057176, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:432}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 73.68681319840678,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:432}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 20.632307695553898,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:432}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 14.737362639681356,level: 5}]->(g);
CREATE (n: Building {id: 433, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:433}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.699872630811435, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:433}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.79949052324574,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:433}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.79949052324574,level: 2}]->(g);
CREATE (n: Building {id: 434, name:"building_government_administration", level:13});
MATCH (g: Goods{code: 14}), (b: Building{id:434}) CREATE (g)-[r:Demand{max_demand: 259.99999999999994, current_input: 62.66718975199334, level: 13}]->(b);
CREATE (n: Building {id: 435, name:"building_construction_sector", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:435}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 29.07031647187056, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:435}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 43.84507168877302, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:435}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 35.00494928505728, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:435}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.099617892434305, level: 3}]->(b);
CREATE (n: Building {id: 436, name:"building_university", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:436}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 9.641106115691285, level: 4}]->(b);
CREATE (n: Building {id: 437, name:"building_arms_industry", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:437}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.3336632856704855, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:437}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.5883300742354587, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:437}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 8.882990039858914,level: 1}]->(g);
CREATE (n: Building {id: 438, name:"building_furniture_manufacturies", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:438}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 24.2252637265588, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:438}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 27.403169805483135, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:438}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 26.91247555676594, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:438}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 9.24968157702859, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:438}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 78.93505712211537,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:438}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 49.334410701322106,level: 5}]->(g);
CREATE (n: Building {id: 439, name:"building_glassworks", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:439}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 21.922535844386513, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:439}) CREATE (g)-[r:Demand{max_demand: 15.000000000000002, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:439}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 29.631230221343337, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:439}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 27.061665429024394,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:439}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 27.061665429024394,level: 3}]->(g);
CREATE (n: Building {id: 440, name:"building_paper_mills", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:440}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 43.84507168877302, level: 4}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:440}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 13.36752504017649, level: 4}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:440}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 97.93892127751957,level: 4}]->(g);
CREATE (n: Building {id: 530, name:"building_arms_industry", level:5});
MATCH (g: Goods{code: 24}), (b: Building{id:530}) CREATE (g)-[r:Demand{max_demand: 49.99249824561404, current_input: 11.666565771473563, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:530}) CREATE (g)-[r:Demand{max_demand: 49.99249824561404, current_input: 17.938958494090027, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:530}) CREATE (b)-[r:Supply{max_supply: 149.9774947368421, current_output: 44.408286398345375,level: 5}]->(g);
CREATE (n: Building {id: 531, name:"building_artillery_foundries", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:531}) CREATE (g)-[r:Demand{max_demand: 29.885397849462368, current_input: 6.974245573894601, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:531}) CREATE (g)-[r:Demand{max_demand: 19.923598566308243, current_input: 7.149244792247835, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:531}) CREATE (b)-[r:Supply{max_supply: 49.80899641577061, current_output: 14.748427301888624,level: 2}]->(g);
CREATE (n: Building {id: 532, name:"building_munition_plants", level:2});
MATCH (g: Goods{code: 25}), (b: Building{id:532}) CREATE (g)-[r:Demand{max_demand: 35.41559504132232, current_input: 34.98025500317623, level: 2}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:532}) CREATE (g)-[r:Demand{max_demand: 35.41559504132232, current_input: 30.6292224247156, level: 2}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:532}) CREATE (b)-[r:Supply{max_supply: 88.539, current_output: 82.01185826766816,level: 2}]->(g);
CREATE (n: Building {id: 533, name:"building_chemical_plants", level:2});
MATCH (g: Goods{code: 22}), (b: Building{id:533}) CREATE (g)-[r:Demand{max_demand: 59.99339669421488, current_input: 20.049080813878973, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:533}) CREATE (g)-[r:Demand{max_demand: 19.997793388429752, current_input: 4.666811622500249, level: 2}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:533}) CREATE (b)-[r:Supply{max_supply: 179.98019834710743, current_output: 51.07428165327794,level: 2}]->(g);
CREATE (n: Building {id: 534, name:"building_explosives_factory", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:534}) CREATE (g)-[r:Demand{max_demand: 19.9606, current_input: 6.670595507923671, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:534}) CREATE (g)-[r:Demand{max_demand: 19.9606, current_input: 16.14816029299242, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:534}) CREATE (b)-[r:Supply{max_supply: 49.9015, current_output: 28.52344475114511,level: 1}]->(g);
CREATE (n: Building {id: 535, name:"building_coal_mine", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:535}) CREATE (g)-[r:Demand{max_demand: 19.995594594594593, current_input: 7.398115317734161, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:535}) CREATE (b)-[r:Supply{max_supply: 79.9823963963964, current_output: 29.592467937373822,level: 2}]->(g);
CREATE (n: Building {id: 536, name:"building_iron_mine", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:536}) CREATE (g)-[r:Demand{max_demand: 39.98879611650485, current_input: 15.550132609373547, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:536}) CREATE (g)-[r:Demand{max_demand: 39.98879611650485, current_input: 14.79534522905549, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:536}) CREATE (b)-[r:Supply{max_supply: 159.95519417475728, current_output: 60.69095936059351,level: 4}]->(g);
CREATE (n: Building {id: 537, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:537}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.135026221400473, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:537}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.1099617892434306, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:537}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 44.212087919044066,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:537}) CREATE (b)-[r:Supply{max_supply: 21.0, current_output: 12.37938461733234,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:537}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 8.842417583808814,level: 3}]->(g);
CREATE (n: Building {id: 952, name:"building_textile_mills", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:952}) CREATE (g)-[r:Demand{max_demand: 319.99999999999994, current_input: 155.04168784997628, level: 8}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:952}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 0.0, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:952}) CREATE (b)-[r:Supply{max_supply: 480.0, current_output: 116.28126588748223,level: 8}]->(g);
CREATE (n: Building {id: 953, name:"building_construction_sector", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:953}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 29.07031647187056, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:953}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 43.84507168877302, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:953}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 35.00494928505728, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:953}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.099617892434305, level: 3}]->(b);
CREATE (n: Building {id: 954, name:"building_arms_industry", level:4});
MATCH (g: Goods{code: 24}), (b: Building{id:954}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 9.334653142681942, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:954}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 14.353320296941835, level: 4}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:954}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 35.53196015943566,level: 4}]->(g);
CREATE (n: Building {id: 955, name:"building_coal_mine", level:7});
MATCH (g: Goods{code: 33}), (b: Building{id:955}) CREATE (g)-[r:Demand{max_demand: 70.00000000000001, current_input: 25.89910841568005, level: 7}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:955}) CREATE (b)-[r:Supply{max_supply: 280.00000000000006, current_output: 103.5964336627202,level: 7}]->(g);
CREATE (n: Building {id: 956, name:"building_lead_mine", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:956}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 15.554489376543582, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:956}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 14.79949052324574, level: 4}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:956}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 60.707959799578646,level: 4}]->(g);
CREATE (n: Building {id: 957, name:"building_iron_mine", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:957}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 15.554489376543582, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:957}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 14.79949052324574, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:957}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 60.707959799578646,level: 4}]->(g);
CREATE (n: Building {id: 958, name:"building_livestock_ranch", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:958}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 29.957860078221948, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:958}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 18.49936315405718, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:958}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 48.45722323227912,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:958}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 12.11430580806978,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:958}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 60.571529040348906,level: 5}]->(g);
CREATE (n: Building {id: 964, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:964}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 965, name:"building_rye_farm", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:965}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 12.135026221400473, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:965}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.1099617892434306, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:965}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 44.212087919044066,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:965}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 26.527252751426442,level: 3}]->(g);
CREATE (n: Building {id: 966, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:966}) CREATE (g)-[r:Demand{max_demand: 14.9979, current_input: 0.1810165210619028, level: 3}]->(b);
CREATE (n: Building {id: 967, name:"building_textile_mills", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:967}) CREATE (g)-[r:Demand{max_demand: 79.96479207920791, current_input: 38.74336353916504, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:967}) CREATE (g)-[r:Demand{max_demand: 9.99559405940594, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:967}) CREATE (b)-[r:Supply{max_supply: 119.94719801980197, current_output: 29.057525052914745,level: 2}]->(g);
CREATE (n: Building {id: 968, name:"building_logging_camp", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:968}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.099617892434305, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:968}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 44.39847156973722,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:968}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 44.39847156973722,level: 6}]->(g);
CREATE (n: Building {id: 969, name:"building_rye_farm", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:969}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.09001748093365, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:969}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7399745261622871, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:969}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 29.474725279362712,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:969}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 17.684835167617628,level: 2}]->(g);
CREATE (n: Building {id: 970, name:"building_food_industry", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:970}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 119.83144031288779, level: 5}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:970}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 36.036396555571216, level: 5}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:970}) CREATE (b)-[r:Supply{max_supply: 175.0, current_output: 68.19217862995082,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:970}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 116.90087765134425,level: 5}]->(g);
CREATE (n: Building {id: 971, name:"building_rye_farm", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:971}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.09001748093365, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:971}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7399745261622871, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:971}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 29.474725279362712,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:971}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 11.789890111745084,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:971}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 5.894945055872542,level: 2}]->(g);
CREATE (n: Building {id: 972, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:972}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 17.97471604693317, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:972}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 11.099617892434308, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:972}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 29.074333939367477,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:972}) CREATE (b)-[r:Supply{max_supply: 15.000000000000002, current_output: 7.268583484841869,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:972}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 36.34291742420934,level: 3}]->(g);
CREATE (n: Building {id: 2728, name:"building_subsistence_farms", level:51});
MATCH (g: Goods{code: 7}), (b: Building{id:2728}) CREATE (b)-[r:Supply{max_supply: 101.88066, current_output: 101.88066,level: 51}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2728}) CREATE (b)-[r:Supply{max_supply: 25.47016, current_output: 25.47016,level: 51}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2728}) CREATE (b)-[r:Supply{max_supply: 25.47016, current_output: 25.47016,level: 51}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2728}) CREATE (b)-[r:Supply{max_supply: 25.47016, current_output: 25.47016,level: 51}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2728}) CREATE (b)-[r:Supply{max_supply: 25.47016, current_output: 25.47016,level: 51}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2728}) CREATE (b)-[r:Supply{max_supply: 25.47016, current_output: 25.47016,level: 51}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2728}) CREATE (b)-[r:Supply{max_supply: 25.47016, current_output: 25.47016,level: 51}]->(g);
CREATE (n: Building {id: 3686, name:"building_subsistence_farms", level:85});
MATCH (g: Goods{code: 7}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 165.32840000000002, current_output: 198.39408,level: 85}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 41.332100000000004, current_output: 49.59852,level: 85}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 41.332100000000004, current_output: 49.59852,level: 85}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 41.332100000000004, current_output: 49.59852,level: 85}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 41.332100000000004, current_output: 49.59852,level: 85}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 41.332100000000004, current_output: 49.59852,level: 85}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 41.332100000000004, current_output: 49.59852,level: 85}]->(g);
CREATE (n: Building {id: 3687, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3687}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.36537559740644177, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3687}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.35405850932965227, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.194341067360941,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.7985852668402353,level: 1}]->(g);
CREATE (n: Building {id: 3688, name:"building_subsistence_farms", level:53});
MATCH (g: Goods{code: 7}), (b: Building{id:3688}) CREATE (b)-[r:Supply{max_supply: 105.34598, current_output: 105.34598,level: 53}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3688}) CREATE (b)-[r:Supply{max_supply: 26.33649, current_output: 26.33649,level: 53}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3688}) CREATE (b)-[r:Supply{max_supply: 26.33649, current_output: 26.33649,level: 53}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3688}) CREATE (b)-[r:Supply{max_supply: 26.33649, current_output: 26.33649,level: 53}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3688}) CREATE (b)-[r:Supply{max_supply: 26.33649, current_output: 26.33649,level: 53}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3688}) CREATE (b)-[r:Supply{max_supply: 26.33649, current_output: 26.33649,level: 53}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3688}) CREATE (b)-[r:Supply{max_supply: 26.33649, current_output: 26.33649,level: 53}]->(g);
CREATE (n: Building {id: 3689, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3689}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7307511948128835, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3689}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7081170186593045, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3689}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.388682134721883,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3689}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 3.5971705336804707,level: 2}]->(g);
CREATE (n: Building {id: 3692, name:"building_subsistence_farms", level:64});
MATCH (g: Goods{code: 7}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 124.89343333333333, current_output: 149.87212,level: 64}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 31.223358333333334, current_output: 37.46803,level: 64}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 31.223358333333334, current_output: 37.46803,level: 64}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 31.223358333333334, current_output: 37.46803,level: 64}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 31.223358333333334, current_output: 37.46803,level: 64}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 31.223358333333334, current_output: 37.46803,level: 64}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 31.223358333333334, current_output: 37.46803,level: 64}]->(g);
CREATE (n: Building {id: 3693, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3693}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.36537559740644177, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3693}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.514193414893941, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3693}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.35405850932965227, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3693}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 8.224183477533566,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3693}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 3.2896733910134266,level: 1}]->(g);
CREATE (n: Building {id: 3695, name:"building_subsistence_farms", level:37});
MATCH (g: Goods{code: 7}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 71.14138, current_output: 71.14138,level: 37}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 17.78534, current_output: 17.78534,level: 37}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 17.78534, current_output: 17.78534,level: 37}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 17.78534, current_output: 17.78534,level: 37}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 17.78534, current_output: 17.78534,level: 37}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 17.78534, current_output: 17.78534,level: 37}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 17.78534, current_output: 17.78534,level: 37}]->(g);
CREATE (n: Building {id: 3696, name:"building_subsistence_farms", level:38});
MATCH (g: Goods{code: 7}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 74.68292, current_output: 74.68292,level: 38}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 18.67073, current_output: 18.67073,level: 38}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 18.67073, current_output: 18.67073,level: 38}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 18.67073, current_output: 18.67073,level: 38}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 18.67073, current_output: 18.67073,level: 38}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 18.67073, current_output: 18.67073,level: 38}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 18.67073, current_output: 18.67073,level: 38}]->(g);
CREATE (n: Building {id: 3697, name:"building_subsistence_farms", level:27});
MATCH (g: Goods{code: 7}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 38.58192, current_output: 38.58192,level: 27}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 9.64548, current_output: 9.64548,level: 27}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 9.64548, current_output: 9.64548,level: 27}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 9.64548, current_output: 9.64548,level: 27}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 9.64548, current_output: 9.64548,level: 27}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 9.64548, current_output: 9.64548,level: 27}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 9.64548, current_output: 9.64548,level: 27}]->(g);
CREATE (n: Building {id: 3698, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3698}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.0961267922193254, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3698}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.5425802446818229, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3698}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.0621755279889569, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3698}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 24.6725504326007,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3698}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 9.86902017304028,level: 3}]->(g);
CREATE (n: Building {id: 3700, name:"building_subsistence_farms", level:72});
MATCH (g: Goods{code: 7}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 88.42032, current_output: 88.42032,level: 72}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 22.10508, current_output: 22.10508,level: 72}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 22.10508, current_output: 22.10508,level: 72}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 22.10508, current_output: 22.10508,level: 72}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 22.10508, current_output: 22.10508,level: 72}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 22.10508, current_output: 22.10508,level: 72}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3700}) CREATE (b)-[r:Supply{max_supply: 22.10508, current_output: 22.10508,level: 72}]->(g);
CREATE (n: Building {id: 3701, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3701}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.36537559740644177, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3701}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.514193414893941, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3701}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.35405850932965227, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 8.224183477533566,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 3.2896733910134266,level: 1}]->(g);
CREATE (n: Building {id: 3702, name:"building_subsistence_farms", level:36});
MATCH (g: Goods{code: 7}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 52.22952, current_output: 52.22952,level: 36}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 13.05738, current_output: 13.05738,level: 36}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 13.05738, current_output: 13.05738,level: 36}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 13.05738, current_output: 13.05738,level: 36}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 13.05738, current_output: 13.05738,level: 36}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 13.05738, current_output: 13.05738,level: 36}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 13.05738, current_output: 13.05738,level: 36}]->(g);
CREATE (n: Building {id: 3703, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3703}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.0961267922193254, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3703}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.5425802446818229, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3703}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.0621755279889569, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 24.6725504326007,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 9.86902017304028,level: 3}]->(g);
CREATE (n: Building {id: 3704, name:"building_subsistence_farms", level:100});
MATCH (g: Goods{code: 7}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 198.776, current_output: 198.776,level: 100}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 49.694, current_output: 49.694,level: 100}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 49.694, current_output: 49.694,level: 100}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 49.694, current_output: 49.694,level: 100}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 49.694, current_output: 49.694,level: 100}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 49.694, current_output: 49.694,level: 100}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 49.694, current_output: 49.694,level: 100}]->(g);
CREATE (n: Building {id: 3705, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3705}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.461502389625767, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3705}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.416234037318609, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 28.777364269443765,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.194341067360941,level: 4}]->(g);
CREATE (n: Building {id: 3706, name:"building_subsistence_farms", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:3706}) CREATE (b)-[r:Supply{max_supply: 98.974, current_output: 98.974,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3706}) CREATE (b)-[r:Supply{max_supply: 24.7435, current_output: 24.7435,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3706}) CREATE (b)-[r:Supply{max_supply: 24.7435, current_output: 24.7435,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3706}) CREATE (b)-[r:Supply{max_supply: 24.7435, current_output: 24.7435,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3706}) CREATE (b)-[r:Supply{max_supply: 24.7435, current_output: 24.7435,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3706}) CREATE (b)-[r:Supply{max_supply: 24.7435, current_output: 24.7435,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3706}) CREATE (b)-[r:Supply{max_supply: 24.7435, current_output: 24.7435,level: 50}]->(g);
CREATE (n: Building {id: 3707, name:"building_urban_center", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3707}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 2.5576291818450927, level: 7}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3707}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 3.5993539042575864, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3707}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 2.478409565307566, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 57.56928434273497,level: 7}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 56.0, current_output: 23.027713737093986,level: 7}]->(g);
CREATE (n: Building {id: 3729, name:"building_subsistence_farms", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 2.5, current_output: 2.5,level: 5}]->(g);
CREATE (n: Building {id: 3768, name:"building_barracks", level:14});
MATCH (g: Goods{code: 0}), (b: Building{id:3768}) CREATE (g)-[r:Demand{max_demand: 8.9999, current_input: 9.50985667300159, level: 14}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3768}) CREATE (g)-[r:Demand{max_demand: 22.99962, current_input: 8.39087091813284, level: 14}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3768}) CREATE (g)-[r:Demand{max_demand: 1.9999, current_input: 1.759597392917751, level: 14}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3768}) CREATE (g)-[r:Demand{max_demand: 5.99956, current_input: 3.5946795802179454, level: 14}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3768}) CREATE (g)-[r:Demand{max_demand: 2.99978, current_input: 0.7000476451088609, level: 14}]->(b);
CREATE (n: Building {id: 3769, name:"building_barracks", level:44});
MATCH (g: Goods{code: 0}), (b: Building{id:3769}) CREATE (g)-[r:Demand{max_demand: 26.99972, current_input: 28.529591152254408, level: 44}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3769}) CREATE (g)-[r:Demand{max_demand: 72.99908, current_input: 26.631999025307927, level: 44}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3769}) CREATE (g)-[r:Demand{max_demand: 5.9994, current_input: 5.278528225946675, level: 44}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3769}) CREATE (g)-[r:Demand{max_demand: 23.99892, current_input: 14.379125747768843, level: 44}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3769}) CREATE (g)-[r:Demand{max_demand: 16.9994, current_input: 3.967087565842685, level: 44}]->(b);
CREATE (n: Building {id: 3770, name:"building_barracks", level:8});
MATCH (g: Goods{code: 0}), (b: Building{id:3770}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.283312410694335, level: 8}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3770}) CREATE (g)-[r:Demand{max_demand: 14.0, current_input: 5.107571031776167, level: 8}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3770}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.7596853771866103, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3770}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 2.396628806257756, level: 8}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3770}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.9334653142681941, level: 8}]->(b);
CREATE (n: Building {id: 3771, name:"building_barracks", level:6});
MATCH (g: Goods{code: 0}), (b: Building{id:3771}) CREATE (g)-[r:Demand{max_demand: 3.99996, current_input: 4.226607662056183, level: 6}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3771}) CREATE (g)-[r:Demand{max_demand: 11.99988, current_input: 4.377874248056441, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3771}) CREATE (g)-[r:Demand{max_demand: 3.99996, current_input: 2.3966048399696933, level: 6}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3771}) CREATE (g)-[r:Demand{max_demand: 3.99996, current_input: 0.9334559796150514, level: 6}]->(b);
CREATE (n: Building {id: 3772, name:"building_barracks", level:14});
MATCH (g: Goods{code: 0}), (b: Building{id:3772}) CREATE (g)-[r:Demand{max_demand: 8.9999, current_input: 9.50985667300159, level: 14}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3772}) CREATE (g)-[r:Demand{max_demand: 25.99982, current_input: 9.485423390242472, level: 14}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3772}) CREATE (g)-[r:Demand{max_demand: 1.9999, current_input: 1.759597392917751, level: 14}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3772}) CREATE (g)-[r:Demand{max_demand: 7.99988, current_input: 4.793185713651324, level: 14}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3772}) CREATE (g)-[r:Demand{max_demand: 7.99988, current_input: 1.8669026245769602, level: 14}]->(b);
CREATE (n: Building {id: 3773, name:"building_barracks", level:14});
MATCH (g: Goods{code: 0}), (b: Building{id:3773}) CREATE (g)-[r:Demand{max_demand: 8.9999, current_input: 9.50985667300159, level: 14}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3773}) CREATE (g)-[r:Demand{max_demand: 25.99982, current_input: 9.485423390242472, level: 14}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3773}) CREATE (g)-[r:Demand{max_demand: 1.9999, current_input: 1.759597392917751, level: 14}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3773}) CREATE (g)-[r:Demand{max_demand: 7.99988, current_input: 4.793185713651324, level: 14}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3773}) CREATE (g)-[r:Demand{max_demand: 7.99988, current_input: 1.8669026245769602, level: 14}]->(b);
CREATE (n: Building {id: 3774, name:"building_barracks", level:9});
MATCH (g: Goods{code: 0}), (b: Building{id:3774}) CREATE (g)-[r:Demand{max_demand: 9.0, current_input: 9.509962339249803, level: 9}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3774}) CREATE (g)-[r:Demand{max_demand: 18.0, current_input: 6.566877040855072, level: 9}]->(b);
CREATE (n: Building {id: 3775, name:"building_barracks", level:8});
MATCH (g: Goods{code: 1}), (b: Building{id:3775}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 4.377918027236714, level: 8}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3775}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 3.5193707543732207, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3775}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 7.189886418773268, level: 8}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3775}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 2.800395942804582, level: 8}]->(b);
CREATE (n: Building {id: 3776, name:"building_barracks", level:11});
MATCH (g: Goods{code: 0}), (b: Building{id:3776}) CREATE (g)-[r:Demand{max_demand: 8.99998, current_input: 9.50994120600016, level: 11}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3776}) CREATE (g)-[r:Demand{max_demand: 21.99989, current_input: 8.026142919018728, level: 11}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3776}) CREATE (g)-[r:Demand{max_demand: 3.99993, current_input: 2.396586865253646, level: 11}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3776}) CREATE (g)-[r:Demand{max_demand: 3.99993, current_input: 0.9334489786251944, level: 11}]->(b);
CREATE (n: Building {id: 3777, name:"building_naval_base", level:8});
MATCH (g: Goods{code: 5}), (b: Building{id:3777}) CREATE (g)-[r:Demand{max_demand: 11.34, current_input: 0.0, level: 8}]->(b);
CREATE (n: Building {id: 4143, name:"building_trade_center", level:24});
CREATE (n: Building {id: 4144, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:4144}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7307511948128835, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:4144}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7081170186593045, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:4144}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.388682134721883,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:4144}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 3.5971705336804707,level: 2}]->(g);
CREATE (n: Building {id: 4227, name:"building_trade_center", level:11});
CREATE (n: Building {id: 4430, name:"building_conscription_center", level:6});
CREATE (n: Building {id: 4569, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 4651, name:"building_motor_industry", level:1});
MATCH (g: Goods{code: 30}), (b: Building{id:4651}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.815706244087174, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:4651}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 10.420941658782898,level: 1}]->(g);
CREATE (n: Building {id: 4652, name:"building_conscription_center", level:7});
CREATE (n: Building {id: 4821, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 4937, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 4941, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:4941}) CREATE (g)-[r:Demand{max_demand: 19.984, current_input: 9.682353406231021, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4941}) CREATE (g)-[r:Demand{max_demand: 39.968, current_input: 14.603331877140668, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4941}) CREATE (g)-[r:Demand{max_demand: 49.96, current_input: 11.658981775209746, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4941}) CREATE (g)-[r:Demand{max_demand: 9.992, current_input: 3.6969127327067866, level: 1}]->(b);
CREATE (n: Building {id: 4988, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 5020, name:"building_naval_base", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:5020}) CREATE (g)-[r:Demand{max_demand: 1.214, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 5065, name:"building_naval_base", level:4});
MATCH (g: Goods{code: 5}), (b: Building{id:5065}) CREATE (g)-[r:Demand{max_demand: 1.842, current_input: 0.0, level: 4}]->(b);
CREATE (n: Building {id: 5198, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:5198}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 19.38021098124704, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5198}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 29.23004779251534, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5198}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 23.336632856704853, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5198}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.39974526162287, level: 2}]->(b);
CREATE (n: Building {id: 5258, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:5258}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.9915720156443895, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5258}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.699872630811435, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:5258}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 9.691444646455825,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:5258}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 2.422861161613956,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:5258}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 12.11430580806978,level: 1}]->(g);
CREATE (n: Building {id: 5404, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5404}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.36537559740644177, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:5404}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.514193414893941, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5404}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.35405850932965227, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5404}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 8.224183477533566,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:5404}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 3.2896733910134266,level: 1}]->(g);
CREATE (n: Building {id: 5428, name:"building_chemical_plants", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:5428}) CREATE (g)-[r:Demand{max_demand: 25.05, current_input: 8.371412556410528, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5428}) CREATE (g)-[r:Demand{max_demand: 8.35, current_input: 1.9486088435348552, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:5428}) CREATE (b)-[r:Supply{max_supply: 75.15, current_output: 21.325858630522642,level: 1}]->(g);
CREATE (n: Building {id: 5526, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5526}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 9.69010549062352, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5526}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 14.61502389625767, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5526}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 11.668316428352426, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5526}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.699872630811435, level: 1}]->(b);
CREATE (n: Building {id: 5587, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:5587}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.06034728897442402, level: 1}]->(b);
CREATE (n: Building {id: 5625, name:"building_railway", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:5625}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.777724468827179, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:5625}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.5709670744697046, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:5625}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 9.030556493075304,level: 1}]->(g);
CREATE (n: Building {id: 16783032, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783032}) CREATE (g)-[r:Demand{max_demand: 0.2, current_input: 0.09690105490623521, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783032}) CREATE (g)-[r:Demand{max_demand: 0.4, current_input: 0.14615023896257673, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16783032}) CREATE (g)-[r:Demand{max_demand: 0.2, current_input: 0.07176660148470919, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16783032}) CREATE (g)-[r:Demand{max_demand: 0.05, current_input: 0.02570967074469705, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:16783032}) CREATE (b)-[r:Supply{max_supply: 0.7, current_output: 0.30150877649464336,level: 1}]->(g);
CREATE (n: Building {id: 5838, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5838}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.36537559740644177, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:5838}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.514193414893941, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5838}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.35405850932965227, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5838}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 8.224183477533566,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:5838}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 3.2896733910134266,level: 1}]->(g);
CREATE (n: Building {id: 5845, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5845}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 9.69010549062352, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5845}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 14.61502389625767, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5845}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 11.668316428352426, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5845}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.699872630811435, level: 1}]->(b);
CREATE (n: Building {id: 5847, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5847}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 9.69010549062352, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5847}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 14.61502389625767, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5847}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 11.668316428352426, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5847}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.699872630811435, level: 1}]->(b);
