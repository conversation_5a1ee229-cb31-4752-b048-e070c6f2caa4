CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:14.08431850385802, pop_demand:1371.4091768408105});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:6.929431819134302, pop_demand:31.456697993786086});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:14.018781924804227, pop_demand:175.57126728110597});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:25.544589592261346, pop_demand:103.82114271889402});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:24.403671196475255, pop_demand:277.08557927204276});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:21.645002339472626, pop_demand:231.86538128205183});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:52.5, pop_demand:14.914360702468581});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:16.35015404256686, pop_demand:157.46934916666672});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:68.31688438839201, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:19.815077074849366, pop_demand:7.218211927600159});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:86.24146474275219, pop_demand:79.86311131504213});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:45.263799181526295, pop_demand:46.763627556618175});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:47.91838574711023, pop_demand:89.80406845479978});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:23.58253979569159, pop_demand:395.4383462319206});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:109.51760000000002});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:29.971498288448824, pop_demand:27.395093449441116});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:11.237399108858256, pop_demand:8.319034208854037});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:87.5, pop_demand:67.76626939376432});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:27.849597967410435});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:70.43460549837374});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 2683, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2683}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 2684, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2684}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 41.57989439445729, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2684}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 47.27705203869327, level: 1}]->(b);
CREATE (n: Building {id: 2685, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2685}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 56.732462446431924, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2685}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2685}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 9.455410407738654,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2685}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 23.638526019346635,level: 3}]->(g);
CREATE (n: Building {id: 2686, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2686}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.151803469246218, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2686}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 81.51803469246218,level: 2}]->(g);
CREATE (n: Building {id: 2687, name:"building_rice_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2687}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.9552,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2687}) CREATE (b)-[r:Supply{max_supply: 17.928, current_output: 18.28656,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2687}) CREATE (b)-[r:Supply{max_supply: 26.892, current_output: 27.42984,level: 3}]->(g);
CREATE (n: Building {id: 2688, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:2688}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 76.194,level: 3}]->(g);
CREATE (n: Building {id: 2689, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2690, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:2690}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 2691, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2691}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 33.263915515565834, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2691}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 25.214427753969744, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:2691}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 28.531312142361763,level: 1}]->(g);
CREATE (n: Building {id: 2692, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2692}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.910820815477308, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2692}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2692}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 3.151803469246218,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2692}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 7.879508673115546,level: 1}]->(g);
CREATE (n: Building {id: 2693, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2693}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.151803469246218, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2693}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 81.51803469246218,level: 2}]->(g);
CREATE (n: Building {id: 2694, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2694}) CREATE (b)-[r:Supply{max_supply: 34.86, current_output: 34.86,level: 1}]->(g);
CREATE (n: Building {id: 2695, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2695}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 2696, name:"building_barrackslevel", level:3});
CREATE (n: Building {id: 2697, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2697}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.075901734623109, level: 1}]->(b);
CREATE (n: Building {id: 2698, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:2698}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 2699, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2699}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.910820815477308, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2699}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2699}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 3.151803469246218,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2699}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 7.879508673115546,level: 1}]->(g);
CREATE (n: Building {id: 2700, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2700}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.151803469246218, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2700}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 81.51803469246218,level: 2}]->(g);
CREATE (n: Building {id: 2701, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2701}) CREATE (b)-[r:Supply{max_supply: 34.86, current_output: 34.86,level: 1}]->(g);
CREATE (n: Building {id: 2702, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:2702}) CREATE (b)-[r:Supply{max_supply: 24.9, current_output: 24.9,level: 1}]->(g);
CREATE (n: Building {id: 2703, name:"building_barrackslevel", level:2});
CREATE (n: Building {id: 2704, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:2704}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 2705, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2705}) CREATE (b)-[r:Supply{max_supply: 34.86, current_output: 34.86,level: 1}]->(g);
CREATE (n: Building {id: 2706, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2706}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2706}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 2707, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2707}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 2708, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 2709, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:2709}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 2710, name:"building_glassworkslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2710}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 37.821641630954616, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2710}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2710}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 6.303606938492436,level: 2}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2710}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 15.759017346231092,level: 2}]->(g);
CREATE (n: Building {id: 2711, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:2711}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2711}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 2712, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2712}) CREATE (b)-[r:Supply{max_supply: 69.72, current_output: 70.4172,level: 2}]->(g);
CREATE (n: Building {id: 2713, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2713}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2713}) CREATE (b)-[r:Supply{max_supply: 9.959999999999999, current_output: 10.0596,level: 2}]->(g);
CREATE (n: Building {id: 2714, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 3671, name:"building_subsistence_rice_paddieslevel", level:64});
MATCH (g: Goods{code: 7}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 336.61056, current_output: 336.61056,level: 64}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 45.90144, current_output: 45.90144,level: 64}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 45.90144, current_output: 45.90144,level: 64}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 61.20192, current_output: 61.20192,level: 64}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 61.20192, current_output: 61.20192,level: 64}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 61.20192, current_output: 61.20192,level: 64}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 91.80288, current_output: 91.80288,level: 64}]->(g);
CREATE (n: Building {id: 3672, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3672}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.151803469246218, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3672}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 15.759017346231092,level: 1}]->(g);
CREATE (n: Building {id: 3673, name:"building_subsistence_rice_paddieslevel", level:80});
MATCH (g: Goods{code: 7}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 439.208, current_output: 439.208,level: 80}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 59.892, current_output: 59.892,level: 80}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 59.892, current_output: 59.892,level: 80}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 79.856, current_output: 79.856,level: 80}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 79.856, current_output: 79.856,level: 80}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 79.856, current_output: 79.856,level: 80}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 119.784, current_output: 119.784,level: 80}]->(g);
CREATE (n: Building {id: 3674, name:"building_subsistence_rice_paddieslevel", level:108});
MATCH (g: Goods{code: 7}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 592.812, current_output: 592.812,level: 108}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 80.838, current_output: 80.838,level: 108}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 80.838, current_output: 80.838,level: 108}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 107.784, current_output: 107.784,level: 108}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 107.784, current_output: 107.784,level: 108}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 107.784, current_output: 107.784,level: 108}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 161.676, current_output: 161.676,level: 108}]->(g);
CREATE (n: Building {id: 3675, name:"building_subsistence_rice_paddieslevel", level:36});
MATCH (g: Goods{code: 7}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 197.604, current_output: 197.604,level: 36}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 26.946, current_output: 26.946,level: 36}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 26.946, current_output: 26.946,level: 36}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 35.928, current_output: 35.928,level: 36}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 35.928, current_output: 35.928,level: 36}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 35.928, current_output: 35.928,level: 36}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 53.892, current_output: 53.892,level: 36}]->(g);
CREATE (n: Building {id: 3676, name:"building_subsistence_rice_paddieslevel", level:96});
MATCH (g: Goods{code: 7}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 462.27984, current_output: 462.27984,level: 96}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 63.03816, current_output: 63.03816,level: 96}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 63.03816, current_output: 63.03816,level: 96}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 84.05088, current_output: 84.05088,level: 96}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 84.05088, current_output: 84.05088,level: 96}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 84.05088, current_output: 84.05088,level: 96}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 126.07632, current_output: 126.07632,level: 96}]->(g);
CREATE (n: Building {id: 3677, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3677}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.151803469246218, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 15.759017346231092,level: 1}]->(g);
CREATE (n: Building {id: 4495, name:"building_conscription_centerlevel", level:13});
CREATE (n: Building {id: 4496, name:"building_conscription_centerlevel", level:17});
CREATE (n: Building {id: 4497, name:"building_conscription_centerlevel", level:22});
CREATE (n: Building {id: 4498, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4499, name:"building_conscription_centerlevel", level:17});
