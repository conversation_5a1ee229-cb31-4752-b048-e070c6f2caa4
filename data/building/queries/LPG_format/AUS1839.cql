CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:100.83882713703517, pop_demand:17.154524998398852});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:118.51547248533429, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:101.45045226783853, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:22.33696568336964, pop_demand:3731.2663579171463});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:23.935633028006293, pop_demand:659.5664717670595});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:17.90938000416997, pop_demand:105.24324070227173});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:27.226822986534238, pop_demand:631.4594442136317});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:44.1595304157735, pop_demand:558.3372402591729});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:33.18653200983196, pop_demand:1391.9872748222099});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:32.12600224678727, pop_demand:1134.2121386221381});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:44.649275832613505, pop_demand:44.81594463888053});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:21.421702906322185, pop_demand:734.3224114150086});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:48.54904054379436, pop_demand:372.9620500000001});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:71.33535460149018, pop_demand:11.476715031073466});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:47.348381678873295, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:50.514077775822884, pop_demand:251.00097755606419});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:68.40873775284707, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:63.274559299036795, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:66.15796539510151, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:61.76964587525149, pop_demand:68.50886608114503});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:64.97023501172912, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:111.98738451694061, pop_demand:203.82796272458575});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:40.18078896224525, pop_demand:465.86278088118735});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:47.80191845249058, pop_demand:287.2736880667883});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:37.463034383043194, pop_demand:1952.4728641666702});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:44.69842620171515, pop_demand:246.6510724812334});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:87.5, pop_demand:46.38549529760017});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:50.95793053622214, pop_demand:107.07390407602162});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:82.84356068266527, pop_demand:548.4067284445616});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:86.23613915358642, pop_demand:251.9736858767559});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:283.32368615272554, pop_demand:4.643963872899385});
CREATE (n: Building {id: 352, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:352}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 34.94992919373827, level: 10}]->(b);
CREATE (n: Building {id: 353, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:353}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 46.4816814836901, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:353}) CREATE (g)-[r:Demand{max_demand: 79.9992, current_input: 41.49201922915288, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:353}) CREATE (g)-[r:Demand{max_demand: 99.999, current_input: 5.343298909403064, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:353}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 3.3626867232780926, level: 2}]->(b);
CREATE (n: Building {id: 354, name:"building_university", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:354}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 8.73748229843457, level: 5}]->(b);
CREATE (n: Building {id: 355, name:"building_arts_academy", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:355}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4949929193738276, level: 1}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:355}) CREATE (b)-[r:Supply{max_supply: 4.0, current_output: 1.397997167749531,level: 1}]->(g);
CREATE (n: Building {id: 356, name:"building_furniture_manufacturies", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:356}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 58.102682881441446, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:356}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 38.899157018901015, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:356}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 9.658780818221166, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:356}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 54.91463903872097,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:356}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 68.64329879840122,level: 5}]->(g);
CREATE (n: Building {id: 357, name:"building_paper_mills", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:357}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 77.79831403780203, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:357}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 103.73108538373603,level: 5}]->(g);
CREATE (n: Building {id: 358, name:"building_wheat_farm", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:358}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.6813601752407987, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:358}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 8.406800876203993,level: 10}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:358}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 11.76952122668559,level: 10}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:358}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 8.406800876203993,level: 10}]->(g);
CREATE (n: Building {id: 359, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:359}) CREATE (g)-[r:Demand{max_demand: 13.939196428571428, current_input: 11.768303979625978, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:359}) CREATE (g)-[r:Demand{max_demand: 13.939196428571428, current_input: 2.343680974985877, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:359}) CREATE (b)-[r:Supply{max_supply: 27.878392857142856, current_output: 14.111984954611856,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:359}) CREATE (b)-[r:Supply{max_supply: 55.75679464285714, current_output: 28.223974428848148,level: 3}]->(g);
CREATE (n: Building {id: 360, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:360}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 34.94992919373827, level: 10}]->(b);
CREATE (n: Building {id: 361, name:"building_arms_industry", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:361}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.0686704685852986, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:361}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.5756748848589774, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:361}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 5.466518030166414,level: 2}]->(g);
CREATE (n: Building {id: 362, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:362}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 46.4816814836901, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:362}) CREATE (g)-[r:Demand{max_demand: 79.9992, current_input: 41.49201922915288, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:362}) CREATE (g)-[r:Demand{max_demand: 99.999, current_input: 5.343298909403064, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:362}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 3.3626867232780926, level: 2}]->(b);
CREATE (n: Building {id: 363, name:"building_university", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:363}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4949929193738276, level: 2}]->(b);
CREATE (n: Building {id: 364, name:"building_food_industry", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:364}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 135.08157707575472, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:364}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 11.019259308093707, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:364}) CREATE (b)-[r:Supply{max_supply: 139.99999999999997, current_output: 63.919115917933674,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:364}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 109.5756272878863,level: 4}]->(g);
CREATE (n: Building {id: 365, name:"building_glassworks", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:365}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 31.11932561512081, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:365}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:365}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.763037225508134, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:365}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 14.881800022045692,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:365}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 14.881800022045692,level: 3}]->(g);
CREATE (n: Building {id: 366, name:"building_iron_mine", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:366}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 2.6626985553824487, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:366}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.044080525722396, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:366}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 15.41355816220969,level: 3}]->(g);
CREATE (n: Building {id: 367, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:367}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.522040262861198, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:367}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 30.26448315433438,level: 3}]->(g);
CREATE (n: Building {id: 368, name:"building_rye_farm", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:368}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 1.345088140192639, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:368}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 6.725440700963194,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:368}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 20.176322102889586,level: 8}]->(g);
CREATE (n: Building {id: 369, name:"building_coal_mine", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:369}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.3627203504815975, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:369}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 13.450881401926388,level: 2}]->(g);
CREATE (n: Building {id: 370, name:"building_tooling_workshops", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:370}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 46.678988422681215, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:370}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 3.206011405755896, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:370}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 51.48800553131505,level: 3}]->(g);
CREATE (n: Building {id: 371, name:"building_textile_mills", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:371}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 87.15402432216217, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:371}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 33.97742748169006, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:371}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:371}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 52.651618321126705,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:371}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 52.651618321126705,level: 3}]->(g);
CREATE (n: Building {id: 372, name:"building_food_industry", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:372}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 101.31118280681605, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:372}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 8.26444448107028, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:372}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 47.93933693845026,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:372}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 82.18172046591474,level: 3}]->(g);
CREATE (n: Building {id: 373, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:373}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.522040262861198, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:373}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 10.088161051444793,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:373}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 10.088161051444793,level: 3}]->(g);
CREATE (n: Building {id: 374, name:"building_wheat_farm", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:374}) CREATE (g)-[r:Demand{max_demand: 5.000000000000001, current_input: 0.8406800876203996, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:374}) CREATE (b)-[r:Supply{max_supply: 25.000000000000004, current_output: 4.203400438101998,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:374}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 5.884760613342796,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:374}) CREATE (b)-[r:Supply{max_supply: 25.000000000000004, current_output: 4.203400438101998,level: 5}]->(g);
CREATE (n: Building {id: 375, name:"building_coal_mine", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:375}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.044080525722396, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:375}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 20.176322102889586,level: 3}]->(g);
CREATE (n: Building {id: 376, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:376}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.7751323702549655, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:376}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.3627203504815975, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:376}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 10.275705441473125,level: 2}]->(g);
CREATE (n: Building {id: 377, name:"building_lead_mine", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:377}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.6813601752407987, level: 2}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:377}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 6.725440700963194,level: 2}]->(g);
CREATE (n: Building {id: 378, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:378}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.16813601752407986, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:378}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:378}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 1.176952122668559,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:378}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
CREATE (n: Building {id: 379, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:379}) CREATE (g)-[r:Demand{max_demand: 9.419594594594594, current_input: 7.952585582825577, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:379}) CREATE (g)-[r:Demand{max_demand: 9.419594594594594, current_input: 1.5837731218264848, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:379}) CREATE (b)-[r:Supply{max_supply: 18.839198198198197, current_output: 9.536363264993838,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:379}) CREATE (b)-[r:Supply{max_supply: 37.678396396396394, current_output: 19.072726529987676,level: 2}]->(g);
CREATE (n: Building {id: 380, name:"building_iron_mine", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:380}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8875661851274828, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:380}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.6813601752407987, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:380}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 5.1378527207365625,level: 1}]->(g);
CREATE (n: Building {id: 381, name:"building_gold_mine", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:381}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.7751323702549655, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:381}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.3627203504815975, level: 2}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:381}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 5.1378527207365625,level: 2}]->(g);
CREATE (n: Building {id: 382, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:382}) CREATE (g)-[r:Demand{max_demand: 3.0185999999999997, current_input: 2.5484828035054576, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:382}) CREATE (g)-[r:Demand{max_demand: 3.0185999999999997, current_input: 0.5075353824981874, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:382}) CREATE (b)-[r:Supply{max_supply: 6.0371999999999995, current_output: 3.056018186003645,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:382}) CREATE (b)-[r:Supply{max_supply: 12.074399999999999, current_output: 6.11203637200729,level: 1}]->(g);
CREATE (n: Building {id: 383, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:383}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.16813601752407986, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:383}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:383}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 1.176952122668559,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:383}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
CREATE (n: Building {id: 16777612, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16777612}) CREATE (g)-[r:Demand{max_demand: 19.9838, current_input: 23.22224788332299, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16777612}) CREATE (g)-[r:Demand{max_demand: 39.9676, current_input: 20.72941264091504, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16777612}) CREATE (g)-[r:Demand{max_demand: 49.9595, current_input: 2.6695121137643616, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16777612}) CREATE (g)-[r:Demand{max_demand: 9.9919, current_input: 1.6799982734988537, level: 1}]->(b);
CREATE (n: Building {id: 542, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:542}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.484978758121482, level: 3}]->(b);
CREATE (n: Building {id: 543, name:"building_construction_sector", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:543}) CREATE (g)-[r:Demand{max_demand: 79.9552, current_input: 92.91223260644455, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:543}) CREATE (g)-[r:Demand{max_demand: 159.9104, current_input: 82.93839678073691, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:543}) CREATE (g)-[r:Demand{max_demand: 199.888, current_input: 10.68072013122891, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:543}) CREATE (g)-[r:Demand{max_demand: 39.9776, current_input: 6.721674454170657, level: 4}]->(b);
CREATE (n: Building {id: 544, name:"building_textile_mills", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:544}) CREATE (g)-[r:Demand{max_demand: 104.67625, current_input: 121.63941917936971, level: 5}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:544}) CREATE (g)-[r:Demand{max_demand: 62.80575, current_input: 47.421729245736785, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:544}) CREATE (g)-[r:Demand{max_demand: 20.935249999999996, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:544}) CREATE (b)-[r:Supply{max_supply: 125.6115, current_output: 73.48498616382453,level: 5}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:544}) CREATE (b)-[r:Supply{max_supply: 125.6115, current_output: 73.48498616382453,level: 5}]->(g);
CREATE (n: Building {id: 545, name:"building_arms_industry", level:5});
MATCH (g: Goods{code: 24}), (b: Building{id:545}) CREATE (g)-[r:Demand{max_demand: 49.98, current_input: 2.6706075009946613, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:545}) CREATE (g)-[r:Demand{max_demand: 49.98, current_input: 6.436611537262585, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:545}) CREATE (b)-[r:Supply{max_supply: 149.94, current_output: 13.660828557385871,level: 5}]->(g);
CREATE (n: Building {id: 546, name:"building_artillery_foundries", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:546}) CREATE (g)-[r:Demand{max_demand: 29.98889108910891, current_input: 1.6024121146275756, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:546}) CREATE (g)-[r:Demand{max_demand: 19.99259405940594, current_input: 2.574721120099633, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:546}) CREATE (b)-[r:Supply{max_supply: 49.98149504950495, current_output: 4.553745731046536,level: 2}]->(g);
CREATE (n: Building {id: 547, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:547}) CREATE (g)-[r:Demand{max_demand: 2.999098214285714, current_input: 0.5042564299135794, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:547}) CREATE (b)-[r:Supply{max_supply: 14.995499999999998, current_output: 2.521283650782339,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:547}) CREATE (b)-[r:Supply{max_supply: 20.993696428571425, current_output: 3.529796510609498,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:547}) CREATE (b)-[r:Supply{max_supply: 14.995499999999998, current_output: 2.521283650782339,level: 3}]->(g);
CREATE (n: Building {id: 548, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:548}) CREATE (b)-[r:Supply{max_supply: 19.996, current_output: 21.9956,level: 1}]->(g);
CREATE (n: Building {id: 645, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:645}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.484978758121482, level: 3}]->(b);
CREATE (n: Building {id: 646, name:"building_paper_mills", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:646}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 77.79831403780203, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:646}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 103.73108538373603,level: 5}]->(g);
CREATE (n: Building {id: 647, name:"building_glassworks", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:647}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 77.79831403780203, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:647}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:647}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 12.966385672967004,level: 5}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:647}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 25.932771345934007,level: 5}]->(g);
CREATE (n: Building {id: 648, name:"building_textile_mills", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:648}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 232.41073152576578, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:648}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 90.6064732845068, level: 8}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:648}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 0.0, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:648}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 140.40431552300453,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:648}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 140.40431552300453,level: 8}]->(g);
CREATE (n: Building {id: 649, name:"building_fishing_wharf", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:649}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.970532516983349, level: 4}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:649}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 149.7053251698335,level: 4}]->(g);
CREATE (n: Building {id: 650, name:"building_silk_plantation", level:6});
MATCH (g: Goods{code: 20}), (b: Building{id:650}) CREATE (b)-[r:Supply{max_supply: 119.8512, current_output: 137.82888,level: 6}]->(g);
CREATE (n: Building {id: 651, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:651}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.3362720350481597, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:651}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 1.6813601752407983,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:651}) CREATE (b)-[r:Supply{max_supply: 13.999999999999998, current_output: 2.3539042453371177,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:651}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 1.6813601752407983,level: 2}]->(g);
CREATE (n: Building {id: 652, name:"building_vineyard_plantation", level:2});
MATCH (g: Goods{code: 39}), (b: Building{id:652}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 44.4,level: 2}]->(g);
CREATE (n: Building {id: 653, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:653}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 11.227899387737512, level: 3}]->(b);
CREATE (n: Building {id: 799, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:799}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.5044080525722396, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:799}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 2.522040262861198,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:799}) CREATE (b)-[r:Supply{max_supply: 20.999999999999996, current_output: 3.530856368005677,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:799}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 2.522040262861198,level: 3}]->(g);
CREATE (n: Building {id: 800, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:800}) CREATE (g)-[r:Demand{max_demand: 5.3587938931297705, current_input: 4.524214564424331, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:800}) CREATE (g)-[r:Demand{max_demand: 5.3587938931297705, current_input: 0.9010062639231993, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:800}) CREATE (b)-[r:Supply{max_supply: 10.717595419847328, current_output: 5.42522469245392,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:800}) CREATE (b)-[r:Supply{max_supply: 21.435198473282444, current_output: 10.850453249014231,level: 2}]->(g);
CREATE (n: Building {id: 801, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:801}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.6,level: 2}]->(g);
CREATE (n: Building {id: 802, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:802}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 11.227899387737512, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:802}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 112.27899387737513,level: 3}]->(g);
CREATE (n: Building {id: 803, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:803}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.16813601752407986, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:803}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:803}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 1.176952122668559,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:803}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
CREATE (n: Building {id: 804, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:804}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 805, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:805}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.7426331292458372, level: 1}]->(b);
CREATE (n: Building {id: 806, name:"building_government_administration", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:806}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 13.97997167749531, level: 4}]->(b);
CREATE (n: Building {id: 807, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:807}) CREATE (g)-[r:Demand{max_demand: 1.944, current_input: 0.32685641806681126, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:807}) CREATE (b)-[r:Supply{max_supply: 9.719999999999999, current_output: 1.634282090334056,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:807}) CREATE (b)-[r:Supply{max_supply: 13.608, current_output: 2.287994926467679,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:807}) CREATE (b)-[r:Supply{max_supply: 9.719999999999999, current_output: 1.634282090334056,level: 2}]->(g);
CREATE (n: Building {id: 808, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:808}) CREATE (b)-[r:Supply{max_supply: 19.856, current_output: 24.82,level: 1}]->(g);
CREATE (n: Building {id: 809, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:809}) CREATE (g)-[r:Demand{max_demand: 4.2637, current_input: 3.599670751111846, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:809}) CREATE (g)-[r:Demand{max_demand: 4.2637, current_input: 0.7168815379174194, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:809}) CREATE (b)-[r:Supply{max_supply: 8.5274, current_output: 4.316552289029265,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:809}) CREATE (b)-[r:Supply{max_supply: 17.0548, current_output: 8.63310457805853,level: 1}]->(g);
CREATE (n: Building {id: 810, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:810}) CREATE (g)-[r:Demand{max_demand: 14.991294117647058, current_input: 2.520576490473341, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:810}) CREATE (b)-[r:Supply{max_supply: 179.89559803921566, current_output: 30.24692942442639,level: 3}]->(g);
CREATE (n: Building {id: 811, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:811}) CREATE (g)-[r:Demand{max_demand: 9.996, current_input: 7.482272151988279, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:811}) CREATE (b)-[r:Supply{max_supply: 99.96, current_output: 74.82272151988278,level: 2}]->(g);
CREATE (n: Building {id: 812, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:812}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.7426331292458372, level: 1}]->(b);
CREATE (n: Building {id: 813, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:813}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.989985838747655, level: 2}]->(b);
CREATE (n: Building {id: 814, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:814}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.16813601752407986, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:814}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:814}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 1.176952122668559,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:814}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
CREATE (n: Building {id: 815, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:815}) CREATE (g)-[r:Demand{max_demand: 3.7166454545454544, current_input: 3.1378145589464705, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:815}) CREATE (g)-[r:Demand{max_demand: 3.7166454545454544, current_input: 0.6249019652762463, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:815}) CREATE (b)-[r:Supply{max_supply: 7.433299999999999, current_output: 3.762721126022145,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:815}) CREATE (b)-[r:Supply{max_supply: 14.866599999999998, current_output: 7.52544225204429,level: 1}]->(g);
CREATE (n: Building {id: 816, name:"building_tooling_workshops", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:816}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 46.678988422681215, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:816}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 3.206011405755896, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:816}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 51.48800553131505,level: 3}]->(g);
CREATE (n: Building {id: 817, name:"building_shipyards", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:817}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 69.72321945772974, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:817}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 62.23865123024162, level: 3}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:817}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 79.7294099132307,level: 3}]->(g);
CREATE (n: Building {id: 818, name:"building_military_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:818}) CREATE (g)-[r:Demand{max_demand: 7.196, current_input: 8.362138120297054, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:818}) CREATE (g)-[r:Demand{max_demand: 14.392, current_input: 7.4644889042136455, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:818}) CREATE (g)-[r:Demand{max_demand: 3.598, current_input: 0.19225381729849522, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:818}) CREATE (b)-[r:Supply{max_supply: 12.593, current_output: 6.599105383910557,level: 1}]->(g);
CREATE (n: Building {id: 819, name:"building_fishing_wharf", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:819}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.970532516983349, level: 4}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:819}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 149.7053251698335,level: 4}]->(g);
CREATE (n: Building {id: 820, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:820}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 11.227899387737512, level: 3}]->(b);
CREATE (n: Building {id: 823, name:"building_port", level:2});
CREATE (n: Building {id: 973, name:"building_government_administration", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:973}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 17.47496459686914, level: 5}]->(b);
CREATE (n: Building {id: 974, name:"building_rye_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:974}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.3362720350481597, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:974}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 1.6813601752407983,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:974}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 5.0440805257223955,level: 2}]->(g);
CREATE (n: Building {id: 975, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:975}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.221299283617335, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:975}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8406800876203994, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:975}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 5.061979371237735,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:975}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 10.12395874247547,level: 1}]->(g);
CREATE (n: Building {id: 979, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:979}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.484978758121482, level: 3}]->(b);
CREATE (n: Building {id: 980, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:980}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.3362720350481597, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:980}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 1.6813601752407983,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:980}) CREATE (b)-[r:Supply{max_supply: 13.999999999999998, current_output: 2.3539042453371177,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:980}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 1.6813601752407983,level: 2}]->(g);
CREATE (n: Building {id: 981, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:981}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 8.44259856723467, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:981}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 16.88519713446934,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:981}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 16.88519713446934,level: 2}]->(g);
CREATE (n: Building {id: 982, name:"building_government_administration", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:982}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 17.47496459686914, level: 5}]->(b);
CREATE (n: Building {id: 983, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:983}) CREATE (g)-[r:Demand{max_demand: 19.8158, current_input: 23.027022868841346, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:983}) CREATE (g)-[r:Demand{max_demand: 39.6316, current_input: 20.555144417470366, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:983}) CREATE (g)-[r:Demand{max_demand: 49.5395, current_input: 2.64707003392407, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:983}) CREATE (g)-[r:Demand{max_demand: 9.9079, current_input: 1.6658748480268308, level: 1}]->(b);
CREATE (n: Building {id: 984, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:984}) CREATE (g)-[r:Demand{max_demand: 19.98679207920792, current_input: 1.7739600798678763, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:984}) CREATE (g)-[r:Demand{max_demand: 19.98679207920792, current_input: 3.360499623279843, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:984}) CREATE (b)-[r:Supply{max_supply: 79.94719801980197, current_output: 10.268923221532608,level: 2}]->(g);
CREATE (n: Building {id: 985, name:"building_food_industry", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:985}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 101.31118280681605, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:985}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 8.26444448107028, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:985}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 47.93933693845026,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:985}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 82.18172046591474,level: 3}]->(g);
CREATE (n: Building {id: 986, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:986}) CREATE (g)-[r:Demand{max_demand: 2.9993032786885245, current_input: 0.504290908625604, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:986}) CREATE (b)-[r:Supply{max_supply: 14.99654918032787, current_output: 2.5214600557843325,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:986}) CREATE (b)-[r:Supply{max_supply: 20.99516393442623, current_output: 3.530043251199619,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:986}) CREATE (b)-[r:Supply{max_supply: 14.99654918032787, current_output: 2.5214600557843325,level: 3}]->(g);
CREATE (n: Building {id: 987, name:"building_vineyard_plantation", level:3});
MATCH (g: Goods{code: 39}), (b: Building{id:987}) CREATE (b)-[r:Supply{max_supply: 59.98800000000001, current_output: 73.18536,level: 3}]->(g);
CREATE (n: Building {id: 988, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:988}) CREATE (g)-[r:Demand{max_demand: 13.17044262295082, current_input: 11.119276001837102, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:988}) CREATE (g)-[r:Demand{max_demand: 13.17044262295082, current_input: 2.214425771652347, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:988}) CREATE (b)-[r:Supply{max_supply: 26.340893442622953, current_output: 13.33370592265287,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:988}) CREATE (b)-[r:Supply{max_supply: 52.68179508196721, current_output: 26.667415994469156,level: 3}]->(g);
CREATE (n: Building {id: 989, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:989}) CREATE (g)-[r:Demand{max_demand: 1.2208760330578512, current_input: 0.205273234088944, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:989}) CREATE (b)-[r:Supply{max_supply: 6.104396694214876, current_output: 1.0263689495524477,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:989}) CREATE (b)-[r:Supply{max_supply: 8.546157024793388, current_output: 1.4369168072841996,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:989}) CREATE (b)-[r:Supply{max_supply: 6.104396694214876, current_output: 1.0263689495524477,level: 2}]->(g);
CREATE (n: Building {id: 990, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:990}) CREATE (g)-[r:Demand{max_demand: 8.294099173553718, current_input: 7.002374979914689, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:990}) CREATE (g)-[r:Demand{max_demand: 8.294099173553718, current_input: 1.3945368039910844, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:990}) CREATE (b)-[r:Supply{max_supply: 16.588198347107436, current_output: 8.396911783905773,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:990}) CREATE (b)-[r:Supply{max_supply: 33.17639669421487, current_output: 16.793823567811547,level: 2}]->(g);
CREATE (n: Building {id: 991, name:"building_food_industry", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:991}) CREATE (g)-[r:Demand{max_demand: 79.85439603960396, current_input: 67.41786095913503, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:991}) CREATE (g)-[r:Demand{max_demand: 79.85439603960396, current_input: 5.4996018553225445, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:991}) CREATE (b)-[r:Supply{max_supply: 69.87259405940594, current_output: 31.90138885121352,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:991}) CREATE (b)-[r:Supply{max_supply: 119.78159405940595, current_output: 54.68809711084319,level: 2}]->(g);
CREATE (n: Building {id: 992, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:992}) CREATE (g)-[r:Demand{max_demand: 0.9977666666666667, current_input: 0.1677605137516094, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:992}) CREATE (b)-[r:Supply{max_supply: 4.98885, current_output: 0.8388053710250059,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:992}) CREATE (b)-[r:Supply{max_supply: 6.984383333333333, current_output: 1.1743263985282246,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:992}) CREATE (b)-[r:Supply{max_supply: 4.98885, current_output: 0.8388053710250059,level: 1}]->(g);
CREATE (n: Building {id: 993, name:"building_vineyard_plantation", level:3});
MATCH (g: Goods{code: 39}), (b: Building{id:993}) CREATE (b)-[r:Supply{max_supply: 50.28, current_output: 61.3416,level: 3}]->(g);
CREATE (n: Building {id: 994, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:994}) CREATE (g)-[r:Demand{max_demand: 9.752844262295081, current_input: 8.233934899531532, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:994}) CREATE (g)-[r:Demand{max_demand: 9.752844262295081, current_input: 1.6398043937948676, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:994}) CREATE (b)-[r:Supply{max_supply: 19.505696721311477, current_output: 9.87374344248982,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:994}) CREATE (b)-[r:Supply{max_supply: 39.01139344262295, current_output: 19.74748688497964,level: 3}]->(g);
CREATE (n: Building {id: 995, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:995}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.16813601752407986, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:995}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:995}) CREATE (b)-[r:Supply{max_supply: 7.000000000000001, current_output: 1.176952122668559,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:995}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
CREATE (n: Building {id: 996, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:996}) CREATE (g)-[r:Demand{max_demand: 15.000000000000002, current_input: 12.663897850852008, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:996}) CREATE (b)-[r:Supply{max_supply: 30.000000000000004, current_output: 25.327795701704016,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:996}) CREATE (b)-[r:Supply{max_supply: 30.000000000000004, current_output: 25.327795701704016,level: 3}]->(g);
CREATE (n: Building {id: 997, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:997}) CREATE (g)-[r:Demand{max_demand: 2.973532786885246, current_input: 0.49995796076416377, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:997}) CREATE (b)-[r:Supply{max_supply: 14.867696721311477, current_output: 2.4997953164771314,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:997}) CREATE (b)-[r:Supply{max_supply: 20.81477868852459, current_output: 3.4997139943336144,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:997}) CREATE (b)-[r:Supply{max_supply: 14.867696721311477, current_output: 2.4997953164771314,level: 3}]->(g);
CREATE (n: Building {id: 998, name:"building_livestock_ranch", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:998}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.88519713446934, level: 4}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:998}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 33.77039426893868,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:998}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 33.77039426893868,level: 4}]->(g);
CREATE (n: Building {id: 999, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:999}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.484978758121482, level: 3}]->(b);
CREATE (n: Building {id: 1000, name:"building_furniture_manufacturies", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1000}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 23.24107315257658, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1000}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 15.559662807560406, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1000}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 3.863512327288466, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1000}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.6813601752407987, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1000}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 36.31150377371418,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1000}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 22.694689858571362,level: 2}]->(g);
CREATE (n: Building {id: 1001, name:"building_iron_mine", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:1001}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8875661851274828, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1001}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.6813601752407987, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1001}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 5.1378527207365625,level: 1}]->(g);
CREATE (n: Building {id: 1002, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1002}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.3362720350481597, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1002}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 1.6813601752407983,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1002}) CREATE (b)-[r:Supply{max_supply: 13.999999999999998, current_output: 2.3539042453371177,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1002}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 1.6813601752407983,level: 2}]->(g);
CREATE (n: Building {id: 1003, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1003}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.16813601752407986, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1003}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1003}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 1.176952122668559,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1003}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
CREATE (n: Building {id: 1004, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1004}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.522040262861198, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1004}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 30.26448315433438,level: 3}]->(g);
CREATE (n: Building {id: 1005, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1005}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1005}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 7.7,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1005}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 1006, name:"building_silk_plantation", level:1});
MATCH (g: Goods{code: 20}), (b: Building{id:1006}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 22.0,level: 1}]->(g);
CREATE (n: Building {id: 1007, name:"building_logging_camp", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:1007}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 4.203400438101997, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1007}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 50.44080525722396,level: 5}]->(g);
CREATE (n: Building {id: 1008, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:1008}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.7751323702549655, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1008}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.3627203504815975, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1008}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 10.275705441473125,level: 2}]->(g);
CREATE (n: Building {id: 1009, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1009}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.989985838747655, level: 2}]->(b);
CREATE (n: Building {id: 1010, name:"building_furniture_manufacturies", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1010}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 11.62053657628829, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1010}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 7.779831403780203, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1010}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.931756163644233, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1010}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 10.982927807744193,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1010}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.728659759680243,level: 1}]->(g);
CREATE (n: Building {id: 1011, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1011}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.522040262861198, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1011}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 10.088161051444793,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1011}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 10.088161051444793,level: 3}]->(g);
CREATE (n: Building {id: 1012, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1012}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.16813601752407986, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1012}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1012}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 1.176952122668559,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1012}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.8406800876203993,level: 1}]->(g);
CREATE (n: Building {id: 1013, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1013}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.221299283617335, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1013}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8406800876203994, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1013}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 5.061979371237735,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1013}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 10.12395874247547,level: 1}]->(g);
CREATE (n: Building {id: 1014, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1014}) CREATE (g)-[r:Demand{max_demand: 0.28843846153846153, current_input: 0.04849689422384941, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1014}) CREATE (b)-[r:Supply{max_supply: 1.4422, current_output: 0.242485764473228,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1014}) CREATE (b)-[r:Supply{max_supply: 2.019076923076923, current_output: 0.33947955292092685,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1014}) CREATE (b)-[r:Supply{max_supply: 1.4422, current_output: 0.242485764473228,level: 1}]->(g);
CREATE (n: Building {id: 1015, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:1015}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 26.0,level: 1}]->(g);
CREATE (n: Building {id: 1016, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1016}) CREATE (g)-[r:Demand{max_demand: 7.5126946564885495, current_input: 6.34266651429418, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1016}) CREATE (g)-[r:Demand{max_demand: 7.5126946564885495, current_input: 1.26315456041642, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1016}) CREATE (b)-[r:Supply{max_supply: 15.025396946564886, current_output: 7.60582493881699,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1016}) CREATE (b)-[r:Supply{max_supply: 30.05079389312977, current_output: 15.21164987763398,level: 2}]->(g);
CREATE (n: Building {id: 1017, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1017}) CREATE (g)-[r:Demand{max_demand: 9.98359405940594, current_input: 1.6786017457255769, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1017}) CREATE (b)-[r:Supply{max_supply: 39.93439603960396, current_output: 6.714410312328398,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1017}) CREATE (b)-[r:Supply{max_supply: 39.93439603960396, current_output: 6.714410312328398,level: 2}]->(g);
CREATE (n: Building {id: 2726, name:"building_subsistence_farms", level:60});
MATCH (g: Goods{code: 7}), (b: Building{id:2726}) CREATE (b)-[r:Supply{max_supply: 118.46159999999999, current_output: 130.30776,level: 60}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2726}) CREATE (b)-[r:Supply{max_supply: 29.615399999999998, current_output: 32.57694,level: 60}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2726}) CREATE (b)-[r:Supply{max_supply: 29.615399999999998, current_output: 32.57694,level: 60}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2726}) CREATE (b)-[r:Supply{max_supply: 29.615399999999998, current_output: 32.57694,level: 60}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2726}) CREATE (b)-[r:Supply{max_supply: 29.615399999999998, current_output: 32.57694,level: 60}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2726}) CREATE (b)-[r:Supply{max_supply: 29.615399999999998, current_output: 32.57694,level: 60}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2726}) CREATE (b)-[r:Supply{max_supply: 29.615399999999998, current_output: 32.57694,level: 60}]->(g);
CREATE (n: Building {id: 2727, name:"building_urban_center", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2727}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 3.111932561512081, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2727}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 1.651227334657179, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2727}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 47.6315989616926,level: 6}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2727}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 11.90789974042315,level: 6}]->(g);
CREATE (n: Building {id: 2848, name:"building_subsistence_farms", level:47});
MATCH (g: Goods{code: 7}), (b: Building{id:2848}) CREATE (b)-[r:Supply{max_supply: 93.51683636363636, current_output: 102.86852,level: 47}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2848}) CREATE (b)-[r:Supply{max_supply: 23.37920909090909, current_output: 25.71713,level: 47}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2848}) CREATE (b)-[r:Supply{max_supply: 23.37920909090909, current_output: 25.71713,level: 47}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2848}) CREATE (b)-[r:Supply{max_supply: 23.37920909090909, current_output: 25.71713,level: 47}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2848}) CREATE (b)-[r:Supply{max_supply: 23.37920909090909, current_output: 25.71713,level: 47}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2848}) CREATE (b)-[r:Supply{max_supply: 23.37920909090909, current_output: 25.71713,level: 47}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2848}) CREATE (b)-[r:Supply{max_supply: 23.37920909090909, current_output: 25.71713,level: 47}]->(g);
CREATE (n: Building {id: 2850, name:"building_subsistence_farms", level:48});
MATCH (g: Goods{code: 7}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 95.05823636363635, current_output: 104.56406,level: 48}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 23.764554545454544, current_output: 26.14101,level: 48}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 23.764554545454544, current_output: 26.14101,level: 48}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 23.764554545454544, current_output: 26.14101,level: 48}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 23.764554545454544, current_output: 26.14101,level: 48}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 23.764554545454544, current_output: 26.14101,level: 48}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 23.764554545454544, current_output: 26.14101,level: 48}]->(g);
CREATE (n: Building {id: 2851, name:"building_subsistence_farms", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:2851}) CREATE (b)-[r:Supply{max_supply: 33.516, current_output: 43.5708,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2851}) CREATE (b)-[r:Supply{max_supply: 8.379, current_output: 10.8927,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2851}) CREATE (b)-[r:Supply{max_supply: 8.379, current_output: 10.8927,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2851}) CREATE (b)-[r:Supply{max_supply: 8.379, current_output: 10.8927,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2851}) CREATE (b)-[r:Supply{max_supply: 8.379, current_output: 10.8927,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2851}) CREATE (b)-[r:Supply{max_supply: 8.379, current_output: 10.8927,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2851}) CREATE (b)-[r:Supply{max_supply: 8.379, current_output: 10.8927,level: 20}]->(g);
CREATE (n: Building {id: 3459, name:"building_subsistence_farms", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:3459}) CREATE (b)-[r:Supply{max_supply: 2.0, current_output: 2.5,level: 1}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3459}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.625,level: 1}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3459}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.625,level: 1}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3459}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.625,level: 1}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3459}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.625,level: 1}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3459}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.625,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3459}) CREATE (b)-[r:Supply{max_supply: 0.5, current_output: 0.625,level: 1}]->(g);
CREATE (n: Building {id: 3656, name:"building_subsistence_farms", level:128});
MATCH (g: Goods{code: 7}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 236.33919999999998, current_output: 259.97312,level: 128}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 59.084799999999994, current_output: 64.99328,level: 128}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 59.084799999999994, current_output: 64.99328,level: 128}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 59.084799999999994, current_output: 64.99328,level: 128}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 59.084799999999994, current_output: 64.99328,level: 128}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 59.084799999999994, current_output: 64.99328,level: 128}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 59.084799999999994, current_output: 64.99328,level: 128}]->(g);
CREATE (n: Building {id: 3657, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3657}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5186554269186802, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3657}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.2752045557761965, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3657}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.938599826948767,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3657}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.9846499567371918,level: 1}]->(g);
CREATE (n: Building {id: 3659, name:"building_subsistence_farms", level:141});
MATCH (g: Goods{code: 7}), (b: Building{id:3659}) CREATE (b)-[r:Supply{max_supply: 281.2414181818182, current_output: 309.36556,level: 141}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3659}) CREATE (b)-[r:Supply{max_supply: 70.31035454545454, current_output: 77.34139,level: 141}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3659}) CREATE (b)-[r:Supply{max_supply: 70.31035454545454, current_output: 77.34139,level: 141}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3659}) CREATE (b)-[r:Supply{max_supply: 70.31035454545454, current_output: 77.34139,level: 141}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3659}) CREATE (b)-[r:Supply{max_supply: 70.31035454545454, current_output: 77.34139,level: 141}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3659}) CREATE (b)-[r:Supply{max_supply: 70.31035454545454, current_output: 77.34139,level: 141}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3659}) CREATE (b)-[r:Supply{max_supply: 70.31035454545454, current_output: 77.34139,level: 141}]->(g);
CREATE (n: Building {id: 3660, name:"building_subsistence_farms", level:103});
MATCH (g: Goods{code: 7}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 205.6456727272727, current_output: 226.21024,level: 103}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 51.41141818181818, current_output: 56.55256,level: 103}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 51.41141818181818, current_output: 56.55256,level: 103}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 51.41141818181818, current_output: 56.55256,level: 103}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 51.41141818181818, current_output: 56.55256,level: 103}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 51.41141818181818, current_output: 56.55256,level: 103}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3660}) CREATE (b)-[r:Supply{max_supply: 51.41141818181818, current_output: 56.55256,level: 103}]->(g);
CREATE (n: Building {id: 3661, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3661}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5186554269186802, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3661}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.2752045557761965, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3661}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.938599826948767,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3661}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.9846499567371918,level: 1}]->(g);
CREATE (n: Building {id: 3662, name:"building_subsistence_farms", level:69});
MATCH (g: Goods{code: 7}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 137.91581818181817, current_output: 151.7074,level: 69}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 34.47895454545454, current_output: 37.92685,level: 69}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 34.47895454545454, current_output: 37.92685,level: 69}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 34.47895454545454, current_output: 37.92685,level: 69}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 34.47895454545454, current_output: 37.92685,level: 69}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 34.47895454545454, current_output: 37.92685,level: 69}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3662}) CREATE (b)-[r:Supply{max_supply: 34.47895454545454, current_output: 37.92685,level: 69}]->(g);
CREATE (n: Building {id: 3663, name:"building_subsistence_farms", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3663}) CREATE (b)-[r:Supply{max_supply: 34.17192, current_output: 42.7149,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3663}) CREATE (b)-[r:Supply{max_supply: 8.542976, current_output: 10.67872,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3663}) CREATE (b)-[r:Supply{max_supply: 8.542976, current_output: 10.67872,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3663}) CREATE (b)-[r:Supply{max_supply: 8.542976, current_output: 10.67872,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3663}) CREATE (b)-[r:Supply{max_supply: 8.542976, current_output: 10.67872,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3663}) CREATE (b)-[r:Supply{max_supply: 8.542976, current_output: 10.67872,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3663}) CREATE (b)-[r:Supply{max_supply: 8.542976, current_output: 10.67872,level: 18}]->(g);
CREATE (n: Building {id: 3664, name:"building_subsistence_farms", level:31});
MATCH (g: Goods{code: 7}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 61.952256, current_output: 77.44032,level: 31}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 15.488064, current_output: 19.36008,level: 31}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 15.488064, current_output: 19.36008,level: 31}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 15.488064, current_output: 19.36008,level: 31}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 15.488064, current_output: 19.36008,level: 31}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 15.488064, current_output: 19.36008,level: 31}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3664}) CREATE (b)-[r:Supply{max_supply: 15.488064, current_output: 19.36008,level: 31}]->(g);
CREATE (n: Building {id: 3665, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3665}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5186554269186802, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3665}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.2752045557761965, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3665}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.938599826948767,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3665}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.9846499567371918,level: 1}]->(g);
CREATE (n: Building {id: 3666, name:"building_subsistence_farms", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 35.72567272727272, current_output: 39.29824,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 8.93141818181818, current_output: 9.82456,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 8.93141818181818, current_output: 9.82456,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 8.93141818181818, current_output: 9.82456,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 8.93141818181818, current_output: 9.82456,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 8.93141818181818, current_output: 9.82456,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3666}) CREATE (b)-[r:Supply{max_supply: 8.93141818181818, current_output: 9.82456,level: 18}]->(g);
CREATE (n: Building {id: 3667, name:"building_subsistence_farms", level:76});
MATCH (g: Goods{code: 7}), (b: Building{id:3667}) CREATE (b)-[r:Supply{max_supply: 149.2944, current_output: 179.15328,level: 76}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3667}) CREATE (b)-[r:Supply{max_supply: 37.3236, current_output: 44.78832,level: 76}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3667}) CREATE (b)-[r:Supply{max_supply: 37.3236, current_output: 44.78832,level: 76}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3667}) CREATE (b)-[r:Supply{max_supply: 37.3236, current_output: 44.78832,level: 76}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3667}) CREATE (b)-[r:Supply{max_supply: 37.3236, current_output: 44.78832,level: 76}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3667}) CREATE (b)-[r:Supply{max_supply: 37.3236, current_output: 44.78832,level: 76}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3667}) CREATE (b)-[r:Supply{max_supply: 37.3236, current_output: 44.78832,level: 76}]->(g);
CREATE (n: Building {id: 3668, name:"building_subsistence_farms", level:118});
MATCH (g: Goods{code: 7}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 235.546875, current_output: 282.65625,level: 118}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 58.88671666666667, current_output: 70.66406,level: 118}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 58.88671666666667, current_output: 70.66406,level: 118}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 58.88671666666667, current_output: 70.66406,level: 118}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 58.88671666666667, current_output: 70.66406,level: 118}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 58.88671666666667, current_output: 70.66406,level: 118}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3668}) CREATE (b)-[r:Supply{max_supply: 58.88671666666667, current_output: 70.66406,level: 118}]->(g);
CREATE (n: Building {id: 3669, name:"building_subsistence_farms", level:25});
MATCH (g: Goods{code: 7}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 62.5,level: 25}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 12.5, current_output: 15.625,level: 25}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 12.5, current_output: 15.625,level: 25}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 12.5, current_output: 15.625,level: 25}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 12.5, current_output: 15.625,level: 25}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 12.5, current_output: 15.625,level: 25}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3669}) CREATE (b)-[r:Supply{max_supply: 12.5, current_output: 15.625,level: 25}]->(g);
CREATE (n: Building {id: 3670, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3670}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.0373108538373603, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3670}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.550409111552393, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3670}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 15.877199653897534,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3670}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 3.9692999134743836,level: 2}]->(g);
CREATE (n: Building {id: 3671, name:"building_subsistence_farms", level:105});
MATCH (g: Goods{code: 7}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 209.00879999999998, current_output: 229.90968,level: 105}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 52.252199999999995, current_output: 57.47742,level: 105}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 52.252199999999995, current_output: 57.47742,level: 105}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 52.252199999999995, current_output: 57.47742,level: 105}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 52.252199999999995, current_output: 57.47742,level: 105}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 52.252199999999995, current_output: 57.47742,level: 105}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3671}) CREATE (b)-[r:Supply{max_supply: 52.252199999999995, current_output: 57.47742,level: 105}]->(g);
CREATE (n: Building {id: 3672, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3672}) CREATE (g)-[r:Demand{max_demand: 2.999392156862745, current_input: 1.5556510196141877, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3672}) CREATE (g)-[r:Demand{max_demand: 2.999392156862745, current_input: 0.8254463861280197, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3672}) CREATE (b)-[r:Supply{max_supply: 59.988, current_output: 23.81103632095013,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3672}) CREATE (b)-[r:Supply{max_supply: 14.997, current_output: 5.9527590802375325,level: 3}]->(g);
CREATE (n: Building {id: 3673, name:"building_subsistence_farms", level:187});
MATCH (g: Goods{code: 7}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 372.8331181818181, current_output: 410.11643,level: 187}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 93.20827272727271, current_output: 102.5291,level: 187}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 93.20827272727271, current_output: 102.5291,level: 187}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 93.20827272727271, current_output: 102.5291,level: 187}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 93.20827272727271, current_output: 102.5291,level: 187}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 93.20827272727271, current_output: 102.5291,level: 187}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 93.20827272727271, current_output: 102.5291,level: 187}]->(g);
CREATE (n: Building {id: 3674, name:"building_urban_center", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3674}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 3.111932561512081, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3674}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 1.651227334657179, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 47.6315989616926,level: 6}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 11.90789974042315,level: 6}]->(g);
CREATE (n: Building {id: 3675, name:"building_subsistence_farms", level:75});
MATCH (g: Goods{code: 7}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 147.49949999999998, current_output: 162.24945,level: 75}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 36.874872727272724, current_output: 40.56236,level: 75}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 36.874872727272724, current_output: 40.56236,level: 75}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 36.874872727272724, current_output: 40.56236,level: 75}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 36.874872727272724, current_output: 40.56236,level: 75}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 36.874872727272724, current_output: 40.56236,level: 75}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 36.874872727272724, current_output: 40.56236,level: 75}]->(g);
CREATE (n: Building {id: 3676, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3676}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.0373108538373603, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3676}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.550409111552393, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 15.877199653897534,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 3.9692999134743836,level: 2}]->(g);
CREATE (n: Building {id: 3677, name:"building_subsistence_farms", level:101});
MATCH (g: Goods{code: 7}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 200.4244, current_output: 240.50928,level: 101}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 50.1061, current_output: 60.12732,level: 101}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 50.1061, current_output: 60.12732,level: 101}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 50.1061, current_output: 60.12732,level: 101}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 50.1061, current_output: 60.12732,level: 101}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 50.1061, current_output: 60.12732,level: 101}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 50.1061, current_output: 60.12732,level: 101}]->(g);
CREATE (n: Building {id: 3678, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3678}) CREATE (g)-[r:Demand{max_demand: 1.9995940594059405, current_input: 1.0371003105452448, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3678}) CREATE (g)-[r:Demand{max_demand: 1.9995940594059405, current_input: 0.5502973948515334, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3678}) CREATE (b)-[r:Supply{max_supply: 39.992, current_output: 15.874024213966752,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3678}) CREATE (b)-[r:Supply{max_supply: 9.998, current_output: 3.968506053491688,level: 2}]->(g);
CREATE (n: Building {id: 3679, name:"building_subsistence_farms", level:37});
MATCH (g: Goods{code: 7}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 74.0, current_output: 81.4,level: 37}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 18.5, current_output: 20.35,level: 37}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 18.5, current_output: 20.35,level: 37}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 18.5, current_output: 20.35,level: 37}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 18.5, current_output: 20.35,level: 37}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 18.5, current_output: 20.35,level: 37}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 18.5, current_output: 20.35,level: 37}]->(g);
CREATE (n: Building {id: 3680, name:"building_subsistence_farms", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 57.99999999999999, current_output: 63.8,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 14.499999999999998, current_output: 15.95,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 14.499999999999998, current_output: 15.95,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 14.499999999999998, current_output: 15.95,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 14.499999999999998, current_output: 15.95,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 14.499999999999998, current_output: 15.95,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 14.499999999999998, current_output: 15.95,level: 29}]->(g);
CREATE (n: Building {id: 3681, name:"building_subsistence_farms", level:67});
MATCH (g: Goods{code: 7}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 132.59433636363636, current_output: 145.85377,level: 67}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 33.14858181818182, current_output: 36.46344,level: 67}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 33.14858181818182, current_output: 36.46344,level: 67}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 33.14858181818182, current_output: 36.46344,level: 67}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 33.14858181818182, current_output: 36.46344,level: 67}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 33.14858181818182, current_output: 36.46344,level: 67}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 33.14858181818182, current_output: 36.46344,level: 67}]->(g);
CREATE (n: Building {id: 3682, name:"building_urban_center", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3682}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 3.111932561512081, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3682}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 1.651227334657179, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3682}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 47.6315989616926,level: 6}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3682}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 11.90789974042315,level: 6}]->(g);
CREATE (n: Building {id: 3683, name:"building_subsistence_farms", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 28.0, current_output: 30.8,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 7.7,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 7.7,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 7.7,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 7.7,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 7.7,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 7.7,level: 14}]->(g);
CREATE (n: Building {id: 3684, name:"building_subsistence_farms", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3684}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 52.0,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3684}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 13.0,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3684}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 13.0,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3684}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 13.0,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3684}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 13.0,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3684}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 13.0,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3684}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 13.0,level: 20}]->(g);
CREATE (n: Building {id: 3685, name:"building_subsistence_farms", level:45});
MATCH (g: Goods{code: 7}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 83.59830000000001, current_output: 117.03762,level: 45}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 20.89957142857143, current_output: 29.2594,level: 45}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 20.89957142857143, current_output: 29.2594,level: 45}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 20.89957142857143, current_output: 29.2594,level: 45}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 20.89957142857143, current_output: 29.2594,level: 45}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 20.89957142857143, current_output: 29.2594,level: 45}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 20.89957142857143, current_output: 29.2594,level: 45}]->(g);
CREATE (n: Building {id: 3752, name:"building_subsistence_farms", level:63});
MATCH (g: Goods{code: 7}), (b: Building{id:3752}) CREATE (b)-[r:Supply{max_supply: 125.68625833333334, current_output: 150.82351,level: 63}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3752}) CREATE (b)-[r:Supply{max_supply: 31.421558333333333, current_output: 37.70587,level: 63}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3752}) CREATE (b)-[r:Supply{max_supply: 31.421558333333333, current_output: 37.70587,level: 63}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3752}) CREATE (b)-[r:Supply{max_supply: 31.421558333333333, current_output: 37.70587,level: 63}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3752}) CREATE (b)-[r:Supply{max_supply: 31.421558333333333, current_output: 37.70587,level: 63}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3752}) CREATE (b)-[r:Supply{max_supply: 31.421558333333333, current_output: 37.70587,level: 63}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3752}) CREATE (b)-[r:Supply{max_supply: 31.421558333333333, current_output: 37.70587,level: 63}]->(g);
CREATE (n: Building {id: 3778, name:"building_barracks", level:30});
MATCH (g: Goods{code: 1}), (b: Building{id:3778}) CREATE (g)-[r:Demand{max_demand: 29.9997, current_input: 2.8069078104541196, level: 30}]->(b);
CREATE (n: Building {id: 3779, name:"building_barracks", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:3779}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.8712905865419454, level: 20}]->(b);
CREATE (n: Building {id: 3780, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:3780}) CREATE (g)-[r:Demand{max_demand: 19.964, current_input: 1.8679222634861696, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3780}) CREATE (g)-[r:Demand{max_demand: 19.964, current_input: 16.854803779627296, level: 10}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3780}) CREATE (g)-[r:Demand{max_demand: 19.964, current_input: 1.0667468617418452, level: 10}]->(b);
CREATE (n: Building {id: 3781, name:"building_barracks", level:10});
MATCH (g: Goods{code: 2}), (b: Building{id:3781}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.5358737429273965, level: 10}]->(b);
CREATE (n: Building {id: 3782, name:"building_barracks", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:3782}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.8712905865419454, level: 20}]->(b);
CREATE (n: Building {id: 3783, name:"building_barracks", level:15});
MATCH (g: Goods{code: 1}), (b: Building{id:3783}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.403467939906459, level: 15}]->(b);
CREATE (n: Building {id: 3784, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:3784}) CREATE (g)-[r:Demand{max_demand: 9.73, current_input: 0.9103828703526565, level: 10}]->(b);
CREATE (n: Building {id: 3785, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:3785}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.46782264663548634, level: 5}]->(b);
CREATE (n: Building {id: 3786, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:3786}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9356452932709727, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3786}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.44259856723467, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3786}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.5343352342926493, level: 5}]->(b);
CREATE (n: Building {id: 3787, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:3787}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9356452932709727, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3787}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.44259856723467, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3787}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.5343352342926493, level: 5}]->(b);
CREATE (n: Building {id: 3788, name:"building_barracks", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:3788}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.7679368714636983, level: 5}]->(b);
CREATE (n: Building {id: 3789, name:"building_barracks", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:3789}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.7679368714636983, level: 5}]->(b);
CREATE (n: Building {id: 3790, name:"building_barracks", level:15});
MATCH (g: Goods{code: 1}), (b: Building{id:3790}) CREATE (g)-[r:Demand{max_demand: 14.99985, current_input: 1.4034539052270598, level: 15}]->(b);
CREATE (n: Building {id: 3791, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:3791}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9356452932709727, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3791}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.7679368714636983, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3791}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.44259856723467, level: 10}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3791}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.5343352342926493, level: 10}]->(b);
CREATE (n: Building {id: 3792, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:3792}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9356452932709727, level: 10}]->(b);
CREATE (n: Building {id: 3793, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:3793}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.46782264663548634, level: 5}]->(b);
CREATE (n: Building {id: 3794, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:3794}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9356452932709727, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3794}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.44259856723467, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3794}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.5343352342926493, level: 5}]->(b);
CREATE (n: Building {id: 3795, name:"building_barracks", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:3795}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.7679368714636983, level: 5}]->(b);
CREATE (n: Building {id: 3796, name:"building_naval_base", level:6});
MATCH (g: Goods{code: 5}), (b: Building{id:3796}) CREATE (g)-[r:Demand{max_demand: 9.82496, current_input: 3.9433364711265226, level: 6}]->(b);
CREATE (n: Building {id: 3797, name:"building_naval_base", level:6});
MATCH (g: Goods{code: 5}), (b: Building{id:3797}) CREATE (g)-[r:Demand{max_demand: 5.44997, current_input: 2.1873947036471812, level: 6}]->(b);
CREATE (n: Building {id: 4142, name:"building_trade_center", level:25});
CREATE (n: Building {id: 4225, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4225}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5186554269186802, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:4225}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.2752045557761965, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:4225}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.938599826948767,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:4225}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.9846499567371918,level: 1}]->(g);
CREATE (n: Building {id: 4260, name:"building_conscription_center", level:7});
CREATE (n: Building {id: 4504, name:"building_conscription_center", level:8});
CREATE (n: Building {id: 4608, name:"building_conscription_center", level:6});
CREATE (n: Building {id: 4820, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:4820}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 23.24084074184505, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4820}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 20.74600961457644, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4820}) CREATE (g)-[r:Demand{max_demand: 49.9995, current_input: 2.671649454701532, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4820}) CREATE (g)-[r:Demand{max_demand: 9.9999, current_input: 1.6813433616390463, level: 1}]->(b);
CREATE (n: Building {id: 4837, name:"building_conscription_center", level:15});
CREATE (n: Building {id: 4901, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:4901}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 46.4816814836901, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4901}) CREATE (g)-[r:Demand{max_demand: 79.9992, current_input: 41.49201922915288, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4901}) CREATE (g)-[r:Demand{max_demand: 99.999, current_input: 5.343298909403064, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4901}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 3.3626867232780926, level: 2}]->(b);
CREATE (n: Building {id: 4920, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 4930, name:"building_conscription_center", level:5});
CREATE (n: Building {id: 4983, name:"building_naval_base", level:4});
MATCH (g: Goods{code: 5}), (b: Building{id:4983}) CREATE (g)-[r:Demand{max_demand: 1.184, current_input: 0.4752090982369192, level: 4}]->(b);
CREATE (n: Building {id: 4984, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 4989, name:"building_conscription_center", level:6});
CREATE (n: Building {id: 5012, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 5014, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5014}) CREATE (g)-[r:Demand{max_demand: 12.98787, current_input: 15.092601838307736, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5014}) CREATE (g)-[r:Demand{max_demand: 25.97574, current_input: 13.472458519228637, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5014}) CREATE (g)-[r:Demand{max_demand: 32.46967, current_input: 1.7349688726855008, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5014}) CREATE (g)-[r:Demand{max_demand: 6.49393, current_input: 1.091863528280148, level: 1}]->(b);
CREATE (n: Building {id: 5023, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5023}) CREATE (g)-[r:Demand{max_demand: 19.9998, current_input: 23.24084074184505, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5023}) CREATE (g)-[r:Demand{max_demand: 39.9996, current_input: 20.74600961457644, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5023}) CREATE (g)-[r:Demand{max_demand: 49.9995, current_input: 2.671649454701532, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5023}) CREATE (g)-[r:Demand{max_demand: 9.9999, current_input: 1.6813433616390463, level: 1}]->(b);
CREATE (n: Building {id: 5024, name:"building_conscription_center", level:1});
CREATE (n: Building {id: 5028, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5028}) CREATE (g)-[r:Demand{max_demand: 19.9958, current_input: 23.236192527214538, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5028}) CREATE (g)-[r:Demand{max_demand: 39.9916, current_input: 20.74186037116109, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5028}) CREATE (g)-[r:Demand{max_demand: 49.9895, current_input: 2.671115119467239, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5028}) CREATE (g)-[r:Demand{max_demand: 9.9979, current_input: 1.6810070896039981, level: 1}]->(b);
CREATE (n: Building {id: 5059, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 5063, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 5071, name:"building_conscription_center", level:1});
CREATE (n: Building {id: 5074, name:"building_conscription_center", level:7});
CREATE (n: Building {id: 5079, name:"building_conscription_center", level:1});
CREATE (n: Building {id: 5085, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 5096, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 5123, name:"building_trade_center", level:10});
CREATE (n: Building {id: 5251, name:"building_silk_plantation", level:1});
MATCH (g: Goods{code: 20}), (b: Building{id:5251}) CREATE (b)-[r:Supply{max_supply: 11.092, current_output: 14.4196,level: 1}]->(g);
CREATE (n: Building {id: 5266, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5266}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5186554269186802, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5266}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.2752045557761965, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5266}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.938599826948767,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:5266}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.9846499567371918,level: 1}]->(g);
CREATE (n: Building {id: 5331, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:5331}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8406800876203994, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5331}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 10.088161051444791,level: 1}]->(g);
CREATE (n: Building {id: 5360, name:"building_trade_center", level:2});
CREATE (n: Building {id: 5390, name:"building_arts_academy", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:5390}) CREATE (g)-[r:Demand{max_demand: 4.766, current_input: 1.6657136253735663, level: 1}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:5390}) CREATE (b)-[r:Supply{max_supply: 1.9064, current_output: 0.6662854501494265,level: 1}]->(g);
CREATE (n: Building {id: 5422, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:5422}) CREATE (b)-[r:Supply{max_supply: 9.564, current_output: 10.5204,level: 1}]->(g);
CREATE (n: Building {id: 5475, name:"building_food_industry", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:5475}) CREATE (g)-[r:Demand{max_demand: 38.222, current_input: 32.26930024368436, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:5475}) CREATE (g)-[r:Demand{max_demand: 38.222, current_input: 2.632363307962235, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:5475}) CREATE (b)-[r:Supply{max_supply: 33.44425, current_output: 15.269477803845385,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:5475}) CREATE (b)-[r:Supply{max_supply: 57.333, current_output: 26.176247663734948,level: 1}]->(g);
CREATE (n: Building {id: 5477, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 5547, name:"building_cotton_plantation", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5547}) CREATE (b)-[r:Supply{max_supply: 10.655999999999999, current_output: 16.5168,level: 1}]->(g);
CREATE (n: Building {id: 5677, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5677}) CREATE (g)-[r:Demand{max_demand: 4.95075, current_input: 5.7530371455059255, level: 1}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:5677}) CREATE (g)-[r:Demand{max_demand: 2.97045, current_input: 2.242849988066361, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:5677}) CREATE (g)-[r:Demand{max_demand: 0.99015, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:5677}) CREATE (b)-[r:Supply{max_supply: 5.9409, current_output: 3.4755333253775738,level: 1}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:5677}) CREATE (b)-[r:Supply{max_supply: 5.9409, current_output: 3.4755333253775738,level: 1}]->(g);
CREATE (n: Building {id: 5764, name:"building_trade_center", level:5});
CREATE (n: Building {id: 16782993, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16782993}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.5186554269186802, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:16782993}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.2752045557761965, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:16782993}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.938599826948767,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:16782993}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.9846499567371918,level: 1}]->(g);
CREATE (n: Building {id: 5803, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5803}) CREATE (g)-[r:Demand{max_demand: 5.572, current_input: 6.474962980307835, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5803}) CREATE (g)-[r:Demand{max_demand: 11.144, current_input: 5.779896077581772, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:5803}) CREATE (b)-[r:Supply{max_supply: 9.751, current_output: 7.404204533942025,level: 1}]->(g);
CREATE (n: Building {id: 5879, name:"building_cotton_plantation", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5879}) CREATE (b)-[r:Supply{max_supply: 2.384, current_output: 3.2184,level: 1}]->(g);
