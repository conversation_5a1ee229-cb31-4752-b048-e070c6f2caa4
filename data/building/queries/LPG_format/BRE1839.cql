CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:32.8706988190181, pop_demand:22.362736430915323});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:20, pop_demand:0});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:13.347092338278301, pop_demand:0.8980508928571429});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:21.606973823222773, pop_demand:5.388305357142857});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:37.797033974563945, pop_demand:3.8292915657830973});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:46.64857910479482, pop_demand:6.187343321972815});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:41.858326979176525, pop_demand:3.4021671149973365});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:7.706000143694707, pop_demand:0.32698848200570557});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:28.617666708097694, pop_demand:1.5102416666666667});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:52.5, pop_demand:0.313375});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:0.06441009554166985});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:52.35207605260372, pop_demand:1.4380172495683672});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:52.5, pop_demand:0.6679790513978123});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:48.4579792920135, pop_demand:8.957091666666667});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:0.475685});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:52.5, pop_demand:0.0007933156599128952});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:0.1856459978749779});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:0.37572555732640733});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 389, name:"building_paper_mills", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:389}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 26.786052353554453, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:389}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 35.714736471405935,level: 1}]->(g);
CREATE (n: Building {id: 390, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:390}) CREATE (g)-[r:Demand{max_demand: 4.999, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 3137, name:"building_subsistence_farms", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 6.4364, current_output: 6.4364,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 1.6091, current_output: 1.6091,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 1.6091, current_output: 1.6091,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 1.6091, current_output: 1.6091,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 1.6091, current_output: 1.6091,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 1.6091, current_output: 1.6091,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3137}) CREATE (b)-[r:Supply{max_supply: 1.6091, current_output: 1.6091,level: 4}]->(g);
CREATE (n: Building {id: 3911, name:"building_barracks", level:2});
MATCH (g: Goods{code: 1}), (b: Building{id:3911}) CREATE (g)-[r:Demand{max_demand: 1.875, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3911}) CREATE (g)-[r:Demand{max_demand: 1.25, current_input: 0.1774417650818251, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3911}) CREATE (g)-[r:Demand{max_demand: 1.25, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 4886, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4886}) CREATE (b)-[r:Supply{max_supply: 29.988, current_output: 29.988,level: 1}]->(g);
CREATE (n: Building {id: 4957, name:"building_food_industry", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:4957}) CREATE (g)-[r:Demand{max_demand: 21.695600000000002, current_input: 3.079764446807396, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:4957}) CREATE (g)-[r:Demand{max_demand: 8.13585, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:4957}) CREATE (b)-[r:Supply{max_supply: 35.25535000000001, current_output: 2.5023086130310097,level: 1}]->(g);
CREATE (n: Building {id: 5181, name:"building_artillery_foundries", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:5181}) CREATE (g)-[r:Demand{max_demand: 0.62175, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:5181}) CREATE (g)-[r:Demand{max_demand: 0.41450000000000004, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:5181}) CREATE (b)-[r:Supply{max_supply: 1.0362500000000001, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 5495, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:5495}) CREATE (g)-[r:Demand{max_demand: 0.0333, current_input: 0.0047270486217798205, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5495}) CREATE (g)-[r:Demand{max_demand: 0.0333, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:5495}) CREATE (b)-[r:Supply{max_supply: 0.0666, current_output: 0.0047270486217798205,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:5495}) CREATE (b)-[r:Supply{max_supply: 0.1332, current_output: 0.009454097243559641,level: 1}]->(g);
