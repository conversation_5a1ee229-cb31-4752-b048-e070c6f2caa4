CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:71.31236187483519, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:58.38999952810011, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:73.02643199888723, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:103.17148497538432, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:28.796480504843274, pop_demand:5594.714011195205});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:26.281000085546413, pop_demand:824.1079832039401});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:11.74282881580094, pop_demand:668.8923942431982});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:29.636531761460738, pop_demand:355.8310516623011});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:46.49114529092272, pop_demand:516.0390471857761});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:45.41207153843713, pop_demand:2404.593957480887});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:45.4749659232763, pop_demand:1970.7282053357114});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:47.55546998443675, pop_demand:102.21534680395091});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:26.248527584372766, pop_demand:963.8573351388891});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:75.11421997939253, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:80.44647983203859, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:45.372377151734135, pop_demand:112.10516286465501});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:56.89631759160097, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:59.137660763418374, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:63.22366071958248, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:18.241932741405748, pop_demand:3.69706239875908});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:76.93039793991966, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:65.42043416090723, pop_demand:201.35841038163878});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:45.357434297662, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:59.84288976643004, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:83.11455895661841, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:114.10104894971039, pop_demand:393.04289144382483});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:43.02235815535538, pop_demand:366.82622251067414});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:45.603843467878, pop_demand:249.45779223952286});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:34.06777899820935, pop_demand:903.0562054981743});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:83.8745814636903, pop_demand:427.84854725797095});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:87.5, pop_demand:408.9569162420293});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:40.14339443782505, pop_demand:189.6366919372227});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:39.33767879593868, pop_demand:1217.587356501364});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:96.38491403414068});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:98.05627568013706, pop_demand:770.4815538647315});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:350.0, pop_demand:0.11345572916666669});
CREATE (n: Building {id: 16777670, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16777670}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.872360897641129, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16777670}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:16777670}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8230839258419729, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:16777670}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.57147590233502,level: 1}]->(g);
CREATE (n: Building {id: 33554892, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:33554892}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.138185752480351, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33554892}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.442094499461776, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33554892}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 21.160560503884255,level: 1}]->(g);
CREATE (n: Building {id: 1386, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:1386}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 14.019882087044646, level: 6}]->(b);
CREATE (n: Building {id: 1387, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:1387}) CREATE (g)-[r:Demand{max_demand: 4.935999999999999, current_input: 1.6935390726778776, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1387}) CREATE (b)-[r:Supply{max_supply: 49.35999999999999, current_output: 16.935390726778778,level: 1}]->(g);
CREATE (n: Building {id: 1388, name:"building_white_houselevel", level:1});
CREATE (n: Building {id: 1389, name:"building_capitol_hilllevel", level:1});
CREATE (n: Building {id: 1390, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1390}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.729857076921882, level: 2}]->(b);
CREATE (n: Building {id: 1391, name:"building_sugar_plantationlevel", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:1391}) CREATE (b)-[r:Supply{max_supply: 79.58789215686274, current_output: 81.17965,level: 3}]->(g);
CREATE (n: Building {id: 1392, name:"building_cotton_plantationlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1392}) CREATE (b)-[r:Supply{max_supply: 70.75279365079365, current_output: 89.14852,level: 2}]->(g);
CREATE (n: Building {id: 1393, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:1393}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 14.019882087044646, level: 6}]->(b);
CREATE (n: Building {id: 1394, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1394}) CREATE (g)-[r:Demand{max_demand: 35.0508, current_input: 78.28678536964232, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1394}) CREATE (g)-[r:Demand{max_demand: 70.1016, current_input: 26.251098940415876, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1394}) CREATE (g)-[r:Demand{max_demand: 87.627, current_input: 41.38910535039913, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1394}) CREATE (g)-[r:Demand{max_demand: 17.5254, current_input: 6.358325529391161, level: 2}]->(b);
CREATE (n: Building {id: 1395, name:"building_arms_industrylevel", level:4});
MATCH (g: Goods{code: 24}), (b: Building{id:1395}) CREATE (g)-[r:Demand{max_demand: 75.42, current_input: 35.623338988292446, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1395}) CREATE (g)-[r:Demand{max_demand: 37.71, current_input: 9.209055871626378, level: 4}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1395}) CREATE (b)-[r:Supply{max_supply: 56.565000000000005, current_output: 20.26554402432945,level: 4}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:1395}) CREATE (b)-[r:Supply{max_supply: 56.565000000000005, current_output: 20.26554402432945,level: 4}]->(g);
CREATE (n: Building {id: 1396, name:"building_munition_plantslevel", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:1396}) CREATE (g)-[r:Demand{max_demand: 17.7646, current_input: 6.955582912446616, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:1396}) CREATE (g)-[r:Demand{max_demand: 17.7646, current_input: 2.258156201288485, level: 1}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:1396}) CREATE (b)-[r:Supply{max_supply: 44.4115, current_output: 11.517173892168874,level: 1}]->(g);
CREATE (n: Building {id: 1397, name:"building_universitylevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:1397}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 5.841617536268603, level: 5}]->(b);
CREATE (n: Building {id: 1398, name:"building_iron_minelevel", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:1398}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 25.690928762401754, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1398}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 27.210472497308878, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1398}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 105.80280251942126,level: 5}]->(g);
CREATE (n: Building {id: 1399, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1399}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 22.468330771693545, level: 2}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:1399}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 4.067997075369039, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1399}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 40.45104233076744,level: 2}]->(g);
CREATE (n: Building {id: 1400, name:"building_textile_millslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:1400}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 670.0570489373337, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1400}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1400}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 9.070157499102962, level: 5}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1400}) CREATE (b)-[r:Supply{max_supply: 500.00000000000006, current_output: 227.13438332735308,level: 5}]->(g);
CREATE (n: Building {id: 1401, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1401}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 44.670469929155566, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1401}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 29.95777436225806, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1401}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 4.884145251459229, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1401}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 7.256125999282366, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1401}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 89.16685839293875,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1401}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 19.814857420653052,level: 2}]->(g);
CREATE (n: Building {id: 1402, name:"building_tooling_workshopslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:1402}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 56.17082692923387, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1402}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 17.12728584160117, level: 5}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:1402}) CREATE (g)-[r:Demand{max_demand: 100.00000000000001, current_input: 30.617746475622155, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1402}) CREATE (b)-[r:Supply{max_supply: 400.00000000000006, current_output: 136.42604814886278,level: 5}]->(g);
CREATE (n: Building {id: 1403, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1403}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 3.628062999641183, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1403}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 29.024503997129468,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1403}) CREATE (b)-[r:Supply{max_supply: 24.999999999999996, current_output: 9.070157499102958,level: 2}]->(g);
CREATE (n: Building {id: 1404, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:1404}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 6.861989759634839, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1404}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.4512251998564736, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1404}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 49.413405156210224,level: 4}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1404}) CREATE (b)-[r:Supply{max_supply: 32.0, current_output: 11.294492607133765,level: 4}]->(g);
CREATE (n: Building {id: 1405, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1405}) CREATE (g)-[r:Demand{max_demand: 9.998, current_input: 6.728511105506498, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1405}) CREATE (b)-[r:Supply{max_supply: 99.97999999999999, current_output: 67.28511105506497,level: 2}]->(g);
CREATE (n: Building {id: 1406, name:"building_naval_baselevel", level:19});
MATCH (g: Goods{code: 5}), (b: Building{id:1406}) CREATE (g)-[r:Demand{max_demand: 38.0, current_input: 14.176745465093285, level: 19}]->(b);
CREATE (n: Building {id: 1407, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:1407}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.354071761794985, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1407}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.636083924469001, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1407}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.121101742913077, level: 5}]->(b);
CREATE (n: Building {id: 1408, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1408}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.094785615382824, level: 3}]->(b);
CREATE (n: Building {id: 1409, name:"building_central_parklevel", level:1});
CREATE (n: Building {id: 1410, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1410}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.673294029014882, level: 2}]->(b);
CREATE (n: Building {id: 1411, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1411}) CREATE (g)-[r:Demand{max_demand: 17.5254, current_input: 39.14339268482116, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1411}) CREATE (g)-[r:Demand{max_demand: 35.0508, current_input: 13.125549470207938, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1411}) CREATE (g)-[r:Demand{max_demand: 43.8135, current_input: 20.694552675199564, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1411}) CREATE (g)-[r:Demand{max_demand: 8.7627, current_input: 3.1791627646955805, level: 1}]->(b);
CREATE (n: Building {id: 1412, name:"building_arms_industrylevel", level:3});
MATCH (g: Goods{code: 24}), (b: Building{id:1412}) CREATE (g)-[r:Demand{max_demand: 21.883794871794873, current_input: 10.336433877860044, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1412}) CREATE (g)-[r:Demand{max_demand: 21.883794871794873, current_input: 5.344181640349238, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:1412}) CREATE (g)-[r:Demand{max_demand: 21.883794871794873, current_input: 6.700324833091355, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1412}) CREATE (g)-[r:Demand{max_demand: 10.941897435897436, current_input: 3.969789323304823, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1412}) CREATE (b)-[r:Supply{max_supply: 54.709495726495724, current_output: 18.9503273342149,level: 3}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:1412}) CREATE (b)-[r:Supply{max_supply: 32.82569230769231, current_output: 11.370194624216355,level: 3}]->(g);
CREATE (n: Building {id: 1413, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:1413}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.276371504960702, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1413}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.884188998923552, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1413}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 42.32112100776851,level: 2}]->(g);
CREATE (n: Building {id: 1414, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:1414}) CREATE (g)-[r:Demand{max_demand: 24.681, current_input: 8.468038462877372, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1414}) CREATE (b)-[r:Supply{max_supply: 246.81000000000003, current_output: 84.68038462877372,level: 5}]->(g);
CREATE (n: Building {id: 1415, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1415}) CREATE (g)-[r:Demand{max_demand: 16.884198198198195, current_input: 7.223680492991476, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1415}) CREATE (g)-[r:Demand{max_demand: 16.884198198198195, current_input: 6.125693476149121, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1415}) CREATE (b)-[r:Supply{max_supply: 67.53679279279278, current_output: 26.698747938281194,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1415}) CREATE (b)-[r:Supply{max_supply: 8.442099099099098, current_output: 3.3373434922851493,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1415}) CREATE (b)-[r:Supply{max_supply: 42.21049549549549, current_output: 16.686717461425747,level: 2}]->(g);
CREATE (n: Building {id: 1416, name:"building_tobacco_plantationlevel", level:4});
MATCH (g: Goods{code: 43}), (b: Building{id:1416}) CREATE (b)-[r:Supply{max_supply: 88.47, current_output: 91.1241,level: 4}]->(g);
CREATE (n: Building {id: 1417, name:"building_cotton_plantationlevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:1417}) CREATE (b)-[r:Supply{max_supply: 353.88, current_output: 474.1992,level: 10}]->(g);
CREATE (n: Building {id: 1418, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:1418}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.47081435235899705, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1418}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1272167848938002, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1418}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.0242203485826153, level: 1}]->(b);
CREATE (n: Building {id: 1419, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1419}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.729857076921882, level: 2}]->(b);
CREATE (n: Building {id: 1423, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1423}) CREATE (b)-[r:Supply{max_supply: 35.3724, current_output: 44.2155,level: 1}]->(g);
CREATE (n: Building {id: 1424, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1424}) CREATE (g)-[r:Demand{max_demand: 9.2549, current_input: 3.9595863427924707, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1424}) CREATE (g)-[r:Demand{max_demand: 9.2549, current_input: 3.357736025537919, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1424}) CREATE (b)-[r:Supply{max_supply: 37.0196, current_output: 14.63464473666078,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1424}) CREATE (b)-[r:Supply{max_supply: 4.6274454545454535, current_output: 1.8293287951665735,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1424}) CREATE (b)-[r:Supply{max_supply: 23.137245454545454, current_output: 9.146651163496964,level: 1}]->(g);
CREATE (n: Building {id: 1425, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1425}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.336647014507441, level: 1}]->(b);
CREATE (n: Building {id: 1426, name:"building_lead_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:1426}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.276371504960702, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1426}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.884188998923552, level: 2}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1426}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 42.32112100776851,level: 2}]->(g);
CREATE (n: Building {id: 1427, name:"building_maize_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1427}) CREATE (g)-[r:Demand{max_demand: 9.876, current_input: 3.3884505433076826, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1427}) CREATE (b)-[r:Supply{max_supply: 98.75999999999999, current_output: 33.884505433076825,level: 2}]->(g);
CREATE (n: Building {id: 1428, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1428}) CREATE (g)-[r:Demand{max_demand: 8.433300000000001, current_input: 3.6080756685292923, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1428}) CREATE (g)-[r:Demand{max_demand: 8.433300000000001, current_input: 3.0596543694874003, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1428}) CREATE (b)-[r:Supply{max_supply: 33.733200000000004, current_output: 13.335460076033387,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1428}) CREATE (b)-[r:Supply{max_supply: 4.216645454545454, current_output: 1.6669307125881492,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1428}) CREATE (b)-[r:Supply{max_supply: 21.08324545454545, current_output: 8.33466075060484,level: 1}]->(g);
CREATE (n: Building {id: 1429, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1429}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.673294029014882, level: 2}]->(b);
CREATE (n: Building {id: 1430, name:"building_chemical_plantslevel", level:2});
MATCH (g: Goods{code: 22}), (b: Building{id:1430}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 12.20399122610712, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1430}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 9.44665579111441, level: 2}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:1430}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 60.81593789917554,level: 2}]->(g);
CREATE (n: Building {id: 1431, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1431}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.442094499461776, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1431}) CREATE (b)-[r:Supply{max_supply: 179.99999999999997, current_output: 65.3051339935413,level: 3}]->(g);
CREATE (n: Building {id: 1432, name:"building_maize_farmlevel", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:1432}) CREATE (g)-[r:Demand{max_demand: 39.438, current_input: 13.531157607023937, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1432}) CREATE (b)-[r:Supply{max_supply: 394.38, current_output: 135.31157607023937,level: 8}]->(g);
CREATE (n: Building {id: 1433, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1433}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.814031499820592, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1433}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 21.7683779978471,level: 1}]->(g);
CREATE (n: Building {id: 1434, name:"building_sulfur_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:1434}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.850914336640467, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1434}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.256125999282368, level: 2}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:1434}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 28.21408067184567,level: 2}]->(g);
CREATE (n: Building {id: 1435, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1435}) CREATE (g)-[r:Demand{max_demand: 8.450899999999999, current_input: 3.6156055953392134, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1435}) CREATE (b)-[r:Supply{max_supply: 33.803599999999996, current_output: 14.462422381356854,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1435}) CREATE (b)-[r:Supply{max_supply: 4.225445454545454, current_output: 1.8078008529571044,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1435}) CREATE (b)-[r:Supply{max_supply: 4.225445454545454, current_output: 1.8078008529571044,level: 1}]->(g);
CREATE (n: Building {id: 1436, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1436}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.336647014507441, level: 1}]->(b);
CREATE (n: Building {id: 1437, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1437}) CREATE (g)-[r:Demand{max_demand: 17.5254, current_input: 39.14339268482116, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1437}) CREATE (g)-[r:Demand{max_demand: 35.0508, current_input: 13.125549470207938, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1437}) CREATE (g)-[r:Demand{max_demand: 43.8135, current_input: 20.694552675199564, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1437}) CREATE (g)-[r:Demand{max_demand: 8.7627, current_input: 3.1791627646955805, level: 1}]->(b);
CREATE (n: Building {id: 1438, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1438}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 22.468330771693548, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1438}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1438}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.746253074845391, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1438}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 15.320278973794776,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1438}) CREATE (b)-[r:Supply{max_supply: 74.99999999999999, current_output: 19.150348717243467,level: 3}]->(g);
CREATE (n: Building {id: 1439, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1439}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 44.670469929155566, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1439}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 22.468330771693545, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1439}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 9.768290502918457, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1439}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 14.512251998564732, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1439}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 89.16685839293875,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1439}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 39.629714841306104,level: 2}]->(g);
CREATE (n: Building {id: 1440, name:"building_chemical_plantslevel", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:1440}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 6.10199561305356, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1440}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 5.13818575248035, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1440}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.723327895557206, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:1440}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 25.456959003904903,level: 1}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:1440}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 6.788522401041307,level: 1}]->(g);
CREATE (n: Building {id: 1441, name:"building_coal_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1441}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 16.32628349838533, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1441}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 65.30513399354132,level: 3}]->(g);
CREATE (n: Building {id: 1442, name:"building_maize_farmlevel", level:6});
MATCH (g: Goods{code: 32}), (b: Building{id:1442}) CREATE (g)-[r:Demand{max_demand: 29.2116, current_input: 10.022485003127452, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1442}) CREATE (b)-[r:Supply{max_supply: 222.00815652173915, current_output: 76.17088483037911,level: 6}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1442}) CREATE (b)-[r:Supply{max_supply: 35.05391304347826, current_output: 12.026979616973895,level: 6}]->(g);
CREATE (n: Building {id: 1443, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1443}) CREATE (g)-[r:Demand{max_demand: 16.860594594594595, current_input: 7.213582003924118, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1443}) CREATE (g)-[r:Demand{max_demand: 16.860594594594595, current_input: 6.11712994005988, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1443}) CREATE (b)-[r:Supply{max_supply: 67.44239639639639, current_output: 26.661431010878356,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1443}) CREATE (b)-[r:Supply{max_supply: 8.430297297297297, current_output: 3.3326779859959994,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1443}) CREATE (b)-[r:Supply{max_supply: 42.15149549549549, current_output: 16.663393491435176,level: 2}]->(g);
CREATE (n: Building {id: 1444, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:1444}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.412443057076991, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1444}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.3816503546814003, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1444}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.0726610457478465, level: 3}]->(b);
CREATE (n: Building {id: 1445, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1445}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.336647014507441, level: 1}]->(b);
CREATE (n: Building {id: 1446, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1446}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1683235072537206, level: 1}]->(b);
CREATE (n: Building {id: 1447, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:1447}) CREATE (g)-[r:Demand{max_demand: 160.00000000000003, current_input: 68.45388009020037, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1447}) CREATE (g)-[r:Demand{max_demand: 160.00000000000003, current_input: 88.86298464294981, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1447}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 68.8261283207532,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1447}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 117.98764854986261,level: 4}]->(g);
CREATE (n: Building {id: 1448, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1448}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 10.88418899892355, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1448}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 43.5367559956942,level: 2}]->(g);
CREATE (n: Building {id: 1449, name:"building_tobacco_plantationlevel", level:9});
MATCH (g: Goods{code: 43}), (b: Building{id:1449}) CREATE (b)-[r:Supply{max_supply: 198.96974999999998, current_output: 214.88733,level: 9}]->(g);
CREATE (n: Building {id: 1450, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:1450}) CREATE (g)-[r:Demand{max_demand: 24.347500000000004, current_input: 8.353614783635463, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1450}) CREATE (b)-[r:Supply{max_supply: 185.04100000000003, current_output: 63.48747235562951,level: 5}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1450}) CREATE (b)-[r:Supply{max_supply: 29.217000000000006, current_output: 10.024337740362554,level: 5}]->(g);
CREATE (n: Building {id: 1451, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1451}) CREATE (g)-[r:Demand{max_demand: 8.252899999999999, current_input: 3.5308939187275903, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1451}) CREATE (g)-[r:Demand{max_demand: 8.252899999999999, current_input: 2.994204112973872, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1451}) CREATE (b)-[r:Supply{max_supply: 33.011599999999994, current_output: 13.050196063402923,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1451}) CREATE (b)-[r:Supply{max_supply: 4.126445454545454, current_output: 1.6312727110093417,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1451}) CREATE (b)-[r:Supply{max_supply: 20.632245454545455, current_output: 8.156370742710804,level: 1}]->(g);
CREATE (n: Building {id: 1452, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:1452}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.354071761794985, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1452}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.636083924469001, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1452}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.121101742913077, level: 5}]->(b);
CREATE (n: Building {id: 1453, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1453}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.336647014507441, level: 1}]->(b);
CREATE (n: Building {id: 1454, name:"building_food_industrylevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1454}) CREATE (g)-[r:Demand{max_demand: 108.4355982142857, current_input: 46.39273398543661, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1454}) CREATE (g)-[r:Demand{max_demand: 27.108892857142855, current_input: 4.462578791617312, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1454}) CREATE (g)-[r:Demand{max_demand: 108.4355982142857, current_input: 60.22444311790716, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1454}) CREATE (b)-[r:Supply{max_supply: 67.77225, current_output: 25.93072890185798,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1454}) CREATE (b)-[r:Supply{max_supply: 271.089, current_output: 103.72291560743191,level: 3}]->(g);
CREATE (n: Building {id: 1455, name:"building_maize_farmlevel", level:10});
MATCH (g: Goods{code: 32}), (b: Building{id:1455}) CREATE (g)-[r:Demand{max_demand: 49.299, current_input: 16.914461658011895, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1455}) CREATE (b)-[r:Supply{max_supply: 295.79400000000004, current_output: 101.48676994807138,level: 10}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1455}) CREATE (b)-[r:Supply{max_supply: 88.73819327731093, current_output: 30.446028677870235,level: 10}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1455}) CREATE (b)-[r:Supply{max_supply: 59.15879831932774, current_output: 20.29735341297648,level: 10}]->(g);
CREATE (n: Building {id: 1456, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1456}) CREATE (g)-[r:Demand{max_demand: 16.860594594594595, current_input: 7.213582003924118, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1456}) CREATE (g)-[r:Demand{max_demand: 16.860594594594595, current_input: 6.11712994005988, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1456}) CREATE (b)-[r:Supply{max_supply: 67.44239639639639, current_output: 26.661431010878356,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1456}) CREATE (b)-[r:Supply{max_supply: 8.430297297297297, current_output: 3.3326779859959994,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1456}) CREATE (b)-[r:Supply{max_supply: 42.15149549549549, current_output: 16.663393491435176,level: 2}]->(g);
CREATE (n: Building {id: 1457, name:"building_tobacco_plantationlevel", level:10});
MATCH (g: Goods{code: 43}), (b: Building{id:1457}) CREATE (b)-[r:Supply{max_supply: 221.08249541284403, current_output: 240.97992,level: 10}]->(g);
CREATE (n: Building {id: 1458, name:"building_cotton_plantationlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:1458}) CREATE (b)-[r:Supply{max_supply: 177.07399999999998, current_output: 228.42546,level: 5}]->(g);
CREATE (n: Building {id: 1459, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:1459}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.412443057076991, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1459}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.3816503546814003, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1459}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.0726610457478465, level: 3}]->(b);
CREATE (n: Building {id: 1460, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1460}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.673294029014882, level: 2}]->(b);
CREATE (n: Building {id: 1461, name:"building_cotton_plantationlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:1461}) CREATE (b)-[r:Supply{max_supply: 140.156, current_output: 180.80124,level: 5}]->(g);
CREATE (n: Building {id: 1462, name:"building_maize_farmlevel", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:1462}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 6.861989759634839, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1462}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.4512251998564736, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1462}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 70.59057879458605,level: 4}]->(g);
CREATE (n: Building {id: 1463, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:1463}) CREATE (b)-[r:Supply{max_supply: 66.32324509803921, current_output: 67.64971,level: 3}]->(g);
CREATE (n: Building {id: 1464, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1464}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.336647014507441, level: 1}]->(b);
CREATE (n: Building {id: 1465, name:"building_maize_farmlevel", level:6});
MATCH (g: Goods{code: 32}), (b: Building{id:1465}) CREATE (g)-[r:Demand{max_demand: 29.5788, current_input: 10.148471135114347, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1465}) CREATE (b)-[r:Supply{max_supply: 177.4728, current_output: 60.890826810686086,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1465}) CREATE (b)-[r:Supply{max_supply: 53.2418347826087, current_output: 18.26724625312154,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1465}) CREATE (b)-[r:Supply{max_supply: 35.494556521739135, current_output: 12.178164168747694,level: 6}]->(g);
CREATE (n: Building {id: 1466, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1466}) CREATE (b)-[r:Supply{max_supply: 53.05859405940594, current_output: 53.58918,level: 2}]->(g);
CREATE (n: Building {id: 1467, name:"building_cotton_plantationlevel", level:12});
MATCH (g: Goods{code: 9}), (b: Building{id:1467}) CREATE (b)-[r:Supply{max_supply: 424.468794117647, current_output: 577.27756,level: 12}]->(g);
CREATE (n: Building {id: 1468, name:"building_tobacco_plantationlevel", level:4});
MATCH (g: Goods{code: 43}), (b: Building{id:1468}) CREATE (b)-[r:Supply{max_supply: 88.431, current_output: 91.08393,level: 4}]->(g);
CREATE (n: Building {id: 1469, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:1469}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.412443057076991, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1469}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.3816503546814003, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1469}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.0726610457478465, level: 3}]->(b);
CREATE (n: Building {id: 1470, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1470}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.364928538460941, level: 1}]->(b);
CREATE (n: Building {id: 1471, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1471}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.336647014507441, level: 1}]->(b);
CREATE (n: Building {id: 1472, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:1472}) CREATE (g)-[r:Demand{max_demand: 24.649, current_input: 8.457059279261957, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1472}) CREATE (b)-[r:Supply{max_supply: 246.49000000000004, current_output: 84.57059279261958,level: 5}]->(g);
CREATE (n: Building {id: 1473, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1473}) CREATE (g)-[r:Demand{max_demand: 8.4294, current_input: 3.6064071052020927, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1473}) CREATE (g)-[r:Demand{max_demand: 8.4294, current_input: 3.0582394249175393, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1473}) CREATE (b)-[r:Supply{max_supply: 33.7176, current_output: 13.329293060239262,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1473}) CREATE (b)-[r:Supply{max_supply: 4.2147, current_output: 1.6661616325299078,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1473}) CREATE (b)-[r:Supply{max_supply: 21.0735, current_output: 8.33080816264954,level: 1}]->(g);
CREATE (n: Building {id: 1474, name:"building_sugar_plantationlevel", level:5});
MATCH (g: Goods{code: 42}), (b: Building{id:1474}) CREATE (b)-[r:Supply{max_supply: 132.65249999999997, current_output: 137.9586,level: 5}]->(g);
CREATE (n: Building {id: 1475, name:"building_cotton_plantationlevel", level:9});
MATCH (g: Goods{code: 9}), (b: Building{id:1475}) CREATE (b)-[r:Supply{max_supply: 318.3515939849624, current_output: 423.40762,level: 9}]->(g);
CREATE (n: Building {id: 1476, name:"building_tobacco_plantationlevel", level:4});
MATCH (g: Goods{code: 43}), (b: Building{id:1476}) CREATE (b)-[r:Supply{max_supply: 88.43599999999999, current_output: 91.08908,level: 4}]->(g);
CREATE (n: Building {id: 1477, name:"building_barrackslevel", level:4});
MATCH (g: Goods{code: 0}), (b: Building{id:1477}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.8832574094359882, level: 4}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1477}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.508867139575201, level: 4}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1477}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.096881394330461, level: 4}]->(b);
CREATE (n: Building {id: 1478, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1478}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.364928538460941, level: 1}]->(b);
CREATE (n: Building {id: 1479, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1479}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 5.442094499461775, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1479}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 21.7683779978471,level: 1}]->(g);
CREATE (n: Building {id: 1480, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1480}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.814031499820592, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1480}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.512251998564736,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1480}) CREATE (b)-[r:Supply{max_supply: 12.499999999999998, current_output: 4.535078749551479,level: 1}]->(g);
CREATE (n: Building {id: 1481, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1481}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.336647014507441, level: 1}]->(b);
CREATE (n: Building {id: 1482, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 21}), (b: Building{id:1482}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1482}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 13.703961920652958, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1482}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 9.788544229037825,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1482}) CREATE (b)-[r:Supply{max_supply: 24.999999999999996, current_output: 4.8942721145189125,level: 1}]->(g);
CREATE (n: Building {id: 1483, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1483}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.094785615382824, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1483}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 100.94785615382824,level: 3}]->(g);
CREATE (n: Building {id: 1484, name:"building_tobacco_plantationlevel", level:5});
MATCH (g: Goods{code: 43}), (b: Building{id:1484}) CREATE (b)-[r:Supply{max_supply: 110.55375, current_output: 114.9759,level: 5}]->(g);
CREATE (n: Building {id: 1485, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:1485}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.7307224908140224, level: 5}]->(b);
CREATE (n: Building {id: 1486, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1486}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.729857076921882, level: 2}]->(b);
CREATE (n: Building {id: 1487, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1487}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.364928538460941, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1487}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 33.64928538460941,level: 1}]->(g);
CREATE (n: Building {id: 1488, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1488}) CREATE (g)-[r:Demand{max_demand: 8.4294, current_input: 3.6064071052020927, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1488}) CREATE (g)-[r:Demand{max_demand: 8.4294, current_input: 3.0582394249175393, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1488}) CREATE (b)-[r:Supply{max_supply: 33.7176, current_output: 13.329293060239262,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1488}) CREATE (b)-[r:Supply{max_supply: 4.2147, current_output: 1.6661616325299078,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1488}) CREATE (b)-[r:Supply{max_supply: 21.0735, current_output: 8.33080816264954,level: 1}]->(g);
CREATE (n: Building {id: 1489, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:1489}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.009941043522323, level: 3}]->(b);
CREATE (n: Building {id: 1490, name:"building_steel_millslevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:1490}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 30.829114514882097, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1490}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 56.67993474668646, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:1490}) CREATE (b)-[r:Supply{max_supply: 195.0, current_output: 79.45065437280502,level: 3}]->(g);
CREATE (n: Building {id: 1491, name:"building_textile_millslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1491}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 268.02281957493346, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1491}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1491}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 3.628062999641183, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1491}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 90.8537533309412,level: 2}]->(g);
CREATE (n: Building {id: 1492, name:"building_furniture_manufacturieslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:1492}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 89.34093985831117, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1492}) CREATE (g)-[r:Demand{max_demand: 80.00000000000001, current_input: 29.95777436225807, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1492}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 9.768290502918461, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1492}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 7.256125999282369, level: 4}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1492}) CREATE (b)-[r:Supply{max_supply: 180.00000000000003, current_output: 89.16685839293876,level: 4}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1492}) CREATE (b)-[r:Supply{max_supply: 80.00000000000001, current_output: 39.62971484130612,level: 4}]->(g);
CREATE (n: Building {id: 1493, name:"building_glassworkslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1493}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 14.97888718112903, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1493}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1493}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 7.83083538323026, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1493}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 10.213519315863183,level: 2}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1493}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 12.766899144828978,level: 2}]->(g);
CREATE (n: Building {id: 1494, name:"building_coal_minelevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:1494}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 27.210472497308878, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1494}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 108.84188998923551,level: 5}]->(g);
CREATE (n: Building {id: 1495, name:"building_maize_farmlevel", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:1495}) CREATE (g)-[r:Demand{max_demand: 19.473592920353983, current_input: 6.681379760138325, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1495}) CREATE (b)-[r:Supply{max_supply: 147.9993539823009, current_output: 50.778502572956015,level: 4}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1495}) CREATE (b)-[r:Supply{max_supply: 23.368318584070796, current_output: 8.017658141188914,level: 4}]->(g);
CREATE (n: Building {id: 1496, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1496}) CREATE (g)-[r:Demand{max_demand: 25.292098214285712, current_input: 10.820889114939279, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1496}) CREATE (g)-[r:Demand{max_demand: 25.292098214285712, current_input: 9.176132571454087, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1496}) CREATE (b)-[r:Supply{max_supply: 101.16839285714285, current_output: 39.99404337278673,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1496}) CREATE (b)-[r:Supply{max_supply: 12.646044642857142, current_output: 4.999253656770104,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1496}) CREATE (b)-[r:Supply{max_supply: 63.23025, current_output: 24.996278872819946,level: 3}]->(g);
CREATE (n: Building {id: 1497, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:1497}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 9.070157499102962, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1497}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 72.5612599928237,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1497}) CREATE (b)-[r:Supply{max_supply: 62.50000000000001, current_output: 22.6753937477574,level: 5}]->(g);
CREATE (n: Building {id: 1498, name:"building_barrackslevel", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:1498}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.9416287047179941, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1498}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.2544335697876003, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1498}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.0484406971652307, level: 2}]->(b);
CREATE (n: Building {id: 1499, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1499}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 33.70249615754032, level: 3}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:1499}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.10199561305356, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1499}) CREATE (b)-[r:Supply{max_supply: 209.99999999999997, current_output: 60.67656349615116,level: 3}]->(g);
CREATE (n: Building {id: 1500, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1500}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.673294029014882, level: 1}]->(b);
CREATE (n: Building {id: 1501, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1501}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.364928538460941, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1501}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 33.64928538460941,level: 1}]->(g);
CREATE (n: Building {id: 1502, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 32}), (b: Building{id:1502}) CREATE (g)-[r:Demand{max_demand: 4.928999999999999, current_input: 1.6911373762620054, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1502}) CREATE (b)-[r:Supply{max_supply: 29.573999999999995, current_output: 10.146824257572034,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1502}) CREATE (b)-[r:Supply{max_supply: 8.8722, current_output: 3.04404727727161,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1502}) CREATE (b)-[r:Supply{max_supply: 5.9148, current_output: 2.0293648515144067,level: 1}]->(g);
CREATE (n: Building {id: 1503, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1503}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 3.628062999641183, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1503}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 43.5367559956942,level: 2}]->(g);
CREATE (n: Building {id: 1504, name:"building_lead_minelevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:1504}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.276371504960702, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1504}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.884188998923552, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1504}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 42.32112100776851,level: 3}]->(g);
CREATE (n: Building {id: 1505, name:"building_maize_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1505}) CREATE (g)-[r:Demand{max_demand: 9.859, current_input: 3.3826178520119936, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1505}) CREATE (b)-[r:Supply{max_supply: 59.15399999999999, current_output: 20.29570711207196,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1505}) CREATE (b)-[r:Supply{max_supply: 17.746198198198197, current_output: 6.088711515424313,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1505}) CREATE (b)-[r:Supply{max_supply: 11.830792792792792, current_output: 4.059138949625289,level: 2}]->(g);
CREATE (n: Building {id: 1506, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 21}), (b: Building{id:1506}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1506}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 13.703961920652958, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1506}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 9.788544229037825,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1506}) CREATE (b)-[r:Supply{max_supply: 24.999999999999996, current_output: 4.8942721145189125,level: 1}]->(g);
CREATE (n: Building {id: 1507, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1507}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.364928538460941, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1507}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 33.64928538460941,level: 1}]->(g);
CREATE (n: Building {id: 1508, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1508}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.336647014507441, level: 1}]->(b);
CREATE (n: Building {id: 1509, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:1509}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.138185752480351, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1509}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.442094499461776, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1509}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 21.160560503884255,level: 1}]->(g);
CREATE (n: Building {id: 1510, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1510}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.364928538460941, level: 1}]->(b);
CREATE (n: Building {id: 1511, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1511}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.673294029014882, level: 1}]->(b);
CREATE (n: Building {id: 1512, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1512}) CREATE (g)-[r:Demand{max_demand: 17.5254, current_input: 39.14339268482116, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1512}) CREATE (g)-[r:Demand{max_demand: 35.0508, current_input: 13.125549470207938, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1512}) CREATE (g)-[r:Demand{max_demand: 43.8135, current_input: 20.694552675199564, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1512}) CREATE (g)-[r:Demand{max_demand: 8.7627, current_input: 3.1791627646955805, level: 1}]->(b);
CREATE (n: Building {id: 1513, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1513}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.336647014507441, level: 2}]->(b);
CREATE (n: Building {id: 1514, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1514}) CREATE (g)-[r:Demand{max_demand: 179.99999999999997, current_input: 402.0342293624001, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1514}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1514}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.442094499461776, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1514}) CREATE (b)-[r:Supply{max_supply: 299.99999999999994, current_output: 136.28062999641182,level: 3}]->(g);
CREATE (n: Building {id: 1515, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1515}) CREATE (g)-[r:Demand{max_demand: 51.00359821428571, current_input: 19.099428587085495, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1515}) CREATE (g)-[r:Demand{max_demand: 38.252696428571426, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:1515}) CREATE (g)-[r:Demand{max_demand: 25.501794642857142, current_input: 9.985017791257881, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1515}) CREATE (b)-[r:Supply{max_supply: 51.00359821428571, current_output: 13.023155888503302,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1515}) CREATE (b)-[r:Supply{max_supply: 63.75449999999999, current_output: 16.278945430579984,level: 3}]->(g);
CREATE (n: Building {id: 1516, name:"building_shipyardslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1516}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 134.01140978746676, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1516}) CREATE (g)-[r:Demand{max_demand: 120.00000000000001, current_input: 44.936661543387096, level: 3}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1516}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 30.92562403938508,level: 3}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:1516}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 41.23416538584678,level: 3}]->(g);
CREATE (n: Building {id: 1517, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1517}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.094785615382824, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1517}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 100.94785615382824,level: 3}]->(g);
CREATE (n: Building {id: 1518, name:"building_whaling_stationlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1518}) CREATE (g)-[r:Demand{max_demand: 4.9990000000000006, current_input: 3.3642555527532494, level: 1}]->(b);
MATCH (g: Goods{code: 28}), (b: Building{id:1518}) CREATE (b)-[r:Supply{max_supply: 19.996000000000002, current_output: 13.457022211012998,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1518}) CREATE (b)-[r:Supply{max_supply: 9.998000000000001, current_output: 6.728511105506499,level: 1}]->(g);
CREATE (n: Building {id: 1519, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:1519}) CREATE (g)-[r:Demand{max_demand: 9.857999999999999, current_input: 3.382274752524011, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1519}) CREATE (b)-[r:Supply{max_supply: 59.147999999999996, current_output: 20.293648515144067,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:1519}) CREATE (b)-[r:Supply{max_supply: 17.744396396396397, current_output: 6.08809331814867,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:1519}) CREATE (b)-[r:Supply{max_supply: 11.829594594594594, current_output: 4.058727848436987,level: 2}]->(g);
CREATE (n: Building {id: 1520, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:1520}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.354071761794985, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1520}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.636083924469001, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1520}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.121101742913077, level: 5}]->(b);
CREATE (n: Building {id: 1521, name:"building_naval_baselevel", level:11});
MATCH (g: Goods{code: 5}), (b: Building{id:1521}) CREATE (g)-[r:Demand{max_demand: 22.0, current_input: 8.20758947979085, level: 11}]->(b);
CREATE (n: Building {id: 1522, name:"building_portlevel", level:3});
CREATE (n: Building {id: 1523, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1523}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.814031499820592, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1523}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.512251998564736,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1523}) CREATE (b)-[r:Supply{max_supply: 12.499999999999998, current_output: 4.535078749551479,level: 1}]->(g);
CREATE (n: Building {id: 1524, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1524}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.336647014507441, level: 1}]->(b);
CREATE (n: Building {id: 1525, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1525}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.814031499820592, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1525}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 21.7683779978471,level: 1}]->(g);
CREATE (n: Building {id: 1526, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1526}) CREATE (g)-[r:Demand{max_demand: 9.797999999999998, current_input: 4.191944482023644, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1526}) CREATE (g)-[r:Demand{max_demand: 9.797999999999998, current_input: 3.5547761270484317, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1526}) CREATE (b)-[r:Supply{max_supply: 39.19199999999999, current_output: 15.49344121814415,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1526}) CREATE (b)-[r:Supply{max_supply: 4.898999999999999, current_output: 1.9366801522680188,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1526}) CREATE (b)-[r:Supply{max_supply: 24.495, current_output: 9.683400761340096,level: 1}]->(g);
CREATE (n: Building {id: 1527, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1527}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 34.22694004510017, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1527}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 44.43149232147489, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1527}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 34.4130641603766,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1527}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 58.9938242749313,level: 2}]->(g);
CREATE (n: Building {id: 1528, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1528}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 3.628062999641183, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1528}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 43.5367559956942,level: 2}]->(g);
CREATE (n: Building {id: 1529, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1529}) CREATE (g)-[r:Demand{max_demand: 19.213594594594593, current_input: 8.220281878000625, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1529}) CREATE (g)-[r:Demand{max_demand: 19.213594594594593, current_input: 6.97081316387545, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1529}) CREATE (b)-[r:Supply{max_supply: 76.8543963963964, current_output: 30.382197206662518,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:1529}) CREATE (b)-[r:Supply{max_supply: 9.606792792792792, current_output: 3.7977719797414276,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1529}) CREATE (b)-[r:Supply{max_supply: 48.034, current_output: 18.98887414452787,level: 2}]->(g);
CREATE (n: Building {id: 1530, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1530}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 22.33523496457779, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1530}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 11.234165385846772, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1530}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.88414525145923, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1530}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.256125999282368, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1530}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 44.58342919646937,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1530}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.814857420653055,level: 1}]->(g);
CREATE (n: Building {id: 1531, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1531}) CREATE (g)-[r:Demand{max_demand: 20.000000000000004, current_input: 7.256125999282369, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1531}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 58.04900799425895,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1531}) CREATE (b)-[r:Supply{max_supply: 50.00000000000001, current_output: 18.140314998205923,level: 4}]->(g);
CREATE (n: Building {id: 1532, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1532}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 89.34093985831113, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1532}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 29.95777436225806, level: 2}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:1532}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 48.1065262834879,level: 2}]->(g);
CREATE (n: Building {id: 1533, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1533}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 10.094785615382824, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1533}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 100.94785615382824,level: 3}]->(g);
CREATE (n: Building {id: 1534, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1534}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.364928538460941, level: 1}]->(b);
CREATE (n: Building {id: 1535, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1535}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 9.346588058029765, level: 2}]->(b);
CREATE (n: Building {id: 1536, name:"building_food_industrylevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1536}) CREATE (g)-[r:Demand{max_demand: 36.145199999999996, current_input: 15.464244916476934, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1536}) CREATE (g)-[r:Demand{max_demand: 9.036299999999999, current_input: 1.4875266558171638, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:1536}) CREATE (g)-[r:Demand{max_demand: 36.145199999999996, current_input: 20.07481470322718, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:1536}) CREATE (b)-[r:Supply{max_supply: 22.590745454545452, current_output: 8.643574561456916,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1536}) CREATE (b)-[r:Supply{max_supply: 90.36299999999999, current_output: 34.5743052024773,level: 1}]->(g);
CREATE (n: Building {id: 1537, name:"building_coal_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1537}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 10.88418899892355, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:1537}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 43.5367559956942,level: 2}]->(g);
CREATE (n: Building {id: 1538, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1538}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 3.628062999641183, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1538}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 43.5367559956942,level: 2}]->(g);
CREATE (n: Building {id: 1539, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:1539}) CREATE (g)-[r:Demand{max_demand: 24.652000000000005, current_input: 8.458088577725903, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1539}) CREATE (b)-[r:Supply{max_supply: 246.52000000000004, current_output: 84.58088577725903,level: 5}]->(g);
CREATE (n: Building {id: 1540, name:"building_cotton_plantationlevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:1540}) CREATE (b)-[r:Supply{max_supply: 353.73999999999995, current_output: 474.0116,level: 10}]->(g);
CREATE (n: Building {id: 1541, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:1541}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.354071761794985, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1541}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.636083924469001, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1541}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.121101742913077, level: 5}]->(b);
CREATE (n: Building {id: 1542, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1542}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.364928538460941, level: 1}]->(b);
CREATE (n: Building {id: 1543, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1543}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.336647014507441, level: 1}]->(b);
CREATE (n: Building {id: 1544, name:"building_cotton_plantationlevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:1544}) CREATE (b)-[r:Supply{max_supply: 353.724, current_output: 473.99016,level: 10}]->(g);
CREATE (n: Building {id: 1545, name:"building_tobacco_plantationlevel", level:5});
MATCH (g: Goods{code: 43}), (b: Building{id:1545}) CREATE (b)-[r:Supply{max_supply: 110.53875, current_output: 114.9603,level: 5}]->(g);
CREATE (n: Building {id: 1546, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:1546}) CREATE (g)-[r:Demand{max_demand: 24.649, current_input: 8.457059279261957, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1546}) CREATE (b)-[r:Supply{max_supply: 246.49000000000004, current_output: 84.57059279261958,level: 5}]->(g);
CREATE (n: Building {id: 1547, name:"building_barrackslevel", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:1547}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.9416287047179941, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1547}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.2544335697876003, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1547}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.0484406971652307, level: 2}]->(b);
CREATE (n: Building {id: 1548, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1548}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.364928538460941, level: 1}]->(b);
CREATE (n: Building {id: 83888311, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:83888311}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 2797, name:"building_subsistence_farmslevel", level:57});
MATCH (g: Goods{code: 7}), (b: Building{id:2797}) CREATE (b)-[r:Supply{max_supply: 29.65994545454545, current_output: 32.62594,level: 57}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2797}) CREATE (b)-[r:Supply{max_supply: 5.931981818181818, current_output: 6.52518,level: 57}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2797}) CREATE (b)-[r:Supply{max_supply: 5.931981818181818, current_output: 6.52518,level: 57}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2797}) CREATE (b)-[r:Supply{max_supply: 5.931981818181818, current_output: 6.52518,level: 57}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2797}) CREATE (b)-[r:Supply{max_supply: 5.931981818181818, current_output: 6.52518,level: 57}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2797}) CREATE (b)-[r:Supply{max_supply: 5.931981818181818, current_output: 6.52518,level: 57}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2797}) CREATE (b)-[r:Supply{max_supply: 8.304781818181818, current_output: 9.13526,level: 57}]->(g);
CREATE (n: Building {id: 2798, name:"building_subsistence_farmslevel", level:102});
MATCH (g: Goods{code: 7}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 119.80154545454545, current_output: 131.7817,level: 102}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 23.96030909090909, current_output: 26.35634,level: 102}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 23.96030909090909, current_output: 26.35634,level: 102}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 23.96030909090909, current_output: 26.35634,level: 102}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 23.96030909090909, current_output: 26.35634,level: 102}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 23.96030909090909, current_output: 26.35634,level: 102}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2798}) CREATE (b)-[r:Supply{max_supply: 33.54442727272727, current_output: 36.89887,level: 102}]->(g);
CREATE (n: Building {id: 2799, name:"building_urban_centerlevel", level:12});
MATCH (g: Goods{code: 10}), (b: Building{id:2799}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 22.468330771693545, level: 12}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2799}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 41.1054860198428, level: 12}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2799}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 9.877007110103674, level: 12}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2799}) CREATE (b)-[r:Supply{max_supply: 839.9999999999999, current_output: 246.85771082802023,level: 12}]->(g);
CREATE (n: Building {id: 2800, name:"building_subsistence_farmslevel", level:69});
MATCH (g: Goods{code: 7}), (b: Building{id:2800}) CREATE (b)-[r:Supply{max_supply: 64.32351818181817, current_output: 70.75587,level: 69}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2800}) CREATE (b)-[r:Supply{max_supply: 12.8647, current_output: 14.15117,level: 69}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2800}) CREATE (b)-[r:Supply{max_supply: 12.8647, current_output: 14.15117,level: 69}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2800}) CREATE (b)-[r:Supply{max_supply: 12.8647, current_output: 14.15117,level: 69}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2800}) CREATE (b)-[r:Supply{max_supply: 12.8647, current_output: 14.15117,level: 69}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2800}) CREATE (b)-[r:Supply{max_supply: 12.8647, current_output: 14.15117,level: 69}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2800}) CREATE (b)-[r:Supply{max_supply: 18.010581818181816, current_output: 19.81164,level: 69}]->(g);
CREATE (n: Building {id: 2801, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2801}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.744721795282258, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2801}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.850914336640467, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2801}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.6461678516839457, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2801}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 41.14295180467004,level: 2}]->(g);
CREATE (n: Building {id: 2846, name:"building_subsistence_farmslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 8.522999999999998, current_output: 9.3753,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 1.7046, current_output: 1.87506,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 1.7046, current_output: 1.87506,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 1.7046, current_output: 1.87506,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 1.7046, current_output: 1.87506,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 1.7046, current_output: 1.87506,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2846}) CREATE (b)-[r:Supply{max_supply: 2.3864363636363635, current_output: 2.62508,level: 20}]->(g);
CREATE (n: Building {id: 2847, name:"building_subsistence_farmslevel", level:24});
MATCH (g: Goods{code: 7}), (b: Building{id:2847}) CREATE (b)-[r:Supply{max_supply: 13.108199999999998, current_output: 14.41902,level: 24}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2847}) CREATE (b)-[r:Supply{max_supply: 2.6216363636363633, current_output: 2.8838,level: 24}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2847}) CREATE (b)-[r:Supply{max_supply: 2.6216363636363633, current_output: 2.8838,level: 24}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2847}) CREATE (b)-[r:Supply{max_supply: 2.6216363636363633, current_output: 2.8838,level: 24}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2847}) CREATE (b)-[r:Supply{max_supply: 2.6216363636363633, current_output: 2.8838,level: 24}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2847}) CREATE (b)-[r:Supply{max_supply: 2.6216363636363633, current_output: 2.8838,level: 24}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2847}) CREATE (b)-[r:Supply{max_supply: 3.670290909090909, current_output: 4.03732,level: 24}]->(g);
CREATE (n: Building {id: 2850, name:"building_subsistence_farmslevel", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 17.301, current_output: 19.0311,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 3.4602, current_output: 3.80622,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 3.4602, current_output: 3.80622,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 3.4602, current_output: 3.80622,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 3.4602, current_output: 3.80622,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 3.4602, current_output: 3.80622,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2850}) CREATE (b)-[r:Supply{max_supply: 4.8442727272727275, current_output: 5.3287,level: 40}]->(g);
CREATE (n: Building {id: 2854, name:"building_subsistence_farmslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:2854}) CREATE (b)-[r:Supply{max_supply: 11.7865, current_output: 15.32245,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2854}) CREATE (b)-[r:Supply{max_supply: 2.3573, current_output: 3.06449,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2854}) CREATE (b)-[r:Supply{max_supply: 2.3573, current_output: 3.06449,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2854}) CREATE (b)-[r:Supply{max_supply: 2.3573, current_output: 3.06449,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2854}) CREATE (b)-[r:Supply{max_supply: 2.3573, current_output: 3.06449,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2854}) CREATE (b)-[r:Supply{max_supply: 2.3573, current_output: 3.06449,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2854}) CREATE (b)-[r:Supply{max_supply: 3.3002153846153846, current_output: 4.29028,level: 44}]->(g);
CREATE (n: Building {id: 100666151, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:100666151}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 22.468330771693545, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:100666151}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 12.247098590248857, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:100666151}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 54.45197154275577,level: 2}]->(g);
CREATE (n: Building {id: 2857, name:"building_subsistence_farmslevel", level:46});
MATCH (g: Goods{code: 7}), (b: Building{id:2857}) CREATE (b)-[r:Supply{max_supply: 14.193299999999999, current_output: 18.45129,level: 46}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2857}) CREATE (b)-[r:Supply{max_supply: 2.838653846153846, current_output: 3.69025,level: 46}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2857}) CREATE (b)-[r:Supply{max_supply: 2.838653846153846, current_output: 3.69025,level: 46}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2857}) CREATE (b)-[r:Supply{max_supply: 2.838653846153846, current_output: 3.69025,level: 46}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2857}) CREATE (b)-[r:Supply{max_supply: 2.838653846153846, current_output: 3.69025,level: 46}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2857}) CREATE (b)-[r:Supply{max_supply: 2.838653846153846, current_output: 3.69025,level: 46}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2857}) CREATE (b)-[r:Supply{max_supply: 3.974123076923077, current_output: 5.16636,level: 46}]->(g);
CREATE (n: Building {id: 2858, name:"building_subsistence_farmslevel", level:54});
MATCH (g: Goods{code: 7}), (b: Building{id:2858}) CREATE (b)-[r:Supply{max_supply: 11.526299999999999, current_output: 14.98419,level: 54}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2858}) CREATE (b)-[r:Supply{max_supply: 2.305253846153846, current_output: 2.99683,level: 54}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2858}) CREATE (b)-[r:Supply{max_supply: 2.305253846153846, current_output: 2.99683,level: 54}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2858}) CREATE (b)-[r:Supply{max_supply: 2.305253846153846, current_output: 2.99683,level: 54}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2858}) CREATE (b)-[r:Supply{max_supply: 2.305253846153846, current_output: 2.99683,level: 54}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2858}) CREATE (b)-[r:Supply{max_supply: 2.305253846153846, current_output: 2.99683,level: 54}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2858}) CREATE (b)-[r:Supply{max_supply: 3.227361538461538, current_output: 4.19557,level: 54}]->(g);
CREATE (n: Building {id: 2861, name:"building_subsistence_farmslevel", level:70});
MATCH (g: Goods{code: 7}), (b: Building{id:2861}) CREATE (b)-[r:Supply{max_supply: 14.5705, current_output: 18.94165,level: 70}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2861}) CREATE (b)-[r:Supply{max_supply: 2.9141, current_output: 3.78833,level: 70}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2861}) CREATE (b)-[r:Supply{max_supply: 2.9141, current_output: 3.78833,level: 70}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2861}) CREATE (b)-[r:Supply{max_supply: 2.9141, current_output: 3.78833,level: 70}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2861}) CREATE (b)-[r:Supply{max_supply: 2.9141, current_output: 3.78833,level: 70}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2861}) CREATE (b)-[r:Supply{max_supply: 2.9141, current_output: 3.78833,level: 70}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2861}) CREATE (b)-[r:Supply{max_supply: 4.0797384615384615, current_output: 5.30366,level: 70}]->(g);
CREATE (n: Building {id: 16780081, name:"building_subsistence_farmslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:16780081}) CREATE (b)-[r:Supply{max_supply: 4.1049, current_output: 4.51539,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:16780081}) CREATE (b)-[r:Supply{max_supply: 0.8209727272727272, current_output: 0.90307,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:16780081}) CREATE (b)-[r:Supply{max_supply: 0.8209727272727272, current_output: 0.90307,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:16780081}) CREATE (b)-[r:Supply{max_supply: 0.8209727272727272, current_output: 0.90307,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:16780081}) CREATE (b)-[r:Supply{max_supply: 0.8209727272727272, current_output: 0.90307,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:16780081}) CREATE (b)-[r:Supply{max_supply: 0.8209727272727272, current_output: 0.90307,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16780081}) CREATE (b)-[r:Supply{max_supply: 1.1493636363636361, current_output: 1.2643,level: 6}]->(g);
CREATE (n: Building {id: 2866, name:"building_subsistence_farmslevel", level:56});
MATCH (g: Goods{code: 7}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 6.374199999999999, current_output: 7.01162,level: 56}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 1.2748363636363635, current_output: 1.40232,level: 56}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 1.2748363636363635, current_output: 1.40232,level: 56}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 1.2748363636363635, current_output: 1.40232,level: 56}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 1.2748363636363635, current_output: 1.40232,level: 56}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 1.2748363636363635, current_output: 1.40232,level: 56}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2866}) CREATE (b)-[r:Supply{max_supply: 1.7847727272727272, current_output: 1.96325,level: 56}]->(g);
CREATE (n: Building {id: 2867, name:"building_subsistence_farmslevel", level:93});
MATCH (g: Goods{code: 7}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 25.898172727272726, current_output: 28.48799,level: 93}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 5.179627272727272, current_output: 5.69759,level: 93}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 5.179627272727272, current_output: 5.69759,level: 93}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 5.179627272727272, current_output: 5.69759,level: 93}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 5.179627272727272, current_output: 5.69759,level: 93}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 5.179627272727272, current_output: 5.69759,level: 93}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2867}) CREATE (b)-[r:Supply{max_supply: 7.2514818181818175, current_output: 7.97663,level: 93}]->(g);
CREATE (n: Building {id: 2868, name:"building_subsistence_farmslevel", level:68});
MATCH (g: Goods{code: 7}), (b: Building{id:2868}) CREATE (b)-[r:Supply{max_supply: 24.6806, current_output: 27.14866,level: 68}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2868}) CREATE (b)-[r:Supply{max_supply: 4.936118181818181, current_output: 5.42973,level: 68}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2868}) CREATE (b)-[r:Supply{max_supply: 4.936118181818181, current_output: 5.42973,level: 68}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2868}) CREATE (b)-[r:Supply{max_supply: 4.936118181818181, current_output: 5.42973,level: 68}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2868}) CREATE (b)-[r:Supply{max_supply: 4.936118181818181, current_output: 5.42973,level: 68}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2868}) CREATE (b)-[r:Supply{max_supply: 4.936118181818181, current_output: 5.42973,level: 68}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2868}) CREATE (b)-[r:Supply{max_supply: 6.910563636363635, current_output: 7.60162,level: 68}]->(g);
CREATE (n: Building {id: 2869, name:"building_subsistence_farmslevel", level:64});
MATCH (g: Goods{code: 7}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 17.958399999999997, current_output: 19.75424,level: 64}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 3.591672727272727, current_output: 3.95084,level: 64}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 3.591672727272727, current_output: 3.95084,level: 64}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 3.591672727272727, current_output: 3.95084,level: 64}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 3.591672727272727, current_output: 3.95084,level: 64}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 3.591672727272727, current_output: 3.95084,level: 64}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2869}) CREATE (b)-[r:Supply{max_supply: 5.0283454545454545, current_output: 5.53118,level: 64}]->(g);
CREATE (n: Building {id: 2871, name:"building_subsistence_farmslevel", level:78});
MATCH (g: Goods{code: 7}), (b: Building{id:2871}) CREATE (b)-[r:Supply{max_supply: 21.5631, current_output: 23.71941,level: 78}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2871}) CREATE (b)-[r:Supply{max_supply: 4.3126181818181815, current_output: 4.74388,level: 78}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2871}) CREATE (b)-[r:Supply{max_supply: 4.3126181818181815, current_output: 4.74388,level: 78}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2871}) CREATE (b)-[r:Supply{max_supply: 4.3126181818181815, current_output: 4.74388,level: 78}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2871}) CREATE (b)-[r:Supply{max_supply: 4.3126181818181815, current_output: 4.74388,level: 78}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2871}) CREATE (b)-[r:Supply{max_supply: 4.3126181818181815, current_output: 4.74388,level: 78}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2871}) CREATE (b)-[r:Supply{max_supply: 6.037663636363636, current_output: 6.64143,level: 78}]->(g);
CREATE (n: Building {id: 2872, name:"building_subsistence_farmslevel", level:78});
MATCH (g: Goods{code: 7}), (b: Building{id:2872}) CREATE (b)-[r:Supply{max_supply: 47.537099999999995, current_output: 52.29081,level: 78}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2872}) CREATE (b)-[r:Supply{max_supply: 9.507418181818181, current_output: 10.45816,level: 78}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2872}) CREATE (b)-[r:Supply{max_supply: 9.507418181818181, current_output: 10.45816,level: 78}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2872}) CREATE (b)-[r:Supply{max_supply: 9.507418181818181, current_output: 10.45816,level: 78}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2872}) CREATE (b)-[r:Supply{max_supply: 9.507418181818181, current_output: 10.45816,level: 78}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2872}) CREATE (b)-[r:Supply{max_supply: 9.507418181818181, current_output: 10.45816,level: 78}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2872}) CREATE (b)-[r:Supply{max_supply: 13.310381818181817, current_output: 14.64142,level: 78}]->(g);
CREATE (n: Building {id: 2873, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2873}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.872360897641129, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2873}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2873}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8230839258419729, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2873}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.57147590233502,level: 1}]->(g);
CREATE (n: Building {id: 2874, name:"building_subsistence_farmslevel", level:121});
MATCH (g: Goods{code: 7}), (b: Building{id:2874}) CREATE (b)-[r:Supply{max_supply: 20.978372727272724, current_output: 23.07621,level: 121}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2874}) CREATE (b)-[r:Supply{max_supply: 4.1956727272727266, current_output: 4.61524,level: 121}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2874}) CREATE (b)-[r:Supply{max_supply: 4.1956727272727266, current_output: 4.61524,level: 121}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2874}) CREATE (b)-[r:Supply{max_supply: 4.1956727272727266, current_output: 4.61524,level: 121}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2874}) CREATE (b)-[r:Supply{max_supply: 4.1956727272727266, current_output: 4.61524,level: 121}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2874}) CREATE (b)-[r:Supply{max_supply: 4.1956727272727266, current_output: 4.61524,level: 121}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2874}) CREATE (b)-[r:Supply{max_supply: 5.873936363636363, current_output: 6.46133,level: 121}]->(g);
CREATE (n: Building {id: 2875, name:"building_subsistence_farmslevel", level:148});
MATCH (g: Goods{code: 7}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 116.34649999999999, current_output: 127.98115,level: 148}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 23.269299999999998, current_output: 25.59623,level: 148}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 23.269299999999998, current_output: 25.59623,level: 148}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 23.269299999999998, current_output: 25.59623,level: 148}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 23.269299999999998, current_output: 25.59623,level: 148}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 23.269299999999998, current_output: 25.59623,level: 148}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 32.577018181818175, current_output: 35.83472,level: 148}]->(g);
CREATE (n: Building {id: 2876, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2876}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.617082692923387, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2876}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.276371504960702, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2876}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.469251777525919, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2876}) CREATE (b)-[r:Supply{max_supply: 209.99999999999997, current_output: 61.71442770700506,level: 3}]->(g);
CREATE (n: Building {id: 2877, name:"building_subsistence_farmslevel", level:48});
MATCH (g: Goods{code: 7}), (b: Building{id:2877}) CREATE (b)-[r:Supply{max_supply: 54.9264, current_output: 60.41904,level: 48}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2877}) CREATE (b)-[r:Supply{max_supply: 10.985272727272726, current_output: 12.0838,level: 48}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2877}) CREATE (b)-[r:Supply{max_supply: 10.985272727272726, current_output: 12.0838,level: 48}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2877}) CREATE (b)-[r:Supply{max_supply: 10.985272727272726, current_output: 12.0838,level: 48}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2877}) CREATE (b)-[r:Supply{max_supply: 10.985272727272726, current_output: 12.0838,level: 48}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2877}) CREATE (b)-[r:Supply{max_supply: 10.985272727272726, current_output: 12.0838,level: 48}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2877}) CREATE (b)-[r:Supply{max_supply: 15.379390909090908, current_output: 16.91733,level: 48}]->(g);
CREATE (n: Building {id: 2878, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2878}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.744721795282258, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2878}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.850914336640467, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2878}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.6461678516839457, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2878}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 41.14295180467004,level: 2}]->(g);
CREATE (n: Building {id: 2879, name:"building_subsistence_farmslevel", level:45});
MATCH (g: Goods{code: 7}), (b: Building{id:2879}) CREATE (b)-[r:Supply{max_supply: 43.568999999999996, current_output: 47.9259,level: 45}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2879}) CREATE (b)-[r:Supply{max_supply: 8.713799999999999, current_output: 9.58518,level: 45}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2879}) CREATE (b)-[r:Supply{max_supply: 8.713799999999999, current_output: 9.58518,level: 45}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2879}) CREATE (b)-[r:Supply{max_supply: 8.713799999999999, current_output: 9.58518,level: 45}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2879}) CREATE (b)-[r:Supply{max_supply: 8.713799999999999, current_output: 9.58518,level: 45}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2879}) CREATE (b)-[r:Supply{max_supply: 8.713799999999999, current_output: 9.58518,level: 45}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2879}) CREATE (b)-[r:Supply{max_supply: 12.19931818181818, current_output: 13.41925,level: 45}]->(g);
CREATE (n: Building {id: 2880, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2880}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.744721795282258, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2880}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.850914336640467, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2880}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.6461678516839457, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2880}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 41.14295180467004,level: 2}]->(g);
CREATE (n: Building {id: 2881, name:"building_subsistence_farmslevel", level:48});
MATCH (g: Goods{code: 7}), (b: Building{id:2881}) CREATE (b)-[r:Supply{max_supply: 13.6284, current_output: 14.99124,level: 48}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2881}) CREATE (b)-[r:Supply{max_supply: 2.7256727272727272, current_output: 2.99824,level: 48}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2881}) CREATE (b)-[r:Supply{max_supply: 2.7256727272727272, current_output: 2.99824,level: 48}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2881}) CREATE (b)-[r:Supply{max_supply: 2.7256727272727272, current_output: 2.99824,level: 48}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2881}) CREATE (b)-[r:Supply{max_supply: 2.7256727272727272, current_output: 2.99824,level: 48}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2881}) CREATE (b)-[r:Supply{max_supply: 2.7256727272727272, current_output: 2.99824,level: 48}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2881}) CREATE (b)-[r:Supply{max_supply: 3.8159454545454543, current_output: 4.19754,level: 48}]->(g);
CREATE (n: Building {id: 2882, name:"building_subsistence_farmslevel", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:2882}) CREATE (b)-[r:Supply{max_supply: 52.879999999999995, current_output: 58.168,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2882}) CREATE (b)-[r:Supply{max_supply: 10.575999999999999, current_output: 11.6336,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2882}) CREATE (b)-[r:Supply{max_supply: 10.575999999999999, current_output: 11.6336,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2882}) CREATE (b)-[r:Supply{max_supply: 10.575999999999999, current_output: 11.6336,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2882}) CREATE (b)-[r:Supply{max_supply: 10.575999999999999, current_output: 11.6336,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2882}) CREATE (b)-[r:Supply{max_supply: 10.575999999999999, current_output: 11.6336,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2882}) CREATE (b)-[r:Supply{max_supply: 14.8064, current_output: 16.28704,level: 50}]->(g);
CREATE (n: Building {id: 2883, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2883}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.872360897641129, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2883}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2883}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8230839258419729, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2883}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.57147590233502,level: 1}]->(g);
CREATE (n: Building {id: 2884, name:"building_subsistence_farmslevel", level:88});
MATCH (g: Goods{code: 7}), (b: Building{id:2884}) CREATE (b)-[r:Supply{max_supply: 24.552, current_output: 27.0072,level: 88}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2884}) CREATE (b)-[r:Supply{max_supply: 4.910399999999999, current_output: 5.40144,level: 88}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2884}) CREATE (b)-[r:Supply{max_supply: 4.910399999999999, current_output: 5.40144,level: 88}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2884}) CREATE (b)-[r:Supply{max_supply: 4.910399999999999, current_output: 5.40144,level: 88}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2884}) CREATE (b)-[r:Supply{max_supply: 4.910399999999999, current_output: 5.40144,level: 88}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2884}) CREATE (b)-[r:Supply{max_supply: 4.910399999999999, current_output: 5.40144,level: 88}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2884}) CREATE (b)-[r:Supply{max_supply: 6.8745545454545445, current_output: 7.56201,level: 88}]->(g);
CREATE (n: Building {id: 2885, name:"building_subsistence_farmslevel", level:68});
MATCH (g: Goods{code: 7}), (b: Building{id:2885}) CREATE (b)-[r:Supply{max_supply: 48.9923, current_output: 53.89153,level: 68}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2885}) CREATE (b)-[r:Supply{max_supply: 9.798454545454545, current_output: 10.7783,level: 68}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2885}) CREATE (b)-[r:Supply{max_supply: 9.798454545454545, current_output: 10.7783,level: 68}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2885}) CREATE (b)-[r:Supply{max_supply: 9.798454545454545, current_output: 10.7783,level: 68}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2885}) CREATE (b)-[r:Supply{max_supply: 9.798454545454545, current_output: 10.7783,level: 68}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2885}) CREATE (b)-[r:Supply{max_supply: 9.798454545454545, current_output: 10.7783,level: 68}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2885}) CREATE (b)-[r:Supply{max_supply: 13.717836363636362, current_output: 15.08962,level: 68}]->(g);
CREATE (n: Building {id: 2886, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2886}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.872360897641129, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2886}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2886}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8230839258419729, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2886}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.57147590233502,level: 1}]->(g);
CREATE (n: Building {id: 2887, name:"building_subsistence_farmslevel", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 2.485872727272727, current_output: 2.73446,level: 15}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 0.4971727272727272, current_output: 0.54689,level: 15}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 0.4971727272727272, current_output: 0.54689,level: 15}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 0.4971727272727272, current_output: 0.54689,level: 15}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 0.4971727272727272, current_output: 0.54689,level: 15}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 0.4971727272727272, current_output: 0.54689,level: 15}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 0.6960363636363636, current_output: 0.76564,level: 15}]->(g);
CREATE (n: Building {id: 2888, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2888}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.1872360897641129, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2888}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.34254571683202334, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2888}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.0823083925841973, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2888}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 2.057147590233502,level: 1}]->(g);
CREATE (n: Building {id: 2889, name:"building_subsistence_farmslevel", level:54});
MATCH (g: Goods{code: 7}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 21.911845454545453, current_output: 24.10303,level: 54}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 4.382363636363636, current_output: 4.8206,level: 54}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 4.382363636363636, current_output: 4.8206,level: 54}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 4.382363636363636, current_output: 4.8206,level: 54}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 4.382363636363636, current_output: 4.8206,level: 54}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 4.382363636363636, current_output: 4.8206,level: 54}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 6.13530909090909, current_output: 6.74884,level: 54}]->(g);
CREATE (n: Building {id: 2890, name:"building_subsistence_farmslevel", level:27});
MATCH (g: Goods{code: 7}), (b: Building{id:2890}) CREATE (b)-[r:Supply{max_supply: 24.208199999999998, current_output: 26.62902,level: 27}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2890}) CREATE (b)-[r:Supply{max_supply: 4.841636363636363, current_output: 5.3258,level: 27}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2890}) CREATE (b)-[r:Supply{max_supply: 4.841636363636363, current_output: 5.3258,level: 27}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2890}) CREATE (b)-[r:Supply{max_supply: 4.841636363636363, current_output: 5.3258,level: 27}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2890}) CREATE (b)-[r:Supply{max_supply: 4.841636363636363, current_output: 5.3258,level: 27}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2890}) CREATE (b)-[r:Supply{max_supply: 4.841636363636363, current_output: 5.3258,level: 27}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2890}) CREATE (b)-[r:Supply{max_supply: 6.7782909090909085, current_output: 7.45612,level: 27}]->(g);
CREATE (n: Building {id: 2891, name:"building_subsistence_farmslevel", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 3.3736454545454544, current_output: 3.71101,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 0.6747272727272726, current_output: 0.7422,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 0.6747272727272726, current_output: 0.7422,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 0.6747272727272726, current_output: 0.7422,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 0.6747272727272726, current_output: 0.7422,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 0.6747272727272726, current_output: 0.7422,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 0.9446181818181817, current_output: 1.03908,level: 18}]->(g);
CREATE (n: Building {id: 2892, name:"building_subsistence_farmslevel", level:136});
MATCH (g: Goods{code: 7}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 124.12379999999999, current_output: 136.53618,level: 136}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 24.824754545454542, current_output: 27.30723,level: 136}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 24.824754545454542, current_output: 27.30723,level: 136}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 24.824754545454542, current_output: 27.30723,level: 136}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 24.824754545454542, current_output: 27.30723,level: 136}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 24.824754545454542, current_output: 27.30723,level: 136}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 34.75466363636364, current_output: 38.23013,level: 136}]->(g);
CREATE (n: Building {id: 2893, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2893}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.489443590564516, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2893}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 13.701828673280934, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2893}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.2923357033678915, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2893}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 82.28590360934007,level: 4}]->(g);
CREATE (n: Building {id: 2894, name:"building_subsistence_farmslevel", level:91});
MATCH (g: Goods{code: 7}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 25.072772727272724, current_output: 27.58005,level: 91}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 5.014554545454545, current_output: 5.51601,level: 91}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 5.014554545454545, current_output: 5.51601,level: 91}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 5.014554545454545, current_output: 5.51601,level: 91}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 5.014554545454545, current_output: 5.51601,level: 91}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 5.014554545454545, current_output: 5.51601,level: 91}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 7.0203727272727265, current_output: 7.72241,level: 91}]->(g);
CREATE (n: Building {id: 2895, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2895}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.872360897641129, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2895}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2895}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8230839258419729, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2895}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.57147590233502,level: 1}]->(g);
CREATE (n: Building {id: 2896, name:"building_subsistence_farmslevel", level:138});
MATCH (g: Goods{code: 7}), (b: Building{id:2896}) CREATE (b)-[r:Supply{max_supply: 48.09644545454545, current_output: 52.90609,level: 138}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2896}) CREATE (b)-[r:Supply{max_supply: 9.619281818181818, current_output: 10.58121,level: 138}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2896}) CREATE (b)-[r:Supply{max_supply: 9.619281818181818, current_output: 10.58121,level: 138}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2896}) CREATE (b)-[r:Supply{max_supply: 9.619281818181818, current_output: 10.58121,level: 138}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2896}) CREATE (b)-[r:Supply{max_supply: 9.619281818181818, current_output: 10.58121,level: 138}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2896}) CREATE (b)-[r:Supply{max_supply: 9.619281818181818, current_output: 10.58121,level: 138}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2896}) CREATE (b)-[r:Supply{max_supply: 13.466999999999999, current_output: 14.8137,level: 138}]->(g);
CREATE (n: Building {id: 2897, name:"building_subsistence_farmslevel", level:43});
MATCH (g: Goods{code: 7}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 14.622145454545453, current_output: 16.08436,level: 43}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 2.9244272727272724, current_output: 3.21687,level: 43}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 2.9244272727272724, current_output: 3.21687,level: 43}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 2.9244272727272724, current_output: 3.21687,level: 43}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 2.9244272727272724, current_output: 3.21687,level: 43}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 2.9244272727272724, current_output: 3.21687,level: 43}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2897}) CREATE (b)-[r:Supply{max_supply: 4.0942, current_output: 4.50362,level: 43}]->(g);
CREATE (n: Building {id: 2898, name:"building_subsistence_farmslevel", level:19});
MATCH (g: Goods{code: 7}), (b: Building{id:2898}) CREATE (b)-[r:Supply{max_supply: 3.5805454545454545, current_output: 3.9386,level: 19}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2898}) CREATE (b)-[r:Supply{max_supply: 0.7161090909090908, current_output: 0.78772,level: 19}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2898}) CREATE (b)-[r:Supply{max_supply: 0.7161090909090908, current_output: 0.78772,level: 19}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2898}) CREATE (b)-[r:Supply{max_supply: 0.7161090909090908, current_output: 0.78772,level: 19}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2898}) CREATE (b)-[r:Supply{max_supply: 0.7161090909090908, current_output: 0.78772,level: 19}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2898}) CREATE (b)-[r:Supply{max_supply: 0.7161090909090908, current_output: 0.78772,level: 19}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2898}) CREATE (b)-[r:Supply{max_supply: 1.0025454545454544, current_output: 1.1028,level: 19}]->(g);
CREATE (n: Building {id: 2899, name:"building_subsistence_farmslevel", level:100});
MATCH (g: Goods{code: 7}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 25.342499999999998, current_output: 27.87675,level: 100}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 5.0685, current_output: 5.57535,level: 100}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 5.0685, current_output: 5.57535,level: 100}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 5.0685, current_output: 5.57535,level: 100}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 5.0685, current_output: 5.57535,level: 100}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 5.0685, current_output: 5.57535,level: 100}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2899}) CREATE (b)-[r:Supply{max_supply: 7.095899999999999, current_output: 7.80549,level: 100}]->(g);
CREATE (n: Building {id: 2900, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2900}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.489443590564516, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2900}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 13.701828673280934, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2900}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.2923357033678915, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2900}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 82.28590360934007,level: 4}]->(g);
CREATE (n: Building {id: 2901, name:"building_subsistence_farmslevel", level:58});
MATCH (g: Goods{code: 7}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 23.668345454545452, current_output: 26.03518,level: 58}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 4.7336636363636355, current_output: 5.20703,level: 58}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 4.7336636363636355, current_output: 5.20703,level: 58}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 4.7336636363636355, current_output: 5.20703,level: 58}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 4.7336636363636355, current_output: 5.20703,level: 58}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 4.7336636363636355, current_output: 5.20703,level: 58}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 6.627136363636364, current_output: 7.28985,level: 58}]->(g);
CREATE (n: Building {id: 2902, name:"building_subsistence_farmslevel", level:22});
MATCH (g: Goods{code: 7}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 11.152899999999999, current_output: 12.26819,level: 22}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 2.230572727272727, current_output: 2.45363,level: 22}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 2.230572727272727, current_output: 2.45363,level: 22}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 2.230572727272727, current_output: 2.45363,level: 22}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 2.230572727272727, current_output: 2.45363,level: 22}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 2.230572727272727, current_output: 2.45363,level: 22}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2902}) CREATE (b)-[r:Supply{max_supply: 3.1228090909090906, current_output: 3.43509,level: 22}]->(g);
CREATE (n: Building {id: 2903, name:"building_subsistence_farmslevel", level:24});
MATCH (g: Goods{code: 7}), (b: Building{id:2903}) CREATE (b)-[r:Supply{max_supply: 15.302999999999999, current_output: 16.8333,level: 24}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2903}) CREATE (b)-[r:Supply{max_supply: 3.0605999999999995, current_output: 3.36666,level: 24}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2903}) CREATE (b)-[r:Supply{max_supply: 3.0605999999999995, current_output: 3.36666,level: 24}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2903}) CREATE (b)-[r:Supply{max_supply: 3.0605999999999995, current_output: 3.36666,level: 24}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2903}) CREATE (b)-[r:Supply{max_supply: 3.0605999999999995, current_output: 3.36666,level: 24}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2903}) CREATE (b)-[r:Supply{max_supply: 3.0605999999999995, current_output: 3.36666,level: 24}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2903}) CREATE (b)-[r:Supply{max_supply: 4.284836363636364, current_output: 4.71332,level: 24}]->(g);
CREATE (n: Building {id: 2904, name:"building_subsistence_farmslevel", level:70});
MATCH (g: Goods{code: 7}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 70.69999999999999, current_output: 77.77,level: 70}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 14.139999999999999, current_output: 15.554,level: 70}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 14.139999999999999, current_output: 15.554,level: 70}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 14.139999999999999, current_output: 15.554,level: 70}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 14.139999999999999, current_output: 15.554,level: 70}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 14.139999999999999, current_output: 15.554,level: 70}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 19.796, current_output: 21.7756,level: 70}]->(g);
CREATE (n: Building {id: 2905, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2905}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.744721795282258, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2905}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.850914336640467, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2905}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.6461678516839457, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2905}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 41.14295180467004,level: 2}]->(g);
CREATE (n: Building {id: 2919, name:"building_subsistence_farmslevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.40249999999999997, current_output: 0.52325,level: 2}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.0805, current_output: 0.10465,level: 2}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.0805, current_output: 0.10465,level: 2}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.0805, current_output: 0.10465,level: 2}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.0805, current_output: 0.10465,level: 2}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.0805, current_output: 0.10465,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2919}) CREATE (b)-[r:Supply{max_supply: 0.1127, current_output: 0.14651,level: 2}]->(g);
CREATE (n: Building {id: 3040, name:"building_subsistence_farmslevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:3040}) CREATE (b)-[r:Supply{max_supply: 0.20949999999999996, current_output: 0.23045,level: 2}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3040}) CREATE (b)-[r:Supply{max_supply: 0.04189999999999999, current_output: 0.04609,level: 2}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3040}) CREATE (b)-[r:Supply{max_supply: 0.04189999999999999, current_output: 0.04609,level: 2}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3040}) CREATE (b)-[r:Supply{max_supply: 0.04189999999999999, current_output: 0.04609,level: 2}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3040}) CREATE (b)-[r:Supply{max_supply: 0.04189999999999999, current_output: 0.04609,level: 2}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3040}) CREATE (b)-[r:Supply{max_supply: 0.04189999999999999, current_output: 0.04609,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3040}) CREATE (b)-[r:Supply{max_supply: 0.058654545454545445, current_output: 0.06452,level: 2}]->(g);
CREATE (n: Building {id: 33557875, name:"building_portlevel", level:5});
MATCH (g: Goods{code: 18}), (b: Building{id:33557875}) CREATE (g)-[r:Demand{max_demand: 20.744, current_input: 13.960415520366752, level: 5}]->(b);
CREATE (n: Building {id: 33558243, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:33558243}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1683235072537206, level: 1}]->(b);
CREATE (n: Building {id: 16781062, name:"building_subsistence_farmslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:16781062}) CREATE (b)-[r:Supply{max_supply: 2.946, current_output: 3.8298,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:16781062}) CREATE (b)-[r:Supply{max_supply: 0.5892, current_output: 0.76596,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:16781062}) CREATE (b)-[r:Supply{max_supply: 0.5892, current_output: 0.76596,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:16781062}) CREATE (b)-[r:Supply{max_supply: 0.5892, current_output: 0.76596,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:16781062}) CREATE (b)-[r:Supply{max_supply: 0.5892, current_output: 0.76596,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:16781062}) CREATE (b)-[r:Supply{max_supply: 0.5892, current_output: 0.76596,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16781062}) CREATE (b)-[r:Supply{max_supply: 0.8248769230769231, current_output: 1.07234,level: 6}]->(g);
CREATE (n: Building {id: 3847, name:"building_subsistence_farmslevel", level:58});
MATCH (g: Goods{code: 7}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 55.60314545454545, current_output: 61.16346,level: 58}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 11.12062727272727, current_output: 12.23269,level: 58}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 11.12062727272727, current_output: 12.23269,level: 58}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 11.12062727272727, current_output: 12.23269,level: 58}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 11.12062727272727, current_output: 12.23269,level: 58}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 11.12062727272727, current_output: 12.23269,level: 58}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3847}) CREATE (b)-[r:Supply{max_supply: 15.568881818181817, current_output: 17.12577,level: 58}]->(g);
CREATE (n: Building {id: 3848, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3848}) CREATE (g)-[r:Demand{max_demand: 1.94635, current_input: 0.7288539266247623, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3848}) CREATE (g)-[r:Demand{max_demand: 3.8927, current_input: 1.3334277119120175, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3848}) CREATE (g)-[r:Demand{max_demand: 1.94635, current_input: 0.3204018798125048, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3848}) CREATE (b)-[r:Supply{max_supply: 27.2489, current_output: 8.007858424501954,level: 1}]->(g);
CREATE (n: Building {id: 3857, name:"building_trade_centerlevel", level:6});
CREATE (n: Building {id: 3858, name:"building_trade_centerlevel", level:92});
CREATE (n: Building {id: 3859, name:"building_trade_centerlevel", level:9});
CREATE (n: Building {id: 16781102, name:"building_naval_baselevel", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:16781102}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.492288996325609, level: 2}]->(b);
CREATE (n: Building {id: 3930, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 3931, name:"building_conscription_centerlevel", level:46});
CREATE (n: Building {id: 3932, name:"building_conscription_centerlevel", level:23});
CREATE (n: Building {id: 50335603, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:50335603}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.412443057076991, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:50335603}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.3816503546814003, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:50335603}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.0726610457478465, level: 3}]->(b);
CREATE (n: Building {id: 3962, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 3963, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 3965, name:"building_conscription_centerlevel", level:14});
CREATE (n: Building {id: 3966, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 3967, name:"building_conscription_centerlevel", level:29});
CREATE (n: Building {id: 3968, name:"building_conscription_centerlevel", level:20});
CREATE (n: Building {id: 3969, name:"building_conscription_centerlevel", level:20});
CREATE (n: Building {id: 3970, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 3971, name:"building_conscription_centerlevel", level:19});
CREATE (n: Building {id: 3972, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 3973, name:"building_conscription_centerlevel", level:19});
CREATE (n: Building {id: 3974, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 3975, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 3976, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 3977, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 3978, name:"building_conscription_centerlevel", level:34});
CREATE (n: Building {id: 3979, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 3980, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 3981, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 3982, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 3983, name:"building_conscription_centerlevel", level:17});
CREATE (n: Building {id: 3984, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 3985, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 3986, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 3987, name:"building_conscription_centerlevel", level:22});
CREATE (n: Building {id: 16781241, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16781241}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 11.234165385846772, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16781241}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16781241}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.1235492951244295, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16781241}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 27.28520962977255,level: 1}]->(g);
CREATE (n: Building {id: 16781552, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:16781552}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.442094499461776, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16781552}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 21.768377997847104,level: 1}]->(g);
CREATE (n: Building {id: 16781554, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:16781554}) CREATE (b)-[r:Supply{max_supply: 22.1175, current_output: 22.1175,level: 1}]->(g);
CREATE (n: Building {id: 201330991, name:"building_arms_industrylevel", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:201330991}) CREATE (g)-[r:Demand{max_demand: 37.443198198198196, current_input: 17.685650254842685, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:201330991}) CREATE (g)-[r:Demand{max_demand: 18.721594594594592, current_input: 4.571949366946698, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:201330991}) CREATE (b)-[r:Supply{max_supply: 28.082396396396394, current_output: 10.061080888886092,level: 2}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:201330991}) CREATE (b)-[r:Supply{max_supply: 28.082396396396394, current_output: 10.061080888886092,level: 2}]->(g);
CREATE (n: Building {id: 4542, name:"building_conscription_centerlevel", level:17});
CREATE (n: Building {id: 100667901, name:"building_naval_baselevel", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:100667901}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7461444981628045, level: 1}]->(b);
CREATE (n: Building {id: 4672, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:4672}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 22.468330771693545, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:4672}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 12.247098590248857, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4672}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 54.45197154275577,level: 2}]->(g);
CREATE (n: Building {id: 4752, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:4752}) CREATE (g)-[r:Demand{max_demand: 17.5254, current_input: 39.14339268482116, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4752}) CREATE (g)-[r:Demand{max_demand: 35.0508, current_input: 13.125549470207938, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4752}) CREATE (g)-[r:Demand{max_demand: 43.8135, current_input: 20.694552675199564, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4752}) CREATE (g)-[r:Demand{max_demand: 8.7627, current_input: 3.1791627646955805, level: 1}]->(b);
CREATE (n: Building {id: 16781993, name:"building_naval_baselevel", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:16781993}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7461444981628045, level: 1}]->(b);
CREATE (n: Building {id: 218108628, name:"building_munition_plantslevel", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:218108628}) CREATE (g)-[r:Demand{max_demand: 17.7606, current_input: 6.9540167453699695, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:218108628}) CREATE (g)-[r:Demand{max_demand: 17.7606, current_input: 2.257647739245706, level: 1}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:218108628}) CREATE (b)-[r:Supply{max_supply: 44.4015, current_output: 11.514580605769595,level: 1}]->(g);
CREATE (n: Building {id: 4858, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:4858}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 22.468330771693545, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:4858}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 12.247098590248857, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4858}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 54.45197154275577,level: 2}]->(g);
CREATE (n: Building {id: 268440356, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 16782142, name:"building_naval_baselevel", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:16782142}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.7461444981628045, level: 1}]->(b);
CREATE (n: Building {id: 4939, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4939}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 11.234165385846772, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:4939}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:4939}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.1235492951244295, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4939}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 27.28520962977255,level: 1}]->(g);
CREATE (n: Building {id: 16782158, name:"building_naval_baselevel", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:16782158}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.492288996325609, level: 2}]->(b);
CREATE (n: Building {id: 218108808, name:"building_munition_plantslevel", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:218108808}) CREATE (g)-[r:Demand{max_demand: 17.741, current_input: 6.946342526694404, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:218108808}) CREATE (g)-[r:Demand{max_demand: 17.741, current_input: 2.255156275236088, level: 1}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:218108808}) CREATE (b)-[r:Supply{max_supply: 44.3525, current_output: 11.501873502413117,level: 1}]->(g);
CREATE (n: Building {id: 16782352, name:"building_arms_industrylevel", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:16782352}) CREATE (g)-[r:Demand{max_demand: 2.16, current_input: 1.0202388254403565, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782352}) CREATE (g)-[r:Demand{max_demand: 1.08, current_input: 0.26374384357879843, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16782352}) CREATE (b)-[r:Supply{max_supply: 1.6199999999999999, current_output: 0.5803974422242324,level: 2}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:16782352}) CREATE (b)-[r:Supply{max_supply: 1.6199999999999999, current_output: 0.5803974422242324,level: 2}]->(g);
CREATE (n: Building {id: 5145, name:"building_gold_fieldslevel", level:2});
MATCH (g: Goods{code: 50}), (b: Building{id:5145}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 5152, name:"building_gold_fieldslevel", level:1});
MATCH (g: Goods{code: 50}), (b: Building{id:5152}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 16782413, name:"building_barrackslevel", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:16782413}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.9416287047179941, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16782413}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.2544335697876003, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16782413}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.0484406971652307, level: 2}]->(b);
CREATE (n: Building {id: 16782481, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:16782481}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.412443057076991, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16782481}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.3816503546814003, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16782481}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.0726610457478465, level: 3}]->(b);
CREATE (n: Building {id: 16782520, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:16782520}) CREATE (g)-[r:Demand{max_demand: 9.2366, current_input: 4.362749044010369, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782520}) CREATE (g)-[r:Demand{max_demand: 4.6183, current_input: 1.127822400740708, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16782520}) CREATE (b)-[r:Supply{max_supply: 6.927445454545454, current_output: 2.481896063559301,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:16782520}) CREATE (b)-[r:Supply{max_supply: 6.927445454545454, current_output: 2.481896063559301,level: 1}]->(g);
CREATE (n: Building {id: 151000257, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:151000257}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.354071761794985, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:151000257}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.636083924469001, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:151000257}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.121101742913077, level: 5}]->(b);
CREATE (n: Building {id: 100668624, name:"building_subsistence_farmslevel", level:22});
MATCH (g: Goods{code: 7}), (b: Building{id:100668624}) CREATE (b)-[r:Supply{max_supply: 6.602199999999999, current_output: 7.26242,level: 22}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:100668624}) CREATE (b)-[r:Supply{max_supply: 1.3204363636363636, current_output: 1.45248,level: 22}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:100668624}) CREATE (b)-[r:Supply{max_supply: 1.3204363636363636, current_output: 1.45248,level: 22}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:100668624}) CREATE (b)-[r:Supply{max_supply: 1.3204363636363636, current_output: 1.45248,level: 22}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:100668624}) CREATE (b)-[r:Supply{max_supply: 1.3204363636363636, current_output: 1.45248,level: 22}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:100668624}) CREATE (b)-[r:Supply{max_supply: 1.3204363636363636, current_output: 1.45248,level: 22}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:100668624}) CREATE (b)-[r:Supply{max_supply: 1.8486090909090906, current_output: 2.03347,level: 22}]->(g);
CREATE (n: Building {id: 16782545, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 5344, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5344}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.872360897641129, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5344}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5344}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8230839258419729, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5344}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.57147590233502,level: 1}]->(g);
CREATE (n: Building {id: 16782596, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:16782596}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.138185752480351, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782596}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.442094499461776, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782596}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 21.160560503884255,level: 1}]->(g);
CREATE (n: Building {id: 5389, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5389}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.872360897641129, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5389}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5389}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8230839258419729, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5389}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.57147590233502,level: 1}]->(g);
CREATE (n: Building {id: 50337073, name:"building_subsistence_farmslevel", level:23});
MATCH (g: Goods{code: 7}), (b: Building{id:50337073}) CREATE (b)-[r:Supply{max_supply: 10.8514, current_output: 11.93654,level: 23}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:50337073}) CREATE (b)-[r:Supply{max_supply: 2.170272727272727, current_output: 2.3873,level: 23}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:50337073}) CREATE (b)-[r:Supply{max_supply: 2.170272727272727, current_output: 2.3873,level: 23}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:50337073}) CREATE (b)-[r:Supply{max_supply: 2.170272727272727, current_output: 2.3873,level: 23}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:50337073}) CREATE (b)-[r:Supply{max_supply: 2.170272727272727, current_output: 2.3873,level: 23}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:50337073}) CREATE (b)-[r:Supply{max_supply: 2.170272727272727, current_output: 2.3873,level: 23}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:50337073}) CREATE (b)-[r:Supply{max_supply: 3.0383909090909085, current_output: 3.34223,level: 23}]->(g);
CREATE (n: Building {id: 16782644, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:16782644}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.47081435235899705, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16782644}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1272167848938002, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16782644}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.0242203485826153, level: 1}]->(b);
CREATE (n: Building {id: 16782651, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:16782651}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.412443057076991, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16782651}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.3816503546814003, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16782651}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.0726610457478465, level: 3}]->(b);
CREATE (n: Building {id: 16782690, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:16782690}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.354071761794985, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16782690}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.636083924469001, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16782690}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.121101742913077, level: 5}]->(b);
CREATE (n: Building {id: 33559916, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33559916}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 44.67046992915558, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33559916}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 14.978887181129032, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:33559916}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 24.05326314174395,level: 1}]->(g);
CREATE (n: Building {id: 16782703, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:16782703}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.412443057076991, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16782703}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.3816503546814003, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16782703}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.0726610457478465, level: 3}]->(b);
CREATE (n: Building {id: 33559925, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 16782727, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:16782727}) CREATE (g)-[r:Demand{max_demand: 18.6166, current_input: 8.793230610043027, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782727}) CREATE (g)-[r:Demand{max_demand: 9.3083, current_input: 2.273154462207897, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16782727}) CREATE (b)-[r:Supply{max_supply: 13.962445454545453, current_output: 5.00232569692194,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:16782727}) CREATE (b)-[r:Supply{max_supply: 13.962445454545453, current_output: 5.00232569692194,level: 1}]->(g);
CREATE (n: Building {id: 50337203, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:50337203}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.354071761794985, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:50337203}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.636083924469001, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:50337203}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.121101742913077, level: 5}]->(b);
CREATE (n: Building {id: 16782778, name:"building_subsistence_orchardslevel", level:11});
MATCH (g: Goods{code: 7}), (b: Building{id:16782778}) CREATE (b)-[r:Supply{max_supply: 1.9849454545454543, current_output: 2.18344,level: 11}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:16782778}) CREATE (b)-[r:Supply{max_supply: 0.9924727272727272, current_output: 1.09172,level: 11}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:16782778}) CREATE (b)-[r:Supply{max_supply: 2.9774181818181815, current_output: 3.27516,level: 11}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:16782778}) CREATE (b)-[r:Supply{max_supply: 1.9849454545454543, current_output: 2.18344,level: 11}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:16782778}) CREATE (b)-[r:Supply{max_supply: 1.9849454545454543, current_output: 2.18344,level: 11}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:16782778}) CREATE (b)-[r:Supply{max_supply: 1.9849454545454543, current_output: 2.18344,level: 11}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:16782778}) CREATE (b)-[r:Supply{max_supply: 5.279963636363636, current_output: 5.80796,level: 11}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16782778}) CREATE (b)-[r:Supply{max_supply: 2.7789272727272727, current_output: 3.05682,level: 11}]->(g);
CREATE (n: Building {id: 16782787, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:16782787}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.276371504960702, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782787}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.884188998923552, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782787}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 42.32112100776851,level: 2}]->(g);
CREATE (n: Building {id: 5638, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:5638}) CREATE (g)-[r:Demand{max_demand: 18.799999999999997, current_input: 8.879856443647546, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:5638}) CREATE (g)-[r:Demand{max_demand: 9.399999999999999, current_input: 2.2955482681858377, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:5638}) CREATE (b)-[r:Supply{max_supply: 14.099999999999998, current_output: 5.0516073675072075,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:5638}) CREATE (b)-[r:Supply{max_supply: 14.099999999999998, current_output: 5.0516073675072075,level: 1}]->(g);
CREATE (n: Building {id: 83891725, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 16782864, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:16782864}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.47081435235899705, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16782864}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1272167848938002, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16782864}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.0242203485826153, level: 1}]->(b);
CREATE (n: Building {id: 16782867, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:16782867}) CREATE (g)-[r:Demand{max_demand: 25.513495049504954, current_input: 8.739538450622954, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782867}) CREATE (g)-[r:Demand{max_demand: 25.513495049504954, current_input: 9.256456738063743, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782867}) CREATE (b)-[r:Supply{max_supply: 102.054, current_output: 35.991997361056725,level: 2}]->(g);
CREATE (n: Building {id: 100668983, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 117446205, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:117446205}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.47081435235899705, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:117446205}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1272167848938002, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:117446205}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.0242203485826153, level: 1}]->(b);
CREATE (n: Building {id: 16782937, name:"building_subsistence_orchardslevel", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:16782937}) CREATE (b)-[r:Supply{max_supply: 2.2951727272727274, current_output: 2.52469,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:16782937}) CREATE (b)-[r:Supply{max_supply: 1.147581818181818, current_output: 1.26234,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:16782937}) CREATE (b)-[r:Supply{max_supply: 3.4427636363636362, current_output: 3.78704,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:16782937}) CREATE (b)-[r:Supply{max_supply: 2.2951727272727274, current_output: 2.52469,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:16782937}) CREATE (b)-[r:Supply{max_supply: 2.2951727272727274, current_output: 2.52469,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:16782937}) CREATE (b)-[r:Supply{max_supply: 2.2951727272727274, current_output: 2.52469,level: 9}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:16782937}) CREATE (b)-[r:Supply{max_supply: 6.105172727272727, current_output: 6.71569,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16782937}) CREATE (b)-[r:Supply{max_supply: 3.2132454545454543, current_output: 3.53457,level: 9}]->(g);
CREATE (n: Building {id: 16782952, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 21}), (b: Building{id:16782952}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:16782952}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 13.703961920652958, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:16782952}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 9.788544229037825,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:16782952}) CREATE (b)-[r:Supply{max_supply: 24.999999999999996, current_output: 4.8942721145189125,level: 1}]->(g);
CREATE (n: Building {id: 16782954, name:"building_barrackslevel", level:4});
MATCH (g: Goods{code: 0}), (b: Building{id:16782954}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.8832574094359882, level: 4}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16782954}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.508867139575201, level: 4}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16782954}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.096881394330461, level: 4}]->(b);
CREATE (n: Building {id: 50337426, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 16783012, name:"building_arms_industrylevel", level:3});
MATCH (g: Goods{code: 24}), (b: Building{id:16783012}) CREATE (g)-[r:Demand{max_demand: 56.47619642857143, current_input: 26.675559402603966, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16783012}) CREATE (g)-[r:Demand{max_demand: 28.238098214285714, current_input: 6.895948665177146, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16783012}) CREATE (b)-[r:Supply{max_supply: 42.357142857142854, current_output: 15.175294675439586,level: 3}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:16783012}) CREATE (b)-[r:Supply{max_supply: 42.357142857142854, current_output: 15.175294675439586,level: 3}]->(g);
CREATE (n: Building {id: 16783115, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:16783115}) CREATE (g)-[r:Demand{max_demand: 18.873199999999997, current_input: 8.914431203843025, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16783115}) CREATE (g)-[r:Demand{max_demand: 9.436599999999999, current_input: 2.304486253996008, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16783115}) CREATE (b)-[r:Supply{max_supply: 14.154899999999998, current_output: 5.071276391938141,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:16783115}) CREATE (b)-[r:Supply{max_supply: 14.154899999999998, current_output: 5.071276391938141,level: 1}]->(g);
CREATE (n: Building {id: 5925, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5925}) CREATE (g)-[r:Demand{max_demand: 17.5254, current_input: 39.14339268482116, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5925}) CREATE (g)-[r:Demand{max_demand: 35.0508, current_input: 13.125549470207938, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5925}) CREATE (g)-[r:Demand{max_demand: 43.8135, current_input: 20.694552675199564, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5925}) CREATE (g)-[r:Demand{max_demand: 8.7627, current_input: 3.1791627646955805, level: 1}]->(b);
CREATE (n: Building {id: 5940, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5940}) CREATE (g)-[r:Demand{max_demand: 17.5254, current_input: 39.14339268482116, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5940}) CREATE (g)-[r:Demand{max_demand: 35.0508, current_input: 13.125549470207938, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5940}) CREATE (g)-[r:Demand{max_demand: 43.8135, current_input: 20.694552675199564, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5940}) CREATE (g)-[r:Demand{max_demand: 8.7627, current_input: 3.1791627646955805, level: 1}]->(b);
CREATE (n: Building {id: 5942, name:"building_subsistence_farmslevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:5942}) CREATE (b)-[r:Supply{max_supply: 0.3725, current_output: 0.40975,level: 2}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:5942}) CREATE (b)-[r:Supply{max_supply: 0.07449999999999998, current_output: 0.08195,level: 2}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:5942}) CREATE (b)-[r:Supply{max_supply: 0.07449999999999998, current_output: 0.08195,level: 2}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:5942}) CREATE (b)-[r:Supply{max_supply: 0.07449999999999998, current_output: 0.08195,level: 2}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:5942}) CREATE (b)-[r:Supply{max_supply: 0.07449999999999998, current_output: 0.08195,level: 2}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:5942}) CREATE (b)-[r:Supply{max_supply: 0.07449999999999998, current_output: 0.08195,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:5942}) CREATE (b)-[r:Supply{max_supply: 0.10429999999999999, current_output: 0.11473,level: 2}]->(g);
CREATE (n: Building {id: 201332536, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 5970, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:5970}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.276371504960702, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5970}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.884188998923552, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5970}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 42.32112100776851,level: 2}]->(g);
CREATE (n: Building {id: 67114881, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 16783245, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16783245}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 11.234165385846772, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:16783245}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.03399853768452, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:16783245}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.22552116538372,level: 1}]->(g);
CREATE (n: Building {id: 6033, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 6034, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 6056, name:"building_subsistence_farmslevel", level:11});
MATCH (g: Goods{code: 7}), (b: Building{id:6056}) CREATE (b)-[r:Supply{max_supply: 18.3678, current_output: 20.20458,level: 11}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6056}) CREATE (b)-[r:Supply{max_supply: 3.6735545454545453, current_output: 4.04091,level: 11}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6056}) CREATE (b)-[r:Supply{max_supply: 3.6735545454545453, current_output: 4.04091,level: 11}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6056}) CREATE (b)-[r:Supply{max_supply: 3.6735545454545453, current_output: 4.04091,level: 11}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6056}) CREATE (b)-[r:Supply{max_supply: 3.6735545454545453, current_output: 4.04091,level: 11}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6056}) CREATE (b)-[r:Supply{max_supply: 3.6735545454545453, current_output: 4.04091,level: 11}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6056}) CREATE (b)-[r:Supply{max_supply: 5.142981818181818, current_output: 5.65728,level: 11}]->(g);
CREATE (n: Building {id: 6057, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:6057}) CREATE (g)-[r:Demand{max_demand: 1.2231999999999998, current_input: 0.5777574681845573, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:6057}) CREATE (g)-[r:Demand{max_demand: 0.6115999999999999, current_input: 0.14935716178962322, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:6057}) CREATE (b)-[r:Supply{max_supply: 0.9173999999999999, current_output: 0.3286769219114264,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:6057}) CREATE (b)-[r:Supply{max_supply: 0.9173999999999999, current_output: 0.3286769219114264,level: 1}]->(g);
CREATE (n: Building {id: 67114965, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 6132, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6132}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.872360897641129, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6132}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:6132}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8230839258419729, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6132}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.57147590233502,level: 1}]->(g);
CREATE (n: Building {id: 6135, name:"building_barrackslevel", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:6135}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.9416287047179941, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:6135}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.2544335697876003, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:6135}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.0484406971652307, level: 2}]->(b);
CREATE (n: Building {id: 16783359, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:16783359}) CREATE (b)-[r:Supply{max_supply: 20.848, current_output: 20.848,level: 1}]->(g);
CREATE (n: Building {id: 6144, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:6144}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.412443057076991, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:6144}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.3816503546814003, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:6144}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.0726610457478465, level: 3}]->(b);
CREATE (n: Building {id: 16783372, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16783372}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1683235072537206, level: 1}]->(b);
CREATE (n: Building {id: 6171, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:6171}) CREATE (b)-[r:Supply{max_supply: 41.66649504950495, current_output: 42.08316,level: 2}]->(g);
CREATE (n: Building {id: 6175, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:6175}) CREATE (g)-[r:Demand{max_demand: 14.3478, current_input: 5.205472230625177, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:6175}) CREATE (g)-[r:Demand{max_demand: 4.7826, current_input: 0.6079426414488539, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6175}) CREATE (b)-[r:Supply{max_supply: 71.739, current_output: 17.57325038742935,level: 1}]->(g);
CREATE (n: Building {id: 16783404, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:16783404}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.47081435235899705, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16783404}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.1272167848938002, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:16783404}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.0242203485826153, level: 1}]->(b);
CREATE (n: Building {id: 16783411, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783411}) CREATE (b)-[r:Supply{max_supply: 35.3724, current_output: 44.2155,level: 1}]->(g);
CREATE (n: Building {id: 16783435, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:16783435}) CREATE (b)-[r:Supply{max_supply: 22.142, current_output: 22.142,level: 1}]->(g);
CREATE (n: Building {id: 6224, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 16783446, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783446}) CREATE (b)-[r:Supply{max_supply: 35.3724, current_output: 44.2155,level: 1}]->(g);
CREATE (n: Building {id: 50337904, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:50337904}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 16783473, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:16783473}) CREATE (b)-[r:Supply{max_supply: 44.367495049504946, current_output: 44.81117,level: 2}]->(g);
CREATE (n: Building {id: 6261, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6261}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.872360897641129, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6261}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:6261}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8230839258419729, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6261}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.57147590233502,level: 1}]->(g);
CREATE (n: Building {id: 33560698, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 6277, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:6277}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 6292, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 16783528, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16783528}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1683235072537206, level: 1}]->(b);
CREATE (n: Building {id: 16783541, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16783541}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1683235072537206, level: 1}]->(b);
CREATE (n: Building {id: 100669669, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:100669669}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.872360897641129, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:100669669}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.4254571683202335, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:100669669}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8230839258419729, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:100669669}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 20.57147590233502,level: 1}]->(g);
CREATE (n: Building {id: 6445, name:"building_subsistence_fishing_villageslevel", level:43});
MATCH (g: Goods{code: 7}), (b: Building{id:6445}) CREATE (b)-[r:Supply{max_supply: 0.0025727272727272725, current_output: 0.00283,level: 43}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:6445}) CREATE (b)-[r:Supply{max_supply: 0.010318181818181818, current_output: 0.01135,level: 43}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6445}) CREATE (b)-[r:Supply{max_supply: 0.0012818181818181817, current_output: 0.00141,level: 43}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6445}) CREATE (b)-[r:Supply{max_supply: 0.003863636363636364, current_output: 0.00425,level: 43}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6445}) CREATE (b)-[r:Supply{max_supply: 0.0025727272727272725, current_output: 0.00283,level: 43}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6445}) CREATE (b)-[r:Supply{max_supply: 0.0025727272727272725, current_output: 0.00283,level: 43}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6445}) CREATE (b)-[r:Supply{max_supply: 0.0025727272727272725, current_output: 0.00283,level: 43}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6445}) CREATE (b)-[r:Supply{max_supply: 0.0036090909090909086, current_output: 0.00397,level: 43}]->(g);
CREATE (n: Building {id: 6446, name:"building_subsistence_fishing_villageslevel", level:19});
MATCH (g: Goods{code: 7}), (b: Building{id:6446}) CREATE (b)-[r:Supply{max_supply: 0.0009454545454545453, current_output: 0.00104,level: 19}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:6446}) CREATE (b)-[r:Supply{max_supply: 0.0037999999999999996, current_output: 0.00418,level: 19}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6446}) CREATE (b)-[r:Supply{max_supply: 0.00047272727272727267, current_output: 0.00052,level: 19}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6446}) CREATE (b)-[r:Supply{max_supply: 0.001418181818181818, current_output: 0.00156,level: 19}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6446}) CREATE (b)-[r:Supply{max_supply: 0.0009454545454545453, current_output: 0.00104,level: 19}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6446}) CREATE (b)-[r:Supply{max_supply: 0.0009454545454545453, current_output: 0.00104,level: 19}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6446}) CREATE (b)-[r:Supply{max_supply: 0.0009454545454545453, current_output: 0.00104,level: 19}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6446}) CREATE (b)-[r:Supply{max_supply: 0.001327272727272727, current_output: 0.00146,level: 19}]->(g);
CREATE (n: Building {id: 6447, name:"building_subsistence_fishing_villageslevel", level:100});
MATCH (g: Goods{code: 7}), (b: Building{id:6447}) CREATE (b)-[r:Supply{max_supply: 1.801, current_output: 1.9811,level: 100}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:6447}) CREATE (b)-[r:Supply{max_supply: 7.204, current_output: 7.9244,level: 100}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6447}) CREATE (b)-[r:Supply{max_supply: 0.9005, current_output: 0.99055,level: 100}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6447}) CREATE (b)-[r:Supply{max_supply: 2.7015, current_output: 2.97165,level: 100}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6447}) CREATE (b)-[r:Supply{max_supply: 1.801, current_output: 1.9811,level: 100}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6447}) CREATE (b)-[r:Supply{max_supply: 1.801, current_output: 1.9811,level: 100}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6447}) CREATE (b)-[r:Supply{max_supply: 1.801, current_output: 1.9811,level: 100}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6447}) CREATE (b)-[r:Supply{max_supply: 2.5214, current_output: 2.77354,level: 100}]->(g);
CREATE (n: Building {id: 6448, name:"building_subsistence_fishing_villageslevel", level:24});
MATCH (g: Goods{code: 7}), (b: Building{id:6448}) CREATE (b)-[r:Supply{max_supply: 0.0022727272727272726, current_output: 0.0025,level: 24}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:6448}) CREATE (b)-[r:Supply{max_supply: 0.009118181818181819, current_output: 0.01003,level: 24}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6448}) CREATE (b)-[r:Supply{max_supply: 0.0011363636363636363, current_output: 0.00125,level: 24}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6448}) CREATE (b)-[r:Supply{max_supply: 0.0034181818181818176, current_output: 0.00376,level: 24}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6448}) CREATE (b)-[r:Supply{max_supply: 0.0022727272727272726, current_output: 0.0025,level: 24}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6448}) CREATE (b)-[r:Supply{max_supply: 0.0022727272727272726, current_output: 0.0025,level: 24}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6448}) CREATE (b)-[r:Supply{max_supply: 0.0022727272727272726, current_output: 0.0025,level: 24}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6448}) CREATE (b)-[r:Supply{max_supply: 0.0031909090909090906, current_output: 0.00351,level: 24}]->(g);
CREATE (n: Building {id: 6449, name:"building_subsistence_pastureslevel", level:2});
CREATE (n: Building {id: 6450, name:"building_subsistence_pastureslevel", level:2});
