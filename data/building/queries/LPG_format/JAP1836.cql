CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:14.928503959854776, pop_demand:2697.6855993758427});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:15.988704914281108, pop_demand:311.3466392379647});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:17.183864417311497, pop_demand:270.61876890331564});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:21.38947556357373, pop_demand:298.2910640693691});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:25.72446202194884, pop_demand:673.4275991153916});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:25.68507268257546, pop_demand:589.9017565217957});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:33.44487363374719, pop_demand:42.59382598810693});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:13.386849729540023, pop_demand:255.43991666666676});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:52.074269228137894, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:32.25651373968781, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:27.787686177781996, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:24.563106796116504, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:24.422270603993333, pop_demand:19.229694138657777});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:27.847218007966408, pop_demand:28.36768149661944});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:83.18985740932156, pop_demand:159.20960362917452});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:43.306326349734206, pop_demand:122.56058272934494});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:43.270747935972366, pop_demand:146.80426058368226});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:23.22581535664309, pop_demand:714.0143403451674});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:33.82792864914794, pop_demand:244.9415350000001});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:31.23099425093683, pop_demand:67.32797089318926});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:33.36846235958461, pop_demand:294.81652112851947});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:87.5, pop_demand:0.955356890084224});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:75.67931986104479, pop_demand:184.1703526114868});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:76.20936172018757, pop_demand:60.58986815447622});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 2616, name:"building_barrackslevel", level:2});
CREATE (n: Building {id: 2617, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2617}) CREATE (g)-[r:Demand{max_demand: 29.7, current_input: 25.15276680345371, level: 3}]->(b);
CREATE (n: Building {id: 2618, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2618}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 156.0,level: 5}]->(g);
CREATE (n: Building {id: 2619, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2619}) CREATE (b)-[r:Supply{max_supply: 34.86, current_output: 34.86,level: 1}]->(g);
CREATE (n: Building {id: 2620, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:2620}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 76.194,level: 3}]->(g);
CREATE (n: Building {id: 2621, name:"building_barrackslevel", level:15});
CREATE (n: Building {id: 2622, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2622}) CREATE (g)-[r:Demand{max_demand: 29.7, current_input: 25.15276680345371, level: 3}]->(b);
CREATE (n: Building {id: 2623, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2623}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 27.22104887285254, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2623}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 25.2984, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2623}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 9.536841478808757,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2623}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 23.84210369702189,level: 1}]->(g);
CREATE (n: Building {id: 2624, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:2624}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 18.206697368634895, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2624}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 150.0,level: 3}]->(g);
CREATE (n: Building {id: 2625, name:"building_whaling_stationlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2625}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 12.137798245756597, level: 2}]->(b);
MATCH (g: Goods{code: 28}), (b: Building{id:2625}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2625}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 2}]->(g);
CREATE (n: Building {id: 2626, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2626}) CREATE (b)-[r:Supply{max_supply: 69.72, current_output: 70.4172,level: 2}]->(g);
CREATE (n: Building {id: 2627, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2627}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 2628, name:"building_tea_plantationlevel", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2628}) CREATE (b)-[r:Supply{max_supply: 79.68, current_output: 82.0704,level: 4}]->(g);
CREATE (n: Building {id: 2629, name:"building_portlevel", level:1});
CREATE (n: Building {id: 2630, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2631, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:2631}) CREATE (g)-[r:Demand{max_demand: 49.49999999999999, current_input: 41.92127800575618, level: 5}]->(b);
CREATE (n: Building {id: 2632, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2632}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 30.778412099463253, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2632}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 68.05262218213134, level: 1}]->(b);
CREATE (n: Building {id: 2633, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2633}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 54.44209774570508, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2633}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 72.5894636609401,level: 2}]->(g);
CREATE (n: Building {id: 2634, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2634}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 24.622729679570604, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2634}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 36.29473183047005, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2634}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 41.2, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2634}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 48.45613826269585,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2634}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 38.76491061015668,level: 2}]->(g);
CREATE (n: Building {id: 2635, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2635}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 41.2,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2635}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 41.2,level: 4}]->(g);
CREATE (n: Building {id: 2636, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2636}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2636}) CREATE (b)-[r:Supply{max_supply: 9.959999999999999, current_output: 10.0596,level: 2}]->(g);
CREATE (n: Building {id: 2637, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2637}) CREATE (b)-[r:Supply{max_supply: 39.839999999999996, current_output: 40.2384,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2637}) CREATE (b)-[r:Supply{max_supply: 11.952, current_output: 12.07152,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2637}) CREATE (b)-[r:Supply{max_supply: 17.928, current_output: 18.10728,level: 2}]->(g);
CREATE (n: Building {id: 2638, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2638}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 2639, name:"building_tea_plantationlevel", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2639}) CREATE (b)-[r:Supply{max_supply: 79.68, current_output: 82.0704,level: 4}]->(g);
CREATE (n: Building {id: 2640, name:"building_barrackslevel", level:15});
CREATE (n: Building {id: 2641, name:"building_government_administrationlevel", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:2641}) CREATE (g)-[r:Demand{max_demand: 69.3, current_input: 58.68978920805865, level: 7}]->(b);
CREATE (n: Building {id: 2642, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2642}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 30.778412099463253, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2642}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 68.05262218213134, level: 1}]->(b);
CREATE (n: Building {id: 2643, name:"building_textile_millslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:2643}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 147.73637807742358, level: 4}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2643}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 53.9168, level: 4}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2643}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 100.0,level: 4}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2643}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 80.0,level: 4}]->(g);
CREATE (n: Building {id: 2644, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2644}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 81.66314661855762, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2644}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 108.88419549141015,level: 3}]->(g);
CREATE (n: Building {id: 2645, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:2645}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 18.206697368634895, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2645}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 150.0,level: 3}]->(g);
CREATE (n: Building {id: 2646, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2646}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2646}) CREATE (b)-[r:Supply{max_supply: 9.959999999999999, current_output: 10.0596,level: 2}]->(g);
CREATE (n: Building {id: 2647, name:"building_rice_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2647}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.9552,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2647}) CREATE (b)-[r:Supply{max_supply: 17.928, current_output: 18.28656,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2647}) CREATE (b)-[r:Supply{max_supply: 26.892, current_output: 27.42984,level: 3}]->(g);
CREATE (n: Building {id: 2648, name:"building_silk_plantationlevel", level:3});
MATCH (g: Goods{code: 20}), (b: Building{id:2648}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.9552,level: 3}]->(g);
CREATE (n: Building {id: 2649, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2649}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 2650, name:"building_tea_plantationlevel", level:5});
MATCH (g: Goods{code: 40}), (b: Building{id:2650}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 103.584,level: 5}]->(g);
CREATE (n: Building {id: 2651, name:"building_barrackslevel", level:20});
CREATE (n: Building {id: 2652, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:2652}) CREATE (g)-[r:Demand{max_demand: 39.599999999999994, current_input: 33.53702240460494, level: 4}]->(b);
CREATE (n: Building {id: 2653, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2653}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 49.24545935914121, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2653}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 72.5894636609401, level: 2}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:2653}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 66.75789035166129,level: 2}]->(g);
CREATE (n: Building {id: 2654, name:"building_textile_millslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2654}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 73.8681890387118, level: 2}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2654}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 26.9584, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2654}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 2}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2654}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 2}]->(g);
CREATE (n: Building {id: 2655, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2655}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 27.22104887285254, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2655}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 25.2984, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2655}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 9.536841478808757,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2655}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 23.84210369702189,level: 1}]->(g);
CREATE (n: Building {id: 2656, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2656}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 12.137798245756597, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2656}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 100.0,level: 2}]->(g);
CREATE (n: Building {id: 2657, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2657}) CREATE (b)-[r:Supply{max_supply: 69.72, current_output: 70.4172,level: 2}]->(g);
CREATE (n: Building {id: 2658, name:"building_dye_plantationlevel", level:2});
MATCH (g: Goods{code: 21}), (b: Building{id:2658}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 2659, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2659}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 2660, name:"building_tea_plantationlevel", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2660}) CREATE (b)-[r:Supply{max_supply: 79.68, current_output: 82.0704,level: 4}]->(g);
CREATE (n: Building {id: 2661, name:"building_barrackslevel", level:15});
CREATE (n: Building {id: 2662, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2662}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 6.068899122878299, level: 1}]->(b);
CREATE (n: Building {id: 2663, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:2663}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 2664, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2664}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2664}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 2665, name:"building_portlevel", level:1});
CREATE (n: Building {id: 2666, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2666}) CREATE (g)-[r:Demand{max_demand: 29.7, current_input: 25.15276680345371, level: 3}]->(b);
CREATE (n: Building {id: 2667, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2667}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 54.44209774570508, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2667}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 72.5894636609401,level: 2}]->(g);
CREATE (n: Building {id: 2668, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2668}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 27.22104887285254, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2668}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 25.2984, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2668}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 9.536841478808757,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2668}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 23.84210369702189,level: 1}]->(g);
CREATE (n: Building {id: 2669, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2669}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 189.0,level: 6}]->(g);
CREATE (n: Building {id: 2670, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2670}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2670}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 2671, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2671}) CREATE (b)-[r:Supply{max_supply: 39.839999999999996, current_output: 40.2384,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2671}) CREATE (b)-[r:Supply{max_supply: 11.952, current_output: 12.07152,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2671}) CREATE (b)-[r:Supply{max_supply: 17.928, current_output: 18.10728,level: 2}]->(g);
CREATE (n: Building {id: 2672, name:"building_silk_plantationlevel", level:1});
MATCH (g: Goods{code: 20}), (b: Building{id:2672}) CREATE (b)-[r:Supply{max_supply: 19.92, current_output: 19.92,level: 1}]->(g);
CREATE (n: Building {id: 2673, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:2673}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 76.194,level: 3}]->(g);
CREATE (n: Building {id: 2674, name:"building_tea_plantationlevel", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:2674}) CREATE (b)-[r:Supply{max_supply: 19.92, current_output: 19.92,level: 1}]->(g);
CREATE (n: Building {id: 2675, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2676, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:2676}) CREATE (g)-[r:Demand{max_demand: 9.9, current_input: 8.384255601151237, level: 1}]->(b);
CREATE (n: Building {id: 2677, name:"building_glassworkslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2677}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 54.44209774570508, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2677}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 50.5968, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2677}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 19.073682957617514,level: 2}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2677}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 47.68420739404378,level: 2}]->(g);
CREATE (n: Building {id: 2678, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2678}) CREATE (b)-[r:Supply{max_supply: 34.86, current_output: 34.86,level: 1}]->(g);
CREATE (n: Building {id: 2679, name:"building_dye_plantationlevel", level:3});
MATCH (g: Goods{code: 21}), (b: Building{id:2679}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 76.194,level: 3}]->(g);
CREATE (n: Building {id: 2680, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:2680}) CREATE (b)-[r:Supply{max_supply: 24.9, current_output: 24.9,level: 1}]->(g);
CREATE (n: Building {id: 2681, name:"building_tea_plantationlevel", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:2681}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.9552,level: 3}]->(g);
CREATE (n: Building {id: 2682, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 3680, name:"building_subsistence_rice_paddieslevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 1.49264, current_output: 1.49264,level: 7}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 0.20354, current_output: 0.20354,level: 7}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 0.20354, current_output: 0.20354,level: 7}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 0.27139, current_output: 0.27139,level: 7}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 0.27139, current_output: 0.27139,level: 7}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 0.27139, current_output: 0.27139,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 0.40708, current_output: 0.40708,level: 7}]->(g);
CREATE (n: Building {id: 3681, name:"building_subsistence_rice_paddieslevel", level:76});
MATCH (g: Goods{code: 7}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 407.81752, current_output: 407.81752,level: 76}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 55.61148, current_output: 55.61148,level: 76}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 55.61148, current_output: 55.61148,level: 76}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 74.14864, current_output: 74.14864,level: 76}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 74.14864, current_output: 74.14864,level: 76}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 74.14864, current_output: 74.14864,level: 76}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 111.22296, current_output: 111.22296,level: 76}]->(g);
CREATE (n: Building {id: 3682, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3682}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.536841478808756, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3682}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 22.68420739404378,level: 1}]->(g);
CREATE (n: Building {id: 3683, name:"building_subsistence_rice_paddieslevel", level:122});
MATCH (g: Goods{code: 7}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 648.69596, current_output: 648.69596,level: 122}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 88.45854, current_output: 88.45854,level: 122}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 88.45854, current_output: 88.45854,level: 122}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 117.94472, current_output: 117.94472,level: 122}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 117.94472, current_output: 117.94472,level: 122}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 117.94472, current_output: 117.94472,level: 122}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3683}) CREATE (b)-[r:Supply{max_supply: 176.91708, current_output: 176.91708,level: 122}]->(g);
CREATE (n: Building {id: 3684, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3684}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.536841478808756, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3684}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 22.68420739404378,level: 1}]->(g);
CREATE (n: Building {id: 3685, name:"building_subsistence_rice_paddieslevel", level:139});
MATCH (g: Goods{code: 7}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 725.85452, current_output: 725.85452,level: 139}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 98.98016, current_output: 98.98016,level: 139}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 98.98016, current_output: 98.98016,level: 139}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 131.97355, current_output: 131.97355,level: 139}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 131.97355, current_output: 131.97355,level: 139}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 131.97355, current_output: 131.97355,level: 139}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3685}) CREATE (b)-[r:Supply{max_supply: 197.96032, current_output: 197.96032,level: 139}]->(g);
CREATE (n: Building {id: 3686, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3686}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.073682957617512, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3686}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 45.36841478808756,level: 2}]->(g);
CREATE (n: Building {id: 3687, name:"building_subsistence_rice_paddieslevel", level:174});
MATCH (g: Goods{code: 7}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 955.12428, current_output: 955.12428,level: 174}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 130.24422, current_output: 130.24422,level: 174}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 130.24422, current_output: 130.24422,level: 174}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 173.65896, current_output: 173.65896,level: 174}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 173.65896, current_output: 173.65896,level: 174}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 173.65896, current_output: 173.65896,level: 174}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 260.48844, current_output: 260.48844,level: 174}]->(g);
CREATE (n: Building {id: 3688, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3688}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 18.147365915235024, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3688}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 90.73682957617513,level: 4}]->(g);
CREATE (n: Building {id: 3689, name:"building_subsistence_rice_paddieslevel", level:90});
MATCH (g: Goods{code: 7}), (b: Building{id:3689}) CREATE (b)-[r:Supply{max_supply: 494.01, current_output: 494.01,level: 90}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3689}) CREATE (b)-[r:Supply{max_supply: 67.365, current_output: 67.365,level: 90}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3689}) CREATE (b)-[r:Supply{max_supply: 67.365, current_output: 67.365,level: 90}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3689}) CREATE (b)-[r:Supply{max_supply: 89.82, current_output: 89.82,level: 90}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3689}) CREATE (b)-[r:Supply{max_supply: 89.82, current_output: 89.82,level: 90}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3689}) CREATE (b)-[r:Supply{max_supply: 89.82, current_output: 89.82,level: 90}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3689}) CREATE (b)-[r:Supply{max_supply: 134.73, current_output: 134.73,level: 90}]->(g);
CREATE (n: Building {id: 3690, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3690}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.073682957617512, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3690}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 45.36841478808756,level: 2}]->(g);
CREATE (n: Building {id: 3691, name:"building_subsistence_orchardslevel", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 5.3578, current_output: 5.3578,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 2.6789, current_output: 2.6789,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 8.0367, current_output: 8.0367,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 5.3578, current_output: 5.3578,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 5.3578, current_output: 5.3578,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 5.3578, current_output: 5.3578,level: 14}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 17.78789, current_output: 17.78789,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3691}) CREATE (b)-[r:Supply{max_supply: 7.50092, current_output: 7.50092,level: 14}]->(g);
CREATE (n: Building {id: 3692, name:"building_subsistence_rice_paddieslevel", level:67});
MATCH (g: Goods{code: 7}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 313.82197, current_output: 313.82197,level: 67}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 42.7939, current_output: 42.7939,level: 67}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 42.7939, current_output: 42.7939,level: 67}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 57.05854, current_output: 57.05854,level: 67}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 57.05854, current_output: 57.05854,level: 67}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 57.05854, current_output: 57.05854,level: 67}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3692}) CREATE (b)-[r:Supply{max_supply: 85.58781, current_output: 85.58781,level: 67}]->(g);
CREATE (n: Building {id: 3693, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3693}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.073682957617512, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3693}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 45.36841478808756,level: 2}]->(g);
CREATE (n: Building {id: 3694, name:"building_subsistence_rice_paddieslevel", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 171.50672, current_output: 171.50672,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 23.38728, current_output: 23.38728,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 23.38728, current_output: 23.38728,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 31.18304, current_output: 31.18304,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 31.18304, current_output: 31.18304,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 31.18304, current_output: 31.18304,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 46.77456, current_output: 46.77456,level: 32}]->(g);
CREATE (n: Building {id: 3695, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3695}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.536841478808756, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 22.68420739404378,level: 1}]->(g);
CREATE (n: Building {id: 4502, name:"building_conscription_centerlevel", level:16});
CREATE (n: Building {id: 4503, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4504, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4505, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4506, name:"building_conscription_centerlevel", level:20});
CREATE (n: Building {id: 4507, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4508, name:"building_conscription_centerlevel", level:13});
CREATE (n: Building {id: 4509, name:"building_conscription_centerlevel", level:7});
