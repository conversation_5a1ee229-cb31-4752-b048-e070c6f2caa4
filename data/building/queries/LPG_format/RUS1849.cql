CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:92.82528992603078, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:108.29617158036923, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:88.1919400128392, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:17.83560560204617, pop_demand:8223.346437957554});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:26.2609529749262, pop_demand:453.91931240295816});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:14.721670624084622, pop_demand:441.04465746161515});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:25.790696417617266, pop_demand:1170.4994225383823});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:43.41684072336986, pop_demand:561.2514078657422});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:37.9332053324799, pop_demand:2753.6638468607493});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:38.338437721844286, pop_demand:2309.259586928735});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:41.55905661062878, pop_demand:124.57934068497944});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:18.630115809908, pop_demand:966.1833681449799});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:61.96948050190616, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:68.60222467404, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:59.073616294886534, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:63.74998235411661, pop_demand:18.798374243211523});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:66.98382770888215, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:118.91496750290602, pop_demand:382.91435406672997});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:24.834724173647725, pop_demand:236.62759069780347});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:45.1642488652178, pop_demand:144.76701428596664});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:26.71037746355107, pop_demand:2570.4587286772567});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:52.56540777872084, pop_demand:217.86428625790663});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:81.53410775478729, pop_demand:499.9183487420937});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:50.43332932463186, pop_demand:52.4506917323525});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:49.887844071117414, pop_demand:66.62045604349304});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:87.5, pop_demand:208.59260195884684});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:96.76141100209372, pop_demand:121.02265823036537});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:95.92014658147188, pop_demand:413.0241474417827});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:274.65116929228054, pop_demand:1.629073528253454});
CREATE (n: Building {id: 16777736, name:"building_subsistence_pastureslevel", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:16777736}) CREATE (b)-[r:Supply{max_supply: 1.1196727272727272, current_output: 1.23164,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:16777736}) CREATE (b)-[r:Supply{max_supply: 1.6795181818181817, current_output: 1.84747,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:16777736}) CREATE (b)-[r:Supply{max_supply: 0.5598363636363636, current_output: 0.61582,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:16777736}) CREATE (b)-[r:Supply{max_supply: 1.1196727272727272, current_output: 1.23164,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:16777736}) CREATE (b)-[r:Supply{max_supply: 1.1196727272727272, current_output: 1.23164,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:16777736}) CREATE (b)-[r:Supply{max_supply: 1.1196727272727272, current_output: 1.23164,level: 32}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:16777736}) CREATE (b)-[r:Supply{max_supply: 3.7173363636363637, current_output: 4.08907,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16777736}) CREATE (b)-[r:Supply{max_supply: 1.5675454545454544, current_output: 1.7243,level: 32}]->(g);
CREATE (n: Building {id: 33555011, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33555011}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33555011}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33555011}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33555011}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 16777888, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:16777888}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 9.77104760125746, level: 2}]->(b);
CREATE (n: Building {id: 50332552, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:50332552}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.5055220904606177, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50332552}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 2.0220883618424708,level: 1}]->(g);
CREATE (n: Building {id: 936, name:"building_government_administrationlevel", level:12});
MATCH (g: Goods{code: 14}), (b: Building{id:936}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 58.626285607544744, level: 12}]->(b);
CREATE (n: Building {id: 937, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:937}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 15.330698370994584, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:937}) CREATE (g)-[r:Demand{max_demand: 19.872, current_input: 12.226104079365419, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:937}) CREATE (g)-[r:Demand{max_demand: 24.84, current_input: 1.166521704560498, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:937}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 0.5022867490816698, level: 2}]->(b);
CREATE (n: Building {id: 938, name:"building_textile_millslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:938}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 246.87114929137817, level: 4}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:938}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:938}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 119.99999999999999,level: 4}]->(g);
CREATE (n: Building {id: 939, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:939}) CREATE (g)-[r:Demand{max_demand: 76.5, current_input: 47.066070957702024, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:939}) CREATE (g)-[r:Demand{max_demand: 38.25, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:939}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 7.844345159617005,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:939}) CREATE (b)-[r:Supply{max_supply: 63.75000000000001, current_output: 19.610862899042512,level: 3}]->(g);
CREATE (n: Building {id: 940, name:"building_arts_academylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:940}) CREATE (g)-[r:Demand{max_demand: 4.18249504950495, current_input: 2.043367911036827, level: 2}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:940}) CREATE (b)-[r:Supply{max_supply: 1.673, current_output: 0.8173481318451864,level: 2}]->(g);
CREATE (n: Building {id: 941, name:"building_naval_baselevel", level:13});
MATCH (g: Goods{code: 5}), (b: Building{id:941}) CREATE (g)-[r:Demand{max_demand: 23.05784, current_input: 15.095003330900056, level: 13}]->(b);
CREATE (n: Building {id: 942, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:942}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 33.6,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:942}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 50.4,level: 3}]->(g);
CREATE (n: Building {id: 943, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:943}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 14.369215864670354, level: 3}]->(b);
CREATE (n: Building {id: 944, name:"building_government_administrationlevel", level:14});
MATCH (g: Goods{code: 14}), (b: Building{id:944}) CREATE (g)-[r:Demand{max_demand: 140.0, current_input: 68.39733320880221, level: 14}]->(b);
CREATE (n: Building {id: 945, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:945}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 15.330698370994584, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:945}) CREATE (g)-[r:Demand{max_demand: 19.872, current_input: 12.226104079365419, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:945}) CREATE (g)-[r:Demand{max_demand: 24.84, current_input: 1.166521704560498, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:945}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 0.5022867490816698, level: 2}]->(b);
CREATE (n: Building {id: 946, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:946}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 36.9145654570212, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:946}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 1.8784568511441195, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:946}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 39.73225073373738,level: 2}]->(g);
CREATE (n: Building {id: 947, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:947}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.88552380062873, level: 2}]->(b);
CREATE (n: Building {id: 948, name:"building_textile_millslevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:948}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 370.30672393706726, level: 6}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:948}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.0, level: 6}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:948}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 180.0,level: 6}]->(g);
CREATE (n: Building {id: 949, name:"building_furniture_manufacturieslevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:949}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 92.57668098426682, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:949}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 73.8291309140424, level: 6}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:949}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 22.025793360249114, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:949}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 3.033132542763706, level: 6}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:949}) CREATE (b)-[r:Supply{max_supply: 270.0, current_output: 140.63245189064745,level: 6}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:949}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 62.50331195139887,level: 6}]->(g);
CREATE (n: Building {id: 950, name:"building_logging_camplevel", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:950}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 5.055220904606177, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:950}) CREATE (b)-[r:Supply{max_supply: 400.0, current_output: 40.441767236849415,level: 10}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:950}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 10.110441809212354,level: 10}]->(g);
CREATE (n: Building {id: 951, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:951}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.506440669593112, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:951}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.506440669593112, level: 20}]->(b);
CREATE (n: Building {id: 952, name:"building_saint_basils_cathedrallevel", level:1});
CREATE (n: Building {id: 953, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:953}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.753220334796556, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:953}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.753220334796556, level: 10}]->(b);
CREATE (n: Building {id: 954, name:"building_rye_farmlevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:954}) CREATE (g)-[r:Demand{max_demand: 6.000000000000001, current_input: 0.6066265085527414, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:954}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 18.198795256582237,level: 6}]->(g);
CREATE (n: Building {id: 955, name:"building_logging_camplevel", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:955}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 4.0441767236849415, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:955}) CREATE (b)-[r:Supply{max_supply: 319.99999999999994, current_output: 32.35341378947953,level: 8}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:955}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 8.088353447369883,level: 8}]->(g);
CREATE (n: Building {id: 956, name:"building_logging_camplevel", level:7});
MATCH (g: Goods{code: 33}), (b: Building{id:956}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 3.5386546332243243, level: 7}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:956}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 28.309237065794594,level: 7}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:956}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 7.0773092664486486,level: 7}]->(g);
CREATE (n: Building {id: 957, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:957}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 67.8,level: 4}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:957}) CREATE (b)-[r:Supply{max_supply: 32.0, current_output: 36.16,level: 4}]->(g);
CREATE (n: Building {id: 958, name:"building_barrackslevel", level:8});
MATCH (g: Goods{code: 1}), (b: Building{id:958}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 2.202576267837245, level: 8}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:958}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 2.202576267837245, level: 8}]->(b);
CREATE (n: Building {id: 959, name:"building_government_administrationlevel", level:12});
MATCH (g: Goods{code: 14}), (b: Building{id:959}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 58.626285607544744, level: 12}]->(b);
CREATE (n: Building {id: 960, name:"building_arts_academylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:960}) CREATE (g)-[r:Demand{max_demand: 0.005, current_input: 0.0024427619003143646, level: 1}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:960}) CREATE (b)-[r:Supply{max_supply: 0.002, current_output: 0.0009771047601257459,level: 1}]->(g);
CREATE (n: Building {id: 961, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:961}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 46.745716212837685, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:961}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 35.03970918897989, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:961}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.9392284255720598, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:961}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 4.593794976310175, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:961}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 38.7637306639756,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:961}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 51.68497421863413,level: 2}]->(g);
CREATE (n: Building {id: 962, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:962}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 193.5,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:962}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 32.25,level: 5}]->(g);
CREATE (n: Building {id: 963, name:"building_wheat_farmlevel", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:963}) CREATE (b)-[r:Supply{max_supply: 900.0, current_output: 1305.0,level: 30}]->(g);
CREATE (n: Building {id: 964, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:964}) CREATE (g)-[r:Demand{max_demand: 17.272, current_input: 4.755362162260612, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:964}) CREATE (g)-[r:Demand{max_demand: 17.272, current_input: 4.755362162260612, level: 20}]->(b);
CREATE (n: Building {id: 965, name:"building_textile_millslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:965}) CREATE (g)-[r:Demand{max_demand: 54.70559405940594, current_input: 84.40770548820709, level: 2}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:965}) CREATE (g)-[r:Demand{max_demand: 18.235198019801977, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:965}) CREATE (g)-[r:Demand{max_demand: 9.11759405940594, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:965}) CREATE (b)-[r:Supply{max_supply: 72.94079207920791, current_output: 24.31359735973597,level: 2}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:965}) CREATE (b)-[r:Supply{max_supply: 36.470396039603955, current_output: 12.156798679867984,level: 2}]->(g);
CREATE (n: Building {id: 966, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:966}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 186.98286485135074, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:966}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 9.18758995262035, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:966}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 32.7562769857861,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:966}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 131.0251079431444,level: 4}]->(g);
CREATE (n: Building {id: 967, name:"building_rye_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:967}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 45.2,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:967}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 67.8,level: 4}]->(g);
CREATE (n: Building {id: 968, name:"building_logging_camplevel", level:17});
MATCH (g: Goods{code: 33}), (b: Building{id:968}) CREATE (g)-[r:Demand{max_demand: 85.0, current_input: 8.5938755378305, level: 17}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:968}) CREATE (b)-[r:Supply{max_supply: 680.0, current_output: 68.751004302644,level: 17}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:968}) CREATE (b)-[r:Supply{max_supply: 170.0, current_output: 17.187751075661,level: 17}]->(g);
CREATE (n: Building {id: 969, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 1}), (b: Building{id:969}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 6.883050836991391, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:969}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 6.883050836991391, level: 25}]->(b);
CREATE (n: Building {id: 970, name:"building_food_industrylevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:970}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 70.11857431925654, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:970}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 52.55956378346984, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:970}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 1.4088426383580896, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:970}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 6.890692464465263, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:970}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 58.145595995963404,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:970}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 77.5274613279512,level: 3}]->(g);
CREATE (n: Building {id: 971, name:"building_rye_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:971}) CREATE (b)-[r:Supply{max_supply: 50.00000000000001, current_output: 57.0,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:971}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 85.5,level: 5}]->(g);
CREATE (n: Building {id: 972, name:"building_logging_camplevel", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:972}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 4.0441767236849415, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:972}) CREATE (b)-[r:Supply{max_supply: 319.99999999999994, current_output: 32.35341378947953,level: 8}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:972}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 8.088353447369883,level: 8}]->(g);
CREATE (n: Building {id: 973, name:"building_barrackslevel", level:12});
MATCH (g: Goods{code: 1}), (b: Building{id:973}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 3.3038644017558676, level: 12}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:973}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 3.3038644017558676, level: 12}]->(b);
CREATE (n: Building {id: 974, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:974}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 55.37184818553179, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:974}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 73.82913091404238,level: 3}]->(g);
CREATE (n: Building {id: 975, name:"building_logging_camplevel", level:12});
MATCH (g: Goods{code: 33}), (b: Building{id:975}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 6.066265085527412, level: 12}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:975}) CREATE (b)-[r:Supply{max_supply: 480.0, current_output: 48.5301206842193,level: 12}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:975}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 12.132530171054825,level: 12}]->(g);
CREATE (n: Building {id: 976, name:"building_rye_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:976}) CREATE (b)-[r:Supply{max_supply: 70.00000000000001, current_output: 81.2,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:976}) CREATE (b)-[r:Supply{max_supply: 105.0, current_output: 121.8,level: 7}]->(g);
CREATE (n: Building {id: 977, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:977}) CREATE (g)-[r:Demand{max_demand: 8.112, current_input: 2.2334123355869666, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:977}) CREATE (g)-[r:Demand{max_demand: 8.112, current_input: 2.2334123355869666, level: 10}]->(b);
CREATE (n: Building {id: 978, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:978}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.5055220904606177, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:978}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 4.0441767236849415,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:978}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 1.0110441809212354,level: 1}]->(g);
CREATE (n: Building {id: 979, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:979}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.579477243113567, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:979}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 95.79477243113568,level: 2}]->(g);
CREATE (n: Building {id: 980, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:980}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 13.093163393362133, level: 10}]->(b);
CREATE (n: Building {id: 981, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:981}) CREATE (g)-[r:Demand{max_demand: 3.992, current_input: 3.8241273154509363, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:981}) CREATE (b)-[r:Supply{max_supply: 39.92, current_output: 38.241273154509365,level: 1}]->(g);
CREATE (n: Building {id: 982, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:982}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 30.85889366142227, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:982}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 24.609710304680792, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:982}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 12.11432068212765,level: 1}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:982}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 16.152427576170197,level: 1}]->(g);
CREATE (n: Building {id: 983, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:983}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.789738621556784, level: 1}]->(b);
CREATE (n: Building {id: 984, name:"building_arms_industrylevel", level:6});
MATCH (g: Goods{code: 24}), (b: Building{id:984}) CREATE (g)-[r:Demand{max_demand: 119.106, current_input: 5.593387042809288, level: 6}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:984}) CREATE (g)-[r:Demand{max_demand: 59.553, current_input: 21.86170119971526, level: 6}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:984}) CREATE (b)-[r:Supply{max_supply: 89.32949523809523, current_output: 18.4937950549876,level: 6}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:984}) CREATE (b)-[r:Supply{max_supply: 89.32949523809523, current_output: 18.4937950549876,level: 6}]->(g);
CREATE (n: Building {id: 985, name:"building_logging_camplevel", level:15});
MATCH (g: Goods{code: 33}), (b: Building{id:985}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 7.582831356909266, level: 15}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:985}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 60.66265085527413,level: 15}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:985}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 15.165662713818532,level: 15}]->(g);
CREATE (n: Building {id: 986, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 1}), (b: Building{id:986}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 4.129830502194834, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:986}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 4.129830502194834, level: 15}]->(b);
CREATE (n: Building {id: 987, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:987}) CREATE (g)-[r:Demand{max_demand: 13.122, current_input: 8.590424502384897, level: 10}]->(b);
CREATE (n: Building {id: 988, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:988}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.579477243113567, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:988}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 95.79477243113568,level: 2}]->(g);
CREATE (n: Building {id: 989, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:989}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.579477243113567, level: 2}]->(b);
CREATE (n: Building {id: 990, name:"building_livestock_ranchlevel", level:7});
MATCH (g: Goods{code: 9}), (b: Building{id:990}) CREATE (b)-[r:Supply{max_supply: 210.0, current_output: 275.1,level: 7}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:990}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 45.85,level: 7}]->(g);
CREATE (n: Building {id: 991, name:"building_wheat_farmlevel", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:991}) CREATE (b)-[r:Supply{max_supply: 225.00000000000003, current_output: 312.75,level: 15}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:991}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 166.8,level: 15}]->(g);
CREATE (n: Building {id: 992, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:992}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.579477243113567, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:992}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 95.79477243113568,level: 2}]->(g);
CREATE (n: Building {id: 993, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:993}) CREATE (g)-[r:Demand{max_demand: 17.688, current_input: 4.869896128188149, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:993}) CREATE (g)-[r:Demand{max_demand: 17.688, current_input: 4.869896128188149, level: 20}]->(b);
CREATE (n: Building {id: 994, name:"building_rye_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:994}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 45.2,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:994}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 67.8,level: 4}]->(g);
CREATE (n: Building {id: 995, name:"building_logging_camplevel", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:995}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 5.055220904606177, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:995}) CREATE (b)-[r:Supply{max_supply: 400.0, current_output: 40.441767236849415,level: 10}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:995}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 10.110441809212354,level: 10}]->(g);
CREATE (n: Building {id: 996, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:996}) CREATE (g)-[r:Demand{max_demand: 18.212, current_input: 5.014164873731488, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:996}) CREATE (g)-[r:Demand{max_demand: 18.212, current_input: 5.014164873731488, level: 20}]->(b);
CREATE (n: Building {id: 997, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:997}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 55.37184818553179, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:997}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 73.82913091404238,level: 3}]->(g);
CREATE (n: Building {id: 998, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:998}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 2.5276104523030885, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:998}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 20.220883618424708,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:998}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 5.055220904606177,level: 5}]->(g);
CREATE (n: Building {id: 999, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:999}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.753220334796556, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:999}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.753220334796556, level: 10}]->(b);
CREATE (n: Building {id: 1000, name:"building_shipyardslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:1000}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 123.43557464568909, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1000}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 98.43884121872317, level: 4}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1000}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 48.45728272851059,level: 4}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:1000}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 64.60971030468079,level: 4}]->(g);
CREATE (n: Building {id: 1001, name:"building_livestock_ranchlevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:1001}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 153.6,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1001}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 25.6,level: 4}]->(g);
CREATE (n: Building {id: 1002, name:"building_wheat_farmlevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:1002}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 316.8,level: 8}]->(g);
CREATE (n: Building {id: 1003, name:"building_naval_baselevel", level:20});
MATCH (g: Goods{code: 5}), (b: Building{id:1003}) CREATE (g)-[r:Demand{max_demand: 32.462, current_input: 21.25151350376608, level: 20}]->(b);
CREATE (n: Building {id: 1004, name:"building_portlevel", level:3});
CREATE (n: Building {id: 1005, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1005}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 33.6,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1005}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 50.4,level: 3}]->(g);
CREATE (n: Building {id: 1006, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1006}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.0110441809212354, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1006}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 8.088353447369883,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1006}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 2.0220883618424708,level: 2}]->(g);
CREATE (n: Building {id: 1007, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:1007}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.753220334796556, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1007}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.753220334796556, level: 10}]->(b);
CREATE (n: Building {id: 1013, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1013}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1013}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 1014, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 1}), (b: Building{id:1014}) CREATE (g)-[r:Demand{max_demand: 13.7679, current_input: 3.7906062247445504, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1014}) CREATE (g)-[r:Demand{max_demand: 13.7679, current_input: 3.7906062247445504, level: 15}]->(b);
CREATE (n: Building {id: 1015, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1015}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 1.0110441809212354, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1015}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 8.088353447369883,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1015}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 2.0220883618424708,level: 2}]->(g);
CREATE (n: Building {id: 1016, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1016}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 61.71778732284454, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1016}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 49.219420609361585, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1016}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 24.2286413642553,level: 2}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:1016}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 32.304855152340394,level: 2}]->(g);
CREATE (n: Building {id: 1017, name:"building_iron_minelevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:1017}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 3.033132542763706, level: 6}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1017}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 12.132530171054825,level: 6}]->(g);
CREATE (n: Building {id: 1018, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1018}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.0110441809212354, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1018}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 8.088353447369883,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1018}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 2.0220883618424708,level: 2}]->(g);
CREATE (n: Building {id: 1019, name:"building_barrackslevel", level:11});
MATCH (g: Goods{code: 1}), (b: Building{id:1019}) CREATE (g)-[r:Demand{max_demand: 11.0, current_input: 3.028542368276212, level: 11}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1019}) CREATE (g)-[r:Demand{max_demand: 11.0, current_input: 3.028542368276212, level: 11}]->(b);
CREATE (n: Building {id: 1020, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1020}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1021, name:"building_rye_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1021}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 11.0,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1021}) CREATE (b)-[r:Supply{max_supply: 14.999999999999998, current_output: 16.5,level: 1}]->(g);
CREATE (n: Building {id: 1022, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:1022}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.546581696681066, level: 5}]->(b);
CREATE (n: Building {id: 1023, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1023}) CREATE (g)-[r:Demand{max_demand: 3.713, current_input: 1.0222707103099613, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1023}) CREATE (g)-[r:Demand{max_demand: 3.713, current_input: 1.0222707103099613, level: 5}]->(b);
CREATE (n: Building {id: 1024, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1024}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.789738621556784, level: 1}]->(b);
CREATE (n: Building {id: 1025, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1025}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 33.6,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1025}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 50.4,level: 3}]->(g);
CREATE (n: Building {id: 1026, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:1026}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 13.093163393362133, level: 10}]->(b);
CREATE (n: Building {id: 1027, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1027}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 14.369215864670354, level: 3}]->(b);
CREATE (n: Building {id: 1028, name:"building_rye_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:1028}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 45.2,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1028}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 67.8,level: 4}]->(g);
CREATE (n: Building {id: 1029, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1029}) CREATE (g)-[r:Demand{max_demand: 3.893, current_input: 1.0718286763362994, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1029}) CREATE (g)-[r:Demand{max_demand: 3.893, current_input: 1.0718286763362994, level: 5}]->(b);
CREATE (n: Building {id: 1030, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1030}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.789738621556784, level: 1}]->(b);
CREATE (n: Building {id: 1031, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1031}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 30.85889366142227, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1031}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 24.609710304680792, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1031}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.3419311200830375, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1031}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.0110441809212354, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1031}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 46.87748396354914,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:1031}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 20.834437317132952,level: 2}]->(g);
CREATE (n: Building {id: 1032, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1032}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 55.37184818553179, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1032}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 73.82913091404238,level: 3}]->(g);
CREATE (n: Building {id: 1033, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1033}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 22.2,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1033}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.3,level: 2}]->(g);
CREATE (n: Building {id: 1034, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1034}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.4572827285106, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1034}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.9392284255720598, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1034}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 19.86612536686869,level: 1}]->(g);
CREATE (n: Building {id: 1035, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1035}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.516566271381853, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1035}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 12.132530171054825,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1035}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 3.033132542763706,level: 3}]->(g);
CREATE (n: Building {id: 1043, name:"building_textile_millslevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:1043}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 370.30672393706726, level: 6}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1043}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.0, level: 6}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1043}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 180.0,level: 6}]->(g);
CREATE (n: Building {id: 1044, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1044}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 33.6,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1044}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 50.4,level: 3}]->(g);
CREATE (n: Building {id: 1045, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1045}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 36.9145654570212, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1045}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 49.2194206093616,level: 2}]->(g);
CREATE (n: Building {id: 1046, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1046}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 100.8,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1046}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 16.8,level: 3}]->(g);
CREATE (n: Building {id: 1047, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1047}) CREATE (g)-[r:Demand{max_demand: 25.5, current_input: 15.688690319234007, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1047}) CREATE (g)-[r:Demand{max_demand: 12.75, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1047}) CREATE (b)-[r:Supply{max_supply: 8.5, current_output: 2.6147817198723344,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:1047}) CREATE (b)-[r:Supply{max_supply: 21.25, current_output: 6.536954299680836,level: 1}]->(g);
CREATE (n: Building {id: 1119, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1119}) CREATE (g)-[r:Demand{max_demand: 4.25, current_input: 4.071277828323266, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1119}) CREATE (b)-[r:Supply{max_supply: 42.5, current_output: 40.712778283232666,level: 1}]->(g);
CREATE (n: Building {id: 33555751, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:33555751}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 11.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:33555751}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.9,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:33555751}) CREATE (b)-[r:Supply{max_supply: 5.999999999999999, current_output: 6.6,level: 1}]->(g);
CREATE (n: Building {id: 1420, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1420}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.789738621556784, level: 1}]->(b);
CREATE (n: Building {id: 2059, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2059}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 11.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2059}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.9,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2059}) CREATE (b)-[r:Supply{max_supply: 5.999999999999999, current_output: 6.6,level: 1}]->(g);
CREATE (n: Building {id: 2060, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2060}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 100.8,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2060}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 16.8,level: 3}]->(g);
CREATE (n: Building {id: 2061, name:"building_tea_plantationlevel", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:2061}) CREATE (b)-[r:Supply{max_supply: 39.37239603960396, current_output: 39.76612,level: 2}]->(g);
CREATE (n: Building {id: 2064, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:2064}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.2022088361842471, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2064}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 2.0220883618424708,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2064}) CREATE (b)-[r:Supply{max_supply: 18.0, current_output: 1.8198795256582239,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2064}) CREATE (b)-[r:Supply{max_supply: 12.0, current_output: 1.2132530171054825,level: 2}]->(g);
CREATE (n: Building {id: 2065, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2065}) CREATE (b)-[r:Supply{max_supply: 44.220495049504954, current_output: 44.6627,level: 2}]->(g);
CREATE (n: Building {id: 2066, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2066}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2066}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 11.1,level: 2}]->(g);
CREATE (n: Building {id: 2070, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2070}) CREATE (b)-[r:Supply{max_supply: 18.997999999999998, current_output: 21.08778,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2070}) CREATE (b)-[r:Supply{max_supply: 17.098198198198197, current_output: 18.979,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2070}) CREATE (b)-[r:Supply{max_supply: 11.398792792792792, current_output: 12.65266,level: 2}]->(g);
CREATE (n: Building {id: 2071, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2071}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 22.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2071}) CREATE (b)-[r:Supply{max_supply: 5.999999999999999, current_output: 6.6,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2071}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.9,level: 1}]->(g);
CREATE (n: Building {id: 2072, name:"building_tea_plantationlevel", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:2072}) CREATE (b)-[r:Supply{max_supply: 39.37239603960396, current_output: 39.76612,level: 2}]->(g);
CREATE (n: Building {id: 2073, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2073}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 46.28834049213341, level: 1}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2073}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2073}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 12.5,level: 1}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2073}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 10.0,level: 1}]->(g);
CREATE (n: Building {id: 2074, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:2074}) CREATE (g)-[r:Demand{max_demand: 7.815, current_input: 2.151641691643509, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:2074}) CREATE (g)-[r:Demand{max_demand: 7.815, current_input: 2.151641691643509, level: 10}]->(b);
CREATE (n: Building {id: 2075, name:"building_arms_industrylevel", level:10});
MATCH (g: Goods{code: 24}), (b: Building{id:2075}) CREATE (g)-[r:Demand{max_demand: 198.32999999999998, current_input: 9.31385868218533, level: 10}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2075}) CREATE (g)-[r:Demand{max_demand: 99.16499999999999, current_input: 36.40312997615172, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:2075}) CREATE (b)-[r:Supply{max_supply: 148.74749541284402, current_output: 30.795043538259023,level: 10}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:2075}) CREATE (b)-[r:Supply{max_supply: 148.74749541284402, current_output: 30.795043538259023,level: 10}]->(g);
CREATE (n: Building {id: 2076, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2076}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 46.745716212837685, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2076}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 35.03970918897989, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2076}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.9392284255720598, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:2076}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 4.593794976310175, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:2076}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 38.7637306639756,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2076}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 51.68497421863413,level: 2}]->(g);
CREATE (n: Building {id: 2077, name:"building_rye_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:2077}) CREATE (b)-[r:Supply{max_supply: 40.00000000000001, current_output: 45.2,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2077}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 67.8,level: 4}]->(g);
CREATE (n: Building {id: 2078, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:2078}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.506440669593112, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:2078}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.506440669593112, level: 20}]->(b);
CREATE (n: Building {id: 2079, name:"building_arms_industrylevel", level:7});
MATCH (g: Goods{code: 24}), (b: Building{id:2079}) CREATE (g)-[r:Demand{max_demand: 138.82959433962262, current_input: 6.519635065720575, level: 7}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2079}) CREATE (g)-[r:Demand{max_demand: 69.41479245283018, current_input: 25.481931245176956, level: 7}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:2079}) CREATE (b)-[r:Supply{max_supply: 104.12219811320755, current_output: 21.556313370494493,level: 7}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:2079}) CREATE (b)-[r:Supply{max_supply: 104.12219811320755, current_output: 21.556313370494493,level: 7}]->(g);
CREATE (n: Building {id: 2080, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2080}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 36.9145654570212, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2080}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 49.2194206093616,level: 2}]->(g);
CREATE (n: Building {id: 2081, name:"building_food_industrylevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2081}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 70.11857431925654, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2081}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 52.55956378346984, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2081}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 1.4088426383580896, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:2081}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 6.890692464465263, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:2081}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 58.145595995963404,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2081}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 77.5274613279512,level: 3}]->(g);
CREATE (n: Building {id: 2082, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2082}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 22.2,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2082}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.3,level: 2}]->(g);
CREATE (n: Building {id: 2083, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:2083}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.506440669593112, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:2083}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.506440669593112, level: 20}]->(b);
CREATE (n: Building {id: 2084, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:2084}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 2.527610452303089, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2084}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 20.22088361842471,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2084}) CREATE (b)-[r:Supply{max_supply: 50.00000000000001, current_output: 5.055220904606178,level: 5}]->(g);
CREATE (n: Building {id: 2085, name:"building_iron_minelevel", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:2085}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 5.055220904606176, level: 10}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2085}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 20.220883618424704,level: 10}]->(g);
CREATE (n: Building {id: 2086, name:"building_rye_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2086}) CREATE (b)-[r:Supply{max_supply: 50.00000000000001, current_output: 57.0,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2086}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 85.5,level: 5}]->(g);
CREATE (n: Building {id: 2087, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:2087}) CREATE (g)-[r:Demand{max_demand: 10.384, current_input: 2.8589439956527443, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:2087}) CREATE (g)-[r:Demand{max_demand: 10.384, current_input: 2.8589439956527443, level: 20}]->(b);
CREATE (n: Building {id: 2088, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2088}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2088}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 2791, name:"building_iron_minelevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:2791}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 3.033132542763706, level: 6}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2791}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 12.132530171054825,level: 6}]->(g);
CREATE (n: Building {id: 2792, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:2792}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.516566271381853, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2792}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 12.132530171054825,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2792}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 3.033132542763706,level: 3}]->(g);
CREATE (n: Building {id: 2793, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:2793}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.546581696681066, level: 5}]->(b);
CREATE (n: Building {id: 2794, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2794}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.789738621556784, level: 1}]->(b);
CREATE (n: Building {id: 2823, name:"building_subsistence_farmslevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:2823}) CREATE (b)-[r:Supply{max_supply: 2.8878, current_output: 3.17658,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2823}) CREATE (b)-[r:Supply{max_supply: 0.4812999999999999, current_output: 0.52943,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2823}) CREATE (b)-[r:Supply{max_supply: 0.4812999999999999, current_output: 0.52943,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2823}) CREATE (b)-[r:Supply{max_supply: 0.4812999999999999, current_output: 0.52943,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2823}) CREATE (b)-[r:Supply{max_supply: 0.4812999999999999, current_output: 0.52943,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2823}) CREATE (b)-[r:Supply{max_supply: 0.4812999999999999, current_output: 0.52943,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2823}) CREATE (b)-[r:Supply{max_supply: 0.6738181818181818, current_output: 0.7412,level: 4}]->(g);
CREATE (n: Building {id: 2832, name:"building_subsistence_farmslevel", level:41});
MATCH (g: Goods{code: 7}), (b: Building{id:2832}) CREATE (b)-[r:Supply{max_supply: 2.365281818181818, current_output: 2.60181,level: 41}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2832}) CREATE (b)-[r:Supply{max_supply: 0.3942090909090909, current_output: 0.43363,level: 41}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2832}) CREATE (b)-[r:Supply{max_supply: 0.3942090909090909, current_output: 0.43363,level: 41}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2832}) CREATE (b)-[r:Supply{max_supply: 0.3942090909090909, current_output: 0.43363,level: 41}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2832}) CREATE (b)-[r:Supply{max_supply: 0.3942090909090909, current_output: 0.43363,level: 41}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2832}) CREATE (b)-[r:Supply{max_supply: 0.3942090909090909, current_output: 0.43363,level: 41}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2832}) CREATE (b)-[r:Supply{max_supply: 0.5519, current_output: 0.60709,level: 41}]->(g);
CREATE (n: Building {id: 2836, name:"building_subsistence_farmslevel", level:47});
MATCH (g: Goods{code: 7}), (b: Building{id:2836}) CREATE (b)-[r:Supply{max_supply: 56.77646363636363, current_output: 62.45411,level: 47}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2836}) CREATE (b)-[r:Supply{max_supply: 9.462736363636363, current_output: 10.40901,level: 47}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2836}) CREATE (b)-[r:Supply{max_supply: 9.462736363636363, current_output: 10.40901,level: 47}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2836}) CREATE (b)-[r:Supply{max_supply: 9.462736363636363, current_output: 10.40901,level: 47}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2836}) CREATE (b)-[r:Supply{max_supply: 9.462736363636363, current_output: 10.40901,level: 47}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2836}) CREATE (b)-[r:Supply{max_supply: 9.462736363636363, current_output: 10.40901,level: 47}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2836}) CREATE (b)-[r:Supply{max_supply: 13.247836363636363, current_output: 14.57262,level: 47}]->(g);
CREATE (n: Building {id: 2837, name:"building_urban_centerlevel", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:2837}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 24.609710304680792, level: 8}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2837}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 8.37257141636695, level: 8}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2837}) CREATE (b)-[r:Supply{max_supply: 319.35999999999996, current_output: 131.6652686304226,level: 8}]->(g);
CREATE (n: Building {id: 2838, name:"building_subsistence_pastureslevel", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:2838}) CREATE (b)-[r:Supply{max_supply: 2.7830363636363633, current_output: 3.06134,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2838}) CREATE (b)-[r:Supply{max_supply: 4.174554545454545, current_output: 4.59201,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2838}) CREATE (b)-[r:Supply{max_supply: 1.3915181818181817, current_output: 1.53067,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2838}) CREATE (b)-[r:Supply{max_supply: 2.7830363636363633, current_output: 3.06134,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2838}) CREATE (b)-[r:Supply{max_supply: 2.7830363636363633, current_output: 3.06134,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2838}) CREATE (b)-[r:Supply{max_supply: 2.7830363636363633, current_output: 3.06134,level: 32}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2838}) CREATE (b)-[r:Supply{max_supply: 9.239690909090909, current_output: 10.16366,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2838}) CREATE (b)-[r:Supply{max_supply: 3.896254545454545, current_output: 4.28588,level: 32}]->(g);
CREATE (n: Building {id: 16780586, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:16780586}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.579477243113567, level: 2}]->(b);
CREATE (n: Building {id: 67112386, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:67112386}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:67112386}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:67112386}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:67112386}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 3545, name:"building_subsistence_farmslevel", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 37.627199999999995, current_output: 41.38992,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 6.271199999999999, current_output: 6.89832,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 6.271199999999999, current_output: 6.89832,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 6.271199999999999, current_output: 6.89832,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 6.271199999999999, current_output: 6.89832,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 6.271199999999999, current_output: 6.89832,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 8.779672727272727, current_output: 9.65764,level: 30}]->(g);
CREATE (n: Building {id: 3546, name:"building_subsistence_farmslevel", level:33});
MATCH (g: Goods{code: 7}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 55.390499999999996, current_output: 60.92955,level: 33}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 9.231745454545454, current_output: 10.15492,level: 33}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 9.231745454545454, current_output: 10.15492,level: 33}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 9.231745454545454, current_output: 10.15492,level: 33}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 9.231745454545454, current_output: 10.15492,level: 33}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 9.231745454545454, current_output: 10.15492,level: 33}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 12.924445454545452, current_output: 14.21689,level: 33}]->(g);
CREATE (n: Building {id: 3547, name:"building_subsistence_farmslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 87.39191818181818, current_output: 96.13111,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 14.565318181818181, current_output: 16.02185,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 14.565318181818181, current_output: 16.02185,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 14.565318181818181, current_output: 16.02185,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 14.565318181818181, current_output: 16.02185,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 14.565318181818181, current_output: 16.02185,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 20.39144545454545, current_output: 22.43059,level: 44}]->(g);
CREATE (n: Building {id: 3562, name:"building_subsistence_farmslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:3562}) CREATE (b)-[r:Supply{max_supply: 2.8571363636363634, current_output: 3.14285,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3562}) CREATE (b)-[r:Supply{max_supply: 0.4761818181818182, current_output: 0.5238,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3562}) CREATE (b)-[r:Supply{max_supply: 0.4761818181818182, current_output: 0.5238,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3562}) CREATE (b)-[r:Supply{max_supply: 0.4761818181818182, current_output: 0.5238,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3562}) CREATE (b)-[r:Supply{max_supply: 0.4761818181818182, current_output: 0.5238,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3562}) CREATE (b)-[r:Supply{max_supply: 0.4761818181818182, current_output: 0.5238,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3562}) CREATE (b)-[r:Supply{max_supply: 0.6666636363636363, current_output: 0.73333,level: 6}]->(g);
CREATE (n: Building {id: 16780825, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16780825}) CREATE (g)-[r:Demand{max_demand: 4.96303, current_input: 7.657680750422428, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16780825}) CREATE (g)-[r:Demand{max_demand: 9.92606, current_input: 6.106936526671996, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16780825}) CREATE (g)-[r:Demand{max_demand: 12.40758, current_input: 0.5826775914279688, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16780825}) CREATE (g)-[r:Demand{max_demand: 2.48151, current_input: 0.2508916245397855, level: 1}]->(b);
CREATE (n: Building {id: 3673, name:"building_subsistence_farmslevel", level:41});
MATCH (g: Goods{code: 7}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 90.06551818181818, current_output: 99.07207,level: 41}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 15.01091818181818, current_output: 16.51201,level: 41}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 15.01091818181818, current_output: 16.51201,level: 41}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 15.01091818181818, current_output: 16.51201,level: 41}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 15.01091818181818, current_output: 16.51201,level: 41}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 15.01091818181818, current_output: 16.51201,level: 41}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3673}) CREATE (b)-[r:Supply{max_supply: 21.01528181818182, current_output: 23.11681,level: 41}]->(g);
CREATE (n: Building {id: 3674, name:"building_subsistence_farmslevel", level:71});
MATCH (g: Goods{code: 7}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 185.01392727272727, current_output: 203.51532,level: 71}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 30.835654545454545, current_output: 33.91922,level: 71}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 30.835654545454545, current_output: 33.91922,level: 71}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 30.835654545454545, current_output: 33.91922,level: 71}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 30.835654545454545, current_output: 33.91922,level: 71}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 30.835654545454545, current_output: 33.91922,level: 71}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3674}) CREATE (b)-[r:Supply{max_supply: 43.16990909090909, current_output: 47.4869,level: 71}]->(g);
CREATE (n: Building {id: 3675, name:"building_subsistence_farmslevel", level:88});
MATCH (g: Goods{code: 7}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 164.06279999999998, current_output: 180.46908,level: 88}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 27.343799999999998, current_output: 30.07818,level: 88}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 27.343799999999998, current_output: 30.07818,level: 88}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 27.343799999999998, current_output: 30.07818,level: 88}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 27.343799999999998, current_output: 30.07818,level: 88}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 27.343799999999998, current_output: 30.07818,level: 88}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3675}) CREATE (b)-[r:Supply{max_supply: 38.28131818181818, current_output: 42.10945,level: 88}]->(g);
CREATE (n: Building {id: 3676, name:"building_subsistence_pastureslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 2.2577, current_output: 2.03193,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 3.3865444444444446, current_output: 3.04789,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 1.1288444444444443, current_output: 1.01596,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 2.2577, current_output: 2.03193,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 2.2577, current_output: 2.03193,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 2.2577, current_output: 2.03193,level: 20}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 7.495555555555556, current_output: 6.746,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3676}) CREATE (b)-[r:Supply{max_supply: 3.1607777777777777, current_output: 2.8447,level: 20}]->(g);
CREATE (n: Building {id: 3677, name:"building_subsistence_pastureslevel", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 2.8567444444444448, current_output: 2.57107,level: 15}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 4.285122222222222, current_output: 3.85661,level: 15}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 1.4283666666666668, current_output: 1.28553,level: 15}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 2.8567444444444448, current_output: 2.57107,level: 15}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 2.8567444444444448, current_output: 2.57107,level: 15}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 2.8567444444444448, current_output: 2.57107,level: 15}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 9.484399999999999, current_output: 8.53596,level: 15}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3677}) CREATE (b)-[r:Supply{max_supply: 3.9994444444444444, current_output: 3.5995,level: 15}]->(g);
CREATE (n: Building {id: 3678, name:"building_subsistence_farmslevel", level:26});
MATCH (g: Goods{code: 7}), (b: Building{id:3678}) CREATE (b)-[r:Supply{max_supply: 12.752999999999998, current_output: 14.0283,level: 26}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3678}) CREATE (b)-[r:Supply{max_supply: 2.1254999999999997, current_output: 2.33805,level: 26}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3678}) CREATE (b)-[r:Supply{max_supply: 2.1254999999999997, current_output: 2.33805,level: 26}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3678}) CREATE (b)-[r:Supply{max_supply: 2.1254999999999997, current_output: 2.33805,level: 26}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3678}) CREATE (b)-[r:Supply{max_supply: 2.1254999999999997, current_output: 2.33805,level: 26}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3678}) CREATE (b)-[r:Supply{max_supply: 2.1254999999999997, current_output: 2.33805,level: 26}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3678}) CREATE (b)-[r:Supply{max_supply: 2.9757, current_output: 3.27327,level: 26}]->(g);
CREATE (n: Building {id: 3679, name:"building_subsistence_farmslevel", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 0.0036, current_output: 0.00396,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 0.0006, current_output: 0.00066,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 0.0006, current_output: 0.00066,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 0.0006, current_output: 0.00066,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 0.0006, current_output: 0.00066,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 0.0006, current_output: 0.00066,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3679}) CREATE (b)-[r:Supply{max_supply: 0.0008363636363636363, current_output: 0.00092,level: 30}]->(g);
CREATE (n: Building {id: 3680, name:"building_subsistence_farmslevel", level:169});
MATCH (g: Goods{code: 7}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 258.71702727272725, current_output: 284.58873,level: 169}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 43.119499999999995, current_output: 47.43145,level: 169}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 43.119499999999995, current_output: 47.43145,level: 169}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 43.119499999999995, current_output: 47.43145,level: 169}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 43.119499999999995, current_output: 47.43145,level: 169}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 43.119499999999995, current_output: 47.43145,level: 169}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3680}) CREATE (b)-[r:Supply{max_supply: 60.3673, current_output: 66.40403,level: 169}]->(g);
CREATE (n: Building {id: 3681, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3681}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.076213788085099, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3681}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 15.381068940425497,level: 1}]->(g);
CREATE (n: Building {id: 3687, name:"building_subsistence_pastureslevel", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 6.9887999999999995, current_output: 7.68768,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 10.4832, current_output: 11.53152,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 3.4943999999999997, current_output: 3.84384,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 6.9887999999999995, current_output: 7.68768,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 6.9887999999999995, current_output: 7.68768,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 6.9887999999999995, current_output: 7.68768,level: 30}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 23.20280909090909, current_output: 25.52309,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3687}) CREATE (b)-[r:Supply{max_supply: 9.78431818181818, current_output: 10.76275,level: 30}]->(g);
CREATE (n: Building {id: 3693, name:"building_subsistence_pastureslevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:3693}) CREATE (b)-[r:Supply{max_supply: 2.2855, current_output: 2.51405,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3693}) CREATE (b)-[r:Supply{max_supply: 3.428245454545454, current_output: 3.77107,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3693}) CREATE (b)-[r:Supply{max_supply: 1.1427454545454545, current_output: 1.25702,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3693}) CREATE (b)-[r:Supply{max_supply: 2.2855, current_output: 2.51405,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3693}) CREATE (b)-[r:Supply{max_supply: 2.2855, current_output: 2.51405,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3693}) CREATE (b)-[r:Supply{max_supply: 2.2855, current_output: 2.51405,level: 10}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3693}) CREATE (b)-[r:Supply{max_supply: 7.587854545454546, current_output: 8.34664,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3693}) CREATE (b)-[r:Supply{max_supply: 3.1997, current_output: 3.51967,level: 10}]->(g);
CREATE (n: Building {id: 3694, name:"building_subsistence_pastureslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 4.171636363636363, current_output: 4.5888,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 6.257454545454546, current_output: 6.8832,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 2.0858181818181816, current_output: 2.2944,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 4.171636363636363, current_output: 4.5888,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 4.171636363636363, current_output: 4.5888,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 4.171636363636363, current_output: 4.5888,level: 44}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 13.849836363636362, current_output: 15.23482,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3694}) CREATE (b)-[r:Supply{max_supply: 5.840290909090909, current_output: 6.42432,level: 44}]->(g);
CREATE (n: Building {id: 3695, name:"building_subsistence_pastureslevel", level:26});
MATCH (g: Goods{code: 7}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 2.4434777777777774, current_output: 2.19913,level: 26}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 3.665211111111111, current_output: 3.29869,level: 26}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 1.2217333333333333, current_output: 1.09956,level: 26}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 2.4434777777777774, current_output: 2.19913,level: 26}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 2.4434777777777774, current_output: 2.19913,level: 26}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 2.4434777777777774, current_output: 2.19913,level: 26}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 8.112344444444444, current_output: 7.30111,level: 26}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3695}) CREATE (b)-[r:Supply{max_supply: 3.4208666666666665, current_output: 3.07878,level: 26}]->(g);
CREATE (n: Building {id: 3696, name:"building_subsistence_farmslevel", level:36});
MATCH (g: Goods{code: 7}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 27.49571818181818, current_output: 30.24529,level: 36}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 4.582618181818181, current_output: 5.04088,level: 36}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 4.582618181818181, current_output: 5.04088,level: 36}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 4.582618181818181, current_output: 5.04088,level: 36}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 4.582618181818181, current_output: 5.04088,level: 36}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 4.582618181818181, current_output: 5.04088,level: 36}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3696}) CREATE (b)-[r:Supply{max_supply: 6.415663636363636, current_output: 7.05723,level: 36}]->(g);
CREATE (n: Building {id: 3697, name:"building_subsistence_farmslevel", level:48});
MATCH (g: Goods{code: 7}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 60.020636363636356, current_output: 66.0227,level: 48}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 10.003436363636364, current_output: 11.00378,level: 48}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 10.003436363636364, current_output: 11.00378,level: 48}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 10.003436363636364, current_output: 11.00378,level: 48}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 10.003436363636364, current_output: 11.00378,level: 48}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 10.003436363636364, current_output: 11.00378,level: 48}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3697}) CREATE (b)-[r:Supply{max_supply: 14.00480909090909, current_output: 15.40529,level: 48}]->(g);
CREATE (n: Building {id: 3698, name:"building_subsistence_farmslevel", level:64});
MATCH (g: Goods{code: 7}), (b: Building{id:3698}) CREATE (b)-[r:Supply{max_supply: 161.27039999999997, current_output: 177.39744,level: 64}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3698}) CREATE (b)-[r:Supply{max_supply: 26.8784, current_output: 29.56624,level: 64}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3698}) CREATE (b)-[r:Supply{max_supply: 26.8784, current_output: 29.56624,level: 64}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3698}) CREATE (b)-[r:Supply{max_supply: 26.8784, current_output: 29.56624,level: 64}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3698}) CREATE (b)-[r:Supply{max_supply: 26.8784, current_output: 29.56624,level: 64}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3698}) CREATE (b)-[r:Supply{max_supply: 26.8784, current_output: 29.56624,level: 64}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3698}) CREATE (b)-[r:Supply{max_supply: 37.629754545454546, current_output: 41.39273,level: 64}]->(g);
CREATE (n: Building {id: 3701, name:"building_subsistence_farmslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 24.83843636363636, current_output: 27.32228,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 4.139736363636363, current_output: 4.55371,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 4.139736363636363, current_output: 4.55371,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 4.139736363636363, current_output: 4.55371,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 4.139736363636363, current_output: 4.55371,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 4.139736363636363, current_output: 4.55371,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3701}) CREATE (b)-[r:Supply{max_supply: 5.795627272727272, current_output: 6.37519,level: 44}]->(g);
CREATE (n: Building {id: 3702, name:"building_subsistence_pastureslevel", level:56});
MATCH (g: Goods{code: 7}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 3.883311111111111, current_output: 3.49498,level: 56}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 5.824977777777777, current_output: 5.24248,level: 56}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 1.9416555555555555, current_output: 1.74749,level: 56}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 3.883311111111111, current_output: 3.49498,level: 56}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 3.883311111111111, current_output: 3.49498,level: 56}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 3.883311111111111, current_output: 3.49498,level: 56}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 12.892622222222222, current_output: 11.60336,level: 56}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3702}) CREATE (b)-[r:Supply{max_supply: 5.436644444444444, current_output: 4.89298,level: 56}]->(g);
CREATE (n: Building {id: 3703, name:"building_subsistence_pastureslevel", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 3.0612777777777778, current_output: 2.75515,level: 16}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 4.591911111111111, current_output: 4.13272,level: 16}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 1.5306333333333333, current_output: 1.37757,level: 16}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 3.0612777777777778, current_output: 2.75515,level: 16}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 3.0612777777777778, current_output: 2.75515,level: 16}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 3.0612777777777778, current_output: 2.75515,level: 16}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 10.163444444444444, current_output: 9.1471,level: 16}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3703}) CREATE (b)-[r:Supply{max_supply: 4.285788888888889, current_output: 3.85721,level: 16}]->(g);
CREATE (n: Building {id: 3704, name:"building_subsistence_farmslevel", level:52});
MATCH (g: Goods{code: 7}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 69.83183636363636, current_output: 76.81502,level: 52}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 11.638636363636364, current_output: 12.8025,level: 52}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 11.638636363636364, current_output: 12.8025,level: 52}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 11.638636363636364, current_output: 12.8025,level: 52}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 11.638636363636364, current_output: 12.8025,level: 52}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 11.638636363636364, current_output: 12.8025,level: 52}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3704}) CREATE (b)-[r:Supply{max_supply: 16.294090909090908, current_output: 17.9235,level: 52}]->(g);
CREATE (n: Building {id: 3705, name:"building_subsistence_farmslevel", level:162});
MATCH (g: Goods{code: 7}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 258.8727545454545, current_output: 284.76003,level: 162}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 43.14545454545454, current_output: 47.46,level: 162}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 43.14545454545454, current_output: 47.46,level: 162}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 43.14545454545454, current_output: 47.46,level: 162}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 43.14545454545454, current_output: 47.46,level: 162}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 43.14545454545454, current_output: 47.46,level: 162}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3705}) CREATE (b)-[r:Supply{max_supply: 60.40363636363636, current_output: 66.444,level: 162}]->(g);
CREATE (n: Building {id: 3706, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3706}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.2286413642553, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3706}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 46.1432068212765,level: 3}]->(g);
CREATE (n: Building {id: 3707, name:"building_subsistence_farmslevel", level:206});
MATCH (g: Goods{code: 7}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 388.5798545454545, current_output: 427.43784,level: 206}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 64.76330909090908, current_output: 71.23964,level: 206}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 64.76330909090908, current_output: 71.23964,level: 206}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 64.76330909090908, current_output: 71.23964,level: 206}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 64.76330909090908, current_output: 71.23964,level: 206}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 64.76330909090908, current_output: 71.23964,level: 206}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3707}) CREATE (b)-[r:Supply{max_supply: 90.66862727272726, current_output: 99.73549,level: 206}]->(g);
CREATE (n: Building {id: 3708, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3708}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.2286413642553, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3708}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.139714281137607, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3708}) CREATE (b)-[r:Supply{max_supply: 119.75999999999999, current_output: 49.37447573640848,level: 3}]->(g);
CREATE (n: Building {id: 3709, name:"building_subsistence_farmslevel", level:110});
MATCH (g: Goods{code: 7}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 242.9955, current_output: 267.29505,level: 110}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 40.499245454545445, current_output: 44.54917,level: 110}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 40.499245454545445, current_output: 44.54917,level: 110}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 40.499245454545445, current_output: 44.54917,level: 110}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 40.499245454545445, current_output: 44.54917,level: 110}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 40.499245454545445, current_output: 44.54917,level: 110}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3709}) CREATE (b)-[r:Supply{max_supply: 56.69894545454545, current_output: 62.36884,level: 110}]->(g);
CREATE (n: Building {id: 3710, name:"building_subsistence_farmslevel", level:77});
MATCH (g: Goods{code: 7}), (b: Building{id:3710}) CREATE (b)-[r:Supply{max_supply: 131.54525454545453, current_output: 144.69978,level: 77}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3710}) CREATE (b)-[r:Supply{max_supply: 21.92420909090909, current_output: 24.11663,level: 77}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3710}) CREATE (b)-[r:Supply{max_supply: 21.92420909090909, current_output: 24.11663,level: 77}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3710}) CREATE (b)-[r:Supply{max_supply: 21.92420909090909, current_output: 24.11663,level: 77}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3710}) CREATE (b)-[r:Supply{max_supply: 21.92420909090909, current_output: 24.11663,level: 77}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3710}) CREATE (b)-[r:Supply{max_supply: 21.92420909090909, current_output: 24.11663,level: 77}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3710}) CREATE (b)-[r:Supply{max_supply: 30.693890909090907, current_output: 33.76328,level: 77}]->(g);
CREATE (n: Building {id: 3711, name:"building_subsistence_farmslevel", level:150});
MATCH (g: Goods{code: 7}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 261.3465, current_output: 287.48115,level: 150}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 43.55774545454545, current_output: 47.91352,level: 150}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 43.55774545454545, current_output: 47.91352,level: 150}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 43.55774545454545, current_output: 47.91352,level: 150}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 43.55774545454545, current_output: 47.91352,level: 150}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 43.55774545454545, current_output: 47.91352,level: 150}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3711}) CREATE (b)-[r:Supply{max_supply: 60.98084545454545, current_output: 67.07893,level: 150}]->(g);
CREATE (n: Building {id: 3712, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3712}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.152427576170198, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3712}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.093142854091738, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3712}) CREATE (b)-[r:Supply{max_supply: 79.84, current_output: 32.916317157605654,level: 2}]->(g);
CREATE (n: Building {id: 3713, name:"building_subsistence_farmslevel", level:174});
MATCH (g: Goods{code: 7}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 378.711, current_output: 416.5821,level: 174}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 63.1185, current_output: 69.43035,level: 174}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 63.1185, current_output: 69.43035,level: 174}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 63.1185, current_output: 69.43035,level: 174}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 63.1185, current_output: 69.43035,level: 174}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 63.1185, current_output: 69.43035,level: 174}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3713}) CREATE (b)-[r:Supply{max_supply: 88.3659, current_output: 97.20249,level: 174}]->(g);
CREATE (n: Building {id: 3714, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3714}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 21.533496516595697, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3714}) CREATE (b)-[r:Supply{max_supply: 175.0, current_output: 107.6674825829785,level: 7}]->(g);
CREATE (n: Building {id: 3715, name:"building_subsistence_farmslevel", level:27});
MATCH (g: Goods{code: 7}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 70.28450909090908, current_output: 77.31296,level: 27}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 11.714081818181818, current_output: 12.88549,level: 27}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 11.714081818181818, current_output: 12.88549,level: 27}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 11.714081818181818, current_output: 12.88549,level: 27}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 11.714081818181818, current_output: 12.88549,level: 27}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 11.714081818181818, current_output: 12.88549,level: 27}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3715}) CREATE (b)-[r:Supply{max_supply: 16.39971818181818, current_output: 18.03969,level: 27}]->(g);
CREATE (n: Building {id: 3717, name:"building_subsistence_farmslevel", level:55});
MATCH (g: Goods{code: 7}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 99.0825, current_output: 108.99075,level: 55}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 16.513745454545454, current_output: 18.16512,level: 55}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 16.513745454545454, current_output: 18.16512,level: 55}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 16.513745454545454, current_output: 18.16512,level: 55}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 16.513745454545454, current_output: 18.16512,level: 55}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 16.513745454545454, current_output: 18.16512,level: 55}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3717}) CREATE (b)-[r:Supply{max_supply: 23.119245454545453, current_output: 25.43117,level: 55}]->(g);
CREATE (n: Building {id: 3719, name:"building_subsistence_farmslevel", level:86});
MATCH (g: Goods{code: 7}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 167.09885454545454, current_output: 183.80874,level: 86}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 27.849809090909087, current_output: 30.63479,level: 86}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 27.849809090909087, current_output: 30.63479,level: 86}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 27.849809090909087, current_output: 30.63479,level: 86}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 27.849809090909087, current_output: 30.63479,level: 86}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 27.849809090909087, current_output: 30.63479,level: 86}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3719}) CREATE (b)-[r:Supply{max_supply: 38.98972727272727, current_output: 42.8887,level: 86}]->(g);
CREATE (n: Building {id: 3720, name:"building_subsistence_farmslevel", level:52});
MATCH (g: Goods{code: 7}), (b: Building{id:3720}) CREATE (b)-[r:Supply{max_supply: 14.829354545454546, current_output: 16.31229,level: 52}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3720}) CREATE (b)-[r:Supply{max_supply: 2.4715545454545453, current_output: 2.71871,level: 52}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3720}) CREATE (b)-[r:Supply{max_supply: 2.4715545454545453, current_output: 2.71871,level: 52}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3720}) CREATE (b)-[r:Supply{max_supply: 2.4715545454545453, current_output: 2.71871,level: 52}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3720}) CREATE (b)-[r:Supply{max_supply: 2.4715545454545453, current_output: 2.71871,level: 52}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3720}) CREATE (b)-[r:Supply{max_supply: 2.4715545454545453, current_output: 2.71871,level: 52}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3720}) CREATE (b)-[r:Supply{max_supply: 3.460181818181818, current_output: 3.8062,level: 52}]->(g);
CREATE (n: Building {id: 3721, name:"building_subsistence_farmslevel", level:124});
MATCH (g: Goods{code: 7}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 207.1854, current_output: 258.98175,level: 124}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 34.530896, current_output: 43.16362,level: 124}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 34.530896, current_output: 43.16362,level: 124}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 34.530896, current_output: 43.16362,level: 124}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 34.530896, current_output: 43.16362,level: 124}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 34.530896, current_output: 43.16362,level: 124}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3721}) CREATE (b)-[r:Supply{max_supply: 48.343256000000004, current_output: 60.42907,level: 124}]->(g);
CREATE (n: Building {id: 3722, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3722}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.076213788085099, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3722}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 15.381068940425497,level: 1}]->(g);
CREATE (n: Building {id: 3723, name:"building_subsistence_farmslevel", level:222});
MATCH (g: Goods{code: 7}), (b: Building{id:3723}) CREATE (b)-[r:Supply{max_supply: 457.77509999999995, current_output: 503.55261,level: 222}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3723}) CREATE (b)-[r:Supply{max_supply: 76.29584545454546, current_output: 83.92543,level: 222}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3723}) CREATE (b)-[r:Supply{max_supply: 76.29584545454546, current_output: 83.92543,level: 222}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3723}) CREATE (b)-[r:Supply{max_supply: 76.29584545454546, current_output: 83.92543,level: 222}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3723}) CREATE (b)-[r:Supply{max_supply: 76.29584545454546, current_output: 83.92543,level: 222}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3723}) CREATE (b)-[r:Supply{max_supply: 76.29584545454546, current_output: 83.92543,level: 222}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3723}) CREATE (b)-[r:Supply{max_supply: 106.81418181818181, current_output: 117.4956,level: 222}]->(g);
CREATE (n: Building {id: 3724, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3724}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.076213788085099, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3724}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 15.381068940425497,level: 1}]->(g);
CREATE (n: Building {id: 3725, name:"building_subsistence_farmslevel", level:56});
MATCH (g: Goods{code: 7}), (b: Building{id:3725}) CREATE (b)-[r:Supply{max_supply: 80.73071818181818, current_output: 88.80379,level: 56}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3725}) CREATE (b)-[r:Supply{max_supply: 13.45511818181818, current_output: 14.80063,level: 56}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3725}) CREATE (b)-[r:Supply{max_supply: 13.45511818181818, current_output: 14.80063,level: 56}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3725}) CREATE (b)-[r:Supply{max_supply: 13.45511818181818, current_output: 14.80063,level: 56}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3725}) CREATE (b)-[r:Supply{max_supply: 13.45511818181818, current_output: 14.80063,level: 56}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3725}) CREATE (b)-[r:Supply{max_supply: 13.45511818181818, current_output: 14.80063,level: 56}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3725}) CREATE (b)-[r:Supply{max_supply: 18.837163636363634, current_output: 20.72088,level: 56}]->(g);
CREATE (n: Building {id: 3726, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3726}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.076213788085099, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3726}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 15.381068940425497,level: 1}]->(g);
CREATE (n: Building {id: 3727, name:"building_subsistence_farmslevel", level:60});
MATCH (g: Goods{code: 7}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 120.36240000000001, current_output: 150.453,level: 60}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 20.0604, current_output: 25.0755,level: 60}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 20.0604, current_output: 25.0755,level: 60}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 20.0604, current_output: 25.0755,level: 60}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 20.0604, current_output: 25.0755,level: 60}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 20.0604, current_output: 25.0755,level: 60}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3727}) CREATE (b)-[r:Supply{max_supply: 28.08456, current_output: 35.1057,level: 60}]->(g);
CREATE (n: Building {id: 3728, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3728}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.152427576170198, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3728}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.093142854091738, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3728}) CREATE (b)-[r:Supply{max_supply: 79.84, current_output: 32.916317157605654,level: 2}]->(g);
CREATE (n: Building {id: 3729, name:"building_subsistence_farmslevel", level:65});
MATCH (g: Goods{code: 7}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 127.11854545454544, current_output: 139.8304,level: 65}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 21.18641818181818, current_output: 23.30506,level: 65}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 21.18641818181818, current_output: 23.30506,level: 65}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 21.18641818181818, current_output: 23.30506,level: 65}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 21.18641818181818, current_output: 23.30506,level: 65}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 21.18641818181818, current_output: 23.30506,level: 65}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3729}) CREATE (b)-[r:Supply{max_supply: 29.66099090909091, current_output: 32.62709,level: 65}]->(g);
CREATE (n: Building {id: 3730, name:"building_subsistence_farmslevel", level:176});
MATCH (g: Goods{code: 7}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 341.68991818181814, current_output: 375.85891,level: 176}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 56.94831818181817, current_output: 62.64315,level: 176}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 56.94831818181817, current_output: 62.64315,level: 176}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 56.94831818181817, current_output: 62.64315,level: 176}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 56.94831818181817, current_output: 62.64315,level: 176}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 56.94831818181817, current_output: 62.64315,level: 176}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3730}) CREATE (b)-[r:Supply{max_supply: 79.72764545454545, current_output: 87.70041,level: 176}]->(g);
CREATE (n: Building {id: 3731, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3731}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.2286413642553, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3731}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.139714281137607, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3731}) CREATE (b)-[r:Supply{max_supply: 119.75999999999999, current_output: 49.37447573640848,level: 3}]->(g);
CREATE (n: Building {id: 3732, name:"building_subsistence_farmslevel", level:73});
MATCH (g: Goods{code: 7}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 132.72932727272726, current_output: 146.00226,level: 73}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 22.121554545454543, current_output: 24.33371,level: 73}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 22.121554545454543, current_output: 24.33371,level: 73}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 22.121554545454543, current_output: 24.33371,level: 73}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 22.121554545454543, current_output: 24.33371,level: 73}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 22.121554545454543, current_output: 24.33371,level: 73}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3732}) CREATE (b)-[r:Supply{max_supply: 30.970172727272722, current_output: 34.06719,level: 73}]->(g);
CREATE (n: Building {id: 3733, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3733}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.076213788085099, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3733}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 15.381068940425497,level: 1}]->(g);
CREATE (n: Building {id: 3734, name:"building_subsistence_farmslevel", level:141});
MATCH (g: Goods{code: 7}), (b: Building{id:3734}) CREATE (b)-[r:Supply{max_supply: 257.7719636363636, current_output: 283.54916,level: 141}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3734}) CREATE (b)-[r:Supply{max_supply: 42.96199090909091, current_output: 47.25819,level: 141}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3734}) CREATE (b)-[r:Supply{max_supply: 42.96199090909091, current_output: 47.25819,level: 141}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3734}) CREATE (b)-[r:Supply{max_supply: 42.96199090909091, current_output: 47.25819,level: 141}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3734}) CREATE (b)-[r:Supply{max_supply: 42.96199090909091, current_output: 47.25819,level: 141}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3734}) CREATE (b)-[r:Supply{max_supply: 42.96199090909091, current_output: 47.25819,level: 141}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3734}) CREATE (b)-[r:Supply{max_supply: 60.146790909090896, current_output: 66.16147,level: 141}]->(g);
CREATE (n: Building {id: 3735, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3735}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.152427576170198, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3735}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 30.762137880850993,level: 2}]->(g);
CREATE (n: Building {id: 3828, name:"building_subsistence_farmslevel", level:100});
MATCH (g: Goods{code: 7}), (b: Building{id:3828}) CREATE (b)-[r:Supply{max_supply: 178.33499999999998, current_output: 196.1685,level: 100}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3828}) CREATE (b)-[r:Supply{max_supply: 29.722499999999997, current_output: 32.69475,level: 100}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3828}) CREATE (b)-[r:Supply{max_supply: 29.722499999999997, current_output: 32.69475,level: 100}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3828}) CREATE (b)-[r:Supply{max_supply: 29.722499999999997, current_output: 32.69475,level: 100}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3828}) CREATE (b)-[r:Supply{max_supply: 29.722499999999997, current_output: 32.69475,level: 100}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3828}) CREATE (b)-[r:Supply{max_supply: 29.722499999999997, current_output: 32.69475,level: 100}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3828}) CREATE (b)-[r:Supply{max_supply: 41.61149999999999, current_output: 45.77265,level: 100}]->(g);
CREATE (n: Building {id: 3829, name:"building_subsistence_farmslevel", level:61});
MATCH (g: Goods{code: 7}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 157.99853636363636, current_output: 173.79839,level: 61}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 26.333081818181817, current_output: 28.96639,level: 61}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 26.333081818181817, current_output: 28.96639,level: 61}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 26.333081818181817, current_output: 28.96639,level: 61}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 26.333081818181817, current_output: 28.96639,level: 61}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 26.333081818181817, current_output: 28.96639,level: 61}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 36.86631818181818, current_output: 40.55295,level: 61}]->(g);
CREATE (n: Building {id: 3830, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3830}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.076213788085099, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3830}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 15.381068940425497,level: 1}]->(g);
CREATE (n: Building {id: 3831, name:"building_subsistence_farmslevel", level:83});
MATCH (g: Goods{code: 7}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 204.43895454545452, current_output: 224.88285,level: 83}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 34.07315454545454, current_output: 37.48047,level: 83}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 34.07315454545454, current_output: 37.48047,level: 83}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 34.07315454545454, current_output: 37.48047,level: 83}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 34.07315454545454, current_output: 37.48047,level: 83}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 34.07315454545454, current_output: 37.48047,level: 83}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 47.702418181818175, current_output: 52.47266,level: 83}]->(g);
CREATE (n: Building {id: 3832, name:"building_subsistence_farmslevel", level:122});
MATCH (g: Goods{code: 7}), (b: Building{id:3832}) CREATE (b)-[r:Supply{max_supply: 297.2212727272727, current_output: 326.9434,level: 122}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3832}) CREATE (b)-[r:Supply{max_supply: 49.53687272727272, current_output: 54.49056,level: 122}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3832}) CREATE (b)-[r:Supply{max_supply: 49.53687272727272, current_output: 54.49056,level: 122}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3832}) CREATE (b)-[r:Supply{max_supply: 49.53687272727272, current_output: 54.49056,level: 122}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3832}) CREATE (b)-[r:Supply{max_supply: 49.53687272727272, current_output: 54.49056,level: 122}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3832}) CREATE (b)-[r:Supply{max_supply: 49.53687272727272, current_output: 54.49056,level: 122}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3832}) CREATE (b)-[r:Supply{max_supply: 69.35162727272727, current_output: 76.28679,level: 122}]->(g);
CREATE (n: Building {id: 3833, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3833}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.076213788085099, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3833}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 15.381068940425497,level: 1}]->(g);
CREATE (n: Building {id: 3834, name:"building_subsistence_farmslevel", level:90});
MATCH (g: Goods{code: 7}), (b: Building{id:3834}) CREATE (b)-[r:Supply{max_supply: 147.0069, current_output: 161.70759,level: 90}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3834}) CREATE (b)-[r:Supply{max_supply: 24.501145454545455, current_output: 26.95126,level: 90}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3834}) CREATE (b)-[r:Supply{max_supply: 24.501145454545455, current_output: 26.95126,level: 90}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3834}) CREATE (b)-[r:Supply{max_supply: 24.501145454545455, current_output: 26.95126,level: 90}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3834}) CREATE (b)-[r:Supply{max_supply: 24.501145454545455, current_output: 26.95126,level: 90}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3834}) CREATE (b)-[r:Supply{max_supply: 24.501145454545455, current_output: 26.95126,level: 90}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3834}) CREATE (b)-[r:Supply{max_supply: 34.30160909090909, current_output: 37.73177,level: 90}]->(g);
CREATE (n: Building {id: 3835, name:"building_subsistence_farmslevel", level:253});
MATCH (g: Goods{code: 7}), (b: Building{id:3835}) CREATE (b)-[r:Supply{max_supply: 457.66940800000003, current_output: 572.08676,level: 253}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3835}) CREATE (b)-[r:Supply{max_supply: 76.278232, current_output: 95.34779,level: 253}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3835}) CREATE (b)-[r:Supply{max_supply: 76.278232, current_output: 95.34779,level: 253}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3835}) CREATE (b)-[r:Supply{max_supply: 76.278232, current_output: 95.34779,level: 253}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3835}) CREATE (b)-[r:Supply{max_supply: 76.278232, current_output: 95.34779,level: 253}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3835}) CREATE (b)-[r:Supply{max_supply: 76.278232, current_output: 95.34779,level: 253}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3835}) CREATE (b)-[r:Supply{max_supply: 106.78952799999999, current_output: 133.48691,level: 253}]->(g);
CREATE (n: Building {id: 3836, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3836}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 15.381068940425498, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3836}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 5.232857135229345, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3836}) CREATE (b)-[r:Supply{max_supply: 199.6, current_output: 82.29079289401413,level: 5}]->(g);
CREATE (n: Building {id: 3870, name:"building_trade_centerlevel", level:73});
CREATE (n: Building {id: 3920, name:"building_trade_centerlevel", level:11});
CREATE (n: Building {id: 3921, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3921}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.076213788085099, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3921}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.046571427045869, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3921}) CREATE (b)-[r:Supply{max_supply: 39.92, current_output: 16.458158578802827,level: 1}]->(g);
CREATE (n: Building {id: 3951, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4371, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4372, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4373, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4448, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4449, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4450, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4451, name:"building_conscription_centerlevel", level:16});
CREATE (n: Building {id: 4458, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4459, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4460, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4461, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4462, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4463, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4464, name:"building_conscription_centerlevel", level:17});
CREATE (n: Building {id: 4465, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4466, name:"building_conscription_centerlevel", level:14});
CREATE (n: Building {id: 4467, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4468, name:"building_conscription_centerlevel", level:17});
CREATE (n: Building {id: 4469, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4470, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4471, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4473, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4474, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4475, name:"building_conscription_centerlevel", level:15});
CREATE (n: Building {id: 4476, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4477, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4478, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4479, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4480, name:"building_conscription_centerlevel", level:23});
CREATE (n: Building {id: 4481, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4482, name:"building_conscription_centerlevel", level:17});
CREATE (n: Building {id: 4531, name:"building_conscription_centerlevel", level:11});
CREATE (n: Building {id: 4532, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4533, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4534, name:"building_conscription_centerlevel", level:12});
CREATE (n: Building {id: 4535, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4536, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4712, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:4712}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.442761900314365, level: 1}]->(b);
CREATE (n: Building {id: 4728, name:"building_subsistence_pastureslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:4728}) CREATE (b)-[r:Supply{max_supply: 4.089799999999999, current_output: 4.49878,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:4728}) CREATE (b)-[r:Supply{max_supply: 6.1347, current_output: 6.74817,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:4728}) CREATE (b)-[r:Supply{max_supply: 2.0448999999999997, current_output: 2.24939,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:4728}) CREATE (b)-[r:Supply{max_supply: 4.089799999999999, current_output: 4.49878,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:4728}) CREATE (b)-[r:Supply{max_supply: 4.089799999999999, current_output: 4.49878,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:4728}) CREATE (b)-[r:Supply{max_supply: 4.089799999999999, current_output: 4.49878,level: 44}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:4728}) CREATE (b)-[r:Supply{max_supply: 13.578127272727272, current_output: 14.93594,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:4728}) CREATE (b)-[r:Supply{max_supply: 5.7257181818181815, current_output: 6.29829,level: 44}]->(g);
CREATE (n: Building {id: 4764, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4764}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.4572827285106, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4764}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.9392284255720598, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4764}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 19.86612536686869,level: 1}]->(g);
CREATE (n: Building {id: 4779, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:4779}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.442761900314365, level: 1}]->(b);
CREATE (n: Building {id: 4782, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4782}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.4572827285106, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:4782}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 18.4572827285106,level: 1}]->(g);
CREATE (n: Building {id: 4785, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:4785}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.88552380062873, level: 1}]->(b);
CREATE (n: Building {id: 4790, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:4790}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 9.77104760125746, level: 2}]->(b);
CREATE (n: Building {id: 33559225, name:"building_subsistence_pastureslevel", level:34});
MATCH (g: Goods{code: 7}), (b: Building{id:33559225}) CREATE (b)-[r:Supply{max_supply: 6.265854545454545, current_output: 6.89244,level: 34}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:33559225}) CREATE (b)-[r:Supply{max_supply: 9.398781818181819, current_output: 10.33866,level: 34}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:33559225}) CREATE (b)-[r:Supply{max_supply: 3.1329272727272723, current_output: 3.44622,level: 34}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:33559225}) CREATE (b)-[r:Supply{max_supply: 6.265854545454545, current_output: 6.89244,level: 34}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:33559225}) CREATE (b)-[r:Supply{max_supply: 6.265854545454545, current_output: 6.89244,level: 34}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:33559225}) CREATE (b)-[r:Supply{max_supply: 6.265854545454545, current_output: 6.89244,level: 34}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:33559225}) CREATE (b)-[r:Supply{max_supply: 20.802654545454544, current_output: 22.88292,level: 34}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:33559225}) CREATE (b)-[r:Supply{max_supply: 8.772199999999998, current_output: 9.64942,level: 34}]->(g);
CREATE (n: Building {id: 4807, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:4807}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 14.656571401886188, level: 3}]->(b);
CREATE (n: Building {id: 50336706, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:50336706}) CREATE (g)-[r:Demand{max_demand: 0.70446, current_input: 1.0869428114362765, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50336706}) CREATE (g)-[r:Demand{max_demand: 1.40892, current_input: 0.8668278260617716, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50336706}) CREATE (g)-[r:Demand{max_demand: 1.76115, current_input: 0.08270610708481166, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50336706}) CREATE (g)-[r:Demand{max_demand: 0.35223, current_input: 0.03561200918458868, level: 1}]->(b);
CREATE (n: Building {id: 83891188, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:83891188}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:83891188}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:83891188}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:83891188}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 5127, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 5177, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5177}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5177}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5177}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5177}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 33559657, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33559657}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33559657}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33559657}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33559657}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 167777448, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:167777448}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 15.330698370994584, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:167777448}) CREATE (g)-[r:Demand{max_demand: 19.872, current_input: 12.226104079365419, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:167777448}) CREATE (g)-[r:Demand{max_demand: 24.84, current_input: 1.166521704560498, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:167777448}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 0.5022867490816698, level: 2}]->(b);
CREATE (n: Building {id: 16782508, name:"building_gold_fieldslevel", level:1});
MATCH (g: Goods{code: 50}), (b: Building{id:16782508}) CREATE (b)-[r:Supply{max_supply: 16.12, current_output: 16.12,level: 1}]->(g);
CREATE (n: Building {id: 5338, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:5338}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 15.330698370994584, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5338}) CREATE (g)-[r:Demand{max_demand: 19.872, current_input: 12.226104079365419, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5338}) CREATE (g)-[r:Demand{max_demand: 24.84, current_input: 1.166521704560498, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5338}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 0.5022867490816698, level: 2}]->(b);
CREATE (n: Building {id: 16782572, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782572}) CREATE (g)-[r:Demand{max_demand: 4.96501, current_input: 7.66073578089491, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782572}) CREATE (g)-[r:Demand{max_demand: 9.93003, current_input: 6.109379040419736, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782572}) CREATE (g)-[r:Demand{max_demand: 12.41254, current_input: 0.5829105200775108, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782572}) CREATE (g)-[r:Demand{max_demand: 2.4825, current_input: 0.2509917179136967, level: 1}]->(b);
CREATE (n: Building {id: 16782575, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782575}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782575}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782575}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782575}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 16782579, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:16782579}) CREATE (g)-[r:Demand{max_demand: 14.904, current_input: 22.996047556491874, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782579}) CREATE (g)-[r:Demand{max_demand: 29.808, current_input: 18.339156119048127, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782579}) CREATE (g)-[r:Demand{max_demand: 37.26, current_input: 1.7497825568407472, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782579}) CREATE (g)-[r:Demand{max_demand: 7.452, current_input: 0.7534301236225047, level: 3}]->(b);
CREATE (n: Building {id: 16782584, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782584}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782584}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782584}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782584}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 16782586, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782586}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782586}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782586}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782586}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 16782590, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782590}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782590}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782590}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782590}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 67114248, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:67114248}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.789738621556784, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:67114248}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 47.89738621556784,level: 1}]->(g);
CREATE (n: Building {id: 67114260, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:67114260}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:67114260}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:67114260}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:67114260}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 83891502, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:83891502}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:83891502}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:83891502}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:83891502}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 100668722, name:"building_subsistence_farmslevel", level:24});
MATCH (g: Goods{code: 7}), (b: Building{id:100668722}) CREATE (b)-[r:Supply{max_supply: 71.35199999999999, current_output: 78.4872,level: 24}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:100668722}) CREATE (b)-[r:Supply{max_supply: 11.892, current_output: 13.0812,level: 24}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:100668722}) CREATE (b)-[r:Supply{max_supply: 11.892, current_output: 13.0812,level: 24}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:100668722}) CREATE (b)-[r:Supply{max_supply: 11.892, current_output: 13.0812,level: 24}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:100668722}) CREATE (b)-[r:Supply{max_supply: 11.892, current_output: 13.0812,level: 24}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:100668722}) CREATE (b)-[r:Supply{max_supply: 11.892, current_output: 13.0812,level: 24}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:100668722}) CREATE (b)-[r:Supply{max_supply: 16.6488, current_output: 18.31368,level: 24}]->(g);
CREATE (n: Building {id: 33559872, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33559872}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33559872}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33559872}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33559872}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 33559873, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33559873}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33559873}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33559873}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33559873}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 5451, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5451}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5451}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5451}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5451}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 5460, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5460}) CREATE (b)-[r:Supply{max_supply: 39.3724, current_output: 49.2155,level: 1}]->(g);
CREATE (n: Building {id: 16782678, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16782678}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.442761900314365, level: 1}]->(b);
CREATE (n: Building {id: 151000410, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 67114331, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:67114331}) CREATE (b)-[r:Supply{max_supply: 21.51, current_output: 19.359,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:67114331}) CREATE (b)-[r:Supply{max_supply: 3.585, current_output: 3.2265,level: 1}]->(g);
CREATE (n: Building {id: 5469, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:5469}) CREATE (g)-[r:Demand{max_demand: 9.16, current_input: 4.475139801375915, level: 1}]->(b);
CREATE (n: Building {id: 16782689, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16782689}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.442761900314365, level: 1}]->(b);
CREATE (n: Building {id: 16782705, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:16782705}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.5055220904606177, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782705}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 4.0441767236849415,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:16782705}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 1.0110441809212354,level: 1}]->(g);
CREATE (n: Building {id: 16782848, name:"building_gold_fieldslevel", level:5});
MATCH (g: Goods{code: 50}), (b: Building{id:16782848}) CREATE (b)-[r:Supply{max_supply: 56.08, current_output: 58.3232,level: 5}]->(g);
CREATE (n: Building {id: 352327293, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:352327293}) CREATE (b)-[r:Supply{max_supply: 39.3724, current_output: 49.2155,level: 1}]->(g);
CREATE (n: Building {id: 5777, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:5777}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.442761900314365, level: 1}]->(b);
CREATE (n: Building {id: 5888, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:5888}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.88552380062873, level: 1}]->(b);
CREATE (n: Building {id: 6021, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:6021}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.88552380062873, level: 1}]->(b);
CREATE (n: Building {id: 33560508, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 6134, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:6134}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:6134}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:6134}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:6134}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 6170, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:6170}) CREATE (g)-[r:Demand{max_demand: 4.968, current_input: 7.665349185497292, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:6170}) CREATE (g)-[r:Demand{max_demand: 9.936, current_input: 6.113052039682709, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:6170}) CREATE (g)-[r:Demand{max_demand: 12.42, current_input: 0.583260852280249, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:6170}) CREATE (g)-[r:Demand{max_demand: 2.484, current_input: 0.2511433745408349, level: 1}]->(b);
CREATE (n: Building {id: 67115136, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:67115136}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.442761900314365, level: 1}]->(b);
CREATE (n: Building {id: 16783559, name:"building_naval_baselevel", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:16783559}) CREATE (g)-[r:Demand{max_demand: 0.49596, current_input: 0.32468426582859417, level: 3}]->(b);
CREATE (n: Building {id: 50337996, name:"building_naval_baselevel", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:50337996}) CREATE (g)-[r:Demand{max_demand: 0.14, current_input: 0.09165214375353495, level: 1}]->(b);
CREATE (n: Building {id: 6363, name:"building_naval_baselevel", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:6363}) CREATE (g)-[r:Demand{max_demand: 0.128, current_input: 0.08379624571751766, level: 1}]->(b);
CREATE (n: Building {id: 16783583, name:"building_naval_baselevel", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:16783583}) CREATE (g)-[r:Demand{max_demand: 0.116, current_input: 0.07594034768150038, level: 1}]->(b);
CREATE (n: Building {id: 16783587, name:"building_naval_baselevel", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:16783587}) CREATE (g)-[r:Demand{max_demand: 0.244, current_input: 0.15973659339901802, level: 2}]->(b);
CREATE (n: Building {id: 6423, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 6442, name:"building_subsistence_rice_paddieslevel", level:24});
MATCH (g: Goods{code: 7}), (b: Building{id:6442}) CREATE (b)-[r:Supply{max_supply: 7.843436363636362, current_output: 8.62778,level: 24}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6442}) CREATE (b)-[r:Supply{max_supply: 1.0695545454545454, current_output: 1.17651,level: 24}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6442}) CREATE (b)-[r:Supply{max_supply: 1.0695545454545454, current_output: 1.17651,level: 24}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6442}) CREATE (b)-[r:Supply{max_supply: 1.4260727272727272, current_output: 1.56868,level: 24}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6442}) CREATE (b)-[r:Supply{max_supply: 1.4260727272727272, current_output: 1.56868,level: 24}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6442}) CREATE (b)-[r:Supply{max_supply: 1.4260727272727272, current_output: 1.56868,level: 24}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6442}) CREATE (b)-[r:Supply{max_supply: 2.1391181818181817, current_output: 2.35303,level: 24}]->(g);
