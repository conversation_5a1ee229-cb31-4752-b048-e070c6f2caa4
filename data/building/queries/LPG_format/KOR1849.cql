CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:16.504180043576294, pop_demand:1358.94018722171});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:7.876623730313026, pop_demand:36.6743983026459});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:10.831664246785788, pop_demand:140.39083904361547});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:27.031425005721577, pop_demand:104.49296345638463});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:39.481127795955835, pop_demand:381.1495244924954});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:38.61713049897194, pop_demand:357.42876857212707});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:52.5, pop_demand:11.920047179999996});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:18.821813764755454, pop_demand:138.3840633333334});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:87.73573193969622, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:20.391650910031576, pop_demand:11.040886612126432});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:76.91722907063371, pop_demand:45.88906934243646});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:38.739664774508114, pop_demand:18.149993870594255});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:31.046969911798513, pop_demand:43.42043973136907});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:30.174698578211785, pop_demand:311.2014668352686});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:95.68881022550424});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:87.5, pop_demand:0.6347357744958442});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:9.975119034673472, pop_demand:6.831328535698783});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:14.367836541143166, pop_demand:32.64114385268423});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:87.5, pop_demand:57.88036081669155});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:16.34090638838603});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:41.024254378771474});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 2620, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:2620}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 2622, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2622}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 47.81144996567055, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2622}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2622}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 7.968574994278424,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2622}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 19.92143748569606,level: 3}]->(g);
CREATE (n: Building {id: 2623, name:"building_fishing_wharflevel", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:2623}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.673008026801673, level: 4}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2623}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 76.73008026801674,level: 4}]->(g);
CREATE (n: Building {id: 2624, name:"building_rice_farmlevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:2624}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 138.0,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2624}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 41.4,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2624}) CREATE (b)-[r:Supply{max_supply: 54.00000000000001, current_output: 62.1,level: 6}]->(g);
CREATE (n: Building {id: 2625, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:2625}) CREATE (b)-[r:Supply{max_supply: 66.32324509803921, current_output: 67.64971,level: 3}]->(g);
CREATE (n: Building {id: 2626, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 2627, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2627}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 51.44329085223821, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2627}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 21.249533318075798, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:2627}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 26.796670826658165,level: 1}]->(g);
CREATE (n: Building {id: 2628, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2628}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 15.937149988556847, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2628}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2628}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 2.6561916647594748,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2628}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 6.6404791618986865,level: 1}]->(g);
CREATE (n: Building {id: 2629, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2629}) CREATE (g)-[r:Demand{max_demand: 9.92279207920792, current_input: 3.8068831636023215, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2629}) CREATE (b)-[r:Supply{max_supply: 99.22800000000001, current_output: 38.068862024173825,level: 2}]->(g);
CREATE (n: Building {id: 2630, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2630}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 77.7,level: 2}]->(g);
CREATE (n: Building {id: 2631, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2631}) CREATE (b)-[r:Supply{max_supply: 44.215495049504945, current_output: 44.65765,level: 2}]->(g);
CREATE (n: Building {id: 2632, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 2}), (b: Building{id:2632}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 2633, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2633}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.9182520067004183, level: 1}]->(b);
CREATE (n: Building {id: 2634, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:2634}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 2635, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2635}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 15.937149988556847, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2635}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 15.937149988556849,level: 1}]->(g);
CREATE (n: Building {id: 2636, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:2636}) CREATE (g)-[r:Demand{max_demand: 14.92379411764706, current_input: 5.725519602752074, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2636}) CREATE (b)-[r:Supply{max_supply: 149.238, current_output: 57.2552185951914,level: 3}]->(g);
CREATE (n: Building {id: 2637, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2637}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 77.7,level: 2}]->(g);
CREATE (n: Building {id: 2638, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:2638}) CREATE (b)-[r:Supply{max_supply: 22.1175, current_output: 22.1175,level: 1}]->(g);
CREATE (n: Building {id: 2639, name:"building_barrackslevel", level:2});
MATCH (g: Goods{code: 2}), (b: Building{id:2639}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 2640, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2640}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 77.7,level: 2}]->(g);
CREATE (n: Building {id: 2641, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2641}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2641}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 11.1,level: 2}]->(g);
CREATE (n: Building {id: 2642, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:2642}) CREATE (b)-[r:Supply{max_supply: 66.32324509803921, current_output: 67.64971,level: 3}]->(g);
CREATE (n: Building {id: 2643, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:2643}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 2644, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:2644}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 2645, name:"building_glassworkslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2645}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 31.874299977113694, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2645}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2645}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 5.3123833295189495,level: 2}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2645}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 13.280958323797373,level: 2}]->(g);
CREATE (n: Building {id: 2646, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:2646}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2646}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 2647, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2647}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 77.7,level: 2}]->(g);
CREATE (n: Building {id: 2648, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:2648}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2648}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 0.0,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2648}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 0.0,level: 3}]->(g);
CREATE (n: Building {id: 2649, name:"building_barrackslevel", level:10});
CREATE (n: Building {id: 16780078, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16780078}) CREATE (b)-[r:Supply{max_supply: 35.3724, current_output: 44.2155,level: 1}]->(g);
CREATE (n: Building {id: 50335084, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:50335084}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 3582, name:"building_subsistence_farmslevel", level:140});
MATCH (g: Goods{code: 7}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 354.12719999999996, current_output: 389.53992,level: 140}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 59.0212, current_output: 64.92332,level: 140}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 59.0212, current_output: 64.92332,level: 140}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 59.0212, current_output: 64.92332,level: 140}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 59.0212, current_output: 64.92332,level: 140}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 59.0212, current_output: 64.92332,level: 140}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3582}) CREATE (b)-[r:Supply{max_supply: 82.62967272727272, current_output: 90.89264,level: 140}]->(g);
CREATE (n: Building {id: 3583, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3583}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.3123833295189495, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3583}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 26.561916647594746,level: 2}]->(g);
CREATE (n: Building {id: 3584, name:"building_subsistence_farmslevel", level:70});
MATCH (g: Goods{code: 7}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 165.42959999999997, current_output: 181.97256,level: 70}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 27.571599999999997, current_output: 30.32876,level: 70}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 27.571599999999997, current_output: 30.32876,level: 70}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 27.571599999999997, current_output: 30.32876,level: 70}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 27.571599999999997, current_output: 30.32876,level: 70}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 27.571599999999997, current_output: 30.32876,level: 70}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3584}) CREATE (b)-[r:Supply{max_supply: 38.600236363636355, current_output: 42.46026,level: 70}]->(g);
CREATE (n: Building {id: 3585, name:"building_subsistence_farmslevel", level:72});
MATCH (g: Goods{code: 7}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 173.8648727272727, current_output: 191.25136,level: 72}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 28.977472727272723, current_output: 31.87522,level: 72}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 28.977472727272723, current_output: 31.87522,level: 72}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 28.977472727272723, current_output: 31.87522,level: 72}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 28.977472727272723, current_output: 31.87522,level: 72}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 28.977472727272723, current_output: 31.87522,level: 72}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3585}) CREATE (b)-[r:Supply{max_supply: 40.56846363636363, current_output: 44.62531,level: 72}]->(g);
CREATE (n: Building {id: 3586, name:"building_subsistence_farmslevel", level:73});
MATCH (g: Goods{code: 7}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 170.2308818181818, current_output: 187.25397,level: 73}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 28.37180909090909, current_output: 31.20899,level: 73}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 28.37180909090909, current_output: 31.20899,level: 73}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 28.37180909090909, current_output: 31.20899,level: 73}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 28.37180909090909, current_output: 31.20899,level: 73}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 28.37180909090909, current_output: 31.20899,level: 73}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 39.72053636363636, current_output: 43.69259,level: 73}]->(g);
CREATE (n: Building {id: 3587, name:"building_subsistence_farmslevel", level:129});
MATCH (g: Goods{code: 7}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 337.65362727272725, current_output: 371.41899,level: 129}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 56.2756, current_output: 61.90316,level: 129}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 56.2756, current_output: 61.90316,level: 129}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 56.2756, current_output: 61.90316,level: 129}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 56.2756, current_output: 61.90316,level: 129}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 56.2756, current_output: 61.90316,level: 129}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3587}) CREATE (b)-[r:Supply{max_supply: 78.78584545454544, current_output: 86.66443,level: 129}]->(g);
CREATE (n: Building {id: 3588, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3588}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.6561916647594748, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3588}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.280958323797373,level: 1}]->(g);
CREATE (n: Building {id: 33558319, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:33558319}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:33558319}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 0.0,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:33558319}) CREATE (b)-[r:Supply{max_supply: 74.99999999999999, current_output: 0.0,level: 3}]->(g);
CREATE (n: Building {id: 4393, name:"building_conscription_centerlevel", level:20});
CREATE (n: Building {id: 4394, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4395, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4396, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4397, name:"building_conscription_centerlevel", level:17});
CREATE (n: Building {id: 4656, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:4656}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 51.44329085223821, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4656}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 21.249533318075798, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4656}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4656}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 16782078, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:16782078}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.8365040134008366, level: 2}]->(b);
CREATE (n: Building {id: 5027, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:5027}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 16782289, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:16782289}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.9182520067004183, level: 1}]->(b);
CREATE (n: Building {id: 16782341, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:16782341}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.9182520067004183, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:16782341}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 19.182520067004184,level: 1}]->(g);
CREATE (n: Building {id: 5224, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5224}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.6561916647594748, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5224}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.280958323797373,level: 1}]->(g);
CREATE (n: Building {id: 16782674, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782674}) CREATE (b)-[r:Supply{max_supply: 35.38, current_output: 44.225,level: 1}]->(g);
CREATE (n: Building {id: 16782733, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:16782733}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:16782733}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 0.0,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:16782733}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 0.0,level: 2}]->(g);
CREATE (n: Building {id: 16782837, name:"building_universitylevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:16782837}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 4}]->(b);
CREATE (n: Building {id: 16782842, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16782842}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 5727, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5727}) CREATE (g)-[r:Demand{max_demand: 0.508, current_input: 0.26986907313956265, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5727}) CREATE (b)-[r:Supply{max_supply: 2.54, current_output: 1.3493453656978132,level: 1}]->(g);
CREATE (n: Building {id: 16783102, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783102}) CREATE (b)-[r:Supply{max_supply: 35.3724, current_output: 44.2155,level: 1}]->(g);
CREATE (n: Building {id: 16783103, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783103}) CREATE (b)-[r:Supply{max_supply: 35.4196, current_output: 44.2745,level: 1}]->(g);
CREATE (n: Building {id: 6036, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:6036}) CREATE (b)-[r:Supply{max_supply: 22.1175, current_output: 22.1175,level: 1}]->(g);
CREATE (n: Building {id: 6534, name:"building_subsistence_rice_paddieslevel", level:140});
CREATE (n: Building {id: 6535, name:"building_subsistence_rice_paddieslevel", level:70});
MATCH (g: Goods{code: 7}), (b: Building{id:6535}) CREATE (b)-[r:Supply{max_supply: 0.5236, current_output: 0.57596,level: 70}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6535}) CREATE (b)-[r:Supply{max_supply: 0.07139999999999999, current_output: 0.07854,level: 70}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6535}) CREATE (b)-[r:Supply{max_supply: 0.07139999999999999, current_output: 0.07854,level: 70}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6535}) CREATE (b)-[r:Supply{max_supply: 0.09519999999999999, current_output: 0.10472,level: 70}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6535}) CREATE (b)-[r:Supply{max_supply: 0.09519999999999999, current_output: 0.10472,level: 70}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6535}) CREATE (b)-[r:Supply{max_supply: 0.09519999999999999, current_output: 0.10472,level: 70}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6535}) CREATE (b)-[r:Supply{max_supply: 0.14279999999999998, current_output: 0.15708,level: 70}]->(g);
CREATE (n: Building {id: 6536, name:"building_subsistence_rice_paddieslevel", level:71});
MATCH (g: Goods{code: 7}), (b: Building{id:6536}) CREATE (b)-[r:Supply{max_supply: 0.9762454545454545, current_output: 1.07387,level: 71}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6536}) CREATE (b)-[r:Supply{max_supply: 0.1331181818181818, current_output: 0.14643,level: 71}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6536}) CREATE (b)-[r:Supply{max_supply: 0.1331181818181818, current_output: 0.14643,level: 71}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6536}) CREATE (b)-[r:Supply{max_supply: 0.1775, current_output: 0.19525,level: 71}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6536}) CREATE (b)-[r:Supply{max_supply: 0.1775, current_output: 0.19525,level: 71}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6536}) CREATE (b)-[r:Supply{max_supply: 0.1775, current_output: 0.19525,level: 71}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6536}) CREATE (b)-[r:Supply{max_supply: 0.26624545454545456, current_output: 0.29287,level: 71}]->(g);
CREATE (n: Building {id: 6537, name:"building_subsistence_rice_paddieslevel", level:73});
CREATE (n: Building {id: 6538, name:"building_subsistence_rice_paddieslevel", level:129});
