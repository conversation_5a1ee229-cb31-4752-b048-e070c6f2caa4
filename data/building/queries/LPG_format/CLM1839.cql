CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0.22863859327780844});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:20.35116123382266, pop_demand:321.5589434703178});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:19.81208473961274, pop_demand:24.68680789935457});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:10.910610721757994, pop_demand:11.116180000000004});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:20.809382612342553, pop_demand:66.69707999999999});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:30.254393779622234, pop_demand:78.85242630039743});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:27.48832974923562, pop_demand:68.17958520180821});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:20.70986419641831, pop_demand:0.22217022643471077});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:27.111712898881375, pop_demand:54.4289446525435});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30.49920405929759, pop_demand:10.051000000000002});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0.11857241378376834});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:3.9706602040816317});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:26.530509136361594, pop_demand:7.361060502682899});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:30.58374355480952, pop_demand:36.958866676236326});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:24.25627648042906, pop_demand:24.546746039155973});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:23.490269430085046, pop_demand:20.10149});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:9.928165924954458, pop_demand:4.833798470125335});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:29.843514682801594, pop_demand:80.03550922063309});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:4.632436904761906});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:23.162184523809522});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 1592, name:"building_coffee_plantation", level:1});
MATCH (g: Goods{code: 41}), (b: Building{id:1592}) CREATE (b)-[r:Supply{max_supply: 18.308, current_output: 21.9696,level: 1}]->(g);
CREATE (n: Building {id: 1593, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1593}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 36.0,level: 1}]->(g);
CREATE (n: Building {id: 1656, name:"building_government_administration", level:1});
CREATE (n: Building {id: 1657, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1657}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1658, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1658}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 60.5,level: 2}]->(g);
CREATE (n: Building {id: 1659, name:"building_sugar_plantation", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1659}) CREATE (b)-[r:Supply{max_supply: 29.676, current_output: 35.6112,level: 1}]->(g);
CREATE (n: Building {id: 1660, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1660}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1662, name:"building_rice_farm", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1662}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 48.0,level: 1}]->(g);
CREATE (n: Building {id: 1663, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1663}) CREATE (g)-[r:Demand{max_demand: 4.45585, current_input: 4.351535214418087, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1663}) CREATE (b)-[r:Supply{max_supply: 8.9117, current_output: 8.703070428836174,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1663}) CREATE (b)-[r:Supply{max_supply: 8.9117, current_output: 8.703070428836174,level: 1}]->(g);
CREATE (n: Building {id: 1664, name:"building_logging_camp", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1664}) CREATE (g)-[r:Demand{max_demand: 4.64345, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1664}) CREATE (b)-[r:Supply{max_supply: 55.7214, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 1665, name:"building_government_administration", level:2});
CREATE (n: Building {id: 1666, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1666}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 101.51235265609608, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1666}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 1}]->(g);
CREATE (n: Building {id: 1667, name:"building_sugar_plantation", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1667}) CREATE (b)-[r:Supply{max_supply: 7.65, current_output: 9.18,level: 1}]->(g);
CREATE (n: Building {id: 1668, name:"building_cotton_plantation", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1668}) CREATE (b)-[r:Supply{max_supply: 78.92800000000001, current_output: 115.23488,level: 2}]->(g);
CREATE (n: Building {id: 1669, name:"building_rice_farm", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1669}) CREATE (b)-[r:Supply{max_supply: 75.536, current_output: 91.39856,level: 2}]->(g);
CREATE (n: Building {id: 1670, name:"building_furniture_manufacturies", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1670}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 25.37808816402402, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1670}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 28.381234775314894, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1670}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 43.78592608148617,level: 1}]->(g);
CREATE (n: Building {id: 1671, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1671}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.6,level: 2}]->(g);
CREATE (n: Building {id: 1672, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1672}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 60.5,level: 2}]->(g);
CREATE (n: Building {id: 1673, name:"building_coffee_plantation", level:2});
MATCH (g: Goods{code: 41}), (b: Building{id:1673}) CREATE (b)-[r:Supply{max_supply: 38.528, current_output: 46.61888,level: 2}]->(g);
CREATE (n: Building {id: 1674, name:"building_maize_farm", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1674}) CREATE (b)-[r:Supply{max_supply: 39.216, current_output: 47.45136,level: 2}]->(g);
CREATE (n: Building {id: 2826, name:"building_subsistence_farms", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:2826}) CREATE (b)-[r:Supply{max_supply: 9.268558333333335, current_output: 11.12227,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2826}) CREATE (b)-[r:Supply{max_supply: 2.3171333333333335, current_output: 2.78056,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2826}) CREATE (b)-[r:Supply{max_supply: 2.3171333333333335, current_output: 2.78056,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2826}) CREATE (b)-[r:Supply{max_supply: 2.3171333333333335, current_output: 2.78056,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2826}) CREATE (b)-[r:Supply{max_supply: 2.3171333333333335, current_output: 2.78056,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2826}) CREATE (b)-[r:Supply{max_supply: 2.3171333333333335, current_output: 2.78056,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2826}) CREATE (b)-[r:Supply{max_supply: 2.3171333333333335, current_output: 2.78056,level: 18}]->(g);
CREATE (n: Building {id: 2961, name:"building_subsistence_farms", level:116});
MATCH (g: Goods{code: 7}), (b: Building{id:2961}) CREATE (b)-[r:Supply{max_supply: 53.295033333333336, current_output: 63.95404,level: 116}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2961}) CREATE (b)-[r:Supply{max_supply: 13.323758333333334, current_output: 15.98851,level: 116}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2961}) CREATE (b)-[r:Supply{max_supply: 13.323758333333334, current_output: 15.98851,level: 116}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2961}) CREATE (b)-[r:Supply{max_supply: 13.323758333333334, current_output: 15.98851,level: 116}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2961}) CREATE (b)-[r:Supply{max_supply: 13.323758333333334, current_output: 15.98851,level: 116}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2961}) CREATE (b)-[r:Supply{max_supply: 13.323758333333334, current_output: 15.98851,level: 116}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2961}) CREATE (b)-[r:Supply{max_supply: 13.323758333333334, current_output: 15.98851,level: 116}]->(g);
CREATE (n: Building {id: 2962, name:"building_subsistence_farms", level:58});
MATCH (g: Goods{code: 7}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 5.697916666666667, current_output: 6.8375,level: 58}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 1.4244750000000002, current_output: 1.70937,level: 58}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 1.4244750000000002, current_output: 1.70937,level: 58}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 1.4244750000000002, current_output: 1.70937,level: 58}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 1.4244750000000002, current_output: 1.70937,level: 58}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 1.4244750000000002, current_output: 1.70937,level: 58}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2962}) CREATE (b)-[r:Supply{max_supply: 1.4244750000000002, current_output: 1.70937,level: 58}]->(g);
CREATE (n: Building {id: 2963, name:"building_subsistence_rice_paddies", level:45});
MATCH (g: Goods{code: 7}), (b: Building{id:2963}) CREATE (b)-[r:Supply{max_supply: 26.964900000000004, current_output: 32.35788,level: 45}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2963}) CREATE (b)-[r:Supply{max_supply: 4.49415, current_output: 5.39298,level: 45}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2963}) CREATE (b)-[r:Supply{max_supply: 4.49415, current_output: 5.39298,level: 45}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2963}) CREATE (b)-[r:Supply{max_supply: 5.9922, current_output: 7.19064,level: 45}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2963}) CREATE (b)-[r:Supply{max_supply: 5.9922, current_output: 7.19064,level: 45}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2963}) CREATE (b)-[r:Supply{max_supply: 5.9922, current_output: 7.19064,level: 45}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2963}) CREATE (b)-[r:Supply{max_supply: 5.9922, current_output: 7.19064,level: 45}]->(g);
CREATE (n: Building {id: 2964, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:2964}) CREATE (b)-[r:Supply{max_supply: 14.484, current_output: 14.484,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2964}) CREATE (b)-[r:Supply{max_supply: 4.828, current_output: 4.828,level: 1}]->(g);
CREATE (n: Building {id: 2965, name:"building_subsistence_farms", level:74});
MATCH (g: Goods{code: 7}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 16.416158333333335, current_output: 19.69939,level: 74}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 4.104033333333334, current_output: 4.92484,level: 74}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 4.104033333333334, current_output: 4.92484,level: 74}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 4.104033333333334, current_output: 4.92484,level: 74}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 4.104033333333334, current_output: 4.92484,level: 74}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 4.104033333333334, current_output: 4.92484,level: 74}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2965}) CREATE (b)-[r:Supply{max_supply: 4.104033333333334, current_output: 4.92484,level: 74}]->(g);
CREATE (n: Building {id: 2976, name:"building_subsistence_farms", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:2976}) CREATE (b)-[r:Supply{max_supply: 1.4679, current_output: 1.4679,level: 7}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2976}) CREATE (b)-[r:Supply{max_supply: 0.36697, current_output: 0.36697,level: 7}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2976}) CREATE (b)-[r:Supply{max_supply: 0.36697, current_output: 0.36697,level: 7}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2976}) CREATE (b)-[r:Supply{max_supply: 0.36697, current_output: 0.36697,level: 7}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2976}) CREATE (b)-[r:Supply{max_supply: 0.36697, current_output: 0.36697,level: 7}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2976}) CREATE (b)-[r:Supply{max_supply: 0.36697, current_output: 0.36697,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2976}) CREATE (b)-[r:Supply{max_supply: 0.36697, current_output: 0.36697,level: 7}]->(g);
CREATE (n: Building {id: 4033, name:"building_barracks", level:7});
MATCH (g: Goods{code: 1}), (b: Building{id:4033}) CREATE (g)-[r:Demand{max_demand: 6.84593, current_input: 0.0, level: 7}]->(b);
CREATE (n: Building {id: 4034, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4034}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4034}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 3.906357004313958, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4034}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 4035, name:"building_barracks", level:6});
MATCH (g: Goods{code: 1}), (b: Building{id:4035}) CREATE (g)-[r:Demand{max_demand: 5.99994, current_input: 0.0, level: 6}]->(b);
CREATE (n: Building {id: 4171, name:"building_trade_center", level:25});
CREATE (n: Building {id: 4172, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:4172}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:4172}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 4389, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 4639, name:"building_conscription_center", level:1});
CREATE (n: Building {id: 4716, name:"building_artillery_foundries", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:4716}) CREATE (g)-[r:Demand{max_demand: 0.80715, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:4716}) CREATE (g)-[r:Demand{max_demand: 0.5381, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4716}) CREATE (b)-[r:Supply{max_supply: 1.34525, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 4897, name:"building_paper_mills", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4897}) CREATE (g)-[r:Demand{max_demand: 0.3, current_input: 0.2838123477531489, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:4897}) CREATE (b)-[r:Supply{max_supply: 0.4, current_output: 0.37841646367086523,level: 1}]->(g);
CREATE (n: Building {id: 5462, name:"building_maize_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:5462}) CREATE (g)-[r:Demand{max_demand: 0.96, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:5462}) CREATE (b)-[r:Supply{max_supply: 4.8, current_output: 0.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:5462}) CREATE (b)-[r:Supply{max_supply: 6.720000000000001, current_output: 0.0,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:5462}) CREATE (b)-[r:Supply{max_supply: 4.8, current_output: 0.0,level: 1}]->(g);
