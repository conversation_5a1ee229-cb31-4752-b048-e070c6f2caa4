CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:70.70419556536747, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:102.53676806973353, pop_demand:17.55151782527673});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:87.41676521118279, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:114.25579633842993, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:30.0599562586389, pop_demand:6287.115865768167});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:19.751220500334405, pop_demand:340.54147330101944});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:31.98073185765013, pop_demand:96.34384548695732});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:32.146909311472605, pop_demand:348.15884321478956});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:26.632869358329806, pop_demand:498.0495201981621});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:43.18413128836376, pop_demand:1605.3061160462548});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:40.27498386464233, pop_demand:1311.0409935123948});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:48.27337209997704, pop_demand:64.16596175250085});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:23.62279545407845, pop_demand:906.8471496571648});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:41.81466036257615, pop_demand:650.3751733333347});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:90.97487004588311, pop_demand:76.46145203802327});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:83.3933516487698, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:29.646105139173507, pop_demand:643.1059806697531});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:60.308161489035655, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:45.038574340007344, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:69.06810959839065, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:43.31935028385953, pop_demand:14.039333271812266});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:88.0997157709812, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:68.01260230967507, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:57.35755462736664, pop_demand:26.76500171398213});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:46.66205558325234, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:55.50170724188368, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:79.47405427343786, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:104.05625146097364, pop_demand:135.9078037130628});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:29.120048854127333, pop_demand:285.6598267173346});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:36.13308485743252, pop_demand:421.0863097008726});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:41.85572870003013, pop_demand:1064.414604291476});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50.029409821433866, pop_demand:40.431709049157384});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:79.63465395167215, pop_demand:438.68826352955637});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:37.58939849883538, pop_demand:121.43003929388175});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:27.41964969176792, pop_demand:102.4188321627238});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:54.98881450990577, pop_demand:194.65354697856438});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:87.5, pop_demand:773.172161350231});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:98.48376211641514, pop_demand:792.7955216595338});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:82.95809342445371, pop_demand:474.2991340085594});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:292.7227998596327, pop_demand:5.912940013034491});
CREATE (n: Building {id: 16777269, name:"building_conscription_center", level:7});
CREATE (n: Building {id: 77, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:77}) CREATE (g)-[r:Demand{max_demand: 199.99999999999997, current_input: 37.63073872717362, level: 10}]->(b);
CREATE (n: Building {id: 78, name:"building_arts_academy", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:78}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.6446108090760445, level: 3}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:78}) CREATE (b)-[r:Supply{max_supply: 12.0, current_output: 2.257844323630418,level: 3}]->(g);
CREATE (n: Building {id: 79, name:"building_construction_sector", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:79}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 12.084313946606505, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:79}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 22.83974293950579, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:79}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 48.50484613805043, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:79}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 14.512872496284903, level: 3}]->(b);
CREATE (n: Building {id: 80, name:"building_university", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:80}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 7.526147745434726, level: 4}]->(b);
CREATE (n: Building {id: 81, name:"building_paper_mills", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:81}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 57.09935734876448, level: 10}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:81}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 10.96137930591965, level: 10}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:81}) CREATE (b)-[r:Supply{max_supply: 700.0, current_output: 104.98074447761067,level: 10}]->(g);
CREATE (n: Building {id: 82, name:"building_textile_mills", level:23});
MATCH (g: Goods{code: 9}), (b: Building{id:82}) CREATE (g)-[r:Demand{max_demand: 575.0, current_input: 115.808008654979, level: 23}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:82}) CREATE (g)-[r:Demand{max_demand: 345.0, current_input: 0.0, level: 23}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:82}) CREATE (g)-[r:Demand{max_demand: 115.0, current_input: 0.0, level: 23}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:82}) CREATE (b)-[r:Supply{max_supply: 690.0, current_output: 46.3232034619916,level: 23}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:82}) CREATE (b)-[r:Supply{max_supply: 690.0, current_output: 46.3232034619916,level: 23}]->(g);
CREATE (n: Building {id: 83, name:"building_shipyards", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:83}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 12.084313946606505, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:83}) CREATE (g)-[r:Demand{max_demand: 120.00000000000001, current_input: 22.839742939505793, level: 3}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:83}) CREATE (b)-[r:Supply{max_supply: 105.0, current_output: 20.566162239314473,level: 3}]->(g);
CREATE (n: Building {id: 84, name:"building_military_shipyards", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:84}) CREATE (g)-[r:Demand{max_demand: 18.697793103448276, current_input: 3.765833366179382, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:84}) CREATE (g)-[r:Demand{max_demand: 37.39559482758621, current_input: 7.117548107766511, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:84}) CREATE (g)-[r:Demand{max_demand: 18.697793103448276, current_input: 6.046223850692394, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:84}) CREATE (g)-[r:Demand{max_demand: 37.39559482758621, current_input: 1.1637791256377406, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:84}) CREATE (g)-[r:Demand{max_demand: 9.348896551724138, current_input: 3.5140964240300687, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:84}) CREATE (b)-[r:Supply{max_supply: 84.14009482758621, current_output: 18.88282725838827,level: 2}]->(g);
CREATE (n: Building {id: 85, name:"building_furniture_manufacturies", level:15});
MATCH (g: Goods{code: 9}), (b: Building{id:85}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 30.21078486651626, level: 15}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:85}) CREATE (g)-[r:Demand{max_demand: 225.00000000000003, current_input: 42.82451801157336, level: 15}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:85}) CREATE (g)-[r:Demand{max_demand: 225.00000000000003, current_input: 7.002169760255514, level: 15}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:85}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 36.28218124071226, level: 15}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:85}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 135.9929391958267,level: 15}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:85}) CREATE (b)-[r:Supply{max_supply: 375.00000000000006, current_output: 84.9955869973917,level: 15}]->(g);
CREATE (n: Building {id: 86, name:"building_rye_farm", level:15});
MATCH (g: Goods{code: 32}), (b: Building{id:86}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 19.49466094822397, level: 15}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:86}) CREATE (g)-[r:Demand{max_demand: 15.000000000000002, current_input: 7.256436248142452, level: 15}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:86}) CREATE (b)-[r:Supply{max_supply: 375.0, current_output: 139.4421054723406,level: 15}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:86}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 55.77684218893623,level: 15}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:86}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 27.888421094468114,level: 15}]->(g);
CREATE (n: Building {id: 87, name:"building_livestock_ranch", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:87}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 19.773928004472843, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:87}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 29.025744992569805, level: 6}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:87}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 48.79967299704265,level: 6}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:87}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 12.199918249260662,level: 6}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:87}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 60.99959124630331,level: 6}]->(g);
CREATE (n: Building {id: 88, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:88}) CREATE (g)-[r:Demand{max_demand: 9.998, current_input: 3.120863479722706, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:88}) CREATE (b)-[r:Supply{max_supply: 99.97999999999999, current_output: 31.20863479722706,level: 2}]->(g);
CREATE (n: Building {id: 89, name:"building_railway", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:89}) CREATE (g)-[r:Demand{max_demand: 16.0, current_input: 3.0452990586007718, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:89}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.071608953940124, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:89}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.7588354995563553, level: 2}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:89}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 31.324294822363676,level: 2}]->(g);
CREATE (n: Building {id: 90, name:"building_port", level:8});
MATCH (g: Goods{code: 18}), (b: Building{id:90}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 12.485951109112648, level: 8}]->(b);
CREATE (n: Building {id: 91, name:"building_government_administration", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:91}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 22.578443236304178, level: 6}]->(b);
CREATE (n: Building {id: 92, name:"building_textile_mills", level:20});
MATCH (g: Goods{code: 9}), (b: Building{id:92}) CREATE (g)-[r:Demand{max_demand: 800.0, current_input: 161.1241859547534, level: 20}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:92}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 0.0, level: 20}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:92}) CREATE (b)-[r:Supply{max_supply: 1200.0, current_output: 120.84313946606505,level: 20}]->(g);
CREATE (n: Building {id: 93, name:"building_construction_sector", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:93}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 12.084313946606505, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:93}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 22.83974293950579, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:93}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 48.50484613805043, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:93}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 14.512872496284903, level: 3}]->(b);
CREATE (n: Building {id: 94, name:"building_arms_industry", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:94}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.2336564092033617, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:94}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.31120754490024505, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:94}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 5.317295931155409,level: 1}]->(g);
CREATE (n: Building {id: 95, name:"building_munition_plants", level:2});
MATCH (g: Goods{code: 25}), (b: Building{id:95}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 33.31163980125451, level: 2}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:95}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 8.587022853787916, level: 2}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:95}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 52.373328318803026,level: 2}]->(g);
CREATE (n: Building {id: 96, name:"building_coal_mine", level:25});
MATCH (g: Goods{code: 33}), (b: Building{id:96}) CREATE (g)-[r:Demand{max_demand: 250.0, current_input: 120.94060413570753, level: 25}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:96}) CREATE (b)-[r:Supply{max_supply: 1000.0, current_output: 483.7624165428301,level: 25}]->(g);
CREATE (n: Building {id: 97, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 16}), (b: Building{id:97}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.753815284444971, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:97}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 20.358044769700616, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:97}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 9.675248330856602, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:97}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 52.24383853299539,level: 2}]->(g);
CREATE (n: Building {id: 98, name:"building_rye_farm", level:10});
MATCH (g: Goods{code: 32}), (b: Building{id:98}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 12.99644063214931, level: 10}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:98}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.837624165428301, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:98}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 92.96140364822705,level: 10}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:98}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 37.18456145929082,level: 10}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:98}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 18.59228072964541,level: 10}]->(g);
CREATE (n: Building {id: 99, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:99}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 9.886964002236422, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:99}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 14.512872496284903, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:99}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 24.399836498521324,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:99}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 6.099959124630331,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:99}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 30.499795623151655,level: 3}]->(g);
CREATE (n: Building {id: 100, name:"building_railway", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:100}) CREATE (g)-[r:Demand{max_demand: 16.0, current_input: 3.0452990586007718, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:100}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.071608953940124, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:100}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.7588354995563553, level: 2}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:100}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 31.324294822363676,level: 2}]->(g);
CREATE (n: Building {id: 101, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:101}) CREATE (g)-[r:Demand{max_demand: 9.645892156862745, current_input: 3.0109534468590344, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:101}) CREATE (b)-[r:Supply{max_supply: 96.459, current_output: 30.109558950847422,level: 3}]->(g);
CREATE (n: Building {id: 102, name:"building_port", level:7});
MATCH (g: Goods{code: 18}), (b: Building{id:102}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 10.925207220473567, level: 7}]->(b);
CREATE (n: Building {id: 103, name:"building_paper_mills", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:103}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 28.54967867438224, level: 5}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:103}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 5.480689652959825, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:103}) CREATE (b)-[r:Supply{max_supply: 350.0, current_output: 52.490372238805335,level: 5}]->(g);
CREATE (n: Building {id: 104, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:104}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.591309334824281, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:104}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 9.675248330856602, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:104}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 16.266557665680885,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:104}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 4.066639416420221,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:104}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 20.33319708210111,level: 2}]->(g);
CREATE (n: Building {id: 105, name:"building_coal_mine", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:105}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 19.35049666171321, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:105}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 77.40198664685283,level: 4}]->(g);
CREATE (n: Building {id: 106, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:106}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 7.256436248142451, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:106}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 87.07723497770941,level: 3}]->(g);
CREATE (n: Building {id: 107, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:107}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.560743888639081, level: 1}]->(b);
CREATE (n: Building {id: 108, name:"building_government_administration", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:108}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 15.052295490869453, level: 4}]->(b);
CREATE (n: Building {id: 109, name:"building_tooling_workshops", level:18});
MATCH (g: Goods{code: 10}), (b: Building{id:109}) CREATE (g)-[r:Demand{max_demand: 531.1547948717949, current_input: 101.0953247996477, level: 18}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:109}) CREATE (g)-[r:Demand{max_demand: 354.10319658119664, current_input: 184.10318148801022, level: 18}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:109}) CREATE (b)-[r:Supply{max_supply: 1416.412794871795, current_output: 503.00013241078625,level: 18}]->(g);
CREATE (n: Building {id: 110, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:110}) CREATE (g)-[r:Demand{max_demand: 39.976, current_input: 8.051375572159028, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:110}) CREATE (g)-[r:Demand{max_demand: 79.952, current_input: 15.217359395828057, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:110}) CREATE (g)-[r:Demand{max_demand: 99.94, current_input: 32.317162153578394, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:110}) CREATE (g)-[r:Demand{max_demand: 19.988, current_input: 9.669443181858089, level: 2}]->(b);
CREATE (n: Building {id: 111, name:"building_textile_mills", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:111}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 40.28104648868835, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:111}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 0.0, level: 8}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:111}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 0.0, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:111}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 16.11241859547534,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:111}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 16.11241859547534,level: 8}]->(g);
CREATE (n: Building {id: 112, name:"building_steel_mills", level:8});
MATCH (g: Goods{code: 23}), (b: Building{id:112}) CREATE (g)-[r:Demand{max_demand: 218.9735981308411, current_input: 222.89371570650474, level: 8}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:112}) CREATE (g)-[r:Demand{max_demand: 291.96479439252334, current_input: 94.41138286491248, level: 8}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:112}) CREATE (b)-[r:Supply{max_supply: 474.44279439252335, current_output: 313.9306463406485,level: 8}]->(g);
CREATE (n: Building {id: 113, name:"building_motor_industry", level:3});
MATCH (g: Goods{code: 30}), (b: Building{id:113}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 46.79225291918975, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:113}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 62.38967055891967,level: 3}]->(g);
CREATE (n: Building {id: 114, name:"building_coal_mine", level:20});
MATCH (g: Goods{code: 16}), (b: Building{id:114}) CREATE (g)-[r:Demand{max_demand: 99.99499999999999, current_input: 47.53577593680748, level: 20}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:114}) CREATE (g)-[r:Demand{max_demand: 199.98999999999998, current_input: 96.7476456844006, level: 20}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:114}) CREATE (b)-[r:Supply{max_supply: 799.9599999999999, current_output: 383.6383951160311,level: 20}]->(g);
CREATE (n: Building {id: 115, name:"building_iron_mine", level:4});
MATCH (g: Goods{code: 16}), (b: Building{id:115}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 9.507630568889942, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:115}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 40.71608953940123, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:115}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 19.350496661713205, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:115}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 104.48767706599078,level: 4}]->(g);
CREATE (n: Building {id: 116, name:"building_rye_farm", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:116}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 10.397152505719447, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:116}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 3.870099332342641, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:116}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 74.36912291858164,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:116}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 29.74764916743265,level: 8}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:116}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 14.873824583716326,level: 8}]->(g);
CREATE (n: Building {id: 117, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:117}) CREATE (g)-[r:Demand{max_demand: 25.041, current_input: 8.252648852666741, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:117}) CREATE (g)-[r:Demand{max_demand: 25.041, current_input: 12.113894672649009, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:117}) CREATE (b)-[r:Supply{max_supply: 50.082, current_output: 20.36654352531575,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:117}) CREATE (b)-[r:Supply{max_supply: 12.5205, current_output: 5.091635881328937,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:117}) CREATE (b)-[r:Supply{max_supply: 62.6025, current_output: 25.458179406644685,level: 3}]->(g);
CREATE (n: Building {id: 118, name:"building_railway", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:118}) CREATE (g)-[r:Demand{max_demand: 31.995200000000004, current_input: 6.089684527483965, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:118}) CREATE (g)-[r:Demand{max_demand: 7.998800000000001, current_input: 8.141996425194067, level: 4}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:118}) CREATE (g)-[r:Demand{max_demand: 19.997000000000003, current_input: 7.516543348462844, level: 4}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:118}) CREATE (b)-[r:Supply{max_supply: 119.982, current_output: 62.639192356280645,level: 4}]->(g);
CREATE (n: Building {id: 119, name:"building_port", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:119}) CREATE (g)-[r:Demand{max_demand: 19.996, current_input: 6.241726959445412, level: 4}]->(b);
CREATE (n: Building {id: 120, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:120}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 7.256436248142451, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:120}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 29.025744992569805,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:120}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 29.025744992569805,level: 3}]->(g);
CREATE (n: Building {id: 121, name:"building_whaling_station", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:121}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.121487777278162, level: 2}]->(b);
MATCH (g: Goods{code: 28}), (b: Building{id:121}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 12.485951109112648,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:121}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 6.242975554556324,level: 2}]->(g);
CREATE (n: Building {id: 122, name:"building_fishing_wharf", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:122}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.242975554556324, level: 4}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:122}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 62.42975554556324,level: 4}]->(g);
CREATE (n: Building {id: 123, name:"building_government_administration", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:123}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 15.052295490869453, level: 4}]->(b);
CREATE (n: Building {id: 124, name:"building_arms_industry", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:124}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.4673128184067235, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:124}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.6224150898004901, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:124}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 10.634591862310819,level: 2}]->(g);
CREATE (n: Building {id: 125, name:"building_textile_mills", level:22});
MATCH (g: Goods{code: 9}), (b: Building{id:125}) CREATE (g)-[r:Demand{max_demand: 550.0, current_input: 110.77287784389297, level: 22}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:125}) CREATE (g)-[r:Demand{max_demand: 330.0, current_input: 0.0, level: 22}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:125}) CREATE (g)-[r:Demand{max_demand: 110.0, current_input: 0.0, level: 22}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:125}) CREATE (b)-[r:Supply{max_supply: 660.0, current_output: 44.309151137557194,level: 22}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:125}) CREATE (b)-[r:Supply{max_supply: 660.0, current_output: 44.309151137557194,level: 22}]->(g);
CREATE (n: Building {id: 126, name:"building_artillery_foundries", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:126}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 9.700969227610086, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:126}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.6224150898004901, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:126}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 8.862159885259018,level: 2}]->(g);
CREATE (n: Building {id: 127, name:"building_furniture_manufacturies", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:127}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 10.070261622172087, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:127}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 4.758279779063706, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:127}) CREATE (g)-[r:Demand{max_demand: 125.0, current_input: 3.890094311253063, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:127}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 36.28218124071226, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:127}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 11.332744932985559,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:127}) CREATE (b)-[r:Supply{max_supply: 275.0, current_output: 62.33009713142057,level: 5}]->(g);
CREATE (n: Building {id: 128, name:"building_chemical_plants", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:128}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 3.288413791775895, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:128}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.2336564092033617, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:128}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 19.48407452907897,level: 1}]->(g);
CREATE (n: Building {id: 129, name:"building_explosives_factory", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:129}) CREATE (g)-[r:Demand{max_demand: 18.5332, current_input: 2.0314943495247006, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:129}) CREATE (g)-[r:Demand{max_demand: 18.5332, current_input: 4.817312670474993, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:129}) CREATE (b)-[r:Supply{max_supply: 46.333, current_output: 8.561008774999618,level: 1}]->(g);
CREATE (n: Building {id: 130, name:"building_coal_mine", level:8});
MATCH (g: Goods{code: 16}), (b: Building{id:130}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 19.015261137779884, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:130}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 38.70099332342641, level: 8}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:130}) CREATE (b)-[r:Supply{max_supply: 320.0, current_output: 153.46303119797236,level: 8}]->(g);
CREATE (n: Building {id: 131, name:"building_iron_mine", level:5});
MATCH (g: Goods{code: 16}), (b: Building{id:131}) CREATE (g)-[r:Demand{max_demand: 24.999999999999996, current_input: 11.884538211112426, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:131}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 50.89511192425154, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:131}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 24.188120827141503, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:131}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 130.60959633248845,level: 5}]->(g);
CREATE (n: Building {id: 132, name:"building_rye_farm", level:10});
MATCH (g: Goods{code: 32}), (b: Building{id:132}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 12.99644063214931, level: 10}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:132}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.837624165428301, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:132}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 92.96140364822705,level: 10}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:132}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 37.18456145929082,level: 10}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:132}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 18.59228072964541,level: 10}]->(g);
CREATE (n: Building {id: 133, name:"building_livestock_ranch", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:133}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 16.478273337060703, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:133}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 24.188120827141507, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:133}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 40.66639416420222,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:133}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 10.166598541050554,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:133}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 50.832992705252764,level: 5}]->(g);
CREATE (n: Building {id: 134, name:"building_railway", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:134}) CREATE (g)-[r:Demand{max_demand: 16.0, current_input: 3.0452990586007718, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:134}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.071608953940124, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:134}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.7588354995563553, level: 2}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:134}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 31.324294822363676,level: 2}]->(g);
CREATE (n: Building {id: 135, name:"building_port", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:135}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.242975554556324, level: 4}]->(b);
CREATE (n: Building {id: 136, name:"building_government_administration", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:136}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 15.052295490869453, level: 4}]->(b);
CREATE (n: Building {id: 137, name:"building_university", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:137}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.763073872717363, level: 2}]->(b);
CREATE (n: Building {id: 138, name:"building_furniture_manufacturies", level:15});
MATCH (g: Goods{code: 9}), (b: Building{id:138}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 30.21078486651626, level: 15}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:138}) CREATE (g)-[r:Demand{max_demand: 225.00000000000003, current_input: 42.82451801157336, level: 15}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:138}) CREATE (g)-[r:Demand{max_demand: 225.00000000000003, current_input: 7.002169760255514, level: 15}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:138}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 36.28218124071226, level: 15}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:138}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 135.9929391958267,level: 15}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:138}) CREATE (b)-[r:Supply{max_supply: 375.00000000000006, current_output: 84.9955869973917,level: 15}]->(g);
CREATE (n: Building {id: 139, name:"building_glassworks", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:139}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 11.419871469752895, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:139}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:139}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.983729850940886, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:139}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 20.462443723878224,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:139}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 20.462443723878224,level: 3}]->(g);
CREATE (n: Building {id: 140, name:"building_food_industry", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:140}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 52.73047467859425, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:140}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 67.82188877214755, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:140}) CREATE (b)-[r:Supply{max_supply: 260.0, current_output: 172.84351067635782,level: 4}]->(g);
CREATE (n: Building {id: 141, name:"building_iron_mine", level:1});
MATCH (g: Goods{code: 16}), (b: Building{id:141}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.3769076422224855, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:141}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.179022384850308, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:141}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.837624165428301, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:141}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 26.121919266497695,level: 1}]->(g);
CREATE (n: Building {id: 142, name:"building_shipyards", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:142}) CREATE (g)-[r:Demand{max_demand: 68.0471946902655, current_input: 13.705061063717057, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:142}) CREATE (g)-[r:Demand{max_demand: 136.0943982300885, current_input: 25.903008925682943, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:142}) CREATE (g)-[r:Demand{max_demand: 68.0471946902655, current_input: 2.1176800396906517, level: 4}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:142}) CREATE (g)-[r:Demand{max_demand: 17.011796460176992, current_input: 6.394454444574042, level: 4}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:142}) CREATE (b)-[r:Supply{max_supply: 238.1651946902655, current_output: 47.55806025086641,level: 4}]->(g);
CREATE (n: Building {id: 143, name:"building_military_shipyards", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:143}) CREATE (g)-[r:Demand{max_demand: 19.993396396396395, current_input: 4.026774648550086, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:143}) CREATE (g)-[r:Demand{max_demand: 39.98679279279279, current_input: 7.6107339030222505, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:143}) CREATE (g)-[r:Demand{max_demand: 19.993396396396395, current_input: 6.4651774398950606, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:143}) CREATE (g)-[r:Demand{max_demand: 39.98679279279279, current_input: 1.2444191613479856, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:143}) CREATE (g)-[r:Demand{max_demand: 9.996693693693693, current_input: 3.7575927134047, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:143}) CREATE (b)-[r:Supply{max_supply: 89.9702972972973, current_output: 20.191248723120076,level: 2}]->(g);
CREATE (n: Building {id: 144, name:"building_rye_farm", level:7});
MATCH (g: Goods{code: 32}), (b: Building{id:144}) CREATE (g)-[r:Demand{max_demand: 35.00000000000001, current_input: 9.09750844250452, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:144}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 3.386336915799811, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:144}) CREATE (b)-[r:Supply{max_supply: 175.0, current_output: 65.07298255375893,level: 7}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:144}) CREATE (b)-[r:Supply{max_supply: 70.00000000000001, current_output: 26.02919302150358,level: 7}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:144}) CREATE (b)-[r:Supply{max_supply: 35.00000000000001, current_output: 13.01459651075179,level: 7}]->(g);
CREATE (n: Building {id: 145, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:145}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 9.886964002236422, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:145}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 14.512872496284903, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:145}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 24.399836498521324,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:145}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 6.099959124630331,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:145}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 30.499795623151655,level: 3}]->(g);
CREATE (n: Building {id: 146, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:146}) CREATE (g)-[r:Demand{max_demand: 15.000000000000002, current_input: 4.682231665917244, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:146}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 46.82231665917244,level: 3}]->(g);
CREATE (n: Building {id: 147, name:"building_railway", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:147}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 1.5226495293003859, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:147}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.035804476970062, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:147}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.8794177497781777, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:147}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 15.662147411181838,level: 1}]->(g);
CREATE (n: Building {id: 148, name:"building_port", level:7});
MATCH (g: Goods{code: 18}), (b: Building{id:148}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 10.925207220473567, level: 7}]->(b);
CREATE (n: Building {id: 149, name:"building_coal_mine", level:1});
MATCH (g: Goods{code: 16}), (b: Building{id:149}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.3769076422224855, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:149}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.837624165428301, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:149}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.182878899746544,level: 1}]->(g);
CREATE (n: Building {id: 150, name:"building_government_administration", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:150}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 22.578443236304178, level: 6}]->(b);
CREATE (n: Building {id: 151, name:"building_tooling_workshops", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:151}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 34.25961440925868, level: 6}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:151}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 62.38967055891966, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:151}) CREATE (b)-[r:Supply{max_supply: 480.0, current_output: 170.45882699685092,level: 6}]->(g);
CREATE (n: Building {id: 152, name:"building_textile_mills", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:152}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 20.140523244344173, level: 4}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:152}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:152}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:152}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 8.056209297737668,level: 4}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:152}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 8.056209297737668,level: 4}]->(g);
CREATE (n: Building {id: 153, name:"building_shipyards", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:153}) CREATE (g)-[r:Demand{max_demand: 120.00000000000001, current_input: 24.16862789321301, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:153}) CREATE (g)-[r:Demand{max_demand: 240.00000000000003, current_input: 45.67948587901159, level: 6}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:153}) CREATE (g)-[r:Demand{max_demand: 120.00000000000001, current_input: 3.7344905388029406, level: 6}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:153}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 11.276506498669066, level: 6}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:153}) CREATE (b)-[r:Supply{max_supply: 420.00000000000006, current_output: 83.86777644542326,level: 6}]->(g);
CREATE (n: Building {id: 154, name:"building_military_shipyards", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:154}) CREATE (g)-[r:Demand{max_demand: 19.928198198198196, current_input: 4.0136433902870845, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:154}) CREATE (g)-[r:Demand{max_demand: 39.85639639639639, current_input: 7.58591540157282, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:154}) CREATE (g)-[r:Demand{max_demand: 19.928198198198196, current_input: 6.444094582747849, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:154}) CREATE (g)-[r:Demand{max_demand: 39.85639639639639, current_input: 1.2403611271093495, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:154}) CREATE (g)-[r:Demand{max_demand: 9.964099099099098, current_input: 3.7453409414791183, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:154}) CREATE (b)-[r:Supply{max_supply: 89.67689189189188, current_output: 20.125402308300828,level: 2}]->(g);
CREATE (n: Building {id: 155, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:155}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.8989321896447935, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:155}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.4512872496284903, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:155}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 44.621473751148976,level: 3}]->(g);
CREATE (n: Building {id: 156, name:"building_vineyard_plantation", level:2});
MATCH (g: Goods{code: 39}), (b: Building{id:156}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 157, name:"building_livestock_ranch", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:157}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 13.182618669648562, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:157}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 19.350496661713205, level: 4}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:157}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 32.53311533136177,level: 4}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:157}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 8.133278832840443,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:157}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 40.66639416420222,level: 4}]->(g);
CREATE (n: Building {id: 158, name:"building_lead_mine", level:2});
MATCH (g: Goods{code: 16}), (b: Building{id:158}) CREATE (g)-[r:Demand{max_demand: 9.98749504950495, current_input: 4.7478706619655116, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:158}) CREATE (g)-[r:Demand{max_demand: 19.974999999999998, current_input: 20.332597213738488, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:158}) CREATE (g)-[r:Demand{max_demand: 19.974999999999998, current_input: 9.663154270443032, level: 2}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:158}) CREATE (b)-[r:Supply{max_supply: 79.89999999999999, current_output: 52.17853373482915,level: 2}]->(g);
CREATE (n: Building {id: 159, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:159}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.121487777278162, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:159}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 31.21487777278162,level: 2}]->(g);
CREATE (n: Building {id: 160, name:"building_port", level:7});
MATCH (g: Goods{code: 18}), (b: Building{id:160}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 10.925207220473567, level: 7}]->(b);
CREATE (n: Building {id: 161, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:161}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 11.289221618152089, level: 3}]->(b);
CREATE (n: Building {id: 162, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:162}) CREATE (g)-[r:Demand{max_demand: 15.684, current_input: 3.1588396656429403, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:162}) CREATE (g)-[r:Demand{max_demand: 31.368, current_input: 5.970308804386813, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:162}) CREATE (g)-[r:Demand{max_demand: 39.21, current_input: 12.679166780486382, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:162}) CREATE (g)-[r:Demand{max_demand: 7.842, current_input: 3.793664870528874, level: 1}]->(b);
CREATE (n: Building {id: 163, name:"building_food_industry", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:163}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 65.91309334824281, level: 5}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:163}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 84.77736096518443, level: 5}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:163}) CREATE (b)-[r:Supply{max_supply: 325.0, current_output: 216.05438834544728,level: 5}]->(g);
CREATE (n: Building {id: 164, name:"building_glassworks", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:164}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 15.226495293003861, level: 4}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:164}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:164}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 33.31163980125451, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:164}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 27.283258298504286,level: 4}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:164}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 27.283258298504286,level: 4}]->(g);
CREATE (n: Building {id: 165, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 16}), (b: Building{id:165}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.753815284444971, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:165}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.837624165428301, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:165}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 57.54863669923964,level: 2}]->(g);
CREATE (n: Building {id: 166, name:"building_iron_mine", level:1});
MATCH (g: Goods{code: 16}), (b: Building{id:166}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.3769076422224855, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:166}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.179022384850308, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:166}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.837624165428301, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:166}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 26.121919266497695,level: 1}]->(g);
CREATE (n: Building {id: 167, name:"building_rye_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:167}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 6.498220316074656, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:167}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.4188120827141506, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:167}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 46.480701824113524,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:167}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 18.59228072964541,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:167}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 9.296140364822705,level: 5}]->(g);
CREATE (n: Building {id: 168, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:168}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.2956546674121405, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:168}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.837624165428301, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:168}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 8.133278832840443,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:168}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 2.0333197082101107,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:168}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 10.166598541050554,level: 1}]->(g);
CREATE (n: Building {id: 169, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:169}) CREATE (g)-[r:Demand{max_demand: 9.999, current_input: 3.1211756285004344, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:169}) CREATE (b)-[r:Supply{max_supply: 99.99, current_output: 31.211756285004338,level: 2}]->(g);
CREATE (n: Building {id: 170, name:"building_railway", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:170}) CREATE (g)-[r:Demand{max_demand: 6.837275, current_input: 1.301346695055912, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:170}) CREATE (g)-[r:Demand{max_demand: 1.7093166666666668, current_input: 1.7399172612797715, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:170}) CREATE (g)-[r:Demand{max_demand: 4.2733, current_input: 1.606263174025417, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:170}) CREATE (b)-[r:Supply{max_supply: 25.6398, current_output: 13.385810906440671,level: 1}]->(g);
CREATE (n: Building {id: 171, name:"building_port", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:171}) CREATE (g)-[r:Demand{max_demand: 19.996, current_input: 6.241726959445412, level: 4}]->(b);
CREATE (n: Building {id: 172, name:"building_coal_mine", level:1});
MATCH (g: Goods{code: 16}), (b: Building{id:172}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.3769076422224855, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:172}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.837624165428301, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:172}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.182878899746544,level: 1}]->(g);
CREATE (n: Building {id: 173, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:173}) CREATE (g)-[r:Demand{max_demand: 19.96259405940594, current_input: 6.578981628553506, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:173}) CREATE (g)-[r:Demand{max_demand: 19.96259405940594, current_input: 9.657152742641763, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:173}) CREATE (b)-[r:Supply{max_supply: 39.925198019801975, current_output: 16.23613839757093,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:173}) CREATE (b)-[r:Supply{max_supply: 9.98129702970297, current_output: 4.0590335927988175,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:173}) CREATE (b)-[r:Supply{max_supply: 49.906495049504954, current_output: 20.29517199036975,level: 2}]->(g);
CREATE (n: Building {id: 174, name:"building_rye_farm", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:174}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.8989321896447935, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:174}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.4512872496284903, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:174}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 27.88842109446811,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:174}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 16.73305265668087,level: 3}]->(g);
CREATE (n: Building {id: 175, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:175}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.837624165428301, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:175}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 58.05148998513962,level: 2}]->(g);
CREATE (n: Building {id: 176, name:"building_port", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:176}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.121487777278162, level: 2}]->(b);
CREATE (n: Building {id: 177, name:"building_rye_farm", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:177}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.8989321896447935, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:177}) CREATE (g)-[r:Demand{max_demand: 2.9999999999999996, current_input: 1.4512872496284903, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:177}) CREATE (b)-[r:Supply{max_supply: 74.99999999999999, current_output: 27.88842109446811,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:177}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 16.73305265668087,level: 3}]->(g);
CREATE (n: Building {id: 178, name:"building_port", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:178}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.121487777278162, level: 2}]->(b);
CREATE (n: Building {id: 180, name:"building_lead_mine", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:180}) CREATE (g)-[r:Demand{max_demand: 4.4565, current_input: 4.53628132580854, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:180}) CREATE (g)-[r:Demand{max_demand: 4.4565, current_input: 2.1558872093231223, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:180}) CREATE (b)-[r:Supply{max_supply: 17.826, current_output: 13.224774418646245,level: 1}]->(g);
CREATE (n: Building {id: 181, name:"building_food_industry", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:181}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 52.73047467859425, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:181}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 67.82188877214755, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:181}) CREATE (b)-[r:Supply{max_supply: 260.0, current_output: 172.84351067635782,level: 4}]->(g);
CREATE (n: Building {id: 182, name:"building_rye_farm", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:182}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 6.498220316074656, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:182}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.4188120827141506, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:182}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 46.480701824113524,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:182}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 18.59228072964541,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:182}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 9.296140364822705,level: 5}]->(g);
CREATE (n: Building {id: 183, name:"building_port", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:183}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 4.682231665917243, level: 3}]->(b);
CREATE (n: Building {id: 184, name:"building_rye_farm", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:184}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.5992881264298626, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:184}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.9675248330856603, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:184}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 18.59228072964541,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:184}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 7.436912291858164,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:184}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 3.718456145929082,level: 2}]->(g);
CREATE (n: Building {id: 185, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:185}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.560743888639081, level: 1}]->(b);
CREATE (n: Building {id: 569, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:569}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 570, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:570}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.560743888639081, level: 1}]->(b);
CREATE (n: Building {id: 723, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:723}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.560743888639081, level: 1}]->(b);
CREATE (n: Building {id: 1153, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1153}) CREATE (g)-[r:Demand{max_demand: 4.628, current_input: 1.4446245433243334, level: 1}]->(b);
CREATE (n: Building {id: 1190, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1190}) CREATE (g)-[r:Demand{max_demand: 2.331, current_input: 0.7276188008835396, level: 1}]->(b);
CREATE (n: Building {id: 1508, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1508}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.121487777278162, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1508}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 31.21487777278162,level: 2}]->(g);
CREATE (n: Building {id: 1514, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1514}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.560743888639081, level: 1}]->(b);
CREATE (n: Building {id: 1580, name:"building_port", level:1});
CREATE (n: Building {id: 1610, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1610}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 1611, name:"building_sugar_plantation", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1611}) CREATE (b)-[r:Supply{max_supply: 28.722, current_output: 28.722,level: 1}]->(g);
CREATE (n: Building {id: 1612, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1612}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.560743888639081, level: 1}]->(b);
CREATE (n: Building {id: 1619, name:"building_sugar_plantation", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:1619}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 91.8,level: 3}]->(g);
CREATE (n: Building {id: 1620, name:"building_coffee_plantation", level:2});
MATCH (g: Goods{code: 41}), (b: Building{id:1620}) CREATE (b)-[r:Supply{max_supply: 39.972, current_output: 40.37172,level: 2}]->(g);
CREATE (n: Building {id: 1621, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1621}) CREATE (b)-[r:Supply{max_supply: 49.435, current_output: 49.92935,level: 2}]->(g);
CREATE (n: Building {id: 1622, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1622}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.560743888639081, level: 1}]->(b);
CREATE (n: Building {id: 1623, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1623}) CREATE (b)-[r:Supply{max_supply: 29.532, current_output: 29.532,level: 1}]->(g);
CREATE (n: Building {id: 1624, name:"building_sugar_plantation", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:1624}) CREATE (b)-[r:Supply{max_supply: 88.48169607843137, current_output: 90.25133,level: 3}]->(g);
CREATE (n: Building {id: 1625, name:"building_coffee_plantation", level:5});
MATCH (g: Goods{code: 41}), (b: Building{id:1625}) CREATE (b)-[r:Supply{max_supply: 98.544, current_output: 102.48576,level: 5}]->(g);
CREATE (n: Building {id: 1626, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1626}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.560743888639081, level: 1}]->(b);
CREATE (n: Building {id: 1642, name:"building_sugar_plantation", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1642}) CREATE (b)-[r:Supply{max_supply: 29.933999999999997, current_output: 30.23334,level: 2}]->(g);
CREATE (n: Building {id: 1643, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1643}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.560743888639081, level: 1}]->(b);
CREATE (n: Building {id: 16778912, name:"building_conscription_center", level:9});
CREATE (n: Building {id: 1771, name:"building_port", level:1});
CREATE (n: Building {id: 2190, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2190}) CREATE (g)-[r:Demand{max_demand: 6.971, current_input: 2.1759891295406066, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2190}) CREATE (b)-[r:Supply{max_supply: 69.71, current_output: 21.759891295406064,level: 2}]->(g);
CREATE (n: Building {id: 2191, name:"building_tea_plantation", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2191}) CREATE (b)-[r:Supply{max_supply: 71.72, current_output: 73.8716,level: 4}]->(g);
CREATE (n: Building {id: 2192, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2192}) CREATE (b)-[r:Supply{max_supply: 47.0, current_output: 47.47,level: 2}]->(g);
CREATE (n: Building {id: 2193, name:"building_coffee_plantation", level:2});
MATCH (g: Goods{code: 41}), (b: Building{id:2193}) CREATE (b)-[r:Supply{max_supply: 38.256, current_output: 38.63856,level: 2}]->(g);
CREATE (n: Building {id: 2194, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2194}) CREATE (g)-[r:Demand{max_demand: 3.394, current_input: 1.0594329516082084, level: 1}]->(b);
CREATE (n: Building {id: 2197, name:"building_sugar_plantation", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:2197}) CREATE (b)-[r:Supply{max_supply: 26.19, current_output: 26.19,level: 1}]->(g);
CREATE (n: Building {id: 2198, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2198}) CREATE (g)-[r:Demand{max_demand: 4.25, current_input: 1.326632305343219, level: 1}]->(b);
CREATE (n: Building {id: 2549, name:"building_tea_plantation", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:2549}) CREATE (b)-[r:Supply{max_supply: 18.14, current_output: 18.14,level: 1}]->(g);
CREATE (n: Building {id: 2550, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2550}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.560743888639081, level: 1}]->(b);
CREATE (n: Building {id: 2697, name:"building_subsistence_farms", level:19});
MATCH (g: Goods{code: 7}), (b: Building{id:2697}) CREATE (b)-[r:Supply{max_supply: 25.9426, current_output: 28.53686,level: 19}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2697}) CREATE (b)-[r:Supply{max_supply: 6.4856454545454545, current_output: 7.13421,level: 19}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2697}) CREATE (b)-[r:Supply{max_supply: 6.4856454545454545, current_output: 7.13421,level: 19}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2697}) CREATE (b)-[r:Supply{max_supply: 6.4856454545454545, current_output: 7.13421,level: 19}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2697}) CREATE (b)-[r:Supply{max_supply: 6.4856454545454545, current_output: 7.13421,level: 19}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2697}) CREATE (b)-[r:Supply{max_supply: 6.4856454545454545, current_output: 7.13421,level: 19}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2697}) CREATE (b)-[r:Supply{max_supply: 6.4856454545454545, current_output: 7.13421,level: 19}]->(g);
CREATE (n: Building {id: 2698, name:"building_urban_center", level:28});
MATCH (g: Goods{code: 10}), (b: Building{id:2698}) CREATE (g)-[r:Demand{max_demand: 27.998875, current_input: 5.3290592299612936, level: 28}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2698}) CREATE (g)-[r:Demand{max_demand: 27.998875, current_input: 11.820241155503599, level: 28}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2698}) CREATE (b)-[r:Supply{max_supply: 559.9776, current_output: 171.49303447963007,level: 28}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2698}) CREATE (b)-[r:Supply{max_supply: 139.9944, current_output: 42.87325861990752,level: 28}]->(g);
CREATE (n: Building {id: 2699, name:"building_subsistence_farms", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:2699}) CREATE (b)-[r:Supply{max_supply: 36.72448, current_output: 36.72448,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2699}) CREATE (b)-[r:Supply{max_supply: 9.18112, current_output: 9.18112,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2699}) CREATE (b)-[r:Supply{max_supply: 9.18112, current_output: 9.18112,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2699}) CREATE (b)-[r:Supply{max_supply: 9.18112, current_output: 9.18112,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2699}) CREATE (b)-[r:Supply{max_supply: 9.18112, current_output: 9.18112,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2699}) CREATE (b)-[r:Supply{max_supply: 9.18112, current_output: 9.18112,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2699}) CREATE (b)-[r:Supply{max_supply: 9.18112, current_output: 9.18112,level: 32}]->(g);
CREATE (n: Building {id: 2700, name:"building_urban_center", level:13});
MATCH (g: Goods{code: 10}), (b: Building{id:2700}) CREATE (g)-[r:Demand{max_demand: 11.0029375, current_input: 2.0942022006620706, level: 13}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2700}) CREATE (g)-[r:Demand{max_demand: 11.0029375, current_input: 11.199914711160888, level: 13}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2700}) CREATE (g)-[r:Demand{max_demand: 11.0029375, current_input: 4.645092871371934, level: 13}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2700}) CREATE (b)-[r:Supply{max_supply: 242.06467857142854, current_output: 130.1097343228855,level: 13}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2700}) CREATE (b)-[r:Supply{max_supply: 55.01469642857142, current_output: 29.57039241916269,level: 13}]->(g);
CREATE (n: Building {id: 2819, name:"building_subsistence_farms", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:2819}) CREATE (b)-[r:Supply{max_supply: 8.2328, current_output: 8.2328,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2819}) CREATE (b)-[r:Supply{max_supply: 2.0582, current_output: 2.0582,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2819}) CREATE (b)-[r:Supply{max_supply: 2.0582, current_output: 2.0582,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2819}) CREATE (b)-[r:Supply{max_supply: 2.0582, current_output: 2.0582,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2819}) CREATE (b)-[r:Supply{max_supply: 2.0582, current_output: 2.0582,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2819}) CREATE (b)-[r:Supply{max_supply: 2.0582, current_output: 2.0582,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2819}) CREATE (b)-[r:Supply{max_supply: 2.0582, current_output: 2.0582,level: 20}]->(g);
CREATE (n: Building {id: 2821, name:"building_subsistence_farms", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2821}) CREATE (b)-[r:Supply{max_supply: 3.1412, current_output: 3.1412,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2821}) CREATE (b)-[r:Supply{max_supply: 0.7853, current_output: 0.7853,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2821}) CREATE (b)-[r:Supply{max_supply: 0.7853, current_output: 0.7853,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2821}) CREATE (b)-[r:Supply{max_supply: 0.7853, current_output: 0.7853,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2821}) CREATE (b)-[r:Supply{max_supply: 0.7853, current_output: 0.7853,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2821}) CREATE (b)-[r:Supply{max_supply: 0.7853, current_output: 0.7853,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2821}) CREATE (b)-[r:Supply{max_supply: 0.7853, current_output: 0.7853,level: 5}]->(g);
CREATE (n: Building {id: 2887, name:"building_subsistence_rice_paddies", level:22});
MATCH (g: Goods{code: 7}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 98.6139, current_output: 98.6139,level: 22}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 16.43565, current_output: 16.43565,level: 22}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 16.43565, current_output: 16.43565,level: 22}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 21.9142, current_output: 21.9142,level: 22}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 21.9142, current_output: 21.9142,level: 22}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 21.9142, current_output: 21.9142,level: 22}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2887}) CREATE (b)-[r:Supply{max_supply: 21.9142, current_output: 21.9142,level: 22}]->(g);
CREATE (n: Building {id: 2904, name:"building_subsistence_farms", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 3.8548, current_output: 3.8548,level: 2}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 0.9637, current_output: 0.9637,level: 2}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 0.9637, current_output: 0.9637,level: 2}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 0.9637, current_output: 0.9637,level: 2}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 0.9637, current_output: 0.9637,level: 2}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 0.9637, current_output: 0.9637,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2904}) CREATE (b)-[r:Supply{max_supply: 0.9637, current_output: 0.9637,level: 2}]->(g);
CREATE (n: Building {id: 2957, name:"building_subsistence_farms", level:12});
MATCH (g: Goods{code: 7}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 8.13024, current_output: 8.13024,level: 12}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 2.03256, current_output: 2.03256,level: 12}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 2.03256, current_output: 2.03256,level: 12}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 2.03256, current_output: 2.03256,level: 12}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 2.03256, current_output: 2.03256,level: 12}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 2.03256, current_output: 2.03256,level: 12}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2957}) CREATE (b)-[r:Supply{max_supply: 2.03256, current_output: 2.03256,level: 12}]->(g);
CREATE (n: Building {id: 3029, name:"building_subsistence_pastures", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:3029}) CREATE (b)-[r:Supply{max_supply: 0.4856, current_output: 0.4856,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3029}) CREATE (b)-[r:Supply{max_supply: 0.7284, current_output: 0.7284,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3029}) CREATE (b)-[r:Supply{max_supply: 0.2428, current_output: 0.2428,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3029}) CREATE (b)-[r:Supply{max_supply: 0.4856, current_output: 0.4856,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3029}) CREATE (b)-[r:Supply{max_supply: 0.4856, current_output: 0.4856,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3029}) CREATE (b)-[r:Supply{max_supply: 0.4856, current_output: 0.4856,level: 10}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3029}) CREATE (b)-[r:Supply{max_supply: 1.29169, current_output: 1.29169,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3029}) CREATE (b)-[r:Supply{max_supply: 0.4856, current_output: 0.4856,level: 10}]->(g);
CREATE (n: Building {id: 3073, name:"building_subsistence_farms", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:3073}) CREATE (b)-[r:Supply{max_supply: 9.58116, current_output: 9.58116,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3073}) CREATE (b)-[r:Supply{max_supply: 2.39529, current_output: 2.39529,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3073}) CREATE (b)-[r:Supply{max_supply: 2.39529, current_output: 2.39529,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3073}) CREATE (b)-[r:Supply{max_supply: 2.39529, current_output: 2.39529,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3073}) CREATE (b)-[r:Supply{max_supply: 2.39529, current_output: 2.39529,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3073}) CREATE (b)-[r:Supply{max_supply: 2.39529, current_output: 2.39529,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3073}) CREATE (b)-[r:Supply{max_supply: 2.39529, current_output: 2.39529,level: 6}]->(g);
CREATE (n: Building {id: 3087, name:"building_subsistence_farms", level:38});
MATCH (g: Goods{code: 7}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 74.72396, current_output: 74.72396,level: 38}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 18.68099, current_output: 18.68099,level: 38}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 18.68099, current_output: 18.68099,level: 38}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 18.68099, current_output: 18.68099,level: 38}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 18.68099, current_output: 18.68099,level: 38}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 18.68099, current_output: 18.68099,level: 38}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3087}) CREATE (b)-[r:Supply{max_supply: 18.68099, current_output: 18.68099,level: 38}]->(g);
CREATE (n: Building {id: 3088, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3088}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.38066238232509647, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3088}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.035804476970062, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3088}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.751767099911271, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3088}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.8443368639278255, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3088}) CREATE (b)-[r:Supply{max_supply: 44.0, current_output: 21.87221490390306,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3088}) CREATE (b)-[r:Supply{max_supply: 16.0, current_output: 7.953532692328386,level: 2}]->(g);
CREATE (n: Building {id: 3089, name:"building_subsistence_farms", level:39});
MATCH (g: Goods{code: 7}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 50.50578, current_output: 50.50578,level: 39}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 12.62644, current_output: 12.62644,level: 39}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 12.62644, current_output: 12.62644,level: 39}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 12.62644, current_output: 12.62644,level: 39}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 12.62644, current_output: 12.62644,level: 39}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 12.62644, current_output: 12.62644,level: 39}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3089}) CREATE (b)-[r:Supply{max_supply: 12.62644, current_output: 12.62644,level: 39}]->(g);
CREATE (n: Building {id: 3090, name:"building_urban_center", level:14});
MATCH (g: Goods{code: 10}), (b: Building{id:3090}) CREATE (g)-[r:Demand{max_demand: 12.647451327433629, current_input: 2.407204476320795, level: 14}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3090}) CREATE (g)-[r:Demand{max_demand: 12.647451327433629, current_input: 12.873869017325164, level: 14}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3090}) CREATE (g)-[r:Demand{max_demand: 12.647451327433629, current_input: 4.753968902846867, level: 14}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3090}) CREATE (g)-[r:Demand{max_demand: 12.647451327433629, current_input: 5.3393546952425615, level: 14}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3090}) CREATE (b)-[r:Supply{max_supply: 278.2441150442478, current_output: 138.31397909077532,level: 14}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3090}) CREATE (b)-[r:Supply{max_supply: 101.17967256637168, current_output: 50.29598959723241,level: 14}]->(g);
CREATE (n: Building {id: 3091, name:"building_subsistence_farms", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3091}) CREATE (b)-[r:Supply{max_supply: 58.2876, current_output: 52.45884,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3091}) CREATE (b)-[r:Supply{max_supply: 14.5719, current_output: 13.11471,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3091}) CREATE (b)-[r:Supply{max_supply: 14.5719, current_output: 13.11471,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3091}) CREATE (b)-[r:Supply{max_supply: 14.5719, current_output: 13.11471,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3091}) CREATE (b)-[r:Supply{max_supply: 14.5719, current_output: 13.11471,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3091}) CREATE (b)-[r:Supply{max_supply: 14.5719, current_output: 13.11471,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3091}) CREATE (b)-[r:Supply{max_supply: 14.5719, current_output: 13.11471,level: 30}]->(g);
CREATE (n: Building {id: 3092, name:"building_subsistence_farms", level:65});
MATCH (g: Goods{code: 7}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 129.2811, current_output: 129.2811,level: 65}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 32.32027, current_output: 32.32027,level: 65}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 32.32027, current_output: 32.32027,level: 65}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 32.32027, current_output: 32.32027,level: 65}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 32.32027, current_output: 32.32027,level: 65}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 32.32027, current_output: 32.32027,level: 65}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3092}) CREATE (b)-[r:Supply{max_supply: 32.32027, current_output: 32.32027,level: 65}]->(g);
CREATE (n: Building {id: 3093, name:"building_urban_center", level:11});
MATCH (g: Goods{code: 10}), (b: Building{id:3093}) CREATE (g)-[r:Demand{max_demand: 10.999999999999998, current_input: 2.0936431027880302, level: 11}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3093}) CREATE (g)-[r:Demand{max_demand: 10.999999999999998, current_input: 11.196924623335338, level: 11}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3093}) CREATE (g)-[r:Demand{max_demand: 10.999999999999998, current_input: 4.13471904951199, level: 11}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3093}) CREATE (g)-[r:Demand{max_demand: 10.999999999999998, current_input: 4.643852751603039, level: 11}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3093}) CREATE (b)-[r:Supply{max_supply: 241.99999999999997, current_output: 120.29718197146683,level: 11}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3093}) CREATE (b)-[r:Supply{max_supply: 87.99999999999999, current_output: 43.744429807806114,level: 11}]->(g);
CREATE (n: Building {id: 3094, name:"building_subsistence_farms", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 33.331799999999994, current_output: 36.66498,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 8.332945454545454, current_output: 9.16624,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 8.332945454545454, current_output: 9.16624,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 8.332945454545454, current_output: 9.16624,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 8.332945454545454, current_output: 9.16624,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 8.332945454545454, current_output: 9.16624,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3094}) CREATE (b)-[r:Supply{max_supply: 8.332945454545454, current_output: 9.16624,level: 30}]->(g);
CREATE (n: Building {id: 3095, name:"building_urban_center", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:3095}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.9033119116254826, level: 10}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3095}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.179022384850308, level: 10}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3095}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.221684319639127, level: 10}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3095}) CREATE (b)-[r:Supply{max_supply: 220.0, current_output: 118.24997236260714,level: 10}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3095}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 26.874993718774345,level: 10}]->(g);
CREATE (n: Building {id: 3096, name:"building_subsistence_farms", level:51});
MATCH (g: Goods{code: 7}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 102.0, current_output: 102.0,level: 51}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 25.5,level: 51}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 25.5,level: 51}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 25.5,level: 51}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 25.5,level: 51}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 25.5,level: 51}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3096}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 25.5,level: 51}]->(g);
CREATE (n: Building {id: 3097, name:"building_urban_center", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3097}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 1.3323183381378376, level: 7}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3097}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 7.125315669395216, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3097}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 2.955179023747389, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3097}) CREATE (b)-[r:Supply{max_supply: 154.0, current_output: 82.774980653825,level: 7}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3097}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 18.812495603142043,level: 7}]->(g);
CREATE (n: Building {id: 3098, name:"building_subsistence_farms", level:54});
MATCH (g: Goods{code: 7}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 106.6284, current_output: 106.6284,level: 54}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 26.6571, current_output: 26.6571,level: 54}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 26.6571, current_output: 26.6571,level: 54}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 26.6571, current_output: 26.6571,level: 54}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 26.6571, current_output: 26.6571,level: 54}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 26.6571, current_output: 26.6571,level: 54}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3098}) CREATE (b)-[r:Supply{max_supply: 26.6571, current_output: 26.6571,level: 54}]->(g);
CREATE (n: Building {id: 3099, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3099}) CREATE (g)-[r:Demand{max_demand: 3.8843106796116507, current_input: 0.7393054784958927, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3099}) CREATE (g)-[r:Demand{max_demand: 3.8843106796116507, current_input: 3.9538485357480107, level: 4}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3099}) CREATE (g)-[r:Demand{max_demand: 3.8843106796116507, current_input: 1.4600484873830144, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3099}) CREATE (g)-[r:Demand{max_demand: 3.8843106796116507, current_input: 1.639833348872331, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3099}) CREATE (b)-[r:Supply{max_supply: 85.45503883495147, current_output: 42.47934031862364,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3099}) CREATE (b)-[r:Supply{max_supply: 31.074553398058253, current_output: 15.447029771935028,level: 4}]->(g);
CREATE (n: Building {id: 3100, name:"building_subsistence_farms", level:125});
MATCH (g: Goods{code: 7}), (b: Building{id:3100}) CREATE (b)-[r:Supply{max_supply: 247.3975, current_output: 247.3975,level: 125}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3100}) CREATE (b)-[r:Supply{max_supply: 61.84937, current_output: 61.84937,level: 125}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3100}) CREATE (b)-[r:Supply{max_supply: 61.84937, current_output: 61.84937,level: 125}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3100}) CREATE (b)-[r:Supply{max_supply: 61.84937, current_output: 61.84937,level: 125}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3100}) CREATE (b)-[r:Supply{max_supply: 61.84937, current_output: 61.84937,level: 125}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3100}) CREATE (b)-[r:Supply{max_supply: 61.84937, current_output: 61.84937,level: 125}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3100}) CREATE (b)-[r:Supply{max_supply: 61.84937, current_output: 61.84937,level: 125}]->(g);
CREATE (n: Building {id: 3101, name:"building_subsistence_farms", level:127});
MATCH (g: Goods{code: 7}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 253.24053636363635, current_output: 278.56459,level: 127}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 63.310127272727264, current_output: 69.64114,level: 127}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 63.310127272727264, current_output: 69.64114,level: 127}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 63.310127272727264, current_output: 69.64114,level: 127}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 63.310127272727264, current_output: 69.64114,level: 127}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 63.310127272727264, current_output: 69.64114,level: 127}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3101}) CREATE (b)-[r:Supply{max_supply: 63.310127272727264, current_output: 69.64114,level: 127}]->(g);
CREATE (n: Building {id: 3102, name:"building_subsistence_farms", level:90});
MATCH (g: Goods{code: 7}), (b: Building{id:3102}) CREATE (b)-[r:Supply{max_supply: 179.6184, current_output: 179.6184,level: 90}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3102}) CREATE (b)-[r:Supply{max_supply: 44.9046, current_output: 44.9046,level: 90}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3102}) CREATE (b)-[r:Supply{max_supply: 44.9046, current_output: 44.9046,level: 90}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3102}) CREATE (b)-[r:Supply{max_supply: 44.9046, current_output: 44.9046,level: 90}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3102}) CREATE (b)-[r:Supply{max_supply: 44.9046, current_output: 44.9046,level: 90}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3102}) CREATE (b)-[r:Supply{max_supply: 44.9046, current_output: 44.9046,level: 90}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3102}) CREATE (b)-[r:Supply{max_supply: 44.9046, current_output: 44.9046,level: 90}]->(g);
CREATE (n: Building {id: 3103, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3103}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.19033119116254824, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3103}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.017902238485031, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:3103}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.3758835499556355, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3103}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.42216843196391274, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3103}) CREATE (b)-[r:Supply{max_supply: 22.0, current_output: 10.93610745195153,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3103}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 3.976766346164193,level: 1}]->(g);
CREATE (n: Building {id: 3150, name:"building_subsistence_farms", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 6.336, current_output: 6.336,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 1.584, current_output: 1.584,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 1.584, current_output: 1.584,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 1.584, current_output: 1.584,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 1.584, current_output: 1.584,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 1.584, current_output: 1.584,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3150}) CREATE (b)-[r:Supply{max_supply: 1.584, current_output: 1.584,level: 5}]->(g);
CREATE (n: Building {id: 3175, name:"building_subsistence_fishing_villages", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:3175}) CREATE (b)-[r:Supply{max_supply: 2.28968, current_output: 2.28968,level: 8}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:3175}) CREATE (b)-[r:Supply{max_supply: 9.15872, current_output: 9.15872,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3175}) CREATE (b)-[r:Supply{max_supply: 1.14484, current_output: 1.14484,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3175}) CREATE (b)-[r:Supply{max_supply: 3.43452, current_output: 3.43452,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3175}) CREATE (b)-[r:Supply{max_supply: 2.28968, current_output: 2.28968,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3175}) CREATE (b)-[r:Supply{max_supply: 2.28968, current_output: 2.28968,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3175}) CREATE (b)-[r:Supply{max_supply: 2.28968, current_output: 2.28968,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3175}) CREATE (b)-[r:Supply{max_supply: 2.28968, current_output: 2.28968,level: 8}]->(g);
CREATE (n: Building {id: 3176, name:"building_subsistence_fishing_villages", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 2.3255, current_output: 2.3255,level: 10}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 9.302, current_output: 9.302,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 1.16275, current_output: 1.16275,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 3.48825, current_output: 3.48825,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 2.3255, current_output: 2.3255,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 2.3255, current_output: 2.3255,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 2.3255, current_output: 2.3255,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 2.3255, current_output: 2.3255,level: 10}]->(g);
CREATE (n: Building {id: 3178, name:"building_subsistence_fishing_villages", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:3178}) CREATE (b)-[r:Supply{max_supply: 4.21308, current_output: 4.21308,level: 9}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:3178}) CREATE (b)-[r:Supply{max_supply: 16.85232, current_output: 16.85232,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3178}) CREATE (b)-[r:Supply{max_supply: 2.10654, current_output: 2.10654,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3178}) CREATE (b)-[r:Supply{max_supply: 6.31962, current_output: 6.31962,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3178}) CREATE (b)-[r:Supply{max_supply: 4.21308, current_output: 4.21308,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3178}) CREATE (b)-[r:Supply{max_supply: 4.21308, current_output: 4.21308,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3178}) CREATE (b)-[r:Supply{max_supply: 4.21308, current_output: 4.21308,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3178}) CREATE (b)-[r:Supply{max_supply: 4.21308, current_output: 4.21308,level: 9}]->(g);
CREATE (n: Building {id: 3179, name:"building_subsistence_farms", level:11});
MATCH (g: Goods{code: 7}), (b: Building{id:3179}) CREATE (b)-[r:Supply{max_supply: 21.19744, current_output: 21.19744,level: 11}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3179}) CREATE (b)-[r:Supply{max_supply: 5.29936, current_output: 5.29936,level: 11}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3179}) CREATE (b)-[r:Supply{max_supply: 5.29936, current_output: 5.29936,level: 11}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3179}) CREATE (b)-[r:Supply{max_supply: 5.29936, current_output: 5.29936,level: 11}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3179}) CREATE (b)-[r:Supply{max_supply: 5.29936, current_output: 5.29936,level: 11}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3179}) CREATE (b)-[r:Supply{max_supply: 5.29936, current_output: 5.29936,level: 11}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3179}) CREATE (b)-[r:Supply{max_supply: 5.29936, current_output: 5.29936,level: 11}]->(g);
CREATE (n: Building {id: 16780497, name:"building_lead_mine", level:1});
MATCH (g: Goods{code: 16}), (b: Building{id:16780497}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.3769076422224855, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16780497}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.179022384850308, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16780497}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.837624165428301, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:16780497}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 26.121919266497695,level: 1}]->(g);
CREATE (n: Building {id: 3412, name:"building_subsistence_orchards", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:3412}) CREATE (b)-[r:Supply{max_supply: 3.63208, current_output: 3.63208,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3412}) CREATE (b)-[r:Supply{max_supply: 1.81604, current_output: 1.81604,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3412}) CREATE (b)-[r:Supply{max_supply: 5.44812, current_output: 5.44812,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3412}) CREATE (b)-[r:Supply{max_supply: 3.63208, current_output: 3.63208,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3412}) CREATE (b)-[r:Supply{max_supply: 3.63208, current_output: 3.63208,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3412}) CREATE (b)-[r:Supply{max_supply: 3.63208, current_output: 3.63208,level: 9}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3412}) CREATE (b)-[r:Supply{max_supply: 9.66134, current_output: 9.66134,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3412}) CREATE (b)-[r:Supply{max_supply: 3.63208, current_output: 3.63208,level: 9}]->(g);
CREATE (n: Building {id: 3750, name:"building_subsistence_orchards", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 2.4693, current_output: 2.4693,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 1.23465, current_output: 1.23465,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 3.70395, current_output: 3.70395,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 2.4693, current_output: 2.4693,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 2.4693, current_output: 2.4693,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 2.4693, current_output: 2.4693,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 6.56833, current_output: 6.56833,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3750}) CREATE (b)-[r:Supply{max_supply: 2.4693, current_output: 2.4693,level: 5}]->(g);
CREATE (n: Building {id: 3767, name:"building_subsistence_farms", level:73});
MATCH (g: Goods{code: 7}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 145.14152, current_output: 145.14152,level: 73}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 36.28538, current_output: 36.28538,level: 73}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 36.28538, current_output: 36.28538,level: 73}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 36.28538, current_output: 36.28538,level: 73}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 36.28538, current_output: 36.28538,level: 73}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 36.28538, current_output: 36.28538,level: 73}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 36.28538, current_output: 36.28538,level: 73}]->(g);
CREATE (n: Building {id: 3821, name:"building_barracks", level:17});
MATCH (g: Goods{code: 0}), (b: Building{id:3821}) CREATE (g)-[r:Demand{max_demand: 13.99984, current_input: 6.286840091529205, level: 17}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3821}) CREATE (g)-[r:Demand{max_demand: 29.99973, current_input: 1.6456688721818558, level: 17}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3821}) CREATE (g)-[r:Demand{max_demand: 3.99976, current_input: 2.678592053299382, level: 17}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3821}) CREATE (g)-[r:Demand{max_demand: 1.99988, current_input: 0.6590913856264191, level: 17}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3821}) CREATE (g)-[r:Demand{max_demand: 1.99988, current_input: 0.646692477963762, level: 17}]->(b);
CREATE (n: Building {id: 3822, name:"building_barracks", level:7});
MATCH (g: Goods{code: 0}), (b: Building{id:3822}) CREATE (g)-[r:Demand{max_demand: 6.99993, current_input: 3.1434245364159894, level: 7}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3822}) CREATE (g)-[r:Demand{max_demand: 13.99993, current_input: 0.767981878961075, level: 7}]->(b);
CREATE (n: Building {id: 3823, name:"building_barracks", level:4});
MATCH (g: Goods{code: 0}), (b: Building{id:3823}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.8981302774216284, level: 4}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3823}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 0.4388489822226682, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3823}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.3182618669648563, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3823}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.2934625636813448, level: 4}]->(b);
CREATE (n: Building {id: 3824, name:"building_barracks", level:11});
MATCH (g: Goods{code: 0}), (b: Building{id:3824}) CREATE (g)-[r:Demand{max_demand: 10.99978, current_input: 4.93961773148844, level: 11}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3824}) CREATE (g)-[r:Demand{max_demand: 21.99978, current_input: 1.2068226327653264, level: 11}]->(b);
CREATE (n: Building {id: 3825, name:"building_barracks", level:25});
MATCH (g: Goods{code: 0}), (b: Building{id:3825}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 8.981302774216285, level: 25}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3825}) CREATE (g)-[r:Demand{max_demand: 44.0, current_input: 2.4136694022246754, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3825}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 4.018129167699135, level: 25}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3825}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.3182618669648563, level: 25}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3825}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.2934625636813448, level: 25}]->(b);
CREATE (n: Building {id: 3826, name:"building_barracks", level:13});
MATCH (g: Goods{code: 0}), (b: Building{id:3826}) CREATE (g)-[r:Demand{max_demand: 10.99982, current_input: 4.939635694093988, level: 13}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3826}) CREATE (g)-[r:Demand{max_demand: 25.99974, current_input: 1.4262449296317494, level: 13}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3826}) CREATE (g)-[r:Demand{max_demand: 2.99988, current_input: 0.9886568523676332, level: 13}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3826}) CREATE (g)-[r:Demand{max_demand: 1.99992, current_input: 0.6467054125893987, level: 13}]->(b);
CREATE (n: Building {id: 3827, name:"building_barracks", level:7});
MATCH (g: Goods{code: 0}), (b: Building{id:3827}) CREATE (g)-[r:Demand{max_demand: 2.99999, current_input: 1.3471909254810555, level: 7}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3827}) CREATE (g)-[r:Demand{max_demand: 13.99986, current_input: 0.7679780390324804, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3827}) CREATE (g)-[r:Demand{max_demand: 6.99986, current_input: 2.3069121280231544, level: 7}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3827}) CREATE (g)-[r:Demand{max_demand: 5.99991, current_input: 1.9401647426143342, level: 7}]->(b);
CREATE (n: Building {id: 3828, name:"building_barracks", level:12});
MATCH (g: Goods{code: 0}), (b: Building{id:3828}) CREATE (g)-[r:Demand{max_demand: 10.99992, current_input: 4.9396806006078595, level: 12}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3828}) CREATE (g)-[r:Demand{max_demand: 21.99996, current_input: 1.2068325068674264, level: 12}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3828}) CREATE (g)-[r:Demand{max_demand: 1.99992, current_input: 1.3393228141774756, level: 12}]->(b);
CREATE (n: Building {id: 3829, name:"building_barracks", level:17});
MATCH (g: Goods{code: 0}), (b: Building{id:3829}) CREATE (g)-[r:Demand{max_demand: 13.999833333333333, current_input: 6.286837097761614, level: 17}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3829}) CREATE (g)-[r:Demand{max_demand: 33.999655555555556, current_input: 1.8650892795596052, level: 17}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3829}) CREATE (g)-[r:Demand{max_demand: 5.9998, current_input: 1.977326887353936, level: 17}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3829}) CREATE (g)-[r:Demand{max_demand: 5.9998, current_input: 1.940129172393833, level: 17}]->(b);
CREATE (n: Building {id: 3830, name:"building_barracks", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:3830}) CREATE (g)-[r:Demand{max_demand: 9.935, current_input: 4.461462153091939, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3830}) CREATE (g)-[r:Demand{max_demand: 19.87, current_input: 1.0899911595955523, level: 10}]->(b);
CREATE (n: Building {id: 3831, name:"building_barracks", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:3831}) CREATE (g)-[r:Demand{max_demand: 5.9999666666666664, current_input: 0.3291349081295752, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3831}) CREATE (g)-[r:Demand{max_demand: 5.9999666666666664, current_input: 1.9773818149317262, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3831}) CREATE (g)-[r:Demand{max_demand: 5.9999666666666664, current_input: 1.9401830666673197, level: 3}]->(b);
CREATE (n: Building {id: 3832, name:"building_barracks", level:4});
MATCH (g: Goods{code: 1}), (b: Building{id:3832}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.32913673666700116, level: 4}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3832}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.339376389233045, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:3832}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 1.9773928004472845, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:3832}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 1.9401938455220171, level: 4}]->(b);
CREATE (n: Building {id: 3833, name:"building_barracks", level:14});
MATCH (g: Goods{code: 0}), (b: Building{id:3833}) CREATE (g)-[r:Demand{max_demand: 12.99984, current_input: 5.837774952818392, level: 14}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:3833}) CREATE (g)-[r:Demand{max_demand: 25.99982, current_input: 1.4262493181215716, level: 14}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:3833}) CREATE (g)-[r:Demand{max_demand: 1.9999, current_input: 1.3393094204135834, level: 14}]->(b);
CREATE (n: Building {id: 3834, name:"building_naval_base", level:30});
MATCH (g: Goods{code: 5}), (b: Building{id:3834}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 14.156734257536902, level: 30}]->(b);
CREATE (n: Building {id: 3835, name:"building_naval_base", level:40});
MATCH (g: Goods{code: 5}), (b: Building{id:3835}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 6.2918818922386235, level: 40}]->(b);
CREATE (n: Building {id: 3836, name:"building_naval_base", level:4});
MATCH (g: Goods{code: 5}), (b: Building{id:3836}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.6291881892238623, level: 4}]->(b);
CREATE (n: Building {id: 3837, name:"building_naval_base", level:33});
MATCH (g: Goods{code: 5}), (b: Building{id:3837}) CREATE (g)-[r:Demand{max_demand: 94.99908, current_input: 14.94307478078321, level: 33}]->(b);
CREATE (n: Building {id: 3838, name:"building_naval_base", level:27});
MATCH (g: Goods{code: 5}), (b: Building{id:3838}) CREATE (g)-[r:Demand{max_demand: 81.0, current_input: 12.741060831783212, level: 27}]->(b);
CREATE (n: Building {id: 3839, name:"building_naval_base", level:24});
MATCH (g: Goods{code: 5}), (b: Building{id:3839}) CREATE (g)-[r:Demand{max_demand: 23.99976, current_input: 3.7750913840518208, level: 24}]->(b);
CREATE (n: Building {id: 3840, name:"building_naval_base", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:3840}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.31459409461193116, level: 2}]->(b);
CREATE (n: Building {id: 3841, name:"building_naval_base", level:6});
MATCH (g: Goods{code: 5}), (b: Building{id:3841}) CREATE (g)-[r:Demand{max_demand: 18.0, current_input: 2.831346851507381, level: 6}]->(b);
CREATE (n: Building {id: 3842, name:"building_naval_base", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:3842}) CREATE (g)-[r:Demand{max_demand: 4.99998, current_input: 0.7864820905888819, level: 3}]->(b);
CREATE (n: Building {id: 3843, name:"building_naval_base", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:3843}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.31459409461193116, level: 2}]->(b);
CREATE (n: Building {id: 3844, name:"building_naval_base", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:3844}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.31459409461193116, level: 2}]->(b);
CREATE (n: Building {id: 3845, name:"building_naval_base", level:6});
MATCH (g: Goods{code: 5}), (b: Building{id:3845}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.9437822838357935, level: 6}]->(b);
CREATE (n: Building {id: 3846, name:"building_naval_base", level:6});
MATCH (g: Goods{code: 5}), (b: Building{id:3846}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.9437822838357935, level: 6}]->(b);
CREATE (n: Building {id: 3847, name:"building_naval_base", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:3847}) CREATE (g)-[r:Demand{max_demand: 1.9899999999999998, current_input: 0.31302112413887145, level: 2}]->(b);
CREATE (n: Building {id: 16781143, name:"building_subsistence_rice_paddies", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 38.18056, current_output: 38.18056,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 6.36342, current_output: 6.36342,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 6.36342, current_output: 6.36342,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 8.48457, current_output: 8.48457,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 8.48457, current_output: 8.48457,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 8.48457, current_output: 8.48457,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 8.48457, current_output: 8.48457,level: 9}]->(g);
CREATE (n: Building {id: 4132, name:"building_trade_center", level:167});
CREATE (n: Building {id: 16781399, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 4236, name:"building_chemical_plants", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:4236}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 3.288413791775895, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4236}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.2336564092033617, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:4236}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 19.48407452907897,level: 1}]->(g);
CREATE (n: Building {id: 4377, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 4550, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 4637, name:"building_conscription_center", level:6});
CREATE (n: Building {id: 4712, name:"building_subsistence_farms", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:4712}) CREATE (b)-[r:Supply{max_supply: 4.0824, current_output: 4.0824,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:4712}) CREATE (b)-[r:Supply{max_supply: 1.0206, current_output: 1.0206,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:4712}) CREATE (b)-[r:Supply{max_supply: 1.0206, current_output: 1.0206,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:4712}) CREATE (b)-[r:Supply{max_supply: 1.0206, current_output: 1.0206,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:4712}) CREATE (b)-[r:Supply{max_supply: 1.0206, current_output: 1.0206,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:4712}) CREATE (b)-[r:Supply{max_supply: 1.0206, current_output: 1.0206,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:4712}) CREATE (b)-[r:Supply{max_supply: 1.0206, current_output: 1.0206,level: 4}]->(g);
CREATE (n: Building {id: 4759, name:"building_conscription_center", level:10});
CREATE (n: Building {id: 4785, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:4785}) CREATE (g)-[r:Demand{max_demand: 15.588, current_input: 3.1395047633283695, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4785}) CREATE (g)-[r:Demand{max_demand: 31.176, current_input: 5.933765215683604, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4785}) CREATE (g)-[r:Demand{max_demand: 38.97, current_input: 12.601559026665502, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4785}) CREATE (g)-[r:Demand{max_demand: 7.794, current_input: 3.770444274534818, level: 1}]->(b);
CREATE (n: Building {id: 4877, name:"building_port", level:1});
CREATE (n: Building {id: 4894, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4894}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.7099357348764475, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:4894}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 10.39827842648661, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4894}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 28.409804499475154,level: 1}]->(g);
CREATE (n: Building {id: 4895, name:"building_conscription_center", level:6});
CREATE (n: Building {id: 4952, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:4952}) CREATE (g)-[r:Demand{max_demand: 19.992, current_input: 4.026493407009288, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4952}) CREATE (g)-[r:Demand{max_demand: 39.984, current_input: 7.610202347443329, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:4952}) CREATE (g)-[r:Demand{max_demand: 49.98, current_input: 16.1618147331984, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:4952}) CREATE (g)-[r:Demand{max_demand: 9.996, current_input: 4.83568911576213, level: 1}]->(b);
CREATE (n: Building {id: 4986, name:"building_conscription_center", level:5});
CREATE (n: Building {id: 5003, name:"building_conscription_center", level:7});
CREATE (n: Building {id: 5029, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5029}) CREATE (g)-[r:Demand{max_demand: 15.32, current_input: 3.0855281610335275, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5029}) CREATE (g)-[r:Demand{max_demand: 30.64, current_input: 5.831747697220479, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5029}) CREATE (g)-[r:Demand{max_demand: 38.3, current_input: 12.384904047248874, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5029}) CREATE (g)-[r:Demand{max_demand: 7.66, current_input: 3.705620110718079, level: 1}]->(b);
CREATE (n: Building {id: 5035, name:"building_conscription_center", level:10});
CREATE (n: Building {id: 5045, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 5047, name:"building_conscription_center", level:7});
CREATE (n: Building {id: 5338, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5338}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.7099357348764475, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:5338}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 10.39827842648661, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5338}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 28.409804499475154,level: 1}]->(g);
CREATE (n: Building {id: 5357, name:"building_paper_mills", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5357}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.7099357348764475, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:5357}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.096137930591965, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:5357}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 10.498074447761066,level: 1}]->(g);
CREATE (n: Building {id: 5471, name:"building_food_industry", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:5471}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 13.182618669648562, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:5471}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 45.21459251476503, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:5471}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 23.267395667971247,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:5471}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 39.88696400223642,level: 1}]->(g);
CREATE (n: Building {id: 5611, name:"building_sulfur_mine", level:1});
MATCH (g: Goods{code: 16}), (b: Building{id:5611}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.3769076422224855, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5611}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.179022384850308, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5611}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.837624165428301, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:5611}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 26.121919266497695,level: 1}]->(g);
CREATE (n: Building {id: 5634, name:"building_barracks", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:5634}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.4490651387108142, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:5634}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.10971224555566705, level: 1}]->(b);
CREATE (n: Building {id: 5646, name:"building_artillery_foundries", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:5646}) CREATE (g)-[r:Demand{max_demand: 6.1935, current_input: 2.0027650970401023, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:5646}) CREATE (g)-[r:Demand{max_demand: 4.129, current_input: 0.12849759528931115, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:5646}) CREATE (b)-[r:Supply{max_supply: 10.3225, current_output: 1.8295929083117242,level: 1}]->(g);
CREATE (n: Building {id: 5651, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:5651}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.560743888639081, level: 1}]->(b);
CREATE (n: Building {id: 5743, name:"building_gold_fields", level:1});
MATCH (g: Goods{code: 50}), (b: Building{id:5743}) CREATE (b)-[r:Supply{max_supply: 10.08, current_output: 10.08,level: 1}]->(g);
CREATE (n: Building {id: 5793, name:"building_food_industry", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:5793}) CREATE (g)-[r:Demand{max_demand: 1.1876, current_input: 0.3913919483018658, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:5793}) CREATE (g)-[r:Demand{max_demand: 1.1876, current_input: 1.3424212517633736, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:5793}) CREATE (b)-[r:Supply{max_supply: 1.03915, current_output: 0.6908089773820663,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:5793}) CREATE (b)-[r:Supply{max_supply: 1.7814, current_output: 1.1842439612263993,level: 1}]->(g);
CREATE (n: Building {id: 33560302, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33560302}) CREATE (g)-[r:Demand{max_demand: 4.788, current_input: 0.9643282529391991, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33560302}) CREATE (g)-[r:Demand{max_demand: 9.576, current_input: 1.8226114865725622, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33560302}) CREATE (g)-[r:Demand{max_demand: 11.97, current_input: 3.870686721816424, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33560302}) CREATE (g)-[r:Demand{max_demand: 2.394, current_input: 1.1581272252035353, level: 1}]->(b);
