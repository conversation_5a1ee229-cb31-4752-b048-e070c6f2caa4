CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:66.42857142857143, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:97.30000000000001, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:77.5368510188762, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:20.794359177131145, pop_demand:2550.731869044088});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:17.810068853077222, pop_demand:541.438910190064});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:21.45196890087378, pop_demand:294.27191697441975});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:18.204805939326295, pop_demand:399.6932142755795});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:39.21041336404975, pop_demand:50.269063788555556});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:23.96649982482502, pop_demand:744.1997625694739});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:30.62119453042651, pop_demand:568.0633871977425});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:35.141407222092134, pop_demand:19.290083174350976});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:12.25175844460475, pop_demand:152.21869166666656});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:62.17795335131068, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:32.36669450077538, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40.0, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:45.2003220440554, pop_demand:24.19386256006511});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:38.75, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:103.61227846672905, pop_demand:138.979177312416});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:40.14826858532595, pop_demand:173.7773882008139});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:30.951330872155175, pop_demand:244.05976159354543});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:23.803705771479482, pop_demand:387.64858516512567});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:52.47767746682601, pop_demand:8.532986346548743});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:28.8683786608022, pop_demand:186.4182860446127});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:87.5, pop_demand:52.91552260883856});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:35.63388532995366, pop_demand:16.602603425639856});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:38.4626587061344, pop_demand:576.8982442511557});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:74.00828034339365, pop_demand:165.807049585496});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:71.97272948258492, pop_demand:131.85476855001872});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 837, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:837}) CREATE (g)-[r:Demand{max_demand: 98.99999999999999, current_input: 76.3778082227946, level: 10}]->(b);
CREATE (n: Building {id: 838, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:838}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 45.16010366375407, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:838}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 170.39250787465903, level: 2}]->(b);
CREATE (n: Building {id: 839, name:"building_shipyardslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:839}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 54.19212439650489, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:839}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 136.31400629972723, level: 3}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:839}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 42.82204664868933,level: 3}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:839}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 57.09606219825244,level: 3}]->(g);
CREATE (n: Building {id: 840, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:840}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 81.28818659475732, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:840}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 40.2384, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:840}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 71.37007774781556,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:840}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 57.09606219825244,level: 3}]->(g);
CREATE (n: Building {id: 841, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:841}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 842, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:842}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 843, name:"building_silk_plantationlevel", level:2});
MATCH (g: Goods{code: 20}), (b: Building{id:843}) CREATE (b)-[r:Supply{max_supply: 39.839999999999996, current_output: 40.2384,level: 2}]->(g);
CREATE (n: Building {id: 844, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:844}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 30.4776,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:844}) CREATE (b)-[r:Supply{max_supply: 26.892, current_output: 27.42984,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:844}) CREATE (b)-[r:Supply{max_supply: 17.928, current_output: 18.28656,level: 3}]->(g);
CREATE (n: Building {id: 845, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:845}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.142857142857142, level: 20}]->(b);
CREATE (n: Building {id: 846, name:"building_naval_baselevel", level:15});
MATCH (g: Goods{code: 5}), (b: Building{id:846}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 25.693227989213597, level: 15}]->(b);
CREATE (n: Building {id: 847, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:847}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 14.27401554956311, level: 3}]->(b);
CREATE (n: Building {id: 848, name:"building_hagia_sophialevel", level:1});
CREATE (n: Building {id: 849, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:849}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 850, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:850}) CREATE (b)-[r:Supply{max_supply: 19.919999999999998, current_output: 20.1192,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:850}) CREATE (b)-[r:Supply{max_supply: 17.928, current_output: 18.10728,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:850}) CREATE (b)-[r:Supply{max_supply: 11.952, current_output: 12.07152,level: 2}]->(g);
CREATE (n: Building {id: 851, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:851}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.217391304347826, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:851}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 1}]->(g);
CREATE (n: Building {id: 852, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:852}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.758005183187703, level: 1}]->(b);
CREATE (n: Building {id: 853, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:853}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 36.128082931003256, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:853}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 40.64409329737866,level: 1}]->(g);
CREATE (n: Building {id: 854, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:854}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 855, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:855}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.434782608695652, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:855}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 2}]->(g);
CREATE (n: Building {id: 856, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:856}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.758005183187703, level: 1}]->(b);
CREATE (n: Building {id: 857, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:857}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 68.15700314986361, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:857}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 40.0, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:857}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 2}]->(g);
CREATE (n: Building {id: 858, name:"building_arms_industrylevel", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:858}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 40.0, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:858}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 20.0, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:858}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 2}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:858}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 2}]->(g);
CREATE (n: Building {id: 859, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:859}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.434782608695652, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:859}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 2}]->(g);
CREATE (n: Building {id: 860, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:860}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.434782608695652, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:860}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 2}]->(g);
CREATE (n: Building {id: 861, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:861}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:861}) CREATE (b)-[r:Supply{max_supply: 9.959999999999999, current_output: 10.0596,level: 2}]->(g);
CREATE (n: Building {id: 862, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:862}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 1.2, level: 5}]->(b);
CREATE (n: Building {id: 863, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:863}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 18.064041465501628, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:863}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 45.43800209990907, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:863}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 20.0, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:863}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 48.38670122125136,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:863}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 38.70936097700108,level: 2}]->(g);
CREATE (n: Building {id: 864, name:"building_textile_millslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:864}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 72.25616586200651, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:864}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 81.28818659475732,level: 2}]->(g);
CREATE (n: Building {id: 865, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:865}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
CREATE (n: Building {id: 866, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:866}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 76.194,level: 3}]->(g);
CREATE (n: Building {id: 867, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:867}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.652173913043478, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:867}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:867}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 3}]->(g);
CREATE (n: Building {id: 868, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:868}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.285714285714286, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:868}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 1.2, level: 5}]->(b);
CREATE (n: Building {id: 869, name:"building_furniture_manufacturieslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:869}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 27.096062198252444, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:869}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 68.15700314986361, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:869}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 30.0, level: 3}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:869}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 72.58005183187703,level: 3}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:869}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 58.064041465501624,level: 3}]->(g);
CREATE (n: Building {id: 870, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:870}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
CREATE (n: Building {id: 871, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:871}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 20.869565217391305, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:871}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 160.0,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:871}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 4}]->(g);
CREATE (n: Building {id: 872, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 2}), (b: Building{id:872}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.4, level: 10}]->(b);
CREATE (n: Building {id: 897, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:897}) CREATE (b)-[r:Supply{max_supply: 29.880000000000003, current_output: 34.362,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:897}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 5.727,level: 1}]->(g);
CREATE (n: Building {id: 905, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:905}) CREATE (b)-[r:Supply{max_supply: 89.64, current_output: 91.4328,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:905}) CREATE (b)-[r:Supply{max_supply: 14.94, current_output: 15.2388,level: 3}]->(g);
CREATE (n: Building {id: 906, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:906}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 907, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 2}), (b: Building{id:907}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.4, level: 10}]->(b);
CREATE (n: Building {id: 912, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:912}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 913, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:913}) CREATE (g)-[r:Demand{max_demand: 19.8, current_input: 15.275561644558922, level: 2}]->(b);
CREATE (n: Building {id: 914, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:914}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 108.38424879300977, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:914}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 121.93227989213601,level: 3}]->(g);
CREATE (n: Building {id: 915, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:915}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
CREATE (n: Building {id: 916, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:916}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 917, name:"building_cotton_plantationlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:917}) CREATE (b)-[r:Supply{max_supply: 79.67999999999999, current_output: 80.4768,level: 2}]->(g);
CREATE (n: Building {id: 918, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 2}), (b: Building{id:918}) CREATE (g)-[r:Demand{max_demand: 7.5, current_input: 3.6, level: 15}]->(b);
CREATE (n: Building {id: 919, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:919}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.434782608695652, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:919}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 2}]->(g);
CREATE (n: Building {id: 920, name:"building_tobacco_plantationlevel", level:4});
MATCH (g: Goods{code: 43}), (b: Building{id:920}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 102.588,level: 4}]->(g);
CREATE (n: Building {id: 921, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:921}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 922, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 2}), (b: Building{id:922}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.4, level: 10}]->(b);
CREATE (n: Building {id: 923, name:"building_food_industrylevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:923}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 37.88170886098361, level: 1}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:923}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 29.984203857860173, level: 1}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:923}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 29.691336814494154,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:923}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 50.899434539132834,level: 1}]->(g);
CREATE (n: Building {id: 924, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:924}) CREATE (b)-[r:Supply{max_supply: 39.84, current_output: 39.84,level: 1}]->(g);
CREATE (n: Building {id: 925, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:925}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 926, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:926}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 927, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:927}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 1.2, level: 5}]->(b);
CREATE (n: Building {id: 928, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:928}) CREATE (g)-[r:Demand{max_demand: 19.8, current_input: 15.275561644558922, level: 2}]->(b);
CREATE (n: Building {id: 929, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:929}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 81.28818659475732, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:929}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 40.2384, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:929}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 71.37007774781556,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:929}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 57.09606219825244,level: 3}]->(g);
CREATE (n: Building {id: 930, name:"building_glassworkslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:930}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 136.31400629972723, level: 4}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:930}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:930}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 20.0,level: 4}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:930}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 50.0,level: 4}]->(g);
CREATE (n: Building {id: 931, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:931}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 932, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:932}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
CREATE (n: Building {id: 933, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:933}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 76.194,level: 3}]->(g);
CREATE (n: Building {id: 934, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:934}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.571428571428571, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:934}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.4, level: 10}]->(b);
CREATE (n: Building {id: 935, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:935}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.758005183187703, level: 1}]->(b);
CREATE (n: Building {id: 1986, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1986}) CREATE (b)-[r:Supply{max_supply: 14.94225, current_output: 14.94225,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1986}) CREATE (b)-[r:Supply{max_supply: 7.9692, current_output: 7.9692,level: 1}]->(g);
CREATE (n: Building {id: 1987, name:"building_portlevel", level:1});
CREATE (n: Building {id: 2008, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2008}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 36.128082931003256, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2008}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 40.64409329737866,level: 1}]->(g);
CREATE (n: Building {id: 2009, name:"building_rice_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2009}) CREATE (b)-[r:Supply{max_supply: 69.72, current_output: 84.3612,level: 2}]->(g);
CREATE (n: Building {id: 2010, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:2010}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 76.194,level: 3}]->(g);
CREATE (n: Building {id: 2011, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 2}), (b: Building{id:2011}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.4, level: 10}]->(b);
CREATE (n: Building {id: 2012, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2012}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 2013, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:2013}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 1.2, level: 5}]->(b);
CREATE (n: Building {id: 2014, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2014}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2014}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 2015, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:2015}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 2016, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2016}) CREATE (b)-[r:Supply{max_supply: 34.86, current_output: 41.832,level: 1}]->(g);
CREATE (n: Building {id: 2017, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:2017}) CREATE (b)-[r:Supply{max_supply: 24.9, current_output: 24.9,level: 1}]->(g);
CREATE (n: Building {id: 2018, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2018}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.758005183187703, level: 1}]->(b);
CREATE (n: Building {id: 2064, name:"building_government_administrationlevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:2064}) CREATE (g)-[r:Demand{max_demand: 19.8, current_input: 15.275561644558922, level: 2}]->(b);
CREATE (n: Building {id: 2065, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2065}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 108.38424879300977, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2065}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 121.93227989213601,level: 3}]->(g);
CREATE (n: Building {id: 2066, name:"building_paper_millslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2066}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 136.31400629972723, level: 4}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2066}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 160.0,level: 4}]->(g);
CREATE (n: Building {id: 2067, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:2067}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.652173913043478, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2067}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 180.0,level: 3}]->(g);
CREATE (n: Building {id: 2068, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:2068}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 2069, name:"building_tea_plantationlevel", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2069}) CREATE (b)-[r:Supply{max_supply: 79.68, current_output: 82.0704,level: 4}]->(g);
CREATE (n: Building {id: 2070, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2070}) CREATE (b)-[r:Supply{max_supply: 19.919999999999998, current_output: 20.1192,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2070}) CREATE (b)-[r:Supply{max_supply: 17.928, current_output: 18.10728,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2070}) CREATE (b)-[r:Supply{max_supply: 11.952, current_output: 12.07152,level: 2}]->(g);
CREATE (n: Building {id: 2071, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:2071}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.564409329737867, level: 5}]->(b);
CREATE (n: Building {id: 2072, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2072}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.516010366375406, level: 2}]->(b);
CREATE (n: Building {id: 2073, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2073}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 108.38424879300977, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2073}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 121.93227989213601,level: 3}]->(g);
CREATE (n: Building {id: 2074, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2074}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 18.064041465501628, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2074}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 68.15700314986361, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2074}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 85.64409329737866,level: 2}]->(g);
CREATE (n: Building {id: 2075, name:"building_fishing_wharflevel", level:4});
MATCH (g: Goods{code: 8}), (b: Building{id:2075}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 103.0,level: 4}]->(g);
CREATE (n: Building {id: 2076, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2076}) CREATE (b)-[r:Supply{max_supply: 89.64, current_output: 91.4328,level: 3}]->(g);
CREATE (n: Building {id: 2077, name:"building_silk_plantationlevel", level:2});
MATCH (g: Goods{code: 20}), (b: Building{id:2077}) CREATE (b)-[r:Supply{max_supply: 39.839999999999996, current_output: 40.2384,level: 2}]->(g);
CREATE (n: Building {id: 2078, name:"building_tea_plantationlevel", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:2078}) CREATE (b)-[r:Supply{max_supply: 19.92, current_output: 19.92,level: 1}]->(g);
CREATE (n: Building {id: 2079, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:2079}) CREATE (b)-[r:Supply{max_supply: 24.9, current_output: 24.9,level: 1}]->(g);
CREATE (n: Building {id: 2080, name:"building_cotton_plantationlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2080}) CREATE (b)-[r:Supply{max_supply: 79.67999999999999, current_output: 80.4768,level: 2}]->(g);
CREATE (n: Building {id: 2081, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:2081}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.564409329737867, level: 5}]->(b);
CREATE (n: Building {id: 2082, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2082}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.516010366375406, level: 2}]->(b);
CREATE (n: Building {id: 2083, name:"building_cotton_plantationlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2083}) CREATE (b)-[r:Supply{max_supply: 79.67999999999999, current_output: 80.4768,level: 2}]->(g);
CREATE (n: Building {id: 2084, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:2084}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 76.194,level: 3}]->(g);
CREATE (n: Building {id: 2085, name:"building_tea_plantationlevel", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:2085}) CREATE (b)-[r:Supply{max_supply: 39.839999999999996, current_output: 40.2384,level: 2}]->(g);
CREATE (n: Building {id: 2086, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2086}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2086}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 2087, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:2087}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 1.2, level: 5}]->(b);
CREATE (n: Building {id: 2088, name:"building_tea_plantationlevel", level:5});
MATCH (g: Goods{code: 40}), (b: Building{id:2088}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 103.584,level: 5}]->(g);
CREATE (n: Building {id: 2089, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:2089}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 76.5,level: 3}]->(g);
CREATE (n: Building {id: 2090, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:2090}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 1.2, level: 5}]->(b);
CREATE (n: Building {id: 2091, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2091}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2091}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 2092, name:"building_tea_plantationlevel", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:2092}) CREATE (b)-[r:Supply{max_supply: 39.839999999999996, current_output: 40.2384,level: 2}]->(g);
CREATE (n: Building {id: 2093, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2093}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 2094, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 2}), (b: Building{id:2094}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.4, level: 10}]->(b);
CREATE (n: Building {id: 2100, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:2100}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 2101, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2101}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2101}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 2102, name:"building_tea_plantationlevel", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:2102}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.9552,level: 3}]->(g);
CREATE (n: Building {id: 2103, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:2103}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.434782608695652, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2103}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 2}]->(g);
CREATE (n: Building {id: 2104, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2104}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.758005183187703, level: 1}]->(b);
CREATE (n: Building {id: 2105, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:2105}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.434782608695652, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2105}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 2}]->(g);
CREATE (n: Building {id: 2106, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2106}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2106}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 2107, name:"building_tea_plantationlevel", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:2107}) CREATE (b)-[r:Supply{max_supply: 19.92, current_output: 19.92,level: 1}]->(g);
CREATE (n: Building {id: 2108, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:2108}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 1.2, level: 5}]->(b);
CREATE (n: Building {id: 2109, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2109}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2109}) CREATE (b)-[r:Supply{max_supply: 9.959999999999999, current_output: 10.0596,level: 2}]->(g);
CREATE (n: Building {id: 2110, name:"building_tea_plantationlevel", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:2110}) CREATE (b)-[r:Supply{max_supply: 19.92, current_output: 19.92,level: 1}]->(g);
CREATE (n: Building {id: 2111, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:2111}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 1.2, level: 5}]->(b);
CREATE (n: Building {id: 2112, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:2112}) CREATE (g)-[r:Demand{max_demand: 29.7, current_input: 22.913342466838383, level: 3}]->(b);
CREATE (n: Building {id: 2113, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2113}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2113}) CREATE (b)-[r:Supply{max_supply: 9.959999999999999, current_output: 10.0596,level: 2}]->(g);
CREATE (n: Building {id: 2114, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2114}) CREATE (b)-[r:Supply{max_supply: 49.800000000000004, current_output: 50.298,level: 2}]->(g);
CREATE (n: Building {id: 2115, name:"building_tea_plantationlevel", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:2115}) CREATE (b)-[r:Supply{max_supply: 39.839999999999996, current_output: 40.2384,level: 2}]->(g);
CREATE (n: Building {id: 2116, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 2}), (b: Building{id:2116}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.4, level: 10}]->(b);
CREATE (n: Building {id: 2891, name:"building_subsistence_farmslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 16.9632, current_output: 16.9632,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 2.8272, current_output: 2.8272,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 2.8272, current_output: 2.8272,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 2.8272, current_output: 2.8272,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 2.8272, current_output: 2.8272,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 2.8272, current_output: 2.8272,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2891}) CREATE (b)-[r:Supply{max_supply: 3.95808, current_output: 3.95808,level: 6}]->(g);
CREATE (n: Building {id: 2893, name:"building_subsistence_orchardslevel", level:28});
MATCH (g: Goods{code: 7}), (b: Building{id:2893}) CREATE (b)-[r:Supply{max_supply: 8.575558333333333, current_output: 10.29067,level: 28}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2893}) CREATE (b)-[r:Supply{max_supply: 4.287775000000001, current_output: 5.14533,level: 28}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2893}) CREATE (b)-[r:Supply{max_supply: 12.863333333333333, current_output: 15.436,level: 28}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2893}) CREATE (b)-[r:Supply{max_supply: 8.575558333333333, current_output: 10.29067,level: 28}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2893}) CREATE (b)-[r:Supply{max_supply: 8.575558333333333, current_output: 10.29067,level: 28}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2893}) CREATE (b)-[r:Supply{max_supply: 8.575558333333333, current_output: 10.29067,level: 28}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2893}) CREATE (b)-[r:Supply{max_supply: 28.470858333333336, current_output: 34.16503,level: 28}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2893}) CREATE (b)-[r:Supply{max_supply: 12.005783333333333, current_output: 14.40694,level: 28}]->(g);
CREATE (n: Building {id: 2894, name:"building_subsistence_farmslevel", level:23});
MATCH (g: Goods{code: 7}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 68.724, current_output: 68.724,level: 23}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 11.454, current_output: 11.454,level: 23}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 11.454, current_output: 11.454,level: 23}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 11.454, current_output: 11.454,level: 23}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 11.454, current_output: 11.454,level: 23}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 11.454, current_output: 11.454,level: 23}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2894}) CREATE (b)-[r:Supply{max_supply: 16.0356, current_output: 16.0356,level: 23}]->(g);
CREATE (n: Building {id: 2895, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2895}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 28.39875131244317, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2895}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 125.0,level: 5}]->(g);
CREATE (n: Building {id: 3019, name:"building_subsistence_farmslevel", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:3019}) CREATE (b)-[r:Supply{max_supply: 149.4, current_output: 171.81,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3019}) CREATE (b)-[r:Supply{max_supply: 24.900000000000002, current_output: 28.635,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3019}) CREATE (b)-[r:Supply{max_supply: 24.900000000000002, current_output: 28.635,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3019}) CREATE (b)-[r:Supply{max_supply: 24.900000000000002, current_output: 28.635,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3019}) CREATE (b)-[r:Supply{max_supply: 24.900000000000002, current_output: 28.635,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3019}) CREATE (b)-[r:Supply{max_supply: 24.900000000000002, current_output: 28.635,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3019}) CREATE (b)-[r:Supply{max_supply: 34.86, current_output: 40.089,level: 50}]->(g);
CREATE (n: Building {id: 3020, name:"building_subsistence_farmslevel", level:38});
MATCH (g: Goods{code: 7}), (b: Building{id:3020}) CREATE (b)-[r:Supply{max_supply: 93.05934, current_output: 93.05934,level: 38}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3020}) CREATE (b)-[r:Supply{max_supply: 15.50989, current_output: 15.50989,level: 38}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3020}) CREATE (b)-[r:Supply{max_supply: 15.50989, current_output: 15.50989,level: 38}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3020}) CREATE (b)-[r:Supply{max_supply: 15.50989, current_output: 15.50989,level: 38}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3020}) CREATE (b)-[r:Supply{max_supply: 15.50989, current_output: 15.50989,level: 38}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3020}) CREATE (b)-[r:Supply{max_supply: 15.50989, current_output: 15.50989,level: 38}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3020}) CREATE (b)-[r:Supply{max_supply: 21.71384, current_output: 21.71384,level: 38}]->(g);
CREATE (n: Building {id: 3021, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3021}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.679750262488634, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3021}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3466, name:"building_subsistence_farmslevel", level:25});
MATCH (g: Goods{code: 7}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 74.7,level: 25}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 12.45, current_output: 12.45,level: 25}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 12.45, current_output: 12.45,level: 25}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 12.45, current_output: 12.45,level: 25}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 12.45, current_output: 12.45,level: 25}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 12.45, current_output: 12.45,level: 25}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3466}) CREATE (b)-[r:Supply{max_supply: 17.43, current_output: 17.43,level: 25}]->(g);
CREATE (n: Building {id: 3467, name:"building_subsistence_farmslevel", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 48.74904, current_output: 48.74904,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 8.12484, current_output: 8.12484,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 8.12484, current_output: 8.12484,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 8.12484, current_output: 8.12484,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 8.12484, current_output: 8.12484,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 8.12484, current_output: 8.12484,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3467}) CREATE (b)-[r:Supply{max_supply: 11.37477, current_output: 11.37477,level: 18}]->(g);
CREATE (n: Building {id: 3468, name:"building_subsistence_farmslevel", level:46});
MATCH (g: Goods{code: 7}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 124.28418, current_output: 124.28418,level: 46}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 20.71403, current_output: 20.71403,level: 46}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 20.71403, current_output: 20.71403,level: 46}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 20.71403, current_output: 20.71403,level: 46}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 20.71403, current_output: 20.71403,level: 46}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 20.71403, current_output: 20.71403,level: 46}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3468}) CREATE (b)-[r:Supply{max_supply: 28.99964, current_output: 28.99964,level: 46}]->(g);
CREATE (n: Building {id: 3469, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3469}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.679750262488634, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3469}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3470, name:"building_subsistence_farmslevel", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 41.6259, current_output: 41.6259,level: 15}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 6.93765, current_output: 6.93765,level: 15}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 6.93765, current_output: 6.93765,level: 15}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 6.93765, current_output: 6.93765,level: 15}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 6.93765, current_output: 6.93765,level: 15}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 6.93765, current_output: 6.93765,level: 15}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3470}) CREATE (b)-[r:Supply{max_supply: 9.71271, current_output: 9.71271,level: 15}]->(g);
CREATE (n: Building {id: 3471, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3471}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 11.359500524977268, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3471}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 2}]->(g);
CREATE (n: Building {id: 3472, name:"building_subsistence_farmslevel", level:25});
MATCH (g: Goods{code: 7}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 74.7,level: 25}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 12.45, current_output: 12.45,level: 25}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 12.45, current_output: 12.45,level: 25}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 12.45, current_output: 12.45,level: 25}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 12.45, current_output: 12.45,level: 25}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 12.45, current_output: 12.45,level: 25}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3472}) CREATE (b)-[r:Supply{max_supply: 17.43, current_output: 17.43,level: 25}]->(g);
CREATE (n: Building {id: 3473, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3473}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.679750262488634, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3473}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3474, name:"building_subsistence_farmslevel", level:33});
MATCH (g: Goods{code: 7}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 94.83903, current_output: 94.83903,level: 33}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 15.8065, current_output: 15.8065,level: 33}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 15.8065, current_output: 15.8065,level: 33}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 15.8065, current_output: 15.8065,level: 33}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 15.8065, current_output: 15.8065,level: 33}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 15.8065, current_output: 15.8065,level: 33}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3474}) CREATE (b)-[r:Supply{max_supply: 22.1291, current_output: 22.1291,level: 33}]->(g);
CREATE (n: Building {id: 3475, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3475}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.679750262488634, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3475}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3618, name:"building_subsistence_orchardslevel", level:35});
MATCH (g: Goods{code: 7}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 9.45175, current_output: 11.3421,level: 35}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 4.725875, current_output: 5.67105,level: 35}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 14.177625, current_output: 17.01315,level: 35}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 9.45175, current_output: 11.3421,level: 35}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 9.45175, current_output: 11.3421,level: 35}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 9.45175, current_output: 11.3421,level: 35}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 31.379808333333333, current_output: 37.65577,level: 35}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3618}) CREATE (b)-[r:Supply{max_supply: 13.23245, current_output: 15.87894,level: 35}]->(g);
CREATE (n: Building {id: 3619, name:"building_subsistence_orchardslevel", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 13.51052, current_output: 13.51052,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 6.75526, current_output: 6.75526,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 20.26578, current_output: 20.26578,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 13.51052, current_output: 13.51052,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 13.51052, current_output: 13.51052,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 13.51052, current_output: 13.51052,level: 29}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 44.85492, current_output: 44.85492,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3619}) CREATE (b)-[r:Supply{max_supply: 18.91472, current_output: 18.91472,level: 29}]->(g);
CREATE (n: Building {id: 3636, name:"building_subsistence_farmslevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 23.904, current_output: 27.4896,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 3.9840000000000004, current_output: 4.5816,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 3.9840000000000004, current_output: 4.5816,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 3.9840000000000004, current_output: 4.5816,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 3.9840000000000004, current_output: 4.5816,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 3.9840000000000004, current_output: 4.5816,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3636}) CREATE (b)-[r:Supply{max_supply: 5.5776, current_output: 6.41424,level: 8}]->(g);
CREATE (n: Building {id: 3640, name:"building_subsistence_farmslevel", level:41});
MATCH (g: Goods{code: 7}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 122.508, current_output: 122.508,level: 41}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 20.418, current_output: 20.418,level: 41}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 20.418, current_output: 20.418,level: 41}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 20.418, current_output: 20.418,level: 41}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 20.418, current_output: 20.418,level: 41}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 20.418, current_output: 20.418,level: 41}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3640}) CREATE (b)-[r:Supply{max_supply: 28.5852, current_output: 28.5852,level: 41}]->(g);
CREATE (n: Building {id: 3641, name:"building_subsistence_farmslevel", level:34});
MATCH (g: Goods{code: 7}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 84.13572, current_output: 84.13572,level: 34}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 14.02262, current_output: 14.02262,level: 34}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 14.02262, current_output: 14.02262,level: 34}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 14.02262, current_output: 14.02262,level: 34}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 14.02262, current_output: 14.02262,level: 34}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 14.02262, current_output: 14.02262,level: 34}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3641}) CREATE (b)-[r:Supply{max_supply: 19.63166, current_output: 19.63166,level: 34}]->(g);
CREATE (n: Building {id: 3642, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3642}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 11.359500524977268, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3642}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 2}]->(g);
CREATE (n: Building {id: 3643, name:"building_subsistence_farmslevel", level:26});
MATCH (g: Goods{code: 7}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 77.688, current_output: 77.688,level: 26}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 12.948, current_output: 12.948,level: 26}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 12.948, current_output: 12.948,level: 26}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 12.948, current_output: 12.948,level: 26}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 12.948, current_output: 12.948,level: 26}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 12.948, current_output: 12.948,level: 26}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3643}) CREATE (b)-[r:Supply{max_supply: 18.1272, current_output: 18.1272,level: 26}]->(g);
CREATE (n: Building {id: 3644, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3644}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.679750262488634, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3644}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3645, name:"building_subsistence_farmslevel", level:32});
MATCH (g: Goods{code: 7}), (b: Building{id:3645}) CREATE (b)-[r:Supply{max_supply: 92.88288, current_output: 92.88288,level: 32}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3645}) CREATE (b)-[r:Supply{max_supply: 15.48048, current_output: 15.48048,level: 32}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3645}) CREATE (b)-[r:Supply{max_supply: 15.48048, current_output: 15.48048,level: 32}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3645}) CREATE (b)-[r:Supply{max_supply: 15.48048, current_output: 15.48048,level: 32}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3645}) CREATE (b)-[r:Supply{max_supply: 15.48048, current_output: 15.48048,level: 32}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3645}) CREATE (b)-[r:Supply{max_supply: 15.48048, current_output: 15.48048,level: 32}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3645}) CREATE (b)-[r:Supply{max_supply: 21.67267, current_output: 21.67267,level: 32}]->(g);
CREATE (n: Building {id: 3646, name:"building_subsistence_farmslevel", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 88.8642, current_output: 88.8642,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 14.8107, current_output: 14.8107,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 14.8107, current_output: 14.8107,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 14.8107, current_output: 14.8107,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 14.8107, current_output: 14.8107,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 14.8107, current_output: 14.8107,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3646}) CREATE (b)-[r:Supply{max_supply: 20.73498, current_output: 20.73498,level: 30}]->(g);
CREATE (n: Building {id: 3647, name:"building_subsistence_farmslevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:3647}) CREATE (b)-[r:Supply{max_supply: 18.5052, current_output: 18.5052,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3647}) CREATE (b)-[r:Supply{max_supply: 3.0842, current_output: 3.0842,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3647}) CREATE (b)-[r:Supply{max_supply: 3.0842, current_output: 3.0842,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3647}) CREATE (b)-[r:Supply{max_supply: 3.0842, current_output: 3.0842,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3647}) CREATE (b)-[r:Supply{max_supply: 3.0842, current_output: 3.0842,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3647}) CREATE (b)-[r:Supply{max_supply: 3.0842, current_output: 3.0842,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3647}) CREATE (b)-[r:Supply{max_supply: 4.31788, current_output: 4.31788,level: 8}]->(g);
CREATE (n: Building {id: 3648, name:"building_subsistence_farmslevel", level:36});
MATCH (g: Goods{code: 7}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 107.57016, current_output: 107.57016,level: 36}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 17.92836, current_output: 17.92836,level: 36}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 17.92836, current_output: 17.92836,level: 36}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 17.92836, current_output: 17.92836,level: 36}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 17.92836, current_output: 17.92836,level: 36}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 17.92836, current_output: 17.92836,level: 36}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3648}) CREATE (b)-[r:Supply{max_supply: 25.0997, current_output: 25.0997,level: 36}]->(g);
CREATE (n: Building {id: 3649, name:"building_subsistence_farmslevel", level:38});
MATCH (g: Goods{code: 7}), (b: Building{id:3649}) CREATE (b)-[r:Supply{max_supply: 113.544, current_output: 113.544,level: 38}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3649}) CREATE (b)-[r:Supply{max_supply: 18.924, current_output: 18.924,level: 38}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3649}) CREATE (b)-[r:Supply{max_supply: 18.924, current_output: 18.924,level: 38}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3649}) CREATE (b)-[r:Supply{max_supply: 18.924, current_output: 18.924,level: 38}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3649}) CREATE (b)-[r:Supply{max_supply: 18.924, current_output: 18.924,level: 38}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3649}) CREATE (b)-[r:Supply{max_supply: 18.924, current_output: 18.924,level: 38}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3649}) CREATE (b)-[r:Supply{max_supply: 26.4936, current_output: 26.4936,level: 38}]->(g);
CREATE (n: Building {id: 3650, name:"building_subsistence_farmslevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 11.1594, current_output: 11.1594,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 1.8599, current_output: 1.8599,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 1.8599, current_output: 1.8599,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 1.8599, current_output: 1.8599,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 1.8599, current_output: 1.8599,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 1.8599, current_output: 1.8599,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3650}) CREATE (b)-[r:Supply{max_supply: 2.60386, current_output: 2.60386,level: 4}]->(g);
CREATE (n: Building {id: 3654, name:"building_subsistence_orchardslevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 1.842, current_output: 1.842,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 0.921, current_output: 0.921,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 2.763, current_output: 2.763,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 1.842, current_output: 1.842,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 1.842, current_output: 1.842,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 1.842, current_output: 1.842,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 6.11544, current_output: 6.11544,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3654}) CREATE (b)-[r:Supply{max_supply: 2.5788, current_output: 2.5788,level: 5}]->(g);
CREATE (n: Building {id: 3655, name:"building_subsistence_farmslevel", level:58});
MATCH (g: Goods{code: 7}), (b: Building{id:3655}) CREATE (b)-[r:Supply{max_supply: 173.35098, current_output: 173.35098,level: 58}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3655}) CREATE (b)-[r:Supply{max_supply: 28.89183, current_output: 28.89183,level: 58}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3655}) CREATE (b)-[r:Supply{max_supply: 28.89183, current_output: 28.89183,level: 58}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3655}) CREATE (b)-[r:Supply{max_supply: 28.89183, current_output: 28.89183,level: 58}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3655}) CREATE (b)-[r:Supply{max_supply: 28.89183, current_output: 28.89183,level: 58}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3655}) CREATE (b)-[r:Supply{max_supply: 28.89183, current_output: 28.89183,level: 58}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3655}) CREATE (b)-[r:Supply{max_supply: 40.44856, current_output: 40.44856,level: 58}]->(g);
CREATE (n: Building {id: 3656, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3656}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.679750262488634, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3656}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3658, name:"building_subsistence_orchardslevel", level:35});
MATCH (g: Goods{code: 7}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 14.2485, current_output: 14.2485,level: 35}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 7.12425, current_output: 7.12425,level: 35}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 21.37275, current_output: 21.37275,level: 35}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 14.2485, current_output: 14.2485,level: 35}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 14.2485, current_output: 14.2485,level: 35}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 14.2485, current_output: 14.2485,level: 35}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 47.30502, current_output: 47.30502,level: 35}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3658}) CREATE (b)-[r:Supply{max_supply: 19.9479, current_output: 19.9479,level: 35}]->(g);
CREATE (n: Building {id: 3952, name:"building_subsistence_farmslevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:3952}) CREATE (b)-[r:Supply{max_supply: 5.0832, current_output: 5.0832,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3952}) CREATE (b)-[r:Supply{max_supply: 0.8472, current_output: 0.8472,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3952}) CREATE (b)-[r:Supply{max_supply: 0.8472, current_output: 0.8472,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3952}) CREATE (b)-[r:Supply{max_supply: 0.8472, current_output: 0.8472,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3952}) CREATE (b)-[r:Supply{max_supply: 0.8472, current_output: 0.8472,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3952}) CREATE (b)-[r:Supply{max_supply: 0.8472, current_output: 0.8472,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3952}) CREATE (b)-[r:Supply{max_supply: 1.18608, current_output: 1.18608,level: 4}]->(g);
CREATE (n: Building {id: 3953, name:"building_subsistence_pastureslevel", level:19});
MATCH (g: Goods{code: 7}), (b: Building{id:3953}) CREATE (b)-[r:Supply{max_supply: 5.82929, current_output: 5.82929,level: 19}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3953}) CREATE (b)-[r:Supply{max_supply: 8.74394, current_output: 8.74394,level: 19}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3953}) CREATE (b)-[r:Supply{max_supply: 2.91464, current_output: 2.91464,level: 19}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3953}) CREATE (b)-[r:Supply{max_supply: 5.82929, current_output: 5.82929,level: 19}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3953}) CREATE (b)-[r:Supply{max_supply: 5.82929, current_output: 5.82929,level: 19}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3953}) CREATE (b)-[r:Supply{max_supply: 5.82929, current_output: 5.82929,level: 19}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3953}) CREATE (b)-[r:Supply{max_supply: 19.35325, current_output: 19.35325,level: 19}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3953}) CREATE (b)-[r:Supply{max_supply: 8.16101, current_output: 8.16101,level: 19}]->(g);
CREATE (n: Building {id: 3966, name:"building_trade_centerlevel", level:14});
CREATE (n: Building {id: 4050, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4051, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4112, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4113, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4343, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4344, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4345, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4346, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4347, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4348, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4463, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4464, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4475, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4477, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4478, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4479, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4480, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4481, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4482, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4483, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4484, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4486, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4644, name:"building_conscription_centerlevel", level:1});
