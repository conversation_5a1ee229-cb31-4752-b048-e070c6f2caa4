CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:87.5, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:26.80479822533146, pop_demand:294.566664296394});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:21.72765421923763, pop_demand:28.158172351243017});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:10.01769555742015, pop_demand:55.46335873493728});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:15.952020178416987, pop_demand:15.84022001506271});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:37.9529130506387, pop_demand:75.17072516350167});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:38.04074901088979, pop_demand:75.57134707897085});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:38.762605369815006, pop_demand:46.844040833333345});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:10.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:1.2097609523809525});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:18.70014655698647, pop_demand:21.8979839633423});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:19.332838601885406, pop_demand:30.69739676285469});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:14.422635679022404, pop_demand:1.55096575788999});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:7.092602000000002});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:7.84617642940968, pop_demand:0.39598521721029284});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:29.337701991059582, pop_demand:91.54085880658249});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:5.6455511111111125});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:1.4113877777777781});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 1806, name:"building_government_administrationlevel", level:3});
CREATE (n: Building {id: 1807, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1807}) CREATE (b)-[r:Supply{max_supply: 59.61599999999999, current_output: 75.11616,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1807}) CREATE (b)-[r:Supply{max_supply: 9.936, current_output: 12.51936,level: 2}]->(g);
CREATE (n: Building {id: 1808, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1808}) CREATE (b)-[r:Supply{max_supply: 24.915, current_output: 24.915,level: 1}]->(g);
CREATE (n: Building {id: 1809, name:"building_naval_baselevel", level:4});
MATCH (g: Goods{code: 5}), (b: Building{id:1809}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 0.0, level: 4}]->(b);
CREATE (n: Building {id: 1810, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1810}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1811, name:"building_barrackslevel", level:4});
MATCH (g: Goods{code: 1}), (b: Building{id:1811}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1811}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.0, level: 4}]->(b);
CREATE (n: Building {id: 1812, name:"building_government_administrationlevel", level:1});
CREATE (n: Building {id: 1813, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1813}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
CREATE (n: Building {id: 1814, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1814}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 119.57680435847129, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1814}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 1}]->(g);
CREATE (n: Building {id: 1815, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1815}) CREATE (b)-[r:Supply{max_supply: 51.006, current_output: 56.61666,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1815}) CREATE (b)-[r:Supply{max_supply: 8.501, current_output: 9.43611,level: 2}]->(g);
CREATE (n: Building {id: 1816, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:1816}) CREATE (g)-[r:Demand{max_demand: 2.99799, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1816}) CREATE (g)-[r:Demand{max_demand: 2.99799, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1816}) CREATE (g)-[r:Demand{max_demand: 2.99799, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 1817, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1817}) CREATE (g)-[r:Demand{max_demand: 9.992, current_input: 29.870285728746133, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1817}) CREATE (g)-[r:Demand{max_demand: 29.976, current_input: 41.055439332197366, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1817}) CREATE (b)-[r:Supply{max_supply: 44.964, current_output: 44.964,level: 1}]->(g);
CREATE (n: Building {id: 1818, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1818}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
CREATE (n: Building {id: 1819, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1819}) CREATE (b)-[r:Supply{max_supply: 29.939999999999998, current_output: 37.425,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1819}) CREATE (b)-[r:Supply{max_supply: 4.99, current_output: 6.2375,level: 1}]->(g);
CREATE (n: Building {id: 1820, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1820}) CREATE (b)-[r:Supply{max_supply: 74.57939370078739, current_output: 94.71583,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1820}) CREATE (b)-[r:Supply{max_supply: 12.429897637795277, current_output: 15.78597,level: 3}]->(g);
CREATE (n: Building {id: 1821, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:1821}) CREATE (b)-[r:Supply{max_supply: 66.32324509803921, current_output: 67.64971,level: 3}]->(g);
CREATE (n: Building {id: 1822, name:"building_gold_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1822}) CREATE (g)-[r:Demand{max_demand: 9.959999999999999, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:1822}) CREATE (b)-[r:Supply{max_supply: 19.718369369369366, current_output: 0.0,level: 2}]->(g);
CREATE (n: Building {id: 33556445, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:33556445}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
CREATE (n: Building {id: 3105, name:"building_subsistence_farmslevel", level:35});
MATCH (g: Goods{code: 7}), (b: Building{id:3105}) CREATE (b)-[r:Supply{max_supply: 4.209624, current_output: 5.26203,level: 35}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3105}) CREATE (b)-[r:Supply{max_supply: 0.84192, current_output: 1.0524,level: 35}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3105}) CREATE (b)-[r:Supply{max_supply: 0.84192, current_output: 1.0524,level: 35}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3105}) CREATE (b)-[r:Supply{max_supply: 0.84192, current_output: 1.0524,level: 35}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3105}) CREATE (b)-[r:Supply{max_supply: 0.84192, current_output: 1.0524,level: 35}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3105}) CREATE (b)-[r:Supply{max_supply: 0.84192, current_output: 1.0524,level: 35}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3105}) CREATE (b)-[r:Supply{max_supply: 1.178688, current_output: 1.47336,level: 35}]->(g);
CREATE (n: Building {id: 3109, name:"building_subsistence_farmslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:3109}) CREATE (b)-[r:Supply{max_supply: 6.543450000000001, current_output: 7.85214,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3109}) CREATE (b)-[r:Supply{max_supply: 1.3086833333333334, current_output: 1.57042,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3109}) CREATE (b)-[r:Supply{max_supply: 1.3086833333333334, current_output: 1.57042,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3109}) CREATE (b)-[r:Supply{max_supply: 1.3086833333333334, current_output: 1.57042,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3109}) CREATE (b)-[r:Supply{max_supply: 1.3086833333333334, current_output: 1.57042,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3109}) CREATE (b)-[r:Supply{max_supply: 1.3086833333333334, current_output: 1.57042,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3109}) CREATE (b)-[r:Supply{max_supply: 1.8321583333333333, current_output: 2.19859,level: 6}]->(g);
CREATE (n: Building {id: 3111, name:"building_subsistence_farmslevel", level:54});
MATCH (g: Goods{code: 7}), (b: Building{id:3111}) CREATE (b)-[r:Supply{max_supply: 2.0439, current_output: 2.24829,level: 54}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3111}) CREATE (b)-[r:Supply{max_supply: 0.40877272727272723, current_output: 0.44965,level: 54}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3111}) CREATE (b)-[r:Supply{max_supply: 0.40877272727272723, current_output: 0.44965,level: 54}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3111}) CREATE (b)-[r:Supply{max_supply: 0.40877272727272723, current_output: 0.44965,level: 54}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3111}) CREATE (b)-[r:Supply{max_supply: 0.40877272727272723, current_output: 0.44965,level: 54}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3111}) CREATE (b)-[r:Supply{max_supply: 0.40877272727272723, current_output: 0.44965,level: 54}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3111}) CREATE (b)-[r:Supply{max_supply: 0.572290909090909, current_output: 0.62952,level: 54}]->(g);
CREATE (n: Building {id: 3113, name:"building_subsistence_farmslevel", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3113}) CREATE (b)-[r:Supply{max_supply: 1.714496, current_output: 2.14312,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3113}) CREATE (b)-[r:Supply{max_supply: 0.342896, current_output: 0.42862,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3113}) CREATE (b)-[r:Supply{max_supply: 0.342896, current_output: 0.42862,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3113}) CREATE (b)-[r:Supply{max_supply: 0.342896, current_output: 0.42862,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3113}) CREATE (b)-[r:Supply{max_supply: 0.342896, current_output: 0.42862,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3113}) CREATE (b)-[r:Supply{max_supply: 0.342896, current_output: 0.42862,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3113}) CREATE (b)-[r:Supply{max_supply: 0.480056, current_output: 0.60007,level: 30}]->(g);
CREATE (n: Building {id: 3114, name:"building_subsistence_farmslevel", level:58});
MATCH (g: Goods{code: 7}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 0.004344, current_output: 0.00543,level: 58}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 0.000864, current_output: 0.00108,level: 58}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 0.000864, current_output: 0.00108,level: 58}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 0.000864, current_output: 0.00108,level: 58}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 0.000864, current_output: 0.00108,level: 58}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 0.000864, current_output: 0.00108,level: 58}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3114}) CREATE (b)-[r:Supply{max_supply: 0.001216, current_output: 0.00152,level: 58}]->(g);
CREATE (n: Building {id: 3115, name:"building_subsistence_farmslevel", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:3115}) CREATE (b)-[r:Supply{max_supply: 0.0029000000000000002, current_output: 0.00348,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3115}) CREATE (b)-[r:Supply{max_supply: 0.000575, current_output: 0.00069,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3115}) CREATE (b)-[r:Supply{max_supply: 0.000575, current_output: 0.00069,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3115}) CREATE (b)-[r:Supply{max_supply: 0.000575, current_output: 0.00069,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3115}) CREATE (b)-[r:Supply{max_supply: 0.000575, current_output: 0.00069,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3115}) CREATE (b)-[r:Supply{max_supply: 0.000575, current_output: 0.00069,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3115}) CREATE (b)-[r:Supply{max_supply: 0.0008083333333333334, current_output: 0.00097,level: 29}]->(g);
CREATE (n: Building {id: 3117, name:"building_subsistence_farmslevel", level:12});
MATCH (g: Goods{code: 7}), (b: Building{id:3117}) CREATE (b)-[r:Supply{max_supply: 0.3888, current_output: 0.46656,level: 12}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3117}) CREATE (b)-[r:Supply{max_supply: 0.07775833333333335, current_output: 0.09331,level: 12}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3117}) CREATE (b)-[r:Supply{max_supply: 0.07775833333333335, current_output: 0.09331,level: 12}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3117}) CREATE (b)-[r:Supply{max_supply: 0.07775833333333335, current_output: 0.09331,level: 12}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3117}) CREATE (b)-[r:Supply{max_supply: 0.07775833333333335, current_output: 0.09331,level: 12}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3117}) CREATE (b)-[r:Supply{max_supply: 0.07775833333333335, current_output: 0.09331,level: 12}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3117}) CREATE (b)-[r:Supply{max_supply: 0.10885833333333333, current_output: 0.13063,level: 12}]->(g);
CREATE (n: Building {id: 3119, name:"building_subsistence_farmslevel", level:18});
CREATE (n: Building {id: 3891, name:"building_trade_centerlevel", level:18});
CREATE (n: Building {id: 3892, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3892}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 6.848051663363585, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3892}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 33558524, name:"building_sugar_plantationlevel", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:33558524}) CREATE (b)-[r:Supply{max_supply: 8.646294642857141, current_output: 9.68385,level: 3}]->(g);
CREATE (n: Building {id: 4569, name:"building_sugar_plantationlevel", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:4569}) CREATE (b)-[r:Supply{max_supply: 14.594099999999997, current_output: 16.05351,level: 1}]->(g);
CREATE (n: Building {id: 50336609, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:50336609}) CREATE (b)-[r:Supply{max_supply: 3.0, current_output: 3.75,level: 1}]->(g);
CREATE (n: Building {id: 16782443, name:"building_dye_plantationlevel", level:1});
MATCH (g: Goods{code: 21}), (b: Building{id:16782443}) CREATE (b)-[r:Supply{max_supply: 24.592999999999996, current_output: 27.0523,level: 1}]->(g);
CREATE (n: Building {id: 83891388, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:83891388}) CREATE (b)-[r:Supply{max_supply: 66.39675, current_output: 74.36436,level: 3}]->(g);
CREATE (n: Building {id: 16782871, name:"building_dye_plantationlevel", level:2});
MATCH (g: Goods{code: 21}), (b: Building{id:16782871}) CREATE (b)-[r:Supply{max_supply: 34.647, current_output: 38.45817,level: 2}]->(g);
CREATE (n: Building {id: 50337378, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:50337378}) CREATE (b)-[r:Supply{max_supply: 26.5293, current_output: 29.18223,level: 1}]->(g);
CREATE (n: Building {id: 16783090, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783090}) CREATE (b)-[r:Supply{max_supply: 35.388, current_output: 47.7738,level: 1}]->(g);
CREATE (n: Building {id: 5929, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:5929}) CREATE (b)-[r:Supply{max_supply: 26.535, current_output: 29.1885,level: 1}]->(g);
CREATE (n: Building {id: 6049, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:6049}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 72.6,level: 2}]->(g);
CREATE (n: Building {id: 16783356, name:"building_dye_plantationlevel", level:1});
MATCH (g: Goods{code: 21}), (b: Building{id:16783356}) CREATE (b)-[r:Supply{max_supply: 19.078245454545453, current_output: 20.98607,level: 1}]->(g);
CREATE (n: Building {id: 6565, name:"building_subsistence_pastureslevel", level:36});
MATCH (g: Goods{code: 7}), (b: Building{id:6565}) CREATE (b)-[r:Supply{max_supply: 0.0036, current_output: 0.0045,level: 36}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6565}) CREATE (b)-[r:Supply{max_supply: 0.0054, current_output: 0.00675,level: 36}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6565}) CREATE (b)-[r:Supply{max_supply: 0.0018, current_output: 0.00225,level: 36}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6565}) CREATE (b)-[r:Supply{max_supply: 0.0036, current_output: 0.0045,level: 36}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6565}) CREATE (b)-[r:Supply{max_supply: 0.0036, current_output: 0.0045,level: 36}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6565}) CREATE (b)-[r:Supply{max_supply: 0.0036, current_output: 0.0045,level: 36}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:6565}) CREATE (b)-[r:Supply{max_supply: 0.009576, current_output: 0.01197,level: 36}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6565}) CREATE (b)-[r:Supply{max_supply: 0.00504, current_output: 0.0063,level: 36}]->(g);
CREATE (n: Building {id: 6566, name:"building_subsistence_pastureslevel", level:6});
CREATE (n: Building {id: 6567, name:"building_subsistence_pastureslevel", level:29});
CREATE (n: Building {id: 6568, name:"building_subsistence_pastureslevel", level:18});
