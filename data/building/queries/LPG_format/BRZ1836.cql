CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:83.125, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:25.35555503399773, pop_demand:1185.0679318809546});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:25.682051021177354, pop_demand:243.0792447080119});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:16.504262657976966, pop_demand:101.55518495382363});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:12.651213174090286, pop_demand:226.5592525461765});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:34.150584558012945, pop_demand:280.8659268353185});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:38.4277140317287, pop_demand:150.38250215860035});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:33.59773281411396, pop_demand:32.8400082089644});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:11.291812088335536, pop_demand:41.9719});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:69.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:39.992112500000005, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:9.591661992568179});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:36.89982045337227, pop_demand:21.691481113315113});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:36.41384429129701, pop_demand:123.36695205097006});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:48.76183515559294, pop_demand:415.0915260790553});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:18.114335171697242, pop_demand:85.59300999999998});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:14.184552272462792, pop_demand:35.685883379678955});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:53.631601551258434, pop_demand:91.27343794070835});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:89.52217859730304});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:58.89440324470075});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 1894, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1894}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 1895, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1895}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.001314929050096, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1895}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 1}]->(g);
CREATE (n: Building {id: 1900, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1900}) CREATE (g)-[r:Demand{max_demand: 4.96845, current_input: 4.9697566318477895, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1900}) CREATE (b)-[r:Supply{max_supply: 59.6214, current_output: 59.6214,level: 1}]->(g);
CREATE (n: Building {id: 1901, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1901}) CREATE (b)-[r:Supply{max_supply: 28.44, current_output: 28.44,level: 1}]->(g);
CREATE (n: Building {id: 1902, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1902}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.001314929050096, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1902}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 1}]->(g);
CREATE (n: Building {id: 1903, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1903}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1903}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 1904, name:"building_coffee_plantationlevel", level:5});
MATCH (g: Goods{code: 41}), (b: Building{id:1904}) CREATE (b)-[r:Supply{max_supply: 99.6, current_output: 103.584,level: 5}]->(g);
CREATE (n: Building {id: 1905, name:"building_sugar_plantationlevel", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1905}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 1906, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1906}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1906}) CREATE (b)-[r:Supply{max_supply: 9.959999999999999, current_output: 10.0596,level: 2}]->(g);
CREATE (n: Building {id: 1907, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1907}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 15.003944787150287, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1907}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 180.0,level: 3}]->(g);
CREATE (n: Building {id: 1908, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1908}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.401007638171572, level: 1}]->(b);
CREATE (n: Building {id: 1909, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1909}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 117.62840474079565, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1909}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 40.0, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1909}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 2}]->(g);
CREATE (n: Building {id: 1910, name:"building_gold_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1910}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.002629858100192, level: 2}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:1910}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 2}]->(g);
CREATE (n: Building {id: 1911, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1911}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.002629858100192, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1911}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.0,level: 2}]->(g);
CREATE (n: Building {id: 1912, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1912}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 20.005259716200385, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1912}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 239.99999999999997,level: 4}]->(g);
CREATE (n: Building {id: 1913, name:"building_maize_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1913}) CREATE (b)-[r:Supply{max_supply: 89.64, current_output: 91.4328,level: 3}]->(g);
CREATE (n: Building {id: 1914, name:"building_coffee_plantationlevel", level:10});
MATCH (g: Goods{code: 41}), (b: Building{id:1914}) CREATE (b)-[r:Supply{max_supply: 199.19999999999996, current_output: 217.128,level: 10}]->(g);
CREATE (n: Building {id: 1915, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1915}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1915}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 1916, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:1916}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 42.00503819085786, level: 5}]->(b);
CREATE (n: Building {id: 1917, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:1917}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.401007638171572, level: 2}]->(b);
CREATE (n: Building {id: 1918, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1918}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 65.19322639769145, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1918}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 294.0710118519891, level: 2}]->(b);
CREATE (n: Building {id: 1919, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1919}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 13.03864527953829, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1919}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 58.81420237039782, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1919}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 1}]->(g);
CREATE (n: Building {id: 1920, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1920}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 176.44260711119347, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1920}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 3}]->(g);
CREATE (n: Building {id: 1921, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:1921}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 1922, name:"building_coffee_plantationlevel", level:6});
MATCH (g: Goods{code: 41}), (b: Building{id:1922}) CREATE (b)-[r:Supply{max_supply: 119.52, current_output: 125.496,level: 6}]->(g);
CREATE (n: Building {id: 1923, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1923}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
CREATE (n: Building {id: 1924, name:"building_maize_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:1924}) CREATE (b)-[r:Supply{max_supply: 149.4, current_output: 155.376,level: 5}]->(g);
CREATE (n: Building {id: 1925, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1925}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1925}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 1926, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1926}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.0, level: 2}]->(b);
CREATE (n: Building {id: 1927, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:1927}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 25.203022914514715, level: 3}]->(b);
CREATE (n: Building {id: 1928, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1928}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 26.07729055907658, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1928}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 78.41893649386377, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1928}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:1928}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 1929, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1929}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1929}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 1930, name:"building_coffee_plantationlevel", level:6});
MATCH (g: Goods{code: 41}), (b: Building{id:1930}) CREATE (b)-[r:Supply{max_supply: 119.52, current_output: 125.496,level: 6}]->(g);
CREATE (n: Building {id: 1931, name:"building_sugar_plantationlevel", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1931}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 1932, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1932}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 1933, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:1933}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 20.005259716200385, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1933}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 239.99999999999997,level: 4}]->(g);
CREATE (n: Building {id: 1934, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:1934}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 1935, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:1935}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 15.0, level: 10}]->(b);
CREATE (n: Building {id: 1936, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1936}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.0, level: 2}]->(b);
CREATE (n: Building {id: 1937, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1937}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 1938, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1938}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.001314929050096, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1938}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 1}]->(g);
CREATE (n: Building {id: 1939, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1939}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1940, name:"building_cotton_plantationlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1940}) CREATE (b)-[r:Supply{max_supply: 39.839999999999996, current_output: 49.8,level: 1}]->(g);
CREATE (n: Building {id: 1941, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1941}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 1942, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1942}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 10.002629858100192, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1942}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 2}]->(g);
CREATE (n: Building {id: 1943, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1943}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1944, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1944}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 1945, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1945}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.001314929050096, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1945}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 1}]->(g);
CREATE (n: Building {id: 1946, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1946}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 52.15458111815316, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1946}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 1}]->(g);
CREATE (n: Building {id: 1947, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:1947}) CREATE (b)-[r:Supply{max_supply: 24.9, current_output: 24.9,level: 1}]->(g);
CREATE (n: Building {id: 1948, name:"building_maize_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1948}) CREATE (b)-[r:Supply{max_supply: 59.76, current_output: 60.3576,level: 2}]->(g);
CREATE (n: Building {id: 1949, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:1949}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1949}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1950, name:"building_government_administrationlevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:1950}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.401007638171572, level: 1}]->(b);
CREATE (n: Building {id: 1951, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1951}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 156.46374335445947, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1951}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 135.0,level: 3}]->(g);
CREATE (n: Building {id: 1952, name:"building_cotton_plantationlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:1952}) CREATE (b)-[r:Supply{max_supply: 199.20000000000002, current_output: 256.968,level: 5}]->(g);
CREATE (n: Building {id: 1953, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:1953}) CREATE (b)-[r:Supply{max_supply: 24.9, current_output: 24.9,level: 1}]->(g);
CREATE (n: Building {id: 1954, name:"building_maize_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1954}) CREATE (b)-[r:Supply{max_supply: 89.64, current_output: 91.4328,level: 3}]->(g);
CREATE (n: Building {id: 1955, name:"building_barrackslevel", level:4});
MATCH (g: Goods{code: 1}), (b: Building{id:1955}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1955}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.0, level: 4}]->(b);
CREATE (n: Building {id: 1956, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1956}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.0, level: 1}]->(b);
CREATE (n: Building {id: 1957, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1957}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.001314929050096, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1957}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 1}]->(g);
CREATE (n: Building {id: 1958, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1958}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 1959, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1959}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.001314929050096, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1959}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 1}]->(g);
CREATE (n: Building {id: 3149, name:"building_subsistence_rice_paddieslevel", level:39});
MATCH (g: Goods{code: 7}), (b: Building{id:3149}) CREATE (b)-[r:Supply{max_supply: 12.93084, current_output: 12.93084,level: 39}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3149}) CREATE (b)-[r:Supply{max_supply: 2.15514, current_output: 2.15514,level: 39}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3149}) CREATE (b)-[r:Supply{max_supply: 2.15514, current_output: 2.15514,level: 39}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3149}) CREATE (b)-[r:Supply{max_supply: 2.87352, current_output: 2.87352,level: 39}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3149}) CREATE (b)-[r:Supply{max_supply: 2.87352, current_output: 2.87352,level: 39}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3149}) CREATE (b)-[r:Supply{max_supply: 2.87352, current_output: 2.87352,level: 39}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3149}) CREATE (b)-[r:Supply{max_supply: 4.31028, current_output: 4.31028,level: 39}]->(g);
CREATE (n: Building {id: 3154, name:"building_subsistence_farmslevel", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3154}) CREATE (b)-[r:Supply{max_supply: 6.912, current_output: 6.912,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3154}) CREATE (b)-[r:Supply{max_supply: 1.3824, current_output: 1.3824,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3154}) CREATE (b)-[r:Supply{max_supply: 1.3824, current_output: 1.3824,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3154}) CREATE (b)-[r:Supply{max_supply: 1.3824, current_output: 1.3824,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3154}) CREATE (b)-[r:Supply{max_supply: 1.3824, current_output: 1.3824,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3154}) CREATE (b)-[r:Supply{max_supply: 1.3824, current_output: 1.3824,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3154}) CREATE (b)-[r:Supply{max_supply: 1.93536, current_output: 1.93536,level: 40}]->(g);
CREATE (n: Building {id: 3156, name:"building_subsistence_farmslevel", level:55});
MATCH (g: Goods{code: 7}), (b: Building{id:3156}) CREATE (b)-[r:Supply{max_supply: 8.156495652173914, current_output: 9.37997,level: 55}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3156}) CREATE (b)-[r:Supply{max_supply: 1.6312956521739133, current_output: 1.87599,level: 55}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3156}) CREATE (b)-[r:Supply{max_supply: 1.6312956521739133, current_output: 1.87599,level: 55}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3156}) CREATE (b)-[r:Supply{max_supply: 1.6312956521739133, current_output: 1.87599,level: 55}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3156}) CREATE (b)-[r:Supply{max_supply: 1.6312956521739133, current_output: 1.87599,level: 55}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3156}) CREATE (b)-[r:Supply{max_supply: 1.6312956521739133, current_output: 1.87599,level: 55}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3156}) CREATE (b)-[r:Supply{max_supply: 2.283817391304348, current_output: 2.62639,level: 55}]->(g);
CREATE (n: Building {id: 3158, name:"building_subsistence_farmslevel", level:119});
CREATE (n: Building {id: 3159, name:"building_subsistence_farmslevel", level:119});
MATCH (g: Goods{code: 7}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 2.9036, current_output: 2.9036,level: 119}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 0.58072, current_output: 0.58072,level: 119}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 0.58072, current_output: 0.58072,level: 119}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 0.58072, current_output: 0.58072,level: 119}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 0.58072, current_output: 0.58072,level: 119}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 0.58072, current_output: 0.58072,level: 119}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 0.813, current_output: 0.813,level: 119}]->(g);
CREATE (n: Building {id: 3160, name:"building_subsistence_farmslevel", level:232});
MATCH (g: Goods{code: 7}), (b: Building{id:3160}) CREATE (b)-[r:Supply{max_supply: 14.79, current_output: 14.79,level: 232}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3160}) CREATE (b)-[r:Supply{max_supply: 2.958, current_output: 2.958,level: 232}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3160}) CREATE (b)-[r:Supply{max_supply: 2.958, current_output: 2.958,level: 232}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3160}) CREATE (b)-[r:Supply{max_supply: 2.958, current_output: 2.958,level: 232}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3160}) CREATE (b)-[r:Supply{max_supply: 2.958, current_output: 2.958,level: 232}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3160}) CREATE (b)-[r:Supply{max_supply: 2.958, current_output: 2.958,level: 232}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3160}) CREATE (b)-[r:Supply{max_supply: 4.1412, current_output: 4.1412,level: 232}]->(g);
CREATE (n: Building {id: 3161, name:"building_subsistence_farmslevel", level:227});
MATCH (g: Goods{code: 7}), (b: Building{id:3161}) CREATE (b)-[r:Supply{max_supply: 27.31945, current_output: 27.31945,level: 227}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3161}) CREATE (b)-[r:Supply{max_supply: 5.46389, current_output: 5.46389,level: 227}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3161}) CREATE (b)-[r:Supply{max_supply: 5.46389, current_output: 5.46389,level: 227}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3161}) CREATE (b)-[r:Supply{max_supply: 5.46389, current_output: 5.46389,level: 227}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3161}) CREATE (b)-[r:Supply{max_supply: 5.46389, current_output: 5.46389,level: 227}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3161}) CREATE (b)-[r:Supply{max_supply: 5.46389, current_output: 5.46389,level: 227}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3161}) CREATE (b)-[r:Supply{max_supply: 7.64944, current_output: 7.64944,level: 227}]->(g);
CREATE (n: Building {id: 3162, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3162}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 9.802367061732971, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3162}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3163, name:"building_subsistence_farmslevel", level:167});
MATCH (g: Goods{code: 7}), (b: Building{id:3163}) CREATE (b)-[r:Supply{max_supply: 32.9324, current_output: 32.9324,level: 167}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3163}) CREATE (b)-[r:Supply{max_supply: 6.58648, current_output: 6.58648,level: 167}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3163}) CREATE (b)-[r:Supply{max_supply: 6.58648, current_output: 6.58648,level: 167}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3163}) CREATE (b)-[r:Supply{max_supply: 6.58648, current_output: 6.58648,level: 167}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3163}) CREATE (b)-[r:Supply{max_supply: 6.58648, current_output: 6.58648,level: 167}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3163}) CREATE (b)-[r:Supply{max_supply: 6.58648, current_output: 6.58648,level: 167}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3163}) CREATE (b)-[r:Supply{max_supply: 9.22107, current_output: 9.22107,level: 167}]->(g);
CREATE (n: Building {id: 3164, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3164}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 39.209468246931884, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3164}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 100.0,level: 4}]->(g);
CREATE (n: Building {id: 3165, name:"building_subsistence_farmslevel", level:172});
MATCH (g: Goods{code: 7}), (b: Building{id:3165}) CREATE (b)-[r:Supply{max_supply: 27.8683, current_output: 27.8683,level: 172}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3165}) CREATE (b)-[r:Supply{max_supply: 5.57366, current_output: 5.57366,level: 172}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3165}) CREATE (b)-[r:Supply{max_supply: 5.57366, current_output: 5.57366,level: 172}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3165}) CREATE (b)-[r:Supply{max_supply: 5.57366, current_output: 5.57366,level: 172}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3165}) CREATE (b)-[r:Supply{max_supply: 5.57366, current_output: 5.57366,level: 172}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3165}) CREATE (b)-[r:Supply{max_supply: 5.57366, current_output: 5.57366,level: 172}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3165}) CREATE (b)-[r:Supply{max_supply: 7.80312, current_output: 7.80312,level: 172}]->(g);
CREATE (n: Building {id: 3166, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3166}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 19.604734123465942, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3166}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 2}]->(g);
CREATE (n: Building {id: 3167, name:"building_subsistence_farmslevel", level:89});
MATCH (g: Goods{code: 7}), (b: Building{id:3167}) CREATE (b)-[r:Supply{max_supply: 3.68237, current_output: 3.68237,level: 89}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3167}) CREATE (b)-[r:Supply{max_supply: 0.73647, current_output: 0.73647,level: 89}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3167}) CREATE (b)-[r:Supply{max_supply: 0.73647, current_output: 0.73647,level: 89}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3167}) CREATE (b)-[r:Supply{max_supply: 0.73647, current_output: 0.73647,level: 89}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3167}) CREATE (b)-[r:Supply{max_supply: 0.73647, current_output: 0.73647,level: 89}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3167}) CREATE (b)-[r:Supply{max_supply: 0.73647, current_output: 0.73647,level: 89}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3167}) CREATE (b)-[r:Supply{max_supply: 1.03106, current_output: 1.03106,level: 89}]->(g);
CREATE (n: Building {id: 3168, name:"building_subsistence_farmslevel", level:88});
MATCH (g: Goods{code: 7}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 14.2714, current_output: 14.2714,level: 88}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 2.85428, current_output: 2.85428,level: 88}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 2.85428, current_output: 2.85428,level: 88}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 2.85428, current_output: 2.85428,level: 88}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 2.85428, current_output: 2.85428,level: 88}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 2.85428, current_output: 2.85428,level: 88}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3168}) CREATE (b)-[r:Supply{max_supply: 3.99599, current_output: 3.99599,level: 88}]->(g);
CREATE (n: Building {id: 3169, name:"building_subsistence_farmslevel", level:89});
MATCH (g: Goods{code: 7}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 7.03545, current_output: 7.03545,level: 89}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 1.40709, current_output: 1.40709,level: 89}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 1.40709, current_output: 1.40709,level: 89}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 1.40709, current_output: 1.40709,level: 89}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 1.40709, current_output: 1.40709,level: 89}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 1.40709, current_output: 1.40709,level: 89}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 1.96992, current_output: 1.96992,level: 89}]->(g);
CREATE (n: Building {id: 3170, name:"building_subsistence_farmslevel", level:170});
MATCH (g: Goods{code: 7}), (b: Building{id:3170}) CREATE (b)-[r:Supply{max_supply: 77.37125, current_output: 77.37125,level: 170}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3170}) CREATE (b)-[r:Supply{max_supply: 15.47425, current_output: 15.47425,level: 170}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3170}) CREATE (b)-[r:Supply{max_supply: 15.47425, current_output: 15.47425,level: 170}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3170}) CREATE (b)-[r:Supply{max_supply: 15.47425, current_output: 15.47425,level: 170}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3170}) CREATE (b)-[r:Supply{max_supply: 15.47425, current_output: 15.47425,level: 170}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3170}) CREATE (b)-[r:Supply{max_supply: 15.47425, current_output: 15.47425,level: 170}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3170}) CREATE (b)-[r:Supply{max_supply: 21.66395, current_output: 21.66395,level: 170}]->(g);
CREATE (n: Building {id: 3171, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3171}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 9.802367061732971, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3171}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3172, name:"building_subsistence_farmslevel", level:147});
MATCH (g: Goods{code: 7}), (b: Building{id:3172}) CREATE (b)-[r:Supply{max_supply: 6.4386, current_output: 6.4386,level: 147}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3172}) CREATE (b)-[r:Supply{max_supply: 1.28772, current_output: 1.28772,level: 147}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3172}) CREATE (b)-[r:Supply{max_supply: 1.28772, current_output: 1.28772,level: 147}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3172}) CREATE (b)-[r:Supply{max_supply: 1.28772, current_output: 1.28772,level: 147}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3172}) CREATE (b)-[r:Supply{max_supply: 1.28772, current_output: 1.28772,level: 147}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3172}) CREATE (b)-[r:Supply{max_supply: 1.28772, current_output: 1.28772,level: 147}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3172}) CREATE (b)-[r:Supply{max_supply: 1.8028, current_output: 1.8028,level: 147}]->(g);
CREATE (n: Building {id: 3996, name:"building_trade_centerlevel", level:28});
CREATE (n: Building {id: 3997, name:"building_trade_centerlevel", level:14});
CREATE (n: Building {id: 4180, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4181, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4182, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4183, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4184, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4185, name:"building_conscription_centerlevel", level:5});
