CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:23.91057520559436, pop_demand:277.72326852820026});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:23.168658162301234, pop_demand:58.636628838621796});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:13.622739380281581, pop_demand:17.3668469527242});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:14.695795067983234, pop_demand:45.05227054727576});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:49.51590285038179, pop_demand:57.7246454667331});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:49.214739669726065, pop_demand:52.43296807033755});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:21.191598046823373, pop_demand:35.08445833333334});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:29.989946078431373, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:2.2805375});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:22.82596048491518, pop_demand:7.655637404500818});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:33.8015294575908, pop_demand:12.897224639471935});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:12.617609999999999});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:8.789413087932834, pop_demand:5.11057330106473});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:34.494839458173836, pop_demand:70.88982902039608});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:10.642508333333334});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:2.6606270833333334});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 1823, name:"building_government_administrationlevel", level:3});
CREATE (n: Building {id: 1824, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1824}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 46.411872037803064, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1824}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 20.0, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1824}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 1}]->(g);
CREATE (n: Building {id: 1825, name:"building_gold_minelevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1825}) CREATE (g)-[r:Demand{max_demand: 14.979892156862745, current_input: 22.481139415917166, level: 3}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:1825}) CREATE (b)-[r:Supply{max_supply: 29.95979411764706, current_output: 29.95979411764706,level: 3}]->(g);
CREATE (n: Building {id: 1826, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1826}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.503772116816566, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1826}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 1827, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1827}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 100.8,level: 3}]->(g);
CREATE (n: Building {id: 1828, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1828}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1828}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 1829, name:"building_portlevel", level:2});
CREATE (n: Building {id: 1830, name:"building_naval_baselevel", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:1830}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 1831, name:"building_barrackslevel", level:5});
CREATE (n: Building {id: 1832, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1832}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 33.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1832}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.5,level: 1}]->(g);
CREATE (n: Building {id: 1833, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:1833}) CREATE (b)-[r:Supply{max_supply: 22.10775, current_output: 22.10775,level: 1}]->(g);
CREATE (n: Building {id: 1834, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1834}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 15.00754423363313, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1834}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 119.99999999999999,level: 2}]->(g);
CREATE (n: Building {id: 1835, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1835}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1835}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 3107, name:"building_subsistence_farmslevel", level:14});
MATCH (g: Goods{code: 7}), (b: Building{id:3107}) CREATE (b)-[r:Supply{max_supply: 2.4097454545454546, current_output: 2.65072,level: 14}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3107}) CREATE (b)-[r:Supply{max_supply: 0.48194545454545445, current_output: 0.53014,level: 14}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3107}) CREATE (b)-[r:Supply{max_supply: 0.48194545454545445, current_output: 0.53014,level: 14}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3107}) CREATE (b)-[r:Supply{max_supply: 0.48194545454545445, current_output: 0.53014,level: 14}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3107}) CREATE (b)-[r:Supply{max_supply: 0.48194545454545445, current_output: 0.53014,level: 14}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3107}) CREATE (b)-[r:Supply{max_supply: 0.48194545454545445, current_output: 0.53014,level: 14}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3107}) CREATE (b)-[r:Supply{max_supply: 0.6747272727272726, current_output: 0.7422,level: 14}]->(g);
CREATE (n: Building {id: 3124, name:"building_subsistence_farmslevel", level:80});
MATCH (g: Goods{code: 7}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 29.271999999999995, current_output: 32.1992,level: 80}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 5.8544, current_output: 6.43984,level: 80}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 5.8544, current_output: 6.43984,level: 80}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 5.8544, current_output: 6.43984,level: 80}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 5.8544, current_output: 6.43984,level: 80}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 5.8544, current_output: 6.43984,level: 80}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3124}) CREATE (b)-[r:Supply{max_supply: 8.196154545454545, current_output: 9.01577,level: 80}]->(g);
CREATE (n: Building {id: 3125, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3125}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 15.470624012601023, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3125}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.0,level: 2}]->(g);
CREATE (n: Building {id: 3126, name:"building_subsistence_farmslevel", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 1.2555, current_output: 1.38105,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 0.2511, current_output: 0.27621,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 0.2511, current_output: 0.27621,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 0.2511, current_output: 0.27621,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 0.2511, current_output: 0.27621,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 0.2511, current_output: 0.27621,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3126}) CREATE (b)-[r:Supply{max_supply: 0.3515363636363636, current_output: 0.38669,level: 30}]->(g);
CREATE (n: Building {id: 3127, name:"building_subsistence_farmslevel", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:3127}) CREATE (b)-[r:Supply{max_supply: 1.4531999999999998, current_output: 1.59852,level: 16}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3127}) CREATE (b)-[r:Supply{max_supply: 0.2906363636363636, current_output: 0.3197,level: 16}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3127}) CREATE (b)-[r:Supply{max_supply: 0.2906363636363636, current_output: 0.3197,level: 16}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3127}) CREATE (b)-[r:Supply{max_supply: 0.2906363636363636, current_output: 0.3197,level: 16}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3127}) CREATE (b)-[r:Supply{max_supply: 0.2906363636363636, current_output: 0.3197,level: 16}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3127}) CREATE (b)-[r:Supply{max_supply: 0.2906363636363636, current_output: 0.3197,level: 16}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3127}) CREATE (b)-[r:Supply{max_supply: 0.406890909090909, current_output: 0.44758,level: 16}]->(g);
CREATE (n: Building {id: 3893, name:"building_trade_centerlevel", level:10});
CREATE (n: Building {id: 33558518, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:33558518}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 43.48965954572944, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:33558518}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 116.02968009450767, level: 1}]->(b);
CREATE (n: Building {id: 33558523, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:33558523}) CREATE (b)-[r:Supply{max_supply: 63.4455, current_output: 64.71441,level: 3}]->(g);
CREATE (n: Building {id: 4095, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4760, name:"building_sugar_plantationlevel", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:4760}) CREATE (b)-[r:Supply{max_supply: 29.5293, current_output: 29.5293,level: 1}]->(g);
CREATE (n: Building {id: 100668791, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:100668791}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 66.6,level: 2}]->(g);
CREATE (n: Building {id: 151000563, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:151000563}) CREATE (b)-[r:Supply{max_supply: 59.05859405940594, current_output: 59.64918,level: 2}]->(g);
CREATE (n: Building {id: 16783049, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:16783049}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 100669359, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:100669359}) CREATE (b)-[r:Supply{max_supply: 21.25, current_output: 21.25,level: 1}]->(g);
CREATE (n: Building {id: 16783420, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:16783420}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 15.00754423363313, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783420}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 119.99999999999999,level: 2}]->(g);
CREATE (n: Building {id: 6268, name:"building_gold_fieldslevel", level:2});
MATCH (g: Goods{code: 50}), (b: Building{id:6268}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 6574, name:"building_subsistence_pastureslevel", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:6574}) CREATE (b)-[r:Supply{max_supply: 0.08175454545454544, current_output: 0.08993,level: 16}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6574}) CREATE (b)-[r:Supply{max_supply: 0.12263636363636361, current_output: 0.1349,level: 16}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6574}) CREATE (b)-[r:Supply{max_supply: 0.04087272727272727, current_output: 0.04496,level: 16}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6574}) CREATE (b)-[r:Supply{max_supply: 0.08175454545454544, current_output: 0.08993,level: 16}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6574}) CREATE (b)-[r:Supply{max_supply: 0.08175454545454544, current_output: 0.08993,level: 16}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6574}) CREATE (b)-[r:Supply{max_supply: 0.08175454545454544, current_output: 0.08993,level: 16}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:6574}) CREATE (b)-[r:Supply{max_supply: 0.21747272727272723, current_output: 0.23922,level: 16}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6574}) CREATE (b)-[r:Supply{max_supply: 0.11446363636363635, current_output: 0.12591,level: 16}]->(g);
CREATE (n: Building {id: 6575, name:"building_subsistence_pastureslevel", level:14});
