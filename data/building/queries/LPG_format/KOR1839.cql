CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0.1947705031541171});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:13.206278897109524, pop_demand:1292.8556006002648});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:12.366850006871159, pop_demand:18.76287584416705});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:9.962173689891028, pop_demand:39.881743490595156});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:27.227911891098916, pop_demand:239.2904609435709});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:23.34823393021643, pop_demand:302.9868955045135});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:21.020209232341365, pop_demand:258.47966656197815});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:52.5, pop_demand:7.865270351073467});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:15.457195151039285, pop_demand:162.55737873924477});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:49.869991210086496, pop_demand:71.37424166666665});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:34.085656350865285, pop_demand:0.15790236308011232});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:52.5, pop_demand:0.017016210555987253});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:28.555886096805576, pop_demand:10.039756111106227});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:101.71758086101762, pop_demand:91.62367354573546});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:43.79617298022304, pop_demand:39.500377783743524});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:50.01127201601366, pop_demand:122.37576864956091});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:23.795819065602245, pop_demand:311.54383826808254});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:110.42879999999998});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:40.43649404855818, pop_demand:37.86958798204088});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:11.357259428789273, pop_demand:10.038618739687394});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:87.5, pop_demand:90.97915497583864});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:35.65959745126617});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:74.1274125787091});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 2513, name:"building_government_administration", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:2513}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 0.0, level: 6}]->(b);
CREATE (n: Building {id: 2514, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2514}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 60.457375889756136, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2514}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 20.725568290402887, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2514}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2514}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 2515, name:"building_glassworks", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:2515}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 46.63252865340649, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2515}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2515}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 7.772088108901082,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2515}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 15.544176217802164,level: 3}]->(g);
CREATE (n: Building {id: 2516, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2516}) CREATE (g)-[r:Demand{max_demand: 0.2444950495049505, current_input: 0.5764683710876918, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2516}) CREATE (b)-[r:Supply{max_supply: 2.4450000000000003, current_output: 2.4450000000000003,level: 2}]->(g);
CREATE (n: Building {id: 2517, name:"building_rice_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:2517}) CREATE (g)-[r:Demand{max_demand: 5.406836065573771, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2517}) CREATE (b)-[r:Supply{max_supply: 27.034196721311478, current_output: 0.0,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2517}) CREATE (b)-[r:Supply{max_supply: 32.441032786885245, current_output: 0.0,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2517}) CREATE (b)-[r:Supply{max_supply: 48.661557377049185, current_output: 0.0,level: 3}]->(g);
CREATE (n: Building {id: 2518, name:"building_tobacco_plantation", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:2518}) CREATE (b)-[r:Supply{max_supply: 67.83, current_output: 82.7526,level: 3}]->(g);
CREATE (n: Building {id: 2519, name:"building_barracks", level:10});
CREATE (n: Building {id: 2520, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:2520}) CREATE (g)-[r:Demand{max_demand: 9.52, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 2521, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2521}) CREATE (g)-[r:Demand{max_demand: 15.936, current_input: 48.17243710895769, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2521}) CREATE (g)-[r:Demand{max_demand: 31.872, current_input: 16.51413281379302, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:2521}) CREATE (b)-[r:Supply{max_supply: 27.888, current_output: 21.168933106034448,level: 1}]->(g);
CREATE (n: Building {id: 2522, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2522}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 15.544176217802164, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2522}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2522}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 2.5906960363003604,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2522}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 5.181392072600721,level: 1}]->(g);
CREATE (n: Building {id: 2523, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2523}) CREATE (g)-[r:Demand{max_demand: 3.14229702970297, current_input: 7.408881504367171, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2523}) CREATE (b)-[r:Supply{max_supply: 31.423, current_output: 31.423,level: 2}]->(g);
CREATE (n: Building {id: 2524, name:"building_rice_farm", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2524}) CREATE (b)-[r:Supply{max_supply: 9.4, current_output: 11.28,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2524}) CREATE (b)-[r:Supply{max_supply: 11.28, current_output: 13.536,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2524}) CREATE (b)-[r:Supply{max_supply: 16.919999999999998, current_output: 20.304,level: 1}]->(g);
CREATE (n: Building {id: 2525, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2525}) CREATE (b)-[r:Supply{max_supply: 48.865, current_output: 59.12665,level: 2}]->(g);
CREATE (n: Building {id: 2526, name:"building_barracks", level:3});
CREATE (n: Building {id: 2527, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2527}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 11.788957941171311, level: 1}]->(b);
CREATE (n: Building {id: 2528, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:2528}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 2529, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:2529}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 15.544176217802164, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2529}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2529}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 2.5906960363003604,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2529}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 5.181392072600721,level: 1}]->(g);
CREATE (n: Building {id: 2530, name:"building_fishing_wharf", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2530}) CREATE (g)-[r:Demand{max_demand: 0.4335940594059406, current_input: 1.0223244259756739, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:2530}) CREATE (b)-[r:Supply{max_supply: 4.336, current_output: 4.336,level: 2}]->(g);
CREATE (n: Building {id: 2531, name:"building_rice_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:2531}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2531}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 0.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2531}) CREATE (b)-[r:Supply{max_supply: 12.0, current_output: 0.0,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2531}) CREATE (b)-[r:Supply{max_supply: 18.0, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 2532, name:"building_tobacco_plantation", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:2532}) CREATE (b)-[r:Supply{max_supply: 24.59, current_output: 29.508,level: 1}]->(g);
CREATE (n: Building {id: 2533, name:"building_barracks", level:2});
CREATE (n: Building {id: 2534, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:2534}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 2535, name:"building_rice_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:2535}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2535}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 0.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2535}) CREATE (b)-[r:Supply{max_supply: 12.0, current_output: 0.0,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2535}) CREATE (b)-[r:Supply{max_supply: 18.0, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 2536, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2536}) CREATE (g)-[r:Demand{max_demand: 2.998, current_input: 5.479950238571549, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2536}) CREATE (g)-[r:Demand{max_demand: 2.998, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2536}) CREATE (b)-[r:Supply{max_supply: 5.996, current_output: 2.998,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2536}) CREATE (b)-[r:Supply{max_supply: 11.992, current_output: 5.996,level: 1}]->(g);
CREATE (n: Building {id: 2537, name:"building_tobacco_plantation", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2537}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 2538, name:"building_barracks", level:5});
CREATE (n: Building {id: 2539, name:"building_government_administration", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:2539}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.0, level: 2}]->(b);
CREATE (n: Building {id: 2540, name:"building_glassworks", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2540}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 31.088352435604328, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2540}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2540}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 5.181392072600721,level: 2}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2540}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 10.362784145201442,level: 2}]->(g);
CREATE (n: Building {id: 2541, name:"building_iron_mine", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:2541}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2541}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 2542, name:"building_rice_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:2542}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2542}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 0.0,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2542}) CREATE (b)-[r:Supply{max_supply: 36.0, current_output: 0.0,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2542}) CREATE (b)-[r:Supply{max_supply: 54.0, current_output: 0.0,level: 3}]->(g);
CREATE (n: Building {id: 2543, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2543}) CREATE (g)-[r:Demand{max_demand: 4.642099009900989, current_input: 8.485147290453527, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2543}) CREATE (g)-[r:Demand{max_demand: 4.642099009900989, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2543}) CREATE (b)-[r:Supply{max_supply: 9.284198019801979, current_output: 4.642099009900989,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2543}) CREATE (b)-[r:Supply{max_supply: 18.568396039603957, current_output: 9.284198019801979,level: 2}]->(g);
CREATE (n: Building {id: 2544, name:"building_barracks", level:10});
CREATE (n: Building {id: 3492, name:"building_subsistence_rice_paddies", level:64});
MATCH (g: Goods{code: 7}), (b: Building{id:3492}) CREATE (b)-[r:Supply{max_supply: 335.5369583333333, current_output: 402.64435,level: 64}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3492}) CREATE (b)-[r:Supply{max_supply: 45.75503333333333, current_output: 54.90604,level: 64}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3492}) CREATE (b)-[r:Supply{max_supply: 45.75503333333333, current_output: 54.90604,level: 64}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3492}) CREATE (b)-[r:Supply{max_supply: 61.00671666666667, current_output: 73.20806,level: 64}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3492}) CREATE (b)-[r:Supply{max_supply: 61.00671666666667, current_output: 73.20806,level: 64}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3492}) CREATE (b)-[r:Supply{max_supply: 61.00671666666667, current_output: 73.20806,level: 64}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3492}) CREATE (b)-[r:Supply{max_supply: 61.00671666666667, current_output: 73.20806,level: 64}]->(g);
CREATE (n: Building {id: 3493, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3493}) CREATE (g)-[r:Demand{max_demand: 1.1771980198019802, current_input: 0.6099524487683248, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3493}) CREATE (g)-[r:Demand{max_demand: 1.1771980198019802, current_input: 1.9032203803050454, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3493}) CREATE (b)-[r:Supply{max_supply: 23.544, current_output: 17.87153474786557,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3493}) CREATE (b)-[r:Supply{max_supply: 5.886, current_output: 4.467883686966393,level: 2}]->(g);
CREATE (n: Building {id: 3494, name:"building_subsistence_rice_paddies", level:82});
MATCH (g: Goods{code: 7}), (b: Building{id:3494}) CREATE (b)-[r:Supply{max_supply: 447.72122500000006, current_output: 537.26547,level: 82}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3494}) CREATE (b)-[r:Supply{max_supply: 61.05289166666667, current_output: 73.26347,level: 82}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3494}) CREATE (b)-[r:Supply{max_supply: 61.05289166666667, current_output: 73.26347,level: 82}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3494}) CREATE (b)-[r:Supply{max_supply: 81.40385833333333, current_output: 97.68463,level: 82}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3494}) CREATE (b)-[r:Supply{max_supply: 81.40385833333333, current_output: 97.68463,level: 82}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3494}) CREATE (b)-[r:Supply{max_supply: 81.40385833333333, current_output: 97.68463,level: 82}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3494}) CREATE (b)-[r:Supply{max_supply: 81.40385833333333, current_output: 97.68463,level: 82}]->(g);
CREATE (n: Building {id: 3495, name:"building_subsistence_rice_paddies", level:108});
MATCH (g: Goods{code: 7}), (b: Building{id:3495}) CREATE (b)-[r:Supply{max_supply: 589.9786153846154, current_output: 766.9722,level: 108}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3495}) CREATE (b)-[r:Supply{max_supply: 80.45162307692307, current_output: 104.58711,level: 108}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3495}) CREATE (b)-[r:Supply{max_supply: 80.45162307692307, current_output: 104.58711,level: 108}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3495}) CREATE (b)-[r:Supply{max_supply: 107.26883846153845, current_output: 139.44949,level: 108}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3495}) CREATE (b)-[r:Supply{max_supply: 107.26883846153845, current_output: 139.44949,level: 108}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3495}) CREATE (b)-[r:Supply{max_supply: 107.26883846153845, current_output: 139.44949,level: 108}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3495}) CREATE (b)-[r:Supply{max_supply: 107.26883846153845, current_output: 139.44949,level: 108}]->(g);
CREATE (n: Building {id: 3496, name:"building_subsistence_rice_paddies", level:36});
MATCH (g: Goods{code: 7}), (b: Building{id:3496}) CREATE (b)-[r:Supply{max_supply: 194.39244, current_output: 194.39244,level: 36}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3496}) CREATE (b)-[r:Supply{max_supply: 26.50806, current_output: 26.50806,level: 36}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3496}) CREATE (b)-[r:Supply{max_supply: 26.50806, current_output: 26.50806,level: 36}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3496}) CREATE (b)-[r:Supply{max_supply: 35.34408, current_output: 35.34408,level: 36}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3496}) CREATE (b)-[r:Supply{max_supply: 35.34408, current_output: 35.34408,level: 36}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3496}) CREATE (b)-[r:Supply{max_supply: 35.34408, current_output: 35.34408,level: 36}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3496}) CREATE (b)-[r:Supply{max_supply: 35.34408, current_output: 35.34408,level: 36}]->(g);
CREATE (n: Building {id: 3497, name:"building_subsistence_rice_paddies", level:95});
MATCH (g: Goods{code: 7}), (b: Building{id:3497}) CREATE (b)-[r:Supply{max_supply: 464.58087, current_output: 464.58087,level: 95}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3497}) CREATE (b)-[r:Supply{max_supply: 63.35193, current_output: 63.35193,level: 95}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3497}) CREATE (b)-[r:Supply{max_supply: 63.35193, current_output: 63.35193,level: 95}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3497}) CREATE (b)-[r:Supply{max_supply: 84.46925, current_output: 84.46925,level: 95}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3497}) CREATE (b)-[r:Supply{max_supply: 84.46925, current_output: 84.46925,level: 95}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3497}) CREATE (b)-[r:Supply{max_supply: 84.46925, current_output: 84.46925,level: 95}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3497}) CREATE (b)-[r:Supply{max_supply: 84.46925, current_output: 84.46925,level: 95}]->(g);
CREATE (n: Building {id: 3498, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3498}) CREATE (b)-[r:Supply{max_supply: 9.435, current_output: 9.435,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3498}) CREATE (b)-[r:Supply{max_supply: 3.145, current_output: 3.145,level: 1}]->(g);
CREATE (n: Building {id: 4282, name:"building_conscription_center", level:25});
CREATE (n: Building {id: 4512, name:"building_conscription_center", level:16});
CREATE (n: Building {id: 4689, name:"building_conscription_center", level:7});
CREATE (n: Building {id: 4977, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 4991, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 16782756, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782756}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 75.57171986219517, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782756}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 38.86044054450541, level: 1}]->(b);
CREATE (n: Building {id: 16782876, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782876}) CREATE (g)-[r:Demand{max_demand: 4.335, current_input: 13.104136224104641, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782876}) CREATE (g)-[r:Demand{max_demand: 13.005, current_input: 6.7384003904172385, level: 1}]->(b);
CREATE (n: Building {id: 5734, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:5734}) CREATE (b)-[r:Supply{max_supply: 2.19, current_output: 2.19,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:5734}) CREATE (b)-[r:Supply{max_supply: 0.73, current_output: 0.73,level: 1}]->(g);
CREATE (n: Building {id: 5891, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5891}) CREATE (g)-[r:Demand{max_demand: 4.1, current_input: 12.393762057400007, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5891}) CREATE (g)-[r:Demand{max_demand: 12.3, current_input: 6.373112249298887, level: 1}]->(b);
