CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0.021284207689543654});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:25.445670467689318, pop_demand:115.44137562383862});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:24.56976929039684, pop_demand:35.95318362946047});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:22.43137356121373, pop_demand:6.184760135602363});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:15.534470061682287, pop_demand:37.150746114397634});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:27.621560221251954, pop_demand:46.58622008480517});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:21.814305428425303, pop_demand:31.8193123373905});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:25.937059163802434, pop_demand:15.652309359204684});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:36.32177640255656, pop_demand:3.9428});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0.008237333812432624});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:16.695133884885685, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:38.49037840117345, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:1.2693744897959183});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:30.015120703792366, pop_demand:42.49752071626961});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:21.42774012080422, pop_demand:4.719275462533301});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:20.63504738437662, pop_demand:6.186070240548339});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:19.536288730839868, pop_demand:3.958259534674811});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:8.541862446521538, pop_demand:0.0018753524037387692});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:23.884433303439828, pop_demand:45.553576069588765});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:7.4046845238095225});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:1.4809369047619048});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 1730, name:"building_gold_mine", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1730}) CREATE (g)-[r:Demand{max_demand: 3.543, current_input: 3.730733179578344, level: 1}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:1730}) CREATE (b)-[r:Supply{max_supply: 7.086, current_output: 7.086,level: 1}]->(g);
CREATE (n: Building {id: 1740, name:"building_government_administration", level:3});
CREATE (n: Building {id: 1741, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1741}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 42.71690909605546, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1741}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 1742, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1742}) CREATE (g)-[r:Demand{max_demand: 4.3539, current_input: 2.7732396900484986, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1742}) CREATE (g)-[r:Demand{max_demand: 4.3539, current_input: 4.5846003924826855, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1742}) CREATE (b)-[r:Supply{max_supply: 8.7078, current_output: 7.127139690048498,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1742}) CREATE (b)-[r:Supply{max_supply: 17.4156, current_output: 14.254279380096996,level: 1}]->(g);
CREATE (n: Building {id: 1743, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1743}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1744, name:"building_vineyard_plantation", level:1});
MATCH (g: Goods{code: 39}), (b: Building{id:1744}) CREATE (b)-[r:Supply{max_supply: 18.344, current_output: 21.0956,level: 1}]->(g);
CREATE (n: Building {id: 1745, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1745}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1746, name:"building_government_administration", level:1});
CREATE (n: Building {id: 1747, name:"building_wheat_farm", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1747}) CREATE (g)-[r:Demand{max_demand: 1.8457524752475247, current_input: 1.9435534859426462, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1747}) CREATE (b)-[r:Supply{max_supply: 36.915198019801984, current_output: 36.915198019801984,level: 2}]->(g);
CREATE (n: Building {id: 1748, name:"building_textile_mills", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1748}) CREATE (g)-[r:Demand{max_demand: 30.104, current_input: 25.2243953542148, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:1748}) CREATE (g)-[r:Demand{max_demand: 3.763, current_input: 16.8615, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1748}) CREATE (b)-[r:Supply{max_supply: 45.156, current_output: 41.49629651566109,level: 1}]->(g);
CREATE (n: Building {id: 1749, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1749}) CREATE (g)-[r:Demand{max_demand: 4.3598, current_input: 2.7769977263312073, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1749}) CREATE (b)-[r:Supply{max_supply: 8.7196, current_output: 5.553995452662415,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1749}) CREATE (b)-[r:Supply{max_supply: 8.7196, current_output: 5.553995452662415,level: 1}]->(g);
CREATE (n: Building {id: 1750, name:"building_furniture_manufacturies", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1750}) CREATE (g)-[r:Demand{max_demand: 6.4108, current_input: 5.371663358251403, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1750}) CREATE (g)-[r:Demand{max_demand: 19.2324, current_input: 27.384956083299233, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1750}) CREATE (g)-[r:Demand{max_demand: 3.2054, current_input: 3.375244745645053, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1750}) CREATE (b)-[r:Supply{max_supply: 41.6702, current_output: 39.418737276211374,level: 1}]->(g);
CREATE (n: Building {id: 1751, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1751}) CREATE (g)-[r:Demand{max_demand: 9.563, current_input: 10.069715324952782, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1751}) CREATE (b)-[r:Supply{max_supply: 114.756, current_output: 114.756,level: 2}]->(g);
CREATE (n: Building {id: 1752, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1752}) CREATE (g)-[r:Demand{max_demand: 4.48625, current_input: 2.857540724288586, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1752}) CREATE (b)-[r:Supply{max_supply: 8.9725, current_output: 5.715081448577172,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1752}) CREATE (b)-[r:Supply{max_supply: 8.9725, current_output: 5.715081448577172,level: 1}]->(g);
CREATE (n: Building {id: 1753, name:"building_maize_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1753}) CREATE (g)-[r:Demand{max_demand: 0.9793259259259258, current_input: 1.0312175347087595, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:1753}) CREATE (b)-[r:Supply{max_supply: 19.586599999999997, current_output: 19.586599999999997,level: 1}]->(g);
CREATE (n: Building {id: 1754, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1754}) CREATE (g)-[r:Demand{max_demand: 3.48035, current_input: 2.216827385851832, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:1754}) CREATE (b)-[r:Supply{max_supply: 6.9607, current_output: 4.433654771703664,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1754}) CREATE (b)-[r:Supply{max_supply: 6.9607, current_output: 4.433654771703664,level: 1}]->(g);
CREATE (n: Building {id: 1755, name:"building_tobacco_plantation", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:1755}) CREATE (b)-[r:Supply{max_supply: 71.84474452554744, current_output: 98.4273,level: 3}]->(g);
CREATE (n: Building {id: 1756, name:"building_gold_mine", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1756}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.264935336689732, level: 1}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:1756}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
CREATE (n: Building {id: 3003, name:"building_subsistence_pastures", level:98});
MATCH (g: Goods{code: 7}), (b: Building{id:3003}) CREATE (b)-[r:Supply{max_supply: 0.8330000000000001, current_output: 0.95795,level: 98}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3003}) CREATE (b)-[r:Supply{max_supply: 1.2494956521739131, current_output: 1.43692,level: 98}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3003}) CREATE (b)-[r:Supply{max_supply: 0.4164956521739131, current_output: 0.47897,level: 98}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3003}) CREATE (b)-[r:Supply{max_supply: 0.8330000000000001, current_output: 0.95795,level: 98}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3003}) CREATE (b)-[r:Supply{max_supply: 0.8330000000000001, current_output: 0.95795,level: 98}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3003}) CREATE (b)-[r:Supply{max_supply: 0.8330000000000001, current_output: 0.95795,level: 98}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3003}) CREATE (b)-[r:Supply{max_supply: 2.2157739130434786, current_output: 2.54814,level: 98}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3003}) CREATE (b)-[r:Supply{max_supply: 0.8330000000000001, current_output: 0.95795,level: 98}]->(g);
CREATE (n: Building {id: 3004, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 15}), (b: Building{id:3004}) CREATE (b)-[r:Supply{max_supply: 8.505, current_output: 8.505,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3004}) CREATE (b)-[r:Supply{max_supply: 2.835, current_output: 2.835,level: 1}]->(g);
CREATE (n: Building {id: 3008, name:"building_subsistence_pastures", level:64});
MATCH (g: Goods{code: 7}), (b: Building{id:3008}) CREATE (b)-[r:Supply{max_supply: 1.74144, current_output: 1.74144,level: 64}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3008}) CREATE (b)-[r:Supply{max_supply: 2.61216, current_output: 2.61216,level: 64}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3008}) CREATE (b)-[r:Supply{max_supply: 0.87072, current_output: 0.87072,level: 64}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3008}) CREATE (b)-[r:Supply{max_supply: 1.74144, current_output: 1.74144,level: 64}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3008}) CREATE (b)-[r:Supply{max_supply: 1.74144, current_output: 1.74144,level: 64}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3008}) CREATE (b)-[r:Supply{max_supply: 1.74144, current_output: 1.74144,level: 64}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3008}) CREATE (b)-[r:Supply{max_supply: 4.63223, current_output: 4.63223,level: 64}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3008}) CREATE (b)-[r:Supply{max_supply: 1.74144, current_output: 1.74144,level: 64}]->(g);
CREATE (n: Building {id: 3010, name:"building_subsistence_farms", level:147});
MATCH (g: Goods{code: 7}), (b: Building{id:3010}) CREATE (b)-[r:Supply{max_supply: 8.94642, current_output: 8.94642,level: 147}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3010}) CREATE (b)-[r:Supply{max_supply: 2.2366, current_output: 2.2366,level: 147}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3010}) CREATE (b)-[r:Supply{max_supply: 2.2366, current_output: 2.2366,level: 147}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3010}) CREATE (b)-[r:Supply{max_supply: 2.2366, current_output: 2.2366,level: 147}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3010}) CREATE (b)-[r:Supply{max_supply: 2.2366, current_output: 2.2366,level: 147}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3010}) CREATE (b)-[r:Supply{max_supply: 2.2366, current_output: 2.2366,level: 147}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3010}) CREATE (b)-[r:Supply{max_supply: 2.2366, current_output: 2.2366,level: 147}]->(g);
CREATE (n: Building {id: 3012, name:"building_subsistence_farms", level:57});
MATCH (g: Goods{code: 7}), (b: Building{id:3012}) CREATE (b)-[r:Supply{max_supply: 3.4245565217391305, current_output: 3.93824,level: 57}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3012}) CREATE (b)-[r:Supply{max_supply: 0.8561391304347826, current_output: 0.98456,level: 57}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3012}) CREATE (b)-[r:Supply{max_supply: 0.8561391304347826, current_output: 0.98456,level: 57}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3012}) CREATE (b)-[r:Supply{max_supply: 0.8561391304347826, current_output: 0.98456,level: 57}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3012}) CREATE (b)-[r:Supply{max_supply: 0.8561391304347826, current_output: 0.98456,level: 57}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3012}) CREATE (b)-[r:Supply{max_supply: 0.8561391304347826, current_output: 0.98456,level: 57}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3012}) CREATE (b)-[r:Supply{max_supply: 0.8561391304347826, current_output: 0.98456,level: 57}]->(g);
CREATE (n: Building {id: 3013, name:"building_subsistence_farms", level:112});
MATCH (g: Goods{code: 7}), (b: Building{id:3013}) CREATE (b)-[r:Supply{max_supply: 4.932474074074073, current_output: 6.65884,level: 112}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3013}) CREATE (b)-[r:Supply{max_supply: 1.2331185185185183, current_output: 1.66471,level: 112}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3013}) CREATE (b)-[r:Supply{max_supply: 1.2331185185185183, current_output: 1.66471,level: 112}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3013}) CREATE (b)-[r:Supply{max_supply: 1.2331185185185183, current_output: 1.66471,level: 112}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3013}) CREATE (b)-[r:Supply{max_supply: 1.2331185185185183, current_output: 1.66471,level: 112}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3013}) CREATE (b)-[r:Supply{max_supply: 1.2331185185185183, current_output: 1.66471,level: 112}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3013}) CREATE (b)-[r:Supply{max_supply: 1.2331185185185183, current_output: 1.66471,level: 112}]->(g);
CREATE (n: Building {id: 3014, name:"building_subsistence_pastures", level:60});
MATCH (g: Goods{code: 7}), (b: Building{id:3014}) CREATE (b)-[r:Supply{max_supply: 2.0043, current_output: 2.0043,level: 60}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3014}) CREATE (b)-[r:Supply{max_supply: 3.00645, current_output: 3.00645,level: 60}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3014}) CREATE (b)-[r:Supply{max_supply: 1.00215, current_output: 1.00215,level: 60}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3014}) CREATE (b)-[r:Supply{max_supply: 2.0043, current_output: 2.0043,level: 60}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3014}) CREATE (b)-[r:Supply{max_supply: 2.0043, current_output: 2.0043,level: 60}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3014}) CREATE (b)-[r:Supply{max_supply: 2.0043, current_output: 2.0043,level: 60}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3014}) CREATE (b)-[r:Supply{max_supply: 5.33143, current_output: 5.33143,level: 60}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3014}) CREATE (b)-[r:Supply{max_supply: 2.0043, current_output: 2.0043,level: 60}]->(g);
CREATE (n: Building {id: 3016, name:"building_subsistence_farms", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3016}) CREATE (b)-[r:Supply{max_supply: 5.0348, current_output: 4.02784,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3016}) CREATE (b)-[r:Supply{max_supply: 1.2587, current_output: 1.00696,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3016}) CREATE (b)-[r:Supply{max_supply: 1.2587, current_output: 1.00696,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3016}) CREATE (b)-[r:Supply{max_supply: 1.2587, current_output: 1.00696,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3016}) CREATE (b)-[r:Supply{max_supply: 1.2587, current_output: 1.00696,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3016}) CREATE (b)-[r:Supply{max_supply: 1.2587, current_output: 1.00696,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3016}) CREATE (b)-[r:Supply{max_supply: 1.2587, current_output: 1.00696,level: 20}]->(g);
CREATE (n: Building {id: 3018, name:"building_subsistence_pastures", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 7.5e-05, current_output: 6e-05,level: 15}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 0.0001125, current_output: 9e-05,level: 15}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 3.75e-05, current_output: 3e-05,level: 15}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 7.5e-05, current_output: 6e-05,level: 15}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 7.5e-05, current_output: 6e-05,level: 15}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 7.5e-05, current_output: 6e-05,level: 15}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 0.00018749999999999998, current_output: 0.00015,level: 15}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 7.5e-05, current_output: 6e-05,level: 15}]->(g);
CREATE (n: Building {id: 4026, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4026}) CREATE (g)-[r:Demand{max_demand: 5.8524, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4026}) CREATE (g)-[r:Demand{max_demand: 0.9754, current_input: 0.621286201721056, level: 5}]->(b);
CREATE (n: Building {id: 4027, name:"building_barracks", level:1});
MATCH (g: Goods{code: 1}), (b: Building{id:4027}) CREATE (g)-[r:Demand{max_demand: 0.977, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 4028, name:"building_naval_base", level:1});
MATCH (g: Goods{code: 5}), (b: Building{id:4028}) CREATE (g)-[r:Demand{max_demand: 0.782, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 4178, name:"building_trade_center", level:15});
CREATE (n: Building {id: 4318, name:"building_dye_plantation", level:1});
MATCH (g: Goods{code: 21}), (b: Building{id:4318}) CREATE (b)-[r:Supply{max_supply: 12.489999999999998, current_output: 16.8615,level: 1}]->(g);
CREATE (n: Building {id: 4319, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 16781590, name:"building_barracks", level:1});
MATCH (g: Goods{code: 2}), (b: Building{id:16781590}) CREATE (g)-[r:Demand{max_demand: 0.208, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 4749, name:"building_artillery_foundries", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:4749}) CREATE (g)-[r:Demand{max_demand: 0.25905, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:4749}) CREATE (g)-[r:Demand{max_demand: 0.1727, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4749}) CREATE (b)-[r:Supply{max_supply: 0.43175, current_output: 0.0,level: 1}]->(g);
CREATE (n: Building {id: 5057, name:"building_conscription_center", level:1});
CREATE (n: Building {id: 5191, name:"building_sugar_plantation", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:5191}) CREATE (b)-[r:Supply{max_supply: 0.03, current_output: 0.0405,level: 1}]->(g);
CREATE (n: Building {id: 5514, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:5514}) CREATE (b)-[r:Supply{max_supply: 5.513999999999999, current_output: 7.4439,level: 1}]->(g);
CREATE (n: Building {id: 5515, name:"building_barracks", level:1});
MATCH (g: Goods{code: 2}), (b: Building{id:5515}) CREATE (g)-[r:Demand{max_demand: 0.182, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 5595, name:"building_banana_plantation", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:5595}) CREATE (b)-[r:Supply{max_supply: 0.18, current_output: 0.18,level: 1}]->(g);
