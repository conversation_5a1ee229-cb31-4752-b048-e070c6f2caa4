CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:85.06669764906948, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:101.4844943251311, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:118.42062737548814, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:104.81393819431965, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:23.386739226390855, pop_demand:6103.922134828689});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:29.83312737256293, pop_demand:974.8373351865081});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:27.51270807271842, pop_demand:535.6788218825379});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:31.747529283940146, pop_demand:828.0294889519782});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:45.17189771439534, pop_demand:602.6824989765327});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:35.11430247571279, pop_demand:1613.0179683790866});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:34.277682849506704, pop_demand:1052.9247347962798});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:50.25498468020508, pop_demand:97.67941690578334});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:21.698010019554467, pop_demand:609.1867179215742});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:48.548755723190204, pop_demand:32.0634133574812});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:86.9590386579184, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:32.3535457348407, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:62.609756097560975, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:86.44743206687173, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:50.06802592902417, pop_demand:172.29796277698844});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:68.29447624539154, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:67.34088101104439, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:67.76228761538792, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:86.18569933692638, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:65.91874526802064, pop_demand:63.697555981494034});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:7.4999999999999964, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:67.13950655944588, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:87.5, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:113.28880776227624, pop_demand:106.05910158196443});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:44.28830764392605, pop_demand:743.9574922096944});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:34.659662120517474, pop_demand:9.667300090675331});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:40.2871076153325, pop_demand:2093.6895532349413});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:27.60842610312593, pop_demand:508.78607151418976});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:29.892577493016837, pop_demand:18.737603485810137});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:41.87850069537335, pop_demand:91.20748611379362});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:66.14374383909636, pop_demand:392.86809194879544});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:72.11980761561105, pop_demand:462.1311615771875});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:92.80866082083165, pop_demand:462.1311615771875});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:337.6044818823818, pop_demand:6.163357105584504});
CREATE (n: Building {id: 209, name:"building_government_administrationlevel", level:14});
MATCH (g: Goods{code: 14}), (b: Building{id:209}) CREATE (g)-[r:Demand{max_demand: 280.0, current_input: 28.52192174257953, level: 14}]->(b);
CREATE (n: Building {id: 210, name:"building_construction_sectorlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:210}) CREATE (g)-[r:Demand{max_demand: 99.007, current_input: 49.523879143077764, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:210}) CREATE (g)-[r:Demand{max_demand: 198.014, current_input: 43.25930324588209, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:210}) CREATE (g)-[r:Demand{max_demand: 247.5175, current_input: 14.833827051872857, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:210}) CREATE (g)-[r:Demand{max_demand: 49.5035, current_input: 4.846388532634407, level: 5}]->(b);
CREATE (n: Building {id: 211, name:"building_universitylevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:211}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 2.5466001555874582, level: 5}]->(b);
CREATE (n: Building {id: 212, name:"building_arts_academylevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:212}) CREATE (g)-[r:Demand{max_demand: 12.5, current_input: 1.2733000777937291, level: 5}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:212}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 0.5093200311174916,level: 5}]->(g);
CREATE (n: Building {id: 213, name:"building_paper_millslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:213}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 32.76988236630901, level: 5}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:213}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 1.4794478293657545, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:213}) CREATE (b)-[r:Supply{max_supply: 350.0, current_output: 43.40959683014066,level: 5}]->(g);
CREATE (n: Building {id: 214, name:"building_furniture_manufacturieslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:214}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 40.016466830084944, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:214}) CREATE (g)-[r:Demand{max_demand: 159.99999999999997, current_input: 34.95454119072961, level: 8}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:214}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 6.217097622700633, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:214}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 3.915996673071121, level: 8}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:214}) CREATE (b)-[r:Supply{max_supply: 359.99999999999994, current_output: 80.4856819435792,level: 8}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:214}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 35.771414197146306,level: 8}]->(g);
CREATE (n: Building {id: 215, name:"building_wheat_farmlevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:215}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 140.4,level: 8}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:215}) CREATE (b)-[r:Supply{max_supply: 64.0, current_output: 74.88,level: 8}]->(g);
CREATE (n: Building {id: 216, name:"building_livestock_ranchlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:216}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 30.96869539629106, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:216}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 3.915996673071122, level: 4}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:216}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 69.76938413872436,level: 4}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:216}) CREATE (b)-[r:Supply{max_supply: 20.000000000000004, current_output: 8.721173017340545,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:216}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 43.60586508670273,level: 4}]->(g);
CREATE (n: Building {id: 217, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:217}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.1261366979887508, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:217}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:217}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 2.8153417449718767,level: 1}]->(g);
CREATE (n: Building {id: 218, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:218}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.7047342027055767, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:218}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8391109300591055, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:218}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8345995809727663, level: 10}]->(b);
CREATE (n: Building {id: 221, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:221}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 45.018525183845576, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:221}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 40.262068965517244, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:221}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.6951219512195124, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:221}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 69.86189195162784,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:221}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 34.93094597581392,level: 3}]->(g);
CREATE (n: Building {id: 222, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:222}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.033298270382315, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:222}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 40.33298270382315,level: 2}]->(g);
CREATE (n: Building {id: 223, name:"building_silk_plantationlevel", level:8});
MATCH (g: Goods{code: 20}), (b: Building{id:223}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 171.2,level: 8}]->(g);
CREATE (n: Building {id: 224, name:"building_wheat_farmlevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:224}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 163.5,level: 10}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:224}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 87.2,level: 10}]->(g);
CREATE (n: Building {id: 225, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:225}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.35236710135278837, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:225}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.41955546502955277, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:225}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.41729979048638316, level: 5}]->(b);
CREATE (n: Building {id: 226, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:226}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.778209824519379, level: 10}]->(b);
CREATE (n: Building {id: 227, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:227}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 6.049947405573473, level: 3}]->(b);
CREATE (n: Building {id: 228, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:228}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 8.149120497879867, level: 4}]->(b);
CREATE (n: Building {id: 229, name:"building_shipyardslevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:229}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 60.024700245127434, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:229}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 52.43181178609442, level: 6}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:229}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 32.340227301815496,level: 6}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:229}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 43.12030306908733,level: 6}]->(g);
CREATE (n: Building {id: 230, name:"building_textile_millslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:230}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 120.04940049025487, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:230}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 107.36551724137928, level: 8}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:230}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 9.853658536585364, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:230}) CREATE (b)-[r:Supply{max_supply: 319.99999999999994, current_output: 186.2983785376742,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:230}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 93.1491892688371,level: 8}]->(g);
CREATE (n: Building {id: 231, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:231}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 6.049947405573473, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:231}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 60.49947405573474,level: 3}]->(g);
CREATE (n: Building {id: 232, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:232}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.7047342027055767, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:232}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8391109300591055, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:232}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8345995809727663, level: 10}]->(b);
CREATE (n: Building {id: 233, name:"building_wheat_farmlevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:233}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 94.5,level: 6}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:233}) CREATE (b)-[r:Supply{max_supply: 48.0, current_output: 50.4,level: 6}]->(g);
CREATE (n: Building {id: 234, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:234}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.9579983365355607, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:234}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 5.873995009606682,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:234}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 4.894995841338902,level: 2}]->(g);
CREATE (n: Building {id: 235, name:"building_naval_baselevel", level:15});
MATCH (g: Goods{code: 5}), (b: Building{id:235}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 10.167314736779069, level: 15}]->(b);
CREATE (n: Building {id: 236, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:236}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 6.049947405573473, level: 3}]->(b);
CREATE (n: Building {id: 237, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:237}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 6.111840373409899, level: 3}]->(b);
CREATE (n: Building {id: 238, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:238}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.0186400622349834, level: 2}]->(b);
CREATE (n: Building {id: 239, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:239}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.033298270382315, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:239}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 40.33298270382315,level: 2}]->(g);
CREATE (n: Building {id: 240, name:"building_glassworkslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:240}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 21.84658824420601, level: 5}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:240}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 4.671940513786593, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:240}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 31.190469271779197,level: 5}]->(g);
CREATE (n: Building {id: 241, name:"building_wheat_farmlevel", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:241}) CREATE (b)-[r:Supply{max_supply: 135.0, current_output: 145.8,level: 9}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:241}) CREATE (b)-[r:Supply{max_supply: 72.0, current_output: 77.76,level: 9}]->(g);
CREATE (n: Building {id: 242, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:242}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 15.484347698145527, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:242}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.9579983365355607, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:242}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 34.884692069362174,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:242}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 4.360586508670272,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:242}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 21.802932543351357,level: 2}]->(g);
CREATE (n: Building {id: 243, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:243}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.057101304058365, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:243}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.2586663950886585, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:243}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.2518993714591495, level: 15}]->(b);
CREATE (n: Building {id: 244, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:244}) CREATE (g)-[r:Demand{max_demand: 39.6028, current_input: 19.809551657231104, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:244}) CREATE (g)-[r:Demand{max_demand: 79.2056, current_input: 17.303721298352837, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:244}) CREATE (g)-[r:Demand{max_demand: 99.007, current_input: 5.933530820749143, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:244}) CREATE (g)-[r:Demand{max_demand: 19.8014, current_input: 1.9385554130537628, level: 2}]->(b);
CREATE (n: Building {id: 245, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:245}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 12.223680746819799, level: 6}]->(b);
CREATE (n: Building {id: 246, name:"building_textile_millslevel", level:7});
MATCH (g: Goods{code: 9}), (b: Building{id:246}) CREATE (g)-[r:Demand{max_demand: 209.99999999999997, current_input: 105.04322542897299, level: 7}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:246}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 93.9448275862069, level: 7}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:246}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 8.621951219512194, level: 7}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:246}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 163.01108122046494,level: 7}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:246}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 81.50554061023247,level: 7}]->(g);
CREATE (n: Building {id: 247, name:"building_paper_millslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:247}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 39.32385883957081, level: 6}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:247}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 1.7753373952389055, level: 6}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:247}) CREATE (b)-[r:Supply{max_supply: 420.0, current_output: 52.09151619616878,level: 6}]->(g);
CREATE (n: Building {id: 248, name:"building_arms_industrylevel", level:5});
MATCH (g: Goods{code: 24}), (b: Building{id:248}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 5.993041725079179, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:248}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 3.885686014187896, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:248}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 5.161655157545614,level: 5}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:248}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 5.161655157545614,level: 5}]->(g);
CREATE (n: Building {id: 249, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:249}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.9579983365355607, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:249}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 15.663986692284485,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:249}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 3.9159966730711213,level: 4}]->(g);
CREATE (n: Building {id: 250, name:"building_coal_minelevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:250}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 3.915996673071122, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:250}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 15.663986692284489,level: 4}]->(g);
CREATE (n: Building {id: 251, name:"building_silk_plantationlevel", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:251}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 218.0,level: 10}]->(g);
CREATE (n: Building {id: 252, name:"building_wheat_farmlevel", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:252}) CREATE (b)-[r:Supply{max_supply: 225.00000000000003, current_output: 256.5,level: 15}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:252}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 136.8,level: 15}]->(g);
CREATE (n: Building {id: 253, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:253}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.057101304058365, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:253}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.2586663950886585, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:253}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.2518993714591495, level: 15}]->(b);
CREATE (n: Building {id: 254, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:254}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.1261366979887508, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:254}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:254}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 2.8153417449718767,level: 1}]->(g);
CREATE (n: Building {id: 255, name:"building_arms_industrylevel", level:4});
MATCH (g: Goods{code: 24}), (b: Building{id:255}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 4.794433380063343, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:255}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 3.108548811350317, level: 4}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:255}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 4.129324126036491,level: 4}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:255}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 4.129324126036491,level: 4}]->(g);
CREATE (n: Building {id: 256, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:256}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9789991682677803, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:256}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 7.831993346142243,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:256}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 1.9579983365355607,level: 2}]->(g);
CREATE (n: Building {id: 257, name:"building_munition_plantslevel", level:5});
MATCH (g: Goods{code: 25}), (b: Building{id:257}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 9.343881027573186, level: 5}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:257}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:257}) CREATE (b)-[r:Supply{max_supply: 250.0, current_output: 11.679851284466483,level: 5}]->(g);
CREATE (n: Building {id: 258, name:"building_wheat_farmlevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:258}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 140.4,level: 8}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:258}) CREATE (b)-[r:Supply{max_supply: 64.0, current_output: 74.88,level: 8}]->(g);
CREATE (n: Building {id: 259, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:259}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 15.484347698145525, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:259}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 1.9579983365355604, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:259}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 34.884692069362174,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:259}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 4.360586508670272,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:259}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 21.802932543351357,level: 2}]->(g);
CREATE (n: Building {id: 260, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 0}), (b: Building{id:260}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.4094684054111535, level: 20}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:260}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.678221860118211, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:260}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.6691991619455326, level: 20}]->(b);
CREATE (n: Building {id: 261, name:"building_naval_baselevel", level:25});
MATCH (g: Goods{code: 5}), (b: Building{id:261}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 16.94552456129845, level: 25}]->(b);
CREATE (n: Building {id: 262, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:262}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.033298270382315, level: 2}]->(b);
CREATE (n: Building {id: 263, name:"building_coal_minelevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:263}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 5.873995009606682, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:263}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 23.49598003842673,level: 6}]->(g);
CREATE (n: Building {id: 264, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:264}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 10.186400622349833, level: 5}]->(b);
CREATE (n: Building {id: 265, name:"building_textile_millslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:265}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 75.03087530640929, level: 5}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:265}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 67.10344827586206, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:265}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 6.158536585365853, level: 5}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:265}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 116.4364865860464,level: 5}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:265}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 58.2182432930232,level: 5}]->(g);
CREATE (n: Building {id: 266, name:"building_wheat_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:266}) CREATE (b)-[r:Supply{max_supply: 105.0, current_output: 121.8,level: 7}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:266}) CREATE (b)-[r:Supply{max_supply: 56.0, current_output: 64.96,level: 7}]->(g);
CREATE (n: Building {id: 267, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:267}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 15.484347698145525, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:267}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 1.9579983365355604, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:267}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 34.884692069362174,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:267}) CREATE (b)-[r:Supply{max_supply: 9.999999999999998, current_output: 4.360586508670272,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:267}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 21.802932543351357,level: 2}]->(g);
CREATE (n: Building {id: 268, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:268}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 3.3784100939662527, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:268}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 2.3972166900316716, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:268}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 5.607682829114174,level: 1}]->(g);
CREATE (n: Building {id: 269, name:"building_arms_industrylevel", level:3});
MATCH (g: Goods{code: 24}), (b: Building{id:269}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 3.595825035047507, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:269}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 2.3314116085127377, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:269}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 3.0969930945273685,level: 3}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:269}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 3.0969930945273685,level: 3}]->(g);
CREATE (n: Building {id: 270, name:"building_iron_minelevel", level:6});
MATCH (g: Goods{code: 23}), (b: Building{id:270}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 6.756820187932505, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:270}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 5.8739950096066815, level: 6}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:270}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 25.261630395078374,level: 6}]->(g);
CREATE (n: Building {id: 271, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:271}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 2.4474979206694507, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:271}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 19.579983365355606,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:271}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 4.894995841338901,level: 5}]->(g);
CREATE (n: Building {id: 272, name:"building_wheat_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:272}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 111.3,level: 7}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:272}) CREATE (b)-[r:Supply{max_supply: 56.0, current_output: 59.36,level: 7}]->(g);
CREATE (n: Building {id: 273, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:273}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.057101304058365, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:273}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.2586663950886585, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:273}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.2518993714591495, level: 15}]->(b);
CREATE (n: Building {id: 274, name:"building_paper_millslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:274}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 26.215905893047207, level: 4}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:274}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 1.1835582634926036, level: 4}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:274}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 34.72767746411252,level: 4}]->(g);
CREATE (n: Building {id: 275, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:275}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:275}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 24.48,level: 3}]->(g);
CREATE (n: Building {id: 276, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:276}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.033298270382315, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:276}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 40.33298270382315,level: 2}]->(g);
CREATE (n: Building {id: 277, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:277}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0166491351911575, level: 1}]->(b);
CREATE (n: Building {id: 278, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:278}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 61.8,level: 4}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:278}) CREATE (b)-[r:Supply{max_supply: 32.0, current_output: 32.96,level: 4}]->(g);
CREATE (n: Building {id: 279, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:279}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.742173849072763, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:279}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9789991682677803, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:279}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 17.442346034681087,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:279}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 2.180293254335136,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:279}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 10.901466271675678,level: 1}]->(g);
CREATE (n: Building {id: 280, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:280}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.48949958413389016, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:280}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 3.9159966730711213,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:280}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 0.9789991682677803,level: 1}]->(g);
CREATE (n: Building {id: 281, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:281}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 45.018525183845576, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:281}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 40.262068965517244, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:281}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.6951219512195124, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:281}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 69.86189195162784,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:281}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 34.93094597581392,level: 3}]->(g);
CREATE (n: Building {id: 282, name:"building_wheat_farmlevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:282}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 128.4,level: 8}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:282}) CREATE (b)-[r:Supply{max_supply: 64.0, current_output: 68.48,level: 8}]->(g);
CREATE (n: Building {id: 283, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:283}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 15.484347698145527, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:283}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.9579983365355607, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:283}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 34.884692069362174,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:283}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 4.360586508670272,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:283}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 21.802932543351357,level: 2}]->(g);
CREATE (n: Building {id: 284, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:284}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.057101304058365, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:284}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.2586663950886585, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:284}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.2518993714591495, level: 15}]->(b);
CREATE (n: Building {id: 285, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:285}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 6.111840373409899, level: 3}]->(b);
CREATE (n: Building {id: 286, name:"building_textile_millslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:286}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 45.018525183845576, level: 3}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:286}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 40.262068965517244, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:286}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.6951219512195124, level: 3}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:286}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 69.86189195162784,level: 3}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:286}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 34.93094597581392,level: 3}]->(g);
CREATE (n: Building {id: 287, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:287}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 10.004116707521238, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:287}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 8.738635297682404, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:287}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.5542744056751585, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:287}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9789991682677803, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:287}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 20.121420485894802,level: 2}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:287}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 8.942853549286578,level: 2}]->(g);
CREATE (n: Building {id: 288, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:288}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.4684987524016704, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:288}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 11.747990019213363,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:288}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 2.9369975048033408,level: 3}]->(g);
CREATE (n: Building {id: 289, name:"building_wheat_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:289}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 111.3,level: 7}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:289}) CREATE (b)-[r:Supply{max_supply: 56.0, current_output: 59.36,level: 7}]->(g);
CREATE (n: Building {id: 290, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 0}), (b: Building{id:290}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 1.761835506763942, level: 25}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:290}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 2.097777325147764, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:290}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 2.0864989524319157, level: 25}]->(b);
CREATE (n: Building {id: 291, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:291}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 10.186400622349833, level: 5}]->(b);
CREATE (n: Building {id: 292, name:"building_shipyardslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:292}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 40.01646683008495, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:292}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 34.954541190729614, level: 4}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:292}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 21.560151534543657,level: 4}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:292}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 28.74686871272488,level: 4}]->(g);
CREATE (n: Building {id: 293, name:"building_logging_camplevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:293}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.9579983365355607, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:293}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 15.663986692284485,level: 4}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:293}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 3.9159966730711213,level: 4}]->(g);
CREATE (n: Building {id: 294, name:"building_arms_industrylevel", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:294}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 2.3972166900316716, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:294}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.5542744056751585, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:294}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 2.0646620630182455,level: 2}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:294}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 2.0646620630182455,level: 2}]->(g);
CREATE (n: Building {id: 295, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:295}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 6.049947405573473, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:295}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 60.49947405573474,level: 3}]->(g);
CREATE (n: Building {id: 296, name:"building_naval_baselevel", level:20});
MATCH (g: Goods{code: 5}), (b: Building{id:296}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 13.556419649038759, level: 20}]->(b);
CREATE (n: Building {id: 297, name:"building_wheat_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:297}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 61.8,level: 4}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:297}) CREATE (b)-[r:Supply{max_supply: 32.0, current_output: 32.96,level: 4}]->(g);
CREATE (n: Building {id: 298, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:298}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 38.71086924536382, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:298}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 4.894995841338901, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:298}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 87.21173017340543,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:298}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 10.901466271675678,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:298}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 54.507331358378394,level: 5}]->(g);
CREATE (n: Building {id: 299, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:299}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 6.049947405573473, level: 3}]->(b);
CREATE (n: Building {id: 300, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:300}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 10.186400622349833, level: 5}]->(b);
CREATE (n: Building {id: 301, name:"building_tooling_workshopslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:301}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 26.215905893047207, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:301}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 3.0063629870186355, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:301}) CREATE (b)-[r:Supply{max_supply: 320.0, current_output: 40.96726716476688,level: 4}]->(g);
CREATE (n: Building {id: 302, name:"building_furniture_manufacturieslevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:302}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 50.020583537606186, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:302}) CREATE (g)-[r:Demand{max_demand: 199.99999999999997, current_input: 43.69317648841201, level: 10}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:302}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 7.771372028375791, level: 10}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:302}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 4.8949958413389005, level: 10}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:302}) CREATE (b)-[r:Supply{max_supply: 449.99999999999994, current_output: 100.60710242947398,level: 10}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:302}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 44.71426774643288,level: 10}]->(g);
CREATE (n: Building {id: 303, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:303}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9789991682677803, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:303}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 7.831993346142243,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:303}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 1.9579983365355607,level: 2}]->(g);
CREATE (n: Building {id: 304, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:304}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.2522733959775016, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:304}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.9579983365355607, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:304}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 8.420543465026125,level: 2}]->(g);
CREATE (n: Building {id: 305, name:"building_wheat_farmlevel", level:12});
MATCH (g: Goods{code: 7}), (b: Building{id:305}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 199.8,level: 12}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:305}) CREATE (b)-[r:Supply{max_supply: 96.0, current_output: 106.56,level: 12}]->(g);
CREATE (n: Building {id: 306, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:306}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.7047342027055767, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:306}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8391109300591055, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:306}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8345995809727663, level: 10}]->(b);
CREATE (n: Building {id: 307, name:"building_food_industrylevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:307}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 185.81217237774632, level: 6}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:307}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 113.29599258268432, level: 6}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:307}) CREATE (b)-[r:Supply{max_supply: 210.0, current_output: 130.85982217018838,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:307}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 224.33112372032295,level: 6}]->(g);
CREATE (n: Building {id: 308, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:308}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.7047342027055767, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:308}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8391109300591055, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:308}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8345995809727663, level: 10}]->(b);
CREATE (n: Building {id: 309, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:309}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.4684987524016704, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:309}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 11.747990019213363,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:309}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 2.9369975048033408,level: 3}]->(g);
CREATE (n: Building {id: 310, name:"building_wheat_farmlevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:310}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 178.5,level: 10}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:310}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 95.2,level: 10}]->(g);
CREATE (n: Building {id: 311, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:311}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 10.186400622349833, level: 5}]->(b);
CREATE (n: Building {id: 312, name:"building_furniture_manufacturieslevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:312}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 40.016466830084944, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:312}) CREATE (g)-[r:Demand{max_demand: 159.99999999999997, current_input: 34.95454119072961, level: 8}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:312}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 6.217097622700633, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:312}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 3.915996673071121, level: 8}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:312}) CREATE (b)-[r:Supply{max_supply: 359.99999999999994, current_output: 80.4856819435792,level: 8}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:312}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 35.771414197146306,level: 8}]->(g);
CREATE (n: Building {id: 313, name:"building_iron_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:313}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.1261366979887508, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:313}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9789991682677803, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:313}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 4.210271732513062,level: 1}]->(g);
CREATE (n: Building {id: 314, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:314}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:314}) CREATE (b)-[r:Supply{max_supply: 16.0, current_output: 16.16,level: 2}]->(g);
CREATE (n: Building {id: 315, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:315}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.7047342027055767, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:315}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8391109300591055, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:315}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.8345995809727663, level: 10}]->(b);
CREATE (n: Building {id: 316, name:"building_tooling_workshopslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:316}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 26.215905893047207, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:316}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 3.0063629870186355, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:316}) CREATE (b)-[r:Supply{max_supply: 320.0, current_output: 40.96726716476688,level: 4}]->(g);
CREATE (n: Building {id: 317, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:317}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:317}) CREATE (b)-[r:Supply{max_supply: 16.0, current_output: 16.16,level: 2}]->(g);
CREATE (n: Building {id: 318, name:"building_lead_minelevel", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:318}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 4.504546791955003, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:318}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 3.9159966730711213, level: 4}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:318}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 16.84108693005225,level: 4}]->(g);
CREATE (n: Building {id: 319, name:"building_sulfur_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:319}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.1261366979887508, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:319}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9789991682677803, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:319}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 4.210271732513062,level: 1}]->(g);
CREATE (n: Building {id: 320, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:320}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 2.4474979206694507, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:320}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 19.579983365355606,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:320}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 4.894995841338901,level: 5}]->(g);
CREATE (n: Building {id: 321, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:321}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.057101304058365, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:321}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.2586663950886585, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:321}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.2518993714591495, level: 15}]->(b);
CREATE (n: Building {id: 322, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:322}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 6.111840373409899, level: 3}]->(b);
CREATE (n: Building {id: 323, name:"building_food_industrylevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:323}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 92.90608618887316, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:323}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 56.64799629134216, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:323}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 65.42991108509419,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:323}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 112.16556186016147,level: 3}]->(g);
CREATE (n: Building {id: 324, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:324}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.4684987524016704, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:324}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 11.747990019213363,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:324}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 2.9369975048033408,level: 3}]->(g);
CREATE (n: Building {id: 325, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:325}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.033298270382315, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:325}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 40.33298270382315,level: 2}]->(g);
CREATE (n: Building {id: 326, name:"building_wheat_farmlevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:326}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 163.5,level: 10}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:326}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 87.2,level: 10}]->(g);
CREATE (n: Building {id: 327, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:327}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.778209824519379, level: 10}]->(b);
CREATE (n: Building {id: 328, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:328}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 6.049947405573473, level: 3}]->(b);
CREATE (n: Building {id: 329, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:329}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 10.186400622349833, level: 5}]->(b);
CREATE (n: Building {id: 330, name:"building_glassworkslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:330}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 17.477270595364807, level: 4}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:330}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 14.780487804878046, level: 4}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:330}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 3.7375524110292746, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:330}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 14.886564163531364,level: 4}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:330}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 18.608205204414205,level: 4}]->(g);
CREATE (n: Building {id: 331, name:"building_wheat_farmlevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:331}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 140.4,level: 8}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:331}) CREATE (b)-[r:Supply{max_supply: 64.0, current_output: 74.88,level: 8}]->(g);
CREATE (n: Building {id: 332, name:"building_livestock_ranchlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:332}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 30.96869539629106, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:332}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 3.915996673071122, level: 4}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:332}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 69.76938413872436,level: 4}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:332}) CREATE (b)-[r:Supply{max_supply: 20.000000000000004, current_output: 8.721173017340545,level: 4}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:332}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 43.60586508670273,level: 4}]->(g);
CREATE (n: Building {id: 333, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:333}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.35236710135278837, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:333}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.41955546502955277, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:333}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.41729979048638316, level: 5}]->(b);
CREATE (n: Building {id: 634, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:634}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.742173849072763, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:634}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.9789991682677803, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:634}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 17.442346034681087,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:634}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 2.180293254335136,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:634}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 10.901466271675678,level: 1}]->(g);
CREATE (n: Building {id: 635, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:635}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 636, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:636}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.41955546502955277, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:636}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.41729979048638316, level: 5}]->(b);
CREATE (n: Building {id: 637, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:637}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0166491351911575, level: 1}]->(b);
CREATE (n: Building {id: 1207, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1207}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.033298270382315, level: 2}]->(b);
CREATE (n: Building {id: 1208, name:"building_government_administrationlevel", level:1});
CREATE (n: Building {id: 1209, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1209}) CREATE (b)-[r:Supply{max_supply: 15.000000000000002, current_output: 17.25,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1209}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 9.2,level: 1}]->(g);
CREATE (n: Building {id: 1210, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1210}) CREATE (b)-[r:Supply{max_supply: 30.000000000000004, current_output: 34.5,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1210}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.75,level: 1}]->(g);
CREATE (n: Building {id: 1211, name:"building_barrackslevel", level:2});
MATCH (g: Goods{code: 2}), (b: Building{id:1211}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.08345995809727663, level: 2}]->(b);
CREATE (n: Building {id: 1221, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1221}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0166491351911575, level: 1}]->(b);
CREATE (n: Building {id: 1222, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1222}) CREATE (b)-[r:Supply{max_supply: 11.6307, current_output: 11.6307,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1222}) CREATE (b)-[r:Supply{max_supply: 6.20304, current_output: 6.20304,level: 1}]->(g);
CREATE (n: Building {id: 1227, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1227}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0166491351911575, level: 1}]->(b);
CREATE (n: Building {id: 1228, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1228}) CREATE (g)-[r:Demand{max_demand: 4.26, current_input: 1.7181850631828661, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1228}) CREATE (b)-[r:Supply{max_supply: 42.6, current_output: 17.181850631828663,level: 1}]->(g);
CREATE (n: Building {id: 1229, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1229}) CREATE (b)-[r:Supply{max_supply: 12.73845, current_output: 12.73845,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1229}) CREATE (b)-[r:Supply{max_supply: 6.79384, current_output: 6.79384,level: 1}]->(g);
CREATE (n: Building {id: 1259, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1260, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1260}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.6,level: 2}]->(g);
CREATE (n: Building {id: 1261, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1732, name:"building_sugar_plantationlevel", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:1732}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 91.8,level: 3}]->(g);
CREATE (n: Building {id: 1733, name:"building_coffee_plantationlevel", level:2});
MATCH (g: Goods{code: 41}), (b: Building{id:1733}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 1734, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1734}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 1735, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1762, name:"building_portlevel", level:1});
CREATE (n: Building {id: 2328, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:2328}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.6,level: 2}]->(g);
CREATE (n: Building {id: 2329, name:"building_portlevel", level:1});
CREATE (n: Building {id: 2353, name:"building_dye_plantationlevel", level:2});
MATCH (g: Goods{code: 21}), (b: Building{id:2353}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 2354, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2354}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.0166491351911575, level: 1}]->(b);
CREATE (n: Building {id: 2878, name:"building_subsistence_farmslevel", level:35});
MATCH (g: Goods{code: 7}), (b: Building{id:2878}) CREATE (b)-[r:Supply{max_supply: 67.96825, current_output: 67.96825,level: 35}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2878}) CREATE (b)-[r:Supply{max_supply: 13.59365, current_output: 13.59365,level: 35}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2878}) CREATE (b)-[r:Supply{max_supply: 13.59365, current_output: 13.59365,level: 35}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2878}) CREATE (b)-[r:Supply{max_supply: 13.59365, current_output: 13.59365,level: 35}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2878}) CREATE (b)-[r:Supply{max_supply: 13.59365, current_output: 13.59365,level: 35}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2878}) CREATE (b)-[r:Supply{max_supply: 13.59365, current_output: 13.59365,level: 35}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2878}) CREATE (b)-[r:Supply{max_supply: 19.03111, current_output: 19.03111,level: 35}]->(g);
CREATE (n: Building {id: 2879, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2879}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.184658824420601, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2879}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.400362399822422, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2879}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 14.34008489697209,level: 2}]->(g);
CREATE (n: Building {id: 2880, name:"building_subsistence_farmslevel", level:82});
MATCH (g: Goods{code: 7}), (b: Building{id:2880}) CREATE (b)-[r:Supply{max_supply: 205.0, current_output: 205.0,level: 82}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2880}) CREATE (b)-[r:Supply{max_supply: 41.0, current_output: 41.0,level: 82}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2880}) CREATE (b)-[r:Supply{max_supply: 41.0, current_output: 41.0,level: 82}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2880}) CREATE (b)-[r:Supply{max_supply: 41.0, current_output: 41.0,level: 82}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2880}) CREATE (b)-[r:Supply{max_supply: 41.0, current_output: 41.0,level: 82}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2880}) CREATE (b)-[r:Supply{max_supply: 41.0, current_output: 41.0,level: 82}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2880}) CREATE (b)-[r:Supply{max_supply: 57.4, current_output: 57.4,level: 82}]->(g);
CREATE (n: Building {id: 2881, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2881}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.553976473261803, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2881}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 4.201087199467266, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2881}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 43.020254690916275,level: 6}]->(g);
CREATE (n: Building {id: 3131, name:"building_subsistence_farmslevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3131}) CREATE (b)-[r:Supply{max_supply: 2.39, current_output: 2.39,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3131}) CREATE (b)-[r:Supply{max_supply: 0.478, current_output: 0.478,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3131}) CREATE (b)-[r:Supply{max_supply: 0.478, current_output: 0.478,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3131}) CREATE (b)-[r:Supply{max_supply: 0.478, current_output: 0.478,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3131}) CREATE (b)-[r:Supply{max_supply: 0.478, current_output: 0.478,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3131}) CREATE (b)-[r:Supply{max_supply: 0.478, current_output: 0.478,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3131}) CREATE (b)-[r:Supply{max_supply: 0.6692, current_output: 0.6692,level: 5}]->(g);
CREATE (n: Building {id: 3214, name:"building_subsistence_orchardslevel", level:18});
CREATE (n: Building {id: 3219, name:"building_subsistence_farmslevel", level:13});
MATCH (g: Goods{code: 7}), (b: Building{id:3219}) CREATE (b)-[r:Supply{max_supply: 4.666347826086957, current_output: 5.3663,level: 13}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3219}) CREATE (b)-[r:Supply{max_supply: 0.9332695652173915, current_output: 1.07326,level: 13}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3219}) CREATE (b)-[r:Supply{max_supply: 0.9332695652173915, current_output: 1.07326,level: 13}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3219}) CREATE (b)-[r:Supply{max_supply: 0.9332695652173915, current_output: 1.07326,level: 13}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3219}) CREATE (b)-[r:Supply{max_supply: 0.9332695652173915, current_output: 1.07326,level: 13}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3219}) CREATE (b)-[r:Supply{max_supply: 0.9332695652173915, current_output: 1.07326,level: 13}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3219}) CREATE (b)-[r:Supply{max_supply: 1.3065739130434784, current_output: 1.50256,level: 13}]->(g);
CREATE (n: Building {id: 3221, name:"building_subsistence_orchardslevel", level:9});
CREATE (n: Building {id: 3246, name:"building_subsistence_farmslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:3246}) CREATE (b)-[r:Supply{max_supply: 9.267, current_output: 9.267,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3246}) CREATE (b)-[r:Supply{max_supply: 1.8534, current_output: 1.8534,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3246}) CREATE (b)-[r:Supply{max_supply: 1.8534, current_output: 1.8534,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3246}) CREATE (b)-[r:Supply{max_supply: 1.8534, current_output: 1.8534,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3246}) CREATE (b)-[r:Supply{max_supply: 1.8534, current_output: 1.8534,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3246}) CREATE (b)-[r:Supply{max_supply: 1.8534, current_output: 1.8534,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3246}) CREATE (b)-[r:Supply{max_supply: 2.59476, current_output: 2.59476,level: 6}]->(g);
CREATE (n: Building {id: 3278, name:"building_subsistence_farmslevel", level:79});
MATCH (g: Goods{code: 7}), (b: Building{id:3278}) CREATE (b)-[r:Supply{max_supply: 197.5, current_output: 197.5,level: 79}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3278}) CREATE (b)-[r:Supply{max_supply: 39.5, current_output: 39.5,level: 79}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3278}) CREATE (b)-[r:Supply{max_supply: 39.5, current_output: 39.5,level: 79}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3278}) CREATE (b)-[r:Supply{max_supply: 39.5, current_output: 39.5,level: 79}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3278}) CREATE (b)-[r:Supply{max_supply: 39.5, current_output: 39.5,level: 79}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3278}) CREATE (b)-[r:Supply{max_supply: 39.5, current_output: 39.5,level: 79}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3278}) CREATE (b)-[r:Supply{max_supply: 55.3, current_output: 55.3,level: 79}]->(g);
CREATE (n: Building {id: 3279, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3279}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.184658824420601, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3279}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.400362399822422, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3279}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 14.34008489697209,level: 2}]->(g);
CREATE (n: Building {id: 3280, name:"building_subsistence_farmslevel", level:65});
MATCH (g: Goods{code: 7}), (b: Building{id:3280}) CREATE (b)-[r:Supply{max_supply: 162.23025, current_output: 162.23025,level: 65}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3280}) CREATE (b)-[r:Supply{max_supply: 32.44605, current_output: 32.44605,level: 65}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3280}) CREATE (b)-[r:Supply{max_supply: 32.44605, current_output: 32.44605,level: 65}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3280}) CREATE (b)-[r:Supply{max_supply: 32.44605, current_output: 32.44605,level: 65}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3280}) CREATE (b)-[r:Supply{max_supply: 32.44605, current_output: 32.44605,level: 65}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3280}) CREATE (b)-[r:Supply{max_supply: 32.44605, current_output: 32.44605,level: 65}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3280}) CREATE (b)-[r:Supply{max_supply: 45.42447, current_output: 45.42447,level: 65}]->(g);
CREATE (n: Building {id: 3281, name:"building_urban_centerlevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3281}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 7.646305885472103, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3281}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 4.901268399378477, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3281}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 50.19029713940232,level: 7}]->(g);
CREATE (n: Building {id: 3282, name:"building_subsistence_farmslevel", level:90});
MATCH (g: Goods{code: 7}), (b: Building{id:3282}) CREATE (b)-[r:Supply{max_supply: 125.37674545454546, current_output: 137.91442,level: 90}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3282}) CREATE (b)-[r:Supply{max_supply: 25.075345454545452, current_output: 27.58288,level: 90}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3282}) CREATE (b)-[r:Supply{max_supply: 25.075345454545452, current_output: 27.58288,level: 90}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3282}) CREATE (b)-[r:Supply{max_supply: 25.075345454545452, current_output: 27.58288,level: 90}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3282}) CREATE (b)-[r:Supply{max_supply: 25.075345454545452, current_output: 27.58288,level: 90}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3282}) CREATE (b)-[r:Supply{max_supply: 25.075345454545452, current_output: 27.58288,level: 90}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3282}) CREATE (b)-[r:Supply{max_supply: 35.105481818181815, current_output: 38.61603,level: 90}]->(g);
CREATE (n: Building {id: 3283, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3283}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.369317648841202, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3283}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.800724799644844, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3283}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 28.68016979394418,level: 4}]->(g);
CREATE (n: Building {id: 3284, name:"building_subsistence_farmslevel", level:51});
MATCH (g: Goods{code: 7}), (b: Building{id:3284}) CREATE (b)-[r:Supply{max_supply: 127.49999999999999, current_output: 140.25,level: 51}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3284}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 28.05,level: 51}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3284}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 28.05,level: 51}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3284}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 28.05,level: 51}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3284}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 28.05,level: 51}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3284}) CREATE (b)-[r:Supply{max_supply: 25.5, current_output: 28.05,level: 51}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3284}) CREATE (b)-[r:Supply{max_supply: 35.7, current_output: 39.27,level: 51}]->(g);
CREATE (n: Building {id: 3285, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3285}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.184658824420601, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3285}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.400362399822422, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3285}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 14.34008489697209,level: 2}]->(g);
CREATE (n: Building {id: 3286, name:"building_subsistence_farmslevel", level:53});
MATCH (g: Goods{code: 7}), (b: Building{id:3286}) CREATE (b)-[r:Supply{max_supply: 132.09322, current_output: 132.09322,level: 53}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3286}) CREATE (b)-[r:Supply{max_supply: 26.41864, current_output: 26.41864,level: 53}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3286}) CREATE (b)-[r:Supply{max_supply: 26.41864, current_output: 26.41864,level: 53}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3286}) CREATE (b)-[r:Supply{max_supply: 26.41864, current_output: 26.41864,level: 53}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3286}) CREATE (b)-[r:Supply{max_supply: 26.41864, current_output: 26.41864,level: 53}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3286}) CREATE (b)-[r:Supply{max_supply: 26.41864, current_output: 26.41864,level: 53}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3286}) CREATE (b)-[r:Supply{max_supply: 36.9861, current_output: 36.9861,level: 53}]->(g);
CREATE (n: Building {id: 3287, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3287}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.184658824420601, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3287}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.400362399822422, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3287}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 14.34008489697209,level: 2}]->(g);
CREATE (n: Building {id: 3288, name:"building_subsistence_farmslevel", level:57});
MATCH (g: Goods{code: 7}), (b: Building{id:3288}) CREATE (b)-[r:Supply{max_supply: 142.5, current_output: 142.5,level: 57}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3288}) CREATE (b)-[r:Supply{max_supply: 28.5, current_output: 28.5,level: 57}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3288}) CREATE (b)-[r:Supply{max_supply: 28.5, current_output: 28.5,level: 57}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3288}) CREATE (b)-[r:Supply{max_supply: 28.5, current_output: 28.5,level: 57}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3288}) CREATE (b)-[r:Supply{max_supply: 28.5, current_output: 28.5,level: 57}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3288}) CREATE (b)-[r:Supply{max_supply: 28.5, current_output: 28.5,level: 57}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3288}) CREATE (b)-[r:Supply{max_supply: 39.9, current_output: 39.9,level: 57}]->(g);
CREATE (n: Building {id: 3289, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3289}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0923294122103004, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3289}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.700181199911211, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3289}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 7.170042448486045,level: 1}]->(g);
CREATE (n: Building {id: 3290, name:"building_subsistence_farmslevel", level:70});
MATCH (g: Goods{code: 7}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 174.96325, current_output: 174.96325,level: 70}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 34.99265, current_output: 34.99265,level: 70}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 34.99265, current_output: 34.99265,level: 70}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 34.99265, current_output: 34.99265,level: 70}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 34.99265, current_output: 34.99265,level: 70}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 34.99265, current_output: 34.99265,level: 70}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3290}) CREATE (b)-[r:Supply{max_supply: 48.98971, current_output: 48.98971,level: 70}]->(g);
CREATE (n: Building {id: 3291, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3291}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0923294122103004, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3291}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.700181199911211, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3291}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 7.170042448486045,level: 1}]->(g);
CREATE (n: Building {id: 3292, name:"building_subsistence_farmslevel", level:63});
MATCH (g: Goods{code: 7}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 157.5, current_output: 157.5,level: 63}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 31.5, current_output: 31.5,level: 63}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 31.5, current_output: 31.5,level: 63}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 31.5, current_output: 31.5,level: 63}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 31.5, current_output: 31.5,level: 63}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 31.5, current_output: 31.5,level: 63}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3292}) CREATE (b)-[r:Supply{max_supply: 44.1, current_output: 44.1,level: 63}]->(g);
CREATE (n: Building {id: 3293, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3293}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.184658824420601, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3293}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.400362399822422, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3293}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 14.34008489697209,level: 2}]->(g);
CREATE (n: Building {id: 3294, name:"building_subsistence_farmslevel", level:91});
MATCH (g: Goods{code: 7}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 227.5, current_output: 227.5,level: 91}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 45.5, current_output: 45.5,level: 91}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 45.5, current_output: 45.5,level: 91}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 45.5, current_output: 45.5,level: 91}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 45.5, current_output: 45.5,level: 91}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 45.5, current_output: 45.5,level: 91}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3294}) CREATE (b)-[r:Supply{max_supply: 63.7, current_output: 63.7,level: 91}]->(g);
CREATE (n: Building {id: 3295, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3295}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.369317648841202, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3295}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.800724799644844, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3295}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 28.68016979394418,level: 4}]->(g);
CREATE (n: Building {id: 3296, name:"building_subsistence_farmslevel", level:78});
MATCH (g: Goods{code: 7}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 113.0376, current_output: 113.0376,level: 78}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 22.60752, current_output: 22.60752,level: 78}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 22.60752, current_output: 22.60752,level: 78}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 22.60752, current_output: 22.60752,level: 78}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 22.60752, current_output: 22.60752,level: 78}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 22.60752, current_output: 22.60752,level: 78}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3296}) CREATE (b)-[r:Supply{max_supply: 31.65052, current_output: 31.65052,level: 78}]->(g);
CREATE (n: Building {id: 3297, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3297}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.369317648841202, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3297}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.800724799644844, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3297}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 28.68016979394418,level: 4}]->(g);
CREATE (n: Building {id: 3298, name:"building_subsistence_farmslevel", level:70});
MATCH (g: Goods{code: 7}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 104.63949999999998, current_output: 115.10345,level: 70}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 20.927899999999998, current_output: 23.02069,level: 70}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 20.927899999999998, current_output: 23.02069,level: 70}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 20.927899999999998, current_output: 23.02069,level: 70}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 20.927899999999998, current_output: 23.02069,level: 70}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 20.927899999999998, current_output: 23.02069,level: 70}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3298}) CREATE (b)-[r:Supply{max_supply: 29.299054545454545, current_output: 32.22896,level: 70}]->(g);
CREATE (n: Building {id: 3299, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3299}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.184658824420601, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3299}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.400362399822422, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3299}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 14.34008489697209,level: 2}]->(g);
CREATE (n: Building {id: 3300, name:"building_subsistence_farmslevel", level:88});
MATCH (g: Goods{code: 7}), (b: Building{id:3300}) CREATE (b)-[r:Supply{max_supply: 76.5006, current_output: 76.5006,level: 88}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3300}) CREATE (b)-[r:Supply{max_supply: 15.30012, current_output: 15.30012,level: 88}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3300}) CREATE (b)-[r:Supply{max_supply: 15.30012, current_output: 15.30012,level: 88}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3300}) CREATE (b)-[r:Supply{max_supply: 15.30012, current_output: 15.30012,level: 88}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3300}) CREATE (b)-[r:Supply{max_supply: 15.30012, current_output: 15.30012,level: 88}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3300}) CREATE (b)-[r:Supply{max_supply: 15.30012, current_output: 15.30012,level: 88}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3300}) CREATE (b)-[r:Supply{max_supply: 21.42016, current_output: 21.42016,level: 88}]->(g);
CREATE (n: Building {id: 3301, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3301}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.2769882366309013, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3301}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.100543599733633, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3301}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 21.510127345458137,level: 3}]->(g);
CREATE (n: Building {id: 3302, name:"building_subsistence_farmslevel", level:53});
MATCH (g: Goods{code: 7}), (b: Building{id:3302}) CREATE (b)-[r:Supply{max_supply: 115.69635, current_output: 115.69635,level: 53}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3302}) CREATE (b)-[r:Supply{max_supply: 23.13927, current_output: 23.13927,level: 53}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3302}) CREATE (b)-[r:Supply{max_supply: 23.13927, current_output: 23.13927,level: 53}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3302}) CREATE (b)-[r:Supply{max_supply: 23.13927, current_output: 23.13927,level: 53}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3302}) CREATE (b)-[r:Supply{max_supply: 23.13927, current_output: 23.13927,level: 53}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3302}) CREATE (b)-[r:Supply{max_supply: 23.13927, current_output: 23.13927,level: 53}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3302}) CREATE (b)-[r:Supply{max_supply: 32.39497, current_output: 32.39497,level: 53}]->(g);
CREATE (n: Building {id: 3303, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3303}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.0923294122103004, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3303}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.700181199911211, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3303}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 7.170042448486045,level: 1}]->(g);
CREATE (n: Building {id: 3304, name:"building_subsistence_farmslevel", level:70});
MATCH (g: Goods{code: 7}), (b: Building{id:3304}) CREATE (b)-[r:Supply{max_supply: 174.7025, current_output: 174.7025,level: 70}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3304}) CREATE (b)-[r:Supply{max_supply: 34.9405, current_output: 34.9405,level: 70}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3304}) CREATE (b)-[r:Supply{max_supply: 34.9405, current_output: 34.9405,level: 70}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3304}) CREATE (b)-[r:Supply{max_supply: 34.9405, current_output: 34.9405,level: 70}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3304}) CREATE (b)-[r:Supply{max_supply: 34.9405, current_output: 34.9405,level: 70}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3304}) CREATE (b)-[r:Supply{max_supply: 34.9405, current_output: 34.9405,level: 70}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3304}) CREATE (b)-[r:Supply{max_supply: 48.9167, current_output: 48.9167,level: 70}]->(g);
CREATE (n: Building {id: 3305, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3305}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.184658824420601, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3305}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.400362399822422, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3305}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 14.34008489697209,level: 2}]->(g);
CREATE (n: Building {id: 3306, name:"building_subsistence_farmslevel", level:107});
MATCH (g: Goods{code: 7}), (b: Building{id:3306}) CREATE (b)-[r:Supply{max_supply: 222.47975, current_output: 222.47975,level: 107}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3306}) CREATE (b)-[r:Supply{max_supply: 44.49595, current_output: 44.49595,level: 107}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3306}) CREATE (b)-[r:Supply{max_supply: 44.49595, current_output: 44.49595,level: 107}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3306}) CREATE (b)-[r:Supply{max_supply: 44.49595, current_output: 44.49595,level: 107}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3306}) CREATE (b)-[r:Supply{max_supply: 44.49595, current_output: 44.49595,level: 107}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3306}) CREATE (b)-[r:Supply{max_supply: 44.49595, current_output: 44.49595,level: 107}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3306}) CREATE (b)-[r:Supply{max_supply: 62.29433, current_output: 62.29433,level: 107}]->(g);
CREATE (n: Building {id: 3307, name:"building_subsistence_farmslevel", level:58});
MATCH (g: Goods{code: 7}), (b: Building{id:3307}) CREATE (b)-[r:Supply{max_supply: 133.0375, current_output: 146.34125,level: 58}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3307}) CREATE (b)-[r:Supply{max_supply: 26.607499999999995, current_output: 29.26825,level: 58}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3307}) CREATE (b)-[r:Supply{max_supply: 26.607499999999995, current_output: 29.26825,level: 58}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3307}) CREATE (b)-[r:Supply{max_supply: 26.607499999999995, current_output: 29.26825,level: 58}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3307}) CREATE (b)-[r:Supply{max_supply: 26.607499999999995, current_output: 29.26825,level: 58}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3307}) CREATE (b)-[r:Supply{max_supply: 26.607499999999995, current_output: 29.26825,level: 58}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3307}) CREATE (b)-[r:Supply{max_supply: 37.250499999999995, current_output: 40.97555,level: 58}]->(g);
CREATE (n: Building {id: 3308, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3308}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.184658824420601, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3308}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.400362399822422, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3308}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 14.34008489697209,level: 2}]->(g);
CREATE (n: Building {id: 3309, name:"building_subsistence_farmslevel", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3309}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 49.5,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3309}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.9,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3309}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.9,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3309}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.9,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3309}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.9,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3309}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.9,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3309}) CREATE (b)-[r:Supply{max_supply: 12.599999999999998, current_output: 13.86,level: 18}]->(g);
CREATE (n: Building {id: 3310, name:"building_urban_centerlevel", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:3310}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 8.738635297682402, level: 8}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3310}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 5.601449599289687, level: 8}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3310}) CREATE (b)-[r:Supply{max_supply: 319.99999999999994, current_output: 57.36033958788835,level: 8}]->(g);
CREATE (n: Building {id: 3352, name:"building_subsistence_fishing_villageslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:3352}) CREATE (b)-[r:Supply{max_supply: 2.81949, current_output: 2.81949,level: 6}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:3352}) CREATE (b)-[r:Supply{max_supply: 11.27796, current_output: 11.27796,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3352}) CREATE (b)-[r:Supply{max_supply: 1.40974, current_output: 1.40974,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3352}) CREATE (b)-[r:Supply{max_supply: 4.22923, current_output: 4.22923,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3352}) CREATE (b)-[r:Supply{max_supply: 2.81949, current_output: 2.81949,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3352}) CREATE (b)-[r:Supply{max_supply: 2.81949, current_output: 2.81949,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3352}) CREATE (b)-[r:Supply{max_supply: 2.81949, current_output: 2.81949,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3352}) CREATE (b)-[r:Supply{max_supply: 3.94728, current_output: 3.94728,level: 6}]->(g);
CREATE (n: Building {id: 3379, name:"building_subsistence_rice_paddieslevel", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3379}) CREATE (b)-[r:Supply{max_supply: 18.262258333333335, current_output: 21.91471,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3379}) CREATE (b)-[r:Supply{max_supply: 3.0437083333333335, current_output: 3.65245,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3379}) CREATE (b)-[r:Supply{max_supply: 3.0437083333333335, current_output: 3.65245,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3379}) CREATE (b)-[r:Supply{max_supply: 4.058275, current_output: 4.86993,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3379}) CREATE (b)-[r:Supply{max_supply: 4.058275, current_output: 4.86993,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3379}) CREATE (b)-[r:Supply{max_supply: 4.058275, current_output: 4.86993,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3379}) CREATE (b)-[r:Supply{max_supply: 6.087416666666667, current_output: 7.3049,level: 18}]->(g);
CREATE (n: Building {id: 3579, name:"building_subsistence_farmslevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 7.5, current_output: 7.5,level: 3}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 1.5, current_output: 1.5,level: 3}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 1.5, current_output: 1.5,level: 3}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 1.5, current_output: 1.5,level: 3}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 1.5, current_output: 1.5,level: 3}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 1.5, current_output: 1.5,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 2.1, current_output: 2.1,level: 3}]->(g);
CREATE (n: Building {id: 3586, name:"building_subsistence_orchardslevel", level:17});
MATCH (g: Goods{code: 7}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 2.88167, current_output: 2.88167,level: 17}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 1.44083, current_output: 1.44083,level: 17}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 4.3225, current_output: 4.3225,level: 17}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 2.88167, current_output: 2.88167,level: 17}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 2.88167, current_output: 2.88167,level: 17}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 2.88167, current_output: 2.88167,level: 17}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 7.66524, current_output: 7.66524,level: 17}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3586}) CREATE (b)-[r:Supply{max_supply: 4.03433, current_output: 4.03433,level: 17}]->(g);
CREATE (n: Building {id: 3941, name:"building_subsistence_pastureslevel", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:3941}) CREATE (b)-[r:Supply{max_supply: 3.81168, current_output: 3.81168,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3941}) CREATE (b)-[r:Supply{max_supply: 5.71752, current_output: 5.71752,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3941}) CREATE (b)-[r:Supply{max_supply: 1.90584, current_output: 1.90584,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3941}) CREATE (b)-[r:Supply{max_supply: 3.81168, current_output: 3.81168,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3941}) CREATE (b)-[r:Supply{max_supply: 3.81168, current_output: 3.81168,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3941}) CREATE (b)-[r:Supply{max_supply: 3.81168, current_output: 3.81168,level: 9}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3941}) CREATE (b)-[r:Supply{max_supply: 10.13906, current_output: 10.13906,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3941}) CREATE (b)-[r:Supply{max_supply: 5.33635, current_output: 5.33635,level: 9}]->(g);
CREATE (n: Building {id: 3962, name:"building_trade_centerlevel", level:7});
CREATE (n: Building {id: 3963, name:"building_trade_centerlevel", level:36});
CREATE (n: Building {id: 4003, name:"building_trade_centerlevel", level:27});
CREATE (n: Building {id: 4015, name:"building_trade_centerlevel", level:5});
CREATE (n: Building {id: 4043, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4044, name:"building_conscription_centerlevel", level:16});
CREATE (n: Building {id: 4245, name:"building_conscription_centerlevel", level:14});
CREATE (n: Building {id: 4246, name:"building_conscription_centerlevel", level:16});
CREATE (n: Building {id: 4247, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4248, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4249, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4250, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4251, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4252, name:"building_conscription_centerlevel", level:14});
CREATE (n: Building {id: 4253, name:"building_conscription_centerlevel", level:16});
CREATE (n: Building {id: 4254, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 4255, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4256, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4257, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4258, name:"building_conscription_centerlevel", level:11});
CREATE (n: Building {id: 4259, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4260, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4261, name:"building_conscription_centerlevel", level:11});
