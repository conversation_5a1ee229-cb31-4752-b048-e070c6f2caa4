CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:94.40822155114519, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:110.14292514300271, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:84.06886959590301, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:19.17272846185116, pop_demand:10113.844938268758});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:24.106585928236946, pop_demand:311.8856932838622});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:16.14858148066457, pop_demand:646.4075129275988});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:26.998029993314926, pop_demand:1488.3322758224026});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:48.1601584812584, pop_demand:584.052128684291});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:31.73892338208778, pop_demand:2296.0552830325064});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:32.92783177782895, pop_demand:1638.3460844590277});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:39.33532338685778, pop_demand:106.87435463683855});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:15.506405823639927, pop_demand:639.5740935459489});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:46.43536446121035, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:67.80466594314404, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:60.51670742837772, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:54.555235542583844, pop_demand:46.654980793358355});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:67.43872727235217, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:120.75729194772623, pop_demand:602.991887306482});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:44.20143654225472, pop_demand:550.8362137947645});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:42.518170640760836, pop_demand:95.07776238646682});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:33.054817587623866, pop_demand:3408.062166931146});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:66.13392378016808, pop_demand:611.6237658962538});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:72.26541025302437, pop_demand:198.88950410374616});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:50.86006021739525, pop_demand:104.40692878148474});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:52.23150831910205, pop_demand:85.26328667664595});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:133.13558378487687});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:100.2648138954401, pop_demand:703.4905351908945});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:284.1719111429157, pop_demand:4.036394093107616});
CREATE (n: Building {id: 954, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:954}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 59.04624644962186, level: 10}]->(b);
CREATE (n: Building {id: 955, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:955}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 67.27313257751713, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:955}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 80.06390990207721, level: 2}]->(b);
CREATE (n: Building {id: 956, name:"building_textile_millslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:956}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 215.27402424805484, level: 4}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:956}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 180.0,level: 4}]->(g);
CREATE (n: Building {id: 957, name:"building_glassworkslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:957}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 48.03834594124633, level: 3}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:957}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 0.0, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:957}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 8.006390990207722,level: 3}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:957}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 20.015977475519303,level: 3}]->(g);
CREATE (n: Building {id: 958, name:"building_arts_academylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:958}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.952312322481094, level: 2}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:958}) CREATE (b)-[r:Supply{max_supply: 2.0, current_output: 1.1809249289924375,level: 2}]->(g);
CREATE (n: Building {id: 959, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:959}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 14.687154948589235, level: 10}]->(b);
CREATE (n: Building {id: 960, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:960}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.6,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:960}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
CREATE (n: Building {id: 961, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:961}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 21.54116059126422, level: 3}]->(b);
CREATE (n: Building {id: 962, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:962}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 59.04624644962186, level: 10}]->(b);
CREATE (n: Building {id: 963, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:963}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 67.27313257751713, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:963}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 80.06390990207721, level: 2}]->(b);
CREATE (n: Building {id: 964, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:964}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 32.02556396083088, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:964}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 2.9342839623291157, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:964}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 36.42698990432455,level: 2}]->(g);
CREATE (n: Building {id: 965, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:965}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.904624644962188, level: 2}]->(b);
CREATE (n: Building {id: 966, name:"building_textile_millslevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:966}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 322.91103637208226, level: 6}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:966}) CREATE (b)-[r:Supply{max_supply: 270.0, current_output: 270.0,level: 6}]->(g);
CREATE (n: Building {id: 967, name:"building_furniture_manufacturieslevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:967}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 80.72775909302057, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:967}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 64.05112792166176, level: 6}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:967}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 19.01305627001342, level: 6}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:967}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 92.53218352570359,level: 6}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:967}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 74.02574682056286,level: 6}]->(g);
CREATE (n: Building {id: 968, name:"building_logging_camplevel", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:968}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 4.3241428392617305, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:968}) CREATE (b)-[r:Supply{max_supply: 400.0, current_output: 34.593142714093844,level: 10}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:968}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 8.648285678523461,level: 10}]->(g);
CREATE (n: Building {id: 969, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:969}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:969}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
CREATE (n: Building {id: 970, name:"building_saint_basils_cathedrallevel", level:1});
CREATE (n: Building {id: 971, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:971}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:971}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
CREATE (n: Building {id: 972, name:"building_rye_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:972}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 156.0,level: 5}]->(g);
CREATE (n: Building {id: 973, name:"building_logging_camplevel", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:973}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 3.459314271409384, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:973}) CREATE (b)-[r:Supply{max_supply: 320.0, current_output: 27.67451417127507,level: 8}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:973}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 6.918628542818768,level: 8}]->(g);
CREATE (n: Building {id: 974, name:"building_logging_camplevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:974}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 2.594485703557038, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:974}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 20.755885628456305,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:974}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 5.188971407114076,level: 6}]->(g);
CREATE (n: Building {id: 975, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:975}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:975}) CREATE (b)-[r:Supply{max_supply: 24.0, current_output: 24.48,level: 3}]->(g);
CREATE (n: Building {id: 976, name:"building_barrackslevel", level:8});
MATCH (g: Goods{code: 1}), (b: Building{id:976}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 1.959418554249311, level: 8}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:976}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 1.959418554249311, level: 8}]->(b);
CREATE (n: Building {id: 977, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:977}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 59.04624644962186, level: 10}]->(b);
CREATE (n: Building {id: 978, name:"building_arts_academylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:978}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 1.476156161240547, level: 1}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:978}) CREATE (b)-[r:Supply{max_supply: 1.0, current_output: 0.5904624644962188,level: 1}]->(g);
CREATE (n: Building {id: 979, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:979}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 84.66965293451074, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:979}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 3.644310628010554, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:979}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 16.093293188403166,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:979}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 64.37317275361266,level: 2}]->(g);
CREATE (n: Building {id: 980, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:980}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 178.5,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:980}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 29.75,level: 5}]->(g);
CREATE (n: Building {id: 981, name:"building_wheat_farmlevel", level:45});
MATCH (g: Goods{code: 7}), (b: Building{id:981}) CREATE (b)-[r:Supply{max_supply: 1350.0, current_output: 1822.5,level: 45}]->(g);
CREATE (n: Building {id: 982, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:982}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:982}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
CREATE (n: Building {id: 983, name:"building_textile_millslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:983}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 107.63701212402742, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:983}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 90.0,level: 2}]->(g);
CREATE (n: Building {id: 984, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:984}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 169.33930586902147, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:984}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 7.288621256021108, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:984}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 32.186586376806325,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:984}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 128.7463455072253,level: 4}]->(g);
CREATE (n: Building {id: 985, name:"building_rye_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:985}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 41.2,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:985}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 61.8,level: 4}]->(g);
CREATE (n: Building {id: 986, name:"building_logging_camplevel", level:15});
MATCH (g: Goods{code: 33}), (b: Building{id:986}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 6.486214258892595, level: 15}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:986}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 51.88971407114076,level: 15}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:986}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 12.97242851778519,level: 15}]->(g);
CREATE (n: Building {id: 987, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 1}), (b: Building{id:987}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 6.123182982029096, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:987}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 6.123182982029096, level: 25}]->(b);
CREATE (n: Building {id: 988, name:"building_food_industrylevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:988}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 127.0044794017661, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:988}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 5.466465942015832, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:988}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 24.13993978260475,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:988}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 96.559759130419,level: 3}]->(g);
CREATE (n: Building {id: 989, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:989}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.6,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:989}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
CREATE (n: Building {id: 990, name:"building_logging_camplevel", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:990}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 3.459314271409384, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:990}) CREATE (b)-[r:Supply{max_supply: 320.0, current_output: 27.67451417127507,level: 8}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:990}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 6.918628542818768,level: 8}]->(g);
CREATE (n: Building {id: 991, name:"building_barrackslevel", level:12});
MATCH (g: Goods{code: 1}), (b: Building{id:991}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 2.9391278313739666, level: 12}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:991}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 2.9391278313739666, level: 12}]->(b);
CREATE (n: Building {id: 992, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:992}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 48.03834594124633, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:992}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 64.05112792166177,level: 3}]->(g);
CREATE (n: Building {id: 993, name:"building_logging_camplevel", level:12});
MATCH (g: Goods{code: 33}), (b: Building{id:993}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 5.188971407114075, level: 12}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:993}) CREATE (b)-[r:Supply{max_supply: 479.99999999999994, current_output: 41.5117712569126,level: 12}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:993}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 10.37794281422815,level: 12}]->(g);
CREATE (n: Building {id: 994, name:"building_rye_farmlevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:994}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 74.2,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:994}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 111.3,level: 7}]->(g);
CREATE (n: Building {id: 995, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:995}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:995}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
CREATE (n: Building {id: 996, name:"building_logging_camplevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:996}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.432414283926173, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:996}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 3.459314271409384,level: 1}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:996}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 0.864828567852346,level: 1}]->(g);
CREATE (n: Building {id: 997, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:997}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 998, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:998}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.343577474294618, level: 5}]->(b);
CREATE (n: Building {id: 999, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:999}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1000, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1000}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 26.909253031006855, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1000}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 21.350375973887257, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1000}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 11.50319549510386,level: 1}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:1000}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 15.337593993471813,level: 1}]->(g);
CREATE (n: Building {id: 1001, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1001}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.18038686375474, level: 1}]->(b);
CREATE (n: Building {id: 1002, name:"building_arms_industrylevel", level:6});
MATCH (g: Goods{code: 24}), (b: Building{id:1002}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 8.802851886987346, level: 6}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:1002}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 19.01305627001342, level: 6}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:1002}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 17.56086166013032,level: 6}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:1002}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 17.56086166013032,level: 6}]->(g);
CREATE (n: Building {id: 1003, name:"building_logging_camplevel", level:15});
MATCH (g: Goods{code: 33}), (b: Building{id:1003}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 6.486214258892595, level: 15}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1003}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 51.88971407114076,level: 15}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1003}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 12.97242851778519,level: 15}]->(g);
CREATE (n: Building {id: 1004, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 1}), (b: Building{id:1004}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.6739097892174586, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1004}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.6739097892174586, level: 15}]->(b);
CREATE (n: Building {id: 1005, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:1005}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.343577474294618, level: 5}]->(b);
CREATE (n: Building {id: 1006, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:1006}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 1007, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1007}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 14.36077372750948, level: 2}]->(b);
CREATE (n: Building {id: 1008, name:"building_livestock_ranchlevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:1008}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 372.0,level: 10}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1008}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 62.0,level: 10}]->(g);
CREATE (n: Building {id: 1009, name:"building_wheat_farmlevel", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:1009}) CREATE (b)-[r:Supply{max_supply: 449.99999999999994, current_output: 607.5,level: 30}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1009}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 324.0,level: 30}]->(g);
CREATE (n: Building {id: 1010, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 8}), (b: Building{id:1010}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 1011, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:1011}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1011}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
CREATE (n: Building {id: 1012, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1012}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.6,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1012}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
CREATE (n: Building {id: 1013, name:"building_logging_camplevel", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:1013}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 4.3241428392617305, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1013}) CREATE (b)-[r:Supply{max_supply: 400.0, current_output: 34.593142714093844,level: 10}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1013}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 8.648285678523461,level: 10}]->(g);
CREATE (n: Building {id: 1014, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:1014}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1014}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
CREATE (n: Building {id: 1015, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1015}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 48.03834594124633, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1015}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 64.05112792166177,level: 3}]->(g);
CREATE (n: Building {id: 1016, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:1016}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 2.1620714196308652, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1016}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 17.296571357046922,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1016}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 4.3241428392617305,level: 5}]->(g);
CREATE (n: Building {id: 1017, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:1017}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1017}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
CREATE (n: Building {id: 1018, name:"building_shipyardslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:1018}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 107.63701212402742, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1018}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 85.40150389554903, level: 4}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1018}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 46.01278198041544,level: 4}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:1018}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 61.35037597388725,level: 4}]->(g);
CREATE (n: Building {id: 1019, name:"building_livestock_ranchlevel", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:1019}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 292.8,level: 8}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1019}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 48.8,level: 8}]->(g);
CREATE (n: Building {id: 1020, name:"building_wheat_farmlevel", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:1020}) CREATE (b)-[r:Supply{max_supply: 450.0, current_output: 580.5,level: 15}]->(g);
CREATE (n: Building {id: 1021, name:"building_naval_baselevel", level:20});
MATCH (g: Goods{code: 5}), (b: Building{id:1021}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 29.37430989717847, level: 20}]->(b);
CREATE (n: Building {id: 1022, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1022}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 21.54116059126422, level: 3}]->(b);
CREATE (n: Building {id: 1023, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1023}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.6,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1023}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
CREATE (n: Building {id: 1024, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1024}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.864828567852346, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1024}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 6.918628542818768,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1024}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 1.729657135704692,level: 2}]->(g);
CREATE (n: Building {id: 1025, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:1025}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1025}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
CREATE (n: Building {id: 1031, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1031}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1031}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 1032, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:1032}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1032}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
CREATE (n: Building {id: 1033, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1033}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.864828567852346, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1033}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 6.918628542818768,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1033}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 1.729657135704692,level: 2}]->(g);
CREATE (n: Building {id: 1034, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1034}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 53.81850606201371, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1034}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 42.70075194777451, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:1034}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 23.00639099020772,level: 2}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:1034}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 30.675187986943627,level: 2}]->(g);
CREATE (n: Building {id: 1035, name:"building_iron_minelevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:1035}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 2.594485703557038, level: 6}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1035}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 10.377942814228152,level: 6}]->(g);
CREATE (n: Building {id: 1036, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1036}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.864828567852346, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1036}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 6.918628542818768,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1036}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 1.729657135704692,level: 2}]->(g);
CREATE (n: Building {id: 1037, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:1037}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1037}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
CREATE (n: Building {id: 1038, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1038}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1039, name:"building_rye_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1039}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1039}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
CREATE (n: Building {id: 1040, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:1040}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.343577474294618, level: 5}]->(b);
CREATE (n: Building {id: 1041, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1041}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.2246365964058195, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1041}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.2246365964058195, level: 5}]->(b);
CREATE (n: Building {id: 1042, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1042}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.18038686375474, level: 1}]->(b);
CREATE (n: Building {id: 1043, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1043}) CREATE (b)-[r:Supply{max_supply: 29.99579411764706, current_output: 30.59571,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1043}) CREATE (b)-[r:Supply{max_supply: 44.99369607843137, current_output: 45.89357,level: 3}]->(g);
CREATE (n: Building {id: 1044, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:1044}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.343577474294618, level: 5}]->(b);
CREATE (n: Building {id: 1045, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:1045}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 21.54116059126422, level: 3}]->(b);
CREATE (n: Building {id: 1046, name:"building_rye_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:1046}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 41.2,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1046}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 61.8,level: 4}]->(g);
CREATE (n: Building {id: 1047, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1047}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.2246365964058195, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1047}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.2246365964058195, level: 5}]->(b);
CREATE (n: Building {id: 1048, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1048}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.18038686375474, level: 1}]->(b);
CREATE (n: Building {id: 1049, name:"building_furniture_manufacturieslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1049}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 26.909253031006855, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1049}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 32.02556396083088, level: 2}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1049}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 69.01917297062316,level: 2}]->(g);
CREATE (n: Building {id: 1050, name:"building_paper_millslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:1050}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 48.03834594124633, level: 3}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1050}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 64.05112792166177,level: 3}]->(g);
CREATE (n: Building {id: 1051, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:1051}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.2,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1051}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
CREATE (n: Building {id: 1052, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1052}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 16.01278198041544, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:1052}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 1.4671419811645579, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1052}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 18.213494952162275,level: 1}]->(g);
CREATE (n: Building {id: 1053, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:1053}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.297242851778519, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1053}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 10.377942814228152,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:1053}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 2.594485703557038,level: 3}]->(g);
CREATE (n: Building {id: 1061, name:"building_textile_millslevel", level:6});
MATCH (g: Goods{code: 9}), (b: Building{id:1061}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 322.91103637208226, level: 6}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1061}) CREATE (b)-[r:Supply{max_supply: 270.0, current_output: 270.0,level: 6}]->(g);
CREATE (n: Building {id: 1062, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1062}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.6,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:1062}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.9,level: 3}]->(g);
CREATE (n: Building {id: 1063, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:1063}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 32.02556396083088, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:1063}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 42.700751947774506,level: 2}]->(g);
CREATE (n: Building {id: 1064, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:1064}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 91.8,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1064}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.3,level: 3}]->(g);
CREATE (n: Building {id: 1065, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1065}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 16.01278198041544, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:1065}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 16.01278198041544,level: 1}]->(g);
CREATE (n: Building {id: 1137, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1137}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1466, name:"building_portlevel", level:1});
CREATE (n: Building {id: 2117, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2117}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2117}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.0,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2117}) CREATE (b)-[r:Supply{max_supply: 6.0, current_output: 6.0,level: 1}]->(g);
CREATE (n: Building {id: 2118, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2118}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2118}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 2119, name:"building_tea_plantationlevel", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:2119}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 2120, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:2120}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.7347819578434917, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:2120}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.7347819578434917, level: 3}]->(b);
CREATE (n: Building {id: 2123, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2123}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2123}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.0,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2123}) CREATE (b)-[r:Supply{max_supply: 6.0, current_output: 6.0,level: 1}]->(g);
CREATE (n: Building {id: 2124, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:2124}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 2125, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2125}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2125}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 2126, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 1}), (b: Building{id:2126}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.7347819578434917, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:2126}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.7347819578434917, level: 3}]->(b);
CREATE (n: Building {id: 2130, name:"building_wheat_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2130}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.2,level: 2}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2130}) CREATE (b)-[r:Supply{max_supply: 18.0, current_output: 18.18,level: 2}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2130}) CREATE (b)-[r:Supply{max_supply: 12.0, current_output: 12.12,level: 2}]->(g);
CREATE (n: Building {id: 2131, name:"building_rice_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2131}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2131}) CREATE (b)-[r:Supply{max_supply: 6.0, current_output: 6.0,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2131}) CREATE (b)-[r:Supply{max_supply: 9.0, current_output: 9.0,level: 1}]->(g);
CREATE (n: Building {id: 2132, name:"building_tea_plantationlevel", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:2132}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 2133, name:"building_textile_millslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2133}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 53.81850606201371, level: 1}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2133}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 1}]->(g);
CREATE (n: Building {id: 2134, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:2134}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:2134}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.449273192811639, level: 10}]->(b);
CREATE (n: Building {id: 2135, name:"building_arms_industrylevel", level:10});
MATCH (g: Goods{code: 24}), (b: Building{id:2135}) CREATE (g)-[r:Demand{max_demand: 199.99999999999997, current_input: 14.671419811645576, level: 10}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2135}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 31.688427116689027, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:2135}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 29.268102766883867,level: 10}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:2135}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 29.268102766883867,level: 10}]->(g);
CREATE (n: Building {id: 2136, name:"building_food_industrylevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2136}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 84.66965293451074, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:2136}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 3.644310628010554, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:2136}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 16.093293188403166,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2136}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 64.37317275361266,level: 2}]->(g);
CREATE (n: Building {id: 2137, name:"building_rye_farmlevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:2137}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 41.2,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2137}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 61.8,level: 4}]->(g);
CREATE (n: Building {id: 2138, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:2138}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:2138}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
CREATE (n: Building {id: 2139, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:2139}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 29.523123224810934, level: 5}]->(b);
CREATE (n: Building {id: 2140, name:"building_arms_industrylevel", level:7});
MATCH (g: Goods{code: 24}), (b: Building{id:2140}) CREATE (g)-[r:Demand{max_demand: 140.0, current_input: 10.269993868151905, level: 7}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2140}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 22.181898981682323, level: 7}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:2140}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 20.487671936818703,level: 7}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:2140}) CREATE (b)-[r:Supply{max_supply: 104.99999999999999, current_output: 20.487671936818703,level: 7}]->(g);
CREATE (n: Building {id: 2141, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:2141}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 32.02556396083088, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2141}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 42.700751947774506,level: 2}]->(g);
CREATE (n: Building {id: 2142, name:"building_food_industrylevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2142}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 127.0044794017661, level: 3}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:2142}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 5.466465942015832, level: 3}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:2142}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 24.13993978260475,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2142}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 96.559759130419,level: 3}]->(g);
CREATE (n: Building {id: 2143, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2143}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.2,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2143}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.3,level: 2}]->(g);
CREATE (n: Building {id: 2144, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:2144}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:2144}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
CREATE (n: Building {id: 2145, name:"building_logging_camplevel", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:2145}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 2.1620714196308652, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2145}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 17.296571357046922,level: 5}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2145}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 4.3241428392617305,level: 5}]->(g);
CREATE (n: Building {id: 2146, name:"building_iron_minelevel", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:2146}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 4.32414283926173, level: 10}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2146}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 17.29657135704692,level: 10}]->(g);
CREATE (n: Building {id: 2147, name:"building_rye_farmlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2147}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 52.0,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2147}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 78.0,level: 5}]->(g);
CREATE (n: Building {id: 2148, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:2148}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:2148}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.898546385623278, level: 20}]->(b);
CREATE (n: Building {id: 2149, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2149}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2149}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 5.0,level: 1}]->(g);
CREATE (n: Building {id: 2860, name:"building_iron_minelevel", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:2860}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 2.594485703557038, level: 6}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2860}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 10.377942814228152,level: 6}]->(g);
CREATE (n: Building {id: 2861, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:2861}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.864828567852346, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2861}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 6.918628542818768,level: 2}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2861}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 1.729657135704692,level: 2}]->(g);
CREATE (n: Building {id: 2862, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:2862}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.343577474294618, level: 5}]->(b);
CREATE (n: Building {id: 2863, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2863}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.18038686375474, level: 1}]->(b);
CREATE (n: Building {id: 2892, name:"building_subsistence_farmslevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 3.885, current_output: 3.885,level: 2}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 0.6475, current_output: 0.6475,level: 2}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 0.6475, current_output: 0.6475,level: 2}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 0.6475, current_output: 0.6475,level: 2}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 0.6475, current_output: 0.6475,level: 2}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 0.6475, current_output: 0.6475,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2892}) CREATE (b)-[r:Supply{max_supply: 0.9065, current_output: 0.9065,level: 2}]->(g);
CREATE (n: Building {id: 2901, name:"building_subsistence_farmslevel", level:39});
MATCH (g: Goods{code: 7}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 71.03889, current_output: 71.03889,level: 39}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 11.83981, current_output: 11.83981,level: 39}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 11.83981, current_output: 11.83981,level: 39}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 11.83981, current_output: 11.83981,level: 39}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 11.83981, current_output: 11.83981,level: 39}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 11.83981, current_output: 11.83981,level: 39}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2901}) CREATE (b)-[r:Supply{max_supply: 16.57574, current_output: 16.57574,level: 39}]->(g);
CREATE (n: Building {id: 2905, name:"building_subsistence_farmslevel", level:37});
MATCH (g: Goods{code: 7}), (b: Building{id:2905}) CREATE (b)-[r:Supply{max_supply: 58.01859, current_output: 58.01859,level: 37}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2905}) CREATE (b)-[r:Supply{max_supply: 9.66976, current_output: 9.66976,level: 37}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2905}) CREATE (b)-[r:Supply{max_supply: 9.66976, current_output: 9.66976,level: 37}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2905}) CREATE (b)-[r:Supply{max_supply: 9.66976, current_output: 9.66976,level: 37}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2905}) CREATE (b)-[r:Supply{max_supply: 9.66976, current_output: 9.66976,level: 37}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2905}) CREATE (b)-[r:Supply{max_supply: 9.66976, current_output: 9.66976,level: 37}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2905}) CREATE (b)-[r:Supply{max_supply: 13.53767, current_output: 13.53767,level: 37}]->(g);
CREATE (n: Building {id: 2906, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2906}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 13.343984983679535, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2906}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 66.71992491839768,level: 5}]->(g);
CREATE (n: Building {id: 2907, name:"building_subsistence_pastureslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:2907}) CREATE (b)-[r:Supply{max_supply: 0.6259, current_output: 0.6259,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2907}) CREATE (b)-[r:Supply{max_supply: 0.93885, current_output: 0.93885,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2907}) CREATE (b)-[r:Supply{max_supply: 0.31295, current_output: 0.31295,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2907}) CREATE (b)-[r:Supply{max_supply: 0.6259, current_output: 0.6259,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2907}) CREATE (b)-[r:Supply{max_supply: 0.6259, current_output: 0.6259,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2907}) CREATE (b)-[r:Supply{max_supply: 0.6259, current_output: 0.6259,level: 20}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2907}) CREATE (b)-[r:Supply{max_supply: 2.07798, current_output: 2.07798,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2907}) CREATE (b)-[r:Supply{max_supply: 0.87626, current_output: 0.87626,level: 20}]->(g);
CREATE (n: Building {id: 3633, name:"building_subsistence_farmslevel", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 41.20416, current_output: 41.20416,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 6.86736, current_output: 6.86736,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 6.86736, current_output: 6.86736,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 6.86736, current_output: 6.86736,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 6.86736, current_output: 6.86736,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 6.86736, current_output: 6.86736,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3633}) CREATE (b)-[r:Supply{max_supply: 9.6143, current_output: 9.6143,level: 18}]->(g);
CREATE (n: Building {id: 3634, name:"building_subsistence_farmslevel", level:22});
MATCH (g: Goods{code: 7}), (b: Building{id:3634}) CREATE (b)-[r:Supply{max_supply: 50.17716, current_output: 50.17716,level: 22}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3634}) CREATE (b)-[r:Supply{max_supply: 8.36286, current_output: 8.36286,level: 22}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3634}) CREATE (b)-[r:Supply{max_supply: 8.36286, current_output: 8.36286,level: 22}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3634}) CREATE (b)-[r:Supply{max_supply: 8.36286, current_output: 8.36286,level: 22}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3634}) CREATE (b)-[r:Supply{max_supply: 8.36286, current_output: 8.36286,level: 22}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3634}) CREATE (b)-[r:Supply{max_supply: 8.36286, current_output: 8.36286,level: 22}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3634}) CREATE (b)-[r:Supply{max_supply: 11.708, current_output: 11.708,level: 22}]->(g);
CREATE (n: Building {id: 3635, name:"building_subsistence_farmslevel", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 105.594, current_output: 105.594,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 17.599, current_output: 17.599,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 17.599, current_output: 17.599,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 17.599, current_output: 17.599,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 17.599, current_output: 17.599,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 17.599, current_output: 17.599,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3635}) CREATE (b)-[r:Supply{max_supply: 24.6386, current_output: 24.6386,level: 50}]->(g);
CREATE (n: Building {id: 3651, name:"building_subsistence_farmslevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:3651}) CREATE (b)-[r:Supply{max_supply: 5.9844, current_output: 5.9844,level: 2}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3651}) CREATE (b)-[r:Supply{max_supply: 0.9974, current_output: 0.9974,level: 2}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3651}) CREATE (b)-[r:Supply{max_supply: 0.9974, current_output: 0.9974,level: 2}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3651}) CREATE (b)-[r:Supply{max_supply: 0.9974, current_output: 0.9974,level: 2}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3651}) CREATE (b)-[r:Supply{max_supply: 0.9974, current_output: 0.9974,level: 2}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3651}) CREATE (b)-[r:Supply{max_supply: 0.9974, current_output: 0.9974,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3651}) CREATE (b)-[r:Supply{max_supply: 1.39636, current_output: 1.39636,level: 2}]->(g);
CREATE (n: Building {id: 3764, name:"building_subsistence_farmslevel", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 86.78511, current_output: 86.78511,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 14.46418, current_output: 14.46418,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 14.46418, current_output: 14.46418,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 14.46418, current_output: 14.46418,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 14.46418, current_output: 14.46418,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 14.46418, current_output: 14.46418,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3764}) CREATE (b)-[r:Supply{max_supply: 20.24985, current_output: 20.24985,level: 29}]->(g);
CREATE (n: Building {id: 3765, name:"building_subsistence_farmslevel", level:47});
MATCH (g: Goods{code: 7}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 141.0, current_output: 141.0,level: 47}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 23.5, current_output: 23.5,level: 47}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 23.5, current_output: 23.5,level: 47}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 23.5, current_output: 23.5,level: 47}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 23.5, current_output: 23.5,level: 47}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 23.5, current_output: 23.5,level: 47}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3765}) CREATE (b)-[r:Supply{max_supply: 32.9, current_output: 32.9,level: 47}]->(g);
CREATE (n: Building {id: 3766, name:"building_subsistence_farmslevel", level:56});
MATCH (g: Goods{code: 7}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 166.98024, current_output: 166.98024,level: 56}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 27.83004, current_output: 27.83004,level: 56}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 27.83004, current_output: 27.83004,level: 56}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 27.83004, current_output: 27.83004,level: 56}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 27.83004, current_output: 27.83004,level: 56}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 27.83004, current_output: 27.83004,level: 56}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3766}) CREATE (b)-[r:Supply{max_supply: 38.96205, current_output: 38.96205,level: 56}]->(g);
CREATE (n: Building {id: 3767, name:"building_subsistence_pastureslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 0.2904, current_output: 0.23232,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 0.4356, current_output: 0.34848,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 0.1452, current_output: 0.11616,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 0.2904, current_output: 0.23232,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 0.2904, current_output: 0.23232,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 0.2904, current_output: 0.23232,level: 20}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 0.9641249999999999, current_output: 0.7713,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3767}) CREATE (b)-[r:Supply{max_supply: 0.40654999999999997, current_output: 0.32524,level: 20}]->(g);
CREATE (n: Building {id: 3768, name:"building_subsistence_pastureslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 1.0824, current_output: 0.86592,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 1.6236, current_output: 1.29888,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 0.5412, current_output: 0.43296,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 1.0824, current_output: 0.86592,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 1.0824, current_output: 0.86592,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 1.0824, current_output: 0.86592,level: 20}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 3.5935624999999995, current_output: 2.87485,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3768}) CREATE (b)-[r:Supply{max_supply: 1.51535, current_output: 1.21228,level: 20}]->(g);
CREATE (n: Building {id: 3769, name:"building_subsistence_farmslevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:3769}) CREATE (b)-[r:Supply{max_supply: 18.4404, current_output: 18.4404,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3769}) CREATE (b)-[r:Supply{max_supply: 3.0734, current_output: 3.0734,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3769}) CREATE (b)-[r:Supply{max_supply: 3.0734, current_output: 3.0734,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3769}) CREATE (b)-[r:Supply{max_supply: 3.0734, current_output: 3.0734,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3769}) CREATE (b)-[r:Supply{max_supply: 3.0734, current_output: 3.0734,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3769}) CREATE (b)-[r:Supply{max_supply: 3.0734, current_output: 3.0734,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3769}) CREATE (b)-[r:Supply{max_supply: 4.30276, current_output: 4.30276,level: 10}]->(g);
CREATE (n: Building {id: 3770, name:"building_subsistence_farmslevel", level:15});
MATCH (g: Goods{code: 7}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 14.41845, current_output: 14.41845,level: 15}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 2.40307, current_output: 2.40307,level: 15}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 2.40307, current_output: 2.40307,level: 15}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 2.40307, current_output: 2.40307,level: 15}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 2.40307, current_output: 2.40307,level: 15}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 2.40307, current_output: 2.40307,level: 15}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3770}) CREATE (b)-[r:Supply{max_supply: 3.3643, current_output: 3.3643,level: 15}]->(g);
CREATE (n: Building {id: 3771, name:"building_subsistence_farmslevel", level:115});
MATCH (g: Goods{code: 7}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 345.0, current_output: 345.0,level: 115}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 57.5, current_output: 57.5,level: 115}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 57.5, current_output: 57.5,level: 115}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 57.5, current_output: 57.5,level: 115}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 57.5, current_output: 57.5,level: 115}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 57.5, current_output: 57.5,level: 115}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3771}) CREATE (b)-[r:Supply{max_supply: 80.5, current_output: 80.5,level: 115}]->(g);
CREATE (n: Building {id: 3772, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3772}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.668796996735907, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3772}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.343984983679535,level: 1}]->(g);
CREATE (n: Building {id: 3778, name:"building_subsistence_pastureslevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:3778}) CREATE (b)-[r:Supply{max_supply: 1.4945, current_output: 1.4945,level: 7}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3778}) CREATE (b)-[r:Supply{max_supply: 2.24175, current_output: 2.24175,level: 7}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3778}) CREATE (b)-[r:Supply{max_supply: 0.74725, current_output: 0.74725,level: 7}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3778}) CREATE (b)-[r:Supply{max_supply: 1.4945, current_output: 1.4945,level: 7}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3778}) CREATE (b)-[r:Supply{max_supply: 1.4945, current_output: 1.4945,level: 7}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3778}) CREATE (b)-[r:Supply{max_supply: 1.4945, current_output: 1.4945,level: 7}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3778}) CREATE (b)-[r:Supply{max_supply: 4.96174, current_output: 4.96174,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3778}) CREATE (b)-[r:Supply{max_supply: 2.0923, current_output: 2.0923,level: 7}]->(g);
CREATE (n: Building {id: 3784, name:"building_subsistence_pastureslevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 0.17329, current_output: 0.17329,level: 3}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 0.25994, current_output: 0.25994,level: 3}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 0.08664, current_output: 0.08664,level: 3}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 0.17329, current_output: 0.17329,level: 3}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 0.17329, current_output: 0.17329,level: 3}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 0.17329, current_output: 0.17329,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 0.57533, current_output: 0.57533,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3784}) CREATE (b)-[r:Supply{max_supply: 0.24261, current_output: 0.24261,level: 3}]->(g);
CREATE (n: Building {id: 3786, name:"building_subsistence_pastureslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:3786}) CREATE (b)-[r:Supply{max_supply: 0.792, current_output: 0.792,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3786}) CREATE (b)-[r:Supply{max_supply: 1.188, current_output: 1.188,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3786}) CREATE (b)-[r:Supply{max_supply: 0.396, current_output: 0.396,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3786}) CREATE (b)-[r:Supply{max_supply: 0.792, current_output: 0.792,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3786}) CREATE (b)-[r:Supply{max_supply: 0.792, current_output: 0.792,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3786}) CREATE (b)-[r:Supply{max_supply: 0.792, current_output: 0.792,level: 6}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3786}) CREATE (b)-[r:Supply{max_supply: 2.62944, current_output: 2.62944,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3786}) CREATE (b)-[r:Supply{max_supply: 1.1088, current_output: 1.1088,level: 6}]->(g);
CREATE (n: Building {id: 3789, name:"building_subsistence_pastureslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 0.269, current_output: 0.269,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 0.4035, current_output: 0.4035,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 0.1345, current_output: 0.1345,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 0.269, current_output: 0.269,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 0.269, current_output: 0.269,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 0.269, current_output: 0.269,level: 20}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 0.89308, current_output: 0.89308,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3789}) CREATE (b)-[r:Supply{max_supply: 0.3766, current_output: 0.3766,level: 20}]->(g);
CREATE (n: Building {id: 3790, name:"building_subsistence_pastureslevel", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:3790}) CREATE (b)-[r:Supply{max_supply: 1.106, current_output: 1.106,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3790}) CREATE (b)-[r:Supply{max_supply: 1.659, current_output: 1.659,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3790}) CREATE (b)-[r:Supply{max_supply: 0.553, current_output: 0.553,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3790}) CREATE (b)-[r:Supply{max_supply: 1.106, current_output: 1.106,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3790}) CREATE (b)-[r:Supply{max_supply: 1.106, current_output: 1.106,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3790}) CREATE (b)-[r:Supply{max_supply: 1.106, current_output: 1.106,level: 50}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3790}) CREATE (b)-[r:Supply{max_supply: 3.67192, current_output: 3.67192,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3790}) CREATE (b)-[r:Supply{max_supply: 1.5484, current_output: 1.5484,level: 50}]->(g);
CREATE (n: Building {id: 3791, name:"building_subsistence_pastureslevel", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 2.00295, current_output: 1.60236,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 3.004425, current_output: 2.40354,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 1.001475, current_output: 0.80118,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 2.00295, current_output: 1.60236,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 2.00295, current_output: 1.60236,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 2.00295, current_output: 1.60236,level: 30}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 6.6497874999999995, current_output: 5.31983,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3791}) CREATE (b)-[r:Supply{max_supply: 2.804125, current_output: 2.2433,level: 30}]->(g);
CREATE (n: Building {id: 3792, name:"building_subsistence_farmslevel", level:60});
MATCH (g: Goods{code: 7}), (b: Building{id:3792}) CREATE (b)-[r:Supply{max_supply: 48.0744, current_output: 48.0744,level: 60}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3792}) CREATE (b)-[r:Supply{max_supply: 8.0124, current_output: 8.0124,level: 60}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3792}) CREATE (b)-[r:Supply{max_supply: 8.0124, current_output: 8.0124,level: 60}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3792}) CREATE (b)-[r:Supply{max_supply: 8.0124, current_output: 8.0124,level: 60}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3792}) CREATE (b)-[r:Supply{max_supply: 8.0124, current_output: 8.0124,level: 60}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3792}) CREATE (b)-[r:Supply{max_supply: 8.0124, current_output: 8.0124,level: 60}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3792}) CREATE (b)-[r:Supply{max_supply: 11.21736, current_output: 11.21736,level: 60}]->(g);
CREATE (n: Building {id: 3793, name:"building_subsistence_farmslevel", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3793}) CREATE (b)-[r:Supply{max_supply: 91.3872, current_output: 91.3872,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3793}) CREATE (b)-[r:Supply{max_supply: 15.2312, current_output: 15.2312,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3793}) CREATE (b)-[r:Supply{max_supply: 15.2312, current_output: 15.2312,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3793}) CREATE (b)-[r:Supply{max_supply: 15.2312, current_output: 15.2312,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3793}) CREATE (b)-[r:Supply{max_supply: 15.2312, current_output: 15.2312,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3793}) CREATE (b)-[r:Supply{max_supply: 15.2312, current_output: 15.2312,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3793}) CREATE (b)-[r:Supply{max_supply: 21.32368, current_output: 21.32368,level: 40}]->(g);
CREATE (n: Building {id: 3794, name:"building_subsistence_farmslevel", level:49});
MATCH (g: Goods{code: 7}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 147.0, current_output: 147.0,level: 49}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 24.5, current_output: 24.5,level: 49}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 24.5, current_output: 24.5,level: 49}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 24.5, current_output: 24.5,level: 49}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 24.5, current_output: 24.5,level: 49}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 24.5, current_output: 24.5,level: 49}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3794}) CREATE (b)-[r:Supply{max_supply: 34.3, current_output: 34.3,level: 49}]->(g);
CREATE (n: Building {id: 3797, name:"building_subsistence_farmslevel", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3797}) CREATE (b)-[r:Supply{max_supply: 47.6052, current_output: 47.6052,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3797}) CREATE (b)-[r:Supply{max_supply: 7.9342, current_output: 7.9342,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3797}) CREATE (b)-[r:Supply{max_supply: 7.9342, current_output: 7.9342,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3797}) CREATE (b)-[r:Supply{max_supply: 7.9342, current_output: 7.9342,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3797}) CREATE (b)-[r:Supply{max_supply: 7.9342, current_output: 7.9342,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3797}) CREATE (b)-[r:Supply{max_supply: 7.9342, current_output: 7.9342,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3797}) CREATE (b)-[r:Supply{max_supply: 11.10788, current_output: 11.10788,level: 40}]->(g);
CREATE (n: Building {id: 3798, name:"building_subsistence_pastureslevel", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3798}) CREATE (b)-[r:Supply{max_supply: 0.9918, current_output: 0.79344,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3798}) CREATE (b)-[r:Supply{max_supply: 1.4877, current_output: 1.19016,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3798}) CREATE (b)-[r:Supply{max_supply: 0.4959, current_output: 0.39672,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3798}) CREATE (b)-[r:Supply{max_supply: 0.9918, current_output: 0.79344,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3798}) CREATE (b)-[r:Supply{max_supply: 0.9918, current_output: 0.79344,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3798}) CREATE (b)-[r:Supply{max_supply: 0.9918, current_output: 0.79344,level: 40}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3798}) CREATE (b)-[r:Supply{max_supply: 3.292775, current_output: 2.63422,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3798}) CREATE (b)-[r:Supply{max_supply: 1.3885125, current_output: 1.11081,level: 40}]->(g);
CREATE (n: Building {id: 3799, name:"building_subsistence_pastureslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3799}) CREATE (b)-[r:Supply{max_supply: 0.3398, current_output: 0.27184,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3799}) CREATE (b)-[r:Supply{max_supply: 0.5096999999999999, current_output: 0.40776,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3799}) CREATE (b)-[r:Supply{max_supply: 0.1699, current_output: 0.13592,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3799}) CREATE (b)-[r:Supply{max_supply: 0.3398, current_output: 0.27184,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3799}) CREATE (b)-[r:Supply{max_supply: 0.3398, current_output: 0.27184,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3799}) CREATE (b)-[r:Supply{max_supply: 0.3398, current_output: 0.27184,level: 20}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3799}) CREATE (b)-[r:Supply{max_supply: 1.1281249999999998, current_output: 0.9025,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3799}) CREATE (b)-[r:Supply{max_supply: 0.4757125, current_output: 0.38057,level: 20}]->(g);
CREATE (n: Building {id: 3800, name:"building_subsistence_farmslevel", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3800}) CREATE (b)-[r:Supply{max_supply: 96.8736, current_output: 96.8736,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3800}) CREATE (b)-[r:Supply{max_supply: 16.1456, current_output: 16.1456,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3800}) CREATE (b)-[r:Supply{max_supply: 16.1456, current_output: 16.1456,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3800}) CREATE (b)-[r:Supply{max_supply: 16.1456, current_output: 16.1456,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3800}) CREATE (b)-[r:Supply{max_supply: 16.1456, current_output: 16.1456,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3800}) CREATE (b)-[r:Supply{max_supply: 16.1456, current_output: 16.1456,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3800}) CREATE (b)-[r:Supply{max_supply: 22.60384, current_output: 22.60384,level: 40}]->(g);
CREATE (n: Building {id: 3801, name:"building_subsistence_farmslevel", level:86});
MATCH (g: Goods{code: 7}), (b: Building{id:3801}) CREATE (b)-[r:Supply{max_supply: 258.0, current_output: 258.0,level: 86}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3801}) CREATE (b)-[r:Supply{max_supply: 43.0, current_output: 43.0,level: 86}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3801}) CREATE (b)-[r:Supply{max_supply: 43.0, current_output: 43.0,level: 86}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3801}) CREATE (b)-[r:Supply{max_supply: 43.0, current_output: 43.0,level: 86}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3801}) CREATE (b)-[r:Supply{max_supply: 43.0, current_output: 43.0,level: 86}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3801}) CREATE (b)-[r:Supply{max_supply: 43.0, current_output: 43.0,level: 86}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3801}) CREATE (b)-[r:Supply{max_supply: 60.2, current_output: 60.2,level: 86}]->(g);
CREATE (n: Building {id: 3802, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3802}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 8.00639099020772, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3802}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 40.0319549510386,level: 3}]->(g);
CREATE (n: Building {id: 3804, name:"building_subsistence_farmslevel", level:138});
MATCH (g: Goods{code: 7}), (b: Building{id:3804}) CREATE (b)-[r:Supply{max_supply: 413.9793, current_output: 413.9793,level: 138}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3804}) CREATE (b)-[r:Supply{max_supply: 68.99655, current_output: 68.99655,level: 138}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3804}) CREATE (b)-[r:Supply{max_supply: 68.99655, current_output: 68.99655,level: 138}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3804}) CREATE (b)-[r:Supply{max_supply: 68.99655, current_output: 68.99655,level: 138}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3804}) CREATE (b)-[r:Supply{max_supply: 68.99655, current_output: 68.99655,level: 138}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3804}) CREATE (b)-[r:Supply{max_supply: 68.99655, current_output: 68.99655,level: 138}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3804}) CREATE (b)-[r:Supply{max_supply: 96.59517, current_output: 96.59517,level: 138}]->(g);
CREATE (n: Building {id: 3805, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3805}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 10.675187986943628, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3805}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 53.37593993471814,level: 4}]->(g);
CREATE (n: Building {id: 3806, name:"building_subsistence_farmslevel", level:100});
MATCH (g: Goods{code: 7}), (b: Building{id:3806}) CREATE (b)-[r:Supply{max_supply: 299.247, current_output: 299.247,level: 100}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3806}) CREATE (b)-[r:Supply{max_supply: 49.8745, current_output: 49.8745,level: 100}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3806}) CREATE (b)-[r:Supply{max_supply: 49.8745, current_output: 49.8745,level: 100}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3806}) CREATE (b)-[r:Supply{max_supply: 49.8745, current_output: 49.8745,level: 100}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3806}) CREATE (b)-[r:Supply{max_supply: 49.8745, current_output: 49.8745,level: 100}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3806}) CREATE (b)-[r:Supply{max_supply: 49.8745, current_output: 49.8745,level: 100}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3806}) CREATE (b)-[r:Supply{max_supply: 69.8243, current_output: 69.8243,level: 100}]->(g);
CREATE (n: Building {id: 3807, name:"building_subsistence_farmslevel", level:77});
MATCH (g: Goods{code: 7}), (b: Building{id:3807}) CREATE (b)-[r:Supply{max_supply: 172.07652, current_output: 172.07652,level: 77}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3807}) CREATE (b)-[r:Supply{max_supply: 28.67942, current_output: 28.67942,level: 77}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3807}) CREATE (b)-[r:Supply{max_supply: 28.67942, current_output: 28.67942,level: 77}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3807}) CREATE (b)-[r:Supply{max_supply: 28.67942, current_output: 28.67942,level: 77}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3807}) CREATE (b)-[r:Supply{max_supply: 28.67942, current_output: 28.67942,level: 77}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3807}) CREATE (b)-[r:Supply{max_supply: 28.67942, current_output: 28.67942,level: 77}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3807}) CREATE (b)-[r:Supply{max_supply: 40.15118, current_output: 40.15118,level: 77}]->(g);
CREATE (n: Building {id: 3808, name:"building_subsistence_farmslevel", level:110});
MATCH (g: Goods{code: 7}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 329.0364, current_output: 329.0364,level: 110}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 54.8394, current_output: 54.8394,level: 110}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 54.8394, current_output: 54.8394,level: 110}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 54.8394, current_output: 54.8394,level: 110}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 54.8394, current_output: 54.8394,level: 110}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 54.8394, current_output: 54.8394,level: 110}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3808}) CREATE (b)-[r:Supply{max_supply: 76.77516, current_output: 76.77516,level: 110}]->(g);
CREATE (n: Building {id: 3809, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3809}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.337593993471814, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3809}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 26.68796996735907,level: 2}]->(g);
CREATE (n: Building {id: 3810, name:"building_subsistence_farmslevel", level:120});
MATCH (g: Goods{code: 7}), (b: Building{id:3810}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 360.0,level: 120}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3810}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 120}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3810}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 120}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3810}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 120}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3810}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 120}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3810}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.0,level: 120}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3810}) CREATE (b)-[r:Supply{max_supply: 84.0, current_output: 84.0,level: 120}]->(g);
CREATE (n: Building {id: 3811, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3811}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 16.01278198041544, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3811}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 80.0639099020772,level: 6}]->(g);
CREATE (n: Building {id: 3812, name:"building_subsistence_farmslevel", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:3812}) CREATE (b)-[r:Supply{max_supply: 48.0, current_output: 48.0,level: 16}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3812}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 16}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3812}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 16}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3812}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 16}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3812}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 16}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3812}) CREATE (b)-[r:Supply{max_supply: 8.0, current_output: 8.0,level: 16}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3812}) CREATE (b)-[r:Supply{max_supply: 11.2, current_output: 11.2,level: 16}]->(g);
CREATE (n: Building {id: 3814, name:"building_subsistence_farmslevel", level:52});
MATCH (g: Goods{code: 7}), (b: Building{id:3814}) CREATE (b)-[r:Supply{max_supply: 140.7588, current_output: 140.7588,level: 52}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3814}) CREATE (b)-[r:Supply{max_supply: 23.4598, current_output: 23.4598,level: 52}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3814}) CREATE (b)-[r:Supply{max_supply: 23.4598, current_output: 23.4598,level: 52}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3814}) CREATE (b)-[r:Supply{max_supply: 23.4598, current_output: 23.4598,level: 52}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3814}) CREATE (b)-[r:Supply{max_supply: 23.4598, current_output: 23.4598,level: 52}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3814}) CREATE (b)-[r:Supply{max_supply: 23.4598, current_output: 23.4598,level: 52}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3814}) CREATE (b)-[r:Supply{max_supply: 32.84372, current_output: 32.84372,level: 52}]->(g);
CREATE (n: Building {id: 3816, name:"building_subsistence_farmslevel", level:79});
MATCH (g: Goods{code: 7}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 223.74696, current_output: 223.74696,level: 79}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 37.29116, current_output: 37.29116,level: 79}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 37.29116, current_output: 37.29116,level: 79}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 37.29116, current_output: 37.29116,level: 79}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 37.29116, current_output: 37.29116,level: 79}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 37.29116, current_output: 37.29116,level: 79}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3816}) CREATE (b)-[r:Supply{max_supply: 52.20762, current_output: 52.20762,level: 79}]->(g);
CREATE (n: Building {id: 3817, name:"building_subsistence_farmslevel", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3817}) CREATE (b)-[r:Supply{max_supply: 22.314, current_output: 22.314,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3817}) CREATE (b)-[r:Supply{max_supply: 3.719, current_output: 3.719,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3817}) CREATE (b)-[r:Supply{max_supply: 3.719, current_output: 3.719,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3817}) CREATE (b)-[r:Supply{max_supply: 3.719, current_output: 3.719,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3817}) CREATE (b)-[r:Supply{max_supply: 3.719, current_output: 3.719,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3817}) CREATE (b)-[r:Supply{max_supply: 3.719, current_output: 3.719,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3817}) CREATE (b)-[r:Supply{max_supply: 5.2066, current_output: 5.2066,level: 40}]->(g);
CREATE (n: Building {id: 3818, name:"building_subsistence_farmslevel", level:70});
MATCH (g: Goods{code: 7}), (b: Building{id:3818}) CREATE (b)-[r:Supply{max_supply: 209.31960000000004, current_output: 240.71754,level: 70}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3818}) CREATE (b)-[r:Supply{max_supply: 34.8866, current_output: 40.11959,level: 70}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3818}) CREATE (b)-[r:Supply{max_supply: 34.8866, current_output: 40.11959,level: 70}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3818}) CREATE (b)-[r:Supply{max_supply: 34.8866, current_output: 40.11959,level: 70}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3818}) CREATE (b)-[r:Supply{max_supply: 34.8866, current_output: 40.11959,level: 70}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3818}) CREATE (b)-[r:Supply{max_supply: 34.8866, current_output: 40.11959,level: 70}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3818}) CREATE (b)-[r:Supply{max_supply: 48.8412347826087, current_output: 56.16742,level: 70}]->(g);
CREATE (n: Building {id: 3819, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3819}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.337593993471814, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3819}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 26.68796996735907,level: 2}]->(g);
CREATE (n: Building {id: 3820, name:"building_subsistence_farmslevel", level:167});
MATCH (g: Goods{code: 7}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 501.0, current_output: 501.0,level: 167}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 83.5, current_output: 83.5,level: 167}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 83.5, current_output: 83.5,level: 167}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 83.5, current_output: 83.5,level: 167}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 83.5, current_output: 83.5,level: 167}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 83.5, current_output: 83.5,level: 167}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3820}) CREATE (b)-[r:Supply{max_supply: 116.9, current_output: 116.9,level: 167}]->(g);
CREATE (n: Building {id: 3821, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3821}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.668796996735907, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3821}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.343984983679535,level: 1}]->(g);
CREATE (n: Building {id: 3822, name:"building_subsistence_farmslevel", level:60});
MATCH (g: Goods{code: 7}), (b: Building{id:3822}) CREATE (b)-[r:Supply{max_supply: 109.989, current_output: 109.989,level: 60}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3822}) CREATE (b)-[r:Supply{max_supply: 18.3315, current_output: 18.3315,level: 60}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3822}) CREATE (b)-[r:Supply{max_supply: 18.3315, current_output: 18.3315,level: 60}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3822}) CREATE (b)-[r:Supply{max_supply: 18.3315, current_output: 18.3315,level: 60}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3822}) CREATE (b)-[r:Supply{max_supply: 18.3315, current_output: 18.3315,level: 60}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3822}) CREATE (b)-[r:Supply{max_supply: 18.3315, current_output: 18.3315,level: 60}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3822}) CREATE (b)-[r:Supply{max_supply: 25.6641, current_output: 25.6641,level: 60}]->(g);
CREATE (n: Building {id: 3823, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3823}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.668796996735907, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3823}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.343984983679535,level: 1}]->(g);
CREATE (n: Building {id: 3824, name:"building_subsistence_farmslevel", level:37});
MATCH (g: Goods{code: 7}), (b: Building{id:3824}) CREATE (b)-[r:Supply{max_supply: 110.7713391304348, current_output: 127.38704,level: 37}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3824}) CREATE (b)-[r:Supply{max_supply: 18.461886956521738, current_output: 21.23117,level: 37}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3824}) CREATE (b)-[r:Supply{max_supply: 18.461886956521738, current_output: 21.23117,level: 37}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3824}) CREATE (b)-[r:Supply{max_supply: 18.461886956521738, current_output: 21.23117,level: 37}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3824}) CREATE (b)-[r:Supply{max_supply: 18.461886956521738, current_output: 21.23117,level: 37}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3824}) CREATE (b)-[r:Supply{max_supply: 18.461886956521738, current_output: 21.23117,level: 37}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3824}) CREATE (b)-[r:Supply{max_supply: 25.846643478260873, current_output: 29.72364,level: 37}]->(g);
CREATE (n: Building {id: 3825, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3825}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.337593993471814, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3825}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 26.68796996735907,level: 2}]->(g);
CREATE (n: Building {id: 3826, name:"building_subsistence_farmslevel", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:3826}) CREATE (b)-[r:Supply{max_supply: 149.904, current_output: 149.904,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3826}) CREATE (b)-[r:Supply{max_supply: 24.984, current_output: 24.984,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3826}) CREATE (b)-[r:Supply{max_supply: 24.984, current_output: 24.984,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3826}) CREATE (b)-[r:Supply{max_supply: 24.984, current_output: 24.984,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3826}) CREATE (b)-[r:Supply{max_supply: 24.984, current_output: 24.984,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3826}) CREATE (b)-[r:Supply{max_supply: 24.984, current_output: 24.984,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3826}) CREATE (b)-[r:Supply{max_supply: 34.9776, current_output: 34.9776,level: 50}]->(g);
CREATE (n: Building {id: 3827, name:"building_subsistence_farmslevel", level:146});
MATCH (g: Goods{code: 7}), (b: Building{id:3827}) CREATE (b)-[r:Supply{max_supply: 436.686, current_output: 436.686,level: 146}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3827}) CREATE (b)-[r:Supply{max_supply: 72.781, current_output: 72.781,level: 146}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3827}) CREATE (b)-[r:Supply{max_supply: 72.781, current_output: 72.781,level: 146}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3827}) CREATE (b)-[r:Supply{max_supply: 72.781, current_output: 72.781,level: 146}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3827}) CREATE (b)-[r:Supply{max_supply: 72.781, current_output: 72.781,level: 146}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3827}) CREATE (b)-[r:Supply{max_supply: 72.781, current_output: 72.781,level: 146}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3827}) CREATE (b)-[r:Supply{max_supply: 101.8934, current_output: 101.8934,level: 146}]->(g);
CREATE (n: Building {id: 3828, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3828}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 5.337593993471814, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3828}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 26.68796996735907,level: 2}]->(g);
CREATE (n: Building {id: 3829, name:"building_subsistence_farmslevel", level:77});
MATCH (g: Goods{code: 7}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 184.4535, current_output: 184.4535,level: 77}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 30.74225, current_output: 30.74225,level: 77}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 30.74225, current_output: 30.74225,level: 77}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 30.74225, current_output: 30.74225,level: 77}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 30.74225, current_output: 30.74225,level: 77}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 30.74225, current_output: 30.74225,level: 77}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3829}) CREATE (b)-[r:Supply{max_supply: 43.03915, current_output: 43.03915,level: 77}]->(g);
CREATE (n: Building {id: 3830, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3830}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.668796996735907, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3830}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.343984983679535,level: 1}]->(g);
CREATE (n: Building {id: 3831, name:"building_subsistence_farmslevel", level:113});
MATCH (g: Goods{code: 7}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 338.04063, current_output: 338.04063,level: 113}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 56.3401, current_output: 56.3401,level: 113}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 56.3401, current_output: 56.3401,level: 113}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 56.3401, current_output: 56.3401,level: 113}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 56.3401, current_output: 56.3401,level: 113}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 56.3401, current_output: 56.3401,level: 113}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3831}) CREATE (b)-[r:Supply{max_supply: 78.87614, current_output: 78.87614,level: 113}]->(g);
CREATE (n: Building {id: 3832, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3832}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.668796996735907, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3832}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.343984983679535,level: 1}]->(g);
CREATE (n: Building {id: 3927, name:"building_subsistence_farmslevel", level:95});
MATCH (g: Goods{code: 7}), (b: Building{id:3927}) CREATE (b)-[r:Supply{max_supply: 242.57775, current_output: 242.57775,level: 95}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3927}) CREATE (b)-[r:Supply{max_supply: 40.42962, current_output: 40.42962,level: 95}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3927}) CREATE (b)-[r:Supply{max_supply: 40.42962, current_output: 40.42962,level: 95}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3927}) CREATE (b)-[r:Supply{max_supply: 40.42962, current_output: 40.42962,level: 95}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3927}) CREATE (b)-[r:Supply{max_supply: 40.42962, current_output: 40.42962,level: 95}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3927}) CREATE (b)-[r:Supply{max_supply: 40.42962, current_output: 40.42962,level: 95}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3927}) CREATE (b)-[r:Supply{max_supply: 56.60147, current_output: 56.60147,level: 95}]->(g);
CREATE (n: Building {id: 3928, name:"building_subsistence_farmslevel", level:57});
MATCH (g: Goods{code: 7}), (b: Building{id:3928}) CREATE (b)-[r:Supply{max_supply: 156.0375, current_output: 156.0375,level: 57}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3928}) CREATE (b)-[r:Supply{max_supply: 26.00625, current_output: 26.00625,level: 57}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3928}) CREATE (b)-[r:Supply{max_supply: 26.00625, current_output: 26.00625,level: 57}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3928}) CREATE (b)-[r:Supply{max_supply: 26.00625, current_output: 26.00625,level: 57}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3928}) CREATE (b)-[r:Supply{max_supply: 26.00625, current_output: 26.00625,level: 57}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3928}) CREATE (b)-[r:Supply{max_supply: 26.00625, current_output: 26.00625,level: 57}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3928}) CREATE (b)-[r:Supply{max_supply: 36.40875, current_output: 36.40875,level: 57}]->(g);
CREATE (n: Building {id: 3929, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3929}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.668796996735907, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3929}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.343984983679535,level: 1}]->(g);
CREATE (n: Building {id: 3930, name:"building_subsistence_farmslevel", level:77});
MATCH (g: Goods{code: 7}), (b: Building{id:3930}) CREATE (b)-[r:Supply{max_supply: 213.23379, current_output: 213.23379,level: 77}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3930}) CREATE (b)-[r:Supply{max_supply: 35.53896, current_output: 35.53896,level: 77}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3930}) CREATE (b)-[r:Supply{max_supply: 35.53896, current_output: 35.53896,level: 77}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3930}) CREATE (b)-[r:Supply{max_supply: 35.53896, current_output: 35.53896,level: 77}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3930}) CREATE (b)-[r:Supply{max_supply: 35.53896, current_output: 35.53896,level: 77}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3930}) CREATE (b)-[r:Supply{max_supply: 35.53896, current_output: 35.53896,level: 77}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3930}) CREATE (b)-[r:Supply{max_supply: 49.75455, current_output: 49.75455,level: 77}]->(g);
CREATE (n: Building {id: 3931, name:"building_subsistence_farmslevel", level:98});
MATCH (g: Goods{code: 7}), (b: Building{id:3931}) CREATE (b)-[r:Supply{max_supply: 293.33262, current_output: 293.33262,level: 98}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3931}) CREATE (b)-[r:Supply{max_supply: 48.88877, current_output: 48.88877,level: 98}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3931}) CREATE (b)-[r:Supply{max_supply: 48.88877, current_output: 48.88877,level: 98}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3931}) CREATE (b)-[r:Supply{max_supply: 48.88877, current_output: 48.88877,level: 98}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3931}) CREATE (b)-[r:Supply{max_supply: 48.88877, current_output: 48.88877,level: 98}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3931}) CREATE (b)-[r:Supply{max_supply: 48.88877, current_output: 48.88877,level: 98}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3931}) CREATE (b)-[r:Supply{max_supply: 68.44427, current_output: 68.44427,level: 98}]->(g);
CREATE (n: Building {id: 3932, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3932}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.668796996735907, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3932}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.343984983679535,level: 1}]->(g);
CREATE (n: Building {id: 3933, name:"building_subsistence_farmslevel", level:87});
MATCH (g: Goods{code: 7}), (b: Building{id:3933}) CREATE (b)-[r:Supply{max_supply: 214.7769, current_output: 214.7769,level: 87}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3933}) CREATE (b)-[r:Supply{max_supply: 35.79615, current_output: 35.79615,level: 87}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3933}) CREATE (b)-[r:Supply{max_supply: 35.79615, current_output: 35.79615,level: 87}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3933}) CREATE (b)-[r:Supply{max_supply: 35.79615, current_output: 35.79615,level: 87}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3933}) CREATE (b)-[r:Supply{max_supply: 35.79615, current_output: 35.79615,level: 87}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3933}) CREATE (b)-[r:Supply{max_supply: 35.79615, current_output: 35.79615,level: 87}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3933}) CREATE (b)-[r:Supply{max_supply: 50.11461, current_output: 50.11461,level: 87}]->(g);
CREATE (n: Building {id: 3934, name:"building_subsistence_farmslevel", level:150});
MATCH (g: Goods{code: 7}), (b: Building{id:3934}) CREATE (b)-[r:Supply{max_supply: 450.00000000000006, current_output: 517.5,level: 150}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3934}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 86.25,level: 150}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3934}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 86.25,level: 150}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3934}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 86.25,level: 150}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3934}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 86.25,level: 150}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3934}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 86.25,level: 150}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3934}) CREATE (b)-[r:Supply{max_supply: 105.00000000000001, current_output: 120.75,level: 150}]->(g);
CREATE (n: Building {id: 3935, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3935}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 13.343984983679535, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3935}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 66.71992491839768,level: 5}]->(g);
CREATE (n: Building {id: 3970, name:"building_trade_centerlevel", level:31});
CREATE (n: Building {id: 4024, name:"building_trade_centerlevel", level:21});
CREATE (n: Building {id: 4025, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4025}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.668796996735907, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:4025}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.343984983679535,level: 1}]->(g);
CREATE (n: Building {id: 4026, name:"building_trade_centerlevel", level:22});
CREATE (n: Building {id: 4027, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4027}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 2.668796996735907, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:4027}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 13.343984983679535,level: 1}]->(g);
CREATE (n: Building {id: 4029, name:"building_trade_centerlevel", level:8});
CREATE (n: Building {id: 4055, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4058, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4472, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4473, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4474, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4549, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4550, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4551, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4552, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4553, name:"building_conscription_centerlevel", level:14});
CREATE (n: Building {id: 4560, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4561, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4562, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4563, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4564, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4565, name:"building_conscription_centerlevel", level:19});
CREATE (n: Building {id: 4566, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4567, name:"building_conscription_centerlevel", level:16});
CREATE (n: Building {id: 4568, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4569, name:"building_conscription_centerlevel", level:19});
CREATE (n: Building {id: 4570, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4571, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4572, name:"building_conscription_centerlevel", level:4});
CREATE (n: Building {id: 4574, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4575, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4576, name:"building_conscription_centerlevel", level:17});
CREATE (n: Building {id: 4577, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4578, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4579, name:"building_conscription_centerlevel", level:11});
CREATE (n: Building {id: 4580, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4581, name:"building_conscription_centerlevel", level:25});
CREATE (n: Building {id: 4582, name:"building_conscription_centerlevel", level:11});
CREATE (n: Building {id: 4583, name:"building_conscription_centerlevel", level:20});
CREATE (n: Building {id: 4631, name:"building_conscription_centerlevel", level:13});
CREATE (n: Building {id: 4632, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4633, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4634, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4635, name:"building_conscription_centerlevel", level:11});
CREATE (n: Building {id: 4636, name:"building_conscription_centerlevel", level:25});
