CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:27.83059426768495, pop_demand:205.44612831172296});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:21.23481971545455, pop_demand:27.24265082245402});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:15.274117464257175, pop_demand:30.49356193141932});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:15.575486221344384, pop_demand:22.86857056858068});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:17.69932972780734, pop_demand:43.65836235790691});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:24.626250020765394, pop_demand:39.05663391068909});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:16.7175245133264, pop_demand:12.827328333333332});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:30.0, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:2.7843958333333325});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:26.721638465958005, pop_demand:31.54950891117882});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:32.6895408831371, pop_demand:10.035755548480351});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:30.262866120582714, pop_demand:11.550955});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:32.44827923011249, pop_demand:57.01413958863971});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:12.99384722222222});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:3.248461805555555});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:25.0, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 1849, name:"building_gold_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1849}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.5, level: 1}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:1849}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
CREATE (n: Building {id: 1860, name:"building_government_administrationlevel", level:3});
CREATE (n: Building {id: 1861, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:1861}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 42.55123505260402, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:1861}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 1862, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1862}) CREATE (b)-[r:Supply{max_supply: 29.880000000000003, current_output: 34.362,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1862}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 5.727,level: 1}]->(g);
CREATE (n: Building {id: 1863, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:1863}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 1864, name:"building_naval_baselevel", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:1864}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 0.0, level: 3}]->(b);
CREATE (n: Building {id: 1865, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1865}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
CREATE (n: Building {id: 1866, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1866}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1866}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 1867, name:"building_government_administrationlevel", level:1});
CREATE (n: Building {id: 1868, name:"building_wheat_farmlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:1868}) CREATE (b)-[r:Supply{max_supply: 44.82674509803922, current_output: 45.72328,level: 3}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:1868}) CREATE (b)-[r:Supply{max_supply: 23.90759803921569, current_output: 24.38575,level: 3}]->(g);
CREATE (n: Building {id: 1869, name:"building_textile_millslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1869}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 116.79835316023035, level: 2}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:1869}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 90.0,level: 2}]->(g);
CREATE (n: Building {id: 1870, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1870}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1870}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 4.98,level: 1}]->(g);
CREATE (n: Building {id: 1871, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:1871}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:1871}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 1872, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1872}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 14.599794145028794, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1872}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 42.55123505260402, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:1872}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 45.0,level: 1}]->(g);
CREATE (n: Building {id: 1873, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:1873}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 15.0, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:1873}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 2}]->(g);
CREATE (n: Building {id: 1874, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:1874}) CREATE (b)-[r:Supply{max_supply: 29.880000000000003, current_output: 34.362,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1874}) CREATE (b)-[r:Supply{max_supply: 4.98, current_output: 5.727,level: 1}]->(g);
CREATE (n: Building {id: 1875, name:"building_maize_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:1875}) CREATE (b)-[r:Supply{max_supply: 29.880000000000003, current_output: 34.362,level: 1}]->(g);
CREATE (n: Building {id: 1876, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:1876}) CREATE (b)-[r:Supply{max_supply: 59.760000000000005, current_output: 69.3216,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:1876}) CREATE (b)-[r:Supply{max_supply: 9.96, current_output: 11.5536,level: 2}]->(g);
CREATE (n: Building {id: 1877, name:"building_tobacco_plantationlevel", level:3});
MATCH (g: Goods{code: 43}), (b: Building{id:1877}) CREATE (b)-[r:Supply{max_supply: 74.7, current_output: 76.194,level: 3}]->(g);
CREATE (n: Building {id: 1878, name:"building_gold_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:1878}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.5, level: 1}]->(b);
MATCH (g: Goods{code: 50}), (b: Building{id:1878}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 10.0,level: 1}]->(g);
CREATE (n: Building {id: 3176, name:"building_subsistence_pastureslevel", level:92});
MATCH (g: Goods{code: 7}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 0.9108, current_output: 1.04742,level: 92}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 1.3662, current_output: 1.57113,level: 92}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 0.4554, current_output: 0.52371,level: 92}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 0.9108, current_output: 1.04742,level: 92}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 0.9108, current_output: 1.04742,level: 92}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 0.9108, current_output: 1.04742,level: 92}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 2.4227217391304348, current_output: 2.78613,level: 92}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3176}) CREATE (b)-[r:Supply{max_supply: 1.275113043478261, current_output: 1.46638,level: 92}]->(g);
CREATE (n: Building {id: 3177, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3177}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 7.091872508767337, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3177}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3181, name:"building_subsistence_pastureslevel", level:61});
MATCH (g: Goods{code: 7}), (b: Building{id:3181}) CREATE (b)-[r:Supply{max_supply: 0.32634545454545455, current_output: 0.35898,level: 61}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3181}) CREATE (b)-[r:Supply{max_supply: 0.4895181818181818, current_output: 0.53847,level: 61}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3181}) CREATE (b)-[r:Supply{max_supply: 0.16317272727272727, current_output: 0.17949,level: 61}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3181}) CREATE (b)-[r:Supply{max_supply: 0.32634545454545455, current_output: 0.35898,level: 61}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3181}) CREATE (b)-[r:Supply{max_supply: 0.32634545454545455, current_output: 0.35898,level: 61}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3181}) CREATE (b)-[r:Supply{max_supply: 0.32634545454545455, current_output: 0.35898,level: 61}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3181}) CREATE (b)-[r:Supply{max_supply: 0.868090909090909, current_output: 0.9549,level: 61}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3181}) CREATE (b)-[r:Supply{max_supply: 0.4568818181818181, current_output: 0.50257,level: 61}]->(g);
CREATE (n: Building {id: 3183, name:"building_subsistence_farmslevel", level:146});
MATCH (g: Goods{code: 7}), (b: Building{id:3183}) CREATE (b)-[r:Supply{max_supply: 1.6863, current_output: 1.6863,level: 146}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3183}) CREATE (b)-[r:Supply{max_supply: 0.33726, current_output: 0.33726,level: 146}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3183}) CREATE (b)-[r:Supply{max_supply: 0.33726, current_output: 0.33726,level: 146}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3183}) CREATE (b)-[r:Supply{max_supply: 0.33726, current_output: 0.33726,level: 146}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3183}) CREATE (b)-[r:Supply{max_supply: 0.33726, current_output: 0.33726,level: 146}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3183}) CREATE (b)-[r:Supply{max_supply: 0.33726, current_output: 0.33726,level: 146}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3183}) CREATE (b)-[r:Supply{max_supply: 0.47216, current_output: 0.47216,level: 146}]->(g);
CREATE (n: Building {id: 3185, name:"building_subsistence_farmslevel", level:54});
MATCH (g: Goods{code: 7}), (b: Building{id:3185}) CREATE (b)-[r:Supply{max_supply: 2.3894956521739132, current_output: 2.74792,level: 54}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3185}) CREATE (b)-[r:Supply{max_supply: 0.47789565217391305, current_output: 0.54958,level: 54}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3185}) CREATE (b)-[r:Supply{max_supply: 0.47789565217391305, current_output: 0.54958,level: 54}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3185}) CREATE (b)-[r:Supply{max_supply: 0.47789565217391305, current_output: 0.54958,level: 54}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3185}) CREATE (b)-[r:Supply{max_supply: 0.47789565217391305, current_output: 0.54958,level: 54}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3185}) CREATE (b)-[r:Supply{max_supply: 0.47789565217391305, current_output: 0.54958,level: 54}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3185}) CREATE (b)-[r:Supply{max_supply: 0.6690521739130436, current_output: 0.76941,level: 54}]->(g);
CREATE (n: Building {id: 3186, name:"building_subsistence_farmslevel", level:113});
MATCH (g: Goods{code: 7}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 4.50304347826087, current_output: 5.1785,level: 113}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 0.900608695652174, current_output: 1.0357,level: 113}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 0.900608695652174, current_output: 1.0357,level: 113}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 0.900608695652174, current_output: 1.0357,level: 113}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 0.900608695652174, current_output: 1.0357,level: 113}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 0.900608695652174, current_output: 1.0357,level: 113}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 1.2608521739130436, current_output: 1.44998,level: 113}]->(g);
CREATE (n: Building {id: 3187, name:"building_subsistence_pastureslevel", level:60});
MATCH (g: Goods{code: 7}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 1.4690999999999999, current_output: 1.61601,level: 60}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 2.2036454545454545, current_output: 2.42401,level: 60}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 0.7345454545454545, current_output: 0.808,level: 60}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 1.4690999999999999, current_output: 1.61601,level: 60}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 1.4690999999999999, current_output: 1.61601,level: 60}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 1.4690999999999999, current_output: 1.61601,level: 60}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 3.9078, current_output: 4.29858,level: 60}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 2.0567363636363636, current_output: 2.26241,level: 60}]->(g);
CREATE (n: Building {id: 3189, name:"building_subsistence_farmslevel", level:19});
MATCH (g: Goods{code: 7}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 4.6682999999999995, current_output: 5.13513,level: 19}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 0.9336545454545454, current_output: 1.02702,level: 19}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 0.9336545454545454, current_output: 1.02702,level: 19}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 0.9336545454545454, current_output: 1.02702,level: 19}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 0.9336545454545454, current_output: 1.02702,level: 19}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 0.9336545454545454, current_output: 1.02702,level: 19}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 1.3071181818181816, current_output: 1.43783,level: 19}]->(g);
CREATE (n: Building {id: 3191, name:"building_subsistence_pastureslevel", level:35});
MATCH (g: Goods{code: 7}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 0.3087, current_output: 0.33957,level: 35}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 0.4630454545454545, current_output: 0.50935,level: 35}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 0.1543454545454545, current_output: 0.16978,level: 35}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 0.3087, current_output: 0.33957,level: 35}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 0.3087, current_output: 0.33957,level: 35}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 0.3087, current_output: 0.33957,level: 35}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 0.8211363636363636, current_output: 0.90325,level: 35}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 0.4321727272727272, current_output: 0.47539,level: 35}]->(g);
CREATE (n: Building {id: 3998, name:"building_trade_centerlevel", level:14});
CREATE (n: Building {id: 4188, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4193, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4195, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4196, name:"building_conscription_centerlevel", level:2});
