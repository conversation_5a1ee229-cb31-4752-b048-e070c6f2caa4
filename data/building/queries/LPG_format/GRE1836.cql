CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:52.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:16.317001074135863, pop_demand:81.23786242990091});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:19.146152537129527, pop_demand:113.16922029703623});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:11.95032526447292, pop_demand:23.323101054125285});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:16.985447521815654, pop_demand:15.027851445874727});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:41.17358496538133, pop_demand:27.361985151768184});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:40.94890814671266, pop_demand:26.829775395803946});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:14.878506287215739, pop_demand:12.715271666666665});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:69.0, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:1.3102422619047627});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:36.27539749635661, pop_demand:5.548018817705174});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:34.426256372651764, pop_demand:24.00600057995059});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:43.1735619635597, pop_demand:6.518499999999999});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:44.593827157140915, pop_demand:29.402303315037052});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:6.1144638888888885});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:105.0, pop_demand:1.5286159722222221});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 911, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:911}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.0, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:911}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 40.0,level: 1}]->(g);
CREATE (n: Building {id: 939, name:"building_government_administrationlevel", level:2});
CREATE (n: Building {id: 940, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:940}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 43.163447548774634, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:940}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 50.06070894790478, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:940}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 15.0,level: 1}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:940}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 941, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:941}) CREATE (b)-[r:Supply{max_supply: 14.94225, current_output: 14.94225,level: 1}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:941}) CREATE (b)-[r:Supply{max_supply: 7.9692, current_output: 7.9692,level: 1}]->(g);
CREATE (n: Building {id: 942, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:942}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.0, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:942}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 40.0,level: 1}]->(g);
CREATE (n: Building {id: 943, name:"building_tobacco_plantationlevel", level:1});
MATCH (g: Goods{code: 43}), (b: Building{id:943}) CREATE (b)-[r:Supply{max_supply: 24.9, current_output: 24.9,level: 1}]->(g);
CREATE (n: Building {id: 944, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:944}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.6,level: 2}]->(g);
CREATE (n: Building {id: 945, name:"building_cotton_plantationlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:945}) CREATE (b)-[r:Supply{max_supply: 79.67999999999999, current_output: 80.4768,level: 2}]->(g);
CREATE (n: Building {id: 946, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:946}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.0, level: 1}]->(b);
CREATE (n: Building {id: 947, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:947}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 948, name:"building_naval_baselevel", level:2});
MATCH (g: Goods{code: 5}), (b: Building{id:948}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 6.0, level: 2}]->(b);
CREATE (n: Building {id: 949, name:"building_wheat_farmlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:949}) CREATE (b)-[r:Supply{max_supply: 29.88, current_output: 29.88,level: 1}]->(g);
CREATE (n: Building {id: 950, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:950}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.0, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:950}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 40.0,level: 1}]->(g);
CREATE (n: Building {id: 951, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:951}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.0, level: 1}]->(b);
CREATE (n: Building {id: 952, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 2}), (b: Building{id:952}) CREATE (g)-[r:Demand{max_demand: 2.5, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 953, name:"building_naval_baselevel", level:3});
MATCH (g: Goods{code: 5}), (b: Building{id:953}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 9.0, level: 3}]->(b);
CREATE (n: Building {id: 3018, name:"building_subsistence_farmslevel", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 16.18335, current_output: 16.18335,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 3.23667, current_output: 3.23667,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 3.23667, current_output: 3.23667,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 3.23667, current_output: 3.23667,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 3.23667, current_output: 3.23667,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 3.23667, current_output: 3.23667,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3018}) CREATE (b)-[r:Supply{max_supply: 4.53133, current_output: 4.53133,level: 9}]->(g);
CREATE (n: Building {id: 3464, name:"building_subsistence_farmslevel", level:21});
MATCH (g: Goods{code: 7}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 45.16627, current_output: 45.16627,level: 21}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 9.03325, current_output: 9.03325,level: 21}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 9.03325, current_output: 9.03325,level: 21}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 9.03325, current_output: 9.03325,level: 21}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 9.03325, current_output: 9.03325,level: 21}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 9.03325, current_output: 9.03325,level: 21}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3464}) CREATE (b)-[r:Supply{max_supply: 12.64655, current_output: 12.64655,level: 21}]->(g);
CREATE (n: Building {id: 3465, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3465}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 6.257588618488097, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3465}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 3653, name:"building_subsistence_orchardslevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3653}) CREATE (b)-[r:Supply{max_supply: 1.504, current_output: 1.504,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3653}) CREATE (b)-[r:Supply{max_supply: 0.752, current_output: 0.752,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3653}) CREATE (b)-[r:Supply{max_supply: 2.256, current_output: 2.256,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3653}) CREATE (b)-[r:Supply{max_supply: 1.504, current_output: 1.504,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3653}) CREATE (b)-[r:Supply{max_supply: 1.504, current_output: 1.504,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3653}) CREATE (b)-[r:Supply{max_supply: 1.504, current_output: 1.504,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3653}) CREATE (b)-[r:Supply{max_supply: 4.00064, current_output: 4.00064,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3653}) CREATE (b)-[r:Supply{max_supply: 2.1056, current_output: 2.1056,level: 5}]->(g);
CREATE (n: Building {id: 4019, name:"building_trade_centerlevel", level:6});
CREATE (n: Building {id: 4342, name:"building_conscription_centerlevel", level:1});
