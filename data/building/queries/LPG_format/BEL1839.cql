CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:87.5, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:78.2376835067548, pop_demand:6.197551110308917});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:122.5, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:23.38030571022788, pop_demand:597.7888388181649});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:29.64576062441823, pop_demand:44.66206160641301});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:28.520680048426357, pop_demand:10.846681199915965});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:28.30603155086915, pop_demand:42.149232034009266});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:35.88372170681781, pop_demand:138.5351418673065});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:42.00907630523338, pop_demand:205.93431633095017});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:38.49797764440108, pop_demand:172.91927258856376});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:45.569906851224594, pop_demand:7.849234898182867});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:25.74301419229536, pop_demand:116.8884829432484});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:44.50333034694465, pop_demand:63.07848333333334});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:91.1561302005765, pop_demand:10.57134141342872});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:65.97786532432967, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:43.90579809025388, pop_demand:84.54133784404985});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:58.83043582138478, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:9.999999999999996, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:63.0403753278368, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:105.0, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:64.8703406315194, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:53.39436085018461, pop_demand:0.22839099902463256});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:21.149009122306964, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:52.9437561255893, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:12.5, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:15.594327040816328});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:25.899601753899187, pop_demand:70.82545210079908});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:24.83588621102335, pop_demand:25.892355287194437});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:48.33870940698317, pop_demand:297.01121500000016});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:87.5, pop_demand:64.48752999999999});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:35.157022374450705, pop_demand:1.1414511810569816});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:105.0, pop_demand:18.193381547619055});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:88.7129021012852, pop_demand:90.96690773809526});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:315.5059358144645, pop_demand:0.22404580139144126});
CREATE (n: Building {id: 324, name:"building_government_administration", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:324}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.505261156309854, level: 1}]->(b);
CREATE (n: Building {id: 325, name:"building_steel_mills", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:325}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 24.41551221703072, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:325}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 31.530802347681405, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:325}) CREATE (b)-[r:Supply{max_supply: 130.00000000000003, current_output: 52.068915142607764,level: 2}]->(g);
CREATE (n: Building {id: 326, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:326}) CREATE (g)-[r:Demand{max_demand: 24.7808, current_input: 10.911842763012318, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:326}) CREATE (g)-[r:Demand{max_demand: 49.5616, current_input: 23.20571102747233, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:326}) CREATE (g)-[r:Demand{max_demand: 61.952, current_input: 24.417453338044478, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:326}) CREATE (g)-[r:Demand{max_demand: 12.3904, current_input: 7.437378573133005, level: 2}]->(b);
CREATE (n: Building {id: 327, name:"building_chemical_plants", level:2});
MATCH (g: Goods{code: 22}), (b: Building{id:327}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 36.45317742251646, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:327}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.882700586920351, level: 2}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:327}) CREATE (b)-[r:Supply{max_supply: 180.00000000000003, current_output: 90.1519187749163,level: 2}]->(g);
CREATE (n: Building {id: 328, name:"building_explosives_factory", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:328}) CREATE (g)-[r:Demand{max_demand: 0.196, current_input: 0.11908037958022046, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:328}) CREATE (g)-[r:Demand{max_demand: 0.196, current_input: 0.3430513360857275, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:328}) CREATE (b)-[r:Supply{max_supply: 0.49, current_output: 0.39385047447527555,level: 1}]->(g);
CREATE (n: Building {id: 329, name:"building_tooling_workshops", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:329}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 42.13976127632097, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:329}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 38.4078563880361, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:329}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 133.00206114450017,level: 3}]->(g);
CREATE (n: Building {id: 330, name:"building_coal_mine", level:5});
MATCH (g: Goods{code: 16}), (b: Building{id:330}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 9.34760883929165, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:330}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 30.012665342252895, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:330}) CREATE (b)-[r:Supply{max_supply: 197.20000000000002, current_output: 96.05194531708898,level: 5}]->(g);
CREATE (n: Building {id: 331, name:"building_iron_mine", level:7});
MATCH (g: Goods{code: 23}), (b: Building{id:331}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 28.484764253202503, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:331}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 42.01773147915405, level: 7}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:331}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 141.0049914647131,level: 7}]->(g);
CREATE (n: Building {id: 332, name:"building_sulfur_mine", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:332}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 8.138504072343572, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:332}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.005066136901158, level: 2}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:332}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 40.287140418489464,level: 2}]->(g);
CREATE (n: Building {id: 333, name:"building_wheat_farm", level:7});
MATCH (g: Goods{code: 32}), (b: Building{id:333}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 61.259167158165624, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:333}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 4.201773147915405, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:333}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 224.03546295830807,level: 7}]->(g);
CREATE (n: Building {id: 334, name:"building_logging_camp", level:3});
MATCH (g: Goods{code: 16}), (b: Building{id:334}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 5.6085653035749905, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:334}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.003799602675867, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:334}) CREATE (b)-[r:Supply{max_supply: 103.53, current_output: 50.42727129147171,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:334}) CREATE (b)-[r:Supply{max_supply: 29.580000000000002, current_output: 14.407791797563348,level: 3}]->(g);
CREATE (n: Building {id: 335, name:"building_railway", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:335}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 3.7457565578951977, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:335}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.8138504072343572, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:335}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:335}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 8.751447733540783,level: 1}]->(g);
CREATE (n: Building {id: 336, name:"building_barracks", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:336}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:336}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.3168513758605025, level: 5}]->(b);
CREATE (n: Building {id: 337, name:"building_government_administration", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:337}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 19.515783468929563, level: 3}]->(b);
CREATE (n: Building {id: 338, name:"building_construction_sector", level:7});
MATCH (g: Goods{code: 9}), (b: Building{id:338}) CREATE (g)-[r:Demand{max_demand: 86.70764, current_input: 38.18037085291344, level: 7}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:338}) CREATE (g)-[r:Demand{max_demand: 173.41529, current_input: 81.19643246959969, level: 7}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:338}) CREATE (g)-[r:Demand{max_demand: 216.76911, current_input: 85.4362995311601, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:338}) CREATE (g)-[r:Demand{max_demand: 43.35382, current_input: 26.023273819365404, level: 7}]->(b);
CREATE (n: Building {id: 339, name:"building_paper_mills", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:339}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 14.046587092106991, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:339}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.075529570419411, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:339}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 37.65203843725943,level: 1}]->(g);
CREATE (n: Building {id: 340, name:"building_food_industry", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:340}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 62.839364252282344, level: 2}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:340}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 23.654489085448272, level: 2}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:340}) CREATE (b)-[r:Supply{max_supply: 130.0, current_output: 102.30837647345068,level: 2}]->(g);
CREATE (n: Building {id: 341, name:"building_arms_industry", level:1});
MATCH (g: Goods{code: 26}), (b: Building{id:341}) CREATE (g)-[r:Demand{max_demand: 6.2843, current_input: 1.5531486676320723, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:341}) CREATE (g)-[r:Demand{max_demand: 6.2843, current_input: 4.022774864988921, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:341}) CREATE (g)-[r:Demand{max_demand: 3.14215, current_input: 1.8860859281031985, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:341}) CREATE (b)-[r:Supply{max_supply: 31.421500000000005, current_output: 15.580158981378986,level: 1}]->(g);
CREATE (n: Building {id: 342, name:"building_university", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:342}) CREATE (g)-[r:Demand{max_demand: 34.0, current_input: 11.058943965726751, level: 4}]->(b);
CREATE (n: Building {id: 343, name:"building_textile_mills", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:343}) CREATE (g)-[r:Demand{max_demand: 124.416, current_input: 54.78466511181804, level: 4}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:343}) CREATE (g)-[r:Demand{max_demand: 15.552, current_input: 0.0, level: 4}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:343}) CREATE (b)-[r:Supply{max_supply: 186.624, current_output: 41.08849883386353,level: 4}]->(g);
CREATE (n: Building {id: 344, name:"building_furniture_manufacturies", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:344}) CREATE (g)-[r:Demand{max_demand: 30.000000000000004, current_input: 13.21003691932341, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:344}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 21.069880638160484, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:344}) CREATE (g)-[r:Demand{max_demand: 45.0, current_input: 11.121634874758247, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:344}) CREATE (g)-[r:Demand{max_demand: 15.000000000000002, current_input: 9.003799602675869, level: 3}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:344}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 52.67864646662097,level: 3}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:344}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 32.924154041638104,level: 3}]->(g);
CREATE (n: Building {id: 345, name:"building_logging_camp", level:4});
MATCH (g: Goods{code: 16}), (b: Building{id:345}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 7.47808707143332, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:345}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.005066136901158, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:345}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 116.89891925000686,level: 4}]->(g);
CREATE (n: Building {id: 346, name:"building_fishing_wharf", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:346}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.5942091256514364, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:346}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 15.942091256514363,level: 1}]->(g);
CREATE (n: Building {id: 347, name:"building_wheat_farm", level:6});
MATCH (g: Goods{code: 32}), (b: Building{id:347}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 52.507857564141965, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:347}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 3.601519841070347, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:347}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 120.01899801337933,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:347}) CREATE (b)-[r:Supply{max_supply: 42.0, current_output: 33.60531944374621,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:347}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 24.003799602675866,level: 6}]->(g);
CREATE (n: Building {id: 348, name:"building_livestock_ranch", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:348}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 39.27460265767647, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:348}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 30.01266534225289, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:348}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 69.28726799992936,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:348}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 17.32181699998234,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:348}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 86.6090849999117,level: 5}]->(g);
CREATE (n: Building {id: 349, name:"building_railway", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:349}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 3.7457565578951977, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:349}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.8138504072343572, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:349}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:349}) CREATE (b)-[r:Supply{max_supply: 29.999999999999996, current_output: 8.751447733540783,level: 1}]->(g);
CREATE (n: Building {id: 350, name:"building_barracks", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:350}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:350}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.3168513758605025, level: 5}]->(b);
CREATE (n: Building {id: 351, name:"building_port", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:351}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.376836502605745, level: 4}]->(b);
CREATE (n: Building {id: 3157, name:"building_subsistence_farms", level:41});
MATCH (g: Goods{code: 7}), (b: Building{id:3157}) CREATE (b)-[r:Supply{max_supply: 78.7774, current_output: 78.7774,level: 41}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3157}) CREATE (b)-[r:Supply{max_supply: 19.69435, current_output: 19.69435,level: 41}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3157}) CREATE (b)-[r:Supply{max_supply: 19.69435, current_output: 19.69435,level: 41}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3157}) CREATE (b)-[r:Supply{max_supply: 19.69435, current_output: 19.69435,level: 41}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3157}) CREATE (b)-[r:Supply{max_supply: 19.69435, current_output: 19.69435,level: 41}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3157}) CREATE (b)-[r:Supply{max_supply: 19.69435, current_output: 19.69435,level: 41}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3157}) CREATE (b)-[r:Supply{max_supply: 19.69435, current_output: 19.69435,level: 41}]->(g);
CREATE (n: Building {id: 3158, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:3158}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 1.6277008144687144, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3158}) CREATE (b)-[r:Supply{max_supply: 68.0, current_output: 27.670913845968144,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3158}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 8.138504072343572,level: 4}]->(g);
CREATE (n: Building {id: 3159, name:"building_subsistence_farms", level:79});
MATCH (g: Goods{code: 7}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 140.94706, current_output: 140.94706,level: 79}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 35.23676, current_output: 35.23676,level: 79}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 35.23676, current_output: 35.23676,level: 79}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 35.23676, current_output: 35.23676,level: 79}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 35.23676, current_output: 35.23676,level: 79}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 35.23676, current_output: 35.23676,level: 79}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3159}) CREATE (b)-[r:Supply{max_supply: 35.23676, current_output: 35.23676,level: 79}]->(g);
CREATE (n: Building {id: 3160, name:"building_urban_center", level:9});
MATCH (g: Goods{code: 23}), (b: Building{id:3160}) CREATE (g)-[r:Demand{max_demand: 8.8991953125, current_input: 3.621306864568104, level: 9}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3160}) CREATE (b)-[r:Supply{max_supply: 151.2863984375, current_output: 61.5622484886893,level: 9}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3160}) CREATE (b)-[r:Supply{max_supply: 44.496, current_output: 18.10654386014998,level: 9}]->(g);
CREATE (n: Building {id: 3968, name:"building_naval_base", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:3968}) CREATE (g)-[r:Demand{max_demand: 4.496, current_input: 0.0, level: 5}]->(b);
CREATE (n: Building {id: 4191, name:"building_trade_center", level:72});
CREATE (n: Building {id: 4242, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4242}) CREATE (g)-[r:Demand{max_demand: 0.27, current_input: 0.12641928382896295, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:4242}) CREATE (b)-[r:Supply{max_supply: 0.27, current_output: 0.12641928382896295,level: 1}]->(g);
CREATE (n: Building {id: 4424, name:"building_conscription_center", level:17});
CREATE (n: Building {id: 4767, name:"building_conscription_center", level:33});
CREATE (n: Building {id: 5253, name:"building_lead_mine", level:1});
MATCH (g: Goods{code: 16}), (b: Building{id:5253}) CREATE (g)-[r:Demand{max_demand: 0.01385, current_input: 0.005178575296967574, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5253}) CREATE (g)-[r:Demand{max_demand: 0.0277, current_input: 0.011271828140195847, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5253}) CREATE (g)-[r:Demand{max_demand: 0.0277, current_input: 0.0166270165996081, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:5253}) CREATE (b)-[r:Supply{max_supply: 0.10924, current_output: 0.05028983068902117,level: 1}]->(g);
CREATE (n: Building {id: 5345, name:"building_tooling_workshops", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5345}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 14.04658709210699, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:5345}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 12.802618796012032, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5345}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 44.33402038150005,level: 1}]->(g);
CREATE (n: Building {id: 5500, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5500}) CREATE (g)-[r:Demand{max_demand: 10.8216, current_input: 4.76512451753834, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5500}) CREATE (g)-[r:Demand{max_demand: 21.6432, current_input: 10.133769791729668, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:5500}) CREATE (g)-[r:Demand{max_demand: 10.8216, current_input: 2.674530754681863, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:5500}) CREATE (g)-[r:Demand{max_demand: 2.7054, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:5500}) CREATE (b)-[r:Supply{max_supply: 37.8756, current_output: 10.943222647074407,level: 1}]->(g);
CREATE (n: Building {id: 16782988, name:"building_arts_academy", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:16782988}) CREATE (g)-[r:Demand{max_demand: 0.396, current_input: 0.12880417089493512, level: 1}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:16782988}) CREATE (b)-[r:Supply{max_supply: 0.1584, current_output: 0.051521668357974046,level: 1}]->(g);
