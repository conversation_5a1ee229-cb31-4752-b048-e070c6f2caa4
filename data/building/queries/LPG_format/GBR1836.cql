CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:79.90989477597961, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:93.87406993789546, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:109.51974826087803, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:115.34787096525737, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:31.063753965671804, pop_demand:7827.772270980946});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:27.957499385778775, pop_demand:547.552670355116});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:32.87020314797486, pop_demand:728.955922749186});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:33.15043379929322, pop_demand:605.2364946993979});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:42.78222178977777, pop_demand:363.87274429951134});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:46.96729280471316, pop_demand:2001.319361039455});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:44.93791350946756, pop_demand:1550.1971870992577});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:48.91029284495107, pop_demand:87.94327195396903});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:31.113438340573175, pop_demand:1313.5736925391868});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:14.345095773768785, pop_demand:96.99577382463802});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:95.26348820480415, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:82.801118836083, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:48.240084319998175, pop_demand:155.47798036378862});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:63.07806873614523, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:64.73725309641297, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:69.68922967293919, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:34.59258153899051, pop_demand:7.563512252867521});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:90.6852926787356, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:77.51340755164782, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:65.63194603001108, pop_demand:78.3894672198897});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:48.55487278182677, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:63.766793551938875, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:80.03230204606885, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:122.5, pop_demand:164.2211612063414});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:45.52442053494252, pop_demand:567.7490976417806});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:31.14001447091141, pop_demand:76.63295570920941});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:42.29535026594849, pop_demand:1665.3155937702998});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:67.7070250769795, pop_demand:16.242547169414433});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:85.94252067766067, pop_demand:481.5473240912776});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:51.83488818534583, pop_demand:151.82904873930806});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:41.75181879424979, pop_demand:156.8054156305289});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:59.89603550599094, pop_demand:149.94114447831723});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:87.5, pop_demand:582.8964656551655});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:100.0897789172992, pop_demand:801.7080672311797});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:95.57276298534514, pop_demand:432.4655863614223});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:306.6657351168793, pop_demand:1.8239575909272423});
CREATE (n: Building {id: 85, name:"building_government_administrationlevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:85}) CREATE (g)-[r:Demand{max_demand: 199.99999999999997, current_input: 35.40046219946348, level: 10}]->(b);
CREATE (n: Building {id: 86, name:"building_arts_academylevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:86}) CREATE (g)-[r:Demand{max_demand: 7.4424705882352935, current_input: 1.3173344936472116, level: 3}]->(b);
MATCH (g: Goods{code: 51}), (b: Building{id:86}) CREATE (b)-[r:Supply{max_supply: 2.9769803921568627, current_output: 0.526932409205465,level: 3}]->(g);
CREATE (n: Building {id: 87, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:87}) CREATE (g)-[r:Demand{max_demand: 46.1874, current_input: 6.73406108685749, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:87}) CREATE (g)-[r:Demand{max_demand: 92.3748, current_input: 11.635279271036644, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:87}) CREATE (g)-[r:Demand{max_demand: 115.4685, current_input: 29.457529853873016, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:87}) CREATE (g)-[r:Demand{max_demand: 23.0937, current_input: 5.422498841544394, level: 3}]->(b);
CREATE (n: Building {id: 88, name:"building_universitylevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:88}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 4.425057774932935, level: 5}]->(b);
CREATE (n: Building {id: 89, name:"building_paper_millslevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:89}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 37.78718634639527, level: 10}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:89}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 13.854471663636087, level: 10}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:89}) CREATE (b)-[r:Supply{max_supply: 700.0, current_output: 92.57570156018747,level: 10}]->(g);
CREATE (n: Building {id: 90, name:"building_textile_millslevel", level:25});
MATCH (g: Goods{code: 9}), (b: Building{id:90}) CREATE (g)-[r:Demand{max_demand: 750.0, current_input: 109.34899594138483, level: 25}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:90}) CREATE (g)-[r:Demand{max_demand: 250.0, current_input: 0.0, level: 25}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:90}) CREATE (g)-[r:Demand{max_demand: 125.0, current_input: 0.0, level: 25}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:90}) CREATE (b)-[r:Supply{max_supply: 1000.0, current_output: 48.59955375172659,level: 25}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:90}) CREATE (b)-[r:Supply{max_supply: 500.0, current_output: 24.299776875863294,level: 25}]->(g);
CREATE (n: Building {id: 91, name:"building_shipyardslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:91}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 14.579866125517977, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:91}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 25.191457564263512, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:91}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 1.1148449310683315, level: 5}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:91}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 18.5502270966073, level: 5}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:91}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 20.434654384978963,level: 5}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:91}) CREATE (b)-[r:Supply{max_supply: 225.0, current_output: 36.78237789296213,level: 5}]->(g);
CREATE (n: Building {id: 92, name:"building_furniture_manufacturieslevel", level:15});
MATCH (g: Goods{code: 9}), (b: Building{id:92}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 21.869799188276968, level: 15}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:92}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 37.78718634639527, level: 15}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:92}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 1.6722673966024972, level: 15}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:92}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 17.61031853344547, level: 15}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:92}) CREATE (b)-[r:Supply{max_supply: 675.0000000000001, current_output: 87.36333392808905,level: 15}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:92}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 38.82814841248402,level: 15}]->(g);
CREATE (n: Building {id: 93, name:"building_naval_baselevel", level:25});
MATCH (g: Goods{code: 5}), (b: Building{id:93}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 7.261658476543817, level: 25}]->(b);
CREATE (n: Building {id: 94, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:94}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.5359117894227916, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:94}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 4.338565580575129, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:94}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 4.338565580575129, level: 15}]->(b);
CREATE (n: Building {id: 95, name:"building_rye_farmlevel", level:15});
MATCH (g: Goods{code: 32}), (b: Building{id:95}) CREATE (g)-[r:Demand{max_demand: 74.91525, current_input: 14.522085826361522, level: 15}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:95}) CREATE (g)-[r:Demand{max_demand: 14.983048387096776, current_input: 3.5180833959840077, level: 15}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:95}) CREATE (b)-[r:Supply{max_supply: 749.1525, current_output: 160.56252349932097,level: 15}]->(g);
CREATE (n: Building {id: 96, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:96}) CREATE (g)-[r:Demand{max_demand: 49.949000000000005, current_input: 13.519704526306006, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:96}) CREATE (g)-[r:Demand{max_demand: 49.949000000000005, current_input: 11.72823733902757, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:96}) CREATE (b)-[r:Supply{max_supply: 199.79600000000002, current_output: 50.49588373066715,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:96}) CREATE (b)-[r:Supply{max_supply: 24.974500000000003, current_output: 6.311985466333394,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:96}) CREATE (b)-[r:Supply{max_supply: 124.8725, current_output: 31.55992733166697,level: 5}]->(g);
CREATE (n: Building {id: 97, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:97}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.306644457255094, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:97}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 23.066444572550942,level: 2}]->(g);
CREATE (n: Building {id: 98, name:"building_railwaylevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:98}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.8893593173197634, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:98}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.1127398455894, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:98}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 11.13013625796438, level: 3}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:98}) CREATE (b)-[r:Supply{max_supply: 195.0, current_output: 45.54678859941884,level: 3}]->(g);
CREATE (n: Building {id: 99, name:"building_portlevel", level:8});
MATCH (g: Goods{code: 18}), (b: Building{id:99}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 9.226577829020377, level: 8}]->(b);
CREATE (n: Building {id: 100, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:100}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 17.70023109973174, level: 5}]->(b);
CREATE (n: Building {id: 101, name:"building_textile_millslevel", level:25});
MATCH (g: Goods{code: 9}), (b: Building{id:101}) CREATE (g)-[r:Demand{max_demand: 750.0, current_input: 109.34899594138483, level: 25}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:101}) CREATE (g)-[r:Demand{max_demand: 250.0, current_input: 0.0, level: 25}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:101}) CREATE (g)-[r:Demand{max_demand: 125.0, current_input: 0.0, level: 25}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:101}) CREATE (b)-[r:Supply{max_supply: 1000.0, current_output: 48.59955375172659,level: 25}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:101}) CREATE (b)-[r:Supply{max_supply: 500.0, current_output: 24.299776875863294,level: 25}]->(g);
CREATE (n: Building {id: 102, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:102}) CREATE (g)-[r:Demand{max_demand: 46.1874, current_input: 6.73406108685749, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:102}) CREATE (g)-[r:Demand{max_demand: 92.3748, current_input: 11.635279271036644, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:102}) CREATE (g)-[r:Demand{max_demand: 115.4685, current_input: 29.457529853873016, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:102}) CREATE (g)-[r:Demand{max_demand: 23.0937, current_input: 5.422498841544394, level: 3}]->(b);
CREATE (n: Building {id: 103, name:"building_arms_industrylevel", level:5});
MATCH (g: Goods{code: 24}), (b: Building{id:103}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 25.511312482515155, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:103}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 0.5574224655341657, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:103}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 9.984809030093807,level: 5}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:103}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 9.984809030093807,level: 5}]->(g);
CREATE (n: Building {id: 104, name:"building_munition_plantslevel", level:2});
MATCH (g: Goods{code: 25}), (b: Building{id:104}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 7.75850413163621, level: 2}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:104}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 9.243331570169474, level: 2}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:104}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 21.252294627257108,level: 2}]->(g);
CREATE (n: Building {id: 105, name:"building_coal_minelevel", level:15});
MATCH (g: Goods{code: 33}), (b: Building{id:105}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 35.22063706689094, level: 15}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:105}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 140.88254826756375,level: 15}]->(g);
CREATE (n: Building {id: 106, name:"building_iron_minelevel", level:10});
MATCH (g: Goods{code: 23}), (b: Building{id:106}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 20.375799485297996, level: 10}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:106}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 23.48042471126062, level: 10}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:106}) CREATE (b)-[r:Supply{max_supply: 399.99999999999994, current_output: 87.71244839311724,level: 10}]->(g);
CREATE (n: Building {id: 107, name:"building_rye_farmlevel", level:10});
MATCH (g: Goods{code: 32}), (b: Building{id:107}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 9.692342898382853, level: 10}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:107}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.3480424711260626, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:107}) CREATE (b)-[r:Supply{max_supply: 499.99999999999994, current_output: 107.16277627006582,level: 10}]->(g);
CREATE (n: Building {id: 108, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:108}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 8.120105223111175, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:108}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.044127413378186, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:108}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 30.328465272978722,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:108}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 3.7910581591223402,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:108}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 18.955290795611702,level: 3}]->(g);
CREATE (n: Building {id: 109, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:109}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.357274526281861, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:109}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.892377053716753, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:109}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.892377053716753, level: 10}]->(b);
CREATE (n: Building {id: 110, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:110}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.904663390617526, level: 10}]->(b);
CREATE (n: Building {id: 111, name:"building_railwaylevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:111}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.5191457564263513, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:111}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 8.150319794119198, level: 4}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:111}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 14.840181677285841, level: 4}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:111}) CREATE (b)-[r:Supply{max_supply: 260.0, current_output: 60.72905146589177,level: 4}]->(g);
CREATE (n: Building {id: 112, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:112}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.4599666858826414, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:112}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 34.59966685882642,level: 3}]->(g);
CREATE (n: Building {id: 113, name:"building_portlevel", level:7});
MATCH (g: Goods{code: 18}), (b: Building{id:113}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 8.07325560039283, level: 7}]->(b);
CREATE (n: Building {id: 114, name:"building_paper_millslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:114}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 18.893593173197637, level: 5}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:114}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 6.927235831818043, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:114}) CREATE (b)-[r:Supply{max_supply: 350.0, current_output: 46.287850780093734,level: 5}]->(g);
CREATE (n: Building {id: 115, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:115}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.357274526281861, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:115}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.892377053716753, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:115}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.892377053716753, level: 10}]->(b);
CREATE (n: Building {id: 116, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:116}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.413403482074117, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:116}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.696084942252125, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:116}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 20.218976848652485,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:116}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 2.5273721060815606,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:116}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 12.636860530407803,level: 2}]->(g);
CREATE (n: Building {id: 117, name:"building_coal_minelevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:117}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 9.392169884504252, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:117}) CREATE (b)-[r:Supply{max_supply: 160.00000000000003, current_output: 37.56867953801701,level: 4}]->(g);
CREATE (n: Building {id: 118, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:118}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.522063706689093, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:118}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 42.26476448026912,level: 3}]->(g);
CREATE (n: Building {id: 119, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:119}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.6297864391065878, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:119}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.0375799485297996, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:119}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.7100454193214603, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:119}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 15.182262866472943,level: 1}]->(g);
CREATE (n: Building {id: 120, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:120}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.153322228627547, level: 1}]->(b);
CREATE (n: Building {id: 121, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:121}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 17.70023109973174, level: 5}]->(b);
CREATE (n: Building {id: 122, name:"building_tooling_workshopslevel", level:12});
MATCH (g: Goods{code: 10}), (b: Building{id:122}) CREATE (g)-[r:Demand{max_demand: 360.0, current_input: 45.34462361567432, level: 12}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:122}) CREATE (g)-[r:Demand{max_demand: 239.99999999999997, current_input: 73.959321159866, level: 12}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:122}) CREATE (b)-[r:Supply{max_supply: 959.9999999999999, current_output: 208.3781404739644,level: 12}]->(g);
CREATE (n: Building {id: 123, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:123}) CREATE (g)-[r:Demand{max_demand: 30.7916, current_input: 4.4893740579049926, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:123}) CREATE (g)-[r:Demand{max_demand: 61.5832, current_input: 7.756852847357764, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:123}) CREATE (g)-[r:Demand{max_demand: 76.979, current_input: 19.638353235915343, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:123}) CREATE (g)-[r:Demand{max_demand: 15.3958, current_input: 3.614999227696263, level: 2}]->(b);
CREATE (n: Building {id: 124, name:"building_textile_millslevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:124}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 43.739598376553936, level: 10}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:124}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 0.0, level: 10}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:124}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 0.0, level: 10}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:124}) CREATE (b)-[r:Supply{max_supply: 399.99999999999994, current_output: 19.439821500690634,level: 10}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:124}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 9.719910750345317,level: 10}]->(g);
CREATE (n: Building {id: 125, name:"building_steel_millslevel", level:10});
MATCH (g: Goods{code: 23}), (b: Building{id:125}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 61.127398455893996, level: 10}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:125}) CREATE (g)-[r:Demand{max_demand: 399.99999999999994, current_input: 102.0452499300606, level: 10}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:125}) CREATE (b)-[r:Supply{max_supply: 650.0, current_output: 149.13311389539274,level: 10}]->(g);
CREATE (n: Building {id: 126, name:"building_motor_industrylevel", level:8});
MATCH (g: Goods{code: 30}), (b: Building{id:126}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 73.959321159866, level: 8}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:126}) CREATE (b)-[r:Supply{max_supply: 319.99999999999994, current_output: 98.61242821315464,level: 8}]->(g);
CREATE (n: Building {id: 127, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:127}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.357274526281861, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:127}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.892377053716753, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:127}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.892377053716753, level: 10}]->(b);
CREATE (n: Building {id: 128, name:"building_naval_baselevel", level:15});
MATCH (g: Goods{code: 5}), (b: Building{id:128}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 4.35699508592629, level: 15}]->(b);
CREATE (n: Building {id: 129, name:"building_coal_minelevel", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:129}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 23.480424711260625, level: 10}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:129}) CREATE (b)-[r:Supply{max_supply: 400.0, current_output: 93.9216988450425,level: 10}]->(g);
CREATE (n: Building {id: 130, name:"building_iron_minelevel", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:130}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 10.187899742648998, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:130}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 11.740212355630312, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:130}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 43.85622419655862,level: 5}]->(g);
CREATE (n: Building {id: 131, name:"building_rye_farmlevel", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:131}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 7.753874318706282, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:131}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 1.8784339769008498, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:131}) CREATE (b)-[r:Supply{max_supply: 400.0, current_output: 85.73022101605267,level: 8}]->(g);
CREATE (n: Building {id: 132, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:132}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 8.120105223111175, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:132}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.044127413378186, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:132}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 30.328465272978722,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:132}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 3.7910581591223402,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:132}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 18.955290795611702,level: 3}]->(g);
CREATE (n: Building {id: 133, name:"building_railwaylevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:133}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 3.148932195532939, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:133}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 10.187899742648998, level: 5}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:133}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 18.5502270966073, level: 5}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:133}) CREATE (b)-[r:Supply{max_supply: 325.0, current_output: 75.91131433236473,level: 5}]->(g);
CREATE (n: Building {id: 134, name:"building_portlevel", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:134}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.613288914510188, level: 4}]->(b);
CREATE (n: Building {id: 135, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:135}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1786372631409305, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:135}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4461885268583765, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:135}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4461885268583765, level: 5}]->(b);
CREATE (n: Building {id: 136, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:136}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.522063706689093, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:136}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 28.176509653512746,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:136}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 7.044127413378186,level: 3}]->(g);
CREATE (n: Building {id: 137, name:"building_whaling_stationlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:137}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.306644457255094, level: 2}]->(b);
MATCH (g: Goods{code: 28}), (b: Building{id:137}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 9.226577829020377,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:137}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 4.613288914510188,level: 2}]->(g);
CREATE (n: Building {id: 138, name:"building_fishing_wharflevel", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:138}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.613288914510188, level: 4}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:138}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 46.132889145101885,level: 4}]->(g);
CREATE (n: Building {id: 139, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:139}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 17.70023109973174, level: 5}]->(b);
CREATE (n: Building {id: 140, name:"building_arms_industrylevel", level:8});
MATCH (g: Goods{code: 24}), (b: Building{id:140}) CREATE (g)-[r:Demand{max_demand: 159.99999999999997, current_input: 40.81809997202424, level: 8}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:140}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 0.8918759448546651, level: 8}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:140}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 15.975694448150094,level: 8}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:140}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 15.975694448150094,level: 8}]->(g);
CREATE (n: Building {id: 141, name:"building_textile_millslevel", level:25});
MATCH (g: Goods{code: 9}), (b: Building{id:141}) CREATE (g)-[r:Demand{max_demand: 750.0, current_input: 109.34899594138483, level: 25}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:141}) CREATE (g)-[r:Demand{max_demand: 250.0, current_input: 0.0, level: 25}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:141}) CREATE (g)-[r:Demand{max_demand: 125.0, current_input: 0.0, level: 25}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:141}) CREATE (b)-[r:Supply{max_supply: 1000.0, current_output: 48.59955375172659,level: 25}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:141}) CREATE (b)-[r:Supply{max_supply: 500.0, current_output: 24.299776875863294,level: 25}]->(g);
CREATE (n: Building {id: 142, name:"building_furniture_manufacturieslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:142}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 7.289933062758989, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:142}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 12.595728782131756, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:142}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 0.5574224655341657, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:142}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 5.870106177815156, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:142}) CREATE (b)-[r:Supply{max_supply: 225.0, current_output: 29.121111309363013,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:142}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 12.942716137494672,level: 5}]->(g);
CREATE (n: Building {id: 143, name:"building_chemical_plantslevel", level:2});
MATCH (g: Goods{code: 22}), (b: Building{id:143}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 8.312682998181653, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:143}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.1127398455894, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:143}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.102262496503031, level: 2}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:143}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 29.870791815724626,level: 2}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:143}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 7.9655444841932335,level: 2}]->(g);
CREATE (n: Building {id: 144, name:"building_coal_minelevel", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:144}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 18.784339769008497, level: 8}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:144}) CREATE (b)-[r:Supply{max_supply: 319.99999999999994, current_output: 75.13735907603399,level: 8}]->(g);
CREATE (n: Building {id: 145, name:"building_iron_minelevel", level:8});
MATCH (g: Goods{code: 23}), (b: Building{id:145}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 16.300639588238397, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:145}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 18.784339769008497, level: 8}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:145}) CREATE (b)-[r:Supply{max_supply: 319.99999999999994, current_output: 70.16995871449379,level: 8}]->(g);
CREATE (n: Building {id: 146, name:"building_rye_farmlevel", level:10});
MATCH (g: Goods{code: 32}), (b: Building{id:146}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 9.692342898382853, level: 10}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:146}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.3480424711260626, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:146}) CREATE (b)-[r:Supply{max_supply: 499.99999999999994, current_output: 107.16277627006582,level: 10}]->(g);
CREATE (n: Building {id: 147, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:147}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 13.533508705185294, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:147}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 11.740212355630312, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:147}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 50.547442121631214,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:147}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 6.318430265203902,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:147}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 31.592151326019508,level: 5}]->(g);
CREATE (n: Building {id: 148, name:"building_railwaylevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:148}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 3.148932195532939, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:148}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 10.187899742648998, level: 5}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:148}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 18.5502270966073, level: 5}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:148}) CREATE (b)-[r:Supply{max_supply: 325.0, current_output: 75.91131433236473,level: 5}]->(g);
CREATE (n: Building {id: 149, name:"building_barrackslevel", level:20});
MATCH (g: Goods{code: 0}), (b: Building{id:149}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.714549052563722, level: 20}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:149}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.784754107433506, level: 20}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:149}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.784754107433506, level: 20}]->(b);
CREATE (n: Building {id: 150, name:"building_portlevel", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:150}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.613288914510188, level: 4}]->(b);
CREATE (n: Building {id: 151, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:151}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 10.620138659839046, level: 3}]->(b);
CREATE (n: Building {id: 152, name:"building_universitylevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:152}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 4.425057774932935, level: 5}]->(b);
CREATE (n: Building {id: 153, name:"building_furniture_manufacturieslevel", level:15});
MATCH (g: Goods{code: 9}), (b: Building{id:153}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 21.869799188276968, level: 15}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:153}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 37.78718634639527, level: 15}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:153}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 1.6722673966024972, level: 15}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:153}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 17.61031853344547, level: 15}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:153}) CREATE (b)-[r:Supply{max_supply: 675.0000000000001, current_output: 87.36333392808905,level: 15}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:153}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 38.82814841248402,level: 15}]->(g);
CREATE (n: Building {id: 154, name:"building_glassworkslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:154}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 12.595728782131756, level: 5}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:154}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 9.698130164545262, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:154}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 31.99198911122228,level: 5}]->(g);
CREATE (n: Building {id: 155, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:155}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 43.30722785659294, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:155}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 76.43151079644592, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:155}) CREATE (b)-[r:Supply{max_supply: 139.99999999999997, current_output: 52.38569816070449,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:155}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 89.80405398977913,level: 4}]->(g);
CREATE (n: Building {id: 156, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:156}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.075159897059599, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:156}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.696084942252125, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:156}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 17.54248967862345,level: 2}]->(g);
CREATE (n: Building {id: 157, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:157}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.9159732251035955, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:157}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 5.038291512852703, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:157}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.22296898621366631, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:157}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.7100454193214603, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:157}) CREATE (b)-[r:Supply{max_supply: 24.999999999999996, current_output: 4.086930876995792,level: 1}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:157}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 7.356475578592425,level: 1}]->(g);
CREATE (n: Building {id: 158, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:158}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1786372631409305, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:158}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4461885268583765, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:158}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4461885268583765, level: 5}]->(b);
CREATE (n: Building {id: 159, name:"building_naval_baselevel", level:25});
MATCH (g: Goods{code: 5}), (b: Building{id:159}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 7.261658476543817, level: 25}]->(b);
CREATE (n: Building {id: 160, name:"building_rye_farmlevel", level:7});
MATCH (g: Goods{code: 32}), (b: Building{id:160}) CREATE (g)-[r:Demand{max_demand: 35.00000000000001, current_input: 6.784640028868, level: 7}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:160}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 1.6436297297882438, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:160}) CREATE (b)-[r:Supply{max_supply: 350.0, current_output: 75.01394338904609,level: 7}]->(g);
CREATE (n: Building {id: 161, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:161}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 8.120105223111175, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:161}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.044127413378186, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:161}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 30.328465272978722,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:161}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 3.7910581591223402,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:161}) CREATE (b)-[r:Supply{max_supply: 74.99999999999999, current_output: 18.9552907956117,level: 3}]->(g);
CREATE (n: Building {id: 162, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:162}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.4599666858826414, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:162}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 34.59966685882642,level: 3}]->(g);
CREATE (n: Building {id: 163, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:163}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.6297864391065878, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:163}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.0375799485297996, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:163}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.7100454193214603, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:163}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 15.182262866472943,level: 1}]->(g);
CREATE (n: Building {id: 164, name:"building_portlevel", level:5});
MATCH (g: Goods{code: 18}), (b: Building{id:164}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 5.766611143137736, level: 5}]->(b);
CREATE (n: Building {id: 165, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:165}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 17.70023109973174, level: 5}]->(b);
CREATE (n: Building {id: 166, name:"building_tooling_workshopslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:166}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 15.114874538558105, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:166}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 24.653107053288664, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:166}) CREATE (b)-[r:Supply{max_supply: 320.0, current_output: 69.45938015798815,level: 4}]->(g);
CREATE (n: Building {id: 167, name:"building_textile_millslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:167}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 21.869799188276968, level: 5}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:167}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:167}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:167}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 9.719910750345319,level: 5}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:167}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 4.859955375172659,level: 5}]->(g);
CREATE (n: Building {id: 168, name:"building_shipyardslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:168}) CREATE (g)-[r:Demand{max_demand: 80.00000000000001, current_input: 11.663892900414384, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:168}) CREATE (g)-[r:Demand{max_demand: 160.00000000000003, current_input: 20.153166051410814, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:168}) CREATE (g)-[r:Demand{max_demand: 80.00000000000001, current_input: 0.8918759448546655, level: 4}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:168}) CREATE (g)-[r:Demand{max_demand: 40.00000000000001, current_input: 14.840181677285845, level: 4}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:168}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 16.347723507983172,level: 4}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:168}) CREATE (b)-[r:Supply{max_supply: 180.00000000000003, current_output: 29.42590231436971,level: 4}]->(g);
CREATE (n: Building {id: 169, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:169}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1786372631409305, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:169}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4461885268583765, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:169}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4461885268583765, level: 5}]->(b);
CREATE (n: Building {id: 170, name:"building_naval_baselevel", level:25});
MATCH (g: Goods{code: 5}), (b: Building{id:170}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 7.261658476543817, level: 25}]->(b);
CREATE (n: Building {id: 171, name:"building_wheat_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:171}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 4.846171449191426, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:171}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1740212355630313, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:171}) CREATE (b)-[r:Supply{max_supply: 175.0, current_output: 37.50697169452304,level: 5}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:171}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 8.573022101605265,level: 5}]->(g);
CREATE (n: Building {id: 172, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:172}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 8.120105223111175, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:172}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.044127413378186, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:172}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 30.328465272978722,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:172}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 3.7910581591223402,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:172}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 18.955290795611702,level: 3}]->(g);
CREATE (n: Building {id: 173, name:"building_sulfur_minelevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:173}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.1127398455894, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:173}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.044127413378186, level: 3}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:173}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 26.313734517935174,level: 3}]->(g);
CREATE (n: Building {id: 174, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:174}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.6297864391065878, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:174}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.0375799485297996, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:174}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.7100454193214603, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:174}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 15.182262866472943,level: 1}]->(g);
CREATE (n: Building {id: 175, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:175}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.306644457255094, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:175}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 23.066444572550942,level: 2}]->(g);
CREATE (n: Building {id: 176, name:"building_portlevel", level:5});
MATCH (g: Goods{code: 18}), (b: Building{id:176}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 5.766611143137736, level: 5}]->(b);
CREATE (n: Building {id: 177, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:177}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 10.620138659839046, level: 3}]->(b);
CREATE (n: Building {id: 178, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:178}) CREATE (g)-[r:Demand{max_demand: 15.3958, current_input: 2.2446870289524963, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:178}) CREATE (g)-[r:Demand{max_demand: 30.7916, current_input: 3.878426423678882, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:178}) CREATE (g)-[r:Demand{max_demand: 38.4895, current_input: 9.819176617957671, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:178}) CREATE (g)-[r:Demand{max_demand: 7.6979, current_input: 1.8074996138481314, level: 1}]->(b);
CREATE (n: Building {id: 179, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:179}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 43.30722785659294, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:179}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 76.43151079644592, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:179}) CREATE (b)-[r:Supply{max_supply: 139.99999999999997, current_output: 52.38569816070449,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:179}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 89.80405398977913,level: 4}]->(g);
CREATE (n: Building {id: 180, name:"building_glassworkslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:180}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 15.114874538558107, level: 6}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:180}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 11.637756197454314, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:180}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 38.390386933466736,level: 6}]->(g);
CREATE (n: Building {id: 181, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:181}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.3480424711260626, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:181}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 28.17650965351275,level: 2}]->(g);
CREATE (n: Building {id: 182, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:182}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.075159897059599, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:182}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.696084942252125, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:182}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 17.54248967862345,level: 2}]->(g);
CREATE (n: Building {id: 183, name:"building_rye_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:183}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 4.846171449191426, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:183}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1740212355630313, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:183}) CREATE (b)-[r:Supply{max_supply: 250.0, current_output: 53.58138813503291,level: 5}]->(g);
CREATE (n: Building {id: 184, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:184}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.7067017410370586, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:184}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.3480424711260626, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:184}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 10.109488424326242,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:184}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 1.2636860530407803,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:184}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 6.318430265203902,level: 1}]->(g);
CREATE (n: Building {id: 185, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:185}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.306644457255094, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:185}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 23.066444572550942,level: 2}]->(g);
CREATE (n: Building {id: 186, name:"building_naval_baselevel", level:15});
MATCH (g: Goods{code: 5}), (b: Building{id:186}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 4.35699508592629, level: 15}]->(b);
CREATE (n: Building {id: 187, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:187}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.357274526281861, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:187}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.892377053716753, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:187}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.892377053716753, level: 10}]->(b);
CREATE (n: Building {id: 188, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:188}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.6297864391065878, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:188}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.0375799485297996, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:188}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.7100454193214603, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:188}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 15.182262866472943,level: 1}]->(g);
CREATE (n: Building {id: 189, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:189}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.4599666858826414, level: 3}]->(b);
CREATE (n: Building {id: 190, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:190}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 5.413403482074117, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:190}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.696084942252125, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:190}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 20.218976848652485,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:190}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 2.5273721060815606,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:190}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 12.636860530407803,level: 2}]->(g);
CREATE (n: Building {id: 191, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:191}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.907702869514856, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:191}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.7044127413378186, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:191}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 19.289299728611848,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:191}) CREATE (b)-[r:Supply{max_supply: 45.0, current_output: 9.644649864305924,level: 3}]->(g);
CREATE (n: Building {id: 192, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:192}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1786372631409305, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:192}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4461885268583765, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:192}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4461885268583765, level: 5}]->(b);
CREATE (n: Building {id: 193, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:193}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.3480424711260626, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:193}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 28.17650965351275,level: 2}]->(g);
CREATE (n: Building {id: 194, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:194}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.306644457255094, level: 2}]->(b);
CREATE (n: Building {id: 195, name:"building_rye_farmlevel", level:3});
MATCH (g: Goods{code: 32}), (b: Building{id:195}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.907702869514856, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:195}) CREATE (g)-[r:Demand{max_demand: 2.9999999999999996, current_input: 0.7044127413378186, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:195}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 19.289299728611848,level: 3}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:195}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 9.644649864305924,level: 3}]->(g);
CREATE (n: Building {id: 196, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:196}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.7071823578845583, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:196}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.8677131161150259, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:196}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.8677131161150259, level: 3}]->(b);
CREATE (n: Building {id: 197, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:197}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.452331695308763, level: 5}]->(b);
CREATE (n: Building {id: 198, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:198}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.306644457255094, level: 2}]->(b);
CREATE (n: Building {id: 199, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:199}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8850115549865872, level: 1}]->(b);
CREATE (n: Building {id: 200, name:"building_lead_minelevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:200}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.1127398455894, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:200}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 7.044127413378186, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:200}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 26.313734517935174,level: 3}]->(g);
CREATE (n: Building {id: 201, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:201}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 43.30722785659294, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:201}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 76.43151079644592, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:201}) CREATE (b)-[r:Supply{max_supply: 139.99999999999997, current_output: 52.38569816070449,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:201}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 89.80405398977913,level: 4}]->(g);
CREATE (n: Building {id: 202, name:"building_rye_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:202}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 4.846171449191426, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:202}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1740212355630313, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:202}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 32.14883288101974,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:202}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 16.07441644050987,level: 5}]->(g);
CREATE (n: Building {id: 203, name:"building_naval_baselevel", level:15});
MATCH (g: Goods{code: 5}), (b: Building{id:203}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 4.35699508592629, level: 15}]->(b);
CREATE (n: Building {id: 204, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:204}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.1786372631409305, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:204}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4461885268583765, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:204}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.4461885268583765, level: 5}]->(b);
CREATE (n: Building {id: 205, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:205}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 3.4599666858826414, level: 3}]->(b);
CREATE (n: Building {id: 206, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:206}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.9384685796765708, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:206}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.46960849422521245, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:206}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 12.859533152407899,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:206}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 6.429766576203949,level: 2}]->(g);
CREATE (n: Building {id: 207, name:"building_barrackslevel", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:207}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.47145490525637224, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:207}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.5784754107433506, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:207}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 0.5784754107433506, level: 2}]->(b);
CREATE (n: Building {id: 208, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:208}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.153322228627547, level: 1}]->(b);
CREATE (n: Building {id: 628, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 8}), (b: Building{id:628}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 25.0,level: 1}]->(g);
CREATE (n: Building {id: 629, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:629}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.452331695308763, level: 5}]->(b);
CREATE (n: Building {id: 630, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:630}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.153322228627547, level: 1}]->(b);
CREATE (n: Building {id: 790, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:790}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.452331695308763, level: 5}]->(b);
CREATE (n: Building {id: 791, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:791}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.153322228627547, level: 1}]->(b);
CREATE (n: Building {id: 1266, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1302, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1626, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1626}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.306644457255094, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1626}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 23.066444572550942,level: 2}]->(g);
CREATE (n: Building {id: 1632, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:1632}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.452331695308763, level: 5}]->(b);
CREATE (n: Building {id: 1633, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1699, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1729, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1729}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 1730, name:"building_sugar_plantationlevel", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1730}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 1731, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1738, name:"building_sugar_plantationlevel", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:1738}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 91.8,level: 3}]->(g);
CREATE (n: Building {id: 1739, name:"building_coffee_plantationlevel", level:2});
MATCH (g: Goods{code: 41}), (b: Building{id:1739}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 40.4,level: 2}]->(g);
CREATE (n: Building {id: 1740, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1740}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 50.5,level: 2}]->(g);
CREATE (n: Building {id: 1741, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1742, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1742}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 1743, name:"building_sugar_plantationlevel", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:1743}) CREATE (b)-[r:Supply{max_supply: 90.0, current_output: 91.8,level: 3}]->(g);
CREATE (n: Building {id: 1744, name:"building_coffee_plantationlevel", level:5});
MATCH (g: Goods{code: 41}), (b: Building{id:1744}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 104.0,level: 5}]->(g);
CREATE (n: Building {id: 1745, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1763, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1763}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 60.6,level: 2}]->(g);
CREATE (n: Building {id: 1764, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1892, name:"building_portlevel", level:1});
CREATE (n: Building {id: 2330, name:"building_sugar_plantationlevel", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:2330}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 30.0,level: 1}]->(g);
CREATE (n: Building {id: 2331, name:"building_portlevel", level:1});
CREATE (n: Building {id: 2723, name:"building_tea_plantationlevel", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:2723}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 20.0,level: 1}]->(g);
CREATE (n: Building {id: 2724, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2724}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 1.153322228627547, level: 1}]->(b);
CREATE (n: Building {id: 2873, name:"building_subsistence_farmslevel", level:10});
CREATE (n: Building {id: 2874, name:"building_urban_centerlevel", level:24});
MATCH (g: Goods{code: 10}), (b: Building{id:2874}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 15.114874538558107, level: 24}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2874}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 48.9019187647152, level: 24}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2874}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 19.57116669902163, level: 24}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2874}) CREATE (b)-[r:Supply{max_supply: 1680.0, current_output: 275.9726695597076,level: 24}]->(g);
CREATE (n: Building {id: 2875, name:"building_subsistence_farmslevel", level:21});
MATCH (g: Goods{code: 7}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 0.71715, current_output: 0.71715,level: 21}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 0.14343, current_output: 0.14343,level: 21}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 0.14343, current_output: 0.14343,level: 21}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 0.14343, current_output: 0.14343,level: 21}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 0.14343, current_output: 0.14343,level: 21}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 0.14343, current_output: 0.14343,level: 21}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2875}) CREATE (b)-[r:Supply{max_supply: 0.2008, current_output: 0.2008,level: 21}]->(g);
CREATE (n: Building {id: 2876, name:"building_urban_centerlevel", level:12});
MATCH (g: Goods{code: 10}), (b: Building{id:2876}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 7.557437269279053, level: 12}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2876}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 24.450959382357595, level: 12}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2876}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 9.785583349510816, level: 12}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2876}) CREATE (b)-[r:Supply{max_supply: 839.9999999999999, current_output: 137.98633477985376,level: 12}]->(g);
CREATE (n: Building {id: 2993, name:"building_subsistence_farmslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 7.1715, current_output: 7.1715,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 1.4343, current_output: 1.4343,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 1.4343, current_output: 1.4343,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 1.4343, current_output: 1.4343,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 1.4343, current_output: 1.4343,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 1.4343, current_output: 1.4343,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2993}) CREATE (b)-[r:Supply{max_supply: 2.00802, current_output: 2.00802,level: 20}]->(g);
CREATE (n: Building {id: 2995, name:"building_subsistence_farmslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 0.47895, current_output: 0.47895,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 0.09579, current_output: 0.09579,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 0.09579, current_output: 0.09579,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 0.09579, current_output: 0.09579,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 0.09579, current_output: 0.09579,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 0.09579, current_output: 0.09579,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2995}) CREATE (b)-[r:Supply{max_supply: 0.1341, current_output: 0.1341,level: 6}]->(g);
CREATE (n: Building {id: 3079, name:"building_subsistence_farmslevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 7.18, current_output: 7.18,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 1.436, current_output: 1.436,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 1.436, current_output: 1.436,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 1.436, current_output: 1.436,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 1.436, current_output: 1.436,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 1.436, current_output: 1.436,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3079}) CREATE (b)-[r:Supply{max_supply: 2.0104, current_output: 2.0104,level: 5}]->(g);
CREATE (n: Building {id: 3132, name:"building_subsistence_farmslevel", level:12});
MATCH (g: Goods{code: 7}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 8.9064, current_output: 8.9064,level: 12}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 1.78128, current_output: 1.78128,level: 12}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 1.78128, current_output: 1.78128,level: 12}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 1.78128, current_output: 1.78128,level: 12}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 1.78128, current_output: 1.78128,level: 12}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 1.78128, current_output: 1.78128,level: 12}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3132}) CREATE (b)-[r:Supply{max_supply: 2.49379, current_output: 2.49379,level: 12}]->(g);
CREATE (n: Building {id: 3202, name:"building_subsistence_pastureslevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:3202}) CREATE (b)-[r:Supply{max_supply: 0.3151, current_output: 0.3151,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3202}) CREATE (b)-[r:Supply{max_supply: 0.47265, current_output: 0.47265,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3202}) CREATE (b)-[r:Supply{max_supply: 0.15755, current_output: 0.15755,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3202}) CREATE (b)-[r:Supply{max_supply: 0.3151, current_output: 0.3151,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3202}) CREATE (b)-[r:Supply{max_supply: 0.3151, current_output: 0.3151,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3202}) CREATE (b)-[r:Supply{max_supply: 0.3151, current_output: 0.3151,level: 10}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3202}) CREATE (b)-[r:Supply{max_supply: 0.83816, current_output: 0.83816,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3202}) CREATE (b)-[r:Supply{max_supply: 0.44114, current_output: 0.44114,level: 10}]->(g);
CREATE (n: Building {id: 3247, name:"building_subsistence_farmslevel", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:3247}) CREATE (b)-[r:Supply{max_supply: 11.86937, current_output: 11.86937,level: 7}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3247}) CREATE (b)-[r:Supply{max_supply: 2.37387, current_output: 2.37387,level: 7}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3247}) CREATE (b)-[r:Supply{max_supply: 2.37387, current_output: 2.37387,level: 7}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3247}) CREATE (b)-[r:Supply{max_supply: 2.37387, current_output: 2.37387,level: 7}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3247}) CREATE (b)-[r:Supply{max_supply: 2.37387, current_output: 2.37387,level: 7}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3247}) CREATE (b)-[r:Supply{max_supply: 2.37387, current_output: 2.37387,level: 7}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3247}) CREATE (b)-[r:Supply{max_supply: 3.32342, current_output: 3.32342,level: 7}]->(g);
CREATE (n: Building {id: 3261, name:"building_subsistence_farmslevel", level:37});
MATCH (g: Goods{code: 7}), (b: Building{id:3261}) CREATE (b)-[r:Supply{max_supply: 91.4307, current_output: 91.4307,level: 37}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3261}) CREATE (b)-[r:Supply{max_supply: 18.28614, current_output: 18.28614,level: 37}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3261}) CREATE (b)-[r:Supply{max_supply: 18.28614, current_output: 18.28614,level: 37}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3261}) CREATE (b)-[r:Supply{max_supply: 18.28614, current_output: 18.28614,level: 37}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3261}) CREATE (b)-[r:Supply{max_supply: 18.28614, current_output: 18.28614,level: 37}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3261}) CREATE (b)-[r:Supply{max_supply: 18.28614, current_output: 18.28614,level: 37}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3261}) CREATE (b)-[r:Supply{max_supply: 25.60059, current_output: 25.60059,level: 37}]->(g);
CREATE (n: Building {id: 3262, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3262}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.6297864391065878, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3262}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.0375799485297996, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3262}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8154652791259014, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3262}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 11.498861231654482,level: 1}]->(g);
CREATE (n: Building {id: 3263, name:"building_subsistence_farmslevel", level:29});
MATCH (g: Goods{code: 7}), (b: Building{id:3263}) CREATE (b)-[r:Supply{max_supply: 23.5277, current_output: 23.5277,level: 29}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3263}) CREATE (b)-[r:Supply{max_supply: 4.70554, current_output: 4.70554,level: 29}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3263}) CREATE (b)-[r:Supply{max_supply: 4.70554, current_output: 4.70554,level: 29}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3263}) CREATE (b)-[r:Supply{max_supply: 4.70554, current_output: 4.70554,level: 29}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3263}) CREATE (b)-[r:Supply{max_supply: 4.70554, current_output: 4.70554,level: 29}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3263}) CREATE (b)-[r:Supply{max_supply: 4.70554, current_output: 4.70554,level: 29}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3263}) CREATE (b)-[r:Supply{max_supply: 6.58775, current_output: 6.58775,level: 29}]->(g);
CREATE (n: Building {id: 3264, name:"building_urban_centerlevel", level:13});
MATCH (g: Goods{code: 10}), (b: Building{id:3264}) CREATE (g)-[r:Demand{max_demand: 64.99999999999999, current_input: 8.18722370838564, level: 13}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3264}) CREATE (g)-[r:Demand{max_demand: 129.99999999999997, current_input: 26.48853933088739, level: 13}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3264}) CREATE (g)-[r:Demand{max_demand: 64.99999999999999, current_input: 10.601048628636713, level: 13}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3264}) CREATE (b)-[r:Supply{max_supply: 910.0, current_output: 149.48519601150826,level: 13}]->(g);
CREATE (n: Building {id: 3265, name:"building_subsistence_farmslevel", level:30});
MATCH (g: Goods{code: 7}), (b: Building{id:3265}) CREATE (b)-[r:Supply{max_supply: 72.5355, current_output: 72.5355,level: 30}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3265}) CREATE (b)-[r:Supply{max_supply: 14.5071, current_output: 14.5071,level: 30}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3265}) CREATE (b)-[r:Supply{max_supply: 14.5071, current_output: 14.5071,level: 30}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3265}) CREATE (b)-[r:Supply{max_supply: 14.5071, current_output: 14.5071,level: 30}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3265}) CREATE (b)-[r:Supply{max_supply: 14.5071, current_output: 14.5071,level: 30}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3265}) CREATE (b)-[r:Supply{max_supply: 14.5071, current_output: 14.5071,level: 30}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3265}) CREATE (b)-[r:Supply{max_supply: 20.30994, current_output: 20.30994,level: 30}]->(g);
CREATE (n: Building {id: 3266, name:"building_subsistence_farmslevel", level:45});
MATCH (g: Goods{code: 7}), (b: Building{id:3266}) CREATE (b)-[r:Supply{max_supply: 81.74587, current_output: 81.74587,level: 45}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3266}) CREATE (b)-[r:Supply{max_supply: 16.34917, current_output: 16.34917,level: 45}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3266}) CREATE (b)-[r:Supply{max_supply: 16.34917, current_output: 16.34917,level: 45}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3266}) CREATE (b)-[r:Supply{max_supply: 16.34917, current_output: 16.34917,level: 45}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3266}) CREATE (b)-[r:Supply{max_supply: 16.34917, current_output: 16.34917,level: 45}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3266}) CREATE (b)-[r:Supply{max_supply: 16.34917, current_output: 16.34917,level: 45}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3266}) CREATE (b)-[r:Supply{max_supply: 22.88884, current_output: 22.88884,level: 45}]->(g);
CREATE (n: Building {id: 3267, name:"building_urban_centerlevel", level:12});
MATCH (g: Goods{code: 10}), (b: Building{id:3267}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 7.557437269279053, level: 12}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3267}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 24.450959382357595, level: 12}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3267}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 9.785583349510816, level: 12}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3267}) CREATE (b)-[r:Supply{max_supply: 839.9999999999999, current_output: 137.98633477985376,level: 12}]->(g);
CREATE (n: Building {id: 3268, name:"building_subsistence_farmslevel", level:20});
MATCH (g: Goods{code: 7}), (b: Building{id:3268}) CREATE (b)-[r:Supply{max_supply: 32.830499999999994, current_output: 36.11355,level: 20}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3268}) CREATE (b)-[r:Supply{max_supply: 6.5661, current_output: 7.22271,level: 20}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3268}) CREATE (b)-[r:Supply{max_supply: 6.5661, current_output: 7.22271,level: 20}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3268}) CREATE (b)-[r:Supply{max_supply: 6.5661, current_output: 7.22271,level: 20}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3268}) CREATE (b)-[r:Supply{max_supply: 6.5661, current_output: 7.22271,level: 20}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3268}) CREATE (b)-[r:Supply{max_supply: 6.5661, current_output: 7.22271,level: 20}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3268}) CREATE (b)-[r:Supply{max_supply: 9.192536363636362, current_output: 10.11179,level: 20}]->(g);
CREATE (n: Building {id: 3269, name:"building_urban_centerlevel", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:3269}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 5.038291512852702, level: 8}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3269}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 16.300639588238397, level: 8}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3269}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 6.523722233007209, level: 8}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3269}) CREATE (b)-[r:Supply{max_supply: 560.0, current_output: 91.99088985323586,level: 8}]->(g);
CREATE (n: Building {id: 3270, name:"building_subsistence_farmslevel", level:52});
MATCH (g: Goods{code: 7}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 129.9961, current_output: 129.9961,level: 52}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 25.99922, current_output: 25.99922,level: 52}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 25.99922, current_output: 25.99922,level: 52}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 25.99922, current_output: 25.99922,level: 52}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 25.99922, current_output: 25.99922,level: 52}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 25.99922, current_output: 25.99922,level: 52}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 36.3989, current_output: 36.3989,level: 52}]->(g);
CREATE (n: Building {id: 3271, name:"building_urban_centerlevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3271}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 3.148932195532939, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3271}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 10.187899742648998, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3271}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 4.077326395629506, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3271}) CREATE (b)-[r:Supply{max_supply: 350.0, current_output: 57.4943061582724,level: 5}]->(g);
CREATE (n: Building {id: 3272, name:"building_subsistence_farmslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:3272}) CREATE (b)-[r:Supply{max_supply: 109.9945, current_output: 109.9945,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3272}) CREATE (b)-[r:Supply{max_supply: 21.9989, current_output: 21.9989,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3272}) CREATE (b)-[r:Supply{max_supply: 21.9989, current_output: 21.9989,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3272}) CREATE (b)-[r:Supply{max_supply: 21.9989, current_output: 21.9989,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3272}) CREATE (b)-[r:Supply{max_supply: 21.9989, current_output: 21.9989,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3272}) CREATE (b)-[r:Supply{max_supply: 21.9989, current_output: 21.9989,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3272}) CREATE (b)-[r:Supply{max_supply: 30.79846, current_output: 30.79846,level: 44}]->(g);
CREATE (n: Building {id: 3273, name:"building_urban_centerlevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3273}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.5191457564263513, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3273}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 8.150319794119198, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3273}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.2618611165036056, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3273}) CREATE (b)-[r:Supply{max_supply: 279.99999999999994, current_output: 45.99544492661792,level: 4}]->(g);
CREATE (n: Building {id: 3274, name:"building_subsistence_farmslevel", level:105});
MATCH (g: Goods{code: 7}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 262.5, current_output: 262.5,level: 105}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 52.5, current_output: 52.5,level: 105}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 52.5, current_output: 52.5,level: 105}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 52.5, current_output: 52.5,level: 105}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 52.5, current_output: 52.5,level: 105}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 52.5, current_output: 52.5,level: 105}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 73.5, current_output: 73.5,level: 105}]->(g);
CREATE (n: Building {id: 3275, name:"building_subsistence_farmslevel", level:107});
MATCH (g: Goods{code: 7}), (b: Building{id:3275}) CREATE (b)-[r:Supply{max_supply: 267.5, current_output: 294.25,level: 107}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3275}) CREATE (b)-[r:Supply{max_supply: 53.5, current_output: 58.85,level: 107}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3275}) CREATE (b)-[r:Supply{max_supply: 53.5, current_output: 58.85,level: 107}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3275}) CREATE (b)-[r:Supply{max_supply: 53.5, current_output: 58.85,level: 107}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3275}) CREATE (b)-[r:Supply{max_supply: 53.5, current_output: 58.85,level: 107}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3275}) CREATE (b)-[r:Supply{max_supply: 53.5, current_output: 58.85,level: 107}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3275}) CREATE (b)-[r:Supply{max_supply: 74.89999999999999, current_output: 82.39,level: 107}]->(g);
CREATE (n: Building {id: 3276, name:"building_subsistence_farmslevel", level:75});
MATCH (g: Goods{code: 7}), (b: Building{id:3276}) CREATE (b)-[r:Supply{max_supply: 187.5, current_output: 187.5,level: 75}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3276}) CREATE (b)-[r:Supply{max_supply: 37.5, current_output: 37.5,level: 75}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3276}) CREATE (b)-[r:Supply{max_supply: 37.5, current_output: 37.5,level: 75}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3276}) CREATE (b)-[r:Supply{max_supply: 37.5, current_output: 37.5,level: 75}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3276}) CREATE (b)-[r:Supply{max_supply: 37.5, current_output: 37.5,level: 75}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3276}) CREATE (b)-[r:Supply{max_supply: 37.5, current_output: 37.5,level: 75}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3276}) CREATE (b)-[r:Supply{max_supply: 52.5, current_output: 52.5,level: 75}]->(g);
CREATE (n: Building {id: 3277, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3277}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.2595728782131757, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3277}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.075159897059599, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3277}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.6309305582518028, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3277}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 22.997722463308964,level: 2}]->(g);
CREATE (n: Building {id: 3324, name:"building_subsistence_farmslevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3324}) CREATE (b)-[r:Supply{max_supply: 3.6715, current_output: 3.6715,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3324}) CREATE (b)-[r:Supply{max_supply: 0.7343, current_output: 0.7343,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3324}) CREATE (b)-[r:Supply{max_supply: 0.7343, current_output: 0.7343,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3324}) CREATE (b)-[r:Supply{max_supply: 0.7343, current_output: 0.7343,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3324}) CREATE (b)-[r:Supply{max_supply: 0.7343, current_output: 0.7343,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3324}) CREATE (b)-[r:Supply{max_supply: 0.7343, current_output: 0.7343,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3324}) CREATE (b)-[r:Supply{max_supply: 1.02802, current_output: 1.02802,level: 5}]->(g);
CREATE (n: Building {id: 3350, name:"building_subsistence_fishing_villageslevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:3350}) CREATE (b)-[r:Supply{max_supply: 2.2604, current_output: 2.2604,level: 8}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:3350}) CREATE (b)-[r:Supply{max_supply: 9.0416, current_output: 9.0416,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3350}) CREATE (b)-[r:Supply{max_supply: 1.1302, current_output: 1.1302,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3350}) CREATE (b)-[r:Supply{max_supply: 3.3906, current_output: 3.3906,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3350}) CREATE (b)-[r:Supply{max_supply: 2.2604, current_output: 2.2604,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3350}) CREATE (b)-[r:Supply{max_supply: 2.2604, current_output: 2.2604,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3350}) CREATE (b)-[r:Supply{max_supply: 2.2604, current_output: 2.2604,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3350}) CREATE (b)-[r:Supply{max_supply: 3.16456, current_output: 3.16456,level: 8}]->(g);
CREATE (n: Building {id: 3351, name:"building_subsistence_fishing_villageslevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:3351}) CREATE (b)-[r:Supply{max_supply: 0.0957, current_output: 0.0957,level: 10}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:3351}) CREATE (b)-[r:Supply{max_supply: 0.3828, current_output: 0.3828,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3351}) CREATE (b)-[r:Supply{max_supply: 0.04785, current_output: 0.04785,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3351}) CREATE (b)-[r:Supply{max_supply: 0.14355, current_output: 0.14355,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3351}) CREATE (b)-[r:Supply{max_supply: 0.0957, current_output: 0.0957,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3351}) CREATE (b)-[r:Supply{max_supply: 0.0957, current_output: 0.0957,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3351}) CREATE (b)-[r:Supply{max_supply: 0.0957, current_output: 0.0957,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3351}) CREATE (b)-[r:Supply{max_supply: 0.13398, current_output: 0.13398,level: 10}]->(g);
CREATE (n: Building {id: 3354, name:"building_subsistence_fishing_villageslevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:3354}) CREATE (b)-[r:Supply{max_supply: 3.7634, current_output: 3.7634,level: 8}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:3354}) CREATE (b)-[r:Supply{max_supply: 15.0536, current_output: 15.0536,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3354}) CREATE (b)-[r:Supply{max_supply: 1.8817, current_output: 1.8817,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3354}) CREATE (b)-[r:Supply{max_supply: 5.6451, current_output: 5.6451,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3354}) CREATE (b)-[r:Supply{max_supply: 3.7634, current_output: 3.7634,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3354}) CREATE (b)-[r:Supply{max_supply: 3.7634, current_output: 3.7634,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3354}) CREATE (b)-[r:Supply{max_supply: 3.7634, current_output: 3.7634,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3354}) CREATE (b)-[r:Supply{max_supply: 5.26876, current_output: 5.26876,level: 8}]->(g);
CREATE (n: Building {id: 3355, name:"building_subsistence_farmslevel", level:11});
MATCH (g: Goods{code: 7}), (b: Building{id:3355}) CREATE (b)-[r:Supply{max_supply: 27.4439, current_output: 27.4439,level: 11}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3355}) CREATE (b)-[r:Supply{max_supply: 5.48878, current_output: 5.48878,level: 11}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3355}) CREATE (b)-[r:Supply{max_supply: 5.48878, current_output: 5.48878,level: 11}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3355}) CREATE (b)-[r:Supply{max_supply: 5.48878, current_output: 5.48878,level: 11}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3355}) CREATE (b)-[r:Supply{max_supply: 5.48878, current_output: 5.48878,level: 11}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3355}) CREATE (b)-[r:Supply{max_supply: 5.48878, current_output: 5.48878,level: 11}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3355}) CREATE (b)-[r:Supply{max_supply: 7.68429, current_output: 7.68429,level: 11}]->(g);
CREATE (n: Building {id: 3451, name:"building_subsistence_rice_paddieslevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:3451}) CREATE (b)-[r:Supply{max_supply: 2.4516, current_output: 2.4516,level: 1}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3451}) CREATE (b)-[r:Supply{max_supply: 0.4086, current_output: 0.4086,level: 1}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3451}) CREATE (b)-[r:Supply{max_supply: 0.4086, current_output: 0.4086,level: 1}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3451}) CREATE (b)-[r:Supply{max_supply: 0.5448, current_output: 0.5448,level: 1}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3451}) CREATE (b)-[r:Supply{max_supply: 0.5448, current_output: 0.5448,level: 1}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3451}) CREATE (b)-[r:Supply{max_supply: 0.5448, current_output: 0.5448,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3451}) CREATE (b)-[r:Supply{max_supply: 0.8172, current_output: 0.8172,level: 1}]->(g);
CREATE (n: Building {id: 3591, name:"building_subsistence_orchardslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 3.0, current_output: 3.0,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 1.5, current_output: 1.5,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 4.5, current_output: 4.5,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 3.0, current_output: 3.0,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 3.0, current_output: 3.0,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 3.0, current_output: 3.0,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 7.98, current_output: 7.98,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3591}) CREATE (b)-[r:Supply{max_supply: 4.2, current_output: 4.2,level: 6}]->(g);
CREATE (n: Building {id: 3938, name:"building_subsistence_orchardslevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3938}) CREATE (b)-[r:Supply{max_supply: 1.7933, current_output: 1.7933,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3938}) CREATE (b)-[r:Supply{max_supply: 0.89665, current_output: 0.89665,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3938}) CREATE (b)-[r:Supply{max_supply: 2.68995, current_output: 2.68995,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3938}) CREATE (b)-[r:Supply{max_supply: 1.7933, current_output: 1.7933,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3938}) CREATE (b)-[r:Supply{max_supply: 1.7933, current_output: 1.7933,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3938}) CREATE (b)-[r:Supply{max_supply: 1.7933, current_output: 1.7933,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3938}) CREATE (b)-[r:Supply{max_supply: 4.77017, current_output: 4.77017,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3938}) CREATE (b)-[r:Supply{max_supply: 2.51062, current_output: 2.51062,level: 5}]->(g);
CREATE (n: Building {id: 3955, name:"building_subsistence_farmslevel", level:68});
MATCH (g: Goods{code: 7}), (b: Building{id:3955}) CREATE (b)-[r:Supply{max_supply: 169.915, current_output: 169.915,level: 68}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3955}) CREATE (b)-[r:Supply{max_supply: 33.983, current_output: 33.983,level: 68}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3955}) CREATE (b)-[r:Supply{max_supply: 33.983, current_output: 33.983,level: 68}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3955}) CREATE (b)-[r:Supply{max_supply: 33.983, current_output: 33.983,level: 68}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3955}) CREATE (b)-[r:Supply{max_supply: 33.983, current_output: 33.983,level: 68}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3955}) CREATE (b)-[r:Supply{max_supply: 33.983, current_output: 33.983,level: 68}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3955}) CREATE (b)-[r:Supply{max_supply: 47.5762, current_output: 47.5762,level: 68}]->(g);
CREATE (n: Building {id: 3961, name:"building_trade_centerlevel", level:141});
CREATE (n: Building {id: 4041, name:"building_conscription_centerlevel", level:11});
CREATE (n: Building {id: 4042, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4235, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4236, name:"building_conscription_centerlevel", level:8});
CREATE (n: Building {id: 4237, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4238, name:"building_conscription_centerlevel", level:10});
CREATE (n: Building {id: 4239, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4240, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4241, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4242, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4243, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 4244, name:"building_conscription_centerlevel", level:5});
CREATE (n: Building {id: 4281, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 4645, name:"building_conscription_centerlevel", level:3});
