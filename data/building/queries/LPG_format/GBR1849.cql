CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:64.77757597455745, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:49.55801986270991, pop_demand:0});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:76.59555338424802, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:111.83105538239691, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:27.748556391830434, pop_demand:9046.380047154382});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:21.98634765363037, pop_demand:716.3790429886083});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:30.306514778569877, pop_demand:828.0450132338487});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:32.411765415774795, pop_demand:630.2867381375303});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:27.75676168939441, pop_demand:201.49850290986245});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:41.99485180102116, pop_demand:4426.346606719059});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:39.61155985582001, pop_demand:3854.4095821741394});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:38.43703526307331, pop_demand:393.3288909103887});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:15.520149259497158, pop_demand:1117.9015918268556});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:14.087419906338658, pop_demand:341.62368589284785});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:75.75911756081808, pop_demand:0});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:70.0, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:51.40166840549617, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:32.536791150638074, pop_demand:100.01926459395678});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:41.304089976983306, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:56.80715717964449, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:68.84223368020237, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:19.07093545631391, pop_demand:6.65616774384414});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:69.81160311306833, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:56.553446154124956, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:66.8011639669806, pop_demand:143.87322163515634});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:19.25311539522075, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:41.2923578930331, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:38.28357396924668, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:111.7511139691621, pop_demand:603.5769800514589});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:40.64930004830268, pop_demand:877.119353593836});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:49.92886120546713, pop_demand:777.3620892228545});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:30.55085036042051, pop_demand:1303.4961030509955});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:55.11842978527874, pop_demand:42.589179348158325});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:85.5595253937969, pop_demand:1148.9261404777492});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:79.16179406292612, pop_demand:574.288826174091});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:43.102630204786564, pop_demand:124.84844734432288});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:66.89324478361742, pop_demand:431.23110985227356});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:87.5, pop_demand:1143.9274977875862});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:95.10652768624469, pop_demand:926.7259340379752});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:96.14505185583599, pop_demand:1115.4071450686554});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:350.0, pop_demand:1.0779669791666666});
CREATE (n: Building {id: 85, name:"building_government_administrationlevel", level:15});
MATCH (g: Goods{code: 14}), (b: Building{id:85}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 188.22889545926296, level: 15}]->(b);
CREATE (n: Building {id: 86, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:86}) CREATE (g)-[r:Demand{max_demand: 37.68956521739131, current_input: 11.812495774088307, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:86}) CREATE (g)-[r:Demand{max_demand: 75.37913043478262, current_input: 13.030781534550544, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:86}) CREATE (g)-[r:Demand{max_demand: 94.22391304347828, current_input: 90.5481984433377, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:86}) CREATE (g)-[r:Demand{max_demand: 18.844782608695656, current_input: 18.090848445351998, level: 3}]->(b);
CREATE (n: Building {id: 87, name:"building_universitylevel", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:87}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 31.37148257654382, level: 10}]->(b);
CREATE (n: Building {id: 88, name:"building_paper_millslevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:88}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 51.8609652010698, level: 10}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:88}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 0.0, level: 10}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:88}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 96.71098193876571, level: 10}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:88}) CREATE (b)-[r:Supply{max_supply: 1000.0000000000001, current_output: 379.9932344637411,level: 10}]->(g);
CREATE (n: Building {id: 89, name:"building_textile_millslevel", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:89}) CREATE (g)-[r:Demand{max_demand: 500.00000000000006, current_input: 156.70777449878355, level: 10}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:89}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 0.0, level: 10}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:89}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 0.0, level: 10}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:89}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 47.99962095875872, level: 10}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:89}) CREATE (b)-[r:Supply{max_supply: 800.0, current_output: 254.6815936345483,level: 10}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:89}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 63.67039840863708,level: 10}]->(g);
CREATE (n: Building {id: 90, name:"building_shipyardslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:90}) CREATE (g)-[r:Demand{max_demand: 70.782, current_input: 22.18417938914579, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:90}) CREATE (g)-[r:Demand{max_demand: 141.564, current_input: 24.47215225908082, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:90}) CREATE (g)-[r:Demand{max_demand: 70.782, current_input: 2.742806847008632, level: 5}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:90}) CREATE (g)-[r:Demand{max_demand: 35.391, current_input: 27.765029859883843, level: 5}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:90}) CREATE (b)-[r:Supply{max_supply: 247.737, current_output: 81.10648182477972,level: 5}]->(g);
CREATE (n: Building {id: 91, name:"building_furniture_manufacturieslevel", level:15});
MATCH (g: Goods{code: 9}), (b: Building{id:91}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 47.01233234963505, level: 15}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:91}) CREATE (g)-[r:Demand{max_demand: 600.0, current_input: 103.7219304021396, level: 15}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:91}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 5.812509212106113, level: 15}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:91}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 143.99886287627615, level: 15}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:91}) CREATE (b)-[r:Supply{max_supply: 1350.0, current_output: 501.1969208367425,level: 15}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:91}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 111.37709351927612,level: 15}]->(g);
CREATE (n: Building {id: 92, name:"building_naval_baselevel", level:25});
MATCH (g: Goods{code: 5}), (b: Building{id:92}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 10.199396943680766, level: 25}]->(b);
CREATE (n: Building {id: 93, name:"building_barrackslevel", level:25});
MATCH (g: Goods{code: 0}), (b: Building{id:93}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 24.267801545160008, level: 25}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:93}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 52.288067691020856, level: 25}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:93}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 35.11010041374687, level: 25}]->(b);
CREATE (n: Building {id: 94, name:"building_rye_farmlevel", level:8});
MATCH (g: Goods{code: 32}), (b: Building{id:94}) CREATE (g)-[r:Demand{max_demand: 39.3751968503937, current_input: 75.69083533536379, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:94}) CREATE (g)-[r:Demand{max_demand: 7.87503937007874, current_input: 7.559978095981631, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:94}) CREATE (b)-[r:Supply{max_supply: 393.752, current_output: 385.8754675175316,level: 8}]->(g);
CREATE (n: Building {id: 95, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:95}) CREATE (g)-[r:Demand{max_demand: 46.2745, current_input: 22.38710231319533, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:95}) CREATE (g)-[r:Demand{max_demand: 46.2745, current_input: 44.42316920112161, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:95}) CREATE (b)-[r:Supply{max_supply: 185.098, current_output: 133.62054302863388,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:95}) CREATE (b)-[r:Supply{max_supply: 23.13725, current_output: 16.702567878579234,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:95}) CREATE (b)-[r:Supply{max_supply: 115.68625, current_output: 83.51283939289617,level: 5}]->(g);
CREATE (n: Building {id: 96, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:96}) CREATE (g)-[r:Demand{max_demand: 9.782594059405941, current_input: 6.380400730748405, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:96}) CREATE (b)-[r:Supply{max_supply: 97.826, current_output: 63.80404605320982,level: 2}]->(g);
CREATE (n: Building {id: 97, name:"building_railwaylevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:97}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.59304826005349, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:97}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 26.72648377158924, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:97}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 23.535669966842285, level: 3}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:97}) CREATE (b)-[r:Supply{max_supply: 195.0, current_output: 120.13787556016676,level: 3}]->(g);
CREATE (n: Building {id: 98, name:"building_portlevel", level:6});
MATCH (g: Goods{code: 18}), (b: Building{id:98}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 19.566591515510137, level: 6}]->(b);
CREATE (n: Building {id: 99, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:99}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 50.19437212247012, level: 4}]->(b);
CREATE (n: Building {id: 100, name:"building_textile_millslevel", level:15});
MATCH (g: Goods{code: 9}), (b: Building{id:100}) CREATE (g)-[r:Demand{max_demand: 900.0, current_input: 282.0739940978103, level: 15}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:100}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 0.0, level: 15}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:100}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 71.99943143813807, level: 15}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:100}) CREATE (b)-[r:Supply{max_supply: 1500.0, current_output: 636.7039840863706,level: 15}]->(g);
CREATE (n: Building {id: 101, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:101}) CREATE (g)-[r:Demand{max_demand: 37.68956521739131, current_input: 11.812495774088307, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:101}) CREATE (g)-[r:Demand{max_demand: 75.37913043478262, current_input: 13.030781534550544, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:101}) CREATE (g)-[r:Demand{max_demand: 94.22391304347828, current_input: 90.5481984433377, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:101}) CREATE (g)-[r:Demand{max_demand: 18.844782608695656, current_input: 18.090848445351998, level: 3}]->(b);
CREATE (n: Building {id: 102, name:"building_arms_industrylevel", level:6});
MATCH (g: Goods{code: 24}), (b: Building{id:102}) CREATE (g)-[r:Demand{max_demand: 58.95000000000001, current_input: 56.65033562946701, level: 6}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:102}) CREATE (g)-[r:Demand{max_demand: 58.95000000000001, current_input: 2.284316120357703, level: 6}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:102}) CREATE (g)-[r:Demand{max_demand: 58.95000000000001, current_input: 48.87353205092429, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:102}) CREATE (g)-[r:Demand{max_demand: 29.475000000000005, current_input: 28.295776555188272, level: 6}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:102}) CREATE (b)-[r:Supply{max_supply: 147.375, current_output: 102.74983556945344,level: 6}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:102}) CREATE (b)-[r:Supply{max_supply: 88.42500000000001, current_output: 61.64990134167207,level: 6}]->(g);
CREATE (n: Building {id: 103, name:"building_munition_plantslevel", level:2});
MATCH (g: Goods{code: 25}), (b: Building{id:103}) CREATE (g)-[r:Demand{max_demand: 38.73719819819819, current_input: 17.114541538171874, level: 2}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:103}) CREATE (g)-[r:Demand{max_demand: 38.73719819819819, current_input: 56.59241547161468, level: 2}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:103}) CREATE (b)-[r:Supply{max_supply: 96.84299999999999, current_output: 69.8146779177859,level: 2}]->(g);
CREATE (n: Building {id: 104, name:"building_coal_minelevel", level:17});
MATCH (g: Goods{code: 33}), (b: Building{id:104}) CREATE (g)-[r:Demand{max_demand: 255.0, current_input: 244.79806688966946, level: 17}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:104}) CREATE (b)-[r:Supply{max_supply: 1020.0, current_output: 979.1922675586778,level: 17}]->(g);
CREATE (n: Building {id: 105, name:"building_iron_minelevel", level:11});
MATCH (g: Goods{code: 23}), (b: Building{id:105}) CREATE (g)-[r:Demand{max_demand: 165.0, current_input: 146.9956607437408, level: 11}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:105}) CREATE (g)-[r:Demand{max_demand: 165.0, current_input: 158.39874916390377, level: 11}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:105}) CREATE (b)-[r:Supply{max_supply: 660.0, current_output: 610.7888198152892,level: 11}]->(g);
CREATE (n: Building {id: 106, name:"building_rye_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:106}) CREATE (g)-[r:Demand{max_demand: 24.625500000000002, current_input: 47.33753262575408, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:106}) CREATE (g)-[r:Demand{max_demand: 4.925096491228071, current_input: 4.728055295285198, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:106}) CREATE (b)-[r:Supply{max_supply: 246.25500000000002, current_output: 241.3289665919913,level: 5}]->(g);
CREATE (n: Building {id: 107, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:107}) CREATE (g)-[r:Demand{max_demand: 27.762598214285713, current_input: 13.43124456673434, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:107}) CREATE (g)-[r:Demand{max_demand: 27.762598214285713, current_input: 26.651883822320517, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:107}) CREATE (b)-[r:Supply{max_supply: 111.05039285714285, current_output: 80.16625677810971,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:107}) CREATE (b)-[r:Supply{max_supply: 13.881294642857142, current_output: 10.020778874536935,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:107}) CREATE (b)-[r:Supply{max_supply: 69.4065, current_output: 50.10391370904535,level: 3}]->(g);
CREATE (n: Building {id: 108, name:"building_barrackslevel", level:18});
MATCH (g: Goods{code: 0}), (b: Building{id:108}) CREATE (g)-[r:Demand{max_demand: 18.0, current_input: 10.920510695322003, level: 18}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:108}) CREATE (g)-[r:Demand{max_demand: 18.0, current_input: 23.529630460959385, level: 18}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:108}) CREATE (g)-[r:Demand{max_demand: 18.0, current_input: 15.79954518618609, level: 18}]->(b);
CREATE (n: Building {id: 109, name:"building_naval_baselevel", level:10});
MATCH (g: Goods{code: 5}), (b: Building{id:109}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 4.079758777472306, level: 10}]->(b);
CREATE (n: Building {id: 110, name:"building_railwaylevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:110}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 4.3217471000891505, level: 5}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:110}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 44.5441396193154, level: 5}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:110}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 39.22611661140381, level: 5}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:110}) CREATE (b)-[r:Supply{max_supply: 325.0, current_output: 200.22979260027796,level: 5}]->(g);
CREATE (n: Building {id: 111, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:111}) CREATE (g)-[r:Demand{max_demand: 12.75, current_input: 8.315801394091809, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:111}) CREATE (b)-[r:Supply{max_supply: 127.50000000000001, current_output: 83.1580139409181,level: 3}]->(g);
CREATE (n: Building {id: 112, name:"building_portlevel", level:5});
MATCH (g: Goods{code: 18}), (b: Building{id:112}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 16.305492929591782, level: 5}]->(b);
CREATE (n: Building {id: 113, name:"building_paper_millslevel", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:113}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 25.9304826005349, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:113}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:113}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 48.355490969382856, level: 5}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:113}) CREATE (b)-[r:Supply{max_supply: 500.00000000000006, current_output: 189.9966172318705,level: 5}]->(g);
CREATE (n: Building {id: 114, name:"building_barrackslevel", level:17});
MATCH (g: Goods{code: 0}), (b: Building{id:114}) CREATE (g)-[r:Demand{max_demand: 27.2, current_input: 16.502105050708803, level: 17}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:114}) CREATE (g)-[r:Demand{max_demand: 27.2, current_input: 35.55588602989418, level: 17}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:114}) CREATE (g)-[r:Demand{max_demand: 27.2, current_input: 23.874868281347872, level: 17}]->(b);
CREATE (n: Building {id: 115, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:115}) CREATE (g)-[r:Demand{max_demand: 18.51159459459459, current_input: 8.955709130721731, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:115}) CREATE (g)-[r:Demand{max_demand: 18.51159459459459, current_input: 17.770990477654944, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:115}) CREATE (b)-[r:Supply{max_supply: 74.04639639639639, current_output: 53.453412223794764,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:115}) CREATE (b)-[r:Supply{max_supply: 9.255792792792793, current_output: 6.681671650333816,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:115}) CREATE (b)-[r:Supply{max_supply: 46.278999999999996, current_output: 33.408384265751906,level: 2}]->(g);
CREATE (n: Building {id: 116, name:"building_coal_minelevel", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:116}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 57.599545150510465, level: 4}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:116}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 230.39818060204186,level: 4}]->(g);
CREATE (n: Building {id: 117, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:117}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 14.399886287627616, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:117}) CREATE (b)-[r:Supply{max_supply: 179.99999999999997, current_output: 172.79863545153137,level: 3}]->(g);
CREATE (n: Building {id: 118, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:118}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8643494200178301, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:118}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.908827923863079, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:118}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.845223322280762, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:118}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 40.045958520055585,level: 1}]->(g);
CREATE (n: Building {id: 119, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:119}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 120, name:"building_government_administrationlevel", level:5});
MATCH (g: Goods{code: 14}), (b: Building{id:120}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 62.742965153087646, level: 5}]->(b);
CREATE (n: Building {id: 121, name:"building_tooling_workshopslevel", level:14});
MATCH (g: Goods{code: 10}), (b: Building{id:121}) CREATE (g)-[r:Demand{max_demand: 420.0, current_input: 72.60535128149772, level: 14}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:121}) CREATE (g)-[r:Demand{max_demand: 280.0, current_input: 232.13891389751996, level: 14}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:121}) CREATE (b)-[r:Supply{max_supply: 1120.0, current_output: 561.0849628370369,level: 14}]->(g);
CREATE (n: Building {id: 122, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:122}) CREATE (g)-[r:Demand{max_demand: 25.12626086956522, current_input: 7.87496084469087, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:122}) CREATE (g)-[r:Demand{max_demand: 50.25252173913044, current_input: 8.687147603930157, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:122}) CREATE (g)-[r:Demand{max_demand: 62.81565217391305, current_input: 60.365187081188, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:122}) CREATE (g)-[r:Demand{max_demand: 12.56313043478261, current_input: 12.060509978500217, level: 2}]->(b);
CREATE (n: Building {id: 123, name:"building_steel_millslevel", level:10});
MATCH (g: Goods{code: 23}), (b: Building{id:123}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 267.2648377158924, level: 10}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:123}) CREATE (g)-[r:Demand{max_demand: 400.0, current_input: 384.3958312432027, level: 10}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:123}) CREATE (b)-[r:Supply{max_supply: 650.0, current_output: 601.8585204106522,level: 10}]->(g);
CREATE (n: Building {id: 124, name:"building_motor_industrylevel", level:8});
MATCH (g: Goods{code: 30}), (b: Building{id:124}) CREATE (g)-[r:Demand{max_demand: 240.00000000000003, current_input: 198.97621191215998, level: 8}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:124}) CREATE (b)-[r:Supply{max_supply: 320.0, current_output: 265.30161588287996,level: 8}]->(g);
CREATE (n: Building {id: 125, name:"building_barrackslevel", level:19});
MATCH (g: Goods{code: 0}), (b: Building{id:125}) CREATE (g)-[r:Demand{max_demand: 19.0, current_input: 11.527205733951003, level: 19}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:125}) CREATE (g)-[r:Demand{max_demand: 19.0, current_input: 24.836832153234905, level: 19}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:125}) CREATE (g)-[r:Demand{max_demand: 19.0, current_input: 16.677297696529763, level: 19}]->(b);
CREATE (n: Building {id: 126, name:"building_naval_baselevel", level:15});
MATCH (g: Goods{code: 5}), (b: Building{id:126}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.11963816620846, level: 15}]->(b);
CREATE (n: Building {id: 127, name:"building_coal_minelevel", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:127}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 143.99886287627615, level: 10}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:127}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 575.9954515051046,level: 10}]->(g);
CREATE (n: Building {id: 128, name:"building_iron_minelevel", level:5});
MATCH (g: Goods{code: 23}), (b: Building{id:128}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 66.8162094289731, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:128}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 71.99943143813807, level: 5}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:128}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 277.63128173422234,level: 5}]->(g);
CREATE (n: Building {id: 129, name:"building_rye_farmlevel", level:4});
MATCH (g: Goods{code: 32}), (b: Building{id:129}) CREATE (g)-[r:Demand{max_demand: 18.767592920353984, current_input: 36.07689355237977, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:129}) CREATE (g)-[r:Demand{max_demand: 3.7535132743362833, current_input: 3.6033442886362184, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:129}) CREATE (b)-[r:Supply{max_supply: 187.67600000000002, current_output: 183.92176863056002,level: 4}]->(g);
CREATE (n: Building {id: 130, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:130}) CREATE (g)-[r:Demand{max_demand: 27.762598214285713, current_input: 13.43124456673434, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:130}) CREATE (g)-[r:Demand{max_demand: 27.762598214285713, current_input: 26.651883822320517, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:130}) CREATE (b)-[r:Supply{max_supply: 111.05039285714285, current_output: 80.16625677810971,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:130}) CREATE (b)-[r:Supply{max_supply: 13.881294642857142, current_output: 10.020778874536935,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:130}) CREATE (b)-[r:Supply{max_supply: 69.4065, current_output: 50.10391370904535,level: 3}]->(g);
CREATE (n: Building {id: 131, name:"building_railwaylevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:131}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.18609652010698, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:131}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 53.45296754317848, level: 6}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:131}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 47.07133993368457, level: 6}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:131}) CREATE (b)-[r:Supply{max_supply: 390.0, current_output: 240.27575112033352,level: 6}]->(g);
CREATE (n: Building {id: 132, name:"building_portlevel", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:132}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 13.044394343673424, level: 4}]->(b);
CREATE (n: Building {id: 133, name:"building_barrackslevel", level:10});
MATCH (g: Goods{code: 0}), (b: Building{id:133}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.066950386290002, level: 10}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:133}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 13.072016922755214, level: 10}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:133}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.777525103436718, level: 10}]->(b);
CREATE (n: Building {id: 134, name:"building_logging_camplevel", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:134}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 14.399886287627616, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:134}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 115.19909030102093,level: 3}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:134}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 28.799772575255233,level: 3}]->(g);
CREATE (n: Building {id: 135, name:"building_whaling_stationlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:135}) CREATE (g)-[r:Demand{max_demand: 8.438, current_input: 5.503429973595819, level: 2}]->(b);
MATCH (g: Goods{code: 28}), (b: Building{id:135}) CREATE (b)-[r:Supply{max_supply: 33.752, current_output: 22.013719894383275,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:135}) CREATE (b)-[r:Supply{max_supply: 16.876, current_output: 11.006859947191638,level: 2}]->(g);
CREATE (n: Building {id: 136, name:"building_fishing_wharflevel", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:136}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 13.044394343673424, level: 4}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:136}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 130.44394343673423,level: 4}]->(g);
CREATE (n: Building {id: 137, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:137}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 75.29155818370518, level: 6}]->(b);
CREATE (n: Building {id: 138, name:"building_arms_industrylevel", level:8});
MATCH (g: Goods{code: 24}), (b: Building{id:138}) CREATE (g)-[r:Demand{max_demand: 78.57119672131148, current_input: 75.50610118865433, level: 8}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:138}) CREATE (g)-[r:Demand{max_demand: 78.57119672131148, current_input: 3.044638698325497, level: 8}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:138}) CREATE (g)-[r:Demand{max_demand: 78.57119672131148, current_input: 65.14082953754867, level: 8}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:138}) CREATE (g)-[r:Demand{max_demand: 39.28559836065574, current_input: 37.71387660899017, level: 8}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:138}) CREATE (b)-[r:Supply{max_supply: 196.428, current_output: 136.9495823663213,level: 8}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:138}) CREATE (b)-[r:Supply{max_supply: 117.85679508196722, current_output: 82.1697459909408,level: 8}]->(g);
CREATE (n: Building {id: 139, name:"building_textile_millslevel", level:17});
MATCH (g: Goods{code: 9}), (b: Building{id:139}) CREATE (g)-[r:Demand{max_demand: 850.0, current_input: 266.403216647932, level: 17}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:139}) CREATE (g)-[r:Demand{max_demand: 170.0, current_input: 0.0, level: 17}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:139}) CREATE (g)-[r:Demand{max_demand: 170.0, current_input: 0.0, level: 17}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:139}) CREATE (g)-[r:Demand{max_demand: 85.0, current_input: 81.59935562988981, level: 17}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:139}) CREATE (b)-[r:Supply{max_supply: 1360.0, current_output: 432.958709178732,level: 17}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:139}) CREATE (b)-[r:Supply{max_supply: 340.0, current_output: 108.239677294683,level: 17}]->(g);
CREATE (n: Building {id: 140, name:"building_furniture_manufacturieslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:140}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 15.670777449878354, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:140}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 25.9304826005349, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:140}) CREATE (g)-[r:Demand{max_demand: 100.00000000000001, current_input: 3.8750061414040755, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:140}) CREATE (g)-[r:Demand{max_demand: 100.00000000000001, current_input: 95.99924191751747, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:140}) CREATE (b)-[r:Supply{max_supply: 450.00000000000006, current_output: 167.0656402789142,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:140}) CREATE (b)-[r:Supply{max_supply: 200.00000000000003, current_output: 74.25139567951743,level: 5}]->(g);
CREATE (n: Building {id: 141, name:"building_chemical_plantslevel", level:2});
MATCH (g: Goods{code: 22}), (b: Building{id:141}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 58.02658916325941, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:141}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 26.726483771589233, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:141}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 19.219791562160133, level: 2}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:141}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 140.9491094940986,level: 2}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:141}) CREATE (b)-[r:Supply{max_supply: 39.99999999999999, current_output: 37.58642919842628,level: 2}]->(g);
CREATE (n: Building {id: 142, name:"building_coal_minelevel", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:142}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 115.19909030102093, level: 8}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:142}) CREATE (b)-[r:Supply{max_supply: 480.0, current_output: 460.7963612040837,level: 8}]->(g);
CREATE (n: Building {id: 143, name:"building_iron_minelevel", level:9});
MATCH (g: Goods{code: 23}), (b: Building{id:143}) CREATE (g)-[r:Demand{max_demand: 135.0, current_input: 120.26917697215157, level: 9}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:143}) CREATE (g)-[r:Demand{max_demand: 135.0, current_input: 129.59897658864855, level: 9}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:143}) CREATE (b)-[r:Supply{max_supply: 540.0, current_output: 499.73630712160025,level: 9}]->(g);
CREATE (n: Building {id: 144, name:"building_rye_farmlevel", level:10});
MATCH (g: Goods{code: 32}), (b: Building{id:144}) CREATE (g)-[r:Demand{max_demand: 49.22849579831933, current_input: 94.63180548495417, level: 10}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:144}) CREATE (g)-[r:Demand{max_demand: 9.845697478991596, current_input: 9.451794941324058, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:144}) CREATE (b)-[r:Supply{max_supply: 492.285, current_output: 482.4374340368254,level: 10}]->(g);
CREATE (n: Building {id: 145, name:"building_livestock_ranchlevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:145}) CREATE (g)-[r:Demand{max_demand: 46.276, current_input: 22.387827996962198, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:145}) CREATE (g)-[r:Demand{max_demand: 46.276, current_input: 44.42460918975038, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:145}) CREATE (b)-[r:Supply{max_supply: 185.104, current_output: 133.62487437342517,level: 5}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:145}) CREATE (b)-[r:Supply{max_supply: 23.138, current_output: 16.703109296678146,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:145}) CREATE (b)-[r:Supply{max_supply: 115.69, current_output: 83.51554648339072,level: 5}]->(g);
CREATE (n: Building {id: 146, name:"building_railwaylevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:146}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.18609652010698, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:146}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 53.45296754317848, level: 6}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:146}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 47.07133993368457, level: 6}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:146}) CREATE (b)-[r:Supply{max_supply: 390.0, current_output: 240.27575112033352,level: 6}]->(g);
CREATE (n: Building {id: 147, name:"building_barrackslevel", level:28});
MATCH (g: Goods{code: 0}), (b: Building{id:147}) CREATE (g)-[r:Demand{max_demand: 44.8, current_input: 27.17993773057921, level: 28}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:147}) CREATE (g)-[r:Demand{max_demand: 44.8, current_input: 58.56263581394336, level: 28}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:147}) CREATE (g)-[r:Demand{max_demand: 44.8, current_input: 39.3233124633965, level: 28}]->(b);
CREATE (n: Building {id: 148, name:"building_portlevel", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:148}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 13.044394343673424, level: 4}]->(b);
CREATE (n: Building {id: 149, name:"building_government_administrationlevel", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:149}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 50.19437212247012, level: 4}]->(b);
CREATE (n: Building {id: 150, name:"building_universitylevel", level:8});
MATCH (g: Goods{code: 14}), (b: Building{id:150}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 25.097186061235057, level: 8}]->(b);
CREATE (n: Building {id: 151, name:"building_furniture_manufacturieslevel", level:16});
MATCH (g: Goods{code: 9}), (b: Building{id:151}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 50.14648783961073, level: 16}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:151}) CREATE (g)-[r:Demand{max_demand: 800.0, current_input: 138.29590720285282, level: 16}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:151}) CREATE (g)-[r:Demand{max_demand: 160.0, current_input: 153.59878706802792, level: 16}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:151}) CREATE (b)-[r:Supply{max_supply: 1760.0, current_output: 848.4830066101005,level: 16}]->(g);
CREATE (n: Building {id: 152, name:"building_glassworkslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:152}) CREATE (g)-[r:Demand{max_demand: 120.00000000000001, current_input: 20.744386080427926, level: 6}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:152}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 0.0, level: 6}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:152}) CREATE (g)-[r:Demand{max_demand: 60.00000000000001, current_input: 26.508692937376047, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:152}) CREATE (b)-[r:Supply{max_supply: 120.00000000000001, current_output: 24.58725731839334,level: 6}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:152}) CREATE (b)-[r:Supply{max_supply: 240.00000000000003, current_output: 49.17451463678668,level: 6}]->(g);
CREATE (n: Building {id: 153, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:153}) CREATE (g)-[r:Demand{max_demand: 290.368, current_input: 140.47689601136483, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:153}) CREATE (g)-[r:Demand{max_demand: 36.296, current_input: 3.8845252122299216, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:153}) CREATE (g)-[r:Demand{max_demand: 199.62800000000004, current_input: 83.37680611017188, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:153}) CREATE (b)-[r:Supply{max_supply: 290.368, current_output: 97.6094840170908,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:153}) CREATE (b)-[r:Supply{max_supply: 362.96000000000004, current_output: 122.01185502136352,level: 4}]->(g);
CREATE (n: Building {id: 154, name:"building_iron_minelevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:154}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 26.72648377158924, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:154}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 28.799772575255233, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:154}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 111.05251269368894,level: 2}]->(g);
CREATE (n: Building {id: 155, name:"building_shipyardslevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:155}) CREATE (g)-[r:Demand{max_demand: 29.756396694214878, current_input: 9.326117406106745, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:155}) CREATE (g)-[r:Demand{max_demand: 59.512793388429756, current_input: 10.287969689786042, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:155}) CREATE (g)-[r:Demand{max_demand: 29.756396694214878, current_input: 1.1530621993613857, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:155}) CREATE (g)-[r:Demand{max_demand: 14.878198347107439, current_input: 11.672278866624637, level: 2}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:155}) CREATE (b)-[r:Supply{max_supply: 37.19549586776859, current_output: 12.177413182378258,level: 2}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:155}) CREATE (b)-[r:Supply{max_supply: 66.95189256198347, current_output: 21.919343728280865,level: 2}]->(g);
CREATE (n: Building {id: 156, name:"building_barrackslevel", level:16});
MATCH (g: Goods{code: 0}), (b: Building{id:156}) CREATE (g)-[r:Demand{max_demand: 25.6, current_input: 15.531392988902406, level: 16}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:156}) CREATE (g)-[r:Demand{max_demand: 25.6, current_input: 33.46436332225335, level: 16}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:156}) CREATE (g)-[r:Demand{max_demand: 25.6, current_input: 22.470464264797997, level: 16}]->(b);
CREATE (n: Building {id: 157, name:"building_naval_baselevel", level:25});
MATCH (g: Goods{code: 5}), (b: Building{id:157}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 10.199396943680766, level: 25}]->(b);
CREATE (n: Building {id: 158, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:158}) CREATE (g)-[r:Demand{max_demand: 9.382198347107439, current_input: 18.035374727721038, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:158}) CREATE (g)-[r:Demand{max_demand: 1.8764380165289258, current_input: 1.8013662709198695, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:158}) CREATE (b)-[r:Supply{max_supply: 93.822, current_output: 91.94520437592661,level: 2}]->(g);
CREATE (n: Building {id: 159, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:159}) CREATE (g)-[r:Demand{max_demand: 27.76259836065574, current_input: 13.431244637546575, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:159}) CREATE (g)-[r:Demand{max_demand: 27.76259836065574, current_input: 26.651883962834635, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:159}) CREATE (b)-[r:Supply{max_supply: 111.05039344262296, current_output: 80.16625720076242,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:159}) CREATE (b)-[r:Supply{max_supply: 13.881295081967213, current_output: 10.020779191526454,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:159}) CREATE (b)-[r:Supply{max_supply: 69.4065, current_output: 50.10391370904535,level: 3}]->(g);
CREATE (n: Building {id: 160, name:"building_fishing_wharflevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:160}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.783295757755068, level: 3}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:160}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 97.83295757755069,level: 3}]->(g);
CREATE (n: Building {id: 161, name:"building_railwaylevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:161}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.7286988400356602, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:161}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.817655847726158, level: 2}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:161}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 15.690446644561524, level: 2}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:161}) CREATE (b)-[r:Supply{max_supply: 130.0, current_output: 80.09191704011117,level: 2}]->(g);
CREATE (n: Building {id: 162, name:"building_portlevel", level:5});
MATCH (g: Goods{code: 18}), (b: Building{id:162}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 16.305492929591782, level: 5}]->(b);
CREATE (n: Building {id: 163, name:"building_government_administrationlevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:163}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 75.29155818370518, level: 6}]->(b);
CREATE (n: Building {id: 164, name:"building_tooling_workshopslevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:164}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 31.11657912064188, level: 6}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:164}) CREATE (g)-[r:Demand{max_demand: 120.00000000000001, current_input: 99.48810595607999, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:164}) CREATE (b)-[r:Supply{max_supply: 480.00000000000006, current_output: 240.46498407301584,level: 6}]->(g);
CREATE (n: Building {id: 165, name:"building_textile_millslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:165}) CREATE (g)-[r:Demand{max_demand: 250.00000000000003, current_input: 78.35388724939178, level: 5}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:165}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:165}) CREATE (g)-[r:Demand{max_demand: 50.00000000000001, current_input: 0.0, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:165}) CREATE (g)-[r:Demand{max_demand: 25.000000000000004, current_input: 23.999810479379367, level: 5}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:165}) CREATE (b)-[r:Supply{max_supply: 400.00000000000006, current_output: 127.3407968172742,level: 5}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:165}) CREATE (b)-[r:Supply{max_supply: 100.00000000000001, current_output: 31.83519920431855,level: 5}]->(g);
CREATE (n: Building {id: 166, name:"building_shipyardslevel", level:4});
MATCH (g: Goods{code: 9}), (b: Building{id:166}) CREATE (g)-[r:Demand{max_demand: 22.316, current_input: 6.994181391429706, level: 4}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:166}) CREATE (g)-[r:Demand{max_demand: 44.632, current_input: 7.7155286628471575, level: 4}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:166}) CREATE (b)-[r:Supply{max_supply: 39.053, current_output: 9.495452507496623,level: 4}]->(g);
CREATE (n: Building {id: 167, name:"building_barrackslevel", level:14});
MATCH (g: Goods{code: 0}), (b: Building{id:167}) CREATE (g)-[r:Demand{max_demand: 14.0, current_input: 8.493730540806002, level: 14}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:167}) CREATE (g)-[r:Demand{max_demand: 14.0, current_input: 18.300823691857296, level: 14}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:167}) CREATE (g)-[r:Demand{max_demand: 14.0, current_input: 12.288535144811405, level: 14}]->(b);
CREATE (n: Building {id: 168, name:"building_naval_baselevel", level:25});
MATCH (g: Goods{code: 5}), (b: Building{id:168}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 10.199396943680766, level: 25}]->(b);
CREATE (n: Building {id: 169, name:"building_wheat_farmlevel", level:5});
MATCH (g: Goods{code: 32}), (b: Building{id:169}) CREATE (g)-[r:Demand{max_demand: 23.454245614035088, current_input: 45.08603345990276, level: 5}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:169}) CREATE (g)-[r:Demand{max_demand: 4.690842105263158, current_input: 4.503172860600347, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:169}) CREATE (b)-[r:Supply{max_supply: 164.17974561403508, current_output: 160.89552839280867,level: 5}]->(g);
MATCH (g: Goods{code: 39}), (b: Building{id:169}) CREATE (b)-[r:Supply{max_supply: 37.52679824561404, current_output: 36.776120038659876,level: 5}]->(g);
CREATE (n: Building {id: 170, name:"building_livestock_ranchlevel", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:170}) CREATE (g)-[r:Demand{max_demand: 27.764696428571426, current_input: 13.432259660098705, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:170}) CREATE (g)-[r:Demand{max_demand: 27.764696428571426, current_input: 26.653898092128607, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:170}) CREATE (b)-[r:Supply{max_supply: 111.05879464285714, current_output: 80.17232194990818,level: 3}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:170}) CREATE (b)-[r:Supply{max_supply: 13.882348214285713, current_output: 10.021539438056829,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:170}) CREATE (b)-[r:Supply{max_supply: 69.41174999999998, current_output: 50.10770363573769,level: 3}]->(g);
CREATE (n: Building {id: 171, name:"building_sulfur_minelevel", level:6});
MATCH (g: Goods{code: 23}), (b: Building{id:171}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 80.17945131476772, level: 6}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:171}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 86.3993177257657, level: 6}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:171}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 333.15753808106683,level: 6}]->(g);
CREATE (n: Building {id: 172, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:172}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8643494200178301, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:172}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.908827923863079, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:172}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.845223322280762, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:172}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 40.045958520055585,level: 1}]->(g);
CREATE (n: Building {id: 173, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:173}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.522197171836712, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:173}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 65.22197171836712,level: 2}]->(g);
CREATE (n: Building {id: 174, name:"building_portlevel", level:5});
MATCH (g: Goods{code: 18}), (b: Building{id:174}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 16.305492929591782, level: 5}]->(b);
CREATE (n: Building {id: 175, name:"building_government_administrationlevel", level:3});
MATCH (g: Goods{code: 14}), (b: Building{id:175}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 37.64577909185259, level: 3}]->(b);
CREATE (n: Building {id: 176, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:176}) CREATE (g)-[r:Demand{max_demand: 25.12626086956522, current_input: 7.87496084469087, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:176}) CREATE (g)-[r:Demand{max_demand: 50.25252173913044, current_input: 8.687147603930157, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:176}) CREATE (g)-[r:Demand{max_demand: 62.81565217391305, current_input: 60.365187081188, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:176}) CREATE (g)-[r:Demand{max_demand: 12.56313043478261, current_input: 12.060509978500217, level: 2}]->(b);
CREATE (n: Building {id: 177, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:177}) CREATE (g)-[r:Demand{max_demand: 160.00000000000003, current_input: 77.40626846559668, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:177}) CREATE (g)-[r:Demand{max_demand: 160.00000000000003, current_input: 66.8257407659622, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:177}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 63.101504038807,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:177}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 108.17400692366914,level: 4}]->(g);
CREATE (n: Building {id: 178, name:"building_glassworkslevel", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:178}) CREATE (g)-[r:Demand{max_demand: 140.00000000000003, current_input: 24.201783760499247, level: 7}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:178}) CREATE (g)-[r:Demand{max_demand: 210.0, current_input: 0.0, level: 7}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:178}) CREATE (g)-[r:Demand{max_demand: 70.00000000000001, current_input: 30.926808426938727, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:178}) CREATE (b)-[r:Supply{max_supply: 140.00000000000003, current_output: 28.68513353812557,level: 7}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:178}) CREATE (b)-[r:Supply{max_supply: 280.00000000000006, current_output: 57.37026707625114,level: 7}]->(g);
CREATE (n: Building {id: 179, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:179}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 9.599924191751743, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:179}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 115.19909030102093,level: 2}]->(g);
CREATE (n: Building {id: 180, name:"building_iron_minelevel", level:4});
MATCH (g: Goods{code: 23}), (b: Building{id:180}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 53.45296754317847, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:180}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 57.59954515051045, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:180}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 222.10502538737782,level: 4}]->(g);
CREATE (n: Building {id: 181, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:181}) CREATE (g)-[r:Demand{max_demand: 9.843297297297296, current_input: 18.92174400340329, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:181}) CREATE (g)-[r:Demand{max_demand: 1.9686576576576575, current_input: 1.889896427302507, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:181}) CREATE (b)-[r:Supply{max_supply: 98.43299999999999, current_output: 96.46396689833496,level: 2}]->(g);
CREATE (n: Building {id: 182, name:"building_livestock_ranchlevel", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:182}) CREATE (g)-[r:Demand{max_demand: 9.2549, current_input: 4.477420462639065, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:182}) CREATE (g)-[r:Demand{max_demand: 9.2549, current_input: 8.884633840224321, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:182}) CREATE (b)-[r:Supply{max_supply: 37.0196, current_output: 26.724108605726773,level: 1}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:182}) CREATE (b)-[r:Supply{max_supply: 4.6274454545454535, current_output: 3.3405102943940346,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:182}) CREATE (b)-[r:Supply{max_supply: 23.137245454545454, current_output: 16.70256459725742,level: 1}]->(g);
CREATE (n: Building {id: 183, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:183}) CREATE (g)-[r:Demand{max_demand: 8.5, current_input: 5.543867596061206, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:183}) CREATE (b)-[r:Supply{max_supply: 85.0, current_output: 55.438675960612066,level: 2}]->(g);
CREATE (n: Building {id: 184, name:"building_naval_baselevel", level:15});
MATCH (g: Goods{code: 5}), (b: Building{id:184}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.11963816620846, level: 15}]->(b);
CREATE (n: Building {id: 185, name:"building_barrackslevel", level:15});
MATCH (g: Goods{code: 0}), (b: Building{id:185}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.100425579435003, level: 15}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:185}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 19.608025384132823, level: 15}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:185}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 13.166287655155076, level: 15}]->(b);
CREATE (n: Building {id: 186, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:186}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8643494200178301, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:186}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.908827923863079, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:186}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.845223322280762, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:186}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 40.045958520055585,level: 1}]->(g);
CREATE (n: Building {id: 187, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:187}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.783295757755068, level: 3}]->(b);
CREATE (n: Building {id: 188, name:"building_livestock_ranchlevel", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:188}) CREATE (g)-[r:Demand{max_demand: 18.50779279279279, current_input: 8.953869860153462, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:188}) CREATE (g)-[r:Demand{max_demand: 18.50779279279279, current_input: 17.767340776746007, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:188}) CREATE (b)-[r:Supply{max_supply: 74.03119819819818, current_output: 53.442440784361054,level: 2}]->(g);
MATCH (g: Goods{code: 32}), (b: Building{id:188}) CREATE (b)-[r:Supply{max_supply: 9.253891891891891, current_output: 6.6802994074645135,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:188}) CREATE (b)-[r:Supply{max_supply: 46.26949549549549, current_output: 33.4015230514054,level: 2}]->(g);
CREATE (n: Building {id: 189, name:"building_barrackslevel", level:6});
MATCH (g: Goods{code: 0}), (b: Building{id:189}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 3.640170231774001, level: 6}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:189}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 7.8432101536531285, level: 6}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:189}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 5.26651506206203, level: 6}]->(b);
CREATE (n: Building {id: 190, name:"building_logging_camplevel", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:190}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 9.599924191751743, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:190}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 115.19909030102093,level: 2}]->(g);
CREATE (n: Building {id: 191, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:191}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.522197171836712, level: 2}]->(b);
CREATE (n: Building {id: 192, name:"building_rye_farmlevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:192}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.9599924191751744, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:192}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 9.599924191751745,level: 1}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:192}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 14.399886287627616,level: 1}]->(g);
CREATE (n: Building {id: 193, name:"building_barrackslevel", level:3});
MATCH (g: Goods{code: 0}), (b: Building{id:193}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 1.8200851158870004, level: 3}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:193}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.9216050768265642, level: 3}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:193}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 2.633257531031015, level: 3}]->(b);
CREATE (n: Building {id: 194, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:194}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.039879388736153, level: 5}]->(b);
CREATE (n: Building {id: 195, name:"building_portlevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:195}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.522197171836712, level: 2}]->(b);
CREATE (n: Building {id: 196, name:"building_universitylevel", level:1});
MATCH (g: Goods{code: 14}), (b: Building{id:196}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.1371482576543825, level: 1}]->(b);
CREATE (n: Building {id: 197, name:"building_lead_minelevel", level:3});
MATCH (g: Goods{code: 23}), (b: Building{id:197}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 26.72648377158924, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:197}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 28.799772575255233, level: 3}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:197}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 111.05251269368894,level: 3}]->(g);
CREATE (n: Building {id: 198, name:"building_food_industrylevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:198}) CREATE (g)-[r:Demand{max_demand: 160.00000000000003, current_input: 77.40626846559668, level: 4}]->(b);
MATCH (g: Goods{code: 42}), (b: Building{id:198}) CREATE (g)-[r:Demand{max_demand: 160.00000000000003, current_input: 66.8257407659622, level: 4}]->(b);
MATCH (g: Goods{code: 11}), (b: Building{id:198}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 63.101504038807,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:198}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 108.17400692366914,level: 4}]->(g);
CREATE (n: Building {id: 199, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:199}) CREATE (g)-[r:Demand{max_demand: 9.845495495495495, current_input: 18.925969593906025, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:199}) CREATE (g)-[r:Demand{max_demand: 1.969099099099099, current_input: 1.8903202077398007, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:199}) CREATE (b)-[r:Supply{max_supply: 59.072999999999986, current_output: 57.89131608896752,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:199}) CREATE (b)-[r:Supply{max_supply: 29.536495495495494, current_output: 28.945653630086426,level: 2}]->(g);
CREATE (n: Building {id: 200, name:"building_naval_baselevel", level:15});
MATCH (g: Goods{code: 5}), (b: Building{id:200}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 6.11963816620846, level: 15}]->(b);
CREATE (n: Building {id: 201, name:"building_barrackslevel", level:5});
MATCH (g: Goods{code: 0}), (b: Building{id:201}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.033475193145001, level: 5}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:201}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 6.536008461377607, level: 5}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:201}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.388762551718359, level: 5}]->(b);
CREATE (n: Building {id: 202, name:"building_portlevel", level:3});
MATCH (g: Goods{code: 18}), (b: Building{id:202}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 9.783295757755068, level: 3}]->(b);
CREATE (n: Building {id: 203, name:"building_rye_farmlevel", level:2});
MATCH (g: Goods{code: 32}), (b: Building{id:203}) CREATE (g)-[r:Demand{max_demand: 9.843297297297296, current_input: 18.92174400340329, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:203}) CREATE (g)-[r:Demand{max_demand: 1.9686576576576575, current_input: 1.889896427302507, level: 2}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:203}) CREATE (b)-[r:Supply{max_supply: 59.05979279279279, current_output: 57.878373075965236,level: 2}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:203}) CREATE (b)-[r:Supply{max_supply: 29.52989189189189, current_output: 28.939182123585276,level: 2}]->(g);
CREATE (n: Building {id: 204, name:"building_barrackslevel", level:2});
MATCH (g: Goods{code: 0}), (b: Building{id:204}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.2133900772580004, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:204}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.614403384551043, level: 2}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:204}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.7555050206873435, level: 2}]->(b);
CREATE (n: Building {id: 205, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:205}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 134218296, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:134218296}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.7286988400356602, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:134218296}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.817655847726158, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:134218296}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.070235070594534, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:134218296}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 54.63622189430194,level: 2}]->(g);
CREATE (n: Building {id: 616, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:616}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:616}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 32.61098585918356,level: 1}]->(g);
CREATE (n: Building {id: 617, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:617}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.039879388736153, level: 5}]->(b);
CREATE (n: Building {id: 618, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:618}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 775, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:775}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.039879388736153, level: 5}]->(b);
CREATE (n: Building {id: 776, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:776}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 16778022, name:"building_munition_plantslevel", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:16778022}) CREATE (g)-[r:Demand{max_demand: 19.4312, current_input: 8.584928570079024, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:16778022}) CREATE (g)-[r:Demand{max_demand: 19.4312, current_input: 28.387663400064596, level: 1}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:16778022}) CREATE (b)-[r:Supply{max_supply: 48.578, current_output: 35.02016071259878,level: 1}]->(g);
CREATE (n: Building {id: 16778068, name:"building_tooling_workshopslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:16778068}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 15.558289560320938, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16778068}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 49.74405297803999, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16778068}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 120.23249203650789,level: 3}]->(g);
CREATE (n: Building {id: 1261, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1261}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 16778534, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:16778534}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 26.726483771589233, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16778534}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 57.659374686480405, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16778534}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 83.33425667224415,level: 1}]->(g);
CREATE (n: Building {id: 1577, name:"building_fishing_wharflevel", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:1577}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.522197171836712, level: 2}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:1577}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 65.22197171836712,level: 2}]->(g);
CREATE (n: Building {id: 1583, name:"building_naval_baselevel", level:5});
MATCH (g: Goods{code: 5}), (b: Building{id:1583}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 2.039879388736153, level: 5}]->(b);
CREATE (n: Building {id: 1584, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1584}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 1651, name:"building_portlevel", level:1});
CREATE (n: Building {id: 1679, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1679}) CREATE (b)-[r:Supply{max_supply: 26.535, current_output: 26.535,level: 1}]->(g);
CREATE (n: Building {id: 1680, name:"building_sugar_plantationlevel", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:1680}) CREATE (b)-[r:Supply{max_supply: 26.5764, current_output: 26.5764,level: 1}]->(g);
CREATE (n: Building {id: 1681, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1681}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 1688, name:"building_sugar_plantationlevel", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:1688}) CREATE (b)-[r:Supply{max_supply: 79.58789215686274, current_output: 81.17965,level: 3}]->(g);
CREATE (n: Building {id: 1689, name:"building_coffee_plantationlevel", level:2});
MATCH (g: Goods{code: 41}), (b: Building{id:1689}) CREATE (b)-[r:Supply{max_supply: 35.37239603960396, current_output: 35.72612,level: 2}]->(g);
CREATE (n: Building {id: 1690, name:"building_tobacco_plantationlevel", level:2});
MATCH (g: Goods{code: 43}), (b: Building{id:1690}) CREATE (b)-[r:Supply{max_supply: 44.215495049504945, current_output: 44.65765,level: 2}]->(g);
CREATE (n: Building {id: 1691, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1691}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 1692, name:"building_banana_plantationlevel", level:1});
MATCH (g: Goods{code: 37}), (b: Building{id:1692}) CREATE (b)-[r:Supply{max_supply: 26.5293, current_output: 26.5293,level: 1}]->(g);
CREATE (n: Building {id: 1693, name:"building_sugar_plantationlevel", level:3});
MATCH (g: Goods{code: 42}), (b: Building{id:1693}) CREATE (b)-[r:Supply{max_supply: 79.58789215686274, current_output: 81.17965,level: 3}]->(g);
CREATE (n: Building {id: 1694, name:"building_coffee_plantationlevel", level:5});
MATCH (g: Goods{code: 41}), (b: Building{id:1694}) CREATE (b)-[r:Supply{max_supply: 88.431, current_output: 91.96824,level: 5}]->(g);
CREATE (n: Building {id: 1695, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1695}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 1713, name:"building_sugar_plantationlevel", level:2});
MATCH (g: Goods{code: 42}), (b: Building{id:1713}) CREATE (b)-[r:Supply{max_supply: 53.14079207920792, current_output: 53.6722,level: 2}]->(g);
CREATE (n: Building {id: 1714, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1714}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 1836, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:1836}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 2264, name:"building_sugar_plantationlevel", level:1});
MATCH (g: Goods{code: 42}), (b: Building{id:2264}) CREATE (b)-[r:Supply{max_supply: 26.5647, current_output: 26.5647,level: 1}]->(g);
CREATE (n: Building {id: 2265, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2265}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 2658, name:"building_tea_plantationlevel", level:1});
MATCH (g: Goods{code: 40}), (b: Building{id:2658}) CREATE (b)-[r:Supply{max_supply: 19.6862, current_output: 19.6862,level: 1}]->(g);
CREATE (n: Building {id: 2659, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2659}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 2777, name:"building_easter_island_headslevel", level:1});
CREATE (n: Building {id: 2804, name:"building_subsistence_farmslevel", level:103});
MATCH (g: Goods{code: 7}), (b: Building{id:2804}) CREATE (b)-[r:Supply{max_supply: 117.96332500000001, current_output: 141.55599,level: 103}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2804}) CREATE (b)-[r:Supply{max_supply: 23.592658333333333, current_output: 28.31119,level: 103}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2804}) CREATE (b)-[r:Supply{max_supply: 23.592658333333333, current_output: 28.31119,level: 103}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2804}) CREATE (b)-[r:Supply{max_supply: 23.592658333333333, current_output: 28.31119,level: 103}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2804}) CREATE (b)-[r:Supply{max_supply: 23.592658333333333, current_output: 28.31119,level: 103}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2804}) CREATE (b)-[r:Supply{max_supply: 23.592658333333333, current_output: 28.31119,level: 103}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2804}) CREATE (b)-[r:Supply{max_supply: 33.029725, current_output: 39.63567,level: 103}]->(g);
CREATE (n: Building {id: 2805, name:"building_urban_centerlevel", level:24});
MATCH (g: Goods{code: 10}), (b: Building{id:2805}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 20.74438608042792, level: 24}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2805}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 213.81187017271392, level: 24}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2805}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 12.84282084713441, level: 24}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2805}) CREATE (b)-[r:Supply{max_supply: 1680.0000000000002, current_output: 655.6346627316234,level: 24}]->(g);
CREATE (n: Building {id: 2806, name:"building_subsistence_farmslevel", level:90});
MATCH (g: Goods{code: 7}), (b: Building{id:2806}) CREATE (b)-[r:Supply{max_supply: 100.94174545454545, current_output: 111.03592,level: 90}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2806}) CREATE (b)-[r:Supply{max_supply: 20.188345454545455, current_output: 22.20718,level: 90}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2806}) CREATE (b)-[r:Supply{max_supply: 20.188345454545455, current_output: 22.20718,level: 90}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2806}) CREATE (b)-[r:Supply{max_supply: 20.188345454545455, current_output: 22.20718,level: 90}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2806}) CREATE (b)-[r:Supply{max_supply: 20.188345454545455, current_output: 22.20718,level: 90}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2806}) CREATE (b)-[r:Supply{max_supply: 20.188345454545455, current_output: 22.20718,level: 90}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2806}) CREATE (b)-[r:Supply{max_supply: 28.263681818181816, current_output: 31.09005,level: 90}]->(g);
CREATE (n: Building {id: 2807, name:"building_urban_centerlevel", level:11});
MATCH (g: Goods{code: 10}), (b: Building{id:2807}) CREATE (g)-[r:Demand{max_demand: 54.99999999999999, current_input: 9.507843620196128, level: 11}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:2807}) CREATE (g)-[r:Demand{max_demand: 109.99999999999999, current_input: 97.99710716249386, level: 11}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2807}) CREATE (g)-[r:Demand{max_demand: 54.99999999999999, current_input: 5.886292888269938, level: 11}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2807}) CREATE (b)-[r:Supply{max_supply: 769.9999999999999, current_output: 300.4992204186606,level: 11}]->(g);
CREATE (n: Building {id: 100666140, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:100666140}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.274296515308765, level: 2}]->(b);
CREATE (n: Building {id: 67111716, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:67111716}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:67111716}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.581350992679994, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:67111716}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 40.07749734550263,level: 1}]->(g);
CREATE (n: Building {id: 2924, name:"building_subsistence_farmslevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:2924}) CREATE (b)-[r:Supply{max_supply: 7.249499999999999, current_output: 7.97445,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2924}) CREATE (b)-[r:Supply{max_supply: 1.4498999999999997, current_output: 1.59489,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2924}) CREATE (b)-[r:Supply{max_supply: 1.4498999999999997, current_output: 1.59489,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2924}) CREATE (b)-[r:Supply{max_supply: 1.4498999999999997, current_output: 1.59489,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2924}) CREATE (b)-[r:Supply{max_supply: 1.4498999999999997, current_output: 1.59489,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2924}) CREATE (b)-[r:Supply{max_supply: 1.4498999999999997, current_output: 1.59489,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2924}) CREATE (b)-[r:Supply{max_supply: 2.0298545454545454, current_output: 2.23284,level: 10}]->(g);
CREATE (n: Building {id: 2926, name:"building_subsistence_farmslevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 5.551399999999999, current_output: 6.10654,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 1.1102727272727273, current_output: 1.2213,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 1.1102727272727273, current_output: 1.2213,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 1.1102727272727273, current_output: 1.2213,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 1.1102727272727273, current_output: 1.2213,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 1.1102727272727273, current_output: 1.2213,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2926}) CREATE (b)-[r:Supply{max_supply: 1.554390909090909, current_output: 1.70983,level: 8}]->(g);
CREATE (n: Building {id: 3009, name:"building_subsistence_farmslevel", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:3009}) CREATE (b)-[r:Supply{max_supply: 12.049999999999999, current_output: 13.255,level: 5}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3009}) CREATE (b)-[r:Supply{max_supply: 2.4099999999999997, current_output: 2.651,level: 5}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3009}) CREATE (b)-[r:Supply{max_supply: 2.4099999999999997, current_output: 2.651,level: 5}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3009}) CREATE (b)-[r:Supply{max_supply: 2.4099999999999997, current_output: 2.651,level: 5}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3009}) CREATE (b)-[r:Supply{max_supply: 2.4099999999999997, current_output: 2.651,level: 5}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3009}) CREATE (b)-[r:Supply{max_supply: 2.4099999999999997, current_output: 2.651,level: 5}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3009}) CREATE (b)-[r:Supply{max_supply: 3.3739999999999997, current_output: 3.7114,level: 5}]->(g);
CREATE (n: Building {id: 3061, name:"building_subsistence_farmslevel", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:3061}) CREATE (b)-[r:Supply{max_supply: 0.6432, current_output: 0.70752,level: 16}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3061}) CREATE (b)-[r:Supply{max_supply: 0.1286363636363636, current_output: 0.1415,level: 16}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3061}) CREATE (b)-[r:Supply{max_supply: 0.1286363636363636, current_output: 0.1415,level: 16}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3061}) CREATE (b)-[r:Supply{max_supply: 0.1286363636363636, current_output: 0.1415,level: 16}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3061}) CREATE (b)-[r:Supply{max_supply: 0.1286363636363636, current_output: 0.1415,level: 16}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3061}) CREATE (b)-[r:Supply{max_supply: 0.1286363636363636, current_output: 0.1415,level: 16}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3061}) CREATE (b)-[r:Supply{max_supply: 0.18009090909090908, current_output: 0.1981,level: 16}]->(g);
CREATE (n: Building {id: 3130, name:"building_subsistence_farmslevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 0.4105, current_output: 0.45155,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 0.08209999999999999, current_output: 0.09031,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 0.08209999999999999, current_output: 0.09031,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 0.08209999999999999, current_output: 0.09031,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 0.08209999999999999, current_output: 0.09031,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 0.08209999999999999, current_output: 0.09031,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3130}) CREATE (b)-[r:Supply{max_supply: 0.11493636363636361, current_output: 0.12643,level: 10}]->(g);
CREATE (n: Building {id: 3169, name:"building_subsistence_farmslevel", level:9});
MATCH (g: Goods{code: 7}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 9.873445454545454, current_output: 10.86079,level: 9}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 1.9746818181818178, current_output: 2.17215,level: 9}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 1.9746818181818178, current_output: 2.17215,level: 9}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 1.9746818181818178, current_output: 2.17215,level: 9}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 1.9746818181818178, current_output: 2.17215,level: 9}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 1.9746818181818178, current_output: 2.17215,level: 9}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3169}) CREATE (b)-[r:Supply{max_supply: 2.7645636363636363, current_output: 3.04102,level: 9}]->(g);
CREATE (n: Building {id: 3182, name:"building_subsistence_farmslevel", level:44});
MATCH (g: Goods{code: 7}), (b: Building{id:3182}) CREATE (b)-[r:Supply{max_supply: 86.372, current_output: 95.0092,level: 44}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3182}) CREATE (b)-[r:Supply{max_supply: 17.2744, current_output: 19.00184,level: 44}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3182}) CREATE (b)-[r:Supply{max_supply: 17.2744, current_output: 19.00184,level: 44}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3182}) CREATE (b)-[r:Supply{max_supply: 17.2744, current_output: 19.00184,level: 44}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3182}) CREATE (b)-[r:Supply{max_supply: 17.2744, current_output: 19.00184,level: 44}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3182}) CREATE (b)-[r:Supply{max_supply: 17.2744, current_output: 19.00184,level: 44}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3182}) CREATE (b)-[r:Supply{max_supply: 24.184154545454543, current_output: 26.60257,level: 44}]->(g);
CREATE (n: Building {id: 3183, name:"building_urban_centerlevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3183}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 2.59304826005349, level: 3}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3183}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 26.72648377158924, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3183}) CREATE (g)-[r:Demand{max_demand: 15.0, current_input: 1.6053526058918013, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3183}) CREATE (b)-[r:Supply{max_supply: 209.99999999999997, current_output: 81.9543328414529,level: 3}]->(g);
CREATE (n: Building {id: 3184, name:"building_subsistence_farmslevel", level:95});
MATCH (g: Goods{code: 7}), (b: Building{id:3184}) CREATE (b)-[r:Supply{max_supply: 105.79911818181817, current_output: 116.37903,level: 95}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3184}) CREATE (b)-[r:Supply{max_supply: 21.15981818181818, current_output: 23.2758,level: 95}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3184}) CREATE (b)-[r:Supply{max_supply: 21.15981818181818, current_output: 23.2758,level: 95}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3184}) CREATE (b)-[r:Supply{max_supply: 21.15981818181818, current_output: 23.2758,level: 95}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3184}) CREATE (b)-[r:Supply{max_supply: 21.15981818181818, current_output: 23.2758,level: 95}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3184}) CREATE (b)-[r:Supply{max_supply: 21.15981818181818, current_output: 23.2758,level: 95}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3184}) CREATE (b)-[r:Supply{max_supply: 29.623754545454542, current_output: 32.58613,level: 95}]->(g);
CREATE (n: Building {id: 3185, name:"building_urban_centerlevel", level:13});
MATCH (g: Goods{code: 10}), (b: Building{id:3185}) CREATE (g)-[r:Demand{max_demand: 64.99999999999999, current_input: 11.236542460231787, level: 13}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3185}) CREATE (g)-[r:Demand{max_demand: 129.99999999999997, current_input: 115.81476301022, level: 13}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3185}) CREATE (g)-[r:Demand{max_demand: 64.99999999999999, current_input: 6.956527958864471, level: 13}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3185}) CREATE (b)-[r:Supply{max_supply: 910.0, current_output: 355.1354423129626,level: 13}]->(g);
CREATE (n: Building {id: 3186, name:"building_subsistence_farmslevel", level:42});
MATCH (g: Goods{code: 7}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 22.4301, current_output: 24.67311,level: 42}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 4.4860181818181815, current_output: 4.93462,level: 42}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 4.4860181818181815, current_output: 4.93462,level: 42}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 4.4860181818181815, current_output: 4.93462,level: 42}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 4.4860181818181815, current_output: 4.93462,level: 42}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 4.4860181818181815, current_output: 4.93462,level: 42}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3186}) CREATE (b)-[r:Supply{max_supply: 6.280427272727272, current_output: 6.90847,level: 42}]->(g);
CREATE (n: Building {id: 3187, name:"building_subsistence_farmslevel", level:123});
MATCH (g: Goods{code: 7}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 165.63179999999997, current_output: 182.19498,level: 123}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 33.12635454545454, current_output: 36.43899,level: 123}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 33.12635454545454, current_output: 36.43899,level: 123}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 33.12635454545454, current_output: 36.43899,level: 123}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 33.12635454545454, current_output: 36.43899,level: 123}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 33.12635454545454, current_output: 36.43899,level: 123}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3187}) CREATE (b)-[r:Supply{max_supply: 46.37689999999999, current_output: 51.01459,level: 123}]->(g);
CREATE (n: Building {id: 3188, name:"building_urban_centerlevel", level:12});
MATCH (g: Goods{code: 10}), (b: Building{id:3188}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 10.37219304021396, level: 12}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3188}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 106.90593508635693, level: 12}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3188}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 6.421410423567205, level: 12}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3188}) CREATE (b)-[r:Supply{max_supply: 839.9999999999999, current_output: 327.8173313658116,level: 12}]->(g);
CREATE (n: Building {id: 3189, name:"building_subsistence_farmslevel", level:63});
MATCH (g: Goods{code: 7}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 59.68935, current_output: 71.62722,level: 63}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 11.937866666666668, current_output: 14.32544,level: 63}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 11.937866666666668, current_output: 14.32544,level: 63}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 11.937866666666668, current_output: 14.32544,level: 63}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 11.937866666666668, current_output: 14.32544,level: 63}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 11.937866666666668, current_output: 14.32544,level: 63}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3189}) CREATE (b)-[r:Supply{max_supply: 16.713016666666668, current_output: 20.05562,level: 63}]->(g);
CREATE (n: Building {id: 3190, name:"building_urban_centerlevel", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:3190}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 8.6434942001783, level: 10}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3190}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 89.08827923863078, level: 10}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3190}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 5.351175352972671, level: 10}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3190}) CREATE (b)-[r:Supply{max_supply: 700.0, current_output: 273.1811094715097,level: 10}]->(g);
CREATE (n: Building {id: 3191, name:"building_subsistence_farmslevel", level:102});
MATCH (g: Goods{code: 7}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 157.1412, current_output: 172.85532,level: 102}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 31.428236363636362, current_output: 34.57106,level: 102}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 31.428236363636362, current_output: 34.57106,level: 102}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 31.428236363636362, current_output: 34.57106,level: 102}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 31.428236363636362, current_output: 34.57106,level: 102}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 31.428236363636362, current_output: 34.57106,level: 102}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3191}) CREATE (b)-[r:Supply{max_supply: 43.99952727272726, current_output: 48.39948,level: 102}]->(g);
CREATE (n: Building {id: 3192, name:"building_urban_centerlevel", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:3192}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 6.914795360142638, level: 8}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3192}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 71.27062339090462, level: 8}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3192}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 4.280940282378136, level: 8}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3192}) CREATE (b)-[r:Supply{max_supply: 560.0, current_output: 218.54488757720776,level: 8}]->(g);
CREATE (n: Building {id: 3193, name:"building_subsistence_farmslevel", level:85});
MATCH (g: Goods{code: 7}), (b: Building{id:3193}) CREATE (b)-[r:Supply{max_supply: 135.90861818181818, current_output: 149.49948,level: 85}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3193}) CREATE (b)-[r:Supply{max_supply: 27.18171818181818, current_output: 29.89989,level: 85}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3193}) CREATE (b)-[r:Supply{max_supply: 27.18171818181818, current_output: 29.89989,level: 85}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3193}) CREATE (b)-[r:Supply{max_supply: 27.18171818181818, current_output: 29.89989,level: 85}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3193}) CREATE (b)-[r:Supply{max_supply: 27.18171818181818, current_output: 29.89989,level: 85}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3193}) CREATE (b)-[r:Supply{max_supply: 27.18171818181818, current_output: 29.89989,level: 85}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3193}) CREATE (b)-[r:Supply{max_supply: 38.05440909090909, current_output: 41.85985,level: 85}]->(g);
CREATE (n: Building {id: 3194, name:"building_urban_centerlevel", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3194}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.18609652010698, level: 6}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3194}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 53.45296754317848, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3194}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 3.2107052117836026, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3194}) CREATE (b)-[r:Supply{max_supply: 420.0, current_output: 163.90866568290582,level: 6}]->(g);
CREATE (n: Building {id: 3195, name:"building_subsistence_farmslevel", level:128});
MATCH (g: Goods{code: 7}), (b: Building{id:3195}) CREATE (b)-[r:Supply{max_supply: 287.02079999999995, current_output: 315.72288,level: 128}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3195}) CREATE (b)-[r:Supply{max_supply: 57.404154545454546, current_output: 63.14457,level: 128}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3195}) CREATE (b)-[r:Supply{max_supply: 57.404154545454546, current_output: 63.14457,level: 128}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3195}) CREATE (b)-[r:Supply{max_supply: 57.404154545454546, current_output: 63.14457,level: 128}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3195}) CREATE (b)-[r:Supply{max_supply: 57.404154545454546, current_output: 63.14457,level: 128}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3195}) CREATE (b)-[r:Supply{max_supply: 57.404154545454546, current_output: 63.14457,level: 128}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3195}) CREATE (b)-[r:Supply{max_supply: 80.36581818181817, current_output: 88.4024,level: 128}]->(g);
CREATE (n: Building {id: 3196, name:"building_subsistence_farmslevel", level:173});
MATCH (g: Goods{code: 7}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 360.164375, current_output: 432.19725,level: 173}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 72.032875, current_output: 86.43945,level: 173}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 72.032875, current_output: 86.43945,level: 173}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 72.032875, current_output: 86.43945,level: 173}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 72.032875, current_output: 86.43945,level: 173}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 72.032875, current_output: 86.43945,level: 173}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3196}) CREATE (b)-[r:Supply{max_supply: 100.84602500000001, current_output: 121.01523,level: 173}]->(g);
CREATE (n: Building {id: 3197, name:"building_subsistence_farmslevel", level:102});
MATCH (g: Goods{code: 7}), (b: Building{id:3197}) CREATE (b)-[r:Supply{max_supply: 254.99999999999997, current_output: 280.5,level: 102}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3197}) CREATE (b)-[r:Supply{max_supply: 51.0, current_output: 56.1,level: 102}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3197}) CREATE (b)-[r:Supply{max_supply: 51.0, current_output: 56.1,level: 102}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3197}) CREATE (b)-[r:Supply{max_supply: 51.0, current_output: 56.1,level: 102}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3197}) CREATE (b)-[r:Supply{max_supply: 51.0, current_output: 56.1,level: 102}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3197}) CREATE (b)-[r:Supply{max_supply: 51.0, current_output: 56.1,level: 102}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3197}) CREATE (b)-[r:Supply{max_supply: 71.4, current_output: 78.54,level: 102}]->(g);
CREATE (n: Building {id: 3198, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3198}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8643494200178301, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:3198}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.908827923863079, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3198}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.535117535297267, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3198}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 27.31811094715097,level: 1}]->(g);
CREATE (n: Building {id: 3243, name:"building_subsistence_farmslevel", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:3243}) CREATE (b)-[r:Supply{max_supply: 3.7444454545454544, current_output: 4.11889,level: 6}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3243}) CREATE (b)-[r:Supply{max_supply: 0.7488818181818181, current_output: 0.82377,level: 6}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3243}) CREATE (b)-[r:Supply{max_supply: 0.7488818181818181, current_output: 0.82377,level: 6}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3243}) CREATE (b)-[r:Supply{max_supply: 0.7488818181818181, current_output: 0.82377,level: 6}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3243}) CREATE (b)-[r:Supply{max_supply: 0.7488818181818181, current_output: 0.82377,level: 6}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3243}) CREATE (b)-[r:Supply{max_supply: 0.7488818181818181, current_output: 0.82377,level: 6}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3243}) CREATE (b)-[r:Supply{max_supply: 1.0484454545454545, current_output: 1.15329,level: 6}]->(g);
CREATE (n: Building {id: 3269, name:"building_subsistence_farmslevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:3269}) CREATE (b)-[r:Supply{max_supply: 17.6284, current_output: 19.39124,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3269}) CREATE (b)-[r:Supply{max_supply: 3.525672727272727, current_output: 3.87824,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3269}) CREATE (b)-[r:Supply{max_supply: 3.525672727272727, current_output: 3.87824,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3269}) CREATE (b)-[r:Supply{max_supply: 3.525672727272727, current_output: 3.87824,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3269}) CREATE (b)-[r:Supply{max_supply: 3.525672727272727, current_output: 3.87824,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3269}) CREATE (b)-[r:Supply{max_supply: 3.525672727272727, current_output: 3.87824,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3269}) CREATE (b)-[r:Supply{max_supply: 4.935945454545454, current_output: 5.42954,level: 8}]->(g);
CREATE (n: Building {id: 3270, name:"building_subsistence_farmslevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 5.841399999999999, current_output: 6.42554,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 1.1682727272727271, current_output: 1.2851,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 1.1682727272727271, current_output: 1.2851,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 1.1682727272727271, current_output: 1.2851,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 1.1682727272727271, current_output: 1.2851,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 1.1682727272727271, current_output: 1.2851,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3270}) CREATE (b)-[r:Supply{max_supply: 1.6355909090909089, current_output: 1.79915,level: 8}]->(g);
CREATE (n: Building {id: 3273, name:"building_subsistence_farmslevel", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:3273}) CREATE (b)-[r:Supply{max_supply: 26.588699999999996, current_output: 29.24757,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3273}) CREATE (b)-[r:Supply{max_supply: 5.317736363636364, current_output: 5.84951,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3273}) CREATE (b)-[r:Supply{max_supply: 5.317736363636364, current_output: 5.84951,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3273}) CREATE (b)-[r:Supply{max_supply: 5.317736363636364, current_output: 5.84951,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3273}) CREATE (b)-[r:Supply{max_supply: 5.317736363636364, current_output: 5.84951,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3273}) CREATE (b)-[r:Supply{max_supply: 5.317736363636364, current_output: 5.84951,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3273}) CREATE (b)-[r:Supply{max_supply: 7.444827272727273, current_output: 8.18931,level: 18}]->(g);
CREATE (n: Building {id: 3274, name:"building_subsistence_farmslevel", level:27});
MATCH (g: Goods{code: 7}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 39.38017272727272, current_output: 43.31819,level: 27}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 7.8760272727272715, current_output: 8.66363,level: 27}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 7.8760272727272715, current_output: 8.66363,level: 27}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 7.8760272727272715, current_output: 8.66363,level: 27}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 7.8760272727272715, current_output: 8.66363,level: 27}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 7.8760272727272715, current_output: 8.66363,level: 27}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3274}) CREATE (b)-[r:Supply{max_supply: 11.026445454545453, current_output: 12.12909,level: 27}]->(g);
CREATE (n: Building {id: 16780574, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:16780574}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 26.726483771589233, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16780574}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 57.659374686480405, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16780574}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 83.33425667224415,level: 1}]->(g);
CREATE (n: Building {id: 3369, name:"building_subsistence_farmslevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:3369}) CREATE (b)-[r:Supply{max_supply: 8.126499999999998, current_output: 8.93915,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3369}) CREATE (b)-[r:Supply{max_supply: 1.6253, current_output: 1.78783,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3369}) CREATE (b)-[r:Supply{max_supply: 1.6253, current_output: 1.78783,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3369}) CREATE (b)-[r:Supply{max_supply: 1.6253, current_output: 1.78783,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3369}) CREATE (b)-[r:Supply{max_supply: 1.6253, current_output: 1.78783,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3369}) CREATE (b)-[r:Supply{max_supply: 1.6253, current_output: 1.78783,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3369}) CREATE (b)-[r:Supply{max_supply: 2.2754181818181816, current_output: 2.50296,level: 4}]->(g);
CREATE (n: Building {id: 67112257, name:"building_steel_millslevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:67112257}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 53.45296754317847, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:67112257}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 115.31874937296081, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:67112257}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 166.66851334448833,level: 2}]->(g);
CREATE (n: Building {id: 3503, name:"building_subsistence_orchardslevel", level:21});
MATCH (g: Goods{code: 7}), (b: Building{id:3503}) CREATE (b)-[r:Supply{max_supply: 10.5, current_output: 11.55,level: 21}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3503}) CREATE (b)-[r:Supply{max_supply: 5.25, current_output: 5.775,level: 21}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3503}) CREATE (b)-[r:Supply{max_supply: 15.749999999999998, current_output: 17.325,level: 21}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3503}) CREATE (b)-[r:Supply{max_supply: 10.5, current_output: 11.55,level: 21}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3503}) CREATE (b)-[r:Supply{max_supply: 10.5, current_output: 11.55,level: 21}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3503}) CREATE (b)-[r:Supply{max_supply: 10.5, current_output: 11.55,level: 21}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3503}) CREATE (b)-[r:Supply{max_supply: 27.929999999999996, current_output: 30.723,level: 21}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3503}) CREATE (b)-[r:Supply{max_supply: 14.700000000000001, current_output: 16.17,level: 21}]->(g);
CREATE (n: Building {id: 134221549, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:134221549}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:134221549}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:134221549}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.67109819387657, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:134221549}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 37.9993234463741,level: 1}]->(g);
CREATE (n: Building {id: 3839, name:"building_subsistence_orchardslevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 1.7238727272727272, current_output: 1.89626,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 0.8619363636363636, current_output: 0.94813,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 2.5858181818181816, current_output: 2.8444,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 1.7238727272727272, current_output: 1.89626,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 1.7238727272727272, current_output: 1.89626,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 1.7238727272727272, current_output: 1.89626,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 4.585518181818181, current_output: 5.04407,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3839}) CREATE (b)-[r:Supply{max_supply: 2.4134272727272728, current_output: 2.65477,level: 8}]->(g);
CREATE (n: Building {id: 3856, name:"building_subsistence_farmslevel", level:90});
MATCH (g: Goods{code: 7}), (b: Building{id:3856}) CREATE (b)-[r:Supply{max_supply: 216.63899999999998, current_output: 238.3029,level: 90}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3856}) CREATE (b)-[r:Supply{max_supply: 43.327799999999996, current_output: 47.66058,level: 90}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3856}) CREATE (b)-[r:Supply{max_supply: 43.327799999999996, current_output: 47.66058,level: 90}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3856}) CREATE (b)-[r:Supply{max_supply: 43.327799999999996, current_output: 47.66058,level: 90}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3856}) CREATE (b)-[r:Supply{max_supply: 43.327799999999996, current_output: 47.66058,level: 90}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3856}) CREATE (b)-[r:Supply{max_supply: 43.327799999999996, current_output: 47.66058,level: 90}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3856}) CREATE (b)-[r:Supply{max_supply: 60.65891818181818, current_output: 66.72481,level: 90}]->(g);
CREATE (n: Building {id: 3861, name:"building_trade_centerlevel", level:159});
CREATE (n: Building {id: 16781082, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16781082}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16781082}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.581350992679994, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16781082}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 40.07749734550263,level: 1}]->(g);
CREATE (n: Building {id: 50335551, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:50335551}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 10.37219304021396, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:50335551}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 33.16270198535999, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50335551}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 80.15499469100526,level: 2}]->(g);
CREATE (n: Building {id: 16781143, name:"building_subsistence_pastureslevel", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 0.0032999999999999995, current_output: 0.00363,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 0.004945454545454545, current_output: 0.00544,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 0.0016454545454545452, current_output: 0.00181,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 0.0032999999999999995, current_output: 0.00363,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 0.0032999999999999995, current_output: 0.00363,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 0.0032999999999999995, current_output: 0.00363,level: 10}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 0.008772727272727272, current_output: 0.00965,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16781143}) CREATE (b)-[r:Supply{max_supply: 0.004618181818181818, current_output: 0.00508,level: 10}]->(g);
CREATE (n: Building {id: 3934, name:"building_conscription_centerlevel", level:24});
CREATE (n: Building {id: 3935, name:"building_conscription_centerlevel", level:16});
CREATE (n: Building {id: 16781180, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16781180}) CREATE (g)-[r:Demand{max_demand: 12.56313043478261, current_input: 3.937480422345435, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16781180}) CREATE (g)-[r:Demand{max_demand: 25.12626086956522, current_input: 4.3435738019650785, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16781180}) CREATE (g)-[r:Demand{max_demand: 31.407826086956526, current_input: 30.182593540594, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16781180}) CREATE (g)-[r:Demand{max_demand: 6.281565217391305, current_input: 6.030254989250109, level: 1}]->(b);
CREATE (n: Building {id: 16781266, name:"building_subsistence_fishing_villageslevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:16781266}) CREATE (b)-[r:Supply{max_supply: 0.0042, current_output: 0.00462,level: 8}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:16781266}) CREATE (b)-[r:Supply{max_supply: 0.0168, current_output: 0.01848,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:16781266}) CREATE (b)-[r:Supply{max_supply: 0.0021, current_output: 0.00231,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:16781266}) CREATE (b)-[r:Supply{max_supply: 0.0063, current_output: 0.00693,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:16781266}) CREATE (b)-[r:Supply{max_supply: 0.0042, current_output: 0.00462,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:16781266}) CREATE (b)-[r:Supply{max_supply: 0.0042, current_output: 0.00462,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:16781266}) CREATE (b)-[r:Supply{max_supply: 0.0042, current_output: 0.00462,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16781266}) CREATE (b)-[r:Supply{max_supply: 0.005872727272727272, current_output: 0.00646,level: 8}]->(g);
CREATE (n: Building {id: 4130, name:"building_conscription_centerlevel", level:7});
CREATE (n: Building {id: 4131, name:"building_conscription_centerlevel", level:16});
CREATE (n: Building {id: 4132, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 4133, name:"building_conscription_centerlevel", level:21});
CREATE (n: Building {id: 4134, name:"building_conscription_centerlevel", level:12});
CREATE (n: Building {id: 4135, name:"building_conscription_centerlevel", level:15});
CREATE (n: Building {id: 4136, name:"building_conscription_centerlevel", level:12});
CREATE (n: Building {id: 4137, name:"building_conscription_centerlevel", level:17});
CREATE (n: Building {id: 4138, name:"building_conscription_centerlevel", level:15});
CREATE (n: Building {id: 4139, name:"building_conscription_centerlevel", level:12});
CREATE (n: Building {id: 4175, name:"building_conscription_centerlevel", level:2});
CREATE (n: Building {id: 4546, name:"building_conscription_centerlevel", level:9});
CREATE (n: Building {id: 33558985, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:33558985}) CREATE (g)-[r:Demand{max_demand: 9.8357, current_input: 9.452005193396923, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:33558985}) CREATE (g)-[r:Demand{max_demand: 9.8357, current_input: 0.38113397905008056, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:33558985}) CREATE (g)-[r:Demand{max_demand: 9.8357, current_input: 8.154459697935131, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33558985}) CREATE (g)-[r:Demand{max_demand: 4.917845454545454, current_input: 4.721094355038726, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:33558985}) CREATE (b)-[r:Supply{max_supply: 24.589245454545452, current_output: 17.14361952319916,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:33558985}) CREATE (b)-[r:Supply{max_supply: 14.753545454545453, current_output: 10.28617044628331,level: 1}]->(g);
CREATE (n: Building {id: 50336210, name:"building_subsistence_fishing_villageslevel", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:50336210}) CREATE (b)-[r:Supply{max_supply: 0.0036, current_output: 0.00396,level: 8}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:50336210}) CREATE (b)-[r:Supply{max_supply: 0.0144, current_output: 0.01584,level: 8}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:50336210}) CREATE (b)-[r:Supply{max_supply: 0.0018, current_output: 0.00198,level: 8}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:50336210}) CREATE (b)-[r:Supply{max_supply: 0.005399999999999999, current_output: 0.00594,level: 8}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:50336210}) CREATE (b)-[r:Supply{max_supply: 0.0036, current_output: 0.00396,level: 8}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:50336210}) CREATE (b)-[r:Supply{max_supply: 0.0036, current_output: 0.00396,level: 8}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:50336210}) CREATE (b)-[r:Supply{max_supply: 0.0036, current_output: 0.00396,level: 8}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:50336210}) CREATE (b)-[r:Supply{max_supply: 0.005036363636363636, current_output: 0.00554,level: 8}]->(g);
CREATE (n: Building {id: 16781802, name:"building_tooling_workshopslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:16781802}) CREATE (g)-[r:Demand{max_demand: 102.00000000000001, current_input: 17.632728168363734, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16781802}) CREATE (g)-[r:Demand{max_demand: 68.00000000000001, current_input: 56.376593375111995, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16781802}) CREATE (b)-[r:Supply{max_supply: 272.00000000000006, current_output: 136.26349097470896,level: 4}]->(g);
CREATE (n: Building {id: 134222452, name:"building_sulfur_minelevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:134222452}) CREATE (g)-[r:Demand{max_demand: 8.81715, current_input: 7.855047212888935, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:134222452}) CREATE (g)-[r:Demand{max_demand: 8.81715, current_input: 8.464397158730389, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:134222452}) CREATE (b)-[r:Supply{max_supply: 35.2686, current_output: 32.638888743238645,level: 1}]->(g);
CREATE (n: Building {id: 4748, name:"building_subsistence_farmslevel", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:4748}) CREATE (b)-[r:Supply{max_supply: 22.214399999999998, current_output: 24.43584,level: 16}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:4748}) CREATE (b)-[r:Supply{max_supply: 4.442872727272727, current_output: 4.88716,level: 16}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:4748}) CREATE (b)-[r:Supply{max_supply: 4.442872727272727, current_output: 4.88716,level: 16}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:4748}) CREATE (b)-[r:Supply{max_supply: 4.442872727272727, current_output: 4.88716,level: 16}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:4748}) CREATE (b)-[r:Supply{max_supply: 4.442872727272727, current_output: 4.88716,level: 16}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:4748}) CREATE (b)-[r:Supply{max_supply: 4.442872727272727, current_output: 4.88716,level: 16}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:4748}) CREATE (b)-[r:Supply{max_supply: 6.220027272727273, current_output: 6.84203,level: 16}]->(g);
CREATE (n: Building {id: 50336496, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:50336496}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 4913, name:"building_portlevel", level:4});
CREATE (n: Building {id: 4917, name:"building_subsistence_farmslevel", level:142});
MATCH (g: Goods{code: 7}), (b: Building{id:4917}) CREATE (b)-[r:Supply{max_supply: 312.4035454545454, current_output: 343.6439,level: 142}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:4917}) CREATE (b)-[r:Supply{max_supply: 62.48070909090909, current_output: 68.72878,level: 142}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:4917}) CREATE (b)-[r:Supply{max_supply: 62.48070909090909, current_output: 68.72878,level: 142}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:4917}) CREATE (b)-[r:Supply{max_supply: 62.48070909090909, current_output: 68.72878,level: 142}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:4917}) CREATE (b)-[r:Supply{max_supply: 62.48070909090909, current_output: 68.72878,level: 142}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:4917}) CREATE (b)-[r:Supply{max_supply: 62.48070909090909, current_output: 68.72878,level: 142}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:4917}) CREATE (b)-[r:Supply{max_supply: 87.47299090909091, current_output: 96.22029,level: 142}]->(g);
CREATE (n: Building {id: 4918, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:4918}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 10.37219304021396, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:4918}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:4918}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 19.342196387753138, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:4918}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 75.9986468927482,level: 2}]->(g);
CREATE (n: Building {id: 4919, name:"building_tea_plantationlevel", level:2});
MATCH (g: Goods{code: 40}), (b: Building{id:4919}) CREATE (b)-[r:Supply{max_supply: 39.37239603960396, current_output: 39.76612,level: 2}]->(g);
CREATE (n: Building {id: 4920, name:"building_barrackslevel", level:1});
MATCH (g: Goods{code: 0}), (b: Building{id:4920}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.6066950386290002, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:4920}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.3072016922755214, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4920}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.8777525103436717, level: 1}]->(b);
CREATE (n: Building {id: 4921, name:"building_conscription_centerlevel", level:14});
CREATE (n: Building {id: 4923, name:"building_fishing_wharflevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:4923}) CREATE (g)-[r:Demand{max_demand: 4.25, current_input: 2.771933798030603, level: 1}]->(b);
MATCH (g: Goods{code: 8}), (b: Building{id:4923}) CREATE (b)-[r:Supply{max_supply: 42.5, current_output: 27.719337980306033,level: 1}]->(g);
CREATE (n: Building {id: 4924, name:"building_portlevel", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:4924}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.261098585918356, level: 1}]->(b);
CREATE (n: Building {id: 4990, name:"building_shipyardslevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:4990}) CREATE (g)-[r:Demand{max_demand: 24.78659821428571, current_input: 7.768505287112472, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:4990}) CREATE (g)-[r:Demand{max_demand: 49.57319642857142, current_input: 8.569712716293134, level: 3}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:4990}) CREATE (g)-[r:Demand{max_demand: 24.78659821428571, current_input: 0.9604822030487241, level: 3}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:4990}) CREATE (g)-[r:Demand{max_demand: 12.39329464285714, current_input: 9.722816417204006, level: 3}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:4990}) CREATE (b)-[r:Supply{max_supply: 86.75309821428571, current_output: 28.402049688017108,level: 3}]->(g);
CREATE (n: Building {id: 50336653, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:50336653}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:50336653}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.908827923863079, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:50336653}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.581350992679994, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50336653}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 50.475206027303294,level: 1}]->(g);
CREATE (n: Building {id: 50336678, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:50336678}) CREATE (g)-[r:Demand{max_demand: 9.8782, current_input: 3.0959814761077666, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50336678}) CREATE (g)-[r:Demand{max_demand: 19.7564, current_input: 3.415286576328051, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:50336678}) CREATE (g)-[r:Demand{max_demand: 9.8782, current_input: 0.3827808566601773, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:50336678}) CREATE (g)-[r:Demand{max_demand: 4.9391, current_input: 3.874834251107691, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:50336678}) CREATE (b)-[r:Supply{max_supply: 34.573699999999995, current_output: 11.319064857753933,level: 1}]->(g);
CREATE (n: Building {id: 50336702, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:50336702}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:50336702}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.908827923863079, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:50336702}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.581350992679994, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50336702}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 50.475206027303294,level: 1}]->(g);
CREATE (n: Building {id: 33559524, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:33559524}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.086434942001783, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:33559524}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.890882792386308, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:33559524}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.05351175352972671, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:33559524}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 2.731811094715097,level: 1}]->(g);
CREATE (n: Building {id: 50336763, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:50336763}) CREATE (g)-[r:Demand{max_demand: 25.12626086956522, current_input: 7.87496084469087, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50336763}) CREATE (g)-[r:Demand{max_demand: 50.25252173913044, current_input: 8.687147603930157, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50336763}) CREATE (g)-[r:Demand{max_demand: 62.81565217391305, current_input: 60.365187081188, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50336763}) CREATE (g)-[r:Demand{max_demand: 12.56313043478261, current_input: 12.060509978500217, level: 2}]->(b);
CREATE (n: Building {id: 16782332, name:"building_construction_sectorlevel", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:16782332}) CREATE (g)-[r:Demand{max_demand: 37.68956521739131, current_input: 11.812495774088307, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782332}) CREATE (g)-[r:Demand{max_demand: 75.37913043478262, current_input: 13.030781534550544, level: 3}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782332}) CREATE (g)-[r:Demand{max_demand: 94.22391304347828, current_input: 90.5481984433377, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782332}) CREATE (g)-[r:Demand{max_demand: 18.844782608695656, current_input: 18.090848445351998, level: 3}]->(b);
CREATE (n: Building {id: 16782351, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782351}) CREATE (g)-[r:Demand{max_demand: 12.56313043478261, current_input: 3.937480422345435, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782351}) CREATE (g)-[r:Demand{max_demand: 25.12626086956522, current_input: 4.3435738019650785, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782351}) CREATE (g)-[r:Demand{max_demand: 31.407826086956526, current_input: 30.182593540594, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782351}) CREATE (g)-[r:Demand{max_demand: 6.281565217391305, current_input: 6.030254989250109, level: 1}]->(b);
CREATE (n: Building {id: 16782367, name:"building_construction_sectorlevel", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:16782367}) CREATE (g)-[r:Demand{max_demand: 25.12626086956522, current_input: 7.87496084469087, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782367}) CREATE (g)-[r:Demand{max_demand: 50.25252173913044, current_input: 8.687147603930157, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16782367}) CREATE (g)-[r:Demand{max_demand: 62.81565217391305, current_input: 60.365187081188, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782367}) CREATE (g)-[r:Demand{max_demand: 12.56313043478261, current_input: 12.060509978500217, level: 2}]->(b);
CREATE (n: Building {id: 5157, name:"building_construction_sectorlevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:5157}) CREATE (g)-[r:Demand{max_demand: 12.56313043478261, current_input: 3.937480422345435, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:5157}) CREATE (g)-[r:Demand{max_demand: 25.12626086956522, current_input: 4.3435738019650785, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:5157}) CREATE (g)-[r:Demand{max_demand: 31.407826086956526, current_input: 30.182593540594, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5157}) CREATE (g)-[r:Demand{max_demand: 6.281565217391305, current_input: 6.030254989250109, level: 1}]->(b);
CREATE (n: Building {id: 16782439, name:"building_subsistence_farmslevel", level:54});
MATCH (g: Goods{code: 7}), (b: Building{id:16782439}) CREATE (b)-[r:Supply{max_supply: 132.85079999999996, current_output: 146.13588,level: 54}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:16782439}) CREATE (b)-[r:Supply{max_supply: 26.570154545454542, current_output: 29.22717,level: 54}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:16782439}) CREATE (b)-[r:Supply{max_supply: 26.570154545454542, current_output: 29.22717,level: 54}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:16782439}) CREATE (b)-[r:Supply{max_supply: 26.570154545454542, current_output: 29.22717,level: 54}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:16782439}) CREATE (b)-[r:Supply{max_supply: 26.570154545454542, current_output: 29.22717,level: 54}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:16782439}) CREATE (b)-[r:Supply{max_supply: 26.570154545454542, current_output: 29.22717,level: 54}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16782439}) CREATE (b)-[r:Supply{max_supply: 37.19821818181818, current_output: 40.91804,level: 54}]->(g);
CREATE (n: Building {id: 5230, name:"building_portlevel", level:4});
MATCH (g: Goods{code: 18}), (b: Building{id:5230}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 13.044394343673424, level: 4}]->(b);
CREATE (n: Building {id: 50336891, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:50336891}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 6.268310979951341, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:50336891}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 6.914795360142641, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:50336891}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.7750012282808151, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:50336891}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.845223322280762, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:50336891}) CREATE (b)-[r:Supply{max_supply: 24.999999999999996, current_output: 8.184736416520312,level: 1}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:50336891}) CREATE (b)-[r:Supply{max_supply: 44.99999999999999, current_output: 14.732525549736563,level: 1}]->(g);
CREATE (n: Building {id: 16782460, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16782460}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782460}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.581350992679994, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782460}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 40.07749734550263,level: 1}]->(g);
CREATE (n: Building {id: 16782510, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16782510}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782510}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.581350992679994, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782510}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 40.07749734550263,level: 1}]->(g);
CREATE (n: Building {id: 33559743, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:33559743}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 10.37219304021396, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:33559743}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 33.16270198535999, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33559743}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 80.15499469100526,level: 2}]->(g);
CREATE (n: Building {id: 5321, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:5321}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 10.37219304021396, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5321}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 17.817655847726154, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:5321}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 33.16270198535999, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5321}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 100.95041205460659,level: 2}]->(g);
CREATE (n: Building {id: 67114230, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:67114230}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.086434942001783, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:67114230}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.890882792386308, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:67114230}) CREATE (g)-[r:Demand{max_demand: 0.5, current_input: 0.05351175352972671, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:67114230}) CREATE (b)-[r:Supply{max_supply: 7.0, current_output: 2.731811094715097,level: 1}]->(g);
CREATE (n: Building {id: 67114249, name:"building_universitylevel", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:67114249}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 18.822889545926294, level: 6}]->(b);
CREATE (n: Building {id: 16782610, name:"building_shipyardslevel", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:16782610}) CREATE (g)-[r:Demand{max_demand: 99.21700000000001, current_input: 31.096150524891613, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782610}) CREATE (g)-[r:Demand{max_demand: 198.43400000000003, current_input: 34.30326256236362, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782610}) CREATE (g)-[r:Demand{max_demand: 99.21700000000001, current_input: 3.844664843316882, level: 5}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16782610}) CREATE (g)-[r:Demand{max_demand: 49.60850000000001, current_input: 38.91897611833652, level: 5}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:16782610}) CREATE (b)-[r:Supply{max_supply: 124.0212456140351, current_output: 40.60324821597613,level: 5}]->(g);
MATCH (g: Goods{code: 18}), (b: Building{id:16782610}) CREATE (b)-[r:Supply{max_supply: 223.23824561403512, current_output: 73.08584793749198,level: 5}]->(g);
CREATE (n: Building {id: 33559880, name:"building_tooling_workshopslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:33559880}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 15.558289560320938, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:33559880}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 49.74405297803999, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:33559880}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 120.23249203650789,level: 3}]->(g);
CREATE (n: Building {id: 16782719, name:"building_tooling_workshopslevel", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:16782719}) CREATE (g)-[r:Demand{max_demand: 89.99999999999999, current_input: 15.558289560320938, level: 3}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782719}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 49.74405297803999, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782719}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 120.23249203650789,level: 3}]->(g);
CREATE (n: Building {id: 83891590, name:"building_universitylevel", level:2});
MATCH (g: Goods{code: 14}), (b: Building{id:83891590}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.274296515308765, level: 2}]->(b);
CREATE (n: Building {id: 16782738, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16782738}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782738}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.581350992679994, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782738}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 40.07749734550263,level: 1}]->(g);
CREATE (n: Building {id: 16782754, name:"building_paper_millslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:16782754}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 10.37219304021396, level: 2}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:16782754}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 0.0, level: 2}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:16782754}) CREATE (g)-[r:Demand{max_demand: 19.999999999999996, current_input: 19.342196387753138, level: 2}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:16782754}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 75.9986468927482,level: 2}]->(g);
CREATE (n: Building {id: 16782757, name:"building_shipyardslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16782757}) CREATE (g)-[r:Demand{max_demand: 13.7694, current_input: 4.3155440603671, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16782757}) CREATE (g)-[r:Demand{max_demand: 27.5388, current_input: 4.760629161597403, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16782757}) CREATE (g)-[r:Demand{max_demand: 13.7694, current_input: 0.5335650956344927, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16782757}) CREATE (g)-[r:Demand{max_demand: 6.8847, current_input: 5.401200900690635, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:16782757}) CREATE (b)-[r:Supply{max_supply: 48.192899999999995, current_output: 15.777847345908867,level: 1}]->(g);
CREATE (n: Building {id: 50337193, name:"building_conscription_centerlevel", level:6});
CREATE (n: Building {id: 50337196, name:"building_chemical_plantslevel", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:50337196}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 43.51994187244456, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:50337196}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 13.363241885794617, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50337196}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.609895781080068, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:50337196}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 70.4745547470493,level: 1}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:50337196}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 37.58642919842629,level: 1}]->(g);
CREATE (n: Building {id: 16782819, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:16782819}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.908827923863079, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16782819}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.845223322280762, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:16782819}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 41.885128115359606,level: 1}]->(g);
CREATE (n: Building {id: 16782838, name:"building_tooling_workshopslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:16782838}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 20.74438608042792, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16782838}) CREATE (g)-[r:Demand{max_demand: 80.00000000000001, current_input: 66.32540397071999, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16782838}) CREATE (b)-[r:Supply{max_supply: 320.00000000000006, current_output: 160.30998938201054,level: 4}]->(g);
CREATE (n: Building {id: 50337271, name:"building_subsistence_farmslevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:50337271}) CREATE (b)-[r:Supply{max_supply: 9.880999999999998, current_output: 10.8691,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:50337271}) CREATE (b)-[r:Supply{max_supply: 1.9762, current_output: 2.17382,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:50337271}) CREATE (b)-[r:Supply{max_supply: 1.9762, current_output: 2.17382,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:50337271}) CREATE (b)-[r:Supply{max_supply: 1.9762, current_output: 2.17382,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:50337271}) CREATE (b)-[r:Supply{max_supply: 1.9762, current_output: 2.17382,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:50337271}) CREATE (b)-[r:Supply{max_supply: 1.9762, current_output: 2.17382,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:50337271}) CREATE (b)-[r:Supply{max_supply: 2.766672727272727, current_output: 3.04334,level: 4}]->(g);
CREATE (n: Building {id: 50337302, name:"building_motor_industrylevel", level:1});
MATCH (g: Goods{code: 30}), (b: Building{id:50337302}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 24.87202648901999, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:50337302}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 33.16270198535999,level: 1}]->(g);
CREATE (n: Building {id: 5663, name:"building_urban_centerlevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:5663}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.7286988400356602, level: 2}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5663}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 17.817655847726158, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5663}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.070235070594534, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:5663}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 54.63622189430194,level: 2}]->(g);
CREATE (n: Building {id: 33560216, name:"building_coal_minelevel", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:33560216}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 14.399886287627613, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:33560216}) CREATE (b)-[r:Supply{max_supply: 59.99999999999999, current_output: 57.59954515051045,level: 1}]->(g);
CREATE (n: Building {id: 16783043, name:"building_arms_industrylevel", level:2});
MATCH (g: Goods{code: 24}), (b: Building{id:16783043}) CREATE (g)-[r:Demand{max_demand: 25.356396946564885, current_input: 24.36723320401854, level: 2}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16783043}) CREATE (g)-[r:Demand{max_demand: 12.678198473282443, current_input: 0.4912809694590923, level: 2}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16783043}) CREATE (b)-[r:Supply{max_supply: 19.017297709923664, current_output: 9.506173178601271,level: 2}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:16783043}) CREATE (b)-[r:Supply{max_supply: 19.017297709923664, current_output: 9.506173178601271,level: 2}]->(g);
CREATE (n: Building {id: 16783081, name:"building_tooling_workshopslevel", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:16783081}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 20.74438608042792, level: 4}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16783081}) CREATE (g)-[r:Demand{max_demand: 80.00000000000001, current_input: 66.32540397071999, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783081}) CREATE (b)-[r:Supply{max_supply: 320.00000000000006, current_output: 160.30998938201054,level: 4}]->(g);
CREATE (n: Building {id: 5866, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:5866}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.908827923863079, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:5866}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.845223322280762, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:5866}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 41.885128115359606,level: 1}]->(g);
CREATE (n: Building {id: 5873, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:5873}) CREATE (g)-[r:Demand{max_demand: 9.825, current_input: 9.441722604911167, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:5873}) CREATE (g)-[r:Demand{max_demand: 9.825, current_input: 0.3807193533929503, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:5873}) CREATE (g)-[r:Demand{max_demand: 9.825, current_input: 8.145588675154046, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:5873}) CREATE (g)-[r:Demand{max_demand: 4.9125, current_input: 4.715962759198043, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:5873}) CREATE (b)-[r:Supply{max_supply: 24.5625, current_output: 17.124972594908908,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:5873}) CREATE (b)-[r:Supply{max_supply: 14.737499999999999, current_output: 10.274983556945346,level: 1}]->(g);
CREATE (n: Building {id: 16783117, name:"building_munition_plantslevel", level:1});
MATCH (g: Goods{code: 25}), (b: Building{id:16783117}) CREATE (g)-[r:Demand{max_demand: 19.360599999999998, current_input: 8.55373667472271, level: 1}]->(b);
MATCH (g: Goods{code: 34}), (b: Building{id:16783117}) CREATE (g)-[r:Demand{max_demand: 19.360599999999998, current_input: 28.28452159533588, level: 1}]->(b);
MATCH (g: Goods{code: 0}), (b: Building{id:16783117}) CREATE (b)-[r:Supply{max_supply: 48.4015, current_output: 34.89292084340339,level: 1}]->(g);
CREATE (n: Building {id: 5916, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:5916}) CREATE (g)-[r:Demand{max_demand: 8.8432, current_input: 8.498223037124726, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:5916}) CREATE (g)-[r:Demand{max_demand: 8.8432, current_input: 0.34267454309664513, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:5916}) CREATE (g)-[r:Demand{max_demand: 13.264799999999997, current_input: 10.997415232385078, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:5916}) CREATE (b)-[r:Supply{max_supply: 8.8432, current_output: 5.39083591171492,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:5916}) CREATE (b)-[r:Supply{max_supply: 22.107999999999997, current_output: 13.477089779287297,level: 1}]->(g);
CREATE (n: Building {id: 16783166, name:"building_arms_industrylevel", level:1});
MATCH (g: Goods{code: 26}), (b: Building{id:16783166}) CREATE (g)-[r:Demand{max_demand: 9.365799999999998, current_input: 0.3629253251916228, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16783166}) CREATE (g)-[r:Demand{max_demand: 23.414499999999997, current_input: 19.412202140905283, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783166}) CREATE (g)-[r:Demand{max_demand: 4.682899999999999, current_input: 4.495548499755423, level: 1}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:16783166}) CREATE (b)-[r:Supply{max_supply: 18.731599999999997, current_output: 11.412602120709721,level: 1}]->(g);
MATCH (g: Goods{code: 2}), (b: Building{id:16783166}) CREATE (b)-[r:Supply{max_supply: 23.414499999999997, current_output: 14.265752650887153,level: 1}]->(g);
CREATE (n: Building {id: 117446484, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:117446484}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:117446484}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:117446484}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.67109819387657, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:117446484}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 37.9993234463741,level: 1}]->(g);
CREATE (n: Building {id: 100669305, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:100669305}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.908827923863079, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:100669305}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.845223322280762, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:100669305}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 41.885128115359606,level: 1}]->(g);
CREATE (n: Building {id: 16783247, name:"building_chemical_plantslevel", level:1});
MATCH (g: Goods{code: 22}), (b: Building{id:16783247}) CREATE (g)-[r:Demand{max_demand: 44.99999999999999, current_input: 43.51994187244456, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16783247}) CREATE (g)-[r:Demand{max_demand: 14.999999999999998, current_input: 13.363241885794617, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783247}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.609895781080068, level: 1}]->(b);
MATCH (g: Goods{code: 32}), (b: Building{id:16783247}) CREATE (b)-[r:Supply{max_supply: 75.0, current_output: 70.4745547470493,level: 1}]->(g);
MATCH (g: Goods{code: 34}), (b: Building{id:16783247}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 37.58642919842629,level: 1}]->(g);
CREATE (n: Building {id: 16783256, name:"building_railwaylevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16783256}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.8643494200178301, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16783256}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 8.908827923863079, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:16783256}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 7.845223322280762, level: 1}]->(b);
MATCH (g: Goods{code: 16}), (b: Building{id:16783256}) CREATE (b)-[r:Supply{max_supply: 65.0, current_output: 40.045958520055585,level: 1}]->(g);
CREATE (n: Building {id: 16783303, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:16783303}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 26.726483771589233, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:16783303}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 57.659374686480405, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:16783303}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 83.33425667224415,level: 1}]->(g);
CREATE (n: Building {id: 100669392, name:"building_motor_industrylevel", level:1});
MATCH (g: Goods{code: 30}), (b: Building{id:100669392}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 24.872026489019994, level: 1}]->(b);
MATCH (g: Goods{code: 29}), (b: Building{id:100669392}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 33.16270198535999,level: 1}]->(g);
CREATE (n: Building {id: 134223850, name:"building_conscription_centerlevel", level:3});
CREATE (n: Building {id: 16783340, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16783340}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:16783340}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:16783340}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.67109819387657, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:16783340}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 37.9993234463741,level: 1}]->(g);
CREATE (n: Building {id: 16783354, name:"building_paper_millslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16783354}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:16783354}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 22}), (b: Building{id:16783354}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.67109819387657, level: 1}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:16783354}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 37.9993234463741,level: 1}]->(g);
CREATE (n: Building {id: 6155, name:"building_urban_centerlevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6155}) CREATE (g)-[r:Demand{max_demand: 0.46, current_input: 0.07952014664164037, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:6155}) CREATE (g)-[r:Demand{max_demand: 0.92, current_input: 0.8196121689954033, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:6155}) CREATE (g)-[r:Demand{max_demand: 0.46, current_input: 0.04923081324734858, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:6155}) CREATE (b)-[r:Supply{max_supply: 6.44, current_output: 2.5132662071378893,level: 1}]->(g);
CREATE (n: Building {id: 16783378, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783378}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.1341554899756705, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783378}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16783378}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.7750012282808151, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783378}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 19.19984838350349, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16783378}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 33.413128055782835,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:16783378}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 14.850279135903484,level: 1}]->(g);
CREATE (n: Building {id: 16783397, name:"building_furniture_manufacturieslevel", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:16783397}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 3.1341554899756705, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:16783397}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:16783397}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.4544139619315395, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:16783397}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 0.7750012282808151, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:16783397}) CREATE (g)-[r:Demand{max_demand: 24.999999999999996, current_input: 23.999810479379356, level: 1}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:16783397}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 42.766392707579804,level: 1}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:16783397}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 19.007285647813248,level: 1}]->(g);
CREATE (n: Building {id: 33560621, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:33560621}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 26.726483771589233, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:33560621}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 57.659374686480405, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:33560621}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 83.33425667224415,level: 1}]->(g);
CREATE (n: Building {id: 50337841, name:"building_steel_millslevel", level:1});
MATCH (g: Goods{code: 23}), (b: Building{id:50337841}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 31.18089773352078, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50337841}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 57.659374686480405, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50337841}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 4.799962095875872, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:50337841}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 84.35594369008469,level: 1}]->(g);
CREATE (n: Building {id: 251664457, name:"building_subsistence_fishing_villageslevel", level:18});
MATCH (g: Goods{code: 7}), (b: Building{id:251664457}) CREATE (b)-[r:Supply{max_supply: 0.004672727272727272, current_output: 0.00514,level: 18}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:251664457}) CREATE (b)-[r:Supply{max_supply: 0.018718181818181818, current_output: 0.02059,level: 18}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:251664457}) CREATE (b)-[r:Supply{max_supply: 0.002336363636363636, current_output: 0.00257,level: 18}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:251664457}) CREATE (b)-[r:Supply{max_supply: 0.0070181818181818175, current_output: 0.00772,level: 18}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:251664457}) CREATE (b)-[r:Supply{max_supply: 0.004672727272727272, current_output: 0.00514,level: 18}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:251664457}) CREATE (b)-[r:Supply{max_supply: 0.004672727272727272, current_output: 0.00514,level: 18}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:251664457}) CREATE (b)-[r:Supply{max_supply: 0.004672727272727272, current_output: 0.00514,level: 18}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:251664457}) CREATE (b)-[r:Supply{max_supply: 0.006545454545454544, current_output: 0.0072,level: 18}]->(g);
CREATE (n: Building {id: 218110029, name:"building_tooling_workshopslevel", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:218110029}) CREATE (g)-[r:Demand{max_demand: 59.99999999999999, current_input: 10.37219304021396, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:218110029}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 33.16270198535999, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:218110029}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 80.15499469100526,level: 2}]->(g);
CREATE (n: Building {id: 50337871, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:50337871}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:50337871}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.581350992679994, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50337871}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 40.07749734550263,level: 1}]->(g);
CREATE (n: Building {id: 6234, name:"building_tooling_workshopslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:6234}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 5.18609652010698, level: 1}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:6234}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 16.581350992679994, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:6234}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 40.07749734550263,level: 1}]->(g);
CREATE (n: Building {id: 50337903, name:"building_steel_millslevel", level:2});
MATCH (g: Goods{code: 23}), (b: Building{id:50337903}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 62.36179546704156, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:50337903}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 115.31874937296081, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:50337903}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 9.599924191751745, level: 2}]->(b);
MATCH (g: Goods{code: 30}), (b: Building{id:50337903}) CREATE (b)-[r:Supply{max_supply: 180.0, current_output: 168.7118873801694,level: 2}]->(g);
CREATE (n: Building {id: 16783478, name:"building_trade_centerlevel", level:17});
CREATE (n: Building {id: 6285, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 21}), (b: Building{id:6285}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:6285}) CREATE (g)-[r:Demand{max_demand: 35.0, current_input: 15.46340421346936, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:6285}) CREATE (b)-[r:Supply{max_supply: 49.99999999999999, current_output: 11.045288723906685,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:6285}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 8.836230979125348,level: 1}]->(g);
CREATE (n: Building {id: 16783546, name:"building_glassworkslevel", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:16783546}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.4573976800713204, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:16783546}) CREATE (g)-[r:Demand{max_demand: 29.999999999999996, current_input: 0.0, level: 1}]->(b);
MATCH (g: Goods{code: 25}), (b: Building{id:16783546}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 4.418115489562674, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:16783546}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 4.097876219732222,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:16783546}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 8.195752439464444,level: 1}]->(g);
CREATE (n: Building {id: 16783551, name:"building_trade_centerlevel", level:3});
CREATE (n: Building {id: 33560768, name:"building_trade_centerlevel", level:3});
CREATE (n: Building {id: 33560769, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 6394, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 6397, name:"building_conscription_centerlevel", level:1});
CREATE (n: Building {id: 33560869, name:"building_subsistence_rice_paddieslevel", level:4});
MATCH (g: Goods{code: 7}), (b: Building{id:33560869}) CREATE (b)-[r:Supply{max_supply: 0.009899999999999999, current_output: 0.01089,level: 4}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:33560869}) CREATE (b)-[r:Supply{max_supply: 0.0016454545454545452, current_output: 0.00181,level: 4}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:33560869}) CREATE (b)-[r:Supply{max_supply: 0.0016454545454545452, current_output: 0.00181,level: 4}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:33560869}) CREATE (b)-[r:Supply{max_supply: 0.0021999999999999997, current_output: 0.00242,level: 4}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:33560869}) CREATE (b)-[r:Supply{max_supply: 0.0021999999999999997, current_output: 0.00242,level: 4}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:33560869}) CREATE (b)-[r:Supply{max_supply: 0.0021999999999999997, current_output: 0.00242,level: 4}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:33560869}) CREATE (b)-[r:Supply{max_supply: 0.0032999999999999995, current_output: 0.00363,level: 4}]->(g);
CREATE (n: Building {id: 16783654, name:"building_subsistence_fishing_villageslevel", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:16783654}) CREATE (b)-[r:Supply{max_supply: 0.0015999999999999999, current_output: 0.00176,level: 16}]->(g);
MATCH (g: Goods{code: 8}), (b: Building{id:16783654}) CREATE (b)-[r:Supply{max_supply: 0.0063999999999999994, current_output: 0.00704,level: 16}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:16783654}) CREATE (b)-[r:Supply{max_supply: 0.0007999999999999999, current_output: 0.00088,level: 16}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:16783654}) CREATE (b)-[r:Supply{max_supply: 0.0024, current_output: 0.00264,level: 16}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:16783654}) CREATE (b)-[r:Supply{max_supply: 0.0015999999999999999, current_output: 0.00176,level: 16}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:16783654}) CREATE (b)-[r:Supply{max_supply: 0.0015999999999999999, current_output: 0.00176,level: 16}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:16783654}) CREATE (b)-[r:Supply{max_supply: 0.0015999999999999999, current_output: 0.00176,level: 16}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:16783654}) CREATE (b)-[r:Supply{max_supply: 0.002236363636363636, current_output: 0.00246,level: 16}]->(g);
CREATE (n: Building {id: 6441, name:"building_subsistence_rice_paddieslevel", level:142});
MATCH (g: Goods{code: 7}), (b: Building{id:6441}) CREATE (b)-[r:Supply{max_supply: 0.006381818181818181, current_output: 0.00702,level: 142}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:6441}) CREATE (b)-[r:Supply{max_supply: 0.0010636363636363636, current_output: 0.00117,level: 142}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:6441}) CREATE (b)-[r:Supply{max_supply: 0.0010636363636363636, current_output: 0.00117,level: 142}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:6441}) CREATE (b)-[r:Supply{max_supply: 0.001418181818181818, current_output: 0.00156,level: 142}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:6441}) CREATE (b)-[r:Supply{max_supply: 0.001418181818181818, current_output: 0.00156,level: 142}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:6441}) CREATE (b)-[r:Supply{max_supply: 0.001418181818181818, current_output: 0.00156,level: 142}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:6441}) CREATE (b)-[r:Supply{max_supply: 0.002127272727272727, current_output: 0.00234,level: 142}]->(g);
