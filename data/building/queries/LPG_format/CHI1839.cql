CREATE (n: Goods {name:"ammunition", code: 0, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"small_arms", code: 1, base_price:60, current_price:99.24569822893329, pop_demand:1.3571739425883182});
CREATE (n: Goods {name:"artillery", code: 2, base_price:70, current_price:17.499999999999993, pop_demand:0});
CREATE (n: Goods {name:"tanks", code: 3, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"aeroplanes", code: 4, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"manowars", code: 5, base_price:70, current_price:91.2298385369036, pop_demand:0});
CREATE (n: Goods {name:"ironclads", code: 6, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"grain", code: 7, base_price:20, current_price:14.188735152818126, pop_demand:28914.144499616494});
CREATE (n: Goods {name:"fish", code: 8, base_price:20, current_price:14.993980440792098, pop_demand:454.8636538775203});
CREATE (n: Goods {name:"fabric", code: 9, base_price:20, current_price:9.0694444796999, pop_demand:905.1568457472486});
CREATE (n: Goods {name:"wood", code: 10, base_price:20, current_price:24.590628515852643, pop_demand:5430.941074483516});
CREATE (n: Goods {name:"groceries", code: 11, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clothes", code: 12, base_price:30, current_price:28.240524363117785, pop_demand:8211.06415913378});
CREATE (n: Goods {name:"furniture", code: 13, base_price:30, current_price:25.71363141441053, pop_demand:7414.458978113219});
CREATE (n: Goods {name:"paper", code: 14, base_price:30, current_price:40.280011677996974, pop_demand:214.54426723087616});
CREATE (n: Goods {name:"services", code: 15, base_price:30, current_price:15.774092711829795, pop_demand:3922.6333227816094});
CREATE (n: Goods {name:"transportation", code: 16, base_price:30, current_price:45.2535011064525, pop_demand:1551.9371583333343});
CREATE (n: Goods {name:"electricity", code: 17, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"clippers", code: 18, base_price:60, current_price:76.06369291804519, pop_demand:1.102062442474188});
CREATE (n: Goods {name:"steamers", code: 19, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"silk", code: 20, base_price:40, current_price:12.836879432624112, pop_demand:0});
CREATE (n: Goods {name:"dye", code: 21, base_price:40, current_price:15.674598073410904, pop_demand:0});
CREATE (n: Goods {name:"sulfur", code: 22, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"coal", code: 23, base_price:30, current_price:50.09925828259345, pop_demand:0.401116512831926});
CREATE (n: Goods {name:"iron", code: 24, base_price:40, current_price:66.35208141806578, pop_demand:0});
CREATE (n: Goods {name:"lead", code: 25, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"hardwood", code: 26, base_price:40, current_price:65.50415288850103, pop_demand:0});
CREATE (n: Goods {name:"rubber", code: 27, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"oil", code: 28, base_price:40, current_price:40, pop_demand:0});
CREATE (n: Goods {name:"engines", code: 29, base_price:60, current_price:60, pop_demand:0});
CREATE (n: Goods {name:"steel", code: 30, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"glass", code: 31, base_price:40, current_price:38.22746136177535, pop_demand:288.42366904780755});
CREATE (n: Goods {name:"fertilizer", code: 32, base_price:30, current_price:30, pop_demand:0});
CREATE (n: Goods {name:"tools", code: 33, base_price:40, current_price:64.29814707628029, pop_demand:0});
CREATE (n: Goods {name:"explosives", code: 34, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"porcelain", code: 35, base_price:70, current_price:102.11229004932031, pop_demand:2224.7126789579465});
CREATE (n: Goods {name:"meat", code: 36, base_price:30, current_price:38.2971794823423, pop_demand:893.5593632156663});
CREATE (n: Goods {name:"fruit", code: 37, base_price:30, current_price:43.72269514395158, pop_demand:2756.7968736169464});
CREATE (n: Goods {name:"liquor", code: 38, base_price:30, current_price:20.40585704980673, pop_demand:4971.698233782816});
CREATE (n: Goods {name:"wine", code: 39, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"tea", code: 40, base_price:50, current_price:37.77000536215643, pop_demand:2931.147135});
CREATE (n: Goods {name:"coffee", code: 41, base_price:50, current_price:50, pop_demand:0});
CREATE (n: Goods {name:"sugar", code: 42, base_price:30, current_price:40.396727220919374, pop_demand:868.3298284964351});
CREATE (n: Goods {name:"tobacco", code: 43, base_price:40, current_price:70.0, pop_demand:128.12557692863905});
CREATE (n: Goods {name:"opium", code: 44, base_price:50, current_price:82.32246387604441, pop_demand:3375.157523121099});
CREATE (n: Goods {name:"automobiles", code: 45, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"telephones", code: 46, base_price:70, current_price:70, pop_demand:0});
CREATE (n: Goods {name:"radios", code: 47, base_price:80, current_price:80, pop_demand:0});
CREATE (n: Goods {name:"luxury_clothes", code: 48, base_price:60, current_price:83.75325750801704, pop_demand:1016.6264314706287});
CREATE (n: Goods {name:"luxury_furniture", code: 49, base_price:60, current_price:88.96433979320452, pop_demand:1724.9125180784342});
CREATE (n: Goods {name:"gold", code: 50, base_price:100, current_price:100, pop_demand:0});
CREATE (n: Goods {name:"fine_art", code: 51, base_price:200, current_price:200, pop_demand:0});
CREATE (n: Building {id: 2040, name:"building_wheat_farm", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:2040}) CREATE (g)-[r:Demand{max_demand: 0.98422, current_input: 0.18721607240177848, level: 1}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2040}) CREATE (b)-[r:Supply{max_supply: 4.9211, current_output: 0.9360803620088924,level: 1}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2040}) CREATE (b)-[r:Supply{max_supply: 6.88954, current_output: 1.3105125068124495,level: 1}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2040}) CREATE (b)-[r:Supply{max_supply: 4.9211, current_output: 0.9360803620088924,level: 1}]->(g);
CREATE (n: Building {id: 2244, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2244}) CREATE (g)-[r:Demand{max_demand: 0.9371980198019803, current_input: 1.5299184538680468, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2244}) CREATE (g)-[r:Demand{max_demand: 0.9371980198019803, current_input: 0.17827165911081969, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2244}) CREATE (b)-[r:Supply{max_supply: 1.8743960396039605, current_output: 1.1154696789128,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2244}) CREATE (b)-[r:Supply{max_supply: 3.748792079207921, current_output: 2.2309393578256,level: 2}]->(g);
CREATE (n: Building {id: 2245, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2245}) CREATE (g)-[r:Demand{max_demand: 9.591098901098901, current_input: 15.656882421460008, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2245}) CREATE (b)-[r:Supply{max_supply: 19.182197802197802, current_output: 19.182197802197802,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2245}) CREATE (b)-[r:Supply{max_supply: 19.182197802197802, current_output: 19.182197802197802,level: 2}]->(g);
CREATE (n: Building {id: 2246, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2246}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 54.324137614009324, level: 10}]->(b);
CREATE (n: Building {id: 2247, name:"building_logging_camp", level:10});
MATCH (g: Goods{code: 33}), (b: Building{id:2247}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 9.510885391567863, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2247}) CREATE (b)-[r:Supply{max_supply: 600.0, current_output: 114.13062469881436,level: 10}]->(g);
CREATE (n: Building {id: 2248, name:"building_opium_plantation", level:6});
MATCH (g: Goods{code: 44}), (b: Building{id:2248}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 144.0,level: 6}]->(g);
CREATE (n: Building {id: 2249, name:"building_paper_mills", level:12});
MATCH (g: Goods{code: 10}), (b: Building{id:2249}) CREATE (g)-[r:Demand{max_demand: 360.0, current_input: 249.8392850042919, level: 12}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2249}) CREATE (b)-[r:Supply{max_supply: 479.99999999999994, current_output: 333.1190466723892,level: 12}]->(g);
CREATE (n: Building {id: 2250, name:"building_government_administration", level:8});
MATCH (g: Goods{code: 14}), (b: Building{id:2250}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 43.459310091207456, level: 8}]->(b);
CREATE (n: Building {id: 2251, name:"building_logging_camp", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:2251}) CREATE (g)-[r:Demand{max_demand: 27.006295238095237, current_input: 5.1370755772073755, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2251}) CREATE (b)-[r:Supply{max_supply: 324.0756, current_output: 61.644917796071816,level: 6}]->(g);
CREATE (n: Building {id: 2252, name:"building_tea_plantation", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2252}) CREATE (b)-[r:Supply{max_supply: 199.296, current_output: 217.23264,level: 10}]->(g);
CREATE (n: Building {id: 2253, name:"building_opium_plantation", level:10});
MATCH (g: Goods{code: 44}), (b: Building{id:2253}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 218.0,level: 10}]->(g);
CREATE (n: Building {id: 2254, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2254}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 54.324137614009324, level: 10}]->(b);
CREATE (n: Building {id: 2255, name:"building_furniture_manufacturies", level:7});
MATCH (g: Goods{code: 9}), (b: Building{id:2255}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 258.0205579125064, level: 7}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2255}) CREATE (g)-[r:Demand{max_demand: 104.99999999999999, current_input: 72.86979145958512, level: 7}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2255}) CREATE (g)-[r:Demand{max_demand: 104.99999999999999, current_input: 15.750985724891004, level: 7}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2255}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 86.05367874865607,level: 7}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2255}) CREATE (b)-[r:Supply{max_supply: 175.0, current_output: 107.56709843582009,level: 7}]->(g);
CREATE (n: Building {id: 2256, name:"building_glassworks", level:8});
MATCH (g: Goods{code: 10}), (b: Building{id:2256}) CREATE (g)-[r:Demand{max_demand: 240.0, current_input: 166.55952333619456, level: 8}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2256}) CREATE (g)-[r:Demand{max_demand: 39.99999999999999, current_input: 211.46872156862742, level: 8}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2256}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 67.75992055603241,level: 8}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2256}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 135.51984111206482,level: 8}]->(g);
CREATE (n: Building {id: 2257, name:"building_logging_camp", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:2257}) CREATE (g)-[r:Demand{max_demand: 26.625895238095236, current_input: 5.064716761148326, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2257}) CREATE (b)-[r:Supply{max_supply: 186.38129523809522, current_output: 35.45302276282994,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2257}) CREATE (b)-[r:Supply{max_supply: 53.251799999999996, current_output: 10.12943533389387,level: 6}]->(g);
CREATE (n: Building {id: 2258, name:"building_opium_plantation", level:5});
MATCH (g: Goods{code: 44}), (b: Building{id:2258}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 104.0,level: 5}]->(g);
CREATE (n: Building {id: 2259, name:"building_dye_plantation", level:8});
MATCH (g: Goods{code: 21}), (b: Building{id:2259}) CREATE (b)-[r:Supply{max_supply: 196.164, current_output: 209.89548,level: 8}]->(g);
CREATE (n: Building {id: 2260, name:"building_tea_plantation", level:4});
MATCH (g: Goods{code: 40}), (b: Building{id:2260}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 82.4,level: 4}]->(g);
CREATE (n: Building {id: 2261, name:"building_government_administration", level:8});
MATCH (g: Goods{code: 14}), (b: Building{id:2261}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 43.459310091207456, level: 8}]->(b);
CREATE (n: Building {id: 2262, name:"building_iron_mine", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:2262}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 0.9510885391567865, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2262}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 3.804354156627146,level: 1}]->(g);
CREATE (n: Building {id: 2263, name:"building_rice_farm", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:2263}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 85.6,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2263}) CREATE (b)-[r:Supply{max_supply: 96.0, current_output: 102.72,level: 8}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2263}) CREATE (b)-[r:Supply{max_supply: 144.0, current_output: 154.08,level: 8}]->(g);
CREATE (n: Building {id: 2264, name:"building_dye_plantation", level:6});
MATCH (g: Goods{code: 21}), (b: Building{id:2264}) CREATE (b)-[r:Supply{max_supply: 143.88449523809524, current_output: 151.07872,level: 6}]->(g);
CREATE (n: Building {id: 2265, name:"building_silk_plantation", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2265}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 268.0,level: 10}]->(g);
CREATE (n: Building {id: 2266, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2266}) CREATE (g)-[r:Demand{max_demand: 0.026444444444444444, current_input: 0.04316893837056143, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2266}) CREATE (g)-[r:Demand{max_demand: 0.026444444444444444, current_input: 0.005030201607095892, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2266}) CREATE (b)-[r:Supply{max_supply: 0.052899999999999996, current_output: 0.031481258372139394,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2266}) CREATE (b)-[r:Supply{max_supply: 0.10579999999999999, current_output: 0.06296251674427879,level: 1}]->(g);
CREATE (n: Building {id: 2267, name:"building_livestock_ranch", level:1});
MATCH (g: Goods{code: 7}), (b: Building{id:2267}) CREATE (g)-[r:Demand{max_demand: 0.41564545454545454, current_input: 0.6785157861407164, level: 1}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2267}) CREATE (g)-[r:Demand{max_demand: 0.41564545454545454, current_input: 0.07906312563415897, level: 1}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2267}) CREATE (b)-[r:Supply{max_supply: 0.8312999999999999, current_output: 0.49471399026010365,level: 1}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2267}) CREATE (b)-[r:Supply{max_supply: 1.6625999999999999, current_output: 0.9894279805202073,level: 1}]->(g);
CREATE (n: Building {id: 2268, name:"building_government_administration", level:9});
MATCH (g: Goods{code: 14}), (b: Building{id:2268}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 48.89172385260841, level: 9}]->(b);
CREATE (n: Building {id: 2269, name:"building_banana_plantation", level:8});
MATCH (g: Goods{code: 37}), (b: Building{id:2269}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 304.8,level: 8}]->(g);
CREATE (n: Building {id: 2270, name:"building_rice_farm", level:7});
MATCH (g: Goods{code: 33}), (b: Building{id:2270}) CREATE (g)-[r:Demand{max_demand: 14.0, current_input: 2.663047909639002, level: 7}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2270}) CREATE (b)-[r:Supply{max_supply: 70.0, current_output: 13.315239548195011,level: 7}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2270}) CREATE (b)-[r:Supply{max_supply: 84.0, current_output: 15.978287457834014,level: 7}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2270}) CREATE (b)-[r:Supply{max_supply: 125.99999999999999, current_output: 23.967431186751018,level: 7}]->(g);
CREATE (n: Building {id: 2271, name:"building_tea_plantation", level:8});
MATCH (g: Goods{code: 40}), (b: Building{id:2271}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 203.2,level: 8}]->(g);
CREATE (n: Building {id: 2272, name:"building_dye_plantation", level:10});
MATCH (g: Goods{code: 21}), (b: Building{id:2272}) CREATE (b)-[r:Supply{max_supply: 250.0, current_output: 322.5,level: 10}]->(g);
CREATE (n: Building {id: 2273, name:"building_silk_plantation", level:16});
MATCH (g: Goods{code: 20}), (b: Building{id:2273}) CREATE (b)-[r:Supply{max_supply: 320.0, current_output: 512.0,level: 16}]->(g);
CREATE (n: Building {id: 2274, name:"building_government_administration", level:20});
MATCH (g: Goods{code: 14}), (b: Building{id:2274}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 108.64827522801868, level: 20}]->(b);
CREATE (n: Building {id: 2275, name:"building_construction_sector", level:3});
MATCH (g: Goods{code: 9}), (b: Building{id:2275}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 276.4505977633997, level: 3}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2275}) CREATE (g)-[r:Demand{max_demand: 225.0, current_input: 156.14955312768242, level: 3}]->(b);
CREATE (n: Building {id: 2276, name:"building_arms_industry", level:4});
MATCH (g: Goods{code: 24}), (b: Building{id:2276}) CREATE (g)-[r:Demand{max_demand: 39.590796116504855, current_input: 4.818881829938282, level: 4}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2276}) CREATE (g)-[r:Demand{max_demand: 39.590796116504855, current_input: 5.9389910901727445, level: 4}]->(b);
MATCH (g: Goods{code: 1}), (b: Building{id:2276}) CREATE (b)-[r:Supply{max_supply: 118.77239805825243, current_output: 16.13681069922777,level: 4}]->(g);
CREATE (n: Building {id: 2277, name:"building_tooling_workshops", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2277}) CREATE (g)-[r:Demand{max_demand: 114.78239805825243, current_input: 79.65875628319945, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2277}) CREATE (g)-[r:Demand{max_demand: 76.5215922330097, current_input: 9.313995841974762, level: 4}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2277}) CREATE (b)-[r:Supply{max_supply: 229.56479611650485, current_output: 93.62975122788222,level: 4}]->(g);
CREATE (n: Building {id: 2278, name:"building_furniture_manufacturies", level:5});
MATCH (g: Goods{code: 9}), (b: Building{id:2278}) CREATE (g)-[r:Demand{max_demand: 50.0, current_input: 184.30039850893314, level: 5}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2278}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 52.0498510425608, level: 5}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2278}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 11.250704089207861, level: 5}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2278}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 61.466913391897194,level: 5}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2278}) CREATE (b)-[r:Supply{max_supply: 125.0, current_output: 76.8336417398715,level: 5}]->(g);
CREATE (n: Building {id: 2279, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:2279}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.5706531234940718, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2279}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 2.8532656174703592,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2279}) CREATE (b)-[r:Supply{max_supply: 21.0, current_output: 3.9945718644585027,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2279}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 2.8532656174703592,level: 3}]->(g);
CREATE (n: Building {id: 2280, name:"building_tea_plantation", level:5});
MATCH (g: Goods{code: 40}), (b: Building{id:2280}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 104.0,level: 5}]->(g);
CREATE (n: Building {id: 2281, name:"building_logging_camp", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:2281}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.902177078313573, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2281}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 22.826124939762877,level: 2}]->(g);
CREATE (n: Building {id: 2282, name:"building_forbidden_city", level:1});
CREATE (n: Building {id: 2283, name:"building_logging_camp", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:2283}) CREATE (g)-[r:Demand{max_demand: 28.046695238095236, current_input: 5.3349780804351195, level: 6}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2283}) CREATE (b)-[r:Supply{max_supply: 112.18679999999999, current_output: 21.339915944934912,level: 6}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2283}) CREATE (b)-[r:Supply{max_supply: 112.18679999999999, current_output: 21.339915944934912,level: 6}]->(g);
CREATE (n: Building {id: 2284, name:"building_livestock_ranch", level:8});
MATCH (g: Goods{code: 7}), (b: Building{id:2284}) CREATE (g)-[r:Demand{max_demand: 39.096, current_input: 63.821829121089195, level: 8}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2284}) CREATE (b)-[r:Supply{max_supply: 78.192, current_output: 78.192,level: 8}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2284}) CREATE (b)-[r:Supply{max_supply: 78.192, current_output: 78.192,level: 8}]->(g);
CREATE (n: Building {id: 2285, name:"building_government_administration", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:2285}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 32.5944825684056, level: 6}]->(b);
CREATE (n: Building {id: 2286, name:"building_iron_mine", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:2286}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.804354156627146, level: 4}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2286}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 15.217416626508584,level: 4}]->(g);
CREATE (n: Building {id: 2287, name:"building_logging_camp", level:9});
MATCH (g: Goods{code: 33}), (b: Building{id:2287}) CREATE (g)-[r:Demand{max_demand: 44.61929629629629, current_input: 8.48738026652965, level: 9}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2287}) CREATE (b)-[r:Supply{max_supply: 178.47719444444442, current_output: 33.94952282739367,level: 9}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2287}) CREATE (b)-[r:Supply{max_supply: 178.47719444444442, current_output: 33.94952282739367,level: 9}]->(g);
CREATE (n: Building {id: 2288, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2288}) CREATE (g)-[r:Demand{max_demand: 10.34894117647059, current_input: 16.894013590876554, level: 3}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2288}) CREATE (g)-[r:Demand{max_demand: 10.34894117647059, current_input: 1.9685518690697854, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2288}) CREATE (b)-[r:Supply{max_supply: 20.697892156862746, current_output: 12.317498879940901,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2288}) CREATE (b)-[r:Supply{max_supply: 41.39579411764706, current_output: 24.635003594282328,level: 3}]->(g);
CREATE (n: Building {id: 2289, name:"building_logging_camp", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:2289}) CREATE (g)-[r:Demand{max_demand: 39.998794392523365, current_input: 7.608478985363542, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2289}) CREATE (b)-[r:Supply{max_supply: 159.99519626168225, current_output: 30.433919496925345,level: 8}]->(g);
MATCH (g: Goods{code: 26}), (b: Building{id:2289}) CREATE (b)-[r:Supply{max_supply: 159.99519626168225, current_output: 30.433919496925345,level: 8}]->(g);
CREATE (n: Building {id: 2290, name:"building_livestock_ranch", level:2});
MATCH (g: Goods{code: 7}), (b: Building{id:2290}) CREATE (g)-[r:Demand{max_demand: 9.999999999999998, current_input: 16.324388459456003, level: 2}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2290}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 19.999999999999996,level: 2}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2290}) CREATE (b)-[r:Supply{max_supply: 19.999999999999996, current_output: 19.999999999999996,level: 2}]->(g);
CREATE (n: Building {id: 2291, name:"building_government_administration", level:7});
MATCH (g: Goods{code: 14}), (b: Building{id:2291}) CREATE (g)-[r:Demand{max_demand: 70.0, current_input: 38.02689632980653, level: 7}]->(b);
CREATE (n: Building {id: 2292, name:"building_wheat_farm", level:7});
MATCH (g: Goods{code: 7}), (b: Building{id:2292}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 42.35,level: 7}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2292}) CREATE (b)-[r:Supply{max_supply: 49.0, current_output: 59.29,level: 7}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2292}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 42.35,level: 7}]->(g);
CREATE (n: Building {id: 2293, name:"building_tea_plantation", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:2293}) CREATE (b)-[r:Supply{max_supply: 60.00000000000001, current_output: 70.2,level: 3}]->(g);
CREATE (n: Building {id: 2294, name:"building_government_administration", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:2294}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 21.729655045603735, level: 4}]->(b);
CREATE (n: Building {id: 2295, name:"building_tea_plantation", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:2295}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 61.2,level: 3}]->(g);
CREATE (n: Building {id: 2296, name:"building_livestock_ranch", level:6});
MATCH (g: Goods{code: 7}), (b: Building{id:2296}) CREATE (g)-[r:Demand{max_demand: 29.976295238095236, current_input: 48.93446880420079, level: 6}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2296}) CREATE (b)-[r:Supply{max_supply: 59.9526, current_output: 59.9526,level: 6}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2296}) CREATE (b)-[r:Supply{max_supply: 59.9526, current_output: 59.9526,level: 6}]->(g);
CREATE (n: Building {id: 2297, name:"building_government_administration", level:4});
MATCH (g: Goods{code: 14}), (b: Building{id:2297}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 21.729655045603735, level: 4}]->(b);
CREATE (n: Building {id: 2298, name:"building_tea_plantation", level:5});
MATCH (g: Goods{code: 40}), (b: Building{id:2298}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 119.0,level: 5}]->(g);
CREATE (n: Building {id: 2299, name:"building_livestock_ranch", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2299}) CREATE (g)-[r:Demand{max_demand: 22.50275, current_input: 36.734363240602356, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2299}) CREATE (b)-[r:Supply{max_supply: 45.0055, current_output: 45.0055,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2299}) CREATE (b)-[r:Supply{max_supply: 45.0055, current_output: 45.0055,level: 5}]->(g);
CREATE (n: Building {id: 2300, name:"building_government_administration", level:12});
MATCH (g: Goods{code: 14}), (b: Building{id:2300}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 65.18896513681119, level: 12}]->(b);
CREATE (n: Building {id: 2301, name:"building_paper_mills", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:2301}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 83.27976166809728, level: 4}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2301}) CREATE (b)-[r:Supply{max_supply: 160.0, current_output: 111.03968222412972,level: 4}]->(g);
CREATE (n: Building {id: 2302, name:"building_tea_plantation", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2302}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 248.0,level: 10}]->(g);
CREATE (n: Building {id: 2303, name:"building_silk_plantation", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2303}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 298.0,level: 10}]->(g);
CREATE (n: Building {id: 2304, name:"building_government_administration", level:13});
MATCH (g: Goods{code: 14}), (b: Building{id:2304}) CREATE (g)-[r:Demand{max_demand: 129.99999999999997, current_input: 70.62137889821213, level: 13}]->(b);
CREATE (n: Building {id: 2305, name:"building_banana_plantation", level:7});
MATCH (g: Goods{code: 37}), (b: Building{id:2305}) CREATE (b)-[r:Supply{max_supply: 210.00000000000003, current_output: 264.6,level: 7}]->(g);
CREATE (n: Building {id: 2306, name:"building_fishing_wharf", level:5});
MATCH (g: Goods{code: 8}), (b: Building{id:2306}) CREATE (b)-[r:Supply{max_supply: 117.64999999999999, current_output: 122.356,level: 5}]->(g);
CREATE (n: Building {id: 2307, name:"building_silk_plantation", level:20});
MATCH (g: Goods{code: 20}), (b: Building{id:2307}) CREATE (b)-[r:Supply{max_supply: 400.0, current_output: 656.0,level: 20}]->(g);
CREATE (n: Building {id: 2308, name:"building_rice_farm", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:2308}) CREATE (g)-[r:Demand{max_demand: 16.0, current_input: 3.0434833253017164, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2308}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 15.217416626508582,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2308}) CREATE (b)-[r:Supply{max_supply: 96.0, current_output: 18.2608999518103,level: 8}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2308}) CREATE (b)-[r:Supply{max_supply: 144.0, current_output: 27.391349927715446,level: 8}]->(g);
CREATE (n: Building {id: 2309, name:"building_dye_plantation", level:12});
MATCH (g: Goods{code: 21}), (b: Building{id:2309}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 393.0,level: 12}]->(g);
CREATE (n: Building {id: 2310, name:"building_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2310}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 73.72015940357325, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2310}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 27.75992055603243, level: 1}]->(b);
MATCH (g: Goods{code: 18}), (b: Building{id:2310}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 29.64496524326419,level: 1}]->(g);
CREATE (n: Building {id: 2311, name:"building_military_shipyards", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2311}) CREATE (g)-[r:Demand{max_demand: 5.732, current_input: 21.128197685064098, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2311}) CREATE (g)-[r:Demand{max_demand: 11.464, current_input: 7.955993231358895, level: 1}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2311}) CREATE (g)-[r:Demand{max_demand: 2.866, current_input: 0.34884156620547313, level: 1}]->(b);
MATCH (g: Goods{code: 5}), (b: Building{id:2311}) CREATE (b)-[r:Supply{max_supply: 10.031, current_output: 6.071146519719397,level: 1}]->(g);
CREATE (n: Building {id: 2312, name:"building_port", level:5});
MATCH (g: Goods{code: 18}), (b: Building{id:2312}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 16.077312080903692, level: 5}]->(b);
CREATE (n: Building {id: 2316, name:"building_government_administration", level:11});
MATCH (g: Goods{code: 14}), (b: Building{id:2316}) CREATE (g)-[r:Demand{max_demand: 109.99999999999999, current_input: 59.75655137541026, level: 11}]->(b);
CREATE (n: Building {id: 2317, name:"building_silk_plantation", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2317}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 268.0,level: 10}]->(g);
CREATE (n: Building {id: 2318, name:"building_rice_farm", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:2318}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.902177078313573, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2318}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 9.510885391567864,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2318}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 11.413062469881439,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2318}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 17.119593704822154,level: 5}]->(g);
CREATE (n: Building {id: 2319, name:"building_dye_plantation", level:10});
MATCH (g: Goods{code: 21}), (b: Building{id:2319}) CREATE (b)-[r:Supply{max_supply: 249.20999999999995, current_output: 271.6389,level: 10}]->(g);
CREATE (n: Building {id: 2320, name:"building_fishing_wharf", level:4});
MATCH (g: Goods{code: 8}), (b: Building{id:2320}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 103.0,level: 4}]->(g);
CREATE (n: Building {id: 2321, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2321}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 54.324137614009324, level: 10}]->(b);
CREATE (n: Building {id: 2322, name:"building_textile_mills", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:2322}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 737.2015940357326, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2322}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 1269.0, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2322}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2322}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 240.0,level: 8}]->(g);
CREATE (n: Building {id: 2323, name:"building_tea_plantation", level:15});
MATCH (g: Goods{code: 40}), (b: Building{id:2323}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 342.0,level: 15}]->(g);
CREATE (n: Building {id: 2324, name:"building_rice_farm", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:2324}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 1.5217416626508582, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2324}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 7.608708313254291,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2324}) CREATE (b)-[r:Supply{max_supply: 48.0, current_output: 9.13044997590515,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2324}) CREATE (b)-[r:Supply{max_supply: 72.0, current_output: 13.695674963857723,level: 4}]->(g);
CREATE (n: Building {id: 2325, name:"building_fishing_wharf", level:3});
MATCH (g: Goods{code: 8}), (b: Building{id:2325}) CREATE (b)-[r:Supply{max_supply: 74.90474509803921, current_output: 76.40284,level: 3}]->(g);
CREATE (n: Building {id: 2326, name:"building_government_administration", level:12});
MATCH (g: Goods{code: 14}), (b: Building{id:2326}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 65.18896513681119, level: 12}]->(b);
CREATE (n: Building {id: 2327, name:"building_glassworks", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2327}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 104.0997020851216, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2327}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 132.16795098039216, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2327}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 42.34995034752027,level: 5}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2327}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 84.69990069504054,level: 5}]->(g);
CREATE (n: Building {id: 2328, name:"building_tea_plantation", level:15});
MATCH (g: Goods{code: 40}), (b: Building{id:2328}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 342.0,level: 15}]->(g);
CREATE (n: Building {id: 2329, name:"building_rice_farm", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:2329}) CREATE (g)-[r:Demand{max_demand: 16.0, current_input: 3.0434833253017164, level: 8}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2329}) CREATE (b)-[r:Supply{max_supply: 79.99999999999999, current_output: 15.217416626508578,level: 8}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2329}) CREATE (b)-[r:Supply{max_supply: 96.0, current_output: 18.2608999518103,level: 8}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2329}) CREATE (b)-[r:Supply{max_supply: 144.0, current_output: 27.391349927715446,level: 8}]->(g);
CREATE (n: Building {id: 2330, name:"building_government_administration", level:17});
MATCH (g: Goods{code: 14}), (b: Building{id:2330}) CREATE (g)-[r:Demand{max_demand: 170.0, current_input: 92.35103394381586, level: 17}]->(b);
CREATE (n: Building {id: 2331, name:"building_construction_sector", level:2});
MATCH (g: Goods{code: 9}), (b: Building{id:2331}) CREATE (g)-[r:Demand{max_demand: 40.0, current_input: 147.4403188071465, level: 2}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2331}) CREATE (g)-[r:Demand{max_demand: 80.0, current_input: 55.51984111206486, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2331}) CREATE (g)-[r:Demand{max_demand: 100.0, current_input: 12.171722477511274, level: 2}]->(b);
MATCH (g: Goods{code: 33}), (b: Building{id:2331}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 3.804354156627146, level: 2}]->(b);
CREATE (n: Building {id: 2332, name:"building_glassworks", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2332}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 208.1994041702432, level: 10}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2332}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 264.3359019607843, level: 10}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2332}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 84.69990069504053,level: 10}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2332}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 169.39980139008105,level: 10}]->(g);
CREATE (n: Building {id: 2333, name:"building_tea_plantation", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2333}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 218.0,level: 10}]->(g);
CREATE (n: Building {id: 2334, name:"building_rice_farm", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:2334}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.902177078313573, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2334}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 9.510885391567864,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2334}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 11.413062469881439,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2334}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 17.119593704822154,level: 5}]->(g);
CREATE (n: Building {id: 2335, name:"building_government_administration", level:15});
MATCH (g: Goods{code: 14}), (b: Building{id:2335}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 81.486206421014, level: 15}]->(b);
CREATE (n: Building {id: 2336, name:"building_construction_sector", level:1});
MATCH (g: Goods{code: 9}), (b: Building{id:2336}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 92.15019925446657, level: 1}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2336}) CREATE (g)-[r:Demand{max_demand: 75.0, current_input: 52.0498510425608, level: 1}]->(b);
CREATE (n: Building {id: 2337, name:"building_glassworks", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2337}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 124.91964250214595, level: 6}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2337}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 158.6015411764706, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2337}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 50.81994041702433,level: 6}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2337}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 101.63988083404865,level: 6}]->(g);
CREATE (n: Building {id: 2338, name:"building_tea_plantation", level:12});
MATCH (g: Goods{code: 40}), (b: Building{id:2338}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 266.4,level: 12}]->(g);
CREATE (n: Building {id: 2339, name:"building_government_administration", level:13});
MATCH (g: Goods{code: 14}), (b: Building{id:2339}) CREATE (g)-[r:Demand{max_demand: 129.99999999999997, current_input: 70.62137889821213, level: 13}]->(b);
CREATE (n: Building {id: 2340, name:"building_iron_mine", level:2});
MATCH (g: Goods{code: 33}), (b: Building{id:2340}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.902177078313573, level: 2}]->(b);
MATCH (g: Goods{code: 24}), (b: Building{id:2340}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 7.608708313254292,level: 2}]->(g);
CREATE (n: Building {id: 2341, name:"building_paper_mills", level:11});
MATCH (g: Goods{code: 10}), (b: Building{id:2341}) CREATE (g)-[r:Demand{max_demand: 330.0, current_input: 229.01934458726754, level: 11}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2341}) CREATE (b)-[r:Supply{max_supply: 439.99999999999994, current_output: 305.35912611635666,level: 11}]->(g);
CREATE (n: Building {id: 2342, name:"building_glassworks", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2342}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 124.91964250214595, level: 6}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2342}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 158.6015411764706, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2342}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 50.81994041702433,level: 6}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2342}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 101.63988083404865,level: 6}]->(g);
CREATE (n: Building {id: 2343, name:"building_wheat_farm", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:2343}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 0.7608708313254291, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2343}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 3.8043541566271455,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2343}) CREATE (b)-[r:Supply{max_supply: 28.0, current_output: 5.326095819278004,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2343}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 3.8043541566271455,level: 4}]->(g);
CREATE (n: Building {id: 2344, name:"building_tea_plantation", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2344}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 218.0,level: 10}]->(g);
CREATE (n: Building {id: 2345, name:"building_government_administration", level:12});
MATCH (g: Goods{code: 14}), (b: Building{id:2345}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 65.18896513681119, level: 12}]->(b);
CREATE (n: Building {id: 2346, name:"building_glassworks", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:2346}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 104.0997020851216, level: 5}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2346}) CREATE (g)-[r:Demand{max_demand: 25.0, current_input: 132.16795098039216, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2346}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 42.34995034752027,level: 5}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2346}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 84.69990069504054,level: 5}]->(g);
CREATE (n: Building {id: 2347, name:"building_rice_farm", level:5});
MATCH (g: Goods{code: 33}), (b: Building{id:2347}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.902177078313573, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2347}) CREATE (b)-[r:Supply{max_supply: 50.0, current_output: 9.510885391567864,level: 5}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2347}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 11.413062469881439,level: 5}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2347}) CREATE (b)-[r:Supply{max_supply: 89.99999999999999, current_output: 17.119593704822154,level: 5}]->(g);
CREATE (n: Building {id: 2348, name:"building_silk_plantation", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2348}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 268.0,level: 10}]->(g);
CREATE (n: Building {id: 2349, name:"building_tea_plantation", level:17});
MATCH (g: Goods{code: 40}), (b: Building{id:2349}) CREATE (b)-[r:Supply{max_supply: 340.0, current_output: 394.4,level: 17}]->(g);
CREATE (n: Building {id: 2350, name:"building_government_administration", level:12});
MATCH (g: Goods{code: 14}), (b: Building{id:2350}) CREATE (g)-[r:Demand{max_demand: 119.99999999999999, current_input: 65.18896513681119, level: 12}]->(b);
CREATE (n: Building {id: 2351, name:"building_furniture_manufacturies", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:2351}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 368.6007970178662, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2351}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 104.0997020851216, level: 10}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2351}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 22.501408178415723, level: 10}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2351}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 122.93382678379437,level: 10}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2351}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 153.66728347974296,level: 10}]->(g);
CREATE (n: Building {id: 2352, name:"building_rice_farm", level:12});
MATCH (g: Goods{code: 33}), (b: Building{id:2352}) CREATE (g)-[r:Demand{max_demand: 24.0, current_input: 4.565224987952575, level: 12}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2352}) CREATE (b)-[r:Supply{max_supply: 119.99999999999999, current_output: 22.82612493976287,level: 12}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2352}) CREATE (b)-[r:Supply{max_supply: 144.0, current_output: 27.391349927715446,level: 12}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2352}) CREATE (b)-[r:Supply{max_supply: 215.99999999999997, current_output: 41.087024891573165,level: 12}]->(g);
CREATE (n: Building {id: 2353, name:"building_paper_mills", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2353}) CREATE (g)-[r:Demand{max_demand: 180.0, current_input: 124.91964250214595, level: 6}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2353}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 166.55952333619462,level: 6}]->(g);
CREATE (n: Building {id: 2354, name:"building_tea_plantation", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2354}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 218.0,level: 10}]->(g);
CREATE (n: Building {id: 2355, name:"building_government_administration", level:11});
MATCH (g: Goods{code: 14}), (b: Building{id:2355}) CREATE (g)-[r:Demand{max_demand: 109.99999999999999, current_input: 59.75655137541026, level: 11}]->(b);
CREATE (n: Building {id: 2356, name:"building_rice_farm", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:2356}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 2.2826124939762873, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2356}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 11.413062469881437,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2356}) CREATE (b)-[r:Supply{max_supply: 71.99999999999999, current_output: 13.695674963857721,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2356}) CREATE (b)-[r:Supply{max_supply: 108.0, current_output: 20.543512445786586,level: 6}]->(g);
CREATE (n: Building {id: 2357, name:"building_tea_plantation", level:15});
MATCH (g: Goods{code: 40}), (b: Building{id:2357}) CREATE (b)-[r:Supply{max_supply: 300.0, current_output: 342.0,level: 15}]->(g);
CREATE (n: Building {id: 2358, name:"building_government_administration", level:9});
MATCH (g: Goods{code: 14}), (b: Building{id:2358}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 48.89172385260841, level: 9}]->(b);
CREATE (n: Building {id: 2359, name:"building_paper_mills", level:9});
MATCH (g: Goods{code: 10}), (b: Building{id:2359}) CREATE (g)-[r:Demand{max_demand: 270.0, current_input: 187.37946375321891, level: 9}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2359}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 249.8392850042919,level: 9}]->(g);
CREATE (n: Building {id: 2360, name:"building_furniture_manufacturies", level:10});
MATCH (g: Goods{code: 9}), (b: Building{id:2360}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 368.6007970178662, level: 10}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2360}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 104.0997020851216, level: 10}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2360}) CREATE (g)-[r:Demand{max_demand: 150.0, current_input: 22.501408178415723, level: 10}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2360}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 122.93382678379437,level: 10}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2360}) CREATE (b)-[r:Supply{max_supply: 249.99999999999997, current_output: 153.66728347974296,level: 10}]->(g);
CREATE (n: Building {id: 2361, name:"building_rice_farm", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:2361}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 2.2826124939762873, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2361}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 11.413062469881437,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2361}) CREATE (b)-[r:Supply{max_supply: 71.99999999999999, current_output: 13.695674963857721,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2361}) CREATE (b)-[r:Supply{max_supply: 108.0, current_output: 20.543512445786586,level: 6}]->(g);
CREATE (n: Building {id: 2362, name:"building_tea_plantation", level:10});
MATCH (g: Goods{code: 40}), (b: Building{id:2362}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 218.0,level: 10}]->(g);
CREATE (n: Building {id: 2363, name:"building_government_administration", level:10});
MATCH (g: Goods{code: 14}), (b: Building{id:2363}) CREATE (g)-[r:Demand{max_demand: 99.99999999999999, current_input: 54.324137614009324, level: 10}]->(b);
CREATE (n: Building {id: 2364, name:"building_furniture_manufacturies", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:2364}) CREATE (g)-[r:Demand{max_demand: 79.99999999999999, current_input: 294.880637614293, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2364}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 83.27976166809728, level: 8}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:2364}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 18.001126542732578, level: 8}]->(b);
MATCH (g: Goods{code: 13}), (b: Building{id:2364}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 98.34706142703547,level: 8}]->(g);
MATCH (g: Goods{code: 49}), (b: Building{id:2364}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 122.93382678379436,level: 8}]->(g);
CREATE (n: Building {id: 2365, name:"building_rice_farm", level:4});
MATCH (g: Goods{code: 33}), (b: Building{id:2365}) CREATE (g)-[r:Demand{max_demand: 8.0, current_input: 1.5217416626508582, level: 4}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2365}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 7.608708313254291,level: 4}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2365}) CREATE (b)-[r:Supply{max_supply: 48.0, current_output: 9.13044997590515,level: 4}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2365}) CREATE (b)-[r:Supply{max_supply: 72.0, current_output: 13.695674963857723,level: 4}]->(g);
CREATE (n: Building {id: 2366, name:"building_paper_mills", level:9});
MATCH (g: Goods{code: 10}), (b: Building{id:2366}) CREATE (g)-[r:Demand{max_demand: 270.0, current_input: 187.37946375321891, level: 9}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2366}) CREATE (b)-[r:Supply{max_supply: 360.0, current_output: 249.8392850042919,level: 9}]->(g);
CREATE (n: Building {id: 2367, name:"building_tea_plantation", level:6});
MATCH (g: Goods{code: 40}), (b: Building{id:2367}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 126.0,level: 6}]->(g);
CREATE (n: Building {id: 2368, name:"building_silk_plantation", level:10});
MATCH (g: Goods{code: 20}), (b: Building{id:2368}) CREATE (b)-[r:Supply{max_supply: 200.0, current_output: 268.0,level: 10}]->(g);
CREATE (n: Building {id: 2369, name:"building_government_administration", level:6});
MATCH (g: Goods{code: 14}), (b: Building{id:2369}) CREATE (g)-[r:Demand{max_demand: 60.0, current_input: 32.5944825684056, level: 6}]->(b);
CREATE (n: Building {id: 2370, name:"building_logging_camp", level:8});
MATCH (g: Goods{code: 33}), (b: Building{id:2370}) CREATE (g)-[r:Demand{max_demand: 36.57359813084112, current_input: 6.956946003593811, level: 8}]->(b);
MATCH (g: Goods{code: 10}), (b: Building{id:2370}) CREATE (b)-[r:Supply{max_supply: 438.88319626168226, current_output: 83.48335559859692,level: 8}]->(g);
CREATE (n: Building {id: 2371, name:"building_rice_farm", level:6});
MATCH (g: Goods{code: 33}), (b: Building{id:2371}) CREATE (g)-[r:Demand{max_demand: 12.0, current_input: 2.2826124939762873, level: 6}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2371}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 11.413062469881437,level: 6}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2371}) CREATE (b)-[r:Supply{max_supply: 71.99999999999999, current_output: 13.695674963857721,level: 6}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2371}) CREATE (b)-[r:Supply{max_supply: 108.0, current_output: 20.543512445786586,level: 6}]->(g);
CREATE (n: Building {id: 2372, name:"building_tea_plantation", level:8});
MATCH (g: Goods{code: 40}), (b: Building{id:2372}) CREATE (b)-[r:Supply{max_supply: 159.99999999999997, current_output: 171.2,level: 8}]->(g);
CREATE (n: Building {id: 2373, name:"building_fishing_wharf", level:5});
MATCH (g: Goods{code: 8}), (b: Building{id:2373}) CREATE (b)-[r:Supply{max_supply: 115.81499999999998, current_output: 120.4476,level: 5}]->(g);
CREATE (n: Building {id: 2374, name:"building_tea_plantation", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:2374}) CREATE (b)-[r:Supply{max_supply: 59.71559803921569, current_output: 60.90991,level: 3}]->(g);
CREATE (n: Building {id: 2375, name:"building_banana_plantation", level:5});
MATCH (g: Goods{code: 37}), (b: Building{id:2375}) CREATE (b)-[r:Supply{max_supply: 149.922, current_output: 155.91888,level: 5}]->(g);
CREATE (n: Building {id: 2376, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2376}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.2154624161807384, level: 1}]->(b);
CREATE (n: Building {id: 2377, name:"building_government_administration", level:9});
MATCH (g: Goods{code: 14}), (b: Building{id:2377}) CREATE (g)-[r:Demand{max_demand: 90.0, current_input: 48.89172385260841, level: 9}]->(b);
CREATE (n: Building {id: 2378, name:"building_glassworks", level:10});
MATCH (g: Goods{code: 10}), (b: Building{id:2378}) CREATE (g)-[r:Demand{max_demand: 300.0, current_input: 208.1994041702432, level: 10}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:2378}) CREATE (g)-[r:Demand{max_demand: 49.99999999999999, current_input: 264.3359019607843, level: 10}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2378}) CREATE (b)-[r:Supply{max_supply: 99.99999999999999, current_output: 84.69990069504053,level: 10}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:2378}) CREATE (b)-[r:Supply{max_supply: 199.99999999999997, current_output: 169.39980139008105,level: 10}]->(g);
CREATE (n: Building {id: 2379, name:"building_textile_mills", level:8});
MATCH (g: Goods{code: 9}), (b: Building{id:2379}) CREATE (g)-[r:Demand{max_demand: 200.0, current_input: 737.2015940357326, level: 8}]->(b);
MATCH (g: Goods{code: 20}), (b: Building{id:2379}) CREATE (g)-[r:Demand{max_demand: 120.0, current_input: 1269.0, level: 8}]->(b);
MATCH (g: Goods{code: 12}), (b: Building{id:2379}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 120.0,level: 8}]->(g);
MATCH (g: Goods{code: 48}), (b: Building{id:2379}) CREATE (b)-[r:Supply{max_supply: 240.0, current_output: 240.0,level: 8}]->(g);
CREATE (n: Building {id: 2380, name:"building_tea_plantation", level:3});
MATCH (g: Goods{code: 40}), (b: Building{id:2380}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 61.2,level: 3}]->(g);
CREATE (n: Building {id: 2381, name:"building_wheat_farm", level:3});
MATCH (g: Goods{code: 33}), (b: Building{id:2381}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 0.5706531234940718, level: 3}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:2381}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 2.8532656174703592,level: 3}]->(g);
MATCH (g: Goods{code: 37}), (b: Building{id:2381}) CREATE (b)-[r:Supply{max_supply: 21.0, current_output: 3.9945718644585027,level: 3}]->(g);
MATCH (g: Goods{code: 42}), (b: Building{id:2381}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 2.8532656174703592,level: 3}]->(g);
CREATE (n: Building {id: 2382, name:"building_fishing_wharf", level:6});
MATCH (g: Goods{code: 8}), (b: Building{id:2382}) CREATE (b)-[r:Supply{max_supply: 150.0, current_output: 157.5,level: 6}]->(g);
CREATE (n: Building {id: 2383, name:"building_port", level:2});
MATCH (g: Goods{code: 18}), (b: Building{id:2383}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 6.430924832361477, level: 2}]->(b);
CREATE (n: Building {id: 2384, name:"building_government_administration", level:17});
MATCH (g: Goods{code: 14}), (b: Building{id:2384}) CREATE (g)-[r:Demand{max_demand: 170.0, current_input: 92.35103394381586, level: 17}]->(b);
CREATE (n: Building {id: 2385, name:"building_paper_mills", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:2385}) CREATE (g)-[r:Demand{max_demand: 209.99999999999997, current_input: 145.73958291917023, level: 7}]->(b);
MATCH (g: Goods{code: 14}), (b: Building{id:2385}) CREATE (b)-[r:Supply{max_supply: 280.0, current_output: 194.319443892227,level: 7}]->(g);
CREATE (n: Building {id: 2386, name:"building_tea_plantation", level:12});
MATCH (g: Goods{code: 40}), (b: Building{id:2386}) CREATE (b)-[r:Supply{max_supply: 239.99999999999997, current_output: 266.4,level: 12}]->(g);
CREATE (n: Building {id: 2387, name:"building_fishing_wharf", level:4});
MATCH (g: Goods{code: 8}), (b: Building{id:2387}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 103.0,level: 4}]->(g);
CREATE (n: Building {id: 2388, name:"building_port", level:1});
MATCH (g: Goods{code: 18}), (b: Building{id:2388}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.2154624161807384, level: 1}]->(b);
CREATE (n: Building {id: 2389, name:"building_livestock_ranch", level:3});
MATCH (g: Goods{code: 7}), (b: Building{id:2389}) CREATE (g)-[r:Demand{max_demand: 14.400892857142857, current_input: 23.50857691630053, level: 3}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2389}) CREATE (b)-[r:Supply{max_supply: 28.80179464285714, current_output: 28.80179464285714,level: 3}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2389}) CREATE (b)-[r:Supply{max_supply: 28.80179464285714, current_output: 28.80179464285714,level: 3}]->(g);
CREATE (n: Building {id: 2390, name:"building_livestock_ranch", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:2390}) CREATE (g)-[r:Demand{max_demand: 23.609745614035088, current_input: 38.54146588324464, level: 5}]->(b);
MATCH (g: Goods{code: 9}), (b: Building{id:2390}) CREATE (b)-[r:Supply{max_supply: 47.219500000000004, current_output: 47.219500000000004,level: 5}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2390}) CREATE (b)-[r:Supply{max_supply: 47.219500000000004, current_output: 47.219500000000004,level: 5}]->(g);
CREATE (n: Building {id: 2721, name:"building_subsistence_rice_paddies", level:343});
MATCH (g: Goods{code: 7}), (b: Building{id:2721}) CREATE (b)-[r:Supply{max_supply: 1882.44402, current_output: 1882.44402,level: 343}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2721}) CREATE (b)-[r:Supply{max_supply: 256.69691, current_output: 256.69691,level: 343}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2721}) CREATE (b)-[r:Supply{max_supply: 256.69691, current_output: 256.69691,level: 343}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2721}) CREATE (b)-[r:Supply{max_supply: 342.26255, current_output: 342.26255,level: 343}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2721}) CREATE (b)-[r:Supply{max_supply: 342.26255, current_output: 342.26255,level: 343}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2721}) CREATE (b)-[r:Supply{max_supply: 342.26255, current_output: 342.26255,level: 343}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2721}) CREATE (b)-[r:Supply{max_supply: 342.26255, current_output: 342.26255,level: 343}]->(g);
CREATE (n: Building {id: 2722, name:"building_urban_center", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2722}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 4.163988083404864, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2722}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 6.377398069775855, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2722}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 101.63988083404865,level: 6}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2722}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 25.409970208512163,level: 6}]->(g);
CREATE (n: Building {id: 2723, name:"building_subsistence_rice_paddies", level:388});
MATCH (g: Goods{code: 7}), (b: Building{id:2723}) CREATE (b)-[r:Supply{max_supply: 2129.92406, current_output: 2129.92406,level: 388}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2723}) CREATE (b)-[r:Supply{max_supply: 290.44419, current_output: 290.44419,level: 388}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2723}) CREATE (b)-[r:Supply{max_supply: 290.44419, current_output: 290.44419,level: 388}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2723}) CREATE (b)-[r:Supply{max_supply: 387.25892, current_output: 387.25892,level: 388}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2723}) CREATE (b)-[r:Supply{max_supply: 387.25892, current_output: 387.25892,level: 388}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2723}) CREATE (b)-[r:Supply{max_supply: 387.25892, current_output: 387.25892,level: 388}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2723}) CREATE (b)-[r:Supply{max_supply: 387.25892, current_output: 387.25892,level: 388}]->(g);
CREATE (n: Building {id: 2724, name:"building_urban_center", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:2724}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 4.163988083404864, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:2724}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 6.377398069775855, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:2724}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 101.63988083404865,level: 6}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:2724}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 25.409970208512163,level: 6}]->(g);
CREATE (n: Building {id: 2888, name:"building_subsistence_pastures", level:35});
MATCH (g: Goods{code: 7}), (b: Building{id:2888}) CREATE (b)-[r:Supply{max_supply: 5.842022222222222, current_output: 5.25782,level: 35}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2888}) CREATE (b)-[r:Supply{max_supply: 8.763033333333333, current_output: 7.88673,level: 35}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2888}) CREATE (b)-[r:Supply{max_supply: 2.921011111111111, current_output: 2.62891,level: 35}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2888}) CREATE (b)-[r:Supply{max_supply: 5.842022222222222, current_output: 5.25782,level: 35}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2888}) CREATE (b)-[r:Supply{max_supply: 5.842022222222222, current_output: 5.25782,level: 35}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2888}) CREATE (b)-[r:Supply{max_supply: 5.842022222222222, current_output: 5.25782,level: 35}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2888}) CREATE (b)-[r:Supply{max_supply: 19.395522222222223, current_output: 17.45597,level: 35}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2888}) CREATE (b)-[r:Supply{max_supply: 5.842022222222222, current_output: 5.25782,level: 35}]->(g);
CREATE (n: Building {id: 2889, name:"building_subsistence_pastures", level:27});
MATCH (g: Goods{code: 7}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 5.434288888888889, current_output: 4.89086,level: 27}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 8.151433333333333, current_output: 7.33629,level: 27}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 2.7171444444444446, current_output: 2.44543,level: 27}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 5.434288888888889, current_output: 4.89086,level: 27}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 5.434288888888889, current_output: 4.89086,level: 27}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 5.434288888888889, current_output: 4.89086,level: 27}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 18.041833333333333, current_output: 16.23765,level: 27}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:2889}) CREATE (b)-[r:Supply{max_supply: 5.434288888888889, current_output: 4.89086,level: 27}]->(g);
CREATE (n: Building {id: 3522, name:"building_subsistence_pastures", level:28});
MATCH (g: Goods{code: 7}), (b: Building{id:3522}) CREATE (b)-[r:Supply{max_supply: 5.89834, current_output: 5.89834,level: 28}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3522}) CREATE (b)-[r:Supply{max_supply: 8.84751, current_output: 8.84751,level: 28}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3522}) CREATE (b)-[r:Supply{max_supply: 2.94917, current_output: 2.94917,level: 28}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3522}) CREATE (b)-[r:Supply{max_supply: 5.89834, current_output: 5.89834,level: 28}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3522}) CREATE (b)-[r:Supply{max_supply: 5.89834, current_output: 5.89834,level: 28}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3522}) CREATE (b)-[r:Supply{max_supply: 5.89834, current_output: 5.89834,level: 28}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3522}) CREATE (b)-[r:Supply{max_supply: 19.58248, current_output: 19.58248,level: 28}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3522}) CREATE (b)-[r:Supply{max_supply: 5.89834, current_output: 5.89834,level: 28}]->(g);
CREATE (n: Building {id: 3523, name:"building_subsistence_pastures", level:38});
MATCH (g: Goods{code: 7}), (b: Building{id:3523}) CREATE (b)-[r:Supply{max_supply: 8.1882375, current_output: 6.55059,level: 38}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3523}) CREATE (b)-[r:Supply{max_supply: 12.28235, current_output: 9.82588,level: 38}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3523}) CREATE (b)-[r:Supply{max_supply: 4.0941125, current_output: 3.27529,level: 38}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3523}) CREATE (b)-[r:Supply{max_supply: 8.1882375, current_output: 6.55059,level: 38}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3523}) CREATE (b)-[r:Supply{max_supply: 8.1882375, current_output: 6.55059,level: 38}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3523}) CREATE (b)-[r:Supply{max_supply: 8.1882375, current_output: 6.55059,level: 38}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3523}) CREATE (b)-[r:Supply{max_supply: 27.184949999999997, current_output: 21.74796,level: 38}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3523}) CREATE (b)-[r:Supply{max_supply: 8.1882375, current_output: 6.55059,level: 38}]->(g);
CREATE (n: Building {id: 3524, name:"building_subsistence_rice_paddies", level:324});
MATCH (g: Goods{code: 7}), (b: Building{id:3524}) CREATE (b)-[r:Supply{max_supply: 1778.4894521739132, current_output: 2045.26287,level: 324}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3524}) CREATE (b)-[r:Supply{max_supply: 242.52128695652175, current_output: 278.89948,level: 324}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3524}) CREATE (b)-[r:Supply{max_supply: 242.52128695652175, current_output: 278.89948,level: 324}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3524}) CREATE (b)-[r:Supply{max_supply: 323.3617130434783, current_output: 371.86597,level: 324}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3524}) CREATE (b)-[r:Supply{max_supply: 323.3617130434783, current_output: 371.86597,level: 324}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3524}) CREATE (b)-[r:Supply{max_supply: 323.3617130434783, current_output: 371.86597,level: 324}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3524}) CREATE (b)-[r:Supply{max_supply: 323.3617130434783, current_output: 371.86597,level: 324}]->(g);
CREATE (n: Building {id: 3525, name:"building_urban_center", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3525}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.4699900695040538, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3525}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.314498391479878, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3525}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 84.69990069504054,level: 5}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3525}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 21.174975173760135,level: 5}]->(g);
CREATE (n: Building {id: 3526, name:"building_subsistence_rice_paddies", level:130});
MATCH (g: Goods{code: 7}), (b: Building{id:3526}) CREATE (b)-[r:Supply{max_supply: 713.27685, current_output: 713.27685,level: 130}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3526}) CREATE (b)-[r:Supply{max_supply: 97.26502, current_output: 97.26502,level: 130}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3526}) CREATE (b)-[r:Supply{max_supply: 97.26502, current_output: 97.26502,level: 130}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3526}) CREATE (b)-[r:Supply{max_supply: 129.6867, current_output: 129.6867,level: 130}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3526}) CREATE (b)-[r:Supply{max_supply: 129.6867, current_output: 129.6867,level: 130}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3526}) CREATE (b)-[r:Supply{max_supply: 129.6867, current_output: 129.6867,level: 130}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3526}) CREATE (b)-[r:Supply{max_supply: 129.6867, current_output: 129.6867,level: 130}]->(g);
CREATE (n: Building {id: 3527, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3527}) CREATE (g)-[r:Demand{max_demand: 2.373774509803922, current_input: 1.6473947952522923, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3527}) CREATE (g)-[r:Demand{max_demand: 2.373774509803922, current_input: 2.5230841628177765, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3527}) CREATE (b)-[r:Supply{max_supply: 47.47559803921568, current_output: 40.211784393592296,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3527}) CREATE (b)-[r:Supply{max_supply: 11.868892156862746, current_output: 10.0529398704642,level: 3}]->(g);
CREATE (n: Building {id: 3528, name:"building_subsistence_rice_paddies", level:93});
MATCH (g: Goods{code: 7}), (b: Building{id:3528}) CREATE (b)-[r:Supply{max_supply: 481.49029, current_output: 481.49029,level: 93}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3528}) CREATE (b)-[r:Supply{max_supply: 65.65776, current_output: 65.65776,level: 93}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3528}) CREATE (b)-[r:Supply{max_supply: 65.65776, current_output: 65.65776,level: 93}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3528}) CREATE (b)-[r:Supply{max_supply: 87.54369, current_output: 87.54369,level: 93}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3528}) CREATE (b)-[r:Supply{max_supply: 87.54369, current_output: 87.54369,level: 93}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3528}) CREATE (b)-[r:Supply{max_supply: 87.54369, current_output: 87.54369,level: 93}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3528}) CREATE (b)-[r:Supply{max_supply: 87.54369, current_output: 87.54369,level: 93}]->(g);
CREATE (n: Building {id: 3529, name:"building_urban_center", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3529}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 4.163988083404864, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3529}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 6.377398069775855, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3529}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 101.63988083404865,level: 6}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3529}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 25.409970208512163,level: 6}]->(g);
CREATE (n: Building {id: 3530, name:"building_subsistence_rice_paddies", level:167});
MATCH (g: Goods{code: 7}), (b: Building{id:3530}) CREATE (b)-[r:Supply{max_supply: 916.65381, current_output: 916.65381,level: 167}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3530}) CREATE (b)-[r:Supply{max_supply: 124.99824, current_output: 124.99824,level: 167}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3530}) CREATE (b)-[r:Supply{max_supply: 124.99824, current_output: 124.99824,level: 167}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3530}) CREATE (b)-[r:Supply{max_supply: 166.66433, current_output: 166.66433,level: 167}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3530}) CREATE (b)-[r:Supply{max_supply: 166.66433, current_output: 166.66433,level: 167}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3530}) CREATE (b)-[r:Supply{max_supply: 166.66433, current_output: 166.66433,level: 167}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3530}) CREATE (b)-[r:Supply{max_supply: 166.66433, current_output: 166.66433,level: 167}]->(g);
CREATE (n: Building {id: 3531, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3531}) CREATE (g)-[r:Demand{max_demand: 2.949598039215686, current_input: 2.047015181021412, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3531}) CREATE (g)-[r:Demand{max_demand: 2.949598039215686, current_input: 3.1351268069847937, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3531}) CREATE (b)-[r:Supply{max_supply: 58.992000000000004, current_output: 49.96616541801832,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3531}) CREATE (b)-[r:Supply{max_supply: 14.748000000000001, current_output: 12.49154135450458,level: 3}]->(g);
CREATE (n: Building {id: 3532, name:"building_subsistence_pastures", level:10});
MATCH (g: Goods{code: 7}), (b: Building{id:3532}) CREATE (b)-[r:Supply{max_supply: 0.27619999999999995, current_output: 0.22096,level: 10}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3532}) CREATE (b)-[r:Supply{max_supply: 0.4143, current_output: 0.33144,level: 10}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3532}) CREATE (b)-[r:Supply{max_supply: 0.13809999999999997, current_output: 0.11048,level: 10}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3532}) CREATE (b)-[r:Supply{max_supply: 0.27619999999999995, current_output: 0.22096,level: 10}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3532}) CREATE (b)-[r:Supply{max_supply: 0.27619999999999995, current_output: 0.22096,level: 10}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3532}) CREATE (b)-[r:Supply{max_supply: 0.27619999999999995, current_output: 0.22096,level: 10}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3532}) CREATE (b)-[r:Supply{max_supply: 0.916975, current_output: 0.73358,level: 10}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3532}) CREATE (b)-[r:Supply{max_supply: 0.27619999999999995, current_output: 0.22096,level: 10}]->(g);
CREATE (n: Building {id: 3533, name:"building_subsistence_pastures", level:101});
MATCH (g: Goods{code: 7}), (b: Building{id:3533}) CREATE (b)-[r:Supply{max_supply: 49.998533333333334, current_output: 44.99868,level: 101}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3533}) CREATE (b)-[r:Supply{max_supply: 74.9978, current_output: 67.49802,level: 101}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3533}) CREATE (b)-[r:Supply{max_supply: 24.999266666666667, current_output: 22.49934,level: 101}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3533}) CREATE (b)-[r:Supply{max_supply: 49.998533333333334, current_output: 44.99868,level: 101}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3533}) CREATE (b)-[r:Supply{max_supply: 49.998533333333334, current_output: 44.99868,level: 101}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3533}) CREATE (b)-[r:Supply{max_supply: 49.998533333333334, current_output: 44.99868,level: 101}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3533}) CREATE (b)-[r:Supply{max_supply: 165.99513333333334, current_output: 149.39562,level: 101}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3533}) CREATE (b)-[r:Supply{max_supply: 49.998533333333334, current_output: 44.99868,level: 101}]->(g);
CREATE (n: Building {id: 3534, name:"building_subsistence_rice_paddies", level:351});
MATCH (g: Goods{code: 7}), (b: Building{id:3534}) CREATE (b)-[r:Supply{max_supply: 1900.557941666667, current_output: 2280.66953,level: 351}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3534}) CREATE (b)-[r:Supply{max_supply: 259.16699166666666, current_output: 311.00039,level: 351}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3534}) CREATE (b)-[r:Supply{max_supply: 259.16699166666666, current_output: 311.00039,level: 351}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3534}) CREATE (b)-[r:Supply{max_supply: 345.5559833333333, current_output: 414.66718,level: 351}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3534}) CREATE (b)-[r:Supply{max_supply: 345.5559833333333, current_output: 414.66718,level: 351}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3534}) CREATE (b)-[r:Supply{max_supply: 345.5559833333333, current_output: 414.66718,level: 351}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3534}) CREATE (b)-[r:Supply{max_supply: 345.5559833333333, current_output: 414.66718,level: 351}]->(g);
CREATE (n: Building {id: 3535, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3535}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 2.775992055603243, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3535}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.2515987131839035, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3535}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 67.75992055603243,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3535}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 16.939980139008107,level: 4}]->(g);
CREATE (n: Building {id: 3536, name:"building_subsistence_rice_paddies", level:332});
MATCH (g: Goods{code: 7}), (b: Building{id:3536}) CREATE (b)-[r:Supply{max_supply: 1822.42104, current_output: 1822.42104,level: 332}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3536}) CREATE (b)-[r:Supply{max_supply: 248.51196, current_output: 248.51196,level: 332}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3536}) CREATE (b)-[r:Supply{max_supply: 248.51196, current_output: 248.51196,level: 332}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3536}) CREATE (b)-[r:Supply{max_supply: 331.34928, current_output: 331.34928,level: 332}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3536}) CREATE (b)-[r:Supply{max_supply: 331.34928, current_output: 331.34928,level: 332}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3536}) CREATE (b)-[r:Supply{max_supply: 331.34928, current_output: 331.34928,level: 332}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3536}) CREATE (b)-[r:Supply{max_supply: 331.34928, current_output: 331.34928,level: 332}]->(g);
CREATE (n: Building {id: 3537, name:"building_urban_center", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3537}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 4.857986097305676, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3537}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 7.44029774807183, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3537}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 118.57986097305675,level: 7}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3537}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 29.64496524326419,level: 7}]->(g);
CREATE (n: Building {id: 3538, name:"building_subsistence_rice_paddies", level:75});
MATCH (g: Goods{code: 7}), (b: Building{id:3538}) CREATE (b)-[r:Supply{max_supply: 308.47575, current_output: 308.47575,level: 75}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3538}) CREATE (b)-[r:Supply{max_supply: 42.06487, current_output: 42.06487,level: 75}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3538}) CREATE (b)-[r:Supply{max_supply: 42.06487, current_output: 42.06487,level: 75}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3538}) CREATE (b)-[r:Supply{max_supply: 56.0865, current_output: 56.0865,level: 75}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3538}) CREATE (b)-[r:Supply{max_supply: 56.0865, current_output: 56.0865,level: 75}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3538}) CREATE (b)-[r:Supply{max_supply: 56.0865, current_output: 56.0865,level: 75}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3538}) CREATE (b)-[r:Supply{max_supply: 56.0865, current_output: 56.0865,level: 75}]->(g);
CREATE (n: Building {id: 3539, name:"building_subsistence_farms", level:62});
MATCH (g: Goods{code: 7}), (b: Building{id:3539}) CREATE (b)-[r:Supply{max_supply: 132.24135, current_output: 132.24135,level: 62}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3539}) CREATE (b)-[r:Supply{max_supply: 26.44827, current_output: 26.44827,level: 62}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3539}) CREATE (b)-[r:Supply{max_supply: 26.44827, current_output: 26.44827,level: 62}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3539}) CREATE (b)-[r:Supply{max_supply: 26.44827, current_output: 26.44827,level: 62}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3539}) CREATE (b)-[r:Supply{max_supply: 26.44827, current_output: 26.44827,level: 62}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3539}) CREATE (b)-[r:Supply{max_supply: 26.44827, current_output: 26.44827,level: 62}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3539}) CREATE (b)-[r:Supply{max_supply: 26.44827, current_output: 26.44827,level: 62}]->(g);
CREATE (n: Building {id: 3540, name:"building_subsistence_rice_paddies", level:197});
MATCH (g: Goods{code: 7}), (b: Building{id:3540}) CREATE (b)-[r:Supply{max_supply: 1081.38717, current_output: 1081.38717,level: 197}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3540}) CREATE (b)-[r:Supply{max_supply: 147.46188, current_output: 147.46188,level: 197}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3540}) CREATE (b)-[r:Supply{max_supply: 147.46188, current_output: 147.46188,level: 197}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3540}) CREATE (b)-[r:Supply{max_supply: 196.61585, current_output: 196.61585,level: 197}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3540}) CREATE (b)-[r:Supply{max_supply: 196.61585, current_output: 196.61585,level: 197}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3540}) CREATE (b)-[r:Supply{max_supply: 196.61585, current_output: 196.61585,level: 197}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3540}) CREATE (b)-[r:Supply{max_supply: 196.61585, current_output: 196.61585,level: 197}]->(g);
CREATE (n: Building {id: 3541, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3541}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.3879960278016215, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3541}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.1257993565919517, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3541}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 33.87996027801621,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3541}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 8.469990069504053,level: 2}]->(g);
CREATE (n: Building {id: 3542, name:"building_subsistence_farms", level:138});
MATCH (g: Goods{code: 7}), (b: Building{id:3542}) CREATE (b)-[r:Supply{max_supply: 343.76834444444444, current_output: 309.39151,level: 138}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3542}) CREATE (b)-[r:Supply{max_supply: 68.75366666666667, current_output: 61.8783,level: 138}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3542}) CREATE (b)-[r:Supply{max_supply: 68.75366666666667, current_output: 61.8783,level: 138}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3542}) CREATE (b)-[r:Supply{max_supply: 68.75366666666667, current_output: 61.8783,level: 138}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3542}) CREATE (b)-[r:Supply{max_supply: 68.75366666666667, current_output: 61.8783,level: 138}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3542}) CREATE (b)-[r:Supply{max_supply: 68.75366666666667, current_output: 61.8783,level: 138}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3542}) CREATE (b)-[r:Supply{max_supply: 68.75366666666667, current_output: 61.8783,level: 138}]->(g);
CREATE (n: Building {id: 3543, name:"building_subsistence_rice_paddies", level:365});
MATCH (g: Goods{code: 7}), (b: Building{id:3543}) CREATE (b)-[r:Supply{max_supply: 1981.1013739130435, current_output: 2278.26658,level: 365}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3543}) CREATE (b)-[r:Supply{max_supply: 270.1501826086957, current_output: 310.67271,level: 365}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3543}) CREATE (b)-[r:Supply{max_supply: 270.1501826086957, current_output: 310.67271,level: 365}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3543}) CREATE (b)-[r:Supply{max_supply: 360.2002434782609, current_output: 414.23028,level: 365}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3543}) CREATE (b)-[r:Supply{max_supply: 360.2002434782609, current_output: 414.23028,level: 365}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3543}) CREATE (b)-[r:Supply{max_supply: 360.2002434782609, current_output: 414.23028,level: 365}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3543}) CREATE (b)-[r:Supply{max_supply: 360.2002434782609, current_output: 414.23028,level: 365}]->(g);
CREATE (n: Building {id: 3544, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3544}) CREATE (g)-[r:Demand{max_demand: 1.9581980198019804, current_input: 1.3589855365670749, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3544}) CREATE (g)-[r:Demand{max_demand: 1.9581980198019804, current_input: 2.0813680452873418, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3544}) CREATE (b)-[r:Supply{max_supply: 39.163999999999994, current_output: 33.17186910820568,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3544}) CREATE (b)-[r:Supply{max_supply: 9.790999999999999, current_output: 8.29296727705142,level: 2}]->(g);
CREATE (n: Building {id: 3545, name:"building_subsistence_rice_paddies", level:197});
MATCH (g: Goods{code: 7}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 1081.23548, current_output: 1081.23548,level: 197}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 147.4412, current_output: 147.4412,level: 197}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 147.4412, current_output: 147.4412,level: 197}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 196.58827, current_output: 196.58827,level: 197}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 196.58827, current_output: 196.58827,level: 197}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 196.58827, current_output: 196.58827,level: 197}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3545}) CREATE (b)-[r:Supply{max_supply: 196.58827, current_output: 196.58827,level: 197}]->(g);
CREATE (n: Building {id: 3546, name:"building_subsistence_rice_paddies", level:144});
MATCH (g: Goods{code: 7}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 787.93704, current_output: 787.93704,level: 144}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 107.44596, current_output: 107.44596,level: 144}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 107.44596, current_output: 107.44596,level: 144}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 143.26128, current_output: 143.26128,level: 144}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 143.26128, current_output: 143.26128,level: 144}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 143.26128, current_output: 143.26128,level: 144}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3546}) CREATE (b)-[r:Supply{max_supply: 143.26128, current_output: 143.26128,level: 144}]->(g);
CREATE (n: Building {id: 3547, name:"building_subsistence_rice_paddies", level:245});
MATCH (g: Goods{code: 7}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 1328.446347826087, current_output: 1527.7133,level: 245}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 181.1517739130435, current_output: 208.32454,level: 245}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 181.1517739130435, current_output: 208.32454,level: 245}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 241.53569565217393, current_output: 277.76605,level: 245}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 241.53569565217393, current_output: 277.76605,level: 245}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 241.53569565217393, current_output: 277.76605,level: 245}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3547}) CREATE (b)-[r:Supply{max_supply: 241.53569565217393, current_output: 277.76605,level: 245}]->(g);
CREATE (n: Building {id: 3548, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:3548}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.6939980139008107, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3548}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.0628996782959759, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3548}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 16.939980139008107,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3548}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 4.234995034752027,level: 1}]->(g);
CREATE (n: Building {id: 3549, name:"building_subsistence_farms", level:55});
MATCH (g: Goods{code: 7}), (b: Building{id:3549}) CREATE (b)-[r:Supply{max_supply: 134.74312, current_output: 134.74312,level: 55}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3549}) CREATE (b)-[r:Supply{max_supply: 26.94862, current_output: 26.94862,level: 55}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3549}) CREATE (b)-[r:Supply{max_supply: 26.94862, current_output: 26.94862,level: 55}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3549}) CREATE (b)-[r:Supply{max_supply: 26.94862, current_output: 26.94862,level: 55}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3549}) CREATE (b)-[r:Supply{max_supply: 26.94862, current_output: 26.94862,level: 55}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3549}) CREATE (b)-[r:Supply{max_supply: 26.94862, current_output: 26.94862,level: 55}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3549}) CREATE (b)-[r:Supply{max_supply: 26.94862, current_output: 26.94862,level: 55}]->(g);
CREATE (n: Building {id: 3550, name:"building_subsistence_rice_paddies", level:480});
MATCH (g: Goods{code: 7}), (b: Building{id:3550}) CREATE (b)-[r:Supply{max_supply: 2627.8824000000004, current_output: 3022.06476,level: 480}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3550}) CREATE (b)-[r:Supply{max_supply: 358.3476, current_output: 412.09974,level: 480}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3550}) CREATE (b)-[r:Supply{max_supply: 358.3476, current_output: 412.09974,level: 480}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3550}) CREATE (b)-[r:Supply{max_supply: 477.7968, current_output: 549.46632,level: 480}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3550}) CREATE (b)-[r:Supply{max_supply: 477.7968, current_output: 549.46632,level: 480}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3550}) CREATE (b)-[r:Supply{max_supply: 477.7968, current_output: 549.46632,level: 480}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3550}) CREATE (b)-[r:Supply{max_supply: 477.7968, current_output: 549.46632,level: 480}]->(g);
CREATE (n: Building {id: 3551, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3551}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 2.775992055603243, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3551}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.2515987131839035, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3551}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 67.75992055603243,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3551}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 16.939980139008107,level: 4}]->(g);
CREATE (n: Building {id: 3552, name:"building_subsistence_rice_paddies", level:270});
MATCH (g: Goods{code: 7}), (b: Building{id:3552}) CREATE (b)-[r:Supply{max_supply: 1480.20345, current_output: 1776.24414,level: 270}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3552}) CREATE (b)-[r:Supply{max_supply: 201.84592500000002, current_output: 242.21511,level: 270}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3552}) CREATE (b)-[r:Supply{max_supply: 201.84592500000002, current_output: 242.21511,level: 270}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3552}) CREATE (b)-[r:Supply{max_supply: 269.1279, current_output: 322.95348,level: 270}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3552}) CREATE (b)-[r:Supply{max_supply: 269.1279, current_output: 322.95348,level: 270}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3552}) CREATE (b)-[r:Supply{max_supply: 269.1279, current_output: 322.95348,level: 270}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3552}) CREATE (b)-[r:Supply{max_supply: 269.1279, current_output: 322.95348,level: 270}]->(g);
CREATE (n: Building {id: 3553, name:"building_urban_center", level:11});
MATCH (g: Goods{code: 10}), (b: Building{id:3553}) CREATE (g)-[r:Demand{max_demand: 10.741499999999998, current_input: 7.454579666315557, level: 11}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3553}) CREATE (g)-[r:Demand{max_demand: 10.741499999999998, current_input: 11.417136894416222, level: 11}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3553}) CREATE (b)-[r:Supply{max_supply: 214.82999999999998, current_output: 181.96079666315558,level: 11}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3553}) CREATE (b)-[r:Supply{max_supply: 53.707499999999996, current_output: 45.490199165788894,level: 11}]->(g);
CREATE (n: Building {id: 3555, name:"building_subsistence_rice_paddies", level:375});
MATCH (g: Goods{code: 7}), (b: Building{id:3555}) CREATE (b)-[r:Supply{max_supply: 2058.4575, current_output: 2058.4575,level: 375}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3555}) CREATE (b)-[r:Supply{max_supply: 280.69875, current_output: 280.69875,level: 375}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3555}) CREATE (b)-[r:Supply{max_supply: 280.69875, current_output: 280.69875,level: 375}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3555}) CREATE (b)-[r:Supply{max_supply: 374.265, current_output: 374.265,level: 375}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3555}) CREATE (b)-[r:Supply{max_supply: 374.265, current_output: 374.265,level: 375}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3555}) CREATE (b)-[r:Supply{max_supply: 374.265, current_output: 374.265,level: 375}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3555}) CREATE (b)-[r:Supply{max_supply: 374.265, current_output: 374.265,level: 375}]->(g);
CREATE (n: Building {id: 3556, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3556}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 2.775992055603243, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3556}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.2515987131839035, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3556}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 67.75992055603243,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3556}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 16.939980139008107,level: 4}]->(g);
CREATE (n: Building {id: 3557, name:"building_subsistence_rice_paddies", level:231});
MATCH (g: Goods{code: 7}), (b: Building{id:3557}) CREATE (b)-[r:Supply{max_supply: 1179.55761, current_output: 1179.55761,level: 231}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3557}) CREATE (b)-[r:Supply{max_supply: 160.84876, current_output: 160.84876,level: 231}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3557}) CREATE (b)-[r:Supply{max_supply: 160.84876, current_output: 160.84876,level: 231}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3557}) CREATE (b)-[r:Supply{max_supply: 214.46502, current_output: 214.46502,level: 231}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3557}) CREATE (b)-[r:Supply{max_supply: 214.46502, current_output: 214.46502,level: 231}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3557}) CREATE (b)-[r:Supply{max_supply: 214.46502, current_output: 214.46502,level: 231}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3557}) CREATE (b)-[r:Supply{max_supply: 214.46502, current_output: 214.46502,level: 231}]->(g);
CREATE (n: Building {id: 3558, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3558}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 2.775992055603243, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3558}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.2515987131839035, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3558}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 67.75992055603243,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3558}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 16.939980139008107,level: 4}]->(g);
CREATE (n: Building {id: 3559, name:"building_subsistence_rice_paddies", level:345});
MATCH (g: Goods{code: 7}), (b: Building{id:3559}) CREATE (b)-[r:Supply{max_supply: 1387.24327, current_output: 1387.24327,level: 345}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3559}) CREATE (b)-[r:Supply{max_supply: 189.16953, current_output: 189.16953,level: 345}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3559}) CREATE (b)-[r:Supply{max_supply: 189.16953, current_output: 189.16953,level: 345}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3559}) CREATE (b)-[r:Supply{max_supply: 252.22605, current_output: 252.22605,level: 345}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3559}) CREATE (b)-[r:Supply{max_supply: 252.22605, current_output: 252.22605,level: 345}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3559}) CREATE (b)-[r:Supply{max_supply: 252.22605, current_output: 252.22605,level: 345}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3559}) CREATE (b)-[r:Supply{max_supply: 252.22605, current_output: 252.22605,level: 345}]->(g);
CREATE (n: Building {id: 3560, name:"building_urban_center", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3560}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 4.163988083404864, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3560}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 6.377398069775855, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3560}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 101.63988083404865,level: 6}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3560}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 25.409970208512163,level: 6}]->(g);
CREATE (n: Building {id: 3561, name:"building_subsistence_rice_paddies", level:257});
MATCH (g: Goods{code: 7}), (b: Building{id:3561}) CREATE (b)-[r:Supply{max_supply: 1379.51946, current_output: 1379.51946,level: 257}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3561}) CREATE (b)-[r:Supply{max_supply: 188.11629, current_output: 188.11629,level: 257}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3561}) CREATE (b)-[r:Supply{max_supply: 188.11629, current_output: 188.11629,level: 257}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3561}) CREATE (b)-[r:Supply{max_supply: 250.82172, current_output: 250.82172,level: 257}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3561}) CREATE (b)-[r:Supply{max_supply: 250.82172, current_output: 250.82172,level: 257}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3561}) CREATE (b)-[r:Supply{max_supply: 250.82172, current_output: 250.82172,level: 257}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3561}) CREATE (b)-[r:Supply{max_supply: 250.82172, current_output: 250.82172,level: 257}]->(g);
CREATE (n: Building {id: 3562, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3562}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 2.775992055603243, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3562}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.2515987131839035, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3562}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 67.75992055603243,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3562}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 16.939980139008107,level: 4}]->(g);
CREATE (n: Building {id: 3563, name:"building_subsistence_rice_paddies", level:488});
MATCH (g: Goods{code: 7}), (b: Building{id:3563}) CREATE (b)-[r:Supply{max_supply: 2678.95408, current_output: 2678.95408,level: 488}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3563}) CREATE (b)-[r:Supply{max_supply: 365.31192, current_output: 365.31192,level: 488}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3563}) CREATE (b)-[r:Supply{max_supply: 365.31192, current_output: 365.31192,level: 488}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3563}) CREATE (b)-[r:Supply{max_supply: 487.08256, current_output: 487.08256,level: 488}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3563}) CREATE (b)-[r:Supply{max_supply: 487.08256, current_output: 487.08256,level: 488}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3563}) CREATE (b)-[r:Supply{max_supply: 487.08256, current_output: 487.08256,level: 488}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3563}) CREATE (b)-[r:Supply{max_supply: 487.08256, current_output: 487.08256,level: 488}]->(g);
CREATE (n: Building {id: 3564, name:"building_urban_center", level:4});
MATCH (g: Goods{code: 10}), (b: Building{id:3564}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 2.775992055603243, level: 4}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3564}) CREATE (g)-[r:Demand{max_demand: 4.0, current_input: 4.2515987131839035, level: 4}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3564}) CREATE (b)-[r:Supply{max_supply: 80.0, current_output: 67.75992055603243,level: 4}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3564}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 16.939980139008107,level: 4}]->(g);
CREATE (n: Building {id: 3565, name:"building_subsistence_rice_paddies", level:186});
MATCH (g: Goods{code: 7}), (b: Building{id:3565}) CREATE (b)-[r:Supply{max_supply: 1019.70594, current_output: 1019.70594,level: 186}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3565}) CREATE (b)-[r:Supply{max_supply: 139.05081, current_output: 139.05081,level: 186}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3565}) CREATE (b)-[r:Supply{max_supply: 139.05081, current_output: 139.05081,level: 186}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3565}) CREATE (b)-[r:Supply{max_supply: 185.40108, current_output: 185.40108,level: 186}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3565}) CREATE (b)-[r:Supply{max_supply: 185.40108, current_output: 185.40108,level: 186}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3565}) CREATE (b)-[r:Supply{max_supply: 185.40108, current_output: 185.40108,level: 186}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3565}) CREATE (b)-[r:Supply{max_supply: 185.40108, current_output: 185.40108,level: 186}]->(g);
CREATE (n: Building {id: 3566, name:"building_urban_center", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3566}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 4.857986097305676, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3566}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 7.44029774807183, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3566}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 118.57986097305675,level: 7}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3566}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 29.64496524326419,level: 7}]->(g);
CREATE (n: Building {id: 3567, name:"building_subsistence_rice_paddies", level:568});
MATCH (g: Goods{code: 7}), (b: Building{id:3567}) CREATE (b)-[r:Supply{max_supply: 3117.84572, current_output: 3117.84572,level: 568}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3567}) CREATE (b)-[r:Supply{max_supply: 425.16078, current_output: 425.16078,level: 568}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3567}) CREATE (b)-[r:Supply{max_supply: 425.16078, current_output: 425.16078,level: 568}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3567}) CREATE (b)-[r:Supply{max_supply: 566.88104, current_output: 566.88104,level: 568}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3567}) CREATE (b)-[r:Supply{max_supply: 566.88104, current_output: 566.88104,level: 568}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3567}) CREATE (b)-[r:Supply{max_supply: 566.88104, current_output: 566.88104,level: 568}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3567}) CREATE (b)-[r:Supply{max_supply: 566.88104, current_output: 566.88104,level: 568}]->(g);
CREATE (n: Building {id: 3568, name:"building_urban_center", level:5});
MATCH (g: Goods{code: 10}), (b: Building{id:3568}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 3.4699900695040538, level: 5}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3568}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 5.314498391479878, level: 5}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3568}) CREATE (b)-[r:Supply{max_supply: 100.0, current_output: 84.69990069504054,level: 5}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3568}) CREATE (b)-[r:Supply{max_supply: 25.0, current_output: 21.174975173760135,level: 5}]->(g);
CREATE (n: Building {id: 3569, name:"building_subsistence_rice_paddies", level:528});
MATCH (g: Goods{code: 7}), (b: Building{id:3569}) CREATE (b)-[r:Supply{max_supply: 2898.30816, current_output: 2898.30816,level: 528}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3569}) CREATE (b)-[r:Supply{max_supply: 395.22384, current_output: 395.22384,level: 528}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3569}) CREATE (b)-[r:Supply{max_supply: 395.22384, current_output: 395.22384,level: 528}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3569}) CREATE (b)-[r:Supply{max_supply: 526.96512, current_output: 526.96512,level: 528}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3569}) CREATE (b)-[r:Supply{max_supply: 526.96512, current_output: 526.96512,level: 528}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3569}) CREATE (b)-[r:Supply{max_supply: 526.96512, current_output: 526.96512,level: 528}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3569}) CREATE (b)-[r:Supply{max_supply: 526.96512, current_output: 526.96512,level: 528}]->(g);
CREATE (n: Building {id: 3570, name:"building_urban_center", level:7});
MATCH (g: Goods{code: 10}), (b: Building{id:3570}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 4.857986097305676, level: 7}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3570}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 7.44029774807183, level: 7}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3570}) CREATE (b)-[r:Supply{max_supply: 140.0, current_output: 118.57986097305675,level: 7}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3570}) CREATE (b)-[r:Supply{max_supply: 35.0, current_output: 29.64496524326419,level: 7}]->(g);
CREATE (n: Building {id: 3571, name:"building_subsistence_rice_paddies", level:249});
MATCH (g: Goods{code: 7}), (b: Building{id:3571}) CREATE (b)-[r:Supply{max_supply: 1366.66513, current_output: 1366.66513,level: 249}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3571}) CREATE (b)-[r:Supply{max_supply: 186.36342, current_output: 186.36342,level: 249}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3571}) CREATE (b)-[r:Supply{max_supply: 186.36342, current_output: 186.36342,level: 249}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3571}) CREATE (b)-[r:Supply{max_supply: 248.48457, current_output: 248.48457,level: 249}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3571}) CREATE (b)-[r:Supply{max_supply: 248.48457, current_output: 248.48457,level: 249}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3571}) CREATE (b)-[r:Supply{max_supply: 248.48457, current_output: 248.48457,level: 249}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3571}) CREATE (b)-[r:Supply{max_supply: 248.48457, current_output: 248.48457,level: 249}]->(g);
CREATE (n: Building {id: 3572, name:"building_urban_center", level:3});
MATCH (g: Goods{code: 10}), (b: Building{id:3572}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 2.081994041702432, level: 3}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3572}) CREATE (g)-[r:Demand{max_demand: 3.0, current_input: 3.1886990348879274, level: 3}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3572}) CREATE (b)-[r:Supply{max_supply: 60.0, current_output: 50.81994041702433,level: 3}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3572}) CREATE (b)-[r:Supply{max_supply: 15.0, current_output: 12.704985104256082,level: 3}]->(g);
CREATE (n: Building {id: 3573, name:"building_subsistence_rice_paddies", level:304});
MATCH (g: Goods{code: 7}), (b: Building{id:3573}) CREATE (b)-[r:Supply{max_supply: 1664.67664, current_output: 1664.67664,level: 304}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3573}) CREATE (b)-[r:Supply{max_supply: 227.00136, current_output: 227.00136,level: 304}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3573}) CREATE (b)-[r:Supply{max_supply: 227.00136, current_output: 227.00136,level: 304}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3573}) CREATE (b)-[r:Supply{max_supply: 302.66848, current_output: 302.66848,level: 304}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3573}) CREATE (b)-[r:Supply{max_supply: 302.66848, current_output: 302.66848,level: 304}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3573}) CREATE (b)-[r:Supply{max_supply: 302.66848, current_output: 302.66848,level: 304}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3573}) CREATE (b)-[r:Supply{max_supply: 302.66848, current_output: 302.66848,level: 304}]->(g);
CREATE (n: Building {id: 3574, name:"building_urban_center", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3574}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 4.163988083404864, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3574}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 6.377398069775855, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3574}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 101.63988083404865,level: 6}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3574}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 25.409970208512163,level: 6}]->(g);
CREATE (n: Building {id: 3575, name:"building_subsistence_rice_paddies", level:300});
MATCH (g: Goods{code: 7}), (b: Building{id:3575}) CREATE (b)-[r:Supply{max_supply: 1646.766, current_output: 1646.766,level: 300}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3575}) CREATE (b)-[r:Supply{max_supply: 224.559, current_output: 224.559,level: 300}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3575}) CREATE (b)-[r:Supply{max_supply: 224.559, current_output: 224.559,level: 300}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3575}) CREATE (b)-[r:Supply{max_supply: 299.412, current_output: 299.412,level: 300}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3575}) CREATE (b)-[r:Supply{max_supply: 299.412, current_output: 299.412,level: 300}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3575}) CREATE (b)-[r:Supply{max_supply: 299.412, current_output: 299.412,level: 300}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3575}) CREATE (b)-[r:Supply{max_supply: 299.412, current_output: 299.412,level: 300}]->(g);
CREATE (n: Building {id: 3576, name:"building_urban_center", level:6});
MATCH (g: Goods{code: 10}), (b: Building{id:3576}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 4.163988083404864, level: 6}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3576}) CREATE (g)-[r:Demand{max_demand: 6.0, current_input: 6.377398069775855, level: 6}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3576}) CREATE (b)-[r:Supply{max_supply: 120.0, current_output: 101.63988083404865,level: 6}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3576}) CREATE (b)-[r:Supply{max_supply: 30.0, current_output: 25.409970208512163,level: 6}]->(g);
CREATE (n: Building {id: 3577, name:"building_subsistence_rice_paddies", level:316});
MATCH (g: Goods{code: 7}), (b: Building{id:3577}) CREATE (b)-[r:Supply{max_supply: 1733.93308, current_output: 1733.93308,level: 316}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3577}) CREATE (b)-[r:Supply{max_supply: 236.44542, current_output: 236.44542,level: 316}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3577}) CREATE (b)-[r:Supply{max_supply: 236.44542, current_output: 236.44542,level: 316}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3577}) CREATE (b)-[r:Supply{max_supply: 315.26056, current_output: 315.26056,level: 316}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3577}) CREATE (b)-[r:Supply{max_supply: 315.26056, current_output: 315.26056,level: 316}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3577}) CREATE (b)-[r:Supply{max_supply: 315.26056, current_output: 315.26056,level: 316}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3577}) CREATE (b)-[r:Supply{max_supply: 315.26056, current_output: 315.26056,level: 316}]->(g);
CREATE (n: Building {id: 3578, name:"building_urban_center", level:2});
MATCH (g: Goods{code: 10}), (b: Building{id:3578}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 1.3879960278016215, level: 2}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:3578}) CREATE (g)-[r:Demand{max_demand: 2.0, current_input: 2.1257993565919517, level: 2}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:3578}) CREATE (b)-[r:Supply{max_supply: 40.0, current_output: 33.87996027801621,level: 2}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:3578}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 8.469990069504053,level: 2}]->(g);
CREATE (n: Building {id: 3579, name:"building_subsistence_rice_paddies", level:72});
MATCH (g: Goods{code: 7}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 365.08032, current_output: 365.08032,level: 72}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 49.78368, current_output: 49.78368,level: 72}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 49.78368, current_output: 49.78368,level: 72}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 66.37824, current_output: 66.37824,level: 72}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 66.37824, current_output: 66.37824,level: 72}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 66.37824, current_output: 66.37824,level: 72}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3579}) CREATE (b)-[r:Supply{max_supply: 66.37824, current_output: 66.37824,level: 72}]->(g);
CREATE (n: Building {id: 3597, name:"building_subsistence_pastures", level:16});
MATCH (g: Goods{code: 7}), (b: Building{id:3597}) CREATE (b)-[r:Supply{max_supply: 2.62808, current_output: 2.62808,level: 16}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3597}) CREATE (b)-[r:Supply{max_supply: 3.94212, current_output: 3.94212,level: 16}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3597}) CREATE (b)-[r:Supply{max_supply: 1.31404, current_output: 1.31404,level: 16}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3597}) CREATE (b)-[r:Supply{max_supply: 2.62808, current_output: 2.62808,level: 16}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3597}) CREATE (b)-[r:Supply{max_supply: 2.62808, current_output: 2.62808,level: 16}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3597}) CREATE (b)-[r:Supply{max_supply: 2.62808, current_output: 2.62808,level: 16}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3597}) CREATE (b)-[r:Supply{max_supply: 8.72522, current_output: 8.72522,level: 16}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3597}) CREATE (b)-[r:Supply{max_supply: 2.62808, current_output: 2.62808,level: 16}]->(g);
CREATE (n: Building {id: 3605, name:"building_subsistence_pastures", level:50});
MATCH (g: Goods{code: 7}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 1.17375, current_output: 1.17375,level: 50}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 1.76062, current_output: 1.76062,level: 50}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 0.58687, current_output: 0.58687,level: 50}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 1.17375, current_output: 1.17375,level: 50}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 1.17375, current_output: 1.17375,level: 50}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 1.17375, current_output: 1.17375,level: 50}]->(g);
MATCH (g: Goods{code: 36}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 3.89685, current_output: 3.89685,level: 50}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3605}) CREATE (b)-[r:Supply{max_supply: 1.17375, current_output: 1.17375,level: 50}]->(g);
CREATE (n: Building {id: 33558040, name:"building_conscription_center", level:6});
CREATE (n: Building {id: 3615, name:"building_subsistence_farms", level:13});
MATCH (g: Goods{code: 7}), (b: Building{id:3615}) CREATE (b)-[r:Supply{max_supply: 6.8094, current_output: 6.8094,level: 13}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3615}) CREATE (b)-[r:Supply{max_supply: 1.36188, current_output: 1.36188,level: 13}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3615}) CREATE (b)-[r:Supply{max_supply: 1.36188, current_output: 1.36188,level: 13}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3615}) CREATE (b)-[r:Supply{max_supply: 1.36188, current_output: 1.36188,level: 13}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3615}) CREATE (b)-[r:Supply{max_supply: 1.36188, current_output: 1.36188,level: 13}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3615}) CREATE (b)-[r:Supply{max_supply: 1.36188, current_output: 1.36188,level: 13}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3615}) CREATE (b)-[r:Supply{max_supply: 1.36188, current_output: 1.36188,level: 13}]->(g);
CREATE (n: Building {id: 3616, name:"building_subsistence_farms", level:40});
MATCH (g: Goods{code: 7}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 4.428, current_output: 3.9852,level: 40}]->(g);
MATCH (g: Goods{code: 9}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 0.8855999999999999, current_output: 0.79704,level: 40}]->(g);
MATCH (g: Goods{code: 10}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 0.8855999999999999, current_output: 0.79704,level: 40}]->(g);
MATCH (g: Goods{code: 12}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 0.8855999999999999, current_output: 0.79704,level: 40}]->(g);
MATCH (g: Goods{code: 13}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 0.8855999999999999, current_output: 0.79704,level: 40}]->(g);
MATCH (g: Goods{code: 15}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 0.8855999999999999, current_output: 0.79704,level: 40}]->(g);
MATCH (g: Goods{code: 38}), (b: Building{id:3616}) CREATE (b)-[r:Supply{max_supply: 0.8855999999999999, current_output: 0.79704,level: 40}]->(g);
CREATE (n: Building {id: 4092, name:"building_barracks", level:20});
CREATE (n: Building {id: 4093, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:4093}) CREATE (g)-[r:Demand{max_demand: 18.494, current_input: 2.3746469539133397, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4093}) CREATE (g)-[r:Demand{max_demand: 9.247, current_input: 15.095162008458969, level: 10}]->(b);
CREATE (n: Building {id: 4094, name:"building_barracks", level:10});
CREATE (n: Building {id: 4095, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4095}) CREATE (g)-[r:Demand{max_demand: 3.8624, current_input: 0.4959357842973334, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4095}) CREATE (g)-[r:Demand{max_demand: 2.414, current_input: 3.9407073741126797, level: 5}]->(b);
CREATE (n: Building {id: 4096, name:"building_barracks", level:5});
MATCH (g: Goods{code: 7}), (b: Building{id:4096}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.162194229728003, level: 5}]->(b);
CREATE (n: Building {id: 4097, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4097}) CREATE (g)-[r:Demand{max_demand: 9.204, current_input: 1.181802236607461, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4097}) CREATE (g)-[r:Demand{max_demand: 4.602, current_input: 7.512483569041654, level: 5}]->(b);
CREATE (n: Building {id: 4098, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4098}) CREATE (g)-[r:Demand{max_demand: 9.712, current_input: 1.2470299132911409, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4098}) CREATE (g)-[r:Demand{max_demand: 4.856, current_input: 7.927123035911836, level: 5}]->(b);
CREATE (n: Building {id: 4099, name:"building_barracks", level:15});
CREATE (n: Building {id: 4100, name:"building_barracks", level:20});
CREATE (n: Building {id: 4101, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:4101}) CREATE (g)-[r:Demand{max_demand: 17.822, current_input: 2.288361523339653, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4101}) CREATE (g)-[r:Demand{max_demand: 8.911, current_input: 14.546662556221245, level: 10}]->(b);
CREATE (n: Building {id: 4102, name:"building_barracks", level:20});
CREATE (n: Building {id: 4103, name:"building_barracks", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:4103}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.568018767074013, level: 20}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4103}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 16.324388459456006, level: 20}]->(b);
CREATE (n: Building {id: 4104, name:"building_barracks", level:25});
MATCH (g: Goods{code: 1}), (b: Building{id:4104}) CREATE (g)-[r:Demand{max_demand: 14.0, current_input: 1.7976131369518091, level: 25}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4104}) CREATE (g)-[r:Demand{max_demand: 7.0, current_input: 11.427071921619206, level: 25}]->(b);
CREATE (n: Building {id: 4105, name:"building_barracks", level:15});
CREATE (n: Building {id: 4106, name:"building_barracks", level:5});
MATCH (g: Goods{code: 1}), (b: Building{id:4106}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 1.2840093835370066, level: 5}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4106}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 8.162194229728003, level: 5}]->(b);
CREATE (n: Building {id: 4107, name:"building_barracks", level:20});
CREATE (n: Building {id: 4108, name:"building_barracks", level:10});
MATCH (g: Goods{code: 1}), (b: Building{id:4108}) CREATE (g)-[r:Demand{max_demand: 20.0, current_input: 2.568018767074013, level: 10}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4108}) CREATE (g)-[r:Demand{max_demand: 10.0, current_input: 16.324388459456006, level: 10}]->(b);
CREATE (n: Building {id: 4109, name:"building_barracks", level:20});
CREATE (n: Building {id: 4110, name:"building_barracks", level:20});
CREATE (n: Building {id: 4111, name:"building_barracks", level:15});
CREATE (n: Building {id: 4112, name:"building_barracks", level:20});
MATCH (g: Goods{code: 1}), (b: Building{id:4112}) CREATE (g)-[r:Demand{max_demand: 1.7421, current_input: 0.2236872747059819, level: 20}]->(b);
MATCH (g: Goods{code: 7}), (b: Building{id:4112}) CREATE (g)-[r:Demand{max_demand: 0.87105, current_input: 1.4219358567609153, level: 20}]->(b);
CREATE (n: Building {id: 4113, name:"building_barracks", level:20});
CREATE (n: Building {id: 4114, name:"building_barracks", level:20});
CREATE (n: Building {id: 4115, name:"building_barracks", level:20});
CREATE (n: Building {id: 4116, name:"building_barracks", level:20});
CREATE (n: Building {id: 4117, name:"building_barracks", level:20});
CREATE (n: Building {id: 4118, name:"building_barracks", level:20});
CREATE (n: Building {id: 4119, name:"building_barracks", level:20});
CREATE (n: Building {id: 4120, name:"building_barracks", level:10});
CREATE (n: Building {id: 4121, name:"building_barracks", level:5});
CREATE (n: Building {id: 4122, name:"building_barracks", level:20});
CREATE (n: Building {id: 4123, name:"building_barracks", level:10});
CREATE (n: Building {id: 4124, name:"building_naval_base", level:7});
MATCH (g: Goods{code: 5}), (b: Building{id:4124}) CREATE (g)-[r:Demand{max_demand: 4.15695, current_input: 2.4768840457835224, level: 7}]->(b);
CREATE (n: Building {id: 4125, name:"building_naval_base", level:8});
MATCH (g: Goods{code: 5}), (b: Building{id:4125}) CREATE (g)-[r:Demand{max_demand: 6.036, current_input: 3.5965003428834454, level: 8}]->(b);
CREATE (n: Building {id: 4212, name:"building_urban_center", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:4212}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 0.6939980139008107, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:4212}) CREATE (g)-[r:Demand{max_demand: 1.0, current_input: 1.0628996782959759, level: 1}]->(b);
MATCH (g: Goods{code: 15}), (b: Building{id:4212}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 16.939980139008107,level: 1}]->(g);
MATCH (g: Goods{code: 16}), (b: Building{id:4212}) CREATE (b)-[r:Supply{max_supply: 5.0, current_output: 4.234995034752027,level: 1}]->(g);
CREATE (n: Building {id: 4213, name:"building_trade_center", level:107});
CREATE (n: Building {id: 4434, name:"building_artillery_foundries", level:1});
MATCH (g: Goods{code: 24}), (b: Building{id:4434}) CREATE (g)-[r:Demand{max_demand: 0.02715, current_input: 0.003304622652644311, level: 1}]->(b);
MATCH (g: Goods{code: 26}), (b: Building{id:4434}) CREATE (g)-[r:Demand{max_demand: 0.0181, current_input: 0.0027151699201954974, level: 1}]->(b);
MATCH (g: Goods{code: 2}), (b: Building{id:4434}) CREATE (b)-[r:Supply{max_supply: 0.04525, current_output: 0.006147814610781297,level: 1}]->(g);
CREATE (n: Building {id: 4435, name:"building_conscription_center", level:25});
CREATE (n: Building {id: 4574, name:"building_conscription_center", level:13});
CREATE (n: Building {id: 4653, name:"building_conscription_center", level:24});
CREATE (n: Building {id: 4724, name:"building_conscription_center", level:16});
CREATE (n: Building {id: 4770, name:"building_conscription_center", level:23});
CREATE (n: Building {id: 4824, name:"building_conscription_center", level:25});
CREATE (n: Building {id: 4902, name:"building_conscription_center", level:25});
CREATE (n: Building {id: 4962, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 4993, name:"building_conscription_center", level:25});
CREATE (n: Building {id: 5017, name:"building_conscription_center", level:25});
CREATE (n: Building {id: 5034, name:"building_glassworks", level:1});
MATCH (g: Goods{code: 10}), (b: Building{id:5034}) CREATE (g)-[r:Demand{max_demand: 30.0, current_input: 20.81994041702432, level: 1}]->(b);
MATCH (g: Goods{code: 21}), (b: Building{id:5034}) CREATE (g)-[r:Demand{max_demand: 5.0, current_input: 26.433590196078434, level: 1}]->(b);
MATCH (g: Goods{code: 31}), (b: Building{id:5034}) CREATE (b)-[r:Supply{max_supply: 10.0, current_output: 8.469990069504053,level: 1}]->(g);
MATCH (g: Goods{code: 35}), (b: Building{id:5034}) CREATE (b)-[r:Supply{max_supply: 20.0, current_output: 16.939980139008107,level: 1}]->(g);
CREATE (n: Building {id: 5036, name:"building_coal_mine", level:1});
MATCH (g: Goods{code: 33}), (b: Building{id:5036}) CREATE (g)-[r:Demand{max_demand: 0.045, current_input: 0.008559796852411077, level: 1}]->(b);
MATCH (g: Goods{code: 23}), (b: Building{id:5036}) CREATE (b)-[r:Supply{max_supply: 0.225, current_output: 0.04279898426205539,level: 1}]->(g);
CREATE (n: Building {id: 33559493, name:"building_conscription_center", level:9});
CREATE (n: Building {id: 5110, name:"building_conscription_center", level:25});
CREATE (n: Building {id: 5118, name:"building_conscription_center", level:9});
CREATE (n: Building {id: 5122, name:"building_conscription_center", level:15});
CREATE (n: Building {id: 5214, name:"building_conscription_center", level:21});
CREATE (n: Building {id: 33560011, name:"building_conscription_center", level:4});
CREATE (n: Building {id: 16782831, name:"building_conscription_center", level:3});
CREATE (n: Building {id: 16782849, name:"building_conscription_center", level:6});
CREATE (n: Building {id: 5669, name:"building_conscription_center", level:15});
CREATE (n: Building {id: 5681, name:"building_conscription_center", level:10});
CREATE (n: Building {id: 5684, name:"building_conscription_center", level:10});
CREATE (n: Building {id: 5687, name:"building_conscription_center", level:16});
CREATE (n: Building {id: 5694, name:"building_conscription_center", level:14});
CREATE (n: Building {id: 5715, name:"building_conscription_center", level:22});
CREATE (n: Building {id: 5737, name:"building_conscription_center", level:12});
CREATE (n: Building {id: 5748, name:"building_conscription_center", level:22});
CREATE (n: Building {id: 5765, name:"building_conscription_center", level:5});
CREATE (n: Building {id: 5790, name:"building_conscription_center", level:8});
CREATE (n: Building {id: 5830, name:"building_conscription_center", level:2});
CREATE (n: Building {id: 5843, name:"building_conscription_center", level:1});
