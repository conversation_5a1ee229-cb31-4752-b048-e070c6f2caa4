
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 79.90989477597961, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 93.87406993789546, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 109.51974826087803, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 115.34787096525737, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 31.063753965671804, 7827.772270980946);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 27.957499385778775, 547.552670355116);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 32.87020314797486, 728.955922749186);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 33.15043379929322, 605.2364946993979);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 42.78222178977777, 363.87274429951134);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 46.96729280471316, 2001.319361039455);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 44.93791350946756, 1550.1971870992577);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 48.91029284495107, 87.94327195396903);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 31.113438340573175, 1313.5736925391868);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 14.345095773768785, 96.99577382463802);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 95.26348820480415, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 82.801118836083, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 48.240084319998175, 155.47798036378862);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 63.07806873614523, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 64.73725309641297, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 69.68922967293919, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 34.59258153899051, 7.563512252867521);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 90.6852926787356, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 77.51340755164782, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 65.63194603001108, 78.3894672198897);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 48.55487278182677, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 63.766793551938875, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 80.03230204606885, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 164.2211612063414);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 45.52442053494252, 567.7490976417806);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 31.14001447091141, 76.63295570920941);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 42.29535026594849, 1665.3155937702998);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 67.7070250769795, 16.242547169414433);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 85.94252067766067, 481.5473240912776);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 51.83488818534583, 151.82904873930806);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 41.75181879424979, 156.8054156305289);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 59.89603550599094, 149.94114447831723);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 87.5, 582.8964656551655);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 100.0897789172992, 801.7080672311797);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 95.57276298534514, 432.4655863614223);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 306.6657351168793, 1.8239575909272423);
INSERT INTO building(id, name, level) VALUES (85, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 85, 199.99999999999997, 35.40046219946348, 10);
INSERT INTO building(id, name, level) VALUES (86, "building_arts_academylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 86, 7.4424705882352935, 1.3173344936472116, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 86, 2.9769803921568627, 0.526932409205465, 3);
INSERT INTO building(id, name, level) VALUES (87, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 87, 46.1874, 6.73406108685749, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 87, 92.3748, 11.635279271036644, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 87, 115.4685, 29.457529853873016, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 87, 23.0937, 5.422498841544394, 3);
INSERT INTO building(id, name, level) VALUES (88, "building_universitylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 88, 25.0, 4.425057774932935, 5);
INSERT INTO building(id, name, level) VALUES (89, "building_paper_millslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 89, 300.0, 37.78718634639527, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 89, 99.99999999999999, 13.854471663636087, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 89, 700.0, 92.57570156018747, 10);
INSERT INTO building(id, name, level) VALUES (90, "building_textile_millslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 90, 750.0, 109.34899594138483, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 90, 250.0, 0.0, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 90, 125.0, 0.0, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 90, 1000.0, 48.59955375172659, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 90, 500.0, 24.299776875863294, 25);
INSERT INTO building(id, name, level) VALUES (91, "building_shipyardslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 91, 100.0, 14.579866125517977, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 91, 200.0, 25.191457564263512, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 91, 100.0, 1.1148449310683315, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 91, 50.0, 18.5502270966073, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 91, 125.0, 20.434654384978963, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 91, 225.0, 36.78237789296213, 5);
INSERT INTO building(id, name, level) VALUES (92, "building_furniture_manufacturieslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 92, 150.0, 21.869799188276968, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 92, 300.0, 37.78718634639527, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 92, 150.0, 1.6722673966024972, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 92, 75.0, 17.61031853344547, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 92, 675.0000000000001, 87.36333392808905, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 92, 300.0, 38.82814841248402, 15);
INSERT INTO building(id, name, level) VALUES (93, "building_naval_baselevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 93, 50.0, 7.261658476543817, 25);
INSERT INTO building(id, name, level) VALUES (94, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 94, 15.0, 3.5359117894227916, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 94, 15.0, 4.338565580575129, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 94, 15.0, 4.338565580575129, 15);
INSERT INTO building(id, name, level) VALUES (95, "building_rye_farmlevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 95, 74.91525, 14.522085826361522, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 95, 14.983048387096776, 3.5180833959840077, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 95, 749.1525, 160.56252349932097, 15);
INSERT INTO building(id, name, level) VALUES (96, "building_livestock_ranchlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 96, 49.949000000000005, 13.519704526306006, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 96, 49.949000000000005, 11.72823733902757, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 96, 199.79600000000002, 50.49588373066715, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 96, 24.974500000000003, 6.311985466333394, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 96, 124.8725, 31.55992733166697, 5);
INSERT INTO building(id, name, level) VALUES (97, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 97, 10.0, 2.306644457255094, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 97, 100.0, 23.066444572550942, 2);
INSERT INTO building(id, name, level) VALUES (98, "building_railwaylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 98, 15.0, 1.8893593173197634, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 98, 30.0, 6.1127398455894, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 98, 30.0, 11.13013625796438, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 98, 195.0, 45.54678859941884, 3);
INSERT INTO building(id, name, level) VALUES (99, "building_portlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 99, 40.0, 9.226577829020377, 8);
INSERT INTO building(id, name, level) VALUES (100, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 100, 100.0, 17.70023109973174, 5);
INSERT INTO building(id, name, level) VALUES (101, "building_textile_millslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 101, 750.0, 109.34899594138483, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 101, 250.0, 0.0, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 101, 125.0, 0.0, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 101, 1000.0, 48.59955375172659, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 101, 500.0, 24.299776875863294, 25);
INSERT INTO building(id, name, level) VALUES (102, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 102, 46.1874, 6.73406108685749, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 102, 92.3748, 11.635279271036644, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 102, 115.4685, 29.457529853873016, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 102, 23.0937, 5.422498841544394, 3);
INSERT INTO building(id, name, level) VALUES (103, "building_arms_industrylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 103, 100.0, 25.511312482515155, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 103, 50.0, 0.5574224655341657, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 103, 75.0, 9.984809030093807, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 103, 75.0, 9.984809030093807, 5);
INSERT INTO building(id, name, level) VALUES (104, "building_munition_plantslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 104, 40.0, 7.75850413163621, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 104, 40.0, 9.243331570169474, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 104, 100.0, 21.252294627257108, 2);
INSERT INTO building(id, name, level) VALUES (105, "building_coal_minelevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 105, 150.0, 35.22063706689094, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 105, 600.0, 140.88254826756375, 15);
INSERT INTO building(id, name, level) VALUES (106, "building_iron_minelevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 106, 99.99999999999999, 20.375799485297996, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 106, 99.99999999999999, 23.48042471126062, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 106, 399.99999999999994, 87.71244839311724, 10);
INSERT INTO building(id, name, level) VALUES (107, "building_rye_farmlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 107, 49.99999999999999, 9.692342898382853, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 107, 10.0, 2.3480424711260626, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 107, 499.99999999999994, 107.16277627006582, 10);
INSERT INTO building(id, name, level) VALUES (108, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 108, 30.0, 8.120105223111175, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 108, 30.0, 7.044127413378186, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 108, 120.0, 30.328465272978722, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 108, 15.0, 3.7910581591223402, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 108, 75.0, 18.955290795611702, 3);
INSERT INTO building(id, name, level) VALUES (109, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 109, 10.0, 2.357274526281861, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 109, 10.0, 2.892377053716753, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 109, 10.0, 2.892377053716753, 10);
INSERT INTO building(id, name, level) VALUES (110, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 110, 20.0, 2.904663390617526, 10);
INSERT INTO building(id, name, level) VALUES (111, "building_railwaylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 111, 20.0, 2.5191457564263513, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 111, 40.0, 8.150319794119198, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 111, 40.0, 14.840181677285841, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 111, 260.0, 60.72905146589177, 4);
INSERT INTO building(id, name, level) VALUES (112, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 112, 15.0, 3.4599666858826414, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 112, 150.0, 34.59966685882642, 3);
INSERT INTO building(id, name, level) VALUES (113, "building_portlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 113, 35.0, 8.07325560039283, 7);
INSERT INTO building(id, name, level) VALUES (114, "building_paper_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 114, 150.0, 18.893593173197637, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 114, 50.0, 6.927235831818043, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 114, 350.0, 46.287850780093734, 5);
INSERT INTO building(id, name, level) VALUES (115, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 115, 10.0, 2.357274526281861, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 115, 10.0, 2.892377053716753, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 115, 10.0, 2.892377053716753, 10);
INSERT INTO building(id, name, level) VALUES (116, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 116, 20.0, 5.413403482074117, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 116, 20.0, 4.696084942252125, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 116, 80.0, 20.218976848652485, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 116, 10.0, 2.5273721060815606, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 116, 50.0, 12.636860530407803, 2);
INSERT INTO building(id, name, level) VALUES (117, "building_coal_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 117, 40.00000000000001, 9.392169884504252, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 117, 160.00000000000003, 37.56867953801701, 4);
INSERT INTO building(id, name, level) VALUES (118, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 118, 15.0, 3.522063706689093, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 118, 180.0, 42.26476448026912, 3);
INSERT INTO building(id, name, level) VALUES (119, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 119, 5.0, 0.6297864391065878, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 119, 10.0, 2.0375799485297996, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 119, 10.0, 3.7100454193214603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 119, 65.0, 15.182262866472943, 1);
INSERT INTO building(id, name, level) VALUES (120, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 120, 5.0, 1.153322228627547, 1);
INSERT INTO building(id, name, level) VALUES (121, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 121, 100.0, 17.70023109973174, 5);
INSERT INTO building(id, name, level) VALUES (122, "building_tooling_workshopslevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 122, 360.0, 45.34462361567432, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 122, 239.99999999999997, 73.959321159866, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 122, 959.9999999999999, 208.3781404739644, 12);
INSERT INTO building(id, name, level) VALUES (123, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 123, 30.7916, 4.4893740579049926, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 123, 61.5832, 7.756852847357764, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 123, 76.979, 19.638353235915343, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 123, 15.3958, 3.614999227696263, 2);
INSERT INTO building(id, name, level) VALUES (124, "building_textile_millslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 124, 300.0, 43.739598376553936, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 124, 99.99999999999999, 0.0, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 124, 49.99999999999999, 0.0, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 124, 399.99999999999994, 19.439821500690634, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 124, 199.99999999999997, 9.719910750345317, 10);
INSERT INTO building(id, name, level) VALUES (125, "building_steel_millslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 125, 300.0, 61.127398455893996, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 125, 399.99999999999994, 102.0452499300606, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 125, 650.0, 149.13311389539274, 10);
INSERT INTO building(id, name, level) VALUES (126, "building_motor_industrylevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 126, 240.0, 73.959321159866, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 126, 319.99999999999994, 98.61242821315464, 8);
INSERT INTO building(id, name, level) VALUES (127, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 127, 10.0, 2.357274526281861, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 127, 10.0, 2.892377053716753, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 127, 10.0, 2.892377053716753, 10);
INSERT INTO building(id, name, level) VALUES (128, "building_naval_baselevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 128, 30.0, 4.35699508592629, 15);
INSERT INTO building(id, name, level) VALUES (129, "building_coal_minelevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 129, 100.0, 23.480424711260625, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 129, 400.0, 93.9216988450425, 10);
INSERT INTO building(id, name, level) VALUES (130, "building_iron_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 130, 50.0, 10.187899742648998, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 130, 50.0, 11.740212355630312, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 130, 200.0, 43.85622419655862, 5);
INSERT INTO building(id, name, level) VALUES (131, "building_rye_farmlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 131, 39.99999999999999, 7.753874318706282, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 131, 8.0, 1.8784339769008498, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 131, 400.0, 85.73022101605267, 8);
INSERT INTO building(id, name, level) VALUES (132, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 132, 30.0, 8.120105223111175, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 132, 30.0, 7.044127413378186, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 132, 120.0, 30.328465272978722, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 132, 15.0, 3.7910581591223402, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 132, 75.0, 18.955290795611702, 3);
INSERT INTO building(id, name, level) VALUES (133, "building_railwaylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 133, 25.0, 3.148932195532939, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 133, 50.0, 10.187899742648998, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 133, 50.0, 18.5502270966073, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 133, 325.0, 75.91131433236473, 5);
INSERT INTO building(id, name, level) VALUES (134, "building_portlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 134, 20.0, 4.613288914510188, 4);
INSERT INTO building(id, name, level) VALUES (135, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 135, 5.0, 1.1786372631409305, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 135, 5.0, 1.4461885268583765, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 135, 5.0, 1.4461885268583765, 5);
INSERT INTO building(id, name, level) VALUES (136, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 136, 15.0, 3.522063706689093, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 136, 120.0, 28.176509653512746, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 136, 30.0, 7.044127413378186, 3);
INSERT INTO building(id, name, level) VALUES (137, "building_whaling_stationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 137, 10.0, 2.306644457255094, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (28, 137, 40.0, 9.226577829020377, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 137, 20.0, 4.613288914510188, 2);
INSERT INTO building(id, name, level) VALUES (138, "building_fishing_wharflevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 138, 20.0, 4.613288914510188, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 138, 200.0, 46.132889145101885, 4);
INSERT INTO building(id, name, level) VALUES (139, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 139, 100.0, 17.70023109973174, 5);
INSERT INTO building(id, name, level) VALUES (140, "building_arms_industrylevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 140, 159.99999999999997, 40.81809997202424, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 140, 79.99999999999999, 0.8918759448546651, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 140, 120.0, 15.975694448150094, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 140, 120.0, 15.975694448150094, 8);
INSERT INTO building(id, name, level) VALUES (141, "building_textile_millslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 141, 750.0, 109.34899594138483, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 141, 250.0, 0.0, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 141, 125.0, 0.0, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 141, 1000.0, 48.59955375172659, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 141, 500.0, 24.299776875863294, 25);
INSERT INTO building(id, name, level) VALUES (142, "building_furniture_manufacturieslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 142, 50.0, 7.289933062758989, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 142, 100.0, 12.595728782131756, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 142, 50.0, 0.5574224655341657, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 142, 25.0, 5.870106177815156, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 142, 225.0, 29.121111309363013, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 142, 100.0, 12.942716137494672, 5);
INSERT INTO building(id, name, level) VALUES (143, "building_chemical_plantslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 143, 60.0, 8.312682998181653, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 143, 30.0, 6.1127398455894, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 143, 20.0, 5.102262496503031, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 143, 150.0, 29.870791815724626, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 143, 40.0, 7.9655444841932335, 2);
INSERT INTO building(id, name, level) VALUES (144, "building_coal_minelevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 144, 79.99999999999999, 18.784339769008497, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 144, 319.99999999999994, 75.13735907603399, 8);
INSERT INTO building(id, name, level) VALUES (145, "building_iron_minelevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 145, 79.99999999999999, 16.300639588238397, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 145, 79.99999999999999, 18.784339769008497, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 145, 319.99999999999994, 70.16995871449379, 8);
INSERT INTO building(id, name, level) VALUES (146, "building_rye_farmlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 146, 49.99999999999999, 9.692342898382853, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 146, 10.0, 2.3480424711260626, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 146, 499.99999999999994, 107.16277627006582, 10);
INSERT INTO building(id, name, level) VALUES (147, "building_livestock_ranchlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 147, 50.0, 13.533508705185294, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 147, 50.0, 11.740212355630312, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 147, 200.0, 50.547442121631214, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 147, 25.0, 6.318430265203902, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 147, 125.0, 31.592151326019508, 5);
INSERT INTO building(id, name, level) VALUES (148, "building_railwaylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 148, 25.0, 3.148932195532939, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 148, 50.0, 10.187899742648998, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 148, 50.0, 18.5502270966073, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 148, 325.0, 75.91131433236473, 5);
INSERT INTO building(id, name, level) VALUES (149, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 149, 20.0, 4.714549052563722, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 149, 20.0, 5.784754107433506, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 149, 20.0, 5.784754107433506, 20);
INSERT INTO building(id, name, level) VALUES (150, "building_portlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 150, 20.0, 4.613288914510188, 4);
INSERT INTO building(id, name, level) VALUES (151, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 151, 60.0, 10.620138659839046, 3);
INSERT INTO building(id, name, level) VALUES (152, "building_universitylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 152, 25.0, 4.425057774932935, 5);
INSERT INTO building(id, name, level) VALUES (153, "building_furniture_manufacturieslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 153, 150.0, 21.869799188276968, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 153, 300.0, 37.78718634639527, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 153, 150.0, 1.6722673966024972, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 153, 75.0, 17.61031853344547, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 153, 675.0000000000001, 87.36333392808905, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 153, 300.0, 38.82814841248402, 15);
INSERT INTO building(id, name, level) VALUES (154, "building_glassworkslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 154, 100.0, 12.595728782131756, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 154, 50.0, 9.698130164545262, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 154, 200.0, 31.99198911122228, 5);
INSERT INTO building(id, name, level) VALUES (155, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 155, 160.0, 43.30722785659294, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 155, 160.0, 76.43151079644592, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 155, 139.99999999999997, 52.38569816070449, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 155, 239.99999999999997, 89.80405398977913, 4);
INSERT INTO building(id, name, level) VALUES (156, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 156, 20.0, 4.075159897059599, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 156, 20.0, 4.696084942252125, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 156, 80.0, 17.54248967862345, 2);
INSERT INTO building(id, name, level) VALUES (157, "building_shipyardslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 157, 20.0, 2.9159732251035955, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 157, 40.0, 5.038291512852703, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 157, 20.0, 0.22296898621366631, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 157, 10.0, 3.7100454193214603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 157, 24.999999999999996, 4.086930876995792, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 157, 44.99999999999999, 7.356475578592425, 1);
INSERT INTO building(id, name, level) VALUES (158, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 158, 5.0, 1.1786372631409305, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 158, 5.0, 1.4461885268583765, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 158, 5.0, 1.4461885268583765, 5);
INSERT INTO building(id, name, level) VALUES (159, "building_naval_baselevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 159, 50.0, 7.261658476543817, 25);
INSERT INTO building(id, name, level) VALUES (160, "building_rye_farmlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 160, 35.00000000000001, 6.784640028868, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 160, 7.0, 1.6436297297882438, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 160, 350.0, 75.01394338904609, 7);
INSERT INTO building(id, name, level) VALUES (161, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 161, 30.0, 8.120105223111175, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 161, 30.0, 7.044127413378186, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 161, 120.0, 30.328465272978722, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 161, 15.0, 3.7910581591223402, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 161, 74.99999999999999, 18.9552907956117, 3);
INSERT INTO building(id, name, level) VALUES (162, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 162, 15.0, 3.4599666858826414, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 162, 150.0, 34.59966685882642, 3);
INSERT INTO building(id, name, level) VALUES (163, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 163, 5.0, 0.6297864391065878, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 163, 10.0, 2.0375799485297996, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 163, 10.0, 3.7100454193214603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 163, 65.0, 15.182262866472943, 1);
INSERT INTO building(id, name, level) VALUES (164, "building_portlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 164, 25.0, 5.766611143137736, 5);
INSERT INTO building(id, name, level) VALUES (165, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 165, 100.0, 17.70023109973174, 5);
INSERT INTO building(id, name, level) VALUES (166, "building_tooling_workshopslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 166, 119.99999999999999, 15.114874538558105, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 166, 80.0, 24.653107053288664, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 166, 320.0, 69.45938015798815, 4);
INSERT INTO building(id, name, level) VALUES (167, "building_textile_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 167, 150.0, 21.869799188276968, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 167, 50.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 167, 25.0, 0.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 167, 200.0, 9.719910750345319, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 167, 100.0, 4.859955375172659, 5);
INSERT INTO building(id, name, level) VALUES (168, "building_shipyardslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 168, 80.00000000000001, 11.663892900414384, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 168, 160.00000000000003, 20.153166051410814, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 168, 80.00000000000001, 0.8918759448546655, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 168, 40.00000000000001, 14.840181677285845, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 168, 100.00000000000001, 16.347723507983172, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 168, 180.00000000000003, 29.42590231436971, 4);
INSERT INTO building(id, name, level) VALUES (169, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 169, 5.0, 1.1786372631409305, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 169, 5.0, 1.4461885268583765, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 169, 5.0, 1.4461885268583765, 5);
INSERT INTO building(id, name, level) VALUES (170, "building_naval_baselevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 170, 50.0, 7.261658476543817, 25);
INSERT INTO building(id, name, level) VALUES (171, "building_wheat_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 171, 25.0, 4.846171449191426, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 171, 5.0, 1.1740212355630313, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 171, 175.0, 37.50697169452304, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 171, 40.0, 8.573022101605265, 5);
INSERT INTO building(id, name, level) VALUES (172, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 172, 30.0, 8.120105223111175, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 172, 30.0, 7.044127413378186, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 172, 120.0, 30.328465272978722, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 172, 15.0, 3.7910581591223402, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 172, 75.0, 18.955290795611702, 3);
INSERT INTO building(id, name, level) VALUES (173, "building_sulfur_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 173, 30.0, 6.1127398455894, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 173, 30.0, 7.044127413378186, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 173, 120.0, 26.313734517935174, 3);
INSERT INTO building(id, name, level) VALUES (174, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 174, 5.0, 0.6297864391065878, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 174, 10.0, 2.0375799485297996, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 174, 10.0, 3.7100454193214603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 174, 65.0, 15.182262866472943, 1);
INSERT INTO building(id, name, level) VALUES (175, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 175, 10.0, 2.306644457255094, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 175, 100.0, 23.066444572550942, 2);
INSERT INTO building(id, name, level) VALUES (176, "building_portlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 176, 25.0, 5.766611143137736, 5);
INSERT INTO building(id, name, level) VALUES (177, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 177, 60.0, 10.620138659839046, 3);
INSERT INTO building(id, name, level) VALUES (178, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 178, 15.3958, 2.2446870289524963, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 178, 30.7916, 3.878426423678882, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 178, 38.4895, 9.819176617957671, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 178, 7.6979, 1.8074996138481314, 1);
INSERT INTO building(id, name, level) VALUES (179, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 179, 160.0, 43.30722785659294, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 179, 160.0, 76.43151079644592, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 179, 139.99999999999997, 52.38569816070449, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 179, 239.99999999999997, 89.80405398977913, 4);
INSERT INTO building(id, name, level) VALUES (180, "building_glassworkslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 180, 120.0, 15.114874538558107, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 180, 60.0, 11.637756197454314, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 180, 240.0, 38.390386933466736, 6);
INSERT INTO building(id, name, level) VALUES (181, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 181, 10.0, 2.3480424711260626, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 181, 120.0, 28.17650965351275, 2);
INSERT INTO building(id, name, level) VALUES (182, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 182, 20.0, 4.075159897059599, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 182, 20.0, 4.696084942252125, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 182, 80.0, 17.54248967862345, 2);
INSERT INTO building(id, name, level) VALUES (183, "building_rye_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 183, 25.0, 4.846171449191426, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 183, 5.0, 1.1740212355630313, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 183, 250.0, 53.58138813503291, 5);
INSERT INTO building(id, name, level) VALUES (184, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 184, 10.0, 2.7067017410370586, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 184, 10.0, 2.3480424711260626, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 184, 40.0, 10.109488424326242, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 184, 5.0, 1.2636860530407803, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 184, 25.0, 6.318430265203902, 1);
INSERT INTO building(id, name, level) VALUES (185, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 185, 10.0, 2.306644457255094, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 185, 100.0, 23.066444572550942, 2);
INSERT INTO building(id, name, level) VALUES (186, "building_naval_baselevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 186, 30.0, 4.35699508592629, 15);
INSERT INTO building(id, name, level) VALUES (187, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 187, 10.0, 2.357274526281861, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 187, 10.0, 2.892377053716753, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 187, 10.0, 2.892377053716753, 10);
INSERT INTO building(id, name, level) VALUES (188, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 188, 5.0, 0.6297864391065878, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 188, 10.0, 2.0375799485297996, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 188, 10.0, 3.7100454193214603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 188, 65.0, 15.182262866472943, 1);
INSERT INTO building(id, name, level) VALUES (189, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 189, 15.0, 3.4599666858826414, 3);
INSERT INTO building(id, name, level) VALUES (190, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 190, 20.0, 5.413403482074117, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 190, 20.0, 4.696084942252125, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 190, 80.0, 20.218976848652485, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 190, 10.0, 2.5273721060815606, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 190, 50.0, 12.636860530407803, 2);
INSERT INTO building(id, name, level) VALUES (191, "building_rye_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 191, 15.0, 2.907702869514856, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 191, 3.0, 0.7044127413378186, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 191, 90.0, 19.289299728611848, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 191, 45.0, 9.644649864305924, 3);
INSERT INTO building(id, name, level) VALUES (192, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 192, 5.0, 1.1786372631409305, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 192, 5.0, 1.4461885268583765, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 192, 5.0, 1.4461885268583765, 5);
INSERT INTO building(id, name, level) VALUES (193, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 193, 10.0, 2.3480424711260626, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 193, 120.0, 28.17650965351275, 2);
INSERT INTO building(id, name, level) VALUES (194, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 194, 10.0, 2.306644457255094, 2);
INSERT INTO building(id, name, level) VALUES (195, "building_rye_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 195, 15.0, 2.907702869514856, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 195, 2.9999999999999996, 0.7044127413378186, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 195, 89.99999999999999, 19.289299728611848, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 195, 44.99999999999999, 9.644649864305924, 3);
INSERT INTO building(id, name, level) VALUES (196, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 196, 3.0, 0.7071823578845583, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 196, 3.0, 0.8677131161150259, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 196, 3.0, 0.8677131161150259, 3);
INSERT INTO building(id, name, level) VALUES (197, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 197, 10.0, 1.452331695308763, 5);
INSERT INTO building(id, name, level) VALUES (198, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 198, 10.0, 2.306644457255094, 2);
INSERT INTO building(id, name, level) VALUES (199, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 199, 5.0, 0.8850115549865872, 1);
INSERT INTO building(id, name, level) VALUES (200, "building_lead_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 200, 30.0, 6.1127398455894, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 200, 30.0, 7.044127413378186, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 200, 120.0, 26.313734517935174, 3);
INSERT INTO building(id, name, level) VALUES (201, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 201, 160.0, 43.30722785659294, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 201, 160.0, 76.43151079644592, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 201, 139.99999999999997, 52.38569816070449, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 201, 239.99999999999997, 89.80405398977913, 4);
INSERT INTO building(id, name, level) VALUES (202, "building_rye_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 202, 25.0, 4.846171449191426, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 202, 5.0, 1.1740212355630313, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 202, 150.0, 32.14883288101974, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 202, 75.0, 16.07441644050987, 5);
INSERT INTO building(id, name, level) VALUES (203, "building_naval_baselevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 203, 30.0, 4.35699508592629, 15);
INSERT INTO building(id, name, level) VALUES (204, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 204, 5.0, 1.1786372631409305, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 204, 5.0, 1.4461885268583765, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 204, 5.0, 1.4461885268583765, 5);
INSERT INTO building(id, name, level) VALUES (205, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 205, 15.0, 3.4599666858826414, 3);
INSERT INTO building(id, name, level) VALUES (206, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 206, 10.0, 1.9384685796765708, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 206, 2.0, 0.46960849422521245, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 206, 60.0, 12.859533152407899, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 206, 30.0, 6.429766576203949, 2);
INSERT INTO building(id, name, level) VALUES (207, "building_barrackslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 207, 2.0, 0.47145490525637224, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 207, 2.0, 0.5784754107433506, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 207, 2.0, 0.5784754107433506, 2);
INSERT INTO building(id, name, level) VALUES (208, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 208, 5.0, 1.153322228627547, 1);
INSERT INTO building(id, name, level) VALUES (628, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 628, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (629, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 629, 10.0, 1.452331695308763, 5);
INSERT INTO building(id, name, level) VALUES (630, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 630, 5.0, 1.153322228627547, 1);
INSERT INTO building(id, name, level) VALUES (790, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 790, 10.0, 1.452331695308763, 5);
INSERT INTO building(id, name, level) VALUES (791, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 791, 5.0, 1.153322228627547, 1);
INSERT INTO building(id, name, level) VALUES (1266, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1302, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1626, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1626, 10.0, 2.306644457255094, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1626, 100.0, 23.066444572550942, 2);
INSERT INTO building(id, name, level) VALUES (1632, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1632, 10.0, 1.452331695308763, 5);
INSERT INTO building(id, name, level) VALUES (1633, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1699, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1729, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1729, 30.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (1730, "building_sugar_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1730, 30.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (1731, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1738, "building_sugar_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1738, 90.0, 91.8, 3);
INSERT INTO building(id, name, level) VALUES (1739, "building_coffee_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1739, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (1740, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1740, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (1741, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1742, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1742, 30.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (1743, "building_sugar_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1743, 90.0, 91.8, 3);
INSERT INTO building(id, name, level) VALUES (1744, "building_coffee_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1744, 100.0, 104.0, 5);
INSERT INTO building(id, name, level) VALUES (1745, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1763, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1763, 60.0, 60.6, 2);
INSERT INTO building(id, name, level) VALUES (1764, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1892, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (2330, "building_sugar_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2330, 30.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (2331, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (2723, "building_tea_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2723, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (2724, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2724, 5.0, 1.153322228627547, 1);
INSERT INTO building(id, name, level) VALUES (2873, "building_subsistence_farmslevel", 10);
INSERT INTO building(id, name, level) VALUES (2874, "building_urban_centerlevel", 24);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2874, 120.0, 15.114874538558107, 24);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2874, 240.0, 48.9019187647152, 24);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2874, 120.0, 19.57116669902163, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2874, 1680.0, 275.9726695597076, 24);
INSERT INTO building(id, name, level) VALUES (2875, "building_subsistence_farmslevel", 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2875, 0.71715, 0.71715, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2875, 0.14343, 0.14343, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2875, 0.14343, 0.14343, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2875, 0.14343, 0.14343, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2875, 0.14343, 0.14343, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2875, 0.14343, 0.14343, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2875, 0.2008, 0.2008, 21);
INSERT INTO building(id, name, level) VALUES (2876, "building_urban_centerlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2876, 59.99999999999999, 7.557437269279053, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2876, 119.99999999999999, 24.450959382357595, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2876, 59.99999999999999, 9.785583349510816, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2876, 839.9999999999999, 137.98633477985376, 12);
INSERT INTO building(id, name, level) VALUES (2993, "building_subsistence_farmslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2993, 7.1715, 7.1715, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2993, 1.4343, 1.4343, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2993, 1.4343, 1.4343, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2993, 1.4343, 1.4343, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2993, 1.4343, 1.4343, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2993, 1.4343, 1.4343, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2993, 2.00802, 2.00802, 20);
INSERT INTO building(id, name, level) VALUES (2995, "building_subsistence_farmslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2995, 0.47895, 0.47895, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2995, 0.09579, 0.09579, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2995, 0.09579, 0.09579, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2995, 0.09579, 0.09579, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2995, 0.09579, 0.09579, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2995, 0.09579, 0.09579, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2995, 0.1341, 0.1341, 6);
INSERT INTO building(id, name, level) VALUES (3079, "building_subsistence_farmslevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3079, 7.18, 7.18, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3079, 1.436, 1.436, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3079, 1.436, 1.436, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3079, 1.436, 1.436, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3079, 1.436, 1.436, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3079, 1.436, 1.436, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3079, 2.0104, 2.0104, 5);
INSERT INTO building(id, name, level) VALUES (3132, "building_subsistence_farmslevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3132, 8.9064, 8.9064, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3132, 1.78128, 1.78128, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3132, 1.78128, 1.78128, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3132, 1.78128, 1.78128, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3132, 1.78128, 1.78128, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3132, 1.78128, 1.78128, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3132, 2.49379, 2.49379, 12);
INSERT INTO building(id, name, level) VALUES (3202, "building_subsistence_pastureslevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3202, 0.3151, 0.3151, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3202, 0.47265, 0.47265, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3202, 0.15755, 0.15755, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3202, 0.3151, 0.3151, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3202, 0.3151, 0.3151, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3202, 0.3151, 0.3151, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3202, 0.83816, 0.83816, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3202, 0.44114, 0.44114, 10);
INSERT INTO building(id, name, level) VALUES (3247, "building_subsistence_farmslevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3247, 11.86937, 11.86937, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3247, 2.37387, 2.37387, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3247, 2.37387, 2.37387, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3247, 2.37387, 2.37387, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3247, 2.37387, 2.37387, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3247, 2.37387, 2.37387, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3247, 3.32342, 3.32342, 7);
INSERT INTO building(id, name, level) VALUES (3261, "building_subsistence_farmslevel", 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3261, 91.4307, 91.4307, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3261, 18.28614, 18.28614, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3261, 18.28614, 18.28614, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3261, 18.28614, 18.28614, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3261, 18.28614, 18.28614, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3261, 18.28614, 18.28614, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3261, 25.60059, 25.60059, 37);
INSERT INTO building(id, name, level) VALUES (3262, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3262, 5.0, 0.6297864391065878, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3262, 10.0, 2.0375799485297996, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3262, 5.0, 0.8154652791259014, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3262, 70.0, 11.498861231654482, 1);
INSERT INTO building(id, name, level) VALUES (3263, "building_subsistence_farmslevel", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3263, 23.5277, 23.5277, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3263, 4.70554, 4.70554, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3263, 4.70554, 4.70554, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3263, 4.70554, 4.70554, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3263, 4.70554, 4.70554, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3263, 4.70554, 4.70554, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3263, 6.58775, 6.58775, 29);
INSERT INTO building(id, name, level) VALUES (3264, "building_urban_centerlevel", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3264, 64.99999999999999, 8.18722370838564, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3264, 129.99999999999997, 26.48853933088739, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3264, 64.99999999999999, 10.601048628636713, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3264, 910.0, 149.48519601150826, 13);
INSERT INTO building(id, name, level) VALUES (3265, "building_subsistence_farmslevel", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3265, 72.5355, 72.5355, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3265, 14.5071, 14.5071, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3265, 14.5071, 14.5071, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3265, 14.5071, 14.5071, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3265, 14.5071, 14.5071, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3265, 14.5071, 14.5071, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3265, 20.30994, 20.30994, 30);
INSERT INTO building(id, name, level) VALUES (3266, "building_subsistence_farmslevel", 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3266, 81.74587, 81.74587, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3266, 16.34917, 16.34917, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3266, 16.34917, 16.34917, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3266, 16.34917, 16.34917, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3266, 16.34917, 16.34917, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3266, 16.34917, 16.34917, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3266, 22.88884, 22.88884, 45);
INSERT INTO building(id, name, level) VALUES (3267, "building_urban_centerlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3267, 59.99999999999999, 7.557437269279053, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3267, 119.99999999999999, 24.450959382357595, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3267, 59.99999999999999, 9.785583349510816, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3267, 839.9999999999999, 137.98633477985376, 12);
INSERT INTO building(id, name, level) VALUES (3268, "building_subsistence_farmslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3268, 32.830499999999994, 36.11355, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3268, 6.5661, 7.22271, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3268, 6.5661, 7.22271, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3268, 6.5661, 7.22271, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3268, 6.5661, 7.22271, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3268, 6.5661, 7.22271, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3268, 9.192536363636362, 10.11179, 20);
INSERT INTO building(id, name, level) VALUES (3269, "building_urban_centerlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3269, 39.99999999999999, 5.038291512852702, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3269, 79.99999999999999, 16.300639588238397, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3269, 39.99999999999999, 6.523722233007209, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3269, 560.0, 91.99088985323586, 8);
INSERT INTO building(id, name, level) VALUES (3270, "building_subsistence_farmslevel", 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3270, 129.9961, 129.9961, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3270, 25.99922, 25.99922, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3270, 25.99922, 25.99922, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3270, 25.99922, 25.99922, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3270, 25.99922, 25.99922, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3270, 25.99922, 25.99922, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3270, 36.3989, 36.3989, 52);
INSERT INTO building(id, name, level) VALUES (3271, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3271, 25.0, 3.148932195532939, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3271, 50.0, 10.187899742648998, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3271, 25.0, 4.077326395629506, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3271, 350.0, 57.4943061582724, 5);
INSERT INTO building(id, name, level) VALUES (3272, "building_subsistence_farmslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3272, 109.9945, 109.9945, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3272, 21.9989, 21.9989, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3272, 21.9989, 21.9989, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3272, 21.9989, 21.9989, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3272, 21.9989, 21.9989, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3272, 21.9989, 21.9989, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3272, 30.79846, 30.79846, 44);
INSERT INTO building(id, name, level) VALUES (3273, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3273, 20.0, 2.5191457564263513, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3273, 40.0, 8.150319794119198, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3273, 20.0, 3.2618611165036056, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3273, 279.99999999999994, 45.99544492661792, 4);
INSERT INTO building(id, name, level) VALUES (3274, "building_subsistence_farmslevel", 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3274, 262.5, 262.5, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3274, 52.5, 52.5, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3274, 52.5, 52.5, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3274, 52.5, 52.5, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3274, 52.5, 52.5, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3274, 52.5, 52.5, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3274, 73.5, 73.5, 105);
INSERT INTO building(id, name, level) VALUES (3275, "building_subsistence_farmslevel", 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3275, 267.5, 294.25, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3275, 53.5, 58.85, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3275, 53.5, 58.85, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3275, 53.5, 58.85, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3275, 53.5, 58.85, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3275, 53.5, 58.85, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3275, 74.89999999999999, 82.39, 107);
INSERT INTO building(id, name, level) VALUES (3276, "building_subsistence_farmslevel", 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3276, 187.5, 187.5, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3276, 37.5, 37.5, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3276, 37.5, 37.5, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3276, 37.5, 37.5, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3276, 37.5, 37.5, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3276, 37.5, 37.5, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3276, 52.5, 52.5, 75);
INSERT INTO building(id, name, level) VALUES (3277, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3277, 10.0, 1.2595728782131757, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3277, 20.0, 4.075159897059599, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3277, 10.0, 1.6309305582518028, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3277, 140.0, 22.997722463308964, 2);
INSERT INTO building(id, name, level) VALUES (3324, "building_subsistence_farmslevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3324, 3.6715, 3.6715, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3324, 0.7343, 0.7343, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3324, 0.7343, 0.7343, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3324, 0.7343, 0.7343, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3324, 0.7343, 0.7343, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3324, 0.7343, 0.7343, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3324, 1.02802, 1.02802, 5);
INSERT INTO building(id, name, level) VALUES (3350, "building_subsistence_fishing_villageslevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3350, 2.2604, 2.2604, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 3350, 9.0416, 9.0416, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3350, 1.1302, 1.1302, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3350, 3.3906, 3.3906, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3350, 2.2604, 2.2604, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3350, 2.2604, 2.2604, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3350, 2.2604, 2.2604, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3350, 3.16456, 3.16456, 8);
INSERT INTO building(id, name, level) VALUES (3351, "building_subsistence_fishing_villageslevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3351, 0.0957, 0.0957, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 3351, 0.3828, 0.3828, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3351, 0.04785, 0.04785, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3351, 0.14355, 0.14355, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3351, 0.0957, 0.0957, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3351, 0.0957, 0.0957, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3351, 0.0957, 0.0957, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3351, 0.13398, 0.13398, 10);
INSERT INTO building(id, name, level) VALUES (3354, "building_subsistence_fishing_villageslevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3354, 3.7634, 3.7634, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 3354, 15.0536, 15.0536, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3354, 1.8817, 1.8817, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3354, 5.6451, 5.6451, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3354, 3.7634, 3.7634, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3354, 3.7634, 3.7634, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3354, 3.7634, 3.7634, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3354, 5.26876, 5.26876, 8);
INSERT INTO building(id, name, level) VALUES (3355, "building_subsistence_farmslevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3355, 27.4439, 27.4439, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3355, 5.48878, 5.48878, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3355, 5.48878, 5.48878, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3355, 5.48878, 5.48878, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3355, 5.48878, 5.48878, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3355, 5.48878, 5.48878, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3355, 7.68429, 7.68429, 11);
INSERT INTO building(id, name, level) VALUES (3451, "building_subsistence_rice_paddieslevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3451, 2.4516, 2.4516, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3451, 0.4086, 0.4086, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3451, 0.4086, 0.4086, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3451, 0.5448, 0.5448, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3451, 0.5448, 0.5448, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3451, 0.5448, 0.5448, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3451, 0.8172, 0.8172, 1);
INSERT INTO building(id, name, level) VALUES (3591, "building_subsistence_orchardslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3591, 3.0, 3.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3591, 1.5, 1.5, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3591, 4.5, 4.5, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3591, 3.0, 3.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3591, 3.0, 3.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3591, 3.0, 3.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3591, 7.98, 7.98, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3591, 4.2, 4.2, 6);
INSERT INTO building(id, name, level) VALUES (3938, "building_subsistence_orchardslevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3938, 1.7933, 1.7933, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3938, 0.89665, 0.89665, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3938, 2.68995, 2.68995, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3938, 1.7933, 1.7933, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3938, 1.7933, 1.7933, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3938, 1.7933, 1.7933, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3938, 4.77017, 4.77017, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3938, 2.51062, 2.51062, 5);
INSERT INTO building(id, name, level) VALUES (3955, "building_subsistence_farmslevel", 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3955, 169.915, 169.915, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3955, 33.983, 33.983, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3955, 33.983, 33.983, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3955, 33.983, 33.983, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3955, 33.983, 33.983, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3955, 33.983, 33.983, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3955, 47.5762, 47.5762, 68);
INSERT INTO building(id, name, level) VALUES (3961, "building_trade_centerlevel", 141);
INSERT INTO building(id, name, level) VALUES (4041, "building_conscription_centerlevel", 11);
INSERT INTO building(id, name, level) VALUES (4042, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4235, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4236, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4237, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4238, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4239, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4240, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4241, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4242, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4243, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4244, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4281, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4645, "building_conscription_centerlevel", 3);
