
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0.15656057085960834);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 27.65804107950114, 224.19941066494198);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 6.095074434917054, 3.690043121355071);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 30.4061192309726, 7.124219285714287);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 18.927151794746425, 42.745315714285724);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 52.5, 11.43634278340708);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 39.23713860297489, 42.81808487626801);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 30.03399562490553, 33.99933160954269);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 52.5, 1.8028688129046537);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 35.288233003553096, 32.99430704106278);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 52.5, 7.400241666666667);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 34.85260790578519, 0.08491639193126083);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 70.0, 0.6153168756945075);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 46.429489061567644, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 1.3650130529753446);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 50.95661381635877, 6.001726962589305);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 52.5, 9.114206707302207);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 34.767167274173026, 79.62647959590359);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 87.5, 5.416779041411771);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 87.5, 2.6801214508145077);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 52.5, 0.667022002783079);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 70.0, 0.9176528030723322);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 6.746909829863262);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 43.69807808383523, 6.942058274998835);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 350.0, 0.03347690231220267);
INSERT INTO building(id, name, level) VALUES (64, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 64, 10.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (65, "building_furniture_manufacturies", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 65, 10.0, 3.062587179351597, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 65, 15.0, 16.15549276090134, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 65, 15.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 65, 20.0, 8.708391452901065, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 65, 25.0, 10.88548931612633, 1);
INSERT INTO building(id, name, level) VALUES (66, "building_fishing_wharf", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 66, 26.509999999999998, 26.7751, 2);
INSERT INTO building(id, name, level) VALUES (67, "building_rye_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 67, 1.1835346534653466, 0.9298838831839565, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 67, 5.917693069306931, 4.649434974012811, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 67, 17.75309900990099, 13.94832048013146, 2);
INSERT INTO building(id, name, level) VALUES (68, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 68, 10.0, 7.8568369794774515, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 68, 120.0, 94.28204375372943, 2);
INSERT INTO building(id, name, level) VALUES (69, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 69, 5.0, 11.333523588829525, 1);
INSERT INTO building(id, name, level) VALUES (70, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 70, 10.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (71, "building_fishing_wharf", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 71, 19.77, 19.77, 1);
INSERT INTO building(id, name, level) VALUES (72, "building_rye_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 72, 1.0, 0.7856836979477452, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 72, 5.0, 3.9284184897387258, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 72, 15.0, 11.785255469216178, 1);
INSERT INTO building(id, name, level) VALUES (73, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 73, 5.0, 3.9284184897387258, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 73, 60.0, 47.14102187686471, 1);
INSERT INTO building(id, name, level) VALUES (74, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 74, 5.0, 11.333523588829525, 1);
INSERT INTO building(id, name, level) VALUES (75, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 75, 10.0, 7.8568369794774515, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 75, 120.0, 94.28204375372943, 2);
INSERT INTO building(id, name, level) VALUES (76, "building_rye_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 76, 0.99977, 0.7855029906972172, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 76, 4.99885, 3.927514953486086, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 76, 14.99655, 11.782544860458257, 1);
INSERT INTO building(id, name, level) VALUES (3580, "building_subsistence_farms", 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3580, 55.00264, 55.00264, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3580, 13.75066, 13.75066, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3580, 13.75066, 13.75066, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3580, 13.75066, 13.75066, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3580, 13.75066, 13.75066, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3580, 13.75066, 13.75066, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3580, 13.75066, 13.75066, 28);
INSERT INTO building(id, name, level) VALUES (3581, "building_subsistence_fishing_villages", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3581, 1.0, 1.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 3581, 4.0, 4.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3581, 0.5, 0.5, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3581, 1.5, 1.5, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3581, 1.0, 1.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3581, 1.0, 1.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3581, 1.0, 1.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3581, 1.0, 1.0, 2);
INSERT INTO building(id, name, level) VALUES (3582, "building_subsistence_farms", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3582, 9.0279, 9.0279, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3582, 2.25697, 2.25697, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3582, 2.25697, 2.25697, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3582, 2.25697, 2.25697, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3582, 2.25697, 2.25697, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3582, 2.25697, 2.25697, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3582, 2.25697, 2.25697, 9);
INSERT INTO building(id, name, level) VALUES (3583, "building_subsistence_farms", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3583, 32.92776, 32.92776, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3583, 8.23194, 8.23194, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3583, 8.23194, 8.23194, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3583, 8.23194, 8.23194, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3583, 8.23194, 8.23194, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3583, 8.23194, 8.23194, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3583, 8.23194, 8.23194, 18);
INSERT INTO building(id, name, level) VALUES (3934, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3934, 2.0, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (3935, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3935, 1.99998, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3935, 1.99998, 0.0, 3);
INSERT INTO building(id, name, level) VALUES (3936, "building_naval_base", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3936, 1.165, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (4380, "building_conscription_center", 2);
INSERT INTO building(id, name, level) VALUES (4714, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4714, 0.42055, 0.20584405493438637, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 4714, 0.8411, 0.41168810986877274, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 4714, 0.8411, 0.41168810986877274, 1);
INSERT INTO building(id, name, level) VALUES (4788, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4788, 20.0, 6.125174358703194, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4788, 40.0, 43.08131402907024, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4788, 50.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4788, 10.0, 7.8568369794774515, 1);
INSERT INTO building(id, name, level) VALUES (4896, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4896, 30.0, 32.31098552180268, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4896, 20.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4896, 60.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (5086, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5086, 20.0, 6.125174358703194, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5086, 40.0, 43.08131402907024, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 5086, 35.0, 22.859527563865296, 1);
INSERT INTO building(id, name, level) VALUES (5112, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5112, 25.0, 7.6564679483789915, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5112, 75.0, 80.7774638045067, 1);
INSERT INTO building(id, name, level) VALUES (5200, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 5200, 1.063, 0.0, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 5200, 1.063, 0.0, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 5200, 1.063, 0.5203001554993525, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5200, 1.063, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (5264, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5264, 0.07, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (5693, "building_conscription_center", 1);
