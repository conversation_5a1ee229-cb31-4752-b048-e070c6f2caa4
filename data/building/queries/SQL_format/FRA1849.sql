
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 72.05065954625938, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 93.24863693631818, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 99.7640257102008, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 101.41924450010515, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 14.384710966176435, 3309.384047806331);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 11.793689574414294, 268.0445650379086);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 24.83546156658404, 261.8474190478729);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 25.781086718500987, 214.87789856805307);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 27.438859593590866, 355.14919201933003);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 27.93869345711018, 1427.8283206989565);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 16.194059369825922, 1085.5716161559965);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 39.886154440968404, 205.14985588816236);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 11.327403098865725, 647.7479221382582);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 12.550352676590755, 76.82043301320405);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 42.15458539939617, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 37.86539973803792, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 62.11923823529412, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 62.904671008188764, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 46.85201237603826, 110.97124658938283);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 42.06984444463092, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 55.29307536381378, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 59.940499208869255, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 79.02651533456734, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 64.90962458582506, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 57.13111027150019, 71.10404855832094);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 36.89334810791959, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 38.42261492988414, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 41.97650567954601, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 48.94409512115359);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 18.72804095430139, 285.73920645456326);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 18.040054418012566, 8.89487371954519);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 17.031605856012735, 671.1855596286567);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 36.91834865503474, 468.6121627103061);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 87.5, 0.008334411476027872);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 26.691789206715576, 12.745452878218046);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 45.843777829033066, 74.40633430724502);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 67.48575016441386, 532.8545640285079);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 45.57863116458612, 298.8603669756624);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 26.992538302612562, 436.0992007996589);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 183.7783117054393, 2.2469257495888906);
INSERT INTO building(id, name, level) VALUES (206, "building_government_administrationlevel", 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 206, 360.0, 201.82152894450556, 18);
INSERT INTO building(id, name, level) VALUES (207, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 207, 4.932, 3.3421002369071684, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 207, 9.864, 6.062357373913753, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 207, 12.33, 11.479293933256693, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 207, 2.466, 2.602856921592245, 3);
INSERT INTO building(id, name, level) VALUES (208, "building_universitylevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 208, 90.0, 50.45538223612639, 9);
INSERT INTO building(id, name, level) VALUES (209, "building_arts_academylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 209, 14.979892156862745, 8.397957607005129, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 209, 4.4939607843137255, 2.5193834347412376, 3);
INSERT INTO building(id, name, level) VALUES (210, "building_paper_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 210, 150.0, 92.18913281499015, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 210, 50.00000000000001, 32.79377198908165, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 210, 350.00000000000006, 222.33219024594095, 5);
INSERT INTO building(id, name, level) VALUES (211, "building_furniture_manufacturieslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 211, 150.0, 101.6453843341596, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 211, 300.0, 184.3782656299803, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 211, 75.0, 18.826625413205807, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 211, 150.0, 50.29750395565374, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 211, 150.0, 158.32463026716815, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 211, 672.6375, 387.2466211315102, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 211, 298.95, 172.1096093917823, 15);
INSERT INTO building(id, name, level) VALUES (212, "building_wheat_farmlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 212, 37.5843937007874, 26.06962440280395, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 212, 263.09079527559055, 182.48739812785493, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 212, 60.13503937007874, 41.711405598460864, 8);
INSERT INTO building(id, name, level) VALUES (213, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 213, 27.7665, 44.38042913640115, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 213, 27.7665, 29.3074723087555, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 213, 111.066, 111.066, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 213, 13.883245901639345, 13.883245901639345, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 213, 69.41624590163934, 69.41624590163934, 3);
INSERT INTO building(id, name, level) VALUES (214, "building_railwaylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 214, 30.0, 7.5306501652823234, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 214, 30.0, 17.315656443621773, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 214, 150.0, 62.11576652226024, 3);
INSERT INTO building(id, name, level) VALUES (215, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 215, 24.0, 9.887577890393997, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 215, 24.0, 6.2673936339636365, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 215, 24.0, 10.393588246765345, 15);
INSERT INTO building(id, name, level) VALUES (218, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 218, 89.99999999999999, 60.98723060049576, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 218, 30.0, 32.298119117647055, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 218, 15.0, 3.940380882352941, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 218, 120.0, 77.61311817538375, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 218, 60.0, 38.80655908769187, 3);
INSERT INTO building(id, name, level) VALUES (219, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 219, 8.502, 14.089333141080019, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 219, 85.02, 85.02, 2);
INSERT INTO building(id, name, level) VALUES (220, "building_silk_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 220, 141.55679439252336, 151.46577, 8);
INSERT INTO building(id, name, level) VALUES (221, "building_wheat_farmlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 221, 46.888000000000005, 32.52287528514962, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 221, 328.216, 227.6601269960473, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 221, 75.02079831932774, 52.03659929047641, 10);
INSERT INTO building(id, name, level) VALUES (222, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 222, 14.4784, 5.964846155345019, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 222, 14.4784, 3.78090966624913, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 222, 14.4784, 6.270105336331974, 10);
INSERT INTO building(id, name, level) VALUES (223, "building_naval_baselevel", 21);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 223, 42.0, 16.86460439991589, 21);
INSERT INTO building(id, name, level) VALUES (224, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 224, 10.0, 16.571786804375463, 2);
INSERT INTO building(id, name, level) VALUES (225, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 225, 80.0, 44.849228654334574, 4);
INSERT INTO building(id, name, level) VALUES (226, "building_shipyardslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 226, 128.30859482758623, 86.94650956417311, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 226, 256.6171982758621, 157.71544649642743, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 226, 128.30859482758623, 43.02401370589927, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 226, 64.15429310344828, 37.02912329209081, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 226, 95.8946379310345, 52.85557466202213, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 226, 351.61367241379315, 193.80377376074782, 7);
INSERT INTO building(id, name, level) VALUES (227, "building_textile_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 227, 240.00000000000003, 162.6326149346554, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 227, 80.0, 86.12831764705882, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 227, 40.0, 10.507682352941176, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 227, 320.0, 206.96831513435666, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 227, 160.0, 103.48415756717833, 8);
INSERT INTO building(id, name, level) VALUES (228, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 228, 10.0, 2.5102167217607745, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 228, 10.0, 5.771885481207258, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 228, 50.0, 20.705255507420084, 1);
INSERT INTO building(id, name, level) VALUES (229, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 229, 15.0, 24.857680206563195, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 229, 150.0, 150.0, 3);
INSERT INTO building(id, name, level) VALUES (230, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 230, 23.98737, 9.882374552529177, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 230, 23.98737, 6.2640954180637625, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 230, 23.98737, 10.388118620950483, 15);
INSERT INTO building(id, name, level) VALUES (231, "building_wheat_farmlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 231, 28.130695652173912, 19.512265534468366, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 231, 196.91489565217393, 136.58587683594743, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 231, 45.009113043478266, 31.219624855149387, 6);
INSERT INTO building(id, name, level) VALUES (232, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 232, 18.50979279279279, 29.58502322474958, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 232, 18.50979279279279, 19.537040668272084, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 232, 74.03919819819819, 74.03919819819819, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 232, 9.25489189189189, 9.25489189189189, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 232, 46.27449549549549, 46.27449549549549, 2);
INSERT INTO building(id, name, level) VALUES (233, "building_naval_baselevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 233, 50.0, 20.076909999899872, 25);
INSERT INTO building(id, name, level) VALUES (234, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 234, 15.0, 24.857680206563195, 3);
INSERT INTO building(id, name, level) VALUES (235, "building_universitylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 235, 20.0, 11.212307163583644, 4);
INSERT INTO building(id, name, level) VALUES (236, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 236, 10.0, 16.571786804375463, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 236, 100.0, 100.0, 2);
INSERT INTO building(id, name, level) VALUES (237, "building_glassworkslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 237, 100.00000000000001, 61.459421876660116, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 237, 50.00000000000001, 24.511541060310368, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 237, 200.00000000000003, 110.48250399728084, 5);
INSERT INTO building(id, name, level) VALUES (238, "building_wheat_farmlevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 238, 42.19784745762713, 29.269649594057643, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 238, 295.3849491525424, 204.88755891482674, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 238, 67.5165593220339, 46.831441701776875, 9);
INSERT INTO building(id, name, level) VALUES (239, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 239, 18.50979279279279, 29.58502322474958, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 239, 18.50979279279279, 19.537040668272084, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 239, 74.03919819819819, 74.03919819819819, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 239, 9.25489189189189, 9.25489189189189, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 239, 46.27449549549549, 46.27449549549549, 2);
INSERT INTO building(id, name, level) VALUES (240, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 240, 32.0, 13.183437187191997, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 240, 32.0, 8.356524845284849, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 240, 32.0, 13.858117662353793, 20);
INSERT INTO building(id, name, level) VALUES (241, "building_construction_sectorlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 241, 8.22, 5.570167061511947, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 241, 16.44, 10.103928956522923, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 241, 20.55, 19.132156555427823, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 241, 4.11, 4.3380948693204076, 5);
INSERT INTO building(id, name, level) VALUES (242, "building_government_administrationlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 242, 159.99999999999997, 89.69845730866912, 8);
INSERT INTO building(id, name, level) VALUES (243, "building_textile_millslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 243, 210.0, 142.30353806782347, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 243, 70.00000000000001, 75.36227794117649, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 243, 35.00000000000001, 9.19422205882353, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 243, 280.00000000000006, 181.09727574256212, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 243, 140.00000000000003, 90.54863787128106, 7);
INSERT INTO building(id, name, level) VALUES (244, "building_paper_millslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 244, 180.0, 110.62695937798819, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 244, 60.00000000000001, 39.35252638689798, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 244, 420.00000000000006, 266.79862829512916, 6);
INSERT INTO building(id, name, level) VALUES (245, "building_arms_industrylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 245, 118.99560000000001, 110.7855206134826, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 245, 59.497800000000005, 19.950605539017968, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 245, 88.93433043478261, 56.309744177057546, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 245, 88.93433043478261, 56.309744177057546, 6);
INSERT INTO building(id, name, level) VALUES (246, "building_logging_camplevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 246, 35.00000000000001, 36.94241372900591, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 246, 280.00000000000006, 280.00000000000006, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 246, 70.00000000000001, 70.00000000000001, 7);
INSERT INTO building(id, name, level) VALUES (247, "building_coal_minelevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 247, 90.0, 94.9947781603009, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 247, 360.0, 360.0, 6);
INSERT INTO building(id, name, level) VALUES (248, "building_silk_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 248, 196.86199999999997, 214.57958, 10);
INSERT INTO building(id, name, level) VALUES (249, "building_wheat_farmlevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 249, 73.98825, 51.32039386018749, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 249, 517.9177500000001, 359.24275702131246, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 249, 118.38119354838709, 82.11262570127434, 15);
INSERT INTO building(id, name, level) VALUES (250, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 250, 32.0, 13.183437187191997, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 250, 32.0, 8.356524845284849, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 250, 32.0, 13.858117662353793, 20);
INSERT INTO building(id, name, level) VALUES (251, "building_railwaylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 251, 30.0, 7.5306501652823234, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 251, 30.0, 17.315656443621773, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 251, 150.0, 62.11576652226024, 3);
INSERT INTO building(id, name, level) VALUES (252, "building_arms_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 252, 39.71519469026549, 36.97505218702945, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 252, 39.71519469026549, 13.317167746887913, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 252, 59.572796460177, 35.887248973536806, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 252, 39.57619469026549, 24.652432357771293, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 252, 98.94048672566373, 61.631080894428244, 4);
INSERT INTO building(id, name, level) VALUES (253, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 253, 9.999999999999998, 10.554975351144543, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 253, 79.99999999999999, 79.99999999999999, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 253, 19.999999999999996, 19.999999999999996, 2);
INSERT INTO building(id, name, level) VALUES (254, "building_munition_plantslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 254, 97.14500000000001, 47.62347312607701, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 254, 97.14500000000001, 123.58783431130594, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 254, 242.8625, 180.96059140759627, 5);
INSERT INTO building(id, name, level) VALUES (255, "building_wheat_farmlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 255, 37.52, 26.024959066260315, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 255, 262.64, 182.17471346382217, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 255, 60.03200000000001, 41.63993450601651, 8);
INSERT INTO building(id, name, level) VALUES (256, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 256, 18.50979279279279, 29.58502322474958, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 256, 18.50979279279279, 19.537040668272084, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 256, 74.03919819819819, 74.03919819819819, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 256, 9.25489189189189, 9.25489189189189, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 256, 46.27449549549549, 46.27449549549549, 2);
INSERT INTO building(id, name, level) VALUES (257, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 257, 40.0, 16.479296483989994, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 257, 40.0, 10.445656056606062, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 257, 40.0, 17.322647077942243, 25);
INSERT INTO building(id, name, level) VALUES (258, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 258, 5.0, 8.285893402187732, 1);
INSERT INTO building(id, name, level) VALUES (259, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 259, 80.0, 44.849228654334574, 4);
INSERT INTO building(id, name, level) VALUES (260, "building_textile_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 260, 150.0, 101.6453843341596, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 260, 50.00000000000001, 53.83019852941177, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 260, 25.000000000000004, 6.5673014705882355, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 260, 200.00000000000003, 129.3551969589729, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 260, 100.00000000000001, 64.67759847948645, 5);
INSERT INTO building(id, name, level) VALUES (261, "building_coal_minelevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 261, 90.0, 94.9947781603009, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 261, 360.0, 360.0, 6);
INSERT INTO building(id, name, level) VALUES (262, "building_wheat_farmlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 262, 35.0, 24.27701405434731, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 262, 7.0, 7.38848274580118, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 262, 245.0, 207.46954919021556, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 262, 56.0, 47.42161124347785, 7);
INSERT INTO building(id, name, level) VALUES (263, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 263, 18.507793388429754, 29.581827488029116, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 263, 18.507793388429754, 19.5349303018952, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 263, 74.03119834710743, 74.03119834710743, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 263, 9.253892561983472, 9.253892561983472, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 263, 46.2694958677686, 46.2694958677686, 2);
INSERT INTO building(id, name, level) VALUES (264, "building_naval_baselevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 264, 50.0, 20.076909999899872, 25);
INSERT INTO building(id, name, level) VALUES (265, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 265, 10.0, 16.571786804375463, 2);
INSERT INTO building(id, name, level) VALUES (266, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 266, 10.0, 2.5102167217607745, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 266, 10.0, 5.771885481207258, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 266, 50.0, 20.705255507420084, 1);
INSERT INTO building(id, name, level) VALUES (267, "building_steel_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 267, 89.99999999999999, 22.59195049584697, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 267, 179.99999999999997, 167.58093333221447, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 267, 269.99999999999994, 159.5736257429313, 3);
INSERT INTO building(id, name, level) VALUES (268, "building_arms_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 268, 29.78879464285714, 27.733522272731506, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 268, 29.78879464285714, 9.98868010922176, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 268, 44.68319642857143, 26.91760485404665, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 268, 29.68453571428571, 18.490812835734143, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 268, 74.21133928571427, 46.22703208933537, 3);
INSERT INTO building(id, name, level) VALUES (269, "building_iron_minelevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 269, 150.0, 37.653250826411615, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 269, 150.0, 158.32463026716815, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 269, 600.0, 375.3065016528232, 10);
INSERT INTO building(id, name, level) VALUES (270, "building_logging_camplevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 270, 35.00000000000001, 36.94241372900591, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 270, 280.00000000000006, 280.00000000000006, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 270, 70.00000000000001, 70.00000000000001, 7);
INSERT INTO building(id, name, level) VALUES (271, "building_wheat_farmlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 271, 32.31409482758621, 22.41399240808063, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 271, 226.19869827586209, 156.89797077480486, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 271, 51.70255172413794, 35.86238785292901, 7);
INSERT INTO building(id, name, level) VALUES (272, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 272, 32.0, 13.183437187191997, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 272, 32.0, 8.356524845284849, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 272, 32.0, 13.858117662353793, 20);
INSERT INTO building(id, name, level) VALUES (273, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 273, 59.99999999999999, 36.87565312599606, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 273, 19.999999999999996, 13.117508795632656, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 273, 140.0, 88.93287609837638, 2);
INSERT INTO building(id, name, level) VALUES (274, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 274, 10.0, 16.571786804375463, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 274, 100.0, 100.0, 2);
INSERT INTO building(id, name, level) VALUES (275, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 275, 5.0, 8.285893402187732, 1);
INSERT INTO building(id, name, level) VALUES (276, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 276, 89.99999999999999, 60.98723060049576, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 276, 30.0, 32.298119117647055, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 276, 15.0, 3.940380882352941, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 276, 120.0, 77.61311817538375, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 276, 60.0, 38.80655908769187, 3);
INSERT INTO building(id, name, level) VALUES (277, "building_wheat_farmlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 277, 37.16919658119659, 25.781631651157355, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 277, 260.1843931623932, 180.471433415007, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 277, 59.470717948717954, 41.25061301323287, 8);
INSERT INTO building(id, name, level) VALUES (278, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 278, 18.539198198198196, 29.6320231891247, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 278, 18.539198198198196, 19.568078001196533, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 278, 74.15679279279279, 74.15679279279279, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 278, 9.269594594594594, 9.269594594594594, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 278, 46.348, 46.348, 2);
INSERT INTO building(id, name, level) VALUES (279, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 279, 32.0, 13.183437187191997, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 279, 32.0, 8.356524845284849, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 279, 32.0, 13.858117662353793, 20);
INSERT INTO building(id, name, level) VALUES (280, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 280, 89.99999999999999, 60.98723060049576, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 280, 30.0, 32.298119117647055, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 280, 15.0, 3.940380882352941, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 280, 120.0, 77.61311817538375, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 280, 60.0, 38.80655908769187, 3);
INSERT INTO building(id, name, level) VALUES (281, "building_furniture_manufacturieslevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 281, 90.00000000000001, 60.98723060049578, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 281, 90.00000000000001, 55.31347968899409, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 281, 180.00000000000003, 60.3570047467845, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 281, 135.00000000000003, 142.49216724045138, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 281, 403.58250000000004, 265.10797734588226, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 281, 358.74, 235.651535418562, 9);
INSERT INTO building(id, name, level) VALUES (282, "building_logging_camplevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 282, 30.000000000000004, 31.664926053433636, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 282, 240.00000000000003, 240.00000000000003, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 282, 60.00000000000001, 60.00000000000001, 6);
INSERT INTO building(id, name, level) VALUES (283, "building_wheat_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 283, 23.449, 16.264905787439716, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 283, 164.143, 113.854340512078, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 283, 37.518394736842104, 26.023845609224736, 5);
INSERT INTO building(id, name, level) VALUES (284, "building_barrackslevel", 30);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 284, 48.0, 19.775155780787994, 30);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 284, 48.0, 12.534787267927273, 30);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 284, 48.0, 20.78717649353069, 30);
INSERT INTO building(id, name, level) VALUES (285, "building_government_administrationlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 285, 140.0, 78.4861501450855, 7);
INSERT INTO building(id, name, level) VALUES (286, "building_shipyardslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 286, 80.99100000000001, 54.88240881738615, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 286, 161.98200000000003, 99.55320074425158, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 286, 80.99100000000001, 27.157634285815686, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 286, 40.49550000000001, 23.373538850422854, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 286, 60.5306403508772, 33.36351071794787, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 286, 221.94571052631582, 122.33288874896203, 5);
INSERT INTO building(id, name, level) VALUES (287, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 287, 25.000000000000004, 26.387438377861365, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 287, 200.00000000000003, 200.00000000000003, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 287, 50.00000000000001, 50.00000000000001, 5);
INSERT INTO building(id, name, level) VALUES (288, "building_arms_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 288, 19.849594594594592, 18.480075491268007, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 288, 19.849594594594592, 6.655900417598297, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 288, 29.774396396396394, 17.936394462001388, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 288, 19.780126126126124, 12.321250821834328, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 288, 49.450315315315315, 30.803127054585822, 2);
INSERT INTO building(id, name, level) VALUES (289, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 289, 15.0, 24.857680206563195, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 289, 150.0, 150.0, 3);
INSERT INTO building(id, name, level) VALUES (290, "building_naval_baselevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 290, 22.0, 8.833840399955943, 11);
INSERT INTO building(id, name, level) VALUES (291, "building_wheat_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 291, 18.76919469026549, 13.01885723669589, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 291, 131.38439823008852, 91.13202521010922, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 291, 30.030716814159298, 20.83017526169912, 4);
INSERT INTO building(id, name, level) VALUES (292, "building_livestock_ranchlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 292, 46.270500000000006, 73.95619348336483, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 292, 46.270500000000006, 48.838398698513366, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 292, 185.08200000000002, 185.08200000000002, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 292, 23.13524561403509, 23.13524561403509, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 292, 115.67624561403511, 115.67624561403511, 5);
INSERT INTO building(id, name, level) VALUES (293, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 293, 15.0, 24.857680206563195, 3);
INSERT INTO building(id, name, level) VALUES (294, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 294, 80.0, 44.849228654334574, 4);
INSERT INTO building(id, name, level) VALUES (295, "building_tooling_workshopslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 295, 210.0, 129.0647859409862, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 295, 140.00000000000003, 84.33740154625312, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 295, 560.0000000000001, 340.76118434715454, 7);
INSERT INTO building(id, name, level) VALUES (296, "building_furniture_manufacturieslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 296, 60.00000000000001, 40.65815373366385, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 296, 60.00000000000001, 36.87565312599607, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 296, 120.00000000000001, 40.23800316452299, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 296, 90.0, 94.9947781603009, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 296, 269.055, 176.7386515639215, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 296, 239.16000000000003, 157.1010236123747, 6);
INSERT INTO building(id, name, level) VALUES (297, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 297, 20.000000000000004, 21.10995070228909, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 297, 160.00000000000003, 160.00000000000003, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 297, 40.00000000000001, 40.00000000000001, 4);
INSERT INTO building(id, name, level) VALUES (298, "building_iron_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 298, 75.0, 18.826625413205807, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 298, 75.0, 79.16231513358407, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 298, 300.0, 187.6532508264116, 5);
INSERT INTO building(id, name, level) VALUES (299, "building_wheat_farmlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 299, 60.0, 41.6177383788811, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 299, 12.0, 12.665970421373453, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 299, 420.0, 355.66208432608386, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 299, 96.0, 81.29419070310487, 12);
INSERT INTO building(id, name, level) VALUES (300, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 300, 24.0, 9.887577890393997, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 300, 24.0, 6.2673936339636365, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 300, 24.0, 10.393588246765345, 15);
INSERT INTO building(id, name, level) VALUES (301, "building_food_industrylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 301, 240.00000000000003, 383.60265041457427, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 301, 240.00000000000003, 70.99970315698066, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 301, 210.00000000000003, 136.06237013117905, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 301, 360.0, 233.24977736773548, 6);
INSERT INTO building(id, name, level) VALUES (302, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 302, 24.0, 9.887577890393997, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 302, 24.0, 6.2673936339636365, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 302, 24.0, 10.393588246765345, 15);
INSERT INTO building(id, name, level) VALUES (303, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 303, 25.000000000000004, 26.387438377861365, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 303, 200.00000000000003, 200.00000000000003, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 303, 50.00000000000001, 50.00000000000001, 5);
INSERT INTO building(id, name, level) VALUES (304, "building_wheat_farmlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 304, 46.537496124031, 32.279755641635255, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 304, 325.762496124031, 225.95830562235315, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 304, 74.46, 51.64761332819144, 10);
INSERT INTO building(id, name, level) VALUES (305, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 305, 120.0, 67.27384298150186, 6);
INSERT INTO building(id, name, level) VALUES (306, "building_furniture_manufacturieslevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 306, 90.00000000000001, 60.98723060049578, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 306, 90.00000000000001, 55.31347968899409, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 306, 180.00000000000003, 60.3570047467845, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 306, 135.00000000000003, 142.49216724045138, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 306, 403.58250000000004, 265.10797734588226, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 306, 358.74, 235.651535418562, 9);
INSERT INTO building(id, name, level) VALUES (307, "building_iron_minelevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 307, 120.0, 30.122600661129294, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 307, 120.0, 126.65970421373453, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 307, 480.0, 300.2452013222586, 8);
INSERT INTO building(id, name, level) VALUES (308, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 308, 23.85264, 9.826868162146978, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 308, 23.85264, 6.2289118370511, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 308, 23.85264, 10.329771614930205, 15);
INSERT INTO building(id, name, level) VALUES (309, "building_tooling_workshopslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 309, 180.0, 110.62695937798819, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 309, 120.00000000000001, 72.28920132535981, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 309, 480.00000000000006, 292.08101515470395, 6);
INSERT INTO building(id, name, level) VALUES (310, "building_lead_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 310, 50.0, 12.551083608803873, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 310, 50.0, 52.774876755722715, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 310, 200.0, 125.10216721760774, 5);
INSERT INTO building(id, name, level) VALUES (311, "building_sulfur_minelevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 311, 90.0, 22.59195049584697, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 311, 90.0, 94.9947781603009, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 311, 360.0, 225.18390099169395, 6);
INSERT INTO building(id, name, level) VALUES (312, "building_logging_camplevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 312, 30.000000000000004, 31.664926053433636, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 312, 240.00000000000003, 240.00000000000003, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 312, 60.00000000000001, 60.00000000000001, 6);
INSERT INTO building(id, name, level) VALUES (313, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 313, 10.0, 2.5102167217607745, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 313, 10.0, 5.771885481207258, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 313, 50.0, 20.705255507420084, 1);
INSERT INTO building(id, name, level) VALUES (314, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 314, 31.8528, 13.122793376130913, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 314, 31.8528, 8.318084830996538, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 314, 31.8528, 13.794370321106964, 20);
INSERT INTO building(id, name, level) VALUES (315, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 315, 80.0, 44.849228654334574, 4);
INSERT INTO building(id, name, level) VALUES (316, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 316, 160.00000000000003, 255.7351002763829, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 316, 160.00000000000003, 47.33313543798711, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 316, 140.0, 90.70824675411934, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 316, 240.0, 155.4998515784903, 4);
INSERT INTO building(id, name, level) VALUES (317, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 317, 15.0, 15.832463026716816, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 317, 120.0, 120.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 317, 30.0, 30.0, 3);
INSERT INTO building(id, name, level) VALUES (318, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 318, 10.0, 16.571786804375463, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 318, 100.0, 100.0, 2);
INSERT INTO building(id, name, level) VALUES (319, "building_wheat_farmlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 319, 50.0, 34.68144864906758, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 319, 10.0, 10.554975351144543, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 319, 350.0, 296.38507027173654, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 319, 80.0, 67.74515891925407, 10);
INSERT INTO building(id, name, level) VALUES (320, "building_naval_baselevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 320, 50.0, 20.076909999899872, 25);
INSERT INTO building(id, name, level) VALUES (321, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 321, 15.0, 24.857680206563195, 3);
INSERT INTO building(id, name, level) VALUES (322, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 322, 80.0, 44.849228654334574, 4);
INSERT INTO building(id, name, level) VALUES (323, "building_glassworkslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 323, 80.00000000000001, 49.16753750132809, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 323, 40.00000000000001, 19.609232848248297, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 323, 160.00000000000003, 88.38600319782468, 4);
INSERT INTO building(id, name, level) VALUES (324, "building_wheat_farmlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 324, 37.5103937007874, 26.018295858803327, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 324, 262.5727952755905, 182.12809831985058, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 324, 60.01663779527559, 41.62927883573079, 8);
INSERT INTO building(id, name, level) VALUES (325, "building_livestock_ranchlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 325, 37.01959349593496, 59.17005909296161, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 325, 37.01959349593496, 39.07408968589844, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 325, 148.07839837398373, 148.07839837398373, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 325, 18.50979674796748, 18.50979674796748, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 325, 92.54899999999999, 92.54899999999999, 4);
INSERT INTO building(id, name, level) VALUES (326, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 326, 8.0, 3.295859296797999, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 326, 8.0, 2.089131211321212, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 326, 8.0, 3.4645294155884483, 5);
INSERT INTO building(id, name, level) VALUES (16777777, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16777777, 29.999999999999996, 20.32907686683192, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 16777777, 10.0, 10.766039705882353, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 16777777, 5.0, 1.313460294117647, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16777777, 40.0, 25.871039391794582, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 16777777, 20.0, 12.935519695897291, 1);
INSERT INTO building(id, name, level) VALUES (134218308, "building_railwaylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 134218308, 10.0, 6.1459421876660105, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 134218308, 20.0, 5.020433443521549, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 134218308, 20.0, 11.543770962414516, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 134218308, 130.0, 62.52152569274753, 2);
INSERT INTO building(id, name, level) VALUES (622, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 622, 8.225999999999999, 8.682522723851502, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 622, 24.678, 24.678, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 622, 20.564999999999998, 20.564999999999998, 1);
INSERT INTO building(id, name, level) VALUES (623, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 623, 3.636, 6.025501682070918, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 623, 19.6344, 19.6344, 1);
INSERT INTO building(id, name, level) VALUES (624, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 624, 5.0, 2.0599120604987493, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 624, 5.0, 1.3057070070757577, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 624, 5.0, 2.1653308847427803, 5);
INSERT INTO building(id, name, level) VALUES (625, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 625, 5.0, 8.285893402187732, 1);
INSERT INTO building(id, name, level) VALUES (16778133, "building_tooling_workshopslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16778133, 150.0, 92.18913281499015, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16778133, 100.00000000000001, 60.241001104466505, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16778133, 400.00000000000006, 243.4008459622532, 5);
INSERT INTO building(id, name, level) VALUES (1182, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1182, 3.63, 6.015558609988293, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1182, 19.602, 19.602, 1);
INSERT INTO building(id, name, level) VALUES (1183, "building_lead_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1183, 1.27, 1.340481869595357, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 1183, 5.08, 5.08, 2);
INSERT INTO building(id, name, level) VALUES (1184, "building_wheat_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1184, 9.375896825396826, 6.503393685779116, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1184, 65.63129365079365, 45.523766810437515, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1184, 15.001436507936509, 10.405430998244956, 2);
INSERT INTO building(id, name, level) VALUES (1185, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1185, 8.254895999999999, 13.194166602069446, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1185, 8.254895999999999, 8.713022380626168, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1185, 33.019600000000004, 33.019600000000004, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1185, 4.127447999999999, 4.127447999999999, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1185, 20.637248, 20.637248, 1);
INSERT INTO building(id, name, level) VALUES (1186, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1186, 15.9968, 6.590400249877279, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1186, 15.9968, 4.177426770157895, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1186, 15.9968, 6.927673019410661, 10);
INSERT INTO building(id, name, level) VALUES (1187, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1187, 5.0, 8.285893402187732, 1);
INSERT INTO building(id, name, level) VALUES (1222, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1222, 5.0, 8.285893402187732, 1);
INSERT INTO building(id, name, level) VALUES (1223, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1223, 59.05859405940594, 59.64918, 2);
INSERT INTO building(id, name, level) VALUES (1224, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1224, 5.0, 8.285893402187732, 1);
INSERT INTO building(id, name, level) VALUES (16778858, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16778858, 4.932, 3.3421002369071684, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16778858, 9.864, 6.062357373913753, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16778858, 12.33, 11.479293933256693, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16778858, 2.466, 2.602856921592245, 3);
INSERT INTO building(id, name, level) VALUES (33556105, "building_munition_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 33556105, 19.407799999999998, 9.514301731805828, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 33556105, 19.407799999999998, 24.690596229831314, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 33556105, 48.5195, 36.152627164757284, 1);
INSERT INTO building(id, name, level) VALUES (1682, "building_sugar_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1682, 79.58789215686274, 81.17965, 3);
INSERT INTO building(id, name, level) VALUES (1683, "building_coffee_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1683, 33.34479207920792, 33.67824, 2);
INSERT INTO building(id, name, level) VALUES (1684, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1684, 44.215495049504945, 44.65765, 2);
INSERT INTO building(id, name, level) VALUES (1685, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1685, 5.0, 8.285893402187732, 1);
INSERT INTO building(id, name, level) VALUES (1712, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1712, 5.0, 8.285893402187732, 1);
INSERT INTO building(id, name, level) VALUES (218105762, "building_iron_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 218105762, 59.99999999999999, 15.061300330564645, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 218105762, 59.99999999999999, 63.329852106867264, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 218105762, 239.99999999999997, 150.12260066112927, 4);
INSERT INTO building(id, name, level) VALUES (2262, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2262, 59.05859405940594, 59.64918, 2);
INSERT INTO building(id, name, level) VALUES (2263, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2263, 5.0, 8.285893402187732, 1);
INSERT INTO building(id, name, level) VALUES (2287, "building_dye_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2287, 44.215495049504945, 44.65765, 2);
INSERT INTO building(id, name, level) VALUES (2288, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2288, 5.0, 8.285893402187732, 1);
INSERT INTO building(id, name, level) VALUES (2809, "building_subsistence_farmslevel", 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2809, 64.61777272727272, 71.07955, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2809, 12.923554545454543, 14.21591, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2809, 12.923554545454543, 14.21591, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2809, 12.923554545454543, 14.21591, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2809, 12.923554545454543, 14.21591, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2809, 12.923554545454543, 14.21591, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2809, 18.092972727272727, 19.90227, 73);
INSERT INTO building(id, name, level) VALUES (2810, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2810, 30.0, 18.437826562998033, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2810, 60.0, 15.061300330564647, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2810, 30.0, 12.868889728499811, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2810, 420.0, 181.24104346497413, 6);
INSERT INTO building(id, name, level) VALUES (2811, "building_subsistence_farmslevel", 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2811, 188.076, 206.8836, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2811, 37.615199999999994, 41.37672, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2811, 37.615199999999994, 41.37672, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2811, 37.615199999999994, 41.37672, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2811, 37.615199999999994, 41.37672, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2811, 37.615199999999994, 41.37672, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2811, 52.661272727272724, 57.9274, 140);
INSERT INTO building(id, name, level) VALUES (2812, "building_urban_centerlevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2812, 54.99999999999999, 33.802682032163055, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2812, 109.99999999999999, 27.612383939368513, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2812, 54.99999999999999, 23.59296450224965, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2812, 769.9999999999999, 332.2752463524525, 11);
INSERT INTO building(id, name, level) VALUES (16780214, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16780214, 59.99999999999999, 36.87565312599606, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16780214, 39.99999999999999, 24.096400441786596, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16780214, 159.99999999999997, 97.36033838490127, 2);
INSERT INTO building(id, name, level) VALUES (3060, "building_subsistence_farmslevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3060, 2.177872727272727, 2.39566, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3060, 0.4355727272727272, 0.47913, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3060, 0.4355727272727272, 0.47913, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3060, 0.4355727272727272, 0.47913, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3060, 0.4355727272727272, 0.47913, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3060, 0.4355727272727272, 0.47913, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3060, 0.6098, 0.67078, 7);
INSERT INTO building(id, name, level) VALUES (3143, "building_subsistence_farmslevel", 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3143, 169.859024, 212.32378, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3143, 33.9718, 42.46475, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3143, 33.9718, 42.46475, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3143, 33.9718, 42.46475, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3143, 33.9718, 42.46475, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3143, 33.9718, 42.46475, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3143, 47.560520000000004, 59.45065, 69);
INSERT INTO building(id, name, level) VALUES (3168, "building_subsistence_farmslevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3168, 18.121399999999998, 19.93354, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3168, 3.624272727272727, 3.9867, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3168, 3.624272727272727, 3.9867, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3168, 3.624272727272727, 3.9867, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3168, 3.624272727272727, 3.9867, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3168, 3.624272727272727, 3.9867, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3168, 5.0739909090909086, 5.58139, 8);
INSERT INTO building(id, name, level) VALUES (3199, "building_subsistence_farmslevel", 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3199, 135.85181818181817, 149.437, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3199, 27.170363636363632, 29.8874, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3199, 27.170363636363632, 29.8874, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3199, 27.170363636363632, 29.8874, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3199, 27.170363636363632, 29.8874, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3199, 27.170363636363632, 29.8874, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3199, 38.03850909090909, 41.84236, 89);
INSERT INTO building(id, name, level) VALUES (3200, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3200, 25.0, 15.364855469165025, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3200, 50.0, 12.551083608803873, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3200, 25.0, 10.724074773749843, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3200, 350.0, 151.03420288747844, 5);
INSERT INTO building(id, name, level) VALUES (3201, "building_subsistence_farmslevel", 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3201, 134.23507272727272, 147.65858, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3201, 26.84700909090909, 29.53171, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3201, 26.84700909090909, 29.53171, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3201, 26.84700909090909, 29.53171, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3201, 26.84700909090909, 29.53171, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3201, 26.84700909090909, 29.53171, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3201, 37.585818181818176, 41.3444, 111);
INSERT INTO building(id, name, level) VALUES (3202, "building_urban_centerlevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3202, 45.0, 27.656739844497046, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3202, 90.0, 22.59195049584697, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3202, 45.0, 19.303334592749714, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3202, 629.9999999999999, 271.8615651974611, 9);
INSERT INTO building(id, name, level) VALUES (3203, "building_subsistence_farmslevel", 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3203, 83.61959999999999, 91.98156, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3203, 16.72391818181818, 18.39631, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3203, 16.72391818181818, 18.39631, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3203, 16.72391818181818, 18.39631, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3203, 16.72391818181818, 18.39631, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3203, 16.72391818181818, 18.39631, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3203, 23.413481818181815, 25.75483, 102);
INSERT INTO building(id, name, level) VALUES (3204, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3204, 30.0, 18.437826562998033, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3204, 60.0, 15.061300330564647, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3204, 30.0, 12.868889728499811, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3204, 420.0, 181.24104346497413, 6);
INSERT INTO building(id, name, level) VALUES (3205, "building_subsistence_farmslevel", 151);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3205, 250.14660000000003, 300.17592, 151);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3205, 50.029316666666666, 60.03518, 151);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3205, 50.029316666666666, 60.03518, 151);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3205, 50.029316666666666, 60.03518, 151);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3205, 50.029316666666666, 60.03518, 151);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3205, 50.029316666666666, 60.03518, 151);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3205, 70.04104166666667, 84.04925, 151);
INSERT INTO building(id, name, level) VALUES (3206, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3206, 25.0, 15.364855469165025, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3206, 50.0, 12.551083608803873, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3206, 25.0, 10.724074773749843, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3206, 350.0, 151.03420288747844, 5);
INSERT INTO building(id, name, level) VALUES (3207, "building_subsistence_farmslevel", 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3207, 86.19521818181818, 94.81474, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3207, 17.239036363636362, 18.96294, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3207, 17.239036363636362, 18.96294, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3207, 17.239036363636362, 18.96294, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3207, 17.239036363636362, 18.96294, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3207, 17.239036363636362, 18.96294, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3207, 24.134654545454545, 26.54812, 53);
INSERT INTO building(id, name, level) VALUES (3208, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3208, 20.0, 12.291884375332021, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3208, 40.0, 10.040866887043098, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3208, 20.0, 8.579259818999875, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3208, 279.99999999999994, 120.82736230998272, 4);
INSERT INTO building(id, name, level) VALUES (3209, "building_subsistence_farmslevel", 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3209, 126.20789999999998, 138.82869, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3209, 25.241572727272725, 27.76573, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3209, 25.241572727272725, 27.76573, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3209, 25.241572727272725, 27.76573, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3209, 25.241572727272725, 27.76573, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3209, 25.241572727272725, 27.76573, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3209, 35.33820909090909, 38.87203, 92);
INSERT INTO building(id, name, level) VALUES (3210, "building_subsistence_farmslevel", 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3210, 163.55949999999999, 179.91545, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3210, 32.71189999999999, 35.98309, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3210, 32.71189999999999, 35.98309, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3210, 32.71189999999999, 35.98309, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3210, 32.71189999999999, 35.98309, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3210, 32.71189999999999, 35.98309, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3210, 45.796654545454544, 50.37632, 130);
INSERT INTO building(id, name, level) VALUES (3211, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3211, 15.0, 9.218913281499017, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3211, 30.0, 7.5306501652823234, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3211, 15.0, 6.4344448642499055, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3211, 209.99999999999997, 90.62052173248705, 3);
INSERT INTO building(id, name, level) VALUES (3212, "building_subsistence_farmslevel", 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3212, 125.83514545454544, 138.41866, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3212, 25.16702727272727, 27.68373, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3212, 25.16702727272727, 27.68373, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3212, 25.16702727272727, 27.68373, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3212, 25.16702727272727, 27.68373, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3212, 25.16702727272727, 27.68373, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3212, 35.23383636363636, 38.75722, 111);
INSERT INTO building(id, name, level) VALUES (3213, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3213, 24.98525, 15.355790204438218, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3213, 49.9705, 12.54367846947468, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3213, 24.98525, 10.71774756963333, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3213, 349.7935, 150.94509270777482, 5);
INSERT INTO building(id, name, level) VALUES (3214, "building_subsistence_farmslevel", 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3214, 267.10484545454545, 293.81533, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3214, 53.42096363636364, 58.76306, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3214, 53.42096363636364, 58.76306, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3214, 53.42096363636364, 58.76306, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3214, 53.42096363636364, 58.76306, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3214, 53.42096363636364, 58.76306, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3214, 74.78935454545453, 82.26829, 149);
INSERT INTO building(id, name, level) VALUES (3215, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3215, 29.997895238095236, 18.436532988499483, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3215, 59.995799999999996, 15.060246039541505, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3215, 29.997895238095236, 12.867986863537906, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3215, 419.9706, 181.22835659193157, 6);
INSERT INTO building(id, name, level) VALUES (3216, "building_subsistence_farmslevel", 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3216, 222.21934545454545, 244.44128, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3216, 44.44386363636363, 48.88825, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3216, 44.44386363636363, 48.88825, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3216, 44.44386363636363, 48.88825, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3216, 44.44386363636363, 48.88825, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3216, 44.44386363636363, 48.88825, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3216, 62.221409090909084, 68.44355, 142);
INSERT INTO building(id, name, level) VALUES (3217, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3217, 25.0, 15.364855469165025, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3217, 50.0, 12.551083608803873, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3217, 25.0, 10.724074773749843, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3217, 350.0, 151.03420288747844, 5);
INSERT INTO building(id, name, level) VALUES (3218, "building_subsistence_farmslevel", 94);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3218, 182.41405, 218.89686, 94);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3218, 36.48280833333334, 43.77937, 94);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3218, 36.48280833333334, 43.77937, 94);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3218, 36.48280833333334, 43.77937, 94);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3218, 36.48280833333334, 43.77937, 94);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3218, 36.48280833333334, 43.77937, 94);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3218, 51.07593333333333, 61.29112, 94);
INSERT INTO building(id, name, level) VALUES (3219, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3219, 14.990843137254902, 9.21328552659384, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3219, 29.981696078431373, 7.526055484282787, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3219, 14.990843137254902, 6.43051690901905, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3219, 209.87189215686274, 90.5652398297103, 3);
INSERT INTO building(id, name, level) VALUES (3220, "building_subsistence_farmslevel", 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3220, 215.00359999999998, 236.50396, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3220, 43.00071818181818, 47.30079, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3220, 43.00071818181818, 47.30079, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3220, 43.00071818181818, 47.30079, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3220, 43.00071818181818, 47.30079, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3220, 43.00071818181818, 47.30079, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3220, 60.201, 66.2211, 124);
INSERT INTO building(id, name, level) VALUES (3221, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3221, 24.998, 15.363626280727495, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3221, 49.996, 12.550079522115169, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3221, 24.998, 10.723216847767944, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3221, 349.97200000000004, 151.02212015124746, 5);
INSERT INTO building(id, name, level) VALUES (3222, "building_subsistence_farmslevel", 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3222, 124.28504545454544, 136.71355, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3222, 24.857009090909088, 27.34271, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3222, 24.857009090909088, 27.34271, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3222, 24.857009090909088, 27.34271, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3222, 24.857009090909088, 27.34271, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3222, 24.857009090909088, 27.34271, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3222, 34.799809090909086, 38.27979, 86);
INSERT INTO building(id, name, level) VALUES (3223, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3223, 20.0, 12.291884375332021, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3223, 40.0, 10.040866887043098, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3223, 20.0, 8.579259818999875, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3223, 279.99999999999994, 120.82736230998272, 4);
INSERT INTO building(id, name, level) VALUES (3224, "building_subsistence_farmslevel", 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3224, 147.2891, 162.01801, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3224, 29.457818181818176, 32.4036, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3224, 29.457818181818176, 32.4036, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3224, 29.457818181818176, 32.4036, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3224, 29.457818181818176, 32.4036, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3224, 29.457818181818176, 32.4036, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3224, 41.240945454545454, 45.36504, 98);
INSERT INTO building(id, name, level) VALUES (3225, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3225, 35.0, 21.510797656831034, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3225, 70.0, 17.571517052325422, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3225, 35.0, 15.013704683249781, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3225, 489.99999999999994, 211.44788404246978, 7);
INSERT INTO building(id, name, level) VALUES (3226, "building_subsistence_farmslevel", 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3226, 171.10750000000002, 205.329, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3226, 34.221500000000006, 41.0658, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3226, 34.221500000000006, 41.0658, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3226, 34.221500000000006, 41.0658, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3226, 34.221500000000006, 41.0658, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3226, 34.221500000000006, 41.0658, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3226, 47.9101, 57.49212, 100);
INSERT INTO building(id, name, level) VALUES (3227, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3227, 15.0, 9.218913281499017, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3227, 30.0, 7.5306501652823234, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3227, 15.0, 6.4344448642499055, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3227, 209.99999999999997, 90.62052173248705, 3);
INSERT INTO building(id, name, level) VALUES (3228, "building_subsistence_farmslevel", 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3228, 29.869125000000004, 35.84295, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3228, 5.973825000000001, 7.16859, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3228, 5.973825000000001, 7.16859, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3228, 5.973825000000001, 7.16859, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3228, 5.973825000000001, 7.16859, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3228, 5.973825000000001, 7.16859, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3228, 8.36335, 10.03602, 55);
INSERT INTO building(id, name, level) VALUES (3229, "building_urban_centerlevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3229, 54.99999999999999, 33.802682032163055, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3229, 109.99999999999999, 27.612383939368513, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3229, 54.99999999999999, 23.59296450224965, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3229, 769.9999999999999, 332.2752463524525, 11);
INSERT INTO building(id, name, level) VALUES (3271, "building_subsistence_farmslevel", 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3271, 31.377772727272724, 34.51555, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3271, 6.275554545454545, 6.90311, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3271, 6.275554545454545, 6.90311, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3271, 6.275554545454545, 6.90311, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3271, 6.275554545454545, 6.90311, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3271, 6.275554545454545, 6.90311, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3271, 8.785772727272727, 9.66435, 13);
INSERT INTO building(id, name, level) VALUES (3298, "building_subsistence_farmslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3298, 17.4196, 22.64548, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3298, 3.4839153846153845, 4.52909, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3298, 3.4839153846153845, 4.52909, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3298, 3.4839153846153845, 4.52909, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3298, 3.4839153846153845, 4.52909, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3298, 3.4839153846153845, 4.52909, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3298, 4.877484615384615, 6.34073, 44);
INSERT INTO building(id, name, level) VALUES (3491, "building_subsistence_farmslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3491, 35.248999999999995, 38.7739, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3491, 7.049799999999999, 7.75478, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3491, 7.049799999999999, 7.75478, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3491, 7.049799999999999, 7.75478, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3491, 7.049799999999999, 7.75478, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3491, 7.049799999999999, 7.75478, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3491, 9.86971818181818, 10.85669, 20);
INSERT INTO building(id, name, level) VALUES (3498, "building_subsistence_orchardslevel", 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3498, 6.48940909090909, 7.13835, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3498, 3.2447, 3.56917, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3498, 9.73410909090909, 10.70752, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3498, 6.48940909090909, 7.13835, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3498, 6.48940909090909, 7.13835, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3498, 6.48940909090909, 7.13835, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3498, 17.26182727272727, 18.98801, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3498, 9.085172727272727, 9.99369, 34);
INSERT INTO building(id, name, level) VALUES (67112363, "building_subsistence_pastureslevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 67112363, 0.9947777777777778, 0.8953, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 67112363, 1.4921666666666666, 1.34295, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 67112363, 0.4973888888888889, 0.44765, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 67112363, 0.9947777777777778, 0.8953, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 67112363, 0.9947777777777778, 0.8953, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 67112363, 0.9947777777777778, 0.8953, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 67112363, 2.646122222222222, 2.38151, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 67112363, 1.3926888888888889, 1.25342, 11);
INSERT INTO building(id, name, level) VALUES (3842, "building_subsistence_pastureslevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3842, 6.748045454545454, 7.42285, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3842, 10.122072727272727, 11.13428, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3842, 3.3740181818181814, 3.71142, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3842, 6.748045454545454, 7.42285, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3842, 6.748045454545454, 7.42285, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3842, 6.748045454545454, 7.42285, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3842, 17.94980909090909, 19.74479, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3842, 9.447263636363635, 10.39199, 15);
INSERT INTO building(id, name, level) VALUES (3862, "building_trade_centerlevel", 29);
INSERT INTO building(id, name, level) VALUES (3863, "building_trade_centerlevel", 63);
INSERT INTO building(id, name, level) VALUES (3898, "building_trade_centerlevel", 33);
INSERT INTO building(id, name, level) VALUES (3899, "building_trade_centerlevel", 44);
INSERT INTO building(id, name, level) VALUES (33558337, "building_naval_baselevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 33558337, 0.24, 0.09636916799951938, 1);
INSERT INTO building(id, name, level) VALUES (3910, "building_trade_centerlevel", 40);
INSERT INTO building(id, name, level) VALUES (16781139, "building_sulfur_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16781139, 45.0, 11.295975247923485, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16781139, 45.0, 47.49738908015045, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 16781139, 180.0, 112.59195049584697, 3);
INSERT INTO building(id, name, level) VALUES (3936, "building_conscription_centerlevel", 13);
INSERT INTO building(id, name, level) VALUES (3937, "building_conscription_centerlevel", 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3937, 19.475, 8.02335747564263, 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3937, 19.475, 5.085728792560076, 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3937, 19.475, 8.43396379607313, 28);
INSERT INTO building(id, name, level) VALUES (16781206, "building_conscription_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 16781206, 2.0, 0.8239648241994998, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16781206, 2.0, 0.522282802830303, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16781206, 2.0, 0.8661323538971121, 2);
INSERT INTO building(id, name, level) VALUES (33558522, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4106, "building_conscription_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 4106, 9.1551, 3.7717401810144198, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4106, 9.1551, 2.390775644095853, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4106, 9.1551, 3.964764156581725, 7);
INSERT INTO building(id, name, level) VALUES (4140, "building_conscription_centerlevel", 18);
INSERT INTO building(id, name, level) VALUES (4141, "building_conscription_centerlevel", 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 4141, 18.75507, 7.726758977699656, 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4141, 18.75507, 4.897725263439265, 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4141, 18.75507, 8.122186463302555, 28);
INSERT INTO building(id, name, level) VALUES (4142, "building_conscription_centerlevel", 15);
INSERT INTO building(id, name, level) VALUES (4143, "building_conscription_centerlevel", 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 4143, 19.61254, 8.080021536602828, 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4143, 19.61254, 5.121646180910715, 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4143, 19.61254, 8.493527718050633, 28);
INSERT INTO building(id, name, level) VALUES (4144, "building_conscription_centerlevel", 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 4144, 9.54867, 3.9338840989445187, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4144, 9.54867, 2.4935530654508145, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4144, 9.54867, 4.135206011843368, 14);
INSERT INTO building(id, name, level) VALUES (4145, "building_conscription_centerlevel", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 4145, 17.14876, 7.064987509319706, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4145, 17.14876, 4.478251218932093, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4145, 17.14876, 7.426547932608319, 13);
INSERT INTO building(id, name, level) VALUES (4146, "building_conscription_centerlevel", 18);
INSERT INTO building(id, name, level) VALUES (4147, "building_conscription_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (4148, "building_conscription_centerlevel", 31);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 4148, 44.69555, 18.413780499124975, 31);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4148, 44.69555, 11.671858564020974, 31);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4148, 44.69555, 19.35613096511303, 31);
INSERT INTO building(id, name, level) VALUES (4149, "building_conscription_centerlevel", 26);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 4149, 19.0399, 7.844103928138027, 26);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4149, 19.0399, 4.972106168804343, 26);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4149, 19.0399, 8.245536702482811, 26);
INSERT INTO building(id, name, level) VALUES (4150, "building_conscription_centerlevel", 20);
INSERT INTO building(id, name, level) VALUES (4151, "building_conscription_centerlevel", 24);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 4151, 15.69907, 6.467740726322821, 24);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4151, 15.69907, 4.0996771407145625, 24);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4151, 15.69907, 6.798736226547768, 24);
INSERT INTO building(id, name, level) VALUES (4152, "building_conscription_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (4153, "building_conscription_centerlevel", 20);
INSERT INTO building(id, name, level) VALUES (4154, "building_conscription_centerlevel", 19);
INSERT INTO building(id, name, level) VALUES (4155, "building_conscription_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (16781545, "building_chemical_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 16781545, 44.99999999999999, 29.514394790173473, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16781545, 14.999999999999998, 3.7653250826411613, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16781545, 10.0, 9.310051851789694, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 16781545, 75.0, 45.947557428417, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 16781545, 40.0, 24.5053639618224, 1);
INSERT INTO building(id, name, level) VALUES (16781569, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16781569, 19.999999999999996, 13.552717911221277, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16781569, 59.99999999999999, 36.87565312599606, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16781569, 9.999999999999998, 2.510216721760774, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16781569, 19.999999999999996, 21.109950702289087, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16781569, 130.0, 82.65568306137163, 2);
INSERT INTO building(id, name, level) VALUES (16781757, "building_motor_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16781757, 59.99999999999999, 36.1446006626799, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 16781757, 79.99999999999999, 48.1928008835732, 2);
INSERT INTO building(id, name, level) VALUES (67113408, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 67113408, 19.999999999999996, 13.552717911221277, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 67113408, 19.999999999999996, 12.291884375332017, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 67113408, 39.99999999999999, 13.412667721507663, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 67113408, 29.999999999999996, 31.664926053433632, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 67113408, 89.68499999999999, 58.91288385464049, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 67113408, 79.71999999999998, 52.36700787079155, 2);
INSERT INTO building(id, name, level) VALUES (67113438, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 67113438, 29.999999999999996, 7.530650165282323, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 67113438, 59.99999999999999, 55.86031111073816, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 67113438, 89.99999999999999, 53.191208580977104, 1);
INSERT INTO building(id, name, level) VALUES (4780, "building_tooling_workshopslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4780, 150.0, 92.18913281499015, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 4780, 100.00000000000001, 60.241001104466505, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4780, 400.00000000000006, 243.4008459622532, 5);
INSERT INTO building(id, name, level) VALUES (134222525, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 134222525, 9.999999999999998, 10.554975351144543, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 134222525, 79.99999999999999, 79.99999999999999, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 134222525, 19.999999999999996, 19.999999999999996, 2);
INSERT INTO building(id, name, level) VALUES (167777001, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 167777001, 5.0, 2.803076790895911, 1);
INSERT INTO building(id, name, level) VALUES (16782098, "building_tooling_workshopslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782098, 119.986796460177, 73.74319143274965, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782098, 79.9911946902655, 48.187496476838795, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16782098, 319.964796460177, 194.69925534136814, 4);
INSERT INTO building(id, name, level) VALUES (4962, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4962, 3.28734, 2.227619584913708, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4962, 6.57468, 4.040760318240396, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4962, 8.21835, 7.651326463615583, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4962, 1.64367, 1.7348896335415753, 2);
INSERT INTO building(id, name, level) VALUES (16782183, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782183, 3.288, 2.228066824604779, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782183, 6.576, 4.041571582609168, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782183, 8.22, 7.65286262217113, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782183, 1.644, 1.7352379477281628, 2);
INSERT INTO building(id, name, level) VALUES (33559400, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33559400, 3.288, 2.228066824604779, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559400, 6.576, 4.041571582609168, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33559400, 8.22, 7.65286262217113, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33559400, 1.644, 1.7352379477281628, 2);
INSERT INTO building(id, name, level) VALUES (5011, "building_construction_sectorlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5011, 8.22, 5.570167061511947, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5011, 16.44, 10.103928956522923, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5011, 20.55, 19.132156555427823, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5011, 4.11, 4.3380948693204076, 5);
INSERT INTO building(id, name, level) VALUES (16782236, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782236, 3.288, 2.228066824604779, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782236, 6.576, 4.041571582609168, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782236, 8.22, 7.65286262217113, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782236, 1.644, 1.7352379477281628, 2);
INSERT INTO building(id, name, level) VALUES (100668325, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 100668325, 4.932, 3.3421002369071684, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 100668325, 9.864, 6.062357373913753, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 100668325, 12.33, 11.479293933256693, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 100668325, 2.466, 2.602856921592245, 3);
INSERT INTO building(id, name, level) VALUES (16782249, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782249, 3.288, 2.228066824604779, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782249, 6.576, 4.041571582609168, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782249, 8.22, 7.65286262217113, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782249, 1.644, 1.7352379477281628, 2);
INSERT INTO building(id, name, level) VALUES (5035, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5035, 4.932, 3.3421002369071684, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5035, 9.864, 6.062357373913753, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5035, 12.33, 11.479293933256693, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5035, 2.466, 2.602856921592245, 3);
INSERT INTO building(id, name, level) VALUES (33559468, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33559468, 1.644, 1.1140334123023894, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559468, 3.288, 2.020785791304584, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33559468, 4.11, 3.826431311085565, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33559468, 0.822, 0.8676189738640814, 1);
INSERT INTO building(id, name, level) VALUES (16782256, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782256, 1.644, 1.1140334123023894, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782256, 3.288, 2.020785791304584, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782256, 4.11, 3.826431311085565, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782256, 0.822, 0.8676189738640814, 1);
INSERT INTO building(id, name, level) VALUES (16782265, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782265, 1.644, 1.1140334123023894, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782265, 3.288, 2.020785791304584, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782265, 4.11, 3.826431311085565, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782265, 0.822, 0.8676189738640814, 1);
INSERT INTO building(id, name, level) VALUES (16782273, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782273, 3.288, 2.228066824604779, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782273, 6.576, 4.041571582609168, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782273, 8.22, 7.65286262217113, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782273, 1.644, 1.7352379477281628, 2);
INSERT INTO building(id, name, level) VALUES (5070, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5070, 3.288, 2.228066824604779, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5070, 6.576, 4.041571582609168, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5070, 8.22, 7.65286262217113, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5070, 1.644, 1.7352379477281628, 2);
INSERT INTO building(id, name, level) VALUES (50336719, "building_steel_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 50336719, 104.99999999999999, 26.357275578488128, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 50336719, 179.99999999999997, 167.58093333221447, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50336719, 15.0, 15.832463026716816, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 50336719, 269.99999999999994, 196.3824171619542, 3);
INSERT INTO building(id, name, level) VALUES (16782306, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782306, 3.288, 2.228066824604779, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782306, 6.576, 4.041571582609168, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782306, 8.22, 7.65286262217113, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782306, 1.644, 1.7352379477281628, 2);
INSERT INTO building(id, name, level) VALUES (5100, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5100, 2.92409900990099, 0.7340122230737589, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5100, 2.92409900990099, 3.0863792973811113, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 5100, 11.69639603960396, 7.316222465949497, 2);
INSERT INTO building(id, name, level) VALUES (16782318, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782318, 4.932, 3.3421002369071684, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782318, 9.864, 6.062357373913753, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782318, 12.33, 11.479293933256693, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782318, 2.466, 2.602856921592245, 3);
INSERT INTO building(id, name, level) VALUES (33559552, "building_universitylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 33559552, 15.0, 8.409230372687732, 3);
INSERT INTO building(id, name, level) VALUES (33559553, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 33559553, 40.0, 22.424614327167287, 2);
INSERT INTO building(id, name, level) VALUES (16782412, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782412, 59.99999999999999, 36.87565312599606, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782412, 19.999999999999996, 5.020433443521548, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782412, 39.99999999999999, 24.096400441786596, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16782412, 159.99999999999997, 78.2947147726583, 2);
INSERT INTO building(id, name, level) VALUES (16782416, "building_chemical_plantslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 16782416, 90.0, 59.02878958034696, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782416, 29.999999999999996, 7.530650165282323, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782416, 19.999999999999996, 18.62010370357939, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 16782416, 150.0, 91.895114856834, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 16782416, 79.99999999999999, 49.010727923644794, 2);
INSERT INTO building(id, name, level) VALUES (5204, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5204, 20.0, 12.291884375332021, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 5204, 10.0, 4.9023082120620725, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 5204, 40.0, 22.096500799456166, 1);
INSERT INTO building(id, name, level) VALUES (16782519, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782519, 8.514599999999998, 5.769798596344235, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782519, 8.514599999999998, 5.2330239351101, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782519, 4.257299999999999, 1.0686745649552143, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782519, 17.029199999999996, 5.710175029077456, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782519, 17.029199999999996, 17.974278624971063, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16782519, 38.18159090909091, 21.98166481793097, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 16782519, 33.939190909090904, 19.53925703440965, 1);
INSERT INTO building(id, name, level) VALUES (50336954, "building_motor_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 50336954, 29.999999999999996, 18.07230033133995, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 50336954, 40.0, 24.096400441786603, 1);
INSERT INTO building(id, name, level) VALUES (16782526, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16782526, 5.0, 2.803076790895911, 1);
INSERT INTO building(id, name, level) VALUES (16782539, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16782539, 5.0, 2.803076790895911, 1);
INSERT INTO building(id, name, level) VALUES (16782546, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16782546, 5.0, 2.803076790895911, 1);
INSERT INTO building(id, name, level) VALUES (234886369, "building_tooling_workshopslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 234886369, 89.99999999999999, 55.313479688994086, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 234886369, 60.0, 36.1446006626799, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 234886369, 240.0, 146.04050757735192, 3);
INSERT INTO building(id, name, level) VALUES (16782593, "building_motor_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782593, 29.999999999999996, 18.07230033133995, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 16782593, 40.0, 24.096400441786603, 1);
INSERT INTO building(id, name, level) VALUES (33559815, "building_chemical_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 33559815, 44.99999999999999, 29.514394790173473, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 33559815, 14.999999999999998, 3.7653250826411613, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33559815, 10.0, 9.310051851789694, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 33559815, 75.0, 45.947557428417, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 33559815, 40.0, 24.5053639618224, 1);
INSERT INTO building(id, name, level) VALUES (16782622, "building_furniture_manufacturieslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782622, 40.00000000000001, 27.10543582244257, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782622, 40.00000000000001, 24.583768750664046, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782622, 80.00000000000001, 26.825335443015334, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782622, 60.0, 63.329852106867264, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16782622, 179.37, 117.825767709281, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 16782622, 159.44000000000003, 104.73401574158312, 4);
INSERT INTO building(id, name, level) VALUES (151000382, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 151000382, 15.0, 3.7653250826411617, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 151000382, 15.0, 15.832463026716816, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 151000382, 60.0, 37.530650165282324, 1);
INSERT INTO building(id, name, level) VALUES (201332062, "building_tooling_workshopslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 201332062, 120.0, 73.75130625199213, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 201332062, 80.00000000000001, 48.19280088357321, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 201332062, 320.00000000000006, 194.72067676980262, 4);
INSERT INTO building(id, name, level) VALUES (16782699, "building_furniture_manufacturieslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782699, 68.0, 46.07924089815236, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782699, 68.0, 41.792406876128865, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782699, 34.0, 8.534736853986633, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782699, 136.0, 45.603070253126056, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782699, 136.0, 143.5476647755658, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16782699, 304.92900000000003, 175.551801579618, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 16782699, 271.04800000000006, 156.04604584854937, 8);
INSERT INTO building(id, name, level) VALUES (117446002, "building_steel_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 117446002, 59.99999999999999, 15.061300330564645, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 117446002, 119.99999999999999, 111.72062222147632, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 117446002, 180.0, 106.38241716195422, 2);
INSERT INTO building(id, name, level) VALUES (16782717, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782717, 59.99999999999999, 36.87565312599606, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782717, 39.99999999999999, 24.096400441786596, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16782717, 159.99999999999997, 97.36033838490127, 2);
INSERT INTO building(id, name, level) VALUES (16782722, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782722, 29.999999999999996, 18.43782656299803, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 16782722, 10.0, 6.558754397816329, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 16782722, 70.0, 44.46643804918819, 1);
INSERT INTO building(id, name, level) VALUES (184554883, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 184554883, 9.918399999999998, 9.23408182867909, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 184554883, 9.918399999999998, 3.32580508822504, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 184554883, 14.877599999999997, 8.962415180318107, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 184554883, 9.883681818181817, 6.156650465649576, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 184554883, 24.709209090909088, 15.391628995535893, 1);
INSERT INTO building(id, name, level) VALUES (117446025, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 117446025, 59.99999999999999, 36.87565312599606, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 117446025, 19.999999999999996, 13.117508795632656, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 117446025, 9.999999999999998, 2.510216721760774, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 117446025, 9.999999999999998, 10.554975351144543, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 117446025, 140.0, 88.2521965753509, 2);
INSERT INTO building(id, name, level) VALUES (50337197, "building_iron_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 50337197, 45.117, 11.325344783568086, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50337197, 45.117, 47.62088229175884, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 50337197, 180.468, 112.88468956713616, 4);
INSERT INTO building(id, name, level) VALUES (16782783, "building_furniture_manufacturieslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782783, 34.0603982300885, 23.080548457812505, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782783, 34.0603982300885, 20.933323841100563, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782783, 68.120796460177, 22.84204019612024, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782783, 51.09059292035399, 53.925994894969634, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16782783, 152.73534513274336, 100.32981711884642, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 16782783, 135.7647522123894, 89.18206030710311, 4);
INSERT INTO building(id, name, level) VALUES (16782786, "building_chemical_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 16782786, 44.99999999999999, 29.514394790173473, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782786, 14.999999999999998, 3.7653250826411613, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782786, 10.0, 9.310051851789694, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 16782786, 75.0, 45.947557428417, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 16782786, 40.0, 24.5053639618224, 1);
INSERT INTO building(id, name, level) VALUES (33560006, "building_iron_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 33560006, 75.0, 18.826625413205807, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33560006, 75.0, 79.16231513358407, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 33560006, 300.0, 187.6532508264116, 5);
INSERT INTO building(id, name, level) VALUES (16782813, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782813, 5.0, 3.0729710938330053, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782813, 10.0, 2.5102167217607745, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 16782813, 10.0, 5.771885481207258, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 16782813, 65.0, 31.260762846373765, 1);
INSERT INTO building(id, name, level) VALUES (33560062, "building_steel_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 33560062, 59.99999999999999, 15.061300330564645, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33560062, 119.99999999999999, 111.72062222147632, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 33560062, 180.0, 106.38241716195422, 2);
INSERT INTO building(id, name, level) VALUES (5635, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5635, 10.0, 6.1459421876660105, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5635, 20.0, 5.020433443521549, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5635, 10.0, 4.289629909499937, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5635, 140.0, 60.41368115499137, 2);
INSERT INTO building(id, name, level) VALUES (16782874, "building_motor_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782874, 0.4861, 0.12202163484479124, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782874, 2.9166, 1.75698903821287, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782874, 0.4861, 0.5130773518191363, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 16782874, 3.8888, 2.4025417099029407, 1);
INSERT INTO building(id, name, level) VALUES (33560093, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33560093, 29.999999999999996, 18.43782656299803, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 33560093, 10.0, 6.558754397816329, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 33560093, 70.0, 44.46643804918819, 1);
INSERT INTO building(id, name, level) VALUES (16782882, "building_furniture_manufacturieslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782882, 50.00000000000001, 33.881794778053205, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782882, 50.00000000000001, 30.729710938330058, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782882, 100.00000000000001, 33.53166930376916, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782882, 75.0, 79.16231513358407, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16782882, 224.2125, 147.28220963660127, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 16782882, 199.3, 130.91751967697888, 5);
INSERT INTO building(id, name, level) VALUES (33560101, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33560101, 59.99999999999999, 36.87565312599606, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 33560101, 19.999999999999996, 13.117508795632656, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 33560101, 140.0, 88.93287609837638, 2);
INSERT INTO building(id, name, level) VALUES (16782905, "building_tooling_workshopslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782905, 180.0, 110.62695937798819, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782905, 120.00000000000001, 72.28920132535981, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16782905, 480.00000000000006, 292.08101515470395, 6);
INSERT INTO building(id, name, level) VALUES (16782915, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782915, 8.498199999999999, 5.7586853676570335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782915, 8.498199999999999, 5.222944589922329, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782915, 16.996399999999998, 5.699176641545821, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782915, 12.7473, 13.454743729364484, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16782915, 38.10804545454545, 25.032668292256194, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 16782915, 33.87381818181818, 22.251260704227732, 1);
INSERT INTO building(id, name, level) VALUES (16782919, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782919, 29.999999999999996, 18.43782656299803, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 16782919, 10.0, 6.558754397816329, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 16782919, 70.0, 44.46643804918819, 1);
INSERT INTO building(id, name, level) VALUES (5723, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5723, 9.996294117647059, 6.143664573796464, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5723, 19.992598039215686, 5.018575390948089, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5723, 9.996294117647059, 4.288040223121711, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5723, 139.94819607843138, 60.39132640070405, 3);
INSERT INTO building(id, name, level) VALUES (167777888, "building_motor_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 167777888, 29.999999999999996, 18.07230033133995, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 167777888, 40.0, 24.096400441786603, 1);
INSERT INTO building(id, name, level) VALUES (16782969, "building_furniture_manufacturieslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782969, 30.0, 20.329076866831922, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782969, 30.0, 18.437826562998033, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782969, 15.0, 3.7653250826411617, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782969, 60.0, 20.119001582261497, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782969, 60.0, 63.329852106867264, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16782969, 134.5275, 77.44932422630205, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 16782969, 119.57999999999998, 68.84384375671293, 3);
INSERT INTO building(id, name, level) VALUES (117446278, "building_textile_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 117446278, 59.99999999999999, 40.65815373366384, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 117446278, 19.999999999999996, 21.532079411764702, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 117446278, 9.999999999999998, 2.6269205882352935, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 117446278, 79.99999999999999, 51.74207878358915, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 117446278, 39.99999999999999, 25.871039391794575, 2);
INSERT INTO building(id, name, level) VALUES (16782996, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782996, 59.99999999999999, 36.87565312599606, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782996, 39.99999999999999, 24.096400441786596, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16782996, 159.99999999999997, 97.36033838490127, 2);
INSERT INTO building(id, name, level) VALUES (16782998, "building_furniture_manufacturieslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782998, 30.0, 20.329076866831922, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782998, 30.0, 18.437826562998033, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782998, 60.0, 20.119001582261497, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782998, 44.99999999999999, 47.49738908015044, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16782998, 134.5275, 88.36932578196075, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 16782998, 119.57999999999998, 78.55051180618732, 3);
INSERT INTO building(id, name, level) VALUES (83891865, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 83891865, 29.999999999999996, 18.43782656299803, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 83891865, 20.0, 12.048200220893301, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 83891865, 43.2, 26.28729136392335, 1);
INSERT INTO building(id, name, level) VALUES (16783023, "building_food_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 16783023, 18.9564, 30.298855342995147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 16783023, 7.108645454545454, 2.102965488003946, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 16783023, 16.63423636363636, 10.777588690279085, 1);
INSERT INTO building(id, name, level) VALUES (16783029, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16783029, 29.999999999999996, 7.530650165282323, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783029, 59.99999999999999, 55.86031111073816, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 16783029, 89.99999999999999, 53.191208580977104, 1);
INSERT INTO building(id, name, level) VALUES (16783051, "building_chemical_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 16783051, 44.99999999999999, 29.514394790173473, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16783051, 14.999999999999998, 3.7653250826411613, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783051, 10.0, 9.310051851789694, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 16783051, 75.0, 45.947557428417, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 16783051, 40.0, 24.5053639618224, 1);
INSERT INTO building(id, name, level) VALUES (67114726, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 67114726, 59.99999999999999, 36.87565312599606, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 67114726, 19.999999999999996, 5.020433443521548, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 67114726, 39.99999999999999, 24.096400441786596, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 67114726, 159.99999999999997, 78.2947147726583, 2);
INSERT INTO building(id, name, level) VALUES (16783084, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783084, 29.999999999999996, 20.32907686683192, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 16783084, 10.0, 10.766039705882353, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 16783084, 5.0, 1.313460294117647, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16783084, 40.0, 25.871039391794582, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 16783084, 20.0, 12.935519695897291, 1);
INSERT INTO building(id, name, level) VALUES (369104645, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (16783110, "building_food_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 16783110, 40.0, 63.93377506909572, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 16783110, 40.0, 11.833283859496776, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 16783110, 35.0, 22.677061688529836, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16783110, 59.99999999999999, 38.87496289462257, 1);
INSERT INTO building(id, name, level) VALUES (33560343, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33560343, 17.03379279279279, 11.54270943394574, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33560343, 17.03379279279279, 10.468870574118645, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 33560343, 34.06759459459459, 11.423433159208194, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33560343, 25.55069369369369, 26.968694214158127, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 33560343, 76.3838108108108, 50.17550955758633, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 33560343, 67.89672072072071, 44.600452940076735, 2);
INSERT INTO building(id, name, level) VALUES (5917, "building_steel_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5917, 59.99999999999999, 15.061300330564645, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5917, 119.99999999999999, 111.72062222147632, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 5917, 180.0, 106.38241716195422, 2);
INSERT INTO building(id, name, level) VALUES (83892036, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 83892036, 29.999999999999996, 7.530650165282323, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 83892036, 59.99999999999999, 55.86031111073816, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 83892036, 89.99999999999999, 53.191208580977104, 1);
INSERT INTO building(id, name, level) VALUES (5974, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 5974, 39.99999999999999, 63.9337750690957, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (8, 5974, 59.99999999999999, 132.47587929090676, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5974, 19.999999999999996, 18.62010370357939, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 5974, 79.99999999999999, 23.666567718993548, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 5974, 130.0, 104.87221165415764, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 5974, 119.99999999999999, 96.80511844999167, 2);
INSERT INTO building(id, name, level) VALUES (50337638, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50337638, 5.0, 5.277487675572272, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 50337638, 40.0, 40.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 50337638, 10.0, 10.0, 1);
INSERT INTO building(id, name, level) VALUES (16783213, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16783213, 29.999999999999996, 7.530650165282323, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783213, 59.99999999999999, 55.86031111073816, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 16783213, 89.99999999999999, 53.191208580977104, 1);
INSERT INTO building(id, name, level) VALUES (6022, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6022, 29.999999999999996, 7.530650165282323, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 6022, 59.99999999999999, 55.86031111073816, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 6022, 89.99999999999999, 53.191208580977104, 1);
INSERT INTO building(id, name, level) VALUES (16783248, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 16783248, 35.0, 17.158078742217256, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 16783248, 70.0, 34.31615748443451, 1);
INSERT INTO building(id, name, level) VALUES (6035, "building_arms_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 6035, 28.136696428571426, 9.434703999434, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 6035, 70.34174999999999, 42.374574394401066, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 6035, 14.068348214285713, 14.849106863310405, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 6035, 56.0764375, 36.220270137851756, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 6035, 70.09555357142857, 45.275341997597515, 3);
INSERT INTO building(id, name, level) VALUES (16783268, "building_motor_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16783268, 29.999999999999996, 18.07230033133995, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 16783268, 40.0, 24.096400441786603, 1);
INSERT INTO building(id, name, level) VALUES (33560496, "building_conscription_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 33560496, 2.0, 0.8239648241994998, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 33560496, 2.0, 0.522282802830303, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 33560496, 2.0, 0.8661323538971121, 2);
INSERT INTO building(id, name, level) VALUES (16783287, "building_furniture_manufacturieslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783287, 30.0, 20.329076866831922, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783287, 30.0, 18.437826562998033, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16783287, 15.0, 3.7653250826411617, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16783287, 60.0, 20.119001582261497, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783287, 60.0, 63.329852106867264, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16783287, 134.5275, 77.44932422630205, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 16783287, 119.57999999999998, 68.84384375671293, 3);
INSERT INTO building(id, name, level) VALUES (83892159, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 83892159, 5.0, 5.277487675572272, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 83892159, 40.0, 40.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 83892159, 12.499999999999998, 12.499999999999998, 1);
INSERT INTO building(id, name, level) VALUES (33560524, "building_chemical_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 33560524, 44.99999999999999, 29.514394790173473, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 33560524, 14.999999999999998, 3.7653250826411613, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33560524, 10.0, 9.310051851789694, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 33560524, 75.0, 45.947557428417, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 33560524, 40.0, 24.5053639618224, 1);
INSERT INTO building(id, name, level) VALUES (6105, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6105, 29.999999999999996, 18.43782656299803, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 6105, 26.249999999999996, 16.133098242623277, 1);
INSERT INTO building(id, name, level) VALUES (33560562, "building_railwaylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 33560562, 20.0, 5.020433443521549, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 33560562, 20.0, 11.543770962414516, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 33560562, 100.0, 41.41051101484017, 2);
INSERT INTO building(id, name, level) VALUES (16783355, "building_food_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 16783355, 40.0, 63.93377506909572, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 16783355, 40.0, 11.833283859496776, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 16783355, 35.0, 22.677061688529836, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16783355, 59.99999999999999, 38.87496289462257, 1);
INSERT INTO building(id, name, level) VALUES (33560585, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33560585, 15.0, 15.832463026716816, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 33560585, 120.0, 120.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 33560585, 30.0, 30.0, 3);
INSERT INTO building(id, name, level) VALUES (6157, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6157, 5.0, 3.0729710938330053, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6157, 10.0, 2.5102167217607745, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 6157, 5.0, 2.1448149547499686, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6157, 70.0, 30.206840577495687, 1);
INSERT INTO building(id, name, level) VALUES (6185, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6185, 59.99999999999999, 36.87565312599606, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 6185, 39.99999999999999, 24.096400441786596, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 6185, 159.99999999999997, 97.36033838490127, 2);
INSERT INTO building(id, name, level) VALUES (50337842, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50337842, 15.0, 15.832463026716816, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 50337842, 120.0, 120.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 50337842, 30.0, 30.0, 3);
INSERT INTO building(id, name, level) VALUES (100669508, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 100669508, 29.999999999999996, 20.32907686683192, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 100669508, 10.0, 10.766039705882353, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 100669508, 5.0, 1.313460294117647, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 100669508, 40.0, 25.871039391794582, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 100669508, 20.0, 12.935519695897291, 1);
INSERT INTO building(id, name, level) VALUES (33560645, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 33560645, 10.0, 2.5102167217607745, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 33560645, 10.0, 5.771885481207258, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 33560645, 50.0, 20.705255507420084, 1);
INSERT INTO building(id, name, level) VALUES (16783468, "building_tooling_workshopslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783468, 89.99999999999999, 55.313479688994086, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16783468, 60.0, 36.1446006626799, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16783468, 240.0, 146.04050757735192, 3);
INSERT INTO building(id, name, level) VALUES (33560712, "building_food_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 33560712, 36.16, 57.79613266246252, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 33560712, 9.04, 3.8778254381879425, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 33560712, 36.16, 10.697288608985083, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 33560712, 22.599999999999998, 12.993456325361844, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 33560712, 90.39999999999999, 51.97382530144738, 1);
INSERT INTO building(id, name, level) VALUES (33560713, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (6282, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6282, 12.8478, 3.2250762397838075, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 6282, 12.8478, 13.560821231643487, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 6282, 4.2826, 5.4483221907622505, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 6282, 61.66944, 46.27308198365409, 1);
INSERT INTO building(id, name, level) VALUES (16783503, "building_naval_baselevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 16783503, 0.482, 0.19354141239903475, 1);
INSERT INTO building(id, name, level) VALUES (50337936, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50337936, 29.999999999999996, 18.43782656299803, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 50337936, 20.0, 12.048200220893301, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 50337936, 80.0, 48.68016919245065, 1);
INSERT INTO building(id, name, level) VALUES (33560737, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33560737, 29.999999999999996, 18.43782656299803, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 33560737, 20.0, 12.048200220893301, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 33560737, 80.0, 48.68016919245065, 1);
INSERT INTO building(id, name, level) VALUES (6315, "building_conscription_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 6315, 2.0, 0.8239648241994998, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 6315, 2.0, 0.522282802830303, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 6315, 2.0, 0.8661323538971121, 2);
INSERT INTO building(id, name, level) VALUES (134224056, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 134224056, 5.0, 5.277487675572272, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 134224056, 40.0, 40.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 134224056, 10.0, 10.0, 1);
INSERT INTO building(id, name, level) VALUES (16783570, "building_munition_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 16783570, 14.0, 6.863231496886902, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 16783570, 14.0, 17.810795000857304, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 16783570, 35.0, 26.079039371108628, 1);
INSERT INTO building(id, name, level) VALUES (33560830, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33560830, 3.5042999999999997, 3.698780012301582, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 33560830, 28.034399999999998, 28.034399999999998, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 33560830, 7.0085999999999995, 7.0085999999999995, 1);
INSERT INTO building(id, name, level) VALUES (16783615, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (16783616, "building_subsistence_pastureslevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16783616, 0.07730000000000001, 0.06957, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16783616, 0.11594444444444443, 0.10435, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16783616, 0.03864444444444444, 0.03478, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16783616, 0.07730000000000001, 0.06957, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16783616, 0.07730000000000001, 0.06957, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16783616, 0.07730000000000001, 0.06957, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 16783616, 0.2056111111111111, 0.18505, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16783616, 0.10821111111111112, 0.09739, 2);
INSERT INTO building(id, name, level) VALUES (6414, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6414, 1.36905, 0.8414102152024152, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6414, 2.7381, 0.6873224405853178, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6414, 10.54168, 4.562522862616308, 1);
INSERT INTO building(id, name, level) VALUES (6443, "building_subsistence_fishing_villageslevel", 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6443, 0.009872727272727273, 0.01086, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 6443, 0.03951818181818181, 0.04347, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6443, 0.004936363636363636, 0.00543, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6443, 0.014818181818181815, 0.0163, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6443, 0.009872727272727273, 0.01086, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6443, 0.009872727272727273, 0.01086, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6443, 0.009872727272727273, 0.01086, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6443, 0.013827272727272726, 0.01521, 13);
INSERT INTO building(id, name, level) VALUES (6444, "building_subsistence_rice_paddieslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6444, 0.029699999999999997, 0.03861, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6444, 0.004946153846153846, 0.00643, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6444, 0.004946153846153846, 0.00643, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6444, 0.006600000000000001, 0.00858, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6444, 0.006600000000000001, 0.00858, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6444, 0.006600000000000001, 0.00858, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6444, 0.009899999999999999, 0.01287, 44);
