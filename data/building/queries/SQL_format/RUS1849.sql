
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 92.82528992603078, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 108.29617158036923, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 88.1919400128392, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 17.83560560204617, 8223.346437957554);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 26.2609529749262, 453.91931240295816);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 14.721670624084622, 441.04465746161515);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 25.790696417617266, 1170.4994225383823);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 43.41684072336986, 561.2514078657422);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 37.9332053324799, 2753.6638468607493);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 38.338437721844286, 2309.259586928735);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 41.55905661062878, 124.57934068497944);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 18.630115809908, 966.1833681449799);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 61.96948050190616, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 68.60222467404, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 59.073616294886534, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 63.74998235411661, 18.798374243211523);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 66.98382770888215, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 118.91496750290602, 382.91435406672997);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 24.834724173647725, 236.62759069780347);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 45.1642488652178, 144.76701428596664);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 26.71037746355107, 2570.4587286772567);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 52.56540777872084, 217.86428625790663);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 81.53410775478729, 499.9183487420937);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 50.43332932463186, 52.4506917323525);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 49.887844071117414, 66.62045604349304);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 87.5, 208.59260195884684);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 96.76141100209372, 121.02265823036537);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 95.92014658147188, 413.0241474417827);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 274.65116929228054, 1.629073528253454);
INSERT INTO building(id, name, level) VALUES (16777736, "building_subsistence_pastureslevel", 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16777736, 1.1196727272727272, 1.23164, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16777736, 1.6795181818181817, 1.84747, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16777736, 0.5598363636363636, 0.61582, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16777736, 1.1196727272727272, 1.23164, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16777736, 1.1196727272727272, 1.23164, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16777736, 1.1196727272727272, 1.23164, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 16777736, 3.7173363636363637, 4.08907, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16777736, 1.5675454545454544, 1.7243, 32);
INSERT INTO building(id, name, level) VALUES (33555011, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33555011, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33555011, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33555011, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33555011, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (16777888, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16777888, 20.0, 9.77104760125746, 2);
INSERT INTO building(id, name, level) VALUES (50332552, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50332552, 5.0, 0.5055220904606177, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 50332552, 20.0, 2.0220883618424708, 1);
INSERT INTO building(id, name, level) VALUES (936, "building_government_administrationlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 936, 119.99999999999999, 58.626285607544744, 12);
INSERT INTO building(id, name, level) VALUES (937, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 937, 9.936, 15.330698370994584, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 937, 19.872, 12.226104079365419, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 937, 24.84, 1.166521704560498, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 937, 4.968, 0.5022867490816698, 2);
INSERT INTO building(id, name, level) VALUES (938, "building_textile_millslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 938, 160.0, 246.87114929137817, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 938, 20.0, 0.0, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 938, 239.99999999999997, 119.99999999999999, 4);
INSERT INTO building(id, name, level) VALUES (939, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 939, 76.5, 47.066070957702024, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 939, 38.25, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 939, 25.5, 7.844345159617005, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 939, 63.75000000000001, 19.610862899042512, 3);
INSERT INTO building(id, name, level) VALUES (940, "building_arts_academylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 940, 4.18249504950495, 2.043367911036827, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 940, 1.673, 0.8173481318451864, 2);
INSERT INTO building(id, name, level) VALUES (941, "building_naval_baselevel", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 941, 23.05784, 15.095003330900056, 13);
INSERT INTO building(id, name, level) VALUES (942, "building_rye_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 942, 30.0, 33.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 942, 44.99999999999999, 50.4, 3);
INSERT INTO building(id, name, level) VALUES (943, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 943, 15.0, 14.369215864670354, 3);
INSERT INTO building(id, name, level) VALUES (944, "building_government_administrationlevel", 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 944, 140.0, 68.39733320880221, 14);
INSERT INTO building(id, name, level) VALUES (945, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 945, 9.936, 15.330698370994584, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 945, 19.872, 12.226104079365419, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 945, 24.84, 1.166521704560498, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 945, 4.968, 0.5022867490816698, 2);
INSERT INTO building(id, name, level) VALUES (946, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 946, 60.00000000000001, 36.9145654570212, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 946, 40.0, 1.8784568511441195, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 946, 120.00000000000001, 39.73225073373738, 2);
INSERT INTO building(id, name, level) VALUES (947, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 947, 10.0, 4.88552380062873, 2);
INSERT INTO building(id, name, level) VALUES (948, "building_textile_millslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 948, 240.0, 370.30672393706726, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 948, 30.0, 0.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 948, 360.0, 180.0, 6);
INSERT INTO building(id, name, level) VALUES (949, "building_furniture_manufacturieslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 949, 60.0, 92.57668098426682, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 949, 120.0, 73.8291309140424, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 949, 60.0, 22.025793360249114, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 949, 30.0, 3.033132542763706, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 949, 270.0, 140.63245189064745, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 949, 120.0, 62.50331195139887, 6);
INSERT INTO building(id, name, level) VALUES (950, "building_logging_camplevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 950, 50.0, 5.055220904606177, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 950, 400.0, 40.441767236849415, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 950, 100.0, 10.110441809212354, 10);
INSERT INTO building(id, name, level) VALUES (951, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 951, 20.0, 5.506440669593112, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 951, 20.0, 5.506440669593112, 20);
INSERT INTO building(id, name, level) VALUES (952, "building_saint_basils_cathedrallevel", 1);
INSERT INTO building(id, name, level) VALUES (953, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 953, 10.0, 2.753220334796556, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 953, 10.0, 2.753220334796556, 10);
INSERT INTO building(id, name, level) VALUES (954, "building_rye_farmlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 954, 6.000000000000001, 0.6066265085527414, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 954, 180.0, 18.198795256582237, 6);
INSERT INTO building(id, name, level) VALUES (955, "building_logging_camplevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 955, 39.99999999999999, 4.0441767236849415, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 955, 319.99999999999994, 32.35341378947953, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 955, 79.99999999999999, 8.088353447369883, 8);
INSERT INTO building(id, name, level) VALUES (956, "building_logging_camplevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 956, 35.0, 3.5386546332243243, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 956, 280.0, 28.309237065794594, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 956, 70.0, 7.0773092664486486, 7);
INSERT INTO building(id, name, level) VALUES (957, "building_wheat_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 957, 60.0, 67.8, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 957, 32.0, 36.16, 4);
INSERT INTO building(id, name, level) VALUES (958, "building_barrackslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 958, 8.0, 2.202576267837245, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 958, 8.0, 2.202576267837245, 8);
INSERT INTO building(id, name, level) VALUES (959, "building_government_administrationlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 959, 119.99999999999999, 58.626285607544744, 12);
INSERT INTO building(id, name, level) VALUES (960, "building_arts_academylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 960, 0.005, 0.0024427619003143646, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 960, 0.002, 0.0009771047601257459, 1);
INSERT INTO building(id, name, level) VALUES (961, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 961, 40.0, 46.745716212837685, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (8, 961, 60.0, 35.03970918897989, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 961, 20.0, 0.9392284255720598, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 961, 50.0, 4.593794976310175, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 961, 90.0, 38.7637306639756, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 961, 120.0, 51.68497421863413, 2);
INSERT INTO building(id, name, level) VALUES (962, "building_livestock_ranchlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 962, 150.0, 193.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 962, 25.0, 32.25, 5);
INSERT INTO building(id, name, level) VALUES (963, "building_wheat_farmlevel", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 963, 900.0, 1305.0, 30);
INSERT INTO building(id, name, level) VALUES (964, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 964, 17.272, 4.755362162260612, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 964, 17.272, 4.755362162260612, 20);
INSERT INTO building(id, name, level) VALUES (965, "building_textile_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 965, 54.70559405940594, 84.40770548820709, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 965, 18.235198019801977, 0.0, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 965, 9.11759405940594, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 965, 72.94079207920791, 24.31359735973597, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 965, 36.470396039603955, 12.156798679867984, 2);
INSERT INTO building(id, name, level) VALUES (966, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 966, 160.0, 186.98286485135074, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 966, 100.0, 9.18758995262035, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 966, 59.99999999999999, 32.7562769857861, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 966, 239.99999999999997, 131.0251079431444, 4);
INSERT INTO building(id, name, level) VALUES (967, "building_rye_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 967, 40.00000000000001, 45.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 967, 60.0, 67.8, 4);
INSERT INTO building(id, name, level) VALUES (968, "building_logging_camplevel", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 968, 85.0, 8.5938755378305, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 968, 680.0, 68.751004302644, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 968, 170.0, 17.187751075661, 17);
INSERT INTO building(id, name, level) VALUES (969, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 969, 25.0, 6.883050836991391, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 969, 25.0, 6.883050836991391, 25);
INSERT INTO building(id, name, level) VALUES (970, "building_food_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 970, 60.0, 70.11857431925654, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (8, 970, 90.0, 52.55956378346984, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 970, 30.0, 1.4088426383580896, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 970, 75.0, 6.890692464465263, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 970, 135.0, 58.145595995963404, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 970, 180.0, 77.5274613279512, 3);
INSERT INTO building(id, name, level) VALUES (971, "building_rye_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 971, 50.00000000000001, 57.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 971, 75.0, 85.5, 5);
INSERT INTO building(id, name, level) VALUES (972, "building_logging_camplevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 972, 39.99999999999999, 4.0441767236849415, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 972, 319.99999999999994, 32.35341378947953, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 972, 79.99999999999999, 8.088353447369883, 8);
INSERT INTO building(id, name, level) VALUES (973, "building_barrackslevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 973, 12.0, 3.3038644017558676, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 973, 12.0, 3.3038644017558676, 12);
INSERT INTO building(id, name, level) VALUES (974, "building_paper_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 974, 90.0, 55.37184818553179, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 974, 120.0, 73.82913091404238, 3);
INSERT INTO building(id, name, level) VALUES (975, "building_logging_camplevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 975, 60.0, 6.066265085527412, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 975, 480.0, 48.5301206842193, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 975, 120.0, 12.132530171054825, 12);
INSERT INTO building(id, name, level) VALUES (976, "building_rye_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 976, 70.00000000000001, 81.2, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 976, 105.0, 121.8, 7);
INSERT INTO building(id, name, level) VALUES (977, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 977, 8.112, 2.2334123355869666, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 977, 8.112, 2.2334123355869666, 10);
INSERT INTO building(id, name, level) VALUES (978, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 978, 5.0, 0.5055220904606177, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 978, 40.0, 4.0441767236849415, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 978, 10.0, 1.0110441809212354, 1);
INSERT INTO building(id, name, level) VALUES (979, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 979, 10.0, 9.579477243113567, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 979, 100.0, 95.79477243113568, 2);
INSERT INTO building(id, name, level) VALUES (980, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 980, 20.0, 13.093163393362133, 10);
INSERT INTO building(id, name, level) VALUES (981, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 981, 3.992, 3.8241273154509363, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 981, 39.92, 38.241273154509365, 1);
INSERT INTO building(id, name, level) VALUES (982, "building_shipyardslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 982, 20.0, 30.85889366142227, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 982, 40.0, 24.609710304680792, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 982, 15.0, 12.11432068212765, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 982, 20.0, 16.152427576170197, 1);
INSERT INTO building(id, name, level) VALUES (983, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 983, 5.0, 4.789738621556784, 1);
INSERT INTO building(id, name, level) VALUES (984, "building_arms_industrylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 984, 119.106, 5.593387042809288, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 984, 59.553, 21.86170119971526, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 984, 89.32949523809523, 18.4937950549876, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 984, 89.32949523809523, 18.4937950549876, 6);
INSERT INTO building(id, name, level) VALUES (985, "building_logging_camplevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 985, 75.0, 7.582831356909266, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 985, 600.0, 60.66265085527413, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 985, 150.0, 15.165662713818532, 15);
INSERT INTO building(id, name, level) VALUES (986, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 986, 15.0, 4.129830502194834, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 986, 15.0, 4.129830502194834, 15);
INSERT INTO building(id, name, level) VALUES (987, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 987, 13.122, 8.590424502384897, 10);
INSERT INTO building(id, name, level) VALUES (988, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 988, 10.0, 9.579477243113567, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 988, 100.0, 95.79477243113568, 2);
INSERT INTO building(id, name, level) VALUES (989, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 989, 10.0, 9.579477243113567, 2);
INSERT INTO building(id, name, level) VALUES (990, "building_livestock_ranchlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 990, 210.0, 275.1, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 990, 35.0, 45.85, 7);
INSERT INTO building(id, name, level) VALUES (991, "building_wheat_farmlevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 991, 225.00000000000003, 312.75, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 991, 120.00000000000001, 166.8, 15);
INSERT INTO building(id, name, level) VALUES (992, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 992, 10.0, 9.579477243113567, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 992, 100.0, 95.79477243113568, 2);
INSERT INTO building(id, name, level) VALUES (993, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 993, 17.688, 4.869896128188149, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 993, 17.688, 4.869896128188149, 20);
INSERT INTO building(id, name, level) VALUES (994, "building_rye_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 994, 40.00000000000001, 45.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 994, 60.0, 67.8, 4);
INSERT INTO building(id, name, level) VALUES (995, "building_logging_camplevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 995, 50.0, 5.055220904606177, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 995, 400.0, 40.441767236849415, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 995, 100.0, 10.110441809212354, 10);
INSERT INTO building(id, name, level) VALUES (996, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 996, 18.212, 5.014164873731488, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 996, 18.212, 5.014164873731488, 20);
INSERT INTO building(id, name, level) VALUES (997, "building_paper_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 997, 90.0, 55.37184818553179, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 997, 120.0, 73.82913091404238, 3);
INSERT INTO building(id, name, level) VALUES (998, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 998, 25.0, 2.5276104523030885, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 998, 200.0, 20.220883618424708, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 998, 50.0, 5.055220904606177, 5);
INSERT INTO building(id, name, level) VALUES (999, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 999, 10.0, 2.753220334796556, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 999, 10.0, 2.753220334796556, 10);
INSERT INTO building(id, name, level) VALUES (1000, "building_shipyardslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1000, 80.0, 123.43557464568909, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1000, 160.0, 98.43884121872317, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1000, 59.99999999999999, 48.45728272851059, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1000, 80.0, 64.60971030468079, 4);
INSERT INTO building(id, name, level) VALUES (1001, "building_livestock_ranchlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1001, 120.0, 153.6, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1001, 20.0, 25.6, 4);
INSERT INTO building(id, name, level) VALUES (1002, "building_wheat_farmlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1002, 240.0, 316.8, 8);
INSERT INTO building(id, name, level) VALUES (1003, "building_naval_baselevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1003, 32.462, 21.25151350376608, 20);
INSERT INTO building(id, name, level) VALUES (1004, "building_portlevel", 3);
INSERT INTO building(id, name, level) VALUES (1005, "building_rye_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1005, 30.0, 33.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1005, 44.99999999999999, 50.4, 3);
INSERT INTO building(id, name, level) VALUES (1006, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1006, 10.0, 1.0110441809212354, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1006, 80.0, 8.088353447369883, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1006, 20.0, 2.0220883618424708, 2);
INSERT INTO building(id, name, level) VALUES (1007, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1007, 10.0, 2.753220334796556, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1007, 10.0, 2.753220334796556, 10);
INSERT INTO building(id, name, level) VALUES (1013, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1013, 29.999999999999996, 33.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1013, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (1014, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1014, 13.7679, 3.7906062247445504, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1014, 13.7679, 3.7906062247445504, 15);
INSERT INTO building(id, name, level) VALUES (1015, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1015, 9.999999999999998, 1.0110441809212354, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1015, 79.99999999999999, 8.088353447369883, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1015, 19.999999999999996, 2.0220883618424708, 2);
INSERT INTO building(id, name, level) VALUES (1016, "building_shipyardslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1016, 40.0, 61.71778732284454, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1016, 80.0, 49.219420609361585, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1016, 30.0, 24.2286413642553, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1016, 40.0, 32.304855152340394, 2);
INSERT INTO building(id, name, level) VALUES (1017, "building_iron_minelevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1017, 30.0, 3.033132542763706, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1017, 120.0, 12.132530171054825, 6);
INSERT INTO building(id, name, level) VALUES (1018, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1018, 10.0, 1.0110441809212354, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1018, 80.0, 8.088353447369883, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1018, 20.0, 2.0220883618424708, 2);
INSERT INTO building(id, name, level) VALUES (1019, "building_barrackslevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1019, 11.0, 3.028542368276212, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1019, 11.0, 3.028542368276212, 11);
INSERT INTO building(id, name, level) VALUES (1020, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1020, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1021, "building_rye_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1021, 10.0, 11.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1021, 14.999999999999998, 16.5, 1);
INSERT INTO building(id, name, level) VALUES (1022, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1022, 10.0, 6.546581696681066, 5);
INSERT INTO building(id, name, level) VALUES (1023, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1023, 3.713, 1.0222707103099613, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1023, 3.713, 1.0222707103099613, 5);
INSERT INTO building(id, name, level) VALUES (1024, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1024, 5.0, 4.789738621556784, 1);
INSERT INTO building(id, name, level) VALUES (1025, "building_rye_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1025, 30.0, 33.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1025, 44.99999999999999, 50.4, 3);
INSERT INTO building(id, name, level) VALUES (1026, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1026, 20.0, 13.093163393362133, 10);
INSERT INTO building(id, name, level) VALUES (1027, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1027, 15.0, 14.369215864670354, 3);
INSERT INTO building(id, name, level) VALUES (1028, "building_rye_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1028, 40.00000000000001, 45.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1028, 60.0, 67.8, 4);
INSERT INTO building(id, name, level) VALUES (1029, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1029, 3.893, 1.0718286763362994, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1029, 3.893, 1.0718286763362994, 5);
INSERT INTO building(id, name, level) VALUES (1030, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1030, 5.0, 4.789738621556784, 1);
INSERT INTO building(id, name, level) VALUES (1031, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1031, 20.0, 30.85889366142227, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1031, 40.0, 24.609710304680792, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1031, 20.0, 7.3419311200830375, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1031, 10.0, 1.0110441809212354, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1031, 90.0, 46.87748396354914, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1031, 40.0, 20.834437317132952, 2);
INSERT INTO building(id, name, level) VALUES (1032, "building_paper_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1032, 90.0, 55.37184818553179, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1032, 120.0, 73.82913091404238, 3);
INSERT INTO building(id, name, level) VALUES (1033, "building_rye_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1033, 19.999999999999996, 22.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1033, 29.999999999999996, 33.3, 2);
INSERT INTO building(id, name, level) VALUES (1034, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1034, 30.0, 18.4572827285106, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1034, 20.0, 0.9392284255720598, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1034, 60.0, 19.86612536686869, 1);
INSERT INTO building(id, name, level) VALUES (1035, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1035, 15.0, 1.516566271381853, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1035, 120.0, 12.132530171054825, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1035, 30.0, 3.033132542763706, 3);
INSERT INTO building(id, name, level) VALUES (1043, "building_textile_millslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1043, 240.0, 370.30672393706726, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1043, 30.0, 0.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1043, 360.0, 180.0, 6);
INSERT INTO building(id, name, level) VALUES (1044, "building_rye_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1044, 30.0, 33.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1044, 44.99999999999999, 50.4, 3);
INSERT INTO building(id, name, level) VALUES (1045, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1045, 60.0, 36.9145654570212, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1045, 80.0, 49.2194206093616, 2);
INSERT INTO building(id, name, level) VALUES (1046, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1046, 89.99999999999999, 100.8, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1046, 15.0, 16.8, 3);
INSERT INTO building(id, name, level) VALUES (1047, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1047, 25.5, 15.688690319234007, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1047, 12.75, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1047, 8.5, 2.6147817198723344, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1047, 21.25, 6.536954299680836, 1);
INSERT INTO building(id, name, level) VALUES (1119, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1119, 4.25, 4.071277828323266, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1119, 42.5, 40.712778283232666, 1);
INSERT INTO building(id, name, level) VALUES (33555751, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 33555751, 10.0, 11.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 33555751, 9.0, 9.9, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 33555751, 5.999999999999999, 6.6, 1);
INSERT INTO building(id, name, level) VALUES (1420, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1420, 5.0, 4.789738621556784, 1);
INSERT INTO building(id, name, level) VALUES (2059, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2059, 10.0, 11.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2059, 9.0, 9.9, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2059, 5.999999999999999, 6.6, 1);
INSERT INTO building(id, name, level) VALUES (2060, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2060, 89.99999999999999, 100.8, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2060, 15.0, 16.8, 3);
INSERT INTO building(id, name, level) VALUES (2061, "building_tea_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2061, 39.37239603960396, 39.76612, 2);
INSERT INTO building(id, name, level) VALUES (2064, "building_wheat_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2064, 2.0, 0.2022088361842471, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2064, 19.999999999999996, 2.0220883618424708, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2064, 18.0, 1.8198795256582239, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2064, 12.0, 1.2132530171054825, 2);
INSERT INTO building(id, name, level) VALUES (2065, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2065, 44.220495049504954, 44.6627, 2);
INSERT INTO building(id, name, level) VALUES (2066, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2066, 59.99999999999999, 66.6, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2066, 9.999999999999998, 11.1, 2);
INSERT INTO building(id, name, level) VALUES (2070, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2070, 18.997999999999998, 21.08778, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2070, 17.098198198198197, 18.979, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2070, 11.398792792792792, 12.65266, 2);
INSERT INTO building(id, name, level) VALUES (2071, "building_rice_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2071, 20.0, 22.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2071, 5.999999999999999, 6.6, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2071, 9.0, 9.9, 1);
INSERT INTO building(id, name, level) VALUES (2072, "building_tea_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2072, 39.37239603960396, 39.76612, 2);
INSERT INTO building(id, name, level) VALUES (2073, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2073, 30.0, 46.28834049213341, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2073, 10.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2073, 25.0, 12.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2073, 20.0, 10.0, 1);
INSERT INTO building(id, name, level) VALUES (2074, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2074, 7.815, 2.151641691643509, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2074, 7.815, 2.151641691643509, 10);
INSERT INTO building(id, name, level) VALUES (2075, "building_arms_industrylevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2075, 198.32999999999998, 9.31385868218533, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2075, 99.16499999999999, 36.40312997615172, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 2075, 148.74749541284402, 30.795043538259023, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 2075, 148.74749541284402, 30.795043538259023, 10);
INSERT INTO building(id, name, level) VALUES (2076, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2076, 40.0, 46.745716212837685, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (8, 2076, 60.0, 35.03970918897989, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2076, 20.0, 0.9392284255720598, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 2076, 50.0, 4.593794976310175, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 2076, 90.0, 38.7637306639756, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2076, 120.0, 51.68497421863413, 2);
INSERT INTO building(id, name, level) VALUES (2077, "building_rye_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2077, 40.00000000000001, 45.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2077, 60.0, 67.8, 4);
INSERT INTO building(id, name, level) VALUES (2078, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2078, 20.0, 5.506440669593112, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2078, 20.0, 5.506440669593112, 20);
INSERT INTO building(id, name, level) VALUES (2079, "building_arms_industrylevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2079, 138.82959433962262, 6.519635065720575, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2079, 69.41479245283018, 25.481931245176956, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 2079, 104.12219811320755, 21.556313370494493, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 2079, 104.12219811320755, 21.556313370494493, 7);
INSERT INTO building(id, name, level) VALUES (2080, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2080, 60.0, 36.9145654570212, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2080, 80.0, 49.2194206093616, 2);
INSERT INTO building(id, name, level) VALUES (2081, "building_food_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2081, 60.0, 70.11857431925654, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (8, 2081, 90.0, 52.55956378346984, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2081, 30.0, 1.4088426383580896, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 2081, 75.0, 6.890692464465263, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 2081, 135.0, 58.145595995963404, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2081, 180.0, 77.5274613279512, 3);
INSERT INTO building(id, name, level) VALUES (2082, "building_rye_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2082, 19.999999999999996, 22.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2082, 29.999999999999996, 33.3, 2);
INSERT INTO building(id, name, level) VALUES (2083, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2083, 20.0, 5.506440669593112, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2083, 20.0, 5.506440669593112, 20);
INSERT INTO building(id, name, level) VALUES (2084, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2084, 25.000000000000004, 2.527610452303089, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2084, 200.00000000000003, 20.22088361842471, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2084, 50.00000000000001, 5.055220904606178, 5);
INSERT INTO building(id, name, level) VALUES (2085, "building_iron_minelevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2085, 49.99999999999999, 5.055220904606176, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2085, 199.99999999999997, 20.220883618424704, 10);
INSERT INTO building(id, name, level) VALUES (2086, "building_rye_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2086, 50.00000000000001, 57.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2086, 75.0, 85.5, 5);
INSERT INTO building(id, name, level) VALUES (2087, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2087, 10.384, 2.8589439956527443, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2087, 10.384, 2.8589439956527443, 20);
INSERT INTO building(id, name, level) VALUES (2088, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2088, 29.999999999999996, 33.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2088, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (2791, "building_iron_minelevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2791, 30.0, 3.033132542763706, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2791, 120.0, 12.132530171054825, 6);
INSERT INTO building(id, name, level) VALUES (2792, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2792, 15.0, 1.516566271381853, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2792, 120.0, 12.132530171054825, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2792, 30.0, 3.033132542763706, 3);
INSERT INTO building(id, name, level) VALUES (2793, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 2793, 10.0, 6.546581696681066, 5);
INSERT INTO building(id, name, level) VALUES (2794, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2794, 5.0, 4.789738621556784, 1);
INSERT INTO building(id, name, level) VALUES (2823, "building_subsistence_farmslevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2823, 2.8878, 3.17658, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2823, 0.4812999999999999, 0.52943, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2823, 0.4812999999999999, 0.52943, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2823, 0.4812999999999999, 0.52943, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2823, 0.4812999999999999, 0.52943, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2823, 0.4812999999999999, 0.52943, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2823, 0.6738181818181818, 0.7412, 4);
INSERT INTO building(id, name, level) VALUES (2832, "building_subsistence_farmslevel", 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2832, 2.365281818181818, 2.60181, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2832, 0.3942090909090909, 0.43363, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2832, 0.3942090909090909, 0.43363, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2832, 0.3942090909090909, 0.43363, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2832, 0.3942090909090909, 0.43363, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2832, 0.3942090909090909, 0.43363, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2832, 0.5519, 0.60709, 41);
INSERT INTO building(id, name, level) VALUES (2836, "building_subsistence_farmslevel", 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2836, 56.77646363636363, 62.45411, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2836, 9.462736363636363, 10.40901, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2836, 9.462736363636363, 10.40901, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2836, 9.462736363636363, 10.40901, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2836, 9.462736363636363, 10.40901, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2836, 9.462736363636363, 10.40901, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2836, 13.247836363636363, 14.57262, 47);
INSERT INTO building(id, name, level) VALUES (2837, "building_urban_centerlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2837, 39.99999999999999, 24.609710304680792, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2837, 39.99999999999999, 8.37257141636695, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2837, 319.35999999999996, 131.6652686304226, 8);
INSERT INTO building(id, name, level) VALUES (2838, "building_subsistence_pastureslevel", 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2838, 2.7830363636363633, 3.06134, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2838, 4.174554545454545, 4.59201, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2838, 1.3915181818181817, 1.53067, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2838, 2.7830363636363633, 3.06134, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2838, 2.7830363636363633, 3.06134, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2838, 2.7830363636363633, 3.06134, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2838, 9.239690909090909, 10.16366, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2838, 3.896254545454545, 4.28588, 32);
INSERT INTO building(id, name, level) VALUES (16780586, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 16780586, 10.0, 9.579477243113567, 2);
INSERT INTO building(id, name, level) VALUES (67112386, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 67112386, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 67112386, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 67112386, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 67112386, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (3545, "building_subsistence_farmslevel", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3545, 37.627199999999995, 41.38992, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3545, 6.271199999999999, 6.89832, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3545, 6.271199999999999, 6.89832, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3545, 6.271199999999999, 6.89832, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3545, 6.271199999999999, 6.89832, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3545, 6.271199999999999, 6.89832, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3545, 8.779672727272727, 9.65764, 30);
INSERT INTO building(id, name, level) VALUES (3546, "building_subsistence_farmslevel", 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3546, 55.390499999999996, 60.92955, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3546, 9.231745454545454, 10.15492, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3546, 9.231745454545454, 10.15492, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3546, 9.231745454545454, 10.15492, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3546, 9.231745454545454, 10.15492, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3546, 9.231745454545454, 10.15492, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3546, 12.924445454545452, 14.21689, 33);
INSERT INTO building(id, name, level) VALUES (3547, "building_subsistence_farmslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3547, 87.39191818181818, 96.13111, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3547, 14.565318181818181, 16.02185, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3547, 14.565318181818181, 16.02185, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3547, 14.565318181818181, 16.02185, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3547, 14.565318181818181, 16.02185, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3547, 14.565318181818181, 16.02185, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3547, 20.39144545454545, 22.43059, 44);
INSERT INTO building(id, name, level) VALUES (3562, "building_subsistence_farmslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3562, 2.8571363636363634, 3.14285, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3562, 0.4761818181818182, 0.5238, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3562, 0.4761818181818182, 0.5238, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3562, 0.4761818181818182, 0.5238, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3562, 0.4761818181818182, 0.5238, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3562, 0.4761818181818182, 0.5238, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3562, 0.6666636363636363, 0.73333, 6);
INSERT INTO building(id, name, level) VALUES (16780825, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16780825, 4.96303, 7.657680750422428, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16780825, 9.92606, 6.106936526671996, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16780825, 12.40758, 0.5826775914279688, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16780825, 2.48151, 0.2508916245397855, 1);
INSERT INTO building(id, name, level) VALUES (3673, "building_subsistence_farmslevel", 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3673, 90.06551818181818, 99.07207, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3673, 15.01091818181818, 16.51201, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3673, 15.01091818181818, 16.51201, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3673, 15.01091818181818, 16.51201, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3673, 15.01091818181818, 16.51201, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3673, 15.01091818181818, 16.51201, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3673, 21.01528181818182, 23.11681, 41);
INSERT INTO building(id, name, level) VALUES (3674, "building_subsistence_farmslevel", 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3674, 185.01392727272727, 203.51532, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3674, 30.835654545454545, 33.91922, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3674, 30.835654545454545, 33.91922, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3674, 30.835654545454545, 33.91922, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3674, 30.835654545454545, 33.91922, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3674, 30.835654545454545, 33.91922, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3674, 43.16990909090909, 47.4869, 71);
INSERT INTO building(id, name, level) VALUES (3675, "building_subsistence_farmslevel", 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3675, 164.06279999999998, 180.46908, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3675, 27.343799999999998, 30.07818, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3675, 27.343799999999998, 30.07818, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3675, 27.343799999999998, 30.07818, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3675, 27.343799999999998, 30.07818, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3675, 27.343799999999998, 30.07818, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3675, 38.28131818181818, 42.10945, 88);
INSERT INTO building(id, name, level) VALUES (3676, "building_subsistence_pastureslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3676, 2.2577, 2.03193, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3676, 3.3865444444444446, 3.04789, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3676, 1.1288444444444443, 1.01596, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3676, 2.2577, 2.03193, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3676, 2.2577, 2.03193, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3676, 2.2577, 2.03193, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3676, 7.495555555555556, 6.746, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3676, 3.1607777777777777, 2.8447, 20);
INSERT INTO building(id, name, level) VALUES (3677, "building_subsistence_pastureslevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3677, 2.8567444444444448, 2.57107, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3677, 4.285122222222222, 3.85661, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3677, 1.4283666666666668, 1.28553, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3677, 2.8567444444444448, 2.57107, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3677, 2.8567444444444448, 2.57107, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3677, 2.8567444444444448, 2.57107, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3677, 9.484399999999999, 8.53596, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3677, 3.9994444444444444, 3.5995, 15);
INSERT INTO building(id, name, level) VALUES (3678, "building_subsistence_farmslevel", 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3678, 12.752999999999998, 14.0283, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3678, 2.1254999999999997, 2.33805, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3678, 2.1254999999999997, 2.33805, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3678, 2.1254999999999997, 2.33805, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3678, 2.1254999999999997, 2.33805, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3678, 2.1254999999999997, 2.33805, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3678, 2.9757, 3.27327, 26);
INSERT INTO building(id, name, level) VALUES (3679, "building_subsistence_farmslevel", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3679, 0.0036, 0.00396, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3679, 0.0006, 0.00066, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3679, 0.0006, 0.00066, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3679, 0.0006, 0.00066, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3679, 0.0006, 0.00066, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3679, 0.0006, 0.00066, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3679, 0.0008363636363636363, 0.00092, 30);
INSERT INTO building(id, name, level) VALUES (3680, "building_subsistence_farmslevel", 169);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3680, 258.71702727272725, 284.58873, 169);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3680, 43.119499999999995, 47.43145, 169);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3680, 43.119499999999995, 47.43145, 169);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3680, 43.119499999999995, 47.43145, 169);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3680, 43.119499999999995, 47.43145, 169);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3680, 43.119499999999995, 47.43145, 169);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3680, 60.3673, 66.40403, 169);
INSERT INTO building(id, name, level) VALUES (3681, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3681, 5.0, 3.076213788085099, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3681, 25.0, 15.381068940425497, 1);
INSERT INTO building(id, name, level) VALUES (3687, "building_subsistence_pastureslevel", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3687, 6.9887999999999995, 7.68768, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3687, 10.4832, 11.53152, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3687, 3.4943999999999997, 3.84384, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3687, 6.9887999999999995, 7.68768, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3687, 6.9887999999999995, 7.68768, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3687, 6.9887999999999995, 7.68768, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3687, 23.20280909090909, 25.52309, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3687, 9.78431818181818, 10.76275, 30);
INSERT INTO building(id, name, level) VALUES (3693, "building_subsistence_pastureslevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3693, 2.2855, 2.51405, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3693, 3.428245454545454, 3.77107, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3693, 1.1427454545454545, 1.25702, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3693, 2.2855, 2.51405, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3693, 2.2855, 2.51405, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3693, 2.2855, 2.51405, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3693, 7.587854545454546, 8.34664, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3693, 3.1997, 3.51967, 10);
INSERT INTO building(id, name, level) VALUES (3694, "building_subsistence_pastureslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3694, 4.171636363636363, 4.5888, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3694, 6.257454545454546, 6.8832, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3694, 2.0858181818181816, 2.2944, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3694, 4.171636363636363, 4.5888, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3694, 4.171636363636363, 4.5888, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3694, 4.171636363636363, 4.5888, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3694, 13.849836363636362, 15.23482, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3694, 5.840290909090909, 6.42432, 44);
INSERT INTO building(id, name, level) VALUES (3695, "building_subsistence_pastureslevel", 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3695, 2.4434777777777774, 2.19913, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3695, 3.665211111111111, 3.29869, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3695, 1.2217333333333333, 1.09956, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3695, 2.4434777777777774, 2.19913, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3695, 2.4434777777777774, 2.19913, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3695, 2.4434777777777774, 2.19913, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3695, 8.112344444444444, 7.30111, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3695, 3.4208666666666665, 3.07878, 26);
INSERT INTO building(id, name, level) VALUES (3696, "building_subsistence_farmslevel", 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3696, 27.49571818181818, 30.24529, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3696, 4.582618181818181, 5.04088, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3696, 4.582618181818181, 5.04088, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3696, 4.582618181818181, 5.04088, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3696, 4.582618181818181, 5.04088, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3696, 4.582618181818181, 5.04088, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3696, 6.415663636363636, 7.05723, 36);
INSERT INTO building(id, name, level) VALUES (3697, "building_subsistence_farmslevel", 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3697, 60.020636363636356, 66.0227, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3697, 10.003436363636364, 11.00378, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3697, 10.003436363636364, 11.00378, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3697, 10.003436363636364, 11.00378, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3697, 10.003436363636364, 11.00378, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3697, 10.003436363636364, 11.00378, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3697, 14.00480909090909, 15.40529, 48);
INSERT INTO building(id, name, level) VALUES (3698, "building_subsistence_farmslevel", 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3698, 161.27039999999997, 177.39744, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3698, 26.8784, 29.56624, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3698, 26.8784, 29.56624, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3698, 26.8784, 29.56624, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3698, 26.8784, 29.56624, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3698, 26.8784, 29.56624, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3698, 37.629754545454546, 41.39273, 64);
INSERT INTO building(id, name, level) VALUES (3701, "building_subsistence_farmslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3701, 24.83843636363636, 27.32228, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3701, 4.139736363636363, 4.55371, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3701, 4.139736363636363, 4.55371, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3701, 4.139736363636363, 4.55371, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3701, 4.139736363636363, 4.55371, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3701, 4.139736363636363, 4.55371, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3701, 5.795627272727272, 6.37519, 44);
INSERT INTO building(id, name, level) VALUES (3702, "building_subsistence_pastureslevel", 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3702, 3.883311111111111, 3.49498, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3702, 5.824977777777777, 5.24248, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3702, 1.9416555555555555, 1.74749, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3702, 3.883311111111111, 3.49498, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3702, 3.883311111111111, 3.49498, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3702, 3.883311111111111, 3.49498, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3702, 12.892622222222222, 11.60336, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3702, 5.436644444444444, 4.89298, 56);
INSERT INTO building(id, name, level) VALUES (3703, "building_subsistence_pastureslevel", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3703, 3.0612777777777778, 2.75515, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3703, 4.591911111111111, 4.13272, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3703, 1.5306333333333333, 1.37757, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3703, 3.0612777777777778, 2.75515, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3703, 3.0612777777777778, 2.75515, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3703, 3.0612777777777778, 2.75515, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3703, 10.163444444444444, 9.1471, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3703, 4.285788888888889, 3.85721, 16);
INSERT INTO building(id, name, level) VALUES (3704, "building_subsistence_farmslevel", 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3704, 69.83183636363636, 76.81502, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3704, 11.638636363636364, 12.8025, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3704, 11.638636363636364, 12.8025, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3704, 11.638636363636364, 12.8025, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3704, 11.638636363636364, 12.8025, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3704, 11.638636363636364, 12.8025, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3704, 16.294090909090908, 17.9235, 52);
INSERT INTO building(id, name, level) VALUES (3705, "building_subsistence_farmslevel", 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3705, 258.8727545454545, 284.76003, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3705, 43.14545454545454, 47.46, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3705, 43.14545454545454, 47.46, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3705, 43.14545454545454, 47.46, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3705, 43.14545454545454, 47.46, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3705, 43.14545454545454, 47.46, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3705, 60.40363636363636, 66.444, 162);
INSERT INTO building(id, name, level) VALUES (3706, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3706, 15.0, 9.2286413642553, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3706, 75.0, 46.1432068212765, 3);
INSERT INTO building(id, name, level) VALUES (3707, "building_subsistence_farmslevel", 206);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3707, 388.5798545454545, 427.43784, 206);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3707, 64.76330909090908, 71.23964, 206);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3707, 64.76330909090908, 71.23964, 206);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3707, 64.76330909090908, 71.23964, 206);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3707, 64.76330909090908, 71.23964, 206);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3707, 64.76330909090908, 71.23964, 206);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3707, 90.66862727272726, 99.73549, 206);
INSERT INTO building(id, name, level) VALUES (3708, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3708, 15.0, 9.2286413642553, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3708, 15.0, 3.139714281137607, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3708, 119.75999999999999, 49.37447573640848, 3);
INSERT INTO building(id, name, level) VALUES (3709, "building_subsistence_farmslevel", 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3709, 242.9955, 267.29505, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3709, 40.499245454545445, 44.54917, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3709, 40.499245454545445, 44.54917, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3709, 40.499245454545445, 44.54917, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3709, 40.499245454545445, 44.54917, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3709, 40.499245454545445, 44.54917, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3709, 56.69894545454545, 62.36884, 110);
INSERT INTO building(id, name, level) VALUES (3710, "building_subsistence_farmslevel", 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3710, 131.54525454545453, 144.69978, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3710, 21.92420909090909, 24.11663, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3710, 21.92420909090909, 24.11663, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3710, 21.92420909090909, 24.11663, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3710, 21.92420909090909, 24.11663, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3710, 21.92420909090909, 24.11663, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3710, 30.693890909090907, 33.76328, 77);
INSERT INTO building(id, name, level) VALUES (3711, "building_subsistence_farmslevel", 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3711, 261.3465, 287.48115, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3711, 43.55774545454545, 47.91352, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3711, 43.55774545454545, 47.91352, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3711, 43.55774545454545, 47.91352, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3711, 43.55774545454545, 47.91352, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3711, 43.55774545454545, 47.91352, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3711, 60.98084545454545, 67.07893, 150);
INSERT INTO building(id, name, level) VALUES (3712, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3712, 10.0, 6.152427576170198, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3712, 10.0, 2.093142854091738, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3712, 79.84, 32.916317157605654, 2);
INSERT INTO building(id, name, level) VALUES (3713, "building_subsistence_farmslevel", 174);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3713, 378.711, 416.5821, 174);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3713, 63.1185, 69.43035, 174);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3713, 63.1185, 69.43035, 174);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3713, 63.1185, 69.43035, 174);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3713, 63.1185, 69.43035, 174);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3713, 63.1185, 69.43035, 174);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3713, 88.3659, 97.20249, 174);
INSERT INTO building(id, name, level) VALUES (3714, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3714, 35.0, 21.533496516595697, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3714, 175.0, 107.6674825829785, 7);
INSERT INTO building(id, name, level) VALUES (3715, "building_subsistence_farmslevel", 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3715, 70.28450909090908, 77.31296, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3715, 11.714081818181818, 12.88549, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3715, 11.714081818181818, 12.88549, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3715, 11.714081818181818, 12.88549, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3715, 11.714081818181818, 12.88549, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3715, 11.714081818181818, 12.88549, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3715, 16.39971818181818, 18.03969, 27);
INSERT INTO building(id, name, level) VALUES (3717, "building_subsistence_farmslevel", 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3717, 99.0825, 108.99075, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3717, 16.513745454545454, 18.16512, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3717, 16.513745454545454, 18.16512, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3717, 16.513745454545454, 18.16512, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3717, 16.513745454545454, 18.16512, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3717, 16.513745454545454, 18.16512, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3717, 23.119245454545453, 25.43117, 55);
INSERT INTO building(id, name, level) VALUES (3719, "building_subsistence_farmslevel", 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3719, 167.09885454545454, 183.80874, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3719, 27.849809090909087, 30.63479, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3719, 27.849809090909087, 30.63479, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3719, 27.849809090909087, 30.63479, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3719, 27.849809090909087, 30.63479, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3719, 27.849809090909087, 30.63479, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3719, 38.98972727272727, 42.8887, 86);
INSERT INTO building(id, name, level) VALUES (3720, "building_subsistence_farmslevel", 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3720, 14.829354545454546, 16.31229, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3720, 2.4715545454545453, 2.71871, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3720, 2.4715545454545453, 2.71871, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3720, 2.4715545454545453, 2.71871, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3720, 2.4715545454545453, 2.71871, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3720, 2.4715545454545453, 2.71871, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3720, 3.460181818181818, 3.8062, 52);
INSERT INTO building(id, name, level) VALUES (3721, "building_subsistence_farmslevel", 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3721, 207.1854, 258.98175, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3721, 34.530896, 43.16362, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3721, 34.530896, 43.16362, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3721, 34.530896, 43.16362, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3721, 34.530896, 43.16362, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3721, 34.530896, 43.16362, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3721, 48.343256000000004, 60.42907, 124);
INSERT INTO building(id, name, level) VALUES (3722, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3722, 5.0, 3.076213788085099, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3722, 25.0, 15.381068940425497, 1);
INSERT INTO building(id, name, level) VALUES (3723, "building_subsistence_farmslevel", 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3723, 457.77509999999995, 503.55261, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3723, 76.29584545454546, 83.92543, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3723, 76.29584545454546, 83.92543, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3723, 76.29584545454546, 83.92543, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3723, 76.29584545454546, 83.92543, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3723, 76.29584545454546, 83.92543, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3723, 106.81418181818181, 117.4956, 222);
INSERT INTO building(id, name, level) VALUES (3724, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3724, 5.0, 3.076213788085099, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3724, 25.0, 15.381068940425497, 1);
INSERT INTO building(id, name, level) VALUES (3725, "building_subsistence_farmslevel", 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3725, 80.73071818181818, 88.80379, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3725, 13.45511818181818, 14.80063, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3725, 13.45511818181818, 14.80063, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3725, 13.45511818181818, 14.80063, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3725, 13.45511818181818, 14.80063, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3725, 13.45511818181818, 14.80063, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3725, 18.837163636363634, 20.72088, 56);
INSERT INTO building(id, name, level) VALUES (3726, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3726, 5.0, 3.076213788085099, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3726, 25.0, 15.381068940425497, 1);
INSERT INTO building(id, name, level) VALUES (3727, "building_subsistence_farmslevel", 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3727, 120.36240000000001, 150.453, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3727, 20.0604, 25.0755, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3727, 20.0604, 25.0755, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3727, 20.0604, 25.0755, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3727, 20.0604, 25.0755, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3727, 20.0604, 25.0755, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3727, 28.08456, 35.1057, 60);
INSERT INTO building(id, name, level) VALUES (3728, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3728, 10.0, 6.152427576170198, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3728, 10.0, 2.093142854091738, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3728, 79.84, 32.916317157605654, 2);
INSERT INTO building(id, name, level) VALUES (3729, "building_subsistence_farmslevel", 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3729, 127.11854545454544, 139.8304, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3729, 21.18641818181818, 23.30506, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3729, 21.18641818181818, 23.30506, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3729, 21.18641818181818, 23.30506, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3729, 21.18641818181818, 23.30506, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3729, 21.18641818181818, 23.30506, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3729, 29.66099090909091, 32.62709, 65);
INSERT INTO building(id, name, level) VALUES (3730, "building_subsistence_farmslevel", 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3730, 341.68991818181814, 375.85891, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3730, 56.94831818181817, 62.64315, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3730, 56.94831818181817, 62.64315, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3730, 56.94831818181817, 62.64315, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3730, 56.94831818181817, 62.64315, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3730, 56.94831818181817, 62.64315, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3730, 79.72764545454545, 87.70041, 176);
INSERT INTO building(id, name, level) VALUES (3731, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3731, 15.0, 9.2286413642553, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3731, 15.0, 3.139714281137607, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3731, 119.75999999999999, 49.37447573640848, 3);
INSERT INTO building(id, name, level) VALUES (3732, "building_subsistence_farmslevel", 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3732, 132.72932727272726, 146.00226, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3732, 22.121554545454543, 24.33371, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3732, 22.121554545454543, 24.33371, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3732, 22.121554545454543, 24.33371, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3732, 22.121554545454543, 24.33371, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3732, 22.121554545454543, 24.33371, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3732, 30.970172727272722, 34.06719, 73);
INSERT INTO building(id, name, level) VALUES (3733, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3733, 5.0, 3.076213788085099, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3733, 25.0, 15.381068940425497, 1);
INSERT INTO building(id, name, level) VALUES (3734, "building_subsistence_farmslevel", 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3734, 257.7719636363636, 283.54916, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3734, 42.96199090909091, 47.25819, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3734, 42.96199090909091, 47.25819, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3734, 42.96199090909091, 47.25819, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3734, 42.96199090909091, 47.25819, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3734, 42.96199090909091, 47.25819, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3734, 60.146790909090896, 66.16147, 141);
INSERT INTO building(id, name, level) VALUES (3735, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3735, 10.0, 6.152427576170198, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3735, 50.0, 30.762137880850993, 2);
INSERT INTO building(id, name, level) VALUES (3828, "building_subsistence_farmslevel", 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3828, 178.33499999999998, 196.1685, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3828, 29.722499999999997, 32.69475, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3828, 29.722499999999997, 32.69475, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3828, 29.722499999999997, 32.69475, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3828, 29.722499999999997, 32.69475, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3828, 29.722499999999997, 32.69475, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3828, 41.61149999999999, 45.77265, 100);
INSERT INTO building(id, name, level) VALUES (3829, "building_subsistence_farmslevel", 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3829, 157.99853636363636, 173.79839, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3829, 26.333081818181817, 28.96639, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3829, 26.333081818181817, 28.96639, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3829, 26.333081818181817, 28.96639, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3829, 26.333081818181817, 28.96639, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3829, 26.333081818181817, 28.96639, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3829, 36.86631818181818, 40.55295, 61);
INSERT INTO building(id, name, level) VALUES (3830, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3830, 5.0, 3.076213788085099, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3830, 25.0, 15.381068940425497, 1);
INSERT INTO building(id, name, level) VALUES (3831, "building_subsistence_farmslevel", 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3831, 204.43895454545452, 224.88285, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3831, 34.07315454545454, 37.48047, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3831, 34.07315454545454, 37.48047, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3831, 34.07315454545454, 37.48047, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3831, 34.07315454545454, 37.48047, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3831, 34.07315454545454, 37.48047, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3831, 47.702418181818175, 52.47266, 83);
INSERT INTO building(id, name, level) VALUES (3832, "building_subsistence_farmslevel", 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3832, 297.2212727272727, 326.9434, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3832, 49.53687272727272, 54.49056, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3832, 49.53687272727272, 54.49056, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3832, 49.53687272727272, 54.49056, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3832, 49.53687272727272, 54.49056, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3832, 49.53687272727272, 54.49056, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3832, 69.35162727272727, 76.28679, 122);
INSERT INTO building(id, name, level) VALUES (3833, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3833, 5.0, 3.076213788085099, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3833, 25.0, 15.381068940425497, 1);
INSERT INTO building(id, name, level) VALUES (3834, "building_subsistence_farmslevel", 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3834, 147.0069, 161.70759, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3834, 24.501145454545455, 26.95126, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3834, 24.501145454545455, 26.95126, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3834, 24.501145454545455, 26.95126, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3834, 24.501145454545455, 26.95126, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3834, 24.501145454545455, 26.95126, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3834, 34.30160909090909, 37.73177, 90);
INSERT INTO building(id, name, level) VALUES (3835, "building_subsistence_farmslevel", 253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3835, 457.66940800000003, 572.08676, 253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3835, 76.278232, 95.34779, 253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3835, 76.278232, 95.34779, 253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3835, 76.278232, 95.34779, 253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3835, 76.278232, 95.34779, 253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3835, 76.278232, 95.34779, 253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3835, 106.78952799999999, 133.48691, 253);
INSERT INTO building(id, name, level) VALUES (3836, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3836, 25.0, 15.381068940425498, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3836, 25.0, 5.232857135229345, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3836, 199.6, 82.29079289401413, 5);
INSERT INTO building(id, name, level) VALUES (3870, "building_trade_centerlevel", 73);
INSERT INTO building(id, name, level) VALUES (3920, "building_trade_centerlevel", 11);
INSERT INTO building(id, name, level) VALUES (3921, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3921, 5.0, 3.076213788085099, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3921, 5.0, 1.046571427045869, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3921, 39.92, 16.458158578802827, 1);
INSERT INTO building(id, name, level) VALUES (3951, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4371, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4372, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4373, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4448, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4449, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4450, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4451, "building_conscription_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (4458, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4459, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4460, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4461, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4462, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4463, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4464, "building_conscription_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (4465, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4466, "building_conscription_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4467, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4468, "building_conscription_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (4469, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4470, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4471, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4473, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4474, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4475, "building_conscription_centerlevel", 15);
INSERT INTO building(id, name, level) VALUES (4476, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4477, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4478, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4479, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4480, "building_conscription_centerlevel", 23);
INSERT INTO building(id, name, level) VALUES (4481, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4482, "building_conscription_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (4531, "building_conscription_centerlevel", 11);
INSERT INTO building(id, name, level) VALUES (4532, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4533, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4534, "building_conscription_centerlevel", 12);
INSERT INTO building(id, name, level) VALUES (4535, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4536, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4712, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 4712, 5.0, 2.442761900314365, 1);
INSERT INTO building(id, name, level) VALUES (4728, "building_subsistence_pastureslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 4728, 4.089799999999999, 4.49878, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 4728, 6.1347, 6.74817, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 4728, 2.0448999999999997, 2.24939, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 4728, 4.089799999999999, 4.49878, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 4728, 4.089799999999999, 4.49878, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4728, 4.089799999999999, 4.49878, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 4728, 13.578127272727272, 14.93594, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 4728, 5.7257181818181815, 6.29829, 44);
INSERT INTO building(id, name, level) VALUES (4764, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4764, 30.0, 18.4572827285106, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4764, 20.0, 0.9392284255720598, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4764, 60.0, 19.86612536686869, 1);
INSERT INTO building(id, name, level) VALUES (4779, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 4779, 5.0, 2.442761900314365, 1);
INSERT INTO building(id, name, level) VALUES (4782, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4782, 30.0, 18.4572827285106, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 4782, 30.0, 18.4572827285106, 1);
INSERT INTO building(id, name, level) VALUES (4785, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 4785, 10.0, 4.88552380062873, 1);
INSERT INTO building(id, name, level) VALUES (4790, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 4790, 20.0, 9.77104760125746, 2);
INSERT INTO building(id, name, level) VALUES (33559225, "building_subsistence_pastureslevel", 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 33559225, 6.265854545454545, 6.89244, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 33559225, 9.398781818181819, 10.33866, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 33559225, 3.1329272727272723, 3.44622, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 33559225, 6.265854545454545, 6.89244, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 33559225, 6.265854545454545, 6.89244, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 33559225, 6.265854545454545, 6.89244, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 33559225, 20.802654545454544, 22.88292, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 33559225, 8.772199999999998, 9.64942, 34);
INSERT INTO building(id, name, level) VALUES (4807, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 4807, 30.0, 14.656571401886188, 3);
INSERT INTO building(id, name, level) VALUES (50336706, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 50336706, 0.70446, 1.0869428114362765, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50336706, 1.40892, 0.8668278260617716, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 50336706, 1.76115, 0.08270610708481166, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50336706, 0.35223, 0.03561200918458868, 1);
INSERT INTO building(id, name, level) VALUES (83891188, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 83891188, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 83891188, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 83891188, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 83891188, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (5127, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (5177, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5177, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5177, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5177, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5177, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (33559657, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33559657, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559657, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33559657, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33559657, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (167777448, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 167777448, 9.936, 15.330698370994584, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 167777448, 19.872, 12.226104079365419, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 167777448, 24.84, 1.166521704560498, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 167777448, 4.968, 0.5022867490816698, 2);
INSERT INTO building(id, name, level) VALUES (16782508, "building_gold_fieldslevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 16782508, 16.12, 16.12, 1);
INSERT INTO building(id, name, level) VALUES (5338, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5338, 9.936, 15.330698370994584, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5338, 19.872, 12.226104079365419, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5338, 24.84, 1.166521704560498, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5338, 4.968, 0.5022867490816698, 2);
INSERT INTO building(id, name, level) VALUES (16782572, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782572, 4.96501, 7.66073578089491, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782572, 9.93003, 6.109379040419736, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782572, 12.41254, 0.5829105200775108, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782572, 2.4825, 0.2509917179136967, 1);
INSERT INTO building(id, name, level) VALUES (16782575, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782575, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782575, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782575, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782575, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (16782579, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782579, 14.904, 22.996047556491874, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782579, 29.808, 18.339156119048127, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782579, 37.26, 1.7497825568407472, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782579, 7.452, 0.7534301236225047, 3);
INSERT INTO building(id, name, level) VALUES (16782584, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782584, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782584, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782584, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782584, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (16782586, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782586, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782586, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782586, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782586, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (16782590, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782590, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782590, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782590, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782590, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (67114248, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 67114248, 5.0, 4.789738621556784, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 67114248, 50.0, 47.89738621556784, 1);
INSERT INTO building(id, name, level) VALUES (67114260, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 67114260, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 67114260, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 67114260, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 67114260, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (83891502, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 83891502, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 83891502, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 83891502, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 83891502, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (100668722, "building_subsistence_farmslevel", 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 100668722, 71.35199999999999, 78.4872, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 100668722, 11.892, 13.0812, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 100668722, 11.892, 13.0812, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 100668722, 11.892, 13.0812, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 100668722, 11.892, 13.0812, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 100668722, 11.892, 13.0812, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 100668722, 16.6488, 18.31368, 24);
INSERT INTO building(id, name, level) VALUES (33559872, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33559872, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559872, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33559872, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33559872, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (33559873, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33559873, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559873, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33559873, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33559873, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (5451, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5451, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5451, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5451, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5451, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (5460, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 5460, 39.3724, 49.2155, 1);
INSERT INTO building(id, name, level) VALUES (16782678, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16782678, 5.0, 2.442761900314365, 1);
INSERT INTO building(id, name, level) VALUES (151000410, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (67114331, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 67114331, 21.51, 19.359, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 67114331, 3.585, 3.2265, 1);
INSERT INTO building(id, name, level) VALUES (5469, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 5469, 9.16, 4.475139801375915, 1);
INSERT INTO building(id, name, level) VALUES (16782689, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16782689, 5.0, 2.442761900314365, 1);
INSERT INTO building(id, name, level) VALUES (16782705, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782705, 5.0, 0.5055220904606177, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16782705, 40.0, 4.0441767236849415, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 16782705, 10.0, 1.0110441809212354, 1);
INSERT INTO building(id, name, level) VALUES (16782848, "building_gold_fieldslevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 16782848, 56.08, 58.3232, 5);
INSERT INTO building(id, name, level) VALUES (352327293, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 352327293, 39.3724, 49.2155, 1);
INSERT INTO building(id, name, level) VALUES (5777, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 5777, 5.0, 2.442761900314365, 1);
INSERT INTO building(id, name, level) VALUES (5888, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 5888, 10.0, 4.88552380062873, 1);
INSERT INTO building(id, name, level) VALUES (6021, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 6021, 10.0, 4.88552380062873, 1);
INSERT INTO building(id, name, level) VALUES (33560508, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (6134, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 6134, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6134, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 6134, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 6134, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (6170, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 6170, 4.968, 7.665349185497292, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6170, 9.936, 6.113052039682709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 6170, 12.42, 0.583260852280249, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 6170, 2.484, 0.2511433745408349, 1);
INSERT INTO building(id, name, level) VALUES (67115136, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 67115136, 5.0, 2.442761900314365, 1);
INSERT INTO building(id, name, level) VALUES (16783559, "building_naval_baselevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 16783559, 0.49596, 0.32468426582859417, 3);
INSERT INTO building(id, name, level) VALUES (50337996, "building_naval_baselevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 50337996, 0.14, 0.09165214375353495, 1);
INSERT INTO building(id, name, level) VALUES (6363, "building_naval_baselevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 6363, 0.128, 0.08379624571751766, 1);
INSERT INTO building(id, name, level) VALUES (16783583, "building_naval_baselevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 16783583, 0.116, 0.07594034768150038, 1);
INSERT INTO building(id, name, level) VALUES (16783587, "building_naval_baselevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 16783587, 0.244, 0.15973659339901802, 2);
INSERT INTO building(id, name, level) VALUES (6423, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (6442, "building_subsistence_rice_paddieslevel", 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6442, 7.843436363636362, 8.62778, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6442, 1.0695545454545454, 1.17651, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6442, 1.0695545454545454, 1.17651, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6442, 1.4260727272727272, 1.56868, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6442, 1.4260727272727272, 1.56868, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6442, 1.4260727272727272, 1.56868, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6442, 2.1391181818181817, 2.35303, 24);
