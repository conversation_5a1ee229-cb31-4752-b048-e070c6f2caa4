
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 14.08431850385802, 1371.4091768408105);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 6.929431819134302, 31.456697993786086);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 14.018781924804227, 175.57126728110597);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 25.544589592261346, 103.82114271889402);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 24.403671196475255, 277.08557927204276);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 21.645002339472626, 231.86538128205183);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 52.5, 14.914360702468581);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 16.35015404256686, 157.46934916666672);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 68.31688438839201, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 19.815077074849366, 7.218211927600159);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 86.24146474275219, 79.86311131504213);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 45.263799181526295, 46.763627556618175);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 47.91838574711023, 89.80406845479978);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 23.58253979569159, 395.4383462319206);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 87.5, 109.51760000000002);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 29.971498288448824, 27.395093449441116);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 11.237399108858256, 8.319034208854037);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 87.5, 67.76626939376432);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 27.849597967410435);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 70.43460549837374);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (2683, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2683, 30.0, 0.0, 3);
INSERT INTO building(id, name, level) VALUES (2684, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2684, 25.0, 41.57989439445729, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2684, 75.0, 47.27705203869327, 1);
INSERT INTO building(id, name, level) VALUES (2685, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2685, 90.0, 56.732462446431924, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2685, 45.0, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2685, 30.0, 9.455410407738654, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2685, 75.0, 23.638526019346635, 3);
INSERT INTO building(id, name, level) VALUES (2686, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2686, 10.0, 8.151803469246218, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2686, 100.0, 81.51803469246218, 2);
INSERT INTO building(id, name, level) VALUES (2687, "building_rice_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2687, 59.76, 60.9552, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2687, 17.928, 18.28656, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2687, 26.892, 27.42984, 3);
INSERT INTO building(id, name, level) VALUES (2688, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2688, 74.7, 76.194, 3);
INSERT INTO building(id, name, level) VALUES (2689, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (2690, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2690, 10.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (2691, "building_shipyardslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2691, 20.0, 33.263915515565834, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2691, 40.0, 25.214427753969744, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 2691, 35.0, 28.531312142361763, 1);
INSERT INTO building(id, name, level) VALUES (2692, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2692, 30.0, 18.910820815477308, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2692, 15.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2692, 10.0, 3.151803469246218, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2692, 25.0, 7.879508673115546, 1);
INSERT INTO building(id, name, level) VALUES (2693, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2693, 10.0, 8.151803469246218, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2693, 100.0, 81.51803469246218, 2);
INSERT INTO building(id, name, level) VALUES (2694, "building_rice_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2694, 34.86, 34.86, 1);
INSERT INTO building(id, name, level) VALUES (2695, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2695, 49.800000000000004, 50.298, 2);
INSERT INTO building(id, name, level) VALUES (2696, "building_barrackslevel", 3);
INSERT INTO building(id, name, level) VALUES (2697, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2697, 5.0, 4.075901734623109, 1);
INSERT INTO building(id, name, level) VALUES (2698, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2698, 20.0, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (2699, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2699, 30.0, 18.910820815477308, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2699, 15.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2699, 10.0, 3.151803469246218, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2699, 25.0, 7.879508673115546, 1);
INSERT INTO building(id, name, level) VALUES (2700, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2700, 10.0, 8.151803469246218, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2700, 100.0, 81.51803469246218, 2);
INSERT INTO building(id, name, level) VALUES (2701, "building_rice_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2701, 34.86, 34.86, 1);
INSERT INTO building(id, name, level) VALUES (2702, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2702, 24.9, 24.9, 1);
INSERT INTO building(id, name, level) VALUES (2703, "building_barrackslevel", 2);
INSERT INTO building(id, name, level) VALUES (2704, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2704, 10.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (2705, "building_rice_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2705, 34.86, 34.86, 1);
INSERT INTO building(id, name, level) VALUES (2706, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2706, 29.88, 29.88, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2706, 4.98, 4.98, 1);
INSERT INTO building(id, name, level) VALUES (2707, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2707, 49.800000000000004, 50.298, 2);
INSERT INTO building(id, name, level) VALUES (2708, "building_barrackslevel", 5);
INSERT INTO building(id, name, level) VALUES (2709, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2709, 20.0, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (2710, "building_glassworkslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2710, 60.0, 37.821641630954616, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2710, 30.0, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2710, 20.0, 6.303606938492436, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2710, 50.0, 15.759017346231092, 2);
INSERT INTO building(id, name, level) VALUES (2711, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2711, 5.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2711, 20.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (2712, "building_rice_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2712, 69.72, 70.4172, 2);
INSERT INTO building(id, name, level) VALUES (2713, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2713, 59.76, 60.3576, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2713, 9.959999999999999, 10.0596, 2);
INSERT INTO building(id, name, level) VALUES (2714, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (3671, "building_subsistence_rice_paddieslevel", 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3671, 336.61056, 336.61056, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3671, 45.90144, 45.90144, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3671, 45.90144, 45.90144, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3671, 61.20192, 61.20192, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3671, 61.20192, 61.20192, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3671, 61.20192, 61.20192, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3671, 91.80288, 91.80288, 64);
INSERT INTO building(id, name, level) VALUES (3672, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3672, 5.0, 3.151803469246218, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3672, 25.0, 15.759017346231092, 1);
INSERT INTO building(id, name, level) VALUES (3673, "building_subsistence_rice_paddieslevel", 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3673, 439.208, 439.208, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3673, 59.892, 59.892, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3673, 59.892, 59.892, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3673, 79.856, 79.856, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3673, 79.856, 79.856, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3673, 79.856, 79.856, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3673, 119.784, 119.784, 80);
INSERT INTO building(id, name, level) VALUES (3674, "building_subsistence_rice_paddieslevel", 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3674, 592.812, 592.812, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3674, 80.838, 80.838, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3674, 80.838, 80.838, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3674, 107.784, 107.784, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3674, 107.784, 107.784, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3674, 107.784, 107.784, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3674, 161.676, 161.676, 108);
INSERT INTO building(id, name, level) VALUES (3675, "building_subsistence_rice_paddieslevel", 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3675, 197.604, 197.604, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3675, 26.946, 26.946, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3675, 26.946, 26.946, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3675, 35.928, 35.928, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3675, 35.928, 35.928, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3675, 35.928, 35.928, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3675, 53.892, 53.892, 36);
INSERT INTO building(id, name, level) VALUES (3676, "building_subsistence_rice_paddieslevel", 96);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3676, 462.27984, 462.27984, 96);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3676, 63.03816, 63.03816, 96);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3676, 63.03816, 63.03816, 96);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3676, 84.05088, 84.05088, 96);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3676, 84.05088, 84.05088, 96);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3676, 84.05088, 84.05088, 96);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3676, 126.07632, 126.07632, 96);
INSERT INTO building(id, name, level) VALUES (3677, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3677, 5.0, 3.151803469246218, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3677, 25.0, 15.759017346231092, 1);
INSERT INTO building(id, name, level) VALUES (4495, "building_conscription_centerlevel", 13);
INSERT INTO building(id, name, level) VALUES (4496, "building_conscription_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (4497, "building_conscription_centerlevel", 22);
INSERT INTO building(id, name, level) VALUES (4498, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4499, "building_conscription_centerlevel", 17);
