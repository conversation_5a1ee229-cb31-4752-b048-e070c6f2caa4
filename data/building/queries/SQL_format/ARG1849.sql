
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 87.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 26.80479822533146, 294.566664296394);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 21.72765421923763, 28.158172351243017);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 10.01769555742015, 55.46335873493728);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 15.952020178416987, 15.84022001506271);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 37.9529130506387, 75.17072516350167);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 38.04074901088979, 75.57134707897085);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 38.762605369815006, 46.844040833333345);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 10.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 1.2097609523809525);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 18.70014655698647, 21.8979839633423);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 19.332838601885406, 30.69739676285469);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 14.422635679022404, 1.55096575788999);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 87.5, 7.092602000000002);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 7.84617642940968, 0.39598521721029284);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 29.337701991059582, 91.54085880658249);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 5.6455511111111125);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 1.4113877777777781);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (1806, "building_government_administrationlevel", 3);
INSERT INTO building(id, name, level) VALUES (1807, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1807, 59.61599999999999, 75.11616, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1807, 9.936, 12.51936, 2);
INSERT INTO building(id, name, level) VALUES (1808, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1808, 24.915, 24.915, 1);
INSERT INTO building(id, name, level) VALUES (1809, "building_naval_baselevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1809, 8.0, 0.0, 4);
INSERT INTO building(id, name, level) VALUES (1810, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1810, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (1811, "building_barrackslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1811, 4.0, 0.0, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1811, 4.0, 0.0, 4);
INSERT INTO building(id, name, level) VALUES (1812, "building_government_administrationlevel", 1);
INSERT INTO building(id, name, level) VALUES (1813, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1813, 59.99999999999999, 66.6, 2);
INSERT INTO building(id, name, level) VALUES (1814, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1814, 40.0, 119.57680435847129, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1814, 45.0, 45.0, 1);
INSERT INTO building(id, name, level) VALUES (1815, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1815, 51.006, 56.61666, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1815, 8.501, 9.43611, 2);
INSERT INTO building(id, name, level) VALUES (1816, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1816, 2.99799, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1816, 2.99799, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1816, 2.99799, 0.0, 3);
INSERT INTO building(id, name, level) VALUES (1817, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1817, 9.992, 29.870285728746133, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1817, 29.976, 41.055439332197366, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1817, 44.964, 44.964, 1);
INSERT INTO building(id, name, level) VALUES (1818, "building_logging_camplevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1818, 29.999999999999996, 33.0, 1);
INSERT INTO building(id, name, level) VALUES (1819, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1819, 29.939999999999998, 37.425, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1819, 4.99, 6.2375, 1);
INSERT INTO building(id, name, level) VALUES (1820, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1820, 74.57939370078739, 94.71583, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1820, 12.429897637795277, 15.78597, 3);
INSERT INTO building(id, name, level) VALUES (1821, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1821, 66.32324509803921, 67.64971, 3);
INSERT INTO building(id, name, level) VALUES (1822, "building_gold_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1822, 9.959999999999999, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 1822, 19.718369369369366, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (33556445, "building_logging_camplevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 33556445, 29.999999999999996, 33.0, 1);
INSERT INTO building(id, name, level) VALUES (3105, "building_subsistence_farmslevel", 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3105, 4.209624, 5.26203, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3105, 0.84192, 1.0524, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3105, 0.84192, 1.0524, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3105, 0.84192, 1.0524, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3105, 0.84192, 1.0524, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3105, 0.84192, 1.0524, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3105, 1.178688, 1.47336, 35);
INSERT INTO building(id, name, level) VALUES (3109, "building_subsistence_farmslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3109, 6.543450000000001, 7.85214, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3109, 1.3086833333333334, 1.57042, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3109, 1.3086833333333334, 1.57042, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3109, 1.3086833333333334, 1.57042, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3109, 1.3086833333333334, 1.57042, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3109, 1.3086833333333334, 1.57042, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3109, 1.8321583333333333, 2.19859, 6);
INSERT INTO building(id, name, level) VALUES (3111, "building_subsistence_farmslevel", 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3111, 2.0439, 2.24829, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3111, 0.40877272727272723, 0.44965, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3111, 0.40877272727272723, 0.44965, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3111, 0.40877272727272723, 0.44965, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3111, 0.40877272727272723, 0.44965, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3111, 0.40877272727272723, 0.44965, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3111, 0.572290909090909, 0.62952, 54);
INSERT INTO building(id, name, level) VALUES (3113, "building_subsistence_farmslevel", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3113, 1.714496, 2.14312, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3113, 0.342896, 0.42862, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3113, 0.342896, 0.42862, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3113, 0.342896, 0.42862, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3113, 0.342896, 0.42862, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3113, 0.342896, 0.42862, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3113, 0.480056, 0.60007, 30);
INSERT INTO building(id, name, level) VALUES (3114, "building_subsistence_farmslevel", 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3114, 0.004344, 0.00543, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3114, 0.000864, 0.00108, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3114, 0.000864, 0.00108, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3114, 0.000864, 0.00108, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3114, 0.000864, 0.00108, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3114, 0.000864, 0.00108, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3114, 0.001216, 0.00152, 58);
INSERT INTO building(id, name, level) VALUES (3115, "building_subsistence_farmslevel", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3115, 0.0029000000000000002, 0.00348, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3115, 0.000575, 0.00069, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3115, 0.000575, 0.00069, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3115, 0.000575, 0.00069, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3115, 0.000575, 0.00069, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3115, 0.000575, 0.00069, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3115, 0.0008083333333333334, 0.00097, 29);
INSERT INTO building(id, name, level) VALUES (3117, "building_subsistence_farmslevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3117, 0.3888, 0.46656, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3117, 0.07775833333333335, 0.09331, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3117, 0.07775833333333335, 0.09331, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3117, 0.07775833333333335, 0.09331, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3117, 0.07775833333333335, 0.09331, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3117, 0.07775833333333335, 0.09331, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3117, 0.10885833333333333, 0.13063, 12);
INSERT INTO building(id, name, level) VALUES (3119, "building_subsistence_farmslevel", 18);
INSERT INTO building(id, name, level) VALUES (3891, "building_trade_centerlevel", 18);
INSERT INTO building(id, name, level) VALUES (3892, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3892, 5.0, 6.848051663363585, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3892, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (33558524, "building_sugar_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 33558524, 8.646294642857141, 9.68385, 3);
INSERT INTO building(id, name, level) VALUES (4569, "building_sugar_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 4569, 14.594099999999997, 16.05351, 1);
INSERT INTO building(id, name, level) VALUES (50336609, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 50336609, 3.0, 3.75, 1);
INSERT INTO building(id, name, level) VALUES (16782443, "building_dye_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 16782443, 24.592999999999996, 27.0523, 1);
INSERT INTO building(id, name, level) VALUES (83891388, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 83891388, 66.39675, 74.36436, 3);
INSERT INTO building(id, name, level) VALUES (16782871, "building_dye_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 16782871, 34.647, 38.45817, 2);
INSERT INTO building(id, name, level) VALUES (50337378, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 50337378, 26.5293, 29.18223, 1);
INSERT INTO building(id, name, level) VALUES (16783090, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16783090, 35.388, 47.7738, 1);
INSERT INTO building(id, name, level) VALUES (5929, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 5929, 26.535, 29.1885, 1);
INSERT INTO building(id, name, level) VALUES (6049, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6049, 60.0, 72.6, 2);
INSERT INTO building(id, name, level) VALUES (16783356, "building_dye_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 16783356, 19.078245454545453, 20.98607, 1);
INSERT INTO building(id, name, level) VALUES (6565, "building_subsistence_pastureslevel", 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6565, 0.0036, 0.0045, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6565, 0.0054, 0.00675, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6565, 0.0018, 0.00225, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6565, 0.0036, 0.0045, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6565, 0.0036, 0.0045, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6565, 0.0036, 0.0045, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 6565, 0.009576, 0.01197, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6565, 0.00504, 0.0063, 36);
INSERT INTO building(id, name, level) VALUES (6566, "building_subsistence_pastureslevel", 6);
INSERT INTO building(id, name, level) VALUES (6567, "building_subsistence_pastureslevel", 29);
INSERT INTO building(id, name, level) VALUES (6568, "building_subsistence_pastureslevel", 18);
