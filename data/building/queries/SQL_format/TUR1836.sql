
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 66.42857142857143, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 97.30000000000001, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 77.5368510188762, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 20.794359177131145, 2550.731869044088);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 17.810068853077222, 541.438910190064);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 21.45196890087378, 294.27191697441975);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 18.204805939326295, 399.6932142755795);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 39.21041336404975, 50.269063788555556);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 23.96649982482502, 744.1997625694739);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 30.62119453042651, 568.0633871977425);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 35.141407222092134, 19.290083174350976);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 12.25175844460475, 152.21869166666656);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 62.17795335131068, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 32.36669450077538, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 45.2003220440554, 24.19386256006511);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 38.75, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 103.61227846672905, 138.979177312416);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 40.14826858532595, 173.7773882008139);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 30.951330872155175, 244.05976159354543);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 23.803705771479482, 387.64858516512567);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 52.47767746682601, 8.532986346548743);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 28.8683786608022, 186.4182860446127);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 87.5, 52.91552260883856);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 35.63388532995366, 16.602603425639856);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 38.4626587061344, 576.8982442511557);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 74.00828034339365, 165.807049585496);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 71.97272948258492, 131.85476855001872);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (837, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 837, 98.99999999999999, 76.3778082227946, 10);
INSERT INTO building(id, name, level) VALUES (838, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 838, 50.0, 45.16010366375407, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 838, 150.0, 170.39250787465903, 2);
INSERT INTO building(id, name, level) VALUES (839, "building_shipyardslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 839, 60.0, 54.19212439650489, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 839, 120.0, 136.31400629972723, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 839, 45.0, 42.82204664868933, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 839, 60.0, 57.09606219825244, 3);
INSERT INTO building(id, name, level) VALUES (840, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 840, 90.0, 81.28818659475732, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 840, 30.0, 40.2384, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 840, 75.0, 71.37007774781556, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 840, 60.0, 57.09606219825244, 3);
INSERT INTO building(id, name, level) VALUES (841, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 841, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (842, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 842, 49.800000000000004, 50.298, 2);
INSERT INTO building(id, name, level) VALUES (843, "building_silk_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 843, 39.839999999999996, 40.2384, 2);
INSERT INTO building(id, name, level) VALUES (844, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 844, 29.88, 30.4776, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 844, 26.892, 27.42984, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 844, 17.928, 18.28656, 3);
INSERT INTO building(id, name, level) VALUES (845, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 845, 20.0, 17.142857142857142, 20);
INSERT INTO building(id, name, level) VALUES (846, "building_naval_baselevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 846, 30.0, 25.693227989213597, 15);
INSERT INTO building(id, name, level) VALUES (847, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 847, 15.0, 14.27401554956311, 3);
INSERT INTO building(id, name, level) VALUES (848, "building_hagia_sophialevel", 1);
INSERT INTO building(id, name, level) VALUES (849, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 849, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (850, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 850, 19.919999999999998, 20.1192, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 850, 17.928, 18.10728, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 850, 11.952, 12.07152, 2);
INSERT INTO building(id, name, level) VALUES (851, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 851, 5.0, 5.217391304347826, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 851, 60.0, 60.0, 1);
INSERT INTO building(id, name, level) VALUES (852, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 852, 5.0, 4.758005183187703, 1);
INSERT INTO building(id, name, level) VALUES (853, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 853, 40.0, 36.128082931003256, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 853, 45.0, 40.64409329737866, 1);
INSERT INTO building(id, name, level) VALUES (854, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 854, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (855, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 855, 10.0, 10.434782608695652, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 855, 120.0, 120.0, 2);
INSERT INTO building(id, name, level) VALUES (856, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 856, 5.0, 4.758005183187703, 1);
INSERT INTO building(id, name, level) VALUES (857, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 857, 60.0, 68.15700314986361, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 857, 40.0, 40.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 857, 120.0, 120.0, 2);
INSERT INTO building(id, name, level) VALUES (858, "building_arms_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 858, 40.0, 40.0, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 858, 20.0, 20.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 858, 30.0, 30.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 858, 30.0, 30.0, 2);
INSERT INTO building(id, name, level) VALUES (859, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 859, 10.0, 10.434782608695652, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 859, 40.0, 40.0, 2);
INSERT INTO building(id, name, level) VALUES (860, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 860, 10.0, 10.434782608695652, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 860, 120.0, 120.0, 2);
INSERT INTO building(id, name, level) VALUES (861, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 861, 59.76, 60.3576, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 861, 9.959999999999999, 10.0596, 2);
INSERT INTO building(id, name, level) VALUES (862, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 862, 2.5, 1.2, 5);
INSERT INTO building(id, name, level) VALUES (863, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 863, 20.0, 18.064041465501628, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 863, 40.0, 45.43800209990907, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 863, 20.0, 20.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 863, 50.0, 48.38670122125136, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 863, 40.0, 38.70936097700108, 2);
INSERT INTO building(id, name, level) VALUES (864, "building_textile_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 864, 80.0, 72.25616586200651, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 864, 90.0, 81.28818659475732, 2);
INSERT INTO building(id, name, level) VALUES (865, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 865, 59.76, 60.3576, 2);
INSERT INTO building(id, name, level) VALUES (866, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 866, 74.7, 76.194, 3);
INSERT INTO building(id, name, level) VALUES (867, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 867, 15.0, 15.652173913043478, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 867, 120.0, 120.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 867, 30.0, 30.0, 3);
INSERT INTO building(id, name, level) VALUES (868, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 868, 5.0, 4.285714285714286, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 868, 2.5, 1.2, 5);
INSERT INTO building(id, name, level) VALUES (869, "building_furniture_manufacturieslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 869, 30.0, 27.096062198252444, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 869, 60.0, 68.15700314986361, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 869, 30.0, 30.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 869, 75.0, 72.58005183187703, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 869, 60.0, 58.064041465501624, 3);
INSERT INTO building(id, name, level) VALUES (870, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 870, 59.76, 60.3576, 2);
INSERT INTO building(id, name, level) VALUES (871, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 871, 20.0, 20.869565217391305, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 871, 160.0, 160.0, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 871, 40.0, 40.0, 4);
INSERT INTO building(id, name, level) VALUES (872, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 872, 5.0, 2.4, 10);
INSERT INTO building(id, name, level) VALUES (897, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 897, 29.880000000000003, 34.362, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 897, 4.98, 5.727, 1);
INSERT INTO building(id, name, level) VALUES (905, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 905, 89.64, 91.4328, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 905, 14.94, 15.2388, 3);
INSERT INTO building(id, name, level) VALUES (906, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 906, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (907, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 907, 5.0, 2.4, 10);
INSERT INTO building(id, name, level) VALUES (912, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 912, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (913, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 913, 19.8, 15.275561644558922, 2);
INSERT INTO building(id, name, level) VALUES (914, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 914, 120.0, 108.38424879300977, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 914, 135.0, 121.93227989213601, 3);
INSERT INTO building(id, name, level) VALUES (915, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 915, 59.76, 60.3576, 2);
INSERT INTO building(id, name, level) VALUES (916, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 916, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (917, "building_cotton_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 917, 79.67999999999999, 80.4768, 2);
INSERT INTO building(id, name, level) VALUES (918, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 918, 7.5, 3.6, 15);
INSERT INTO building(id, name, level) VALUES (919, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 919, 10.0, 10.434782608695652, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 919, 40.0, 40.0, 2);
INSERT INTO building(id, name, level) VALUES (920, "building_tobacco_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 920, 99.6, 102.588, 4);
INSERT INTO building(id, name, level) VALUES (921, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 921, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (922, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 922, 5.0, 2.4, 10);
INSERT INTO building(id, name, level) VALUES (923, "building_food_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 923, 40.0, 37.88170886098361, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 923, 40.0, 29.984203857860173, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 923, 35.0, 29.691336814494154, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 923, 60.0, 50.899434539132834, 1);
INSERT INTO building(id, name, level) VALUES (924, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 924, 39.84, 39.84, 1);
INSERT INTO building(id, name, level) VALUES (925, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 925, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (926, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 926, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (927, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 927, 2.5, 1.2, 5);
INSERT INTO building(id, name, level) VALUES (928, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 928, 19.8, 15.275561644558922, 2);
INSERT INTO building(id, name, level) VALUES (929, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 929, 90.0, 81.28818659475732, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 929, 30.0, 40.2384, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 929, 75.0, 71.37007774781556, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 929, 60.0, 57.09606219825244, 3);
INSERT INTO building(id, name, level) VALUES (930, "building_glassworkslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 930, 119.99999999999999, 136.31400629972723, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 930, 59.99999999999999, 0.0, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 930, 40.0, 20.0, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 930, 100.0, 50.0, 4);
INSERT INTO building(id, name, level) VALUES (931, "building_fishing_wharflevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 931, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (932, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 932, 59.76, 60.3576, 2);
INSERT INTO building(id, name, level) VALUES (933, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 933, 74.7, 76.194, 3);
INSERT INTO building(id, name, level) VALUES (934, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 934, 10.0, 8.571428571428571, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 934, 5.0, 2.4, 10);
INSERT INTO building(id, name, level) VALUES (935, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 935, 5.0, 4.758005183187703, 1);
INSERT INTO building(id, name, level) VALUES (1986, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1986, 14.94225, 14.94225, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1986, 7.9692, 7.9692, 1);
INSERT INTO building(id, name, level) VALUES (1987, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (2008, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2008, 40.0, 36.128082931003256, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2008, 45.0, 40.64409329737866, 1);
INSERT INTO building(id, name, level) VALUES (2009, "building_rice_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2009, 69.72, 84.3612, 2);
INSERT INTO building(id, name, level) VALUES (2010, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2010, 74.7, 76.194, 3);
INSERT INTO building(id, name, level) VALUES (2011, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2011, 5.0, 2.4, 10);
INSERT INTO building(id, name, level) VALUES (2012, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2012, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (2013, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2013, 2.5, 1.2, 5);
INSERT INTO building(id, name, level) VALUES (2014, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2014, 29.88, 29.88, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2014, 4.98, 4.98, 1);
INSERT INTO building(id, name, level) VALUES (2015, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2015, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (2016, "building_rice_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2016, 34.86, 41.832, 1);
INSERT INTO building(id, name, level) VALUES (2017, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2017, 24.9, 24.9, 1);
INSERT INTO building(id, name, level) VALUES (2018, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2018, 5.0, 4.758005183187703, 1);
INSERT INTO building(id, name, level) VALUES (2064, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2064, 19.8, 15.275561644558922, 2);
INSERT INTO building(id, name, level) VALUES (2065, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2065, 120.0, 108.38424879300977, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2065, 135.0, 121.93227989213601, 3);
INSERT INTO building(id, name, level) VALUES (2066, "building_paper_millslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2066, 119.99999999999999, 136.31400629972723, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2066, 160.0, 160.0, 4);
INSERT INTO building(id, name, level) VALUES (2067, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2067, 15.0, 15.652173913043478, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2067, 180.0, 180.0, 3);
INSERT INTO building(id, name, level) VALUES (2068, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2068, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (2069, "building_tea_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2069, 79.68, 82.0704, 4);
INSERT INTO building(id, name, level) VALUES (2070, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2070, 19.919999999999998, 20.1192, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2070, 17.928, 18.10728, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2070, 11.952, 12.07152, 2);
INSERT INTO building(id, name, level) VALUES (2071, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 2071, 10.0, 8.564409329737867, 5);
INSERT INTO building(id, name, level) VALUES (2072, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2072, 10.0, 9.516010366375406, 2);
INSERT INTO building(id, name, level) VALUES (2073, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2073, 120.0, 108.38424879300977, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2073, 135.0, 121.93227989213601, 3);
INSERT INTO building(id, name, level) VALUES (2074, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2074, 20.0, 18.064041465501628, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2074, 60.0, 68.15700314986361, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2074, 90.0, 85.64409329737866, 2);
INSERT INTO building(id, name, level) VALUES (2075, "building_fishing_wharflevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2075, 100.0, 103.0, 4);
INSERT INTO building(id, name, level) VALUES (2076, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2076, 89.64, 91.4328, 3);
INSERT INTO building(id, name, level) VALUES (2077, "building_silk_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2077, 39.839999999999996, 40.2384, 2);
INSERT INTO building(id, name, level) VALUES (2078, "building_tea_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2078, 19.92, 19.92, 1);
INSERT INTO building(id, name, level) VALUES (2079, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2079, 24.9, 24.9, 1);
INSERT INTO building(id, name, level) VALUES (2080, "building_cotton_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2080, 79.67999999999999, 80.4768, 2);
INSERT INTO building(id, name, level) VALUES (2081, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 2081, 10.0, 8.564409329737867, 5);
INSERT INTO building(id, name, level) VALUES (2082, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2082, 10.0, 9.516010366375406, 2);
INSERT INTO building(id, name, level) VALUES (2083, "building_cotton_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2083, 79.67999999999999, 80.4768, 2);
INSERT INTO building(id, name, level) VALUES (2084, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2084, 74.7, 76.194, 3);
INSERT INTO building(id, name, level) VALUES (2085, "building_tea_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2085, 39.839999999999996, 40.2384, 2);
INSERT INTO building(id, name, level) VALUES (2086, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2086, 29.88, 29.88, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2086, 4.98, 4.98, 1);
INSERT INTO building(id, name, level) VALUES (2087, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2087, 2.5, 1.2, 5);
INSERT INTO building(id, name, level) VALUES (2088, "building_tea_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2088, 99.6, 103.584, 5);
INSERT INTO building(id, name, level) VALUES (2089, "building_fishing_wharflevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2089, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (2090, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2090, 2.5, 1.2, 5);
INSERT INTO building(id, name, level) VALUES (2091, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2091, 29.88, 29.88, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2091, 4.98, 4.98, 1);
INSERT INTO building(id, name, level) VALUES (2092, "building_tea_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2092, 39.839999999999996, 40.2384, 2);
INSERT INTO building(id, name, level) VALUES (2093, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2093, 49.800000000000004, 50.298, 2);
INSERT INTO building(id, name, level) VALUES (2094, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2094, 5.0, 2.4, 10);
INSERT INTO building(id, name, level) VALUES (2100, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2100, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (2101, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2101, 29.88, 29.88, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2101, 4.98, 4.98, 1);
INSERT INTO building(id, name, level) VALUES (2102, "building_tea_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2102, 59.76, 60.9552, 3);
INSERT INTO building(id, name, level) VALUES (2103, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2103, 10.0, 10.434782608695652, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2103, 120.0, 120.0, 2);
INSERT INTO building(id, name, level) VALUES (2104, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2104, 5.0, 4.758005183187703, 1);
INSERT INTO building(id, name, level) VALUES (2105, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2105, 10.0, 10.434782608695652, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2105, 120.0, 120.0, 2);
INSERT INTO building(id, name, level) VALUES (2106, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2106, 29.88, 29.88, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2106, 4.98, 4.98, 1);
INSERT INTO building(id, name, level) VALUES (2107, "building_tea_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2107, 19.92, 19.92, 1);
INSERT INTO building(id, name, level) VALUES (2108, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2108, 2.5, 1.2, 5);
INSERT INTO building(id, name, level) VALUES (2109, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2109, 59.76, 60.3576, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2109, 9.959999999999999, 10.0596, 2);
INSERT INTO building(id, name, level) VALUES (2110, "building_tea_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2110, 19.92, 19.92, 1);
INSERT INTO building(id, name, level) VALUES (2111, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2111, 2.5, 1.2, 5);
INSERT INTO building(id, name, level) VALUES (2112, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2112, 29.7, 22.913342466838383, 3);
INSERT INTO building(id, name, level) VALUES (2113, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2113, 59.76, 60.3576, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2113, 9.959999999999999, 10.0596, 2);
INSERT INTO building(id, name, level) VALUES (2114, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2114, 49.800000000000004, 50.298, 2);
INSERT INTO building(id, name, level) VALUES (2115, "building_tea_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2115, 39.839999999999996, 40.2384, 2);
INSERT INTO building(id, name, level) VALUES (2116, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2116, 5.0, 2.4, 10);
INSERT INTO building(id, name, level) VALUES (2891, "building_subsistence_farmslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2891, 16.9632, 16.9632, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2891, 2.8272, 2.8272, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2891, 2.8272, 2.8272, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2891, 2.8272, 2.8272, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2891, 2.8272, 2.8272, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2891, 2.8272, 2.8272, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2891, 3.95808, 3.95808, 6);
INSERT INTO building(id, name, level) VALUES (2893, "building_subsistence_orchardslevel", 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2893, 8.575558333333333, 10.29067, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2893, 4.287775000000001, 5.14533, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2893, 12.863333333333333, 15.436, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2893, 8.575558333333333, 10.29067, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2893, 8.575558333333333, 10.29067, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2893, 8.575558333333333, 10.29067, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2893, 28.470858333333336, 34.16503, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2893, 12.005783333333333, 14.40694, 28);
INSERT INTO building(id, name, level) VALUES (2894, "building_subsistence_farmslevel", 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2894, 68.724, 68.724, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2894, 11.454, 11.454, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2894, 11.454, 11.454, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2894, 11.454, 11.454, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2894, 11.454, 11.454, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2894, 11.454, 11.454, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2894, 16.0356, 16.0356, 23);
INSERT INTO building(id, name, level) VALUES (2895, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2895, 25.0, 28.39875131244317, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2895, 125.0, 125.0, 5);
INSERT INTO building(id, name, level) VALUES (3019, "building_subsistence_farmslevel", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3019, 149.4, 171.81, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3019, 24.900000000000002, 28.635, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3019, 24.900000000000002, 28.635, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3019, 24.900000000000002, 28.635, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3019, 24.900000000000002, 28.635, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3019, 24.900000000000002, 28.635, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3019, 34.86, 40.089, 50);
INSERT INTO building(id, name, level) VALUES (3020, "building_subsistence_farmslevel", 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3020, 93.05934, 93.05934, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3020, 15.50989, 15.50989, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3020, 15.50989, 15.50989, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3020, 15.50989, 15.50989, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3020, 15.50989, 15.50989, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3020, 15.50989, 15.50989, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3020, 21.71384, 21.71384, 38);
INSERT INTO building(id, name, level) VALUES (3021, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3021, 5.0, 5.679750262488634, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3021, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3466, "building_subsistence_farmslevel", 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3466, 74.7, 74.7, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3466, 12.45, 12.45, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3466, 12.45, 12.45, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3466, 12.45, 12.45, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3466, 12.45, 12.45, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3466, 12.45, 12.45, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3466, 17.43, 17.43, 25);
INSERT INTO building(id, name, level) VALUES (3467, "building_subsistence_farmslevel", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3467, 48.74904, 48.74904, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3467, 8.12484, 8.12484, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3467, 8.12484, 8.12484, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3467, 8.12484, 8.12484, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3467, 8.12484, 8.12484, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3467, 8.12484, 8.12484, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3467, 11.37477, 11.37477, 18);
INSERT INTO building(id, name, level) VALUES (3468, "building_subsistence_farmslevel", 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3468, 124.28418, 124.28418, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3468, 20.71403, 20.71403, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3468, 20.71403, 20.71403, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3468, 20.71403, 20.71403, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3468, 20.71403, 20.71403, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3468, 20.71403, 20.71403, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3468, 28.99964, 28.99964, 46);
INSERT INTO building(id, name, level) VALUES (3469, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3469, 5.0, 5.679750262488634, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3469, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3470, "building_subsistence_farmslevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3470, 41.6259, 41.6259, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3470, 6.93765, 6.93765, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3470, 6.93765, 6.93765, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3470, 6.93765, 6.93765, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3470, 6.93765, 6.93765, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3470, 6.93765, 6.93765, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3470, 9.71271, 9.71271, 15);
INSERT INTO building(id, name, level) VALUES (3471, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3471, 10.0, 11.359500524977268, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3471, 50.0, 50.0, 2);
INSERT INTO building(id, name, level) VALUES (3472, "building_subsistence_farmslevel", 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3472, 74.7, 74.7, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3472, 12.45, 12.45, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3472, 12.45, 12.45, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3472, 12.45, 12.45, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3472, 12.45, 12.45, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3472, 12.45, 12.45, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3472, 17.43, 17.43, 25);
INSERT INTO building(id, name, level) VALUES (3473, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3473, 5.0, 5.679750262488634, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3473, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3474, "building_subsistence_farmslevel", 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3474, 94.83903, 94.83903, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3474, 15.8065, 15.8065, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3474, 15.8065, 15.8065, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3474, 15.8065, 15.8065, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3474, 15.8065, 15.8065, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3474, 15.8065, 15.8065, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3474, 22.1291, 22.1291, 33);
INSERT INTO building(id, name, level) VALUES (3475, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3475, 5.0, 5.679750262488634, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3475, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3618, "building_subsistence_orchardslevel", 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3618, 9.45175, 11.3421, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3618, 4.725875, 5.67105, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3618, 14.177625, 17.01315, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3618, 9.45175, 11.3421, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3618, 9.45175, 11.3421, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3618, 9.45175, 11.3421, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3618, 31.379808333333333, 37.65577, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3618, 13.23245, 15.87894, 35);
INSERT INTO building(id, name, level) VALUES (3619, "building_subsistence_orchardslevel", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3619, 13.51052, 13.51052, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3619, 6.75526, 6.75526, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3619, 20.26578, 20.26578, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3619, 13.51052, 13.51052, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3619, 13.51052, 13.51052, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3619, 13.51052, 13.51052, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3619, 44.85492, 44.85492, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3619, 18.91472, 18.91472, 29);
INSERT INTO building(id, name, level) VALUES (3636, "building_subsistence_farmslevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3636, 23.904, 27.4896, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3636, 3.9840000000000004, 4.5816, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3636, 3.9840000000000004, 4.5816, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3636, 3.9840000000000004, 4.5816, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3636, 3.9840000000000004, 4.5816, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3636, 3.9840000000000004, 4.5816, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3636, 5.5776, 6.41424, 8);
INSERT INTO building(id, name, level) VALUES (3640, "building_subsistence_farmslevel", 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3640, 122.508, 122.508, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3640, 20.418, 20.418, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3640, 20.418, 20.418, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3640, 20.418, 20.418, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3640, 20.418, 20.418, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3640, 20.418, 20.418, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3640, 28.5852, 28.5852, 41);
INSERT INTO building(id, name, level) VALUES (3641, "building_subsistence_farmslevel", 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3641, 84.13572, 84.13572, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3641, 14.02262, 14.02262, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3641, 14.02262, 14.02262, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3641, 14.02262, 14.02262, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3641, 14.02262, 14.02262, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3641, 14.02262, 14.02262, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3641, 19.63166, 19.63166, 34);
INSERT INTO building(id, name, level) VALUES (3642, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3642, 10.0, 11.359500524977268, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3642, 50.0, 50.0, 2);
INSERT INTO building(id, name, level) VALUES (3643, "building_subsistence_farmslevel", 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3643, 77.688, 77.688, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3643, 12.948, 12.948, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3643, 12.948, 12.948, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3643, 12.948, 12.948, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3643, 12.948, 12.948, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3643, 12.948, 12.948, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3643, 18.1272, 18.1272, 26);
INSERT INTO building(id, name, level) VALUES (3644, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3644, 5.0, 5.679750262488634, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3644, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3645, "building_subsistence_farmslevel", 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3645, 92.88288, 92.88288, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3645, 15.48048, 15.48048, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3645, 15.48048, 15.48048, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3645, 15.48048, 15.48048, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3645, 15.48048, 15.48048, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3645, 15.48048, 15.48048, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3645, 21.67267, 21.67267, 32);
INSERT INTO building(id, name, level) VALUES (3646, "building_subsistence_farmslevel", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3646, 88.8642, 88.8642, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3646, 14.8107, 14.8107, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3646, 14.8107, 14.8107, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3646, 14.8107, 14.8107, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3646, 14.8107, 14.8107, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3646, 14.8107, 14.8107, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3646, 20.73498, 20.73498, 30);
INSERT INTO building(id, name, level) VALUES (3647, "building_subsistence_farmslevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3647, 18.5052, 18.5052, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3647, 3.0842, 3.0842, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3647, 3.0842, 3.0842, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3647, 3.0842, 3.0842, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3647, 3.0842, 3.0842, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3647, 3.0842, 3.0842, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3647, 4.31788, 4.31788, 8);
INSERT INTO building(id, name, level) VALUES (3648, "building_subsistence_farmslevel", 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3648, 107.57016, 107.57016, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3648, 17.92836, 17.92836, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3648, 17.92836, 17.92836, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3648, 17.92836, 17.92836, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3648, 17.92836, 17.92836, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3648, 17.92836, 17.92836, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3648, 25.0997, 25.0997, 36);
INSERT INTO building(id, name, level) VALUES (3649, "building_subsistence_farmslevel", 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3649, 113.544, 113.544, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3649, 18.924, 18.924, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3649, 18.924, 18.924, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3649, 18.924, 18.924, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3649, 18.924, 18.924, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3649, 18.924, 18.924, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3649, 26.4936, 26.4936, 38);
INSERT INTO building(id, name, level) VALUES (3650, "building_subsistence_farmslevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3650, 11.1594, 11.1594, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3650, 1.8599, 1.8599, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3650, 1.8599, 1.8599, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3650, 1.8599, 1.8599, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3650, 1.8599, 1.8599, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3650, 1.8599, 1.8599, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3650, 2.60386, 2.60386, 4);
INSERT INTO building(id, name, level) VALUES (3654, "building_subsistence_orchardslevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3654, 1.842, 1.842, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3654, 0.921, 0.921, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3654, 2.763, 2.763, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3654, 1.842, 1.842, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3654, 1.842, 1.842, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3654, 1.842, 1.842, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3654, 6.11544, 6.11544, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3654, 2.5788, 2.5788, 5);
INSERT INTO building(id, name, level) VALUES (3655, "building_subsistence_farmslevel", 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3655, 173.35098, 173.35098, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3655, 28.89183, 28.89183, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3655, 28.89183, 28.89183, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3655, 28.89183, 28.89183, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3655, 28.89183, 28.89183, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3655, 28.89183, 28.89183, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3655, 40.44856, 40.44856, 58);
INSERT INTO building(id, name, level) VALUES (3656, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3656, 5.0, 5.679750262488634, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3656, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3658, "building_subsistence_orchardslevel", 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3658, 14.2485, 14.2485, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3658, 7.12425, 7.12425, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3658, 21.37275, 21.37275, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3658, 14.2485, 14.2485, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3658, 14.2485, 14.2485, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3658, 14.2485, 14.2485, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3658, 47.30502, 47.30502, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3658, 19.9479, 19.9479, 35);
INSERT INTO building(id, name, level) VALUES (3952, "building_subsistence_farmslevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3952, 5.0832, 5.0832, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3952, 0.8472, 0.8472, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3952, 0.8472, 0.8472, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3952, 0.8472, 0.8472, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3952, 0.8472, 0.8472, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3952, 0.8472, 0.8472, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3952, 1.18608, 1.18608, 4);
INSERT INTO building(id, name, level) VALUES (3953, "building_subsistence_pastureslevel", 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3953, 5.82929, 5.82929, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3953, 8.74394, 8.74394, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3953, 2.91464, 2.91464, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3953, 5.82929, 5.82929, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3953, 5.82929, 5.82929, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3953, 5.82929, 5.82929, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3953, 19.35325, 19.35325, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3953, 8.16101, 8.16101, 19);
INSERT INTO building(id, name, level) VALUES (3966, "building_trade_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4050, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4051, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4112, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4113, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4343, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4344, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4345, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4346, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4347, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4348, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4463, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4464, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4475, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4477, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4478, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4479, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4480, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4481, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4482, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4483, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4484, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4486, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4644, "building_conscription_centerlevel", 1);
