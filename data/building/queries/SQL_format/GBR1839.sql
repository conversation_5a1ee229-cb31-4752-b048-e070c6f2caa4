
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 70.70419556536747, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 102.53676806973353, 17.55151782527673);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 87.41676521118279, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 114.25579633842993, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 30.0599562586389, 6287.115865768167);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 19.751220500334405, 340.54147330101944);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 31.98073185765013, 96.34384548695732);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 32.146909311472605, 348.15884321478956);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 26.632869358329806, 498.0495201981621);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 43.18413128836376, 1605.3061160462548);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 40.27498386464233, 1311.0409935123948);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 48.27337209997704, 64.16596175250085);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 23.62279545407845, 906.8471496571648);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 41.81466036257615, 650.3751733333347);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 90.97487004588311, 76.46145203802327);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 83.3933516487698, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 29.646105139173507, 643.1059806697531);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 60.308161489035655, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 45.038574340007344, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 69.06810959839065, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 43.31935028385953, 14.039333271812266);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 88.0997157709812, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 68.01260230967507, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 57.35755462736664, 26.76500171398213);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 46.66205558325234, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 55.50170724188368, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 79.47405427343786, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 104.05625146097364, 135.9078037130628);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 29.120048854127333, 285.6598267173346);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 36.13308485743252, 421.0863097008726);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 41.85572870003013, 1064.414604291476);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50.029409821433866, 40.431709049157384);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 79.63465395167215, 438.68826352955637);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 37.58939849883538, 121.43003929388175);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 27.41964969176792, 102.4188321627238);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 54.98881450990577, 194.65354697856438);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 87.5, 773.172161350231);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 98.48376211641514, 792.7955216595338);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 82.95809342445371, 474.2991340085594);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 292.7227998596327, 5.912940013034491);
INSERT INTO building(id, name, level) VALUES (16777269, "building_conscription_center", 7);
INSERT INTO building(id, name, level) VALUES (77, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 77, 199.99999999999997, 37.63073872717362, 10);
INSERT INTO building(id, name, level) VALUES (78, "building_arts_academy", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 78, 30.0, 5.6446108090760445, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 78, 12.0, 2.257844323630418, 3);
INSERT INTO building(id, name, level) VALUES (79, "building_construction_sector", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 79, 60.0, 12.084313946606505, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 79, 120.0, 22.83974293950579, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 79, 150.0, 48.50484613805043, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 79, 30.0, 14.512872496284903, 3);
INSERT INTO building(id, name, level) VALUES (80, "building_university", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 80, 40.0, 7.526147745434726, 4);
INSERT INTO building(id, name, level) VALUES (81, "building_paper_mills", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 81, 300.0, 57.09935734876448, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 81, 100.0, 10.96137930591965, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 81, 700.0, 104.98074447761067, 10);
INSERT INTO building(id, name, level) VALUES (82, "building_textile_mills", 23);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 82, 575.0, 115.808008654979, 23);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 82, 345.0, 0.0, 23);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 82, 115.0, 0.0, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 82, 690.0, 46.3232034619916, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 82, 690.0, 46.3232034619916, 23);
INSERT INTO building(id, name, level) VALUES (83, "building_shipyards", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 83, 60.00000000000001, 12.084313946606505, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 83, 120.00000000000001, 22.839742939505793, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 83, 105.0, 20.566162239314473, 3);
INSERT INTO building(id, name, level) VALUES (84, "building_military_shipyards", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 84, 18.697793103448276, 3.765833366179382, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 84, 37.39559482758621, 7.117548107766511, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 84, 18.697793103448276, 6.046223850692394, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 84, 37.39559482758621, 1.1637791256377406, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 84, 9.348896551724138, 3.5140964240300687, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 84, 84.14009482758621, 18.88282725838827, 2);
INSERT INTO building(id, name, level) VALUES (85, "building_furniture_manufacturies", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 85, 150.0, 30.21078486651626, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 85, 225.00000000000003, 42.82451801157336, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 85, 225.00000000000003, 7.002169760255514, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 85, 75.0, 36.28218124071226, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 85, 600.0, 135.9929391958267, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 85, 375.00000000000006, 84.9955869973917, 15);
INSERT INTO building(id, name, level) VALUES (86, "building_rye_farm", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 86, 75.0, 19.49466094822397, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 86, 15.000000000000002, 7.256436248142452, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 86, 375.0, 139.4421054723406, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 86, 150.0, 55.77684218893623, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 86, 75.0, 27.888421094468114, 15);
INSERT INTO building(id, name, level) VALUES (87, "building_livestock_ranch", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 87, 60.0, 19.773928004472843, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 87, 60.0, 29.025744992569805, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 87, 120.0, 48.79967299704265, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 87, 30.0, 12.199918249260662, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 87, 150.0, 60.99959124630331, 6);
INSERT INTO building(id, name, level) VALUES (88, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 88, 9.998, 3.120863479722706, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 88, 99.97999999999999, 31.20863479722706, 2);
INSERT INTO building(id, name, level) VALUES (89, "building_railway", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 89, 16.0, 3.0452990586007718, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 89, 4.0, 4.071608953940124, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 89, 10.0, 3.7588354995563553, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 89, 60.0, 31.324294822363676, 2);
INSERT INTO building(id, name, level) VALUES (90, "building_port", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 90, 40.0, 12.485951109112648, 8);
INSERT INTO building(id, name, level) VALUES (91, "building_government_administration", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 91, 120.0, 22.578443236304178, 6);
INSERT INTO building(id, name, level) VALUES (92, "building_textile_mills", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 92, 800.0, 161.1241859547534, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 92, 100.0, 0.0, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 92, 1200.0, 120.84313946606505, 20);
INSERT INTO building(id, name, level) VALUES (93, "building_construction_sector", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 93, 60.0, 12.084313946606505, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 93, 120.0, 22.83974293950579, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 93, 150.0, 48.50484613805043, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 93, 30.0, 14.512872496284903, 3);
INSERT INTO building(id, name, level) VALUES (94, "building_arms_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 94, 10.0, 3.2336564092033617, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 94, 10.0, 0.31120754490024505, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 94, 30.0, 5.317295931155409, 1);
INSERT INTO building(id, name, level) VALUES (95, "building_munition_plants", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 95, 40.0, 33.31163980125451, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 95, 40.0, 8.587022853787916, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 95, 100.0, 52.373328318803026, 2);
INSERT INTO building(id, name, level) VALUES (96, "building_coal_mine", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 96, 250.0, 120.94060413570753, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 96, 1000.0, 483.7624165428301, 25);
INSERT INTO building(id, name, level) VALUES (97, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 97, 10.0, 4.753815284444971, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 97, 20.0, 20.358044769700616, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 97, 20.0, 9.675248330856602, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 97, 80.0, 52.24383853299539, 2);
INSERT INTO building(id, name, level) VALUES (98, "building_rye_farm", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 98, 49.99999999999999, 12.99644063214931, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 98, 10.0, 4.837624165428301, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 98, 249.99999999999997, 92.96140364822705, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 98, 99.99999999999999, 37.18456145929082, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 98, 49.99999999999999, 18.59228072964541, 10);
INSERT INTO building(id, name, level) VALUES (99, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 99, 30.0, 9.886964002236422, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 99, 30.0, 14.512872496284903, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 99, 60.0, 24.399836498521324, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 99, 15.0, 6.099959124630331, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 99, 75.0, 30.499795623151655, 3);
INSERT INTO building(id, name, level) VALUES (100, "building_railway", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 100, 16.0, 3.0452990586007718, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 100, 4.0, 4.071608953940124, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 100, 10.0, 3.7588354995563553, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 100, 60.0, 31.324294822363676, 2);
INSERT INTO building(id, name, level) VALUES (101, "building_fishing_wharf", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 101, 9.645892156862745, 3.0109534468590344, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 101, 96.459, 30.109558950847422, 3);
INSERT INTO building(id, name, level) VALUES (102, "building_port", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 102, 35.0, 10.925207220473567, 7);
INSERT INTO building(id, name, level) VALUES (103, "building_paper_mills", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 103, 150.0, 28.54967867438224, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 103, 50.0, 5.480689652959825, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 103, 350.0, 52.490372238805335, 5);
INSERT INTO building(id, name, level) VALUES (104, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 104, 20.0, 6.591309334824281, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 104, 20.0, 9.675248330856602, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 104, 40.0, 16.266557665680885, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 104, 10.0, 4.066639416420221, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 104, 50.0, 20.33319708210111, 2);
INSERT INTO building(id, name, level) VALUES (105, "building_coal_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 105, 40.00000000000001, 19.35049666171321, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 105, 160.00000000000003, 77.40198664685283, 4);
INSERT INTO building(id, name, level) VALUES (106, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 106, 15.0, 7.256436248142451, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 106, 180.0, 87.07723497770941, 3);
INSERT INTO building(id, name, level) VALUES (107, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 107, 5.0, 1.560743888639081, 1);
INSERT INTO building(id, name, level) VALUES (108, "building_government_administration", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 108, 80.0, 15.052295490869453, 4);
INSERT INTO building(id, name, level) VALUES (109, "building_tooling_workshops", 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 109, 531.1547948717949, 101.0953247996477, 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 109, 354.10319658119664, 184.10318148801022, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 109, 1416.412794871795, 503.00013241078625, 18);
INSERT INTO building(id, name, level) VALUES (110, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 110, 39.976, 8.051375572159028, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 110, 79.952, 15.217359395828057, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 110, 99.94, 32.317162153578394, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 110, 19.988, 9.669443181858089, 2);
INSERT INTO building(id, name, level) VALUES (111, "building_textile_mills", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 111, 200.0, 40.28104648868835, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 111, 120.0, 0.0, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 111, 39.99999999999999, 0.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 111, 240.0, 16.11241859547534, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 111, 240.0, 16.11241859547534, 8);
INSERT INTO building(id, name, level) VALUES (112, "building_steel_mills", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 112, 218.9735981308411, 222.89371570650474, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 112, 291.96479439252334, 94.41138286491248, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 112, 474.44279439252335, 313.9306463406485, 8);
INSERT INTO building(id, name, level) VALUES (113, "building_motor_industry", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 113, 90.0, 46.79225291918975, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 113, 120.0, 62.38967055891967, 3);
INSERT INTO building(id, name, level) VALUES (114, "building_coal_mine", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 114, 99.99499999999999, 47.53577593680748, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 114, 199.98999999999998, 96.7476456844006, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 114, 799.9599999999999, 383.6383951160311, 20);
INSERT INTO building(id, name, level) VALUES (115, "building_iron_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 115, 20.0, 9.507630568889942, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 115, 40.0, 40.71608953940123, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 115, 40.0, 19.350496661713205, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 115, 160.0, 104.48767706599078, 4);
INSERT INTO building(id, name, level) VALUES (116, "building_rye_farm", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 116, 39.99999999999999, 10.397152505719447, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 116, 8.0, 3.870099332342641, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 116, 200.0, 74.36912291858164, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 116, 79.99999999999999, 29.74764916743265, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 116, 39.99999999999999, 14.873824583716326, 8);
INSERT INTO building(id, name, level) VALUES (117, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 117, 25.041, 8.252648852666741, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 117, 25.041, 12.113894672649009, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 117, 50.082, 20.36654352531575, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 117, 12.5205, 5.091635881328937, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 117, 62.6025, 25.458179406644685, 3);
INSERT INTO building(id, name, level) VALUES (118, "building_railway", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 118, 31.995200000000004, 6.089684527483965, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 118, 7.998800000000001, 8.141996425194067, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 118, 19.997000000000003, 7.516543348462844, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 118, 119.982, 62.639192356280645, 4);
INSERT INTO building(id, name, level) VALUES (119, "building_port", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 119, 19.996, 6.241726959445412, 4);
INSERT INTO building(id, name, level) VALUES (120, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 120, 15.0, 7.256436248142451, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 120, 60.0, 29.025744992569805, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 120, 60.0, 29.025744992569805, 3);
INSERT INTO building(id, name, level) VALUES (121, "building_whaling_station", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 121, 10.0, 3.121487777278162, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (28, 121, 40.0, 12.485951109112648, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 121, 20.0, 6.242975554556324, 2);
INSERT INTO building(id, name, level) VALUES (122, "building_fishing_wharf", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 122, 20.0, 6.242975554556324, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 122, 200.0, 62.42975554556324, 4);
INSERT INTO building(id, name, level) VALUES (123, "building_government_administration", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 123, 80.0, 15.052295490869453, 4);
INSERT INTO building(id, name, level) VALUES (124, "building_arms_industry", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 124, 20.0, 6.4673128184067235, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 124, 20.0, 0.6224150898004901, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 124, 60.0, 10.634591862310819, 2);
INSERT INTO building(id, name, level) VALUES (125, "building_textile_mills", 22);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 125, 550.0, 110.77287784389297, 22);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 125, 330.0, 0.0, 22);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 125, 110.0, 0.0, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 125, 660.0, 44.309151137557194, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 125, 660.0, 44.309151137557194, 22);
INSERT INTO building(id, name, level) VALUES (126, "building_artillery_foundries", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 126, 30.0, 9.700969227610086, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 126, 20.0, 0.6224150898004901, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 126, 50.0, 8.862159885259018, 2);
INSERT INTO building(id, name, level) VALUES (127, "building_furniture_manufacturies", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 127, 50.0, 10.070261622172087, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 127, 25.0, 4.758279779063706, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 127, 125.0, 3.890094311253063, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 127, 75.0, 36.28218124071226, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 127, 50.0, 11.332744932985559, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 127, 275.0, 62.33009713142057, 5);
INSERT INTO building(id, name, level) VALUES (128, "building_chemical_plants", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 128, 30.0, 3.288413791775895, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 128, 10.0, 3.2336564092033617, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 128, 90.0, 19.48407452907897, 1);
INSERT INTO building(id, name, level) VALUES (129, "building_explosives_factory", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 129, 18.5332, 2.0314943495247006, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 129, 18.5332, 4.817312670474993, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 129, 46.333, 8.561008774999618, 1);
INSERT INTO building(id, name, level) VALUES (130, "building_coal_mine", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 130, 40.0, 19.015261137779884, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 130, 80.0, 38.70099332342641, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 130, 320.0, 153.46303119797236, 8);
INSERT INTO building(id, name, level) VALUES (131, "building_iron_mine", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 131, 24.999999999999996, 11.884538211112426, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 131, 49.99999999999999, 50.89511192425154, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 131, 49.99999999999999, 24.188120827141503, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 131, 199.99999999999997, 130.60959633248845, 5);
INSERT INTO building(id, name, level) VALUES (132, "building_rye_farm", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 132, 49.99999999999999, 12.99644063214931, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 132, 10.0, 4.837624165428301, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 132, 249.99999999999997, 92.96140364822705, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 132, 99.99999999999999, 37.18456145929082, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 132, 49.99999999999999, 18.59228072964541, 10);
INSERT INTO building(id, name, level) VALUES (133, "building_livestock_ranch", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 133, 50.0, 16.478273337060703, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 133, 50.0, 24.188120827141507, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 133, 100.0, 40.66639416420222, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 133, 25.0, 10.166598541050554, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 133, 125.0, 50.832992705252764, 5);
INSERT INTO building(id, name, level) VALUES (134, "building_railway", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 134, 16.0, 3.0452990586007718, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 134, 4.0, 4.071608953940124, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 134, 10.0, 3.7588354995563553, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 134, 60.0, 31.324294822363676, 2);
INSERT INTO building(id, name, level) VALUES (135, "building_port", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 135, 20.0, 6.242975554556324, 4);
INSERT INTO building(id, name, level) VALUES (136, "building_government_administration", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 136, 80.0, 15.052295490869453, 4);
INSERT INTO building(id, name, level) VALUES (137, "building_university", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 137, 20.0, 3.763073872717363, 2);
INSERT INTO building(id, name, level) VALUES (138, "building_furniture_manufacturies", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 138, 150.0, 30.21078486651626, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 138, 225.00000000000003, 42.82451801157336, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 138, 225.00000000000003, 7.002169760255514, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 138, 75.0, 36.28218124071226, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 138, 600.0, 135.9929391958267, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 138, 375.00000000000006, 84.9955869973917, 15);
INSERT INTO building(id, name, level) VALUES (139, "building_glassworks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 139, 60.0, 11.419871469752895, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 139, 15.0, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 139, 30.0, 24.983729850940886, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 139, 60.0, 20.462443723878224, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 139, 60.0, 20.462443723878224, 3);
INSERT INTO building(id, name, level) VALUES (140, "building_food_industry", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 140, 160.0, 52.73047467859425, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 140, 59.99999999999999, 67.82188877214755, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 140, 260.0, 172.84351067635782, 4);
INSERT INTO building(id, name, level) VALUES (141, "building_iron_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 141, 5.0, 2.3769076422224855, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 141, 10.0, 10.179022384850308, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 141, 10.0, 4.837624165428301, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 141, 40.0, 26.121919266497695, 1);
INSERT INTO building(id, name, level) VALUES (142, "building_shipyards", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 142, 68.0471946902655, 13.705061063717057, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 142, 136.0943982300885, 25.903008925682943, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 142, 68.0471946902655, 2.1176800396906517, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 142, 17.011796460176992, 6.394454444574042, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 142, 238.1651946902655, 47.55806025086641, 4);
INSERT INTO building(id, name, level) VALUES (143, "building_military_shipyards", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 143, 19.993396396396395, 4.026774648550086, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 143, 39.98679279279279, 7.6107339030222505, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 143, 19.993396396396395, 6.4651774398950606, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 143, 39.98679279279279, 1.2444191613479856, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 143, 9.996693693693693, 3.7575927134047, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 143, 89.9702972972973, 20.191248723120076, 2);
INSERT INTO building(id, name, level) VALUES (144, "building_rye_farm", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 144, 35.00000000000001, 9.09750844250452, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 144, 7.0, 3.386336915799811, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 144, 175.0, 65.07298255375893, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 144, 70.00000000000001, 26.02919302150358, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 144, 35.00000000000001, 13.01459651075179, 7);
INSERT INTO building(id, name, level) VALUES (145, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 145, 30.0, 9.886964002236422, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 145, 30.0, 14.512872496284903, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 145, 60.0, 24.399836498521324, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 145, 15.0, 6.099959124630331, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 145, 75.0, 30.499795623151655, 3);
INSERT INTO building(id, name, level) VALUES (146, "building_fishing_wharf", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 146, 15.000000000000002, 4.682231665917244, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 146, 150.0, 46.82231665917244, 3);
INSERT INTO building(id, name, level) VALUES (147, "building_railway", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 147, 8.0, 1.5226495293003859, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 147, 2.0, 2.035804476970062, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 147, 5.0, 1.8794177497781777, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 147, 30.0, 15.662147411181838, 1);
INSERT INTO building(id, name, level) VALUES (148, "building_port", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 148, 35.0, 10.925207220473567, 7);
INSERT INTO building(id, name, level) VALUES (149, "building_coal_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 149, 5.0, 2.3769076422224855, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 149, 10.0, 4.837624165428301, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 149, 40.0, 19.182878899746544, 1);
INSERT INTO building(id, name, level) VALUES (150, "building_government_administration", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 150, 120.0, 22.578443236304178, 6);
INSERT INTO building(id, name, level) VALUES (151, "building_tooling_workshops", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 151, 180.0, 34.25961440925868, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 151, 120.0, 62.38967055891966, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 151, 480.0, 170.45882699685092, 6);
INSERT INTO building(id, name, level) VALUES (152, "building_textile_mills", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 152, 100.0, 20.140523244344173, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 152, 59.99999999999999, 0.0, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 152, 20.0, 0.0, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 152, 119.99999999999999, 8.056209297737668, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 152, 119.99999999999999, 8.056209297737668, 4);
INSERT INTO building(id, name, level) VALUES (153, "building_shipyards", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 153, 120.00000000000001, 24.16862789321301, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 153, 240.00000000000003, 45.67948587901159, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 153, 120.00000000000001, 3.7344905388029406, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 153, 30.000000000000004, 11.276506498669066, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 153, 420.00000000000006, 83.86777644542326, 6);
INSERT INTO building(id, name, level) VALUES (154, "building_military_shipyards", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 154, 19.928198198198196, 4.0136433902870845, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 154, 39.85639639639639, 7.58591540157282, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 154, 19.928198198198196, 6.444094582747849, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 154, 39.85639639639639, 1.2403611271093495, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 154, 9.964099099099098, 3.7453409414791183, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 154, 89.67689189189188, 20.125402308300828, 2);
INSERT INTO building(id, name, level) VALUES (155, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 155, 15.0, 3.8989321896447935, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 155, 3.0, 1.4512872496284903, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 155, 120.0, 44.621473751148976, 3);
INSERT INTO building(id, name, level) VALUES (156, "building_vineyard_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 156, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (157, "building_livestock_ranch", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 157, 40.0, 13.182618669648562, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 157, 40.0, 19.350496661713205, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 157, 80.0, 32.53311533136177, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 157, 20.0, 8.133278832840443, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 157, 100.0, 40.66639416420222, 4);
INSERT INTO building(id, name, level) VALUES (158, "building_lead_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 158, 9.98749504950495, 4.7478706619655116, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 158, 19.974999999999998, 20.332597213738488, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 158, 19.974999999999998, 9.663154270443032, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 158, 79.89999999999999, 52.17853373482915, 2);
INSERT INTO building(id, name, level) VALUES (159, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 159, 10.0, 3.121487777278162, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 159, 100.0, 31.21487777278162, 2);
INSERT INTO building(id, name, level) VALUES (160, "building_port", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 160, 35.0, 10.925207220473567, 7);
INSERT INTO building(id, name, level) VALUES (161, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 161, 60.0, 11.289221618152089, 3);
INSERT INTO building(id, name, level) VALUES (162, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 162, 15.684, 3.1588396656429403, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 162, 31.368, 5.970308804386813, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 162, 39.21, 12.679166780486382, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 162, 7.842, 3.793664870528874, 1);
INSERT INTO building(id, name, level) VALUES (163, "building_food_industry", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 163, 200.0, 65.91309334824281, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 163, 75.0, 84.77736096518443, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 163, 325.0, 216.05438834544728, 5);
INSERT INTO building(id, name, level) VALUES (164, "building_glassworks", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 164, 80.0, 15.226495293003861, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 164, 20.0, 0.0, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 164, 40.0, 33.31163980125451, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 164, 80.0, 27.283258298504286, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 164, 80.0, 27.283258298504286, 4);
INSERT INTO building(id, name, level) VALUES (165, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 165, 10.0, 4.753815284444971, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 165, 10.0, 4.837624165428301, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 165, 120.0, 57.54863669923964, 2);
INSERT INTO building(id, name, level) VALUES (166, "building_iron_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 166, 5.0, 2.3769076422224855, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 166, 10.0, 10.179022384850308, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 166, 10.0, 4.837624165428301, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 166, 40.0, 26.121919266497695, 1);
INSERT INTO building(id, name, level) VALUES (167, "building_rye_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 167, 25.0, 6.498220316074656, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 167, 5.0, 2.4188120827141506, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 167, 125.0, 46.480701824113524, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 167, 50.0, 18.59228072964541, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 167, 25.0, 9.296140364822705, 5);
INSERT INTO building(id, name, level) VALUES (168, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 168, 10.0, 3.2956546674121405, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 168, 10.0, 4.837624165428301, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 168, 20.0, 8.133278832840443, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 168, 5.0, 2.0333197082101107, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 168, 25.0, 10.166598541050554, 1);
INSERT INTO building(id, name, level) VALUES (169, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 169, 9.999, 3.1211756285004344, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 169, 99.99, 31.211756285004338, 2);
INSERT INTO building(id, name, level) VALUES (170, "building_railway", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 170, 6.837275, 1.301346695055912, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 170, 1.7093166666666668, 1.7399172612797715, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 170, 4.2733, 1.606263174025417, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 170, 25.6398, 13.385810906440671, 1);
INSERT INTO building(id, name, level) VALUES (171, "building_port", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 171, 19.996, 6.241726959445412, 4);
INSERT INTO building(id, name, level) VALUES (172, "building_coal_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 172, 5.0, 2.3769076422224855, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 172, 10.0, 4.837624165428301, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 172, 40.0, 19.182878899746544, 1);
INSERT INTO building(id, name, level) VALUES (173, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 173, 19.96259405940594, 6.578981628553506, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 173, 19.96259405940594, 9.657152742641763, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 173, 39.925198019801975, 16.23613839757093, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 173, 9.98129702970297, 4.0590335927988175, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 173, 49.906495049504954, 20.29517199036975, 2);
INSERT INTO building(id, name, level) VALUES (174, "building_rye_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 174, 15.0, 3.8989321896447935, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 174, 3.0, 1.4512872496284903, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 174, 75.0, 27.88842109446811, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 174, 45.0, 16.73305265668087, 3);
INSERT INTO building(id, name, level) VALUES (175, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 175, 10.0, 4.837624165428301, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 175, 120.0, 58.05148998513962, 2);
INSERT INTO building(id, name, level) VALUES (176, "building_port", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 176, 10.0, 3.121487777278162, 2);
INSERT INTO building(id, name, level) VALUES (177, "building_rye_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 177, 15.0, 3.8989321896447935, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 177, 2.9999999999999996, 1.4512872496284903, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 177, 74.99999999999999, 27.88842109446811, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 177, 44.99999999999999, 16.73305265668087, 3);
INSERT INTO building(id, name, level) VALUES (178, "building_port", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 178, 10.0, 3.121487777278162, 2);
INSERT INTO building(id, name, level) VALUES (180, "building_lead_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 180, 4.4565, 4.53628132580854, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 180, 4.4565, 2.1558872093231223, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 180, 17.826, 13.224774418646245, 1);
INSERT INTO building(id, name, level) VALUES (181, "building_food_industry", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 181, 160.0, 52.73047467859425, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 181, 59.99999999999999, 67.82188877214755, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 181, 260.0, 172.84351067635782, 4);
INSERT INTO building(id, name, level) VALUES (182, "building_rye_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 182, 25.0, 6.498220316074656, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 182, 5.0, 2.4188120827141506, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 182, 125.0, 46.480701824113524, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 182, 50.0, 18.59228072964541, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 182, 25.0, 9.296140364822705, 5);
INSERT INTO building(id, name, level) VALUES (183, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 183, 15.0, 4.682231665917243, 3);
INSERT INTO building(id, name, level) VALUES (184, "building_rye_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 184, 10.0, 2.5992881264298626, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 184, 2.0, 0.9675248330856603, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 184, 50.0, 18.59228072964541, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 184, 20.0, 7.436912291858164, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 184, 10.0, 3.718456145929082, 2);
INSERT INTO building(id, name, level) VALUES (185, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 185, 5.0, 1.560743888639081, 1);
INSERT INTO building(id, name, level) VALUES (569, "building_fishing_wharf", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 569, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (570, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 570, 5.0, 1.560743888639081, 1);
INSERT INTO building(id, name, level) VALUES (723, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 723, 5.0, 1.560743888639081, 1);
INSERT INTO building(id, name, level) VALUES (1153, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1153, 4.628, 1.4446245433243334, 1);
INSERT INTO building(id, name, level) VALUES (1190, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1190, 2.331, 0.7276188008835396, 1);
INSERT INTO building(id, name, level) VALUES (1508, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1508, 10.0, 3.121487777278162, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1508, 100.0, 31.21487777278162, 2);
INSERT INTO building(id, name, level) VALUES (1514, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1514, 5.0, 1.560743888639081, 1);
INSERT INTO building(id, name, level) VALUES (1580, "building_port", 1);
INSERT INTO building(id, name, level) VALUES (1610, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1610, 30.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (1611, "building_sugar_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1611, 28.722, 28.722, 1);
INSERT INTO building(id, name, level) VALUES (1612, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1612, 5.0, 1.560743888639081, 1);
INSERT INTO building(id, name, level) VALUES (1619, "building_sugar_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1619, 90.0, 91.8, 3);
INSERT INTO building(id, name, level) VALUES (1620, "building_coffee_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1620, 39.972, 40.37172, 2);
INSERT INTO building(id, name, level) VALUES (1621, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1621, 49.435, 49.92935, 2);
INSERT INTO building(id, name, level) VALUES (1622, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1622, 5.0, 1.560743888639081, 1);
INSERT INTO building(id, name, level) VALUES (1623, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1623, 29.532, 29.532, 1);
INSERT INTO building(id, name, level) VALUES (1624, "building_sugar_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1624, 88.48169607843137, 90.25133, 3);
INSERT INTO building(id, name, level) VALUES (1625, "building_coffee_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1625, 98.544, 102.48576, 5);
INSERT INTO building(id, name, level) VALUES (1626, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1626, 5.0, 1.560743888639081, 1);
INSERT INTO building(id, name, level) VALUES (1642, "building_sugar_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1642, 29.933999999999997, 30.23334, 2);
INSERT INTO building(id, name, level) VALUES (1643, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1643, 5.0, 1.560743888639081, 1);
INSERT INTO building(id, name, level) VALUES (16778912, "building_conscription_center", 9);
INSERT INTO building(id, name, level) VALUES (1771, "building_port", 1);
INSERT INTO building(id, name, level) VALUES (2190, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2190, 6.971, 2.1759891295406066, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2190, 69.71, 21.759891295406064, 2);
INSERT INTO building(id, name, level) VALUES (2191, "building_tea_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2191, 71.72, 73.8716, 4);
INSERT INTO building(id, name, level) VALUES (2192, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2192, 47.0, 47.47, 2);
INSERT INTO building(id, name, level) VALUES (2193, "building_coffee_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 2193, 38.256, 38.63856, 2);
INSERT INTO building(id, name, level) VALUES (2194, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2194, 3.394, 1.0594329516082084, 1);
INSERT INTO building(id, name, level) VALUES (2197, "building_sugar_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2197, 26.19, 26.19, 1);
INSERT INTO building(id, name, level) VALUES (2198, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2198, 4.25, 1.326632305343219, 1);
INSERT INTO building(id, name, level) VALUES (2549, "building_tea_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2549, 18.14, 18.14, 1);
INSERT INTO building(id, name, level) VALUES (2550, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2550, 5.0, 1.560743888639081, 1);
INSERT INTO building(id, name, level) VALUES (2697, "building_subsistence_farms", 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2697, 25.9426, 28.53686, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2697, 6.4856454545454545, 7.13421, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2697, 6.4856454545454545, 7.13421, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2697, 6.4856454545454545, 7.13421, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2697, 6.4856454545454545, 7.13421, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2697, 6.4856454545454545, 7.13421, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2697, 6.4856454545454545, 7.13421, 19);
INSERT INTO building(id, name, level) VALUES (2698, "building_urban_center", 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2698, 27.998875, 5.3290592299612936, 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2698, 27.998875, 11.820241155503599, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2698, 559.9776, 171.49303447963007, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2698, 139.9944, 42.87325861990752, 28);
INSERT INTO building(id, name, level) VALUES (2699, "building_subsistence_farms", 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2699, 36.72448, 36.72448, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2699, 9.18112, 9.18112, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2699, 9.18112, 9.18112, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2699, 9.18112, 9.18112, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2699, 9.18112, 9.18112, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2699, 9.18112, 9.18112, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2699, 9.18112, 9.18112, 32);
INSERT INTO building(id, name, level) VALUES (2700, "building_urban_center", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2700, 11.0029375, 2.0942022006620706, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2700, 11.0029375, 11.199914711160888, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2700, 11.0029375, 4.645092871371934, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2700, 242.06467857142854, 130.1097343228855, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2700, 55.01469642857142, 29.57039241916269, 13);
INSERT INTO building(id, name, level) VALUES (2819, "building_subsistence_farms", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2819, 8.2328, 8.2328, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2819, 2.0582, 2.0582, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2819, 2.0582, 2.0582, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2819, 2.0582, 2.0582, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2819, 2.0582, 2.0582, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2819, 2.0582, 2.0582, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2819, 2.0582, 2.0582, 20);
INSERT INTO building(id, name, level) VALUES (2821, "building_subsistence_farms", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2821, 3.1412, 3.1412, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2821, 0.7853, 0.7853, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2821, 0.7853, 0.7853, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2821, 0.7853, 0.7853, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2821, 0.7853, 0.7853, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2821, 0.7853, 0.7853, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2821, 0.7853, 0.7853, 5);
INSERT INTO building(id, name, level) VALUES (2887, "building_subsistence_rice_paddies", 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2887, 98.6139, 98.6139, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2887, 16.43565, 16.43565, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2887, 16.43565, 16.43565, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2887, 21.9142, 21.9142, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2887, 21.9142, 21.9142, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2887, 21.9142, 21.9142, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2887, 21.9142, 21.9142, 22);
INSERT INTO building(id, name, level) VALUES (2904, "building_subsistence_farms", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2904, 3.8548, 3.8548, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2904, 0.9637, 0.9637, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2904, 0.9637, 0.9637, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2904, 0.9637, 0.9637, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2904, 0.9637, 0.9637, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2904, 0.9637, 0.9637, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2904, 0.9637, 0.9637, 2);
INSERT INTO building(id, name, level) VALUES (2957, "building_subsistence_farms", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2957, 8.13024, 8.13024, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2957, 2.03256, 2.03256, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2957, 2.03256, 2.03256, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2957, 2.03256, 2.03256, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2957, 2.03256, 2.03256, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2957, 2.03256, 2.03256, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2957, 2.03256, 2.03256, 12);
INSERT INTO building(id, name, level) VALUES (3029, "building_subsistence_pastures", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3029, 0.4856, 0.4856, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3029, 0.7284, 0.7284, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3029, 0.2428, 0.2428, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3029, 0.4856, 0.4856, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3029, 0.4856, 0.4856, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3029, 0.4856, 0.4856, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3029, 1.29169, 1.29169, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3029, 0.4856, 0.4856, 10);
INSERT INTO building(id, name, level) VALUES (3073, "building_subsistence_farms", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3073, 9.58116, 9.58116, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3073, 2.39529, 2.39529, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3073, 2.39529, 2.39529, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3073, 2.39529, 2.39529, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3073, 2.39529, 2.39529, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3073, 2.39529, 2.39529, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3073, 2.39529, 2.39529, 6);
INSERT INTO building(id, name, level) VALUES (3087, "building_subsistence_farms", 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3087, 74.72396, 74.72396, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3087, 18.68099, 18.68099, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3087, 18.68099, 18.68099, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3087, 18.68099, 18.68099, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3087, 18.68099, 18.68099, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3087, 18.68099, 18.68099, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3087, 18.68099, 18.68099, 38);
INSERT INTO building(id, name, level) VALUES (3088, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3088, 2.0, 0.38066238232509647, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3088, 2.0, 2.035804476970062, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3088, 2.0, 0.751767099911271, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3088, 2.0, 0.8443368639278255, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3088, 44.0, 21.87221490390306, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3088, 16.0, 7.953532692328386, 2);
INSERT INTO building(id, name, level) VALUES (3089, "building_subsistence_farms", 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3089, 50.50578, 50.50578, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3089, 12.62644, 12.62644, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3089, 12.62644, 12.62644, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3089, 12.62644, 12.62644, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3089, 12.62644, 12.62644, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3089, 12.62644, 12.62644, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3089, 12.62644, 12.62644, 39);
INSERT INTO building(id, name, level) VALUES (3090, "building_urban_center", 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3090, 12.647451327433629, 2.407204476320795, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3090, 12.647451327433629, 12.873869017325164, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3090, 12.647451327433629, 4.753968902846867, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3090, 12.647451327433629, 5.3393546952425615, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3090, 278.2441150442478, 138.31397909077532, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3090, 101.17967256637168, 50.29598959723241, 14);
INSERT INTO building(id, name, level) VALUES (3091, "building_subsistence_farms", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3091, 58.2876, 52.45884, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3091, 14.5719, 13.11471, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3091, 14.5719, 13.11471, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3091, 14.5719, 13.11471, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3091, 14.5719, 13.11471, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3091, 14.5719, 13.11471, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3091, 14.5719, 13.11471, 30);
INSERT INTO building(id, name, level) VALUES (3092, "building_subsistence_farms", 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3092, 129.2811, 129.2811, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3092, 32.32027, 32.32027, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3092, 32.32027, 32.32027, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3092, 32.32027, 32.32027, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3092, 32.32027, 32.32027, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3092, 32.32027, 32.32027, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3092, 32.32027, 32.32027, 65);
INSERT INTO building(id, name, level) VALUES (3093, "building_urban_center", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3093, 10.999999999999998, 2.0936431027880302, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3093, 10.999999999999998, 11.196924623335338, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3093, 10.999999999999998, 4.13471904951199, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3093, 10.999999999999998, 4.643852751603039, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3093, 241.99999999999997, 120.29718197146683, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3093, 87.99999999999999, 43.744429807806114, 11);
INSERT INTO building(id, name, level) VALUES (3094, "building_subsistence_farms", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3094, 33.331799999999994, 36.66498, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3094, 8.332945454545454, 9.16624, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3094, 8.332945454545454, 9.16624, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3094, 8.332945454545454, 9.16624, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3094, 8.332945454545454, 9.16624, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3094, 8.332945454545454, 9.16624, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3094, 8.332945454545454, 9.16624, 30);
INSERT INTO building(id, name, level) VALUES (3095, "building_urban_center", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3095, 10.0, 1.9033119116254826, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3095, 10.0, 10.179022384850308, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3095, 10.0, 4.221684319639127, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3095, 220.0, 118.24997236260714, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3095, 49.99999999999999, 26.874993718774345, 10);
INSERT INTO building(id, name, level) VALUES (3096, "building_subsistence_farms", 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3096, 102.0, 102.0, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3096, 25.5, 25.5, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3096, 25.5, 25.5, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3096, 25.5, 25.5, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3096, 25.5, 25.5, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3096, 25.5, 25.5, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3096, 25.5, 25.5, 51);
INSERT INTO building(id, name, level) VALUES (3097, "building_urban_center", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3097, 7.0, 1.3323183381378376, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3097, 7.0, 7.125315669395216, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3097, 7.0, 2.955179023747389, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3097, 154.0, 82.774980653825, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3097, 35.0, 18.812495603142043, 7);
INSERT INTO building(id, name, level) VALUES (3098, "building_subsistence_farms", 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3098, 106.6284, 106.6284, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3098, 26.6571, 26.6571, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3098, 26.6571, 26.6571, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3098, 26.6571, 26.6571, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3098, 26.6571, 26.6571, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3098, 26.6571, 26.6571, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3098, 26.6571, 26.6571, 54);
INSERT INTO building(id, name, level) VALUES (3099, "building_urban_center", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3099, 3.8843106796116507, 0.7393054784958927, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3099, 3.8843106796116507, 3.9538485357480107, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3099, 3.8843106796116507, 1.4600484873830144, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3099, 3.8843106796116507, 1.639833348872331, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3099, 85.45503883495147, 42.47934031862364, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3099, 31.074553398058253, 15.447029771935028, 4);
INSERT INTO building(id, name, level) VALUES (3100, "building_subsistence_farms", 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3100, 247.3975, 247.3975, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3100, 61.84937, 61.84937, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3100, 61.84937, 61.84937, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3100, 61.84937, 61.84937, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3100, 61.84937, 61.84937, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3100, 61.84937, 61.84937, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3100, 61.84937, 61.84937, 125);
INSERT INTO building(id, name, level) VALUES (3101, "building_subsistence_farms", 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3101, 253.24053636363635, 278.56459, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3101, 63.310127272727264, 69.64114, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3101, 63.310127272727264, 69.64114, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3101, 63.310127272727264, 69.64114, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3101, 63.310127272727264, 69.64114, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3101, 63.310127272727264, 69.64114, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3101, 63.310127272727264, 69.64114, 127);
INSERT INTO building(id, name, level) VALUES (3102, "building_subsistence_farms", 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3102, 179.6184, 179.6184, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3102, 44.9046, 44.9046, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3102, 44.9046, 44.9046, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3102, 44.9046, 44.9046, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3102, 44.9046, 44.9046, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3102, 44.9046, 44.9046, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3102, 44.9046, 44.9046, 90);
INSERT INTO building(id, name, level) VALUES (3103, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3103, 1.0, 0.19033119116254824, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3103, 1.0, 1.017902238485031, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3103, 1.0, 0.3758835499556355, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3103, 1.0, 0.42216843196391274, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3103, 22.0, 10.93610745195153, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3103, 8.0, 3.976766346164193, 1);
INSERT INTO building(id, name, level) VALUES (3150, "building_subsistence_farms", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3150, 6.336, 6.336, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3150, 1.584, 1.584, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3150, 1.584, 1.584, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3150, 1.584, 1.584, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3150, 1.584, 1.584, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3150, 1.584, 1.584, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3150, 1.584, 1.584, 5);
INSERT INTO building(id, name, level) VALUES (3175, "building_subsistence_fishing_villages", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3175, 2.28968, 2.28968, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 3175, 9.15872, 9.15872, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3175, 1.14484, 1.14484, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3175, 3.43452, 3.43452, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3175, 2.28968, 2.28968, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3175, 2.28968, 2.28968, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3175, 2.28968, 2.28968, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3175, 2.28968, 2.28968, 8);
INSERT INTO building(id, name, level) VALUES (3176, "building_subsistence_fishing_villages", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3176, 2.3255, 2.3255, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 3176, 9.302, 9.302, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3176, 1.16275, 1.16275, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3176, 3.48825, 3.48825, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3176, 2.3255, 2.3255, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3176, 2.3255, 2.3255, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3176, 2.3255, 2.3255, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3176, 2.3255, 2.3255, 10);
INSERT INTO building(id, name, level) VALUES (3178, "building_subsistence_fishing_villages", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3178, 4.21308, 4.21308, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 3178, 16.85232, 16.85232, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3178, 2.10654, 2.10654, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3178, 6.31962, 6.31962, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3178, 4.21308, 4.21308, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3178, 4.21308, 4.21308, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3178, 4.21308, 4.21308, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3178, 4.21308, 4.21308, 9);
INSERT INTO building(id, name, level) VALUES (3179, "building_subsistence_farms", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3179, 21.19744, 21.19744, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3179, 5.29936, 5.29936, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3179, 5.29936, 5.29936, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3179, 5.29936, 5.29936, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3179, 5.29936, 5.29936, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3179, 5.29936, 5.29936, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3179, 5.29936, 5.29936, 11);
INSERT INTO building(id, name, level) VALUES (16780497, "building_lead_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 16780497, 5.0, 2.3769076422224855, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16780497, 10.0, 10.179022384850308, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16780497, 10.0, 4.837624165428301, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 16780497, 40.0, 26.121919266497695, 1);
INSERT INTO building(id, name, level) VALUES (3412, "building_subsistence_orchards", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3412, 3.63208, 3.63208, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3412, 1.81604, 1.81604, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3412, 5.44812, 5.44812, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3412, 3.63208, 3.63208, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3412, 3.63208, 3.63208, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3412, 3.63208, 3.63208, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3412, 9.66134, 9.66134, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3412, 3.63208, 3.63208, 9);
INSERT INTO building(id, name, level) VALUES (3750, "building_subsistence_orchards", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3750, 2.4693, 2.4693, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3750, 1.23465, 1.23465, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3750, 3.70395, 3.70395, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3750, 2.4693, 2.4693, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3750, 2.4693, 2.4693, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3750, 2.4693, 2.4693, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3750, 6.56833, 6.56833, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3750, 2.4693, 2.4693, 5);
INSERT INTO building(id, name, level) VALUES (3767, "building_subsistence_farms", 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3767, 145.14152, 145.14152, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3767, 36.28538, 36.28538, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3767, 36.28538, 36.28538, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3767, 36.28538, 36.28538, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3767, 36.28538, 36.28538, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3767, 36.28538, 36.28538, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3767, 36.28538, 36.28538, 73);
INSERT INTO building(id, name, level) VALUES (3821, "building_barracks", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3821, 13.99984, 6.286840091529205, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3821, 29.99973, 1.6456688721818558, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3821, 3.99976, 2.678592053299382, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3821, 1.99988, 0.6590913856264191, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3821, 1.99988, 0.646692477963762, 17);
INSERT INTO building(id, name, level) VALUES (3822, "building_barracks", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3822, 6.99993, 3.1434245364159894, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3822, 13.99993, 0.767981878961075, 7);
INSERT INTO building(id, name, level) VALUES (3823, "building_barracks", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3823, 2.0, 0.8981302774216284, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3823, 8.0, 0.4388489822226682, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3823, 4.0, 1.3182618669648563, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3823, 4.0, 1.2934625636813448, 4);
INSERT INTO building(id, name, level) VALUES (3824, "building_barracks", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3824, 10.99978, 4.93961773148844, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3824, 21.99978, 1.2068226327653264, 11);
INSERT INTO building(id, name, level) VALUES (3825, "building_barracks", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3825, 20.0, 8.981302774216285, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3825, 44.0, 2.4136694022246754, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3825, 6.0, 4.018129167699135, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3825, 4.0, 1.3182618669648563, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3825, 4.0, 1.2934625636813448, 25);
INSERT INTO building(id, name, level) VALUES (3826, "building_barracks", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3826, 10.99982, 4.939635694093988, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3826, 25.99974, 1.4262449296317494, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3826, 2.99988, 0.9886568523676332, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3826, 1.99992, 0.6467054125893987, 13);
INSERT INTO building(id, name, level) VALUES (3827, "building_barracks", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3827, 2.99999, 1.3471909254810555, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3827, 13.99986, 0.7679780390324804, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3827, 6.99986, 2.3069121280231544, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3827, 5.99991, 1.9401647426143342, 7);
INSERT INTO building(id, name, level) VALUES (3828, "building_barracks", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3828, 10.99992, 4.9396806006078595, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3828, 21.99996, 1.2068325068674264, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3828, 1.99992, 1.3393228141774756, 12);
INSERT INTO building(id, name, level) VALUES (3829, "building_barracks", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3829, 13.999833333333333, 6.286837097761614, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3829, 33.999655555555556, 1.8650892795596052, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3829, 5.9998, 1.977326887353936, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3829, 5.9998, 1.940129172393833, 17);
INSERT INTO building(id, name, level) VALUES (3830, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3830, 9.935, 4.461462153091939, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3830, 19.87, 1.0899911595955523, 10);
INSERT INTO building(id, name, level) VALUES (3831, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3831, 5.9999666666666664, 0.3291349081295752, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3831, 5.9999666666666664, 1.9773818149317262, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3831, 5.9999666666666664, 1.9401830666673197, 3);
INSERT INTO building(id, name, level) VALUES (3832, "building_barracks", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3832, 6.0, 0.32913673666700116, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3832, 2.0, 1.339376389233045, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3832, 6.0, 1.9773928004472845, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3832, 6.0, 1.9401938455220171, 4);
INSERT INTO building(id, name, level) VALUES (3833, "building_barracks", 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3833, 12.99984, 5.837774952818392, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3833, 25.99982, 1.4262493181215716, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3833, 1.9999, 1.3393094204135834, 14);
INSERT INTO building(id, name, level) VALUES (3834, "building_naval_base", 30);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3834, 90.0, 14.156734257536902, 30);
INSERT INTO building(id, name, level) VALUES (3835, "building_naval_base", 40);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3835, 40.0, 6.2918818922386235, 40);
INSERT INTO building(id, name, level) VALUES (3836, "building_naval_base", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3836, 4.0, 0.6291881892238623, 4);
INSERT INTO building(id, name, level) VALUES (3837, "building_naval_base", 33);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3837, 94.99908, 14.94307478078321, 33);
INSERT INTO building(id, name, level) VALUES (3838, "building_naval_base", 27);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3838, 81.0, 12.741060831783212, 27);
INSERT INTO building(id, name, level) VALUES (3839, "building_naval_base", 24);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3839, 23.99976, 3.7750913840518208, 24);
INSERT INTO building(id, name, level) VALUES (3840, "building_naval_base", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3840, 2.0, 0.31459409461193116, 2);
INSERT INTO building(id, name, level) VALUES (3841, "building_naval_base", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3841, 18.0, 2.831346851507381, 6);
INSERT INTO building(id, name, level) VALUES (3842, "building_naval_base", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3842, 4.99998, 0.7864820905888819, 3);
INSERT INTO building(id, name, level) VALUES (3843, "building_naval_base", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3843, 2.0, 0.31459409461193116, 2);
INSERT INTO building(id, name, level) VALUES (3844, "building_naval_base", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3844, 2.0, 0.31459409461193116, 2);
INSERT INTO building(id, name, level) VALUES (3845, "building_naval_base", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3845, 6.0, 0.9437822838357935, 6);
INSERT INTO building(id, name, level) VALUES (3846, "building_naval_base", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3846, 6.0, 0.9437822838357935, 6);
INSERT INTO building(id, name, level) VALUES (3847, "building_naval_base", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3847, 1.9899999999999998, 0.31302112413887145, 2);
INSERT INTO building(id, name, level) VALUES (16781143, "building_subsistence_rice_paddies", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16781143, 38.18056, 38.18056, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16781143, 6.36342, 6.36342, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16781143, 6.36342, 6.36342, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16781143, 8.48457, 8.48457, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16781143, 8.48457, 8.48457, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16781143, 8.48457, 8.48457, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16781143, 8.48457, 8.48457, 9);
INSERT INTO building(id, name, level) VALUES (4132, "building_trade_center", 167);
INSERT INTO building(id, name, level) VALUES (16781399, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (4236, "building_chemical_plants", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 4236, 30.0, 3.288413791775895, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4236, 10.0, 3.2336564092033617, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 4236, 90.0, 19.48407452907897, 1);
INSERT INTO building(id, name, level) VALUES (4377, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (4550, "building_conscription_center", 2);
INSERT INTO building(id, name, level) VALUES (4637, "building_conscription_center", 6);
INSERT INTO building(id, name, level) VALUES (4712, "building_subsistence_farms", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 4712, 4.0824, 4.0824, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 4712, 1.0206, 1.0206, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 4712, 1.0206, 1.0206, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 4712, 1.0206, 1.0206, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 4712, 1.0206, 1.0206, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4712, 1.0206, 1.0206, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 4712, 1.0206, 1.0206, 4);
INSERT INTO building(id, name, level) VALUES (4759, "building_conscription_center", 10);
INSERT INTO building(id, name, level) VALUES (4785, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4785, 15.588, 3.1395047633283695, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4785, 31.176, 5.933765215683604, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4785, 38.97, 12.601559026665502, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4785, 7.794, 3.770444274534818, 1);
INSERT INTO building(id, name, level) VALUES (4877, "building_port", 1);
INSERT INTO building(id, name, level) VALUES (4894, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4894, 30.0, 5.7099357348764475, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 4894, 20.0, 10.39827842648661, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4894, 80.0, 28.409804499475154, 1);
INSERT INTO building(id, name, level) VALUES (4895, "building_conscription_center", 6);
INSERT INTO building(id, name, level) VALUES (4952, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4952, 19.992, 4.026493407009288, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4952, 39.984, 7.610202347443329, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4952, 49.98, 16.1618147331984, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4952, 9.996, 4.83568911576213, 1);
INSERT INTO building(id, name, level) VALUES (4986, "building_conscription_center", 5);
INSERT INTO building(id, name, level) VALUES (5003, "building_conscription_center", 7);
INSERT INTO building(id, name, level) VALUES (5029, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5029, 15.32, 3.0855281610335275, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5029, 30.64, 5.831747697220479, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5029, 38.3, 12.384904047248874, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5029, 7.66, 3.705620110718079, 1);
INSERT INTO building(id, name, level) VALUES (5035, "building_conscription_center", 10);
INSERT INTO building(id, name, level) VALUES (5045, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (5047, "building_conscription_center", 7);
INSERT INTO building(id, name, level) VALUES (5338, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5338, 30.0, 5.7099357348764475, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 5338, 20.0, 10.39827842648661, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 5338, 80.0, 28.409804499475154, 1);
INSERT INTO building(id, name, level) VALUES (5357, "building_paper_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5357, 30.0, 5.7099357348764475, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 5357, 10.0, 1.096137930591965, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 5357, 70.0, 10.498074447761066, 1);
INSERT INTO building(id, name, level) VALUES (5471, "building_food_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 5471, 40.0, 13.182618669648562, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 5471, 40.0, 45.21459251476503, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 5471, 35.0, 23.267395667971247, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 5471, 60.0, 39.88696400223642, 1);
INSERT INTO building(id, name, level) VALUES (5611, "building_sulfur_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 5611, 5.0, 2.3769076422224855, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5611, 10.0, 10.179022384850308, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5611, 10.0, 4.837624165428301, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 5611, 40.0, 26.121919266497695, 1);
INSERT INTO building(id, name, level) VALUES (5634, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 5634, 1.0, 0.4490651387108142, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 5634, 2.0, 0.10971224555566705, 1);
INSERT INTO building(id, name, level) VALUES (5646, "building_artillery_foundries", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5646, 6.1935, 2.0027650970401023, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 5646, 4.129, 0.12849759528931115, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 5646, 10.3225, 1.8295929083117242, 1);
INSERT INTO building(id, name, level) VALUES (5651, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 5651, 5.0, 1.560743888639081, 1);
INSERT INTO building(id, name, level) VALUES (5743, "building_gold_fields", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 5743, 10.08, 10.08, 1);
INSERT INTO building(id, name, level) VALUES (5793, "building_food_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 5793, 1.1876, 0.3913919483018658, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 5793, 1.1876, 1.3424212517633736, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 5793, 1.03915, 0.6908089773820663, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 5793, 1.7814, 1.1842439612263993, 1);
INSERT INTO building(id, name, level) VALUES (33560302, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33560302, 4.788, 0.9643282529391991, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33560302, 9.576, 1.8226114865725622, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33560302, 11.97, 3.870686721816424, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33560302, 2.394, 1.1581272252035353, 1);
