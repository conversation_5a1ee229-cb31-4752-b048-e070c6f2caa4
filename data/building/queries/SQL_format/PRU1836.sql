
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 58.006507694648725, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 58.855913385268856, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 68.66523228281366, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 24.11803417749416, 2593.0198287234243);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 15.260311218828036, 69.4281059140697);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 24.022625444340953, 231.9604581400628);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 21.71647940303923, 235.2013204863097);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 48.404068657861984, 163.21337535255674);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 26.04753898303582, 570.3361376101255);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 25.415724704564013, 436.175014892174);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 38.51019336472723, 24.56193142700652);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 16.514837775889177, 308.1300024743806);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 31.470027811587197, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30.52194185584568, 67.5994609157516);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 51.950932927009845, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 23.65842002434278, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 53.846153846153854, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 63.453481258559364, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 63.61265772521836, 62.12231084674532);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 23.192335615208588, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 39.28903581625213, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 66.01301538929744, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 108.66240616073478, 178.84670420168075);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 35.560223704591415, 286.4777259800759);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 30.227255524923642, 20.204066028035246);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 38.23867436003304, 1006.7348983333329);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 71.97534225078299, 231.88916999999992);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 50.99599461208748, 24.600527902554795);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 26.081811029411757);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 77.77538462944848, 208.65448823529405);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 350.0, 0.6974256288429289);
INSERT INTO building(id, name, level) VALUES (425, "building_fishing_wharflevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 425, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (426, "building_rye_farmlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 426, 5.0, 7.169104890349659, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 426, 1.0, 1.0242740726669248, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 426, 30.0, 30.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 426, 15.0, 15.0, 1);
INSERT INTO building(id, name, level) VALUES (427, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 427, 30.0, 21.76393164501168, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 427, 30.0, 30.728222180007744, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 427, 120.0, 103.52786329002336, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 427, 15.0, 12.94098291125292, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 427, 75.0, 64.7049145562646, 3);
INSERT INTO building(id, name, level) VALUES (428, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 428, 10.0, 10.242740726669247, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 428, 80.0, 80.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 428, 20.0, 20.0, 2);
INSERT INTO building(id, name, level) VALUES (429, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 429, 10.0, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (430, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 430, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (453, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 453, 20.0, 12.435383675798024, 2);
INSERT INTO building(id, name, level) VALUES (454, "building_chemical_plantslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 454, 60.0, 118.60815505107817, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 454, 30.0, 29.304077525539096, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 454, 20.0, 12.032711381993439, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 454, 150.0, 128.9219076642154, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 454, 40.0, 34.379175377124106, 2);
INSERT INTO building(id, name, level) VALUES (455, "building_steel_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 455, 60.0, 58.60815505107819, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 455, 80.0, 48.130845527973754, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 455, 130.0, 102.59847996348006, 2);
INSERT INTO building(id, name, level) VALUES (456, "building_iron_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 456, 40.0, 39.07210336738546, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 456, 40.0, 40.97096290667699, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 456, 160.0, 158.1442067347709, 4);
INSERT INTO building(id, name, level) VALUES (457, "building_sulfur_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 457, 50.0, 48.840129209231826, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 457, 50.0, 51.21370363334624, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 457, 200.0, 197.68025841846364, 5);
INSERT INTO building(id, name, level) VALUES (458, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 458, 10.0, 14.338209780699318, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 458, 2.0, 2.0485481453338497, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 458, 60.0, 60.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 458, 30.0, 30.0, 2);
INSERT INTO building(id, name, level) VALUES (459, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 459, 15.0, 11.797396922140512, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 459, 15.0, 15.39131095207634, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 459, 15.0, 15.39131095207634, 15);
INSERT INTO building(id, name, level) VALUES (462, "building_furniture_manufacturieslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 462, 30.0, 21.95474911131809, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 462, 60.0, 53.13408238784309, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 462, 30.0, 16.153846153846153, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 462, 15.0, 15.364111090003872, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 462, 135.0, 106.5100910164715, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 462, 60.0, 47.33781822954289, 3);
INSERT INTO building(id, name, level) VALUES (463, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 463, 25.0, 25.60685181667312, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 463, 200.0, 200.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 463, 50.0, 50.0, 5);
INSERT INTO building(id, name, level) VALUES (464, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 464, 10.0, 14.338209780699318, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 464, 2.0, 2.0485481453338497, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 464, 60.0, 60.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 464, 30.0, 30.0, 2);
INSERT INTO building(id, name, level) VALUES (465, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 465, 20.0, 14.509287763341119, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 465, 20.0, 20.485481453338494, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 465, 80.0, 69.01857552668224, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 465, 10.0, 8.62732194083528, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 465, 50.0, 43.1366097041764, 2);
INSERT INTO building(id, name, level) VALUES (466, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 466, 25.0, 19.662328203567522, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 466, 25.0, 25.652184920127237, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 466, 25.0, 25.652184920127237, 25);
INSERT INTO building(id, name, level) VALUES (469, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 469, 100.0, 62.17691837899012, 5);
INSERT INTO building(id, name, level) VALUES (470, "building_construction_sectorlevel", 2);
INSERT INTO building(id, name, level) VALUES (471, "building_tooling_workshopslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 471, 240.0, 212.53632955137235, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 471, 159.99999999999997, 102.59847996348005, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 471, 639.9999999999999, 488.5787326621231, 8);
INSERT INTO building(id, name, level) VALUES (472, "building_wheat_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 472, 20.0, 28.676419561398635, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 472, 4.0, 4.097096290667699, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 472, 139.99999999999997, 139.99999999999997, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 472, 32.0, 32.0, 4);
INSERT INTO building(id, name, level) VALUES (473, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 473, 10.0, 7.864931281427008, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 473, 10.0, 10.260873968050895, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 473, 10.0, 10.260873968050895, 10);
INSERT INTO building(id, name, level) VALUES (477, "building_arms_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 477, 40.0, 24.065422763986877, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 477, 20.0, 10.76923076923077, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 477, 30.0, 17.101456613418158, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 477, 30.0, 17.101456613418158, 2);
INSERT INTO building(id, name, level) VALUES (478, "building_munition_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 478, 20.0, 43.92894631521414, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 478, 20.0, 11.459725125708037, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 478, 50.0, 39.324656407135045, 1);
INSERT INTO building(id, name, level) VALUES (479, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 479, 120.0, 87.81899644527236, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 479, 135.0, 98.7963710009314, 3);
INSERT INTO building(id, name, level) VALUES (480, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 480, 10.0, 10.242740726669247, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 480, 40.0, 40.0, 1);
INSERT INTO building(id, name, level) VALUES (481, "building_wheat_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 481, 25.0, 35.84552445174829, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 481, 5.0, 5.1213703633346235, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 481, 175.0, 175.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 481, 40.0, 40.0, 5);
INSERT INTO building(id, name, level) VALUES (482, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 482, 10.0, 10.242740726669247, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 482, 80.0, 80.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 482, 20.0, 20.0, 2);
INSERT INTO building(id, name, level) VALUES (483, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 483, 10.0, 7.864931281427008, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 483, 10.0, 10.260873968050895, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 483, 10.0, 10.260873968050895, 10);
INSERT INTO building(id, name, level) VALUES (484, "building_arms_industrylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 484, 120.0, 72.19626829196064, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 484, 60.0, 32.30769230769231, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 484, 90.0, 51.30436984025447, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 484, 90.0, 51.30436984025447, 6);
INSERT INTO building(id, name, level) VALUES (485, "building_munition_plantslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 485, 40.0, 87.85789263042828, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 485, 40.0, 22.919450251416073, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 485, 100.0, 78.64931281427009, 2);
INSERT INTO building(id, name, level) VALUES (486, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 486, 19.999999999999996, 20.48548145333849, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 486, 79.99999999999999, 79.99999999999999, 2);
INSERT INTO building(id, name, level) VALUES (487, "building_wheat_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 487, 15.0, 21.507314671048977, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 487, 3.0, 3.0728222180007747, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 487, 104.99999999999999, 104.99999999999999, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 487, 24.0, 24.0, 3);
INSERT INTO building(id, name, level) VALUES (488, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 488, 15.0, 11.797396922140512, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 488, 15.0, 15.39131095207634, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 488, 15.0, 15.39131095207634, 15);
INSERT INTO building(id, name, level) VALUES (489, "building_government_administrationlevel", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 489, 259.99999999999994, 161.65998778537428, 13);
INSERT INTO building(id, name, level) VALUES (490, "building_construction_sectorlevel", 3);
INSERT INTO building(id, name, level) VALUES (491, "building_universitylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 491, 20.0, 12.435383675798024, 4);
INSERT INTO building(id, name, level) VALUES (492, "building_arms_industrylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 492, 100.0, 60.163556909967184, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 492, 50.0, 26.923076923076923, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 492, 75.0, 42.753641533545384, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 492, 75.0, 42.753641533545384, 5);
INSERT INTO building(id, name, level) VALUES (493, "building_furniture_manufacturieslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 493, 50.0, 36.59124851886349, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 493, 100.0, 88.55680397973848, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 493, 50.0, 26.923076923076923, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 493, 25.0, 25.60685181667312, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 493, 225.0, 177.51681836078586, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 493, 100.0, 78.89636371590483, 5);
INSERT INTO building(id, name, level) VALUES (494, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 494, 60.0, 53.13408238784309, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 494, 45.0, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 494, 30.0, 65.89341947282121, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 494, 60.0, 37.7113607959477, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 494, 75.0, 47.13920099493462, 3);
INSERT INTO building(id, name, level) VALUES (495, "building_paper_millslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 495, 119.99999999999999, 106.26816477568616, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 495, 40.0, 79.07210336738545, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 495, 279.99999999999994, 263.9795255716338, 4);
INSERT INTO building(id, name, level) VALUES (496, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 496, 25.0, 19.662328203567522, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 496, 25.0, 25.652184920127237, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 496, 25.0, 25.652184920127237, 25);
INSERT INTO building(id, name, level) VALUES (1054, "building_textile_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1054, 319.99999999999994, 234.18399052072627, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1054, 359.99999999999994, 263.45698933581707, 8);
INSERT INTO building(id, name, level) VALUES (1055, "building_arms_industrylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1055, 100.0, 60.163556909967184, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1055, 50.0, 26.923076923076923, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 1055, 75.0, 42.753641533545384, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 1055, 75.0, 42.753641533545384, 5);
INSERT INTO building(id, name, level) VALUES (1056, "building_coal_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1056, 50.00000000000001, 51.21370363334625, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1056, 200.00000000000003, 200.00000000000003, 5);
INSERT INTO building(id, name, level) VALUES (1057, "building_lead_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1057, 50.0, 48.840129209231826, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1057, 50.0, 51.21370363334624, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 1057, 200.0, 197.68025841846364, 5);
INSERT INTO building(id, name, level) VALUES (1058, "building_iron_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1058, 30.0, 29.304077525539096, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1058, 30.0, 30.728222180007744, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1058, 120.0, 118.60815505107819, 3);
INSERT INTO building(id, name, level) VALUES (1059, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1059, 20.0, 14.509287763341119, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1059, 20.0, 20.485481453338494, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1059, 80.0, 69.01857552668224, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1059, 10.0, 8.62732194083528, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1059, 50.0, 43.1366097041764, 2);
INSERT INTO building(id, name, level) VALUES (1060, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1060, 15.0, 11.797396922140512, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1060, 15.0, 15.39131095207634, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1060, 15.0, 15.39131095207634, 15);
INSERT INTO building(id, name, level) VALUES (1066, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1066, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1067, "building_rye_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1067, 15.0, 21.507314671048977, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1067, 90.0, 90.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1067, 45.0, 45.0, 3);
INSERT INTO building(id, name, level) VALUES (1068, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1068, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (1069, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1069, 10.0, 7.864931281427008, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1069, 10.0, 10.260873968050895, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1069, 10.0, 10.260873968050895, 10);
INSERT INTO building(id, name, level) VALUES (1070, "building_textile_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1070, 80.0, 58.545997630181574, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1070, 90.0, 65.86424733395427, 2);
INSERT INTO building(id, name, level) VALUES (1071, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1071, 25.0, 25.60685181667312, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1071, 200.0, 200.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1071, 50.0, 50.0, 5);
INSERT INTO building(id, name, level) VALUES (1072, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1072, 10.0, 14.338209780699318, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1072, 60.0, 60.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1072, 30.0, 30.0, 2);
INSERT INTO building(id, name, level) VALUES (1073, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1073, 10.0, 7.864931281427008, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1073, 10.0, 10.260873968050895, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1073, 10.0, 10.260873968050895, 10);
INSERT INTO building(id, name, level) VALUES (1074, "building_food_industrylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1074, 200.0, 145.0928776334112, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1074, 125.0, 8.3555854884029, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1074, 75.0, 29.711590202785473, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1074, 300.0, 118.84636081114189, 5);
INSERT INTO building(id, name, level) VALUES (1075, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1075, 10.0, 14.338209780699318, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1075, 60.0, 60.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1075, 20.0, 20.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1075, 10.0, 10.0, 2);
INSERT INTO building(id, name, level) VALUES (1076, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1076, 30.0, 21.76393164501168, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1076, 30.0, 30.728222180007744, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1076, 120.0, 103.52786329002336, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1076, 15.0, 12.94098291125292, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1076, 75.0, 64.7049145562646, 3);
INSERT INTO building(id, name, level) VALUES (1077, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1077, 15.0, 11.797396922140512, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1077, 15.0, 15.39131095207634, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1077, 15.0, 15.39131095207634, 15);
INSERT INTO building(id, name, level) VALUES (2904, "building_subsistence_farmslevel", 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2904, 114.931, 114.931, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2904, 22.9862, 22.9862, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2904, 22.9862, 22.9862, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2904, 22.9862, 22.9862, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2904, 22.9862, 22.9862, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2904, 22.9862, 22.9862, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2904, 32.18068, 32.18068, 46);
INSERT INTO building(id, name, level) VALUES (3870, "building_subsistence_farmslevel", 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3870, 191.44125, 191.44125, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3870, 38.28825, 38.28825, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3870, 38.28825, 38.28825, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3870, 38.28825, 38.28825, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3870, 38.28825, 38.28825, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3870, 38.28825, 38.28825, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3870, 53.60355, 53.60355, 85);
INSERT INTO building(id, name, level) VALUES (3871, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3871, 5.0, 4.427840198986924, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3871, 5.0, 1.06455704579694, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3871, 40.0, 21.96958897913546, 1);
INSERT INTO building(id, name, level) VALUES (3872, "building_subsistence_farmslevel", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3872, 124.65375, 124.65375, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3872, 24.93075, 24.93075, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3872, 24.93075, 24.93075, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3872, 24.93075, 24.93075, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3872, 24.93075, 24.93075, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3872, 24.93075, 24.93075, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3872, 34.90305, 34.90305, 50);
INSERT INTO building(id, name, level) VALUES (3873, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3873, 10.0, 8.855680397973847, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3873, 10.0, 2.12911409159388, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3873, 80.0, 43.93917795827092, 2);
INSERT INTO building(id, name, level) VALUES (3876, "building_subsistence_farmslevel", 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3876, 132.43242, 132.43242, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3876, 26.48648, 26.48648, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3876, 26.48648, 26.48648, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3876, 26.48648, 26.48648, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3876, 26.48648, 26.48648, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3876, 26.48648, 26.48648, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3876, 37.08107, 37.08107, 53);
INSERT INTO building(id, name, level) VALUES (3877, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3877, 5.0, 4.427840198986924, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3877, 5.0, 1.06455704579694, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3877, 40.0, 21.96958897913546, 1);
INSERT INTO building(id, name, level) VALUES (3879, "building_subsistence_farmslevel", 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3879, 92.19937, 92.19937, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3879, 18.43987, 18.43987, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3879, 18.43987, 18.43987, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3879, 18.43987, 18.43987, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3879, 18.43987, 18.43987, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3879, 18.43987, 18.43987, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3879, 25.81582, 25.81582, 37);
INSERT INTO building(id, name, level) VALUES (3880, "building_subsistence_farmslevel", 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3880, 94.71595, 94.71595, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3880, 18.94319, 18.94319, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3880, 18.94319, 18.94319, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3880, 18.94319, 18.94319, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3880, 18.94319, 18.94319, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3880, 18.94319, 18.94319, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3880, 26.52046, 26.52046, 38);
INSERT INTO building(id, name, level) VALUES (3881, "building_subsistence_farmslevel", 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3881, 43.9496, 43.9496, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3881, 8.78992, 8.78992, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3881, 8.78992, 8.78992, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3881, 8.78992, 8.78992, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3881, 8.78992, 8.78992, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3881, 8.78992, 8.78992, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3881, 12.30588, 12.30588, 32);
INSERT INTO building(id, name, level) VALUES (3882, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3882, 15.0, 13.283520596960772, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3882, 15.0, 3.1936711373908193, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3882, 120.0, 65.90876693740636, 3);
INSERT INTO building(id, name, level) VALUES (3884, "building_subsistence_farmslevel", 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3884, 101.3778, 101.3778, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3884, 20.27556, 20.27556, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3884, 20.27556, 20.27556, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3884, 20.27556, 20.27556, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3884, 20.27556, 20.27556, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3884, 20.27556, 20.27556, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3884, 28.38578, 28.38578, 72);
INSERT INTO building(id, name, level) VALUES (3885, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3885, 5.0, 4.427840198986924, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3885, 5.0, 1.06455704579694, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3885, 40.0, 21.96958897913546, 1);
INSERT INTO building(id, name, level) VALUES (3886, "building_subsistence_farmslevel", 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3886, 71.5068, 71.5068, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3886, 14.30136, 14.30136, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3886, 14.30136, 14.30136, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3886, 14.30136, 14.30136, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3886, 14.30136, 14.30136, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3886, 14.30136, 14.30136, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3886, 20.0219, 20.0219, 36);
INSERT INTO building(id, name, level) VALUES (3887, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3887, 10.0, 8.855680397973847, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3887, 10.0, 2.12911409159388, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3887, 80.0, 43.93917795827092, 2);
INSERT INTO building(id, name, level) VALUES (3888, "building_subsistence_farmslevel", 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3888, 244.18905, 244.18905, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3888, 48.83781, 48.83781, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3888, 48.83781, 48.83781, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3888, 48.83781, 48.83781, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3888, 48.83781, 48.83781, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3888, 48.83781, 48.83781, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3888, 68.37293, 68.37293, 98);
INSERT INTO building(id, name, level) VALUES (3889, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3889, 20.0, 17.711360795947694, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3889, 20.0, 4.25822818318776, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3889, 160.0, 87.87835591654184, 4);
INSERT INTO building(id, name, level) VALUES (3890, "building_subsistence_farmslevel", 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3890, 94.88587, 94.88587, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3890, 18.97717, 18.97717, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3890, 18.97717, 18.97717, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3890, 18.97717, 18.97717, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3890, 18.97717, 18.97717, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3890, 18.97717, 18.97717, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3890, 26.56804, 26.56804, 45);
INSERT INTO building(id, name, level) VALUES (3891, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3891, 35.0, 30.994881392908468, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3891, 35.0, 7.451899320578579, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3891, 280.0, 153.78712285394818, 7);
INSERT INTO building(id, name, level) VALUES (3915, "building_subsistence_farmslevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3915, 12.5, 12.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3915, 2.5, 2.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3915, 2.5, 2.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3915, 2.5, 2.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3915, 2.5, 2.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3915, 2.5, 2.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3915, 3.5, 3.5, 5);
INSERT INTO building(id, name, level) VALUES (3968, "building_trade_centerlevel", 19);
INSERT INTO building(id, name, level) VALUES (3969, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3969, 5.0, 4.427840198986924, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3969, 5.0, 1.06455704579694, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3969, 40.0, 21.96958897913546, 1);
INSERT INTO building(id, name, level) VALUES (4031, "building_trade_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (4032, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4032, 5.0, 4.427840198986924, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 4032, 5.0, 1.06455704579694, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4032, 40.0, 21.96958897913546, 1);
INSERT INTO building(id, name, level) VALUES (4057, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4607, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4608, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4609, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4610, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4611, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4612, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4614, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4615, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4616, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4617, "building_conscription_centerlevel", 6);
