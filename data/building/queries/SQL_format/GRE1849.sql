
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 52.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 17.76229188491277, 121.07354888323897);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 26.365794218006805, 227.2576237689564);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 10.23801883965356, 40.50237381825401);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 11.434044245253839, 24.37744534594767);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 29.756993075327436, 50.33990741407413);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 49.49172896483074, 44.050028554871886);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 20.304677049889357, 36.06595499999999);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 73.86567267443873, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 52.5, 1.6988222238655415);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 1.4527392857142853);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 23.322498577863453, 11.81413227147138);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 31.78087606947741, 8.954053782491213);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 19.262771381578954, 8.223530000000002);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 52.5, 1.420875300358087);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 44.472755642036546, 78.49842591313157);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 6.779449999999998);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 1.6948624999999995);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (893, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 893, 5.0, 3.4593697028401427, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 893, 50.0, 34.59369702840143, 1);
INSERT INTO building(id, name, level) VALUES (921, "building_government_administrationlevel", 4);
INSERT INTO building(id, name, level) VALUES (922, "building_shipyardslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 922, 20.0, 57.273562616632724, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 922, 40.0, 93.2539437294977, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 922, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 922, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (923, "building_wheat_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 923, 75.0, 85.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 923, 40.00000000000001, 45.6, 5);
INSERT INTO building(id, name, level) VALUES (924, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 924, 5.0, 3.4593697028401427, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 924, 50.0, 34.59369702840143, 1);
INSERT INTO building(id, name, level) VALUES (925, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 925, 44.220495049504954, 44.6627, 2);
INSERT INTO building(id, name, level) VALUES (926, "building_logging_camplevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 926, 150.0, 171.0, 5);
INSERT INTO building(id, name, level) VALUES (927, "building_cotton_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 927, 141.49759375, 181.11692, 4);
INSERT INTO building(id, name, level) VALUES (928, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 928, 5.0, 3.4593697028401427, 1);
INSERT INTO building(id, name, level) VALUES (929, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 929, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 929, 5.0, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (930, "building_naval_baselevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 930, 4.0, 6.0, 2);
INSERT INTO building(id, name, level) VALUES (931, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 931, 3.0599999999999996, 3.3966, 2);
INSERT INTO building(id, name, level) VALUES (932, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 932, 8.907, 6.162521188639429, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 932, 89.07000000000001, 61.6252118863943, 2);
INSERT INTO building(id, name, level) VALUES (933, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 933, 5.0, 3.4593697028401427, 1);
INSERT INTO building(id, name, level) VALUES (934, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 934, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 934, 5.0, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (935, "building_naval_baselevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 935, 6.0, 9.0, 3);
INSERT INTO building(id, name, level) VALUES (2949, "building_subsistence_farmslevel", 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2949, 0.0015727272727272725, 0.00173, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2949, 0.0003090909090909091, 0.00034, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2949, 0.0003090909090909091, 0.00034, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2949, 0.0003090909090909091, 0.00034, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2949, 0.0003090909090909091, 0.00034, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2949, 0.0003090909090909091, 0.00034, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2949, 0.0004363636363636363, 0.00048, 21);
INSERT INTO building(id, name, level) VALUES (3382, "building_subsistence_farmslevel", 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3382, 26.769045454545452, 29.44595, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3382, 5.353809090909091, 5.88919, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3382, 5.353809090909091, 5.88919, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3382, 5.353809090909091, 5.88919, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3382, 5.353809090909091, 5.88919, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3382, 5.353809090909091, 5.88919, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3382, 7.4953272727272715, 8.24486, 34);
INSERT INTO building(id, name, level) VALUES (3383, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3383, 11.496892156862744, 26.803263356504544, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3383, 57.4845, 57.4845, 3);
INSERT INTO building(id, name, level) VALUES (3564, "building_subsistence_orchardslevel", 7);
INSERT INTO building(id, name, level) VALUES (3914, "building_trade_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4236, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4697, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 4697, 22.13225, 22.13225, 1);
INSERT INTO building(id, name, level) VALUES (4902, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 4902, 21.779999999999998, 23.958, 1);
INSERT INTO building(id, name, level) VALUES (16782330, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16782330, 89.99999999999999, 100.8, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 16782330, 15.0, 16.8, 3);
INSERT INTO building(id, name, level) VALUES (16783436, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783436, 40.0, 114.54712523326545, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16783436, 45.0, 45.0, 1);
