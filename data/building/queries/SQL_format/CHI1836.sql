
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 74.39657627550262, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 15.606327981751178, 34005.500343081614);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 21.538481629250143, 780.0011641194315);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 15.814875995746576, 4548.694235441974);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 22.512329300287213, 2689.794524558019);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 28.017245034415534, 8062.020220600209);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 25.736935419333168, 7607.064989856635);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 36.65948657229709, 334.5162365575099);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 11.912800744222439, 2307.679311666665);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 51.834696219297314, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 12.038611297983813, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 28.079520965012513, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 30.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 36.37362637362637, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 51.22165873569104, 161.8982628129463);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 14.092744024366368, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 92.80117020878045, 2024.6346431265415);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 42.73120715730779, 1177.2005953225573);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 39.22122152300156, 2259.348444735977);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 22.334912422792847, 8335.066402881186);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 36.837109932060855, 2756.7395950000036);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 24.59725726320241, 692.2802566122953);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 70.0, 176.6863427358829);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 80.73344866791328, 2472.869735082587);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 84.60408827122642, 706.0238439689443);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 85.96440813351157, 1785.6096515500938);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (2166, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2166, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (2384, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2384, 89.64, 91.4328, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2384, 14.94, 15.2388, 3);
INSERT INTO building(id, name, level) VALUES (2385, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2385, 89.64000000000001, 73.5048, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2385, 14.940000000000001, 12.2508, 3);
INSERT INTO building(id, name, level) VALUES (2386, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2386, 99.99999999999999, 70.40228190090181, 10);
INSERT INTO building(id, name, level) VALUES (2387, "building_logging_camplevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2387, 99.99999999999999, 109.0, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2387, 99.99999999999999, 109.0, 10);
INSERT INTO building(id, name, level) VALUES (2388, "building_opium_plantationlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (44, 2388, 119.52, 125.496, 6);
INSERT INTO building(id, name, level) VALUES (2389, "building_paper_millslevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2389, 360.0, 299.7040967931068, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2389, 479.99999999999994, 399.60546239080907, 12);
INSERT INTO building(id, name, level) VALUES (2390, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2391, "building_government_administrationlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2391, 79.99999999999999, 56.32182552072145, 8);
INSERT INTO building(id, name, level) VALUES (2392, "building_logging_camplevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2392, 40.0, 41.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2392, 40.0, 41.2, 4);
INSERT INTO building(id, name, level) VALUES (2393, "building_tea_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2393, 199.19999999999996, 217.128, 10);
INSERT INTO building(id, name, level) VALUES (2394, "building_opium_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (44, 2394, 199.19999999999996, 217.128, 10);
INSERT INTO building(id, name, level) VALUES (2395, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (2396, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2396, 99.99999999999999, 70.40228190090181, 10);
INSERT INTO building(id, name, level) VALUES (2397, "building_furniture_manufacturieslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2397, 70.0, 97.08849185260735, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2397, 140.0, 116.55159319731932, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2397, 70.0, 79.625, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2397, 175.0, 165.22983049888305, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2397, 140.0, 132.18386439910645, 7);
INSERT INTO building(id, name, level) VALUES (2398, "building_glassworkslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2398, 240.0, 199.80273119540456, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2398, 120.0, 199.12032, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2398, 79.99999999999999, 73.30045519923408, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2398, 200.0, 183.25113799808523, 8);
INSERT INTO building(id, name, level) VALUES (2399, "building_logging_camplevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2399, 50.0, 52.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2399, 50.0, 52.0, 5);
INSERT INTO building(id, name, level) VALUES (2400, "building_opium_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (44, 2400, 99.6, 103.584, 5);
INSERT INTO building(id, name, level) VALUES (2401, "building_dye_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2401, 199.2, 213.144, 8);
INSERT INTO building(id, name, level) VALUES (2402, "building_tea_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2402, 79.68, 82.0704, 4);
INSERT INTO building(id, name, level) VALUES (2403, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (2404, "building_government_administrationlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2404, 79.99999999999999, 56.32182552072145, 8);
INSERT INTO building(id, name, level) VALUES (2405, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2405, 5.0, 36.65022759961705, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2405, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (2406, "building_rice_farmlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2406, 159.35999999999999, 170.5152, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2406, 47.80799999999999, 51.15456, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2406, 71.712, 76.73184, 8);
INSERT INTO building(id, name, level) VALUES (2407, "building_dye_plantationlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2407, 149.4, 156.87, 6);
INSERT INTO building(id, name, level) VALUES (2408, "building_silk_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2408, 199.2, 266.928, 10);
INSERT INTO building(id, name, level) VALUES (2409, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2410, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2410, 29.88, 23.904, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2410, 4.9799999999999995, 3.984, 1);
INSERT INTO building(id, name, level) VALUES (2411, "building_barrackslevel", 5);
INSERT INTO building(id, name, level) VALUES (2412, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2412, 59.76, 48.4056, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2412, 9.96, 8.0676, 2);
INSERT INTO building(id, name, level) VALUES (2413, "building_barrackslevel", 5);
INSERT INTO building(id, name, level) VALUES (2414, "building_government_administrationlevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2414, 90.0, 63.36205371081164, 9);
INSERT INTO building(id, name, level) VALUES (2415, "building_banana_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2415, 239.03999999999996, 255.7728, 8);
INSERT INTO building(id, name, level) VALUES (2416, "building_rice_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2416, 139.44, 147.8064, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2416, 41.832, 44.34192, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2416, 62.74799999999999, 66.51288, 7);
INSERT INTO building(id, name, level) VALUES (2417, "building_tea_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2417, 159.35999999999999, 170.5152, 8);
INSERT INTO building(id, name, level) VALUES (2418, "building_dye_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2418, 249.0, 271.41, 10);
INSERT INTO building(id, name, level) VALUES (2419, "building_silk_plantationlevel", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2419, 318.72, 446.208, 16);
INSERT INTO building(id, name, level) VALUES (2420, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2421, "building_government_administrationlevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2421, 200.0, 140.80456380180365, 20);
INSERT INTO building(id, name, level) VALUES (2422, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2422, 75.0, 104.02338412779356, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2422, 225.0, 187.31506049569177, 3);
INSERT INTO building(id, name, level) VALUES (2423, "building_tooling_workshopslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2423, 119.99999999999999, 99.90136559770227, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2423, 80.0, 120.0, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 2423, 239.99999999999997, 219.90136559770227, 4);
INSERT INTO building(id, name, level) VALUES (2424, "building_furniture_manufacturieslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2424, 50.0, 69.34892275186239, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2424, 100.0, 83.25113799808523, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2424, 50.0, 56.875, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2424, 125.0, 118.02130749920218, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2424, 100.0, 94.41704599936175, 5);
INSERT INTO building(id, name, level) VALUES (2425, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2425, 29.88, 30.4776, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2425, 26.892, 27.42984, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2425, 17.928, 18.28656, 3);
INSERT INTO building(id, name, level) VALUES (2426, "building_tea_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2426, 99.6, 103.584, 5);
INSERT INTO building(id, name, level) VALUES (2427, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2428, "building_forbidden_citylevel", 1);
INSERT INTO building(id, name, level) VALUES (2429, "building_logging_camplevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2429, 40.0, 41.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2429, 40.0, 41.2, 4);
INSERT INTO building(id, name, level) VALUES (2430, "building_livestock_ranchlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2430, 298.8, 325.692, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2430, 49.79999999999999, 54.282, 10);
INSERT INTO building(id, name, level) VALUES (2431, "building_barrackslevel", 5);
INSERT INTO building(id, name, level) VALUES (2432, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2432, 60.0, 42.241369140541096, 6);
INSERT INTO building(id, name, level) VALUES (2433, "building_iron_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2433, 15.0, 109.95068279885113, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2433, 60.0, 60.0, 3);
INSERT INTO building(id, name, level) VALUES (2434, "building_logging_camplevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2434, 79.99999999999999, 85.6, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2434, 79.99999999999999, 85.6, 8);
INSERT INTO building(id, name, level) VALUES (2435, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2435, 89.64, 91.4328, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2435, 14.94, 15.2388, 3);
INSERT INTO building(id, name, level) VALUES (2436, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2437, "building_logging_camplevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2437, 60.0, 63.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2437, 60.0, 63.0, 6);
INSERT INTO building(id, name, level) VALUES (2438, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2438, 59.76, 60.3576, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2438, 9.959999999999999, 10.0596, 2);
INSERT INTO building(id, name, level) VALUES (2439, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (2440, "building_government_administrationlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2440, 70.0, 49.28159733063128, 7);
INSERT INTO building(id, name, level) VALUES (2441, "building_wheat_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2441, 69.72, 84.3612, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2441, 62.748, 75.92508, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2441, 41.832, 50.61672, 7);
INSERT INTO building(id, name, level) VALUES (2442, "building_tea_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2442, 59.76, 60.9552, 3);
INSERT INTO building(id, name, level) VALUES (2443, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (2444, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2444, 40.0, 28.160912760360727, 4);
INSERT INTO building(id, name, level) VALUES (2445, "building_tea_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2445, 59.76, 60.9552, 3);
INSERT INTO building(id, name, level) VALUES (2446, "building_barrackslevel", 5);
INSERT INTO building(id, name, level) VALUES (2447, "building_livestock_ranchlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2447, 179.28, 188.244, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2447, 29.88, 31.374, 6);
INSERT INTO building(id, name, level) VALUES (2448, "building_barrackslevel", 5);
INSERT INTO building(id, name, level) VALUES (2449, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2449, 40.0, 28.160912760360727, 4);
INSERT INTO building(id, name, level) VALUES (2450, "building_tea_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2450, 99.6, 103.584, 5);
INSERT INTO building(id, name, level) VALUES (2451, "building_barrackslevel", 15);
INSERT INTO building(id, name, level) VALUES (2452, "building_livestock_ranchlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2452, 149.4, 155.376, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2452, 24.9, 25.896, 5);
INSERT INTO building(id, name, level) VALUES (2453, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (2454, "building_government_administrationlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2454, 119.99999999999999, 84.48273828108218, 12);
INSERT INTO building(id, name, level) VALUES (2455, "building_paper_millslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2455, 119.99999999999999, 99.90136559770227, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2455, 160.0, 133.20182079693637, 4);
INSERT INTO building(id, name, level) VALUES (2456, "building_tea_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2456, 199.19999999999996, 217.128, 10);
INSERT INTO building(id, name, level) VALUES (2457, "building_silk_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2457, 199.2, 266.928, 10);
INSERT INTO building(id, name, level) VALUES (2458, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2459, "building_government_administrationlevel", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2459, 129.99999999999997, 91.52296647117234, 13);
INSERT INTO building(id, name, level) VALUES (2460, "building_banana_plantationlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2460, 209.16, 221.7096, 7);
INSERT INTO building(id, name, level) VALUES (2461, "building_fishing_wharflevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2461, 125.0, 130.0, 5);
INSERT INTO building(id, name, level) VALUES (2462, "building_silk_plantationlevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2462, 398.40000000000003, 573.696, 20);
INSERT INTO building(id, name, level) VALUES (2463, "building_rice_farmlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2463, 159.35999999999999, 170.5152, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2463, 47.80799999999999, 51.15456, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2463, 71.712, 76.73184, 8);
INSERT INTO building(id, name, level) VALUES (2464, "building_dye_plantationlevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2464, 298.79999999999995, 331.668, 12);
INSERT INTO building(id, name, level) VALUES (2465, "building_shipyardslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2465, 40.0, 55.4791382014899, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2465, 80.0, 66.60091039846819, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 2465, 30.0, 27.487670699712787, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 2465, 40.0, 36.65022759961705, 2);
INSERT INTO building(id, name, level) VALUES (2466, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2467, "building_naval_baselevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 2467, 30.0, 27.487670699712787, 15);
INSERT INTO building(id, name, level) VALUES (2468, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2468, 15.0, 18.325113799808523, 3);
INSERT INTO building(id, name, level) VALUES (2472, "building_government_administrationlevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2472, 109.99999999999999, 77.44251009099199, 11);
INSERT INTO building(id, name, level) VALUES (2473, "building_silk_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2473, 199.2, 266.928, 10);
INSERT INTO building(id, name, level) VALUES (2474, "building_rice_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2474, 99.6, 103.584, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2474, 29.88, 31.0752, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2474, 44.82, 46.6128, 5);
INSERT INTO building(id, name, level) VALUES (2475, "building_dye_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2475, 249.0, 271.41, 10);
INSERT INTO building(id, name, level) VALUES (2476, "building_fishing_wharflevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2476, 100.0, 103.0, 4);
INSERT INTO building(id, name, level) VALUES (2477, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2478, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2478, 99.99999999999999, 70.40228190090181, 10);
INSERT INTO building(id, name, level) VALUES (2479, "building_textile_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2479, 240.0, 332.87482920893945, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2479, 79.99999999999999, 1177.272, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2479, 200.0, 200.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2479, 159.99999999999997, 159.99999999999997, 8);
INSERT INTO building(id, name, level) VALUES (2480, "building_tea_plantationlevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2480, 298.8, 340.632, 15);
INSERT INTO building(id, name, level) VALUES (2481, "building_rice_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2481, 79.68, 82.0704, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2481, 23.904, 24.62112, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2481, 35.856, 36.93168, 4);
INSERT INTO building(id, name, level) VALUES (2482, "building_fishing_wharflevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2482, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (2483, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (2484, "building_government_administrationlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2484, 119.99999999999999, 84.48273828108218, 12);
INSERT INTO building(id, name, level) VALUES (2485, "building_glassworkslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2485, 150.0, 124.87670699712784, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2485, 75.0, 124.4502, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2485, 50.0, 45.81278449952131, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2485, 125.0, 114.53196124880327, 5);
INSERT INTO building(id, name, level) VALUES (2486, "building_tea_plantationlevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2486, 298.8, 340.632, 15);
INSERT INTO building(id, name, level) VALUES (2487, "building_rice_farmlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2487, 159.35999999999999, 170.5152, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2487, 47.80799999999999, 51.15456, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2487, 71.712, 76.73184, 8);
INSERT INTO building(id, name, level) VALUES (2488, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2489, "building_government_administrationlevel", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2489, 170.0, 119.6838792315331, 17);
INSERT INTO building(id, name, level) VALUES (2490, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2490, 50.0, 69.34892275186239, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2490, 150.0, 124.87670699712784, 2);
INSERT INTO building(id, name, level) VALUES (2491, "building_glassworkslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2491, 300.0, 249.75341399425568, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2491, 150.0, 248.9004, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2491, 99.99999999999999, 91.6255689990426, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2491, 249.99999999999997, 229.0639224976065, 10);
INSERT INTO building(id, name, level) VALUES (2492, "building_tea_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2492, 199.19999999999996, 217.128, 10);
INSERT INTO building(id, name, level) VALUES (2493, "building_rice_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2493, 99.6, 103.584, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2493, 29.88, 31.0752, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2493, 44.82, 46.6128, 5);
INSERT INTO building(id, name, level) VALUES (2494, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2495, "building_government_administrationlevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2495, 150.0, 105.60342285135273, 15);
INSERT INTO building(id, name, level) VALUES (2496, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2496, 25.0, 34.674461375931195, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2496, 75.0, 62.43835349856392, 1);
INSERT INTO building(id, name, level) VALUES (2497, "building_glassworkslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2497, 180.0, 149.8520483965534, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2497, 90.0, 149.34024, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2497, 60.0, 54.975341399425574, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2497, 150.0, 137.4383534985639, 6);
INSERT INTO building(id, name, level) VALUES (2498, "building_tea_plantationlevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2498, 239.04, 265.3344, 12);
INSERT INTO building(id, name, level) VALUES (2499, "building_government_administrationlevel", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2499, 129.99999999999997, 91.52296647117234, 13);
INSERT INTO building(id, name, level) VALUES (2500, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2500, 10.0, 73.3004551992341, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2500, 40.0, 40.0, 2);
INSERT INTO building(id, name, level) VALUES (2501, "building_paper_millslevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2501, 360.0, 299.7040967931068, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2501, 479.99999999999994, 399.60546239080907, 12);
INSERT INTO building(id, name, level) VALUES (2502, "building_glassworkslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2502, 180.0, 149.8520483965534, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2502, 90.0, 149.34024, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2502, 60.0, 54.975341399425574, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2502, 150.0, 137.4383534985639, 6);
INSERT INTO building(id, name, level) VALUES (2503, "building_wheat_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2503, 39.84, 41.0352, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2503, 35.856, 36.93168, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2503, 23.904, 24.62112, 4);
INSERT INTO building(id, name, level) VALUES (2504, "building_tea_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2504, 199.19999999999996, 217.128, 10);
INSERT INTO building(id, name, level) VALUES (2505, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2506, "building_government_administrationlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2506, 119.99999999999999, 84.48273828108218, 12);
INSERT INTO building(id, name, level) VALUES (2507, "building_glassworkslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2507, 150.0, 124.87670699712784, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2507, 75.0, 124.4502, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2507, 50.0, 45.81278449952131, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2507, 125.0, 114.53196124880327, 5);
INSERT INTO building(id, name, level) VALUES (2508, "building_rice_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2508, 99.6, 103.584, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2508, 29.88, 31.0752, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2508, 44.82, 46.6128, 5);
INSERT INTO building(id, name, level) VALUES (2509, "building_silk_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2509, 199.2, 266.928, 10);
INSERT INTO building(id, name, level) VALUES (2510, "building_tea_plantationlevel", 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2510, 338.64000000000004, 392.8224, 17);
INSERT INTO building(id, name, level) VALUES (2511, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2512, "building_government_administrationlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2512, 119.99999999999999, 84.48273828108218, 12);
INSERT INTO building(id, name, level) VALUES (2513, "building_furniture_manufacturieslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2513, 99.99999999999999, 138.69784550372475, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2513, 199.99999999999997, 166.50227599617043, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2513, 99.99999999999999, 113.74999999999999, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2513, 249.99999999999997, 236.04261499840433, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2513, 199.99999999999997, 188.83409199872347, 10);
INSERT INTO building(id, name, level) VALUES (2514, "building_rice_farmlevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2514, 239.04, 265.3344, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2514, 71.71199999999999, 79.60032, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2514, 107.568, 119.40048, 12);
INSERT INTO building(id, name, level) VALUES (2515, "building_paper_millslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2515, 209.99999999999997, 174.82738979597895, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2515, 280.0, 233.1031863946386, 7);
INSERT INTO building(id, name, level) VALUES (2516, "building_tea_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2516, 199.19999999999996, 217.128, 10);
INSERT INTO building(id, name, level) VALUES (2517, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2518, "building_government_administrationlevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2518, 109.99999999999999, 77.44251009099199, 11);
INSERT INTO building(id, name, level) VALUES (2519, "building_rice_farmlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2519, 119.52, 125.496, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2519, 35.856, 37.6488, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2519, 53.784, 56.4732, 6);
INSERT INTO building(id, name, level) VALUES (2520, "building_tea_plantationlevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2520, 298.8, 340.632, 15);
INSERT INTO building(id, name, level) VALUES (2521, "building_barrackslevel", 15);
INSERT INTO building(id, name, level) VALUES (2522, "building_government_administrationlevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2522, 90.0, 63.36205371081164, 9);
INSERT INTO building(id, name, level) VALUES (2523, "building_paper_millslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2523, 300.0, 249.75341399425568, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2523, 399.99999999999994, 333.00455199234085, 10);
INSERT INTO building(id, name, level) VALUES (2524, "building_furniture_manufacturieslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2524, 99.99999999999999, 138.69784550372475, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2524, 199.99999999999997, 166.50227599617043, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2524, 99.99999999999999, 113.74999999999999, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2524, 249.99999999999997, 236.04261499840433, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2524, 199.99999999999997, 188.83409199872347, 10);
INSERT INTO building(id, name, level) VALUES (2525, "building_rice_farmlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2525, 119.52, 125.496, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2525, 35.856, 37.6488, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2525, 53.784, 56.4732, 6);
INSERT INTO building(id, name, level) VALUES (2526, "building_tea_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2526, 199.19999999999996, 217.128, 10);
INSERT INTO building(id, name, level) VALUES (2527, "building_barrackslevel", 15);
INSERT INTO building(id, name, level) VALUES (2528, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2528, 99.99999999999999, 70.40228190090181, 10);
INSERT INTO building(id, name, level) VALUES (2529, "building_furniture_manufacturieslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2529, 79.99999999999999, 110.95827640297979, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2529, 159.99999999999997, 133.20182079693635, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2529, 79.99999999999999, 90.99999999999999, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2529, 200.0, 188.8340919987235, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2529, 159.99999999999997, 151.06727359897877, 8);
INSERT INTO building(id, name, level) VALUES (2530, "building_rice_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2530, 79.68, 82.0704, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2530, 23.904, 24.62112, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2530, 35.856, 36.93168, 4);
INSERT INTO building(id, name, level) VALUES (2531, "building_paper_millslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2531, 300.0, 249.75341399425568, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2531, 399.99999999999994, 333.00455199234085, 10);
INSERT INTO building(id, name, level) VALUES (2532, "building_tea_plantationlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2532, 119.52, 125.496, 6);
INSERT INTO building(id, name, level) VALUES (2533, "building_silk_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2533, 199.2, 266.928, 10);
INSERT INTO building(id, name, level) VALUES (2534, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2535, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2535, 60.0, 42.241369140541096, 6);
INSERT INTO building(id, name, level) VALUES (2536, "building_logging_camplevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2536, 60.0, 63.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2536, 60.0, 63.0, 6);
INSERT INTO building(id, name, level) VALUES (2537, "building_rice_farmlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2537, 119.52, 125.496, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2537, 35.856, 37.6488, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2537, 53.784, 56.4732, 6);
INSERT INTO building(id, name, level) VALUES (2538, "building_tea_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2538, 159.35999999999999, 170.5152, 8);
INSERT INTO building(id, name, level) VALUES (2539, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (2540, "building_fishing_wharflevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2540, 125.0, 130.0, 5);
INSERT INTO building(id, name, level) VALUES (2541, "building_tea_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2541, 59.76, 60.9552, 3);
INSERT INTO building(id, name, level) VALUES (2542, "building_banana_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2542, 149.4, 155.376, 5);
INSERT INTO building(id, name, level) VALUES (2543, "building_barrackslevel", 5);
INSERT INTO building(id, name, level) VALUES (2544, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2544, 5.0, 6.108371266602841, 1);
INSERT INTO building(id, name, level) VALUES (2545, "building_government_administrationlevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2545, 90.0, 63.36205371081164, 9);
INSERT INTO building(id, name, level) VALUES (2546, "building_glassworkslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2546, 300.0, 249.75341399425568, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2546, 150.0, 248.9004, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2546, 99.99999999999999, 91.6255689990426, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2546, 249.99999999999997, 229.0639224976065, 10);
INSERT INTO building(id, name, level) VALUES (2547, "building_textile_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2547, 240.0, 332.87482920893945, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2547, 79.99999999999999, 1177.272, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2547, 200.0, 200.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2547, 159.99999999999997, 159.99999999999997, 8);
INSERT INTO building(id, name, level) VALUES (2548, "building_tea_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2548, 59.76, 60.9552, 3);
INSERT INTO building(id, name, level) VALUES (2549, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2549, 29.88, 30.4776, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2549, 26.892, 27.42984, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2549, 17.928, 18.28656, 3);
INSERT INTO building(id, name, level) VALUES (2550, "building_fishing_wharflevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2550, 150.0, 157.5, 6);
INSERT INTO building(id, name, level) VALUES (2551, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (2552, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2552, 5.0, 6.108371266602841, 1);
INSERT INTO building(id, name, level) VALUES (2553, "building_government_administrationlevel", 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2553, 160.0, 112.64365104144291, 16);
INSERT INTO building(id, name, level) VALUES (2554, "building_paper_millslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2554, 300.0, 249.75341399425568, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2554, 399.99999999999994, 333.00455199234085, 10);
INSERT INTO building(id, name, level) VALUES (2555, "building_tea_plantationlevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2555, 239.04, 265.3344, 12);
INSERT INTO building(id, name, level) VALUES (2556, "building_fishing_wharflevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2556, 100.0, 103.0, 4);
INSERT INTO building(id, name, level) VALUES (2557, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (2558, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2558, 5.0, 6.108371266602841, 1);
INSERT INTO building(id, name, level) VALUES (2559, "building_livestock_ranchlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2559, 119.52, 123.1056, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2559, 19.92, 20.5176, 4);
INSERT INTO building(id, name, level) VALUES (2560, "building_livestock_ranchlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2560, 179.28, 188.244, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2560, 29.88, 31.374, 6);
INSERT INTO building(id, name, level) VALUES (2897, "building_subsistence_rice_paddieslevel", 344);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2897, 1888.216, 1888.216, 344);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2897, 257.484, 257.484, 344);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2897, 257.484, 257.484, 344);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2897, 343.312, 343.312, 344);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2897, 343.312, 343.312, 344);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2897, 343.312, 343.312, 344);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2897, 514.968, 514.968, 344);
INSERT INTO building(id, name, level) VALUES (2898, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2898, 30.0, 24.97534139942557, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2898, 30.0, 18.778341264308956, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2898, 240.0, 175.0147306549381, 6);
INSERT INTO building(id, name, level) VALUES (2899, "building_subsistence_rice_paddieslevel", 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2899, 2129.732, 2129.732, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2899, 290.418, 290.418, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2899, 290.418, 290.418, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2899, 387.224, 387.224, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2899, 387.224, 387.224, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2899, 387.224, 387.224, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2899, 580.836, 580.836, 388);
INSERT INTO building(id, name, level) VALUES (2900, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2900, 30.0, 24.97534139942557, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2900, 30.0, 18.778341264308956, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2900, 240.0, 175.0147306549381, 6);
INSERT INTO building(id, name, level) VALUES (3062, "building_subsistence_pastureslevel", 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3062, 16.31167, 16.31167, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3062, 24.4675, 24.4675, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3062, 8.15583, 8.15583, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3062, 16.31167, 16.31167, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3062, 16.31167, 16.31167, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3062, 16.31167, 16.31167, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3062, 54.15474, 54.15474, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3062, 22.83633, 22.83633, 34);
INSERT INTO building(id, name, level) VALUES (3063, "building_subsistence_pastureslevel", 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3063, 12.948, 12.948, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3063, 19.422, 19.422, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3063, 6.474, 6.474, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3063, 12.948, 12.948, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3063, 12.948, 12.948, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3063, 12.948, 12.948, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3063, 42.98736, 42.98736, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3063, 18.1272, 18.1272, 26);
INSERT INTO building(id, name, level) VALUES (3701, "building_subsistence_pastureslevel", 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3701, 12.56404, 12.56404, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3701, 18.84606, 18.84606, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3701, 6.28202, 6.28202, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3701, 12.56404, 12.56404, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3701, 12.56404, 12.56404, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3701, 12.56404, 12.56404, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3701, 41.71262, 41.71262, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3701, 17.58966, 17.58966, 27);
INSERT INTO building(id, name, level) VALUES (3702, "building_subsistence_pastureslevel", 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3702, 18.3514375, 14.68115, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3702, 27.5271625, 22.02173, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3702, 9.1757125, 7.34057, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3702, 18.3514375, 14.68115, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3702, 18.3514375, 14.68115, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3702, 18.3514375, 14.68115, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3702, 60.926787499999996, 48.74143, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3702, 25.692012499999997, 20.55361, 37);
INSERT INTO building(id, name, level) VALUES (3703, "building_subsistence_rice_paddieslevel", 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3703, 1778.4360000000001, 2045.2014, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3703, 242.514, 278.8911, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3703, 242.514, 278.8911, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3703, 323.35200000000003, 371.8548, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3703, 323.35200000000003, 371.8548, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3703, 323.35200000000003, 371.8548, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3703, 485.028, 557.7822, 324);
INSERT INTO building(id, name, level) VALUES (3704, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3704, 25.0, 20.812784499521307, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3704, 25.0, 15.648617720257466, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3704, 200.0, 145.84560887911508, 5);
INSERT INTO building(id, name, level) VALUES (3705, "building_subsistence_rice_paddieslevel", 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3705, 713.57, 713.57, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3705, 97.305, 97.305, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3705, 97.305, 97.305, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3705, 129.74, 129.74, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3705, 129.74, 129.74, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3705, 129.74, 129.74, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3705, 194.61, 194.61, 130);
INSERT INTO building(id, name, level) VALUES (3706, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3706, 15.0, 12.487670699712785, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3706, 15.0, 9.389170632154478, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3706, 120.0, 87.50736532746905, 3);
INSERT INTO building(id, name, level) VALUES (3707, "building_subsistence_rice_paddieslevel", 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3707, 481.39822, 481.39822, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3707, 65.64521, 65.64521, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3707, 65.64521, 65.64521, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3707, 87.52695, 87.52695, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3707, 87.52695, 87.52695, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3707, 87.52695, 87.52695, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3707, 131.29042, 131.29042, 93);
INSERT INTO building(id, name, level) VALUES (3708, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3708, 30.0, 24.97534139942557, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3708, 30.0, 18.778341264308956, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3708, 240.0, 175.0147306549381, 6);
INSERT INTO building(id, name, level) VALUES (3709, "building_subsistence_rice_paddieslevel", 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3709, 966.064, 966.064, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3709, 131.736, 131.736, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3709, 131.736, 131.736, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3709, 175.648, 175.648, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3709, 175.648, 175.648, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3709, 175.648, 175.648, 176);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3709, 263.472, 263.472, 176);
INSERT INTO building(id, name, level) VALUES (3710, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3710, 15.0, 12.487670699712785, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3710, 15.0, 9.389170632154478, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3710, 120.0, 87.50736532746905, 3);
INSERT INTO building(id, name, level) VALUES (3711, "building_subsistence_pastureslevel", 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3711, 9.462087499999999, 7.56967, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3711, 14.193137499999999, 11.35451, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3711, 4.731037499999999, 3.78483, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3711, 9.462087499999999, 7.56967, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3711, 9.462087499999999, 7.56967, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3711, 9.462087499999999, 7.56967, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3711, 31.414149999999996, 25.13132, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3711, 13.246925, 10.59754, 19);
INSERT INTO building(id, name, level) VALUES (3712, "building_subsistence_pastureslevel", 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3712, 11.453999999999999, 9.1632, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3712, 17.180999999999997, 13.7448, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3712, 5.726999999999999, 4.5816, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3712, 11.453999999999999, 9.1632, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3712, 11.453999999999999, 9.1632, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3712, 11.453999999999999, 9.1632, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3712, 38.027274999999996, 30.42182, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3712, 16.0356, 12.82848, 23);
INSERT INTO building(id, name, level) VALUES (3713, "building_subsistence_rice_paddieslevel", 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3713, 1860.09466, 1860.09466, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3713, 253.64927, 253.64927, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3713, 253.64927, 253.64927, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3713, 338.19903, 338.19903, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3713, 338.19903, 338.19903, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3713, 338.19903, 338.19903, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3713, 507.29854, 507.29854, 351);
INSERT INTO building(id, name, level) VALUES (3714, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3714, 20.0, 16.650227599617047, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3714, 20.0, 12.518894176205974, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3714, 160.0, 116.67648710329209, 4);
INSERT INTO building(id, name, level) VALUES (3715, "building_subsistence_rice_paddieslevel", 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3715, 1822.348, 1822.348, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3715, 248.502, 248.502, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3715, 248.502, 248.502, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3715, 331.336, 331.336, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3715, 331.336, 331.336, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3715, 331.336, 331.336, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3715, 497.004, 497.004, 332);
INSERT INTO building(id, name, level) VALUES (3716, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3716, 30.0, 24.97534139942557, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3716, 30.0, 18.778341264308956, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3716, 240.0, 175.0147306549381, 6);
INSERT INTO building(id, name, level) VALUES (3717, "building_subsistence_rice_paddieslevel", 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3717, 298.188, 298.188, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3717, 40.662, 40.662, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3717, 40.662, 40.662, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3717, 54.216, 54.216, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3717, 54.216, 54.216, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3717, 54.216, 54.216, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3717, 81.324, 81.324, 75);
INSERT INTO building(id, name, level) VALUES (3718, "building_subsistence_farmslevel", 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3718, 156.6864, 156.6864, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3718, 26.1144, 26.1144, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3718, 26.1144, 26.1144, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3718, 26.1144, 26.1144, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3718, 26.1144, 26.1144, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3718, 26.1144, 26.1144, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3718, 36.56016, 36.56016, 60);
INSERT INTO building(id, name, level) VALUES (3719, "building_subsistence_rice_paddieslevel", 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3719, 1081.333, 1081.333, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3719, 147.4545, 147.4545, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3719, 147.4545, 147.4545, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3719, 196.606, 196.606, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3719, 196.606, 196.606, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3719, 196.606, 196.606, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3719, 294.909, 294.909, 197);
INSERT INTO building(id, name, level) VALUES (3720, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3720, 10.0, 8.325113799808523, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3720, 10.0, 6.259447088102987, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3720, 80.0, 58.338243551646045, 2);
INSERT INTO building(id, name, level) VALUES (3721, "building_subsistence_farmslevel", 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3721, 352.584, 352.584, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3721, 58.764, 58.764, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3721, 58.764, 58.764, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3721, 58.764, 58.764, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3721, 58.764, 58.764, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3721, 58.764, 58.764, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3721, 82.2696, 82.2696, 118);
INSERT INTO building(id, name, level) VALUES (3722, "building_subsistence_rice_paddieslevel", 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3722, 1937.5988434782612, 2228.23867, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3722, 264.2180173913044, 303.85072, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3722, 264.2180173913044, 303.85072, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3722, 352.29069565217395, 405.1343, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3722, 352.29069565217395, 405.1343, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3722, 352.29069565217395, 405.1343, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3722, 528.4360434782609, 607.70145, 365);
INSERT INTO building(id, name, level) VALUES (3723, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3723, 10.0, 8.325113799808523, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3723, 10.0, 6.259447088102987, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3723, 80.0, 58.338243551646045, 2);
INSERT INTO building(id, name, level) VALUES (3724, "building_subsistence_rice_paddieslevel", 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3724, 1081.333, 1081.333, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3724, 147.4545, 147.4545, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3724, 147.4545, 147.4545, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3724, 196.606, 196.606, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3724, 196.606, 196.606, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3724, 196.606, 196.606, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3724, 294.909, 294.909, 197);
INSERT INTO building(id, name, level) VALUES (3725, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3725, 5.0, 4.162556899904262, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3725, 5.0, 3.1297235440514934, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3725, 40.0, 29.169121775823022, 1);
INSERT INTO building(id, name, level) VALUES (3726, "building_subsistence_rice_paddieslevel", 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3726, 790.416, 790.416, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3726, 107.784, 107.784, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3726, 107.784, 107.784, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3726, 143.712, 143.712, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3726, 143.712, 143.712, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3726, 143.712, 143.712, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3726, 215.568, 215.568, 144);
INSERT INTO building(id, name, level) VALUES (3727, "building_subsistence_rice_paddieslevel", 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3727, 1294.8127478260872, 1489.03466, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3727, 176.56537391304352, 203.05018, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3727, 176.56537391304352, 203.05018, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3727, 235.4204956521739, 270.73357, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3727, 235.4204956521739, 270.73357, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3727, 235.4204956521739, 270.73357, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3727, 353.13074782608703, 406.10036, 245);
INSERT INTO building(id, name, level) VALUES (3728, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3728, 5.0, 4.162556899904262, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3728, 5.0, 3.1297235440514934, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3728, 40.0, 29.169121775823022, 1);
INSERT INTO building(id, name, level) VALUES (3729, "building_subsistence_farmslevel", 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3729, 164.34, 164.34, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3729, 27.39, 27.39, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3729, 27.39, 27.39, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3729, 27.39, 27.39, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3729, 27.39, 27.39, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3729, 27.39, 27.39, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3729, 38.346, 38.346, 55);
INSERT INTO building(id, name, level) VALUES (3730, "building_subsistence_rice_paddieslevel", 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3730, 2563.1496, 2563.1496, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3730, 349.5204, 349.5204, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3730, 349.5204, 349.5204, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3730, 466.0272, 466.0272, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3730, 466.0272, 466.0272, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3730, 466.0272, 466.0272, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3730, 699.0408, 699.0408, 480);
INSERT INTO building(id, name, level) VALUES (3731, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3731, 20.0, 16.650227599617047, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3731, 20.0, 12.518894176205974, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3731, 160.0, 116.67648710329209, 4);
INSERT INTO building(id, name, level) VALUES (3732, "building_subsistence_rice_paddieslevel", 266);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3732, 1460.074, 1460.074, 266);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3732, 199.101, 199.101, 266);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3732, 199.101, 199.101, 266);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3732, 265.468, 265.468, 266);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3732, 265.468, 265.468, 266);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3732, 265.468, 265.468, 266);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3732, 398.202, 398.202, 266);
INSERT INTO building(id, name, level) VALUES (3733, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3733, 30.0, 24.97534139942557, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3733, 30.0, 18.778341264308956, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3733, 240.0, 175.0147306549381, 6);
INSERT INTO building(id, name, level) VALUES (3735, "building_subsistence_rice_paddieslevel", 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3735, 2058.375, 2058.375, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3735, 280.6875, 280.6875, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3735, 280.6875, 280.6875, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3735, 374.25, 374.25, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3735, 374.25, 374.25, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3735, 374.25, 374.25, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3735, 561.375, 561.375, 375);
INSERT INTO building(id, name, level) VALUES (3736, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3736, 20.0, 16.650227599617047, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3736, 20.0, 12.518894176205974, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3736, 160.0, 116.67648710329209, 4);
INSERT INTO building(id, name, level) VALUES (3737, "building_subsistence_rice_paddieslevel", 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3737, 1170.88009, 1170.88009, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3737, 159.66546, 159.66546, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3737, 159.66546, 159.66546, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3737, 212.88729, 212.88729, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3737, 212.88729, 212.88729, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3737, 212.88729, 212.88729, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3737, 319.33093, 319.33093, 231);
INSERT INTO building(id, name, level) VALUES (3738, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3738, 20.0, 16.650227599617047, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3738, 20.0, 12.518894176205974, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3738, 160.0, 116.67648710329209, 4);
INSERT INTO building(id, name, level) VALUES (3739, "building_subsistence_rice_paddieslevel", 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3739, 1376.6742, 1376.6742, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3739, 187.7283, 187.7283, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3739, 187.7283, 187.7283, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3739, 250.3044, 250.3044, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3739, 250.3044, 250.3044, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3739, 250.3044, 250.3044, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3739, 375.4566, 375.4566, 345);
INSERT INTO building(id, name, level) VALUES (3740, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3740, 30.0, 24.97534139942557, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3740, 30.0, 18.778341264308956, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3740, 240.0, 175.0147306549381, 6);
INSERT INTO building(id, name, level) VALUES (3741, "building_subsistence_rice_paddieslevel", 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3741, 1375.34963, 1375.34963, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3741, 187.54767, 187.54767, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3741, 187.54767, 187.54767, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3741, 250.06357, 250.06357, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3741, 250.06357, 250.06357, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3741, 250.06357, 250.06357, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3741, 375.09535, 375.09535, 257);
INSERT INTO building(id, name, level) VALUES (3742, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3742, 20.0, 16.650227599617047, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3742, 20.0, 12.518894176205974, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3742, 160.0, 116.67648710329209, 4);
INSERT INTO building(id, name, level) VALUES (3743, "building_subsistence_rice_paddieslevel", 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3743, 2678.632, 2678.632, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3743, 365.268, 365.268, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3743, 365.268, 365.268, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3743, 487.024, 487.024, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3743, 487.024, 487.024, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3743, 487.024, 487.024, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3743, 730.536, 730.536, 488);
INSERT INTO building(id, name, level) VALUES (3744, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3744, 20.0, 16.650227599617047, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3744, 20.0, 12.518894176205974, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3744, 160.0, 116.67648710329209, 4);
INSERT INTO building(id, name, level) VALUES (3745, "building_subsistence_rice_paddieslevel", 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3745, 1005.99774, 1005.99774, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3745, 137.18151, 137.18151, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3745, 137.18151, 137.18151, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3745, 182.90868, 182.90868, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3745, 182.90868, 182.90868, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3745, 182.90868, 182.90868, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3745, 274.36302, 274.36302, 186);
INSERT INTO building(id, name, level) VALUES (3746, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3746, 35.0, 29.13789829932983, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3746, 35.0, 21.908064808360454, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3746, 280.0, 204.1838524307611, 7);
INSERT INTO building(id, name, level) VALUES (3747, "building_subsistence_rice_paddieslevel", 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3747, 3117.752, 3117.752, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3747, 425.148, 425.148, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3747, 425.148, 425.148, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3747, 566.864, 566.864, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3747, 566.864, 566.864, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3747, 566.864, 566.864, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3747, 850.296, 850.296, 568);
INSERT INTO building(id, name, level) VALUES (3748, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3748, 25.0, 20.812784499521307, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3748, 25.0, 15.648617720257466, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3748, 200.0, 145.84560887911508, 5);
INSERT INTO building(id, name, level) VALUES (3749, "building_subsistence_rice_paddieslevel", 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3749, 2898.192, 2898.192, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3749, 395.208, 395.208, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3749, 395.208, 395.208, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3749, 526.944, 526.944, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3749, 526.944, 526.944, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3749, 526.944, 526.944, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3749, 790.416, 790.416, 528);
INSERT INTO building(id, name, level) VALUES (3750, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3750, 35.0, 29.13789829932983, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3750, 35.0, 21.908064808360454, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3750, 280.0, 204.1838524307611, 7);
INSERT INTO building(id, name, level) VALUES (3751, "building_subsistence_rice_paddieslevel", 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3751, 1358.51661, 1358.51661, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3751, 185.25226, 185.25226, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3751, 185.25226, 185.25226, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3751, 247.00302, 247.00302, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3751, 247.00302, 247.00302, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3751, 247.00302, 247.00302, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3751, 370.50453, 370.50453, 249);
INSERT INTO building(id, name, level) VALUES (3752, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3752, 15.0, 12.487670699712785, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3752, 15.0, 9.389170632154478, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3752, 120.0, 87.50736532746905, 3);
INSERT INTO building(id, name, level) VALUES (3753, "building_subsistence_rice_paddieslevel", 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3753, 1652.72184, 1652.72184, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3753, 225.37116, 225.37116, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3753, 225.37116, 225.37116, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3753, 300.49488, 300.49488, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3753, 300.49488, 300.49488, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3753, 300.49488, 300.49488, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3753, 450.74232, 450.74232, 304);
INSERT INTO building(id, name, level) VALUES (3754, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3754, 30.0, 24.97534139942557, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3754, 30.0, 18.778341264308956, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3754, 240.0, 175.0147306549381, 6);
INSERT INTO building(id, name, level) VALUES (3755, "building_subsistence_rice_paddieslevel", 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3755, 1646.7, 1646.7, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3755, 224.55, 224.55, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3755, 224.55, 224.55, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3755, 299.4, 299.4, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3755, 299.4, 299.4, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3755, 299.4, 299.4, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3755, 449.1, 449.1, 300);
INSERT INTO building(id, name, level) VALUES (3756, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3756, 35.0, 29.13789829932983, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3756, 35.0, 21.908064808360454, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3756, 280.0, 204.1838524307611, 7);
INSERT INTO building(id, name, level) VALUES (3757, "building_subsistence_rice_paddieslevel", 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3757, 1734.524, 1734.524, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3757, 236.526, 236.526, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3757, 236.526, 236.526, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3757, 315.368, 315.368, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3757, 315.368, 315.368, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3757, 315.368, 315.368, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3757, 473.052, 473.052, 316);
INSERT INTO building(id, name, level) VALUES (3758, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3758, 10.0, 8.325113799808523, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3758, 10.0, 6.259447088102987, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3758, 80.0, 58.338243551646045, 2);
INSERT INTO building(id, name, level) VALUES (3759, "building_subsistence_rice_paddieslevel", 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3759, 356.22972, 356.22972, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3759, 48.57678, 48.57678, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3759, 48.57678, 48.57678, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3759, 64.76904, 64.76904, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3759, 64.76904, 64.76904, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3759, 64.76904, 64.76904, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3759, 97.15356, 97.15356, 72);
INSERT INTO building(id, name, level) VALUES (3777, "building_subsistence_pastureslevel", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3777, 5.6336, 5.6336, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3777, 8.4504, 8.4504, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3777, 2.8168, 2.8168, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3777, 5.6336, 5.6336, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3777, 5.6336, 5.6336, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3777, 5.6336, 5.6336, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3777, 18.70355, 18.70355, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3777, 7.88704, 7.88704, 16);
INSERT INTO building(id, name, level) VALUES (3785, "building_subsistence_pastureslevel", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3785, 6.3155, 6.3155, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3785, 9.47325, 9.47325, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3785, 3.15775, 3.15775, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3785, 6.3155, 6.3155, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3785, 6.3155, 6.3155, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3785, 6.3155, 6.3155, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3785, 20.96746, 20.96746, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3785, 8.8417, 8.8417, 50);
INSERT INTO building(id, name, level) VALUES (3795, "building_subsistence_farmslevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3795, 7.76688, 7.76688, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3795, 1.29448, 1.29448, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3795, 1.29448, 1.29448, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3795, 1.29448, 1.29448, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3795, 1.29448, 1.29448, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3795, 1.29448, 1.29448, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3795, 1.81227, 1.81227, 11);
INSERT INTO building(id, name, level) VALUES (3796, "building_subsistence_farmslevel", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3796, 4.482, 4.482, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3796, 0.747, 0.747, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3796, 0.747, 0.747, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3796, 0.747, 0.747, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3796, 0.747, 0.747, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3796, 0.747, 0.747, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3796, 1.0458, 1.0458, 40);
INSERT INTO building(id, name, level) VALUES (4053, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4054, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4136, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4137, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4513, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4514, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4515, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4516, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4517, "building_conscription_centerlevel", 22);
INSERT INTO building(id, name, level) VALUES (4518, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4519, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4520, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4521, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4522, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4523, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4524, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4525, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4526, "building_conscription_centerlevel", 15);
INSERT INTO building(id, name, level) VALUES (4527, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4528, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4529, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4530, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4531, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4532, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4533, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4534, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4535, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4536, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4537, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4538, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4539, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4540, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4541, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4542, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4543, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4544, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4545, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4546, "building_conscription_centerlevel", 14);
