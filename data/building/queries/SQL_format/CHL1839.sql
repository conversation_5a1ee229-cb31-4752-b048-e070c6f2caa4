
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0.07668667144392691);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 24.719341536149827, 138.61333372723558);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 19.494808830530314, 24.15801471755052);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 6.4529788006549404, 2.805278359213546);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 10.970059326217868, 16.85560091555491);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 42.660440170675, 40.08348511938022);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 38.95003951508052, 29.1073800871194);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 14.14084742243007, 13.389621897169473);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 25.063332065219154, 7.2430166666666675);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0.00900622536725188);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 35.97848709225535, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 42.71922579819582, 10.99675536261577);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 29.06266502353983, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 2.3825214285714282);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 33.173656053070665, 28.424005592912426);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 28.505494401208907, 8.716174985456403);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 27.17629732346587, 15.329199838261005);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 18.762742777662332, 3.9454143855249013);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 10.857127868638795, 1.2935377915268);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 31.74271161187036, 37.37166262130426);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 13.898041666666666);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 2.779608333333334);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (1757, "building_government_administration", 3);
INSERT INTO building(id, name, level) VALUES (1758, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1758, 25.9629, 65.23276884196711, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1758, 17.3086, 19.988, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1758, 51.9258, 51.9258, 1);
INSERT INTO building(id, name, level) VALUES (1759, "building_gold_mine", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1759, 15.0, 23.606353017498368, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 1759, 30.0, 30.0, 3);
INSERT INTO building(id, name, level) VALUES (1760, "building_iron_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1760, 4.997, 7.864063068562622, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1760, 19.988, 19.988, 1);
INSERT INTO building(id, name, level) VALUES (1761, "building_wheat_farm", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1761, 19.107999999999997, 25.7958, 1);
INSERT INTO building(id, name, level) VALUES (1762, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1762, 4.14015, 2.837564542607287, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1762, 4.14015, 6.515589496359724, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1762, 8.2803, 6.977714542607287, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1762, 16.5606, 13.955429085214574, 1);
INSERT INTO building(id, name, level) VALUES (1763, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1763, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (1764, "building_vineyard_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1764, 15.052, 20.47072, 2);
INSERT INTO building(id, name, level) VALUES (1765, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1765, 3.0794, 2.1105506449053486, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1765, 6.1588, 4.221101289810697, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1765, 6.1588, 4.221101289810697, 1);
INSERT INTO building(id, name, level) VALUES (1766, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1766, 37.915, 51.5644, 2);
INSERT INTO building(id, name, level) VALUES (1767, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1767, 2.336, 3.1536, 1);
INSERT INTO building(id, name, level) VALUES (1768, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1768, 2.61629702970297, 4.117415418786715, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1768, 31.39559405940594, 31.39559405940594, 2);
INSERT INTO building(id, name, level) VALUES (1769, "building_fishing_wharf", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1769, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1770, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1770, 4.9076, 7.723369204578333, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1770, 58.8912, 58.8912, 1);
INSERT INTO building(id, name, level) VALUES (3023, "building_subsistence_farms", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3023, 28.610555555555557, 38.62425, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3023, 7.152637037037037, 9.65606, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3023, 7.152637037037037, 9.65606, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3023, 7.152637037037037, 9.65606, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3023, 7.152637037037037, 9.65606, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3023, 7.152637037037037, 9.65606, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3023, 7.152637037037037, 9.65606, 44);
INSERT INTO building(id, name, level) VALUES (3024, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3024, 27.561, 27.83661, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3024, 9.187, 9.27887, 2);
INSERT INTO building(id, name, level) VALUES (3025, "building_subsistence_farms", 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3025, 21.94815555555555, 29.63001, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3025, 5.4870370370370365, 7.4075, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3025, 5.4870370370370365, 7.4075, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3025, 5.4870370370370365, 7.4075, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3025, 5.4870370370370365, 7.4075, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3025, 5.4870370370370365, 7.4075, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3025, 5.4870370370370365, 7.4075, 64);
INSERT INTO building(id, name, level) VALUES (3026, "building_subsistence_pastures", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3026, 0.4655, 0.4655, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3026, 0.69825, 0.69825, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3026, 0.23275, 0.23275, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3026, 0.4655, 0.4655, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3026, 0.4655, 0.4655, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3026, 0.4655, 0.4655, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3026, 1.23823, 1.23823, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3026, 0.4655, 0.4655, 1);
INSERT INTO building(id, name, level) VALUES (4023, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4023, 9.9011, 0.0, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4023, 1.8002, 0.0, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4023, 1.8002, 1.2338160911082057, 10);
INSERT INTO building(id, name, level) VALUES (4024, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4024, 2.99997, 0.0, 3);
INSERT INTO building(id, name, level) VALUES (4025, "building_naval_base", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4025, 0.63599, 0.0, 3);
INSERT INTO building(id, name, level) VALUES (4179, "building_trade_center", 15);
INSERT INTO building(id, name, level) VALUES (4461, "building_conscription_center", 2);
INSERT INTO building(id, name, level) VALUES (4731, "building_sugar_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 4731, 1.482, 2.0007, 1);
INSERT INTO building(id, name, level) VALUES (4907, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4907, 1.3337573529411764, 2.099009794214238, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 4907, 6.6687941176470575, 6.6687941176470575, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 4907, 9.336316176470588, 9.336316176470588, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 4907, 6.6687941176470575, 6.6687941176470575, 2);
INSERT INTO building(id, name, level) VALUES (5054, "building_conscription_center", 1);
INSERT INTO building(id, name, level) VALUES (5204, "building_whaling_station", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (28, 5204, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 5204, 5.0, 5.0, 1);
