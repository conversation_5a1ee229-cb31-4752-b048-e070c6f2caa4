
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 24.804428886179608, 458.1032046031077);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 25.964254895568423, 41.5018347314912);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 14.298625577647243, 52.64200004690045);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 28.208802795364765, 43.6933224530995);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 38.41536085757416, 105.58677861475282);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 32.82077846528534, 98.86353903180903);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 52.5, 4.860700120500845);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 32.37746192806993, 61.555920833333374);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 10.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 3.245512738095237);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 25.42891483117357, 8.764370726070888);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 29.895105465090474, 26.40562095177887);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 16.702131506638857, 12.079143158581584);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 18.99380515901235, 18.43868699999999);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 8.515006245584274, 4.771705859263203);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 32.88654101779905, 119.42365700606373);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 15.145726111111115);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 3.7864315277777787);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (134218286, "building_coffee_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 134218286, 17.69, 17.69, 1);
INSERT INTO building(id, name, level) VALUES (1663, "building_coffee_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1663, 17.6862, 17.6862, 1);
INSERT INTO building(id, name, level) VALUES (1664, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1664, 26.5293, 26.5293, 1);
INSERT INTO building(id, name, level) VALUES (1725, "building_government_administrationlevel", 1);
INSERT INTO building(id, name, level) VALUES (1726, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1726, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1727, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1727, 66.32324509803921, 67.64971, 3);
INSERT INTO building(id, name, level) VALUES (1728, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1728, 53.05859405940594, 53.58918, 2);
INSERT INTO building(id, name, level) VALUES (1729, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1729, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1729, 5.0, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (1730, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1730, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (1731, "building_rice_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1731, 35.0, 38.5, 1);
INSERT INTO building(id, name, level) VALUES (1732, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1732, 29.994, 32.9934, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1732, 4.999, 5.4989, 1);
INSERT INTO building(id, name, level) VALUES (1733, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1733, 1.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1733, 1.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (1734, "building_government_administrationlevel", 4);
INSERT INTO building(id, name, level) VALUES (1735, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1735, 40.0, 64.52566510929599, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1735, 45.0, 45.0, 1);
INSERT INTO building(id, name, level) VALUES (1736, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1736, 51.67019801980198, 52.1869, 2);
INSERT INTO building(id, name, level) VALUES (1737, "building_cotton_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1737, 106.25879527559054, 134.94867, 3);
INSERT INTO building(id, name, level) VALUES (1738, "building_maize_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1738, 59.99999999999999, 66.6, 2);
INSERT INTO building(id, name, level) VALUES (1739, "building_barrackslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1739, 6.0, 0.0, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1739, 6.0, 0.0, 6);
INSERT INTO building(id, name, level) VALUES (1740, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1740, 20.0, 32.26283255464799, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1740, 60.00000000000001, 27.164788818540938, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1740, 90.00000000000001, 65.37359161390572, 2);
INSERT INTO building(id, name, level) VALUES (1741, "building_logging_camplevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1741, 59.99999999999999, 66.6, 2);
INSERT INTO building(id, name, level) VALUES (1742, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1742, 44.225, 44.66725, 2);
INSERT INTO building(id, name, level) VALUES (1743, "building_coffee_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1743, 35.37239603960396, 35.72612, 2);
INSERT INTO building(id, name, level) VALUES (1744, "building_maize_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1744, 89.99999999999999, 100.8, 3);
INSERT INTO building(id, name, level) VALUES (1745, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1745, 3.0, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1745, 3.0, 0.0, 3);
INSERT INTO building(id, name, level) VALUES (2931, "building_subsistence_farmslevel", 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2931, 2.3375, 2.57125, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2931, 0.46749999999999997, 0.51425, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2931, 0.46749999999999997, 0.51425, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2931, 0.46749999999999997, 0.51425, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2931, 0.46749999999999997, 0.51425, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2931, 0.46749999999999997, 0.51425, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2931, 0.6545, 0.71995, 17);
INSERT INTO building(id, name, level) VALUES (3065, "building_subsistence_farmslevel", 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3065, 58.21554545454545, 64.0371, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3065, 11.64310909090909, 12.80742, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3065, 11.64310909090909, 12.80742, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3065, 11.64310909090909, 12.80742, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3065, 11.64310909090909, 12.80742, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3065, 11.64310909090909, 12.80742, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3065, 16.300345454545454, 17.93038, 86);
INSERT INTO building(id, name, level) VALUES (3066, "building_subsistence_farmslevel", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3066, 3.4747999999999997, 3.82228, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3066, 0.6949545454545454, 0.76445, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3066, 0.6949545454545454, 0.76445, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3066, 0.6949545454545454, 0.76445, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3066, 0.6949545454545454, 0.76445, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3066, 0.6949545454545454, 0.76445, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3066, 0.9729363636363636, 1.07023, 16);
INSERT INTO building(id, name, level) VALUES (3067, "building_subsistence_farmslevel", 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3067, 20.61421818181818, 22.67564, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3067, 4.122836363636363, 4.53512, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3067, 4.122836363636363, 4.53512, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3067, 4.122836363636363, 4.53512, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3067, 4.122836363636363, 4.53512, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3067, 4.122836363636363, 4.53512, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3067, 5.771981818181818, 6.34918, 67);
INSERT INTO building(id, name, level) VALUES (3068, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3068, 10.0, 4.527464803090156, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3068, 50.0, 22.637324015450783, 2);
INSERT INTO building(id, name, level) VALUES (3069, "building_subsistence_farmslevel", 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3069, 8.7856, 9.66416, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3069, 1.7571181818181818, 1.93283, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3069, 1.7571181818181818, 1.93283, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3069, 1.7571181818181818, 1.93283, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3069, 1.7571181818181818, 1.93283, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3069, 1.7571181818181818, 1.93283, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3069, 2.459963636363636, 2.70596, 38);
INSERT INTO building(id, name, level) VALUES (3079, "building_subsistence_farmslevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3079, 2.4314999999999998, 2.67465, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3079, 0.48629999999999995, 0.53493, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3079, 0.48629999999999995, 0.53493, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3079, 0.48629999999999995, 0.53493, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3079, 0.48629999999999995, 0.53493, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3079, 0.48629999999999995, 0.53493, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3079, 0.6808181818181818, 0.7489, 3);
INSERT INTO building(id, name, level) VALUES (268439046, "building_dye_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 268439046, 24.58325, 24.58325, 1);
INSERT INTO building(id, name, level) VALUES (3884, "building_trade_centerlevel", 26);
INSERT INTO building(id, name, level) VALUES (3885, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3885, 5.0, 2.263732401545078, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3885, 25.0, 11.318662007725392, 2);
INSERT INTO building(id, name, level) VALUES (4069, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4070, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4755, "building_coffee_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 4755, 17.69, 17.69, 1);
INSERT INTO building(id, name, level) VALUES (268440876, "building_coffee_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 268440876, 17.6862, 17.6862, 1);
INSERT INTO building(id, name, level) VALUES (33559988, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 33559988, 22.1175, 22.1175, 1);
INSERT INTO building(id, name, level) VALUES (151000616, "building_dye_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 151000616, 24.60275, 24.60275, 1);
INSERT INTO building(id, name, level) VALUES (5984, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 5984, 22.10775, 22.10775, 1);
INSERT INTO building(id, name, level) VALUES (134223744, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 134223744, 29.999999999999996, 33.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 134223744, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (83892387, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 83892387, 25.0, 40.328540693309996, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 83892387, 75.0, 33.955986023176166, 1);
INSERT INTO building(id, name, level) VALUES (6576, "building_subsistence_rice_paddieslevel", 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6576, 0.027127272727272722, 0.02984, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6576, 0.004518181818181817, 0.00497, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6576, 0.004518181818181817, 0.00497, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6576, 0.006027272727272727, 0.00663, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6576, 0.006027272727272727, 0.00663, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6576, 0.006027272727272727, 0.00663, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6576, 0.009036363636363634, 0.00994, 67);
