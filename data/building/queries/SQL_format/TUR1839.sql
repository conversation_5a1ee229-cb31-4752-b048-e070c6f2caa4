
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 91.2001297409415, 5.9497832513247095);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 52.782136551031705, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 95.07454499864178, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 22.285342021521956, 2088.3156977566264);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 17.363458910894344, 510.3414944617977);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 15.574890573844609, 63.09768254590383);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 18.687046920186546, 378.58609527542166);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 42.1568738875419, 90.33105211604317);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 36.92471723353991, 769.165693509338);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 31.5072724852718, 498.15032694856063);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 37.21706081680074, 25.5450058950265);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 21.025378942861636, 353.2765079974053);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 37.44584282145021, 121.1277558333333);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 79.6125423897829, 1.7430272432360612);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 48.971479021838306, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 52.5, 106.78619228578235);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 36.12247329959593, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 10.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 52.002535322840416, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 45.43897318580073, 22.086939472243106);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 46.07574356254899, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 88.86731081353689, 56.453200590136795);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 38.83123034877268, 194.5733318251866);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 24.714308266216225, 189.0295291382874);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 21.556721485595244, 215.26100931997823);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 37.19244612222259, 12.713646659409967);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 20.023968242467504, 83.62835726374675);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 87.5, 118.22411289877552);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 15.211465614052152, 16.55521188343779);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 37.11100797543431, 567.6753536350182);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 54.80999025340232, 173.18948110564298);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 54.223566071534634, 130.98502028919748);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (766, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 766, 99.99999999999999, 67.92417414755224, 10);
INSERT INTO building(id, name, level) VALUES (767, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 767, 50.0, 70.92271969745119, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 767, 150.0, 164.38900320284236, 2);
INSERT INTO building(id, name, level) VALUES (768, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 768, 20.0, 28.369087878980476, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 768, 40.0, 43.83706752075796, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 768, 35.0, 35.0, 1);
INSERT INTO building(id, name, level) VALUES (769, "building_military_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 769, 20.0, 28.369087878980476, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 769, 40.0, 43.83706752075796, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 769, 10.0, 11.48436430805503, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 769, 35.0, 35.0, 1);
INSERT INTO building(id, name, level) VALUES (770, "building_textile_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 770, 49.986000000000004, 70.90286133593591, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 770, 29.99159405940594, 21.022628828224253, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 770, 29.99159405940594, 25.507111443815095, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 770, 59.98319801980198, 51.0142313081782, 2);
INSERT INTO building(id, name, level) VALUES (771, "building_fishing_wharf", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 771, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (772, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 772, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (773, "building_silk_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 773, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (774, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 774, 3.0, 2.392425643745101, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 774, 15.0, 11.962128218725505, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 774, 21.0, 16.74697950621571, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 774, 15.0, 11.962128218725505, 3);
INSERT INTO building(id, name, level) VALUES (775, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 775, 15.0, 8.462485870072367, 3);
INSERT INTO building(id, name, level) VALUES (776, "building_hagia_sophia", 1);
INSERT INTO building(id, name, level) VALUES (777, "building_fishing_wharf", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 777, 46.7, 47.167, 2);
INSERT INTO building(id, name, level) VALUES (778, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 778, 1.9528712871287128, 1.5573664487534118, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 778, 9.76439603960396, 7.786863826943875, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 778, 13.670158415841584, 10.901612516039105, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 778, 9.76439603960396, 7.786863826943875, 2);
INSERT INTO building(id, name, level) VALUES (779, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 779, 4.99455, 3.9830298329890317, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 779, 59.9346, 47.79635799586838, 1);
INSERT INTO building(id, name, level) VALUES (780, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 780, 5.0, 2.8208286233574555, 1);
INSERT INTO building(id, name, level) VALUES (781, "building_textile_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 781, 16.83625, 23.88145279012425, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 781, 10.10175, 7.080828726371497, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 781, 10.10175, 8.591289363185748, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 781, 20.2035, 17.182578726371496, 1);
INSERT INTO building(id, name, level) VALUES (782, "building_fishing_wharf", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 782, 38.62, 39.0062, 2);
INSERT INTO building(id, name, level) VALUES (783, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 783, 9.98149504950495, 7.95999490645014, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 783, 119.778, 95.5199862521669, 2);
INSERT INTO building(id, name, level) VALUES (784, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 784, 5.0, 2.8208286233574555, 1);
INSERT INTO building(id, name, level) VALUES (785, "building_tooling_workshops", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 785, 60.0, 65.75560128113693, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 785, 40.0, 45.93745723222012, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 785, 120.0, 120.0, 2);
INSERT INTO building(id, name, level) VALUES (786, "building_arms_industry", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 786, 20.0, 22.96872861611006, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 786, 20.0, 11.998309784773056, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 786, 60.0, 47.997464677159584, 2);
INSERT INTO building(id, name, level) VALUES (787, "building_artillery_foundries", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 787, 13.3281, 15.306475593418822, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 787, 8.8854, 5.330489088081126, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 787, 22.2135, 17.76986136010141, 1);
INSERT INTO building(id, name, level) VALUES (788, "building_iron_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 788, 20.0, 15.949504291634007, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 788, 80.0, 63.79801716653603, 4);
INSERT INTO building(id, name, level) VALUES (789, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 789, 10.0, 7.974752145817003, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 789, 120.0, 95.69702574980404, 2);
INSERT INTO building(id, name, level) VALUES (790, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 790, 5.0, 4.2382193261593475, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 790, 10.0, 8.476438652318695, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 790, 10.0, 8.476438652318695, 1);
INSERT INTO building(id, name, level) VALUES (791, "building_furniture_manufacturies", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 791, 20.0, 28.369087878980476, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 791, 30.0, 32.877800640568466, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 791, 30.0, 17.997464677159584, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 791, 40.0, 34.66553985651537, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 791, 50.0, 43.33192482064421, 2);
INSERT INTO building(id, name, level) VALUES (792, "building_textile_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 792, 40.0, 56.73817575796095, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 792, 45.0, 45.0, 1);
INSERT INTO building(id, name, level) VALUES (793, "building_wheat_farm", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 793, 39.844, 40.24244, 2);
INSERT INTO building(id, name, level) VALUES (794, "building_tobacco_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 794, 74.92949999999999, 76.42809, 3);
INSERT INTO building(id, name, level) VALUES (795, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 795, 15.0, 11.962128218725505, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 795, 60.0, 47.84851287490202, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 795, 60.0, 47.84851287490202, 3);
INSERT INTO building(id, name, level) VALUES (796, "building_furniture_manufacturies", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 796, 30.0, 42.55363181847072, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 796, 45.0, 49.31670096085271, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 796, 45.0, 26.996197015739376, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 796, 60.0, 51.998309784773056, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 796, 75.0, 64.99788723096631, 3);
INSERT INTO building(id, name, level) VALUES (797, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 797, 2.0, 1.5949504291634007, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 797, 40.0, 31.899008583268014, 2);
INSERT INTO building(id, name, level) VALUES (798, "building_logging_camp", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 798, 19.99779611650485, 15.9477467491708, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 798, 139.98459223300972, 111.63424272915125, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 798, 39.9955922330097, 31.8954934983416, 4);
INSERT INTO building(id, name, level) VALUES (821, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 821, 4.85095, 4.111878008046538, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 821, 9.7019, 8.223756016093075, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 821, 9.7019, 8.223756016093075, 1);
INSERT INTO building(id, name, level) VALUES (827, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 827, 14.842049019607844, 12.580771798941273, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 827, 29.684098039215687, 25.161543597882545, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 827, 29.684098039215687, 25.161543597882545, 3);
INSERT INTO building(id, name, level) VALUES (828, "building_wheat_farm", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 828, 5.0, 5.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 828, 7.0, 7.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 828, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (833, "building_fishing_wharf", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 833, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (843, "building_government_administration", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 843, 20.0, 13.58483482951045, 2);
INSERT INTO building(id, name, level) VALUES (844, "building_textile_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 844, 40.0, 56.73817575796095, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 844, 45.0, 45.0, 1);
INSERT INTO building(id, name, level) VALUES (845, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 845, 2.0, 1.5949504291634007, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 845, 40.0, 31.899008583268014, 2);
INSERT INTO building(id, name, level) VALUES (846, "building_fishing_wharf", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 846, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (847, "building_cotton_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 847, 79.91199999999999, 80.71112, 2);
INSERT INTO building(id, name, level) VALUES (848, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 848, 10.0, 7.974752145817003, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 848, 40.0, 31.899008583268014, 2);
INSERT INTO building(id, name, level) VALUES (849, "building_tobacco_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 849, 99.545, 102.53135, 4);
INSERT INTO building(id, name, level) VALUES (850, "building_wheat_farm", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 850, 19.56, 19.56, 1);
INSERT INTO building(id, name, level) VALUES (851, "building_food_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 851, 27.6608, 23.446507427405695, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 851, 10.3728, 30.2650639555094, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 851, 44.9488, 41.524687284767126, 1);
INSERT INTO building(id, name, level) VALUES (852, "building_cotton_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 852, 40.0, 40.0, 1);
INSERT INTO building(id, name, level) VALUES (853, "building_fishing_wharf", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 853, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (854, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 854, 0.99266, 0.7916217465066706, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 854, 4.9633, 3.9581087325333537, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 854, 6.94862, 5.541352225546695, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 854, 4.9633, 3.9581087325333537, 1);
INSERT INTO building(id, name, level) VALUES (855, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 855, 30.0, 20.377252244265676, 3);
INSERT INTO building(id, name, level) VALUES (856, "building_textile_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 856, 50.0, 70.92271969745119, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 856, 30.0, 21.0285209781617, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 856, 30.0, 25.51426048908085, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 856, 60.0, 51.0285209781617, 2);
INSERT INTO building(id, name, level) VALUES (857, "building_glassworks", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 857, 108.49559223300972, 118.90321506057674, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 857, 18.08259223300971, 0.0, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 857, 36.16519417475728, 18.08259708737864, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 857, 72.33039805825243, 36.165199029126214, 4);
INSERT INTO building(id, name, level) VALUES (858, "building_fishing_wharf", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 858, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (859, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 859, 1.9026534653465348, 1.5173189805518437, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 859, 38.053198019801975, 30.346482256361515, 2);
INSERT INTO building(id, name, level) VALUES (860, "building_tobacco_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 860, 74.97, 76.4694, 3);
INSERT INTO building(id, name, level) VALUES (861, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 861, 5.0, 2.8208286233574555, 1);
INSERT INTO building(id, name, level) VALUES (16778323, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16778323, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 16778323, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (1875, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1875, 0.85644, 0.6829896727763515, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1875, 17.1288, 13.65979345552703, 1);
INSERT INTO building(id, name, level) VALUES (1876, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1876, 19.308, 19.308, 1);
INSERT INTO building(id, name, level) VALUES (1877, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1877, 5.0, 2.8208286233574555, 1);
INSERT INTO building(id, name, level) VALUES (1894, "building_textile_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1894, 25.0, 35.46135984872559, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 1894, 15.0, 10.51426048908085, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1894, 15.0, 12.757130244540425, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 1894, 30.0, 25.51426048908085, 1);
INSERT INTO building(id, name, level) VALUES (1895, "building_rice_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1895, 4.0, 3.1899008583268014, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1895, 20.0, 15.949504291634007, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1895, 24.0, 19.139405149960808, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1895, 36.0, 28.709107724941212, 2);
INSERT INTO building(id, name, level) VALUES (1896, "building_tobacco_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1896, 75.0, 91.5, 3);
INSERT INTO building(id, name, level) VALUES (1897, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1897, 1.0, 0.7974752145817003, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1897, 20.0, 15.949504291634007, 1);
INSERT INTO building(id, name, level) VALUES (1898, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1898, 4.66075, 3.950656144879436, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1898, 9.3215, 7.901312289758872, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1898, 9.3215, 7.901312289758872, 1);
INSERT INTO building(id, name, level) VALUES (1899, "building_fishing_wharf", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1899, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1900, "building_rice_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1900, 2.0, 1.5949504291634007, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1900, 10.0, 7.974752145817003, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1900, 12.0, 9.569702574980404, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1900, 18.000000000000004, 14.35455386247061, 1);
INSERT INTO building(id, name, level) VALUES (1901, "building_tobacco_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1901, 24.13, 28.956, 1);
INSERT INTO building(id, name, level) VALUES (1902, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1902, 5.0, 2.8208286233574555, 1);
INSERT INTO building(id, name, level) VALUES (1949, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1949, 30.0, 20.377252244265676, 3);
INSERT INTO building(id, name, level) VALUES (1950, "building_textile_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1950, 25.0, 35.46135984872559, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 1950, 15.0, 10.51426048908085, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1950, 15.0, 12.757130244540425, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 1950, 30.0, 25.51426048908085, 1);
INSERT INTO building(id, name, level) VALUES (1951, "building_paper_mills", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1951, 119.99399999999999, 131.50462700214575, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1951, 159.99200000000002, 159.99200000000002, 4);
INSERT INTO building(id, name, level) VALUES (1952, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1952, 14.996696078431373, 11.959493423163604, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1952, 179.96039215686272, 143.51395235150105, 3);
INSERT INTO building(id, name, level) VALUES (1953, "building_fishing_wharf", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1953, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1954, "building_tea_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 1954, 79.992, 82.39176, 4);
INSERT INTO building(id, name, level) VALUES (1955, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1955, 1.7048712871287128, 1.35959259553715, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1955, 8.52439603960396, 6.7979945608625645, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1955, 11.934158415841583, 9.517195543525272, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1955, 8.52439603960396, 6.7979945608625645, 2);
INSERT INTO building(id, name, level) VALUES (1956, "building_port", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1956, 9.998, 5.640528915265568, 2);
INSERT INTO building(id, name, level) VALUES (1957, "building_textile_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1957, 25.0, 35.46135984872559, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 1957, 15.0, 10.51426048908085, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1957, 15.0, 12.757130244540425, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 1957, 30.0, 25.51426048908085, 1);
INSERT INTO building(id, name, level) VALUES (1958, "building_furniture_manufacturies", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1958, 19.36, 27.4612770668531, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1958, 29.04, 31.82571102007028, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1958, 29.04, 17.421545807490478, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1958, 38.72, 33.55624258110688, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1958, 48.4, 41.945303226383594, 2);
INSERT INTO building(id, name, level) VALUES (1959, "building_fishing_wharf", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1959, 100.0, 103.0, 4);
INSERT INTO building(id, name, level) VALUES (1960, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1960, 2.994862745098039, 2.388328810289799, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1960, 59.89739215686274, 47.76668566317837, 3);
INSERT INTO building(id, name, level) VALUES (1961, "building_silk_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 1961, 39.876, 40.27476, 2);
INSERT INTO building(id, name, level) VALUES (1962, "building_tea_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 1962, 19.936, 19.936, 1);
INSERT INTO building(id, name, level) VALUES (1963, "building_tobacco_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1963, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1964, "building_cotton_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1964, 79.896, 80.69496, 2);
INSERT INTO building(id, name, level) VALUES (1965, "building_port", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1965, 10.0, 5.641657246714911, 2);
INSERT INTO building(id, name, level) VALUES (1966, "building_cotton_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1966, 80.0, 80.8, 2);
INSERT INTO building(id, name, level) VALUES (1967, "building_tobacco_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1967, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (1968, "building_tea_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 1968, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (1969, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1969, 4.9029, 4.155913106845333, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1969, 9.8058, 8.311826213690667, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1969, 9.8058, 8.311826213690667, 1);
INSERT INTO building(id, name, level) VALUES (1970, "building_tea_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 1970, 100.0, 104.0, 5);
INSERT INTO building(id, name, level) VALUES (1971, "building_fishing_wharf", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1971, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (1972, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1972, 4.97055, 4.21325621432827, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1972, 9.9411, 8.42651242865654, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1972, 9.9411, 8.42651242865654, 1);
INSERT INTO building(id, name, level) VALUES (1973, "building_tea_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 1973, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (1974, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1974, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (1980, "building_fishing_wharf", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1980, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (1981, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1981, 4.9843, 4.2249113174752075, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1981, 9.9686, 8.449822634950415, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1981, 9.9686, 8.449822634950415, 1);
INSERT INTO building(id, name, level) VALUES (1982, "building_tea_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 1982, 60.0, 61.2, 3);
INSERT INTO building(id, name, level) VALUES (1983, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1983, 10.0, 7.974752145817003, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1983, 120.0, 95.69702574980404, 2);
INSERT INTO building(id, name, level) VALUES (1984, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1984, 5.0, 2.8208286233574555, 1);
INSERT INTO building(id, name, level) VALUES (1985, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1985, 10.0, 7.974752145817003, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1985, 120.0, 95.69702574980404, 2);
INSERT INTO building(id, name, level) VALUES (1986, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1986, 4.98135, 4.222410768072773, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1986, 9.9627, 8.444821536145547, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1986, 9.9627, 8.444821536145547, 1);
INSERT INTO building(id, name, level) VALUES (1987, "building_tea_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 1987, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (1988, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1988, 30.0, 20.377252244265676, 3);
INSERT INTO building(id, name, level) VALUES (1989, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1989, 4.7392, 4.017153806106877, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1989, 9.4784, 8.034307612213754, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1989, 9.4784, 8.034307612213754, 1);
INSERT INTO building(id, name, level) VALUES (1990, "building_wheat_farm", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1990, 59.79959803921569, 60.99559, 3);
INSERT INTO building(id, name, level) VALUES (1991, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1991, 49.29, 49.7829, 2);
INSERT INTO building(id, name, level) VALUES (1992, "building_tea_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 1992, 28.2, 28.482, 2);
INSERT INTO building(id, name, level) VALUES (1993, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1993, 4.4696, 3.7886290200403643, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1993, 8.9392, 7.577258040080729, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1993, 8.9392, 7.577258040080729, 1);
INSERT INTO building(id, name, level) VALUES (1994, "building_tea_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 1994, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (1995, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1995, 1.0, 0.7974752145817003, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1995, 20.0, 15.949504291634007, 1);
INSERT INTO building(id, name, level) VALUES (2715, "building_subsistence_farms", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2715, 13.9034, 13.9034, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2715, 2.78068, 2.78068, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2715, 2.78068, 2.78068, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2715, 2.78068, 2.78068, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2715, 2.78068, 2.78068, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2715, 2.78068, 2.78068, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2715, 2.78068, 2.78068, 7);
INSERT INTO building(id, name, level) VALUES (2717, "building_subsistence_orchards", 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2717, 8.475458333333334, 10.17055, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2717, 4.237725, 5.08527, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2717, 12.713183333333333, 15.25582, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2717, 8.475458333333334, 10.17055, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2717, 8.475458333333334, 10.17055, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2717, 8.475458333333334, 10.17055, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2717, 28.138525, 33.76623, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2717, 8.475458333333334, 10.17055, 28);
INSERT INTO building(id, name, level) VALUES (2718, "building_subsistence_farms", 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2718, 57.49712, 57.49712, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2718, 11.49942, 11.49942, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2718, 11.49942, 11.49942, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2718, 11.49942, 11.49942, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2718, 11.49942, 11.49942, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2718, 11.49942, 11.49942, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2718, 11.49942, 11.49942, 23);
INSERT INTO building(id, name, level) VALUES (2719, "building_urban_center", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2719, 74.99399999999999, 77.99376, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2719, 24.998, 25.99792, 5);
INSERT INTO building(id, name, level) VALUES (2845, "building_subsistence_farms", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2845, 123.34374782608698, 141.84531, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2845, 24.66874782608696, 28.36906, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2845, 24.66874782608696, 28.36906, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2845, 24.66874782608696, 28.36906, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2845, 24.66874782608696, 28.36906, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2845, 24.66874782608696, 28.36906, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2845, 24.66874782608696, 28.36906, 50);
INSERT INTO building(id, name, level) VALUES (2846, "building_subsistence_farms", 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2846, 70.98487, 70.98487, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2846, 14.19697, 14.19697, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2846, 14.19697, 14.19697, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2846, 14.19697, 14.19697, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2846, 14.19697, 14.19697, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2846, 14.19697, 14.19697, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2846, 14.19697, 14.19697, 39);
INSERT INTO building(id, name, level) VALUES (2847, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2847, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2847, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (3289, "building_subsistence_farms", 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3289, 61.91437, 61.91437, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3289, 12.38287, 12.38287, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3289, 12.38287, 12.38287, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3289, 12.38287, 12.38287, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3289, 12.38287, 12.38287, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3289, 12.38287, 12.38287, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3289, 12.38287, 12.38287, 25);
INSERT INTO building(id, name, level) VALUES (3290, "building_subsistence_farms", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3290, 37.33875, 37.33875, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3290, 7.46775, 7.46775, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3290, 7.46775, 7.46775, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3290, 7.46775, 7.46775, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3290, 7.46775, 7.46775, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3290, 7.46775, 7.46775, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3290, 7.46775, 7.46775, 18);
INSERT INTO building(id, name, level) VALUES (3291, "building_subsistence_farms", 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3291, 109.0637, 109.0637, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3291, 21.81274, 21.81274, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3291, 21.81274, 21.81274, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3291, 21.81274, 21.81274, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3291, 21.81274, 21.81274, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3291, 21.81274, 21.81274, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3291, 21.81274, 21.81274, 46);
INSERT INTO building(id, name, level) VALUES (3292, "building_subsistence_farms", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3292, 36.62775, 36.62775, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3292, 7.32555, 7.32555, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3292, 7.32555, 7.32555, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3292, 7.32555, 7.32555, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3292, 7.32555, 7.32555, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3292, 7.32555, 7.32555, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3292, 7.32555, 7.32555, 15);
INSERT INTO building(id, name, level) VALUES (3293, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3293, 29.988, 30.28788, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3293, 9.996, 10.09596, 2);
INSERT INTO building(id, name, level) VALUES (3294, "building_subsistence_farms", 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3294, 62.23062, 62.23062, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3294, 12.44612, 12.44612, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3294, 12.44612, 12.44612, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3294, 12.44612, 12.44612, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3294, 12.44612, 12.44612, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3294, 12.44612, 12.44612, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3294, 12.44612, 12.44612, 25);
INSERT INTO building(id, name, level) VALUES (3295, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3295, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3295, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (3296, "building_subsistence_farms", 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3296, 78.31642, 78.31642, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3296, 15.66328, 15.66328, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3296, 15.66328, 15.66328, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3296, 15.66328, 15.66328, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3296, 15.66328, 15.66328, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3296, 15.66328, 15.66328, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3296, 15.66328, 15.66328, 33);
INSERT INTO building(id, name, level) VALUES (3439, "building_subsistence_orchards", 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3439, 8.355375, 10.02645, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3439, 4.177683333333333, 5.01322, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3439, 12.533058333333333, 15.03967, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3439, 8.355375, 10.02645, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3439, 8.355375, 10.02645, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3439, 8.355375, 10.02645, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3439, 27.739841666666667, 33.28781, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3439, 8.355375, 10.02645, 35);
INSERT INTO building(id, name, level) VALUES (3440, "building_subsistence_orchards", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3440, 14.44432, 14.44432, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3440, 7.22216, 7.22216, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3440, 21.66648, 21.66648, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3440, 14.44432, 14.44432, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3440, 14.44432, 14.44432, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3440, 14.44432, 14.44432, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3440, 47.95514, 47.95514, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3440, 14.44432, 14.44432, 29);
INSERT INTO building(id, name, level) VALUES (3457, "building_subsistence_farms", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3457, 22.410443478260873, 25.77201, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3457, 4.482086956521739, 5.1544, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3457, 4.482086956521739, 5.1544, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3457, 4.482086956521739, 5.1544, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3457, 4.482086956521739, 5.1544, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3457, 4.482086956521739, 5.1544, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3457, 4.482086956521739, 5.1544, 9);
INSERT INTO building(id, name, level) VALUES (3461, "building_subsistence_farms", 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3461, 102.10537, 102.10537, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3461, 20.42107, 20.42107, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3461, 20.42107, 20.42107, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3461, 20.42107, 20.42107, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3461, 20.42107, 20.42107, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3461, 20.42107, 20.42107, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3461, 20.42107, 20.42107, 41);
INSERT INTO building(id, name, level) VALUES (3462, "building_subsistence_farms", 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3462, 69.02255, 69.02255, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3462, 13.80451, 13.80451, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3462, 13.80451, 13.80451, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3462, 13.80451, 13.80451, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3462, 13.80451, 13.80451, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3462, 13.80451, 13.80451, 34);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3462, 13.80451, 13.80451, 34);
INSERT INTO building(id, name, level) VALUES (3463, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3463, 29.553, 29.84853, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3463, 9.851, 9.94951, 2);
INSERT INTO building(id, name, level) VALUES (3464, "building_subsistence_farms", 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3464, 64.753, 64.753, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3464, 12.9506, 12.9506, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3464, 12.9506, 12.9506, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3464, 12.9506, 12.9506, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3464, 12.9506, 12.9506, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3464, 12.9506, 12.9506, 26);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3464, 12.9506, 12.9506, 26);
INSERT INTO building(id, name, level) VALUES (3465, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3465, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3465, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (3466, "building_subsistence_farms", 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3466, 79.6744, 79.6744, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3466, 15.93488, 15.93488, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3466, 15.93488, 15.93488, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3466, 15.93488, 15.93488, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3466, 15.93488, 15.93488, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3466, 15.93488, 15.93488, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3466, 15.93488, 15.93488, 32);
INSERT INTO building(id, name, level) VALUES (3467, "building_subsistence_farms", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3467, 74.805, 74.805, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3467, 14.961, 14.961, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3467, 14.961, 14.961, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3467, 14.961, 14.961, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3467, 14.961, 14.961, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3467, 14.961, 14.961, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3467, 14.961, 14.961, 30);
INSERT INTO building(id, name, level) VALUES (3468, "building_subsistence_farms", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3468, 11.80287, 11.80287, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3468, 2.36057, 2.36057, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3468, 2.36057, 2.36057, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3468, 2.36057, 2.36057, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3468, 2.36057, 2.36057, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3468, 2.36057, 2.36057, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3468, 2.36057, 2.36057, 7);
INSERT INTO building(id, name, level) VALUES (3469, "building_subsistence_farms", 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3469, 89.6832, 89.6832, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3469, 17.93664, 17.93664, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3469, 17.93664, 17.93664, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3469, 17.93664, 17.93664, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3469, 17.93664, 17.93664, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3469, 17.93664, 17.93664, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3469, 17.93664, 17.93664, 36);
INSERT INTO building(id, name, level) VALUES (3470, "building_subsistence_farms", 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3470, 94.63995, 94.63995, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3470, 18.92799, 18.92799, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3470, 18.92799, 18.92799, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3470, 18.92799, 18.92799, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3470, 18.92799, 18.92799, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3470, 18.92799, 18.92799, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3470, 18.92799, 18.92799, 38);
INSERT INTO building(id, name, level) VALUES (3471, "building_subsistence_farms", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3471, 9.9305, 9.9305, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3471, 1.9861, 1.9861, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3471, 1.9861, 1.9861, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3471, 1.9861, 1.9861, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3471, 1.9861, 1.9861, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3471, 1.9861, 1.9861, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3471, 1.9861, 1.9861, 4);
INSERT INTO building(id, name, level) VALUES (3475, "building_subsistence_orchards", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3475, 1.7724, 1.7724, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3475, 0.8862, 0.8862, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3475, 2.6586, 2.6586, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3475, 1.7724, 1.7724, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3475, 1.7724, 1.7724, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3475, 1.7724, 1.7724, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3475, 5.88436, 5.88436, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3475, 1.7724, 1.7724, 5);
INSERT INTO building(id, name, level) VALUES (3476, "building_subsistence_farms", 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3476, 141.91575, 141.91575, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3476, 28.38315, 28.38315, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3476, 28.38315, 28.38315, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3476, 28.38315, 28.38315, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3476, 28.38315, 28.38315, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3476, 28.38315, 28.38315, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3476, 28.38315, 28.38315, 57);
INSERT INTO building(id, name, level) VALUES (3477, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3477, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3477, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (3479, "building_subsistence_orchards", 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3479, 14.38605, 14.38605, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3479, 7.19302, 7.19302, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3479, 21.57907, 21.57907, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3479, 14.38605, 14.38605, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3479, 14.38605, 14.38605, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3479, 14.38605, 14.38605, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3479, 47.76168, 47.76168, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3479, 14.38605, 14.38605, 35);
INSERT INTO building(id, name, level) VALUES (3764, "building_subsistence_farms", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3764, 6.06097, 6.06097, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3764, 1.21219, 1.21219, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3764, 1.21219, 1.21219, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3764, 1.21219, 1.21219, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3764, 1.21219, 1.21219, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3764, 1.21219, 1.21219, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3764, 1.21219, 1.21219, 3);
INSERT INTO building(id, name, level) VALUES (3765, "building_subsistence_pastures", 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3765, 5.78189, 5.78189, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3765, 8.67283, 8.67283, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3765, 2.89094, 2.89094, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3765, 5.78189, 5.78189, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3765, 5.78189, 5.78189, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3765, 5.78189, 5.78189, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3765, 19.19587, 19.19587, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3765, 5.78189, 5.78189, 19);
INSERT INTO building(id, name, level) VALUES (4055, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4055, 10.0, 3.066637835346329, 10);
INSERT INTO building(id, name, level) VALUES (4056, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4056, 10.0, 3.066637835346329, 10);
INSERT INTO building(id, name, level) VALUES (4057, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4057, 10.0, 3.066637835346329, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4057, 5.0, 4.2382193261593475, 5);
INSERT INTO building(id, name, level) VALUES (4058, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4058, 5.0, 7.440025623740863, 5);
INSERT INTO building(id, name, level) VALUES (4059, "building_barracks", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4059, 14.99985, 4.5999107534519625, 15);
INSERT INTO building(id, name, level) VALUES (4060, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4060, 10.0, 3.066637835346329, 10);
INSERT INTO building(id, name, level) VALUES (4061, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4061, 10.0, 3.066637835346329, 10);
INSERT INTO building(id, name, level) VALUES (4062, "building_barracks", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4062, 19.99992, 6.1332511375899745, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4062, 1.99992, 2.975891209086365, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4062, 9.99984, 8.47630302930026, 12);
INSERT INTO building(id, name, level) VALUES (4063, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4063, 4.854, 1.488546005277108, 5);
INSERT INTO building(id, name, level) VALUES (4064, "building_barracks", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4064, 9.71139, 2.978131600780398, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4064, 1.94218, 2.889973793183406, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4064, 4.85566, 4.115870410651779, 7);
INSERT INTO building(id, name, level) VALUES (4065, "building_barracks", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4065, 20.0, 6.133275670692658, 20);
INSERT INTO building(id, name, level) VALUES (4066, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4066, 10.0, 3.066637835346329, 10);
INSERT INTO building(id, name, level) VALUES (4067, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4067, 5.0, 1.5333189176731645, 5);
INSERT INTO building(id, name, level) VALUES (4068, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4068, 5.0, 1.5333189176731645, 5);
INSERT INTO building(id, name, level) VALUES (4069, "building_barracks", 15);
INSERT INTO building(id, name, level) VALUES (4070, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4070, 1.0, 0.30666378353463286, 10);
INSERT INTO building(id, name, level) VALUES (4071, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4071, 10.0, 3.066637835346329, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4071, 5.0, 4.2382193261593475, 5);
INSERT INTO building(id, name, level) VALUES (4072, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4072, 2.99997, 4.463970734090775, 3);
INSERT INTO building(id, name, level) VALUES (4073, "building_naval_base", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4073, 22.0, 11.492571619616776, 10);
INSERT INTO building(id, name, level) VALUES (4074, "building_naval_base", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4074, 36.99982, 19.32832187558769, 17);
INSERT INTO building(id, name, level) VALUES (4075, "building_naval_base", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4075, 4.99998, 2.6119376475750675, 3);
INSERT INTO building(id, name, level) VALUES (4076, "building_naval_base", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4076, 3.0, 1.5671688572204694, 3);
INSERT INTO building(id, name, level) VALUES (4141, "building_trade_center", 28);
INSERT INTO building(id, name, level) VALUES (4157, "building_trade_center", 24);
INSERT INTO building(id, name, level) VALUES (4204, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4204, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 4204, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (4205, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4205, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 4205, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (4247, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4247, 1.516, 2.1503768612267202, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4247, 3.032, 3.3228497180734533, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 4247, 2.653, 2.653, 1);
INSERT INTO building(id, name, level) VALUES (4973, "building_lead_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4973, 0.1, 0.07974752145817003, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 4973, 0.4, 0.3189900858326801, 1);
INSERT INTO building(id, name, level) VALUES (5815, "building_trade_center", 5);
