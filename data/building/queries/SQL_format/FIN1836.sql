
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 24.403940145587697, 225.80127263094843);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 6.360974887696733, 6.963146744756876);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 18.589892620585143, 13.572765001742994);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 28.5519727479498, 31.250849998257003);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 52.5, 7.136581729193065);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 37.78775778826273, 40.02930630996308);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 18.92551976791227, 29.628302847422507);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 52.5, 2.191564279149287);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 27.294755461391176, 23.027247825905448);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 70.0, 0.9567064961333237);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 4.7642718478278585);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 52.5, 5.462728622212168);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 52.5, 0.9426627180084036);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 27.6706029389215, 73.0300973774304);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 87.5, 6.121184071331419);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 87.5, 1.979590928668581);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 52.5, 0.6682625217263221);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 70.0, 1.7563738419272008);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 1.051911521734993);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 5.55831715579917);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 350.0, 0.01272195111418207);
INSERT INTO building(id, name, level) VALUES (71, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 71, 10.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (72, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 72, 10.0, 11.03761480593225, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 72, 30.0, 12.896054504100396, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 72, 45.0, 32.1720408780753, 1);
INSERT INTO building(id, name, level) VALUES (73, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 73, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (74, "building_rye_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 74, 9.96, 9.96, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 74, 14.94, 14.94, 1);
INSERT INTO building(id, name, level) VALUES (75, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 75, 10.0, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 75, 120.0, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (76, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 76, 10.0, 0.0, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 76, 10.0, 0.0, 10);
INSERT INTO building(id, name, level) VALUES (77, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 77, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (78, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 78, 10.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (79, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 79, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (80, "building_rye_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 80, 9.96, 9.96, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 80, 14.94, 14.94, 1);
INSERT INTO building(id, name, level) VALUES (81, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 81, 5.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 81, 60.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (82, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 82, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (83, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 83, 10.0, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 83, 120.0, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (84, "building_rye_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 84, 9.96, 9.96, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 84, 14.94, 14.94, 1);
INSERT INTO building(id, name, level) VALUES (3760, "building_subsistence_farmslevel", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3760, 72.21, 72.21, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3760, 14.442, 14.442, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3760, 14.442, 14.442, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3760, 14.442, 14.442, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3760, 14.442, 14.442, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3760, 14.442, 14.442, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3760, 20.2188, 20.2188, 29);
INSERT INTO building(id, name, level) VALUES (3761, "building_subsistence_fishing_villageslevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3761, 0.3111, 0.3111, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 3761, 1.2444, 1.2444, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3761, 0.15555, 0.15555, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3761, 0.46665, 0.46665, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3761, 0.3111, 0.3111, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3761, 0.3111, 0.3111, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3761, 0.3111, 0.3111, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3761, 0.43554, 0.43554, 2);
INSERT INTO building(id, name, level) VALUES (3762, "building_subsistence_farmslevel", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3762, 11.26642, 11.26642, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3762, 2.25328, 2.25328, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3762, 2.25328, 2.25328, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3762, 2.25328, 2.25328, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3762, 2.25328, 2.25328, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3762, 2.25328, 2.25328, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3762, 3.15459, 3.15459, 9);
INSERT INTO building(id, name, level) VALUES (3763, "building_subsistence_farmslevel", 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3763, 45.8394, 45.8394, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3763, 9.16788, 9.16788, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3763, 9.16788, 9.16788, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3763, 9.16788, 9.16788, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3763, 9.16788, 9.16788, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3763, 9.16788, 9.16788, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3763, 12.83503, 12.83503, 19);
INSERT INTO building(id, name, level) VALUES (4547, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4548, "building_conscription_centerlevel", 1);
