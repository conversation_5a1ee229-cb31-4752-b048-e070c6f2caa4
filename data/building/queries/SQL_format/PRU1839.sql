
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 49.53703420652127, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 89.71065833529889, 35.428819592519936);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 79.48160278296399, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 26.149288831046775, 2203.383040891663);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 13.933278464286193, 60.43940566430301);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 27.807809148656013, 22.32745764875957);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 29.642624033602942, 154.09100961442738);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 45.04432813182943, 205.792857612761);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 34.40416104395754, 604.3061600320591);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 29.47015101521749, 413.4009085373445);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 47.34977254759124, 27.869593936779964);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 23.39838519388448, 354.4526598495314);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 43.93314864860597, 213.11226666666659);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 104.48261730932, 1.2281990792073578);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 75.72507373390717, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 44.23440579970556, 251.99884432454192);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 63.42197738807869, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 42.15895089180336, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 59.830269737191976, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 83.73277212493286, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 78.39990726640816, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 59.553219886564264, 51.712936814531155);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 35.409546644040965, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 59.46102965361785, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 57.29779170955857, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 114.98134065777458, 188.9615382685047);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 40.06897029205548, 306.88809843060363);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 38.19865634317368, 96.4536742962436);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 40.69713096765964, 957.732789166668);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 56.55230887017078, 126.01909407842545);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 48.70157391705287, 44.4312001932004);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 51.408513392235214);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 91.1809753439332, 257.042566961176);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 350.0, 3.0043639014456907);
INSERT INTO building(id, name, level) VALUES (384, "building_fishing_wharf", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 384, 74.985, 76.4847, 3);
INSERT INTO building(id, name, level) VALUES (385, "building_rye_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 385, 5.0, 4.045008740466825, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 385, 1.0, 0.36998726308114355, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 385, 25.0, 14.737362639681356, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 385, 15.0, 8.842417583808814, 1);
INSERT INTO building(id, name, level) VALUES (386, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 386, 29.960696078431372, 17.951166819275603, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 386, 29.960696078431372, 11.085075942064774, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 386, 59.921392156862744, 29.036242761340375, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 386, 14.980343137254902, 7.259058314981013, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 386, 74.90174509803921, 36.295305827029544, 3);
INSERT INTO building(id, name, level) VALUES (387, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 387, 15.0, 5.549808946217152, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 387, 60.0, 22.19923578486861, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 387, 60.0, 22.19923578486861, 3);
INSERT INTO building(id, name, level) VALUES (388, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 388, 4.998, 0.060323150058834256, 1);
INSERT INTO building(id, name, level) VALUES (408, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 408, 20.0, 4.820553057845642, 1);
INSERT INTO building(id, name, level) VALUES (409, "building_steel_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 409, 60.0, 23.331734064815375, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 409, 79.99999999999999, 18.66930628536388, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 409, 130.0, 40.44485659374148, 2);
INSERT INTO building(id, name, level) VALUES (410, "building_tooling_workshops", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 410, 150.0, 54.80633961096627, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 410, 100.0, 26.05235414695725, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 410, 400.0, 125.17982777520287, 5);
INSERT INTO building(id, name, level) VALUES (411, "building_iron_mine", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 411, 50.0, 19.443111720679475, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 411, 50.0, 18.49936315405718, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 411, 200.0, 75.88494974947331, 5);
INSERT INTO building(id, name, level) VALUES (412, "building_sulfur_mine", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 412, 30.0, 11.665867032407688, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 412, 30.0, 11.099617892434305, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 412, 120.0, 45.530969849683984, 3);
INSERT INTO building(id, name, level) VALUES (413, "building_rye_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 413, 10.0, 8.09001748093365, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 413, 2.0, 0.7399745261622871, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 413, 50.0, 29.474725279362712, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 413, 30.0, 17.684835167617628, 2);
INSERT INTO building(id, name, level) VALUES (415, "building_furniture_manufacturies", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 415, 30.0, 14.53515823593528, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 415, 45.0, 16.44190188328988, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 415, 45.0, 16.147485334059567, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 415, 15.0, 5.549808946217152, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 415, 120.0, 47.361034273269226, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 415, 75.0, 29.600646420793264, 3);
INSERT INTO building(id, name, level) VALUES (416, "building_logging_camp", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 416, 25.0, 9.24968157702859, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 416, 300.0, 110.99617892434307, 5);
INSERT INTO building(id, name, level) VALUES (417, "building_rye_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 417, 10.0, 8.09001748093365, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 417, 2.0, 0.7399745261622871, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 417, 50.0, 29.474725279362712, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 417, 30.0, 17.684835167617628, 2);
INSERT INTO building(id, name, level) VALUES (418, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 418, 20.0, 11.983144031288779, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 418, 20.0, 7.39974526162287, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 418, 40.0, 19.38288929291165, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 418, 10.0, 4.845722323227912, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 418, 50.0, 24.22861161613956, 2);
INSERT INTO building(id, name, level) VALUES (420, "building_government_administration", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 420, 80.0, 19.28221223138257, 4);
INSERT INTO building(id, name, level) VALUES (421, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 421, 39.996, 19.37827296014892, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 421, 79.992, 29.227124787736095, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 421, 99.99, 23.33429919341918, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 421, 19.998, 7.399005287096709, 2);
INSERT INTO building(id, name, level) VALUES (422, "building_tooling_workshops", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 422, 180.0, 65.76760753315952, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 422, 120.0, 31.262824976348696, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 422, 480.0, 150.2157933302434, 6);
INSERT INTO building(id, name, level) VALUES (423, "building_wheat_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 423, 20.0, 16.1800349618673, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 423, 4.0, 1.4799490523245742, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 423, 100.0, 58.949450558725424, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 423, 28.0, 16.50584615644312, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 423, 20.0, 11.789890111745084, 4);
INSERT INTO building(id, name, level) VALUES (424, "building_steel_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 424, 29.999999999999996, 11.665867032407686, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 424, 40.0, 9.334653142681942, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 424, 65.0, 20.22242829687074, 1);
INSERT INTO building(id, name, level) VALUES (425, "building_vineyard_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 425, 100.0, 104.0, 5);
INSERT INTO building(id, name, level) VALUES (429, "building_textile_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 429, 120.0, 58.14063294374112, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 429, 15.0, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 429, 180.0, 43.60547470780584, 3);
INSERT INTO building(id, name, level) VALUES (430, "building_coal_mine", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 430, 50.00000000000001, 18.49936315405718, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 430, 200.00000000000003, 73.99745261622871, 5);
INSERT INTO building(id, name, level) VALUES (431, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 431, 20.0, 7.777244688271791, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 431, 20.0, 7.39974526162287, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 431, 80.0, 30.353979899789323, 2);
INSERT INTO building(id, name, level) VALUES (432, "building_wheat_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 432, 25.0, 20.225043702334123, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 432, 5.0, 1.8499363154057176, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 432, 125.0, 73.68681319840678, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 432, 35.0, 20.632307695553898, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 432, 25.0, 14.737362639681356, 5);
INSERT INTO building(id, name, level) VALUES (433, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 433, 10.0, 3.699872630811435, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 433, 40.0, 14.79949052324574, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 433, 40.0, 14.79949052324574, 2);
INSERT INTO building(id, name, level) VALUES (434, "building_government_administration", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 434, 259.99999999999994, 62.66718975199334, 13);
INSERT INTO building(id, name, level) VALUES (435, "building_construction_sector", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 435, 60.0, 29.07031647187056, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 435, 120.0, 43.84507168877302, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 435, 150.0, 35.00494928505728, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 435, 30.0, 11.099617892434305, 3);
INSERT INTO building(id, name, level) VALUES (436, "building_university", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 436, 40.0, 9.641106115691285, 4);
INSERT INTO building(id, name, level) VALUES (437, "building_arms_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 437, 10.0, 2.3336632856704855, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 437, 10.0, 3.5883300742354587, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 437, 29.999999999999996, 8.882990039858914, 1);
INSERT INTO building(id, name, level) VALUES (438, "building_furniture_manufacturies", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 438, 50.0, 24.2252637265588, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 438, 75.0, 27.403169805483135, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 438, 75.0, 26.91247555676594, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 438, 25.0, 9.24968157702859, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 438, 200.0, 78.93505712211537, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 438, 125.0, 49.334410701322106, 5);
INSERT INTO building(id, name, level) VALUES (439, "building_glassworks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 439, 60.00000000000001, 21.922535844386513, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 439, 15.000000000000002, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 439, 30.000000000000004, 29.631230221343337, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 439, 60.00000000000001, 27.061665429024394, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 439, 60.00000000000001, 27.061665429024394, 3);
INSERT INTO building(id, name, level) VALUES (440, "building_paper_mills", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 440, 120.0, 43.84507168877302, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 440, 40.0, 13.36752504017649, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 440, 280.0, 97.93892127751957, 4);
INSERT INTO building(id, name, level) VALUES (530, "building_arms_industry", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 530, 49.99249824561404, 11.666565771473563, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 530, 49.99249824561404, 17.938958494090027, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 530, 149.9774947368421, 44.408286398345375, 5);
INSERT INTO building(id, name, level) VALUES (531, "building_artillery_foundries", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 531, 29.885397849462368, 6.974245573894601, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 531, 19.923598566308243, 7.149244792247835, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 531, 49.80899641577061, 14.748427301888624, 2);
INSERT INTO building(id, name, level) VALUES (532, "building_munition_plants", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 532, 35.41559504132232, 34.98025500317623, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 532, 35.41559504132232, 30.6292224247156, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 532, 88.539, 82.01185826766816, 2);
INSERT INTO building(id, name, level) VALUES (533, "building_chemical_plants", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 533, 59.99339669421488, 20.049080813878973, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 533, 19.997793388429752, 4.666811622500249, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 533, 179.98019834710743, 51.07428165327794, 2);
INSERT INTO building(id, name, level) VALUES (534, "building_explosives_factory", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 534, 19.9606, 6.670595507923671, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 534, 19.9606, 16.14816029299242, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 534, 49.9015, 28.52344475114511, 1);
INSERT INTO building(id, name, level) VALUES (535, "building_coal_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 535, 19.995594594594593, 7.398115317734161, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 535, 79.9823963963964, 29.592467937373822, 2);
INSERT INTO building(id, name, level) VALUES (536, "building_iron_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 536, 39.98879611650485, 15.550132609373547, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 536, 39.98879611650485, 14.79534522905549, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 536, 159.95519417475728, 60.69095936059351, 4);
INSERT INTO building(id, name, level) VALUES (537, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 537, 15.0, 12.135026221400473, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 537, 3.0, 1.1099617892434306, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 537, 75.0, 44.212087919044066, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 537, 21.0, 12.37938461733234, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 537, 15.0, 8.842417583808814, 3);
INSERT INTO building(id, name, level) VALUES (952, "building_textile_mills", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 952, 319.99999999999994, 155.04168784997628, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 952, 39.99999999999999, 0.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 952, 480.0, 116.28126588748223, 8);
INSERT INTO building(id, name, level) VALUES (953, "building_construction_sector", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 953, 60.0, 29.07031647187056, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 953, 120.0, 43.84507168877302, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 953, 150.0, 35.00494928505728, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 953, 30.0, 11.099617892434305, 3);
INSERT INTO building(id, name, level) VALUES (954, "building_arms_industry", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 954, 40.0, 9.334653142681942, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 954, 40.0, 14.353320296941835, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 954, 120.0, 35.53196015943566, 4);
INSERT INTO building(id, name, level) VALUES (955, "building_coal_mine", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 955, 70.00000000000001, 25.89910841568005, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 955, 280.00000000000006, 103.5964336627202, 7);
INSERT INTO building(id, name, level) VALUES (956, "building_lead_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 956, 40.0, 15.554489376543582, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 956, 40.0, 14.79949052324574, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 956, 160.0, 60.707959799578646, 4);
INSERT INTO building(id, name, level) VALUES (957, "building_iron_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 957, 40.0, 15.554489376543582, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 957, 40.0, 14.79949052324574, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 957, 160.0, 60.707959799578646, 4);
INSERT INTO building(id, name, level) VALUES (958, "building_livestock_ranch", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 958, 50.0, 29.957860078221948, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 958, 50.0, 18.49936315405718, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 958, 100.0, 48.45722323227912, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 958, 25.0, 12.11430580806978, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 958, 125.0, 60.571529040348906, 5);
INSERT INTO building(id, name, level) VALUES (964, "building_fishing_wharf", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 964, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (965, "building_rye_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 965, 15.0, 12.135026221400473, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 965, 3.0, 1.1099617892434306, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 965, 75.0, 44.212087919044066, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 965, 45.0, 26.527252751426442, 3);
INSERT INTO building(id, name, level) VALUES (966, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 966, 14.9979, 0.1810165210619028, 3);
INSERT INTO building(id, name, level) VALUES (967, "building_textile_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 967, 79.96479207920791, 38.74336353916504, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 967, 9.99559405940594, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 967, 119.94719801980197, 29.057525052914745, 2);
INSERT INTO building(id, name, level) VALUES (968, "building_logging_camp", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 968, 30.0, 11.099617892434305, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 968, 120.0, 44.39847156973722, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 968, 120.0, 44.39847156973722, 6);
INSERT INTO building(id, name, level) VALUES (969, "building_rye_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 969, 10.0, 8.09001748093365, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 969, 2.0, 0.7399745261622871, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 969, 50.0, 29.474725279362712, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 969, 30.0, 17.684835167617628, 2);
INSERT INTO building(id, name, level) VALUES (970, "building_food_industry", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 970, 200.0, 119.83144031288779, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 970, 200.0, 36.036396555571216, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 970, 175.0, 68.19217862995082, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 970, 300.0, 116.90087765134425, 5);
INSERT INTO building(id, name, level) VALUES (971, "building_rye_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 971, 10.0, 8.09001748093365, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 971, 2.0, 0.7399745261622871, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 971, 50.0, 29.474725279362712, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 971, 20.0, 11.789890111745084, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 971, 10.0, 5.894945055872542, 2);
INSERT INTO building(id, name, level) VALUES (972, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 972, 30.000000000000004, 17.97471604693317, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 972, 30.000000000000004, 11.099617892434308, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 972, 60.00000000000001, 29.074333939367477, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 972, 15.000000000000002, 7.268583484841869, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 972, 75.0, 36.34291742420934, 3);
INSERT INTO building(id, name, level) VALUES (2728, "building_subsistence_farms", 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2728, 101.88066, 101.88066, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2728, 25.47016, 25.47016, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2728, 25.47016, 25.47016, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2728, 25.47016, 25.47016, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2728, 25.47016, 25.47016, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2728, 25.47016, 25.47016, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2728, 25.47016, 25.47016, 51);
INSERT INTO building(id, name, level) VALUES (3686, "building_subsistence_farms", 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3686, 165.32840000000002, 198.39408, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3686, 41.332100000000004, 49.59852, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3686, 41.332100000000004, 49.59852, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3686, 41.332100000000004, 49.59852, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3686, 41.332100000000004, 49.59852, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3686, 41.332100000000004, 49.59852, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3686, 41.332100000000004, 49.59852, 85);
INSERT INTO building(id, name, level) VALUES (3687, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3687, 1.0, 0.36537559740644177, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3687, 1.0, 0.35405850932965227, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3687, 20.0, 7.194341067360941, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3687, 5.0, 1.7985852668402353, 1);
INSERT INTO building(id, name, level) VALUES (3688, "building_subsistence_farms", 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3688, 105.34598, 105.34598, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3688, 26.33649, 26.33649, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3688, 26.33649, 26.33649, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3688, 26.33649, 26.33649, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3688, 26.33649, 26.33649, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3688, 26.33649, 26.33649, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3688, 26.33649, 26.33649, 53);
INSERT INTO building(id, name, level) VALUES (3689, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3689, 2.0, 0.7307511948128835, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3689, 2.0, 0.7081170186593045, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3689, 40.0, 14.388682134721883, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3689, 10.0, 3.5971705336804707, 2);
INSERT INTO building(id, name, level) VALUES (3692, "building_subsistence_farms", 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3692, 124.89343333333333, 149.87212, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3692, 31.223358333333334, 37.46803, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3692, 31.223358333333334, 37.46803, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3692, 31.223358333333334, 37.46803, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3692, 31.223358333333334, 37.46803, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3692, 31.223358333333334, 37.46803, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3692, 31.223358333333334, 37.46803, 64);
INSERT INTO building(id, name, level) VALUES (3693, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3693, 1.0, 0.36537559740644177, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3693, 1.0, 0.514193414893941, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3693, 1.0, 0.35405850932965227, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3693, 20.0, 8.224183477533566, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3693, 8.0, 3.2896733910134266, 1);
INSERT INTO building(id, name, level) VALUES (3695, "building_subsistence_farms", 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3695, 71.14138, 71.14138, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3695, 17.78534, 17.78534, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3695, 17.78534, 17.78534, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3695, 17.78534, 17.78534, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3695, 17.78534, 17.78534, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3695, 17.78534, 17.78534, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3695, 17.78534, 17.78534, 37);
INSERT INTO building(id, name, level) VALUES (3696, "building_subsistence_farms", 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3696, 74.68292, 74.68292, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3696, 18.67073, 18.67073, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3696, 18.67073, 18.67073, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3696, 18.67073, 18.67073, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3696, 18.67073, 18.67073, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3696, 18.67073, 18.67073, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3696, 18.67073, 18.67073, 38);
INSERT INTO building(id, name, level) VALUES (3697, "building_subsistence_farms", 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3697, 38.58192, 38.58192, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3697, 9.64548, 9.64548, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3697, 9.64548, 9.64548, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3697, 9.64548, 9.64548, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3697, 9.64548, 9.64548, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3697, 9.64548, 9.64548, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3697, 9.64548, 9.64548, 27);
INSERT INTO building(id, name, level) VALUES (3698, "building_urban_center", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3698, 3.0, 1.0961267922193254, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3698, 3.0, 1.5425802446818229, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3698, 3.0, 1.0621755279889569, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3698, 60.0, 24.6725504326007, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3698, 24.0, 9.86902017304028, 3);
INSERT INTO building(id, name, level) VALUES (3700, "building_subsistence_farms", 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3700, 88.42032, 88.42032, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3700, 22.10508, 22.10508, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3700, 22.10508, 22.10508, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3700, 22.10508, 22.10508, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3700, 22.10508, 22.10508, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3700, 22.10508, 22.10508, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3700, 22.10508, 22.10508, 72);
INSERT INTO building(id, name, level) VALUES (3701, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3701, 1.0, 0.36537559740644177, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3701, 1.0, 0.514193414893941, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3701, 1.0, 0.35405850932965227, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3701, 20.0, 8.224183477533566, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3701, 8.0, 3.2896733910134266, 1);
INSERT INTO building(id, name, level) VALUES (3702, "building_subsistence_farms", 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3702, 52.22952, 52.22952, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3702, 13.05738, 13.05738, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3702, 13.05738, 13.05738, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3702, 13.05738, 13.05738, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3702, 13.05738, 13.05738, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3702, 13.05738, 13.05738, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3702, 13.05738, 13.05738, 36);
INSERT INTO building(id, name, level) VALUES (3703, "building_urban_center", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3703, 3.0, 1.0961267922193254, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3703, 3.0, 1.5425802446818229, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3703, 3.0, 1.0621755279889569, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3703, 60.00000000000001, 24.6725504326007, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3703, 24.0, 9.86902017304028, 3);
INSERT INTO building(id, name, level) VALUES (3704, "building_subsistence_farms", 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3704, 198.776, 198.776, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3704, 49.694, 49.694, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3704, 49.694, 49.694, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3704, 49.694, 49.694, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3704, 49.694, 49.694, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3704, 49.694, 49.694, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3704, 49.694, 49.694, 100);
INSERT INTO building(id, name, level) VALUES (3705, "building_urban_center", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3705, 4.0, 1.461502389625767, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3705, 4.0, 1.416234037318609, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3705, 80.0, 28.777364269443765, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3705, 20.0, 7.194341067360941, 4);
INSERT INTO building(id, name, level) VALUES (3706, "building_subsistence_farms", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3706, 98.974, 98.974, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3706, 24.7435, 24.7435, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3706, 24.7435, 24.7435, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3706, 24.7435, 24.7435, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3706, 24.7435, 24.7435, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3706, 24.7435, 24.7435, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3706, 24.7435, 24.7435, 50);
INSERT INTO building(id, name, level) VALUES (3707, "building_urban_center", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3707, 7.0, 2.5576291818450927, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3707, 7.0, 3.5993539042575864, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3707, 7.0, 2.478409565307566, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3707, 140.0, 57.56928434273497, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3707, 56.0, 23.027713737093986, 7);
INSERT INTO building(id, name, level) VALUES (3729, "building_subsistence_farms", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3729, 10.0, 10.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3729, 2.5, 2.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3729, 2.5, 2.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3729, 2.5, 2.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3729, 2.5, 2.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3729, 2.5, 2.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3729, 2.5, 2.5, 5);
INSERT INTO building(id, name, level) VALUES (3768, "building_barracks", 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3768, 8.9999, 9.50985667300159, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3768, 22.99962, 8.39087091813284, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3768, 1.9999, 1.759597392917751, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3768, 5.99956, 3.5946795802179454, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3768, 2.99978, 0.7000476451088609, 14);
INSERT INTO building(id, name, level) VALUES (3769, "building_barracks", 44);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3769, 26.99972, 28.529591152254408, 44);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3769, 72.99908, 26.631999025307927, 44);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3769, 5.9994, 5.278528225946675, 44);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3769, 23.99892, 14.379125747768843, 44);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3769, 16.9994, 3.967087565842685, 44);
INSERT INTO building(id, name, level) VALUES (3770, "building_barracks", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3770, 5.0, 5.283312410694335, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3770, 14.0, 5.107571031776167, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3770, 2.0, 1.7596853771866103, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3770, 4.0, 2.396628806257756, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3770, 4.0, 0.9334653142681941, 8);
INSERT INTO building(id, name, level) VALUES (3771, "building_barracks", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3771, 3.99996, 4.226607662056183, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3771, 11.99988, 4.377874248056441, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3771, 3.99996, 2.3966048399696933, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3771, 3.99996, 0.9334559796150514, 6);
INSERT INTO building(id, name, level) VALUES (3772, "building_barracks", 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3772, 8.9999, 9.50985667300159, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3772, 25.99982, 9.485423390242472, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3772, 1.9999, 1.759597392917751, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3772, 7.99988, 4.793185713651324, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3772, 7.99988, 1.8669026245769602, 14);
INSERT INTO building(id, name, level) VALUES (3773, "building_barracks", 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3773, 8.9999, 9.50985667300159, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3773, 25.99982, 9.485423390242472, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3773, 1.9999, 1.759597392917751, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3773, 7.99988, 4.793185713651324, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3773, 7.99988, 1.8669026245769602, 14);
INSERT INTO building(id, name, level) VALUES (3774, "building_barracks", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3774, 9.0, 9.509962339249803, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3774, 18.0, 6.566877040855072, 9);
INSERT INTO building(id, name, level) VALUES (3775, "building_barracks", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3775, 12.0, 4.377918027236714, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3775, 4.0, 3.5193707543732207, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3775, 12.0, 7.189886418773268, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3775, 12.0, 2.800395942804582, 8);
INSERT INTO building(id, name, level) VALUES (3776, "building_barracks", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3776, 8.99998, 9.50994120600016, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3776, 21.99989, 8.026142919018728, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3776, 3.99993, 2.396586865253646, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3776, 3.99993, 0.9334489786251944, 11);
INSERT INTO building(id, name, level) VALUES (3777, "building_naval_base", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3777, 11.34, 0.0, 8);
INSERT INTO building(id, name, level) VALUES (4143, "building_trade_center", 24);
INSERT INTO building(id, name, level) VALUES (4144, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4144, 2.0, 0.7307511948128835, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 4144, 2.0, 0.7081170186593045, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4144, 40.0, 14.388682134721883, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 4144, 10.0, 3.5971705336804707, 2);
INSERT INTO building(id, name, level) VALUES (4227, "building_trade_center", 11);
INSERT INTO building(id, name, level) VALUES (4430, "building_conscription_center", 6);
INSERT INTO building(id, name, level) VALUES (4569, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (4651, "building_motor_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 4651, 30.0, 7.815706244087174, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 4651, 40.0, 10.420941658782898, 1);
INSERT INTO building(id, name, level) VALUES (4652, "building_conscription_center", 7);
INSERT INTO building(id, name, level) VALUES (4821, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (4937, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (4941, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4941, 19.984, 9.682353406231021, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4941, 39.968, 14.603331877140668, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4941, 49.96, 11.658981775209746, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4941, 9.992, 3.6969127327067866, 1);
INSERT INTO building(id, name, level) VALUES (4988, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (5020, "building_naval_base", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5020, 1.214, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (5065, "building_naval_base", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5065, 1.842, 0.0, 4);
INSERT INTO building(id, name, level) VALUES (5198, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5198, 40.0, 19.38021098124704, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5198, 80.0, 29.23004779251534, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5198, 100.0, 23.336632856704853, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5198, 20.0, 7.39974526162287, 2);
INSERT INTO building(id, name, level) VALUES (5258, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 5258, 10.0, 5.9915720156443895, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5258, 10.0, 3.699872630811435, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 5258, 20.0, 9.691444646455825, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 5258, 5.0, 2.422861161613956, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 5258, 25.0, 12.11430580806978, 1);
INSERT INTO building(id, name, level) VALUES (5404, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5404, 1.0, 0.36537559740644177, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 5404, 1.0, 0.514193414893941, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5404, 1.0, 0.35405850932965227, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5404, 20.0, 8.224183477533566, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 5404, 8.0, 3.2896733910134266, 1);
INSERT INTO building(id, name, level) VALUES (5428, "building_chemical_plants", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 5428, 25.05, 8.371412556410528, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5428, 8.35, 1.9486088435348552, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 5428, 75.15, 21.325858630522642, 1);
INSERT INTO building(id, name, level) VALUES (5526, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5526, 20.0, 9.69010549062352, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5526, 40.0, 14.61502389625767, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5526, 50.0, 11.668316428352426, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5526, 10.0, 3.699872630811435, 1);
INSERT INTO building(id, name, level) VALUES (5587, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 5587, 5.0, 0.06034728897442402, 1);
INSERT INTO building(id, name, level) VALUES (5625, "building_railway", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5625, 2.0, 0.777724468827179, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 5625, 5.0, 2.5709670744697046, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 5625, 20.0, 9.030556493075304, 1);
INSERT INTO building(id, name, level) VALUES (16783032, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783032, 0.2, 0.09690105490623521, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783032, 0.4, 0.14615023896257673, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16783032, 0.2, 0.07176660148470919, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 16783032, 0.05, 0.02570967074469705, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 16783032, 0.7, 0.30150877649464336, 1);
INSERT INTO building(id, name, level) VALUES (5838, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5838, 1.0, 0.36537559740644177, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 5838, 1.0, 0.514193414893941, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5838, 1.0, 0.35405850932965227, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5838, 20.0, 8.224183477533566, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 5838, 8.0, 3.2896733910134266, 1);
INSERT INTO building(id, name, level) VALUES (5845, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5845, 20.0, 9.69010549062352, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5845, 40.0, 14.61502389625767, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5845, 50.0, 11.668316428352426, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5845, 10.0, 3.699872630811435, 1);
INSERT INTO building(id, name, level) VALUES (5847, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5847, 20.0, 9.69010549062352, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5847, 40.0, 14.61502389625767, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5847, 50.0, 11.668316428352426, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5847, 10.0, 3.699872630811435, 1);
