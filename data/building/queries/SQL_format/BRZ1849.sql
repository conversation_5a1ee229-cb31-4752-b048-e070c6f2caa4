
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 85.06380821687672, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 29.941884991227028, 2225.223218625074);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 22.88183032502158, 130.75452337259188);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 21.557090080613015, 162.30920176576637);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 12.99312867663967, 265.5998719842331);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 40.32735385771238, 591.5975915219248);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 45.70358635006128, 505.27308097272027);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 39.417075014977804, 40.451824024169724);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 22.593883030826095, 256.2135025000002);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 80.61684619688165, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 51.785346383972396, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 49.11334817546142, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 42.68202770146965, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 15.749682266532684);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 47.636002442499816, 34.3524938727937);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 35.09208175381959, 97.5005009787665);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 47.096729945285844, 535.2364624967825);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 28.95246798685522, 8.287656540578537);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 25.47400688997191, 140.62893145942164);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 13.171855512057604, 26.884035504075847);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 57.2188781182797, 197.13405625241327);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 53.71678530507576);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 146.99679246730278);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (16778775, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (1838, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1838, 22.3704, 22.3704, 1);
INSERT INTO building(id, name, level) VALUES (1839, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1839, 8.917348214285713, 8.12012904980687, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1839, 107.00819642857142, 97.44156485838025, 3);
INSERT INTO building(id, name, level) VALUES (1840, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1840, 16.26659523809524, 5.485287909648733, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1840, 65.06639682539682, 21.941156991163233, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1840, 8.13329365079365, 2.7426426166822906, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1840, 8.13329365079365, 2.7426426166822906, 2);
INSERT INTO building(id, name, level) VALUES (1841, "building_coffee_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1841, 36.73679411764706, 37.47153, 3);
INSERT INTO building(id, name, level) VALUES (1842, "building_barrackslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1842, 8.0, 0.0, 8);
INSERT INTO building(id, name, level) VALUES (1843, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1843, 5.0, 2.7092607292790682, 1);
INSERT INTO building(id, name, level) VALUES (1844, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1844, 4.979299999999999, 4.5341459822024035, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1844, 59.751599999999996, 54.40975178642885, 1);
INSERT INTO building(id, name, level) VALUES (1845, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1845, 26.535, 26.535, 1);
INSERT INTO building(id, name, level) VALUES (1846, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1846, 5.0, 4.5529953830883905, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1846, 59.99999999999999, 54.63594459706068, 1);
INSERT INTO building(id, name, level) VALUES (1847, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1847, 9.2686, 3.1254813177071186, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1847, 37.0744, 12.501925270828474, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1847, 4.6343, 1.5627406588535593, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1847, 4.6343, 1.5627406588535593, 1);
INSERT INTO building(id, name, level) VALUES (1848, "building_coffee_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1848, 53.05859803921569, 54.11977, 3);
INSERT INTO building(id, name, level) VALUES (1849, "building_sugar_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1849, 26.5293, 26.5293, 1);
INSERT INTO building(id, name, level) VALUES (1850, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1850, 18.50979279279279, 6.241720601655453, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1850, 74.03919819819819, 24.966891520454332, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1850, 9.25489189189189, 3.1208587818556413, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1850, 9.25489189189189, 3.1208587818556413, 2);
INSERT INTO building(id, name, level) VALUES (1851, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1851, 20.000000000000004, 18.211981532353565, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1851, 240.0, 218.54377838824274, 4);
INSERT INTO building(id, name, level) VALUES (1852, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1852, 10.0, 5.814633326676531, 1);
INSERT INTO building(id, name, level) VALUES (1853, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1853, 59.98679207920792, 112.7045341016657, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1853, 39.99119801980198, 24.311258208235255, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1853, 119.97359405940594, 96.45368434205585, 2);
INSERT INTO building(id, name, level) VALUES (1854, "building_gold_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1854, 10.0, 9.105990766176781, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 1854, 20.0, 18.211981532353562, 2);
INSERT INTO building(id, name, level) VALUES (1855, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1855, 10.0, 9.105990766176781, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1855, 40.0, 36.423963064707124, 2);
INSERT INTO building(id, name, level) VALUES (1856, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1856, 20.000000000000004, 18.211981532353565, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1856, 240.0, 218.54377838824274, 4);
INSERT INTO building(id, name, level) VALUES (1857, "building_maize_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1857, 89.99999999999999, 100.8, 3);
INSERT INTO building(id, name, level) VALUES (1858, "building_coffee_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1858, 141.54879439252335, 151.45721, 8);
INSERT INTO building(id, name, level) VALUES (1859, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1859, 5.0, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (1860, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1860, 60.0, 34.88779996005918, 6);
INSERT INTO building(id, name, level) VALUES (1861, "building_universitylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1861, 25.0, 14.536583316691326, 5);
INSERT INTO building(id, name, level) VALUES (1862, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1862, 50.0, 44.80982970445847, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1862, 150.0, 281.8233736007622, 2);
INSERT INTO building(id, name, level) VALUES (1863, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1863, 10.0, 8.961965940891696, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1863, 30.0, 56.36467472015243, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1863, 5.0, 4.5529953830883905, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1863, 65.0, 60.81390619864837, 1);
INSERT INTO building(id, name, level) VALUES (1864, "building_paper_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1864, 90.0, 169.09402416045728, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1864, 120.0, 120.0, 3);
INSERT INTO building(id, name, level) VALUES (1865, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1865, 48.83, 49.3183, 2);
INSERT INTO building(id, name, level) VALUES (1866, "building_coffee_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1866, 70.74479611650486, 72.86714, 4);
INSERT INTO building(id, name, level) VALUES (1867, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1867, 53.05859405940594, 53.58918, 2);
INSERT INTO building(id, name, level) VALUES (1868, "building_maize_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1868, 24.675, 3.7141105612651497, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1868, 243.75652631578947, 36.69052436744803, 5);
INSERT INTO building(id, name, level) VALUES (1869, "building_barrackslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1869, 6.0, 0.0, 6);
INSERT INTO building(id, name, level) VALUES (1870, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1870, 10.0, 5.4185214585581365, 2);
INSERT INTO building(id, name, level) VALUES (1871, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1871, 79.836, 46.42170662685475, 4);
INSERT INTO building(id, name, level) VALUES (1872, "building_shipyardslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1872, 20.0, 17.923931881783393, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1872, 40.0, 75.15289962686991, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1872, 15.0, 14.221474455668773, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1872, 20.0, 18.961965940891695, 1);
INSERT INTO building(id, name, level) VALUES (1873, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1873, 4.99, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1873, 2.495, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (1874, "building_coffee_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1874, 70.74879611650486, 72.87126, 4);
INSERT INTO building(id, name, level) VALUES (1875, "building_sugar_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1875, 26.5293, 26.5293, 1);
INSERT INTO building(id, name, level) VALUES (1876, "building_maize_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1876, 29.999999999999996, 33.0, 1);
INSERT INTO building(id, name, level) VALUES (1877, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1877, 20.000000000000004, 18.211981532353565, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1877, 240.0, 218.54377838824274, 4);
INSERT INTO building(id, name, level) VALUES (1878, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1878, 10.04, 10.1404, 2);
INSERT INTO building(id, name, level) VALUES (1879, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1879, 19.944, 14.221586883168147, 10);
INSERT INTO building(id, name, level) VALUES (1880, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1880, 9.998, 5.417437754266425, 2);
INSERT INTO building(id, name, level) VALUES (1881, "building_maize_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1881, 29.999999999999996, 33.0, 1);
INSERT INTO building(id, name, level) VALUES (1882, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1882, 5.0, 4.5529953830883905, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1882, 59.99999999999999, 54.63594459706068, 1);
INSERT INTO building(id, name, level) VALUES (1883, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1883, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1884, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1884, 39.3724, 49.2155, 1);
INSERT INTO building(id, name, level) VALUES (1885, "building_maize_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1885, 29.999999999999996, 33.0, 1);
INSERT INTO building(id, name, level) VALUES (1886, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1886, 15.0, 13.658986149265171, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1886, 179.99999999999997, 163.90783379118204, 3);
INSERT INTO building(id, name, level) VALUES (1887, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1887, 21.175, 21.175, 1);
INSERT INTO building(id, name, level) VALUES (1888, "building_maize_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1888, 29.999999999999996, 33.0, 1);
INSERT INTO building(id, name, level) VALUES (1889, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1889, 9.999999999999998, 9.10599076617678, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1889, 119.99999999999999, 109.27188919412136, 2);
INSERT INTO building(id, name, level) VALUES (1890, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1890, 39.72, 35.59692871722181, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1890, 44.685, 40.046544806874536, 1);
INSERT INTO building(id, name, level) VALUES (1891, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1891, 39.348, 39.74148, 2);
INSERT INTO building(id, name, level) VALUES (1892, "building_maize_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1892, 9.859, 1.4839884913277857, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1892, 97.39403603603603, 14.659866984627355, 2);
INSERT INTO building(id, name, level) VALUES (1893, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1893, 1.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1893, 0.5, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (1894, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1894, 20.0, 11.629266653353062, 2);
INSERT INTO building(id, name, level) VALUES (1895, "building_textile_millslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1895, 160.0, 143.39145505426714, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1895, 180.0, 161.31538693605054, 4);
INSERT INTO building(id, name, level) VALUES (1896, "building_cotton_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1896, 196.862, 253.95198, 5);
INSERT INTO building(id, name, level) VALUES (1897, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1897, 22.13725, 22.13725, 1);
INSERT INTO building(id, name, level) VALUES (1898, "building_maize_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1898, 14.803946428571427, 2.228307751925456, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1898, 146.2436339285714, 22.01276698244788, 3);
INSERT INTO building(id, name, level) VALUES (1899, "building_barrackslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1899, 4.0, 0.0, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1899, 4.0, 0.0, 4);
INSERT INTO building(id, name, level) VALUES (1900, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1900, 4.997, 2.7076351728415005, 1);
INSERT INTO building(id, name, level) VALUES (1901, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1901, 5.0, 4.5529953830883905, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1901, 59.99999999999999, 54.63594459706068, 1);
INSERT INTO building(id, name, level) VALUES (1902, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1902, 26.5293, 26.5293, 1);
INSERT INTO building(id, name, level) VALUES (1903, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1903, 5.0, 4.5529953830883905, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1903, 59.99999999999999, 54.63594459706068, 1);
INSERT INTO building(id, name, level) VALUES (1904, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1904, 5.0, 4.5529953830883905, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1904, 59.99999999999999, 54.63594459706068, 1);
INSERT INTO building(id, name, level) VALUES (1905, "building_barrackslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1905, 8.0, 0.0, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1905, 4.0, 0.0, 8);
INSERT INTO building(id, name, level) VALUES (1906, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1906, 5.0, 2.7092607292790682, 1);
INSERT INTO building(id, name, level) VALUES (3077, "building_subsistence_farmslevel", 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3077, 29.729, 32.7019, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3077, 5.945799999999999, 6.54038, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3077, 5.945799999999999, 6.54038, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3077, 5.945799999999999, 6.54038, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3077, 5.945799999999999, 6.54038, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3077, 5.945799999999999, 6.54038, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3077, 8.32411818181818, 9.15653, 28);
INSERT INTO building(id, name, level) VALUES (3078, "building_subsistence_farmslevel", 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3078, 16.180445454545453, 17.79849, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3078, 3.236081818181818, 3.55969, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3078, 3.236081818181818, 3.55969, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3078, 3.236081818181818, 3.55969, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3078, 3.236081818181818, 3.55969, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3078, 3.236081818181818, 3.55969, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3078, 4.530518181818182, 4.98357, 73);
INSERT INTO building(id, name, level) VALUES (3080, "building_subsistence_farmslevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3080, 4.3923, 4.83153, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3080, 0.8784545454545454, 0.9663, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3080, 0.8784545454545454, 0.9663, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3080, 0.8784545454545454, 0.9663, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3080, 0.8784545454545454, 0.9663, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3080, 0.8784545454545454, 0.9663, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3080, 1.2298363636363634, 1.35282, 12);
INSERT INTO building(id, name, level) VALUES (3083, "building_subsistence_farmslevel", 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3083, 8.944745454545453, 9.83922, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3083, 1.7889454545454544, 1.96784, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3083, 1.7889454545454544, 1.96784, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3083, 1.7889454545454544, 1.96784, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3083, 1.7889454545454544, 1.96784, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3083, 1.7889454545454544, 1.96784, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3083, 2.5045272727272727, 2.75498, 74);
INSERT INTO building(id, name, level) VALUES (3085, "building_subsistence_farmslevel", 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3085, 11.528400000000001, 14.4105, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3085, 2.3056799999999997, 2.8821, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3085, 2.3056799999999997, 2.8821, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3085, 2.3056799999999997, 2.8821, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3085, 2.3056799999999997, 2.8821, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3085, 2.3056799999999997, 2.8821, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3085, 3.2279519999999997, 4.03494, 78);
INSERT INTO building(id, name, level) VALUES (3086, "building_subsistence_farmslevel", 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3086, 20.335, 25.41875, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3086, 4.067, 5.08375, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3086, 4.067, 5.08375, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3086, 4.067, 5.08375, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3086, 4.067, 5.08375, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3086, 4.067, 5.08375, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3086, 5.6938, 7.11725, 49);
INSERT INTO building(id, name, level) VALUES (3087, "building_subsistence_farmslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3087, 8.4337, 9.27707, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3087, 1.6867363636363635, 1.85541, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3087, 1.6867363636363635, 1.85541, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3087, 1.6867363636363635, 1.85541, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3087, 1.6867363636363635, 1.85541, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3087, 1.6867363636363635, 1.85541, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3087, 2.3614272727272727, 2.59757, 44);
INSERT INTO building(id, name, level) VALUES (3088, "building_subsistence_farmslevel", 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3088, 5.4837454545454545, 6.03212, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3088, 1.0967454545454545, 1.20642, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3088, 1.0967454545454545, 1.20642, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3088, 1.0967454545454545, 1.20642, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3088, 1.0967454545454545, 1.20642, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3088, 1.0967454545454545, 1.20642, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3088, 1.5354454545454543, 1.68899, 41);
INSERT INTO building(id, name, level) VALUES (3089, "building_subsistence_farmslevel", 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3089, 35.8974, 39.48714, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3089, 7.179472727272727, 7.89742, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3089, 7.179472727272727, 7.89742, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3089, 7.179472727272727, 7.89742, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3089, 7.179472727272727, 7.89742, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3089, 7.179472727272727, 7.89742, 222);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3089, 10.051263636363636, 11.05639, 222);
INSERT INTO building(id, name, level) VALUES (3090, "building_subsistence_farmslevel", 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3090, 44.90541818181818, 49.39596, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3090, 8.981081818181817, 9.87919, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3090, 8.981081818181817, 9.87919, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3090, 8.981081818181817, 9.87919, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3090, 8.981081818181817, 9.87919, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3090, 8.981081818181817, 9.87919, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3090, 12.573518181818182, 13.83087, 103);
INSERT INTO building(id, name, level) VALUES (3091, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3091, 10.0, 18.788224906717478, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3091, 50.0, 50.0, 2);
INSERT INTO building(id, name, level) VALUES (3092, "building_subsistence_farmslevel", 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3092, 70.14757272727272, 77.16233, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3092, 14.029509090909091, 15.43246, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3092, 14.029509090909091, 15.43246, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3092, 14.029509090909091, 15.43246, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3092, 14.029509090909091, 15.43246, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3092, 14.029509090909091, 15.43246, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3092, 19.641318181818182, 21.60545, 89);
INSERT INTO building(id, name, level) VALUES (3093, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3093, 28.02389523809524, 52.651924649562226, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3093, 140.11949523809523, 140.11949523809523, 6);
INSERT INTO building(id, name, level) VALUES (3094, "building_subsistence_farmslevel", 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3094, 37.47477272727273, 41.22225, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3094, 7.494954545454545, 8.24445, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3094, 7.494954545454545, 8.24445, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3094, 7.494954545454545, 8.24445, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3094, 7.494954545454545, 8.24445, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3094, 7.494954545454545, 8.24445, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3094, 10.492936363636362, 11.54223, 67);
INSERT INTO building(id, name, level) VALUES (3095, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3095, 15.0, 28.182337360076215, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3095, 75.0, 75.0, 3);
INSERT INTO building(id, name, level) VALUES (3096, "building_subsistence_farmslevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3096, 2.7128727272727273, 2.98416, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3096, 0.5425727272727272, 0.59683, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3096, 0.5425727272727272, 0.59683, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3096, 0.5425727272727272, 0.59683, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3096, 0.5425727272727272, 0.59683, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3096, 0.5425727272727272, 0.59683, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3096, 0.7595999999999999, 0.83556, 11);
INSERT INTO building(id, name, level) VALUES (3097, "building_subsistence_farmslevel", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3097, 11.937445454545454, 13.13119, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3097, 2.387481818181818, 2.62623, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3097, 2.387481818181818, 2.62623, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3097, 2.387481818181818, 2.62623, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3097, 2.387481818181818, 2.62623, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3097, 2.387481818181818, 2.62623, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3097, 3.342481818181818, 3.67673, 14);
INSERT INTO building(id, name, level) VALUES (3098, "building_subsistence_farmslevel", 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3098, 7.728572727272726, 8.50143, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3098, 1.5457090909090907, 1.70028, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3098, 1.5457090909090907, 1.70028, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3098, 1.5457090909090907, 1.70028, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3098, 1.5457090909090907, 1.70028, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3098, 1.5457090909090907, 1.70028, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3098, 2.1639999999999997, 2.3804, 23);
INSERT INTO building(id, name, level) VALUES (3099, "building_subsistence_farmslevel", 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3099, 91.89434545454544, 101.08378, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3099, 18.378863636363636, 20.21675, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3099, 18.378863636363636, 20.21675, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3099, 18.378863636363636, 20.21675, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3099, 18.378863636363636, 20.21675, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3099, 18.378863636363636, 20.21675, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3099, 25.73040909090909, 28.30345, 51);
INSERT INTO building(id, name, level) VALUES (3100, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3100, 5.0, 9.394112453358739, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3100, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3101, "building_subsistence_farmslevel", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3101, 5.2455, 5.77005, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3101, 1.0491, 1.15401, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3101, 1.0491, 1.15401, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3101, 1.0491, 1.15401, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3101, 1.0491, 1.15401, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3101, 1.0491, 1.15401, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3101, 1.4687363636363635, 1.61561, 30);
INSERT INTO building(id, name, level) VALUES (3889, "building_trade_centerlevel", 40);
INSERT INTO building(id, name, level) VALUES (3890, "building_trade_centerlevel", 28);
INSERT INTO building(id, name, level) VALUES (4074, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4076, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4077, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4078, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4079, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4080, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4081, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (33558998, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33558998, 9.999999999999998, 9.10599076617678, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 33558998, 119.99999999999999, 109.27188919412136, 2);
INSERT INTO building(id, name, level) VALUES (4652, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4652, 24.99, 22.395952886288345, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4652, 74.97, 140.8553221256609, 1);
INSERT INTO building(id, name, level) VALUES (4945, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 4945, 10.0, 5.814633326676531, 2);
INSERT INTO building(id, name, level) VALUES (50336614, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 50336614, 1.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 50336614, 0.5, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (16782259, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16782259, 5.0, 2.9073166633382654, 1);
INSERT INTO building(id, name, level) VALUES (16782319, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782319, 40.0, 35.847863763566785, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 16782319, 5.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16782319, 60.0, 26.88589782267509, 1);
INSERT INTO building(id, name, level) VALUES (16782451, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16782451, 10.0, 5.814633326676531, 1);
INSERT INTO building(id, name, level) VALUES (16782620, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16782620, 5.0, 2.9073166633382654, 1);
INSERT INTO building(id, name, level) VALUES (16782812, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (5660, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (16782884, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16782884, 1.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16782884, 0.5, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (16782900, "building_barrackslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16782900, 2.0, 0.0, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16782900, 2.0, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (16782918, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782918, 30.0, 56.36467472015243, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 16782918, 40.0, 40.0, 1);
INSERT INTO building(id, name, level) VALUES (33560264, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33560264, 30.0, 56.36467472015243, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33560264, 20.0, 12.158304533011155, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 33560264, 60.0, 48.23745679951673, 1);
INSERT INTO building(id, name, level) VALUES (16783120, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16783120, 4.88, 2.8375410634181466, 1);
INSERT INTO building(id, name, level) VALUES (33560366, "building_coffee_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 33560366, 17.6862, 17.6862, 1);
INSERT INTO building(id, name, level) VALUES (16783217, "building_tea_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 16783217, 18.89, 18.89, 1);
INSERT INTO building(id, name, level) VALUES (33560494, "building_gold_fieldslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 33560494, 120.0, 126.0, 6);
INSERT INTO building(id, name, level) VALUES (6164, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 6164, 5.0, 2.9073166633382654, 1);
INSERT INTO building(id, name, level) VALUES (16783403, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16783403, 22.10775, 22.10775, 1);
INSERT INTO building(id, name, level) VALUES (33560792, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (6573, "building_subsistence_rice_paddieslevel", 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6573, 0.0032818181818181813, 0.00361, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6573, 0.0005454545454545454, 0.0006, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6573, 0.0005454545454545454, 0.0006, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6573, 0.0007272727272727272, 0.0008, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6573, 0.0007272727272727272, 0.0008, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6573, 0.0007272727272727272, 0.0008, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6573, 0.0010909090909090907, 0.0012, 73);
