
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 27.83059426768495, 205.44612831172296);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 21.23481971545455, 27.24265082245402);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 15.274117464257175, 30.49356193141932);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 15.575486221344384, 22.86857056858068);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 17.69932972780734, 43.65836235790691);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 24.626250020765394, 39.05663391068909);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 16.7175245133264, 12.827328333333332);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 30.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 2.7843958333333325);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 26.721638465958005, 31.54950891117882);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 32.6895408831371, 10.035755548480351);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 30.262866120582714, 11.550955);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 32.44827923011249, 57.01413958863971);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 12.99384722222222);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 3.248461805555555);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (1849, "building_gold_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1849, 5.0, 7.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 1849, 10.0, 10.0, 1);
INSERT INTO building(id, name, level) VALUES (1860, "building_government_administrationlevel", 3);
INSERT INTO building(id, name, level) VALUES (1861, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1861, 30.0, 42.55123505260402, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1861, 30.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (1862, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1862, 29.880000000000003, 34.362, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1862, 4.98, 5.727, 1);
INSERT INTO building(id, name, level) VALUES (1863, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1863, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1864, "building_naval_baselevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1864, 6.0, 0.0, 3);
INSERT INTO building(id, name, level) VALUES (1865, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1865, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (1866, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1866, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1866, 2.5, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (1867, "building_government_administrationlevel", 1);
INSERT INTO building(id, name, level) VALUES (1868, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1868, 44.82674509803922, 45.72328, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1868, 23.90759803921569, 24.38575, 3);
INSERT INTO building(id, name, level) VALUES (1869, "building_textile_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1869, 80.0, 116.79835316023035, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1869, 90.0, 90.0, 2);
INSERT INTO building(id, name, level) VALUES (1870, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1870, 29.88, 29.88, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1870, 4.98, 4.98, 1);
INSERT INTO building(id, name, level) VALUES (1871, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1871, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1871, 2.5, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (1872, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1872, 10.0, 14.599794145028794, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1872, 30.0, 42.55123505260402, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1872, 45.0, 45.0, 1);
INSERT INTO building(id, name, level) VALUES (1873, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1873, 10.0, 15.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1873, 120.0, 120.0, 2);
INSERT INTO building(id, name, level) VALUES (1874, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1874, 29.880000000000003, 34.362, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1874, 4.98, 5.727, 1);
INSERT INTO building(id, name, level) VALUES (1875, "building_maize_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1875, 29.880000000000003, 34.362, 1);
INSERT INTO building(id, name, level) VALUES (1876, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1876, 59.760000000000005, 69.3216, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1876, 9.96, 11.5536, 2);
INSERT INTO building(id, name, level) VALUES (1877, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1877, 74.7, 76.194, 3);
INSERT INTO building(id, name, level) VALUES (1878, "building_gold_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1878, 5.0, 7.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 1878, 10.0, 10.0, 1);
INSERT INTO building(id, name, level) VALUES (3176, "building_subsistence_pastureslevel", 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3176, 0.9108, 1.04742, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3176, 1.3662, 1.57113, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3176, 0.4554, 0.52371, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3176, 0.9108, 1.04742, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3176, 0.9108, 1.04742, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3176, 0.9108, 1.04742, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3176, 2.4227217391304348, 2.78613, 92);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3176, 1.275113043478261, 1.46638, 92);
INSERT INTO building(id, name, level) VALUES (3177, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3177, 5.0, 7.091872508767337, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3177, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3181, "building_subsistence_pastureslevel", 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3181, 0.32634545454545455, 0.35898, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3181, 0.4895181818181818, 0.53847, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3181, 0.16317272727272727, 0.17949, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3181, 0.32634545454545455, 0.35898, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3181, 0.32634545454545455, 0.35898, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3181, 0.32634545454545455, 0.35898, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3181, 0.868090909090909, 0.9549, 61);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3181, 0.4568818181818181, 0.50257, 61);
INSERT INTO building(id, name, level) VALUES (3183, "building_subsistence_farmslevel", 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3183, 1.6863, 1.6863, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3183, 0.33726, 0.33726, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3183, 0.33726, 0.33726, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3183, 0.33726, 0.33726, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3183, 0.33726, 0.33726, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3183, 0.33726, 0.33726, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3183, 0.47216, 0.47216, 146);
INSERT INTO building(id, name, level) VALUES (3185, "building_subsistence_farmslevel", 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3185, 2.3894956521739132, 2.74792, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3185, 0.47789565217391305, 0.54958, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3185, 0.47789565217391305, 0.54958, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3185, 0.47789565217391305, 0.54958, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3185, 0.47789565217391305, 0.54958, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3185, 0.47789565217391305, 0.54958, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3185, 0.6690521739130436, 0.76941, 54);
INSERT INTO building(id, name, level) VALUES (3186, "building_subsistence_farmslevel", 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3186, 4.50304347826087, 5.1785, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3186, 0.900608695652174, 1.0357, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3186, 0.900608695652174, 1.0357, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3186, 0.900608695652174, 1.0357, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3186, 0.900608695652174, 1.0357, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3186, 0.900608695652174, 1.0357, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3186, 1.2608521739130436, 1.44998, 113);
INSERT INTO building(id, name, level) VALUES (3187, "building_subsistence_pastureslevel", 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3187, 1.4690999999999999, 1.61601, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3187, 2.2036454545454545, 2.42401, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3187, 0.7345454545454545, 0.808, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3187, 1.4690999999999999, 1.61601, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3187, 1.4690999999999999, 1.61601, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3187, 1.4690999999999999, 1.61601, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3187, 3.9078, 4.29858, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3187, 2.0567363636363636, 2.26241, 60);
INSERT INTO building(id, name, level) VALUES (3189, "building_subsistence_farmslevel", 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3189, 4.6682999999999995, 5.13513, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3189, 0.9336545454545454, 1.02702, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3189, 0.9336545454545454, 1.02702, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3189, 0.9336545454545454, 1.02702, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3189, 0.9336545454545454, 1.02702, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3189, 0.9336545454545454, 1.02702, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3189, 1.3071181818181816, 1.43783, 19);
INSERT INTO building(id, name, level) VALUES (3191, "building_subsistence_pastureslevel", 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3191, 0.3087, 0.33957, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3191, 0.4630454545454545, 0.50935, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3191, 0.1543454545454545, 0.16978, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3191, 0.3087, 0.33957, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3191, 0.3087, 0.33957, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3191, 0.3087, 0.33957, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3191, 0.8211363636363636, 0.90325, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3191, 0.4321727272727272, 0.47539, 35);
INSERT INTO building(id, name, level) VALUES (3998, "building_trade_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4188, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4193, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4195, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4196, "building_conscription_centerlevel", 2);
