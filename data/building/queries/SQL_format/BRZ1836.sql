
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 83.125, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 25.35555503399773, 1185.0679318809546);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 25.682051021177354, 243.0792447080119);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 16.504262657976966, 101.55518495382363);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 12.651213174090286, 226.5592525461765);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 34.150584558012945, 280.8659268353185);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 38.4277140317287, 150.38250215860035);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 33.59773281411396, 32.8400082089644);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 11.291812088335536, 41.9719);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 69.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 39.992112500000005, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 9.591661992568179);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 36.89982045337227, 21.691481113315113);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 36.41384429129701, 123.36695205097006);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 48.76183515559294, 415.0915260790553);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 18.114335171697242, 85.59300999999998);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 14.184552272462792, 35.685883379678955);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 53.631601551258434, 91.27343794070835);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 89.52217859730304);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 58.89440324470075);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (1894, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1894, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (1895, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1895, 5.0, 5.001314929050096, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1895, 60.0, 60.0, 1);
INSERT INTO building(id, name, level) VALUES (1900, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1900, 4.96845, 4.9697566318477895, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1900, 59.6214, 59.6214, 1);
INSERT INTO building(id, name, level) VALUES (1901, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1901, 28.44, 28.44, 1);
INSERT INTO building(id, name, level) VALUES (1902, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1902, 5.0, 5.001314929050096, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1902, 60.0, 60.0, 1);
INSERT INTO building(id, name, level) VALUES (1903, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1903, 29.88, 29.88, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1903, 4.98, 4.98, 1);
INSERT INTO building(id, name, level) VALUES (1904, "building_coffee_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1904, 99.6, 103.584, 5);
INSERT INTO building(id, name, level) VALUES (1905, "building_sugar_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1905, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (1906, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1906, 59.76, 60.3576, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1906, 9.959999999999999, 10.0596, 2);
INSERT INTO building(id, name, level) VALUES (1907, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1907, 15.0, 15.003944787150287, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1907, 180.0, 180.0, 3);
INSERT INTO building(id, name, level) VALUES (1908, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1908, 10.0, 8.401007638171572, 1);
INSERT INTO building(id, name, level) VALUES (1909, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1909, 60.0, 117.62840474079565, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1909, 40.0, 40.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1909, 120.0, 120.0, 2);
INSERT INTO building(id, name, level) VALUES (1910, "building_gold_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1910, 10.0, 10.002629858100192, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 1910, 20.0, 20.0, 2);
INSERT INTO building(id, name, level) VALUES (1911, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1911, 10.0, 10.002629858100192, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1911, 40.0, 40.0, 2);
INSERT INTO building(id, name, level) VALUES (1912, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1912, 20.0, 20.005259716200385, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1912, 239.99999999999997, 239.99999999999997, 4);
INSERT INTO building(id, name, level) VALUES (1913, "building_maize_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1913, 89.64, 91.4328, 3);
INSERT INTO building(id, name, level) VALUES (1914, "building_coffee_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1914, 199.19999999999996, 217.128, 10);
INSERT INTO building(id, name, level) VALUES (1915, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1915, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1915, 2.5, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (1916, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1916, 50.0, 42.00503819085786, 5);
INSERT INTO building(id, name, level) VALUES (1917, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1917, 10.0, 8.401007638171572, 2);
INSERT INTO building(id, name, level) VALUES (1918, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1918, 50.0, 65.19322639769145, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1918, 150.0, 294.0710118519891, 2);
INSERT INTO building(id, name, level) VALUES (1919, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1919, 10.0, 13.03864527953829, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1919, 30.0, 58.81420237039782, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1919, 45.0, 45.0, 1);
INSERT INTO building(id, name, level) VALUES (1920, "building_paper_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1920, 90.0, 176.44260711119347, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1920, 120.0, 120.0, 3);
INSERT INTO building(id, name, level) VALUES (1921, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1921, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (1922, "building_coffee_plantationlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1922, 119.52, 125.496, 6);
INSERT INTO building(id, name, level) VALUES (1923, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1923, 59.76, 60.3576, 2);
INSERT INTO building(id, name, level) VALUES (1924, "building_maize_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1924, 149.4, 155.376, 5);
INSERT INTO building(id, name, level) VALUES (1925, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1925, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1925, 2.5, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (1926, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1926, 10.0, 8.0, 2);
INSERT INTO building(id, name, level) VALUES (1927, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1927, 30.0, 25.203022914514715, 3);
INSERT INTO building(id, name, level) VALUES (1928, "building_shipyardslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1928, 20.0, 26.07729055907658, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1928, 40.0, 78.41893649386377, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1928, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1928, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (1929, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1929, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1929, 2.5, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (1930, "building_coffee_plantationlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1930, 119.52, 125.496, 6);
INSERT INTO building(id, name, level) VALUES (1931, "building_sugar_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1931, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (1932, "building_maize_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1932, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (1933, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1933, 20.0, 20.005259716200385, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1933, 239.99999999999997, 239.99999999999997, 4);
INSERT INTO building(id, name, level) VALUES (1934, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1934, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (1935, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1935, 20.0, 15.0, 10);
INSERT INTO building(id, name, level) VALUES (1936, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1936, 10.0, 8.0, 2);
INSERT INTO building(id, name, level) VALUES (1937, "building_maize_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1937, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (1938, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1938, 5.0, 5.001314929050096, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1938, 60.0, 60.0, 1);
INSERT INTO building(id, name, level) VALUES (1939, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1939, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1940, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1940, 39.839999999999996, 49.8, 1);
INSERT INTO building(id, name, level) VALUES (1941, "building_maize_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1941, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (1942, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1942, 10.0, 10.002629858100192, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1942, 120.0, 120.0, 2);
INSERT INTO building(id, name, level) VALUES (1943, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1943, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1944, "building_maize_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1944, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (1945, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1945, 5.0, 5.001314929050096, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1945, 60.0, 60.0, 1);
INSERT INTO building(id, name, level) VALUES (1946, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1946, 40.0, 52.15458111815316, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1946, 45.0, 45.0, 1);
INSERT INTO building(id, name, level) VALUES (1947, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1947, 24.9, 24.9, 1);
INSERT INTO building(id, name, level) VALUES (1948, "building_maize_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1948, 59.76, 60.3576, 2);
INSERT INTO building(id, name, level) VALUES (1949, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1949, 1.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1949, 0.5, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (1950, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1950, 10.0, 8.401007638171572, 1);
INSERT INTO building(id, name, level) VALUES (1951, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1951, 120.0, 156.46374335445947, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1951, 135.0, 135.0, 3);
INSERT INTO building(id, name, level) VALUES (1952, "building_cotton_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1952, 199.20000000000002, 256.968, 5);
INSERT INTO building(id, name, level) VALUES (1953, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1953, 24.9, 24.9, 1);
INSERT INTO building(id, name, level) VALUES (1954, "building_maize_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1954, 89.64, 91.4328, 3);
INSERT INTO building(id, name, level) VALUES (1955, "building_barrackslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1955, 4.0, 0.0, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1955, 2.0, 0.0, 4);
INSERT INTO building(id, name, level) VALUES (1956, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1956, 5.0, 4.0, 1);
INSERT INTO building(id, name, level) VALUES (1957, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1957, 5.0, 5.001314929050096, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1957, 60.0, 60.0, 1);
INSERT INTO building(id, name, level) VALUES (1958, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1958, 29.88, 29.88, 1);
INSERT INTO building(id, name, level) VALUES (1959, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1959, 5.0, 5.001314929050096, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1959, 60.0, 60.0, 1);
INSERT INTO building(id, name, level) VALUES (3149, "building_subsistence_rice_paddieslevel", 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3149, 12.93084, 12.93084, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3149, 2.15514, 2.15514, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3149, 2.15514, 2.15514, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3149, 2.87352, 2.87352, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3149, 2.87352, 2.87352, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3149, 2.87352, 2.87352, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3149, 4.31028, 4.31028, 39);
INSERT INTO building(id, name, level) VALUES (3154, "building_subsistence_farmslevel", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3154, 6.912, 6.912, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3154, 1.3824, 1.3824, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3154, 1.3824, 1.3824, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3154, 1.3824, 1.3824, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3154, 1.3824, 1.3824, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3154, 1.3824, 1.3824, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3154, 1.93536, 1.93536, 40);
INSERT INTO building(id, name, level) VALUES (3156, "building_subsistence_farmslevel", 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3156, 8.156495652173914, 9.37997, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3156, 1.6312956521739133, 1.87599, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3156, 1.6312956521739133, 1.87599, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3156, 1.6312956521739133, 1.87599, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3156, 1.6312956521739133, 1.87599, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3156, 1.6312956521739133, 1.87599, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3156, 2.283817391304348, 2.62639, 55);
INSERT INTO building(id, name, level) VALUES (3158, "building_subsistence_farmslevel", 119);
INSERT INTO building(id, name, level) VALUES (3159, "building_subsistence_farmslevel", 119);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3159, 2.9036, 2.9036, 119);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3159, 0.58072, 0.58072, 119);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3159, 0.58072, 0.58072, 119);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3159, 0.58072, 0.58072, 119);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3159, 0.58072, 0.58072, 119);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3159, 0.58072, 0.58072, 119);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3159, 0.813, 0.813, 119);
INSERT INTO building(id, name, level) VALUES (3160, "building_subsistence_farmslevel", 232);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3160, 14.79, 14.79, 232);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3160, 2.958, 2.958, 232);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3160, 2.958, 2.958, 232);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3160, 2.958, 2.958, 232);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3160, 2.958, 2.958, 232);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3160, 2.958, 2.958, 232);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3160, 4.1412, 4.1412, 232);
INSERT INTO building(id, name, level) VALUES (3161, "building_subsistence_farmslevel", 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3161, 27.31945, 27.31945, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3161, 5.46389, 5.46389, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3161, 5.46389, 5.46389, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3161, 5.46389, 5.46389, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3161, 5.46389, 5.46389, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3161, 5.46389, 5.46389, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3161, 7.64944, 7.64944, 227);
INSERT INTO building(id, name, level) VALUES (3162, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3162, 5.0, 9.802367061732971, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3162, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3163, "building_subsistence_farmslevel", 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3163, 32.9324, 32.9324, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3163, 6.58648, 6.58648, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3163, 6.58648, 6.58648, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3163, 6.58648, 6.58648, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3163, 6.58648, 6.58648, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3163, 6.58648, 6.58648, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3163, 9.22107, 9.22107, 167);
INSERT INTO building(id, name, level) VALUES (3164, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3164, 20.0, 39.209468246931884, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3164, 100.0, 100.0, 4);
INSERT INTO building(id, name, level) VALUES (3165, "building_subsistence_farmslevel", 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3165, 27.8683, 27.8683, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3165, 5.57366, 5.57366, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3165, 5.57366, 5.57366, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3165, 5.57366, 5.57366, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3165, 5.57366, 5.57366, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3165, 5.57366, 5.57366, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3165, 7.80312, 7.80312, 172);
INSERT INTO building(id, name, level) VALUES (3166, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3166, 10.0, 19.604734123465942, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3166, 50.0, 50.0, 2);
INSERT INTO building(id, name, level) VALUES (3167, "building_subsistence_farmslevel", 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3167, 3.68237, 3.68237, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3167, 0.73647, 0.73647, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3167, 0.73647, 0.73647, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3167, 0.73647, 0.73647, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3167, 0.73647, 0.73647, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3167, 0.73647, 0.73647, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3167, 1.03106, 1.03106, 89);
INSERT INTO building(id, name, level) VALUES (3168, "building_subsistence_farmslevel", 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3168, 14.2714, 14.2714, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3168, 2.85428, 2.85428, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3168, 2.85428, 2.85428, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3168, 2.85428, 2.85428, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3168, 2.85428, 2.85428, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3168, 2.85428, 2.85428, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3168, 3.99599, 3.99599, 88);
INSERT INTO building(id, name, level) VALUES (3169, "building_subsistence_farmslevel", 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3169, 7.03545, 7.03545, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3169, 1.40709, 1.40709, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3169, 1.40709, 1.40709, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3169, 1.40709, 1.40709, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3169, 1.40709, 1.40709, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3169, 1.40709, 1.40709, 89);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3169, 1.96992, 1.96992, 89);
INSERT INTO building(id, name, level) VALUES (3170, "building_subsistence_farmslevel", 170);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3170, 77.37125, 77.37125, 170);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3170, 15.47425, 15.47425, 170);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3170, 15.47425, 15.47425, 170);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3170, 15.47425, 15.47425, 170);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3170, 15.47425, 15.47425, 170);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3170, 15.47425, 15.47425, 170);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3170, 21.66395, 21.66395, 170);
INSERT INTO building(id, name, level) VALUES (3171, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3171, 5.0, 9.802367061732971, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3171, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3172, "building_subsistence_farmslevel", 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3172, 6.4386, 6.4386, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3172, 1.28772, 1.28772, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3172, 1.28772, 1.28772, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3172, 1.28772, 1.28772, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3172, 1.28772, 1.28772, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3172, 1.28772, 1.28772, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3172, 1.8028, 1.8028, 147);
INSERT INTO building(id, name, level) VALUES (3996, "building_trade_centerlevel", 28);
INSERT INTO building(id, name, level) VALUES (3997, "building_trade_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4180, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4181, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4182, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4183, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4184, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4185, "building_conscription_centerlevel", 5);
