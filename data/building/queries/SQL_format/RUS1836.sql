
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 94.40822155114519, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 110.14292514300271, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 84.06886959590301, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 19.17272846185116, 10113.844938268758);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 24.106585928236946, 311.8856932838622);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 16.14858148066457, 646.4075129275988);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 26.998029993314926, 1488.3322758224026);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 48.1601584812584, 584.052128684291);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 31.73892338208778, 2296.0552830325064);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 32.92783177782895, 1638.3460844590277);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 39.33532338685778, 106.87435463683855);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 15.506405823639927, 639.5740935459489);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 46.43536446121035, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 67.80466594314404, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 60.51670742837772, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 54.555235542583844, 46.654980793358355);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 67.43872727235217, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 120.75729194772623, 602.991887306482);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 44.20143654225472, 550.8362137947645);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 42.518170640760836, 95.07776238646682);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 33.054817587623866, 3408.062166931146);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 66.13392378016808, 611.6237658962538);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 72.26541025302437, 198.88950410374616);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 50.86006021739525, 104.40692878148474);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 52.23150831910205, 85.26328667664595);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 133.13558378487687);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 100.2648138954401, 703.4905351908945);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 284.1719111429157, 4.036394093107616);
INSERT INTO building(id, name, level) VALUES (954, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 954, 99.99999999999999, 59.04624644962186, 10);
INSERT INTO building(id, name, level) VALUES (955, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 955, 50.0, 67.27313257751713, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 955, 150.0, 80.06390990207721, 2);
INSERT INTO building(id, name, level) VALUES (956, "building_textile_millslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 956, 160.0, 215.27402424805484, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 956, 180.0, 180.0, 4);
INSERT INTO building(id, name, level) VALUES (957, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 957, 90.0, 48.03834594124633, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 957, 45.0, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 957, 30.0, 8.006390990207722, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 957, 75.0, 20.015977475519303, 3);
INSERT INTO building(id, name, level) VALUES (958, "building_arts_academylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 958, 5.0, 2.952312322481094, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 958, 2.0, 1.1809249289924375, 2);
INSERT INTO building(id, name, level) VALUES (959, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 959, 20.0, 14.687154948589235, 10);
INSERT INTO building(id, name, level) VALUES (960, "building_rye_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 960, 30.0, 30.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 960, 45.0, 45.9, 3);
INSERT INTO building(id, name, level) VALUES (961, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 961, 15.0, 21.54116059126422, 3);
INSERT INTO building(id, name, level) VALUES (962, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 962, 99.99999999999999, 59.04624644962186, 10);
INSERT INTO building(id, name, level) VALUES (963, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 963, 50.0, 67.27313257751713, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 963, 150.0, 80.06390990207721, 2);
INSERT INTO building(id, name, level) VALUES (964, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 964, 60.0, 32.02556396083088, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 964, 40.0, 2.9342839623291157, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 964, 120.0, 36.42698990432455, 2);
INSERT INTO building(id, name, level) VALUES (965, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 965, 10.0, 5.904624644962188, 2);
INSERT INTO building(id, name, level) VALUES (966, "building_textile_millslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 966, 240.0, 322.91103637208226, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 966, 270.0, 270.0, 6);
INSERT INTO building(id, name, level) VALUES (967, "building_furniture_manufacturieslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 967, 60.0, 80.72775909302057, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 967, 120.0, 64.05112792166176, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 967, 60.0, 19.01305627001342, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 967, 150.0, 92.53218352570359, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 967, 120.0, 74.02574682056286, 6);
INSERT INTO building(id, name, level) VALUES (968, "building_logging_camplevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 968, 50.0, 4.3241428392617305, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 968, 400.0, 34.593142714093844, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 968, 100.0, 8.648285678523461, 10);
INSERT INTO building(id, name, level) VALUES (969, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 969, 20.0, 4.898546385623278, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 969, 20.0, 4.898546385623278, 20);
INSERT INTO building(id, name, level) VALUES (970, "building_saint_basils_cathedrallevel", 1);
INSERT INTO building(id, name, level) VALUES (971, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 971, 10.0, 2.449273192811639, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 971, 10.0, 2.449273192811639, 10);
INSERT INTO building(id, name, level) VALUES (972, "building_rye_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 972, 150.0, 156.0, 5);
INSERT INTO building(id, name, level) VALUES (973, "building_logging_camplevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 973, 40.0, 3.459314271409384, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 973, 320.0, 27.67451417127507, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 973, 80.0, 6.918628542818768, 8);
INSERT INTO building(id, name, level) VALUES (974, "building_logging_camplevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 974, 30.0, 2.594485703557038, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 974, 240.0, 20.755885628456305, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 974, 60.0, 5.188971407114076, 6);
INSERT INTO building(id, name, level) VALUES (975, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 975, 45.0, 45.9, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 975, 24.0, 24.48, 3);
INSERT INTO building(id, name, level) VALUES (976, "building_barrackslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 976, 8.0, 1.959418554249311, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 976, 8.0, 1.959418554249311, 8);
INSERT INTO building(id, name, level) VALUES (977, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 977, 99.99999999999999, 59.04624644962186, 10);
INSERT INTO building(id, name, level) VALUES (978, "building_arts_academylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 978, 2.5, 1.476156161240547, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 978, 1.0, 0.5904624644962188, 1);
INSERT INTO building(id, name, level) VALUES (979, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 979, 80.0, 84.66965293451074, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 979, 50.0, 3.644310628010554, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 979, 30.0, 16.093293188403166, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 979, 120.0, 64.37317275361266, 2);
INSERT INTO building(id, name, level) VALUES (980, "building_livestock_ranchlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 980, 150.0, 178.5, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 980, 25.0, 29.75, 5);
INSERT INTO building(id, name, level) VALUES (981, "building_wheat_farmlevel", 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 981, 1350.0, 1822.5, 45);
INSERT INTO building(id, name, level) VALUES (982, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 982, 20.0, 4.898546385623278, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 982, 20.0, 4.898546385623278, 20);
INSERT INTO building(id, name, level) VALUES (983, "building_textile_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 983, 80.0, 107.63701212402742, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 983, 90.0, 90.0, 2);
INSERT INTO building(id, name, level) VALUES (984, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 984, 160.0, 169.33930586902147, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 984, 100.0, 7.288621256021108, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 984, 59.99999999999999, 32.186586376806325, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 984, 239.99999999999997, 128.7463455072253, 4);
INSERT INTO building(id, name, level) VALUES (985, "building_rye_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 985, 40.0, 41.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 985, 59.99999999999999, 61.8, 4);
INSERT INTO building(id, name, level) VALUES (986, "building_logging_camplevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 986, 75.0, 6.486214258892595, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 986, 600.0, 51.88971407114076, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 986, 150.0, 12.97242851778519, 15);
INSERT INTO building(id, name, level) VALUES (987, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 987, 25.0, 6.123182982029096, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 987, 25.0, 6.123182982029096, 25);
INSERT INTO building(id, name, level) VALUES (988, "building_food_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 988, 120.0, 127.0044794017661, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 988, 75.0, 5.466465942015832, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 988, 45.0, 24.13993978260475, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 988, 180.0, 96.559759130419, 3);
INSERT INTO building(id, name, level) VALUES (989, "building_rye_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 989, 30.0, 30.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 989, 45.0, 45.9, 3);
INSERT INTO building(id, name, level) VALUES (990, "building_logging_camplevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 990, 40.0, 3.459314271409384, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 990, 320.0, 27.67451417127507, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 990, 80.0, 6.918628542818768, 8);
INSERT INTO building(id, name, level) VALUES (991, "building_barrackslevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 991, 12.0, 2.9391278313739666, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 991, 12.0, 2.9391278313739666, 12);
INSERT INTO building(id, name, level) VALUES (992, "building_paper_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 992, 90.0, 48.03834594124633, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 992, 120.0, 64.05112792166177, 3);
INSERT INTO building(id, name, level) VALUES (993, "building_logging_camplevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 993, 59.99999999999999, 5.188971407114075, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 993, 479.99999999999994, 41.5117712569126, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 993, 119.99999999999999, 10.37794281422815, 12);
INSERT INTO building(id, name, level) VALUES (994, "building_rye_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 994, 70.0, 74.2, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 994, 104.99999999999999, 111.3, 7);
INSERT INTO building(id, name, level) VALUES (995, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 995, 10.0, 2.449273192811639, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 995, 10.0, 2.449273192811639, 10);
INSERT INTO building(id, name, level) VALUES (996, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 996, 5.0, 0.432414283926173, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 996, 40.0, 3.459314271409384, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 996, 10.0, 0.864828567852346, 1);
INSERT INTO building(id, name, level) VALUES (997, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 997, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (998, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 998, 10.0, 7.343577474294618, 5);
INSERT INTO building(id, name, level) VALUES (999, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 999, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1000, "building_shipyardslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1000, 20.0, 26.909253031006855, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1000, 40.0, 21.350375973887257, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1000, 15.0, 11.50319549510386, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1000, 20.0, 15.337593993471813, 1);
INSERT INTO building(id, name, level) VALUES (1001, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1001, 5.0, 7.18038686375474, 1);
INSERT INTO building(id, name, level) VALUES (1002, "building_arms_industrylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1002, 120.0, 8.802851886987346, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1002, 60.0, 19.01305627001342, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 1002, 90.0, 17.56086166013032, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 1002, 90.0, 17.56086166013032, 6);
INSERT INTO building(id, name, level) VALUES (1003, "building_logging_camplevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1003, 75.0, 6.486214258892595, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1003, 600.0, 51.88971407114076, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1003, 150.0, 12.97242851778519, 15);
INSERT INTO building(id, name, level) VALUES (1004, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1004, 15.0, 3.6739097892174586, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1004, 15.0, 3.6739097892174586, 15);
INSERT INTO building(id, name, level) VALUES (1005, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1005, 10.0, 7.343577474294618, 5);
INSERT INTO building(id, name, level) VALUES (1006, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1006, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (1007, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1007, 10.0, 14.36077372750948, 2);
INSERT INTO building(id, name, level) VALUES (1008, "building_livestock_ranchlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1008, 300.0, 372.0, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1008, 50.0, 62.0, 10);
INSERT INTO building(id, name, level) VALUES (1009, "building_wheat_farmlevel", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1009, 449.99999999999994, 607.5, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1009, 239.99999999999997, 324.0, 30);
INSERT INTO building(id, name, level) VALUES (1010, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1010, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (1011, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1011, 20.0, 4.898546385623278, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1011, 20.0, 4.898546385623278, 20);
INSERT INTO building(id, name, level) VALUES (1012, "building_rye_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1012, 30.0, 30.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1012, 45.0, 45.9, 3);
INSERT INTO building(id, name, level) VALUES (1013, "building_logging_camplevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1013, 50.0, 4.3241428392617305, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1013, 400.0, 34.593142714093844, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1013, 100.0, 8.648285678523461, 10);
INSERT INTO building(id, name, level) VALUES (1014, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1014, 20.0, 4.898546385623278, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1014, 20.0, 4.898546385623278, 20);
INSERT INTO building(id, name, level) VALUES (1015, "building_paper_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1015, 90.0, 48.03834594124633, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1015, 120.0, 64.05112792166177, 3);
INSERT INTO building(id, name, level) VALUES (1016, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1016, 25.0, 2.1620714196308652, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1016, 200.0, 17.296571357046922, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1016, 50.0, 4.3241428392617305, 5);
INSERT INTO building(id, name, level) VALUES (1017, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1017, 10.0, 2.449273192811639, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1017, 10.0, 2.449273192811639, 10);
INSERT INTO building(id, name, level) VALUES (1018, "building_shipyardslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1018, 80.0, 107.63701212402742, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1018, 160.0, 85.40150389554903, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1018, 59.99999999999999, 46.01278198041544, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1018, 80.0, 61.35037597388725, 4);
INSERT INTO building(id, name, level) VALUES (1019, "building_livestock_ranchlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1019, 240.00000000000003, 292.8, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1019, 40.0, 48.8, 8);
INSERT INTO building(id, name, level) VALUES (1020, "building_wheat_farmlevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1020, 450.0, 580.5, 15);
INSERT INTO building(id, name, level) VALUES (1021, "building_naval_baselevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1021, 40.0, 29.37430989717847, 20);
INSERT INTO building(id, name, level) VALUES (1022, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1022, 15.0, 21.54116059126422, 3);
INSERT INTO building(id, name, level) VALUES (1023, "building_rye_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1023, 30.0, 30.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1023, 45.0, 45.9, 3);
INSERT INTO building(id, name, level) VALUES (1024, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1024, 10.0, 0.864828567852346, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1024, 80.0, 6.918628542818768, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1024, 20.0, 1.729657135704692, 2);
INSERT INTO building(id, name, level) VALUES (1025, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1025, 10.0, 2.449273192811639, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1025, 10.0, 2.449273192811639, 10);
INSERT INTO building(id, name, level) VALUES (1031, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1031, 30.0, 30.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1031, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (1032, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1032, 10.0, 2.449273192811639, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1032, 10.0, 2.449273192811639, 10);
INSERT INTO building(id, name, level) VALUES (1033, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1033, 10.0, 0.864828567852346, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1033, 80.0, 6.918628542818768, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1033, 20.0, 1.729657135704692, 2);
INSERT INTO building(id, name, level) VALUES (1034, "building_shipyardslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1034, 40.0, 53.81850606201371, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1034, 80.0, 42.70075194777451, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1034, 30.0, 23.00639099020772, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1034, 40.0, 30.675187986943627, 2);
INSERT INTO building(id, name, level) VALUES (1035, "building_iron_minelevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1035, 30.0, 2.594485703557038, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1035, 120.0, 10.377942814228152, 6);
INSERT INTO building(id, name, level) VALUES (1036, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1036, 10.0, 0.864828567852346, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1036, 80.0, 6.918628542818768, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1036, 20.0, 1.729657135704692, 2);
INSERT INTO building(id, name, level) VALUES (1037, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1037, 10.0, 2.449273192811639, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1037, 10.0, 2.449273192811639, 10);
INSERT INTO building(id, name, level) VALUES (1038, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1038, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1039, "building_rye_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1039, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1039, 15.0, 15.0, 1);
INSERT INTO building(id, name, level) VALUES (1040, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1040, 10.0, 7.343577474294618, 5);
INSERT INTO building(id, name, level) VALUES (1041, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1041, 5.0, 1.2246365964058195, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1041, 5.0, 1.2246365964058195, 5);
INSERT INTO building(id, name, level) VALUES (1042, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1042, 5.0, 7.18038686375474, 1);
INSERT INTO building(id, name, level) VALUES (1043, "building_rye_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1043, 29.99579411764706, 30.59571, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1043, 44.99369607843137, 45.89357, 3);
INSERT INTO building(id, name, level) VALUES (1044, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1044, 10.0, 7.343577474294618, 5);
INSERT INTO building(id, name, level) VALUES (1045, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1045, 15.0, 21.54116059126422, 3);
INSERT INTO building(id, name, level) VALUES (1046, "building_rye_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1046, 40.0, 41.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1046, 59.99999999999999, 61.8, 4);
INSERT INTO building(id, name, level) VALUES (1047, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1047, 5.0, 1.2246365964058195, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1047, 5.0, 1.2246365964058195, 5);
INSERT INTO building(id, name, level) VALUES (1048, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1048, 5.0, 7.18038686375474, 1);
INSERT INTO building(id, name, level) VALUES (1049, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1049, 20.0, 26.909253031006855, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1049, 60.0, 32.02556396083088, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1049, 90.0, 69.01917297062316, 2);
INSERT INTO building(id, name, level) VALUES (1050, "building_paper_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1050, 90.0, 48.03834594124633, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1050, 120.0, 64.05112792166177, 3);
INSERT INTO building(id, name, level) VALUES (1051, "building_rye_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1051, 20.0, 20.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1051, 30.0, 30.3, 2);
INSERT INTO building(id, name, level) VALUES (1052, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1052, 30.0, 16.01278198041544, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1052, 20.0, 1.4671419811645579, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1052, 60.0, 18.213494952162275, 1);
INSERT INTO building(id, name, level) VALUES (1053, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1053, 15.0, 1.297242851778519, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1053, 120.0, 10.377942814228152, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1053, 30.0, 2.594485703557038, 3);
INSERT INTO building(id, name, level) VALUES (1061, "building_textile_millslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1061, 240.0, 322.91103637208226, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1061, 270.0, 270.0, 6);
INSERT INTO building(id, name, level) VALUES (1062, "building_rye_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1062, 30.0, 30.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1062, 45.0, 45.9, 3);
INSERT INTO building(id, name, level) VALUES (1063, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1063, 60.0, 32.02556396083088, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1063, 80.0, 42.700751947774506, 2);
INSERT INTO building(id, name, level) VALUES (1064, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1064, 90.0, 91.8, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1064, 15.0, 15.3, 3);
INSERT INTO building(id, name, level) VALUES (1065, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1065, 30.0, 16.01278198041544, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1065, 30.0, 16.01278198041544, 1);
INSERT INTO building(id, name, level) VALUES (1137, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1137, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1466, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (2117, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2117, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2117, 9.0, 9.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2117, 6.0, 6.0, 1);
INSERT INTO building(id, name, level) VALUES (2118, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2118, 30.0, 30.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2118, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (2119, "building_tea_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2119, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (2120, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2120, 3.0, 0.7347819578434917, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2120, 3.0, 0.7347819578434917, 3);
INSERT INTO building(id, name, level) VALUES (2123, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2123, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2123, 9.0, 9.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2123, 6.0, 6.0, 1);
INSERT INTO building(id, name, level) VALUES (2124, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2124, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (2125, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2125, 30.0, 30.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2125, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (2126, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2126, 3.0, 0.7347819578434917, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2126, 3.0, 0.7347819578434917, 3);
INSERT INTO building(id, name, level) VALUES (2130, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2130, 20.0, 20.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2130, 18.0, 18.18, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2130, 12.0, 12.12, 2);
INSERT INTO building(id, name, level) VALUES (2131, "building_rice_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2131, 20.0, 20.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2131, 6.0, 6.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2131, 9.0, 9.0, 1);
INSERT INTO building(id, name, level) VALUES (2132, "building_tea_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2132, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (2133, "building_textile_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2133, 40.0, 53.81850606201371, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2133, 45.0, 45.0, 1);
INSERT INTO building(id, name, level) VALUES (2134, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2134, 10.0, 2.449273192811639, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2134, 10.0, 2.449273192811639, 10);
INSERT INTO building(id, name, level) VALUES (2135, "building_arms_industrylevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2135, 199.99999999999997, 14.671419811645576, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2135, 99.99999999999999, 31.688427116689027, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 2135, 150.0, 29.268102766883867, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 2135, 150.0, 29.268102766883867, 10);
INSERT INTO building(id, name, level) VALUES (2136, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2136, 80.0, 84.66965293451074, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 2136, 50.0, 3.644310628010554, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 2136, 30.0, 16.093293188403166, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2136, 120.0, 64.37317275361266, 2);
INSERT INTO building(id, name, level) VALUES (2137, "building_rye_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2137, 40.0, 41.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2137, 59.99999999999999, 61.8, 4);
INSERT INTO building(id, name, level) VALUES (2138, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2138, 20.0, 4.898546385623278, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2138, 20.0, 4.898546385623278, 20);
INSERT INTO building(id, name, level) VALUES (2139, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2139, 50.0, 29.523123224810934, 5);
INSERT INTO building(id, name, level) VALUES (2140, "building_arms_industrylevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2140, 140.0, 10.269993868151905, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2140, 70.0, 22.181898981682323, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 2140, 104.99999999999999, 20.487671936818703, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 2140, 104.99999999999999, 20.487671936818703, 7);
INSERT INTO building(id, name, level) VALUES (2141, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2141, 60.0, 32.02556396083088, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2141, 80.0, 42.700751947774506, 2);
INSERT INTO building(id, name, level) VALUES (2142, "building_food_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2142, 120.0, 127.0044794017661, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 2142, 75.0, 5.466465942015832, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 2142, 45.0, 24.13993978260475, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2142, 180.0, 96.559759130419, 3);
INSERT INTO building(id, name, level) VALUES (2143, "building_rye_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2143, 20.0, 20.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2143, 30.0, 30.3, 2);
INSERT INTO building(id, name, level) VALUES (2144, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2144, 20.0, 4.898546385623278, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2144, 20.0, 4.898546385623278, 20);
INSERT INTO building(id, name, level) VALUES (2145, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2145, 25.0, 2.1620714196308652, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2145, 200.0, 17.296571357046922, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2145, 50.0, 4.3241428392617305, 5);
INSERT INTO building(id, name, level) VALUES (2146, "building_iron_minelevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2146, 49.99999999999999, 4.32414283926173, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2146, 199.99999999999997, 17.29657135704692, 10);
INSERT INTO building(id, name, level) VALUES (2147, "building_rye_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2147, 50.0, 52.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2147, 75.0, 78.0, 5);
INSERT INTO building(id, name, level) VALUES (2148, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2148, 20.0, 4.898546385623278, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2148, 20.0, 4.898546385623278, 20);
INSERT INTO building(id, name, level) VALUES (2149, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2149, 30.0, 30.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2149, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (2860, "building_iron_minelevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2860, 30.0, 2.594485703557038, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2860, 120.0, 10.377942814228152, 6);
INSERT INTO building(id, name, level) VALUES (2861, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2861, 10.0, 0.864828567852346, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2861, 80.0, 6.918628542818768, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2861, 20.0, 1.729657135704692, 2);
INSERT INTO building(id, name, level) VALUES (2862, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 2862, 10.0, 7.343577474294618, 5);
INSERT INTO building(id, name, level) VALUES (2863, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2863, 5.0, 7.18038686375474, 1);
INSERT INTO building(id, name, level) VALUES (2892, "building_subsistence_farmslevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2892, 3.885, 3.885, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2892, 0.6475, 0.6475, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2892, 0.6475, 0.6475, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2892, 0.6475, 0.6475, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2892, 0.6475, 0.6475, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2892, 0.6475, 0.6475, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2892, 0.9065, 0.9065, 2);
INSERT INTO building(id, name, level) VALUES (2901, "building_subsistence_farmslevel", 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2901, 71.03889, 71.03889, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2901, 11.83981, 11.83981, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2901, 11.83981, 11.83981, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2901, 11.83981, 11.83981, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2901, 11.83981, 11.83981, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2901, 11.83981, 11.83981, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2901, 16.57574, 16.57574, 39);
INSERT INTO building(id, name, level) VALUES (2905, "building_subsistence_farmslevel", 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2905, 58.01859, 58.01859, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2905, 9.66976, 9.66976, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2905, 9.66976, 9.66976, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2905, 9.66976, 9.66976, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2905, 9.66976, 9.66976, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2905, 9.66976, 9.66976, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2905, 13.53767, 13.53767, 37);
INSERT INTO building(id, name, level) VALUES (2906, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2906, 25.0, 13.343984983679535, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2906, 125.0, 66.71992491839768, 5);
INSERT INTO building(id, name, level) VALUES (2907, "building_subsistence_pastureslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2907, 0.6259, 0.6259, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2907, 0.93885, 0.93885, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2907, 0.31295, 0.31295, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2907, 0.6259, 0.6259, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2907, 0.6259, 0.6259, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2907, 0.6259, 0.6259, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2907, 2.07798, 2.07798, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2907, 0.87626, 0.87626, 20);
INSERT INTO building(id, name, level) VALUES (3633, "building_subsistence_farmslevel", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3633, 41.20416, 41.20416, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3633, 6.86736, 6.86736, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3633, 6.86736, 6.86736, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3633, 6.86736, 6.86736, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3633, 6.86736, 6.86736, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3633, 6.86736, 6.86736, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3633, 9.6143, 9.6143, 18);
INSERT INTO building(id, name, level) VALUES (3634, "building_subsistence_farmslevel", 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3634, 50.17716, 50.17716, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3634, 8.36286, 8.36286, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3634, 8.36286, 8.36286, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3634, 8.36286, 8.36286, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3634, 8.36286, 8.36286, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3634, 8.36286, 8.36286, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3634, 11.708, 11.708, 22);
INSERT INTO building(id, name, level) VALUES (3635, "building_subsistence_farmslevel", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3635, 105.594, 105.594, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3635, 17.599, 17.599, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3635, 17.599, 17.599, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3635, 17.599, 17.599, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3635, 17.599, 17.599, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3635, 17.599, 17.599, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3635, 24.6386, 24.6386, 50);
INSERT INTO building(id, name, level) VALUES (3651, "building_subsistence_farmslevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3651, 5.9844, 5.9844, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3651, 0.9974, 0.9974, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3651, 0.9974, 0.9974, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3651, 0.9974, 0.9974, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3651, 0.9974, 0.9974, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3651, 0.9974, 0.9974, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3651, 1.39636, 1.39636, 2);
INSERT INTO building(id, name, level) VALUES (3764, "building_subsistence_farmslevel", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3764, 86.78511, 86.78511, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3764, 14.46418, 14.46418, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3764, 14.46418, 14.46418, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3764, 14.46418, 14.46418, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3764, 14.46418, 14.46418, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3764, 14.46418, 14.46418, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3764, 20.24985, 20.24985, 29);
INSERT INTO building(id, name, level) VALUES (3765, "building_subsistence_farmslevel", 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3765, 141.0, 141.0, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3765, 23.5, 23.5, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3765, 23.5, 23.5, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3765, 23.5, 23.5, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3765, 23.5, 23.5, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3765, 23.5, 23.5, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3765, 32.9, 32.9, 47);
INSERT INTO building(id, name, level) VALUES (3766, "building_subsistence_farmslevel", 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3766, 166.98024, 166.98024, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3766, 27.83004, 27.83004, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3766, 27.83004, 27.83004, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3766, 27.83004, 27.83004, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3766, 27.83004, 27.83004, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3766, 27.83004, 27.83004, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3766, 38.96205, 38.96205, 56);
INSERT INTO building(id, name, level) VALUES (3767, "building_subsistence_pastureslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3767, 0.2904, 0.23232, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3767, 0.4356, 0.34848, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3767, 0.1452, 0.11616, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3767, 0.2904, 0.23232, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3767, 0.2904, 0.23232, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3767, 0.2904, 0.23232, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3767, 0.9641249999999999, 0.7713, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3767, 0.40654999999999997, 0.32524, 20);
INSERT INTO building(id, name, level) VALUES (3768, "building_subsistence_pastureslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3768, 1.0824, 0.86592, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3768, 1.6236, 1.29888, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3768, 0.5412, 0.43296, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3768, 1.0824, 0.86592, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3768, 1.0824, 0.86592, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3768, 1.0824, 0.86592, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3768, 3.5935624999999995, 2.87485, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3768, 1.51535, 1.21228, 20);
INSERT INTO building(id, name, level) VALUES (3769, "building_subsistence_farmslevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3769, 18.4404, 18.4404, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3769, 3.0734, 3.0734, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3769, 3.0734, 3.0734, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3769, 3.0734, 3.0734, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3769, 3.0734, 3.0734, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3769, 3.0734, 3.0734, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3769, 4.30276, 4.30276, 10);
INSERT INTO building(id, name, level) VALUES (3770, "building_subsistence_farmslevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3770, 14.41845, 14.41845, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3770, 2.40307, 2.40307, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3770, 2.40307, 2.40307, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3770, 2.40307, 2.40307, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3770, 2.40307, 2.40307, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3770, 2.40307, 2.40307, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3770, 3.3643, 3.3643, 15);
INSERT INTO building(id, name, level) VALUES (3771, "building_subsistence_farmslevel", 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3771, 345.0, 345.0, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3771, 57.5, 57.5, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3771, 57.5, 57.5, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3771, 57.5, 57.5, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3771, 57.5, 57.5, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3771, 57.5, 57.5, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3771, 80.5, 80.5, 115);
INSERT INTO building(id, name, level) VALUES (3772, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3772, 5.0, 2.668796996735907, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3772, 25.0, 13.343984983679535, 1);
INSERT INTO building(id, name, level) VALUES (3778, "building_subsistence_pastureslevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3778, 1.4945, 1.4945, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3778, 2.24175, 2.24175, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3778, 0.74725, 0.74725, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3778, 1.4945, 1.4945, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3778, 1.4945, 1.4945, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3778, 1.4945, 1.4945, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3778, 4.96174, 4.96174, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3778, 2.0923, 2.0923, 7);
INSERT INTO building(id, name, level) VALUES (3784, "building_subsistence_pastureslevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3784, 0.17329, 0.17329, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3784, 0.25994, 0.25994, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3784, 0.08664, 0.08664, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3784, 0.17329, 0.17329, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3784, 0.17329, 0.17329, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3784, 0.17329, 0.17329, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3784, 0.57533, 0.57533, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3784, 0.24261, 0.24261, 3);
INSERT INTO building(id, name, level) VALUES (3786, "building_subsistence_pastureslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3786, 0.792, 0.792, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3786, 1.188, 1.188, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3786, 0.396, 0.396, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3786, 0.792, 0.792, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3786, 0.792, 0.792, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3786, 0.792, 0.792, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3786, 2.62944, 2.62944, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3786, 1.1088, 1.1088, 6);
INSERT INTO building(id, name, level) VALUES (3789, "building_subsistence_pastureslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3789, 0.269, 0.269, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3789, 0.4035, 0.4035, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3789, 0.1345, 0.1345, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3789, 0.269, 0.269, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3789, 0.269, 0.269, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3789, 0.269, 0.269, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3789, 0.89308, 0.89308, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3789, 0.3766, 0.3766, 20);
INSERT INTO building(id, name, level) VALUES (3790, "building_subsistence_pastureslevel", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3790, 1.106, 1.106, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3790, 1.659, 1.659, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3790, 0.553, 0.553, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3790, 1.106, 1.106, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3790, 1.106, 1.106, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3790, 1.106, 1.106, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3790, 3.67192, 3.67192, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3790, 1.5484, 1.5484, 50);
INSERT INTO building(id, name, level) VALUES (3791, "building_subsistence_pastureslevel", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3791, 2.00295, 1.60236, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3791, 3.004425, 2.40354, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3791, 1.001475, 0.80118, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3791, 2.00295, 1.60236, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3791, 2.00295, 1.60236, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3791, 2.00295, 1.60236, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3791, 6.6497874999999995, 5.31983, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3791, 2.804125, 2.2433, 30);
INSERT INTO building(id, name, level) VALUES (3792, "building_subsistence_farmslevel", 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3792, 48.0744, 48.0744, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3792, 8.0124, 8.0124, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3792, 8.0124, 8.0124, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3792, 8.0124, 8.0124, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3792, 8.0124, 8.0124, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3792, 8.0124, 8.0124, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3792, 11.21736, 11.21736, 60);
INSERT INTO building(id, name, level) VALUES (3793, "building_subsistence_farmslevel", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3793, 91.3872, 91.3872, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3793, 15.2312, 15.2312, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3793, 15.2312, 15.2312, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3793, 15.2312, 15.2312, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3793, 15.2312, 15.2312, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3793, 15.2312, 15.2312, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3793, 21.32368, 21.32368, 40);
INSERT INTO building(id, name, level) VALUES (3794, "building_subsistence_farmslevel", 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3794, 147.0, 147.0, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3794, 24.5, 24.5, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3794, 24.5, 24.5, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3794, 24.5, 24.5, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3794, 24.5, 24.5, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3794, 24.5, 24.5, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3794, 34.3, 34.3, 49);
INSERT INTO building(id, name, level) VALUES (3797, "building_subsistence_farmslevel", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3797, 47.6052, 47.6052, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3797, 7.9342, 7.9342, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3797, 7.9342, 7.9342, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3797, 7.9342, 7.9342, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3797, 7.9342, 7.9342, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3797, 7.9342, 7.9342, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3797, 11.10788, 11.10788, 40);
INSERT INTO building(id, name, level) VALUES (3798, "building_subsistence_pastureslevel", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3798, 0.9918, 0.79344, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3798, 1.4877, 1.19016, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3798, 0.4959, 0.39672, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3798, 0.9918, 0.79344, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3798, 0.9918, 0.79344, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3798, 0.9918, 0.79344, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3798, 3.292775, 2.63422, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3798, 1.3885125, 1.11081, 40);
INSERT INTO building(id, name, level) VALUES (3799, "building_subsistence_pastureslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3799, 0.3398, 0.27184, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3799, 0.5096999999999999, 0.40776, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3799, 0.1699, 0.13592, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3799, 0.3398, 0.27184, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3799, 0.3398, 0.27184, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3799, 0.3398, 0.27184, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3799, 1.1281249999999998, 0.9025, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3799, 0.4757125, 0.38057, 20);
INSERT INTO building(id, name, level) VALUES (3800, "building_subsistence_farmslevel", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3800, 96.8736, 96.8736, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3800, 16.1456, 16.1456, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3800, 16.1456, 16.1456, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3800, 16.1456, 16.1456, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3800, 16.1456, 16.1456, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3800, 16.1456, 16.1456, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3800, 22.60384, 22.60384, 40);
INSERT INTO building(id, name, level) VALUES (3801, "building_subsistence_farmslevel", 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3801, 258.0, 258.0, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3801, 43.0, 43.0, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3801, 43.0, 43.0, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3801, 43.0, 43.0, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3801, 43.0, 43.0, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3801, 43.0, 43.0, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3801, 60.2, 60.2, 86);
INSERT INTO building(id, name, level) VALUES (3802, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3802, 15.0, 8.00639099020772, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3802, 75.0, 40.0319549510386, 3);
INSERT INTO building(id, name, level) VALUES (3804, "building_subsistence_farmslevel", 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3804, 413.9793, 413.9793, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3804, 68.99655, 68.99655, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3804, 68.99655, 68.99655, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3804, 68.99655, 68.99655, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3804, 68.99655, 68.99655, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3804, 68.99655, 68.99655, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3804, 96.59517, 96.59517, 138);
INSERT INTO building(id, name, level) VALUES (3805, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3805, 20.0, 10.675187986943628, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3805, 100.0, 53.37593993471814, 4);
INSERT INTO building(id, name, level) VALUES (3806, "building_subsistence_farmslevel", 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3806, 299.247, 299.247, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3806, 49.8745, 49.8745, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3806, 49.8745, 49.8745, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3806, 49.8745, 49.8745, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3806, 49.8745, 49.8745, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3806, 49.8745, 49.8745, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3806, 69.8243, 69.8243, 100);
INSERT INTO building(id, name, level) VALUES (3807, "building_subsistence_farmslevel", 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3807, 172.07652, 172.07652, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3807, 28.67942, 28.67942, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3807, 28.67942, 28.67942, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3807, 28.67942, 28.67942, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3807, 28.67942, 28.67942, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3807, 28.67942, 28.67942, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3807, 40.15118, 40.15118, 77);
INSERT INTO building(id, name, level) VALUES (3808, "building_subsistence_farmslevel", 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3808, 329.0364, 329.0364, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3808, 54.8394, 54.8394, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3808, 54.8394, 54.8394, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3808, 54.8394, 54.8394, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3808, 54.8394, 54.8394, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3808, 54.8394, 54.8394, 110);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3808, 76.77516, 76.77516, 110);
INSERT INTO building(id, name, level) VALUES (3809, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3809, 10.0, 5.337593993471814, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3809, 50.0, 26.68796996735907, 2);
INSERT INTO building(id, name, level) VALUES (3810, "building_subsistence_farmslevel", 120);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3810, 360.0, 360.0, 120);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3810, 60.0, 60.0, 120);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3810, 60.0, 60.0, 120);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3810, 60.0, 60.0, 120);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3810, 60.0, 60.0, 120);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3810, 60.0, 60.0, 120);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3810, 84.0, 84.0, 120);
INSERT INTO building(id, name, level) VALUES (3811, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3811, 30.0, 16.01278198041544, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3811, 150.0, 80.0639099020772, 6);
INSERT INTO building(id, name, level) VALUES (3812, "building_subsistence_farmslevel", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3812, 48.0, 48.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3812, 8.0, 8.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3812, 8.0, 8.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3812, 8.0, 8.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3812, 8.0, 8.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3812, 8.0, 8.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3812, 11.2, 11.2, 16);
INSERT INTO building(id, name, level) VALUES (3814, "building_subsistence_farmslevel", 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3814, 140.7588, 140.7588, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3814, 23.4598, 23.4598, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3814, 23.4598, 23.4598, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3814, 23.4598, 23.4598, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3814, 23.4598, 23.4598, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3814, 23.4598, 23.4598, 52);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3814, 32.84372, 32.84372, 52);
INSERT INTO building(id, name, level) VALUES (3816, "building_subsistence_farmslevel", 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3816, 223.74696, 223.74696, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3816, 37.29116, 37.29116, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3816, 37.29116, 37.29116, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3816, 37.29116, 37.29116, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3816, 37.29116, 37.29116, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3816, 37.29116, 37.29116, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3816, 52.20762, 52.20762, 79);
INSERT INTO building(id, name, level) VALUES (3817, "building_subsistence_farmslevel", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3817, 22.314, 22.314, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3817, 3.719, 3.719, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3817, 3.719, 3.719, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3817, 3.719, 3.719, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3817, 3.719, 3.719, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3817, 3.719, 3.719, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3817, 5.2066, 5.2066, 40);
INSERT INTO building(id, name, level) VALUES (3818, "building_subsistence_farmslevel", 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3818, 209.31960000000004, 240.71754, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3818, 34.8866, 40.11959, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3818, 34.8866, 40.11959, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3818, 34.8866, 40.11959, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3818, 34.8866, 40.11959, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3818, 34.8866, 40.11959, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3818, 48.8412347826087, 56.16742, 70);
INSERT INTO building(id, name, level) VALUES (3819, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3819, 10.0, 5.337593993471814, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3819, 50.0, 26.68796996735907, 2);
INSERT INTO building(id, name, level) VALUES (3820, "building_subsistence_farmslevel", 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3820, 501.0, 501.0, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3820, 83.5, 83.5, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3820, 83.5, 83.5, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3820, 83.5, 83.5, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3820, 83.5, 83.5, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3820, 83.5, 83.5, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3820, 116.9, 116.9, 167);
INSERT INTO building(id, name, level) VALUES (3821, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3821, 5.0, 2.668796996735907, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3821, 25.0, 13.343984983679535, 1);
INSERT INTO building(id, name, level) VALUES (3822, "building_subsistence_farmslevel", 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3822, 109.989, 109.989, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3822, 18.3315, 18.3315, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3822, 18.3315, 18.3315, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3822, 18.3315, 18.3315, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3822, 18.3315, 18.3315, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3822, 18.3315, 18.3315, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3822, 25.6641, 25.6641, 60);
INSERT INTO building(id, name, level) VALUES (3823, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3823, 5.0, 2.668796996735907, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3823, 25.0, 13.343984983679535, 1);
INSERT INTO building(id, name, level) VALUES (3824, "building_subsistence_farmslevel", 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3824, 110.7713391304348, 127.38704, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3824, 18.461886956521738, 21.23117, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3824, 18.461886956521738, 21.23117, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3824, 18.461886956521738, 21.23117, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3824, 18.461886956521738, 21.23117, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3824, 18.461886956521738, 21.23117, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3824, 25.846643478260873, 29.72364, 37);
INSERT INTO building(id, name, level) VALUES (3825, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3825, 10.0, 5.337593993471814, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3825, 50.0, 26.68796996735907, 2);
INSERT INTO building(id, name, level) VALUES (3826, "building_subsistence_farmslevel", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3826, 149.904, 149.904, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3826, 24.984, 24.984, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3826, 24.984, 24.984, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3826, 24.984, 24.984, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3826, 24.984, 24.984, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3826, 24.984, 24.984, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3826, 34.9776, 34.9776, 50);
INSERT INTO building(id, name, level) VALUES (3827, "building_subsistence_farmslevel", 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3827, 436.686, 436.686, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3827, 72.781, 72.781, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3827, 72.781, 72.781, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3827, 72.781, 72.781, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3827, 72.781, 72.781, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3827, 72.781, 72.781, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3827, 101.8934, 101.8934, 146);
INSERT INTO building(id, name, level) VALUES (3828, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3828, 10.0, 5.337593993471814, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3828, 50.0, 26.68796996735907, 2);
INSERT INTO building(id, name, level) VALUES (3829, "building_subsistence_farmslevel", 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3829, 184.4535, 184.4535, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3829, 30.74225, 30.74225, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3829, 30.74225, 30.74225, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3829, 30.74225, 30.74225, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3829, 30.74225, 30.74225, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3829, 30.74225, 30.74225, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3829, 43.03915, 43.03915, 77);
INSERT INTO building(id, name, level) VALUES (3830, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3830, 5.0, 2.668796996735907, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3830, 25.0, 13.343984983679535, 1);
INSERT INTO building(id, name, level) VALUES (3831, "building_subsistence_farmslevel", 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3831, 338.04063, 338.04063, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3831, 56.3401, 56.3401, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3831, 56.3401, 56.3401, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3831, 56.3401, 56.3401, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3831, 56.3401, 56.3401, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3831, 56.3401, 56.3401, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3831, 78.87614, 78.87614, 113);
INSERT INTO building(id, name, level) VALUES (3832, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3832, 5.0, 2.668796996735907, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3832, 25.0, 13.343984983679535, 1);
INSERT INTO building(id, name, level) VALUES (3927, "building_subsistence_farmslevel", 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3927, 242.57775, 242.57775, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3927, 40.42962, 40.42962, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3927, 40.42962, 40.42962, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3927, 40.42962, 40.42962, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3927, 40.42962, 40.42962, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3927, 40.42962, 40.42962, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3927, 56.60147, 56.60147, 95);
INSERT INTO building(id, name, level) VALUES (3928, "building_subsistence_farmslevel", 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3928, 156.0375, 156.0375, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3928, 26.00625, 26.00625, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3928, 26.00625, 26.00625, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3928, 26.00625, 26.00625, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3928, 26.00625, 26.00625, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3928, 26.00625, 26.00625, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3928, 36.40875, 36.40875, 57);
INSERT INTO building(id, name, level) VALUES (3929, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3929, 5.0, 2.668796996735907, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3929, 25.0, 13.343984983679535, 1);
INSERT INTO building(id, name, level) VALUES (3930, "building_subsistence_farmslevel", 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3930, 213.23379, 213.23379, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3930, 35.53896, 35.53896, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3930, 35.53896, 35.53896, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3930, 35.53896, 35.53896, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3930, 35.53896, 35.53896, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3930, 35.53896, 35.53896, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3930, 49.75455, 49.75455, 77);
INSERT INTO building(id, name, level) VALUES (3931, "building_subsistence_farmslevel", 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3931, 293.33262, 293.33262, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3931, 48.88877, 48.88877, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3931, 48.88877, 48.88877, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3931, 48.88877, 48.88877, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3931, 48.88877, 48.88877, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3931, 48.88877, 48.88877, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3931, 68.44427, 68.44427, 98);
INSERT INTO building(id, name, level) VALUES (3932, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3932, 5.0, 2.668796996735907, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3932, 25.0, 13.343984983679535, 1);
INSERT INTO building(id, name, level) VALUES (3933, "building_subsistence_farmslevel", 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3933, 214.7769, 214.7769, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3933, 35.79615, 35.79615, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3933, 35.79615, 35.79615, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3933, 35.79615, 35.79615, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3933, 35.79615, 35.79615, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3933, 35.79615, 35.79615, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3933, 50.11461, 50.11461, 87);
INSERT INTO building(id, name, level) VALUES (3934, "building_subsistence_farmslevel", 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3934, 450.00000000000006, 517.5, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3934, 75.0, 86.25, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3934, 75.0, 86.25, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3934, 75.0, 86.25, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3934, 75.0, 86.25, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3934, 75.0, 86.25, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3934, 105.00000000000001, 120.75, 150);
INSERT INTO building(id, name, level) VALUES (3935, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3935, 25.0, 13.343984983679535, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3935, 125.0, 66.71992491839768, 5);
INSERT INTO building(id, name, level) VALUES (3970, "building_trade_centerlevel", 31);
INSERT INTO building(id, name, level) VALUES (4024, "building_trade_centerlevel", 21);
INSERT INTO building(id, name, level) VALUES (4025, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4025, 5.0, 2.668796996735907, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4025, 25.0, 13.343984983679535, 1);
INSERT INTO building(id, name, level) VALUES (4026, "building_trade_centerlevel", 22);
INSERT INTO building(id, name, level) VALUES (4027, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4027, 5.0, 2.668796996735907, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4027, 25.0, 13.343984983679535, 1);
INSERT INTO building(id, name, level) VALUES (4029, "building_trade_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4055, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4058, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4472, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4473, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4474, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4549, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4550, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4551, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4552, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4553, "building_conscription_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4560, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4561, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4562, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4563, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4564, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4565, "building_conscription_centerlevel", 19);
INSERT INTO building(id, name, level) VALUES (4566, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4567, "building_conscription_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (4568, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4569, "building_conscription_centerlevel", 19);
INSERT INTO building(id, name, level) VALUES (4570, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4571, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4572, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4574, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4575, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4576, "building_conscription_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (4577, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4578, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4579, "building_conscription_centerlevel", 11);
INSERT INTO building(id, name, level) VALUES (4580, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4581, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4582, "building_conscription_centerlevel", 11);
INSERT INTO building(id, name, level) VALUES (4583, "building_conscription_centerlevel", 20);
INSERT INTO building(id, name, level) VALUES (4631, "building_conscription_centerlevel", 13);
INSERT INTO building(id, name, level) VALUES (4632, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4633, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4634, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4635, "building_conscription_centerlevel", 11);
INSERT INTO building(id, name, level) VALUES (4636, "building_conscription_centerlevel", 25);
