
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 14.30103554957357, 2854.945766054345);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 14.228969559384241, 135.2441993617192);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 11.402155930377692, 45.24826806128882);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 17.166976035136535, 270.17363864170164);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 26.26382676641565, 758.8813409602889);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 24.952157058860585, 705.8323718476561);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 32.44955885616782, 39.20755058738327);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 18.77783658506942, 517.7712719223558);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 44.96197929706403, 211.92565833333316);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 59.87728108562806, 9.904551955488495);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 33.03754266211604, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 15.439145749525164, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 49.38698112700099, 160.0929985235045);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 27.344167528027903, 28.906945880046504);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 99.65480090554853, 229.80758356687267);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 37.664412731451336, 168.30767505464183);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 36.33681884980158, 274.14477594626254);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 26.555706186729285, 694.4632428634162);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 38.78084757194246, 350.69163000000015);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 20.07763731336806, 158.7153605372523);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 35.39753767431215, 375.8726281727739);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 87.5, 0.24874624373121867);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 81.85730233082664, 350.00241180998523);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 70.00048236199697);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (2446, "building_barracks", 2);
INSERT INTO building(id, name, level) VALUES (2447, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2447, 30.0, 26.733921525109572, 3);
INSERT INTO building(id, name, level) VALUES (2448, "building_logging_camp", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2448, 150.0, 156.0, 5);
INSERT INTO building(id, name, level) VALUES (2449, "building_rice_farm", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2449, 9.683, 11.6196, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2449, 11.6196, 13.94352, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2449, 17.4294, 20.91528, 1);
INSERT INTO building(id, name, level) VALUES (2450, "building_tobacco_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2450, 75.0, 91.5, 3);
INSERT INTO building(id, name, level) VALUES (2451, "building_barracks", 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2451, 1.0, 1.6127236499690312, 16);
INSERT INTO building(id, name, level) VALUES (2452, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2452, 30.0, 26.733921525109572, 3);
INSERT INTO building(id, name, level) VALUES (2453, "building_glassworks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2453, 30.0, 36.98536092291647, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2453, 5.0, 27.57786, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2453, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2453, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (2454, "building_fishing_wharf", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2454, 74.90474509803921, 76.40284, 3);
INSERT INTO building(id, name, level) VALUES (2455, "building_whaling_station", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2455, 25.0, 25.0683636081572, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (28, 2455, 100.0, 100.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2455, 50.0, 50.0, 5);
INSERT INTO building(id, name, level) VALUES (2456, "building_rice_farm", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2456, 20.0, 26.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2456, 24.0, 31.44, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2456, 35.99999999999999, 47.16, 2);
INSERT INTO building(id, name, level) VALUES (2457, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2457, 50.0, 60.5, 2);
INSERT INTO building(id, name, level) VALUES (2458, "building_tea_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2458, 80.0, 98.4, 4);
INSERT INTO building(id, name, level) VALUES (2459, "building_port", 1);
INSERT INTO building(id, name, level) VALUES (2460, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (2461, "building_government_administration", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2461, 50.0, 44.55653587518262, 5);
INSERT INTO building(id, name, level) VALUES (2462, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2462, 25.0, 58.57401851470947, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2462, 75.0, 92.46340230729119, 1);
INSERT INTO building(id, name, level) VALUES (2463, "building_paper_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2463, 60.0, 73.97072184583294, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2463, 80.0, 80.0, 2);
INSERT INTO building(id, name, level) VALUES (2464, "building_furniture_manufacturies", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2464, 20.0, 46.85921481176758, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2464, 60.0, 73.97072184583294, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2464, 90.0, 90.0, 2);
INSERT INTO building(id, name, level) VALUES (2465, "building_logging_camp", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2465, 119.99999999999999, 123.6, 4);
INSERT INTO building(id, name, level) VALUES (2466, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2466, 10.0, 16.127236499690312, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2466, 20.0, 20.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2466, 20.0, 20.0, 2);
INSERT INTO building(id, name, level) VALUES (2467, "building_rice_farm", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2467, 20.0, 24.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2467, 24.0, 29.04, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2467, 36.0, 43.56, 2);
INSERT INTO building(id, name, level) VALUES (2468, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2468, 50.0, 60.5, 2);
INSERT INTO building(id, name, level) VALUES (2469, "building_tea_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2469, 80.0, 98.4, 4);
INSERT INTO building(id, name, level) VALUES (2470, "building_barracks", 15);
INSERT INTO building(id, name, level) VALUES (2471, "building_government_administration", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2471, 70.0, 62.37915022525567, 7);
INSERT INTO building(id, name, level) VALUES (2472, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2472, 25.0, 58.57401851470947, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2472, 75.0, 92.46340230729119, 1);
INSERT INTO building(id, name, level) VALUES (2473, "building_textile_mills", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2473, 100.0, 234.2960740588379, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2473, 59.99999999999999, 78.13333333333333, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2473, 59.99999999999999, 59.99999999999999, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2473, 119.99999999999999, 119.99999999999999, 4);
INSERT INTO building(id, name, level) VALUES (2474, "building_paper_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2474, 90.0, 110.95608276874943, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2474, 120.0, 120.0, 3);
INSERT INTO building(id, name, level) VALUES (2475, "building_fishing_wharf", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2475, 74.8545, 76.35159, 3);
INSERT INTO building(id, name, level) VALUES (2476, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2476, 9.928396694214877, 16.01176015503468, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2476, 19.856793388429754, 19.856793388429754, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2476, 19.856793388429754, 19.856793388429754, 2);
INSERT INTO building(id, name, level) VALUES (2477, "building_rice_farm", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2477, 29.78489393939394, 39.31606, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2477, 35.74187878787878, 47.17928, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2477, 53.61281818181818, 70.76892, 3);
INSERT INTO building(id, name, level) VALUES (2478, "building_silk_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2478, 60.0, 88.2, 3);
INSERT INTO building(id, name, level) VALUES (2479, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2479, 50.0, 60.5, 2);
INSERT INTO building(id, name, level) VALUES (2480, "building_tea_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2480, 100.0, 124.0, 5);
INSERT INTO building(id, name, level) VALUES (2481, "building_barracks", 22);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2481, 1.9998, 3.2251247552080686, 22);
INSERT INTO building(id, name, level) VALUES (2482, "building_government_administration", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2482, 40.0, 35.6452287001461, 4);
INSERT INTO building(id, name, level) VALUES (2483, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2483, 20.0, 46.85921481176758, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2483, 40.0, 49.31381456388863, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 2483, 35.0, 35.0, 1);
INSERT INTO building(id, name, level) VALUES (2484, "building_textile_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2484, 50.0, 117.14803702941894, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2484, 30.0, 39.06666666666667, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2484, 30.0, 30.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2484, 60.0, 60.0, 2);
INSERT INTO building(id, name, level) VALUES (2485, "building_glassworks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2485, 30.0, 36.98536092291647, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2485, 5.0, 27.57786, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2485, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2485, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (2486, "building_fishing_wharf", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2486, 43.025, 43.45525, 2);
INSERT INTO building(id, name, level) VALUES (2487, "building_rice_farm", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2487, 20.0, 20.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2487, 24.0, 24.24, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2487, 36.0, 36.36, 2);
INSERT INTO building(id, name, level) VALUES (2488, "building_dye_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2488, 45.93, 46.3893, 2);
INSERT INTO building(id, name, level) VALUES (2489, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2489, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (2490, "building_tea_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2490, 80.0, 82.4, 4);
INSERT INTO building(id, name, level) VALUES (2491, "building_barracks", 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2491, 1.0, 1.6127236499690312, 16);
INSERT INTO building(id, name, level) VALUES (2492, "building_port", 1);
INSERT INTO building(id, name, level) VALUES (2493, "building_fishing_wharf", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2493, 23.605, 23.605, 1);
INSERT INTO building(id, name, level) VALUES (2494, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2494, 1.72645, 2.784286745489034, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2494, 3.4529, 3.4529, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2494, 3.4529, 3.4529, 1);
INSERT INTO building(id, name, level) VALUES (2495, "building_port", 1);
INSERT INTO building(id, name, level) VALUES (2496, "building_government_administration", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2496, 40.0, 35.6452287001461, 4);
INSERT INTO building(id, name, level) VALUES (2497, "building_paper_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2497, 60.0, 73.97072184583294, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2497, 80.0, 80.0, 2);
INSERT INTO building(id, name, level) VALUES (2498, "building_glassworks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2498, 30.0, 36.98536092291647, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2498, 5.0, 27.57786, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2498, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2498, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (2499, "building_logging_camp", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2499, 180.0, 189.0, 6);
INSERT INTO building(id, name, level) VALUES (2500, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2500, 5.0, 8.063618249845156, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2500, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2500, 10.0, 10.0, 1);
INSERT INTO building(id, name, level) VALUES (2501, "building_rice_farm", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2501, 20.0, 24.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2501, 24.0, 29.04, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2501, 36.0, 43.56, 2);
INSERT INTO building(id, name, level) VALUES (2502, "building_silk_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2502, 20.0, 29.0, 1);
INSERT INTO building(id, name, level) VALUES (2503, "building_tobacco_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2503, 74.16974590163935, 90.48709, 3);
INSERT INTO building(id, name, level) VALUES (2504, "building_tea_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2504, 20.0, 24.0, 1);
INSERT INTO building(id, name, level) VALUES (2505, "building_barracks", 10);
INSERT INTO building(id, name, level) VALUES (2506, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2506, 10.0, 8.911307175036525, 1);
INSERT INTO building(id, name, level) VALUES (2507, "building_glassworks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2507, 60.0, 73.97072184583294, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2507, 10.0, 55.15572, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2507, 20.0, 20.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2507, 40.0, 40.0, 2);
INSERT INTO building(id, name, level) VALUES (2508, "building_rice_farm", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2508, 10.0, 12.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2508, 12.0, 14.4, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2508, 18.000000000000004, 21.6, 1);
INSERT INTO building(id, name, level) VALUES (2509, "building_dye_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2509, 75.0, 91.5, 3);
INSERT INTO building(id, name, level) VALUES (2510, "building_tobacco_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2510, 25.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (2511, "building_tea_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2511, 60.00000000000001, 73.2, 3);
INSERT INTO building(id, name, level) VALUES (2512, "building_barracks", 5);
INSERT INTO building(id, name, level) VALUES (3501, "building_subsistence_rice_paddies", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3501, 8.9353, 8.9353, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3501, 1.21845, 1.21845, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3501, 1.21845, 1.21845, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3501, 1.6246, 1.6246, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3501, 1.6246, 1.6246, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3501, 1.6246, 1.6246, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3501, 1.6246, 1.6246, 20);
INSERT INTO building(id, name, level) VALUES (3502, "building_subsistence_rice_paddies", 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3502, 417.05531666666667, 500.46638, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3502, 56.87117500000001, 68.24541, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3502, 56.87117500000001, 68.24541, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3502, 75.82823333333334, 90.99388, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3502, 75.82823333333334, 90.99388, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3502, 75.82823333333334, 90.99388, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3502, 75.82823333333334, 90.99388, 76);
INSERT INTO building(id, name, level) VALUES (3503, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3503, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3503, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (3504, "building_subsistence_rice_paddies", 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3504, 656.3722, 853.28386, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3504, 89.5053, 116.35689, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3504, 89.5053, 116.35689, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3504, 119.34039999999999, 155.14252, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3504, 119.34039999999999, 155.14252, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3504, 119.34039999999999, 155.14252, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3504, 119.34039999999999, 155.14252, 122);
INSERT INTO building(id, name, level) VALUES (3505, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3505, 30.0, 30.3, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3505, 10.0, 10.1, 2);
INSERT INTO building(id, name, level) VALUES (3506, "building_subsistence_rice_paddies", 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3506, 745.8682, 895.04184, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3506, 101.7093, 122.05116, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3506, 101.7093, 122.05116, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3506, 135.6124, 162.73488, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3506, 135.6124, 162.73488, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3506, 135.6124, 162.73488, 140);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3506, 135.6124, 162.73488, 140);
INSERT INTO building(id, name, level) VALUES (3507, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3507, 30.0, 30.3, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3507, 10.0, 10.1, 2);
INSERT INTO building(id, name, level) VALUES (3508, "building_subsistence_rice_paddies", 175);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3508, 960.09375, 1152.1125, 175);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3508, 130.921875, 157.10625, 175);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3508, 130.921875, 157.10625, 175);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3508, 174.5625, 209.475, 175);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3508, 174.5625, 209.475, 175);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3508, 174.5625, 209.475, 175);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3508, 174.5625, 209.475, 175);
INSERT INTO building(id, name, level) VALUES (3509, "building_urban_center", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3509, 59.99999999999999, 61.8, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3509, 20.0, 20.6, 4);
INSERT INTO building(id, name, level) VALUES (3510, "building_subsistence_rice_paddies", 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3510, 494.15355, 494.15355, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3510, 67.38457, 67.38457, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3510, 67.38457, 67.38457, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3510, 89.8461, 89.8461, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3510, 89.8461, 89.8461, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3510, 89.8461, 89.8461, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3510, 89.8461, 89.8461, 90);
INSERT INTO building(id, name, level) VALUES (3511, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3511, 30.0, 30.3, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3511, 10.0, 10.1, 2);
INSERT INTO building(id, name, level) VALUES (3512, "building_subsistence_orchards", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3512, 5.762675, 2.30507, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3512, 2.881325, 1.15253, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3512, 8.643999999999998, 3.4576, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3512, 5.762675, 2.30507, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3512, 5.762675, 2.30507, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3512, 5.762675, 2.30507, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3512, 19.132074999999997, 7.65283, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3512, 5.762675, 2.30507, 14);
INSERT INTO building(id, name, level) VALUES (3513, "building_subsistence_rice_paddies", 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3513, 317.296925, 380.75631, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3513, 43.26775833333333, 51.92131, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3513, 43.26775833333333, 51.92131, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3513, 57.69035, 69.22842, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3513, 57.69035, 69.22842, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3513, 57.69035, 69.22842, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3513, 57.69035, 69.22842, 67);
INSERT INTO building(id, name, level) VALUES (3514, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3514, 30.0, 30.3, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3514, 10.0, 10.1, 2);
INSERT INTO building(id, name, level) VALUES (3515, "building_subsistence_rice_paddies", 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3515, 173.40223333333336, 208.08268, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3515, 23.645758333333333, 28.37491, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3515, 23.645758333333333, 28.37491, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3515, 31.527675000000002, 37.83321, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3515, 31.527675000000002, 37.83321, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3515, 31.527675000000002, 37.83321, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3515, 31.527675000000002, 37.83321, 32);
INSERT INTO building(id, name, level) VALUES (3516, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3516, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3516, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (4240, "building_whaling_station", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (28, 4240, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 4240, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (4633, "building_conscription_center", 19);
INSERT INTO building(id, name, level) VALUES (4702, "building_conscription_center", 25);
INSERT INTO building(id, name, level) VALUES (4753, "building_conscription_center", 11);
INSERT INTO building(id, name, level) VALUES (4916, "building_university", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 4916, 5.0, 4.455653587518262, 1);
INSERT INTO building(id, name, level) VALUES (5025, "building_conscription_center", 25);
INSERT INTO building(id, name, level) VALUES (5044, "building_conscription_center", 25);
INSERT INTO building(id, name, level) VALUES (5051, "building_conscription_center", 22);
INSERT INTO building(id, name, level) VALUES (5135, "building_subsistence_pastures", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 5135, 0.80288, 0.80288, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 5135, 1.20432, 1.20432, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 5135, 0.40144, 0.40144, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 5135, 0.80288, 0.80288, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 5135, 0.80288, 0.80288, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5135, 0.80288, 0.80288, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 5135, 2.66556, 2.66556, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 5135, 0.80288, 0.80288, 8);
