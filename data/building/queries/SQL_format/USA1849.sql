
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 71.31236187483519, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 58.38999952810011, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 73.02643199888723, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 103.17148497538432, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 28.796480504843274, 5594.714011195205);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 26.281000085546413, 824.1079832039401);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 11.74282881580094, 668.8923942431982);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 29.636531761460738, 355.8310516623011);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 46.49114529092272, 516.0390471857761);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 45.41207153843713, 2404.593957480887);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 45.4749659232763, 1970.7282053357114);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 47.55546998443675, 102.21534680395091);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 26.248527584372766, 963.8573351388891);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 75.11421997939253, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 80.44647983203859, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 45.372377151734135, 112.10516286465501);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 56.89631759160097, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 59.137660763418374, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 63.22366071958248, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 18.241932741405748, 3.69706239875908);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 76.93039793991966, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 65.42043416090723, 201.35841038163878);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 45.357434297662, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 59.84288976643004, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 83.11455895661841, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 114.10104894971039, 393.04289144382483);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 43.02235815535538, 366.82622251067414);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 45.603843467878, 249.45779223952286);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 34.06777899820935, 903.0562054981743);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 83.8745814636903, 427.84854725797095);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 87.5, 408.9569162420293);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 40.14339443782505, 189.6366919372227);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 39.33767879593868, 1217.587356501364);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 96.38491403414068);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 98.05627568013706, 770.4815538647315);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 350.0, 0.11345572916666669);
INSERT INTO building(id, name, level) VALUES (16777670, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16777670, 5.0, 1.872360897641129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16777670, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 16777670, 5.0, 0.8230839258419729, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16777670, 70.0, 20.57147590233502, 1);
INSERT INTO building(id, name, level) VALUES (33554892, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 33554892, 15.0, 5.138185752480351, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33554892, 15.0, 5.442094499461776, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 33554892, 60.0, 21.160560503884255, 1);
INSERT INTO building(id, name, level) VALUES (1386, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1386, 60.0, 14.019882087044646, 6);
INSERT INTO building(id, name, level) VALUES (1387, "building_maize_farmlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1387, 4.935999999999999, 1.6935390726778776, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1387, 49.35999999999999, 16.935390726778778, 1);
INSERT INTO building(id, name, level) VALUES (1388, "building_white_houselevel", 1);
INSERT INTO building(id, name, level) VALUES (1389, "building_capitol_hilllevel", 1);
INSERT INTO building(id, name, level) VALUES (1390, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1390, 10.0, 6.729857076921882, 2);
INSERT INTO building(id, name, level) VALUES (1391, "building_sugar_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1391, 79.58789215686274, 81.17965, 3);
INSERT INTO building(id, name, level) VALUES (1392, "building_cotton_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1392, 70.75279365079365, 89.14852, 2);
INSERT INTO building(id, name, level) VALUES (1393, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1393, 60.0, 14.019882087044646, 6);
INSERT INTO building(id, name, level) VALUES (1394, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1394, 35.0508, 78.28678536964232, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1394, 70.1016, 26.251098940415876, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1394, 87.627, 41.38910535039913, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1394, 17.5254, 6.358325529391161, 2);
INSERT INTO building(id, name, level) VALUES (1395, "building_arms_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1395, 75.42, 35.623338988292446, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1395, 37.71, 9.209055871626378, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 1395, 56.565000000000005, 20.26554402432945, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 1395, 56.565000000000005, 20.26554402432945, 4);
INSERT INTO building(id, name, level) VALUES (1396, "building_munition_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1396, 17.7646, 6.955582912446616, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 1396, 17.7646, 2.258156201288485, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 1396, 44.4115, 11.517173892168874, 1);
INSERT INTO building(id, name, level) VALUES (1397, "building_universitylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1397, 25.0, 5.841617536268603, 5);
INSERT INTO building(id, name, level) VALUES (1398, "building_iron_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1398, 75.0, 25.690928762401754, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1398, 75.0, 27.210472497308878, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1398, 300.0, 105.80280251942126, 5);
INSERT INTO building(id, name, level) VALUES (1399, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1399, 59.99999999999999, 22.468330771693545, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1399, 19.999999999999996, 4.067997075369039, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1399, 140.0, 40.45104233076744, 2);
INSERT INTO building(id, name, level) VALUES (1400, "building_textile_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1400, 300.0, 670.0570489373337, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1400, 50.00000000000001, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1400, 25.000000000000004, 9.070157499102962, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1400, 500.00000000000006, 227.13438332735308, 5);
INSERT INTO building(id, name, level) VALUES (1401, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1401, 19.999999999999996, 44.670469929155566, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1401, 79.99999999999999, 29.95777436225806, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1401, 19.999999999999996, 4.884145251459229, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1401, 19.999999999999996, 7.256125999282366, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1401, 180.0, 89.16685839293875, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1401, 39.99999999999999, 19.814857420653052, 2);
INSERT INTO building(id, name, level) VALUES (1402, "building_tooling_workshopslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1402, 150.0, 56.17082692923387, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1402, 50.00000000000001, 17.12728584160117, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 1402, 100.00000000000001, 30.617746475622155, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1402, 400.00000000000006, 136.42604814886278, 5);
INSERT INTO building(id, name, level) VALUES (1403, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1403, 9.999999999999998, 3.628062999641183, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1403, 79.99999999999999, 29.024503997129468, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1403, 24.999999999999996, 9.070157499102958, 2);
INSERT INTO building(id, name, level) VALUES (1404, "building_wheat_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1404, 20.000000000000004, 6.861989759634839, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1404, 4.0, 1.4512251998564736, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1404, 140.0, 49.413405156210224, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1404, 32.0, 11.294492607133765, 4);
INSERT INTO building(id, name, level) VALUES (1405, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1405, 9.998, 6.728511105506498, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1405, 99.97999999999999, 67.28511105506497, 2);
INSERT INTO building(id, name, level) VALUES (1406, "building_naval_baselevel", 19);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1406, 38.0, 14.176745465093285, 19);
INSERT INTO building(id, name, level) VALUES (1407, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1407, 5.0, 2.354071761794985, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1407, 5.0, 5.636083924469001, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1407, 5.0, 5.121101742913077, 5);
INSERT INTO building(id, name, level) VALUES (1408, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1408, 15.0, 10.094785615382824, 3);
INSERT INTO building(id, name, level) VALUES (1409, "building_central_parklevel", 1);
INSERT INTO building(id, name, level) VALUES (1410, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1410, 20.0, 4.673294029014882, 2);
INSERT INTO building(id, name, level) VALUES (1411, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1411, 17.5254, 39.14339268482116, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1411, 35.0508, 13.125549470207938, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1411, 43.8135, 20.694552675199564, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1411, 8.7627, 3.1791627646955805, 1);
INSERT INTO building(id, name, level) VALUES (1412, "building_arms_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1412, 21.883794871794873, 10.336433877860044, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1412, 21.883794871794873, 5.344181640349238, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 1412, 21.883794871794873, 6.700324833091355, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1412, 10.941897435897436, 3.969789323304823, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 1412, 54.709495726495724, 18.9503273342149, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 1412, 32.82569230769231, 11.370194624216355, 3);
INSERT INTO building(id, name, level) VALUES (1413, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1413, 30.0, 10.276371504960702, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1413, 30.0, 10.884188998923552, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1413, 120.0, 42.32112100776851, 2);
INSERT INTO building(id, name, level) VALUES (1414, "building_maize_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1414, 24.681, 8.468038462877372, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1414, 246.81000000000003, 84.68038462877372, 5);
INSERT INTO building(id, name, level) VALUES (1415, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1415, 16.884198198198195, 7.223680492991476, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1415, 16.884198198198195, 6.125693476149121, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1415, 67.53679279279278, 26.698747938281194, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1415, 8.442099099099098, 3.3373434922851493, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1415, 42.21049549549549, 16.686717461425747, 2);
INSERT INTO building(id, name, level) VALUES (1416, "building_tobacco_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1416, 88.47, 91.1241, 4);
INSERT INTO building(id, name, level) VALUES (1417, "building_cotton_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1417, 353.88, 474.1992, 10);
INSERT INTO building(id, name, level) VALUES (1418, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1418, 1.0, 0.47081435235899705, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1418, 1.0, 1.1272167848938002, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1418, 1.0, 1.0242203485826153, 1);
INSERT INTO building(id, name, level) VALUES (1419, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1419, 10.0, 6.729857076921882, 2);
INSERT INTO building(id, name, level) VALUES (1423, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1423, 35.3724, 44.2155, 1);
INSERT INTO building(id, name, level) VALUES (1424, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1424, 9.2549, 3.9595863427924707, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1424, 9.2549, 3.357736025537919, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1424, 37.0196, 14.63464473666078, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1424, 4.6274454545454535, 1.8293287951665735, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1424, 23.137245454545454, 9.146651163496964, 1);
INSERT INTO building(id, name, level) VALUES (1425, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1425, 10.0, 2.336647014507441, 1);
INSERT INTO building(id, name, level) VALUES (1426, "building_lead_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1426, 30.0, 10.276371504960702, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1426, 30.0, 10.884188998923552, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 1426, 120.0, 42.32112100776851, 2);
INSERT INTO building(id, name, level) VALUES (1427, "building_maize_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1427, 9.876, 3.3884505433076826, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1427, 98.75999999999999, 33.884505433076825, 2);
INSERT INTO building(id, name, level) VALUES (1428, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1428, 8.433300000000001, 3.6080756685292923, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1428, 8.433300000000001, 3.0596543694874003, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1428, 33.733200000000004, 13.335460076033387, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1428, 4.216645454545454, 1.6669307125881492, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1428, 21.08324545454545, 8.33466075060484, 1);
INSERT INTO building(id, name, level) VALUES (1429, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1429, 20.0, 4.673294029014882, 2);
INSERT INTO building(id, name, level) VALUES (1430, "building_chemical_plantslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1430, 59.99999999999999, 12.20399122610712, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1430, 19.999999999999996, 9.44665579111441, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1430, 180.0, 60.81593789917554, 2);
INSERT INTO building(id, name, level) VALUES (1431, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1431, 15.0, 5.442094499461776, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1431, 179.99999999999997, 65.3051339935413, 3);
INSERT INTO building(id, name, level) VALUES (1432, "building_maize_farmlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1432, 39.438, 13.531157607023937, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1432, 394.38, 135.31157607023937, 8);
INSERT INTO building(id, name, level) VALUES (1433, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1433, 5.0, 1.814031499820592, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1433, 59.99999999999999, 21.7683779978471, 1);
INSERT INTO building(id, name, level) VALUES (1434, "building_sulfur_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1434, 20.0, 6.850914336640467, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1434, 20.0, 7.256125999282368, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 1434, 80.0, 28.21408067184567, 2);
INSERT INTO building(id, name, level) VALUES (1435, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1435, 8.450899999999999, 3.6156055953392134, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1435, 33.803599999999996, 14.462422381356854, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1435, 4.225445454545454, 1.8078008529571044, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1435, 4.225445454545454, 1.8078008529571044, 1);
INSERT INTO building(id, name, level) VALUES (1436, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1436, 10.0, 2.336647014507441, 1);
INSERT INTO building(id, name, level) VALUES (1437, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1437, 17.5254, 39.14339268482116, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1437, 35.0508, 13.125549470207938, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1437, 43.8135, 20.694552675199564, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1437, 8.7627, 3.1791627646955805, 1);
INSERT INTO building(id, name, level) VALUES (1438, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1438, 60.0, 22.468330771693548, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1438, 44.99999999999999, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1438, 30.0, 11.746253074845391, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1438, 60.0, 15.320278973794776, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1438, 74.99999999999999, 19.150348717243467, 3);
INSERT INTO building(id, name, level) VALUES (1439, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1439, 19.999999999999996, 44.670469929155566, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1439, 59.99999999999999, 22.468330771693545, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1439, 39.99999999999999, 9.768290502918457, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1439, 39.99999999999999, 14.512251998564732, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1439, 180.0, 89.16685839293875, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1439, 79.99999999999999, 39.629714841306104, 2);
INSERT INTO building(id, name, level) VALUES (1440, "building_chemical_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1440, 29.999999999999996, 6.10199561305356, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1440, 14.999999999999998, 5.13818575248035, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1440, 10.0, 4.723327895557206, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1440, 75.0, 25.456959003904903, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 1440, 20.0, 6.788522401041307, 1);
INSERT INTO building(id, name, level) VALUES (1441, "building_coal_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1441, 45.0, 16.32628349838533, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1441, 180.0, 65.30513399354132, 3);
INSERT INTO building(id, name, level) VALUES (1442, "building_maize_farmlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1442, 29.2116, 10.022485003127452, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1442, 222.00815652173915, 76.17088483037911, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1442, 35.05391304347826, 12.026979616973895, 6);
INSERT INTO building(id, name, level) VALUES (1443, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1443, 16.860594594594595, 7.213582003924118, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1443, 16.860594594594595, 6.11712994005988, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1443, 67.44239639639639, 26.661431010878356, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1443, 8.430297297297297, 3.3326779859959994, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1443, 42.15149549549549, 16.663393491435176, 2);
INSERT INTO building(id, name, level) VALUES (1444, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1444, 3.0, 1.412443057076991, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1444, 3.0, 3.3816503546814003, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1444, 3.0, 3.0726610457478465, 3);
INSERT INTO building(id, name, level) VALUES (1445, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1445, 10.0, 2.336647014507441, 1);
INSERT INTO building(id, name, level) VALUES (1446, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1446, 5.0, 1.1683235072537206, 1);
INSERT INTO building(id, name, level) VALUES (1447, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1447, 160.00000000000003, 68.45388009020037, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1447, 160.00000000000003, 88.86298464294981, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1447, 140.0, 68.8261283207532, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1447, 240.0, 117.98764854986261, 4);
INSERT INTO building(id, name, level) VALUES (1448, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1448, 29.999999999999996, 10.88418899892355, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1448, 119.99999999999999, 43.5367559956942, 2);
INSERT INTO building(id, name, level) VALUES (1449, "building_tobacco_plantationlevel", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1449, 198.96974999999998, 214.88733, 9);
INSERT INTO building(id, name, level) VALUES (1450, "building_maize_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1450, 24.347500000000004, 8.353614783635463, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1450, 185.04100000000003, 63.48747235562951, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1450, 29.217000000000006, 10.024337740362554, 5);
INSERT INTO building(id, name, level) VALUES (1451, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1451, 8.252899999999999, 3.5308939187275903, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1451, 8.252899999999999, 2.994204112973872, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1451, 33.011599999999994, 13.050196063402923, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1451, 4.126445454545454, 1.6312727110093417, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1451, 20.632245454545455, 8.156370742710804, 1);
INSERT INTO building(id, name, level) VALUES (1452, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1452, 5.0, 2.354071761794985, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1452, 5.0, 5.636083924469001, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1452, 5.0, 5.121101742913077, 5);
INSERT INTO building(id, name, level) VALUES (1453, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1453, 10.0, 2.336647014507441, 1);
INSERT INTO building(id, name, level) VALUES (1454, "building_food_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1454, 108.4355982142857, 46.39273398543661, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 1454, 27.108892857142855, 4.462578791617312, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1454, 108.4355982142857, 60.22444311790716, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1454, 67.77225, 25.93072890185798, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1454, 271.089, 103.72291560743191, 3);
INSERT INTO building(id, name, level) VALUES (1455, "building_maize_farmlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1455, 49.299, 16.914461658011895, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1455, 295.79400000000004, 101.48676994807138, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1455, 88.73819327731093, 30.446028677870235, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1455, 59.15879831932774, 20.29735341297648, 10);
INSERT INTO building(id, name, level) VALUES (1456, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1456, 16.860594594594595, 7.213582003924118, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1456, 16.860594594594595, 6.11712994005988, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1456, 67.44239639639639, 26.661431010878356, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1456, 8.430297297297297, 3.3326779859959994, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1456, 42.15149549549549, 16.663393491435176, 2);
INSERT INTO building(id, name, level) VALUES (1457, "building_tobacco_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1457, 221.08249541284403, 240.97992, 10);
INSERT INTO building(id, name, level) VALUES (1458, "building_cotton_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1458, 177.07399999999998, 228.42546, 5);
INSERT INTO building(id, name, level) VALUES (1459, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1459, 3.0, 1.412443057076991, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1459, 3.0, 3.3816503546814003, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1459, 3.0, 3.0726610457478465, 3);
INSERT INTO building(id, name, level) VALUES (1460, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1460, 20.0, 4.673294029014882, 2);
INSERT INTO building(id, name, level) VALUES (1461, "building_cotton_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1461, 140.156, 180.80124, 5);
INSERT INTO building(id, name, level) VALUES (1462, "building_maize_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1462, 20.000000000000004, 6.861989759634839, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1462, 4.0, 1.4512251998564736, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1462, 200.00000000000003, 70.59057879458605, 4);
INSERT INTO building(id, name, level) VALUES (1463, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1463, 66.32324509803921, 67.64971, 3);
INSERT INTO building(id, name, level) VALUES (1464, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1464, 10.0, 2.336647014507441, 1);
INSERT INTO building(id, name, level) VALUES (1465, "building_maize_farmlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1465, 29.5788, 10.148471135114347, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1465, 177.4728, 60.890826810686086, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1465, 53.2418347826087, 18.26724625312154, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1465, 35.494556521739135, 12.178164168747694, 6);
INSERT INTO building(id, name, level) VALUES (1466, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1466, 53.05859405940594, 53.58918, 2);
INSERT INTO building(id, name, level) VALUES (1467, "building_cotton_plantationlevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1467, 424.468794117647, 577.27756, 12);
INSERT INTO building(id, name, level) VALUES (1468, "building_tobacco_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1468, 88.431, 91.08393, 4);
INSERT INTO building(id, name, level) VALUES (1469, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1469, 3.0, 1.412443057076991, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1469, 3.0, 3.3816503546814003, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1469, 3.0, 3.0726610457478465, 3);
INSERT INTO building(id, name, level) VALUES (1470, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1470, 5.0, 3.364928538460941, 1);
INSERT INTO building(id, name, level) VALUES (1471, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1471, 10.0, 2.336647014507441, 1);
INSERT INTO building(id, name, level) VALUES (1472, "building_maize_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1472, 24.649, 8.457059279261957, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1472, 246.49000000000004, 84.57059279261958, 5);
INSERT INTO building(id, name, level) VALUES (1473, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1473, 8.4294, 3.6064071052020927, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1473, 8.4294, 3.0582394249175393, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1473, 33.7176, 13.329293060239262, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1473, 4.2147, 1.6661616325299078, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1473, 21.0735, 8.33080816264954, 1);
INSERT INTO building(id, name, level) VALUES (1474, "building_sugar_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1474, 132.65249999999997, 137.9586, 5);
INSERT INTO building(id, name, level) VALUES (1475, "building_cotton_plantationlevel", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1475, 318.3515939849624, 423.40762, 9);
INSERT INTO building(id, name, level) VALUES (1476, "building_tobacco_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1476, 88.43599999999999, 91.08908, 4);
INSERT INTO building(id, name, level) VALUES (1477, "building_barrackslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1477, 4.0, 1.8832574094359882, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1477, 4.0, 4.508867139575201, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1477, 4.0, 4.096881394330461, 4);
INSERT INTO building(id, name, level) VALUES (1478, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1478, 5.0, 3.364928538460941, 1);
INSERT INTO building(id, name, level) VALUES (1479, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1479, 14.999999999999998, 5.442094499461775, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1479, 59.99999999999999, 21.7683779978471, 1);
INSERT INTO building(id, name, level) VALUES (1480, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1480, 5.0, 1.814031499820592, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1480, 40.0, 14.512251998564736, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1480, 12.499999999999998, 4.535078749551479, 1);
INSERT INTO building(id, name, level) VALUES (1481, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1481, 10.0, 2.336647014507441, 1);
INSERT INTO building(id, name, level) VALUES (1482, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1482, 14.999999999999998, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1482, 35.0, 13.703961920652958, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1482, 49.99999999999999, 9.788544229037825, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1482, 24.999999999999996, 4.8942721145189125, 1);
INSERT INTO building(id, name, level) VALUES (1483, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1483, 15.0, 10.094785615382824, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1483, 150.0, 100.94785615382824, 3);
INSERT INTO building(id, name, level) VALUES (1484, "building_tobacco_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1484, 110.55375, 114.9759, 5);
INSERT INTO building(id, name, level) VALUES (1485, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1485, 10.0, 3.7307224908140224, 5);
INSERT INTO building(id, name, level) VALUES (1486, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1486, 10.0, 6.729857076921882, 2);
INSERT INTO building(id, name, level) VALUES (1487, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1487, 5.0, 3.364928538460941, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1487, 50.0, 33.64928538460941, 1);
INSERT INTO building(id, name, level) VALUES (1488, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1488, 8.4294, 3.6064071052020927, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1488, 8.4294, 3.0582394249175393, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1488, 33.7176, 13.329293060239262, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1488, 4.2147, 1.6661616325299078, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1488, 21.0735, 8.33080816264954, 1);
INSERT INTO building(id, name, level) VALUES (1489, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1489, 30.0, 7.009941043522323, 3);
INSERT INTO building(id, name, level) VALUES (1490, "building_steel_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1490, 89.99999999999999, 30.829114514882097, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1490, 120.0, 56.67993474668646, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 1490, 195.0, 79.45065437280502, 3);
INSERT INTO building(id, name, level) VALUES (1491, "building_textile_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1491, 119.99999999999999, 268.02281957493346, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1491, 19.999999999999996, 0.0, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1491, 9.999999999999998, 3.628062999641183, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1491, 199.99999999999997, 90.8537533309412, 2);
INSERT INTO building(id, name, level) VALUES (1492, "building_furniture_manufacturieslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1492, 40.00000000000001, 89.34093985831117, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1492, 80.00000000000001, 29.95777436225807, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1492, 40.00000000000001, 9.768290502918461, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1492, 20.000000000000004, 7.256125999282369, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1492, 180.00000000000003, 89.16685839293876, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1492, 80.00000000000001, 39.62971484130612, 4);
INSERT INTO building(id, name, level) VALUES (1493, "building_glassworkslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1493, 39.99999999999999, 14.97888718112903, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1493, 29.999999999999996, 0.0, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1493, 19.999999999999996, 7.83083538323026, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1493, 39.99999999999999, 10.213519315863183, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1493, 49.99999999999999, 12.766899144828978, 2);
INSERT INTO building(id, name, level) VALUES (1494, "building_coal_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1494, 75.0, 27.210472497308878, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1494, 300.0, 108.84188998923551, 5);
INSERT INTO building(id, name, level) VALUES (1495, "building_maize_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1495, 19.473592920353983, 6.681379760138325, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1495, 147.9993539823009, 50.778502572956015, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1495, 23.368318584070796, 8.017658141188914, 4);
INSERT INTO building(id, name, level) VALUES (1496, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1496, 25.292098214285712, 10.820889114939279, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1496, 25.292098214285712, 9.176132571454087, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1496, 101.16839285714285, 39.99404337278673, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1496, 12.646044642857142, 4.999253656770104, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1496, 63.23025, 24.996278872819946, 3);
INSERT INTO building(id, name, level) VALUES (1497, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1497, 25.000000000000004, 9.070157499102962, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1497, 200.00000000000003, 72.5612599928237, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1497, 62.50000000000001, 22.6753937477574, 5);
INSERT INTO building(id, name, level) VALUES (1498, "building_barrackslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1498, 2.0, 0.9416287047179941, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1498, 2.0, 2.2544335697876003, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1498, 2.0, 2.0484406971652307, 2);
INSERT INTO building(id, name, level) VALUES (1499, "building_paper_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1499, 89.99999999999999, 33.70249615754032, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1499, 30.0, 6.10199561305356, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1499, 209.99999999999997, 60.67656349615116, 3);
INSERT INTO building(id, name, level) VALUES (1500, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1500, 20.0, 4.673294029014882, 1);
INSERT INTO building(id, name, level) VALUES (1501, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1501, 5.0, 3.364928538460941, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1501, 50.0, 33.64928538460941, 1);
INSERT INTO building(id, name, level) VALUES (1502, "building_maize_farmlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1502, 4.928999999999999, 1.6911373762620054, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1502, 29.573999999999995, 10.146824257572034, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1502, 8.8722, 3.04404727727161, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1502, 5.9148, 2.0293648515144067, 1);
INSERT INTO building(id, name, level) VALUES (1503, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1503, 9.999999999999998, 3.628062999641183, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1503, 119.99999999999999, 43.5367559956942, 2);
INSERT INTO building(id, name, level) VALUES (1504, "building_lead_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1504, 30.0, 10.276371504960702, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1504, 30.0, 10.884188998923552, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 1504, 120.0, 42.32112100776851, 3);
INSERT INTO building(id, name, level) VALUES (1505, "building_maize_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1505, 9.859, 3.3826178520119936, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1505, 59.15399999999999, 20.29570711207196, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1505, 17.746198198198197, 6.088711515424313, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1505, 11.830792792792792, 4.059138949625289, 2);
INSERT INTO building(id, name, level) VALUES (1506, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1506, 14.999999999999998, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1506, 35.0, 13.703961920652958, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1506, 49.99999999999999, 9.788544229037825, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1506, 24.999999999999996, 4.8942721145189125, 1);
INSERT INTO building(id, name, level) VALUES (1507, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1507, 5.0, 3.364928538460941, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1507, 50.0, 33.64928538460941, 1);
INSERT INTO building(id, name, level) VALUES (1508, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1508, 10.0, 2.336647014507441, 1);
INSERT INTO building(id, name, level) VALUES (1509, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1509, 15.0, 5.138185752480351, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1509, 15.0, 5.442094499461776, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1509, 60.0, 21.160560503884255, 1);
INSERT INTO building(id, name, level) VALUES (1510, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1510, 5.0, 3.364928538460941, 1);
INSERT INTO building(id, name, level) VALUES (1511, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1511, 20.0, 4.673294029014882, 1);
INSERT INTO building(id, name, level) VALUES (1512, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1512, 17.5254, 39.14339268482116, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1512, 35.0508, 13.125549470207938, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1512, 43.8135, 20.694552675199564, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1512, 8.7627, 3.1791627646955805, 1);
INSERT INTO building(id, name, level) VALUES (1513, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1513, 10.0, 2.336647014507441, 2);
INSERT INTO building(id, name, level) VALUES (1514, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1514, 179.99999999999997, 402.0342293624001, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1514, 30.0, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1514, 15.0, 5.442094499461776, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1514, 299.99999999999994, 136.28062999641182, 3);
INSERT INTO building(id, name, level) VALUES (1515, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1515, 51.00359821428571, 19.099428587085495, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1515, 38.252696428571426, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1515, 25.501794642857142, 9.985017791257881, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1515, 51.00359821428571, 13.023155888503302, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1515, 63.75449999999999, 16.278945430579984, 3);
INSERT INTO building(id, name, level) VALUES (1516, "building_shipyardslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1516, 60.00000000000001, 134.01140978746676, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1516, 120.00000000000001, 44.936661543387096, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1516, 45.0, 30.92562403938508, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1516, 60.00000000000001, 41.23416538584678, 3);
INSERT INTO building(id, name, level) VALUES (1517, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1517, 15.0, 10.094785615382824, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1517, 150.0, 100.94785615382824, 3);
INSERT INTO building(id, name, level) VALUES (1518, "building_whaling_stationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1518, 4.9990000000000006, 3.3642555527532494, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (28, 1518, 19.996000000000002, 13.457022211012998, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1518, 9.998000000000001, 6.728511105506499, 1);
INSERT INTO building(id, name, level) VALUES (1519, "building_wheat_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1519, 9.857999999999999, 3.382274752524011, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1519, 59.147999999999996, 20.293648515144067, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1519, 17.744396396396397, 6.08809331814867, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1519, 11.829594594594594, 4.058727848436987, 2);
INSERT INTO building(id, name, level) VALUES (1520, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1520, 5.0, 2.354071761794985, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1520, 5.0, 5.636083924469001, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1520, 5.0, 5.121101742913077, 5);
INSERT INTO building(id, name, level) VALUES (1521, "building_naval_baselevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1521, 22.0, 8.20758947979085, 11);
INSERT INTO building(id, name, level) VALUES (1522, "building_portlevel", 3);
INSERT INTO building(id, name, level) VALUES (1523, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1523, 5.0, 1.814031499820592, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1523, 40.0, 14.512251998564736, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1523, 12.499999999999998, 4.535078749551479, 1);
INSERT INTO building(id, name, level) VALUES (1524, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1524, 10.0, 2.336647014507441, 1);
INSERT INTO building(id, name, level) VALUES (1525, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1525, 5.0, 1.814031499820592, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1525, 59.99999999999999, 21.7683779978471, 1);
INSERT INTO building(id, name, level) VALUES (1526, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1526, 9.797999999999998, 4.191944482023644, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1526, 9.797999999999998, 3.5547761270484317, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1526, 39.19199999999999, 15.49344121814415, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1526, 4.898999999999999, 1.9366801522680188, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1526, 24.495, 9.683400761340096, 1);
INSERT INTO building(id, name, level) VALUES (1527, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1527, 79.99999999999999, 34.22694004510017, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1527, 79.99999999999999, 44.43149232147489, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1527, 70.0, 34.4130641603766, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1527, 119.99999999999999, 58.9938242749313, 2);
INSERT INTO building(id, name, level) VALUES (1528, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1528, 9.999999999999998, 3.628062999641183, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1528, 119.99999999999999, 43.5367559956942, 2);
INSERT INTO building(id, name, level) VALUES (1529, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1529, 19.213594594594593, 8.220281878000625, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1529, 19.213594594594593, 6.97081316387545, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1529, 76.8543963963964, 30.382197206662518, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1529, 9.606792792792792, 3.7977719797414276, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1529, 48.034, 18.98887414452787, 2);
INSERT INTO building(id, name, level) VALUES (1530, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1530, 10.0, 22.33523496457779, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1530, 29.999999999999996, 11.234165385846772, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1530, 20.0, 4.88414525145923, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1530, 20.0, 7.256125999282368, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1530, 89.99999999999999, 44.58342919646937, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1530, 40.0, 19.814857420653055, 1);
INSERT INTO building(id, name, level) VALUES (1531, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1531, 20.000000000000004, 7.256125999282369, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1531, 160.00000000000003, 58.04900799425895, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1531, 50.00000000000001, 18.140314998205923, 4);
INSERT INTO building(id, name, level) VALUES (1532, "building_shipyardslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1532, 39.99999999999999, 89.34093985831113, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1532, 79.99999999999999, 29.95777436225806, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1532, 70.0, 48.1065262834879, 2);
INSERT INTO building(id, name, level) VALUES (1533, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1533, 15.0, 10.094785615382824, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1533, 150.0, 100.94785615382824, 3);
INSERT INTO building(id, name, level) VALUES (1534, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1534, 5.0, 3.364928538460941, 1);
INSERT INTO building(id, name, level) VALUES (1535, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1535, 40.0, 9.346588058029765, 2);
INSERT INTO building(id, name, level) VALUES (1536, "building_food_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1536, 36.145199999999996, 15.464244916476934, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 1536, 9.036299999999999, 1.4875266558171638, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1536, 36.145199999999996, 20.07481470322718, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1536, 22.590745454545452, 8.643574561456916, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1536, 90.36299999999999, 34.5743052024773, 1);
INSERT INTO building(id, name, level) VALUES (1537, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1537, 29.999999999999996, 10.88418899892355, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1537, 119.99999999999999, 43.5367559956942, 2);
INSERT INTO building(id, name, level) VALUES (1538, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1538, 9.999999999999998, 3.628062999641183, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1538, 119.99999999999999, 43.5367559956942, 2);
INSERT INTO building(id, name, level) VALUES (1539, "building_maize_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1539, 24.652000000000005, 8.458088577725903, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1539, 246.52000000000004, 84.58088577725903, 5);
INSERT INTO building(id, name, level) VALUES (1540, "building_cotton_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1540, 353.73999999999995, 474.0116, 10);
INSERT INTO building(id, name, level) VALUES (1541, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1541, 5.0, 2.354071761794985, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1541, 5.0, 5.636083924469001, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1541, 5.0, 5.121101742913077, 5);
INSERT INTO building(id, name, level) VALUES (1542, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1542, 5.0, 3.364928538460941, 1);
INSERT INTO building(id, name, level) VALUES (1543, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1543, 10.0, 2.336647014507441, 1);
INSERT INTO building(id, name, level) VALUES (1544, "building_cotton_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1544, 353.724, 473.99016, 10);
INSERT INTO building(id, name, level) VALUES (1545, "building_tobacco_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1545, 110.53875, 114.9603, 5);
INSERT INTO building(id, name, level) VALUES (1546, "building_maize_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1546, 24.649, 8.457059279261957, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1546, 246.49000000000004, 84.57059279261958, 5);
INSERT INTO building(id, name, level) VALUES (1547, "building_barrackslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1547, 2.0, 0.9416287047179941, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1547, 2.0, 2.2544335697876003, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1547, 2.0, 2.0484406971652307, 2);
INSERT INTO building(id, name, level) VALUES (1548, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1548, 5.0, 3.364928538460941, 1);
INSERT INTO building(id, name, level) VALUES (83888311, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 83888311, 22.10775, 22.10775, 1);
INSERT INTO building(id, name, level) VALUES (2797, "building_subsistence_farmslevel", 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2797, 29.65994545454545, 32.62594, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2797, 5.931981818181818, 6.52518, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2797, 5.931981818181818, 6.52518, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2797, 5.931981818181818, 6.52518, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2797, 5.931981818181818, 6.52518, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2797, 5.931981818181818, 6.52518, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2797, 8.304781818181818, 9.13526, 57);
INSERT INTO building(id, name, level) VALUES (2798, "building_subsistence_farmslevel", 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2798, 119.80154545454545, 131.7817, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2798, 23.96030909090909, 26.35634, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2798, 23.96030909090909, 26.35634, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2798, 23.96030909090909, 26.35634, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2798, 23.96030909090909, 26.35634, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2798, 23.96030909090909, 26.35634, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2798, 33.54442727272727, 36.89887, 102);
INSERT INTO building(id, name, level) VALUES (2799, "building_urban_centerlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2799, 59.99999999999999, 22.468330771693545, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2799, 119.99999999999999, 41.1054860198428, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2799, 59.99999999999999, 9.877007110103674, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2799, 839.9999999999999, 246.85771082802023, 12);
INSERT INTO building(id, name, level) VALUES (2800, "building_subsistence_farmslevel", 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2800, 64.32351818181817, 70.75587, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2800, 12.8647, 14.15117, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2800, 12.8647, 14.15117, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2800, 12.8647, 14.15117, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2800, 12.8647, 14.15117, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2800, 12.8647, 14.15117, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2800, 18.010581818181816, 19.81164, 69);
INSERT INTO building(id, name, level) VALUES (2801, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2801, 10.0, 3.744721795282258, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2801, 20.0, 6.850914336640467, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2801, 10.0, 1.6461678516839457, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2801, 140.0, 41.14295180467004, 2);
INSERT INTO building(id, name, level) VALUES (2846, "building_subsistence_farmslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2846, 8.522999999999998, 9.3753, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2846, 1.7046, 1.87506, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2846, 1.7046, 1.87506, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2846, 1.7046, 1.87506, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2846, 1.7046, 1.87506, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2846, 1.7046, 1.87506, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2846, 2.3864363636363635, 2.62508, 20);
INSERT INTO building(id, name, level) VALUES (2847, "building_subsistence_farmslevel", 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2847, 13.108199999999998, 14.41902, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2847, 2.6216363636363633, 2.8838, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2847, 2.6216363636363633, 2.8838, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2847, 2.6216363636363633, 2.8838, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2847, 2.6216363636363633, 2.8838, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2847, 2.6216363636363633, 2.8838, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2847, 3.670290909090909, 4.03732, 24);
INSERT INTO building(id, name, level) VALUES (2850, "building_subsistence_farmslevel", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2850, 17.301, 19.0311, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2850, 3.4602, 3.80622, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2850, 3.4602, 3.80622, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2850, 3.4602, 3.80622, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2850, 3.4602, 3.80622, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2850, 3.4602, 3.80622, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2850, 4.8442727272727275, 5.3287, 40);
INSERT INTO building(id, name, level) VALUES (2854, "building_subsistence_farmslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2854, 11.7865, 15.32245, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2854, 2.3573, 3.06449, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2854, 2.3573, 3.06449, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2854, 2.3573, 3.06449, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2854, 2.3573, 3.06449, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2854, 2.3573, 3.06449, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2854, 3.3002153846153846, 4.29028, 44);
INSERT INTO building(id, name, level) VALUES (100666151, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 100666151, 59.99999999999999, 22.468330771693545, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 100666151, 39.99999999999999, 12.247098590248857, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 100666151, 159.99999999999997, 54.45197154275577, 2);
INSERT INTO building(id, name, level) VALUES (2857, "building_subsistence_farmslevel", 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2857, 14.193299999999999, 18.45129, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2857, 2.838653846153846, 3.69025, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2857, 2.838653846153846, 3.69025, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2857, 2.838653846153846, 3.69025, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2857, 2.838653846153846, 3.69025, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2857, 2.838653846153846, 3.69025, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2857, 3.974123076923077, 5.16636, 46);
INSERT INTO building(id, name, level) VALUES (2858, "building_subsistence_farmslevel", 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2858, 11.526299999999999, 14.98419, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2858, 2.305253846153846, 2.99683, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2858, 2.305253846153846, 2.99683, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2858, 2.305253846153846, 2.99683, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2858, 2.305253846153846, 2.99683, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2858, 2.305253846153846, 2.99683, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2858, 3.227361538461538, 4.19557, 54);
INSERT INTO building(id, name, level) VALUES (2861, "building_subsistence_farmslevel", 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2861, 14.5705, 18.94165, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2861, 2.9141, 3.78833, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2861, 2.9141, 3.78833, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2861, 2.9141, 3.78833, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2861, 2.9141, 3.78833, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2861, 2.9141, 3.78833, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2861, 4.0797384615384615, 5.30366, 70);
INSERT INTO building(id, name, level) VALUES (16780081, "building_subsistence_farmslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16780081, 4.1049, 4.51539, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16780081, 0.8209727272727272, 0.90307, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16780081, 0.8209727272727272, 0.90307, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16780081, 0.8209727272727272, 0.90307, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16780081, 0.8209727272727272, 0.90307, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16780081, 0.8209727272727272, 0.90307, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16780081, 1.1493636363636361, 1.2643, 6);
INSERT INTO building(id, name, level) VALUES (2866, "building_subsistence_farmslevel", 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2866, 6.374199999999999, 7.01162, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2866, 1.2748363636363635, 1.40232, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2866, 1.2748363636363635, 1.40232, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2866, 1.2748363636363635, 1.40232, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2866, 1.2748363636363635, 1.40232, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2866, 1.2748363636363635, 1.40232, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2866, 1.7847727272727272, 1.96325, 56);
INSERT INTO building(id, name, level) VALUES (2867, "building_subsistence_farmslevel", 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2867, 25.898172727272726, 28.48799, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2867, 5.179627272727272, 5.69759, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2867, 5.179627272727272, 5.69759, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2867, 5.179627272727272, 5.69759, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2867, 5.179627272727272, 5.69759, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2867, 5.179627272727272, 5.69759, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2867, 7.2514818181818175, 7.97663, 93);
INSERT INTO building(id, name, level) VALUES (2868, "building_subsistence_farmslevel", 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2868, 24.6806, 27.14866, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2868, 4.936118181818181, 5.42973, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2868, 4.936118181818181, 5.42973, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2868, 4.936118181818181, 5.42973, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2868, 4.936118181818181, 5.42973, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2868, 4.936118181818181, 5.42973, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2868, 6.910563636363635, 7.60162, 68);
INSERT INTO building(id, name, level) VALUES (2869, "building_subsistence_farmslevel", 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2869, 17.958399999999997, 19.75424, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2869, 3.591672727272727, 3.95084, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2869, 3.591672727272727, 3.95084, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2869, 3.591672727272727, 3.95084, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2869, 3.591672727272727, 3.95084, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2869, 3.591672727272727, 3.95084, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2869, 5.0283454545454545, 5.53118, 64);
INSERT INTO building(id, name, level) VALUES (2871, "building_subsistence_farmslevel", 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2871, 21.5631, 23.71941, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2871, 4.3126181818181815, 4.74388, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2871, 4.3126181818181815, 4.74388, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2871, 4.3126181818181815, 4.74388, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2871, 4.3126181818181815, 4.74388, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2871, 4.3126181818181815, 4.74388, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2871, 6.037663636363636, 6.64143, 78);
INSERT INTO building(id, name, level) VALUES (2872, "building_subsistence_farmslevel", 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2872, 47.537099999999995, 52.29081, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2872, 9.507418181818181, 10.45816, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2872, 9.507418181818181, 10.45816, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2872, 9.507418181818181, 10.45816, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2872, 9.507418181818181, 10.45816, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2872, 9.507418181818181, 10.45816, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2872, 13.310381818181817, 14.64142, 78);
INSERT INTO building(id, name, level) VALUES (2873, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2873, 5.0, 1.872360897641129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2873, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2873, 5.0, 0.8230839258419729, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2873, 70.0, 20.57147590233502, 1);
INSERT INTO building(id, name, level) VALUES (2874, "building_subsistence_farmslevel", 121);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2874, 20.978372727272724, 23.07621, 121);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2874, 4.1956727272727266, 4.61524, 121);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2874, 4.1956727272727266, 4.61524, 121);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2874, 4.1956727272727266, 4.61524, 121);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2874, 4.1956727272727266, 4.61524, 121);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2874, 4.1956727272727266, 4.61524, 121);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2874, 5.873936363636363, 6.46133, 121);
INSERT INTO building(id, name, level) VALUES (2875, "building_subsistence_farmslevel", 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2875, 116.34649999999999, 127.98115, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2875, 23.269299999999998, 25.59623, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2875, 23.269299999999998, 25.59623, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2875, 23.269299999999998, 25.59623, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2875, 23.269299999999998, 25.59623, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2875, 23.269299999999998, 25.59623, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2875, 32.577018181818175, 35.83472, 148);
INSERT INTO building(id, name, level) VALUES (2876, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2876, 15.0, 5.617082692923387, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2876, 30.0, 10.276371504960702, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2876, 15.0, 2.469251777525919, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2876, 209.99999999999997, 61.71442770700506, 3);
INSERT INTO building(id, name, level) VALUES (2877, "building_subsistence_farmslevel", 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2877, 54.9264, 60.41904, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2877, 10.985272727272726, 12.0838, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2877, 10.985272727272726, 12.0838, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2877, 10.985272727272726, 12.0838, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2877, 10.985272727272726, 12.0838, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2877, 10.985272727272726, 12.0838, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2877, 15.379390909090908, 16.91733, 48);
INSERT INTO building(id, name, level) VALUES (2878, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2878, 10.0, 3.744721795282258, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2878, 20.0, 6.850914336640467, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2878, 10.0, 1.6461678516839457, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2878, 140.0, 41.14295180467004, 2);
INSERT INTO building(id, name, level) VALUES (2879, "building_subsistence_farmslevel", 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2879, 43.568999999999996, 47.9259, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2879, 8.713799999999999, 9.58518, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2879, 8.713799999999999, 9.58518, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2879, 8.713799999999999, 9.58518, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2879, 8.713799999999999, 9.58518, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2879, 8.713799999999999, 9.58518, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2879, 12.19931818181818, 13.41925, 45);
INSERT INTO building(id, name, level) VALUES (2880, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2880, 10.0, 3.744721795282258, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2880, 20.0, 6.850914336640467, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2880, 10.0, 1.6461678516839457, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2880, 140.0, 41.14295180467004, 2);
INSERT INTO building(id, name, level) VALUES (2881, "building_subsistence_farmslevel", 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2881, 13.6284, 14.99124, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2881, 2.7256727272727272, 2.99824, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2881, 2.7256727272727272, 2.99824, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2881, 2.7256727272727272, 2.99824, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2881, 2.7256727272727272, 2.99824, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2881, 2.7256727272727272, 2.99824, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2881, 3.8159454545454543, 4.19754, 48);
INSERT INTO building(id, name, level) VALUES (2882, "building_subsistence_farmslevel", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2882, 52.879999999999995, 58.168, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2882, 10.575999999999999, 11.6336, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2882, 10.575999999999999, 11.6336, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2882, 10.575999999999999, 11.6336, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2882, 10.575999999999999, 11.6336, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2882, 10.575999999999999, 11.6336, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2882, 14.8064, 16.28704, 50);
INSERT INTO building(id, name, level) VALUES (2883, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2883, 5.0, 1.872360897641129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2883, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2883, 5.0, 0.8230839258419729, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2883, 70.0, 20.57147590233502, 1);
INSERT INTO building(id, name, level) VALUES (2884, "building_subsistence_farmslevel", 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2884, 24.552, 27.0072, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2884, 4.910399999999999, 5.40144, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2884, 4.910399999999999, 5.40144, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2884, 4.910399999999999, 5.40144, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2884, 4.910399999999999, 5.40144, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2884, 4.910399999999999, 5.40144, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2884, 6.8745545454545445, 7.56201, 88);
INSERT INTO building(id, name, level) VALUES (2885, "building_subsistence_farmslevel", 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2885, 48.9923, 53.89153, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2885, 9.798454545454545, 10.7783, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2885, 9.798454545454545, 10.7783, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2885, 9.798454545454545, 10.7783, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2885, 9.798454545454545, 10.7783, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2885, 9.798454545454545, 10.7783, 68);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2885, 13.717836363636362, 15.08962, 68);
INSERT INTO building(id, name, level) VALUES (2886, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2886, 5.0, 1.872360897641129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2886, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2886, 5.0, 0.8230839258419729, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2886, 70.0, 20.57147590233502, 1);
INSERT INTO building(id, name, level) VALUES (2887, "building_subsistence_farmslevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2887, 2.485872727272727, 2.73446, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2887, 0.4971727272727272, 0.54689, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2887, 0.4971727272727272, 0.54689, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2887, 0.4971727272727272, 0.54689, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2887, 0.4971727272727272, 0.54689, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2887, 0.4971727272727272, 0.54689, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2887, 0.6960363636363636, 0.76564, 15);
INSERT INTO building(id, name, level) VALUES (2888, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2888, 0.5, 0.1872360897641129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2888, 1.0, 0.34254571683202334, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2888, 0.5, 0.0823083925841973, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2888, 7.0, 2.057147590233502, 1);
INSERT INTO building(id, name, level) VALUES (2889, "building_subsistence_farmslevel", 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2889, 21.911845454545453, 24.10303, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2889, 4.382363636363636, 4.8206, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2889, 4.382363636363636, 4.8206, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2889, 4.382363636363636, 4.8206, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2889, 4.382363636363636, 4.8206, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2889, 4.382363636363636, 4.8206, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2889, 6.13530909090909, 6.74884, 54);
INSERT INTO building(id, name, level) VALUES (2890, "building_subsistence_farmslevel", 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2890, 24.208199999999998, 26.62902, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2890, 4.841636363636363, 5.3258, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2890, 4.841636363636363, 5.3258, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2890, 4.841636363636363, 5.3258, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2890, 4.841636363636363, 5.3258, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2890, 4.841636363636363, 5.3258, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2890, 6.7782909090909085, 7.45612, 27);
INSERT INTO building(id, name, level) VALUES (2891, "building_subsistence_farmslevel", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2891, 3.3736454545454544, 3.71101, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2891, 0.6747272727272726, 0.7422, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2891, 0.6747272727272726, 0.7422, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2891, 0.6747272727272726, 0.7422, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2891, 0.6747272727272726, 0.7422, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2891, 0.6747272727272726, 0.7422, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2891, 0.9446181818181817, 1.03908, 18);
INSERT INTO building(id, name, level) VALUES (2892, "building_subsistence_farmslevel", 136);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2892, 124.12379999999999, 136.53618, 136);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2892, 24.824754545454542, 27.30723, 136);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2892, 24.824754545454542, 27.30723, 136);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2892, 24.824754545454542, 27.30723, 136);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2892, 24.824754545454542, 27.30723, 136);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2892, 24.824754545454542, 27.30723, 136);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2892, 34.75466363636364, 38.23013, 136);
INSERT INTO building(id, name, level) VALUES (2893, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2893, 20.0, 7.489443590564516, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2893, 40.0, 13.701828673280934, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2893, 20.0, 3.2923357033678915, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2893, 279.99999999999994, 82.28590360934007, 4);
INSERT INTO building(id, name, level) VALUES (2894, "building_subsistence_farmslevel", 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2894, 25.072772727272724, 27.58005, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2894, 5.014554545454545, 5.51601, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2894, 5.014554545454545, 5.51601, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2894, 5.014554545454545, 5.51601, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2894, 5.014554545454545, 5.51601, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2894, 5.014554545454545, 5.51601, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2894, 7.0203727272727265, 7.72241, 91);
INSERT INTO building(id, name, level) VALUES (2895, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2895, 5.0, 1.872360897641129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2895, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2895, 5.0, 0.8230839258419729, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2895, 70.0, 20.57147590233502, 1);
INSERT INTO building(id, name, level) VALUES (2896, "building_subsistence_farmslevel", 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2896, 48.09644545454545, 52.90609, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2896, 9.619281818181818, 10.58121, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2896, 9.619281818181818, 10.58121, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2896, 9.619281818181818, 10.58121, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2896, 9.619281818181818, 10.58121, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2896, 9.619281818181818, 10.58121, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2896, 13.466999999999999, 14.8137, 138);
INSERT INTO building(id, name, level) VALUES (2897, "building_subsistence_farmslevel", 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2897, 14.622145454545453, 16.08436, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2897, 2.9244272727272724, 3.21687, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2897, 2.9244272727272724, 3.21687, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2897, 2.9244272727272724, 3.21687, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2897, 2.9244272727272724, 3.21687, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2897, 2.9244272727272724, 3.21687, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2897, 4.0942, 4.50362, 43);
INSERT INTO building(id, name, level) VALUES (2898, "building_subsistence_farmslevel", 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2898, 3.5805454545454545, 3.9386, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2898, 0.7161090909090908, 0.78772, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2898, 0.7161090909090908, 0.78772, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2898, 0.7161090909090908, 0.78772, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2898, 0.7161090909090908, 0.78772, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2898, 0.7161090909090908, 0.78772, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2898, 1.0025454545454544, 1.1028, 19);
INSERT INTO building(id, name, level) VALUES (2899, "building_subsistence_farmslevel", 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2899, 25.342499999999998, 27.87675, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2899, 5.0685, 5.57535, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2899, 5.0685, 5.57535, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2899, 5.0685, 5.57535, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2899, 5.0685, 5.57535, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2899, 5.0685, 5.57535, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2899, 7.095899999999999, 7.80549, 100);
INSERT INTO building(id, name, level) VALUES (2900, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2900, 20.0, 7.489443590564516, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2900, 40.0, 13.701828673280934, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2900, 20.0, 3.2923357033678915, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2900, 279.99999999999994, 82.28590360934007, 4);
INSERT INTO building(id, name, level) VALUES (2901, "building_subsistence_farmslevel", 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2901, 23.668345454545452, 26.03518, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2901, 4.7336636363636355, 5.20703, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2901, 4.7336636363636355, 5.20703, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2901, 4.7336636363636355, 5.20703, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2901, 4.7336636363636355, 5.20703, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2901, 4.7336636363636355, 5.20703, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2901, 6.627136363636364, 7.28985, 58);
INSERT INTO building(id, name, level) VALUES (2902, "building_subsistence_farmslevel", 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2902, 11.152899999999999, 12.26819, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2902, 2.230572727272727, 2.45363, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2902, 2.230572727272727, 2.45363, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2902, 2.230572727272727, 2.45363, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2902, 2.230572727272727, 2.45363, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2902, 2.230572727272727, 2.45363, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2902, 3.1228090909090906, 3.43509, 22);
INSERT INTO building(id, name, level) VALUES (2903, "building_subsistence_farmslevel", 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2903, 15.302999999999999, 16.8333, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2903, 3.0605999999999995, 3.36666, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2903, 3.0605999999999995, 3.36666, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2903, 3.0605999999999995, 3.36666, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2903, 3.0605999999999995, 3.36666, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2903, 3.0605999999999995, 3.36666, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2903, 4.284836363636364, 4.71332, 24);
INSERT INTO building(id, name, level) VALUES (2904, "building_subsistence_farmslevel", 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2904, 70.69999999999999, 77.77, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2904, 14.139999999999999, 15.554, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2904, 14.139999999999999, 15.554, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2904, 14.139999999999999, 15.554, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2904, 14.139999999999999, 15.554, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2904, 14.139999999999999, 15.554, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2904, 19.796, 21.7756, 70);
INSERT INTO building(id, name, level) VALUES (2905, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2905, 10.0, 3.744721795282258, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2905, 20.0, 6.850914336640467, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2905, 10.0, 1.6461678516839457, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2905, 140.0, 41.14295180467004, 2);
INSERT INTO building(id, name, level) VALUES (2919, "building_subsistence_farmslevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2919, 0.40249999999999997, 0.52325, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2919, 0.0805, 0.10465, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2919, 0.0805, 0.10465, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2919, 0.0805, 0.10465, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2919, 0.0805, 0.10465, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2919, 0.0805, 0.10465, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2919, 0.1127, 0.14651, 2);
INSERT INTO building(id, name, level) VALUES (3040, "building_subsistence_farmslevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3040, 0.20949999999999996, 0.23045, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3040, 0.04189999999999999, 0.04609, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3040, 0.04189999999999999, 0.04609, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3040, 0.04189999999999999, 0.04609, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3040, 0.04189999999999999, 0.04609, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3040, 0.04189999999999999, 0.04609, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3040, 0.058654545454545445, 0.06452, 2);
INSERT INTO building(id, name, level) VALUES (33557875, "building_portlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 33557875, 20.744, 13.960415520366752, 5);
INSERT INTO building(id, name, level) VALUES (33558243, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 33558243, 5.0, 1.1683235072537206, 1);
INSERT INTO building(id, name, level) VALUES (16781062, "building_subsistence_farmslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16781062, 2.946, 3.8298, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16781062, 0.5892, 0.76596, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16781062, 0.5892, 0.76596, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16781062, 0.5892, 0.76596, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16781062, 0.5892, 0.76596, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16781062, 0.5892, 0.76596, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16781062, 0.8248769230769231, 1.07234, 6);
INSERT INTO building(id, name, level) VALUES (3847, "building_subsistence_farmslevel", 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3847, 55.60314545454545, 61.16346, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3847, 11.12062727272727, 12.23269, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3847, 11.12062727272727, 12.23269, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3847, 11.12062727272727, 12.23269, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3847, 11.12062727272727, 12.23269, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3847, 11.12062727272727, 12.23269, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3847, 15.568881818181817, 17.12577, 58);
INSERT INTO building(id, name, level) VALUES (3848, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3848, 1.94635, 0.7288539266247623, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3848, 3.8927, 1.3334277119120175, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3848, 1.94635, 0.3204018798125048, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3848, 27.2489, 8.007858424501954, 1);
INSERT INTO building(id, name, level) VALUES (3857, "building_trade_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (3858, "building_trade_centerlevel", 92);
INSERT INTO building(id, name, level) VALUES (3859, "building_trade_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (16781102, "building_naval_baselevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 16781102, 4.0, 1.492288996325609, 2);
INSERT INTO building(id, name, level) VALUES (3930, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (3931, "building_conscription_centerlevel", 46);
INSERT INTO building(id, name, level) VALUES (3932, "building_conscription_centerlevel", 23);
INSERT INTO building(id, name, level) VALUES (50335603, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 50335603, 3.0, 1.412443057076991, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 50335603, 3.0, 3.3816503546814003, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 50335603, 3.0, 3.0726610457478465, 3);
INSERT INTO building(id, name, level) VALUES (3962, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (3963, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (3965, "building_conscription_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (3966, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (3967, "building_conscription_centerlevel", 29);
INSERT INTO building(id, name, level) VALUES (3968, "building_conscription_centerlevel", 20);
INSERT INTO building(id, name, level) VALUES (3969, "building_conscription_centerlevel", 20);
INSERT INTO building(id, name, level) VALUES (3970, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (3971, "building_conscription_centerlevel", 19);
INSERT INTO building(id, name, level) VALUES (3972, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (3973, "building_conscription_centerlevel", 19);
INSERT INTO building(id, name, level) VALUES (3974, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (3975, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (3976, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (3977, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (3978, "building_conscription_centerlevel", 34);
INSERT INTO building(id, name, level) VALUES (3979, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (3980, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (3981, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (3982, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (3983, "building_conscription_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (3984, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (3985, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (3986, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (3987, "building_conscription_centerlevel", 22);
INSERT INTO building(id, name, level) VALUES (16781241, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16781241, 29.999999999999996, 11.234165385846772, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16781241, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16781241, 20.0, 6.1235492951244295, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16781241, 80.0, 27.28520962977255, 1);
INSERT INTO building(id, name, level) VALUES (16781552, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16781552, 15.0, 5.442094499461776, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 16781552, 60.0, 21.768377997847104, 1);
INSERT INTO building(id, name, level) VALUES (16781554, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16781554, 22.1175, 22.1175, 1);
INSERT INTO building(id, name, level) VALUES (201330991, "building_arms_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 201330991, 37.443198198198196, 17.685650254842685, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 201330991, 18.721594594594592, 4.571949366946698, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 201330991, 28.082396396396394, 10.061080888886092, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 201330991, 28.082396396396394, 10.061080888886092, 2);
INSERT INTO building(id, name, level) VALUES (4542, "building_conscription_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (100667901, "building_naval_baselevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 100667901, 2.0, 0.7461444981628045, 1);
INSERT INTO building(id, name, level) VALUES (4672, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4672, 59.99999999999999, 22.468330771693545, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 4672, 39.99999999999999, 12.247098590248857, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4672, 159.99999999999997, 54.45197154275577, 2);
INSERT INTO building(id, name, level) VALUES (4752, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4752, 17.5254, 39.14339268482116, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4752, 35.0508, 13.125549470207938, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4752, 43.8135, 20.694552675199564, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4752, 8.7627, 3.1791627646955805, 1);
INSERT INTO building(id, name, level) VALUES (16781993, "building_naval_baselevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 16781993, 2.0, 0.7461444981628045, 1);
INSERT INTO building(id, name, level) VALUES (218108628, "building_munition_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 218108628, 17.7606, 6.9540167453699695, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 218108628, 17.7606, 2.257647739245706, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 218108628, 44.4015, 11.514580605769595, 1);
INSERT INTO building(id, name, level) VALUES (4858, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4858, 59.99999999999999, 22.468330771693545, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 4858, 39.99999999999999, 12.247098590248857, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4858, 159.99999999999997, 54.45197154275577, 2);
INSERT INTO building(id, name, level) VALUES (268440356, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (16782142, "building_naval_baselevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 16782142, 2.0, 0.7461444981628045, 1);
INSERT INTO building(id, name, level) VALUES (4939, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4939, 29.999999999999996, 11.234165385846772, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 4939, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 4939, 20.0, 6.1235492951244295, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4939, 80.0, 27.28520962977255, 1);
INSERT INTO building(id, name, level) VALUES (16782158, "building_naval_baselevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 16782158, 4.0, 1.492288996325609, 2);
INSERT INTO building(id, name, level) VALUES (218108808, "building_munition_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 218108808, 17.741, 6.946342526694404, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 218108808, 17.741, 2.255156275236088, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 218108808, 44.3525, 11.501873502413117, 1);
INSERT INTO building(id, name, level) VALUES (16782352, "building_arms_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782352, 2.16, 1.0202388254403565, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782352, 1.08, 0.26374384357879843, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 16782352, 1.6199999999999999, 0.5803974422242324, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 16782352, 1.6199999999999999, 0.5803974422242324, 2);
INSERT INTO building(id, name, level) VALUES (5145, "building_gold_fieldslevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 5145, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (5152, "building_gold_fieldslevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 5152, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (16782413, "building_barrackslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 16782413, 2.0, 0.9416287047179941, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16782413, 2.0, 2.2544335697876003, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16782413, 2.0, 2.0484406971652307, 2);
INSERT INTO building(id, name, level) VALUES (16782481, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 16782481, 3.0, 1.412443057076991, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16782481, 3.0, 3.3816503546814003, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16782481, 3.0, 3.0726610457478465, 3);
INSERT INTO building(id, name, level) VALUES (16782520, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782520, 9.2366, 4.362749044010369, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782520, 4.6183, 1.127822400740708, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 16782520, 6.927445454545454, 2.481896063559301, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 16782520, 6.927445454545454, 2.481896063559301, 1);
INSERT INTO building(id, name, level) VALUES (151000257, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 151000257, 5.0, 2.354071761794985, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 151000257, 5.0, 5.636083924469001, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 151000257, 5.0, 5.121101742913077, 5);
INSERT INTO building(id, name, level) VALUES (100668624, "building_subsistence_farmslevel", 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 100668624, 6.602199999999999, 7.26242, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 100668624, 1.3204363636363636, 1.45248, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 100668624, 1.3204363636363636, 1.45248, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 100668624, 1.3204363636363636, 1.45248, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 100668624, 1.3204363636363636, 1.45248, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 100668624, 1.3204363636363636, 1.45248, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 100668624, 1.8486090909090906, 2.03347, 22);
INSERT INTO building(id, name, level) VALUES (16782545, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (5344, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5344, 5.0, 1.872360897641129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5344, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5344, 5.0, 0.8230839258419729, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5344, 70.0, 20.57147590233502, 1);
INSERT INTO building(id, name, level) VALUES (16782596, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782596, 15.0, 5.138185752480351, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782596, 15.0, 5.442094499461776, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 16782596, 60.0, 21.160560503884255, 1);
INSERT INTO building(id, name, level) VALUES (5389, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5389, 5.0, 1.872360897641129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5389, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5389, 5.0, 0.8230839258419729, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5389, 70.0, 20.57147590233502, 1);
INSERT INTO building(id, name, level) VALUES (50337073, "building_subsistence_farmslevel", 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 50337073, 10.8514, 11.93654, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 50337073, 2.170272727272727, 2.3873, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 50337073, 2.170272727272727, 2.3873, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 50337073, 2.170272727272727, 2.3873, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 50337073, 2.170272727272727, 2.3873, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 50337073, 2.170272727272727, 2.3873, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 50337073, 3.0383909090909085, 3.34223, 23);
INSERT INTO building(id, name, level) VALUES (16782644, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 16782644, 1.0, 0.47081435235899705, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16782644, 1.0, 1.1272167848938002, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16782644, 1.0, 1.0242203485826153, 1);
INSERT INTO building(id, name, level) VALUES (16782651, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 16782651, 3.0, 1.412443057076991, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16782651, 3.0, 3.3816503546814003, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16782651, 3.0, 3.0726610457478465, 3);
INSERT INTO building(id, name, level) VALUES (16782690, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 16782690, 5.0, 2.354071761794985, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16782690, 5.0, 5.636083924469001, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16782690, 5.0, 5.121101742913077, 5);
INSERT INTO building(id, name, level) VALUES (33559916, "building_shipyardslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33559916, 20.0, 44.67046992915558, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559916, 40.0, 14.978887181129032, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 33559916, 35.0, 24.05326314174395, 1);
INSERT INTO building(id, name, level) VALUES (16782703, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 16782703, 3.0, 1.412443057076991, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16782703, 3.0, 3.3816503546814003, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16782703, 3.0, 3.0726610457478465, 3);
INSERT INTO building(id, name, level) VALUES (33559925, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (16782727, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782727, 18.6166, 8.793230610043027, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782727, 9.3083, 2.273154462207897, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 16782727, 13.962445454545453, 5.00232569692194, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 16782727, 13.962445454545453, 5.00232569692194, 1);
INSERT INTO building(id, name, level) VALUES (50337203, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 50337203, 5.0, 2.354071761794985, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 50337203, 5.0, 5.636083924469001, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 50337203, 5.0, 5.121101742913077, 5);
INSERT INTO building(id, name, level) VALUES (16782778, "building_subsistence_orchardslevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16782778, 1.9849454545454543, 2.18344, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16782778, 0.9924727272727272, 1.09172, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16782778, 2.9774181818181815, 3.27516, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16782778, 1.9849454545454543, 2.18344, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16782778, 1.9849454545454543, 2.18344, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16782778, 1.9849454545454543, 2.18344, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 16782778, 5.279963636363636, 5.80796, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16782778, 2.7789272727272727, 3.05682, 11);
INSERT INTO building(id, name, level) VALUES (16782787, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782787, 30.0, 10.276371504960702, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782787, 30.0, 10.884188998923552, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 16782787, 120.0, 42.32112100776851, 2);
INSERT INTO building(id, name, level) VALUES (5638, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5638, 18.799999999999997, 8.879856443647546, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 5638, 9.399999999999999, 2.2955482681858377, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 5638, 14.099999999999998, 5.0516073675072075, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 5638, 14.099999999999998, 5.0516073675072075, 1);
INSERT INTO building(id, name, level) VALUES (83891725, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (16782864, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 16782864, 1.0, 0.47081435235899705, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16782864, 1.0, 1.1272167848938002, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16782864, 1.0, 1.0242203485826153, 1);
INSERT INTO building(id, name, level) VALUES (16782867, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782867, 25.513495049504954, 8.739538450622954, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782867, 25.513495049504954, 9.256456738063743, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 16782867, 102.054, 35.991997361056725, 2);
INSERT INTO building(id, name, level) VALUES (100668983, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (117446205, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 117446205, 1.0, 0.47081435235899705, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 117446205, 1.0, 1.1272167848938002, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 117446205, 1.0, 1.0242203485826153, 1);
INSERT INTO building(id, name, level) VALUES (16782937, "building_subsistence_orchardslevel", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16782937, 2.2951727272727274, 2.52469, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16782937, 1.147581818181818, 1.26234, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16782937, 3.4427636363636362, 3.78704, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16782937, 2.2951727272727274, 2.52469, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16782937, 2.2951727272727274, 2.52469, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16782937, 2.2951727272727274, 2.52469, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 16782937, 6.105172727272727, 6.71569, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16782937, 3.2132454545454543, 3.53457, 9);
INSERT INTO building(id, name, level) VALUES (16782952, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 16782952, 14.999999999999998, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 16782952, 35.0, 13.703961920652958, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 16782952, 49.99999999999999, 9.788544229037825, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 16782952, 24.999999999999996, 4.8942721145189125, 1);
INSERT INTO building(id, name, level) VALUES (16782954, "building_barrackslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 16782954, 4.0, 1.8832574094359882, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16782954, 4.0, 4.508867139575201, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16782954, 4.0, 4.096881394330461, 4);
INSERT INTO building(id, name, level) VALUES (50337426, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (16783012, "building_arms_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783012, 56.47619642857143, 26.675559402603966, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16783012, 28.238098214285714, 6.895948665177146, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 16783012, 42.357142857142854, 15.175294675439586, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 16783012, 42.357142857142854, 15.175294675439586, 3);
INSERT INTO building(id, name, level) VALUES (16783115, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783115, 18.873199999999997, 8.914431203843025, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16783115, 9.436599999999999, 2.304486253996008, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 16783115, 14.154899999999998, 5.071276391938141, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 16783115, 14.154899999999998, 5.071276391938141, 1);
INSERT INTO building(id, name, level) VALUES (5925, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5925, 17.5254, 39.14339268482116, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5925, 35.0508, 13.125549470207938, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5925, 43.8135, 20.694552675199564, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5925, 8.7627, 3.1791627646955805, 1);
INSERT INTO building(id, name, level) VALUES (5940, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5940, 17.5254, 39.14339268482116, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5940, 35.0508, 13.125549470207938, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5940, 43.8135, 20.694552675199564, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5940, 8.7627, 3.1791627646955805, 1);
INSERT INTO building(id, name, level) VALUES (5942, "building_subsistence_farmslevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 5942, 0.3725, 0.40975, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 5942, 0.07449999999999998, 0.08195, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 5942, 0.07449999999999998, 0.08195, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 5942, 0.07449999999999998, 0.08195, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 5942, 0.07449999999999998, 0.08195, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5942, 0.07449999999999998, 0.08195, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 5942, 0.10429999999999999, 0.11473, 2);
INSERT INTO building(id, name, level) VALUES (201332536, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (5970, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5970, 30.0, 10.276371504960702, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5970, 30.0, 10.884188998923552, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 5970, 120.0, 42.32112100776851, 2);
INSERT INTO building(id, name, level) VALUES (67114881, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (16783245, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783245, 29.999999999999996, 11.234165385846772, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 16783245, 10.0, 2.03399853768452, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 16783245, 70.0, 20.22552116538372, 1);
INSERT INTO building(id, name, level) VALUES (6033, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (6034, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (6056, "building_subsistence_farmslevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6056, 18.3678, 20.20458, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6056, 3.6735545454545453, 4.04091, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6056, 3.6735545454545453, 4.04091, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6056, 3.6735545454545453, 4.04091, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6056, 3.6735545454545453, 4.04091, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6056, 3.6735545454545453, 4.04091, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6056, 5.142981818181818, 5.65728, 11);
INSERT INTO building(id, name, level) VALUES (6057, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 6057, 1.2231999999999998, 0.5777574681845573, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 6057, 0.6115999999999999, 0.14935716178962322, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 6057, 0.9173999999999999, 0.3286769219114264, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 6057, 0.9173999999999999, 0.3286769219114264, 1);
INSERT INTO building(id, name, level) VALUES (67114965, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (6132, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6132, 5.0, 1.872360897641129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6132, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 6132, 5.0, 0.8230839258419729, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6132, 70.0, 20.57147590233502, 1);
INSERT INTO building(id, name, level) VALUES (6135, "building_barrackslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 6135, 2.0, 0.9416287047179941, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 6135, 2.0, 2.2544335697876003, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 6135, 2.0, 2.0484406971652307, 2);
INSERT INTO building(id, name, level) VALUES (16783359, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16783359, 20.848, 20.848, 1);
INSERT INTO building(id, name, level) VALUES (6144, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 6144, 3.0, 1.412443057076991, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 6144, 3.0, 3.3816503546814003, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 6144, 3.0, 3.0726610457478465, 3);
INSERT INTO building(id, name, level) VALUES (16783372, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16783372, 5.0, 1.1683235072537206, 1);
INSERT INTO building(id, name, level) VALUES (6171, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 6171, 41.66649504950495, 42.08316, 2);
INSERT INTO building(id, name, level) VALUES (6175, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 6175, 14.3478, 5.205472230625177, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 6175, 4.7826, 0.6079426414488539, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 6175, 71.739, 17.57325038742935, 1);
INSERT INTO building(id, name, level) VALUES (16783404, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 16783404, 1.0, 0.47081435235899705, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16783404, 1.0, 1.1272167848938002, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16783404, 1.0, 1.0242203485826153, 1);
INSERT INTO building(id, name, level) VALUES (16783411, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16783411, 35.3724, 44.2155, 1);
INSERT INTO building(id, name, level) VALUES (16783435, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16783435, 22.142, 22.142, 1);
INSERT INTO building(id, name, level) VALUES (6224, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (16783446, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16783446, 35.3724, 44.2155, 1);
INSERT INTO building(id, name, level) VALUES (50337904, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 50337904, 22.10775, 22.10775, 1);
INSERT INTO building(id, name, level) VALUES (16783473, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16783473, 44.367495049504946, 44.81117, 2);
INSERT INTO building(id, name, level) VALUES (6261, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6261, 5.0, 1.872360897641129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6261, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 6261, 5.0, 0.8230839258419729, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6261, 70.0, 20.57147590233502, 1);
INSERT INTO building(id, name, level) VALUES (33560698, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (6277, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 6277, 22.10775, 22.10775, 1);
INSERT INTO building(id, name, level) VALUES (6292, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (16783528, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16783528, 5.0, 1.1683235072537206, 1);
INSERT INTO building(id, name, level) VALUES (16783541, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16783541, 5.0, 1.1683235072537206, 1);
INSERT INTO building(id, name, level) VALUES (100669669, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 100669669, 5.0, 1.872360897641129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 100669669, 10.0, 3.4254571683202335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 100669669, 5.0, 0.8230839258419729, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 100669669, 70.0, 20.57147590233502, 1);
INSERT INTO building(id, name, level) VALUES (6445, "building_subsistence_fishing_villageslevel", 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6445, 0.0025727272727272725, 0.00283, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 6445, 0.010318181818181818, 0.01135, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6445, 0.0012818181818181817, 0.00141, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6445, 0.003863636363636364, 0.00425, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6445, 0.0025727272727272725, 0.00283, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6445, 0.0025727272727272725, 0.00283, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6445, 0.0025727272727272725, 0.00283, 43);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6445, 0.0036090909090909086, 0.00397, 43);
INSERT INTO building(id, name, level) VALUES (6446, "building_subsistence_fishing_villageslevel", 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6446, 0.0009454545454545453, 0.00104, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 6446, 0.0037999999999999996, 0.00418, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6446, 0.00047272727272727267, 0.00052, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6446, 0.001418181818181818, 0.00156, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6446, 0.0009454545454545453, 0.00104, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6446, 0.0009454545454545453, 0.00104, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6446, 0.0009454545454545453, 0.00104, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6446, 0.001327272727272727, 0.00146, 19);
INSERT INTO building(id, name, level) VALUES (6447, "building_subsistence_fishing_villageslevel", 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6447, 1.801, 1.9811, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 6447, 7.204, 7.9244, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6447, 0.9005, 0.99055, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6447, 2.7015, 2.97165, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6447, 1.801, 1.9811, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6447, 1.801, 1.9811, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6447, 1.801, 1.9811, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6447, 2.5214, 2.77354, 100);
INSERT INTO building(id, name, level) VALUES (6448, "building_subsistence_fishing_villageslevel", 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6448, 0.0022727272727272726, 0.0025, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 6448, 0.009118181818181819, 0.01003, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6448, 0.0011363636363636363, 0.00125, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6448, 0.0034181818181818176, 0.00376, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6448, 0.0022727272727272726, 0.0025, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6448, 0.0022727272727272726, 0.0025, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6448, 0.0022727272727272726, 0.0025, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6448, 0.0031909090909090906, 0.00351, 24);
INSERT INTO building(id, name, level) VALUES (6449, "building_subsistence_pastureslevel", 2);
INSERT INTO building(id, name, level) VALUES (6450, "building_subsistence_pastureslevel", 2);
