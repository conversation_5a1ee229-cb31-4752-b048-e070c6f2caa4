
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 87.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 62.51800738458702, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 72.93767528201819, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 112.89524980156281, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 24.3265186858076, 6513.849455111417);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 27.870470058138768, 596.7948643788249);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 15.609831908338514, 491.09556025200345);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 17.79610914067884, 576.9347965867256);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 42.86188987048169, 346.8503206057664);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 39.77108383273406, 2285.3289070702945);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 34.9945057882764, 1673.2179624625098);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 43.71800188872919, 291.55474833140016);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 14.500694007486214, 1095.7809249924603);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 78.13432835820896, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 42.08025454545455, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 12.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 32.300598741160776, 135.61187877418283);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 22.234040612283867, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 30.237066083320006, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 32.13605925875232, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 63.47191889211562, 157.1698108115073);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 45.0070601361291, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 45.54286747449574, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 87.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 99.70016615496242, 328.0798940557407);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 44.389537120597, 287.24507064298865);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 46.66728648445806, 244.09349736079247);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 38.019516106403096, 2469.754224748588);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 77.79212859551431, 435.77344743625264);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 87.5, 223.07565206374795);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 49.54388186020346, 46.0804012050963);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 70.0, 72.6828789385579);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 82.15145440778596, 380.241349853899);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 66.52900621931823, 223.1365529144044);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 297.31762955118137, 0.09557300113049176);
INSERT INTO building(id, name, level) VALUES (382, "building_government_administrationlevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 382, 109.99999999999999, 42.93421298843504, 11);
INSERT INTO building(id, name, level) VALUES (383, "building_construction_sectorlevel", 2);
INSERT INTO building(id, name, level) VALUES (384, "building_universitylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 384, 30.0, 11.70933081502774, 6);
INSERT INTO building(id, name, level) VALUES (385, "building_arts_academylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 385, 0.215, 0.08391687084103214, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 385, 0.08600000000000001, 0.03356674833641286, 2);
INSERT INTO building(id, name, level) VALUES (386, "building_furniture_manufacturieslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 386, 50.00000000000001, 70.68981134531536, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 386, 100.00000000000001, 117.22313271238828, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 386, 50.00000000000001, 67.76273872717066, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 386, 25.000000000000004, 20.380943771253552, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 386, 225.00000000000003, 214.6071234853205, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 386, 100.00000000000001, 95.38094377125356, 5);
INSERT INTO building(id, name, level) VALUES (387, "building_paper_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 387, 150.0, 175.83469906858238, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 387, 200.00000000000003, 200.00000000000003, 5);
INSERT INTO building(id, name, level) VALUES (388, "building_wheat_farmlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 388, 46.364000000000004, 15.440426734299308, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 388, 278.18399999999997, 92.64256040579583, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 388, 83.45519327731093, 27.792765882907158, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 388, 55.63679831932774, 18.52851152145127, 10);
INSERT INTO building(id, name, level) VALUES (389, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 389, 24.80759821428571, 17.652325144576558, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 389, 99.23039285714285, 70.60930057830623, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 389, 12.403794642857143, 8.826159395639648, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 389, 12.403794642857143, 8.826159395639648, 3);
INSERT INTO building(id, name, level) VALUES (390, "building_barrackslevel", 34);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 390, 34.0, 32.09750553164536, 34);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 390, 34.0, 32.09750553164536, 34);
INSERT INTO building(id, name, level) VALUES (391, "building_government_administrationlevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 391, 109.99999999999999, 42.93421298843504, 11);
INSERT INTO building(id, name, level) VALUES (392, "building_arms_industrylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 392, 99.16300000000001, 243.16496031678975, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 392, 49.581500000000005, 67.19556460402424, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 392, 74.3722456140351, 74.3722456140351, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 392, 74.3722456140351, 74.3722456140351, 5);
INSERT INTO building(id, name, level) VALUES (393, "building_construction_sectorlevel", 2);
INSERT INTO building(id, name, level) VALUES (394, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 394, 5.0, 1.9515551358379566, 1);
INSERT INTO building(id, name, level) VALUES (395, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 395, 160.00000000000003, 113.85108702324138, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 395, 100.00000000000001, 13.138834365755113, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 395, 60.0, 25.28872912658429, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 395, 240.0, 101.15491650633716, 4);
INSERT INTO building(id, name, level) VALUES (396, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 396, 60.0, 70.33387962743295, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 396, 89.99999999999999, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 396, 30.0, 44.47284978437694, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 396, 60.0, 40.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 396, 120.0, 80.0, 3);
INSERT INTO building(id, name, level) VALUES (397, "building_iron_minelevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 397, 104.99999999999999, 94.26387254124968, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 397, 104.99999999999999, 85.59996383926489, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 397, 35.0, 0.0, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 397, 478.8, 273.3930312983822, 7);
INSERT INTO building(id, name, level) VALUES (398, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 398, 25.000000000000004, 20.380943771253552, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 398, 200.00000000000003, 163.04755017002842, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 398, 50.00000000000001, 40.761887542507104, 5);
INSERT INTO building(id, name, level) VALUES (399, "building_rye_farmlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 399, 34.54779310344828, 11.505320253764214, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 399, 207.28679310344828, 69.03193300624734, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 399, 103.64339655172414, 34.51596650312367, 7);
INSERT INTO building(id, name, level) VALUES (400, "building_barrackslevel", 31);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 400, 31.0, 29.265372690617827, 31);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 400, 31.0, 29.265372690617827, 31);
INSERT INTO building(id, name, level) VALUES (401, "building_coal_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 401, 45.0, 36.68569878825639, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 401, 180.0, 146.74279515302555, 3);
INSERT INTO building(id, name, level) VALUES (402, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 402, 89.99999999999999, 127.2416604215676, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 402, 30.0, 27.919745454545456, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 402, 15.0, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 402, 120.0, 77.22632727272727, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 402, 60.0, 38.61316363636364, 3);
INSERT INTO building(id, name, level) VALUES (403, "building_food_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 403, 120.0, 85.388315267431, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 403, 74.99999999999999, 9.85412577431633, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 403, 44.99999999999999, 18.966546844938208, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 403, 179.99999999999997, 75.86618737975283, 3);
INSERT INTO building(id, name, level) VALUES (404, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 404, 20.000000000000004, 16.30475501700284, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 404, 160.00000000000003, 130.43804013602272, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 404, 40.00000000000001, 32.60951003400568, 4);
INSERT INTO building(id, name, level) VALUES (405, "building_wheat_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 405, 18.761, 6.247904537188105, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 405, 112.566, 37.487427223128634, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 405, 33.76979646017699, 11.246226988084784, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 405, 22.51319469026549, 7.497483676345019, 4);
INSERT INTO building(id, name, level) VALUES (406, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 406, 9.458, 8.928770803479466, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 406, 9.458, 8.928770803479466, 10);
INSERT INTO building(id, name, level) VALUES (407, "building_coal_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 407, 45.0, 36.68569878825639, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 407, 180.0, 146.74279515302555, 3);
INSERT INTO building(id, name, level) VALUES (408, "building_iron_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 408, 59.99999999999999, 53.86507002357125, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 408, 59.99999999999999, 48.91426505100851, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 408, 239.99999999999997, 205.55867014915952, 4);
INSERT INTO building(id, name, level) VALUES (409, "building_lead_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 409, 13.638, 11.118212446094235, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 409, 54.552, 44.47284978437694, 3);
INSERT INTO building(id, name, level) VALUES (410, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 410, 14.999999999999998, 16.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 410, 8.0, 8.8, 1);
INSERT INTO building(id, name, level) VALUES (411, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 411, 16.860594594594595, 11.997481390329886, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 411, 67.44239639639639, 47.9899383823879, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 411, 8.430297297297297, 5.998740695164943, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 411, 8.430297297297297, 5.998740695164943, 2);
INSERT INTO building(id, name, level) VALUES (412, "building_barrackslevel", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 412, 12.298, 11.609856559652195, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 412, 12.298, 11.609856559652195, 13);
INSERT INTO building(id, name, level) VALUES (413, "building_iron_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 413, 45.0, 40.39880251767844, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 413, 45.0, 36.68569878825639, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 413, 180.0, 154.16900261186964, 3);
INSERT INTO building(id, name, level) VALUES (414, "building_gold_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 414, 30.0, 26.932535011785628, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 414, 30.0, 24.457132525504257, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 414, 60.0, 51.38966753728989, 2);
INSERT INTO building(id, name, level) VALUES (415, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 415, 8.4294, 5.998102205960691, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 415, 33.7176, 23.992408823842766, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 415, 4.2147, 2.9990511029803457, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 415, 4.2147, 2.9990511029803457, 1);
INSERT INTO building(id, name, level) VALUES (416, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 416, 14.971045454545454, 16.46815, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 416, 7.984554545454546, 8.78301, 1);
INSERT INTO building(id, name, level) VALUES (417, "building_barrackslevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 417, 8.52192, 8.045069833536447, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 417, 8.52192, 8.045069833536447, 9);
INSERT INTO building(id, name, level) VALUES (33554985, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33554985, 29.999999999999996, 35.166939813716475, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33554985, 20.0, 49.04348604152551, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 33554985, 59.99999999999999, 59.99999999999999, 1);
INSERT INTO building(id, name, level) VALUES (588, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 588, 30.0, 11.70933081502774, 3);
INSERT INTO building(id, name, level) VALUES (589, "building_construction_sectorlevel", 3);
INSERT INTO building(id, name, level) VALUES (590, "building_textile_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 590, 200.00000000000003, 282.75924538126145, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 590, 25.000000000000004, 0.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 590, 300.0, 150.0, 5);
INSERT INTO building(id, name, level) VALUES (591, "building_arms_industrylevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 591, 198.33, 486.33972933078775, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 591, 99.165, 134.39383971759753, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 591, 148.74749579831933, 148.74749579831933, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 591, 148.74749579831933, 148.74749579831933, 10);
INSERT INTO building(id, name, level) VALUES (592, "building_wheat_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 592, 14.063392857142855, 4.683478281562904, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 592, 98.44379464285714, 32.7843628381814, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 592, 22.501437499999998, 7.49356822394886, 3);
INSERT INTO building(id, name, level) VALUES (593, "building_barrackslevel", 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 593, 1.64592, 1.553821361901345, 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 593, 1.64592, 1.553821361901345, 16);
INSERT INTO building(id, name, level) VALUES (694, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 694, 40.0, 15.612441086703653, 4);
INSERT INTO building(id, name, level) VALUES (695, "building_paper_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 695, 149.10600000000002, 174.78672426213367, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 695, 198.80800000000002, 198.80800000000002, 5);
INSERT INTO building(id, name, level) VALUES (696, "building_glassworkslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 696, 149.9505, 175.77667361788977, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 696, 74.97524561403509, 0.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 696, 49.9835, 24.99175, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 696, 124.9587456140351, 62.47937280701755, 5);
INSERT INTO building(id, name, level) VALUES (697, "building_textile_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 697, 240.00000000000003, 339.3110944575137, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 697, 80.0, 74.45265454545454, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 697, 200.0, 193.06581818181817, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 697, 160.0, 154.45265454545452, 8);
INSERT INTO building(id, name, level) VALUES (698, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 698, 4.99, 4.710780958909128, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 698, 4.99, 4.710780958909128, 10);
INSERT INTO building(id, name, level) VALUES (699, "building_fishing_wharflevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 699, 17.0, 10.149253731343283, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 699, 170.0, 101.49253731343282, 4);
INSERT INTO building(id, name, level) VALUES (700, "building_silk_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 700, 98.435, 102.3724, 5);
INSERT INTO building(id, name, level) VALUES (701, "building_wheat_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 701, 9.866297297297296, 3.2857354964570393, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 701, 69.0640990990991, 23.000154475671348, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 701, 15.78607207207207, 5.257175594236848, 2);
INSERT INTO building(id, name, level) VALUES (702, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 702, 15.0, 8.955223880597012, 3);
INSERT INTO building(id, name, level) VALUES (857, "building_wheat_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 857, 14.066999999999998, 4.6846795546412805, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 857, 84.402, 28.10807732784769, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 857, 25.32059821428571, 8.432422603664662, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 857, 16.880392857142855, 5.6216130868109655, 3);
INSERT INTO building(id, name, level) VALUES (858, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 858, 9.7235, 6.918944029190545, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 858, 4.861745454545455, 3.9634784295695376, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 858, 38.894, 29.691816599163797, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 858, 4.861745454545455, 3.711473604879464, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 858, 14.585245454545452, 11.134427754670412, 1);
INSERT INTO building(id, name, level) VALUES (859, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 859, 9.999999999999998, 8.152377508501417, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 859, 79.99999999999999, 65.21902006801133, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 859, 19.999999999999996, 16.304755017002833, 2);
INSERT INTO building(id, name, level) VALUES (860, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 860, 3.517, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 860, 3.517, 3.3202037339646098, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 860, 3.517, 3.3202037339646098, 5);
INSERT INTO building(id, name, level) VALUES (861, "building_fishing_wharflevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 861, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (862, "building_wheat_farmlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 862, 4.686496, 1.5607259539424287, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 862, 32.805496, 10.9250896702258, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 862, 7.498399999999999, 2.4971636576755656, 1);
INSERT INTO building(id, name, level) VALUES (863, "building_barrackslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 863, 0.64896, 0.0, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 863, 0.64896, 0.6126469761710757, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 863, 0.64896, 0.6126469761710757, 6);
INSERT INTO building(id, name, level) VALUES (864, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 864, 5.0, 2.9850746268656714, 1);
INSERT INTO building(id, name, level) VALUES (865, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 865, 40.0, 15.612441086703653, 4);
INSERT INTO building(id, name, level) VALUES (866, "building_wheat_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 866, 9.375896825396826, 3.1224192908482693, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 866, 65.63129365079365, 21.85694032206804, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 866, 15.001436507936509, 4.995871393970247, 2);
INSERT INTO building(id, name, level) VALUES (867, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 867, 8.4294, 5.998102205960691, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 867, 33.7176, 23.992408823842766, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 867, 4.214696, 2.9990482567031704, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 867, 4.214696, 2.9990482567031704, 1);
INSERT INTO building(id, name, level) VALUES (868, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 868, 20.000000000000004, 16.30475501700284, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 868, 240.0, 195.65706020403405, 4);
INSERT INTO building(id, name, level) VALUES (869, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 869, 10.0, 5.970149253731343, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 869, 100.0, 59.70149253731343, 2);
INSERT INTO building(id, name, level) VALUES (870, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 870, 5.0, 2.9850746268656714, 1);
INSERT INTO building(id, name, level) VALUES (871, "building_barrackslevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 871, 9.09392, 0.0, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 871, 9.09392, 8.585063161892364, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 871, 9.09392, 8.585063161892364, 11);
INSERT INTO building(id, name, level) VALUES (872, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 872, 30.0, 11.70933081502774, 3);
INSERT INTO building(id, name, level) VALUES (873, "building_wheat_farmlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 873, 5.0, 1.6651309997303196, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 873, 1.0, 0.8152377508501419, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 873, 29.999999999999996, 17.223959261943087, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 873, 9.0, 5.167187778582926, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 873, 5.999999999999999, 3.444791852388617, 1);
INSERT INTO building(id, name, level) VALUES (874, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 874, 9.2549, 6.585502658071226, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 874, 37.0196, 26.342010632284904, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 874, 4.6274454545454535, 3.292748094629731, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 874, 4.6274454545454535, 3.292748094629731, 1);
INSERT INTO building(id, name, level) VALUES (875, "building_barrackslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 875, 6.75598, 6.377944277108395, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 875, 6.75598, 6.377944277108395, 7);
INSERT INTO building(id, name, level) VALUES (876, "building_tooling_workshopslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 876, 89.99999999999999, 105.5008194411494, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 876, 60.0, 147.13045812457653, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 876, 179.99999999999997, 179.99999999999997, 3);
INSERT INTO building(id, name, level) VALUES (877, "building_shipyardslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 877, 39.99999999999999, 56.55184907625227, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 877, 79.99999999999999, 93.77850616991059, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 877, 29.999999999999996, 29.999999999999996, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 877, 39.99999999999999, 39.99999999999999, 2);
INSERT INTO building(id, name, level) VALUES (878, "building_fishing_wharflevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 878, 100.0, 103.0, 4);
INSERT INTO building(id, name, level) VALUES (879, "building_naval_baselevel", 30);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 879, 44.1078, 8.06941715814529, 30);
INSERT INTO building(id, name, level) VALUES (880, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 880, 15.0, 8.955223880597012, 3);
INSERT INTO building(id, name, level) VALUES (1060, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1060, 60.0, 23.41866163005548, 6);
INSERT INTO building(id, name, level) VALUES (1061, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1061, 9.999999999999998, 3.330261999460639, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1061, 2.0, 1.6304755017002839, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1061, 59.99999999999999, 34.44791852388617, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1061, 29.999999999999996, 17.223959261943087, 2);
INSERT INTO building(id, name, level) VALUES (1062, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1062, 9.2549, 6.585502658071226, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1062, 37.0196, 26.342010632284904, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1062, 4.6274454545454535, 3.292748094629731, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1062, 4.6274454545454535, 3.292748094629731, 1);
INSERT INTO building(id, name, level) VALUES (1063, "building_barrackslevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1063, 7.11194, 6.713986279139114, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1063, 7.11194, 6.713986279139114, 11);
INSERT INTO building(id, name, level) VALUES (1067, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1067, 50.0, 19.515551358379568, 5);
INSERT INTO building(id, name, level) VALUES (1068, "building_wheat_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1068, 14.063392857142855, 4.683478281562904, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1068, 98.44379464285714, 32.7843628381814, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1068, 22.501437499999998, 7.49356822394886, 3);
INSERT INTO building(id, name, level) VALUES (1069, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1069, 18.50979279279279, 13.17100018771511, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1069, 74.03919819819819, 52.68401998246298, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1069, 9.25489189189189, 6.585496888590465, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1069, 9.25489189189189, 6.585496888590465, 2);
INSERT INTO building(id, name, level) VALUES (1070, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1070, 6.09, 5.749229667285889, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1070, 6.09, 5.749229667285889, 10);
INSERT INTO building(id, name, level) VALUES (1071, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1071, 60.0, 23.41866163005548, 6);
INSERT INTO building(id, name, level) VALUES (1072, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (1073, "building_iron_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1073, 45.0, 40.39880251767844, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1073, 45.0, 36.68569878825639, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1073, 180.0, 154.16900261186964, 3);
INSERT INTO building(id, name, level) VALUES (1074, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1074, 39.99999999999999, 28.46277175581033, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (8, 1074, 59.99999999999999, 28.518119767444926, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1074, 19.999999999999996, 49.043486041525505, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1074, 49.99999999999999, 6.569417182877554, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1074, 90.0, 52.16084175773006, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1074, 119.99999999999999, 69.54778901030674, 2);
INSERT INTO building(id, name, level) VALUES (1075, "building_wheat_farmlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1075, 28.130695652173912, 9.36825867488274, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1075, 196.91489565217393, 65.57781941181919, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1075, 45.009113043478266, 14.989213879812386, 6);
INSERT INTO building(id, name, level) VALUES (1076, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1076, 27.764696428571426, 19.756505432894773, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1076, 111.05879464285714, 79.02602808487637, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1076, 13.882348214285713, 9.878252716447387, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1076, 13.882348214285713, 9.878252716447387, 3);
INSERT INTO building(id, name, level) VALUES (1077, "building_barrackslevel", 27);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1077, 27.0, 25.489195569247784, 27);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1077, 27.0, 25.489195569247784, 27);
INSERT INTO building(id, name, level) VALUES (1078, "building_wheat_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1078, 13.908598214285712, 4.631927609880181, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1078, 97.36019642857141, 32.42349624260948, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1078, 22.253758928571425, 7.411084770497933, 3);
INSERT INTO building(id, name, level) VALUES (1079, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1079, 18.537198198198197, 13.190501032688347, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1079, 74.14879279279279, 52.76200413075339, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1079, 9.268594594594594, 6.595247311077084, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1079, 9.268594594594594, 6.595247311077084, 2);
INSERT INTO building(id, name, level) VALUES (1080, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1080, 15.0, 14.160664205137659, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1080, 15.0, 14.160664205137659, 15);
INSERT INTO building(id, name, level) VALUES (1081, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1081, 39.99999999999999, 28.46277175581033, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (8, 1081, 59.99999999999999, 28.518119767444926, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1081, 19.999999999999996, 49.043486041525505, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1081, 49.99999999999999, 6.569417182877554, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1081, 90.0, 52.16084175773006, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1081, 119.99999999999999, 69.54778901030674, 2);
INSERT INTO building(id, name, level) VALUES (1082, "building_wheat_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1082, 14.090249999999997, 4.692422413790026, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1082, 98.63175, 32.84695689653019, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1082, 22.544392857142856, 7.507873483305472, 3);
INSERT INTO building(id, name, level) VALUES (1083, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1083, 27.78029464285714, 19.767604643220082, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1083, 111.12119642857142, 79.07043127947487, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1083, 13.890142857142855, 9.883799144961408, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1083, 13.890142857142855, 9.883799144961408, 3);
INSERT INTO building(id, name, level) VALUES (1084, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1084, 10.0, 9.440442803425105, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1084, 10.0, 9.440442803425105, 10);
INSERT INTO building(id, name, level) VALUES (1085, "building_wheat_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1085, 14.063392857142855, 4.683478281562904, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1085, 98.44379464285714, 32.7843628381814, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1085, 22.501437499999998, 7.49356822394886, 3);
INSERT INTO building(id, name, level) VALUES (1086, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1086, 25.292098214285712, 17.99708046746881, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1086, 101.16839285714285, 71.98832186987524, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1086, 12.646044642857142, 8.998537057085771, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1086, 12.646044642857142, 8.998537057085771, 3);
INSERT INTO building(id, name, level) VALUES (1087, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1087, 10.0, 9.440442803425105, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1087, 10.0, 9.440442803425105, 10);
INSERT INTO building(id, name, level) VALUES (1088, "building_wheat_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1088, 14.063392857142855, 4.683478281562904, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1088, 98.44379464285714, 32.7843628381814, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1088, 22.501437499999998, 7.49356822394886, 3);
INSERT INTO building(id, name, level) VALUES (1089, "building_livestock_ranchlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1089, 37.019592920353986, 26.34200559462619, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1089, 148.0783982300885, 105.36804126972496, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1089, 18.509796460176993, 13.171002797313095, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1089, 18.509796460176993, 13.171002797313095, 4);
INSERT INTO building(id, name, level) VALUES (1090, "building_barrackslevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1090, 11.0, 10.384487083767615, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1090, 11.0, 10.384487083767615, 11);
INSERT INTO building(id, name, level) VALUES (1091, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1091, 50.0, 19.515551358379568, 5);
INSERT INTO building(id, name, level) VALUES (1092, "building_furniture_manufacturieslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1092, 30.0, 42.41388680718921, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1092, 60.0, 70.33387962743295, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1092, 30.0, 40.65764323630239, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1092, 15.0, 12.228566262752128, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1092, 134.99999999999997, 128.76427409119225, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1092, 60.0, 57.228566262752125, 3);
INSERT INTO building(id, name, level) VALUES (1093, "building_iron_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1093, 59.99999999999999, 53.86507002357125, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1093, 59.99999999999999, 48.91426505100851, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1093, 239.99999999999997, 205.55867014915952, 4);
INSERT INTO building(id, name, level) VALUES (1094, "building_wheat_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1094, 9.302999999999999, 3.0981427380982325, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1094, 55.818, 18.588856428589395, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1094, 16.745396396396394, 5.576655728482404, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1094, 11.163594594594594, 3.7177694855762575, 2);
INSERT INTO building(id, name, level) VALUES (1095, "building_barrackslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1095, 6.0, 5.664265682055064, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1095, 6.0, 5.664265682055064, 6);
INSERT INTO building(id, name, level) VALUES (1096, "building_wheat_farmlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1096, 4.897745454545454, 1.6310775570303802, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1096, 0.9795454545454544, 0.7985624332191162, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1096, 29.386499999999998, 16.871729295036353, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1096, 8.815945454545455, 5.061516178820109, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1096, 5.877299999999999, 3.37434585900727, 1);
INSERT INTO building(id, name, level) VALUES (1097, "building_logging_camplevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1097, 30.000000000000004, 24.45713252550426, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1097, 240.00000000000003, 195.65706020403408, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1097, 60.00000000000001, 48.91426505100852, 6);
INSERT INTO building(id, name, level) VALUES (1098, "building_barrackslevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1098, 11.0, 10.384487083767615, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1098, 11.0, 10.384487083767615, 11);
INSERT INTO building(id, name, level) VALUES (1099, "building_barrackslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1099, 6.0, 5.664265682055064, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1099, 6.0, 5.664265682055064, 6);
INSERT INTO building(id, name, level) VALUES (1100, "building_wheat_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1100, 9.375891891891891, 3.1224176478618686, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1100, 65.6312972972973, 21.856941536449295, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1100, 15.00143243243243, 4.995870036720611, 2);
INSERT INTO building(id, name, level) VALUES (1101, "building_logging_camplevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1101, 30.000000000000004, 24.45713252550426, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1101, 240.00000000000003, 195.65706020403408, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1101, 60.00000000000001, 48.91426505100852, 6);
INSERT INTO building(id, name, level) VALUES (1102, "building_iron_minelevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1102, 90.0, 80.79760503535688, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1102, 90.0, 73.37139757651278, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1102, 360.0, 308.3380052237393, 6);
INSERT INTO building(id, name, level) VALUES (1103, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1103, 50.0, 19.515551358379568, 5);
INSERT INTO building(id, name, level) VALUES (1104, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1104, 10.0, 14.13796226906307, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1104, 20.0, 23.44462654247765, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1104, 10.0, 13.55254774543413, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1104, 5.0, 4.076188754250709, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1104, 44.99999999999999, 42.92142469706409, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1104, 20.0, 19.07618875425071, 1);
INSERT INTO building(id, name, level) VALUES (1105, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1105, 20.0, 16.304755017002837, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1105, 160.0, 130.4380401360227, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1105, 40.0, 32.60951003400567, 4);
INSERT INTO building(id, name, level) VALUES (1106, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1106, 14.999999999999998, 16.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1106, 8.0, 8.8, 1);
INSERT INTO building(id, name, level) VALUES (1107, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1107, 9.256799999999998, 6.586854639729627, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1107, 37.02719999999999, 26.34741855891851, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1107, 4.628399999999999, 3.2934273198648136, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1107, 4.628399999999999, 3.2934273198648136, 1);
INSERT INTO building(id, name, level) VALUES (1108, "building_barrackslevel", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1108, 13.0, 12.272575644452639, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1108, 13.0, 12.272575644452639, 13);
INSERT INTO building(id, name, level) VALUES (1109, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1109, 14.999999999999998, 16.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1109, 8.0, 8.8, 1);
INSERT INTO building(id, name, level) VALUES (1110, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1110, 18.531198198198197, 13.186231616924976, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1110, 74.12479279279279, 52.74492646769991, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1110, 9.265594594594594, 6.593112603195399, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1110, 9.265594594594594, 6.593112603195399, 2);
INSERT INTO building(id, name, level) VALUES (1111, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1111, 20.000000000000004, 16.30475501700284, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1111, 160.00000000000003, 130.43804013602272, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1111, 40.00000000000001, 32.60951003400568, 4);
INSERT INTO building(id, name, level) VALUES (2833, "building_subsistence_farmslevel", 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2833, 158.94314545454543, 174.83746, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2833, 31.78862727272727, 34.96749, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2833, 31.78862727272727, 34.96749, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2833, 31.78862727272727, 34.96749, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2833, 31.78862727272727, 34.96749, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2833, 31.78862727272727, 34.96749, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2833, 44.50408181818182, 48.95449, 107);
INSERT INTO building(id, name, level) VALUES (2834, "building_urban_centerlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2834, 39.993598130841114, 46.88174861337511, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2834, 79.98719626168223, 71.80859879374417, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2834, 439.9295981308411, 417.43845413841484, 8);
INSERT INTO building(id, name, level) VALUES (2952, "building_subsistence_farmslevel", 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2952, 120.96114545454546, 133.05726, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2952, 24.192227272727273, 26.61145, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2952, 24.192227272727273, 26.61145, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2952, 24.192227272727273, 26.61145, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2952, 24.192227272727273, 26.61145, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2952, 24.192227272727273, 26.61145, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2952, 33.86911818181818, 37.25603, 86);
INSERT INTO building(id, name, level) VALUES (2954, "building_subsistence_farmslevel", 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2954, 102.07274545454544, 112.28002, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2954, 20.414545454545454, 22.456, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2954, 20.414545454545454, 22.456, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2954, 20.414545454545454, 22.456, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2954, 20.414545454545454, 22.456, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2954, 20.414545454545454, 22.456, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2954, 28.580363636363636, 31.4384, 78);
INSERT INTO building(id, name, level) VALUES (2955, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2955, 10.0, 11.722313271238825, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2955, 20.0, 17.95502334119042, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2955, 10.0, 2.1760270359614635, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2955, 140.0, 98.71651396393115, 2);
INSERT INTO building(id, name, level) VALUES (2956, "building_subsistence_farmslevel", 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2956, 42.036, 46.2396, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2956, 8.4072, 9.24792, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2956, 8.4072, 9.24792, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2956, 8.4072, 9.24792, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2956, 8.4072, 9.24792, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2956, 8.4072, 9.24792, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2956, 11.770072727272726, 12.94708, 32);
INSERT INTO building(id, name, level) VALUES (16780908, "building_naval_baselevel", 29);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 16780908, 58.0, 10.61096212398775, 29);
INSERT INTO building(id, name, level) VALUES (3742, "building_subsistence_farmslevel", 143);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3742, 264.8503, 291.33533, 143);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3742, 52.970054545454545, 58.26706, 143);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3742, 52.970054545454545, 58.26706, 143);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3742, 52.970054545454545, 58.26706, 143);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3742, 52.970054545454545, 58.26706, 143);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3742, 52.970054545454545, 58.26706, 143);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3742, 74.15808181818181, 81.57389, 143);
INSERT INTO building(id, name, level) VALUES (3743, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3743, 10.0, 11.722313271238825, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3743, 20.0, 17.95502334119042, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3743, 10.0, 2.1760270359614635, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3743, 140.0, 98.71651396393115, 2);
INSERT INTO building(id, name, level) VALUES (3745, "building_subsistence_farmslevel", 189);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3745, 353.58591818181816, 388.94451, 189);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3745, 70.71718181818181, 77.7889, 189);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3745, 70.71718181818181, 77.7889, 189);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3745, 70.71718181818181, 77.7889, 189);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3745, 70.71718181818181, 77.7889, 189);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3745, 70.71718181818181, 77.7889, 189);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3745, 99.00405454545454, 108.90446, 189);
INSERT INTO building(id, name, level) VALUES (3746, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3746, 5.0, 5.8611566356194125, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3746, 10.0, 8.97751167059521, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3746, 5.0, 1.0880135179807318, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3746, 70.0, 49.358256981965575, 1);
INSERT INTO building(id, name, level) VALUES (3747, "building_subsistence_farmslevel", 154);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3747, 267.05139999999994, 293.75654, 154);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3747, 53.41027272727273, 58.7513, 154);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3747, 53.41027272727273, 58.7513, 154);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3747, 53.41027272727273, 58.7513, 154);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3747, 53.41027272727273, 58.7513, 154);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3747, 53.41027272727273, 58.7513, 154);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3747, 74.7743909090909, 82.25183, 154);
INSERT INTO building(id, name, level) VALUES (3748, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3748, 10.0, 11.722313271238825, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3748, 20.0, 17.95502334119042, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3748, 10.0, 2.1760270359614635, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3748, 140.0, 98.71651396393115, 2);
INSERT INTO building(id, name, level) VALUES (3749, "building_subsistence_farmslevel", 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3749, 161.1465727272727, 177.26123, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3749, 32.22930909090909, 35.45224, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3749, 32.22930909090909, 35.45224, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3749, 32.22930909090909, 35.45224, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3749, 32.22930909090909, 35.45224, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3749, 32.22930909090909, 35.45224, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3749, 45.12103636363636, 49.63314, 83);
INSERT INTO building(id, name, level) VALUES (3750, "building_subsistence_farmslevel", 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3750, 35.0632, 43.829, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3750, 7.01264, 8.7658, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3750, 7.01264, 8.7658, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3750, 7.01264, 8.7658, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3750, 7.01264, 8.7658, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3750, 7.01264, 8.7658, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3750, 9.817696, 12.27212, 41);
INSERT INTO building(id, name, level) VALUES (3751, "building_subsistence_farmslevel", 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3751, 59.262896, 74.07862, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3751, 11.852576000000001, 14.81572, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3751, 11.852576000000001, 14.81572, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3751, 11.852576000000001, 14.81572, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3751, 11.852576000000001, 14.81572, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3751, 11.852576000000001, 14.81572, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3751, 16.593608, 20.74201, 57);
INSERT INTO building(id, name, level) VALUES (3752, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3752, 10.0, 11.722313271238825, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3752, 20.0, 17.95502334119042, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3752, 10.0, 2.1760270359614635, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3752, 140.0, 98.71651396393115, 2);
INSERT INTO building(id, name, level) VALUES (3753, "building_subsistence_farmslevel", 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3753, 30.619199999999996, 33.68112, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3753, 6.1238363636363635, 6.73622, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3753, 6.1238363636363635, 6.73622, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3753, 6.1238363636363635, 6.73622, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3753, 6.1238363636363635, 6.73622, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3753, 6.1238363636363635, 6.73622, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3753, 8.573372727272726, 9.43071, 32);
INSERT INTO building(id, name, level) VALUES (3754, "building_subsistence_farmslevel", 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3754, 204.90569999999997, 225.39627, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3754, 40.98113636363636, 45.07925, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3754, 40.98113636363636, 45.07925, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3754, 40.98113636363636, 45.07925, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3754, 40.98113636363636, 45.07925, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3754, 40.98113636363636, 45.07925, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3754, 57.37359090909091, 63.11095, 123);
INSERT INTO building(id, name, level) VALUES (3755, "building_subsistence_farmslevel", 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3755, 317.1645, 348.88095, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3755, 63.4329, 69.77619, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3755, 63.4329, 69.77619, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3755, 63.4329, 69.77619, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3755, 63.4329, 69.77619, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3755, 63.4329, 69.77619, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3755, 88.80605454545454, 97.68666, 180);
INSERT INTO building(id, name, level) VALUES (3756, "building_subsistence_farmslevel", 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3756, 35.414248, 44.26781, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3756, 7.082848, 8.85356, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3756, 7.082848, 8.85356, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3756, 7.082848, 8.85356, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3756, 7.082848, 8.85356, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3756, 7.082848, 8.85356, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3756, 9.915984, 12.39498, 46);
INSERT INTO building(id, name, level) VALUES (3757, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3757, 20.0, 23.44462654247765, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3757, 40.0, 35.91004668238084, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3757, 20.0, 4.352054071922927, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3757, 279.99999999999994, 197.43302792786227, 4);
INSERT INTO building(id, name, level) VALUES (3758, "building_subsistence_farmslevel", 157);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3758, 280.3666727272727, 308.40334, 157);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3758, 56.07332727272727, 61.68066, 157);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3758, 56.07332727272727, 61.68066, 157);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3758, 56.07332727272727, 61.68066, 157);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3758, 56.07332727272727, 61.68066, 157);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3758, 56.07332727272727, 61.68066, 157);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3758, 78.50266363636364, 86.35293, 157);
INSERT INTO building(id, name, level) VALUES (3759, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3759, 20.0, 23.44462654247765, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3759, 40.0, 35.91004668238084, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3759, 220.0, 208.7526283765473, 4);
INSERT INTO building(id, name, level) VALUES (3760, "building_subsistence_farmslevel", 265);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3760, 459.51661818181816, 505.46828, 265);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3760, 91.90331818181816, 101.09365, 265);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3760, 91.90331818181816, 101.09365, 265);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3760, 91.90331818181816, 101.09365, 265);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3760, 91.90331818181816, 101.09365, 265);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3760, 91.90331818181816, 101.09365, 265);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3760, 128.6646545454545, 141.53112, 265);
INSERT INTO building(id, name, level) VALUES (3761, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3761, 34.991594339622644, 41.01824307091639, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3761, 69.98319811320755, 62.82749778068973, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3761, 384.9075943396226, 365.2294181840457, 7);
INSERT INTO building(id, name, level) VALUES (3762, "building_subsistence_farmslevel", 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3762, 196.225, 215.8475, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3762, 39.245, 43.1695, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3762, 39.245, 43.1695, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3762, 39.245, 43.1695, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3762, 39.245, 43.1695, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3762, 39.245, 43.1695, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3762, 54.943, 60.4373, 100);
INSERT INTO building(id, name, level) VALUES (3763, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3763, 10.0, 11.722313271238825, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3763, 20.0, 17.95502334119042, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3763, 10.0, 2.1760270359614635, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3763, 140.0, 98.71651396393115, 2);
INSERT INTO building(id, name, level) VALUES (3764, "building_subsistence_farmslevel", 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3764, 252.85371818181818, 278.13909, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3764, 50.57073636363636, 55.62781, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3764, 50.57073636363636, 55.62781, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3764, 50.57073636363636, 55.62781, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3764, 50.57073636363636, 55.62781, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3764, 50.57073636363636, 55.62781, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3764, 70.79903636363636, 77.87894, 159);
INSERT INTO building(id, name, level) VALUES (3765, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3765, 15.0, 17.583469906858237, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3765, 30.0, 26.932535011785628, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3765, 165.0, 156.56447128241047, 3);
INSERT INTO building(id, name, level) VALUES (3766, "building_subsistence_farmslevel", 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3766, 57.76487272727272, 63.54136, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3766, 11.552972727272726, 12.70827, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3766, 11.552972727272726, 12.70827, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3766, 11.552972727272726, 12.70827, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3766, 11.552972727272726, 12.70827, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3766, 11.552972727272726, 12.70827, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3766, 16.174163636363634, 17.79158, 49);
INSERT INTO building(id, name, level) VALUES (3767, "building_subsistence_farmslevel", 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3767, 55.52834545454545, 61.08118, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3767, 11.105663636363635, 12.21623, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3767, 11.105663636363635, 12.21623, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3767, 11.105663636363635, 12.21623, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3767, 11.105663636363635, 12.21623, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3767, 11.105663636363635, 12.21623, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3767, 15.547936363636364, 17.10273, 41);
INSERT INTO building(id, name, level) VALUES (3768, "building_subsistence_farmslevel", 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3768, 196.5026, 216.15286, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3768, 39.30051818181818, 43.23057, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3768, 39.30051818181818, 43.23057, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3768, 39.30051818181818, 43.23057, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3768, 39.30051818181818, 43.23057, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3768, 39.30051818181818, 43.23057, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3768, 55.020727272727264, 60.5228, 97);
INSERT INTO building(id, name, level) VALUES (3769, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3769, 35.0, 41.028096449335884, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3769, 70.0, 62.84258169416646, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3769, 35.0, 7.616094625865121, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3769, 489.99999999999994, 345.507798873759, 7);
INSERT INTO building(id, name, level) VALUES (3770, "building_subsistence_farmslevel", 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3770, 46.36281818181818, 50.9991, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3770, 9.272563636363635, 10.19982, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3770, 9.272563636363635, 10.19982, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3770, 9.272563636363635, 10.19982, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3770, 9.272563636363635, 10.19982, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3770, 9.272563636363635, 10.19982, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3770, 12.981590909090908, 14.27975, 31);
INSERT INTO building(id, name, level) VALUES (3771, "building_subsistence_farmslevel", 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3771, 46.69534545454545, 51.36488, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3771, 9.339063636363637, 10.27297, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3771, 9.339063636363637, 10.27297, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3771, 9.339063636363637, 10.27297, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3771, 9.339063636363637, 10.27297, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3771, 9.339063636363637, 10.27297, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3771, 13.074690909090908, 14.38216, 38);
INSERT INTO building(id, name, level) VALUES (3772, "building_subsistence_farmslevel", 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3772, 87.21999999999998, 95.942, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3772, 17.444, 19.1884, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3772, 17.444, 19.1884, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3772, 17.444, 19.1884, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3772, 17.444, 19.1884, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3772, 17.444, 19.1884, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3772, 24.421599999999998, 26.86376, 56);
INSERT INTO building(id, name, level) VALUES (16781035, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (3841, "building_subsistence_farmslevel", 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3841, 116.49591818181817, 128.14551, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3841, 23.299181818181818, 25.6291, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3841, 23.299181818181818, 25.6291, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3841, 23.299181818181818, 25.6291, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3841, 23.299181818181818, 25.6291, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3841, 23.299181818181818, 25.6291, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3841, 32.618854545454546, 35.88074, 91);
INSERT INTO building(id, name, level) VALUES (3867, "building_trade_centerlevel", 53);
INSERT INTO building(id, name, level) VALUES (16781104, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16781104, 19.83, 48.626616410172545, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16781104, 9.915, 13.437351089597938, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 16781104, 14.872499999999997, 14.872499999999997, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 16781104, 14.872499999999997, 14.872499999999997, 1);
INSERT INTO building(id, name, level) VALUES (33558334, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33558334, 5.0, 4.076188754250709, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 33558334, 40.0, 32.60951003400567, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 33558334, 10.0, 8.152377508501418, 1);
INSERT INTO building(id, name, level) VALUES (3949, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4008, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4010, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4011, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (16781242, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (67113119, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 67113119, 15.0, 12.228566262752128, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 67113119, 60.0, 48.91426505100851, 1);
INSERT INTO building(id, name, level) VALUES (4487, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4489, "building_conscription_centerlevel", 12);
INSERT INTO building(id, name, level) VALUES (4490, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4491, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4492, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4493, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4494, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4495, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4496, "building_conscription_centerlevel", 11);
INSERT INTO building(id, name, level) VALUES (4497, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4498, "building_conscription_centerlevel", 11);
INSERT INTO building(id, name, level) VALUES (4499, "building_conscription_centerlevel", 19);
INSERT INTO building(id, name, level) VALUES (4500, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4501, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4502, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4503, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4504, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4505, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4506, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4539, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (33559062, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33559062, 10.0, 14.13796226906307, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559062, 20.0, 23.44462654247765, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 33559062, 10.0, 13.55254774543413, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33559062, 5.0, 4.076188754250709, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 33559062, 44.99999999999999, 42.92142469706409, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 33559062, 20.0, 19.07618875425071, 1);
INSERT INTO building(id, name, level) VALUES (4666, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (50336326, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50336326, 9.999999999999998, 8.152377508501417, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 50336326, 79.99999999999999, 65.21902006801133, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 50336326, 19.999999999999996, 16.304755017002833, 2);
INSERT INTO building(id, name, level) VALUES (4691, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4691, 29.999999999999996, 35.166939813716475, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4691, 20.0, 49.04348604152551, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4691, 59.99999999999999, 59.99999999999999, 1);
INSERT INTO building(id, name, level) VALUES (16781992, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (100668094, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (16782017, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (184554198, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 184554198, 9.999999999999998, 8.152377508501417, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 184554198, 79.99999999999999, 65.21902006801133, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 184554198, 19.999999999999996, 16.304755017002833, 2);
INSERT INTO building(id, name, level) VALUES (4824, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (4826, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (4833, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (4834, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4834, 59.99999999999999, 70.33387962743295, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4834, 39.99999999999999, 98.08697208305101, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4834, 119.99999999999999, 119.99999999999999, 2);
INSERT INTO building(id, name, level) VALUES (4838, "building_construction_sectorlevel", 2);
INSERT INTO building(id, name, level) VALUES (83890920, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 83890920, 20.0, 7.806220543351826, 2);
INSERT INTO building(id, name, level) VALUES (16782059, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (4845, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (16782065, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (4850, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (4853, "building_construction_sectorlevel", 1);
INSERT INTO building(id, name, level) VALUES (4854, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4854, 30.0, 24.457132525504257, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 4854, 120.0, 97.82853010201703, 2);
INSERT INTO building(id, name, level) VALUES (4855, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 4855, 10.0, 3.903110271675913, 2);
INSERT INTO building(id, name, level) VALUES (4856, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4856, 5.0, 5.8611566356194125, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 4856, 10.0, 8.97751167059521, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 4856, 5.0, 1.0880135179807318, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4856, 70.0, 49.358256981965575, 1);
INSERT INTO building(id, name, level) VALUES (4857, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 4857, 10.0, 3.903110271675913, 2);
INSERT INTO building(id, name, level) VALUES (4906, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 4906, 20.0, 7.806220543351826, 2);
INSERT INTO building(id, name, level) VALUES (4938, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4938, 5.0, 5.8611566356194125, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 4938, 10.0, 8.97751167059521, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 4938, 5.0, 1.0880135179807318, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4938, 70.0, 49.358256981965575, 1);
INSERT INTO building(id, name, level) VALUES (4940, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4940, 59.99999999999999, 70.33387962743295, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4940, 39.99999999999999, 98.08697208305101, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4940, 119.99999999999999, 119.99999999999999, 2);
INSERT INTO building(id, name, level) VALUES (4943, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4943, 59.99999999999999, 70.33387962743295, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4943, 39.99999999999999, 98.08697208305101, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4943, 119.99999999999999, 119.99999999999999, 2);
INSERT INTO building(id, name, level) VALUES (4947, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4947, 29.999999999999996, 35.166939813716475, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4947, 20.0, 49.04348604152551, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4947, 59.99999999999999, 59.99999999999999, 1);
INSERT INTO building(id, name, level) VALUES (16782185, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16782185, 5.0, 1.9515551358379566, 1);
INSERT INTO building(id, name, level) VALUES (4980, "building_iron_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 4980, 59.99999999999999, 53.86507002357125, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4980, 59.99999999999999, 48.91426505100851, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 4980, 239.99999999999997, 205.55867014915952, 4);
INSERT INTO building(id, name, level) VALUES (16782280, "building_coal_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782280, 45.0, 36.68569878825639, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 16782280, 180.0, 146.74279515302555, 3);
INSERT INTO building(id, name, level) VALUES (16782309, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782309, 15.0, 12.228566262752128, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16782309, 120.0, 97.82853010201703, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 16782309, 30.0, 24.457132525504257, 3);
INSERT INTO building(id, name, level) VALUES (5267, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (100668646, "building_naval_baselevel", 30);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 100668646, 56.5896, 10.35293279675202, 30);
INSERT INTO building(id, name, level) VALUES (100668683, "building_naval_baselevel", 22);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 100668683, 5.28396, 0.9666879211149365, 22);
INSERT INTO building(id, name, level) VALUES (33559964, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559964, 59.99999999999999, 70.33387962743295, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33559964, 39.99999999999999, 98.08697208305101, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 33559964, 119.99999999999999, 119.99999999999999, 2);
INSERT INTO building(id, name, level) VALUES (5564, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5564, 5.0, 5.8611566356194125, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5564, 10.0, 8.97751167059521, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5564, 5.0, 1.0880135179807318, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5564, 70.0, 49.358256981965575, 1);
INSERT INTO building(id, name, level) VALUES (5591, "building_sulfur_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5591, 0.0913, 0.08196468155253427, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5591, 0.0913, 0.07443120665261796, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 5591, 0.3652, 0.3127917764103045, 1);
INSERT INTO building(id, name, level) VALUES (33560078, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33560078, 29.999999999999996, 35.166939813716475, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33560078, 20.0, 49.04348604152551, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 33560078, 59.99999999999999, 59.99999999999999, 1);
INSERT INTO building(id, name, level) VALUES (16782887, "building_trade_centerlevel", 37);
INSERT INTO building(id, name, level) VALUES (67114586, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 67114586, 9.999999999999998, 8.152377508501417, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 67114586, 79.99999999999999, 65.21902006801133, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 67114586, 19.999999999999996, 16.304755017002833, 2);
INSERT INTO building(id, name, level) VALUES (5726, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5726, 5.0, 5.8611566356194125, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5726, 10.0, 8.97751167059521, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5726, 55.0, 52.18815709413683, 1);
INSERT INTO building(id, name, level) VALUES (16782958, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782958, 30.0, 24.457132525504257, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 16782958, 120.0, 97.82853010201703, 2);
INSERT INTO building(id, name, level) VALUES (134223473, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 134223473, 9.999999999999998, 8.152377508501417, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 134223473, 79.99999999999999, 65.21902006801133, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 134223473, 19.999999999999996, 16.304755017002833, 2);
INSERT INTO building(id, name, level) VALUES (5791, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5791, 29.999999999999996, 35.166939813716475, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5791, 20.0, 49.04348604152551, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 5791, 59.99999999999999, 59.99999999999999, 1);
INSERT INTO building(id, name, level) VALUES (16783022, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783022, 30.0, 24.457132525504257, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 16783022, 120.0, 97.82853010201703, 2);
INSERT INTO building(id, name, level) VALUES (5817, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5817, 1.5018, 1.760457007074647, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5817, 3.0036, 2.6964854053799767, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5817, 1.5018, 0.3267957402606925, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5817, 21.0252, 14.825246067103182, 1);
INSERT INTO building(id, name, level) VALUES (16783121, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16783121, 30.0, 26.932535011785628, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783121, 30.0, 24.457132525504257, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 16783121, 120.0, 102.77933507457978, 2);
INSERT INTO building(id, name, level) VALUES (5910, "building_coal_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5910, 45.0, 36.68569878825639, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 5910, 180.0, 146.74279515302555, 3);
INSERT INTO building(id, name, level) VALUES (5933, "building_barrackslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 5933, 6.96696, 6.577118739375058, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 5933, 6.96696, 6.577118739375058, 7);
INSERT INTO building(id, name, level) VALUES (16783164, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16783164, 1.0, 0.9440442803425105, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16783164, 1.0, 0.9440442803425105, 1);
INSERT INTO building(id, name, level) VALUES (16783296, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783296, 15.0, 12.228566262752128, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 16783296, 60.0, 48.91426505100851, 1);
INSERT INTO building(id, name, level) VALUES (6111, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6111, 5.0, 5.8611566356194125, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6111, 10.0, 8.97751167059521, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 6111, 5.0, 1.0880135179807318, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6111, 70.0, 49.358256981965575, 1);
INSERT INTO building(id, name, level) VALUES (6126, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 6126, 15.0, 12.228566262752128, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 6126, 60.0, 48.91426505100851, 1);
INSERT INTO building(id, name, level) VALUES (6129, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6129, 5.0, 5.8611566356194125, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6129, 10.0, 8.97751167059521, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 6129, 5.0, 1.0880135179807318, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6129, 70.0, 49.358256981965575, 1);
INSERT INTO building(id, name, level) VALUES (134223882, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 134223882, 15.0, 12.228566262752128, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 134223882, 60.0, 48.91426505100851, 1);
INSERT INTO building(id, name, level) VALUES (6241, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6241, 5.0, 5.8611566356194125, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6241, 10.0, 8.97751167059521, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6241, 55.0, 52.18815709413683, 1);
INSERT INTO building(id, name, level) VALUES (6254, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6254, 5.0, 5.8611566356194125, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6254, 10.0, 8.97751167059521, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 6254, 5.0, 1.0880135179807318, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6254, 70.0, 49.358256981965575, 1);
INSERT INTO building(id, name, level) VALUES (50337913, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 50337913, 15.0, 13.466267505892814, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50337913, 15.0, 12.228566262752128, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 50337913, 60.0, 51.38966753728989, 1);
INSERT INTO building(id, name, level) VALUES (6632, "building_trade_centerlevel", 2);
