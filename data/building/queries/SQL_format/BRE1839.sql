
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 32.8706988190181, 22.362736430915323);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 20, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 13.347092338278301, 0.8980508928571429);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 21.606973823222773, 5.388305357142857);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 37.797033974563945, 3.8292915657830973);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 46.64857910479482, 6.187343321972815);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 41.858326979176525, 3.4021671149973365);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 7.706000143694707, 0.32698848200570557);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 28.617666708097694, 1.5102416666666667);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 52.5, 0.313375);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 0.06441009554166985);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 52.35207605260372, 1.4380172495683672);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 52.5, 0.6679790513978123);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 48.4579792920135, 8.957091666666667);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 87.5, 0.475685);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 52.5, 0.0007933156599128952);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 0.1856459978749779);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 0.37572555732640733);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (389, "building_paper_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 389, 30.0, 26.786052353554453, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 389, 40.0, 35.714736471405935, 1);
INSERT INTO building(id, name, level) VALUES (390, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 390, 4.999, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (3137, "building_subsistence_farms", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3137, 6.4364, 6.4364, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3137, 1.6091, 1.6091, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3137, 1.6091, 1.6091, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3137, 1.6091, 1.6091, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3137, 1.6091, 1.6091, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3137, 1.6091, 1.6091, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3137, 1.6091, 1.6091, 4);
INSERT INTO building(id, name, level) VALUES (3911, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3911, 1.875, 0.0, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3911, 1.25, 0.1774417650818251, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3911, 1.25, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (4886, "building_logging_camp", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 4886, 29.988, 29.988, 1);
INSERT INTO building(id, name, level) VALUES (4957, "building_food_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4957, 21.695600000000002, 3.079764446807396, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 4957, 8.13585, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 4957, 35.25535000000001, 2.5023086130310097, 1);
INSERT INTO building(id, name, level) VALUES (5181, "building_artillery_foundries", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5181, 0.62175, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 5181, 0.41450000000000004, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 5181, 1.0362500000000001, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (5495, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 5495, 0.0333, 0.0047270486217798205, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5495, 0.0333, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 5495, 0.0666, 0.0047270486217798205, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 5495, 0.1332, 0.009454097243559641, 1);
