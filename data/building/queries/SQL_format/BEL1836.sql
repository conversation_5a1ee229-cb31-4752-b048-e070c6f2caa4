
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 76.32884592305817, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 89.05032024356787, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 22.607015716609716, 625.9120391316214);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 35.0, 47.68327999174284);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 23.765514663654017, 39.95000739014781);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 22.91539830667198, 78.18212489499795);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 40.05979307309262, 74.10590353752072);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 34.88252767061638, 185.01549652511525);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 31.474612873281362, 155.98853051221937);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 46.15502823288821, 9.991239076626567);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 16.326366528094624, 100.57147566514884);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 16.394726660400412, 11.342803908058514);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 87.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 39.588317162078766, 31.152828476569532);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 59.131160174276516, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 55.399932029058675, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 28.92428380743105, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 23.960674137170372, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 50.55604624483742, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 12.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 7.7333115595290565);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 27.52160689925915, 81.87641114579797);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 25.95315171727482, 36.43127396918208);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 41.53218177826278, 301.9542033333334);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 60.53559704709743, 64.07146000000002);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 44.74968286759414, 5.972213699797237);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 33.439870291611584);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 79.83969219678127, 72.17757455560451);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (363, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 363, 20.0, 5.65479957173265, 1);
INSERT INTO building(id, name, level) VALUES (364, "building_steel_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 364, 90.0, 51.646731351684934, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 364, 120.0, 43.54160927869452, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 364, 195.0, 91.32818316993132, 3);
INSERT INTO building(id, name, level) VALUES (365, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 365, 20.0, 14.983684374358438, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 365, 40.0, 32.225604515541384, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 365, 50.0, 18.14233719945605, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 365, 10.0, 6.488919098196149, 1);
INSERT INTO building(id, name, level) VALUES (366, "building_chemical_plantslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 366, 90.0, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 366, 45.0, 25.823365675842467, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 366, 30.0, 10.88540231967363, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 366, 225.0, 70.25244859225486, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 366, 60.0, 18.73398629126796, 3);
INSERT INTO building(id, name, level) VALUES (367, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 367, 60.0, 48.33840677331207, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 367, 40.0, 91.57071991681559, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 367, 160.0, 144.45120903108275, 2);
INSERT INTO building(id, name, level) VALUES (368, "building_coal_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 368, 50.00000000000001, 32.444595490980745, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 368, 200.00000000000003, 129.77838196392298, 5);
INSERT INTO building(id, name, level) VALUES (369, "building_iron_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 369, 40.0, 22.95410282297108, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 369, 40.0, 25.955676392784596, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 369, 160.0, 97.81955843151135, 4);
INSERT INTO building(id, name, level) VALUES (370, "building_wheat_farmlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 370, 34.92544339622641, 47.843820454495855, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 370, 6.9850849056603765, 4.532565084686126, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 370, 244.47814150943398, 201.5590148313203, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 370, 55.88071698113207, 46.070631072554114, 7);
INSERT INTO building(id, name, level) VALUES (371, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 371, 15.0, 9.733378647294222, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 371, 120.0, 77.86702917835377, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 371, 30.0, 19.466757294588444, 3);
INSERT INTO building(id, name, level) VALUES (372, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 372, 10.0, 5.73852570574277, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 372, 10.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 372, 50.0, 14.346314264356923, 1);
INSERT INTO building(id, name, level) VALUES (373, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 373, 5.0, 3.193950563640153, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 373, 5.0, 3.193950563640153, 5);
INSERT INTO building(id, name, level) VALUES (374, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 374, 60.0, 16.96439871519795, 3);
INSERT INTO building(id, name, level) VALUES (375, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 375, 20.0, 14.983684374358438, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 375, 40.0, 32.225604515541384, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 375, 50.0, 18.14233719945605, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 375, 10.0, 6.488919098196149, 1);
INSERT INTO building(id, name, level) VALUES (376, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 376, 30.0, 24.169203386656037, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 376, 10.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 376, 70.0, 28.19740395109871, 1);
INSERT INTO building(id, name, level) VALUES (377, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 377, 80.0, 66.09591617808152, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 377, 80.0, 27.556683137443052, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 377, 70.0, 40.973012200542, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 377, 120.0, 70.23944948664344, 2);
INSERT INTO building(id, name, level) VALUES (378, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 378, 20.0, 7.2569348797824205, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 378, 10.0, 4.866689323647111, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 378, 15.0, 6.371367572653741, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 378, 15.0, 6.371367572653741, 1);
INSERT INTO building(id, name, level) VALUES (379, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 379, 10.0, 2.827399785866325, 2);
INSERT INTO building(id, name, level) VALUES (380, "building_textile_millslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 380, 160.0, 119.8694749948675, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 380, 20.0, 0.0, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 380, 239.99999999999997, 89.90210624615061, 4);
INSERT INTO building(id, name, level) VALUES (381, "building_furniture_manufacturieslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 381, 30.0, 22.475526561537656, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 381, 60.0, 48.33840677331207, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 381, 30.0, 14.600067970941334, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 381, 15.0, 9.733378647294222, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 381, 135.0, 90.8004996154389, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 381, 60.0, 40.355777606861736, 3);
INSERT INTO building(id, name, level) VALUES (382, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 382, 20.0, 12.977838196392298, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 382, 239.99999999999997, 155.73405835670755, 4);
INSERT INTO building(id, name, level) VALUES (383, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 383, 5.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 383, 50.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (384, "building_wheat_farmlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 384, 29.933095238095234, 41.00488053856716, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 384, 5.9866190476190475, 3.8846686671720074, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 384, 179.5986, 148.06933927746454, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 384, 53.879571428571424, 44.42079471655975, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 384, 35.919714285714285, 29.613863144373166, 6);
INSERT INTO building(id, name, level) VALUES (385, "building_livestock_ranchlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 385, 49.9015, 41.22856701450669, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 385, 49.9015, 32.38067963786351, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 385, 199.606, 147.21849330474038, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 385, 24.95075, 18.402311663092547, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 385, 124.75375, 92.01155831546274, 5);
INSERT INTO building(id, name, level) VALUES (386, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 386, 10.0, 5.73852570574277, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 386, 10.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 386, 50.0, 14.346314264356923, 1);
INSERT INTO building(id, name, level) VALUES (387, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 387, 5.0, 3.193950563640153, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 387, 5.0, 3.193950563640153, 5);
INSERT INTO building(id, name, level) VALUES (388, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 388, 15.0, 0.0, 3);
INSERT INTO building(id, name, level) VALUES (3331, "building_subsistence_farmslevel", 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3331, 102.4965, 102.4965, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3331, 20.4993, 20.4993, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3331, 20.4993, 20.4993, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3331, 20.4993, 20.4993, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3331, 20.4993, 20.4993, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3331, 20.4993, 20.4993, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3331, 28.69902, 28.69902, 45);
INSERT INTO building(id, name, level) VALUES (3332, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3332, 20.0, 16.112802257770692, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3332, 100.0, 80.56401128885346, 4);
INSERT INTO building(id, name, level) VALUES (3333, "building_subsistence_farmslevel", 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3333, 172.3275, 172.3275, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3333, 34.4655, 34.4655, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3333, 34.4655, 34.4655, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3333, 34.4655, 34.4655, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3333, 34.4655, 34.4655, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3333, 34.4655, 34.4655, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3333, 48.2517, 48.2517, 69);
INSERT INTO building(id, name, level) VALUES (3334, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3334, 30.0, 24.169203386656037, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3334, 150.0, 120.84601693328018, 6);
INSERT INTO building(id, name, level) VALUES (4009, "building_trade_centerlevel", 46);
INSERT INTO building(id, name, level) VALUES (4270, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4271, "building_conscription_centerlevel", 43);
