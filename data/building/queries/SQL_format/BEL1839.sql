
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 87.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 78.2376835067548, 6.197551110308917);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 23.38030571022788, 597.7888388181649);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 29.64576062441823, 44.66206160641301);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 28.520680048426357, 10.846681199915965);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 28.30603155086915, 42.149232034009266);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 35.88372170681781, 138.5351418673065);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 42.00907630523338, 205.93431633095017);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 38.49797764440108, 172.91927258856376);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 45.569906851224594, 7.849234898182867);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 25.74301419229536, 116.8884829432484);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 44.50333034694465, 63.07848333333334);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 91.1561302005765, 10.57134141342872);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 65.97786532432967, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 43.90579809025388, 84.54133784404985);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 58.83043582138478, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 9.999999999999996, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 63.0403753278368, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 64.8703406315194, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 53.39436085018461, 0.22839099902463256);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 21.149009122306964, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 52.9437561255893, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 12.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 15.594327040816328);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 25.899601753899187, 70.82545210079908);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 24.83588621102335, 25.892355287194437);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 48.33870940698317, 297.01121500000016);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 87.5, 64.48752999999999);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 35.157022374450705, 1.1414511810569816);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 18.193381547619055);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 88.7129021012852, 90.96690773809526);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 315.5059358144645, 0.22404580139144126);
INSERT INTO building(id, name, level) VALUES (324, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 324, 20.0, 6.505261156309854, 1);
INSERT INTO building(id, name, level) VALUES (325, "building_steel_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 325, 60.00000000000001, 24.41551221703072, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 325, 80.0, 31.530802347681405, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 325, 130.00000000000003, 52.068915142607764, 2);
INSERT INTO building(id, name, level) VALUES (326, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 326, 24.7808, 10.911842763012318, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 326, 49.5616, 23.20571102747233, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 326, 61.952, 24.417453338044478, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 326, 12.3904, 7.437378573133005, 2);
INSERT INTO building(id, name, level) VALUES (327, "building_chemical_plants", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 327, 60.0, 36.45317742251646, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 327, 20.0, 7.882700586920351, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 327, 180.00000000000003, 90.1519187749163, 2);
INSERT INTO building(id, name, level) VALUES (328, "building_explosives_factory", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 328, 0.196, 0.11908037958022046, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 328, 0.196, 0.3430513360857275, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 328, 0.49, 0.39385047447527555, 1);
INSERT INTO building(id, name, level) VALUES (329, "building_tooling_workshops", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 329, 90.0, 42.13976127632097, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 329, 60.00000000000001, 38.4078563880361, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 329, 240.00000000000003, 133.00206114450017, 3);
INSERT INTO building(id, name, level) VALUES (330, "building_coal_mine", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 330, 25.000000000000004, 9.34760883929165, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 330, 50.00000000000001, 30.012665342252895, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 330, 197.20000000000002, 96.05194531708898, 5);
INSERT INTO building(id, name, level) VALUES (331, "building_iron_mine", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 331, 70.0, 28.484764253202503, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 331, 70.0, 42.01773147915405, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 331, 280.0, 141.0049914647131, 7);
INSERT INTO building(id, name, level) VALUES (332, "building_sulfur_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 332, 20.0, 8.138504072343572, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 332, 20.0, 12.005066136901158, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 332, 80.0, 40.287140418489464, 2);
INSERT INTO building(id, name, level) VALUES (333, "building_wheat_farm", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 333, 35.0, 61.259167158165624, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 333, 7.0, 4.201773147915405, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 333, 280.0, 224.03546295830807, 7);
INSERT INTO building(id, name, level) VALUES (334, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 334, 15.0, 5.6085653035749905, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 334, 15.0, 9.003799602675867, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 334, 103.53, 50.42727129147171, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 334, 29.580000000000002, 14.407791797563348, 3);
INSERT INTO building(id, name, level) VALUES (335, "building_railway", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 335, 8.0, 3.7457565578951977, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 335, 2.0, 0.8138504072343572, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 335, 5.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 335, 29.999999999999996, 8.751447733540783, 1);
INSERT INTO building(id, name, level) VALUES (336, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 336, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 336, 10.0, 6.3168513758605025, 5);
INSERT INTO building(id, name, level) VALUES (337, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 337, 60.0, 19.515783468929563, 3);
INSERT INTO building(id, name, level) VALUES (338, "building_construction_sector", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 338, 86.70764, 38.18037085291344, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 338, 173.41529, 81.19643246959969, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 338, 216.76911, 85.4362995311601, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 338, 43.35382, 26.023273819365404, 7);
INSERT INTO building(id, name, level) VALUES (339, "building_paper_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 339, 30.0, 14.046587092106991, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 339, 10.0, 6.075529570419411, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 339, 70.0, 37.65203843725943, 1);
INSERT INTO building(id, name, level) VALUES (340, "building_food_industry", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 340, 80.0, 62.839364252282344, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 340, 30.0, 23.654489085448272, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 340, 130.0, 102.30837647345068, 2);
INSERT INTO building(id, name, level) VALUES (341, "building_arms_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 341, 6.2843, 1.5531486676320723, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 341, 6.2843, 4.022774864988921, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 341, 3.14215, 1.8860859281031985, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 341, 31.421500000000005, 15.580158981378986, 1);
INSERT INTO building(id, name, level) VALUES (342, "building_university", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 342, 34.0, 11.058943965726751, 4);
INSERT INTO building(id, name, level) VALUES (343, "building_textile_mills", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 343, 124.416, 54.78466511181804, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 343, 15.552, 0.0, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 343, 186.624, 41.08849883386353, 4);
INSERT INTO building(id, name, level) VALUES (344, "building_furniture_manufacturies", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 344, 30.000000000000004, 13.21003691932341, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 344, 45.0, 21.069880638160484, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 344, 45.0, 11.121634874758247, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 344, 15.000000000000002, 9.003799602675869, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 344, 120.00000000000001, 52.67864646662097, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 344, 75.0, 32.924154041638104, 3);
INSERT INTO building(id, name, level) VALUES (345, "building_logging_camp", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 345, 20.0, 7.47808707143332, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 345, 20.0, 12.005066136901158, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 345, 239.99999999999997, 116.89891925000686, 4);
INSERT INTO building(id, name, level) VALUES (346, "building_fishing_wharf", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 346, 5.0, 1.5942091256514364, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 346, 50.0, 15.942091256514363, 1);
INSERT INTO building(id, name, level) VALUES (347, "building_wheat_farm", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 347, 30.0, 52.507857564141965, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 347, 6.0, 3.601519841070347, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 347, 150.0, 120.01899801337933, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 347, 42.0, 33.60531944374621, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 347, 30.0, 24.003799602675866, 6);
INSERT INTO building(id, name, level) VALUES (348, "building_livestock_ranch", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 348, 50.0, 39.27460265767647, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 348, 50.0, 30.01266534225289, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 348, 100.0, 69.28726799992936, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 348, 25.0, 17.32181699998234, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 348, 125.0, 86.6090849999117, 5);
INSERT INTO building(id, name, level) VALUES (349, "building_railway", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 349, 8.0, 3.7457565578951977, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 349, 2.0, 0.8138504072343572, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 349, 5.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 349, 29.999999999999996, 8.751447733540783, 1);
INSERT INTO building(id, name, level) VALUES (350, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 350, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 350, 10.0, 6.3168513758605025, 5);
INSERT INTO building(id, name, level) VALUES (351, "building_port", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 351, 20.0, 6.376836502605745, 4);
INSERT INTO building(id, name, level) VALUES (3157, "building_subsistence_farms", 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3157, 78.7774, 78.7774, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3157, 19.69435, 19.69435, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3157, 19.69435, 19.69435, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3157, 19.69435, 19.69435, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3157, 19.69435, 19.69435, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3157, 19.69435, 19.69435, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3157, 19.69435, 19.69435, 41);
INSERT INTO building(id, name, level) VALUES (3158, "building_urban_center", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3158, 4.0, 1.6277008144687144, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3158, 68.0, 27.670913845968144, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3158, 20.0, 8.138504072343572, 4);
INSERT INTO building(id, name, level) VALUES (3159, "building_subsistence_farms", 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3159, 140.94706, 140.94706, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3159, 35.23676, 35.23676, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3159, 35.23676, 35.23676, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3159, 35.23676, 35.23676, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3159, 35.23676, 35.23676, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3159, 35.23676, 35.23676, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3159, 35.23676, 35.23676, 79);
INSERT INTO building(id, name, level) VALUES (3160, "building_urban_center", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3160, 8.8991953125, 3.621306864568104, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3160, 151.2863984375, 61.5622484886893, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3160, 44.496, 18.10654386014998, 9);
INSERT INTO building(id, name, level) VALUES (3968, "building_naval_base", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3968, 4.496, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (4191, "building_trade_center", 72);
INSERT INTO building(id, name, level) VALUES (4242, "building_glassworks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4242, 0.27, 0.12641928382896295, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 4242, 0.27, 0.12641928382896295, 1);
INSERT INTO building(id, name, level) VALUES (4424, "building_conscription_center", 17);
INSERT INTO building(id, name, level) VALUES (4767, "building_conscription_center", 33);
INSERT INTO building(id, name, level) VALUES (5253, "building_lead_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 5253, 0.01385, 0.005178575296967574, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5253, 0.0277, 0.011271828140195847, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5253, 0.0277, 0.0166270165996081, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 5253, 0.10924, 0.05028983068902117, 1);
INSERT INTO building(id, name, level) VALUES (5345, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5345, 29.999999999999996, 14.04658709210699, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 5345, 20.0, 12.802618796012032, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 5345, 80.0, 44.33402038150005, 1);
INSERT INTO building(id, name, level) VALUES (5500, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5500, 10.8216, 4.76512451753834, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5500, 21.6432, 10.133769791729668, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 5500, 10.8216, 2.674530754681863, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 5500, 2.7054, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 5500, 37.8756, 10.943222647074407, 1);
INSERT INTO building(id, name, level) VALUES (16782988, "building_arts_academy", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16782988, 0.396, 0.12880417089493512, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 16782988, 0.1584, 0.051521668357974046, 1);
