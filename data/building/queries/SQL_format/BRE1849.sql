
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 34.99667004949866, 31.278783227075856);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 14.07342744111052, 18.1217517329673);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 6.773253888989053, 3.90141508650519);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 34.9987554591999, 8.767224913494811);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 52.4932790125581, 14.862180007343147);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 52.49675577227792, 15.04980666666667);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 52.4671137851434, 1.4846646296296295);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 52.4968804008022, 11.877317037037036);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 52.49345733575476, 11.030594166666665);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 52.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 0.14911369047619047);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 14.356416017834185, 1.6760128043594678);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 52.49701970751087, 22.9507675);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 87.5, 1.01164);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 0.6958638888888888);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 0.1739659722222222);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (424, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 424, 14.886, 0.0012350822900254557, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 424, 19.848, 0.0016467763867006077, 1);
INSERT INTO building(id, name, level) VALUES (425, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 425, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (426, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 426, 1.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 426, 1.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (3230, "building_subsistence_farmslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3230, 0.009899999999999999, 0.01089, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3230, 0.0019727272727272727, 0.00217, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3230, 0.0019727272727272727, 0.00217, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3230, 0.0019727272727272727, 0.00217, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3230, 0.0019727272727272727, 0.00217, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3230, 0.0019727272727272727, 0.00217, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3230, 0.0027636363636363635, 0.00304, 6);
INSERT INTO building(id, name, level) VALUES (4745, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4745, 5.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 4745, 60.00000000000001, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (4872, "building_food_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4872, 17.776, 0.003946213340784939, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 4872, 19.998, 0.004439490008383056, 1);
INSERT INTO building(id, name, level) VALUES (16782376, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 16782376, 9.859, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16782376, 59.15399999999999, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16782376, 29.576999999999995, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (67114922, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 67114922, 23.044999999999998, 29.9585, 1);
INSERT INTO building(id, name, level) VALUES (6136, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6136, 29.999999999999996, 33.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 6136, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (50337989, "building_trade_centerlevel", 12);
INSERT INTO building(id, name, level) VALUES (16783602, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783602, 2.501, 0.000207506436071051, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16783602, 12.505, 0.0010375321803552551, 1);
