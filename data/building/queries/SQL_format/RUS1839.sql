
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 79.87342497654045, 19.624740600295308);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 115.762935278352, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 95.82198289835594, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 21.462596485496434, 7602.935267063429);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 21.35677710086855, 120.83040706742239);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 12.497458060106265, 252.54271142857166);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 18.213533652884998, 1515.2562685714313);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 36.96413156314169, 655.8951327577911);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 35.46845091240578, 2020.2177410898205);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 32.093882451228765, 1741.2639881190726);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 28.583043059205455, 80.60261191018024);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 18.697102620341504, 1010.6715529709107);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 44.03860325207872, 524.4461000000003);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 73.90899895676797, 10.864354498531332);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 63.83858113378089, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 30.76534957982723, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 50.52210252267003, 30.073240742834862);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 58.81499195402688, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 106.26026369598651, 126.2441835027407);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 30.842965859451432, 322.46913446768195);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 44.31456492082245, 522.8859218252315);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 38.605454824233526, 2945.9667383268165);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 54.40792388565028, 295.10612196873643);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 74.10416105297205, 226.18964082665718);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 42.44447572822571, 84.73872256484262);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 46.68849267622903, 64.98936250489217);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 92.3825723537201, 641.9692053782594);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 93.28648413811335, 672.3002805352085);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 177.14902619743137, 4.253219692321545);
INSERT INTO building(id, name, level) VALUES (869, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 869, 99.99999999999999, 106.72083691531361, 10);
INSERT INTO building(id, name, level) VALUES (870, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 870, 50.0, 100.03432926764532, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 870, 150.0, 170.297261888143, 2);
INSERT INTO building(id, name, level) VALUES (871, "building_textile_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 871, 50.0, 100.03432926764532, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 871, 30.0, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 871, 30.0, 15.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 871, 60.0, 30.0, 2);
INSERT INTO building(id, name, level) VALUES (872, "building_glassworks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 872, 90.0, 102.1783571328858, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 872, 15.0, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 872, 30.0, 15.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 872, 60.0, 30.0, 3);
INSERT INTO building(id, name, level) VALUES (873, "building_arts_academy", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 873, 10.0, 10.672083691531363, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 873, 4.0, 4.0, 1);
INSERT INTO building(id, name, level) VALUES (874, "building_artillery_foundries", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 874, 8.51445, 1.7490224733337625, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 874, 5.6763, 8.202130207852978, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 874, 14.19075, 8.552893727778136, 1);
INSERT INTO building(id, name, level) VALUES (875, "building_rye_farm", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 875, 15.0, 15.3, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 875, 45.0, 45.9, 3);
INSERT INTO building(id, name, level) VALUES (876, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 876, 15.0, 10.363667014410673, 3);
INSERT INTO building(id, name, level) VALUES (877, "building_military_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 877, 20.0, 40.01373170705813, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 877, 40.0, 45.41260317017147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 877, 10.0, 2.054181389677269, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 877, 35.0, 25.729878287956815, 1);
INSERT INTO building(id, name, level) VALUES (878, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 878, 99.99999999999999, 106.72083691531361, 10);
INSERT INTO building(id, name, level) VALUES (879, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 879, 40.0, 80.02746341411626, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 879, 80.0, 90.82520634034294, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 879, 100.0, 20.54181389677269, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 879, 20.0, 7.457351159849866, 2);
INSERT INTO building(id, name, level) VALUES (880, "building_tooling_workshops", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 880, 209.99999999999997, 238.41616664340015, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 880, 140.0, 28.758539455481767, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 880, 419.99999999999994, 253.13780918322263, 7);
INSERT INTO building(id, name, level) VALUES (881, "building_university", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 881, 10.0, 10.672083691531363, 2);
INSERT INTO building(id, name, level) VALUES (882, "building_textile_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 882, 75.0, 150.05149390146798, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 882, 45.0, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 882, 45.0, 22.5, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 882, 90.0, 45.0, 3);
INSERT INTO building(id, name, level) VALUES (883, "building_furniture_manufacturies", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 883, 60.0, 120.04119512117437, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 883, 90.0, 102.1783571328858, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 883, 90.0, 130.04804515384458, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 883, 120.0, 120.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 883, 150.0, 150.0, 6);
INSERT INTO building(id, name, level) VALUES (884, "building_logging_camp", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 884, 50.0, 18.643377899624664, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 884, 600.0, 223.72053479549595, 10);
INSERT INTO building(id, name, level) VALUES (885, "building_iron_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 885, 5.0, 1.8643377899624665, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 885, 20.0, 7.457351159849866, 1);
INSERT INTO building(id, name, level) VALUES (886, "building_saint_basils_cathedral", 1);
INSERT INTO building(id, name, level) VALUES (887, "building_rye_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 887, 4.70775, 1.7553672461391603, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 887, 23.53875, 8.776836230695803, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 887, 47.0775, 17.553672461391606, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 887, 23.53875, 8.776836230695803, 5);
INSERT INTO building(id, name, level) VALUES (888, "building_logging_camp", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 888, 40.0, 14.914702319699732, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 888, 160.0, 59.65880927879893, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 888, 160.0, 59.65880927879893, 8);
INSERT INTO building(id, name, level) VALUES (889, "building_logging_camp", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 889, 30.0, 11.1860267397748, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 889, 360.0, 134.2323208772976, 6);
INSERT INTO building(id, name, level) VALUES (890, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 890, 2.0033039215686275, 0.7469670411520793, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 890, 10.016549019607842, 3.7348461724532784, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 890, 14.023166666666667, 5.228783910321732, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 890, 10.016549019607842, 3.7348461724532784, 3);
INSERT INTO building(id, name, level) VALUES (891, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 891, 99.99999999999999, 106.72083691531361, 10);
INSERT INTO building(id, name, level) VALUES (892, "building_food_industry", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 892, 80.0, 72.20012602653706, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 892, 80.0, 35.75950500324, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 892, 70.0, 47.232338575527464, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 892, 120.0, 80.9697232723328, 2);
INSERT INTO building(id, name, level) VALUES (893, "building_livestock_ranch", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 893, 23.973499999999998, 21.63612151621483, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 893, 47.946999999999996, 43.27224303242966, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 893, 47.946999999999996, 43.27224303242966, 5);
INSERT INTO building(id, name, level) VALUES (894, "building_wheat_farm", 45);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 894, 43.5798, 16.24949360380126, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 894, 217.899, 81.24746801900629, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 894, 305.0586, 113.74645522660882, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 894, 217.899, 81.24746801900629, 45);
INSERT INTO building(id, name, level) VALUES (895, "building_textile_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 895, 75.0, 150.05149390146798, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 895, 45.0, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 895, 45.0, 22.5, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 895, 90.0, 45.0, 3);
INSERT INTO building(id, name, level) VALUES (896, "building_textile_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 896, 50.0, 100.03432926764532, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 896, 30.0, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 896, 30.0, 15.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 896, 60.0, 30.0, 2);
INSERT INTO building(id, name, level) VALUES (897, "building_food_industry", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 897, 66.33039603960397, 59.863286918119016, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 897, 24.873891089108913, 11.118475410637963, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 897, 107.78689108910892, 72.72895619891852, 2);
INSERT INTO building(id, name, level) VALUES (898, "building_rye_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 898, 4.0, 1.4914702319699733, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 898, 20.0, 7.457351159849867, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 898, 59.99999999999999, 22.372053479549596, 4);
INSERT INTO building(id, name, level) VALUES (899, "building_logging_camp", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 899, 75.0, 27.965066849436997, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 899, 900.0, 335.580802193244, 15);
INSERT INTO building(id, name, level) VALUES (900, "building_food_industry", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 900, 107.14679411764706, 96.70015048291923, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 900, 40.18004901960784, 17.96023329933869, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 900, 174.11354901960783, 117.48271568401165, 3);
INSERT INTO building(id, name, level) VALUES (901, "building_rye_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 901, 3.0, 1.11860267397748, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 901, 15.0, 5.5930133698874, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 901, 45.0, 16.7790401096622, 3);
INSERT INTO building(id, name, level) VALUES (902, "building_logging_camp", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 902, 40.0, 14.914702319699732, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 902, 480.0, 178.97642783639677, 8);
INSERT INTO building(id, name, level) VALUES (903, "building_paper_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 903, 90.0, 102.1783571328858, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 903, 120.0, 120.0, 3);
INSERT INTO building(id, name, level) VALUES (904, "building_logging_camp", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 904, 59.163, 22.05996333350988, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 904, 236.652, 88.23985333403952, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 904, 236.652, 88.23985333403952, 12);
INSERT INTO building(id, name, level) VALUES (905, "building_rye_farm", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 905, 7.0, 2.6100729059474532, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 905, 35.0, 13.050364529737266, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 905, 104.99999999999999, 39.1510935892118, 7);
INSERT INTO building(id, name, level) VALUES (906, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 906, 5.0, 1.8643377899624665, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 906, 20.0, 7.457351159849866, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 906, 20.0, 7.457351159849866, 1);
INSERT INTO building(id, name, level) VALUES (907, "building_fishing_wharf", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 907, 49.485, 49.97985, 2);
INSERT INTO building(id, name, level) VALUES (908, "building_fishing_wharf", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 908, 0.05, 0.03454555671470225, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 908, 0.5, 0.34545556714702247, 1);
INSERT INTO building(id, name, level) VALUES (909, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 909, 5.0, 3.454555671470225, 1);
INSERT INTO building(id, name, level) VALUES (910, "building_arms_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 910, 6.7254, 1.3815191518135506, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 910, 6.7254, 9.718056920862958, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 910, 20.1762, 12.160378727720328, 1);
INSERT INTO building(id, name, level) VALUES (911, "building_logging_camp", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 911, 73.44449999999999, 27.38507136297967, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 911, 881.334, 328.620856355756, 15);
INSERT INTO building(id, name, level) VALUES (912, "building_fishing_wharf", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 912, 48.285000000000004, 48.76785, 2);
INSERT INTO building(id, name, level) VALUES (913, "building_port", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 913, 9.992, 6.903584053866097, 2);
INSERT INTO building(id, name, level) VALUES (914, "building_livestock_ranch", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 914, 49.185, 44.38953998269032, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 914, 98.37, 88.77907996538065, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 914, 98.37, 88.77907996538065, 10);
INSERT INTO building(id, name, level) VALUES (915, "building_wheat_farm", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 915, 19.753194029850746, 7.365325220462346, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 915, 395.06399999999996, 147.30654893074637, 20);
INSERT INTO building(id, name, level) VALUES (916, "building_vineyard_plantation", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 916, 159.86719672131147, 195.03798, 8);
INSERT INTO building(id, name, level) VALUES (917, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 917, 0.6846930693069307, 0.47306206515812266, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 917, 6.8469999999999995, 4.730668536511326, 2);
INSERT INTO building(id, name, level) VALUES (918, "building_rye_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 918, 3.0, 1.11860267397748, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 918, 15.000000000000002, 5.593013369887401, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 918, 45.0, 16.7790401096622, 3);
INSERT INTO building(id, name, level) VALUES (919, "building_logging_camp", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 919, 50.0, 18.643377899624664, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 919, 600.0, 223.72053479549595, 10);
INSERT INTO building(id, name, level) VALUES (920, "building_paper_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 920, 90.0, 102.1783571328858, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 920, 120.0, 120.0, 3);
INSERT INTO building(id, name, level) VALUES (921, "building_logging_camp", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 921, 25.0, 9.321688949812332, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 921, 100.0, 37.28675579924933, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 921, 100.0, 37.28675579924933, 5);
INSERT INTO building(id, name, level) VALUES (922, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 922, 16.508, 33.027334151005775, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 922, 33.016, 37.48356265665953, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 922, 28.889, 28.889, 1);
INSERT INTO building(id, name, level) VALUES (923, "building_military_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 923, 17.076, 34.16372413148623, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 923, 34.152, 38.7732805866924, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 923, 8.538, 1.7538600705064524, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 923, 29.883, 21.96817008225753, 1);
INSERT INTO building(id, name, level) VALUES (924, "building_livestock_ranch", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 924, 35.44679487179487, 31.99078821225497, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 924, 70.89359829059829, 63.98158413819862, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 924, 70.89359829059829, 63.98158413819862, 8);
INSERT INTO building(id, name, level) VALUES (925, "building_wheat_farm", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 925, 14.949744186046512, 5.5742746072636375, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 925, 74.74874418604651, 27.871381707656745, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 925, 104.6482480620155, 39.01993670307639, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 925, 74.74874418604651, 27.871381707656745, 15);
INSERT INTO building(id, name, level) VALUES (926, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 926, 15.0, 10.363667014410673, 3);
INSERT INTO building(id, name, level) VALUES (927, "building_rye_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 927, 3.0, 1.11860267397748, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 927, 15.0, 5.5930133698874, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 927, 45.0, 16.7790401096622, 3);
INSERT INTO building(id, name, level) VALUES (928, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 928, 10.0, 3.728675579924933, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 928, 40.0, 14.914702319699732, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 928, 40.0, 14.914702319699732, 2);
INSERT INTO building(id, name, level) VALUES (934, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 934, 5.0, 4.5125078766585665, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 934, 10.0, 9.025015753317133, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 934, 10.0, 9.025015753317133, 1);
INSERT INTO building(id, name, level) VALUES (935, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 935, 10.0, 3.728675579924933, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 935, 120.0, 44.74410695909919, 2);
INSERT INTO building(id, name, level) VALUES (936, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 936, 20.0, 40.01373170705813, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 936, 40.0, 45.41260317017147, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 936, 35.0, 35.0, 1);
INSERT INTO building(id, name, level) VALUES (937, "building_military_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 937, 20.0, 40.01373170705813, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 937, 40.0, 45.41260317017147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 937, 10.0, 2.054181389677269, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 937, 35.0, 25.729878287956815, 1);
INSERT INTO building(id, name, level) VALUES (938, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 938, 10.0, 3.728675579924933, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 938, 40.0, 14.914702319699732, 2);
INSERT INTO building(id, name, level) VALUES (939, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 939, 10.0, 3.728675579924933, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 939, 40.0, 14.914702319699732, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 939, 40.0, 14.914702319699732, 2);
INSERT INTO building(id, name, level) VALUES (940, "building_fishing_wharf", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 940, 0.4326, 0.2988881566956038, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 940, 4.326, 2.9888815669560382, 1);
INSERT INTO building(id, name, level) VALUES (941, "building_rye_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 941, 1.0, 0.3728675579924933, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 941, 5.0, 1.8643377899624667, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 941, 15.0, 5.5930133698874, 1);
INSERT INTO building(id, name, level) VALUES (942, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 942, 5.0, 3.454555671470225, 1);
INSERT INTO building(id, name, level) VALUES (943, "building_rye_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 943, 3.0, 1.11860267397748, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 943, 15.0, 5.5930133698874, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 943, 45.0, 16.7790401096622, 3);
INSERT INTO building(id, name, level) VALUES (944, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 944, 15.0, 10.363667014410673, 3);
INSERT INTO building(id, name, level) VALUES (945, "building_rye_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 945, 4.0, 1.4914702319699733, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 945, 20.0, 7.457351159849867, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 945, 59.99999999999999, 22.372053479549596, 4);
INSERT INTO building(id, name, level) VALUES (946, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 946, 5.0, 3.454555671470225, 1);
INSERT INTO building(id, name, level) VALUES (947, "building_furniture_manufacturies", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 947, 10.0, 20.006865853529064, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 947, 15.0, 17.0297261888143, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 947, 15.0, 21.674674192307428, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 947, 20.0, 20.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 947, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (948, "building_paper_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 948, 87.16139130434782, 98.95564187660953, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 948, 116.2151956521739, 116.2151956521739, 3);
INSERT INTO building(id, name, level) VALUES (949, "building_rye_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 949, 2.0, 0.7457351159849867, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 949, 10.0, 3.7286755799249334, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 949, 30.0, 11.1860267397748, 2);
INSERT INTO building(id, name, level) VALUES (950, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 950, 29.982, 34.03901670620202, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 950, 29.982, 29.982, 1);
INSERT INTO building(id, name, level) VALUES (951, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 951, 15.0, 5.5930133698874, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 951, 60.0, 22.3720534795496, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 951, 60.0, 22.3720534795496, 3);
INSERT INTO building(id, name, level) VALUES (959, "building_textile_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 959, 25.0, 50.01716463382266, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 959, 15.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 959, 15.0, 7.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 959, 30.0, 15.0, 1);
INSERT INTO building(id, name, level) VALUES (960, "building_rye_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 960, 3.0, 1.11860267397748, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 960, 15.0, 5.5930133698874, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 960, 45.0, 16.7790401096622, 3);
INSERT INTO building(id, name, level) VALUES (961, "building_paper_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 961, 29.244, 33.20115417771236, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 961, 38.992, 38.992, 1);
INSERT INTO building(id, name, level) VALUES (962, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 962, 14.970441176470588, 13.510846745175453, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 962, 29.940892156862745, 27.021702338405564, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 962, 29.940892156862745, 27.021702338405564, 3);
INSERT INTO building(id, name, level) VALUES (963, "building_glassworks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 963, 27.1527, 30.826869752467868, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 963, 4.525444444444444, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 963, 9.0509, 4.52545, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 963, 18.1018, 9.0509, 1);
INSERT INTO building(id, name, level) VALUES (1031, "building_fishing_wharf", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1031, 0.447, 0.3088372770294381, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1031, 4.47, 3.088372770294381, 1);
INSERT INTO building(id, name, level) VALUES (1348, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1348, 5.0, 3.454555671470225, 1);
INSERT INTO building(id, name, level) VALUES (1996, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1996, 0.95733, 0.3569572992929536, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1996, 4.78665, 1.784786496464768, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1996, 6.70131, 2.4987010950506754, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1996, 4.78665, 1.784786496464768, 1);
INSERT INTO building(id, name, level) VALUES (1997, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1997, 0.8127, 0.7334630302720834, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1997, 0.8127, 0.30302946438049927, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1997, 1.6254, 1.0364924946525826, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1997, 3.2508, 2.0729849893051653, 1);
INSERT INTO building(id, name, level) VALUES (1998, "building_tea_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 1998, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (2001, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2001, 1.0, 0.3728675579924933, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2001, 5.0, 1.8643377899624667, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2001, 7.0, 2.6100729059474532, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2001, 5.0, 1.8643377899624667, 1);
INSERT INTO building(id, name, level) VALUES (2002, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2002, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (2003, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2003, 5.0, 4.5125078766585665, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2003, 10.0, 9.025015753317133, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2003, 10.0, 9.025015753317133, 1);
INSERT INTO building(id, name, level) VALUES (2007, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2007, 1.952, 0.7278374732013468, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2007, 9.76, 3.6391873660067344, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2007, 13.664, 5.094862312409428, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2007, 9.76, 3.6391873660067344, 2);
INSERT INTO building(id, name, level) VALUES (2008, "building_rice_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2008, 1.99888, 0.745317504320035, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2008, 9.9944, 3.726587521600175, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2008, 11.99328, 4.4719050259202096, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2008, 17.98992, 6.707857538880315, 1);
INSERT INTO building(id, name, level) VALUES (2009, "building_tea_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2009, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (2010, "building_textile_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2010, 25.0, 50.01716463382266, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2010, 15.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2010, 15.0, 7.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2010, 30.0, 15.0, 1);
INSERT INTO building(id, name, level) VALUES (2011, "building_arms_industry", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2011, 45.116, 9.267644757667966, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2011, 45.116, 65.1916400573428, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 2011, 135.34799999999998, 81.57546713650194, 5);
INSERT INTO building(id, name, level) VALUES (2012, "building_food_industry", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2012, 80.0, 72.20012602653706, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 2012, 30.0, 13.409814376215, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 2012, 130.0, 87.71720021169385, 2);
INSERT INTO building(id, name, level) VALUES (2013, "building_rye_farm", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2013, 5.566619047619048, 2.075611650560213, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2013, 27.833095238095236, 10.378058252801065, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2013, 83.49929523809524, 31.1341783095228, 6);
INSERT INTO building(id, name, level) VALUES (2014, "building_iron_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2014, 5.0, 1.8643377899624665, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2014, 20.0, 7.457351159849866, 1);
INSERT INTO building(id, name, level) VALUES (2015, "building_government_administration", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2015, 42.134, 44.96575742589824, 5);
INSERT INTO building(id, name, level) VALUES (2016, "building_arms_industry", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2016, 30.0, 6.162544169031808, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2016, 30.0, 43.349348384614856, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 2016, 90.0, 54.24381625354771, 3);
INSERT INTO building(id, name, level) VALUES (2017, "building_paper_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2017, 60.0, 68.1189047552572, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2017, 80.0, 80.0, 2);
INSERT INTO building(id, name, level) VALUES (2018, "building_food_industry", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2018, 116.48639215686273, 105.12915242627635, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 2018, 43.68239215686275, 19.525759011085317, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 2018, 189.29039215686274, 127.72325559210442, 3);
INSERT INTO building(id, name, level) VALUES (2019, "building_rye_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2019, 1.6950990099009902, 0.6320474283772755, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2019, 8.47549504950495, 3.160237141886377, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2019, 25.42649504950495, 9.480715117417132, 2);
INSERT INTO building(id, name, level) VALUES (2020, "building_logging_camp", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2020, 22.319499999999998, 8.322217460613453, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2020, 89.27799999999999, 33.28886984245381, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2020, 89.27799999999999, 33.28886984245381, 5);
INSERT INTO building(id, name, level) VALUES (2021, "building_iron_mine", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2021, 39.998, 14.913956584583747, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2021, 159.992, 59.65582633833499, 8);
INSERT INTO building(id, name, level) VALUES (2022, "building_rye_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2022, 5.0, 1.8643377899624665, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2022, 25.0, 9.321688949812332, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2022, 75.0, 27.965066849436994, 5);
INSERT INTO building(id, name, level) VALUES (2023, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2023, 1.5028918918918917, 1.3563622999856912, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2023, 1.5028918918918917, 0.5603796296564478, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2023, 3.0057927927927923, 1.9167476745481453, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2023, 6.011594594594594, 3.8335010940022967, 2);
INSERT INTO building(id, name, level) VALUES (2024, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2024, 0.31999999999999995, 0.17869600176671327, 1);
INSERT INTO building(id, name, level) VALUES (2685, "building_iron_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2685, 20.0, 7.457351159849866, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2685, 80.0, 29.829404639399463, 4);
INSERT INTO building(id, name, level) VALUES (2686, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2686, 10.0, 3.728675579924933, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2686, 120.0, 44.74410695909919, 2);
INSERT INTO building(id, name, level) VALUES (2687, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2687, 5.0, 3.454555671470225, 1);
INSERT INTO building(id, name, level) VALUES (2716, "building_subsistence_farms", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2716, 2.5, 2.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2716, 0.5, 0.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2716, 0.5, 0.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2716, 0.5, 0.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2716, 0.5, 0.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2716, 0.5, 0.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2716, 0.5, 0.5, 1);
INSERT INTO building(id, name, level) VALUES (2725, "building_subsistence_farms", 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2725, 63.92929565217393, 73.51869, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2725, 12.785852173913044, 14.70373, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2725, 12.785852173913044, 14.70373, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2725, 12.785852173913044, 14.70373, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2725, 12.785852173913044, 14.70373, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2725, 12.785852173913044, 14.70373, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2725, 12.785852173913044, 14.70373, 38);
INSERT INTO building(id, name, level) VALUES (2729, "building_subsistence_farms", 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2729, 45.04287, 45.04287, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2729, 9.00857, 9.00857, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2729, 9.00857, 9.00857, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2729, 9.00857, 9.00857, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2729, 9.00857, 9.00857, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2729, 9.00857, 9.00857, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2729, 9.00857, 9.00857, 37);
INSERT INTO building(id, name, level) VALUES (2730, "building_urban_center", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2730, 118.818, 127.13526, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2730, 39.605999999999995, 42.37842, 8);
INSERT INTO building(id, name, level) VALUES (2731, "building_subsistence_pastures", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2731, 2.4006, 2.4006, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2731, 3.6009, 3.6009, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2731, 1.2003, 1.2003, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2731, 2.4006, 2.4006, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2731, 2.4006, 2.4006, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2731, 2.4006, 2.4006, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2731, 7.96999, 7.96999, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2731, 2.4006, 2.4006, 20);
INSERT INTO building(id, name, level) VALUES (3454, "building_subsistence_farms", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3454, 35.0613, 35.0613, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3454, 7.01226, 7.01226, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3454, 7.01226, 7.01226, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3454, 7.01226, 7.01226, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3454, 7.01226, 7.01226, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3454, 7.01226, 7.01226, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3454, 7.01226, 7.01226, 18);
INSERT INTO building(id, name, level) VALUES (3455, "building_subsistence_farms", 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3455, 40.79515, 40.79515, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3455, 8.15903, 8.15903, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3455, 8.15903, 8.15903, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3455, 8.15903, 8.15903, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3455, 8.15903, 8.15903, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3455, 8.15903, 8.15903, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3455, 8.15903, 8.15903, 22);
INSERT INTO building(id, name, level) VALUES (3456, "building_subsistence_farms", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3456, 94.18, 94.18, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3456, 18.836, 18.836, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3456, 18.836, 18.836, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3456, 18.836, 18.836, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3456, 18.836, 18.836, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3456, 18.836, 18.836, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3456, 18.836, 18.836, 50);
INSERT INTO building(id, name, level) VALUES (3472, "building_subsistence_farms", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3472, 5.43547, 5.43547, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3472, 1.08709, 1.08709, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3472, 1.08709, 1.08709, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3472, 1.08709, 1.08709, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3472, 1.08709, 1.08709, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3472, 1.08709, 1.08709, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3472, 1.08709, 1.08709, 3);
INSERT INTO building(id, name, level) VALUES (3584, "building_subsistence_farms", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3584, 72.1433, 72.1433, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3584, 14.42866, 14.42866, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3584, 14.42866, 14.42866, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3584, 14.42866, 14.42866, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3584, 14.42866, 14.42866, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3584, 14.42866, 14.42866, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3584, 14.42866, 14.42866, 29);
INSERT INTO building(id, name, level) VALUES (3585, "building_subsistence_farms", 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3585, 141.80317, 141.80317, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3585, 28.36063, 28.36063, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3585, 28.36063, 28.36063, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3585, 28.36063, 28.36063, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3585, 28.36063, 28.36063, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3585, 28.36063, 28.36063, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3585, 28.36063, 28.36063, 57);
INSERT INTO building(id, name, level) VALUES (3586, "building_subsistence_farms", 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3586, 138.9108, 138.9108, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3586, 27.78216, 27.78216, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3586, 27.78216, 27.78216, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3586, 27.78216, 27.78216, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3586, 27.78216, 27.78216, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3586, 27.78216, 27.78216, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3586, 27.78216, 27.78216, 56);
INSERT INTO building(id, name, level) VALUES (3587, "building_subsistence_pastures", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3587, 1.9671, 1.9671, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3587, 2.95065, 2.95065, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3587, 0.98355, 0.98355, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3587, 1.9671, 1.9671, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3587, 1.9671, 1.9671, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3587, 1.9671, 1.9671, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3587, 6.53077, 6.53077, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3587, 1.9671, 1.9671, 20);
INSERT INTO building(id, name, level) VALUES (3588, "building_subsistence_pastures", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3588, 1.1614, 1.1614, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3588, 1.7421, 1.7421, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3588, 0.5807, 0.5807, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3588, 1.1614, 1.1614, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3588, 1.1614, 1.1614, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3588, 1.1614, 1.1614, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3588, 3.85584, 3.85584, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3588, 1.1614, 1.1614, 20);
INSERT INTO building(id, name, level) VALUES (3589, "building_subsistence_farms", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3589, 16.196, 16.196, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3589, 3.2392, 3.2392, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3589, 3.2392, 3.2392, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3589, 3.2392, 3.2392, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3589, 3.2392, 3.2392, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3589, 3.2392, 3.2392, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3589, 3.2392, 3.2392, 10);
INSERT INTO building(id, name, level) VALUES (3590, "building_subsistence_farms", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3590, 20.38687, 20.38687, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3590, 4.07737, 4.07737, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3590, 4.07737, 4.07737, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3590, 4.07737, 4.07737, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3590, 4.07737, 4.07737, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3590, 4.07737, 4.07737, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3590, 4.07737, 4.07737, 15);
INSERT INTO building(id, name, level) VALUES (3591, "building_subsistence_farms", 145);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3591, 361.43062, 361.43062, 145);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3591, 72.28612, 72.28612, 145);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3591, 72.28612, 72.28612, 145);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3591, 72.28612, 72.28612, 145);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3591, 72.28612, 72.28612, 145);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3591, 72.28612, 72.28612, 145);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3591, 72.28612, 72.28612, 145);
INSERT INTO building(id, name, level) VALUES (3592, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3592, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3592, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (3598, "building_subsistence_pastures", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3598, 2.31739, 2.31739, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3598, 3.47608, 3.47608, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3598, 1.15869, 1.15869, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3598, 2.31739, 2.31739, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3598, 2.31739, 2.31739, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3598, 2.31739, 2.31739, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3598, 7.69373, 7.69373, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3598, 2.31739, 2.31739, 29);
INSERT INTO building(id, name, level) VALUES (3604, "building_subsistence_pastures", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3604, 2.97258, 2.97258, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3604, 4.45887, 4.45887, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3604, 1.48629, 1.48629, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3604, 2.97258, 2.97258, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3604, 2.97258, 2.97258, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3604, 2.97258, 2.97258, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3604, 9.86896, 9.86896, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3604, 2.97258, 2.97258, 6);
INSERT INTO building(id, name, level) VALUES (3606, "building_subsistence_pastures", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3606, 2.77479, 2.77479, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3606, 4.16218, 4.16218, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3606, 1.38739, 1.38739, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3606, 2.77479, 2.77479, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3606, 2.77479, 2.77479, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3606, 2.77479, 2.77479, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3606, 9.2123, 9.2123, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3606, 2.77479, 2.77479, 9);
INSERT INTO building(id, name, level) VALUES (3609, "building_subsistence_pastures", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3609, 0.18, 0.18, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3609, 0.27, 0.27, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3609, 0.09, 0.09, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3609, 0.18, 0.18, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3609, 0.18, 0.18, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3609, 0.18, 0.18, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3609, 0.5976, 0.5976, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3609, 0.18, 0.18, 20);
INSERT INTO building(id, name, level) VALUES (3610, "building_subsistence_pastures", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3610, 1.85625, 1.85625, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3610, 2.78437, 2.78437, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3610, 0.92812, 0.92812, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3610, 1.85625, 1.85625, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3610, 1.85625, 1.85625, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3610, 1.85625, 1.85625, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3610, 6.16275, 6.16275, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3610, 1.85625, 1.85625, 50);
INSERT INTO building(id, name, level) VALUES (3611, "building_subsistence_pastures", 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3611, 1.9983, 1.9983, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3611, 2.99745, 2.99745, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3611, 0.99915, 0.99915, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3611, 1.9983, 1.9983, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3611, 1.9983, 1.9983, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3611, 1.9983, 1.9983, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3611, 6.63435, 6.63435, 30);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3611, 1.9983, 1.9983, 30);
INSERT INTO building(id, name, level) VALUES (3612, "building_subsistence_farms", 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3612, 44.4165, 44.4165, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3612, 8.8833, 8.8833, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3612, 8.8833, 8.8833, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3612, 8.8833, 8.8833, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3612, 8.8833, 8.8833, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3612, 8.8833, 8.8833, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3612, 8.8833, 8.8833, 60);
INSERT INTO building(id, name, level) VALUES (3613, "building_subsistence_farms", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3613, 77.122, 77.122, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3613, 15.4244, 15.4244, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3613, 15.4244, 15.4244, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3613, 15.4244, 15.4244, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3613, 15.4244, 15.4244, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3613, 15.4244, 15.4244, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3613, 15.4244, 15.4244, 40);
INSERT INTO building(id, name, level) VALUES (3614, "building_subsistence_farms", 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3614, 167.25042, 167.25042, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3614, 33.45008, 33.45008, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3614, 33.45008, 33.45008, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3614, 33.45008, 33.45008, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3614, 33.45008, 33.45008, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3614, 33.45008, 33.45008, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3614, 33.45008, 33.45008, 67);
INSERT INTO building(id, name, level) VALUES (3617, "building_subsistence_farms", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3617, 42.125, 42.125, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3617, 8.425, 8.425, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3617, 8.425, 8.425, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3617, 8.425, 8.425, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3617, 8.425, 8.425, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3617, 8.425, 8.425, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3617, 8.425, 8.425, 40);
INSERT INTO building(id, name, level) VALUES (3618, "building_subsistence_pastures", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3618, 1.0844, 1.0844, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3618, 1.6266, 1.6266, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3618, 0.5422, 0.5422, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3618, 1.0844, 1.0844, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3618, 1.0844, 1.0844, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3618, 1.0844, 1.0844, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3618, 3.6002, 3.6002, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3618, 1.0844, 1.0844, 40);
INSERT INTO building(id, name, level) VALUES (3619, "building_subsistence_pastures", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3619, 0.3721, 0.3721, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3619, 0.55815, 0.55815, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3619, 0.18605, 0.18605, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3619, 0.3721, 0.3721, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3619, 0.3721, 0.3721, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3619, 0.3721, 0.3721, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3619, 1.23537, 1.23537, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3619, 0.3721, 0.3721, 20);
INSERT INTO building(id, name, level) VALUES (3620, "building_subsistence_farms", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3620, 89.177, 89.177, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3620, 17.8354, 17.8354, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3620, 17.8354, 17.8354, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3620, 17.8354, 17.8354, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3620, 17.8354, 17.8354, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3620, 17.8354, 17.8354, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3620, 17.8354, 17.8354, 40);
INSERT INTO building(id, name, level) VALUES (3621, "building_subsistence_farms", 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3621, 344.8476, 344.8476, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3621, 68.96952, 68.96952, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3621, 68.96952, 68.96952, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3621, 68.96952, 68.96952, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3621, 68.96952, 68.96952, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3621, 68.96952, 68.96952, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3621, 68.96952, 68.96952, 144);
INSERT INTO building(id, name, level) VALUES (3622, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3622, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3622, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (3624, "building_subsistence_farms", 163);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3624, 401.12262, 401.12262, 163);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3624, 80.22452, 80.22452, 163);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3624, 80.22452, 80.22452, 163);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3624, 80.22452, 80.22452, 163);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3624, 80.22452, 80.22452, 163);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3624, 80.22452, 80.22452, 163);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3624, 80.22452, 80.22452, 163);
INSERT INTO building(id, name, level) VALUES (3625, "building_urban_center", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3625, 43.13069607843137, 43.99331, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3625, 14.376892156862745, 14.66443, 3);
INSERT INTO building(id, name, level) VALUES (3626, "building_subsistence_farms", 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3626, 249.715, 249.715, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3626, 49.943, 49.943, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3626, 49.943, 49.943, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3626, 49.943, 49.943, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3626, 49.943, 49.943, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3626, 49.943, 49.943, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3626, 49.943, 49.943, 100);
INSERT INTO building(id, name, level) VALUES (3627, "building_subsistence_farms", 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3627, 155.04142, 155.04142, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3627, 31.00828, 31.00828, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3627, 31.00828, 31.00828, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3627, 31.00828, 31.00828, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3627, 31.00828, 31.00828, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3627, 31.00828, 31.00828, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3627, 31.00828, 31.00828, 77);
INSERT INTO building(id, name, level) VALUES (3628, "building_subsistence_farms", 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3628, 286.87612, 286.87612, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3628, 57.37522, 57.37522, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3628, 57.37522, 57.37522, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3628, 57.37522, 57.37522, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3628, 57.37522, 57.37522, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3628, 57.37522, 57.37522, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3628, 57.37522, 57.37522, 115);
INSERT INTO building(id, name, level) VALUES (3629, "building_subsistence_farms", 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3629, 396.23992, 396.23992, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3629, 79.24798, 79.24798, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3629, 79.24798, 79.24798, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3629, 79.24798, 79.24798, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3629, 79.24798, 79.24798, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3629, 79.24798, 79.24798, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3629, 79.24798, 79.24798, 159);
INSERT INTO building(id, name, level) VALUES (3630, "building_urban_center", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3630, 104.99999999999999, 111.3, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3630, 35.0, 37.1, 7);
INSERT INTO building(id, name, level) VALUES (3631, "building_subsistence_farms", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3631, 40.0, 40.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3631, 8.0, 8.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3631, 8.0, 8.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3631, 8.0, 8.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3631, 8.0, 8.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3631, 8.0, 8.0, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3631, 8.0, 8.0, 16);
INSERT INTO building(id, name, level) VALUES (3633, "building_subsistence_farms", 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3633, 109.7206, 109.7206, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3633, 21.94412, 21.94412, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3633, 21.94412, 21.94412, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3633, 21.94412, 21.94412, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3633, 21.94412, 21.94412, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3633, 21.94412, 21.94412, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3633, 21.94412, 21.94412, 53);
INSERT INTO building(id, name, level) VALUES (3635, "building_subsistence_farms", 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3635, 191.10975, 191.10975, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3635, 38.22195, 38.22195, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3635, 38.22195, 38.22195, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3635, 38.22195, 38.22195, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3635, 38.22195, 38.22195, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3635, 38.22195, 38.22195, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3635, 38.22195, 38.22195, 78);
INSERT INTO building(id, name, level) VALUES (3636, "building_subsistence_farms", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3636, 23.567, 23.567, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3636, 4.7134, 4.7134, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3636, 4.7134, 4.7134, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3636, 4.7134, 4.7134, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3636, 4.7134, 4.7134, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3636, 4.7134, 4.7134, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3636, 4.7134, 4.7134, 40);
INSERT INTO building(id, name, level) VALUES (3637, "building_subsistence_farms", 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3637, 178.3512, 205.10388, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3637, 35.670234782608695, 41.02077, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3637, 35.670234782608695, 41.02077, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3637, 35.670234782608695, 41.02077, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3637, 35.670234782608695, 41.02077, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3637, 35.670234782608695, 41.02077, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3637, 35.670234782608695, 41.02077, 72);
INSERT INTO building(id, name, level) VALUES (3638, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3638, 30.0, 30.3, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3638, 10.0, 10.1, 2);
INSERT INTO building(id, name, level) VALUES (3639, "building_subsistence_farms", 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3639, 491.24904347826094, 564.9364, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3639, 98.24980869565218, 112.98728, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3639, 98.24980869565218, 112.98728, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3639, 98.24980869565218, 112.98728, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3639, 98.24980869565218, 112.98728, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3639, 98.24980869565218, 112.98728, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3639, 98.24980869565218, 112.98728, 197);
INSERT INTO building(id, name, level) VALUES (3640, "building_subsistence_farms", 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3640, 98.811, 98.811, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3640, 19.7622, 19.7622, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3640, 19.7622, 19.7622, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3640, 19.7622, 19.7622, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3640, 19.7622, 19.7622, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3640, 19.7622, 19.7622, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3640, 19.7622, 19.7622, 60);
INSERT INTO building(id, name, level) VALUES (3641, "building_subsistence_farms", 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3641, 88.407, 101.66805, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3641, 17.6814, 20.33361, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3641, 17.6814, 20.33361, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3641, 17.6814, 20.33361, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3641, 17.6814, 20.33361, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3641, 17.6814, 20.33361, 36);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3641, 17.6814, 20.33361, 36);
INSERT INTO building(id, name, level) VALUES (3642, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3642, 30.0, 30.3, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3642, 10.0, 10.1, 2);
INSERT INTO building(id, name, level) VALUES (3643, "building_subsistence_farms", 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3643, 122.5, 122.5, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3643, 24.5, 24.5, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3643, 24.5, 24.5, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3643, 24.5, 24.5, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3643, 24.5, 24.5, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3643, 24.5, 24.5, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3643, 24.5, 24.5, 49);
INSERT INTO building(id, name, level) VALUES (3644, "building_subsistence_farms", 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3644, 363.6349, 363.6349, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3644, 72.72698, 72.72698, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3644, 72.72698, 72.72698, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3644, 72.72698, 72.72698, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3644, 72.72698, 72.72698, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3644, 72.72698, 72.72698, 146);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3644, 72.72698, 72.72698, 146);
INSERT INTO building(id, name, level) VALUES (3645, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3645, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3645, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (3646, "building_subsistence_farms", 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3646, 164.63562, 164.63562, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3646, 32.92712, 32.92712, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3646, 32.92712, 32.92712, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3646, 32.92712, 32.92712, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3646, 32.92712, 32.92712, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3646, 32.92712, 32.92712, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3646, 32.92712, 32.92712, 77);
INSERT INTO building(id, name, level) VALUES (3647, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3647, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3647, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (3648, "building_subsistence_farms", 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3648, 281.8898, 281.8898, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3648, 56.37796, 56.37796, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3648, 56.37796, 56.37796, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3648, 56.37796, 56.37796, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3648, 56.37796, 56.37796, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3648, 56.37796, 56.37796, 113);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3648, 56.37796, 56.37796, 113);
INSERT INTO building(id, name, level) VALUES (3649, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3649, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3649, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (3740, "building_subsistence_farms", 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3740, 224.46837, 224.46837, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3740, 44.89367, 44.89367, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3740, 44.89367, 44.89367, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3740, 44.89367, 44.89367, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3740, 44.89367, 44.89367, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3740, 44.89367, 44.89367, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3740, 44.89367, 44.89367, 95);
INSERT INTO building(id, name, level) VALUES (3741, "building_subsistence_farms", 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3741, 142.10527, 142.10527, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3741, 28.42105, 28.42105, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3741, 28.42105, 28.42105, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3741, 28.42105, 28.42105, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3741, 28.42105, 28.42105, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3741, 28.42105, 28.42105, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3741, 28.42105, 28.42105, 57);
INSERT INTO building(id, name, level) VALUES (3742, "building_subsistence_farms", 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3742, 188.68272, 188.68272, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3742, 37.73654, 37.73654, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3742, 37.73654, 37.73654, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3742, 37.73654, 37.73654, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3742, 37.73654, 37.73654, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3742, 37.73654, 37.73654, 77);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3742, 37.73654, 37.73654, 77);
INSERT INTO building(id, name, level) VALUES (3743, "building_subsistence_farms", 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3743, 244.47325, 244.47325, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3743, 48.89465, 48.89465, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3743, 48.89465, 48.89465, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3743, 48.89465, 48.89465, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3743, 48.89465, 48.89465, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3743, 48.89465, 48.89465, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3743, 48.89465, 48.89465, 98);
INSERT INTO building(id, name, level) VALUES (3744, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3744, 12.228, 12.228, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3744, 4.076, 4.076, 1);
INSERT INTO building(id, name, level) VALUES (3745, "building_subsistence_farms", 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3745, 205.1373, 205.1373, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3745, 41.02746, 41.02746, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3745, 41.02746, 41.02746, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3745, 41.02746, 41.02746, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3745, 41.02746, 41.02746, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3745, 41.02746, 41.02746, 87);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3745, 41.02746, 41.02746, 87);
INSERT INTO building(id, name, level) VALUES (3746, "building_subsistence_farms", 185);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3746, 458.16637391304357, 526.89133, 185);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3746, 91.63326956521739, 105.37826, 185);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3746, 91.63326956521739, 105.37826, 185);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3746, 91.63326956521739, 105.37826, 185);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3746, 91.63326956521739, 105.37826, 185);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3746, 91.63326956521739, 105.37826, 185);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3746, 91.63326956521739, 105.37826, 185);
INSERT INTO building(id, name, level) VALUES (3747, "building_urban_center", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3747, 69.915, 72.7116, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3747, 23.305, 24.2372, 5);
INSERT INTO building(id, name, level) VALUES (3848, "building_barracks", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3848, 23.0, 12.84377512698252, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3848, 10.0, 1.2833806668388563, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3848, 6.0, 5.41500945199028, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3848, 6.0, 1.2325088338063617, 25);
INSERT INTO building(id, name, level) VALUES (3849, "building_barracks", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3849, 27.0, 15.077475149066435, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3849, 8.0, 1.0267045334710851, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3849, 12.0, 10.83001890398056, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3849, 12.0, 2.4650176676127233, 25);
INSERT INTO building(id, name, level) VALUES (3850, "building_barracks", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3850, 8.999822222222223, 5.025725774132274, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3850, 3.9999333333333333, 3.609946134555164, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3850, 3.9999333333333333, 0.8216588613283098, 7);
INSERT INTO building(id, name, level) VALUES (3851, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3851, 2.99997, 1.6752582638127713, 3);
INSERT INTO building(id, name, level) VALUES (3852, "building_barracks", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3852, 4.47161, 2.497058838937665, 7);
INSERT INTO building(id, name, level) VALUES (3853, "building_barracks", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3853, 21.9999, 12.285294278960988, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3853, 17.99993, 16.244965180860568, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3853, 17.99993, 3.6975121221493565, 13);
INSERT INTO building(id, name, level) VALUES (3854, "building_barracks", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3854, 14.0, 7.817950077293706, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3854, 12.0, 10.83001890398056, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3854, 12.0, 2.4650176676127233, 8);
INSERT INTO building(id, name, level) VALUES (3855, "building_barracks", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3855, 4.0, 2.2337000220839163, 4);
INSERT INTO building(id, name, level) VALUES (3856, "building_barracks", 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3856, 4.0, 2.2337000220839163, 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3856, 21.0, 2.695099400361598, 16);
INSERT INTO building(id, name, level) VALUES (3857, "building_barracks", 19);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3857, 18.44468, 10.299970530832692, 19);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3857, 10.06073, 9.079824673987028, 19);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3857, 10.06073, 2.066656433256779, 19);
INSERT INTO building(id, name, level) VALUES (3858, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3858, 1.594, 0.8901294588004407, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3858, 1.594, 1.438587511078751, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3858, 1.594, 0.3274365135145567, 1);
INSERT INTO building(id, name, level) VALUES (3859, "building_barracks", 19);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3859, 16.82264, 9.394182834877443, 19);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3859, 7.08314, 0.9090364936512978, 19);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3859, 14.16646, 12.785252466873704, 19);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3859, 12.39567, 2.546295462658084, 19);
INSERT INTO building(id, name, level) VALUES (3860, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3860, 6.0, 3.3505500331258746, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3860, 4.0, 3.610006301326853, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3860, 3.0, 0.6162544169031808, 5);
INSERT INTO building(id, name, level) VALUES (3861, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3861, 5.99994, 0.7700206998193128, 3);
INSERT INTO building(id, name, level) VALUES (3862, "building_barracks", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3862, 7.99992, 4.4673553701673905, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3862, 3.99996, 3.60997020126384, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3862, 3.99996, 0.821664339145349, 6);
INSERT INTO building(id, name, level) VALUES (3863, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3863, 1.0, 0.5584250055209791, 1);
INSERT INTO building(id, name, level) VALUES (3864, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3864, 4.999977777777778, 2.792112618160328, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3864, 3.999988888888889, 3.6099962735315714, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3864, 3.999988888888889, 0.8216702734471413, 3);
INSERT INTO building(id, name, level) VALUES (3865, "building_barracks", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3865, 3.0, 1.6752750165629373, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3865, 4.999911111111111, 0.6416789255912786, 6);
INSERT INTO building(id, name, level) VALUES (3866, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3866, 5.9999666666666664, 3.350531418959023, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3866, 5.9999666666666664, 5.414979368604435, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3866, 5.9999666666666664, 1.2325019865350626, 3);
INSERT INTO building(id, name, level) VALUES (3867, "building_barracks", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3867, 6.99993, 3.9089359488964672, 7);
INSERT INTO building(id, name, level) VALUES (3868, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3868, 1.0, 0.5584250055209791, 1);
INSERT INTO building(id, name, level) VALUES (3869, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3869, 2.0, 1.1168500110419581, 2);
INSERT INTO building(id, name, level) VALUES (3870, "building_barracks", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3870, 4.0, 2.2337000220839163, 4);
INSERT INTO building(id, name, level) VALUES (3871, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3871, 7.8048, 4.3583954830901375, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3871, 7.8048, 7.043844295148956, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3871, 6.8292, 1.4028415546384005, 5);
INSERT INTO building(id, name, level) VALUES (3872, "building_barracks", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3872, 6.41893, 3.5844910206887777, 7);
INSERT INTO building(id, name, level) VALUES (3873, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3873, 2.99997, 1.6752582638127713, 3);
INSERT INTO building(id, name, level) VALUES (3874, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3874, 1.0, 0.5584250055209791, 1);
INSERT INTO building(id, name, level) VALUES (3875, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3875, 1.0, 0.5584250055209791, 1);
INSERT INTO building(id, name, level) VALUES (3876, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3876, 1.0, 0.5584250055209791, 1);
INSERT INTO building(id, name, level) VALUES (3877, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3877, 1.0, 0.5584250055209791, 1);
INSERT INTO building(id, name, level) VALUES (3878, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3878, 1.0, 0.5584250055209791, 1);
INSERT INTO building(id, name, level) VALUES (3879, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3879, 1.0, 0.5584250055209791, 1);
INSERT INTO building(id, name, level) VALUES (3880, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3880, 1.0, 0.5584250055209791, 1);
INSERT INTO building(id, name, level) VALUES (3881, "building_barracks", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3881, 15.83977, 8.845323649701038, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3881, 14.07985, 12.707086805434223, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3881, 14.07985, 2.8922565839447496, 11);
INSERT INTO building(id, name, level) VALUES (3882, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3882, 2.0, 1.1168500110419581, 2);
INSERT INTO building(id, name, level) VALUES (3884, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3884, 1.94, 1.0833445107106994, 2);
INSERT INTO building(id, name, level) VALUES (3885, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3885, 1.9789999999999999, 1.1051230859260175, 2);
INSERT INTO building(id, name, level) VALUES (3886, "building_barracks", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3886, 8.0, 1.0267045334710851, 4);
INSERT INTO building(id, name, level) VALUES (3887, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3887, 2.0, 1.1168500110419581, 2);
INSERT INTO building(id, name, level) VALUES (3888, "building_naval_base", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3888, 44.0, 22.35995942943361, 20);
INSERT INTO building(id, name, level) VALUES (3889, "building_naval_base", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3889, 38.99986666666666, 19.818987191363334, 17);
INSERT INTO building(id, name, level) VALUES (3890, "building_naval_base", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3890, 6.999988888888889, 3.5572606264100366, 3);
INSERT INTO building(id, name, level) VALUES (3891, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3891, 1.0, 0.5081808961234912, 1);
INSERT INTO building(id, name, level) VALUES (3892, "building_naval_base", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3892, 22.49991, 11.4340244264979, 13);
INSERT INTO building(id, name, level) VALUES (3893, "building_naval_base", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3893, 7.0, 3.5572662728644375, 5);
INSERT INTO building(id, name, level) VALUES (3894, "building_naval_base", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3894, 21.99996, 11.17995938748096, 12);
INSERT INTO building(id, name, level) VALUES (3895, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3895, 1.0, 0.5081808961234912, 1);
INSERT INTO building(id, name, level) VALUES (3896, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3896, 1.0, 0.5081808961234912, 1);
INSERT INTO building(id, name, level) VALUES (4145, "building_trade_center", 77);
INSERT INTO building(id, name, level) VALUES (4215, "building_trade_center", 34);
INSERT INTO building(id, name, level) VALUES (4216, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4216, 13.271999999999998, 13.40472, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 4216, 4.4239999999999995, 4.46824, 2);
INSERT INTO building(id, name, level) VALUES (4217, "building_trade_center", 39);
INSERT INTO building(id, name, level) VALUES (4218, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4218, 30.0, 30.3, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 4218, 10.0, 10.1, 2);
INSERT INTO building(id, name, level) VALUES (4220, "building_trade_center", 27);
INSERT INTO building(id, name, level) VALUES (4221, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4221, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 4221, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (4222, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4222, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 4222, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (4237, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 4237, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (4306, "building_arts_academy", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 4306, 2.544, 2.714978091125578, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 4306, 1.0176, 1.0176, 1);
INSERT INTO building(id, name, level) VALUES (4311, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4311, 0.005792792792792792, 0.00522800462106569, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4311, 0.005792792792792792, 0.0021599445026051637, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 4311, 0.011594594594594594, 0.007393694029676818, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 4311, 0.023198198198198194, 0.0147931329653596, 2);
INSERT INTO building(id, name, level) VALUES (16781611, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16781611, 20.0, 40.01373170705813, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16781611, 40.0, 45.41260317017147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16781611, 50.0, 10.270906948386346, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16781611, 10.0, 3.728675579924933, 1);
INSERT INTO building(id, name, level) VALUES (16781914, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16781914, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 16781914, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (4883, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 4883, 20.0, 23.0, 1);
INSERT INTO building(id, name, level) VALUES (4956, "building_barracks", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4956, 6.26896, 3.500744022610797, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4956, 1.56724, 0.2011365516296529, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4956, 3.13448, 2.828873137845749, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4956, 3.13448, 0.6438790482315606, 8);
INSERT INTO building(id, name, level) VALUES (5039, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 5039, 0.3955, 0.22085708968354723, 2);
INSERT INTO building(id, name, level) VALUES (5099, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 5099, 5.36, 5.36, 1);
INSERT INTO building(id, name, level) VALUES (5208, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 5208, 14.8, 17.02, 1);
INSERT INTO building(id, name, level) VALUES (5257, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5257, 7.4346, 8.440613488223919, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5257, 4.9564, 1.018134463979642, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 5257, 14.8692, 8.961801695969463, 1);
INSERT INTO building(id, name, level) VALUES (5323, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 5323, 0.09749999999999999, 0.05444643803829545, 2);
INSERT INTO building(id, name, level) VALUES (5328, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5328, 20.0, 40.01373170705813, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5328, 40.0, 45.41260317017147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5328, 50.0, 10.270906948386346, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5328, 10.0, 3.728675579924933, 1);
INSERT INTO building(id, name, level) VALUES (5470, "building_trade_center", 5);
