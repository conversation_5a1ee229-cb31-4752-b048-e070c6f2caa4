
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 29.24798722030073, 198.59987168869344);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 20, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 8.392227943870793, 16.57616029297466);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 12.398386687412195, 25.372968457025358);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 43.179720955767095, 31.478947530175294);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 41.678220807417404, 27.111306724969545);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 11.760721859743237, 11.937533333333333);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 2.259910119047619);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 37.61176764061144, 19.05602983292313);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 41.083070963597685, 35.977225021750215);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 57.17882966542665, 9.855985);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 39.16607226950283, 24.207839983687343);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 10.546247222222222);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 2.6365618055555555);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (1879, "building_government_administrationlevel", 4);
INSERT INTO building(id, name, level) VALUES (1880, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1880, 30.0, 60.82407138378445, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1880, 20.0, 20.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1880, 60.0, 60.0, 1);
INSERT INTO building(id, name, level) VALUES (1881, "building_gold_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1881, 15.0, 30.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 1881, 30.0, 30.0, 3);
INSERT INTO building(id, name, level) VALUES (1882, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1882, 5.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1882, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (1883, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1883, 14.94225, 14.94225, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1883, 7.9692, 7.9692, 1);
INSERT INTO building(id, name, level) VALUES (1884, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1884, 29.88, 29.88, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1884, 4.98, 4.98, 1);
INSERT INTO building(id, name, level) VALUES (1885, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1885, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (1886, "building_naval_baselevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1886, 6.0, 0.0, 3);
INSERT INTO building(id, name, level) VALUES (1887, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1887, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1887, 2.5, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (1888, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1888, 29.88, 29.88, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1888, 4.98, 4.98, 1);
INSERT INTO building(id, name, level) VALUES (1889, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1889, 24.9, 24.9, 1);
INSERT INTO building(id, name, level) VALUES (1890, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1890, 10.0, 20.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1890, 120.0, 120.0, 2);
INSERT INTO building(id, name, level) VALUES (1891, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1891, 5.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1891, 2.5, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (3196, "building_subsistence_farmslevel", 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3196, 40.78635, 40.78635, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3196, 8.15727, 8.15727, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3196, 8.15727, 8.15727, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3196, 8.15727, 8.15727, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3196, 8.15727, 8.15727, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3196, 8.15727, 8.15727, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3196, 11.42017, 11.42017, 38);
INSERT INTO building(id, name, level) VALUES (3197, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3197, 10.0, 20.274690461261486, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3197, 50.0, 50.0, 2);
INSERT INTO building(id, name, level) VALUES (3198, "building_subsistence_farmslevel", 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3198, 19.432, 19.432, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3198, 3.8864, 3.8864, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3198, 3.8864, 3.8864, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3198, 3.8864, 3.8864, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3198, 3.8864, 3.8864, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3198, 3.8864, 3.8864, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3198, 5.44096, 5.44096, 28);
INSERT INTO building(id, name, level) VALUES (3199, "building_subsistence_pastureslevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3199, 0.996, 0.996, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3199, 1.494, 1.494, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3199, 0.498, 0.498, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3199, 0.996, 0.996, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3199, 0.996, 0.996, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3199, 0.996, 0.996, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3199, 2.64936, 2.64936, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3199, 1.3944, 1.3944, 2);
INSERT INTO building(id, name, level) VALUES (3999, "building_trade_centerlevel", 18);
INSERT INTO building(id, name, level) VALUES (4199, "building_conscription_centerlevel", 2);
