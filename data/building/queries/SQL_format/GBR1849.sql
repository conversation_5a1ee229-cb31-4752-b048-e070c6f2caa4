
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 64.77757597455745, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 49.55801986270991, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 76.59555338424802, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 111.83105538239691, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 27.748556391830434, 9046.380047154382);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 21.98634765363037, 716.3790429886083);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 30.306514778569877, 828.0450132338487);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 32.411765415774795, 630.2867381375303);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 27.75676168939441, 201.49850290986245);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 41.99485180102116, 4426.346606719059);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 39.61155985582001, 3854.4095821741394);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 38.43703526307331, 393.3288909103887);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 15.520149259497158, 1117.9015918268556);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 14.087419906338658, 341.62368589284785);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 75.75911756081808, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 51.40166840549617, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 32.536791150638074, 100.01926459395678);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 41.304089976983306, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 56.80715717964449, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 68.84223368020237, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 19.07093545631391, 6.65616774384414);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 69.81160311306833, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 56.553446154124956, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 66.8011639669806, 143.87322163515634);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 19.25311539522075, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 41.2923578930331, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 38.28357396924668, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 111.7511139691621, 603.5769800514589);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 40.64930004830268, 877.119353593836);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 49.92886120546713, 777.3620892228545);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 30.55085036042051, 1303.4961030509955);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 55.11842978527874, 42.589179348158325);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 85.5595253937969, 1148.9261404777492);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 79.16179406292612, 574.288826174091);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 43.102630204786564, 124.84844734432288);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 66.89324478361742, 431.23110985227356);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 87.5, 1143.9274977875862);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 95.10652768624469, 926.7259340379752);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 96.14505185583599, 1115.4071450686554);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 350.0, 1.0779669791666666);
INSERT INTO building(id, name, level) VALUES (85, "building_government_administrationlevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 85, 300.0, 188.22889545926296, 15);
INSERT INTO building(id, name, level) VALUES (86, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 86, 37.68956521739131, 11.812495774088307, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 86, 75.37913043478262, 13.030781534550544, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 86, 94.22391304347828, 90.5481984433377, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 86, 18.844782608695656, 18.090848445351998, 3);
INSERT INTO building(id, name, level) VALUES (87, "building_universitylevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 87, 49.99999999999999, 31.37148257654382, 10);
INSERT INTO building(id, name, level) VALUES (88, "building_paper_millslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 88, 300.0, 51.8609652010698, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 88, 100.0, 0.0, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 88, 100.0, 96.71098193876571, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 88, 1000.0000000000001, 379.9932344637411, 10);
INSERT INTO building(id, name, level) VALUES (89, "building_textile_millslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 89, 500.00000000000006, 156.70777449878355, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 89, 100.0, 0.0, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 89, 100.0, 0.0, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 89, 50.0, 47.99962095875872, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 89, 800.0, 254.6815936345483, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 89, 200.0, 63.67039840863708, 10);
INSERT INTO building(id, name, level) VALUES (90, "building_shipyardslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 90, 70.782, 22.18417938914579, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 90, 141.564, 24.47215225908082, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 90, 70.782, 2.742806847008632, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 90, 35.391, 27.765029859883843, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 90, 247.737, 81.10648182477972, 5);
INSERT INTO building(id, name, level) VALUES (91, "building_furniture_manufacturieslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 91, 150.0, 47.01233234963505, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 91, 600.0, 103.7219304021396, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 91, 150.0, 5.812509212106113, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 91, 150.0, 143.99886287627615, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 91, 1350.0, 501.1969208367425, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 91, 300.0, 111.37709351927612, 15);
INSERT INTO building(id, name, level) VALUES (92, "building_naval_baselevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 92, 50.0, 10.199396943680766, 25);
INSERT INTO building(id, name, level) VALUES (93, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 93, 40.0, 24.267801545160008, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 93, 40.0, 52.288067691020856, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 93, 40.0, 35.11010041374687, 25);
INSERT INTO building(id, name, level) VALUES (94, "building_rye_farmlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 94, 39.3751968503937, 75.69083533536379, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 94, 7.87503937007874, 7.559978095981631, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 94, 393.752, 385.8754675175316, 8);
INSERT INTO building(id, name, level) VALUES (95, "building_livestock_ranchlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 95, 46.2745, 22.38710231319533, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 95, 46.2745, 44.42316920112161, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 95, 185.098, 133.62054302863388, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 95, 23.13725, 16.702567878579234, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 95, 115.68625, 83.51283939289617, 5);
INSERT INTO building(id, name, level) VALUES (96, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 96, 9.782594059405941, 6.380400730748405, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 96, 97.826, 63.80404605320982, 2);
INSERT INTO building(id, name, level) VALUES (97, "building_railwaylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 97, 15.0, 2.59304826005349, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 97, 30.0, 26.72648377158924, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 97, 30.0, 23.535669966842285, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 97, 195.0, 120.13787556016676, 3);
INSERT INTO building(id, name, level) VALUES (98, "building_portlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 98, 30.0, 19.566591515510137, 6);
INSERT INTO building(id, name, level) VALUES (99, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 99, 80.0, 50.19437212247012, 4);
INSERT INTO building(id, name, level) VALUES (100, "building_textile_millslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 100, 900.0, 282.0739940978103, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 100, 150.0, 0.0, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 100, 75.0, 71.99943143813807, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 100, 1500.0, 636.7039840863706, 15);
INSERT INTO building(id, name, level) VALUES (101, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 101, 37.68956521739131, 11.812495774088307, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 101, 75.37913043478262, 13.030781534550544, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 101, 94.22391304347828, 90.5481984433377, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 101, 18.844782608695656, 18.090848445351998, 3);
INSERT INTO building(id, name, level) VALUES (102, "building_arms_industrylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 102, 58.95000000000001, 56.65033562946701, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 102, 58.95000000000001, 2.284316120357703, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 102, 58.95000000000001, 48.87353205092429, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 102, 29.475000000000005, 28.295776555188272, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 102, 147.375, 102.74983556945344, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 102, 88.42500000000001, 61.64990134167207, 6);
INSERT INTO building(id, name, level) VALUES (103, "building_munition_plantslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 103, 38.73719819819819, 17.114541538171874, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 103, 38.73719819819819, 56.59241547161468, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 103, 96.84299999999999, 69.8146779177859, 2);
INSERT INTO building(id, name, level) VALUES (104, "building_coal_minelevel", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 104, 255.0, 244.79806688966946, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 104, 1020.0, 979.1922675586778, 17);
INSERT INTO building(id, name, level) VALUES (105, "building_iron_minelevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 105, 165.0, 146.9956607437408, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 105, 165.0, 158.39874916390377, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 105, 660.0, 610.7888198152892, 11);
INSERT INTO building(id, name, level) VALUES (106, "building_rye_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 106, 24.625500000000002, 47.33753262575408, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 106, 4.925096491228071, 4.728055295285198, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 106, 246.25500000000002, 241.3289665919913, 5);
INSERT INTO building(id, name, level) VALUES (107, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 107, 27.762598214285713, 13.43124456673434, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 107, 27.762598214285713, 26.651883822320517, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 107, 111.05039285714285, 80.16625677810971, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 107, 13.881294642857142, 10.020778874536935, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 107, 69.4065, 50.10391370904535, 3);
INSERT INTO building(id, name, level) VALUES (108, "building_barrackslevel", 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 108, 18.0, 10.920510695322003, 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 108, 18.0, 23.529630460959385, 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 108, 18.0, 15.79954518618609, 18);
INSERT INTO building(id, name, level) VALUES (109, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 109, 20.0, 4.079758777472306, 10);
INSERT INTO building(id, name, level) VALUES (110, "building_railwaylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 110, 25.0, 4.3217471000891505, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 110, 50.0, 44.5441396193154, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 110, 50.0, 39.22611661140381, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 110, 325.0, 200.22979260027796, 5);
INSERT INTO building(id, name, level) VALUES (111, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 111, 12.75, 8.315801394091809, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 111, 127.50000000000001, 83.1580139409181, 3);
INSERT INTO building(id, name, level) VALUES (112, "building_portlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 112, 25.0, 16.305492929591782, 5);
INSERT INTO building(id, name, level) VALUES (113, "building_paper_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 113, 150.0, 25.9304826005349, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 113, 50.00000000000001, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 113, 50.00000000000001, 48.355490969382856, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 113, 500.00000000000006, 189.9966172318705, 5);
INSERT INTO building(id, name, level) VALUES (114, "building_barrackslevel", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 114, 27.2, 16.502105050708803, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 114, 27.2, 35.55588602989418, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 114, 27.2, 23.874868281347872, 17);
INSERT INTO building(id, name, level) VALUES (115, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 115, 18.51159459459459, 8.955709130721731, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 115, 18.51159459459459, 17.770990477654944, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 115, 74.04639639639639, 53.453412223794764, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 115, 9.255792792792793, 6.681671650333816, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 115, 46.278999999999996, 33.408384265751906, 2);
INSERT INTO building(id, name, level) VALUES (116, "building_coal_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 116, 60.0, 57.599545150510465, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 116, 240.0, 230.39818060204186, 4);
INSERT INTO building(id, name, level) VALUES (117, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 117, 15.0, 14.399886287627616, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 117, 179.99999999999997, 172.79863545153137, 3);
INSERT INTO building(id, name, level) VALUES (118, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 118, 5.0, 0.8643494200178301, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 118, 10.0, 8.908827923863079, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 118, 10.0, 7.845223322280762, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 118, 65.0, 40.045958520055585, 1);
INSERT INTO building(id, name, level) VALUES (119, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 119, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (120, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 120, 100.0, 62.742965153087646, 5);
INSERT INTO building(id, name, level) VALUES (121, "building_tooling_workshopslevel", 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 121, 420.0, 72.60535128149772, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 121, 280.0, 232.13891389751996, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 121, 1120.0, 561.0849628370369, 14);
INSERT INTO building(id, name, level) VALUES (122, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 122, 25.12626086956522, 7.87496084469087, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 122, 50.25252173913044, 8.687147603930157, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 122, 62.81565217391305, 60.365187081188, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 122, 12.56313043478261, 12.060509978500217, 2);
INSERT INTO building(id, name, level) VALUES (123, "building_steel_millslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 123, 300.0, 267.2648377158924, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 123, 400.0, 384.3958312432027, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 123, 650.0, 601.8585204106522, 10);
INSERT INTO building(id, name, level) VALUES (124, "building_motor_industrylevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 124, 240.00000000000003, 198.97621191215998, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 124, 320.0, 265.30161588287996, 8);
INSERT INTO building(id, name, level) VALUES (125, "building_barrackslevel", 19);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 125, 19.0, 11.527205733951003, 19);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 125, 19.0, 24.836832153234905, 19);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 125, 19.0, 16.677297696529763, 19);
INSERT INTO building(id, name, level) VALUES (126, "building_naval_baselevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 126, 30.0, 6.11963816620846, 15);
INSERT INTO building(id, name, level) VALUES (127, "building_coal_minelevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 127, 150.0, 143.99886287627615, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 127, 600.0, 575.9954515051046, 10);
INSERT INTO building(id, name, level) VALUES (128, "building_iron_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 128, 75.0, 66.8162094289731, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 128, 75.0, 71.99943143813807, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 128, 300.0, 277.63128173422234, 5);
INSERT INTO building(id, name, level) VALUES (129, "building_rye_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 129, 18.767592920353984, 36.07689355237977, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 129, 3.7535132743362833, 3.6033442886362184, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 129, 187.67600000000002, 183.92176863056002, 4);
INSERT INTO building(id, name, level) VALUES (130, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 130, 27.762598214285713, 13.43124456673434, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 130, 27.762598214285713, 26.651883822320517, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 130, 111.05039285714285, 80.16625677810971, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 130, 13.881294642857142, 10.020778874536935, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 130, 69.4065, 50.10391370904535, 3);
INSERT INTO building(id, name, level) VALUES (131, "building_railwaylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 131, 30.0, 5.18609652010698, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 131, 60.0, 53.45296754317848, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 131, 60.0, 47.07133993368457, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 131, 390.0, 240.27575112033352, 6);
INSERT INTO building(id, name, level) VALUES (132, "building_portlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 132, 20.0, 13.044394343673424, 4);
INSERT INTO building(id, name, level) VALUES (133, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 133, 10.0, 6.066950386290002, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 133, 10.0, 13.072016922755214, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 133, 10.0, 8.777525103436718, 10);
INSERT INTO building(id, name, level) VALUES (134, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 134, 15.0, 14.399886287627616, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 134, 120.0, 115.19909030102093, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 134, 30.0, 28.799772575255233, 3);
INSERT INTO building(id, name, level) VALUES (135, "building_whaling_stationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 135, 8.438, 5.503429973595819, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (28, 135, 33.752, 22.013719894383275, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 135, 16.876, 11.006859947191638, 2);
INSERT INTO building(id, name, level) VALUES (136, "building_fishing_wharflevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 136, 20.0, 13.044394343673424, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 136, 200.0, 130.44394343673423, 4);
INSERT INTO building(id, name, level) VALUES (137, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 137, 120.0, 75.29155818370518, 6);
INSERT INTO building(id, name, level) VALUES (138, "building_arms_industrylevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 138, 78.57119672131148, 75.50610118865433, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 138, 78.57119672131148, 3.044638698325497, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 138, 78.57119672131148, 65.14082953754867, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 138, 39.28559836065574, 37.71387660899017, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 138, 196.428, 136.9495823663213, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 138, 117.85679508196722, 82.1697459909408, 8);
INSERT INTO building(id, name, level) VALUES (139, "building_textile_millslevel", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 139, 850.0, 266.403216647932, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 139, 170.0, 0.0, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 139, 170.0, 0.0, 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 139, 85.0, 81.59935562988981, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 139, 1360.0, 432.958709178732, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 139, 340.0, 108.239677294683, 17);
INSERT INTO building(id, name, level) VALUES (140, "building_furniture_manufacturieslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 140, 50.00000000000001, 15.670777449878354, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 140, 150.0, 25.9304826005349, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 140, 100.00000000000001, 3.8750061414040755, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 140, 100.00000000000001, 95.99924191751747, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 140, 450.00000000000006, 167.0656402789142, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 140, 200.00000000000003, 74.25139567951743, 5);
INSERT INTO building(id, name, level) VALUES (141, "building_chemical_plantslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 141, 59.99999999999999, 58.02658916325941, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 141, 29.999999999999996, 26.726483771589233, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 141, 19.999999999999996, 19.219791562160133, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 141, 150.0, 140.9491094940986, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 141, 39.99999999999999, 37.58642919842628, 2);
INSERT INTO building(id, name, level) VALUES (142, "building_coal_minelevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 142, 120.0, 115.19909030102093, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 142, 480.0, 460.7963612040837, 8);
INSERT INTO building(id, name, level) VALUES (143, "building_iron_minelevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 143, 135.0, 120.26917697215157, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 143, 135.0, 129.59897658864855, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 143, 540.0, 499.73630712160025, 9);
INSERT INTO building(id, name, level) VALUES (144, "building_rye_farmlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 144, 49.22849579831933, 94.63180548495417, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 144, 9.845697478991596, 9.451794941324058, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 144, 492.285, 482.4374340368254, 10);
INSERT INTO building(id, name, level) VALUES (145, "building_livestock_ranchlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 145, 46.276, 22.387827996962198, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 145, 46.276, 44.42460918975038, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 145, 185.104, 133.62487437342517, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 145, 23.138, 16.703109296678146, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 145, 115.69, 83.51554648339072, 5);
INSERT INTO building(id, name, level) VALUES (146, "building_railwaylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 146, 30.0, 5.18609652010698, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 146, 60.0, 53.45296754317848, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 146, 60.0, 47.07133993368457, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 146, 390.0, 240.27575112033352, 6);
INSERT INTO building(id, name, level) VALUES (147, "building_barrackslevel", 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 147, 44.8, 27.17993773057921, 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 147, 44.8, 58.56263581394336, 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 147, 44.8, 39.3233124633965, 28);
INSERT INTO building(id, name, level) VALUES (148, "building_portlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 148, 20.0, 13.044394343673424, 4);
INSERT INTO building(id, name, level) VALUES (149, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 149, 80.0, 50.19437212247012, 4);
INSERT INTO building(id, name, level) VALUES (150, "building_universitylevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 150, 39.99999999999999, 25.097186061235057, 8);
INSERT INTO building(id, name, level) VALUES (151, "building_furniture_manufacturieslevel", 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 151, 160.0, 50.14648783961073, 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 151, 800.0, 138.29590720285282, 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 151, 160.0, 153.59878706802792, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 151, 1760.0, 848.4830066101005, 16);
INSERT INTO building(id, name, level) VALUES (152, "building_glassworkslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 152, 120.00000000000001, 20.744386080427926, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 152, 180.0, 0.0, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 152, 60.00000000000001, 26.508692937376047, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 152, 120.00000000000001, 24.58725731839334, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 152, 240.00000000000003, 49.17451463678668, 6);
INSERT INTO building(id, name, level) VALUES (153, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 153, 290.368, 140.47689601136483, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 153, 36.296, 3.8845252122299216, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 153, 199.62800000000004, 83.37680611017188, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 153, 290.368, 97.6094840170908, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 153, 362.96000000000004, 122.01185502136352, 4);
INSERT INTO building(id, name, level) VALUES (154, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 154, 30.0, 26.72648377158924, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 154, 30.0, 28.799772575255233, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 154, 120.0, 111.05251269368894, 2);
INSERT INTO building(id, name, level) VALUES (155, "building_shipyardslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 155, 29.756396694214878, 9.326117406106745, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 155, 59.512793388429756, 10.287969689786042, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 155, 29.756396694214878, 1.1530621993613857, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 155, 14.878198347107439, 11.672278866624637, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 155, 37.19549586776859, 12.177413182378258, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 155, 66.95189256198347, 21.919343728280865, 2);
INSERT INTO building(id, name, level) VALUES (156, "building_barrackslevel", 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 156, 25.6, 15.531392988902406, 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 156, 25.6, 33.46436332225335, 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 156, 25.6, 22.470464264797997, 16);
INSERT INTO building(id, name, level) VALUES (157, "building_naval_baselevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 157, 50.0, 10.199396943680766, 25);
INSERT INTO building(id, name, level) VALUES (158, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 158, 9.382198347107439, 18.035374727721038, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 158, 1.8764380165289258, 1.8013662709198695, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 158, 93.822, 91.94520437592661, 2);
INSERT INTO building(id, name, level) VALUES (159, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 159, 27.76259836065574, 13.431244637546575, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 159, 27.76259836065574, 26.651883962834635, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 159, 111.05039344262296, 80.16625720076242, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 159, 13.881295081967213, 10.020779191526454, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 159, 69.4065, 50.10391370904535, 3);
INSERT INTO building(id, name, level) VALUES (160, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 160, 15.0, 9.783295757755068, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 160, 150.0, 97.83295757755069, 3);
INSERT INTO building(id, name, level) VALUES (161, "building_railwaylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 161, 10.0, 1.7286988400356602, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 161, 20.0, 17.817655847726158, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 161, 20.0, 15.690446644561524, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 161, 130.0, 80.09191704011117, 2);
INSERT INTO building(id, name, level) VALUES (162, "building_portlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 162, 25.0, 16.305492929591782, 5);
INSERT INTO building(id, name, level) VALUES (163, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 163, 120.0, 75.29155818370518, 6);
INSERT INTO building(id, name, level) VALUES (164, "building_tooling_workshopslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 164, 180.0, 31.11657912064188, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 164, 120.00000000000001, 99.48810595607999, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 164, 480.00000000000006, 240.46498407301584, 6);
INSERT INTO building(id, name, level) VALUES (165, "building_textile_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 165, 250.00000000000003, 78.35388724939178, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 165, 50.00000000000001, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 165, 50.00000000000001, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 165, 25.000000000000004, 23.999810479379367, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 165, 400.00000000000006, 127.3407968172742, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 165, 100.00000000000001, 31.83519920431855, 5);
INSERT INTO building(id, name, level) VALUES (166, "building_shipyardslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 166, 22.316, 6.994181391429706, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 166, 44.632, 7.7155286628471575, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 166, 39.053, 9.495452507496623, 4);
INSERT INTO building(id, name, level) VALUES (167, "building_barrackslevel", 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 167, 14.0, 8.493730540806002, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 167, 14.0, 18.300823691857296, 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 167, 14.0, 12.288535144811405, 14);
INSERT INTO building(id, name, level) VALUES (168, "building_naval_baselevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 168, 50.0, 10.199396943680766, 25);
INSERT INTO building(id, name, level) VALUES (169, "building_wheat_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 169, 23.454245614035088, 45.08603345990276, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 169, 4.690842105263158, 4.503172860600347, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 169, 164.17974561403508, 160.89552839280867, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 169, 37.52679824561404, 36.776120038659876, 5);
INSERT INTO building(id, name, level) VALUES (170, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 170, 27.764696428571426, 13.432259660098705, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 170, 27.764696428571426, 26.653898092128607, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 170, 111.05879464285714, 80.17232194990818, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 170, 13.882348214285713, 10.021539438056829, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 170, 69.41174999999998, 50.10770363573769, 3);
INSERT INTO building(id, name, level) VALUES (171, "building_sulfur_minelevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 171, 90.0, 80.17945131476772, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 171, 90.0, 86.3993177257657, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 171, 360.0, 333.15753808106683, 6);
INSERT INTO building(id, name, level) VALUES (172, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 172, 5.0, 0.8643494200178301, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 172, 10.0, 8.908827923863079, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 172, 10.0, 7.845223322280762, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 172, 65.0, 40.045958520055585, 1);
INSERT INTO building(id, name, level) VALUES (173, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 173, 10.0, 6.522197171836712, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 173, 100.0, 65.22197171836712, 2);
INSERT INTO building(id, name, level) VALUES (174, "building_portlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 174, 25.0, 16.305492929591782, 5);
INSERT INTO building(id, name, level) VALUES (175, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 175, 60.0, 37.64577909185259, 3);
INSERT INTO building(id, name, level) VALUES (176, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 176, 25.12626086956522, 7.87496084469087, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 176, 50.25252173913044, 8.687147603930157, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 176, 62.81565217391305, 60.365187081188, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 176, 12.56313043478261, 12.060509978500217, 2);
INSERT INTO building(id, name, level) VALUES (177, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 177, 160.00000000000003, 77.40626846559668, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 177, 160.00000000000003, 66.8257407659622, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 177, 140.0, 63.101504038807, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 177, 240.0, 108.17400692366914, 4);
INSERT INTO building(id, name, level) VALUES (178, "building_glassworkslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 178, 140.00000000000003, 24.201783760499247, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 178, 210.0, 0.0, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 178, 70.00000000000001, 30.926808426938727, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 178, 140.00000000000003, 28.68513353812557, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 178, 280.00000000000006, 57.37026707625114, 7);
INSERT INTO building(id, name, level) VALUES (179, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 179, 9.999999999999998, 9.599924191751743, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 179, 119.99999999999999, 115.19909030102093, 2);
INSERT INTO building(id, name, level) VALUES (180, "building_iron_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 180, 59.99999999999999, 53.45296754317847, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 180, 59.99999999999999, 57.59954515051045, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 180, 239.99999999999997, 222.10502538737782, 4);
INSERT INTO building(id, name, level) VALUES (181, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 181, 9.843297297297296, 18.92174400340329, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 181, 1.9686576576576575, 1.889896427302507, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 181, 98.43299999999999, 96.46396689833496, 2);
INSERT INTO building(id, name, level) VALUES (182, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 182, 9.2549, 4.477420462639065, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 182, 9.2549, 8.884633840224321, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 182, 37.0196, 26.724108605726773, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 182, 4.6274454545454535, 3.3405102943940346, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 182, 23.137245454545454, 16.70256459725742, 1);
INSERT INTO building(id, name, level) VALUES (183, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 183, 8.5, 5.543867596061206, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 183, 85.0, 55.438675960612066, 2);
INSERT INTO building(id, name, level) VALUES (184, "building_naval_baselevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 184, 30.0, 6.11963816620846, 15);
INSERT INTO building(id, name, level) VALUES (185, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 185, 15.0, 9.100425579435003, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 185, 15.0, 19.608025384132823, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 185, 15.0, 13.166287655155076, 15);
INSERT INTO building(id, name, level) VALUES (186, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 186, 5.0, 0.8643494200178301, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 186, 10.0, 8.908827923863079, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 186, 10.0, 7.845223322280762, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 186, 65.0, 40.045958520055585, 1);
INSERT INTO building(id, name, level) VALUES (187, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 187, 15.0, 9.783295757755068, 3);
INSERT INTO building(id, name, level) VALUES (188, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 188, 18.50779279279279, 8.953869860153462, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 188, 18.50779279279279, 17.767340776746007, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 188, 74.03119819819818, 53.442440784361054, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 188, 9.253891891891891, 6.6802994074645135, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 188, 46.26949549549549, 33.4015230514054, 2);
INSERT INTO building(id, name, level) VALUES (189, "building_barrackslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 189, 6.0, 3.640170231774001, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 189, 6.0, 7.8432101536531285, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 189, 6.0, 5.26651506206203, 6);
INSERT INTO building(id, name, level) VALUES (190, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 190, 9.999999999999998, 9.599924191751743, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 190, 119.99999999999999, 115.19909030102093, 2);
INSERT INTO building(id, name, level) VALUES (191, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 191, 10.0, 6.522197171836712, 2);
INSERT INTO building(id, name, level) VALUES (192, "building_rye_farmlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 192, 1.0, 0.9599924191751744, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 192, 10.0, 9.599924191751745, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 192, 15.0, 14.399886287627616, 1);
INSERT INTO building(id, name, level) VALUES (193, "building_barrackslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 193, 3.0, 1.8200851158870004, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 193, 3.0, 3.9216050768265642, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 193, 3.0, 2.633257531031015, 3);
INSERT INTO building(id, name, level) VALUES (194, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 194, 10.0, 2.039879388736153, 5);
INSERT INTO building(id, name, level) VALUES (195, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 195, 10.0, 6.522197171836712, 2);
INSERT INTO building(id, name, level) VALUES (196, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 196, 5.0, 3.1371482576543825, 1);
INSERT INTO building(id, name, level) VALUES (197, "building_lead_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 197, 30.0, 26.72648377158924, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 197, 30.0, 28.799772575255233, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 197, 120.0, 111.05251269368894, 3);
INSERT INTO building(id, name, level) VALUES (198, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 198, 160.00000000000003, 77.40626846559668, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 198, 160.00000000000003, 66.8257407659622, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 198, 140.0, 63.101504038807, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 198, 240.0, 108.17400692366914, 4);
INSERT INTO building(id, name, level) VALUES (199, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 199, 9.845495495495495, 18.925969593906025, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 199, 1.969099099099099, 1.8903202077398007, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 199, 59.072999999999986, 57.89131608896752, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 199, 29.536495495495494, 28.945653630086426, 2);
INSERT INTO building(id, name, level) VALUES (200, "building_naval_baselevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 200, 30.0, 6.11963816620846, 15);
INSERT INTO building(id, name, level) VALUES (201, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 201, 5.0, 3.033475193145001, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 201, 5.0, 6.536008461377607, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 201, 5.0, 4.388762551718359, 5);
INSERT INTO building(id, name, level) VALUES (202, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 202, 15.0, 9.783295757755068, 3);
INSERT INTO building(id, name, level) VALUES (203, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 203, 9.843297297297296, 18.92174400340329, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 203, 1.9686576576576575, 1.889896427302507, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 203, 59.05979279279279, 57.878373075965236, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 203, 29.52989189189189, 28.939182123585276, 2);
INSERT INTO building(id, name, level) VALUES (204, "building_barrackslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 204, 2.0, 1.2133900772580004, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 204, 2.0, 2.614403384551043, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 204, 2.0, 1.7555050206873435, 2);
INSERT INTO building(id, name, level) VALUES (205, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 205, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (134218296, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 134218296, 10.0, 1.7286988400356602, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 134218296, 20.0, 17.817655847726158, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 134218296, 10.0, 1.070235070594534, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 134218296, 140.0, 54.63622189430194, 2);
INSERT INTO building(id, name, level) VALUES (616, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 616, 5.0, 3.261098585918356, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 616, 50.0, 32.61098585918356, 1);
INSERT INTO building(id, name, level) VALUES (617, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 617, 10.0, 2.039879388736153, 5);
INSERT INTO building(id, name, level) VALUES (618, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 618, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (775, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 775, 10.0, 2.039879388736153, 5);
INSERT INTO building(id, name, level) VALUES (776, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 776, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (16778022, "building_munition_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 16778022, 19.4312, 8.584928570079024, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 16778022, 19.4312, 28.387663400064596, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 16778022, 48.578, 35.02016071259878, 1);
INSERT INTO building(id, name, level) VALUES (16778068, "building_tooling_workshopslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16778068, 89.99999999999999, 15.558289560320938, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16778068, 60.0, 49.74405297803999, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16778068, 240.0, 120.23249203650789, 3);
INSERT INTO building(id, name, level) VALUES (1261, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1261, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (16778534, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16778534, 29.999999999999996, 26.726483771589233, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16778534, 59.99999999999999, 57.659374686480405, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 16778534, 89.99999999999999, 83.33425667224415, 1);
INSERT INTO building(id, name, level) VALUES (1577, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1577, 10.0, 6.522197171836712, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1577, 100.0, 65.22197171836712, 2);
INSERT INTO building(id, name, level) VALUES (1583, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1583, 10.0, 2.039879388736153, 5);
INSERT INTO building(id, name, level) VALUES (1584, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1584, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (1651, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1679, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1679, 26.535, 26.535, 1);
INSERT INTO building(id, name, level) VALUES (1680, "building_sugar_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1680, 26.5764, 26.5764, 1);
INSERT INTO building(id, name, level) VALUES (1681, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1681, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (1688, "building_sugar_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1688, 79.58789215686274, 81.17965, 3);
INSERT INTO building(id, name, level) VALUES (1689, "building_coffee_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1689, 35.37239603960396, 35.72612, 2);
INSERT INTO building(id, name, level) VALUES (1690, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1690, 44.215495049504945, 44.65765, 2);
INSERT INTO building(id, name, level) VALUES (1691, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1691, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (1692, "building_banana_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1692, 26.5293, 26.5293, 1);
INSERT INTO building(id, name, level) VALUES (1693, "building_sugar_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1693, 79.58789215686274, 81.17965, 3);
INSERT INTO building(id, name, level) VALUES (1694, "building_coffee_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1694, 88.431, 91.96824, 5);
INSERT INTO building(id, name, level) VALUES (1695, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1695, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (1713, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1713, 53.14079207920792, 53.6722, 2);
INSERT INTO building(id, name, level) VALUES (1714, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1714, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (1836, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1836, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (2264, "building_sugar_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2264, 26.5647, 26.5647, 1);
INSERT INTO building(id, name, level) VALUES (2265, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2265, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (2658, "building_tea_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2658, 19.6862, 19.6862, 1);
INSERT INTO building(id, name, level) VALUES (2659, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2659, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (2777, "building_easter_island_headslevel", 1);
INSERT INTO building(id, name, level) VALUES (2804, "building_subsistence_farmslevel", 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2804, 117.96332500000001, 141.55599, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2804, 23.592658333333333, 28.31119, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2804, 23.592658333333333, 28.31119, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2804, 23.592658333333333, 28.31119, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2804, 23.592658333333333, 28.31119, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2804, 23.592658333333333, 28.31119, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2804, 33.029725, 39.63567, 103);
INSERT INTO building(id, name, level) VALUES (2805, "building_urban_centerlevel", 24);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2805, 120.0, 20.74438608042792, 24);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2805, 240.0, 213.81187017271392, 24);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2805, 120.0, 12.84282084713441, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2805, 1680.0000000000002, 655.6346627316234, 24);
INSERT INTO building(id, name, level) VALUES (2806, "building_subsistence_farmslevel", 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2806, 100.94174545454545, 111.03592, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2806, 20.188345454545455, 22.20718, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2806, 20.188345454545455, 22.20718, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2806, 20.188345454545455, 22.20718, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2806, 20.188345454545455, 22.20718, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2806, 20.188345454545455, 22.20718, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2806, 28.263681818181816, 31.09005, 90);
INSERT INTO building(id, name, level) VALUES (2807, "building_urban_centerlevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2807, 54.99999999999999, 9.507843620196128, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2807, 109.99999999999999, 97.99710716249386, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2807, 54.99999999999999, 5.886292888269938, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2807, 769.9999999999999, 300.4992204186606, 11);
INSERT INTO building(id, name, level) VALUES (100666140, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 100666140, 10.0, 6.274296515308765, 2);
INSERT INTO building(id, name, level) VALUES (67111716, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 67111716, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 67111716, 20.0, 16.581350992679994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 67111716, 80.0, 40.07749734550263, 1);
INSERT INTO building(id, name, level) VALUES (2924, "building_subsistence_farmslevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2924, 7.249499999999999, 7.97445, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2924, 1.4498999999999997, 1.59489, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2924, 1.4498999999999997, 1.59489, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2924, 1.4498999999999997, 1.59489, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2924, 1.4498999999999997, 1.59489, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2924, 1.4498999999999997, 1.59489, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2924, 2.0298545454545454, 2.23284, 10);
INSERT INTO building(id, name, level) VALUES (2926, "building_subsistence_farmslevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2926, 5.551399999999999, 6.10654, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2926, 1.1102727272727273, 1.2213, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2926, 1.1102727272727273, 1.2213, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2926, 1.1102727272727273, 1.2213, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2926, 1.1102727272727273, 1.2213, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2926, 1.1102727272727273, 1.2213, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2926, 1.554390909090909, 1.70983, 8);
INSERT INTO building(id, name, level) VALUES (3009, "building_subsistence_farmslevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3009, 12.049999999999999, 13.255, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3009, 2.4099999999999997, 2.651, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3009, 2.4099999999999997, 2.651, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3009, 2.4099999999999997, 2.651, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3009, 2.4099999999999997, 2.651, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3009, 2.4099999999999997, 2.651, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3009, 3.3739999999999997, 3.7114, 5);
INSERT INTO building(id, name, level) VALUES (3061, "building_subsistence_farmslevel", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3061, 0.6432, 0.70752, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3061, 0.1286363636363636, 0.1415, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3061, 0.1286363636363636, 0.1415, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3061, 0.1286363636363636, 0.1415, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3061, 0.1286363636363636, 0.1415, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3061, 0.1286363636363636, 0.1415, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3061, 0.18009090909090908, 0.1981, 16);
INSERT INTO building(id, name, level) VALUES (3130, "building_subsistence_farmslevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3130, 0.4105, 0.45155, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3130, 0.08209999999999999, 0.09031, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3130, 0.08209999999999999, 0.09031, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3130, 0.08209999999999999, 0.09031, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3130, 0.08209999999999999, 0.09031, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3130, 0.08209999999999999, 0.09031, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3130, 0.11493636363636361, 0.12643, 10);
INSERT INTO building(id, name, level) VALUES (3169, "building_subsistence_farmslevel", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3169, 9.873445454545454, 10.86079, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3169, 1.9746818181818178, 2.17215, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3169, 1.9746818181818178, 2.17215, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3169, 1.9746818181818178, 2.17215, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3169, 1.9746818181818178, 2.17215, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3169, 1.9746818181818178, 2.17215, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3169, 2.7645636363636363, 3.04102, 9);
INSERT INTO building(id, name, level) VALUES (3182, "building_subsistence_farmslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3182, 86.372, 95.0092, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3182, 17.2744, 19.00184, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3182, 17.2744, 19.00184, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3182, 17.2744, 19.00184, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3182, 17.2744, 19.00184, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3182, 17.2744, 19.00184, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3182, 24.184154545454543, 26.60257, 44);
INSERT INTO building(id, name, level) VALUES (3183, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3183, 15.0, 2.59304826005349, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3183, 30.0, 26.72648377158924, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3183, 15.0, 1.6053526058918013, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3183, 209.99999999999997, 81.9543328414529, 3);
INSERT INTO building(id, name, level) VALUES (3184, "building_subsistence_farmslevel", 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3184, 105.79911818181817, 116.37903, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3184, 21.15981818181818, 23.2758, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3184, 21.15981818181818, 23.2758, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3184, 21.15981818181818, 23.2758, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3184, 21.15981818181818, 23.2758, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3184, 21.15981818181818, 23.2758, 95);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3184, 29.623754545454542, 32.58613, 95);
INSERT INTO building(id, name, level) VALUES (3185, "building_urban_centerlevel", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3185, 64.99999999999999, 11.236542460231787, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3185, 129.99999999999997, 115.81476301022, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3185, 64.99999999999999, 6.956527958864471, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3185, 910.0, 355.1354423129626, 13);
INSERT INTO building(id, name, level) VALUES (3186, "building_subsistence_farmslevel", 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3186, 22.4301, 24.67311, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3186, 4.4860181818181815, 4.93462, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3186, 4.4860181818181815, 4.93462, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3186, 4.4860181818181815, 4.93462, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3186, 4.4860181818181815, 4.93462, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3186, 4.4860181818181815, 4.93462, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3186, 6.280427272727272, 6.90847, 42);
INSERT INTO building(id, name, level) VALUES (3187, "building_subsistence_farmslevel", 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3187, 165.63179999999997, 182.19498, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3187, 33.12635454545454, 36.43899, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3187, 33.12635454545454, 36.43899, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3187, 33.12635454545454, 36.43899, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3187, 33.12635454545454, 36.43899, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3187, 33.12635454545454, 36.43899, 123);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3187, 46.37689999999999, 51.01459, 123);
INSERT INTO building(id, name, level) VALUES (3188, "building_urban_centerlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3188, 59.99999999999999, 10.37219304021396, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3188, 119.99999999999999, 106.90593508635693, 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3188, 59.99999999999999, 6.421410423567205, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3188, 839.9999999999999, 327.8173313658116, 12);
INSERT INTO building(id, name, level) VALUES (3189, "building_subsistence_farmslevel", 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3189, 59.68935, 71.62722, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3189, 11.937866666666668, 14.32544, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3189, 11.937866666666668, 14.32544, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3189, 11.937866666666668, 14.32544, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3189, 11.937866666666668, 14.32544, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3189, 11.937866666666668, 14.32544, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3189, 16.713016666666668, 20.05562, 63);
INSERT INTO building(id, name, level) VALUES (3190, "building_urban_centerlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3190, 49.99999999999999, 8.6434942001783, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3190, 99.99999999999999, 89.08827923863078, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3190, 49.99999999999999, 5.351175352972671, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3190, 700.0, 273.1811094715097, 10);
INSERT INTO building(id, name, level) VALUES (3191, "building_subsistence_farmslevel", 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3191, 157.1412, 172.85532, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3191, 31.428236363636362, 34.57106, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3191, 31.428236363636362, 34.57106, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3191, 31.428236363636362, 34.57106, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3191, 31.428236363636362, 34.57106, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3191, 31.428236363636362, 34.57106, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3191, 43.99952727272726, 48.39948, 102);
INSERT INTO building(id, name, level) VALUES (3192, "building_urban_centerlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3192, 39.99999999999999, 6.914795360142638, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3192, 79.99999999999999, 71.27062339090462, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3192, 39.99999999999999, 4.280940282378136, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3192, 560.0, 218.54488757720776, 8);
INSERT INTO building(id, name, level) VALUES (3193, "building_subsistence_farmslevel", 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3193, 135.90861818181818, 149.49948, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3193, 27.18171818181818, 29.89989, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3193, 27.18171818181818, 29.89989, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3193, 27.18171818181818, 29.89989, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3193, 27.18171818181818, 29.89989, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3193, 27.18171818181818, 29.89989, 85);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3193, 38.05440909090909, 41.85985, 85);
INSERT INTO building(id, name, level) VALUES (3194, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3194, 30.0, 5.18609652010698, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3194, 60.0, 53.45296754317848, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3194, 30.0, 3.2107052117836026, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3194, 420.0, 163.90866568290582, 6);
INSERT INTO building(id, name, level) VALUES (3195, "building_subsistence_farmslevel", 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3195, 287.02079999999995, 315.72288, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3195, 57.404154545454546, 63.14457, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3195, 57.404154545454546, 63.14457, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3195, 57.404154545454546, 63.14457, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3195, 57.404154545454546, 63.14457, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3195, 57.404154545454546, 63.14457, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3195, 80.36581818181817, 88.4024, 128);
INSERT INTO building(id, name, level) VALUES (3196, "building_subsistence_farmslevel", 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3196, 360.164375, 432.19725, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3196, 72.032875, 86.43945, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3196, 72.032875, 86.43945, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3196, 72.032875, 86.43945, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3196, 72.032875, 86.43945, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3196, 72.032875, 86.43945, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3196, 100.84602500000001, 121.01523, 173);
INSERT INTO building(id, name, level) VALUES (3197, "building_subsistence_farmslevel", 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3197, 254.99999999999997, 280.5, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3197, 51.0, 56.1, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3197, 51.0, 56.1, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3197, 51.0, 56.1, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3197, 51.0, 56.1, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3197, 51.0, 56.1, 102);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3197, 71.4, 78.54, 102);
INSERT INTO building(id, name, level) VALUES (3198, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3198, 5.0, 0.8643494200178301, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3198, 10.0, 8.908827923863079, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3198, 5.0, 0.535117535297267, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3198, 70.0, 27.31811094715097, 1);
INSERT INTO building(id, name, level) VALUES (3243, "building_subsistence_farmslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3243, 3.7444454545454544, 4.11889, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3243, 0.7488818181818181, 0.82377, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3243, 0.7488818181818181, 0.82377, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3243, 0.7488818181818181, 0.82377, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3243, 0.7488818181818181, 0.82377, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3243, 0.7488818181818181, 0.82377, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3243, 1.0484454545454545, 1.15329, 6);
INSERT INTO building(id, name, level) VALUES (3269, "building_subsistence_farmslevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3269, 17.6284, 19.39124, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3269, 3.525672727272727, 3.87824, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3269, 3.525672727272727, 3.87824, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3269, 3.525672727272727, 3.87824, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3269, 3.525672727272727, 3.87824, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3269, 3.525672727272727, 3.87824, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3269, 4.935945454545454, 5.42954, 8);
INSERT INTO building(id, name, level) VALUES (3270, "building_subsistence_farmslevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3270, 5.841399999999999, 6.42554, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3270, 1.1682727272727271, 1.2851, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3270, 1.1682727272727271, 1.2851, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3270, 1.1682727272727271, 1.2851, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3270, 1.1682727272727271, 1.2851, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3270, 1.1682727272727271, 1.2851, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3270, 1.6355909090909089, 1.79915, 8);
INSERT INTO building(id, name, level) VALUES (3273, "building_subsistence_farmslevel", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3273, 26.588699999999996, 29.24757, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3273, 5.317736363636364, 5.84951, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3273, 5.317736363636364, 5.84951, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3273, 5.317736363636364, 5.84951, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3273, 5.317736363636364, 5.84951, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3273, 5.317736363636364, 5.84951, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3273, 7.444827272727273, 8.18931, 18);
INSERT INTO building(id, name, level) VALUES (3274, "building_subsistence_farmslevel", 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3274, 39.38017272727272, 43.31819, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3274, 7.8760272727272715, 8.66363, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3274, 7.8760272727272715, 8.66363, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3274, 7.8760272727272715, 8.66363, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3274, 7.8760272727272715, 8.66363, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3274, 7.8760272727272715, 8.66363, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3274, 11.026445454545453, 12.12909, 27);
INSERT INTO building(id, name, level) VALUES (16780574, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16780574, 29.999999999999996, 26.726483771589233, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16780574, 59.99999999999999, 57.659374686480405, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 16780574, 89.99999999999999, 83.33425667224415, 1);
INSERT INTO building(id, name, level) VALUES (3369, "building_subsistence_farmslevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3369, 8.126499999999998, 8.93915, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3369, 1.6253, 1.78783, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3369, 1.6253, 1.78783, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3369, 1.6253, 1.78783, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3369, 1.6253, 1.78783, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3369, 1.6253, 1.78783, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3369, 2.2754181818181816, 2.50296, 4);
INSERT INTO building(id, name, level) VALUES (67112257, "building_steel_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 67112257, 59.99999999999999, 53.45296754317847, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 67112257, 119.99999999999999, 115.31874937296081, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 67112257, 180.0, 166.66851334448833, 2);
INSERT INTO building(id, name, level) VALUES (3503, "building_subsistence_orchardslevel", 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3503, 10.5, 11.55, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3503, 5.25, 5.775, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3503, 15.749999999999998, 17.325, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3503, 10.5, 11.55, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3503, 10.5, 11.55, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3503, 10.5, 11.55, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3503, 27.929999999999996, 30.723, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3503, 14.700000000000001, 16.17, 21);
INSERT INTO building(id, name, level) VALUES (134221549, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 134221549, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 134221549, 10.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 134221549, 10.0, 9.67109819387657, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 134221549, 99.99999999999999, 37.9993234463741, 1);
INSERT INTO building(id, name, level) VALUES (3839, "building_subsistence_orchardslevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3839, 1.7238727272727272, 1.89626, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3839, 0.8619363636363636, 0.94813, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3839, 2.5858181818181816, 2.8444, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3839, 1.7238727272727272, 1.89626, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3839, 1.7238727272727272, 1.89626, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3839, 1.7238727272727272, 1.89626, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3839, 4.585518181818181, 5.04407, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3839, 2.4134272727272728, 2.65477, 8);
INSERT INTO building(id, name, level) VALUES (3856, "building_subsistence_farmslevel", 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3856, 216.63899999999998, 238.3029, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3856, 43.327799999999996, 47.66058, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3856, 43.327799999999996, 47.66058, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3856, 43.327799999999996, 47.66058, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3856, 43.327799999999996, 47.66058, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3856, 43.327799999999996, 47.66058, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3856, 60.65891818181818, 66.72481, 90);
INSERT INTO building(id, name, level) VALUES (3861, "building_trade_centerlevel", 159);
INSERT INTO building(id, name, level) VALUES (16781082, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16781082, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16781082, 20.0, 16.581350992679994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16781082, 80.0, 40.07749734550263, 1);
INSERT INTO building(id, name, level) VALUES (50335551, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50335551, 59.99999999999999, 10.37219304021396, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 50335551, 39.99999999999999, 33.16270198535999, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 50335551, 159.99999999999997, 80.15499469100526, 2);
INSERT INTO building(id, name, level) VALUES (16781143, "building_subsistence_pastureslevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16781143, 0.0032999999999999995, 0.00363, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16781143, 0.004945454545454545, 0.00544, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16781143, 0.0016454545454545452, 0.00181, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16781143, 0.0032999999999999995, 0.00363, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16781143, 0.0032999999999999995, 0.00363, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16781143, 0.0032999999999999995, 0.00363, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 16781143, 0.008772727272727272, 0.00965, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16781143, 0.004618181818181818, 0.00508, 10);
INSERT INTO building(id, name, level) VALUES (3934, "building_conscription_centerlevel", 24);
INSERT INTO building(id, name, level) VALUES (3935, "building_conscription_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (16781180, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16781180, 12.56313043478261, 3.937480422345435, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16781180, 25.12626086956522, 4.3435738019650785, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16781180, 31.407826086956526, 30.182593540594, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16781180, 6.281565217391305, 6.030254989250109, 1);
INSERT INTO building(id, name, level) VALUES (16781266, "building_subsistence_fishing_villageslevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16781266, 0.0042, 0.00462, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 16781266, 0.0168, 0.01848, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16781266, 0.0021, 0.00231, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16781266, 0.0063, 0.00693, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16781266, 0.0042, 0.00462, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16781266, 0.0042, 0.00462, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16781266, 0.0042, 0.00462, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16781266, 0.005872727272727272, 0.00646, 8);
INSERT INTO building(id, name, level) VALUES (4130, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4131, "building_conscription_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (4132, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4133, "building_conscription_centerlevel", 21);
INSERT INTO building(id, name, level) VALUES (4134, "building_conscription_centerlevel", 12);
INSERT INTO building(id, name, level) VALUES (4135, "building_conscription_centerlevel", 15);
INSERT INTO building(id, name, level) VALUES (4136, "building_conscription_centerlevel", 12);
INSERT INTO building(id, name, level) VALUES (4137, "building_conscription_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (4138, "building_conscription_centerlevel", 15);
INSERT INTO building(id, name, level) VALUES (4139, "building_conscription_centerlevel", 12);
INSERT INTO building(id, name, level) VALUES (4175, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4546, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (33558985, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33558985, 9.8357, 9.452005193396923, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 33558985, 9.8357, 0.38113397905008056, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 33558985, 9.8357, 8.154459697935131, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33558985, 4.917845454545454, 4.721094355038726, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 33558985, 24.589245454545452, 17.14361952319916, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 33558985, 14.753545454545453, 10.28617044628331, 1);
INSERT INTO building(id, name, level) VALUES (50336210, "building_subsistence_fishing_villageslevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 50336210, 0.0036, 0.00396, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 50336210, 0.0144, 0.01584, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 50336210, 0.0018, 0.00198, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 50336210, 0.005399999999999999, 0.00594, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 50336210, 0.0036, 0.00396, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 50336210, 0.0036, 0.00396, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 50336210, 0.0036, 0.00396, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 50336210, 0.005036363636363636, 0.00554, 8);
INSERT INTO building(id, name, level) VALUES (16781802, "building_tooling_workshopslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16781802, 102.00000000000001, 17.632728168363734, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16781802, 68.00000000000001, 56.376593375111995, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16781802, 272.00000000000006, 136.26349097470896, 4);
INSERT INTO building(id, name, level) VALUES (134222452, "building_sulfur_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 134222452, 8.81715, 7.855047212888935, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 134222452, 8.81715, 8.464397158730389, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 134222452, 35.2686, 32.638888743238645, 1);
INSERT INTO building(id, name, level) VALUES (4748, "building_subsistence_farmslevel", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 4748, 22.214399999999998, 24.43584, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 4748, 4.442872727272727, 4.88716, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 4748, 4.442872727272727, 4.88716, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 4748, 4.442872727272727, 4.88716, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 4748, 4.442872727272727, 4.88716, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4748, 4.442872727272727, 4.88716, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 4748, 6.220027272727273, 6.84203, 16);
INSERT INTO building(id, name, level) VALUES (50336496, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 50336496, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (4913, "building_portlevel", 4);
INSERT INTO building(id, name, level) VALUES (4917, "building_subsistence_farmslevel", 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 4917, 312.4035454545454, 343.6439, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 4917, 62.48070909090909, 68.72878, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 4917, 62.48070909090909, 68.72878, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 4917, 62.48070909090909, 68.72878, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 4917, 62.48070909090909, 68.72878, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4917, 62.48070909090909, 68.72878, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 4917, 87.47299090909091, 96.22029, 142);
INSERT INTO building(id, name, level) VALUES (4918, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4918, 59.99999999999999, 10.37219304021396, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 4918, 19.999999999999996, 0.0, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 4918, 19.999999999999996, 19.342196387753138, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 4918, 199.99999999999997, 75.9986468927482, 2);
INSERT INTO building(id, name, level) VALUES (4919, "building_tea_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 4919, 39.37239603960396, 39.76612, 2);
INSERT INTO building(id, name, level) VALUES (4920, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 4920, 1.0, 0.6066950386290002, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4920, 1.0, 1.3072016922755214, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4920, 1.0, 0.8777525103436717, 1);
INSERT INTO building(id, name, level) VALUES (4921, "building_conscription_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4923, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 4923, 4.25, 2.771933798030603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 4923, 42.5, 27.719337980306033, 1);
INSERT INTO building(id, name, level) VALUES (4924, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 4924, 5.0, 3.261098585918356, 1);
INSERT INTO building(id, name, level) VALUES (4990, "building_shipyardslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4990, 24.78659821428571, 7.768505287112472, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4990, 49.57319642857142, 8.569712716293134, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 4990, 24.78659821428571, 0.9604822030487241, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 4990, 12.39329464285714, 9.722816417204006, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 4990, 86.75309821428571, 28.402049688017108, 3);
INSERT INTO building(id, name, level) VALUES (50336653, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50336653, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 50336653, 10.0, 8.908827923863079, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 50336653, 20.0, 16.581350992679994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 50336653, 80.0, 50.475206027303294, 1);
INSERT INTO building(id, name, level) VALUES (50336678, "building_shipyardslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 50336678, 9.8782, 3.0959814761077666, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50336678, 19.7564, 3.415286576328051, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 50336678, 9.8782, 0.3827808566601773, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 50336678, 4.9391, 3.874834251107691, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 50336678, 34.573699999999995, 11.319064857753933, 1);
INSERT INTO building(id, name, level) VALUES (50336702, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50336702, 30.0, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 50336702, 10.0, 8.908827923863079, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 50336702, 20.0, 16.581350992679994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 50336702, 80.0, 50.475206027303294, 1);
INSERT INTO building(id, name, level) VALUES (33559524, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559524, 0.5, 0.086434942001783, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 33559524, 1.0, 0.890882792386308, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 33559524, 0.5, 0.05351175352972671, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 33559524, 7.0, 2.731811094715097, 1);
INSERT INTO building(id, name, level) VALUES (50336763, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 50336763, 25.12626086956522, 7.87496084469087, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50336763, 50.25252173913044, 8.687147603930157, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 50336763, 62.81565217391305, 60.365187081188, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50336763, 12.56313043478261, 12.060509978500217, 2);
INSERT INTO building(id, name, level) VALUES (16782332, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782332, 37.68956521739131, 11.812495774088307, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782332, 75.37913043478262, 13.030781534550544, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782332, 94.22391304347828, 90.5481984433377, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782332, 18.844782608695656, 18.090848445351998, 3);
INSERT INTO building(id, name, level) VALUES (16782351, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782351, 12.56313043478261, 3.937480422345435, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782351, 25.12626086956522, 4.3435738019650785, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782351, 31.407826086956526, 30.182593540594, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782351, 6.281565217391305, 6.030254989250109, 1);
INSERT INTO building(id, name, level) VALUES (16782367, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782367, 25.12626086956522, 7.87496084469087, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782367, 50.25252173913044, 8.687147603930157, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782367, 62.81565217391305, 60.365187081188, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782367, 12.56313043478261, 12.060509978500217, 2);
INSERT INTO building(id, name, level) VALUES (5157, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5157, 12.56313043478261, 3.937480422345435, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5157, 25.12626086956522, 4.3435738019650785, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5157, 31.407826086956526, 30.182593540594, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5157, 6.281565217391305, 6.030254989250109, 1);
INSERT INTO building(id, name, level) VALUES (16782439, "building_subsistence_farmslevel", 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16782439, 132.85079999999996, 146.13588, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16782439, 26.570154545454542, 29.22717, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16782439, 26.570154545454542, 29.22717, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16782439, 26.570154545454542, 29.22717, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16782439, 26.570154545454542, 29.22717, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16782439, 26.570154545454542, 29.22717, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16782439, 37.19821818181818, 40.91804, 54);
INSERT INTO building(id, name, level) VALUES (5230, "building_portlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 5230, 20.0, 13.044394343673424, 4);
INSERT INTO building(id, name, level) VALUES (50336891, "building_shipyardslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 50336891, 20.0, 6.268310979951341, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50336891, 40.0, 6.914795360142641, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 50336891, 20.0, 0.7750012282808151, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 50336891, 10.0, 7.845223322280762, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 50336891, 24.999999999999996, 8.184736416520312, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 50336891, 44.99999999999999, 14.732525549736563, 1);
INSERT INTO building(id, name, level) VALUES (16782460, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782460, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782460, 20.0, 16.581350992679994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16782460, 80.0, 40.07749734550263, 1);
INSERT INTO building(id, name, level) VALUES (16782510, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782510, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782510, 20.0, 16.581350992679994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16782510, 80.0, 40.07749734550263, 1);
INSERT INTO building(id, name, level) VALUES (33559743, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559743, 59.99999999999999, 10.37219304021396, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 33559743, 39.99999999999999, 33.16270198535999, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 33559743, 159.99999999999997, 80.15499469100526, 2);
INSERT INTO building(id, name, level) VALUES (5321, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5321, 59.99999999999999, 10.37219304021396, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5321, 19.999999999999996, 17.817655847726154, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 5321, 39.99999999999999, 33.16270198535999, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 5321, 159.99999999999997, 100.95041205460659, 2);
INSERT INTO building(id, name, level) VALUES (67114230, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 67114230, 0.5, 0.086434942001783, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 67114230, 1.0, 0.890882792386308, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 67114230, 0.5, 0.05351175352972671, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 67114230, 7.0, 2.731811094715097, 1);
INSERT INTO building(id, name, level) VALUES (67114249, "building_universitylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 67114249, 30.0, 18.822889545926294, 6);
INSERT INTO building(id, name, level) VALUES (16782610, "building_shipyardslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782610, 99.21700000000001, 31.096150524891613, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782610, 198.43400000000003, 34.30326256236362, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782610, 99.21700000000001, 3.844664843316882, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 16782610, 49.60850000000001, 38.91897611833652, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 16782610, 124.0212456140351, 40.60324821597613, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 16782610, 223.23824561403512, 73.08584793749198, 5);
INSERT INTO building(id, name, level) VALUES (33559880, "building_tooling_workshopslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559880, 89.99999999999999, 15.558289560320938, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 33559880, 60.0, 49.74405297803999, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 33559880, 240.0, 120.23249203650789, 3);
INSERT INTO building(id, name, level) VALUES (16782719, "building_tooling_workshopslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782719, 89.99999999999999, 15.558289560320938, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782719, 60.0, 49.74405297803999, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16782719, 240.0, 120.23249203650789, 3);
INSERT INTO building(id, name, level) VALUES (83891590, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 83891590, 10.0, 6.274296515308765, 2);
INSERT INTO building(id, name, level) VALUES (16782738, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782738, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782738, 20.0, 16.581350992679994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16782738, 80.0, 40.07749734550263, 1);
INSERT INTO building(id, name, level) VALUES (16782754, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782754, 59.99999999999999, 10.37219304021396, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 16782754, 19.999999999999996, 0.0, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 16782754, 19.999999999999996, 19.342196387753138, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 16782754, 199.99999999999997, 75.9986468927482, 2);
INSERT INTO building(id, name, level) VALUES (16782757, "building_shipyardslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782757, 13.7694, 4.3155440603671, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782757, 27.5388, 4.760629161597403, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16782757, 13.7694, 0.5335650956344927, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 16782757, 6.8847, 5.401200900690635, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 16782757, 48.192899999999995, 15.777847345908867, 1);
INSERT INTO building(id, name, level) VALUES (50337193, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (50337196, "building_chemical_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 50337196, 44.99999999999999, 43.51994187244456, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 50337196, 14.999999999999998, 13.363241885794617, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 50337196, 10.0, 9.609895781080068, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 50337196, 75.0, 70.4745547470493, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 50337196, 40.0, 37.58642919842629, 1);
INSERT INTO building(id, name, level) VALUES (16782819, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782819, 10.0, 8.908827923863079, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 16782819, 10.0, 7.845223322280762, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 16782819, 50.0, 41.885128115359606, 1);
INSERT INTO building(id, name, level) VALUES (16782838, "building_tooling_workshopslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782838, 120.0, 20.74438608042792, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16782838, 80.00000000000001, 66.32540397071999, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16782838, 320.00000000000006, 160.30998938201054, 4);
INSERT INTO building(id, name, level) VALUES (50337271, "building_subsistence_farmslevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 50337271, 9.880999999999998, 10.8691, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 50337271, 1.9762, 2.17382, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 50337271, 1.9762, 2.17382, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 50337271, 1.9762, 2.17382, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 50337271, 1.9762, 2.17382, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 50337271, 1.9762, 2.17382, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 50337271, 2.766672727272727, 3.04334, 4);
INSERT INTO building(id, name, level) VALUES (50337302, "building_motor_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 50337302, 29.999999999999996, 24.87202648901999, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 50337302, 40.0, 33.16270198535999, 1);
INSERT INTO building(id, name, level) VALUES (5663, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5663, 10.0, 1.7286988400356602, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5663, 20.0, 17.817655847726158, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5663, 10.0, 1.070235070594534, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5663, 140.0, 54.63622189430194, 2);
INSERT INTO building(id, name, level) VALUES (33560216, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33560216, 14.999999999999998, 14.399886287627613, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 33560216, 59.99999999999999, 57.59954515051045, 1);
INSERT INTO building(id, name, level) VALUES (16783043, "building_arms_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783043, 25.356396946564885, 24.36723320401854, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16783043, 12.678198473282443, 0.4912809694590923, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 16783043, 19.017297709923664, 9.506173178601271, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 16783043, 19.017297709923664, 9.506173178601271, 2);
INSERT INTO building(id, name, level) VALUES (16783081, "building_tooling_workshopslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783081, 120.0, 20.74438608042792, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16783081, 80.00000000000001, 66.32540397071999, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16783081, 320.00000000000006, 160.30998938201054, 4);
INSERT INTO building(id, name, level) VALUES (5866, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5866, 10.0, 8.908827923863079, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 5866, 10.0, 7.845223322280762, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 5866, 50.0, 41.885128115359606, 1);
INSERT INTO building(id, name, level) VALUES (5873, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5873, 9.825, 9.441722604911167, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 5873, 9.825, 0.3807193533929503, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 5873, 9.825, 8.145588675154046, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5873, 4.9125, 4.715962759198043, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 5873, 24.5625, 17.124972594908908, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 5873, 14.737499999999999, 10.274983556945346, 1);
INSERT INTO building(id, name, level) VALUES (16783117, "building_munition_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 16783117, 19.360599999999998, 8.55373667472271, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 16783117, 19.360599999999998, 28.28452159533588, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 16783117, 48.4015, 34.89292084340339, 1);
INSERT INTO building(id, name, level) VALUES (5916, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5916, 8.8432, 8.498223037124726, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 5916, 8.8432, 0.34267454309664513, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 5916, 13.264799999999997, 10.997415232385078, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 5916, 8.8432, 5.39083591171492, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 5916, 22.107999999999997, 13.477089779287297, 1);
INSERT INTO building(id, name, level) VALUES (16783166, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16783166, 9.365799999999998, 0.3629253251916228, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16783166, 23.414499999999997, 19.412202140905283, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783166, 4.682899999999999, 4.495548499755423, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 16783166, 18.731599999999997, 11.412602120709721, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 16783166, 23.414499999999997, 14.265752650887153, 1);
INSERT INTO building(id, name, level) VALUES (117446484, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 117446484, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 117446484, 10.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 117446484, 10.0, 9.67109819387657, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 117446484, 99.99999999999999, 37.9993234463741, 1);
INSERT INTO building(id, name, level) VALUES (100669305, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 100669305, 10.0, 8.908827923863079, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 100669305, 10.0, 7.845223322280762, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 100669305, 50.0, 41.885128115359606, 1);
INSERT INTO building(id, name, level) VALUES (16783247, "building_chemical_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 16783247, 44.99999999999999, 43.51994187244456, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16783247, 14.999999999999998, 13.363241885794617, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783247, 10.0, 9.609895781080068, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 16783247, 75.0, 70.4745547470493, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 16783247, 40.0, 37.58642919842629, 1);
INSERT INTO building(id, name, level) VALUES (16783256, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783256, 5.0, 0.8643494200178301, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16783256, 10.0, 8.908827923863079, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 16783256, 10.0, 7.845223322280762, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 16783256, 65.0, 40.045958520055585, 1);
INSERT INTO building(id, name, level) VALUES (16783303, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16783303, 29.999999999999996, 26.726483771589233, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783303, 59.99999999999999, 57.659374686480405, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 16783303, 89.99999999999999, 83.33425667224415, 1);
INSERT INTO building(id, name, level) VALUES (100669392, "building_motor_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 100669392, 30.0, 24.872026489019994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 100669392, 40.0, 33.16270198535999, 1);
INSERT INTO building(id, name, level) VALUES (134223850, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (16783340, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783340, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 16783340, 10.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 16783340, 10.0, 9.67109819387657, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 16783340, 99.99999999999999, 37.9993234463741, 1);
INSERT INTO building(id, name, level) VALUES (16783354, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783354, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 16783354, 10.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 16783354, 10.0, 9.67109819387657, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 16783354, 99.99999999999999, 37.9993234463741, 1);
INSERT INTO building(id, name, level) VALUES (6155, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6155, 0.46, 0.07952014664164037, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6155, 0.92, 0.8196121689954033, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 6155, 0.46, 0.04923081324734858, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6155, 6.44, 2.5132662071378893, 1);
INSERT INTO building(id, name, level) VALUES (16783378, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783378, 10.0, 3.1341554899756705, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783378, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16783378, 20.0, 0.7750012282808151, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783378, 20.0, 19.19984838350349, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16783378, 89.99999999999999, 33.413128055782835, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 16783378, 40.0, 14.850279135903484, 1);
INSERT INTO building(id, name, level) VALUES (16783397, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783397, 10.0, 3.1341554899756705, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783397, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16783397, 5.0, 4.4544139619315395, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16783397, 20.0, 0.7750012282808151, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783397, 24.999999999999996, 23.999810479379356, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16783397, 89.99999999999999, 42.766392707579804, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 16783397, 40.0, 19.007285647813248, 1);
INSERT INTO building(id, name, level) VALUES (33560621, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 33560621, 29.999999999999996, 26.726483771589233, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33560621, 59.99999999999999, 57.659374686480405, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 33560621, 89.99999999999999, 83.33425667224415, 1);
INSERT INTO building(id, name, level) VALUES (50337841, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 50337841, 35.0, 31.18089773352078, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 50337841, 59.99999999999999, 57.659374686480405, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50337841, 5.0, 4.799962095875872, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 50337841, 89.99999999999999, 84.35594369008469, 1);
INSERT INTO building(id, name, level) VALUES (251664457, "building_subsistence_fishing_villageslevel", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 251664457, 0.004672727272727272, 0.00514, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 251664457, 0.018718181818181818, 0.02059, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 251664457, 0.002336363636363636, 0.00257, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 251664457, 0.0070181818181818175, 0.00772, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 251664457, 0.004672727272727272, 0.00514, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 251664457, 0.004672727272727272, 0.00514, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 251664457, 0.004672727272727272, 0.00514, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 251664457, 0.006545454545454544, 0.0072, 18);
INSERT INTO building(id, name, level) VALUES (218110029, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 218110029, 59.99999999999999, 10.37219304021396, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 218110029, 39.99999999999999, 33.16270198535999, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 218110029, 159.99999999999997, 80.15499469100526, 2);
INSERT INTO building(id, name, level) VALUES (50337871, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50337871, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 50337871, 20.0, 16.581350992679994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 50337871, 80.0, 40.07749734550263, 1);
INSERT INTO building(id, name, level) VALUES (6234, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6234, 29.999999999999996, 5.18609652010698, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 6234, 20.0, 16.581350992679994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 6234, 80.0, 40.07749734550263, 1);
INSERT INTO building(id, name, level) VALUES (50337903, "building_steel_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 50337903, 70.0, 62.36179546704156, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 50337903, 119.99999999999999, 115.31874937296081, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50337903, 10.0, 9.599924191751745, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 50337903, 180.0, 168.7118873801694, 2);
INSERT INTO building(id, name, level) VALUES (16783478, "building_trade_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (6285, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 6285, 29.999999999999996, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 6285, 35.0, 15.46340421346936, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 6285, 49.99999999999999, 11.045288723906685, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 6285, 40.0, 8.836230979125348, 1);
INSERT INTO building(id, name, level) VALUES (16783546, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783546, 20.0, 3.4573976800713204, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 16783546, 29.999999999999996, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 16783546, 10.0, 4.418115489562674, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 16783546, 20.0, 4.097876219732222, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 16783546, 40.0, 8.195752439464444, 1);
INSERT INTO building(id, name, level) VALUES (16783551, "building_trade_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (33560768, "building_trade_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (33560769, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (6394, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (6397, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (33560869, "building_subsistence_rice_paddieslevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 33560869, 0.009899999999999999, 0.01089, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 33560869, 0.0016454545454545452, 0.00181, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 33560869, 0.0016454545454545452, 0.00181, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 33560869, 0.0021999999999999997, 0.00242, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 33560869, 0.0021999999999999997, 0.00242, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 33560869, 0.0021999999999999997, 0.00242, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 33560869, 0.0032999999999999995, 0.00363, 4);
INSERT INTO building(id, name, level) VALUES (16783654, "building_subsistence_fishing_villageslevel", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16783654, 0.0015999999999999999, 0.00176, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 16783654, 0.0063999999999999994, 0.00704, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16783654, 0.0007999999999999999, 0.00088, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16783654, 0.0024, 0.00264, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 16783654, 0.0015999999999999999, 0.00176, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 16783654, 0.0015999999999999999, 0.00176, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16783654, 0.0015999999999999999, 0.00176, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 16783654, 0.002236363636363636, 0.00246, 16);
INSERT INTO building(id, name, level) VALUES (6441, "building_subsistence_rice_paddieslevel", 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6441, 0.006381818181818181, 0.00702, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6441, 0.0010636363636363636, 0.00117, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6441, 0.0010636363636363636, 0.00117, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6441, 0.001418181818181818, 0.00156, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6441, 0.001418181818181818, 0.00156, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6441, 0.001418181818181818, 0.00156, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6441, 0.002127272727272727, 0.00234, 142);
