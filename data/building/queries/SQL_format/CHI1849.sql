
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 14.470752760743686, 35693.8291011604);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 24.506049635128875, 961.8656844222608);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 11.795618469575055, 3735.3340512178556);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 17.144324770671343, 2778.295288782143);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 33.01724168338546, 10871.788764857296);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 30.348132182857555, 10123.056100171438);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 36.011066568646925, 326.1928258495359);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 12.592418203048766, 2878.7631772222194);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 43.125, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 12.11384935437668, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 27.07858095462436, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 66.18448426212497, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 61.2352976789451, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 48.707737668801926, 303.5635235347279);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 62.696081600608224, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 78.83138270595909, 1352.5159178341962);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 39.610868245854675, 506.069337354683);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 23.102613099199022, 1188.0431006956665);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 20.062046326842562, 7045.630878010201);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 33.08583285254297, 2834.397939371947);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 87.5, 18.718665628052822);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 11.549932533011125, 206.65793994588807);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 52.87223529852078, 741.2801098851786);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 81.14532439136048, 2340.0347107857424);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 75.12380764913605, 481.98913137549096);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 84.19677233978803, 1209.583153234608);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 350.0, 0.08529166666666667);
INSERT INTO building(id, name, level) VALUES (33554984, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 33554984, 35.3724, 44.2155, 1);
INSERT INTO building(id, name, level) VALUES (16777773, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16777773, 0.504, 0.6225129972981085, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 16777773, 0.504, 0.35771000716412765, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16777773, 4.032, 3.4468400286565104, 1);
INSERT INTO building(id, name, level) VALUES (335544880, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 335544880, 6.5204, 14.39250900888994, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 335544880, 13.0408, 16.107276776121378, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 335544880, 16.301, 2.073224141225753, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 335544880, 3.2602, 0.7937411807528658, 2);
INSERT INTO building(id, name, level) VALUES (318768424, "building_construction_sectorlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 318768424, 13.0408, 28.78501801777988, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 318768424, 26.0816, 32.214553552242755, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 318768424, 32.602, 4.146448282451506, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 318768424, 6.5204, 1.5874823615057316, 4);
INSERT INTO building(id, name, level) VALUES (16778937, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16778937, 30.0, 33.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 16778937, 26.999999999999996, 30.24, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 16778937, 18.0, 20.16, 3);
INSERT INTO building(id, name, level) VALUES (33556206, "building_cotton_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 33556206, 70.74479365079365, 89.13844, 2);
INSERT INTO building(id, name, level) VALUES (2104, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2104, 29.999999999999996, 33.0, 1);
INSERT INTO building(id, name, level) VALUES (2318, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2318, 89.99999999999999, 100.8, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2318, 15.0, 16.8, 3);
INSERT INTO building(id, name, level) VALUES (2319, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2319, 90.0, 82.8, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2319, 15.0, 13.8, 3);
INSERT INTO building(id, name, level) VALUES (2320, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2320, 99.99999999999999, 73.28414858379145, 10);
INSERT INTO building(id, name, level) VALUES (2321, "building_logging_camplevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2321, 45.00000000000001, 10.955877901318619, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2321, 360.00000000000006, 87.64702321054895, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2321, 90.00000000000001, 21.911755802637238, 9);
INSERT INTO building(id, name, level) VALUES (2322, "building_opium_plantationlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (44, 2322, 106.1328, 111.43944, 6);
INSERT INTO building(id, name, level) VALUES (2323, "building_paper_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2323, 240.0, 296.4347606181469, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2323, 319.99999999999994, 319.99999999999994, 8);
INSERT INTO building(id, name, level) VALUES (2324, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2324, 12.5, 0.0, 25);
INSERT INTO building(id, name, level) VALUES (2325, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2325, 30.0, 21.985244575137436, 3);
INSERT INTO building(id, name, level) VALUES (2326, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2326, 20.000000000000004, 4.8692790672527195, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2326, 160.00000000000003, 38.954232538021756, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2326, 40.00000000000001, 9.738558134505439, 4);
INSERT INTO building(id, name, level) VALUES (2327, "building_tea_plantationlevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2327, 212.46239639639637, 235.83326, 12);
INSERT INTO building(id, name, level) VALUES (2328, "building_opium_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (44, 2328, 177.172, 193.11748, 10);
INSERT INTO building(id, name, level) VALUES (2329, "building_barrackslevel", 12);
INSERT INTO building(id, name, level) VALUES (2330, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2330, 40.0, 29.313659433516584, 4);
INSERT INTO building(id, name, level) VALUES (2331, "building_furniture_manufacturieslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2331, 70.0, 154.51132302041222, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2331, 140.0, 172.92027702725238, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2331, 70.0, 20.450972803765065, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2331, 35.0, 8.52123836769226, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2331, 314.99999999999994, 199.68013073154327, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2331, 140.0, 88.7467247695748, 7);
INSERT INTO building(id, name, level) VALUES (2332, "building_glassworkslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2332, 240.0, 296.4347606181469, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2332, 120.0, 210.79034666666666, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2332, 79.99999999999999, 79.99999999999999, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2332, 200.0, 200.0, 8);
INSERT INTO building(id, name, level) VALUES (2333, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2333, 25.000000000000004, 6.086598834065899, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2333, 200.00000000000003, 48.692790672527195, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2333, 50.00000000000001, 12.173197668131799, 5);
INSERT INTO building(id, name, level) VALUES (2334, "building_opium_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (44, 2334, 88.443, 91.98072, 5);
INSERT INTO building(id, name, level) VALUES (2335, "building_dye_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2335, 176.87599999999998, 189.25732, 8);
INSERT INTO building(id, name, level) VALUES (2336, "building_tea_plantationlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2336, 106.12079999999999, 111.42684, 6);
INSERT INTO building(id, name, level) VALUES (2337, "building_barrackslevel", 18);
INSERT INTO building(id, name, level) VALUES (2338, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2338, 50.0, 36.642074291895725, 5);
INSERT INTO building(id, name, level) VALUES (2339, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2339, 10.0, 2.4346395336263598, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2339, 40.0, 9.738558134505439, 2);
INSERT INTO building(id, name, level) VALUES (2340, "building_rice_farmlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2340, 160.0, 187.2, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2340, 48.0, 56.16, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2340, 72.0, 84.24, 8);
INSERT INTO building(id, name, level) VALUES (2341, "building_dye_plantationlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2341, 132.64649523809524, 139.27882, 6);
INSERT INTO building(id, name, level) VALUES (2342, "building_silk_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2342, 196.86199999999997, 263.79508, 10);
INSERT INTO building(id, name, level) VALUES (2343, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2343, 12.5, 0.0, 25);
INSERT INTO building(id, name, level) VALUES (2344, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2344, 30.0, 27.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2344, 5.0, 4.5, 1);
INSERT INTO building(id, name, level) VALUES (2345, "building_barrackslevel", 5);
INSERT INTO building(id, name, level) VALUES (2346, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2346, 60.0, 54.6, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2346, 10.0, 9.1, 2);
INSERT INTO building(id, name, level) VALUES (2347, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2347, 5.0, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (2348, "building_government_administrationlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2348, 70.0, 51.29890400865402, 7);
INSERT INTO building(id, name, level) VALUES (2349, "building_banana_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2349, 265.569, 289.47021, 10);
INSERT INTO building(id, name, level) VALUES (2350, "building_rice_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2350, 140.00000000000003, 162.4, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2350, 42.0, 48.72, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2350, 63.0, 73.08, 7);
INSERT INTO building(id, name, level) VALUES (2351, "building_tea_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2351, 177.07, 193.0063, 10);
INSERT INTO building(id, name, level) VALUES (2352, "building_dye_plantationlevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2352, 243.4245, 267.76695, 11);
INSERT INTO building(id, name, level) VALUES (2353, "building_silk_plantationlevel", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2353, 314.97920000000005, 440.97088, 16);
INSERT INTO building(id, name, level) VALUES (2354, "building_barrackslevel", 25);
INSERT INTO building(id, name, level) VALUES (2355, "building_government_administrationlevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2355, 200.0, 146.5682971675829, 20);
INSERT INTO building(id, name, level) VALUES (2356, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2356, 9.7806, 21.58876351333491, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2356, 19.5612, 24.160915164182065, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2356, 24.4515, 3.10983621183863, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2356, 4.8903, 1.1906117711292987, 3);
INSERT INTO building(id, name, level) VALUES (2357, "building_tooling_workshopslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2357, 119.99999999999999, 148.21738030907346, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 2357, 119.99999999999999, 119.99999999999999, 4);
INSERT INTO building(id, name, level) VALUES (2358, "building_furniture_manufacturieslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2358, 50.0, 110.36523072886587, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2358, 100.0, 123.51448359089457, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2358, 50.0, 14.607837716975046, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2358, 25.0, 6.086598834065899, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2358, 225.0, 142.62866480824522, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2358, 100.0, 63.39051769255343, 5);
INSERT INTO building(id, name, level) VALUES (2359, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2359, 30.0, 33.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2359, 26.999999999999996, 30.24, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2359, 18.0, 20.16, 3);
INSERT INTO building(id, name, level) VALUES (2360, "building_tea_plantationlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2360, 123.80759433962264, 131.23605, 7);
INSERT INTO building(id, name, level) VALUES (2361, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2361, 12.5, 0.0, 25);
INSERT INTO building(id, name, level) VALUES (2362, "building_forbidden_citylevel", 1);
INSERT INTO building(id, name, level) VALUES (2363, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2363, 20.000000000000004, 4.8692790672527195, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2363, 160.00000000000003, 38.954232538021756, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2363, 40.00000000000001, 9.738558134505439, 4);
INSERT INTO building(id, name, level) VALUES (2364, "building_livestock_ranchlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2364, 50.0, 12.173197668131799, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2364, 300.0, 73.0391860087908, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2364, 150.0, 36.5195930043954, 10);
INSERT INTO building(id, name, level) VALUES (2365, "building_barrackslevel", 16);
INSERT INTO building(id, name, level) VALUES (2366, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2366, 30.0, 21.985244575137436, 3);
INSERT INTO building(id, name, level) VALUES (2367, "building_iron_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2367, 12.75, 3.1041654053736085, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2367, 51.0, 12.416661621494434, 3);
INSERT INTO building(id, name, level) VALUES (2368, "building_logging_camplevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2368, 45.00000000000001, 10.955877901318619, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2368, 360.00000000000006, 87.64702321054895, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2368, 90.00000000000001, 21.911755802637238, 9);
INSERT INTO building(id, name, level) VALUES (2369, "building_livestock_ranchlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2369, 120.0, 135.6, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2369, 20.000000000000004, 22.6, 4);
INSERT INTO building(id, name, level) VALUES (2370, "building_barrackslevel", 25);
INSERT INTO building(id, name, level) VALUES (2371, "building_logging_camplevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2371, 30.000000000000004, 7.30391860087908, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2371, 240.00000000000003, 58.43134880703264, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2371, 60.00000000000001, 14.60783720175816, 6);
INSERT INTO building(id, name, level) VALUES (2372, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2372, 89.99999999999999, 100.8, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2372, 15.0, 16.8, 3);
INSERT INTO building(id, name, level) VALUES (2373, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 2373, 20.0, 0.0, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2373, 10.0, 0.0, 20);
INSERT INTO building(id, name, level) VALUES (2374, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2374, 30.0, 21.985244575137436, 3);
INSERT INTO building(id, name, level) VALUES (2375, "building_wheat_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2375, 70.0, 91.7, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2375, 63.0, 82.53, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2375, 42.0, 55.02, 7);
INSERT INTO building(id, name, level) VALUES (2376, "building_tea_plantationlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2376, 123.80339622641507, 131.2316, 7);
INSERT INTO building(id, name, level) VALUES (2377, "building_barrackslevel", 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2377, 9.0, 0.0, 18);
INSERT INTO building(id, name, level) VALUES (2378, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2378, 20.0, 14.656829716758292, 2);
INSERT INTO building(id, name, level) VALUES (2379, "building_tea_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2379, 70.84639805825242, 72.97179, 4);
INSERT INTO building(id, name, level) VALUES (2380, "building_barrackslevel", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2380, 17.0, 0.0, 17);
INSERT INTO building(id, name, level) VALUES (2381, "building_livestock_ranchlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2381, 35.00000000000001, 8.521238367692261, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2381, 210.0, 51.127430206153555, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2381, 105.0, 25.563715103076778, 7);
INSERT INTO building(id, name, level) VALUES (2382, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2382, 10.0, 0.0, 10);
INSERT INTO building(id, name, level) VALUES (2383, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2383, 20.0, 14.656829716758292, 2);
INSERT INTO building(id, name, level) VALUES (2384, "building_tea_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2384, 141.4895981308411, 151.39387, 8);
INSERT INTO building(id, name, level) VALUES (2385, "building_barrackslevel", 22);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2385, 22.0, 0.0, 22);
INSERT INTO building(id, name, level) VALUES (2386, "building_livestock_ranchlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2386, 150.0, 171.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2386, 25.000000000000004, 28.5, 5);
INSERT INTO building(id, name, level) VALUES (2387, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2387, 5.0, 0.0, 10);
INSERT INTO building(id, name, level) VALUES (2388, "building_government_administrationlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2388, 70.0, 51.29890400865402, 7);
INSERT INTO building(id, name, level) VALUES (2389, "building_paper_millslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2389, 119.99999999999999, 148.21738030907346, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2389, 160.0, 160.0, 4);
INSERT INTO building(id, name, level) VALUES (2390, "building_tea_plantationlevel", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2390, 247.609592920354, 279.79884, 14);
INSERT INTO building(id, name, level) VALUES (2391, "building_silk_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2391, 196.78799999999998, 263.69592, 10);
INSERT INTO building(id, name, level) VALUES (2392, "building_barrackslevel", 25);
INSERT INTO building(id, name, level) VALUES (2393, "building_government_administrationlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2393, 79.99999999999999, 58.62731886703315, 8);
INSERT INTO building(id, name, level) VALUES (2394, "building_banana_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2394, 242.87999999999997, 264.7392, 10);
INSERT INTO building(id, name, level) VALUES (2395, "building_fishing_wharflevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2395, 125.0, 130.0, 5);
INSERT INTO building(id, name, level) VALUES (2396, "building_silk_plantationlevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2396, 353.736, 509.37984, 20);
INSERT INTO building(id, name, level) VALUES (2397, "building_rice_farmlevel", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2397, 180.00000000000003, 212.4, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2397, 54.0, 63.72, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2397, 81.0, 95.58, 9);
INSERT INTO building(id, name, level) VALUES (2398, "building_dye_plantationlevel", 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2398, 287.40074999999996, 321.88884, 13);
INSERT INTO building(id, name, level) VALUES (2399, "building_shipyardslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2399, 40.0, 88.29218458309269, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2399, 80.0, 98.81158687271565, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 2399, 30.0, 30.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 2399, 40.0, 40.0, 2);
INSERT INTO building(id, name, level) VALUES (2400, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2400, 25.0, 0.0, 25);
INSERT INTO building(id, name, level) VALUES (2401, "building_naval_baselevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 2401, 30.0, 30.0, 15);
INSERT INTO building(id, name, level) VALUES (2402, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2402, 15.0, 24.0, 3);
INSERT INTO building(id, name, level) VALUES (2406, "building_government_administrationlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2406, 70.0, 51.29890400865402, 7);
INSERT INTO building(id, name, level) VALUES (2407, "building_silk_plantationlevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2407, 216.42719999999997, 292.17672, 11);
INSERT INTO building(id, name, level) VALUES (2408, "building_rice_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2408, 99.84, 113.8176, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2408, 29.952, 34.14528, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2408, 44.928000000000004, 51.21792, 5);
INSERT INTO building(id, name, level) VALUES (2409, "building_dye_plantationlevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2409, 243.18524545454542, 267.50377, 11);
INSERT INTO building(id, name, level) VALUES (2410, "building_fishing_wharflevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2410, 99.43499999999999, 102.41805, 4);
INSERT INTO building(id, name, level) VALUES (2411, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2411, 12.5, 0.0, 25);
INSERT INTO building(id, name, level) VALUES (2412, "building_government_administrationlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2412, 70.0, 51.29890400865402, 7);
INSERT INTO building(id, name, level) VALUES (2413, "building_textile_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2413, 240.0, 529.7531074985562, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2413, 79.99999999999999, 1135.36946, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2413, 200.0, 200.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2413, 159.99999999999997, 159.99999999999997, 8);
INSERT INTO building(id, name, level) VALUES (2414, "building_tea_plantationlevel", 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2414, 300.66539655172414, 348.77186, 17);
INSERT INTO building(id, name, level) VALUES (2415, "building_rice_farmlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2415, 120.00000000000001, 138.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2415, 36.0, 41.4, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2415, 54.00000000000001, 62.1, 6);
INSERT INTO building(id, name, level) VALUES (2416, "building_fishing_wharflevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2416, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (2417, "building_barrackslevel", 21);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2417, 10.5, 0.0, 21);
INSERT INTO building(id, name, level) VALUES (2418, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2418, 99.99999999999999, 73.28414858379145, 10);
INSERT INTO building(id, name, level) VALUES (2419, "building_glassworkslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2419, 150.0, 185.27172538634184, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2419, 75.0, 131.74396666666667, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2419, 50.0, 50.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2419, 125.0, 125.0, 5);
INSERT INTO building(id, name, level) VALUES (2420, "building_tea_plantationlevel", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2420, 283.296, 325.7904, 16);
INSERT INTO building(id, name, level) VALUES (2421, "building_rice_farmlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2421, 160.0, 187.2, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2421, 48.0, 56.16, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2421, 72.0, 84.24, 8);
INSERT INTO building(id, name, level) VALUES (2422, "building_barrackslevel", 25);
INSERT INTO building(id, name, level) VALUES (2423, "building_government_administrationlevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2423, 150.0, 109.92622287568719, 15);
INSERT INTO building(id, name, level) VALUES (2424, "building_construction_sectorlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2424, 13.0408, 28.78501801777988, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2424, 26.0816, 32.214553552242755, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2424, 32.602, 4.146448282451506, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2424, 6.5204, 1.5874823615057316, 4);
INSERT INTO building(id, name, level) VALUES (2425, "building_glassworkslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2425, 300.0, 370.5434507726837, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2425, 150.0, 263.48793333333333, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2425, 99.99999999999999, 99.99999999999999, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2425, 249.99999999999997, 249.99999999999997, 10);
INSERT INTO building(id, name, level) VALUES (2426, "building_tea_plantationlevel", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2426, 247.606796460177, 279.79568, 14);
INSERT INTO building(id, name, level) VALUES (2427, "building_rice_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2427, 100.00000000000001, 114.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2427, 30.000000000000004, 34.2, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2427, 45.0, 51.3, 5);
INSERT INTO building(id, name, level) VALUES (2428, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2428, 25.0, 0.0, 25);
INSERT INTO building(id, name, level) VALUES (2429, "building_government_administrationlevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2429, 150.0, 109.92622287568719, 15);
INSERT INTO building(id, name, level) VALUES (2430, "building_construction_sectorlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2430, 13.0408, 28.78501801777988, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2430, 26.0816, 32.214553552242755, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2430, 32.602, 4.146448282451506, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2430, 6.5204, 1.5874823615057316, 4);
INSERT INTO building(id, name, level) VALUES (2431, "building_glassworkslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2431, 180.0, 222.32607046361022, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2431, 90.0, 158.09276, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2431, 60.0, 60.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2431, 150.0, 150.0, 6);
INSERT INTO building(id, name, level) VALUES (2432, "building_tea_plantationlevel", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2432, 282.9952, 325.44448, 16);
INSERT INTO building(id, name, level) VALUES (2433, "building_government_administrationlevel", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2433, 119.99999999999999, 87.94097830054973, 12);
INSERT INTO building(id, name, level) VALUES (2434, "building_iron_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2434, 20.0, 4.8692790672527195, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2434, 80.0, 19.477116269010878, 4);
INSERT INTO building(id, name, level) VALUES (2435, "building_paper_millslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2435, 180.0, 222.32607046361022, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2435, 240.0, 240.0, 6);
INSERT INTO building(id, name, level) VALUES (2436, "building_glassworkslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2436, 180.0, 222.32607046361022, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2436, 90.0, 158.09276, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2436, 60.0, 60.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2436, 150.0, 150.0, 6);
INSERT INTO building(id, name, level) VALUES (2437, "building_wheat_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2437, 40.00000000000001, 45.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2437, 36.0, 40.68, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2437, 24.000000000000004, 27.12, 4);
INSERT INTO building(id, name, level) VALUES (2438, "building_tea_plantationlevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2438, 194.54819999999998, 214.00302, 11);
INSERT INTO building(id, name, level) VALUES (2439, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2439, 12.5, 0.0, 25);
INSERT INTO building(id, name, level) VALUES (2440, "building_government_administrationlevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2440, 109.99999999999999, 80.6125634421706, 11);
INSERT INTO building(id, name, level) VALUES (2441, "building_glassworkslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2441, 150.0, 185.27172538634184, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2441, 150.0, 150.0, 5);
INSERT INTO building(id, name, level) VALUES (2442, "building_rice_farmlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2442, 159.4751965811966, 186.58598, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2442, 47.84255555555556, 55.97579, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2442, 71.76383760683761, 83.96369, 8);
INSERT INTO building(id, name, level) VALUES (2443, "building_silk_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2443, 196.80999999999997, 263.7254, 10);
INSERT INTO building(id, name, level) VALUES (2444, "building_tea_plantationlevel", 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2444, 371.41020000000003, 445.69224, 21);
INSERT INTO building(id, name, level) VALUES (2445, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2445, 12.5, 0.0, 25);
INSERT INTO building(id, name, level) VALUES (2446, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2446, 99.99999999999999, 73.28414858379145, 10);
INSERT INTO building(id, name, level) VALUES (2447, "building_furniture_manufacturieslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2447, 99.99999999999999, 220.7304614577317, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2447, 199.99999999999997, 247.02896718178906, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2447, 99.99999999999999, 29.215675433950086, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2447, 249.99999999999997, 191.01306286162506, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2447, 199.99999999999997, 152.81045028930004, 10);
INSERT INTO building(id, name, level) VALUES (2448, "building_rice_farmlevel", 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2448, 260.0, 317.2, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2448, 78.0, 95.16, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2448, 117.00000000000001, 142.74, 13);
INSERT INTO building(id, name, level) VALUES (2449, "building_paper_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2449, 150.0, 185.27172538634184, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2449, 200.0, 200.0, 5);
INSERT INTO building(id, name, level) VALUES (2450, "building_tea_plantationlevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2450, 194.54819999999998, 214.00302, 11);
INSERT INTO building(id, name, level) VALUES (2451, "building_barrackslevel", 25);
INSERT INTO building(id, name, level) VALUES (2452, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2452, 99.99999999999999, 73.28414858379145, 10);
INSERT INTO building(id, name, level) VALUES (2453, "building_rice_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2453, 140.00000000000003, 162.4, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2453, 42.0, 48.72, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2453, 63.0, 73.08, 7);
INSERT INTO building(id, name, level) VALUES (2454, "building_tea_plantationlevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2454, 354.3, 421.617, 20);
INSERT INTO building(id, name, level) VALUES (2455, "building_barrackslevel", 23);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2455, 23.0, 0.0, 23);
INSERT INTO building(id, name, level) VALUES (2456, "building_government_administrationlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2456, 79.99999999999999, 58.62731886703315, 8);
INSERT INTO building(id, name, level) VALUES (2457, "building_paper_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2457, 240.0, 296.4347606181469, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2457, 319.99999999999994, 319.99999999999994, 8);
INSERT INTO building(id, name, level) VALUES (2458, "building_furniture_manufacturieslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2458, 99.99999999999999, 220.7304614577317, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2458, 199.99999999999997, 247.02896718178906, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2458, 99.99999999999999, 29.215675433950086, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2458, 249.99999999999997, 191.01306286162506, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2458, 199.99999999999997, 152.81045028930004, 10);
INSERT INTO building(id, name, level) VALUES (2459, "building_rice_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2459, 140.00000000000003, 162.4, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2459, 42.0, 48.72, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2459, 63.0, 73.08, 7);
INSERT INTO building(id, name, level) VALUES (2460, "building_tea_plantationlevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2460, 212.23919819819818, 235.58551, 12);
INSERT INTO building(id, name, level) VALUES (2461, "building_barrackslevel", 25);
INSERT INTO building(id, name, level) VALUES (2462, "building_government_administrationlevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2462, 90.0, 65.95573372541232, 9);
INSERT INTO building(id, name, level) VALUES (2463, "building_furniture_manufacturieslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2463, 79.99999999999999, 176.58436916618535, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2463, 159.99999999999997, 197.62317374543127, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2463, 79.99999999999999, 23.37254034716007, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2463, 39.99999999999999, 9.738558134505437, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2463, 359.99999999999994, 228.2058636931923, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2463, 159.99999999999997, 101.42482830808547, 8);
INSERT INTO building(id, name, level) VALUES (2464, "building_rice_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2464, 4.0, 0.9738558134505438, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2464, 80.00000000000001, 19.47711626901088, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2464, 24.000000000000004, 5.843134880703264, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2464, 36.0, 8.764702321054894, 4);
INSERT INTO building(id, name, level) VALUES (2465, "building_paper_millslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2465, 209.99999999999997, 259.3804155408785, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2465, 280.0, 280.0, 7);
INSERT INTO building(id, name, level) VALUES (2466, "building_tea_plantationlevel", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2466, 159.17579629629628, 171.90986, 9);
INSERT INTO building(id, name, level) VALUES (2467, "building_silk_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2467, 176.862, 236.99508, 10);
INSERT INTO building(id, name, level) VALUES (2468, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2468, 25.0, 0.0, 25);
INSERT INTO building(id, name, level) VALUES (2469, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2469, 50.0, 36.642074291895725, 5);
INSERT INTO building(id, name, level) VALUES (2470, "building_logging_camplevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2470, 30.000000000000004, 7.30391860087908, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2470, 240.00000000000003, 58.43134880703264, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2470, 60.00000000000001, 14.60783720175816, 6);
INSERT INTO building(id, name, level) VALUES (2471, "building_rice_farmlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2471, 120.00000000000001, 138.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2471, 36.0, 41.4, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2471, 54.00000000000001, 62.1, 6);
INSERT INTO building(id, name, level) VALUES (2472, "building_tea_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2472, 176.88199999999998, 192.80138, 10);
INSERT INTO building(id, name, level) VALUES (2473, "building_barrackslevel", 21);
INSERT INTO building(id, name, level) VALUES (2474, "building_fishing_wharflevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2474, 125.0, 130.0, 5);
INSERT INTO building(id, name, level) VALUES (2475, "building_tea_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2475, 70.74479611650486, 72.86714, 4);
INSERT INTO building(id, name, level) VALUES (2476, "building_banana_plantationlevel", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2476, 238.76369444444447, 257.86479, 9);
INSERT INTO building(id, name, level) VALUES (2477, "building_barrackslevel", 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2477, 18.0, 0.0, 18);
INSERT INTO building(id, name, level) VALUES (2478, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2478, 5.0, 8.0, 1);
INSERT INTO building(id, name, level) VALUES (2479, "building_government_administrationlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2479, 70.0, 51.29890400865402, 7);
INSERT INTO building(id, name, level) VALUES (2480, "building_glassworkslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2480, 300.0, 370.5434507726837, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2480, 150.0, 263.48793333333333, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2480, 99.99999999999999, 99.99999999999999, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2480, 249.99999999999997, 249.99999999999997, 10);
INSERT INTO building(id, name, level) VALUES (2481, "building_textile_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2481, 240.0, 529.7531074985562, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2481, 79.99999999999999, 1135.36946, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2481, 200.0, 200.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2481, 159.99999999999997, 159.99999999999997, 8);
INSERT INTO building(id, name, level) VALUES (2482, "building_tea_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2482, 141.49279439252336, 151.39729, 8);
INSERT INTO building(id, name, level) VALUES (2483, "building_wheat_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2483, 40.00000000000001, 45.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2483, 36.0, 40.68, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2483, 24.000000000000004, 27.12, 4);
INSERT INTO building(id, name, level) VALUES (2484, "building_fishing_wharflevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2484, 150.0, 157.5, 6);
INSERT INTO building(id, name, level) VALUES (2485, "building_barrackslevel", 22);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 2485, 22.0, 0.0, 22);
INSERT INTO building(id, name, level) VALUES (2486, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2486, 5.0, 8.0, 1);
INSERT INTO building(id, name, level) VALUES (2487, "building_government_administrationlevel", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2487, 129.99999999999997, 95.26939315892886, 13);
INSERT INTO building(id, name, level) VALUES (2488, "building_paper_millslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2488, 209.99999999999997, 259.3804155408785, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2488, 280.0, 280.0, 7);
INSERT INTO building(id, name, level) VALUES (2489, "building_tea_plantationlevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2489, 194.54819999999998, 214.00302, 11);
INSERT INTO building(id, name, level) VALUES (2490, "building_fishing_wharflevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2490, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (2491, "building_barrackslevel", 13);
INSERT INTO building(id, name, level) VALUES (2493, "building_livestock_ranchlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2493, 120.0, 135.6, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2493, 20.000000000000004, 22.6, 4);
INSERT INTO building(id, name, level) VALUES (2494, "building_livestock_ranchlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2494, 180.0, 207.0, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2494, 30.000000000000004, 34.5, 6);
INSERT INTO building(id, name, level) VALUES (16780033, "building_barrackslevel", 12);
INSERT INTO building(id, name, level) VALUES (2828, "building_subsistence_farmslevel", 915);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2828, 1970.0865, 2167.09515, 915);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2828, 328.3477454545454, 361.18252, 915);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2828, 328.3477454545454, 361.18252, 915);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2828, 328.3477454545454, 361.18252, 915);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2828, 328.3477454545454, 361.18252, 915);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2828, 328.3477454545454, 361.18252, 915);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2828, 459.6868454545454, 505.65553, 915);
INSERT INTO building(id, name, level) VALUES (2829, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2829, 30.0, 37.054345077268366, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2829, 30.0, 21.292262331198074, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2829, 240.0, 205.1690493247923, 6);
INSERT INTO building(id, name, level) VALUES (2830, "building_subsistence_farmslevel", 846);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2830, 1951.722, 2146.8942, 846);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2830, 325.287, 357.8157, 846);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2830, 325.287, 357.8157, 846);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2830, 325.287, 357.8157, 846);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2830, 325.287, 357.8157, 846);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2830, 325.287, 357.8157, 846);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2830, 455.4018, 500.94198, 846);
INSERT INTO building(id, name, level) VALUES (2831, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2831, 25.0, 30.878620897723643, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2831, 25.0, 17.74355194266506, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2831, 200.0, 170.97420777066023, 5);
INSERT INTO building(id, name, level) VALUES (16780130, "building_tea_plantationlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 16780130, 123.82299999999998, 131.25238, 7);
INSERT INTO building(id, name, level) VALUES (2993, "building_subsistence_farmslevel", 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2993, 99.89567272727271, 109.88524, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2993, 16.649272727272727, 18.3142, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2993, 16.649272727272727, 18.3142, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2993, 16.649272727272727, 18.3142, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2993, 16.649272727272727, 18.3142, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2993, 16.649272727272727, 18.3142, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2993, 23.30899090909091, 25.63989, 72);
INSERT INTO building(id, name, level) VALUES (2994, "building_subsistence_farmslevel", 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2994, 73.10015454545453, 80.41017, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2994, 12.183354545454545, 13.40169, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2994, 12.183354545454545, 13.40169, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2994, 12.183354545454545, 13.40169, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2994, 12.183354545454545, 13.40169, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2994, 12.183354545454545, 13.40169, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2994, 17.0567, 18.76237, 48);
INSERT INTO building(id, name, level) VALUES (16780527, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16780527, 3.2602, 7.19625450444497, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16780527, 6.5204, 8.053638388060689, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16780527, 8.1505, 1.0366120706128765, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16780527, 1.6301, 0.3968705903764329, 1);
INSERT INTO building(id, name, level) VALUES (16780568, "building_barrackslevel", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16780568, 8.5, 0.0, 17);
INSERT INTO building(id, name, level) VALUES (33557832, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33557832, 6.5204, 14.39250900888994, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33557832, 13.0408, 16.107276776121378, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33557832, 16.301, 2.073224141225753, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33557832, 3.2602, 0.7937411807528658, 2);
INSERT INTO building(id, name, level) VALUES (3612, "building_subsistence_farmslevel", 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3612, 74.38724545454545, 81.82597, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3612, 12.397872727272727, 13.63766, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3612, 12.397872727272727, 13.63766, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3612, 12.397872727272727, 13.63766, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3612, 12.397872727272727, 13.63766, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3612, 12.397872727272727, 13.63766, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3612, 17.35701818181818, 19.09272, 35);
INSERT INTO building(id, name, level) VALUES (3613, "building_subsistence_farmslevel", 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3613, 103.71092222222222, 93.33983, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3613, 17.285144444444445, 15.55663, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3613, 17.285144444444445, 15.55663, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3613, 17.285144444444445, 15.55663, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3613, 17.285144444444445, 15.55663, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3613, 17.285144444444445, 15.55663, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3613, 24.19921111111111, 21.77929, 53);
INSERT INTO building(id, name, level) VALUES (3614, "building_subsistence_farmslevel", 876);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3614, 1938.62304, 2423.2788, 876);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3614, 323.10384, 403.8798, 876);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3614, 323.10384, 403.8798, 876);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3614, 323.10384, 403.8798, 876);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3614, 323.10384, 403.8798, 876);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3614, 323.10384, 403.8798, 876);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3614, 452.34537600000004, 565.43172, 876);
INSERT INTO building(id, name, level) VALUES (3615, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3615, 20.0, 24.702896718178913, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3615, 20.0, 14.19484155413205, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3615, 160.0, 136.77936621652822, 4);
INSERT INTO building(id, name, level) VALUES (3616, "building_subsistence_farmslevel", 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3616, 796.1908727272727, 875.80996, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3616, 132.69847272727273, 145.96832, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3616, 132.69847272727273, 145.96832, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3616, 132.69847272727273, 145.96832, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3616, 132.69847272727273, 145.96832, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3616, 132.69847272727273, 145.96832, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3616, 185.7778636363636, 204.35565, 373);
INSERT INTO building(id, name, level) VALUES (3617, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3617, 10.0, 12.351448359089456, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3617, 10.0, 7.097420777066025, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3617, 80.0, 68.38968310826411, 2);
INSERT INTO building(id, name, level) VALUES (3618, "building_subsistence_farmslevel", 282);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3618, 509.81651818181814, 560.79817, 282);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3618, 84.96941818181817, 93.46636, 282);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3618, 84.96941818181817, 93.46636, 282);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3618, 84.96941818181817, 93.46636, 282);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3618, 84.96941818181817, 93.46636, 282);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3618, 84.96941818181817, 93.46636, 282);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3618, 118.95718181818181, 130.8529, 282);
INSERT INTO building(id, name, level) VALUES (3619, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3619, 25.0, 30.878620897723643, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3619, 25.0, 17.74355194266506, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3619, 200.0, 170.97420777066023, 5);
INSERT INTO building(id, name, level) VALUES (3620, "building_subsistence_farmslevel", 455);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3620, 1042.6416, 1146.90576, 455);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3620, 173.7736, 191.15096, 455);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3620, 173.7736, 191.15096, 455);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3620, 173.7736, 191.15096, 455);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3620, 173.7736, 191.15096, 455);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3620, 173.7736, 191.15096, 455);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3620, 243.28303636363634, 267.61134, 455);
INSERT INTO building(id, name, level) VALUES (3621, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3621, 15.0, 18.527172538634183, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3621, 15.0, 10.646131165599037, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3621, 120.0, 102.58452466239615, 3);
INSERT INTO building(id, name, level) VALUES (3622, "building_subsistence_farmslevel", 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3622, 67.18997777777777, 60.47098, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3622, 11.198322222222222, 10.07849, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3622, 11.198322222222222, 10.07849, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3622, 11.198322222222222, 10.07849, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3622, 11.198322222222222, 10.07849, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3622, 11.198322222222222, 10.07849, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3622, 15.677655555555555, 14.10989, 41);
INSERT INTO building(id, name, level) VALUES (3623, "building_subsistence_farmslevel", 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3623, 73.9368, 66.54312, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3623, 12.322799999999999, 11.09052, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3623, 12.322799999999999, 11.09052, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3623, 12.322799999999999, 11.09052, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3623, 12.322799999999999, 11.09052, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3623, 12.322799999999999, 11.09052, 42);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3623, 17.25191111111111, 15.52672, 42);
INSERT INTO building(id, name, level) VALUES (3624, "building_subsistence_farmslevel", 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3624, 1982.7961727272725, 2181.07579, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3624, 330.4660272727273, 363.51263, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3624, 330.4660272727273, 363.51263, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3624, 330.4660272727273, 363.51263, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3624, 330.4660272727273, 363.51263, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3624, 330.4660272727273, 363.51263, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3624, 462.65243636363635, 508.91768, 878);
INSERT INTO building(id, name, level) VALUES (3625, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3625, 20.0, 24.702896718178913, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3625, 20.0, 14.19484155413205, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3625, 160.0, 136.77936621652822, 4);
INSERT INTO building(id, name, level) VALUES (3626, "building_subsistence_farmslevel", 710);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3626, 1999.7079, 2199.67869, 710);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3626, 333.28464545454545, 366.61311, 710);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3626, 333.28464545454545, 366.61311, 710);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3626, 333.28464545454545, 366.61311, 710);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3626, 333.28464545454545, 366.61311, 710);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3626, 333.28464545454545, 366.61311, 710);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3626, 466.5985090909091, 513.25836, 710);
INSERT INTO building(id, name, level) VALUES (3627, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3627, 35.0, 43.230069256813096, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3627, 35.0, 24.840972719731088, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3627, 280.0, 239.36389087892434, 7);
INSERT INTO building(id, name, level) VALUES (3628, "building_subsistence_farmslevel", 394);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3628, 272.84105454545454, 300.12516, 394);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3628, 45.47350909090908, 50.02086, 394);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3628, 45.47350909090908, 50.02086, 394);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3628, 45.47350909090908, 50.02086, 394);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3628, 45.47350909090908, 50.02086, 394);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3628, 45.47350909090908, 50.02086, 394);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3628, 63.66290909090909, 70.0292, 394);
INSERT INTO building(id, name, level) VALUES (3629, "building_subsistence_farmslevel", 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3629, 142.82105454545453, 157.10316, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3629, 23.80350909090909, 26.18386, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3629, 23.80350909090909, 26.18386, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3629, 23.80350909090909, 26.18386, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3629, 23.80350909090909, 26.18386, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3629, 23.80350909090909, 26.18386, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3629, 33.32490909090909, 36.6574, 86);
INSERT INTO building(id, name, level) VALUES (3630, "building_subsistence_farmslevel", 514);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3630, 1204.8262727272727, 1325.3089, 514);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3630, 200.8043727272727, 220.88481, 514);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3630, 200.8043727272727, 220.88481, 514);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3630, 200.8043727272727, 220.88481, 514);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3630, 200.8043727272727, 220.88481, 514);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3630, 200.8043727272727, 220.88481, 514);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3630, 281.1261272727273, 309.23874, 514);
INSERT INTO building(id, name, level) VALUES (3631, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3631, 10.0, 12.351448359089456, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3631, 10.0, 7.097420777066025, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3631, 80.0, 68.38968310826411, 2);
INSERT INTO building(id, name, level) VALUES (3632, "building_subsistence_farmslevel", 184);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3632, 404.9803181818181, 445.47835, 184);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3632, 67.49671818181818, 74.24639, 184);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3632, 67.49671818181818, 74.24639, 184);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3632, 67.49671818181818, 74.24639, 184);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3632, 67.49671818181818, 74.24639, 184);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3632, 67.49671818181818, 74.24639, 184);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3632, 94.49539999999999, 103.94494, 184);
INSERT INTO building(id, name, level) VALUES (3633, "building_subsistence_farmslevel", 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3633, 2073.2400000000002, 2591.55, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3633, 345.54, 431.925, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3633, 345.54, 431.925, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3633, 345.54, 431.925, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3633, 345.54, 431.925, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3633, 345.54, 431.925, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3633, 483.75600000000003, 604.695, 886);
INSERT INTO building(id, name, level) VALUES (3634, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3634, 5.0, 6.175724179544728, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3634, 5.0, 3.5487103885330127, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3634, 40.0, 34.194841554132054, 1);
INSERT INTO building(id, name, level) VALUES (3635, "building_subsistence_farmslevel", 544);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3635, 1223.8857545454543, 1346.27433, 544);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3635, 203.98095454545452, 224.37905, 544);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3635, 203.98095454545452, 224.37905, 544);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3635, 203.98095454545452, 224.37905, 544);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3635, 203.98095454545452, 224.37905, 544);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3635, 203.98095454545452, 224.37905, 544);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3635, 285.5733363636364, 314.13067, 544);
INSERT INTO building(id, name, level) VALUES (3636, "building_subsistence_farmslevel", 402);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3636, 893.1515363636363, 982.46669, 402);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3636, 148.8585818181818, 163.74444, 402);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3636, 148.8585818181818, 163.74444, 402);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3636, 148.8585818181818, 163.74444, 402);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3636, 148.8585818181818, 163.74444, 402);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3636, 148.8585818181818, 163.74444, 402);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3636, 208.40201818181816, 229.24222, 402);
INSERT INTO building(id, name, level) VALUES (3637, "building_subsistence_farmslevel", 599);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3637, 1352.27844, 1690.34805, 599);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3637, 225.379736, 281.72467, 599);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3637, 225.379736, 281.72467, 599);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3637, 225.379736, 281.72467, 599);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3637, 225.379736, 281.72467, 599);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3637, 225.379736, 281.72467, 599);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3637, 315.531632, 394.41454, 599);
INSERT INTO building(id, name, level) VALUES (3638, "building_subsistence_farmslevel", 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3638, 175.1050727272727, 192.61558, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3638, 29.184172727272724, 32.10259, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3638, 29.184172727272724, 32.10259, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3638, 29.184172727272724, 32.10259, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3638, 29.184172727272724, 32.10259, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3638, 29.184172727272724, 32.10259, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3638, 40.85784545454545, 44.94363, 79);
INSERT INTO building(id, name, level) VALUES (3639, "building_subsistence_farmslevel", 1177);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3639, 2668.164836363636, 2934.98132, 1177);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3639, 444.6941363636363, 489.16355, 1177);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3639, 444.6941363636363, 489.16355, 1177);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3639, 444.6941363636363, 489.16355, 1177);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3639, 444.6941363636363, 489.16355, 1177);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3639, 444.6941363636363, 489.16355, 1177);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3639, 622.5717909090909, 684.82897, 1177);
INSERT INTO building(id, name, level) VALUES (3640, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3640, 19.897, 24.575676800080288, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3640, 19.897, 14.121738120128269, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3640, 159.176, 136.07495248051305, 4);
INSERT INTO building(id, name, level) VALUES (3641, "building_subsistence_farmslevel", 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3641, 1627.0113545454544, 1789.71249, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3641, 271.1685545454545, 298.28541, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3641, 271.1685545454545, 298.28541, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3641, 271.1685545454545, 298.28541, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3641, 271.1685545454545, 298.28541, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3641, 271.1685545454545, 298.28541, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3641, 379.6359818181818, 417.59958, 666);
INSERT INTO building(id, name, level) VALUES (3642, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3642, 35.0, 43.230069256813096, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3642, 35.0, 24.840972719731088, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3642, 280.0, 239.36389087892434, 7);
INSERT INTO building(id, name, level) VALUES (3644, "building_subsistence_farmslevel", 941);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3644, 2218.6521545454543, 2440.51737, 941);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3644, 369.7753545454545, 406.75289, 941);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3644, 369.7753545454545, 406.75289, 941);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3644, 369.7753545454545, 406.75289, 941);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3644, 369.7753545454545, 406.75289, 941);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3644, 369.7753545454545, 406.75289, 941);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3644, 517.6855, 569.45405, 941);
INSERT INTO building(id, name, level) VALUES (3645, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3645, 15.0, 18.527172538634183, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3645, 15.0, 10.646131165599037, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3645, 120.0, 102.58452466239615, 3);
INSERT INTO building(id, name, level) VALUES (3646, "building_subsistence_farmslevel", 567);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3646, 1216.4871545454546, 1338.13587, 567);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3646, 202.74785454545452, 223.02264, 567);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3646, 202.74785454545452, 223.02264, 567);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3646, 202.74785454545452, 223.02264, 567);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3646, 202.74785454545452, 223.02264, 567);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3646, 202.74785454545452, 223.02264, 567);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3646, 283.847, 312.2317, 567);
INSERT INTO building(id, name, level) VALUES (3647, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3647, 20.0, 24.702896718178913, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3647, 20.0, 14.19484155413205, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3647, 160.0, 136.77936621652822, 4);
INSERT INTO building(id, name, level) VALUES (3648, "building_subsistence_farmslevel", 618);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3648, 1435.422418181818, 1578.96466, 618);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3648, 239.23706363636364, 263.16077, 618);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3648, 239.23706363636364, 263.16077, 618);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3648, 239.23706363636364, 263.16077, 618);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3648, 239.23706363636364, 263.16077, 618);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3648, 239.23706363636364, 263.16077, 618);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3648, 334.93189090909084, 368.42508, 618);
INSERT INTO building(id, name, level) VALUES (3649, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3649, 30.0, 37.054345077268366, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3649, 30.0, 21.292262331198074, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3649, 240.0, 205.1690493247923, 6);
INSERT INTO building(id, name, level) VALUES (3650, "building_subsistence_farmslevel", 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3650, 1425.9386363636363, 1568.5325, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3650, 237.65643636363635, 261.42208, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3650, 237.65643636363635, 261.42208, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3650, 237.65643636363635, 261.42208, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3650, 237.65643636363635, 261.42208, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3650, 237.65643636363635, 261.42208, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3650, 332.719009090909, 365.99091, 658);
INSERT INTO building(id, name, level) VALUES (3651, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3651, 25.0, 30.878620897723643, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3651, 25.0, 17.74355194266506, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3651, 200.0, 170.97420777066023, 5);
INSERT INTO building(id, name, level) VALUES (3652, "building_subsistence_farmslevel", 1253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3652, 2864.696309090909, 3151.16594, 1253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3652, 477.44938181818173, 525.19432, 1253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3652, 477.44938181818173, 525.19432, 1253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3652, 477.44938181818173, 525.19432, 1253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3652, 477.44938181818173, 525.19432, 1253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3652, 477.44938181818173, 525.19432, 1253);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3652, 668.4291363636363, 735.27205, 1253);
INSERT INTO building(id, name, level) VALUES (3653, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3653, 25.0, 30.878620897723643, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3653, 25.0, 17.74355194266506, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3653, 200.0, 170.97420777066023, 5);
INSERT INTO building(id, name, level) VALUES (3654, "building_subsistence_farmslevel", 1377);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3654, 1052.9918999999998, 1158.29109, 1377);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3654, 175.49864545454543, 193.04851, 1377);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3654, 175.49864545454543, 193.04851, 1377);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3654, 175.49864545454543, 193.04851, 1377);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3654, 175.49864545454543, 193.04851, 1377);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3654, 175.49864545454543, 193.04851, 1377);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3654, 245.69810909090907, 270.26792, 1377);
INSERT INTO building(id, name, level) VALUES (3655, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3655, 30.0, 37.054345077268366, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3655, 30.0, 21.292262331198074, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3655, 240.0, 205.1690493247923, 6);
INSERT INTO building(id, name, level) VALUES (3656, "building_subsistence_farmslevel", 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3656, 3305.595418181818, 3636.15496, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3656, 550.9325636363635, 606.02582, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3656, 550.9325636363635, 606.02582, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3656, 550.9325636363635, 606.02582, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3656, 550.9325636363635, 606.02582, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3656, 550.9325636363635, 606.02582, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3656, 771.3055909090908, 848.43615, 1422);
INSERT INTO building(id, name, level) VALUES (3657, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3657, 25.0, 30.878620897723643, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3657, 25.0, 17.74355194266506, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3657, 200.0, 170.97420777066023, 5);
INSERT INTO building(id, name, level) VALUES (3658, "building_subsistence_farmslevel", 1366);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3658, 3105.997136363636, 3416.59685, 1366);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3658, 517.6661818181818, 569.4328, 1366);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3658, 517.6661818181818, 569.4328, 1366);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3658, 517.6661818181818, 569.4328, 1366);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3658, 517.6661818181818, 569.4328, 1366);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3658, 517.6661818181818, 569.4328, 1366);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3658, 724.7326636363636, 797.20593, 1366);
INSERT INTO building(id, name, level) VALUES (3659, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3659, 30.0, 37.054345077268366, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3659, 30.0, 21.292262331198074, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3659, 240.0, 205.1690493247923, 6);
INSERT INTO building(id, name, level) VALUES (3660, "building_subsistence_farmslevel", 611);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3660, 1401.9150545454545, 1542.10656, 611);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3660, 233.6525090909091, 257.01776, 611);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3660, 233.6525090909091, 257.01776, 611);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3660, 233.6525090909091, 257.01776, 611);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3660, 233.6525090909091, 257.01776, 611);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3660, 233.6525090909091, 257.01776, 611);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3660, 327.1135090909091, 359.82486, 611);
INSERT INTO building(id, name, level) VALUES (3661, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3661, 15.0, 18.527172538634183, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3661, 15.0, 10.646131165599037, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3661, 120.0, 102.58452466239615, 3);
INSERT INTO building(id, name, level) VALUES (3662, "building_subsistence_farmslevel", 762);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3662, 1725.0384545454544, 1897.5423, 762);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3662, 287.5064090909091, 316.25705, 762);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3662, 287.5064090909091, 316.25705, 762);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3662, 287.5064090909091, 316.25705, 762);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3662, 287.5064090909091, 316.25705, 762);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3662, 287.5064090909091, 316.25705, 762);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3662, 402.5089727272727, 442.75987, 762);
INSERT INTO building(id, name, level) VALUES (3663, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3663, 30.0, 37.054345077268366, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3663, 30.0, 21.292262331198074, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3663, 240.0, 205.1690493247923, 6);
INSERT INTO building(id, name, level) VALUES (3664, "building_subsistence_farmslevel", 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3664, 1762.8799727272726, 1939.16797, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3664, 293.81332727272724, 323.19466, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3664, 293.81332727272724, 323.19466, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3664, 293.81332727272724, 323.19466, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3664, 293.81332727272724, 323.19466, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3664, 293.81332727272724, 323.19466, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3664, 411.3386545454545, 452.47252, 818);
INSERT INTO building(id, name, level) VALUES (3665, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3665, 30.0, 37.054345077268366, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3665, 30.0, 21.292262331198074, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3665, 240.0, 205.1690493247923, 6);
INSERT INTO building(id, name, level) VALUES (3666, "building_subsistence_farmslevel", 789);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3666, 1811.133718181818, 1992.24709, 789);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3666, 301.85561818181816, 332.04118, 789);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3666, 301.85561818181816, 332.04118, 789);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3666, 301.85561818181816, 332.04118, 789);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3666, 301.85561818181816, 332.04118, 789);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3666, 301.85561818181816, 332.04118, 789);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3666, 422.59786363636357, 464.85765, 789);
INSERT INTO building(id, name, level) VALUES (3667, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3667, 10.0, 12.351448359089456, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3667, 10.0, 7.097420777066025, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3667, 80.0, 68.38968310826411, 2);
INSERT INTO building(id, name, level) VALUES (3668, "building_subsistence_farmslevel", 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3668, 333.4593545454545, 366.80529, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3668, 55.57655454545454, 61.13421, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3668, 55.57655454545454, 61.13421, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3668, 55.57655454545454, 61.13421, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3668, 55.57655454545454, 61.13421, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3668, 55.57655454545454, 61.13421, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3668, 77.80718181818182, 85.5879, 156);
INSERT INTO building(id, name, level) VALUES (3686, "building_subsistence_farmslevel", 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3686, 30.72845454545454, 33.8013, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3686, 5.12140909090909, 5.63355, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3686, 5.12140909090909, 5.63355, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3686, 5.12140909090909, 5.63355, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3686, 5.12140909090909, 5.63355, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3686, 5.12140909090909, 5.63355, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3686, 7.169972727272727, 7.88697, 23);
INSERT INTO building(id, name, level) VALUES (3691, "building_subsistence_pastureslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3691, 8.030218181818181, 8.83324, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3691, 12.045327272727272, 13.24986, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3691, 4.015109090909091, 4.41662, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3691, 8.030218181818181, 8.83324, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3691, 8.030218181818181, 8.83324, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3691, 8.030218181818181, 8.83324, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3691, 26.660327272727272, 29.32636, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3691, 11.242299999999998, 12.36653, 44);
INSERT INTO building(id, name, level) VALUES (3699, "building_subsistence_farmslevel", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3699, 0.30533636363636363, 0.33587, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3699, 0.050881818181818174, 0.05597, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3699, 0.050881818181818174, 0.05597, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3699, 0.050881818181818174, 0.05597, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3699, 0.050881818181818174, 0.05597, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3699, 0.050881818181818174, 0.05597, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3699, 0.07124545454545453, 0.07837, 14);
INSERT INTO building(id, name, level) VALUES (3700, "building_subsistence_farmslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3700, 15.091554545454544, 16.60071, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3700, 2.515254545454545, 2.76678, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3700, 2.515254545454545, 2.76678, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3700, 2.515254545454545, 2.76678, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3700, 2.515254545454545, 2.76678, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3700, 2.515254545454545, 2.76678, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3700, 3.521363636363636, 3.8735, 44);
INSERT INTO building(id, name, level) VALUES (33558209, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 33558209, 44.215495049504945, 44.65765, 2);
INSERT INTO building(id, name, level) VALUES (3946, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (3947, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4031, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4032, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (16781274, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16781274, 4.962, 1.2080681365853996, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16781274, 29.772, 7.248408819512397, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 16781274, 14.886, 3.6242044097561985, 1);
INSERT INTO building(id, name, level) VALUES (16781404, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16781404, 10.0, 2.4346395336263598, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 16781404, 40.0, 9.738558134505439, 2);
INSERT INTO building(id, name, level) VALUES (50335983, "building_tea_plantationlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 50335983, 106.1172, 111.42306, 6);
INSERT INTO building(id, name, level) VALUES (4411, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4412, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4413, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4414, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4415, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4416, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4417, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4418, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4419, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4420, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4421, "building_conscription_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4422, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4423, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4424, "building_conscription_centerlevel", 20);
INSERT INTO building(id, name, level) VALUES (4425, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4426, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4427, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4428, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4429, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4430, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4431, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4432, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4433, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4434, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4435, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4436, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4437, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4438, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4439, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4440, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4441, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4442, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4443, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4444, "building_conscription_centerlevel", 18);
INSERT INTO building(id, name, level) VALUES (33559122, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33559122, 9.7806, 21.58876351333491, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33559122, 19.5612, 24.160915164182065, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33559122, 24.4515, 3.10983621183863, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33559122, 4.8903, 1.1906117711292987, 3);
INSERT INTO building(id, name, level) VALUES (67113569, "building_cotton_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 67113569, 70.76, 89.1576, 2);
INSERT INTO building(id, name, level) VALUES (4783, "building_trade_centerlevel", 36);
INSERT INTO building(id, name, level) VALUES (33559247, "building_rice_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 33559247, 39.99999999999999, 44.4, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 33559247, 12.0, 13.32, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 33559247, 18.0, 19.98, 2);
INSERT INTO building(id, name, level) VALUES (67113682, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 67113682, 19.999999999999996, 22.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 67113682, 18.0, 19.98, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 67113682, 12.0, 13.32, 2);
INSERT INTO building(id, name, level) VALUES (83890987, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 83890987, 35.396, 44.245, 1);
INSERT INTO building(id, name, level) VALUES (83891623, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 83891623, 30.0, 37.054345077268366, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 83891623, 20.0, 2.5436772482985748, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 83891623, 60.0, 33.81551587244787, 1);
INSERT INTO building(id, name, level) VALUES (50337200, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 50337200, 5.20197, 11.482332385892768, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50337200, 10.40395, 12.850385115554875, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 50337200, 13.00493, 1.6540172278357792, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50337200, 2.60098, 0.6332448734171489, 2);
INSERT INTO building(id, name, level) VALUES (50337201, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 50337201, 3.2602, 7.19625450444497, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50337201, 6.5204, 8.053638388060689, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 50337201, 8.1505, 1.0366120706128765, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50337201, 1.6301, 0.3968705903764329, 1);
INSERT INTO building(id, name, level) VALUES (16782823, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782823, 3.2602, 7.19625450444497, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782823, 6.5204, 8.053638388060689, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782823, 8.1505, 1.0366120706128765, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782823, 1.6301, 0.3968705903764329, 1);
INSERT INTO building(id, name, level) VALUES (16782850, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782850, 5.0, 1.2173197668131799, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16782850, 40.0, 9.738558134505439, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 16782850, 10.0, 2.4346395336263598, 1);
INSERT INTO building(id, name, level) VALUES (16782865, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782865, 5.0, 1.2173197668131799, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16782865, 29.999999999999996, 7.303918600879078, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 16782865, 14.999999999999998, 3.651959300439539, 1);
INSERT INTO building(id, name, level) VALUES (33560152, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33560152, 9.999999999999998, 2.4346395336263593, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 33560152, 79.99999999999999, 19.477116269010875, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 33560152, 19.999999999999996, 4.869279067252719, 2);
INSERT INTO building(id, name, level) VALUES (16782955, "building_rice_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16782955, 20.0, 22.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 16782955, 5.999999999999999, 6.6, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 16782955, 9.0, 9.9, 1);
INSERT INTO building(id, name, level) VALUES (16782967, "building_rice_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 16782967, 19.904, 21.8944, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 16782967, 5.9712, 6.56832, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 16782967, 8.9568, 9.85248, 1);
INSERT INTO building(id, name, level) VALUES (16783032, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16783032, 29.999999999999996, 33.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 16783032, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (5839, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 5839, 29.999999999999996, 33.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 5839, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (50337489, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 50337489, 3.2602, 7.19625450444497, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50337489, 6.5204, 8.053638388060689, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 50337489, 8.1505, 1.0366120706128765, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50337489, 1.6301, 0.3968705903764329, 1);
INSERT INTO building(id, name, level) VALUES (134223571, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 134223571, 3.2602, 7.19625450444497, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 134223571, 6.5204, 8.053638388060689, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 134223571, 8.1505, 1.0366120706128765, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 134223571, 1.6301, 0.3968705903764329, 1);
INSERT INTO building(id, name, level) VALUES (16783061, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783061, 5.0, 1.2173197668131799, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16783061, 40.0, 9.738558134505439, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 16783061, 10.0, 2.4346395336263598, 1);
INSERT INTO building(id, name, level) VALUES (33560279, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 33560279, 29.999999999999996, 33.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 33560279, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (50337508, "building_construction_sectorlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 50337508, 13.0408, 28.78501801777988, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50337508, 26.0816, 32.214553552242755, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 50337508, 32.602, 4.146448282451506, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50337508, 6.5204, 1.5874823615057316, 4);
INSERT INTO building(id, name, level) VALUES (67114735, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 67114735, 3.23933, 7.150188057138741, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 67114735, 6.47866, 8.00208344260985, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 67114735, 8.09833, 1.02997688851069, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 67114735, 1.61966, 0.394328826703327, 1);
INSERT INTO building(id, name, level) VALUES (16783095, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783095, 5.0, 1.2173197668131799, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16783095, 40.0, 9.738558134505439, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 16783095, 10.0, 2.4346395336263598, 1);
INSERT INTO building(id, name, level) VALUES (5880, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5880, 0.414, 0.5113499620663035, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5880, 0.414, 0.2938332201705334, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5880, 3.312, 2.831332880682133, 1);
INSERT INTO building(id, name, level) VALUES (5883, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 5883, 29.999999999999996, 33.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 5883, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (16783112, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16783112, 22.10775, 22.10775, 1);
INSERT INTO building(id, name, level) VALUES (5902, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5902, 10.0, 2.4346395336263598, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 5902, 40.0, 9.738558134505439, 2);
INSERT INTO building(id, name, level) VALUES (5907, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5907, 3.2602, 7.19625450444497, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5907, 6.5204, 8.053638388060689, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5907, 8.1505, 1.0366120706128765, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5907, 1.6301, 0.3968705903764329, 1);
INSERT INTO building(id, name, level) VALUES (16783128, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783128, 4.07525, 8.995318130556212, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783128, 12.22575, 15.100571977613791, 1);
INSERT INTO building(id, name, level) VALUES (5915, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5915, 0.483, 0.5965749557440208, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5915, 0.483, 0.34280542353228904, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5915, 3.864, 3.303221694129156, 1);
INSERT INTO building(id, name, level) VALUES (5920, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5920, 9.7806, 21.58876351333491, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5920, 19.5612, 24.160915164182065, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5920, 24.4515, 3.10983621183863, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5920, 4.8903, 1.1906117711292987, 3);
INSERT INTO building(id, name, level) VALUES (16783142, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783142, 9.7806, 21.58876351333491, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783142, 19.5612, 24.160915164182065, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783142, 24.4515, 3.10983621183863, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783142, 4.8903, 1.1906117711292987, 3);
INSERT INTO building(id, name, level) VALUES (16783151, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783151, 6.5204, 14.39250900888994, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783151, 13.0408, 16.107276776121378, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783151, 16.301, 2.073224141225753, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783151, 3.2602, 0.7937411807528658, 2);
INSERT INTO building(id, name, level) VALUES (16783155, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783155, 6.5204, 14.39250900888994, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783155, 13.0408, 16.107276776121378, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783155, 16.301, 2.073224141225753, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783155, 3.2602, 0.7937411807528658, 2);
INSERT INTO building(id, name, level) VALUES (16783165, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783165, 6.5204, 14.39250900888994, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783165, 13.0408, 16.107276776121378, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783165, 16.301, 2.073224141225753, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783165, 3.2602, 0.7937411807528658, 2);
INSERT INTO building(id, name, level) VALUES (5952, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5952, 9.7806, 21.58876351333491, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5952, 19.5612, 24.160915164182065, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5952, 24.4515, 3.10983621183863, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5952, 4.8903, 1.1906117711292987, 3);
INSERT INTO building(id, name, level) VALUES (5954, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5954, 9.7806, 21.58876351333491, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5954, 19.5612, 24.160915164182065, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5954, 24.4515, 3.10983621183863, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5954, 4.8903, 1.1906117711292987, 3);
INSERT INTO building(id, name, level) VALUES (83892037, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 83892037, 6.5204, 14.39250900888994, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 83892037, 13.0408, 16.107276776121378, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 83892037, 16.301, 2.073224141225753, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 83892037, 3.2602, 0.7937411807528658, 2);
INSERT INTO building(id, name, level) VALUES (16783175, "building_construction_sectorlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783175, 13.0408, 28.78501801777988, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783175, 26.0816, 32.214553552242755, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783175, 32.602, 4.146448282451506, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783175, 6.5204, 1.5874823615057316, 4);
INSERT INTO building(id, name, level) VALUES (5961, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5961, 6.5204, 14.39250900888994, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5961, 13.0408, 16.107276776121378, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5961, 16.301, 2.073224141225753, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5961, 3.2602, 0.7937411807528658, 2);
INSERT INTO building(id, name, level) VALUES (16783182, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783182, 9.7806, 21.58876351333491, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783182, 19.5612, 24.160915164182065, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783182, 24.4515, 3.10983621183863, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783182, 4.8903, 1.1906117711292987, 3);
INSERT INTO building(id, name, level) VALUES (16783183, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783183, 10.0, 2.4346395336263598, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 16783183, 40.0, 9.738558134505439, 2);
INSERT INTO building(id, name, level) VALUES (5968, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5968, 3.2602, 7.19625450444497, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5968, 6.5204, 8.053638388060689, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5968, 8.1505, 1.0366120706128765, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5968, 1.6301, 0.3968705903764329, 1);
INSERT INTO building(id, name, level) VALUES (5998, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5998, 3.2602, 7.19625450444497, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5998, 6.5204, 8.053638388060689, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5998, 8.1505, 1.0366120706128765, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5998, 1.6301, 0.3968705903764329, 1);
INSERT INTO building(id, name, level) VALUES (83892083, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 83892083, 22.1225, 22.1225, 1);
INSERT INTO building(id, name, level) VALUES (50337685, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 50337685, 44.215495049504945, 44.65765, 2);
INSERT INTO building(id, name, level) VALUES (6045, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (6047, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6047, 0.516, 0.6373347353290159, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 6047, 0.516, 0.3662269120966069, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6047, 4.128, 3.5289076483864275, 1);
INSERT INTO building(id, name, level) VALUES (6053, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 6053, 5.0, 1.2173197668131799, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 6053, 20.0, 4.8692790672527195, 1);
INSERT INTO building(id, name, level) VALUES (16783300, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16783300, 22.10775, 22.10775, 1);
INSERT INTO building(id, name, level) VALUES (16783301, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16783301, 44.215495049504945, 44.65765, 2);
INSERT INTO building(id, name, level) VALUES (6089, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (16783316, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16783316, 35.4036, 44.2545, 1);
INSERT INTO building(id, name, level) VALUES (6112, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6112, 35.4036, 44.2545, 1);
INSERT INTO building(id, name, level) VALUES (33560565, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 33560565, 22.10775, 22.10775, 1);
INSERT INTO building(id, name, level) VALUES (6152, "building_cotton_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6152, 70.74479365079365, 89.13844, 2);
INSERT INTO building(id, name, level) VALUES (16783383, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16783383, 66.32324509803921, 67.64971, 3);
INSERT INTO building(id, name, level) VALUES (16783414, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16783414, 22.142, 22.142, 1);
INSERT INTO building(id, name, level) VALUES (16783421, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16783421, 44.23, 44.6723, 2);
INSERT INTO building(id, name, level) VALUES (16783442, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16783442, 35.38, 44.225, 1);
INSERT INTO building(id, name, level) VALUES (33560667, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 33560667, 22.10775, 22.10775, 1);
INSERT INTO building(id, name, level) VALUES (16783452, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 16783452, 22.10775, 22.10775, 1);
INSERT INTO building(id, name, level) VALUES (33560678, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 33560678, 22.1175, 22.1175, 1);
INSERT INTO building(id, name, level) VALUES (33560730, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 33560730, 9.7806, 21.58876351333491, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33560730, 19.5612, 24.160915164182065, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33560730, 24.4515, 3.10983621183863, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 33560730, 4.8903, 1.1906117711292987, 3);
INSERT INTO building(id, name, level) VALUES (16783520, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783520, 6.5204, 14.39250900888994, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783520, 13.0408, 16.107276776121378, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783520, 16.301, 2.073224141225753, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783520, 3.2602, 0.7937411807528658, 2);
INSERT INTO building(id, name, level) VALUES (6327, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6327, 5.0, 6.175724179544728, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 6327, 5.0, 3.5487103885330127, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6327, 40.0, 34.194841554132054, 1);
INSERT INTO building(id, name, level) VALUES (6495, "building_subsistence_rice_paddieslevel", 915);
INSERT INTO building(id, name, level) VALUES (6496, "building_subsistence_rice_paddieslevel", 846);
INSERT INTO building(id, name, level) VALUES (6497, "building_subsistence_pastureslevel", 72);
INSERT INTO building(id, name, level) VALUES (6498, "building_subsistence_pastureslevel", 48);
INSERT INTO building(id, name, level) VALUES (6499, "building_subsistence_pastureslevel", 35);
INSERT INTO building(id, name, level) VALUES (6500, "building_subsistence_pastureslevel", 53);
INSERT INTO building(id, name, level) VALUES (6501, "building_subsistence_rice_paddieslevel", 876);
INSERT INTO building(id, name, level) VALUES (6502, "building_subsistence_rice_paddieslevel", 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6502, 0.041027272727272725, 0.04513, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6502, 0.005590909090909091, 0.00615, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6502, 0.005590909090909091, 0.00615, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6502, 0.007454545454545454, 0.0082, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6502, 0.007454545454545454, 0.0082, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6502, 0.007454545454545454, 0.0082, 373);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6502, 0.011181818181818182, 0.0123, 373);
INSERT INTO building(id, name, level) VALUES (6503, "building_subsistence_rice_paddieslevel", 282);
INSERT INTO building(id, name, level) VALUES (6504, "building_subsistence_rice_paddieslevel", 455);
INSERT INTO building(id, name, level) VALUES (6505, "building_subsistence_pastureslevel", 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6505, 0.0002, 0.00018, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6505, 0.0003, 0.00027, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6505, 0.0001, 9e-05, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6505, 0.0002, 0.00018, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6505, 0.0002, 0.00018, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6505, 0.0002, 0.00018, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 6505, 0.0006777777777777777, 0.00061, 41);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6505, 0.0002777777777777778, 0.00025, 41);
INSERT INTO building(id, name, level) VALUES (6506, "building_subsistence_pastureslevel", 42);
INSERT INTO building(id, name, level) VALUES (6507, "building_subsistence_rice_paddieslevel", 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6507, 0.048281818181818176, 0.05311, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6507, 0.006581818181818181, 0.00724, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6507, 0.006581818181818181, 0.00724, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6507, 0.008772727272727272, 0.00965, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6507, 0.008772727272727272, 0.00965, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6507, 0.008772727272727272, 0.00965, 878);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6507, 0.013163636363636362, 0.01448, 878);
INSERT INTO building(id, name, level) VALUES (6508, "building_subsistence_rice_paddieslevel", 710);
INSERT INTO building(id, name, level) VALUES (6509, "building_subsistence_rice_paddieslevel", 394);
INSERT INTO building(id, name, level) VALUES (6510, "building_subsistence_rice_paddieslevel", 514);
INSERT INTO building(id, name, level) VALUES (6511, "building_subsistence_rice_paddieslevel", 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6511, 0.048728, 0.06091, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6511, 0.00664, 0.0083, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6511, 0.00664, 0.0083, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6511, 0.008856, 0.01107, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6511, 0.008856, 0.01107, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6511, 0.008856, 0.01107, 886);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6511, 0.013288, 0.01661, 886);
INSERT INTO building(id, name, level) VALUES (6512, "building_subsistence_rice_paddieslevel", 544);
INSERT INTO building(id, name, level) VALUES (6513, "building_subsistence_rice_paddieslevel", 402);
INSERT INTO building(id, name, level) VALUES (6514, "building_subsistence_rice_paddieslevel", 599);
INSERT INTO building(id, name, level) VALUES (6515, "building_subsistence_rice_paddieslevel", 1177);
INSERT INTO building(id, name, level) VALUES (6516, "building_subsistence_rice_paddieslevel", 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6516, 0.14651818181818183, 0.16117, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6516, 0.01997272727272727, 0.02197, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6516, 0.01997272727272727, 0.02197, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6516, 0.026636363636363635, 0.0293, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6516, 0.026636363636363635, 0.0293, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6516, 0.026636363636363635, 0.0293, 666);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6516, 0.03995454545454545, 0.04395, 666);
INSERT INTO building(id, name, level) VALUES (6517, "building_subsistence_rice_paddieslevel", 941);
INSERT INTO building(id, name, level) VALUES (6518, "building_subsistence_rice_paddieslevel", 567);
INSERT INTO building(id, name, level) VALUES (6519, "building_subsistence_rice_paddieslevel", 618);
INSERT INTO building(id, name, level) VALUES (6520, "building_subsistence_rice_paddieslevel", 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6520, 0.03618181818181818, 0.0398, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6520, 0.004927272727272727, 0.00542, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6520, 0.004927272727272727, 0.00542, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6520, 0.006572727272727273, 0.00723, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6520, 0.006572727272727273, 0.00723, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6520, 0.006572727272727273, 0.00723, 658);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6520, 0.009863636363636363, 0.01085, 658);
INSERT INTO building(id, name, level) VALUES (6521, "building_subsistence_rice_paddieslevel", 1253);
INSERT INTO building(id, name, level) VALUES (6522, "building_subsistence_rice_paddieslevel", 1377);
INSERT INTO building(id, name, level) VALUES (6523, "building_subsistence_rice_paddieslevel", 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6523, 1.3295636363636363, 1.46252, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6523, 0.1813, 0.19943, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6523, 0.1813, 0.19943, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6523, 0.2417363636363636, 0.26591, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6523, 0.2417363636363636, 0.26591, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6523, 0.2417363636363636, 0.26591, 1422);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6523, 0.3626090909090909, 0.39887, 1422);
INSERT INTO building(id, name, level) VALUES (6524, "building_subsistence_rice_paddieslevel", 1366);
INSERT INTO building(id, name, level) VALUES (6525, "building_subsistence_rice_paddieslevel", 611);
INSERT INTO building(id, name, level) VALUES (6526, "building_subsistence_rice_paddieslevel", 762);
INSERT INTO building(id, name, level) VALUES (6527, "building_subsistence_rice_paddieslevel", 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6527, 1.0797545454545454, 1.18773, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6527, 0.1472363636363636, 0.16196, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6527, 0.1472363636363636, 0.16196, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6527, 0.1963181818181818, 0.21595, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6527, 0.1963181818181818, 0.21595, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6527, 0.1963181818181818, 0.21595, 818);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6527, 0.2944727272727272, 0.32392, 818);
INSERT INTO building(id, name, level) VALUES (6528, "building_subsistence_rice_paddieslevel", 789);
INSERT INTO building(id, name, level) VALUES (6529, "building_subsistence_rice_paddieslevel", 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6529, 0.05147272727272727, 0.05662, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6529, 0.0070181818181818175, 0.00772, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6529, 0.0070181818181818175, 0.00772, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6529, 0.009354545454545454, 0.01029, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6529, 0.009354545454545454, 0.01029, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6529, 0.009354545454545454, 0.01029, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6529, 0.014036363636363635, 0.01544, 156);
INSERT INTO building(id, name, level) VALUES (6530, "building_subsistence_pastureslevel", 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6530, 0.004254545454545454, 0.00468, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6530, 0.006381818181818181, 0.00702, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6530, 0.002127272727272727, 0.00234, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6530, 0.004254545454545454, 0.00468, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6530, 0.004254545454545454, 0.00468, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6530, 0.004254545454545454, 0.00468, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 6530, 0.014118181818181818, 0.01553, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6530, 0.0059545454545454546, 0.00655, 23);
