
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 85.06669764906948, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 101.4844943251311, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 118.42062737548814, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 104.81393819431965, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 23.386739226390855, 6103.922134828689);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 29.83312737256293, 974.8373351865081);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 27.51270807271842, 535.6788218825379);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 31.747529283940146, 828.0294889519782);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 45.17189771439534, 602.6824989765327);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 35.11430247571279, 1613.0179683790866);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 34.277682849506704, 1052.9247347962798);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 50.25498468020508, 97.67941690578334);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 21.698010019554467, 609.1867179215742);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 48.548755723190204, 32.0634133574812);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 86.9590386579184, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 32.3535457348407, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 62.609756097560975, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 86.44743206687173, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 50.06802592902417, 172.29796277698844);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 68.29447624539154, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 67.34088101104439, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 67.76228761538792, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 86.18569933692638, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 65.91874526802064, 63.697555981494034);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 7.4999999999999964, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 67.13950655944588, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 87.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 113.28880776227624, 106.05910158196443);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 44.28830764392605, 743.9574922096944);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 34.659662120517474, 9.667300090675331);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 40.2871076153325, 2093.6895532349413);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 27.60842610312593, 508.78607151418976);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 29.892577493016837, 18.737603485810137);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 41.87850069537335, 91.20748611379362);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 66.14374383909636, 392.86809194879544);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 72.11980761561105, 462.1311615771875);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 92.80866082083165, 462.1311615771875);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 337.6044818823818, 6.163357105584504);
INSERT INTO building(id, name, level) VALUES (209, "building_government_administrationlevel", 14);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 209, 280.0, 28.52192174257953, 14);
INSERT INTO building(id, name, level) VALUES (210, "building_construction_sectorlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 210, 99.007, 49.523879143077764, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 210, 198.014, 43.25930324588209, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 210, 247.5175, 14.833827051872857, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 210, 49.5035, 4.846388532634407, 5);
INSERT INTO building(id, name, level) VALUES (211, "building_universitylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 211, 25.0, 2.5466001555874582, 5);
INSERT INTO building(id, name, level) VALUES (212, "building_arts_academylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 212, 12.5, 1.2733000777937291, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 212, 5.0, 0.5093200311174916, 5);
INSERT INTO building(id, name, level) VALUES (213, "building_paper_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 213, 150.0, 32.76988236630901, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 213, 50.0, 1.4794478293657545, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 213, 350.0, 43.40959683014066, 5);
INSERT INTO building(id, name, level) VALUES (214, "building_furniture_manufacturieslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 214, 79.99999999999999, 40.016466830084944, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 214, 159.99999999999997, 34.95454119072961, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 214, 79.99999999999999, 6.217097622700633, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 214, 39.99999999999999, 3.915996673071121, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 214, 359.99999999999994, 80.4856819435792, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 214, 159.99999999999997, 35.771414197146306, 8);
INSERT INTO building(id, name, level) VALUES (215, "building_wheat_farmlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 215, 120.00000000000001, 140.4, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 215, 64.0, 74.88, 8);
INSERT INTO building(id, name, level) VALUES (216, "building_livestock_ranchlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 216, 40.00000000000001, 30.96869539629106, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 216, 40.00000000000001, 3.915996673071122, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 216, 160.00000000000003, 69.76938413872436, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 216, 20.000000000000004, 8.721173017340545, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 216, 100.00000000000001, 43.60586508670273, 4);
INSERT INTO building(id, name, level) VALUES (217, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 217, 10.0, 1.1261366979887508, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 217, 10.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 217, 50.0, 2.8153417449718767, 1);
INSERT INTO building(id, name, level) VALUES (218, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 218, 10.0, 0.7047342027055767, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 218, 10.0, 0.8391109300591055, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 218, 10.0, 0.8345995809727663, 10);
INSERT INTO building(id, name, level) VALUES (221, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 221, 90.0, 45.018525183845576, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 221, 30.0, 40.262068965517244, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 221, 15.0, 3.6951219512195124, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 221, 120.0, 69.86189195162784, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 221, 60.0, 34.93094597581392, 3);
INSERT INTO building(id, name, level) VALUES (222, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 222, 10.0, 4.033298270382315, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 222, 100.0, 40.33298270382315, 2);
INSERT INTO building(id, name, level) VALUES (223, "building_silk_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 223, 159.99999999999997, 171.2, 8);
INSERT INTO building(id, name, level) VALUES (224, "building_wheat_farmlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 224, 150.0, 163.5, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 224, 80.0, 87.2, 10);
INSERT INTO building(id, name, level) VALUES (225, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 225, 5.0, 0.35236710135278837, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 225, 5.0, 0.41955546502955277, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 225, 5.0, 0.41729979048638316, 5);
INSERT INTO building(id, name, level) VALUES (226, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 226, 20.0, 6.778209824519379, 10);
INSERT INTO building(id, name, level) VALUES (227, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 227, 15.0, 6.049947405573473, 3);
INSERT INTO building(id, name, level) VALUES (228, "building_government_administrationlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 228, 80.0, 8.149120497879867, 4);
INSERT INTO building(id, name, level) VALUES (229, "building_shipyardslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 229, 120.0, 60.024700245127434, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 229, 240.0, 52.43181178609442, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 229, 90.0, 32.340227301815496, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 229, 120.0, 43.12030306908733, 6);
INSERT INTO building(id, name, level) VALUES (230, "building_textile_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 230, 240.0, 120.04940049025487, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 230, 79.99999999999999, 107.36551724137928, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 230, 39.99999999999999, 9.853658536585364, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 230, 319.99999999999994, 186.2983785376742, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 230, 159.99999999999997, 93.1491892688371, 8);
INSERT INTO building(id, name, level) VALUES (231, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 231, 15.0, 6.049947405573473, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 231, 150.0, 60.49947405573474, 3);
INSERT INTO building(id, name, level) VALUES (232, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 232, 10.0, 0.7047342027055767, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 232, 10.0, 0.8391109300591055, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 232, 10.0, 0.8345995809727663, 10);
INSERT INTO building(id, name, level) VALUES (233, "building_wheat_farmlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 233, 90.0, 94.5, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 233, 48.0, 50.4, 6);
INSERT INTO building(id, name, level) VALUES (234, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 234, 20.0, 1.9579983365355607, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 234, 60.0, 5.873995009606682, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 234, 50.0, 4.894995841338902, 2);
INSERT INTO building(id, name, level) VALUES (235, "building_naval_baselevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 235, 30.0, 10.167314736779069, 15);
INSERT INTO building(id, name, level) VALUES (236, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 236, 15.0, 6.049947405573473, 3);
INSERT INTO building(id, name, level) VALUES (237, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 237, 60.0, 6.111840373409899, 3);
INSERT INTO building(id, name, level) VALUES (238, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 238, 10.0, 1.0186400622349834, 2);
INSERT INTO building(id, name, level) VALUES (239, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 239, 10.0, 4.033298270382315, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 239, 100.0, 40.33298270382315, 2);
INSERT INTO building(id, name, level) VALUES (240, "building_glassworkslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 240, 100.0, 21.84658824420601, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 240, 50.0, 4.671940513786593, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 240, 200.0, 31.190469271779197, 5);
INSERT INTO building(id, name, level) VALUES (241, "building_wheat_farmlevel", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 241, 135.0, 145.8, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 241, 72.0, 77.76, 9);
INSERT INTO building(id, name, level) VALUES (242, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 242, 20.0, 15.484347698145527, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 242, 20.0, 1.9579983365355607, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 242, 80.0, 34.884692069362174, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 242, 10.0, 4.360586508670272, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 242, 50.0, 21.802932543351357, 2);
INSERT INTO building(id, name, level) VALUES (243, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 243, 15.0, 1.057101304058365, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 243, 15.0, 1.2586663950886585, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 243, 15.0, 1.2518993714591495, 15);
INSERT INTO building(id, name, level) VALUES (244, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 244, 39.6028, 19.809551657231104, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 244, 79.2056, 17.303721298352837, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 244, 99.007, 5.933530820749143, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 244, 19.8014, 1.9385554130537628, 2);
INSERT INTO building(id, name, level) VALUES (245, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 245, 120.0, 12.223680746819799, 6);
INSERT INTO building(id, name, level) VALUES (246, "building_textile_millslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 246, 209.99999999999997, 105.04322542897299, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 246, 70.0, 93.9448275862069, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 246, 35.0, 8.621951219512194, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 246, 280.0, 163.01108122046494, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 246, 140.0, 81.50554061023247, 7);
INSERT INTO building(id, name, level) VALUES (247, "building_paper_millslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 247, 180.0, 39.32385883957081, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 247, 60.0, 1.7753373952389055, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 247, 420.0, 52.09151619616878, 6);
INSERT INTO building(id, name, level) VALUES (248, "building_arms_industrylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 248, 100.0, 5.993041725079179, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 248, 50.0, 3.885686014187896, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 248, 75.0, 5.161655157545614, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 248, 75.0, 5.161655157545614, 5);
INSERT INTO building(id, name, level) VALUES (249, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 249, 20.0, 1.9579983365355607, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 249, 160.0, 15.663986692284485, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 249, 40.0, 3.9159966730711213, 4);
INSERT INTO building(id, name, level) VALUES (250, "building_coal_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 250, 40.00000000000001, 3.915996673071122, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 250, 160.00000000000003, 15.663986692284489, 4);
INSERT INTO building(id, name, level) VALUES (251, "building_silk_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 251, 199.99999999999997, 218.0, 10);
INSERT INTO building(id, name, level) VALUES (252, "building_wheat_farmlevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 252, 225.00000000000003, 256.5, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 252, 120.00000000000001, 136.8, 15);
INSERT INTO building(id, name, level) VALUES (253, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 253, 15.0, 1.057101304058365, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 253, 15.0, 1.2586663950886585, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 253, 15.0, 1.2518993714591495, 15);
INSERT INTO building(id, name, level) VALUES (254, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 254, 10.0, 1.1261366979887508, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 254, 10.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 254, 50.0, 2.8153417449718767, 1);
INSERT INTO building(id, name, level) VALUES (255, "building_arms_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 255, 80.0, 4.794433380063343, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 255, 40.0, 3.108548811350317, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 255, 59.99999999999999, 4.129324126036491, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 255, 59.99999999999999, 4.129324126036491, 4);
INSERT INTO building(id, name, level) VALUES (256, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 256, 10.0, 0.9789991682677803, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 256, 80.0, 7.831993346142243, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 256, 20.0, 1.9579983365355607, 2);
INSERT INTO building(id, name, level) VALUES (257, "building_munition_plantslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 257, 100.0, 9.343881027573186, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 257, 100.0, 0.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 257, 250.0, 11.679851284466483, 5);
INSERT INTO building(id, name, level) VALUES (258, "building_wheat_farmlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 258, 120.00000000000001, 140.4, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 258, 64.0, 74.88, 8);
INSERT INTO building(id, name, level) VALUES (259, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 259, 19.999999999999996, 15.484347698145525, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 259, 19.999999999999996, 1.9579983365355604, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 259, 79.99999999999999, 34.884692069362174, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 259, 9.999999999999998, 4.360586508670272, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 259, 49.99999999999999, 21.802932543351357, 2);
INSERT INTO building(id, name, level) VALUES (260, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 260, 20.0, 1.4094684054111535, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 260, 20.0, 1.678221860118211, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 260, 20.0, 1.6691991619455326, 20);
INSERT INTO building(id, name, level) VALUES (261, "building_naval_baselevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 261, 50.0, 16.94552456129845, 25);
INSERT INTO building(id, name, level) VALUES (262, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 262, 10.0, 4.033298270382315, 2);
INSERT INTO building(id, name, level) VALUES (263, "building_coal_minelevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 263, 60.00000000000001, 5.873995009606682, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 263, 240.00000000000003, 23.49598003842673, 6);
INSERT INTO building(id, name, level) VALUES (264, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 264, 100.0, 10.186400622349833, 5);
INSERT INTO building(id, name, level) VALUES (265, "building_textile_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 265, 150.0, 75.03087530640929, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 265, 50.0, 67.10344827586206, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 265, 25.0, 6.158536585365853, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 265, 200.0, 116.4364865860464, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 265, 100.0, 58.2182432930232, 5);
INSERT INTO building(id, name, level) VALUES (266, "building_wheat_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 266, 105.0, 121.8, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 266, 56.0, 64.96, 7);
INSERT INTO building(id, name, level) VALUES (267, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 267, 19.999999999999996, 15.484347698145525, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 267, 19.999999999999996, 1.9579983365355604, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 267, 79.99999999999999, 34.884692069362174, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 267, 9.999999999999998, 4.360586508670272, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 267, 49.99999999999999, 21.802932543351357, 2);
INSERT INTO building(id, name, level) VALUES (268, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 268, 30.0, 3.3784100939662527, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 268, 40.0, 2.3972166900316716, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 268, 65.0, 5.607682829114174, 1);
INSERT INTO building(id, name, level) VALUES (269, "building_arms_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 269, 60.0, 3.595825035047507, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 269, 30.0, 2.3314116085127377, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 269, 45.0, 3.0969930945273685, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 269, 45.0, 3.0969930945273685, 3);
INSERT INTO building(id, name, level) VALUES (270, "building_iron_minelevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 270, 60.0, 6.756820187932505, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 270, 60.0, 5.8739950096066815, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 270, 240.0, 25.261630395078374, 6);
INSERT INTO building(id, name, level) VALUES (271, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 271, 25.0, 2.4474979206694507, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 271, 200.0, 19.579983365355606, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 271, 50.0, 4.894995841338901, 5);
INSERT INTO building(id, name, level) VALUES (272, "building_wheat_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 272, 104.99999999999999, 111.3, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 272, 56.0, 59.36, 7);
INSERT INTO building(id, name, level) VALUES (273, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 273, 15.0, 1.057101304058365, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 273, 15.0, 1.2586663950886585, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 273, 15.0, 1.2518993714591495, 15);
INSERT INTO building(id, name, level) VALUES (274, "building_paper_millslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 274, 119.99999999999999, 26.215905893047207, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 274, 40.0, 1.1835582634926036, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 274, 279.99999999999994, 34.72767746411252, 4);
INSERT INTO building(id, name, level) VALUES (275, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 275, 45.0, 45.9, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 275, 24.0, 24.48, 3);
INSERT INTO building(id, name, level) VALUES (276, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 276, 10.0, 4.033298270382315, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 276, 100.0, 40.33298270382315, 2);
INSERT INTO building(id, name, level) VALUES (277, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 277, 5.0, 2.0166491351911575, 1);
INSERT INTO building(id, name, level) VALUES (278, "building_wheat_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 278, 59.99999999999999, 61.8, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 278, 32.0, 32.96, 4);
INSERT INTO building(id, name, level) VALUES (279, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 279, 10.0, 7.742173849072763, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 279, 10.0, 0.9789991682677803, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 279, 40.0, 17.442346034681087, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 279, 5.0, 2.180293254335136, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 279, 25.0, 10.901466271675678, 1);
INSERT INTO building(id, name, level) VALUES (280, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 280, 5.0, 0.48949958413389016, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 280, 40.0, 3.9159966730711213, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 280, 10.0, 0.9789991682677803, 1);
INSERT INTO building(id, name, level) VALUES (281, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 281, 90.0, 45.018525183845576, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 281, 30.0, 40.262068965517244, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 281, 15.0, 3.6951219512195124, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 281, 120.0, 69.86189195162784, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 281, 60.0, 34.93094597581392, 3);
INSERT INTO building(id, name, level) VALUES (282, "building_wheat_farmlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 282, 120.0, 128.4, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 282, 64.0, 68.48, 8);
INSERT INTO building(id, name, level) VALUES (283, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 283, 20.0, 15.484347698145527, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 283, 20.0, 1.9579983365355607, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 283, 80.0, 34.884692069362174, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 283, 10.0, 4.360586508670272, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 283, 50.0, 21.802932543351357, 2);
INSERT INTO building(id, name, level) VALUES (284, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 284, 15.0, 1.057101304058365, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 284, 15.0, 1.2586663950886585, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 284, 15.0, 1.2518993714591495, 15);
INSERT INTO building(id, name, level) VALUES (285, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 285, 60.0, 6.111840373409899, 3);
INSERT INTO building(id, name, level) VALUES (286, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 286, 90.0, 45.018525183845576, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 286, 30.0, 40.262068965517244, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 286, 15.0, 3.6951219512195124, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 286, 120.0, 69.86189195162784, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 286, 60.0, 34.93094597581392, 3);
INSERT INTO building(id, name, level) VALUES (287, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 287, 20.0, 10.004116707521238, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 287, 40.0, 8.738635297682404, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 287, 20.0, 1.5542744056751585, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 287, 10.0, 0.9789991682677803, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 287, 90.0, 20.121420485894802, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 287, 40.0, 8.942853549286578, 2);
INSERT INTO building(id, name, level) VALUES (288, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 288, 15.0, 1.4684987524016704, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 288, 120.0, 11.747990019213363, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 288, 30.0, 2.9369975048033408, 3);
INSERT INTO building(id, name, level) VALUES (289, "building_wheat_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 289, 104.99999999999999, 111.3, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 289, 56.0, 59.36, 7);
INSERT INTO building(id, name, level) VALUES (290, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 290, 25.0, 1.761835506763942, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 290, 25.0, 2.097777325147764, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 290, 25.0, 2.0864989524319157, 25);
INSERT INTO building(id, name, level) VALUES (291, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 291, 100.0, 10.186400622349833, 5);
INSERT INTO building(id, name, level) VALUES (292, "building_shipyardslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 292, 80.0, 40.01646683008495, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 292, 160.0, 34.954541190729614, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 292, 59.99999999999999, 21.560151534543657, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 292, 80.0, 28.74686871272488, 4);
INSERT INTO building(id, name, level) VALUES (293, "building_logging_camplevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 293, 20.0, 1.9579983365355607, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 293, 160.0, 15.663986692284485, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 293, 40.0, 3.9159966730711213, 4);
INSERT INTO building(id, name, level) VALUES (294, "building_arms_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 294, 40.0, 2.3972166900316716, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 294, 20.0, 1.5542744056751585, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 294, 30.0, 2.0646620630182455, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 294, 30.0, 2.0646620630182455, 2);
INSERT INTO building(id, name, level) VALUES (295, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 295, 15.0, 6.049947405573473, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 295, 150.0, 60.49947405573474, 3);
INSERT INTO building(id, name, level) VALUES (296, "building_naval_baselevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 296, 40.0, 13.556419649038759, 20);
INSERT INTO building(id, name, level) VALUES (297, "building_wheat_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 297, 59.99999999999999, 61.8, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 297, 32.0, 32.96, 4);
INSERT INTO building(id, name, level) VALUES (298, "building_livestock_ranchlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 298, 50.0, 38.71086924536382, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 298, 50.0, 4.894995841338901, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 298, 200.0, 87.21173017340543, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 298, 25.0, 10.901466271675678, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 298, 125.0, 54.507331358378394, 5);
INSERT INTO building(id, name, level) VALUES (299, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 299, 15.0, 6.049947405573473, 3);
INSERT INTO building(id, name, level) VALUES (300, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 300, 100.0, 10.186400622349833, 5);
INSERT INTO building(id, name, level) VALUES (301, "building_tooling_workshopslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 301, 119.99999999999999, 26.215905893047207, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 301, 80.0, 3.0063629870186355, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 301, 320.0, 40.96726716476688, 4);
INSERT INTO building(id, name, level) VALUES (302, "building_furniture_manufacturieslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 302, 99.99999999999999, 50.020583537606186, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 302, 199.99999999999997, 43.69317648841201, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 302, 99.99999999999999, 7.771372028375791, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 302, 49.99999999999999, 4.8949958413389005, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 302, 449.99999999999994, 100.60710242947398, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 302, 199.99999999999997, 44.71426774643288, 10);
INSERT INTO building(id, name, level) VALUES (303, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 303, 10.0, 0.9789991682677803, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 303, 80.0, 7.831993346142243, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 303, 20.0, 1.9579983365355607, 2);
INSERT INTO building(id, name, level) VALUES (304, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 304, 20.0, 2.2522733959775016, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 304, 20.0, 1.9579983365355607, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 304, 80.0, 8.420543465026125, 2);
INSERT INTO building(id, name, level) VALUES (305, "building_wheat_farmlevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 305, 180.0, 199.8, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 305, 96.0, 106.56, 12);
INSERT INTO building(id, name, level) VALUES (306, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 306, 10.0, 0.7047342027055767, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 306, 10.0, 0.8391109300591055, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 306, 10.0, 0.8345995809727663, 10);
INSERT INTO building(id, name, level) VALUES (307, "building_food_industrylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 307, 240.0, 185.81217237774632, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 307, 240.0, 113.29599258268432, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 307, 210.0, 130.85982217018838, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 307, 360.0, 224.33112372032295, 6);
INSERT INTO building(id, name, level) VALUES (308, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 308, 10.0, 0.7047342027055767, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 308, 10.0, 0.8391109300591055, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 308, 10.0, 0.8345995809727663, 10);
INSERT INTO building(id, name, level) VALUES (309, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 309, 15.0, 1.4684987524016704, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 309, 120.0, 11.747990019213363, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 309, 30.0, 2.9369975048033408, 3);
INSERT INTO building(id, name, level) VALUES (310, "building_wheat_farmlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 310, 150.0, 178.5, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 310, 80.0, 95.2, 10);
INSERT INTO building(id, name, level) VALUES (311, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 311, 100.0, 10.186400622349833, 5);
INSERT INTO building(id, name, level) VALUES (312, "building_furniture_manufacturieslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 312, 79.99999999999999, 40.016466830084944, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 312, 159.99999999999997, 34.95454119072961, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 312, 79.99999999999999, 6.217097622700633, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 312, 39.99999999999999, 3.915996673071121, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 312, 359.99999999999994, 80.4856819435792, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 312, 159.99999999999997, 35.771414197146306, 8);
INSERT INTO building(id, name, level) VALUES (313, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 313, 10.0, 1.1261366979887508, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 313, 10.0, 0.9789991682677803, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 313, 40.0, 4.210271732513062, 1);
INSERT INTO building(id, name, level) VALUES (314, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 314, 30.0, 30.3, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 314, 16.0, 16.16, 2);
INSERT INTO building(id, name, level) VALUES (315, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 315, 10.0, 0.7047342027055767, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 315, 10.0, 0.8391109300591055, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 315, 10.0, 0.8345995809727663, 10);
INSERT INTO building(id, name, level) VALUES (316, "building_tooling_workshopslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 316, 119.99999999999999, 26.215905893047207, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 316, 80.0, 3.0063629870186355, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 316, 320.0, 40.96726716476688, 4);
INSERT INTO building(id, name, level) VALUES (317, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 317, 30.0, 30.3, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 317, 16.0, 16.16, 2);
INSERT INTO building(id, name, level) VALUES (318, "building_lead_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 318, 40.0, 4.504546791955003, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 318, 40.0, 3.9159966730711213, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 318, 160.0, 16.84108693005225, 4);
INSERT INTO building(id, name, level) VALUES (319, "building_sulfur_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 319, 10.0, 1.1261366979887508, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 319, 10.0, 0.9789991682677803, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 319, 40.0, 4.210271732513062, 1);
INSERT INTO building(id, name, level) VALUES (320, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 320, 25.0, 2.4474979206694507, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 320, 200.0, 19.579983365355606, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 320, 50.0, 4.894995841338901, 5);
INSERT INTO building(id, name, level) VALUES (321, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 321, 15.0, 1.057101304058365, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 321, 15.0, 1.2586663950886585, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 321, 15.0, 1.2518993714591495, 15);
INSERT INTO building(id, name, level) VALUES (322, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 322, 60.0, 6.111840373409899, 3);
INSERT INTO building(id, name, level) VALUES (323, "building_food_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 323, 120.0, 92.90608618887316, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 323, 120.0, 56.64799629134216, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 323, 104.99999999999999, 65.42991108509419, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 323, 180.0, 112.16556186016147, 3);
INSERT INTO building(id, name, level) VALUES (324, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 324, 15.0, 1.4684987524016704, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 324, 120.0, 11.747990019213363, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 324, 30.0, 2.9369975048033408, 3);
INSERT INTO building(id, name, level) VALUES (325, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 325, 10.0, 4.033298270382315, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 325, 100.0, 40.33298270382315, 2);
INSERT INTO building(id, name, level) VALUES (326, "building_wheat_farmlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 326, 150.0, 163.5, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 326, 80.0, 87.2, 10);
INSERT INTO building(id, name, level) VALUES (327, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 327, 20.0, 6.778209824519379, 10);
INSERT INTO building(id, name, level) VALUES (328, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 328, 15.0, 6.049947405573473, 3);
INSERT INTO building(id, name, level) VALUES (329, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 329, 100.0, 10.186400622349833, 5);
INSERT INTO building(id, name, level) VALUES (330, "building_glassworkslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 330, 80.0, 17.477270595364807, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 330, 59.99999999999999, 14.780487804878046, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 330, 40.0, 3.7375524110292746, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 330, 80.0, 14.886564163531364, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 330, 100.0, 18.608205204414205, 4);
INSERT INTO building(id, name, level) VALUES (331, "building_wheat_farmlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 331, 120.00000000000001, 140.4, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 331, 64.0, 74.88, 8);
INSERT INTO building(id, name, level) VALUES (332, "building_livestock_ranchlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 332, 40.00000000000001, 30.96869539629106, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 332, 40.00000000000001, 3.915996673071122, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 332, 160.00000000000003, 69.76938413872436, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 332, 20.000000000000004, 8.721173017340545, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 332, 100.00000000000001, 43.60586508670273, 4);
INSERT INTO building(id, name, level) VALUES (333, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 333, 5.0, 0.35236710135278837, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 333, 5.0, 0.41955546502955277, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 333, 5.0, 0.41729979048638316, 5);
INSERT INTO building(id, name, level) VALUES (634, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 634, 10.0, 7.742173849072763, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 634, 10.0, 0.9789991682677803, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 634, 40.0, 17.442346034681087, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 634, 5.0, 2.180293254335136, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 634, 25.0, 10.901466271675678, 1);
INSERT INTO building(id, name, level) VALUES (635, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 635, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (636, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 636, 5.0, 0.41955546502955277, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 636, 5.0, 0.41729979048638316, 5);
INSERT INTO building(id, name, level) VALUES (637, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 637, 5.0, 2.0166491351911575, 1);
INSERT INTO building(id, name, level) VALUES (1207, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1207, 10.0, 4.033298270382315, 2);
INSERT INTO building(id, name, level) VALUES (1208, "building_government_administrationlevel", 1);
INSERT INTO building(id, name, level) VALUES (1209, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1209, 15.000000000000002, 17.25, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1209, 8.0, 9.2, 1);
INSERT INTO building(id, name, level) VALUES (1210, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1210, 30.000000000000004, 34.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1210, 5.0, 5.75, 1);
INSERT INTO building(id, name, level) VALUES (1211, "building_barrackslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1211, 1.0, 0.08345995809727663, 2);
INSERT INTO building(id, name, level) VALUES (1221, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1221, 5.0, 2.0166491351911575, 1);
INSERT INTO building(id, name, level) VALUES (1222, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1222, 11.6307, 11.6307, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1222, 6.20304, 6.20304, 1);
INSERT INTO building(id, name, level) VALUES (1227, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1227, 5.0, 2.0166491351911575, 1);
INSERT INTO building(id, name, level) VALUES (1228, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1228, 4.26, 1.7181850631828661, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1228, 42.6, 17.181850631828663, 1);
INSERT INTO building(id, name, level) VALUES (1229, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1229, 12.73845, 12.73845, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1229, 6.79384, 6.79384, 1);
INSERT INTO building(id, name, level) VALUES (1259, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1260, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1260, 60.0, 60.6, 2);
INSERT INTO building(id, name, level) VALUES (1261, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1732, "building_sugar_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1732, 90.0, 91.8, 3);
INSERT INTO building(id, name, level) VALUES (1733, "building_coffee_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1733, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (1734, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1734, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (1735, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (1762, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (2328, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2328, 60.0, 60.6, 2);
INSERT INTO building(id, name, level) VALUES (2329, "building_portlevel", 1);
INSERT INTO building(id, name, level) VALUES (2353, "building_dye_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2353, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (2354, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2354, 5.0, 2.0166491351911575, 1);
INSERT INTO building(id, name, level) VALUES (2878, "building_subsistence_farmslevel", 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2878, 67.96825, 67.96825, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2878, 13.59365, 13.59365, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2878, 13.59365, 13.59365, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2878, 13.59365, 13.59365, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2878, 13.59365, 13.59365, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2878, 13.59365, 13.59365, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2878, 19.03111, 19.03111, 35);
INSERT INTO building(id, name, level) VALUES (2879, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2879, 10.0, 2.184658824420601, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2879, 10.0, 1.400362399822422, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2879, 80.0, 14.34008489697209, 2);
INSERT INTO building(id, name, level) VALUES (2880, "building_subsistence_farmslevel", 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2880, 205.0, 205.0, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2880, 41.0, 41.0, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2880, 41.0, 41.0, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2880, 41.0, 41.0, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2880, 41.0, 41.0, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2880, 41.0, 41.0, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2880, 57.4, 57.4, 82);
INSERT INTO building(id, name, level) VALUES (2881, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2881, 30.0, 6.553976473261803, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2881, 30.0, 4.201087199467266, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2881, 240.0, 43.020254690916275, 6);
INSERT INTO building(id, name, level) VALUES (3131, "building_subsistence_farmslevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3131, 2.39, 2.39, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3131, 0.478, 0.478, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3131, 0.478, 0.478, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3131, 0.478, 0.478, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3131, 0.478, 0.478, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3131, 0.478, 0.478, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3131, 0.6692, 0.6692, 5);
INSERT INTO building(id, name, level) VALUES (3214, "building_subsistence_orchardslevel", 18);
INSERT INTO building(id, name, level) VALUES (3219, "building_subsistence_farmslevel", 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3219, 4.666347826086957, 5.3663, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3219, 0.9332695652173915, 1.07326, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3219, 0.9332695652173915, 1.07326, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3219, 0.9332695652173915, 1.07326, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3219, 0.9332695652173915, 1.07326, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3219, 0.9332695652173915, 1.07326, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3219, 1.3065739130434784, 1.50256, 13);
INSERT INTO building(id, name, level) VALUES (3221, "building_subsistence_orchardslevel", 9);
INSERT INTO building(id, name, level) VALUES (3246, "building_subsistence_farmslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3246, 9.267, 9.267, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3246, 1.8534, 1.8534, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3246, 1.8534, 1.8534, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3246, 1.8534, 1.8534, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3246, 1.8534, 1.8534, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3246, 1.8534, 1.8534, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3246, 2.59476, 2.59476, 6);
INSERT INTO building(id, name, level) VALUES (3278, "building_subsistence_farmslevel", 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3278, 197.5, 197.5, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3278, 39.5, 39.5, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3278, 39.5, 39.5, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3278, 39.5, 39.5, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3278, 39.5, 39.5, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3278, 39.5, 39.5, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3278, 55.3, 55.3, 79);
INSERT INTO building(id, name, level) VALUES (3279, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3279, 10.0, 2.184658824420601, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3279, 10.0, 1.400362399822422, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3279, 80.0, 14.34008489697209, 2);
INSERT INTO building(id, name, level) VALUES (3280, "building_subsistence_farmslevel", 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3280, 162.23025, 162.23025, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3280, 32.44605, 32.44605, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3280, 32.44605, 32.44605, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3280, 32.44605, 32.44605, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3280, 32.44605, 32.44605, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3280, 32.44605, 32.44605, 65);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3280, 45.42447, 45.42447, 65);
INSERT INTO building(id, name, level) VALUES (3281, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3281, 35.0, 7.646305885472103, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3281, 35.0, 4.901268399378477, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3281, 280.0, 50.19029713940232, 7);
INSERT INTO building(id, name, level) VALUES (3282, "building_subsistence_farmslevel", 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3282, 125.37674545454546, 137.91442, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3282, 25.075345454545452, 27.58288, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3282, 25.075345454545452, 27.58288, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3282, 25.075345454545452, 27.58288, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3282, 25.075345454545452, 27.58288, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3282, 25.075345454545452, 27.58288, 90);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3282, 35.105481818181815, 38.61603, 90);
INSERT INTO building(id, name, level) VALUES (3283, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3283, 20.0, 4.369317648841202, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3283, 20.0, 2.800724799644844, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3283, 160.0, 28.68016979394418, 4);
INSERT INTO building(id, name, level) VALUES (3284, "building_subsistence_farmslevel", 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3284, 127.49999999999999, 140.25, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3284, 25.5, 28.05, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3284, 25.5, 28.05, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3284, 25.5, 28.05, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3284, 25.5, 28.05, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3284, 25.5, 28.05, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3284, 35.7, 39.27, 51);
INSERT INTO building(id, name, level) VALUES (3285, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3285, 10.0, 2.184658824420601, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3285, 10.0, 1.400362399822422, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3285, 80.0, 14.34008489697209, 2);
INSERT INTO building(id, name, level) VALUES (3286, "building_subsistence_farmslevel", 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3286, 132.09322, 132.09322, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3286, 26.41864, 26.41864, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3286, 26.41864, 26.41864, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3286, 26.41864, 26.41864, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3286, 26.41864, 26.41864, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3286, 26.41864, 26.41864, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3286, 36.9861, 36.9861, 53);
INSERT INTO building(id, name, level) VALUES (3287, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3287, 10.0, 2.184658824420601, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3287, 10.0, 1.400362399822422, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3287, 80.0, 14.34008489697209, 2);
INSERT INTO building(id, name, level) VALUES (3288, "building_subsistence_farmslevel", 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3288, 142.5, 142.5, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3288, 28.5, 28.5, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3288, 28.5, 28.5, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3288, 28.5, 28.5, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3288, 28.5, 28.5, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3288, 28.5, 28.5, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3288, 39.9, 39.9, 57);
INSERT INTO building(id, name, level) VALUES (3289, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3289, 5.0, 1.0923294122103004, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3289, 5.0, 0.700181199911211, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3289, 40.0, 7.170042448486045, 1);
INSERT INTO building(id, name, level) VALUES (3290, "building_subsistence_farmslevel", 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3290, 174.96325, 174.96325, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3290, 34.99265, 34.99265, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3290, 34.99265, 34.99265, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3290, 34.99265, 34.99265, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3290, 34.99265, 34.99265, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3290, 34.99265, 34.99265, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3290, 48.98971, 48.98971, 70);
INSERT INTO building(id, name, level) VALUES (3291, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3291, 5.0, 1.0923294122103004, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3291, 5.0, 0.700181199911211, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3291, 40.0, 7.170042448486045, 1);
INSERT INTO building(id, name, level) VALUES (3292, "building_subsistence_farmslevel", 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3292, 157.5, 157.5, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3292, 31.5, 31.5, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3292, 31.5, 31.5, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3292, 31.5, 31.5, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3292, 31.5, 31.5, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3292, 31.5, 31.5, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3292, 44.1, 44.1, 63);
INSERT INTO building(id, name, level) VALUES (3293, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3293, 10.0, 2.184658824420601, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3293, 10.0, 1.400362399822422, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3293, 80.0, 14.34008489697209, 2);
INSERT INTO building(id, name, level) VALUES (3294, "building_subsistence_farmslevel", 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3294, 227.5, 227.5, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3294, 45.5, 45.5, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3294, 45.5, 45.5, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3294, 45.5, 45.5, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3294, 45.5, 45.5, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3294, 45.5, 45.5, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3294, 63.7, 63.7, 91);
INSERT INTO building(id, name, level) VALUES (3295, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3295, 20.0, 4.369317648841202, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3295, 20.0, 2.800724799644844, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3295, 160.0, 28.68016979394418, 4);
INSERT INTO building(id, name, level) VALUES (3296, "building_subsistence_farmslevel", 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3296, 113.0376, 113.0376, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3296, 22.60752, 22.60752, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3296, 22.60752, 22.60752, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3296, 22.60752, 22.60752, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3296, 22.60752, 22.60752, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3296, 22.60752, 22.60752, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3296, 31.65052, 31.65052, 78);
INSERT INTO building(id, name, level) VALUES (3297, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3297, 20.0, 4.369317648841202, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3297, 20.0, 2.800724799644844, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3297, 160.0, 28.68016979394418, 4);
INSERT INTO building(id, name, level) VALUES (3298, "building_subsistence_farmslevel", 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3298, 104.63949999999998, 115.10345, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3298, 20.927899999999998, 23.02069, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3298, 20.927899999999998, 23.02069, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3298, 20.927899999999998, 23.02069, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3298, 20.927899999999998, 23.02069, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3298, 20.927899999999998, 23.02069, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3298, 29.299054545454545, 32.22896, 70);
INSERT INTO building(id, name, level) VALUES (3299, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3299, 10.0, 2.184658824420601, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3299, 10.0, 1.400362399822422, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3299, 80.0, 14.34008489697209, 2);
INSERT INTO building(id, name, level) VALUES (3300, "building_subsistence_farmslevel", 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3300, 76.5006, 76.5006, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3300, 15.30012, 15.30012, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3300, 15.30012, 15.30012, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3300, 15.30012, 15.30012, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3300, 15.30012, 15.30012, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3300, 15.30012, 15.30012, 88);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3300, 21.42016, 21.42016, 88);
INSERT INTO building(id, name, level) VALUES (3301, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3301, 15.0, 3.2769882366309013, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3301, 15.0, 2.100543599733633, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3301, 120.0, 21.510127345458137, 3);
INSERT INTO building(id, name, level) VALUES (3302, "building_subsistence_farmslevel", 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3302, 115.69635, 115.69635, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3302, 23.13927, 23.13927, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3302, 23.13927, 23.13927, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3302, 23.13927, 23.13927, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3302, 23.13927, 23.13927, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3302, 23.13927, 23.13927, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3302, 32.39497, 32.39497, 53);
INSERT INTO building(id, name, level) VALUES (3303, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3303, 5.0, 1.0923294122103004, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3303, 5.0, 0.700181199911211, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3303, 40.0, 7.170042448486045, 1);
INSERT INTO building(id, name, level) VALUES (3304, "building_subsistence_farmslevel", 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3304, 174.7025, 174.7025, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3304, 34.9405, 34.9405, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3304, 34.9405, 34.9405, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3304, 34.9405, 34.9405, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3304, 34.9405, 34.9405, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3304, 34.9405, 34.9405, 70);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3304, 48.9167, 48.9167, 70);
INSERT INTO building(id, name, level) VALUES (3305, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3305, 10.0, 2.184658824420601, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3305, 10.0, 1.400362399822422, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3305, 80.0, 14.34008489697209, 2);
INSERT INTO building(id, name, level) VALUES (3306, "building_subsistence_farmslevel", 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3306, 222.47975, 222.47975, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3306, 44.49595, 44.49595, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3306, 44.49595, 44.49595, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3306, 44.49595, 44.49595, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3306, 44.49595, 44.49595, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3306, 44.49595, 44.49595, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3306, 62.29433, 62.29433, 107);
INSERT INTO building(id, name, level) VALUES (3307, "building_subsistence_farmslevel", 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3307, 133.0375, 146.34125, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3307, 26.607499999999995, 29.26825, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3307, 26.607499999999995, 29.26825, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3307, 26.607499999999995, 29.26825, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3307, 26.607499999999995, 29.26825, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3307, 26.607499999999995, 29.26825, 58);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3307, 37.250499999999995, 40.97555, 58);
INSERT INTO building(id, name, level) VALUES (3308, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3308, 10.0, 2.184658824420601, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3308, 10.0, 1.400362399822422, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3308, 80.0, 14.34008489697209, 2);
INSERT INTO building(id, name, level) VALUES (3309, "building_subsistence_farmslevel", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3309, 44.99999999999999, 49.5, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3309, 9.0, 9.9, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3309, 9.0, 9.9, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3309, 9.0, 9.9, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3309, 9.0, 9.9, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3309, 9.0, 9.9, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3309, 12.599999999999998, 13.86, 18);
INSERT INTO building(id, name, level) VALUES (3310, "building_urban_centerlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3310, 39.99999999999999, 8.738635297682402, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3310, 39.99999999999999, 5.601449599289687, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3310, 319.99999999999994, 57.36033958788835, 8);
INSERT INTO building(id, name, level) VALUES (3352, "building_subsistence_fishing_villageslevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3352, 2.81949, 2.81949, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 3352, 11.27796, 11.27796, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3352, 1.40974, 1.40974, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3352, 4.22923, 4.22923, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3352, 2.81949, 2.81949, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3352, 2.81949, 2.81949, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3352, 2.81949, 2.81949, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3352, 3.94728, 3.94728, 6);
INSERT INTO building(id, name, level) VALUES (3379, "building_subsistence_rice_paddieslevel", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3379, 18.262258333333335, 21.91471, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3379, 3.0437083333333335, 3.65245, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3379, 3.0437083333333335, 3.65245, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3379, 4.058275, 4.86993, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3379, 4.058275, 4.86993, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3379, 4.058275, 4.86993, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3379, 6.087416666666667, 7.3049, 18);
INSERT INTO building(id, name, level) VALUES (3579, "building_subsistence_farmslevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3579, 7.5, 7.5, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3579, 1.5, 1.5, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3579, 1.5, 1.5, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3579, 1.5, 1.5, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3579, 1.5, 1.5, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3579, 1.5, 1.5, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3579, 2.1, 2.1, 3);
INSERT INTO building(id, name, level) VALUES (3586, "building_subsistence_orchardslevel", 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3586, 2.88167, 2.88167, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3586, 1.44083, 1.44083, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3586, 4.3225, 4.3225, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3586, 2.88167, 2.88167, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3586, 2.88167, 2.88167, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3586, 2.88167, 2.88167, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3586, 7.66524, 7.66524, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3586, 4.03433, 4.03433, 17);
INSERT INTO building(id, name, level) VALUES (3941, "building_subsistence_pastureslevel", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3941, 3.81168, 3.81168, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3941, 5.71752, 5.71752, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3941, 1.90584, 1.90584, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3941, 3.81168, 3.81168, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3941, 3.81168, 3.81168, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3941, 3.81168, 3.81168, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3941, 10.13906, 10.13906, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3941, 5.33635, 5.33635, 9);
INSERT INTO building(id, name, level) VALUES (3962, "building_trade_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (3963, "building_trade_centerlevel", 36);
INSERT INTO building(id, name, level) VALUES (4003, "building_trade_centerlevel", 27);
INSERT INTO building(id, name, level) VALUES (4015, "building_trade_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4043, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4044, "building_conscription_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (4245, "building_conscription_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4246, "building_conscription_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (4247, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4248, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4249, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4250, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4251, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4252, "building_conscription_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4253, "building_conscription_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (4254, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4255, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4256, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4257, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4258, "building_conscription_centerlevel", 11);
INSERT INTO building(id, name, level) VALUES (4259, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4260, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4261, "building_conscription_centerlevel", 11);
