
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 73.534375, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 65.00415126990885, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 76.83605659291432, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 25.45446290995452, 4851.462248857292);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 19.837373908798618, 125.12851996420169);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 15.455831417947213, 283.01160990572595);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 25.882380646922417, 496.3949688127902);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 48.85249548258672, 366.49112707323343);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 40.783988733355166, 1754.5187570382643);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 37.40978048626003, 1086.3898655237108);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 37.3944123474744, 128.1675963758767);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 14.776736104047615, 804.9888406628133);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 18.648611413737314, 76.86338947239767);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 44.95940204367083, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 25.44506312261931, 101.62717835432252);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 44.57506444298794, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 38.266840470004425, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 59.72207134766754, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 78.8787815604904, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 48.73639215681785, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 58.230537865023074, 340.4234391425969);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 18.03778948593709, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 48.87437355759645, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 87.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 107.31991196209306, 370.7213818054273);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 35.89283719466227, 488.9947925185462);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 27.904331213339574, 17.881449016205764);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 32.97811920525915, 1921.5376191666667);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 81.44554425482528, 570.43139);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 51.430669185078166, 57.44515813491803);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 94.92951831475506, 86.24215078733594);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 89.80850696624142, 432.5082787729983);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 350.0, 0.34791770780113973);
INSERT INTO building(id, name, level) VALUES (418, "building_fishing_wharflevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 418, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (419, "building_rye_farmlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 419, 4.9288454545454545, 10.524413318865504, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 419, 0.9857636363636364, 0.694191452386219, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 419, 29.5731, 25.19948900650127, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 419, 14.786545454545454, 12.599740630030315, 1);
INSERT INTO building(id, name, level) VALUES (420, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 420, 25.333196428571426, 16.12139761376261, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 420, 25.333196428571426, 17.840066090496443, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 420, 101.33279464285712, 67.92293339330085, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 420, 12.666598214285713, 8.490365926064763, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 420, 63.33299999999999, 42.451835615106575, 3);
INSERT INTO building(id, name, level) VALUES (421, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 421, 9.999999999999998, 7.042169408348311, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 421, 79.99999999999999, 56.33735526678649, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 421, 19.999999999999996, 14.084338816696622, 2);
INSERT INTO building(id, name, level) VALUES (422, "building_naval_baselevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 422, 12.0, 0.0, 6);
INSERT INTO building(id, name, level) VALUES (423, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 423, 10.0, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (446, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 446, 20.0, 13.427889837972034, 2);
INSERT INTO building(id, name, level) VALUES (447, "building_chemical_plantslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 447, 122.784, 141.85839773923718, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 447, 40.928000000000004, 34.687663058996385, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 447, 368.35200000000003, 340.27048376548373, 5);
INSERT INTO building(id, name, level) VALUES (448, "building_steel_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 448, 53.03059459459459, 66.49473927859823, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 448, 60.60639639639639, 51.36567282589354, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 448, 7.575792792792791, 5.335001624939102, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 448, 98.4853963963964, 83.76990040483275, 2);
INSERT INTO building(id, name, level) VALUES (449, "building_iron_minelevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 449, 104.99999999999999, 131.65885990206272, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 449, 104.99999999999999, 73.94277878765728, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 449, 419.99999999999994, 357.8855575753145, 7);
INSERT INTO building(id, name, level) VALUES (450, "building_sulfur_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 450, 50.0, 62.69469519145844, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 450, 50.0, 35.21084704174156, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 450, 200.0, 170.42169408348312, 5);
INSERT INTO building(id, name, level) VALUES (451, "building_rye_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 451, 19.686592920353984, 42.03618122028624, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 451, 3.9373185840707965, 2.7727264483664658, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 451, 118.11959292035401, 100.65070564969065, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 451, 59.059796460177004, 50.325352824845325, 4);
INSERT INTO building(id, name, level) VALUES (452, "building_barrackslevel", 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 452, 16.0, 5.958666666666666, 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 452, 16.0, 14.222647256593175, 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 452, 16.0, 13.918437041793892, 16);
INSERT INTO building(id, name, level) VALUES (455, "building_furniture_manufacturieslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 455, 60.00000000000001, 86.07703426223732, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 455, 120.00000000000001, 72.94571017168781, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 455, 60.00000000000001, 20.557680076742972, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 455, 30.000000000000004, 21.126508225044944, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 455, 270.0, 179.19399556426134, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 455, 120.00000000000001, 79.64177580633839, 6);
INSERT INTO building(id, name, level) VALUES (456, "building_logging_camplevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 456, 30.000000000000004, 21.126508225044944, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 456, 240.00000000000003, 169.01206580035955, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 456, 60.00000000000001, 42.25301645008989, 6);
INSERT INTO building(id, name, level) VALUES (457, "building_rye_farmlevel", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 457, 54.153, 115.63124867932855, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 457, 10.8306, 7.627091999405724, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 457, 324.918, 276.86537999108583, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 457, 162.459, 138.43268999554292, 11);
INSERT INTO building(id, name, level) VALUES (458, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 458, 25.296, 16.097726758941636, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 458, 25.296, 17.813871735357893, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 458, 101.184, 67.82319698859905, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 458, 12.648, 8.477899623574881, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 458, 63.239999999999995, 42.3894981178744, 3);
INSERT INTO building(id, name, level) VALUES (459, "building_barrackslevel", 27);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 459, 27.0, 10.05525, 27);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 459, 27.0, 24.000717245500983, 27);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 459, 27.0, 23.48736250802719, 27);
INSERT INTO building(id, name, level) VALUES (462, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 462, 100.0, 67.13944918986017, 5);
INSERT INTO building(id, name, level) VALUES (463, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 463, 19.1476, 27.469477020660253, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 463, 38.2952, 23.27892133472349, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 463, 47.869, 40.570361194563574, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 463, 9.5738, 6.742032148164507, 2);
INSERT INTO building(id, name, level) VALUES (464, "building_tooling_workshopslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 464, 120.0, 72.9457101716878, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 464, 40.00000000000001, 50.15575615316676, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 464, 80.00000000000001, 82.79403596854479, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 464, 320.00000000000006, 278.17396459705583, 4);
INSERT INTO building(id, name, level) VALUES (465, "building_wheat_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 465, 18.762592920353985, 40.06319221174194, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 465, 3.7525132743362835, 2.6425834184951933, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 465, 131.3381946902655, 111.91438818490687, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 465, 45.0302389380531, 38.37064802403518, 4);
INSERT INTO building(id, name, level) VALUES (466, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 466, 10.0, 3.724166666666666, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 466, 10.0, 8.889154535370736, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 466, 10.0, 8.699023151121182, 10);
INSERT INTO building(id, name, level) VALUES (470, "building_arms_industrylevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 470, 158.66239316239316, 134.47096447789062, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 470, 79.33119658119658, 27.181089323690745, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 470, 118.99679487179488, 70.81242867197705, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 470, 118.99679487179488, 70.81242867197705, 8);
INSERT INTO building(id, name, level) VALUES (471, "building_munition_plantslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 471, 45.583794642857136, 48.38050255690564, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 471, 45.583794642857136, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 471, 113.95949999999999, 56.979749999999996, 3);
INSERT INTO building(id, name, level) VALUES (472, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 472, 89.99999999999999, 129.11555139335596, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 472, 30.0, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 472, 15.0, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 472, 115.79999999999998, 38.599999999999994, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 472, 57.89999999999999, 19.299999999999997, 3);
INSERT INTO building(id, name, level) VALUES (473, "building_coal_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 473, 44.99999999999999, 31.6897623375674, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 473, 179.99999999999997, 126.75904935026959, 3);
INSERT INTO building(id, name, level) VALUES (474, "building_wheat_farmlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 474, 39.40719658119659, 84.14498453706219, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 474, 7.881435897435898, 5.550240677078131, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 474, 275.8503931623932, 235.0544565816496, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 474, 63.05151282051282, 53.72672814699125, 8);
INSERT INTO building(id, name, level) VALUES (475, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 475, 9.999999999999998, 7.042169408348311, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 475, 79.99999999999999, 56.33735526678649, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 475, 19.999999999999996, 14.084338816696622, 2);
INSERT INTO building(id, name, level) VALUES (476, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 476, 25.0, 9.310416666666665, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 476, 25.0, 22.222886338426832, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 476, 25.0, 21.747557877802958, 25);
INSERT INTO building(id, name, level) VALUES (477, "building_government_administrationlevel", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 477, 259.99999999999994, 174.56256789363638, 13);
INSERT INTO building(id, name, level) VALUES (478, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 478, 28.7214, 41.20421553099038, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 478, 57.4428, 34.91838200208523, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 478, 71.8035, 60.85554179184536, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 478, 14.3607, 10.11304822224676, 3);
INSERT INTO building(id, name, level) VALUES (479, "building_universitylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 479, 40.0, 26.855779675944067, 4);
INSERT INTO building(id, name, level) VALUES (480, "building_arms_industrylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 480, 99.18, 84.05791688309378, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 480, 49.59, 16.990922583428063, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 480, 74.385, 44.26491076873122, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 480, 74.385, 44.26491076873122, 5);
INSERT INTO building(id, name, level) VALUES (481, "building_furniture_manufacturieslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 481, 50.00000000000001, 71.73086188519778, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 481, 100.00000000000001, 60.78809180973983, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 481, 50.00000000000001, 17.131400063952476, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 481, 25.000000000000004, 17.605423520870783, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 481, 225.00000000000003, 149.32832963688446, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 481, 100.00000000000001, 66.36814650528198, 5);
INSERT INTO building(id, name, level) VALUES (482, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 482, 60.0, 36.4728550858439, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 482, 44.99999999999999, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 482, 30.0, 31.84059352844163, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 482, 60.0, 32.15761836194797, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 482, 74.99999999999999, 40.197022952434956, 3);
INSERT INTO building(id, name, level) VALUES (483, "building_paper_millslevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 483, 210.0, 127.65499280045364, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 483, 70.00000000000001, 80.87444489303658, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 483, 35.00000000000001, 43.88628663402092, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 483, 35.00000000000001, 24.647592929219098, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 483, 490.0, 405.7319877191981, 7);
INSERT INTO building(id, name, level) VALUES (484, "building_barrackslevel", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 484, 25.0, 9.310416666666665, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 484, 25.0, 22.222886338426832, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 484, 25.0, 21.747557877802958, 25);
INSERT INTO building(id, name, level) VALUES (490, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 490, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (491, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 491, 1.0, 0.8889154535370735, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 491, 1.0, 0.8699023151121182, 1);
INSERT INTO building(id, name, level) VALUES (543, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 543, 30.0, 20.14183475695805, 3);
INSERT INTO building(id, name, level) VALUES (544, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 544, 10.0, 6.713944918986017, 1);
INSERT INTO building(id, name, level) VALUES (545, "building_glassworkslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 545, 100.00000000000001, 60.78809180973983, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 545, 75.0, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 545, 50.00000000000001, 53.06765588073606, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 545, 100.00000000000001, 53.596030603246625, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 545, 125.00000000000001, 66.99503825405827, 5);
INSERT INTO building(id, name, level) VALUES (546, "building_coal_minelevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 546, 104.99999999999999, 73.94277878765728, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 546, 419.99999999999994, 295.7711151506291, 7);
INSERT INTO building(id, name, level) VALUES (547, "building_sulfur_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 547, 20.0, 14.084338816696624, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 547, 80.0, 56.337355266786496, 4);
INSERT INTO building(id, name, level) VALUES (548, "building_rye_farmlevel", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 548, 180.0, 228.6, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 548, 270.0, 342.9, 18);
INSERT INTO building(id, name, level) VALUES (549, "building_livestock_ranchlevel", 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 549, 510.0, 642.6, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 549, 85.0, 107.1, 17);
INSERT INTO building(id, name, level) VALUES (550, "building_barrackslevel", 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 550, 28.0, 24.889632699038057, 28);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 550, 28.0, 24.357264823139314, 28);
INSERT INTO building(id, name, level) VALUES (563, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 563, 120.0, 172.15406852447464, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 563, 134.99999999999997, 134.99999999999997, 3);
INSERT INTO building(id, name, level) VALUES (100663861, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 100663861, 34.99229999999999, 37.139186697509594, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 100663861, 69.98459999999999, 69.98459999999999, 1);
INSERT INTO building(id, name, level) VALUES (575, "building_rye_farmlevel", 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 575, 130.0, 158.6, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 575, 195.0, 237.9, 13);
INSERT INTO building(id, name, level) VALUES (576, "building_livestock_ranchlevel", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 576, 360.00000000000006, 435.6, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 576, 60.0, 72.6, 12);
INSERT INTO building(id, name, level) VALUES (577, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 577, 15.0, 10.56325411252247, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 577, 179.99999999999997, 126.75904935026962, 3);
INSERT INTO building(id, name, level) VALUES (578, "building_barrackslevel", 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 578, 16.0, 14.222647256593175, 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 578, 16.0, 13.918437041793892, 16);
INSERT INTO building(id, name, level) VALUES (581, "building_rye_farmlevel", 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 581, 110.0, 132.0, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 581, 165.0, 198.0, 11);
INSERT INTO building(id, name, level) VALUES (582, "building_livestock_ranchlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 582, 135.51000000000002, 154.4814, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 582, 22.585, 25.7469, 5);
INSERT INTO building(id, name, level) VALUES (583, "building_barrackslevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 583, 6.0, 5.33349272122244, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 583, 6.0, 5.219413890672709, 6);
INSERT INTO building(id, name, level) VALUES (584, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 584, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (16777802, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (67109461, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 67109461, 10.0, 14.34617237703955, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 67109461, 29.999999999999996, 18.236427542921945, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 67109461, 5.0, 3.521084704174156, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 67109461, 65.0, 50.095453610198305, 1);
INSERT INTO building(id, name, level) VALUES (1036, "building_textile_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1036, 320.0, 459.0775160652656, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1036, 40.0, 0.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1036, 480.00000000000006, 240.00000000000003, 8);
INSERT INTO building(id, name, level) VALUES (1037, "building_arms_industrylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1037, 99.16300000000001, 84.04350889169417, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1037, 49.581500000000005, 16.988010245417193, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 1037, 74.3722456140351, 44.25732090845499, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 1037, 74.3722456140351, 44.25732090845499, 5);
INSERT INTO building(id, name, level) VALUES (1038, "building_coal_minelevel", 23);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 1038, 115.0, 232.09322558964055, 23);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1038, 345.0, 242.9548445880168, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1038, 1380.0, 1175.9096891760337, 23);
INSERT INTO building(id, name, level) VALUES (1039, "building_lead_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1039, 50.0, 62.69469519145844, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1039, 50.0, 35.21084704174156, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 1039, 200.0, 170.42169408348312, 5);
INSERT INTO building(id, name, level) VALUES (1040, "building_iron_minelevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1040, 120.0, 150.46726845950027, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1040, 120.0, 84.50603290017976, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1040, 480.0, 409.0120658003595, 8);
INSERT INTO building(id, name, level) VALUES (1041, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1041, 16.860594594594595, 10.729650726481346, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1041, 16.860594594594595, 11.873516346061699, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1041, 67.44239639639639, 45.20634622248553, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1041, 8.430297297297297, 5.650791768135761, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1041, 42.15149549549549, 28.253964879378522, 2);
INSERT INTO building(id, name, level) VALUES (1042, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1042, 15.0, 5.58625, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1042, 15.0, 13.333731803056102, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1042, 15.0, 13.048534726681773, 15);
INSERT INTO building(id, name, level) VALUES (1048, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1048, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1049, "building_rye_farmlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1049, 14.788946428571426, 31.57838609472071, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1049, 88.73369642857142, 88.73369642857142, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1049, 44.36684821428571, 44.36684821428571, 3);
INSERT INTO building(id, name, level) VALUES (1050, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1050, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (1051, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1051, 10.0, 3.724166666666666, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1051, 10.0, 8.889154535370736, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1051, 10.0, 8.699023151121182, 10);
INSERT INTO building(id, name, level) VALUES (1052, "building_textile_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1052, 79.99999999999999, 114.76937901631639, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1052, 9.999999999999998, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1052, 119.99999999999999, 59.99999999999999, 2);
INSERT INTO building(id, name, level) VALUES (1053, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1053, 25.000000000000004, 17.605423520870783, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1053, 200.00000000000003, 140.84338816696626, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1053, 50.00000000000001, 35.210847041741566, 5);
INSERT INTO building(id, name, level) VALUES (1054, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1054, 9.859, 21.05162189960852, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1054, 59.15399999999999, 59.15399999999999, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1054, 29.576999999999995, 29.576999999999995, 2);
INSERT INTO building(id, name, level) VALUES (1055, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1055, 10.0, 3.724166666666666, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1055, 10.0, 8.889154535370736, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1055, 10.0, 8.699023151121182, 10);
INSERT INTO building(id, name, level) VALUES (1056, "building_food_industrylevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1056, 240.00000000000003, 152.72985539792825, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1056, 150.0, 7.1288720994788735, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1056, 90.0, 30.775509516955204, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1056, 360.0, 123.10203806782081, 6);
INSERT INTO building(id, name, level) VALUES (1057, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1057, 9.859, 21.05162189960852, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1057, 59.15399999999999, 59.15399999999999, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1057, 19.718, 19.718, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1057, 9.859, 9.859, 2);
INSERT INTO building(id, name, level) VALUES (1058, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1058, 25.292098214285712, 16.09524376240857, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1058, 25.292098214285712, 17.811124031758382, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1058, 101.16839285714285, 67.8127355883339, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1058, 12.646044642857142, 8.476588956150357, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1058, 63.23025, 42.382962735100065, 3);
INSERT INTO building(id, name, level) VALUES (1059, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1059, 15.0, 5.58625, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1059, 15.0, 13.333731803056102, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1059, 15.0, 13.048534726681773, 15);
INSERT INTO building(id, name, level) VALUES (218106475, "building_logging_camplevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 218106475, 30.000000000000004, 21.126508225044944, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 218106475, 360.0, 253.51809870053927, 6);
INSERT INTO building(id, name, level) VALUES (2835, "building_subsistence_farmslevel", 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2835, 100.66934545454544, 110.73628, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2835, 20.133863636363635, 22.14725, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2835, 20.133863636363635, 22.14725, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2835, 20.133863636363635, 22.14725, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2835, 20.133863636363635, 22.14725, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2835, 20.133863636363635, 22.14725, 82);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2835, 28.18740909090909, 31.00615, 82);
INSERT INTO building(id, name, level) VALUES (16780076, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16780076, 15.0, 10.56325411252247, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 16780076, 60.0, 42.25301645008988, 1);
INSERT INTO building(id, name, level) VALUES (33557560, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 33557560, 0.5009, 0.30448755187498683, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 33557560, 1.0018, 1.2561509128560615, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 33557560, 0.5009, 0.19652477718794953, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 33557560, 7.0126, 4.67559086896037, 1);
INSERT INTO building(id, name, level) VALUES (16780573, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16780573, 10.0, 6.713944918986017, 1);
INSERT INTO building(id, name, level) VALUES (3773, "building_subsistence_farmslevel", 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3773, 200.44091818181815, 220.48501, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3773, 40.088181818181816, 44.097, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3773, 40.088181818181816, 44.097, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3773, 40.088181818181816, 44.097, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3773, 40.088181818181816, 44.097, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3773, 40.088181818181816, 44.097, 127);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3773, 56.123454545454535, 61.7358, 127);
INSERT INTO building(id, name, level) VALUES (3774, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3774, 5.0, 3.0394045904869915, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3774, 10.0, 12.53893903829169, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3774, 5.0, 1.9617166818521614, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3774, 70.0, 46.67189927091605, 1);
INSERT INTO building(id, name, level) VALUES (3775, "building_subsistence_farmslevel", 84);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3775, 122.073, 134.2803, 84);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3775, 24.414599999999997, 26.85606, 84);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3775, 24.414599999999997, 26.85606, 84);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3775, 24.414599999999997, 26.85606, 84);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3775, 24.414599999999997, 26.85606, 84);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3775, 24.414599999999997, 26.85606, 84);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3775, 34.18043636363636, 37.59848, 84);
INSERT INTO building(id, name, level) VALUES (3776, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3776, 25.0, 15.197022952434958, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3776, 50.0, 62.69469519145844, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3776, 25.0, 9.808583409260809, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3776, 350.0, 233.35949635458024, 5);
INSERT INTO building(id, name, level) VALUES (3779, "building_subsistence_farmslevel", 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3779, 144.8421, 159.32631, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3779, 28.96841818181818, 31.86526, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3779, 28.96841818181818, 31.86526, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3779, 28.96841818181818, 31.86526, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3779, 28.96841818181818, 31.86526, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3779, 28.96841818181818, 31.86526, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3779, 40.55578181818181, 44.61136, 78);
INSERT INTO building(id, name, level) VALUES (3780, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3780, 20.0, 12.157618361947966, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3780, 40.0, 50.15575615316676, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3780, 20.0, 7.846866727408646, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3780, 279.99999999999994, 186.68759708366417, 4);
INSERT INTO building(id, name, level) VALUES (3782, "building_subsistence_farmslevel", 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3782, 97.21237272727272, 106.93361, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3782, 19.442472727272726, 21.38672, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3782, 19.442472727272726, 21.38672, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3782, 19.442472727272726, 21.38672, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3782, 19.442472727272726, 21.38672, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3782, 19.442472727272726, 21.38672, 51);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3782, 27.219463636363635, 29.94141, 51);
INSERT INTO building(id, name, level) VALUES (3783, "building_subsistence_farmslevel", 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3783, 105.4067, 115.94737, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3783, 21.08133636363636, 23.18947, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3783, 21.08133636363636, 23.18947, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3783, 21.08133636363636, 23.18947, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3783, 21.08133636363636, 23.18947, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3783, 21.08133636363636, 23.18947, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3783, 29.513872727272727, 32.46526, 46);
INSERT INTO building(id, name, level) VALUES (3784, "building_subsistence_farmslevel", 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3784, 75.8916, 83.48076, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3784, 15.17831818181818, 16.69615, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3784, 15.17831818181818, 16.69615, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3784, 15.17831818181818, 16.69615, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3784, 15.17831818181818, 16.69615, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3784, 15.17831818181818, 16.69615, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3784, 21.249645454545455, 23.37461, 48);
INSERT INTO building(id, name, level) VALUES (3785, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3785, 10.0, 6.078809180973983, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3785, 20.0, 25.07787807658338, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3785, 10.0, 3.923433363704323, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3785, 140.0, 93.3437985418321, 2);
INSERT INTO building(id, name, level) VALUES (3787, "building_subsistence_farmslevel", 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3787, 189.7047, 208.67517, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3787, 37.94093636363636, 41.73503, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3787, 37.94093636363636, 41.73503, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3787, 37.94093636363636, 41.73503, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3787, 37.94093636363636, 41.73503, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3787, 37.94093636363636, 41.73503, 108);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3787, 53.11730909090909, 58.42904, 108);
INSERT INTO building(id, name, level) VALUES (3788, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3788, 25.0, 15.197022952434958, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3788, 50.0, 62.69469519145844, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3788, 25.0, 9.808583409260809, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3788, 350.0, 233.35949635458024, 5);
INSERT INTO building(id, name, level) VALUES (3789, "building_subsistence_farmslevel", 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3789, 214.0736, 235.48096, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3789, 42.81471818181818, 47.09619, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3789, 42.81471818181818, 47.09619, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3789, 42.81471818181818, 47.09619, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3789, 42.81471818181818, 47.09619, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3789, 42.81471818181818, 47.09619, 124);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3789, 59.94059999999999, 65.93466, 124);
INSERT INTO building(id, name, level) VALUES (3790, "building_urban_centerlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3790, 35.0, 21.27583213340894, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3790, 70.0, 87.77257326804184, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3790, 35.0, 13.732016772965132, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3790, 489.99999999999994, 326.70329489641233, 7);
INSERT INTO building(id, name, level) VALUES (3791, "building_subsistence_farmslevel", 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3791, 125.1144, 137.62584, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3791, 25.022872727272723, 27.52516, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3791, 25.022872727272723, 27.52516, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3791, 25.022872727272723, 27.52516, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3791, 25.022872727272723, 27.52516, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3791, 25.022872727272723, 27.52516, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3791, 35.03202727272727, 38.53523, 72);
INSERT INTO building(id, name, level) VALUES (3792, "building_urban_centerlevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3792, 45.0, 27.354641314382924, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3792, 90.0, 112.85045134462521, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3792, 45.0, 17.655450136669455, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3792, 629.9999999999999, 420.04709343824436, 9);
INSERT INTO building(id, name, level) VALUES (3794, "building_subsistence_farmslevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3794, 9.6215, 10.58365, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3794, 1.9243, 2.11673, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3794, 1.9243, 2.11673, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3794, 1.9243, 2.11673, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3794, 1.9243, 2.11673, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3794, 1.9243, 2.11673, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3794, 2.6940181818181816, 2.96342, 5);
INSERT INTO building(id, name, level) VALUES (3808, "building_subsistence_farmslevel", 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3808, 152.055, 167.2605, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3808, 30.410999999999998, 33.4521, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3808, 30.410999999999998, 33.4521, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3808, 30.410999999999998, 33.4521, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3808, 30.410999999999998, 33.4521, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3808, 30.410999999999998, 33.4521, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3808, 42.575399999999995, 46.83294, 93);
INSERT INTO building(id, name, level) VALUES (3809, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3809, 30.0, 18.23642754292195, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3809, 60.0, 75.23363422975014, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3809, 330.0, 265.3003514860707, 6);
INSERT INTO building(id, name, level) VALUES (3816, "building_subsistence_farmslevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3816, 15.351999999999999, 16.8872, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3816, 3.0704, 3.37744, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3816, 3.0704, 3.37744, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3816, 3.0704, 3.37744, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3816, 3.0704, 3.37744, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3816, 3.0704, 3.37744, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3816, 4.298554545454546, 4.72841, 10);
INSERT INTO building(id, name, level) VALUES (3820, "building_subsistence_farmslevel", 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3820, 38.438745454545455, 42.28262, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3820, 7.687745454545453, 8.45652, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3820, 7.687745454545453, 8.45652, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3820, 7.687745454545453, 8.45652, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3820, 7.687745454545453, 8.45652, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3820, 7.687745454545453, 8.45652, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3820, 10.762845454545454, 11.83913, 46);
INSERT INTO building(id, name, level) VALUES (3837, "building_subsistence_farmslevel", 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3837, 0.0055, 0.00605, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3837, 0.0010999999999999998, 0.00121, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3837, 0.0010999999999999998, 0.00121, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3837, 0.0010999999999999998, 0.00121, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3837, 0.0010999999999999998, 0.00121, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3837, 0.0010999999999999998, 0.00121, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3837, 0.0015363636363636363, 0.00169, 22);
INSERT INTO building(id, name, level) VALUES (33558270, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 33558270, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3868, "building_trade_centerlevel", 37);
INSERT INTO building(id, name, level) VALUES (3869, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3869, 10.0, 6.078809180973983, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3869, 20.0, 25.07787807658338, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3869, 10.0, 3.923433363704323, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3869, 140.0, 93.3437985418321, 2);
INSERT INTO building(id, name, level) VALUES (3925, "building_trade_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (3950, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4507, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4508, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4509, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4510, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4511, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4512, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4514, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4515, "building_conscription_centerlevel", 11);
INSERT INTO building(id, name, level) VALUES (4516, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4524, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4527, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4547, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4547, 19.1476, 27.469477020660253, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4547, 38.2952, 23.27892133472349, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4547, 47.869, 40.570361194563574, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4547, 9.5738, 6.742032148164507, 2);
INSERT INTO building(id, name, level) VALUES (4548, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4548, 59.99999999999999, 36.47285508584389, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 4548, 19.999999999999996, 25.077878076583374, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 4548, 39.99999999999999, 41.397017984272374, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4548, 159.99999999999997, 139.0869822985279, 2);
INSERT INTO building(id, name, level) VALUES (4602, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4602, 59.99999999999999, 36.47285508584389, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 4602, 19.999999999999996, 25.077878076583374, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 4602, 39.99999999999999, 41.397017984272374, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4602, 159.99999999999997, 139.0869822985279, 2);
INSERT INTO building(id, name, level) VALUES (4603, "building_tooling_workshopslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4603, 59.99999999999999, 36.47285508584389, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 4603, 19.999999999999996, 25.077878076583374, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 4603, 39.99999999999999, 41.397017984272374, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 4603, 159.99999999999997, 139.0869822985279, 2);
INSERT INTO building(id, name, level) VALUES (4614, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4614, 9.907199999999998, 8.396638376126099, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 4614, 9.907199999999998, 3.394484134271798, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 4614, 24.768, 14.738903137997376, 1);
INSERT INTO building(id, name, level) VALUES (4680, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4680, 5.0, 3.521084704174156, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 4680, 59.99999999999999, 42.253016450089866, 1);
INSERT INTO building(id, name, level) VALUES (4726, "building_sulfur_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4726, 5.0, 3.521084704174156, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 4726, 20.0, 14.084338816696624, 1);
INSERT INTO building(id, name, level) VALUES (4734, "building_arms_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4734, 9.918399999999998, 8.406130699871719, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 4734, 9.918399999999998, 3.398321567886124, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 4734, 14.877599999999997, 15.397206869070267, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 4734, 9.918399999999998, 7.240950755919281, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 4734, 24.796, 18.102376889798204, 1);
INSERT INTO building(id, name, level) VALUES (4878, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 4878, 30.0, 37.61681711487507, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4878, 30.0, 21.12650822504494, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 4878, 120.0, 102.25301645008987, 2);
INSERT INTO building(id, name, level) VALUES (4887, "building_rye_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 4887, 9.863, 21.060162977567586, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 4887, 98.62999999999998, 98.62999999999998, 2);
INSERT INTO building(id, name, level) VALUES (16782117, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 16782117, 60.0, 40.2836695139161, 3);
INSERT INTO building(id, name, level) VALUES (16782218, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782218, 30.0, 21.12650822504494, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 16782218, 120.0, 84.50603290017976, 2);
INSERT INTO building(id, name, level) VALUES (5007, "building_paper_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5007, 29.999999999999996, 18.236427542921945, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 5007, 10.0, 11.553492127576652, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 5007, 70.0, 56.27583213340894, 1);
INSERT INTO building(id, name, level) VALUES (50336676, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 50336676, 120.0, 172.15406852447464, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 50336676, 15.0, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 50336676, 179.99999999999997, 89.99999999999999, 3);
INSERT INTO building(id, name, level) VALUES (33559493, "building_food_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 33559493, 40.0, 25.454975899654706, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 33559493, 44.99999999999999, 28.636847887111536, 1);
INSERT INTO building(id, name, level) VALUES (16782377, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16782377, 5.0, 3.521084704174156, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 16782377, 59.99999999999999, 42.253016450089866, 1);
INSERT INTO building(id, name, level) VALUES (419435599, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 419435599, 89.99999999999999, 54.70928262876584, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 419435599, 89.99999999999999, 54.70928262876584, 3);
INSERT INTO building(id, name, level) VALUES (50336860, "building_tooling_workshopslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 50336860, 89.99999999999999, 54.70928262876584, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 50336860, 30.0, 37.61681711487507, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 50336860, 60.0, 62.095526976408586, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 50336860, 240.0, 208.63047344779184, 3);
INSERT INTO building(id, name, level) VALUES (67114111, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 67114111, 5.0, 3.521084704174156, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 67114111, 24.999999999999996, 17.60542352087078, 1);
INSERT INTO building(id, name, level) VALUES (16782763, "building_railwaylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782763, 20.0, 25.07787807658338, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 16782763, 20.0, 11.609509996434525, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 16782763, 100.0, 79.02377499108631, 2);
INSERT INTO building(id, name, level) VALUES (33559997, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 33559997, 29.999999999999996, 37.61681711487506, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 33559997, 40.0, 33.90115623435925, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 33559997, 65.0, 60.04468944041689, 1);
INSERT INTO building(id, name, level) VALUES (234886623, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 234886623, 9.999999999999998, 7.042169408348311, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 234886623, 119.99999999999999, 84.50603290017973, 2);
INSERT INTO building(id, name, level) VALUES (16782847, "building_steel_millslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782847, 29.999999999999996, 37.61681711487506, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16782847, 40.0, 33.90115623435925, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 16782847, 65.0, 60.04468944041689, 1);
INSERT INTO building(id, name, level) VALUES (16782951, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782951, 20.0, 12.157618361947966, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16782951, 40.0, 50.15575615316676, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 16782951, 20.0, 7.846866727408646, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16782951, 279.99999999999994, 186.68759708366417, 4);
INSERT INTO building(id, name, level) VALUES (5771, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5771, 5.0, 3.0394045904869915, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5771, 10.0, 12.53893903829169, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5771, 55.0, 44.216725247678454, 1);
INSERT INTO building(id, name, level) VALUES (16783004, "building_steel_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16783004, 92.98694642857143, 116.59576526247523, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783004, 106.27079464285714, 90.06757030842522, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783004, 13.283848214285713, 9.354710951978522, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 16783004, 172.69004464285712, 146.88703472759326, 3);
INSERT INTO building(id, name, level) VALUES (117446307, "building_motor_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 117446307, 2.341642857142857, 2.9361717035165458, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 117446307, 14.049892857142856, 14.540591682106074, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 117446307, 2.341642857142857, 1.6490245693848766, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 117446307, 18.733196428571425, 16.886209046043135, 3);
INSERT INTO building(id, name, level) VALUES (16783019, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783019, 9.5738, 13.734738510330127, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783019, 19.1476, 11.639460667361744, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783019, 23.9345, 20.285180597281787, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783019, 4.7869, 3.3710160740822537, 1);
INSERT INTO building(id, name, level) VALUES (83891885, "building_railwaylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 83891885, 20.0, 25.07787807658338, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 83891885, 20.0, 11.609509996434525, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 83891885, 100.0, 79.02377499108631, 2);
INSERT INTO building(id, name, level) VALUES (16783031, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16783031, 19.1476, 27.469477020660253, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16783031, 38.2952, 23.27892133472349, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16783031, 47.869, 40.570361194563574, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16783031, 9.5738, 6.742032148164507, 2);
INSERT INTO building(id, name, level) VALUES (117446409, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 117446409, 9.5738, 13.734738510330127, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 117446409, 19.1476, 11.639460667361744, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 117446409, 23.9345, 20.285180597281787, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 117446409, 4.7869, 3.3710160740822537, 1);
INSERT INTO building(id, name, level) VALUES (5923, "building_tooling_workshopslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5923, 29.999999999999996, 18.236427542921945, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 5923, 20.0, 20.698508992136194, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 5923, 80.0, 64.31523672389594, 1);
INSERT INTO building(id, name, level) VALUES (16783243, "building_motor_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16783243, 29.999999999999996, 31.047763488204286, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 16783243, 40.0, 40.0, 1);
INSERT INTO building(id, name, level) VALUES (6042, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 6042, 10.0, 6.713944918986017, 1);
INSERT INTO building(id, name, level) VALUES (16783290, "building_railwaylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 16783290, 10.0, 12.53893903829169, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 16783290, 10.0, 5.804754998217263, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 16783290, 50.0, 39.51188749554316, 1);
INSERT INTO building(id, name, level) VALUES (6104, "building_chemical_plantslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 6104, 5.686196428571428, 6.569542567335446, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 6104, 1.895392857142857, 1.6064002343872144, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 6104, 17.058598214285713, 15.758126647163628, 3);
INSERT INTO building(id, name, level) VALUES (83892200, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 83892200, 9.5738, 13.734738510330127, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 83892200, 19.1476, 11.639460667361744, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 83892200, 23.9345, 20.285180597281787, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 83892200, 4.7869, 3.3710160740822537, 1);
INSERT INTO building(id, name, level) VALUES (6148, "building_railwaylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6148, 30.0, 37.61681711487507, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 6148, 30.0, 17.414264994651788, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 6148, 150.0, 118.53566248662948, 3);
INSERT INTO building(id, name, level) VALUES (33560590, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 33560590, 20.0, 13.427889837972034, 1);
INSERT INTO building(id, name, level) VALUES (16783464, "building_trade_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (6355, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 6355, 9.5738, 13.734738510330127, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6355, 19.1476, 11.639460667361744, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 6355, 23.9345, 20.285180597281787, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 6355, 4.7869, 3.3710160740822537, 1);
INSERT INTO building(id, name, level) VALUES (6356, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 6356, 9.5738, 13.734738510330127, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6356, 19.1476, 11.639460667361744, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 6356, 23.9345, 20.285180597281787, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 6356, 4.7869, 3.3710160740822537, 1);
INSERT INTO building(id, name, level) VALUES (6357, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 6357, 9.5738, 13.734738510330127, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6357, 19.1476, 11.639460667361744, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 6357, 23.9345, 20.285180597281787, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 6357, 4.7869, 3.3710160740822537, 1);
INSERT INTO building(id, name, level) VALUES (6358, "building_railwaylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6358, 18.0, 22.570090268925043, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 6358, 18.0, 10.448558996791073, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 6358, 90.0, 71.12139749197769, 3);
INSERT INTO building(id, name, level) VALUES (6362, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6362, 0.5, 0.30394045904869915, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 6362, 1.0, 1.253893903829169, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 6362, 0.5, 0.19617166818521617, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6362, 7.0, 4.667189927091605, 1);
