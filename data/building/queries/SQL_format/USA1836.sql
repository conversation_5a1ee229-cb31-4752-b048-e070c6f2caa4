
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 41.13776897761664, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 30.529131943659454, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 65.8128549358294, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 90.01573774908815, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 25.52604378387135, 3363.1898951853245);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 25.155395397694107, 807.7959505390465);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 10.997215258122841, 678.6506425253444);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 30.440030236751078, 229.11245691655648);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 38.935648852609894, 436.1388591307838);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 40.89592155504187, 915.7059024895574);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 40.43023519364419, 741.531855376858);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 48.66690929363021, 37.845948757830506);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 20.081486480914332, 495.15475333333353);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 75.30010308488059, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 81.96115022177779, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 47.83139717224441, 53.191129248597534);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 65.42733073997798, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 59.66081374731854, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 59.9087802304076, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 24.823583607739742, 6.611188342601075);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 73.1954745533655, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 60.16242509917664, 169.0505612510775);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 40.69949855468008, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 61.93426523242095, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 80.6012410894725, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 116.1551144965902, 143.7430331914099);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 38.06711064964166, 296.73478658621315);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 25.594777788994477, 154.18935576663924);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 30.210091044969115, 698.4282942699613);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 78.10975204726986, 273.6939168902939);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 87.5, 69.67334810970584);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 33.23635283002238, 70.37133950257858);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 33.93761315136362, 815.3917042975294);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 209.43217337084278);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 93.00597851174659, 259.6078879058453);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (1430, "building_government_administrationlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1430, 55.90799711815562, 10.190445356548492, 6);
INSERT INTO building(id, name, level) VALUES (1431, "building_maize_farmlevel", 1);
INSERT INTO building(id, name, level) VALUES (1432, "building_white_houselevel", 1);
INSERT INTO building(id, name, level) VALUES (1433, "building_capitol_hilllevel", 1);
INSERT INTO building(id, name, level) VALUES (1434, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1434, 10.0, 6.68986850704834, 2);
INSERT INTO building(id, name, level) VALUES (1435, "building_sugar_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1435, 89.64, 91.4328, 3);
INSERT INTO building(id, name, level) VALUES (1436, "building_cotton_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1436, 79.67999999999999, 100.3968, 2);
INSERT INTO building(id, name, level) VALUES (1437, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1437, 99.99999999999999, 18.227169424460094, 10);
INSERT INTO building(id, name, level) VALUES (1438, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1438, 33.399, 83.80797723084981, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1438, 66.798, 21.375053961606497, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1438, 83.4975, 13.843860288226878, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1438, 16.6995, 4.86830715344446, 3);
INSERT INTO building(id, name, level) VALUES (1439, "building_arms_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1439, 60.0, 9.947981883213423, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1439, 30.0, 10.963796237866273, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 1439, 45.0, 11.953340384604736, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 1439, 45.0, 11.953340384604736, 3);
INSERT INTO building(id, name, level) VALUES (1440, "building_munition_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1440, 20.0, 7.49773867881194, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 1440, 20.0, 4.025505337086399, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 1440, 50.0, 14.404055019872924, 1);
INSERT INTO building(id, name, level) VALUES (1441, "building_universitylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1441, 15.0, 2.7340754136690144, 3);
INSERT INTO building(id, name, level) VALUES (1442, "building_iron_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1442, 30.0, 6.763055578708776, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1442, 30.0, 8.745723800313412, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1442, 120.0, 31.017558758044373, 3);
INSERT INTO building(id, name, level) VALUES (1443, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1443, 60.0, 19.19972510698509, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1443, 20.0, 3.2133165766336886, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1443, 140.0, 33.64628730970051, 2);
INSERT INTO building(id, name, level) VALUES (1444, "building_textile_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1444, 200.0, 501.85920075960246, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1444, 25.0, 0.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1444, 300.0, 150.0, 5);
INSERT INTO building(id, name, level) VALUES (1445, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1445, 20.0, 50.185920075960254, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1445, 40.0, 12.79981673799006, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1445, 20.0, 7.309197491910849, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1445, 10.0, 2.915241266771137, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1445, 90.0, 44.48203694375417, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1445, 40.0, 19.769794197224076, 2);
INSERT INTO building(id, name, level) VALUES (1446, "building_tooling_workshopslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1446, 150.0, 47.99931276746272, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 1446, 100.0, 41.68406055752912, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1446, 400.0, 147.36720480500853, 5);
INSERT INTO building(id, name, level) VALUES (1447, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1447, 10.0, 2.915241266771137, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1447, 80.0, 23.321930134169094, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1447, 25.0, 7.288103166927843, 2);
INSERT INTO building(id, name, level) VALUES (1448, "building_wheat_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1448, 19.961398058252428, 11.235373796556342, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1448, 139.72979611650487, 78.64762204050659, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1448, 31.93823300970874, 17.976595888645274, 4);
INSERT INTO building(id, name, level) VALUES (1449, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1449, 10.0, 6.68986850704834, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1449, 100.0, 66.8986850704834, 2);
INSERT INTO building(id, name, level) VALUES (1450, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1450, 20.0, 12.543503450715637, 10);
INSERT INTO building(id, name, level) VALUES (1451, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1451, 1.0, 1.440519697950409, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1451, 1.0, 3.1640594828182147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1451, 1.0, 1.1865223060568306, 1);
INSERT INTO building(id, name, level) VALUES (1452, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1452, 15.0, 10.03480276057251, 3);
INSERT INTO building(id, name, level) VALUES (1453, "building_central_parklevel", 1);
INSERT INTO building(id, name, level) VALUES (1454, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1454, 20.0, 3.6454338848920194, 2);
INSERT INTO building(id, name, level) VALUES (1455, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1455, 11.133, 27.93599241028327, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1455, 22.266, 7.125017987202165, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1455, 27.8325, 4.614620096075626, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1455, 5.5665, 1.6227690511481532, 1);
INSERT INTO building(id, name, level) VALUES (1456, "building_arms_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1456, 30.0, 4.973990941606711, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1456, 30.0, 10.963796237866273, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 1456, 75.0, 19.92223397434123, 3);
INSERT INTO building(id, name, level) VALUES (1457, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1457, 20.0, 4.508703719139183, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1457, 20.0, 5.830482533542274, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1457, 80.0, 20.678372505362912, 2);
INSERT INTO building(id, name, level) VALUES (1458, "building_maize_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1458, 149.7, 155.688, 5);
INSERT INTO building(id, name, level) VALUES (1459, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1459, 19.96059405940594, 12.877497502632268, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1459, 19.96059405940594, 5.818994751124699, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1459, 79.84239603960397, 37.39299378147564, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1459, 9.98029702970297, 4.674123063439242, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1459, 49.901495049504945, 23.370619954177055, 2);
INSERT INTO building(id, name, level) VALUES (1460, "building_tobacco_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1460, 99.6, 102.588, 4);
INSERT INTO building(id, name, level) VALUES (1461, "building_cotton_plantationlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1461, 278.88, 365.3328, 7);
INSERT INTO building(id, name, level) VALUES (1462, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1462, 1.0, 1.440519697950409, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1462, 1.0, 3.1640594828182147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1462, 1.0, 1.1865223060568306, 1);
INSERT INTO building(id, name, level) VALUES (1463, "building_shipyardslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1463, 40.0, 100.37184015192051, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1463, 80.0, 25.59963347598012, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1463, 30.0, 19.799931276746275, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1463, 40.0, 26.399908368995032, 2);
INSERT INTO building(id, name, level) VALUES (1464, "building_naval_baselevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1464, 30.0, 18.815255176073457, 15);
INSERT INTO building(id, name, level) VALUES (1465, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1465, 15.0, 10.03480276057251, 3);
INSERT INTO building(id, name, level) VALUES (1469, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1469, 39.839999999999996, 49.8, 1);
INSERT INTO building(id, name, level) VALUES (1470, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1470, 9.9803, 6.438750667591395, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1470, 9.9803, 2.909498241475598, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1470, 39.9212, 18.696497818133984, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1470, 4.99015, 2.337062227266748, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1470, 24.95075, 11.685311136333741, 1);
INSERT INTO building(id, name, level) VALUES (1471, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1471, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1472, "building_lead_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1472, 10.0, 2.2543518595695917, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1472, 10.0, 2.915241266771137, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 1472, 40.0, 10.339186252681456, 1);
INSERT INTO building(id, name, level) VALUES (1473, "building_maize_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1473, 59.88, 60.4788, 2);
INSERT INTO building(id, name, level) VALUES (1474, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1474, 9.9803, 6.438750667591395, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1474, 9.9803, 2.909498241475598, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1474, 39.9212, 18.696497818133984, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1474, 4.99015, 2.337062227266748, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1474, 24.95075, 11.685311136333741, 1);
INSERT INTO building(id, name, level) VALUES (1475, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1475, 20.0, 3.6454338848920194, 2);
INSERT INTO building(id, name, level) VALUES (1476, "building_chemical_plantslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1476, 60.0, 9.639949729901065, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1476, 20.0, 3.3159939610711406, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1476, 180.0, 29.38189741967173, 2);
INSERT INTO building(id, name, level) VALUES (1477, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1477, 10.0, 2.915241266771137, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1477, 120.0, 34.98289520125364, 2);
INSERT INTO building(id, name, level) VALUES (1478, "building_maize_farmlevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1478, 39.919999999999995, 22.46917378480431, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1478, 399.2, 224.6917378480431, 8);
INSERT INTO building(id, name, level) VALUES (1479, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1479, 5.0, 1.4576206333855686, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1479, 60.0, 17.49144760062682, 1);
INSERT INTO building(id, name, level) VALUES (1480, "building_sulfur_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1480, 20.0, 4.508703719139183, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1480, 20.0, 5.830482533542274, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 1480, 80.0, 20.678372505362912, 2);
INSERT INTO building(id, name, level) VALUES (1481, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1481, 29.94, 29.94, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1481, 4.99, 4.99, 1);
INSERT INTO building(id, name, level) VALUES (1482, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1482, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1483, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1483, 11.133, 27.93599241028327, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1483, 22.266, 7.125017987202165, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1483, 27.8325, 4.614620096075626, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1483, 5.5665, 1.6227690511481532, 1);
INSERT INTO building(id, name, level) VALUES (1484, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1484, 60.0, 19.19972510698509, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1484, 30.0, 11.24660801821791, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1484, 120.0, 41.69294114342091, 3);
INSERT INTO building(id, name, level) VALUES (1485, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1485, 20.0, 50.185920075960254, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1485, 60.0, 19.19972510698509, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1485, 10.0, 2.915241266771137, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1485, 130.0, 69.83251362216417, 2);
INSERT INTO building(id, name, level) VALUES (1486, "building_chemical_plantslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1486, 30.0, 4.819974864950533, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1486, 15.0, 3.381527789354388, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1486, 10.0, 1.6579969805355703, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1486, 75.0, 13.797517821055017, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 1486, 20.0, 3.6793380856146713, 1);
INSERT INTO building(id, name, level) VALUES (1487, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1487, 20.0, 5.830482533542274, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1487, 80.0, 23.321930134169094, 2);
INSERT INTO building(id, name, level) VALUES (1488, "building_maize_farmlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1488, 29.941495238095236, 16.852721940923164, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1488, 227.5554, 128.08070712100852, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1488, 35.9298, 20.223269545422397, 6);
INSERT INTO building(id, name, level) VALUES (1489, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1489, 19.96059405940594, 12.877497502632268, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1489, 19.96059405940594, 5.818994751124699, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1489, 79.84239603960397, 37.39299378147564, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1489, 9.98029702970297, 4.674123063439242, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1489, 49.901495049504945, 23.370619954177055, 2);
INSERT INTO building(id, name, level) VALUES (1490, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1490, 1.0, 1.440519697950409, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1490, 1.0, 3.1640594828182147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1490, 1.0, 1.1865223060568306, 1);
INSERT INTO building(id, name, level) VALUES (1491, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1491, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1492, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1492, 5.0, 0.9113584712230048, 1);
INSERT INTO building(id, name, level) VALUES (1493, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1493, 160.0, 103.22336070204535, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1493, 160.0, 137.1704174525018, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1493, 139.99999999999997, 105.17227794261434, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1493, 239.99999999999997, 180.29533361591032, 4);
INSERT INTO building(id, name, level) VALUES (1494, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1494, 19.999999999999996, 5.8304825335422725, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1494, 79.99999999999999, 23.32193013416909, 2);
INSERT INTO building(id, name, level) VALUES (1495, "building_tobacco_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1495, 199.2, 213.144, 8);
INSERT INTO building(id, name, level) VALUES (1496, "building_maize_farmlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1496, 24.95125, 14.043937184321107, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1496, 189.62949999999998, 106.73392260084039, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1496, 29.941499999999998, 16.852724621185324, 5);
INSERT INTO building(id, name, level) VALUES (1497, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1497, 9.9803, 6.438750667591395, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1497, 9.9803, 2.909498241475598, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1497, 39.9212, 18.696497818133984, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1497, 4.99015, 2.337062227266748, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1497, 24.95075, 11.685311136333741, 1);
INSERT INTO building(id, name, level) VALUES (1498, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1498, 1.0, 1.440519697950409, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1498, 1.0, 3.1640594828182147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1498, 1.0, 1.1865223060568306, 1);
INSERT INTO building(id, name, level) VALUES (1499, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1499, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1500, "building_food_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1500, 120.0, 77.41752052653402, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1500, 120.0, 102.87781308937633, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1500, 104.99999999999999, 78.87920845696077, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1500, 180.0, 135.22150021193278, 3);
INSERT INTO building(id, name, level) VALUES (1501, "building_maize_farmlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1501, 99.79999999999998, 108.782, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1501, 89.82, 97.9038, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1501, 59.879999999999995, 65.2692, 10);
INSERT INTO building(id, name, level) VALUES (1502, "building_livestock_ranchlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1502, 19.96059405940594, 12.877497502632268, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1502, 19.96059405940594, 5.818994751124699, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1502, 79.84239603960397, 37.39299378147564, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1502, 9.98029702970297, 4.674123063439242, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1502, 49.901495049504945, 23.370619954177055, 2);
INSERT INTO building(id, name, level) VALUES (1503, "building_tobacco_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1503, 249.0, 271.41, 10);
INSERT INTO building(id, name, level) VALUES (1504, "building_cotton_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1504, 119.52, 151.7904, 3);
INSERT INTO building(id, name, level) VALUES (1505, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1505, 1.0, 1.440519697950409, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1505, 1.0, 3.1640594828182147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1505, 1.0, 1.1865223060568306, 1);
INSERT INTO building(id, name, level) VALUES (1506, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1506, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1507, "building_cotton_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1507, 159.35999999999999, 203.9808, 4);
INSERT INTO building(id, name, level) VALUES (1508, "building_maize_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1508, 119.76, 123.3528, 4);
INSERT INTO building(id, name, level) VALUES (1509, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1509, 49.800000000000004, 50.298, 2);
INSERT INTO building(id, name, level) VALUES (1510, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1510, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1511, "building_maize_farmlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1511, 59.88, 62.874, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1511, 53.891999999999996, 56.5866, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1511, 35.928000000000004, 37.7244, 6);
INSERT INTO building(id, name, level) VALUES (1512, "building_sugar_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1512, 59.76, 60.3576, 2);
INSERT INTO building(id, name, level) VALUES (1513, "building_cotton_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1513, 398.4, 533.856, 10);
INSERT INTO building(id, name, level) VALUES (1514, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1514, 74.7, 76.194, 3);
INSERT INTO building(id, name, level) VALUES (1515, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1515, 1.0, 1.440519697950409, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1515, 1.0, 3.1640594828182147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1515, 1.0, 1.1865223060568306, 1);
INSERT INTO building(id, name, level) VALUES (1516, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1516, 5.0, 3.34493425352417, 1);
INSERT INTO building(id, name, level) VALUES (1517, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1517, 20.0, 3.6454338848920194, 2);
INSERT INTO building(id, name, level) VALUES (1518, "building_maize_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1518, 149.7, 155.688, 5);
INSERT INTO building(id, name, level) VALUES (1519, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1519, 9.9803, 6.438750667591395, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1519, 9.9803, 2.909498241475598, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1519, 39.9212, 18.696497818133984, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1519, 4.99015, 2.337062227266748, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1519, 24.95075, 11.685311136333741, 1);
INSERT INTO building(id, name, level) VALUES (1520, "building_sugar_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1520, 119.52, 123.1056, 4);
INSERT INTO building(id, name, level) VALUES (1521, "building_cotton_plantationlevel", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1521, 318.71999999999997, 420.7104, 8);
INSERT INTO building(id, name, level) VALUES (1522, "building_tobacco_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1522, 74.7, 76.194, 3);
INSERT INTO building(id, name, level) VALUES (1523, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1523, 1.0, 1.440519697950409, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1523, 1.0, 3.1640594828182147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1523, 1.0, 1.1865223060568306, 1);
INSERT INTO building(id, name, level) VALUES (1524, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1524, 5.0, 3.34493425352417, 1);
INSERT INTO building(id, name, level) VALUES (1525, "building_coal_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1525, 10.0, 2.915241266771137, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1525, 40.0, 11.660965067084547, 1);
INSERT INTO building(id, name, level) VALUES (1526, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1526, 5.0, 1.4576206333855686, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1526, 40.0, 11.660965067084547, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1526, 12.5, 3.6440515834639213, 1);
INSERT INTO building(id, name, level) VALUES (1527, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1527, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1528, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1528, 20.0, 6.39990836899503, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1528, 15.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1528, 10.0, 3.74886933940597, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1528, 20.0, 4.632549015935656, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1528, 25.0, 5.79068626991957, 1);
INSERT INTO building(id, name, level) VALUES (1529, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1529, 15.0, 10.03480276057251, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1529, 150.0, 100.34802760572512, 3);
INSERT INTO building(id, name, level) VALUES (1530, "building_tobacco_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1530, 99.6, 102.588, 4);
INSERT INTO building(id, name, level) VALUES (1531, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1531, 10.0, 6.271751725357818, 5);
INSERT INTO building(id, name, level) VALUES (1532, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1532, 10.0, 6.68986850704834, 2);
INSERT INTO building(id, name, level) VALUES (1533, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1533, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1534, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1534, 5.0, 3.34493425352417, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1534, 50.0, 33.4493425352417, 1);
INSERT INTO building(id, name, level) VALUES (1535, "building_livestock_ranchlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1535, 9.9803, 6.438750667591395, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1535, 9.9803, 2.909498241475598, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1535, 39.9212, 18.696497818133984, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1535, 4.99015, 2.337062227266748, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1535, 24.95075, 11.685311136333741, 1);
INSERT INTO building(id, name, level) VALUES (1536, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1536, 30.0, 5.468150827338029, 3);
INSERT INTO building(id, name, level) VALUES (1537, "building_steel_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1537, 90.0, 20.289166736126322, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1537, 120.0, 19.895963766426846, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 1537, 195.0, 38.14540119102533, 3);
INSERT INTO building(id, name, level) VALUES (1538, "building_textile_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1538, 80.0, 200.74368030384102, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1538, 10.0, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1538, 120.0, 60.0, 2);
INSERT INTO building(id, name, level) VALUES (1539, "building_furniture_manufacturieslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1539, 40.0, 100.37184015192051, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1539, 80.0, 25.59963347598012, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1539, 40.0, 14.618394983821698, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1539, 20.0, 5.830482533542274, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1539, 180.0, 88.96407388750833, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1539, 80.0, 39.53958839444815, 4);
INSERT INTO building(id, name, level) VALUES (1540, "building_glassworkslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1540, 40.0, 12.79981673799006, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1540, 30.0, 0.0, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1540, 20.0, 7.49773867881194, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1540, 40.0, 9.265098031871313, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1540, 50.0, 11.58137253983914, 2);
INSERT INTO building(id, name, level) VALUES (1541, "building_coal_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1541, 40.00000000000001, 11.66096506708455, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1541, 160.00000000000003, 46.6438602683382, 4);
INSERT INTO building(id, name, level) VALUES (1542, "building_maize_farmlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1542, 19.961000000000002, 11.235149747456887, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1542, 151.70359223300972, 85.3871337089826, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1542, 23.953194174757282, 13.482176418180956, 4);
INSERT INTO building(id, name, level) VALUES (1543, "building_livestock_ranchlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1543, 29.940892156862745, 19.316246942805524, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1543, 29.940892156862745, 8.728492437963055, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1543, 119.76359803921568, 56.08949253609791, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1543, 14.970441176470588, 7.01118254943202, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1543, 74.8522450980392, 35.055931113241094, 3);
INSERT INTO building(id, name, level) VALUES (1544, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1544, 25.0, 7.288103166927842, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1544, 200.0, 58.30482533542274, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1544, 62.5, 18.220257917319607, 5);
INSERT INTO building(id, name, level) VALUES (1545, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1545, 1.0, 1.440519697950409, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1545, 1.0, 3.1640594828182147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1545, 1.0, 1.1865223060568306, 1);
INSERT INTO building(id, name, level) VALUES (1546, "building_paper_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1546, 90.0, 28.79958766047763, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1546, 30.0, 4.819974864950533, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1546, 209.99999999999997, 50.46943096455076, 3);
INSERT INTO building(id, name, level) VALUES (1547, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1547, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1548, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1548, 5.0, 3.34493425352417, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1548, 50.0, 33.4493425352417, 1);
INSERT INTO building(id, name, level) VALUES (1549, "building_maize_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1549, 9.98, 9.98, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1549, 8.982, 8.982, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1549, 5.988, 5.988, 1);
INSERT INTO building(id, name, level) VALUES (1550, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1550, 10.0, 2.915241266771137, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1550, 120.0, 34.98289520125364, 2);
INSERT INTO building(id, name, level) VALUES (1551, "building_lead_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1551, 30.0, 6.763055578708776, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1551, 30.0, 8.745723800313412, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 1551, 120.0, 31.017558758044373, 3);
INSERT INTO building(id, name, level) VALUES (1552, "building_maize_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1552, 19.96, 20.1596, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1552, 17.964000000000002, 18.14364, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1552, 11.976, 12.09576, 2);
INSERT INTO building(id, name, level) VALUES (1553, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1553, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1554, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1554, 20.0, 6.39990836899503, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1554, 10.0, 3.74886933940597, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1554, 40.0, 13.89764704780697, 1);
INSERT INTO building(id, name, level) VALUES (1555, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1555, 5.0, 3.34493425352417, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1555, 50.0, 33.4493425352417, 1);
INSERT INTO building(id, name, level) VALUES (1556, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1556, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1557, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1557, 10.0, 2.2543518595695917, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1557, 10.0, 2.915241266771137, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1557, 40.0, 10.339186252681456, 1);
INSERT INTO building(id, name, level) VALUES (1558, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1558, 5.0, 3.34493425352417, 1);
INSERT INTO building(id, name, level) VALUES (1559, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1559, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1560, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1560, 11.133, 27.93599241028327, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1560, 22.266, 7.125017987202165, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1560, 27.8325, 4.614620096075626, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1560, 5.5665, 1.6227690511481532, 1);
INSERT INTO building(id, name, level) VALUES (1561, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1561, 10.0, 1.8227169424460097, 2);
INSERT INTO building(id, name, level) VALUES (1562, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1562, 120.0, 301.1155204557615, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1562, 15.0, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1562, 180.0, 90.0, 3);
INSERT INTO building(id, name, level) VALUES (1563, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1563, 60.0, 19.19972510698509, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1563, 30.0, 11.24660801821791, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1563, 120.0, 41.69294114342091, 3);
INSERT INTO building(id, name, level) VALUES (1564, "building_shipyardslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1564, 60.0, 150.55776022788075, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1564, 120.0, 38.39945021397018, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1564, 44.99999999999999, 29.699896915119407, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1564, 60.0, 39.59986255349255, 3);
INSERT INTO building(id, name, level) VALUES (1565, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1565, 15.0, 10.03480276057251, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1565, 150.0, 100.34802760572512, 3);
INSERT INTO building(id, name, level) VALUES (1566, "building_whaling_stationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1566, 5.0, 3.34493425352417, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (28, 1566, 20.0, 13.37973701409668, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1566, 10.0, 6.68986850704834, 1);
INSERT INTO building(id, name, level) VALUES (1567, "building_wheat_farmlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1567, 9.98, 5.617293446201078, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1567, 59.88, 33.70376067720647, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1567, 17.964000000000002, 10.111128203161941, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1567, 11.976, 6.740752135441293, 2);
INSERT INTO building(id, name, level) VALUES (1568, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1568, 1.0, 1.440519697950409, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1568, 1.0, 3.1640594828182147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1568, 1.0, 1.1865223060568306, 1);
INSERT INTO building(id, name, level) VALUES (1569, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 1569, 20.0, 12.543503450715637, 10);
INSERT INTO building(id, name, level) VALUES (1570, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1570, 15.0, 10.03480276057251, 3);
INSERT INTO building(id, name, level) VALUES (1571, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1571, 5.0, 1.4576206333855686, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1571, 40.0, 11.660965067084547, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1571, 12.5, 3.6440515834639213, 1);
INSERT INTO building(id, name, level) VALUES (1572, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1572, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1573, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1573, 5.0, 1.4576206333855686, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1573, 60.0, 17.49144760062682, 1);
INSERT INTO building(id, name, level) VALUES (1574, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1574, 29.94, 29.94, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1574, 4.99, 4.99, 1);
INSERT INTO building(id, name, level) VALUES (1575, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1575, 80.0, 51.61168035102268, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1575, 80.0, 68.5852087262509, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1575, 70.0, 52.586138971307186, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1575, 120.0, 90.14766680795518, 2);
INSERT INTO building(id, name, level) VALUES (1576, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1576, 5.0, 1.4576206333855686, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1576, 60.0, 17.49144760062682, 1);
INSERT INTO building(id, name, level) VALUES (1577, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1577, 59.88, 60.4788, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1577, 9.98, 10.0798, 2);
INSERT INTO building(id, name, level) VALUES (1578, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1578, 10.0, 1.8227169424460097, 1);
INSERT INTO building(id, name, level) VALUES (1579, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1579, 10.0, 25.092960037980127, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1579, 20.0, 6.39990836899503, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1579, 10.0, 3.6545987459554246, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1579, 5.0, 1.4576206333855686, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1579, 45.0, 22.241018471877084, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1579, 20.0, 9.884897098612038, 1);
INSERT INTO building(id, name, level) VALUES (1580, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1580, 15.0, 4.372861900156706, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1580, 120.0, 34.98289520125365, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1580, 37.5, 10.932154750391765, 3);
INSERT INTO building(id, name, level) VALUES (1581, "building_shipyardslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1581, 40.0, 100.37184015192051, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1581, 80.0, 25.59963347598012, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1581, 70.0, 46.19983964574131, 2);
INSERT INTO building(id, name, level) VALUES (1582, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1582, 15.0, 10.03480276057251, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1582, 150.0, 100.34802760572512, 3);
INSERT INTO building(id, name, level) VALUES (1583, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1583, 5.0, 3.34493425352417, 1);
INSERT INTO building(id, name, level) VALUES (1584, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1584, 20.0, 3.6454338848920194, 2);
INSERT INTO building(id, name, level) VALUES (1585, "building_food_industrylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1585, 40.0, 25.80584017551134, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1585, 40.0, 34.29260436312545, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1585, 35.0, 26.293069485653593, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1585, 60.0, 45.07383340397759, 1);
INSERT INTO building(id, name, level) VALUES (1586, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1586, 19.999999999999996, 5.8304825335422725, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1586, 79.99999999999999, 23.32193013416909, 2);
INSERT INTO building(id, name, level) VALUES (1587, "building_logging_camplevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1587, 5.0, 1.4576206333855686, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1587, 60.0, 17.49144760062682, 1);
INSERT INTO building(id, name, level) VALUES (1588, "building_maize_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1588, 149.7, 155.688, 5);
INSERT INTO building(id, name, level) VALUES (1589, "building_cotton_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1589, 398.4, 533.856, 10);
INSERT INTO building(id, name, level) VALUES (1590, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1590, 1.0, 1.440519697950409, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1590, 1.0, 3.1640594828182147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1590, 1.0, 1.1865223060568306, 1);
INSERT INTO building(id, name, level) VALUES (1591, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1591, 5.0, 3.34493425352417, 1);
INSERT INTO building(id, name, level) VALUES (1592, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1592, 20.0, 3.6454338848920194, 2);
INSERT INTO building(id, name, level) VALUES (1593, "building_cotton_plantationlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1593, 398.4, 533.856, 10);
INSERT INTO building(id, name, level) VALUES (1594, "building_tobacco_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1594, 124.49999999999999, 129.48, 5);
INSERT INTO building(id, name, level) VALUES (1595, "building_maize_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1595, 149.7, 155.688, 5);
INSERT INTO building(id, name, level) VALUES (1596, "building_barrackslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 1596, 1.0, 1.440519697950409, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1596, 1.0, 3.1640594828182147, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1596, 1.0, 1.1865223060568306, 1);
INSERT INTO building(id, name, level) VALUES (1597, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1597, 5.0, 3.34493425352417, 1);
INSERT INTO building(id, name, level) VALUES (2866, "building_subsistence_farmslevel", 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2866, 19.31425, 19.31425, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2866, 3.86285, 3.86285, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2866, 3.86285, 3.86285, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2866, 3.86285, 3.86285, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2866, 3.86285, 3.86285, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2866, 3.86285, 3.86285, 115);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2866, 5.40799, 5.40799, 115);
INSERT INTO building(id, name, level) VALUES (2867, "building_subsistence_farmslevel", 116);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2867, 118.3519, 118.3519, 116);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2867, 23.67038, 23.67038, 116);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2867, 23.67038, 23.67038, 116);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2867, 23.67038, 23.67038, 116);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2867, 23.67038, 23.67038, 116);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2867, 23.67038, 23.67038, 116);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2867, 33.13853, 33.13853, 116);
INSERT INTO building(id, name, level) VALUES (2868, "building_urban_centerlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2868, 49.99999999999999, 15.999770922487569, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2868, 99.99999999999999, 22.543518595695915, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2868, 49.99999999999999, 17.680744852191598, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2868, 700.0, 209.77728367179333, 10);
INSERT INTO building(id, name, level) VALUES (2869, "building_subsistence_farmslevel", 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2869, 8.13645, 8.13645, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2869, 1.62729, 1.62729, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2869, 1.62729, 1.62729, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2869, 1.62729, 1.62729, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2869, 1.62729, 1.62729, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2869, 1.62729, 1.62729, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2869, 2.2782, 2.2782, 162);
INSERT INTO building(id, name, level) VALUES (2870, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2870, 15.0, 4.799931276746272, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2870, 30.0, 6.763055578708776, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2870, 15.0, 5.30422345565748, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2870, 209.99999999999997, 62.93318510153799, 3);
INSERT INTO building(id, name, level) VALUES (2915, "building_subsistence_farmslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2915, 0.224, 0.224, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2915, 0.0448, 0.0448, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2915, 0.0448, 0.0448, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2915, 0.0448, 0.0448, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2915, 0.0448, 0.0448, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2915, 0.0448, 0.0448, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2915, 0.06272, 0.06272, 20);
INSERT INTO building(id, name, level) VALUES (2916, "building_subsistence_farmslevel", 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2916, 2.4926, 2.4926, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2916, 0.49852, 0.49852, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2916, 0.49852, 0.49852, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2916, 0.49852, 0.49852, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2916, 0.49852, 0.49852, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2916, 0.49852, 0.49852, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2916, 0.69792, 0.69792, 103);
INSERT INTO building(id, name, level) VALUES (2919, "building_subsistence_farmslevel", 126);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2919, 1.4931, 1.4931, 126);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2919, 0.29862, 0.29862, 126);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2919, 0.29862, 0.29862, 126);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2919, 0.29862, 0.29862, 126);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2919, 0.29862, 0.29862, 126);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2919, 0.29862, 0.29862, 126);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2919, 0.41806, 0.41806, 126);
INSERT INTO building(id, name, level) VALUES (2923, "building_subsistence_farmslevel", 129);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2923, 0.744975, 0.89397, 129);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2923, 0.1489916666666667, 0.17879, 129);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2923, 0.1489916666666667, 0.17879, 129);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2923, 0.1489916666666667, 0.17879, 129);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2923, 0.1489916666666667, 0.17879, 129);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2923, 0.1489916666666667, 0.17879, 129);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2923, 0.20859166666666665, 0.25031, 129);
INSERT INTO building(id, name, level) VALUES (2926, "building_subsistence_farmslevel", 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2926, 0.49725, 0.5967, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2926, 0.09945000000000001, 0.11934, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2926, 0.09945000000000001, 0.11934, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2926, 0.09945000000000001, 0.11934, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2926, 0.09945000000000001, 0.11934, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2926, 0.09945000000000001, 0.11934, 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2926, 0.13922500000000002, 0.16707, 17);
INSERT INTO building(id, name, level) VALUES (2927, "building_subsistence_farmslevel", 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2927, 0.496275, 0.59553, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2927, 0.09925, 0.1191, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2927, 0.09925, 0.1191, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2927, 0.09925, 0.1191, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2927, 0.09925, 0.1191, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2927, 0.09925, 0.1191, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2927, 0.13895000000000002, 0.16674, 39);
INSERT INTO building(id, name, level) VALUES (2930, "building_subsistence_farmslevel", 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2930, 1.4948000000000001, 1.79376, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2930, 0.2989583333333334, 0.35875, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2930, 0.2989583333333334, 0.35875, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2930, 0.2989583333333334, 0.35875, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2930, 0.2989583333333334, 0.35875, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2930, 0.2989583333333334, 0.35875, 148);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2930, 0.41854166666666665, 0.50225, 148);
INSERT INTO building(id, name, level) VALUES (2935, "building_subsistence_farmslevel", 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2935, 6.5148, 6.5148, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2935, 1.30296, 1.30296, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2935, 1.30296, 1.30296, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2935, 1.30296, 1.30296, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2935, 1.30296, 1.30296, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2935, 1.30296, 1.30296, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2935, 1.82414, 1.82414, 178);
INSERT INTO building(id, name, level) VALUES (2936, "building_subsistence_farmslevel", 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2936, 33.5506, 33.5506, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2936, 6.71012, 6.71012, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2936, 6.71012, 6.71012, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2936, 6.71012, 6.71012, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2936, 6.71012, 6.71012, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2936, 6.71012, 6.71012, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2936, 9.39416, 9.39416, 227);
INSERT INTO building(id, name, level) VALUES (2937, "building_subsistence_farmslevel", 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2937, 5.484, 5.484, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2937, 1.0968, 1.0968, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2937, 1.0968, 1.0968, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2937, 1.0968, 1.0968, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2937, 1.0968, 1.0968, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2937, 1.0968, 1.0968, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2937, 1.53552, 1.53552, 240);
INSERT INTO building(id, name, level) VALUES (2938, "building_subsistence_farmslevel", 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2938, 1.99245, 1.99245, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2938, 0.39849, 0.39849, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2938, 0.39849, 0.39849, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2938, 0.39849, 0.39849, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2938, 0.39849, 0.39849, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2938, 0.39849, 0.39849, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2938, 0.55788, 0.55788, 111);
INSERT INTO building(id, name, level) VALUES (2940, "building_subsistence_farmslevel", 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2940, 4.4955, 4.4955, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2940, 0.8991, 0.8991, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2940, 0.8991, 0.8991, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2940, 0.8991, 0.8991, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2940, 0.8991, 0.8991, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2940, 0.8991, 0.8991, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2940, 1.25874, 1.25874, 180);
INSERT INTO building(id, name, level) VALUES (2941, "building_subsistence_farmslevel", 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2941, 45.9404, 45.9404, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2941, 9.18808, 9.18808, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2941, 9.18808, 9.18808, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2941, 9.18808, 9.18808, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2941, 9.18808, 9.18808, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2941, 9.18808, 9.18808, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2941, 12.86331, 12.86331, 212);
INSERT INTO building(id, name, level) VALUES (2942, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2942, 5.0, 1.5999770922487575, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2942, 10.0, 2.2543518595695917, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2942, 5.0, 1.7680744852191603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2942, 70.0, 20.97772836717933, 1);
INSERT INTO building(id, name, level) VALUES (2943, "building_subsistence_farmslevel", 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2943, 9.58545, 9.58545, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2943, 1.91709, 1.91709, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2943, 1.91709, 1.91709, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2943, 1.91709, 1.91709, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2943, 1.91709, 1.91709, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2943, 1.91709, 1.91709, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2943, 2.68392, 2.68392, 179);
INSERT INTO building(id, name, level) VALUES (2944, "building_subsistence_farmslevel", 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2944, 117.8286, 117.8286, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2944, 23.56572, 23.56572, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2944, 23.56572, 23.56572, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2944, 23.56572, 23.56572, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2944, 23.56572, 23.56572, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2944, 23.56572, 23.56572, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2944, 32.992, 32.992, 172);
INSERT INTO building(id, name, level) VALUES (2945, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2945, 10.0, 3.199954184497515, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2945, 20.0, 4.508703719139183, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2945, 10.0, 3.5361489704383207, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2945, 140.0, 41.95545673435866, 2);
INSERT INTO building(id, name, level) VALUES (2946, "building_subsistence_farmslevel", 166);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2946, 32.0878, 32.0878, 166);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2946, 6.41756, 6.41756, 166);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2946, 6.41756, 6.41756, 166);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2946, 6.41756, 6.41756, 166);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2946, 6.41756, 6.41756, 166);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2946, 6.41756, 6.41756, 166);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2946, 8.98458, 8.98458, 166);
INSERT INTO building(id, name, level) VALUES (2947, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2947, 10.0, 3.199954184497515, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2947, 20.0, 4.508703719139183, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2947, 10.0, 3.5361489704383207, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2947, 140.0, 41.95545673435866, 2);
INSERT INTO building(id, name, level) VALUES (2948, "building_subsistence_farmslevel", 155);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2948, 17.96837, 17.96837, 155);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2948, 3.59367, 3.59367, 155);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2948, 3.59367, 3.59367, 155);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2948, 3.59367, 3.59367, 155);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2948, 3.59367, 3.59367, 155);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2948, 3.59367, 3.59367, 155);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2948, 5.03114, 5.03114, 155);
INSERT INTO building(id, name, level) VALUES (2949, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2949, 10.0, 3.199954184497515, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2949, 20.0, 4.508703719139183, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2949, 10.0, 3.5361489704383207, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2949, 140.0, 41.95545673435866, 2);
INSERT INTO building(id, name, level) VALUES (2950, "building_subsistence_farmslevel", 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2950, 5.0925, 5.0925, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2950, 1.0185, 1.0185, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2950, 1.0185, 1.0185, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2950, 1.0185, 1.0185, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2950, 1.0185, 1.0185, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2950, 1.0185, 1.0185, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2950, 1.4259, 1.4259, 150);
INSERT INTO building(id, name, level) VALUES (2951, "building_subsistence_farmslevel", 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2951, 14.29282, 14.29282, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2951, 2.85856, 2.85856, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2951, 2.85856, 2.85856, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2951, 2.85856, 2.85856, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2951, 2.85856, 2.85856, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2951, 2.85856, 2.85856, 149);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2951, 4.00199, 4.00199, 149);
INSERT INTO building(id, name, level) VALUES (2952, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2952, 5.0, 1.5999770922487575, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2952, 10.0, 2.2543518595695917, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2952, 5.0, 1.7680744852191603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2952, 70.0, 20.97772836717933, 1);
INSERT INTO building(id, name, level) VALUES (2953, "building_subsistence_farmslevel", 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2953, 14.8825, 14.8825, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2953, 2.9765, 2.9765, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2953, 2.9765, 2.9765, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2953, 2.9765, 2.9765, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2953, 2.9765, 2.9765, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2953, 2.9765, 2.9765, 100);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2953, 4.1671, 4.1671, 100);
INSERT INTO building(id, name, level) VALUES (2954, "building_subsistence_farmslevel", 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2954, 11.50365, 11.50365, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2954, 2.30073, 2.30073, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2954, 2.30073, 2.30073, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2954, 2.30073, 2.30073, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2954, 2.30073, 2.30073, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2954, 2.30073, 2.30073, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2954, 3.22102, 3.22102, 159);
INSERT INTO building(id, name, level) VALUES (2955, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2955, 5.0, 1.5999770922487575, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2955, 10.0, 2.2543518595695917, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2955, 5.0, 1.7680744852191603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2955, 70.0, 20.97772836717933, 1);
INSERT INTO building(id, name, level) VALUES (2956, "building_urban_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (2957, "building_subsistence_farmslevel", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2957, 21.4775, 21.4775, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2957, 4.2955, 4.2955, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2957, 4.2955, 4.2955, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2957, 4.2955, 4.2955, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2957, 4.2955, 4.2955, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2957, 4.2955, 4.2955, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2957, 6.0137, 6.0137, 50);
INSERT INTO building(id, name, level) VALUES (2958, "building_subsistence_farmslevel", 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2958, 14.8092, 14.8092, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2958, 2.96184, 2.96184, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2958, 2.96184, 2.96184, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2958, 2.96184, 2.96184, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2958, 2.96184, 2.96184, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2958, 2.96184, 2.96184, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2958, 4.14657, 4.14657, 21);
INSERT INTO building(id, name, level) VALUES (2959, "building_subsistence_farmslevel", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2959, 4.50975, 4.50975, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2959, 0.90195, 0.90195, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2959, 0.90195, 0.90195, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2959, 0.90195, 0.90195, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2959, 0.90195, 0.90195, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2959, 0.90195, 0.90195, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2959, 1.26273, 1.26273, 14);
INSERT INTO building(id, name, level) VALUES (2960, "building_subsistence_farmslevel", 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2960, 102.97825, 102.97825, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2960, 20.59565, 20.59565, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2960, 20.59565, 20.59565, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2960, 20.59565, 20.59565, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2960, 20.59565, 20.59565, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2960, 20.59565, 20.59565, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2960, 28.83391, 28.83391, 173);
INSERT INTO building(id, name, level) VALUES (2961, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2961, 20.0, 6.39990836899503, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2961, 40.0, 9.017407438278367, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2961, 20.0, 7.072297940876641, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2961, 279.99999999999994, 83.91091346871731, 4);
INSERT INTO building(id, name, level) VALUES (2962, "building_subsistence_farmslevel", 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2962, 21.6255, 21.6255, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2962, 4.3251, 4.3251, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2962, 4.3251, 4.3251, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2962, 4.3251, 4.3251, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2962, 4.3251, 4.3251, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2962, 4.3251, 4.3251, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2962, 6.05514, 6.05514, 39);
INSERT INTO building(id, name, level) VALUES (2963, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2963, 5.0, 1.5999770922487575, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2963, 10.0, 2.2543518595695917, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2963, 5.0, 1.7680744852191603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2963, 70.0, 20.97772836717933, 1);
INSERT INTO building(id, name, level) VALUES (2964, "building_subsistence_farmslevel", 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2964, 45.0977, 45.0977, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2964, 9.01954, 9.01954, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2964, 9.01954, 9.01954, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2964, 9.01954, 9.01954, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2964, 9.01954, 9.01954, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2964, 9.01954, 9.01954, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2964, 12.62735, 12.62735, 268);
INSERT INTO building(id, name, level) VALUES (2965, "building_subsistence_fishing_villageslevel", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2965, 3.51705, 3.51705, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2965, 14.0682, 14.0682, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2965, 1.75852, 1.75852, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2965, 5.27557, 5.27557, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2965, 3.51705, 3.51705, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2965, 3.51705, 3.51705, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2965, 3.51705, 3.51705, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2965, 4.92387, 4.92387, 15);
INSERT INTO building(id, name, level) VALUES (2966, "building_subsistence_fishing_villageslevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2966, 1.1592, 1.1592, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2966, 4.6368, 4.6368, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2966, 0.5796, 0.5796, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2966, 1.7388, 1.7388, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2966, 1.1592, 1.1592, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2966, 1.1592, 1.1592, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2966, 1.1592, 1.1592, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2966, 1.62288, 1.62288, 10);
INSERT INTO building(id, name, level) VALUES (2967, "building_subsistence_fishing_villageslevel", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2967, 7.20468, 7.20468, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2967, 28.81872, 28.81872, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2967, 3.60234, 3.60234, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2967, 10.80702, 10.80702, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2967, 7.20468, 7.20468, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2967, 7.20468, 7.20468, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2967, 7.20468, 7.20468, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2967, 10.08655, 10.08655, 18);
INSERT INTO building(id, name, level) VALUES (2968, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2968, 15.0, 4.799931276746272, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2968, 30.0, 6.763055578708776, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2968, 15.0, 5.30422345565748, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2968, 209.99999999999997, 62.93318510153799, 3);
INSERT INTO building(id, name, level) VALUES (2969, "building_subsistence_farmslevel", 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2969, 23.0824, 23.0824, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2969, 4.61648, 4.61648, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2969, 4.61648, 4.61648, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2969, 4.61648, 4.61648, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2969, 4.61648, 4.61648, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2969, 4.61648, 4.61648, 22);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2969, 6.46307, 6.46307, 22);
INSERT INTO building(id, name, level) VALUES (2970, "building_subsistence_farmslevel", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2970, 27.58385, 27.58385, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2970, 5.51677, 5.51677, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2970, 5.51677, 5.51677, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2970, 5.51677, 5.51677, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2970, 5.51677, 5.51677, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2970, 5.51677, 5.51677, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2970, 7.72347, 7.72347, 14);
INSERT INTO building(id, name, level) VALUES (2971, "building_subsistence_fishing_villageslevel", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2971, 3.5869, 3.5869, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2971, 14.3476, 14.3476, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2971, 1.79345, 1.79345, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2971, 5.38035, 5.38035, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2971, 3.5869, 3.5869, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2971, 3.5869, 3.5869, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2971, 3.5869, 3.5869, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2971, 5.02166, 5.02166, 20);
INSERT INTO building(id, name, level) VALUES (2972, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2972, 5.0, 1.5999770922487575, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2972, 10.0, 2.2543518595695917, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2972, 5.0, 1.7680744852191603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2972, 70.0, 20.97772836717933, 1);
INSERT INTO building(id, name, level) VALUES (2973, "building_subsistence_farmslevel", 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2973, 34.89337, 34.89337, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2973, 6.97867, 6.97867, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2973, 6.97867, 6.97867, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2973, 6.97867, 6.97867, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2973, 6.97867, 6.97867, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2973, 6.97867, 6.97867, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2973, 9.77014, 9.77014, 165);
INSERT INTO building(id, name, level) VALUES (2974, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2974, 5.0, 1.5999770922487575, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2974, 10.0, 2.2543518595695917, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2974, 5.0, 1.7680744852191603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2974, 70.0, 20.97772836717933, 1);
INSERT INTO building(id, name, level) VALUES (3946, "building_subsistence_farmslevel", 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3946, 12.808, 12.808, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3946, 2.5616, 2.5616, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3946, 2.5616, 2.5616, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3946, 2.5616, 2.5616, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3946, 2.5616, 2.5616, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3946, 2.5616, 2.5616, 80);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3946, 3.58624, 3.58624, 80);
INSERT INTO building(id, name, level) VALUES (3947, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3947, 5.0, 1.5999770922487575, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3947, 10.0, 2.2543518595695917, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3947, 5.0, 1.7680744852191603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3947, 70.0, 20.97772836717933, 1);
INSERT INTO building(id, name, level) VALUES (3956, "building_trade_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (3957, "building_trade_centerlevel", 60);
INSERT INTO building(id, name, level) VALUES (3958, "building_trade_centerlevel", 17);
INSERT INTO building(id, name, level) VALUES (3959, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3959, 5.0, 1.5999770922487575, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3959, 10.0, 2.2543518595695917, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3959, 5.0, 1.7680744852191603, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3959, 70.0, 20.97772836717933, 1);
INSERT INTO building(id, name, level) VALUES (4037, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4038, "building_conscription_centerlevel", 38);
INSERT INTO building(id, name, level) VALUES (4039, "building_conscription_centerlevel", 18);
INSERT INTO building(id, name, level) VALUES (4069, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4070, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4072, "building_conscription_centerlevel", 11);
INSERT INTO building(id, name, level) VALUES (4073, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4074, "building_conscription_centerlevel", 23);
INSERT INTO building(id, name, level) VALUES (4075, "building_conscription_centerlevel", 15);
INSERT INTO building(id, name, level) VALUES (4076, "building_conscription_centerlevel", 15);
INSERT INTO building(id, name, level) VALUES (4077, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4078, "building_conscription_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4079, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4080, "building_conscription_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4081, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4082, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4083, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4084, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4085, "building_conscription_centerlevel", 26);
INSERT INTO building(id, name, level) VALUES (4086, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4087, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4088, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4089, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4090, "building_conscription_centerlevel", 14);
INSERT INTO building(id, name, level) VALUES (4091, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4092, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4093, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4094, "building_conscription_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (4642, "building_conscription_centerlevel", 15);
