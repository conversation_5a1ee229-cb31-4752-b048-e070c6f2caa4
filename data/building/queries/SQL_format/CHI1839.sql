
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 99.24569822893329, 1.3571739425883182);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 17.499999999999993, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 91.2298385369036, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 14.188735152818126, 28914.144499616494);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 14.993980440792098, 454.8636538775203);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 9.0694444796999, 905.1568457472486);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 24.590628515852643, 5430.941074483516);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 28.240524363117785, 8211.06415913378);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 25.71363141441053, 7414.458978113219);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 40.280011677996974, 214.54426723087616);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 15.774092711829795, 3922.6333227816094);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 45.2535011064525, 1551.9371583333343);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 76.06369291804519, 1.102062442474188);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 12.836879432624112, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 15.674598073410904, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 50.09925828259345, 0.401116512831926);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 66.35208141806578, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 65.50415288850103, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 38.22746136177535, 288.42366904780755);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 64.29814707628029, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 102.11229004932031, 2224.7126789579465);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 38.2971794823423, 893.5593632156663);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 43.72269514395158, 2756.7968736169464);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 20.40585704980673, 4971.698233782816);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 37.77000536215643, 2931.147135);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 40.396727220919374, 868.3298284964351);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 70.0, 128.12557692863905);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 82.32246387604441, 3375.157523121099);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 83.75325750801704, 1016.6264314706287);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 88.96433979320452, 1724.9125180784342);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (2040, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2040, 0.98422, 0.18721607240177848, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2040, 4.9211, 0.9360803620088924, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2040, 6.88954, 1.3105125068124495, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2040, 4.9211, 0.9360803620088924, 1);
INSERT INTO building(id, name, level) VALUES (2244, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2244, 0.9371980198019803, 1.5299184538680468, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2244, 0.9371980198019803, 0.17827165911081969, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2244, 1.8743960396039605, 1.1154696789128, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2244, 3.748792079207921, 2.2309393578256, 2);
INSERT INTO building(id, name, level) VALUES (2245, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2245, 9.591098901098901, 15.656882421460008, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2245, 19.182197802197802, 19.182197802197802, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2245, 19.182197802197802, 19.182197802197802, 2);
INSERT INTO building(id, name, level) VALUES (2246, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2246, 99.99999999999999, 54.324137614009324, 10);
INSERT INTO building(id, name, level) VALUES (2247, "building_logging_camp", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2247, 49.99999999999999, 9.510885391567863, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2247, 600.0, 114.13062469881436, 10);
INSERT INTO building(id, name, level) VALUES (2248, "building_opium_plantation", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (44, 2248, 120.0, 144.0, 6);
INSERT INTO building(id, name, level) VALUES (2249, "building_paper_mills", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2249, 360.0, 249.8392850042919, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2249, 479.99999999999994, 333.1190466723892, 12);
INSERT INTO building(id, name, level) VALUES (2250, "building_government_administration", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2250, 79.99999999999999, 43.459310091207456, 8);
INSERT INTO building(id, name, level) VALUES (2251, "building_logging_camp", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2251, 27.006295238095237, 5.1370755772073755, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2251, 324.0756, 61.644917796071816, 6);
INSERT INTO building(id, name, level) VALUES (2252, "building_tea_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2252, 199.296, 217.23264, 10);
INSERT INTO building(id, name, level) VALUES (2253, "building_opium_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (44, 2253, 199.99999999999997, 218.0, 10);
INSERT INTO building(id, name, level) VALUES (2254, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2254, 99.99999999999999, 54.324137614009324, 10);
INSERT INTO building(id, name, level) VALUES (2255, "building_furniture_manufacturies", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2255, 70.0, 258.0205579125064, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2255, 104.99999999999999, 72.86979145958512, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2255, 104.99999999999999, 15.750985724891004, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2255, 140.0, 86.05367874865607, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2255, 175.0, 107.56709843582009, 7);
INSERT INTO building(id, name, level) VALUES (2256, "building_glassworks", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2256, 240.0, 166.55952333619456, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2256, 39.99999999999999, 211.46872156862742, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2256, 79.99999999999999, 67.75992055603241, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2256, 159.99999999999997, 135.51984111206482, 8);
INSERT INTO building(id, name, level) VALUES (2257, "building_logging_camp", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2257, 26.625895238095236, 5.064716761148326, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2257, 186.38129523809522, 35.45302276282994, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2257, 53.251799999999996, 10.12943533389387, 6);
INSERT INTO building(id, name, level) VALUES (2258, "building_opium_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (44, 2258, 100.0, 104.0, 5);
INSERT INTO building(id, name, level) VALUES (2259, "building_dye_plantation", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2259, 196.164, 209.89548, 8);
INSERT INTO building(id, name, level) VALUES (2260, "building_tea_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2260, 80.0, 82.4, 4);
INSERT INTO building(id, name, level) VALUES (2261, "building_government_administration", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2261, 79.99999999999999, 43.459310091207456, 8);
INSERT INTO building(id, name, level) VALUES (2262, "building_iron_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2262, 5.0, 0.9510885391567865, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2262, 20.0, 3.804354156627146, 1);
INSERT INTO building(id, name, level) VALUES (2263, "building_rice_farm", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2263, 79.99999999999999, 85.6, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2263, 96.0, 102.72, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2263, 144.0, 154.08, 8);
INSERT INTO building(id, name, level) VALUES (2264, "building_dye_plantation", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2264, 143.88449523809524, 151.07872, 6);
INSERT INTO building(id, name, level) VALUES (2265, "building_silk_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2265, 200.0, 268.0, 10);
INSERT INTO building(id, name, level) VALUES (2266, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2266, 0.026444444444444444, 0.04316893837056143, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2266, 0.026444444444444444, 0.005030201607095892, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2266, 0.052899999999999996, 0.031481258372139394, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2266, 0.10579999999999999, 0.06296251674427879, 1);
INSERT INTO building(id, name, level) VALUES (2267, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2267, 0.41564545454545454, 0.6785157861407164, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2267, 0.41564545454545454, 0.07906312563415897, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2267, 0.8312999999999999, 0.49471399026010365, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2267, 1.6625999999999999, 0.9894279805202073, 1);
INSERT INTO building(id, name, level) VALUES (2268, "building_government_administration", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2268, 90.0, 48.89172385260841, 9);
INSERT INTO building(id, name, level) VALUES (2269, "building_banana_plantation", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2269, 240.0, 304.8, 8);
INSERT INTO building(id, name, level) VALUES (2270, "building_rice_farm", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2270, 14.0, 2.663047909639002, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2270, 70.0, 13.315239548195011, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2270, 84.0, 15.978287457834014, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2270, 125.99999999999999, 23.967431186751018, 7);
INSERT INTO building(id, name, level) VALUES (2271, "building_tea_plantation", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2271, 160.0, 203.2, 8);
INSERT INTO building(id, name, level) VALUES (2272, "building_dye_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2272, 250.0, 322.5, 10);
INSERT INTO building(id, name, level) VALUES (2273, "building_silk_plantation", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2273, 320.0, 512.0, 16);
INSERT INTO building(id, name, level) VALUES (2274, "building_government_administration", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2274, 200.0, 108.64827522801868, 20);
INSERT INTO building(id, name, level) VALUES (2275, "building_construction_sector", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2275, 75.0, 276.4505977633997, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2275, 225.0, 156.14955312768242, 3);
INSERT INTO building(id, name, level) VALUES (2276, "building_arms_industry", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2276, 39.590796116504855, 4.818881829938282, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2276, 39.590796116504855, 5.9389910901727445, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 2276, 118.77239805825243, 16.13681069922777, 4);
INSERT INTO building(id, name, level) VALUES (2277, "building_tooling_workshops", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2277, 114.78239805825243, 79.65875628319945, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2277, 76.5215922330097, 9.313995841974762, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 2277, 229.56479611650485, 93.62975122788222, 4);
INSERT INTO building(id, name, level) VALUES (2278, "building_furniture_manufacturies", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2278, 50.0, 184.30039850893314, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2278, 75.0, 52.0498510425608, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2278, 75.0, 11.250704089207861, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2278, 100.0, 61.466913391897194, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2278, 125.0, 76.8336417398715, 5);
INSERT INTO building(id, name, level) VALUES (2279, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2279, 3.0, 0.5706531234940718, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2279, 15.0, 2.8532656174703592, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2279, 21.0, 3.9945718644585027, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2279, 15.0, 2.8532656174703592, 3);
INSERT INTO building(id, name, level) VALUES (2280, "building_tea_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2280, 100.0, 104.0, 5);
INSERT INTO building(id, name, level) VALUES (2281, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2281, 10.0, 1.902177078313573, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2281, 120.0, 22.826124939762877, 2);
INSERT INTO building(id, name, level) VALUES (2282, "building_forbidden_city", 1);
INSERT INTO building(id, name, level) VALUES (2283, "building_logging_camp", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2283, 28.046695238095236, 5.3349780804351195, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2283, 112.18679999999999, 21.339915944934912, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2283, 112.18679999999999, 21.339915944934912, 6);
INSERT INTO building(id, name, level) VALUES (2284, "building_livestock_ranch", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2284, 39.096, 63.821829121089195, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2284, 78.192, 78.192, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2284, 78.192, 78.192, 8);
INSERT INTO building(id, name, level) VALUES (2285, "building_government_administration", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2285, 60.0, 32.5944825684056, 6);
INSERT INTO building(id, name, level) VALUES (2286, "building_iron_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2286, 20.0, 3.804354156627146, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2286, 80.0, 15.217416626508584, 4);
INSERT INTO building(id, name, level) VALUES (2287, "building_logging_camp", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2287, 44.61929629629629, 8.48738026652965, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2287, 178.47719444444442, 33.94952282739367, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2287, 178.47719444444442, 33.94952282739367, 9);
INSERT INTO building(id, name, level) VALUES (2288, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2288, 10.34894117647059, 16.894013590876554, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2288, 10.34894117647059, 1.9685518690697854, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2288, 20.697892156862746, 12.317498879940901, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2288, 41.39579411764706, 24.635003594282328, 3);
INSERT INTO building(id, name, level) VALUES (2289, "building_logging_camp", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2289, 39.998794392523365, 7.608478985363542, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2289, 159.99519626168225, 30.433919496925345, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2289, 159.99519626168225, 30.433919496925345, 8);
INSERT INTO building(id, name, level) VALUES (2290, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2290, 9.999999999999998, 16.324388459456003, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2290, 19.999999999999996, 19.999999999999996, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2290, 19.999999999999996, 19.999999999999996, 2);
INSERT INTO building(id, name, level) VALUES (2291, "building_government_administration", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2291, 70.0, 38.02689632980653, 7);
INSERT INTO building(id, name, level) VALUES (2292, "building_wheat_farm", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2292, 35.0, 42.35, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2292, 49.0, 59.29, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2292, 35.0, 42.35, 7);
INSERT INTO building(id, name, level) VALUES (2293, "building_tea_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2293, 60.00000000000001, 70.2, 3);
INSERT INTO building(id, name, level) VALUES (2294, "building_government_administration", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2294, 40.0, 21.729655045603735, 4);
INSERT INTO building(id, name, level) VALUES (2295, "building_tea_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2295, 60.0, 61.2, 3);
INSERT INTO building(id, name, level) VALUES (2296, "building_livestock_ranch", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2296, 29.976295238095236, 48.93446880420079, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2296, 59.9526, 59.9526, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2296, 59.9526, 59.9526, 6);
INSERT INTO building(id, name, level) VALUES (2297, "building_government_administration", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2297, 40.0, 21.729655045603735, 4);
INSERT INTO building(id, name, level) VALUES (2298, "building_tea_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2298, 100.0, 119.0, 5);
INSERT INTO building(id, name, level) VALUES (2299, "building_livestock_ranch", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2299, 22.50275, 36.734363240602356, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2299, 45.0055, 45.0055, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2299, 45.0055, 45.0055, 5);
INSERT INTO building(id, name, level) VALUES (2300, "building_government_administration", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2300, 119.99999999999999, 65.18896513681119, 12);
INSERT INTO building(id, name, level) VALUES (2301, "building_paper_mills", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2301, 119.99999999999999, 83.27976166809728, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2301, 160.0, 111.03968222412972, 4);
INSERT INTO building(id, name, level) VALUES (2302, "building_tea_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2302, 200.0, 248.0, 10);
INSERT INTO building(id, name, level) VALUES (2303, "building_silk_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2303, 200.0, 298.0, 10);
INSERT INTO building(id, name, level) VALUES (2304, "building_government_administration", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2304, 129.99999999999997, 70.62137889821213, 13);
INSERT INTO building(id, name, level) VALUES (2305, "building_banana_plantation", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2305, 210.00000000000003, 264.6, 7);
INSERT INTO building(id, name, level) VALUES (2306, "building_fishing_wharf", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2306, 117.64999999999999, 122.356, 5);
INSERT INTO building(id, name, level) VALUES (2307, "building_silk_plantation", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2307, 400.0, 656.0, 20);
INSERT INTO building(id, name, level) VALUES (2308, "building_rice_farm", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2308, 16.0, 3.0434833253017164, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2308, 80.0, 15.217416626508582, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2308, 96.0, 18.2608999518103, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2308, 144.0, 27.391349927715446, 8);
INSERT INTO building(id, name, level) VALUES (2309, "building_dye_plantation", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2309, 300.0, 393.0, 12);
INSERT INTO building(id, name, level) VALUES (2310, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2310, 20.0, 73.72015940357325, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2310, 40.0, 27.75992055603243, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 2310, 35.0, 29.64496524326419, 1);
INSERT INTO building(id, name, level) VALUES (2311, "building_military_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2311, 5.732, 21.128197685064098, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2311, 11.464, 7.955993231358895, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2311, 2.866, 0.34884156620547313, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 2311, 10.031, 6.071146519719397, 1);
INSERT INTO building(id, name, level) VALUES (2312, "building_port", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2312, 25.0, 16.077312080903692, 5);
INSERT INTO building(id, name, level) VALUES (2316, "building_government_administration", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2316, 109.99999999999999, 59.75655137541026, 11);
INSERT INTO building(id, name, level) VALUES (2317, "building_silk_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2317, 200.0, 268.0, 10);
INSERT INTO building(id, name, level) VALUES (2318, "building_rice_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2318, 10.0, 1.902177078313573, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2318, 50.0, 9.510885391567864, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2318, 60.0, 11.413062469881439, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2318, 89.99999999999999, 17.119593704822154, 5);
INSERT INTO building(id, name, level) VALUES (2319, "building_dye_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2319, 249.20999999999995, 271.6389, 10);
INSERT INTO building(id, name, level) VALUES (2320, "building_fishing_wharf", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2320, 100.0, 103.0, 4);
INSERT INTO building(id, name, level) VALUES (2321, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2321, 99.99999999999999, 54.324137614009324, 10);
INSERT INTO building(id, name, level) VALUES (2322, "building_textile_mills", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2322, 200.0, 737.2015940357326, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2322, 120.0, 1269.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2322, 120.0, 120.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2322, 240.0, 240.0, 8);
INSERT INTO building(id, name, level) VALUES (2323, "building_tea_plantation", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2323, 300.0, 342.0, 15);
INSERT INTO building(id, name, level) VALUES (2324, "building_rice_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2324, 8.0, 1.5217416626508582, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2324, 40.0, 7.608708313254291, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2324, 48.0, 9.13044997590515, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2324, 72.0, 13.695674963857723, 4);
INSERT INTO building(id, name, level) VALUES (2325, "building_fishing_wharf", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2325, 74.90474509803921, 76.40284, 3);
INSERT INTO building(id, name, level) VALUES (2326, "building_government_administration", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2326, 119.99999999999999, 65.18896513681119, 12);
INSERT INTO building(id, name, level) VALUES (2327, "building_glassworks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2327, 150.0, 104.0997020851216, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2327, 25.0, 132.16795098039216, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2327, 50.0, 42.34995034752027, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2327, 100.0, 84.69990069504054, 5);
INSERT INTO building(id, name, level) VALUES (2328, "building_tea_plantation", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2328, 300.0, 342.0, 15);
INSERT INTO building(id, name, level) VALUES (2329, "building_rice_farm", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2329, 16.0, 3.0434833253017164, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2329, 79.99999999999999, 15.217416626508578, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2329, 96.0, 18.2608999518103, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2329, 144.0, 27.391349927715446, 8);
INSERT INTO building(id, name, level) VALUES (2330, "building_government_administration", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2330, 170.0, 92.35103394381586, 17);
INSERT INTO building(id, name, level) VALUES (2331, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2331, 40.0, 147.4403188071465, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2331, 80.0, 55.51984111206486, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 2331, 100.0, 12.171722477511274, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2331, 20.0, 3.804354156627146, 2);
INSERT INTO building(id, name, level) VALUES (2332, "building_glassworks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2332, 300.0, 208.1994041702432, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2332, 49.99999999999999, 264.3359019607843, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2332, 99.99999999999999, 84.69990069504053, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2332, 199.99999999999997, 169.39980139008105, 10);
INSERT INTO building(id, name, level) VALUES (2333, "building_tea_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2333, 199.99999999999997, 218.0, 10);
INSERT INTO building(id, name, level) VALUES (2334, "building_rice_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2334, 10.0, 1.902177078313573, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2334, 50.0, 9.510885391567864, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2334, 60.0, 11.413062469881439, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2334, 89.99999999999999, 17.119593704822154, 5);
INSERT INTO building(id, name, level) VALUES (2335, "building_government_administration", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2335, 150.0, 81.486206421014, 15);
INSERT INTO building(id, name, level) VALUES (2336, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2336, 25.0, 92.15019925446657, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2336, 75.0, 52.0498510425608, 1);
INSERT INTO building(id, name, level) VALUES (2337, "building_glassworks", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2337, 180.0, 124.91964250214595, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2337, 30.0, 158.6015411764706, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2337, 60.0, 50.81994041702433, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2337, 120.0, 101.63988083404865, 6);
INSERT INTO building(id, name, level) VALUES (2338, "building_tea_plantation", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2338, 239.99999999999997, 266.4, 12);
INSERT INTO building(id, name, level) VALUES (2339, "building_government_administration", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2339, 129.99999999999997, 70.62137889821213, 13);
INSERT INTO building(id, name, level) VALUES (2340, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2340, 10.0, 1.902177078313573, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 2340, 40.0, 7.608708313254292, 2);
INSERT INTO building(id, name, level) VALUES (2341, "building_paper_mills", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2341, 330.0, 229.01934458726754, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2341, 439.99999999999994, 305.35912611635666, 11);
INSERT INTO building(id, name, level) VALUES (2342, "building_glassworks", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2342, 180.0, 124.91964250214595, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2342, 30.0, 158.6015411764706, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2342, 60.0, 50.81994041702433, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2342, 120.0, 101.63988083404865, 6);
INSERT INTO building(id, name, level) VALUES (2343, "building_wheat_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2343, 4.0, 0.7608708313254291, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2343, 20.0, 3.8043541566271455, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2343, 28.0, 5.326095819278004, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2343, 20.0, 3.8043541566271455, 4);
INSERT INTO building(id, name, level) VALUES (2344, "building_tea_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2344, 199.99999999999997, 218.0, 10);
INSERT INTO building(id, name, level) VALUES (2345, "building_government_administration", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2345, 119.99999999999999, 65.18896513681119, 12);
INSERT INTO building(id, name, level) VALUES (2346, "building_glassworks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2346, 150.0, 104.0997020851216, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2346, 25.0, 132.16795098039216, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2346, 50.0, 42.34995034752027, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2346, 100.0, 84.69990069504054, 5);
INSERT INTO building(id, name, level) VALUES (2347, "building_rice_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2347, 10.0, 1.902177078313573, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2347, 50.0, 9.510885391567864, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2347, 60.0, 11.413062469881439, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2347, 89.99999999999999, 17.119593704822154, 5);
INSERT INTO building(id, name, level) VALUES (2348, "building_silk_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2348, 200.0, 268.0, 10);
INSERT INTO building(id, name, level) VALUES (2349, "building_tea_plantation", 17);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2349, 340.0, 394.4, 17);
INSERT INTO building(id, name, level) VALUES (2350, "building_government_administration", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2350, 119.99999999999999, 65.18896513681119, 12);
INSERT INTO building(id, name, level) VALUES (2351, "building_furniture_manufacturies", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2351, 99.99999999999999, 368.6007970178662, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2351, 150.0, 104.0997020851216, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2351, 150.0, 22.501408178415723, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2351, 199.99999999999997, 122.93382678379437, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2351, 249.99999999999997, 153.66728347974296, 10);
INSERT INTO building(id, name, level) VALUES (2352, "building_rice_farm", 12);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2352, 24.0, 4.565224987952575, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2352, 119.99999999999999, 22.82612493976287, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2352, 144.0, 27.391349927715446, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2352, 215.99999999999997, 41.087024891573165, 12);
INSERT INTO building(id, name, level) VALUES (2353, "building_paper_mills", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2353, 180.0, 124.91964250214595, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2353, 240.0, 166.55952333619462, 6);
INSERT INTO building(id, name, level) VALUES (2354, "building_tea_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2354, 199.99999999999997, 218.0, 10);
INSERT INTO building(id, name, level) VALUES (2355, "building_government_administration", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2355, 109.99999999999999, 59.75655137541026, 11);
INSERT INTO building(id, name, level) VALUES (2356, "building_rice_farm", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2356, 12.0, 2.2826124939762873, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2356, 60.0, 11.413062469881437, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2356, 71.99999999999999, 13.695674963857721, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2356, 108.0, 20.543512445786586, 6);
INSERT INTO building(id, name, level) VALUES (2357, "building_tea_plantation", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2357, 300.0, 342.0, 15);
INSERT INTO building(id, name, level) VALUES (2358, "building_government_administration", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2358, 90.0, 48.89172385260841, 9);
INSERT INTO building(id, name, level) VALUES (2359, "building_paper_mills", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2359, 270.0, 187.37946375321891, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2359, 360.0, 249.8392850042919, 9);
INSERT INTO building(id, name, level) VALUES (2360, "building_furniture_manufacturies", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2360, 99.99999999999999, 368.6007970178662, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2360, 150.0, 104.0997020851216, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2360, 150.0, 22.501408178415723, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2360, 199.99999999999997, 122.93382678379437, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2360, 249.99999999999997, 153.66728347974296, 10);
INSERT INTO building(id, name, level) VALUES (2361, "building_rice_farm", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2361, 12.0, 2.2826124939762873, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2361, 60.0, 11.413062469881437, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2361, 71.99999999999999, 13.695674963857721, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2361, 108.0, 20.543512445786586, 6);
INSERT INTO building(id, name, level) VALUES (2362, "building_tea_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2362, 199.99999999999997, 218.0, 10);
INSERT INTO building(id, name, level) VALUES (2363, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2363, 99.99999999999999, 54.324137614009324, 10);
INSERT INTO building(id, name, level) VALUES (2364, "building_furniture_manufacturies", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2364, 79.99999999999999, 294.880637614293, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2364, 120.0, 83.27976166809728, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2364, 120.0, 18.001126542732578, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2364, 159.99999999999997, 98.34706142703547, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2364, 200.0, 122.93382678379436, 8);
INSERT INTO building(id, name, level) VALUES (2365, "building_rice_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2365, 8.0, 1.5217416626508582, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2365, 40.0, 7.608708313254291, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2365, 48.0, 9.13044997590515, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2365, 72.0, 13.695674963857723, 4);
INSERT INTO building(id, name, level) VALUES (2366, "building_paper_mills", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2366, 270.0, 187.37946375321891, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2366, 360.0, 249.8392850042919, 9);
INSERT INTO building(id, name, level) VALUES (2367, "building_tea_plantation", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2367, 120.0, 126.0, 6);
INSERT INTO building(id, name, level) VALUES (2368, "building_silk_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2368, 200.0, 268.0, 10);
INSERT INTO building(id, name, level) VALUES (2369, "building_government_administration", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2369, 60.0, 32.5944825684056, 6);
INSERT INTO building(id, name, level) VALUES (2370, "building_logging_camp", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2370, 36.57359813084112, 6.956946003593811, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2370, 438.88319626168226, 83.48335559859692, 8);
INSERT INTO building(id, name, level) VALUES (2371, "building_rice_farm", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2371, 12.0, 2.2826124939762873, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2371, 60.0, 11.413062469881437, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2371, 71.99999999999999, 13.695674963857721, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2371, 108.0, 20.543512445786586, 6);
INSERT INTO building(id, name, level) VALUES (2372, "building_tea_plantation", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2372, 159.99999999999997, 171.2, 8);
INSERT INTO building(id, name, level) VALUES (2373, "building_fishing_wharf", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2373, 115.81499999999998, 120.4476, 5);
INSERT INTO building(id, name, level) VALUES (2374, "building_tea_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2374, 59.71559803921569, 60.90991, 3);
INSERT INTO building(id, name, level) VALUES (2375, "building_banana_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2375, 149.922, 155.91888, 5);
INSERT INTO building(id, name, level) VALUES (2376, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2376, 5.0, 3.2154624161807384, 1);
INSERT INTO building(id, name, level) VALUES (2377, "building_government_administration", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2377, 90.0, 48.89172385260841, 9);
INSERT INTO building(id, name, level) VALUES (2378, "building_glassworks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2378, 300.0, 208.1994041702432, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2378, 49.99999999999999, 264.3359019607843, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2378, 99.99999999999999, 84.69990069504053, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2378, 199.99999999999997, 169.39980139008105, 10);
INSERT INTO building(id, name, level) VALUES (2379, "building_textile_mills", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2379, 200.0, 737.2015940357326, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2379, 120.0, 1269.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2379, 120.0, 120.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2379, 240.0, 240.0, 8);
INSERT INTO building(id, name, level) VALUES (2380, "building_tea_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2380, 60.0, 61.2, 3);
INSERT INTO building(id, name, level) VALUES (2381, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 2381, 3.0, 0.5706531234940718, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2381, 15.0, 2.8532656174703592, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2381, 21.0, 3.9945718644585027, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2381, 15.0, 2.8532656174703592, 3);
INSERT INTO building(id, name, level) VALUES (2382, "building_fishing_wharf", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2382, 150.0, 157.5, 6);
INSERT INTO building(id, name, level) VALUES (2383, "building_port", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2383, 10.0, 6.430924832361477, 2);
INSERT INTO building(id, name, level) VALUES (2384, "building_government_administration", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2384, 170.0, 92.35103394381586, 17);
INSERT INTO building(id, name, level) VALUES (2385, "building_paper_mills", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2385, 209.99999999999997, 145.73958291917023, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2385, 280.0, 194.319443892227, 7);
INSERT INTO building(id, name, level) VALUES (2386, "building_tea_plantation", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2386, 239.99999999999997, 266.4, 12);
INSERT INTO building(id, name, level) VALUES (2387, "building_fishing_wharf", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2387, 100.0, 103.0, 4);
INSERT INTO building(id, name, level) VALUES (2388, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2388, 5.0, 3.2154624161807384, 1);
INSERT INTO building(id, name, level) VALUES (2389, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2389, 14.400892857142857, 23.50857691630053, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2389, 28.80179464285714, 28.80179464285714, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2389, 28.80179464285714, 28.80179464285714, 3);
INSERT INTO building(id, name, level) VALUES (2390, "building_livestock_ranch", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 2390, 23.609745614035088, 38.54146588324464, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2390, 47.219500000000004, 47.219500000000004, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2390, 47.219500000000004, 47.219500000000004, 5);
INSERT INTO building(id, name, level) VALUES (2721, "building_subsistence_rice_paddies", 343);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2721, 1882.44402, 1882.44402, 343);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2721, 256.69691, 256.69691, 343);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2721, 256.69691, 256.69691, 343);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2721, 342.26255, 342.26255, 343);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2721, 342.26255, 342.26255, 343);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2721, 342.26255, 342.26255, 343);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2721, 342.26255, 342.26255, 343);
INSERT INTO building(id, name, level) VALUES (2722, "building_urban_center", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2722, 6.0, 4.163988083404864, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2722, 6.0, 6.377398069775855, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2722, 120.0, 101.63988083404865, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2722, 30.0, 25.409970208512163, 6);
INSERT INTO building(id, name, level) VALUES (2723, "building_subsistence_rice_paddies", 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2723, 2129.92406, 2129.92406, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2723, 290.44419, 290.44419, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2723, 290.44419, 290.44419, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2723, 387.25892, 387.25892, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2723, 387.25892, 387.25892, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2723, 387.25892, 387.25892, 388);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2723, 387.25892, 387.25892, 388);
INSERT INTO building(id, name, level) VALUES (2724, "building_urban_center", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2724, 6.0, 4.163988083404864, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2724, 6.0, 6.377398069775855, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2724, 120.0, 101.63988083404865, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2724, 30.0, 25.409970208512163, 6);
INSERT INTO building(id, name, level) VALUES (2888, "building_subsistence_pastures", 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2888, 5.842022222222222, 5.25782, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2888, 8.763033333333333, 7.88673, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2888, 2.921011111111111, 2.62891, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2888, 5.842022222222222, 5.25782, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2888, 5.842022222222222, 5.25782, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2888, 5.842022222222222, 5.25782, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2888, 19.395522222222223, 17.45597, 35);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2888, 5.842022222222222, 5.25782, 35);
INSERT INTO building(id, name, level) VALUES (2889, "building_subsistence_pastures", 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2889, 5.434288888888889, 4.89086, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2889, 8.151433333333333, 7.33629, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2889, 2.7171444444444446, 2.44543, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2889, 5.434288888888889, 4.89086, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2889, 5.434288888888889, 4.89086, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2889, 5.434288888888889, 4.89086, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2889, 18.041833333333333, 16.23765, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2889, 5.434288888888889, 4.89086, 27);
INSERT INTO building(id, name, level) VALUES (3522, "building_subsistence_pastures", 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3522, 5.89834, 5.89834, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3522, 8.84751, 8.84751, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3522, 2.94917, 2.94917, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3522, 5.89834, 5.89834, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3522, 5.89834, 5.89834, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3522, 5.89834, 5.89834, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3522, 19.58248, 19.58248, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3522, 5.89834, 5.89834, 28);
INSERT INTO building(id, name, level) VALUES (3523, "building_subsistence_pastures", 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3523, 8.1882375, 6.55059, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3523, 12.28235, 9.82588, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3523, 4.0941125, 3.27529, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3523, 8.1882375, 6.55059, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3523, 8.1882375, 6.55059, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3523, 8.1882375, 6.55059, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3523, 27.184949999999997, 21.74796, 38);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3523, 8.1882375, 6.55059, 38);
INSERT INTO building(id, name, level) VALUES (3524, "building_subsistence_rice_paddies", 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3524, 1778.4894521739132, 2045.26287, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3524, 242.52128695652175, 278.89948, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3524, 242.52128695652175, 278.89948, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3524, 323.3617130434783, 371.86597, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3524, 323.3617130434783, 371.86597, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3524, 323.3617130434783, 371.86597, 324);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3524, 323.3617130434783, 371.86597, 324);
INSERT INTO building(id, name, level) VALUES (3525, "building_urban_center", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3525, 5.0, 3.4699900695040538, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3525, 5.0, 5.314498391479878, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3525, 100.0, 84.69990069504054, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3525, 25.0, 21.174975173760135, 5);
INSERT INTO building(id, name, level) VALUES (3526, "building_subsistence_rice_paddies", 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3526, 713.27685, 713.27685, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3526, 97.26502, 97.26502, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3526, 97.26502, 97.26502, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3526, 129.6867, 129.6867, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3526, 129.6867, 129.6867, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3526, 129.6867, 129.6867, 130);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3526, 129.6867, 129.6867, 130);
INSERT INTO building(id, name, level) VALUES (3527, "building_urban_center", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3527, 2.373774509803922, 1.6473947952522923, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3527, 2.373774509803922, 2.5230841628177765, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3527, 47.47559803921568, 40.211784393592296, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3527, 11.868892156862746, 10.0529398704642, 3);
INSERT INTO building(id, name, level) VALUES (3528, "building_subsistence_rice_paddies", 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3528, 481.49029, 481.49029, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3528, 65.65776, 65.65776, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3528, 65.65776, 65.65776, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3528, 87.54369, 87.54369, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3528, 87.54369, 87.54369, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3528, 87.54369, 87.54369, 93);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3528, 87.54369, 87.54369, 93);
INSERT INTO building(id, name, level) VALUES (3529, "building_urban_center", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3529, 6.0, 4.163988083404864, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3529, 6.0, 6.377398069775855, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3529, 120.0, 101.63988083404865, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3529, 30.0, 25.409970208512163, 6);
INSERT INTO building(id, name, level) VALUES (3530, "building_subsistence_rice_paddies", 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3530, 916.65381, 916.65381, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3530, 124.99824, 124.99824, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3530, 124.99824, 124.99824, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3530, 166.66433, 166.66433, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3530, 166.66433, 166.66433, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3530, 166.66433, 166.66433, 167);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3530, 166.66433, 166.66433, 167);
INSERT INTO building(id, name, level) VALUES (3531, "building_urban_center", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3531, 2.949598039215686, 2.047015181021412, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3531, 2.949598039215686, 3.1351268069847937, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3531, 58.992000000000004, 49.96616541801832, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3531, 14.748000000000001, 12.49154135450458, 3);
INSERT INTO building(id, name, level) VALUES (3532, "building_subsistence_pastures", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3532, 0.27619999999999995, 0.22096, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3532, 0.4143, 0.33144, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3532, 0.13809999999999997, 0.11048, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3532, 0.27619999999999995, 0.22096, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3532, 0.27619999999999995, 0.22096, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3532, 0.27619999999999995, 0.22096, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3532, 0.916975, 0.73358, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3532, 0.27619999999999995, 0.22096, 10);
INSERT INTO building(id, name, level) VALUES (3533, "building_subsistence_pastures", 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3533, 49.998533333333334, 44.99868, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3533, 74.9978, 67.49802, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3533, 24.999266666666667, 22.49934, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3533, 49.998533333333334, 44.99868, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3533, 49.998533333333334, 44.99868, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3533, 49.998533333333334, 44.99868, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3533, 165.99513333333334, 149.39562, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3533, 49.998533333333334, 44.99868, 101);
INSERT INTO building(id, name, level) VALUES (3534, "building_subsistence_rice_paddies", 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3534, 1900.557941666667, 2280.66953, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3534, 259.16699166666666, 311.00039, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3534, 259.16699166666666, 311.00039, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3534, 345.5559833333333, 414.66718, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3534, 345.5559833333333, 414.66718, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3534, 345.5559833333333, 414.66718, 351);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3534, 345.5559833333333, 414.66718, 351);
INSERT INTO building(id, name, level) VALUES (3535, "building_urban_center", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3535, 4.0, 2.775992055603243, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3535, 4.0, 4.2515987131839035, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3535, 80.0, 67.75992055603243, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3535, 20.0, 16.939980139008107, 4);
INSERT INTO building(id, name, level) VALUES (3536, "building_subsistence_rice_paddies", 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3536, 1822.42104, 1822.42104, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3536, 248.51196, 248.51196, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3536, 248.51196, 248.51196, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3536, 331.34928, 331.34928, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3536, 331.34928, 331.34928, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3536, 331.34928, 331.34928, 332);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3536, 331.34928, 331.34928, 332);
INSERT INTO building(id, name, level) VALUES (3537, "building_urban_center", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3537, 7.0, 4.857986097305676, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3537, 7.0, 7.44029774807183, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3537, 140.0, 118.57986097305675, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3537, 35.0, 29.64496524326419, 7);
INSERT INTO building(id, name, level) VALUES (3538, "building_subsistence_rice_paddies", 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3538, 308.47575, 308.47575, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3538, 42.06487, 42.06487, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3538, 42.06487, 42.06487, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3538, 56.0865, 56.0865, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3538, 56.0865, 56.0865, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3538, 56.0865, 56.0865, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3538, 56.0865, 56.0865, 75);
INSERT INTO building(id, name, level) VALUES (3539, "building_subsistence_farms", 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3539, 132.24135, 132.24135, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3539, 26.44827, 26.44827, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3539, 26.44827, 26.44827, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3539, 26.44827, 26.44827, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3539, 26.44827, 26.44827, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3539, 26.44827, 26.44827, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3539, 26.44827, 26.44827, 62);
INSERT INTO building(id, name, level) VALUES (3540, "building_subsistence_rice_paddies", 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3540, 1081.38717, 1081.38717, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3540, 147.46188, 147.46188, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3540, 147.46188, 147.46188, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3540, 196.61585, 196.61585, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3540, 196.61585, 196.61585, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3540, 196.61585, 196.61585, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3540, 196.61585, 196.61585, 197);
INSERT INTO building(id, name, level) VALUES (3541, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3541, 2.0, 1.3879960278016215, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3541, 2.0, 2.1257993565919517, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3541, 40.0, 33.87996027801621, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3541, 10.0, 8.469990069504053, 2);
INSERT INTO building(id, name, level) VALUES (3542, "building_subsistence_farms", 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3542, 343.76834444444444, 309.39151, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3542, 68.75366666666667, 61.8783, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3542, 68.75366666666667, 61.8783, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3542, 68.75366666666667, 61.8783, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3542, 68.75366666666667, 61.8783, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3542, 68.75366666666667, 61.8783, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3542, 68.75366666666667, 61.8783, 138);
INSERT INTO building(id, name, level) VALUES (3543, "building_subsistence_rice_paddies", 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3543, 1981.1013739130435, 2278.26658, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3543, 270.1501826086957, 310.67271, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3543, 270.1501826086957, 310.67271, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3543, 360.2002434782609, 414.23028, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3543, 360.2002434782609, 414.23028, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3543, 360.2002434782609, 414.23028, 365);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3543, 360.2002434782609, 414.23028, 365);
INSERT INTO building(id, name, level) VALUES (3544, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3544, 1.9581980198019804, 1.3589855365670749, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3544, 1.9581980198019804, 2.0813680452873418, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3544, 39.163999999999994, 33.17186910820568, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3544, 9.790999999999999, 8.29296727705142, 2);
INSERT INTO building(id, name, level) VALUES (3545, "building_subsistence_rice_paddies", 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3545, 1081.23548, 1081.23548, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3545, 147.4412, 147.4412, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3545, 147.4412, 147.4412, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3545, 196.58827, 196.58827, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3545, 196.58827, 196.58827, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3545, 196.58827, 196.58827, 197);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3545, 196.58827, 196.58827, 197);
INSERT INTO building(id, name, level) VALUES (3546, "building_subsistence_rice_paddies", 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3546, 787.93704, 787.93704, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3546, 107.44596, 107.44596, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3546, 107.44596, 107.44596, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3546, 143.26128, 143.26128, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3546, 143.26128, 143.26128, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3546, 143.26128, 143.26128, 144);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3546, 143.26128, 143.26128, 144);
INSERT INTO building(id, name, level) VALUES (3547, "building_subsistence_rice_paddies", 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3547, 1328.446347826087, 1527.7133, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3547, 181.1517739130435, 208.32454, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3547, 181.1517739130435, 208.32454, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3547, 241.53569565217393, 277.76605, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3547, 241.53569565217393, 277.76605, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3547, 241.53569565217393, 277.76605, 245);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3547, 241.53569565217393, 277.76605, 245);
INSERT INTO building(id, name, level) VALUES (3548, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3548, 1.0, 0.6939980139008107, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3548, 1.0, 1.0628996782959759, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3548, 20.0, 16.939980139008107, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3548, 5.0, 4.234995034752027, 1);
INSERT INTO building(id, name, level) VALUES (3549, "building_subsistence_farms", 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3549, 134.74312, 134.74312, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3549, 26.94862, 26.94862, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3549, 26.94862, 26.94862, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3549, 26.94862, 26.94862, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3549, 26.94862, 26.94862, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3549, 26.94862, 26.94862, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3549, 26.94862, 26.94862, 55);
INSERT INTO building(id, name, level) VALUES (3550, "building_subsistence_rice_paddies", 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3550, 2627.8824000000004, 3022.06476, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3550, 358.3476, 412.09974, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3550, 358.3476, 412.09974, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3550, 477.7968, 549.46632, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3550, 477.7968, 549.46632, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3550, 477.7968, 549.46632, 480);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3550, 477.7968, 549.46632, 480);
INSERT INTO building(id, name, level) VALUES (3551, "building_urban_center", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3551, 4.0, 2.775992055603243, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3551, 4.0, 4.2515987131839035, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3551, 80.0, 67.75992055603243, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3551, 20.0, 16.939980139008107, 4);
INSERT INTO building(id, name, level) VALUES (3552, "building_subsistence_rice_paddies", 270);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3552, 1480.20345, 1776.24414, 270);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3552, 201.84592500000002, 242.21511, 270);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3552, 201.84592500000002, 242.21511, 270);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3552, 269.1279, 322.95348, 270);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3552, 269.1279, 322.95348, 270);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3552, 269.1279, 322.95348, 270);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3552, 269.1279, 322.95348, 270);
INSERT INTO building(id, name, level) VALUES (3553, "building_urban_center", 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3553, 10.741499999999998, 7.454579666315557, 11);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3553, 10.741499999999998, 11.417136894416222, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3553, 214.82999999999998, 181.96079666315558, 11);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3553, 53.707499999999996, 45.490199165788894, 11);
INSERT INTO building(id, name, level) VALUES (3555, "building_subsistence_rice_paddies", 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3555, 2058.4575, 2058.4575, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3555, 280.69875, 280.69875, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3555, 280.69875, 280.69875, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3555, 374.265, 374.265, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3555, 374.265, 374.265, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3555, 374.265, 374.265, 375);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3555, 374.265, 374.265, 375);
INSERT INTO building(id, name, level) VALUES (3556, "building_urban_center", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3556, 4.0, 2.775992055603243, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3556, 4.0, 4.2515987131839035, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3556, 80.0, 67.75992055603243, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3556, 20.0, 16.939980139008107, 4);
INSERT INTO building(id, name, level) VALUES (3557, "building_subsistence_rice_paddies", 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3557, 1179.55761, 1179.55761, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3557, 160.84876, 160.84876, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3557, 160.84876, 160.84876, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3557, 214.46502, 214.46502, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3557, 214.46502, 214.46502, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3557, 214.46502, 214.46502, 231);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3557, 214.46502, 214.46502, 231);
INSERT INTO building(id, name, level) VALUES (3558, "building_urban_center", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3558, 4.0, 2.775992055603243, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3558, 4.0, 4.2515987131839035, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3558, 80.0, 67.75992055603243, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3558, 20.0, 16.939980139008107, 4);
INSERT INTO building(id, name, level) VALUES (3559, "building_subsistence_rice_paddies", 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3559, 1387.24327, 1387.24327, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3559, 189.16953, 189.16953, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3559, 189.16953, 189.16953, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3559, 252.22605, 252.22605, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3559, 252.22605, 252.22605, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3559, 252.22605, 252.22605, 345);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3559, 252.22605, 252.22605, 345);
INSERT INTO building(id, name, level) VALUES (3560, "building_urban_center", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3560, 6.0, 4.163988083404864, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3560, 6.0, 6.377398069775855, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3560, 120.0, 101.63988083404865, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3560, 30.0, 25.409970208512163, 6);
INSERT INTO building(id, name, level) VALUES (3561, "building_subsistence_rice_paddies", 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3561, 1379.51946, 1379.51946, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3561, 188.11629, 188.11629, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3561, 188.11629, 188.11629, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3561, 250.82172, 250.82172, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3561, 250.82172, 250.82172, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3561, 250.82172, 250.82172, 257);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3561, 250.82172, 250.82172, 257);
INSERT INTO building(id, name, level) VALUES (3562, "building_urban_center", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3562, 4.0, 2.775992055603243, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3562, 4.0, 4.2515987131839035, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3562, 80.0, 67.75992055603243, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3562, 20.0, 16.939980139008107, 4);
INSERT INTO building(id, name, level) VALUES (3563, "building_subsistence_rice_paddies", 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3563, 2678.95408, 2678.95408, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3563, 365.31192, 365.31192, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3563, 365.31192, 365.31192, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3563, 487.08256, 487.08256, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3563, 487.08256, 487.08256, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3563, 487.08256, 487.08256, 488);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3563, 487.08256, 487.08256, 488);
INSERT INTO building(id, name, level) VALUES (3564, "building_urban_center", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3564, 4.0, 2.775992055603243, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3564, 4.0, 4.2515987131839035, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3564, 80.0, 67.75992055603243, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3564, 20.0, 16.939980139008107, 4);
INSERT INTO building(id, name, level) VALUES (3565, "building_subsistence_rice_paddies", 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3565, 1019.70594, 1019.70594, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3565, 139.05081, 139.05081, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3565, 139.05081, 139.05081, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3565, 185.40108, 185.40108, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3565, 185.40108, 185.40108, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3565, 185.40108, 185.40108, 186);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3565, 185.40108, 185.40108, 186);
INSERT INTO building(id, name, level) VALUES (3566, "building_urban_center", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3566, 7.0, 4.857986097305676, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3566, 7.0, 7.44029774807183, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3566, 140.0, 118.57986097305675, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3566, 35.0, 29.64496524326419, 7);
INSERT INTO building(id, name, level) VALUES (3567, "building_subsistence_rice_paddies", 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3567, 3117.84572, 3117.84572, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3567, 425.16078, 425.16078, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3567, 425.16078, 425.16078, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3567, 566.88104, 566.88104, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3567, 566.88104, 566.88104, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3567, 566.88104, 566.88104, 568);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3567, 566.88104, 566.88104, 568);
INSERT INTO building(id, name, level) VALUES (3568, "building_urban_center", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3568, 5.0, 3.4699900695040538, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3568, 5.0, 5.314498391479878, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3568, 100.0, 84.69990069504054, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3568, 25.0, 21.174975173760135, 5);
INSERT INTO building(id, name, level) VALUES (3569, "building_subsistence_rice_paddies", 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3569, 2898.30816, 2898.30816, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3569, 395.22384, 395.22384, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3569, 395.22384, 395.22384, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3569, 526.96512, 526.96512, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3569, 526.96512, 526.96512, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3569, 526.96512, 526.96512, 528);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3569, 526.96512, 526.96512, 528);
INSERT INTO building(id, name, level) VALUES (3570, "building_urban_center", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3570, 7.0, 4.857986097305676, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3570, 7.0, 7.44029774807183, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3570, 140.0, 118.57986097305675, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3570, 35.0, 29.64496524326419, 7);
INSERT INTO building(id, name, level) VALUES (3571, "building_subsistence_rice_paddies", 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3571, 1366.66513, 1366.66513, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3571, 186.36342, 186.36342, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3571, 186.36342, 186.36342, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3571, 248.48457, 248.48457, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3571, 248.48457, 248.48457, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3571, 248.48457, 248.48457, 249);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3571, 248.48457, 248.48457, 249);
INSERT INTO building(id, name, level) VALUES (3572, "building_urban_center", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3572, 3.0, 2.081994041702432, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3572, 3.0, 3.1886990348879274, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3572, 60.0, 50.81994041702433, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3572, 15.0, 12.704985104256082, 3);
INSERT INTO building(id, name, level) VALUES (3573, "building_subsistence_rice_paddies", 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3573, 1664.67664, 1664.67664, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3573, 227.00136, 227.00136, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3573, 227.00136, 227.00136, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3573, 302.66848, 302.66848, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3573, 302.66848, 302.66848, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3573, 302.66848, 302.66848, 304);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3573, 302.66848, 302.66848, 304);
INSERT INTO building(id, name, level) VALUES (3574, "building_urban_center", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3574, 6.0, 4.163988083404864, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3574, 6.0, 6.377398069775855, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3574, 120.0, 101.63988083404865, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3574, 30.0, 25.409970208512163, 6);
INSERT INTO building(id, name, level) VALUES (3575, "building_subsistence_rice_paddies", 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3575, 1646.766, 1646.766, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3575, 224.559, 224.559, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3575, 224.559, 224.559, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3575, 299.412, 299.412, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3575, 299.412, 299.412, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3575, 299.412, 299.412, 300);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3575, 299.412, 299.412, 300);
INSERT INTO building(id, name, level) VALUES (3576, "building_urban_center", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3576, 6.0, 4.163988083404864, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3576, 6.0, 6.377398069775855, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3576, 120.0, 101.63988083404865, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3576, 30.0, 25.409970208512163, 6);
INSERT INTO building(id, name, level) VALUES (3577, "building_subsistence_rice_paddies", 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3577, 1733.93308, 1733.93308, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3577, 236.44542, 236.44542, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3577, 236.44542, 236.44542, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3577, 315.26056, 315.26056, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3577, 315.26056, 315.26056, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3577, 315.26056, 315.26056, 316);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3577, 315.26056, 315.26056, 316);
INSERT INTO building(id, name, level) VALUES (3578, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3578, 2.0, 1.3879960278016215, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3578, 2.0, 2.1257993565919517, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3578, 40.0, 33.87996027801621, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3578, 10.0, 8.469990069504053, 2);
INSERT INTO building(id, name, level) VALUES (3579, "building_subsistence_rice_paddies", 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3579, 365.08032, 365.08032, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3579, 49.78368, 49.78368, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3579, 49.78368, 49.78368, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3579, 66.37824, 66.37824, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3579, 66.37824, 66.37824, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3579, 66.37824, 66.37824, 72);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3579, 66.37824, 66.37824, 72);
INSERT INTO building(id, name, level) VALUES (3597, "building_subsistence_pastures", 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3597, 2.62808, 2.62808, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3597, 3.94212, 3.94212, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3597, 1.31404, 1.31404, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3597, 2.62808, 2.62808, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3597, 2.62808, 2.62808, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3597, 2.62808, 2.62808, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3597, 8.72522, 8.72522, 16);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3597, 2.62808, 2.62808, 16);
INSERT INTO building(id, name, level) VALUES (3605, "building_subsistence_pastures", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3605, 1.17375, 1.17375, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3605, 1.76062, 1.76062, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3605, 0.58687, 0.58687, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3605, 1.17375, 1.17375, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3605, 1.17375, 1.17375, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3605, 1.17375, 1.17375, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3605, 3.89685, 3.89685, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3605, 1.17375, 1.17375, 50);
INSERT INTO building(id, name, level) VALUES (33558040, "building_conscription_center", 6);
INSERT INTO building(id, name, level) VALUES (3615, "building_subsistence_farms", 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3615, 6.8094, 6.8094, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3615, 1.36188, 1.36188, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3615, 1.36188, 1.36188, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3615, 1.36188, 1.36188, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3615, 1.36188, 1.36188, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3615, 1.36188, 1.36188, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3615, 1.36188, 1.36188, 13);
INSERT INTO building(id, name, level) VALUES (3616, "building_subsistence_farms", 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3616, 4.428, 3.9852, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3616, 0.8855999999999999, 0.79704, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3616, 0.8855999999999999, 0.79704, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3616, 0.8855999999999999, 0.79704, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3616, 0.8855999999999999, 0.79704, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3616, 0.8855999999999999, 0.79704, 40);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3616, 0.8855999999999999, 0.79704, 40);
INSERT INTO building(id, name, level) VALUES (4092, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4093, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4093, 18.494, 2.3746469539133397, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4093, 9.247, 15.095162008458969, 10);
INSERT INTO building(id, name, level) VALUES (4094, "building_barracks", 10);
INSERT INTO building(id, name, level) VALUES (4095, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4095, 3.8624, 0.4959357842973334, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4095, 2.414, 3.9407073741126797, 5);
INSERT INTO building(id, name, level) VALUES (4096, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4096, 5.0, 8.162194229728003, 5);
INSERT INTO building(id, name, level) VALUES (4097, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4097, 9.204, 1.181802236607461, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4097, 4.602, 7.512483569041654, 5);
INSERT INTO building(id, name, level) VALUES (4098, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4098, 9.712, 1.2470299132911409, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4098, 4.856, 7.927123035911836, 5);
INSERT INTO building(id, name, level) VALUES (4099, "building_barracks", 15);
INSERT INTO building(id, name, level) VALUES (4100, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4101, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4101, 17.822, 2.288361523339653, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4101, 8.911, 14.546662556221245, 10);
INSERT INTO building(id, name, level) VALUES (4102, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4103, "building_barracks", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4103, 20.0, 2.568018767074013, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4103, 10.0, 16.324388459456006, 20);
INSERT INTO building(id, name, level) VALUES (4104, "building_barracks", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4104, 14.0, 1.7976131369518091, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4104, 7.0, 11.427071921619206, 25);
INSERT INTO building(id, name, level) VALUES (4105, "building_barracks", 15);
INSERT INTO building(id, name, level) VALUES (4106, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4106, 10.0, 1.2840093835370066, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4106, 5.0, 8.162194229728003, 5);
INSERT INTO building(id, name, level) VALUES (4107, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4108, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4108, 20.0, 2.568018767074013, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4108, 10.0, 16.324388459456006, 10);
INSERT INTO building(id, name, level) VALUES (4109, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4110, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4111, "building_barracks", 15);
INSERT INTO building(id, name, level) VALUES (4112, "building_barracks", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4112, 1.7421, 0.2236872747059819, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4112, 0.87105, 1.4219358567609153, 20);
INSERT INTO building(id, name, level) VALUES (4113, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4114, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4115, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4116, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4117, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4118, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4119, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4120, "building_barracks", 10);
INSERT INTO building(id, name, level) VALUES (4121, "building_barracks", 5);
INSERT INTO building(id, name, level) VALUES (4122, "building_barracks", 20);
INSERT INTO building(id, name, level) VALUES (4123, "building_barracks", 10);
INSERT INTO building(id, name, level) VALUES (4124, "building_naval_base", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4124, 4.15695, 2.4768840457835224, 7);
INSERT INTO building(id, name, level) VALUES (4125, "building_naval_base", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4125, 6.036, 3.5965003428834454, 8);
INSERT INTO building(id, name, level) VALUES (4212, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4212, 1.0, 0.6939980139008107, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 4212, 1.0, 1.0628996782959759, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4212, 20.0, 16.939980139008107, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 4212, 5.0, 4.234995034752027, 1);
INSERT INTO building(id, name, level) VALUES (4213, "building_trade_center", 107);
INSERT INTO building(id, name, level) VALUES (4434, "building_artillery_foundries", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4434, 0.02715, 0.003304622652644311, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 4434, 0.0181, 0.0027151699201954974, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 4434, 0.04525, 0.006147814610781297, 1);
INSERT INTO building(id, name, level) VALUES (4435, "building_conscription_center", 25);
INSERT INTO building(id, name, level) VALUES (4574, "building_conscription_center", 13);
INSERT INTO building(id, name, level) VALUES (4653, "building_conscription_center", 24);
INSERT INTO building(id, name, level) VALUES (4724, "building_conscription_center", 16);
INSERT INTO building(id, name, level) VALUES (4770, "building_conscription_center", 23);
INSERT INTO building(id, name, level) VALUES (4824, "building_conscription_center", 25);
INSERT INTO building(id, name, level) VALUES (4902, "building_conscription_center", 25);
INSERT INTO building(id, name, level) VALUES (4962, "building_conscription_center", 2);
INSERT INTO building(id, name, level) VALUES (4993, "building_conscription_center", 25);
INSERT INTO building(id, name, level) VALUES (5017, "building_conscription_center", 25);
INSERT INTO building(id, name, level) VALUES (5034, "building_glassworks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5034, 30.0, 20.81994041702432, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 5034, 5.0, 26.433590196078434, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 5034, 10.0, 8.469990069504053, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 5034, 20.0, 16.939980139008107, 1);
INSERT INTO building(id, name, level) VALUES (5036, "building_coal_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5036, 0.045, 0.008559796852411077, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 5036, 0.225, 0.04279898426205539, 1);
INSERT INTO building(id, name, level) VALUES (33559493, "building_conscription_center", 9);
INSERT INTO building(id, name, level) VALUES (5110, "building_conscription_center", 25);
INSERT INTO building(id, name, level) VALUES (5118, "building_conscription_center", 9);
INSERT INTO building(id, name, level) VALUES (5122, "building_conscription_center", 15);
INSERT INTO building(id, name, level) VALUES (5214, "building_conscription_center", 21);
INSERT INTO building(id, name, level) VALUES (33560011, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (16782831, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (16782849, "building_conscription_center", 6);
INSERT INTO building(id, name, level) VALUES (5669, "building_conscription_center", 15);
INSERT INTO building(id, name, level) VALUES (5681, "building_conscription_center", 10);
INSERT INTO building(id, name, level) VALUES (5684, "building_conscription_center", 10);
INSERT INTO building(id, name, level) VALUES (5687, "building_conscription_center", 16);
INSERT INTO building(id, name, level) VALUES (5694, "building_conscription_center", 14);
INSERT INTO building(id, name, level) VALUES (5715, "building_conscription_center", 22);
INSERT INTO building(id, name, level) VALUES (5737, "building_conscription_center", 12);
INSERT INTO building(id, name, level) VALUES (5748, "building_conscription_center", 22);
INSERT INTO building(id, name, level) VALUES (5765, "building_conscription_center", 5);
INSERT INTO building(id, name, level) VALUES (5790, "building_conscription_center", 8);
INSERT INTO building(id, name, level) VALUES (5830, "building_conscription_center", 2);
INSERT INTO building(id, name, level) VALUES (5843, "building_conscription_center", 1);
