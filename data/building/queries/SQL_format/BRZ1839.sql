
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 102.09543041955122, 2.9454424358990297);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 107.65651814744628, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 30.46011611366928, 1286.2535136687618);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 27.93094977750499, 189.55227015095173);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 21.726482788545688, 47.91519671880149);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 31.955836605354673, 334.6388757811991);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 40.55655633568723, 410.55239969392267);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 44.00937060947529, 252.5036724148031);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 48.91277603539679, 12.096614457121815);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 22.408651459184856, 114.80034671162181);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 36.64739025775499, 49.983448333333335);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 87.77342087584269, 5.889235458290042);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 67.26137582792347, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 66.15085026545601, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 10.879272600193117);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 44.956647194705944, 26.911259487674396);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 24.67018807328678, 270.67421777534986);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 49.33562211549121, 480.70977314270147);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 18.531425754058713, 67.17749249999999);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 9.733466500198814, 2.734715941950101);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 51.824450771711895, 81.73618201797382);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 63.462423501126445);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 49.665692631981614);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (1772, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1772, 4.17065, 0.6125118403578486, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1772, 50.0478, 7.350142084294183, 1);
INSERT INTO building(id, name, level) VALUES (1773, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1773, 3.847, 1.5130366858432487, 1);
INSERT INTO building(id, name, level) VALUES (1774, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1774, 5.0, 0.7343122059605198, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1774, 60.0, 8.811746471526238, 1);
INSERT INTO building(id, name, level) VALUES (1775, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1775, 30.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (1776, "building_rice_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1776, 2.0, 0.29372488238420796, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1776, 40.0, 5.8744976476841595, 1);
INSERT INTO building(id, name, level) VALUES (1777, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1777, 4.9989, 0.7341506572752086, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1777, 59.9868, 8.809807887302503, 1);
INSERT INTO building(id, name, level) VALUES (1778, "building_rice_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1778, 1.9331, 0.2838997850684562, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1778, 38.662, 5.677995701369124, 1);
INSERT INTO building(id, name, level) VALUES (1780, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1780, 29.880000000000003, 34.362, 1);
INSERT INTO building(id, name, level) VALUES (1781, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1781, 5.0, 0.7343122059605198, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1781, 60.0, 8.811746471526238, 1);
INSERT INTO building(id, name, level) VALUES (1782, "building_maize_farm", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1782, 19.92, 22.908, 1);
INSERT INTO building(id, name, level) VALUES (1783, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1783, 3.259793388429752, 1.0244901715335477, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1783, 6.519595041322314, 2.048982940428312, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1783, 6.519595041322314, 2.048982940428312, 2);
INSERT INTO building(id, name, level) VALUES (1784, "building_maize_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1784, 0.7444396551724138, 0.1093302250788288, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1784, 14.888793103448277, 2.186604501576576, 2);
INSERT INTO building(id, name, level) VALUES (1785, "building_coffee_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1785, 17.644000000000002, 20.2906, 1);
INSERT INTO building(id, name, level) VALUES (1786, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1786, 1.046, 0.4113949501928875, 1);
INSERT INTO building(id, name, level) VALUES (1787, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1787, 4.3402, 0.6374123672619697, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1787, 52.0824, 7.648948407143636, 1);
INSERT INTO building(id, name, level) VALUES (1788, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1788, 21.45, 21.45, 1);
INSERT INTO building(id, name, level) VALUES (1789, "building_maize_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1789, 1.0464356435643565, 0.15368209316429177, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1789, 20.92879207920792, 3.0736534959544444, 2);
INSERT INTO building(id, name, level) VALUES (1790, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1790, 4.4565, 0.6544924691726114, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1790, 53.478, 7.853909630071337, 1);
INSERT INTO building(id, name, level) VALUES (1791, "building_maize_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1791, 0.90777, 0.13331731824095622, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1791, 18.1554, 2.6663463648191246, 1);
INSERT INTO building(id, name, level) VALUES (1792, "building_coffee_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1792, 97.284, 110.90376, 5);
INSERT INTO building(id, name, level) VALUES (1793, "building_sugar_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1793, 6.390000000000001, 7.3485, 1);
INSERT INTO building(id, name, level) VALUES (1794, "building_arms_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1794, 10.0, 1.0509671858644907, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1794, 10.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 1794, 30.0, 1.576450778796736, 1);
INSERT INTO building(id, name, level) VALUES (1795, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1795, 6.403891089108911, 2.0126194204975847, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1795, 12.80779207920792, 4.02524195268534, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1795, 12.80779207920792, 4.02524195268534, 2);
INSERT INTO building(id, name, level) VALUES (1796, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1796, 5.0, 0.7343122059605198, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1796, 60.0, 8.811746471526238, 1);
INSERT INTO building(id, name, level) VALUES (1797, "building_paper_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1797, 60.0, 13.483847798976857, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1797, 80.0, 17.978463731969143, 2);
INSERT INTO building(id, name, level) VALUES (1798, "building_university", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1798, 5.0, 0.9007311080466722, 1);
INSERT INTO building(id, name, level) VALUES (1799, "building_maize_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1799, 3.9997572815533977, 0.5874141185448256, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1799, 79.99519417475727, 11.74828950014123, 4);
INSERT INTO building(id, name, level) VALUES (1800, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1800, 10.0, 1.8014622160933444, 1);
INSERT INTO building(id, name, level) VALUES (1801, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1801, 30.0, 6.7419238994884285, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1801, 20.0, 2.1019343717289813, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1801, 60.0, 9.8948254570819, 1);
INSERT INTO building(id, name, level) VALUES (1802, "building_gold_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1802, 10.0, 1.4686244119210397, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 1802, 20.0, 2.9372488238420793, 2);
INSERT INTO building(id, name, level) VALUES (1803, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1803, 10.0, 1.4686244119210397, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1803, 40.0, 5.874497647684159, 2);
INSERT INTO building(id, name, level) VALUES (1804, "building_logging_camp", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1804, 19.988, 2.9354864745477744, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1804, 239.856, 35.22583769457329, 4);
INSERT INTO building(id, name, level) VALUES (1805, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1805, 4.69015, 1.4740236590063922, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1805, 9.3803, 2.9480473180127844, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1805, 9.3803, 2.9480473180127844, 1);
INSERT INTO building(id, name, level) VALUES (1806, "building_maize_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1806, 4.0, 0.5874497647684159, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1806, 80.0, 11.748995295368319, 4);
INSERT INTO building(id, name, level) VALUES (1807, "building_coffee_plantation", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1807, 120.0, 126.0, 6);
INSERT INTO building(id, name, level) VALUES (1808, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1808, 30.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (1809, "building_government_administration", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1809, 50.0, 9.007311080466723, 5);
INSERT INTO building(id, name, level) VALUES (1810, "building_university", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1810, 9.997, 1.8009217774285164, 2);
INSERT INTO building(id, name, level) VALUES (1811, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1811, 24.805, 21.97561273447813, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1811, 74.415, 16.72334223268105, 1);
INSERT INTO building(id, name, level) VALUES (1812, "building_furniture_manufacturies", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1812, 10.15859405940594, 8.999852004687565, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1812, 30.47579207920792, 6.848849032488401, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1812, 5.07929702970297, 0.7459579613219809, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1812, 66.0308910891089, 27.678565654246697, 2);
INSERT INTO building(id, name, level) VALUES (1813, "building_paper_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1813, 10.283999999999999, 2.311131512744633, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1813, 13.712, 3.081508683659511, 2);
INSERT INTO building(id, name, level) VALUES (1814, "building_fishing_wharf", 2);
INSERT INTO building(id, name, level) VALUES (1815, "building_coffee_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1815, 34.135999999999996, 34.47736, 2);
INSERT INTO building(id, name, level) VALUES (1816, "building_sugar_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1816, 12.900000000000002, 14.964, 2);
INSERT INTO building(id, name, level) VALUES (1817, "building_maize_farm", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1817, 4.858619047619047, 0.7135486541557886, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1817, 97.1724, 14.270975880495605, 6);
INSERT INTO building(id, name, level) VALUES (1818, "building_banana_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1818, 82.03139215686275, 83.67202, 3);
INSERT INTO building(id, name, level) VALUES (1819, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1819, 14.991, 5.896005447745292, 3);
INSERT INTO building(id, name, level) VALUES (1820, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1820, 30.0, 5.404386648280033, 3);
INSERT INTO building(id, name, level) VALUES (1821, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1821, 20.0, 17.71869601651129, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1821, 40.0, 8.989231865984571, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1821, 35.0, 19.43664795581563, 1);
INSERT INTO building(id, name, level) VALUES (1822, "building_military_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1822, 4.784, 4.238312087149501, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1822, 9.568, 2.1502242623435097, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1822, 2.392, 0.25139135085878617, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1822, 8.372, 3.39278737002265, 1);
INSERT INTO building(id, name, level) VALUES (1823, "building_coffee_plantation", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1823, 120.0, 126.0, 6);
INSERT INTO building(id, name, level) VALUES (1824, "building_sugar_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1824, 4.554, 5.2371, 1);
INSERT INTO building(id, name, level) VALUES (1825, "building_maize_farm", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1825, 6.0, 0.881174647152624, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1825, 120.0, 17.62349294305248, 6);
INSERT INTO building(id, name, level) VALUES (1826, "building_banana_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1826, 90.0, 91.8, 3);
INSERT INTO building(id, name, level) VALUES (1827, "building_logging_camp", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1827, 20.0, 2.9372488238420793, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1827, 239.99999999999997, 35.246985886104945, 4);
INSERT INTO building(id, name, level) VALUES (1828, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1828, 10.0, 3.933030116566802, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1828, 100.0, 39.33030116566802, 2);
INSERT INTO building(id, name, level) VALUES (1829, "building_port", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1829, 10.0, 3.933030116566802, 2);
INSERT INTO building(id, name, level) VALUES (1830, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1830, 24.805, 21.97561273447813, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1830, 74.415, 16.72334223268105, 1);
INSERT INTO building(id, name, level) VALUES (1831, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1831, 30.0, 6.7419238994884285, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1831, 20.0, 2.1019343717289813, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1831, 60.0, 9.8948254570819, 1);
INSERT INTO building(id, name, level) VALUES (1832, "building_maize_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1832, 0.97288, 0.14287953178697413, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1832, 19.4576, 2.8575906357394825, 1);
INSERT INTO building(id, name, level) VALUES (1833, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1833, 5.0, 0.7343122059605198, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1833, 60.0, 8.811746471526238, 1);
INSERT INTO building(id, name, level) VALUES (1834, "building_fishing_wharf", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1834, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1835, "building_cotton_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1835, 40.0, 50.0, 1);
INSERT INTO building(id, name, level) VALUES (1836, "building_maize_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1836, 1.7953168316831682, 0.2636646126142637, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1836, 35.906396039603955, 5.273300976786731, 2);
INSERT INTO building(id, name, level) VALUES (1837, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1837, 10.0, 1.4686244119210397, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1837, 120.0, 17.623492943052476, 2);
INSERT INTO building(id, name, level) VALUES (1838, "building_fishing_wharf", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1838, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1839, "building_maize_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1839, 0.99066, 0.1454907459913697, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1839, 19.8132, 2.9098149198273937, 1);
INSERT INTO building(id, name, level) VALUES (1840, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1840, 5.0, 0.7343122059605198, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1840, 60.0, 8.811746471526238, 1);
INSERT INTO building(id, name, level) VALUES (1841, "building_textile_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1841, 30.824, 27.308054300647203, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1841, 34.677, 30.7215610882281, 1);
INSERT INTO building(id, name, level) VALUES (1842, "building_tobacco_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1842, 24.77, 24.77, 1);
INSERT INTO building(id, name, level) VALUES (1843, "building_maize_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1843, 0.98533, 0.14470796917981582, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1843, 19.7066, 2.8941593835963166, 1);
INSERT INTO building(id, name, level) VALUES (1844, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1844, 10.0, 1.8014622160933444, 1);
INSERT INTO building(id, name, level) VALUES (1845, "building_textile_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1845, 120.0, 106.31217609906774, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1845, 135.0, 119.60119811145121, 3);
INSERT INTO building(id, name, level) VALUES (1846, "building_cotton_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1846, 199.02399999999997, 256.74096, 5);
INSERT INTO building(id, name, level) VALUES (1847, "building_tobacco_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1847, 24.75, 24.75, 1);
INSERT INTO building(id, name, level) VALUES (1848, "building_maize_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1848, 3.678, 0.5401600587045584, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1848, 73.56, 10.80320117409117, 4);
INSERT INTO building(id, name, level) VALUES (1849, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1849, 29.904, 29.904, 1);
INSERT INTO building(id, name, level) VALUES (1850, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1850, 5.0, 1.966515058283401, 1);
INSERT INTO building(id, name, level) VALUES (1851, "building_university", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1851, 5.0, 0.9007311080466722, 1);
INSERT INTO building(id, name, level) VALUES (2974, "building_subsistence_farms", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2974, 21.02674, 21.02674, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2974, 5.25668, 5.25668, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2974, 5.25668, 5.25668, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2974, 5.25668, 5.25668, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2974, 5.25668, 5.25668, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2974, 5.25668, 5.25668, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2974, 5.25668, 5.25668, 29);
INSERT INTO building(id, name, level) VALUES (2975, "building_subsistence_rice_paddies", 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2975, 10.54998, 10.54998, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2975, 1.75833, 1.75833, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2975, 1.75833, 1.75833, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2975, 2.34444, 2.34444, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2975, 2.34444, 2.34444, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2975, 2.34444, 2.34444, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2975, 2.34444, 2.34444, 28);
INSERT INTO building(id, name, level) VALUES (2977, "building_subsistence_farms", 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2977, 2.9088, 2.9088, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2977, 0.7272, 0.7272, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2977, 0.7272, 0.7272, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2977, 0.7272, 0.7272, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2977, 0.7272, 0.7272, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2977, 0.7272, 0.7272, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2977, 0.7272, 0.7272, 32);
INSERT INTO building(id, name, level) VALUES (2980, "building_subsistence_farms", 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2980, 2.3634, 2.3634, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2980, 0.59085, 0.59085, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2980, 0.59085, 0.59085, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2980, 0.59085, 0.59085, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2980, 0.59085, 0.59085, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2980, 0.59085, 0.59085, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2980, 0.59085, 0.59085, 39);
INSERT INTO building(id, name, level) VALUES (2982, "building_subsistence_farms", 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2982, 6.2182956521739134, 7.15104, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2982, 1.5545739130434784, 1.78776, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2982, 1.5545739130434784, 1.78776, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2982, 1.5545739130434784, 1.78776, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2982, 1.5545739130434784, 1.78776, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2982, 1.5545739130434784, 1.78776, 55);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2982, 1.5545739130434784, 1.78776, 55);
INSERT INTO building(id, name, level) VALUES (2984, "building_subsistence_farms", 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2984, 11.807434782608697, 13.57855, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2984, 2.9518521739130437, 3.39463, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2984, 2.9518521739130437, 3.39463, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2984, 2.9518521739130437, 3.39463, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2984, 2.9518521739130437, 3.39463, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2984, 2.9518521739130437, 3.39463, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2984, 2.9518521739130437, 3.39463, 74);
INSERT INTO building(id, name, level) VALUES (2985, "building_subsistence_farms", 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2985, 3.74588, 3.74588, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2985, 0.93647, 0.93647, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2985, 0.93647, 0.93647, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2985, 0.93647, 0.93647, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2985, 0.93647, 0.93647, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2985, 0.93647, 0.93647, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2985, 0.93647, 0.93647, 37);
INSERT INTO building(id, name, level) VALUES (2986, "building_subsistence_farms", 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2986, 2.90082, 2.90082, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2986, 0.7252, 0.7252, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2986, 0.7252, 0.7252, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2986, 0.7252, 0.7252, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2986, 0.7252, 0.7252, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2986, 0.7252, 0.7252, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2986, 0.7252, 0.7252, 39);
INSERT INTO building(id, name, level) VALUES (2987, "building_subsistence_farms", 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2987, 11.70796, 11.70796, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2987, 2.92699, 2.92699, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2987, 2.92699, 2.92699, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2987, 2.92699, 2.92699, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2987, 2.92699, 2.92699, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2987, 2.92699, 2.92699, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2987, 2.92699, 2.92699, 118);
INSERT INTO building(id, name, level) VALUES (2988, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2988, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2988, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (2989, "building_subsistence_farms", 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2989, 47.13252, 47.13252, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2989, 11.78313, 11.78313, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2989, 11.78313, 11.78313, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2989, 11.78313, 11.78313, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2989, 11.78313, 11.78313, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2989, 11.78313, 11.78313, 138);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2989, 11.78313, 11.78313, 138);
INSERT INTO building(id, name, level) VALUES (2990, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2990, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2990, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (2991, "building_subsistence_farms", 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2991, 38.74744, 38.74744, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2991, 9.68686, 9.68686, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2991, 9.68686, 9.68686, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2991, 9.68686, 9.68686, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2991, 9.68686, 9.68686, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2991, 9.68686, 9.68686, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2991, 9.68686, 9.68686, 67);
INSERT INTO building(id, name, level) VALUES (2992, "building_urban_center", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2992, 6.087, 6.26961, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2992, 2.029, 2.08987, 4);
INSERT INTO building(id, name, level) VALUES (2993, "building_subsistence_farms", 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2993, 25.62816, 25.62816, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2993, 6.40704, 6.40704, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2993, 6.40704, 6.40704, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2993, 6.40704, 6.40704, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2993, 6.40704, 6.40704, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2993, 6.40704, 6.40704, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2993, 6.40704, 6.40704, 64);
INSERT INTO building(id, name, level) VALUES (2994, "building_urban_center", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2994, 45.0, 45.9, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2994, 15.0, 15.3, 3);
INSERT INTO building(id, name, level) VALUES (2995, "building_subsistence_farms", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2995, 4.65276, 4.65276, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2995, 1.16319, 1.16319, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2995, 1.16319, 1.16319, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2995, 1.16319, 1.16319, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2995, 1.16319, 1.16319, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2995, 1.16319, 1.16319, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2995, 1.16319, 1.16319, 29);
INSERT INTO building(id, name, level) VALUES (2996, "building_subsistence_farms", 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2996, 14.53896, 14.53896, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2996, 3.63474, 3.63474, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2996, 3.63474, 3.63474, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2996, 3.63474, 3.63474, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2996, 3.63474, 3.63474, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2996, 3.63474, 3.63474, 27);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2996, 3.63474, 3.63474, 27);
INSERT INTO building(id, name, level) VALUES (2997, "building_subsistence_farms", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2997, 8.88792, 8.88792, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2997, 2.22198, 2.22198, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2997, 2.22198, 2.22198, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2997, 2.22198, 2.22198, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2997, 2.22198, 2.22198, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2997, 2.22198, 2.22198, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2997, 2.22198, 2.22198, 29);
INSERT INTO building(id, name, level) VALUES (2998, "building_subsistence_farms", 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2998, 45.35244, 45.35244, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2998, 11.33811, 11.33811, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2998, 11.33811, 11.33811, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2998, 11.33811, 11.33811, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2998, 11.33811, 11.33811, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2998, 11.33811, 11.33811, 49);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2998, 11.33811, 11.33811, 49);
INSERT INTO building(id, name, level) VALUES (2999, "building_urban_center", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2999, 23.247, 23.47947, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2999, 7.749, 7.82649, 2);
INSERT INTO building(id, name, level) VALUES (3000, "building_subsistence_farms", 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3000, 10.72512, 10.72512, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3000, 2.68128, 2.68128, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3000, 2.68128, 2.68128, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3000, 2.68128, 2.68128, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3000, 2.68128, 2.68128, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3000, 2.68128, 2.68128, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3000, 2.68128, 2.68128, 48);
INSERT INTO building(id, name, level) VALUES (4014, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4014, 1.501, 0.11394409863819878, 2);
INSERT INTO building(id, name, level) VALUES (4015, "building_barracks", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4015, 2.0, 0.1518242486851416, 4);
INSERT INTO building(id, name, level) VALUES (4016, "building_barracks", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4016, 4.0, 0.3036484973702832, 4);
INSERT INTO building(id, name, level) VALUES (4017, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4017, 1.5, 0.1138681865138562, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4017, 1.125, 0.353565795631737, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4017, 0.75, 0.0788225389398368, 2);
INSERT INTO building(id, name, level) VALUES (4018, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4018, 2.0, 0.1518242486851416, 2);
INSERT INTO building(id, name, level) VALUES (4019, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4019, 1.99864, 0.1517210081960357, 3);
INSERT INTO building(id, name, level) VALUES (4020, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4020, 0.732, 0.05556767501876183, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4020, 0.732, 0.23005347769105022, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4020, 0.732, 0.07693079800528071, 1);
INSERT INTO building(id, name, level) VALUES (4021, "building_naval_base", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4021, 8.99997, 2.6470243391431345, 7);
INSERT INTO building(id, name, level) VALUES (4022, "building_naval_base", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4022, 3.0, 0.8823443875290033, 3);
INSERT INTO building(id, name, level) VALUES (4029, "building_barracks", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4029, 0.906, 0.06877638465436915, 4);
INSERT INTO building(id, name, level) VALUES (4030, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4030, 1.13, 0.085780700507105, 2);
INSERT INTO building(id, name, level) VALUES (4031, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4031, 5.7106, 0.43350377727068484, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4031, 1.6316, 0.5127804019135485, 5);
INSERT INTO building(id, name, level) VALUES (4176, "building_trade_center", 15);
INSERT INTO building(id, name, level) VALUES (4177, "building_trade_center", 14);
INSERT INTO building(id, name, level) VALUES (4494, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (4602, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (4733, "building_whaling_station", 1);
INSERT INTO building(id, name, level) VALUES (4815, "building_construction_sector", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4815, 74.415, 65.92683820343439, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4815, 223.245, 50.170026698043145, 3);
INSERT INTO building(id, name, level) VALUES (4869, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (5013, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5013, 49.59015, 43.933639663159866, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5013, 148.77046, 33.43330399372958, 2);
INSERT INTO building(id, name, level) VALUES (5058, "building_trade_center", 14);
INSERT INTO building(id, name, level) VALUES (5064, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 5064, 29.136000000000003, 33.5064, 1);
INSERT INTO building(id, name, level) VALUES (5162, "building_conscription_center", 2);
INSERT INTO building(id, name, level) VALUES (5337, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5337, 4.19105, 3.7129970469999822, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5337, 8.3821, 1.883716010596732, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5337, 10.47763, 1.1011645315629364, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5337, 2.09552, 0.30775318276687774, 2);
INSERT INTO building(id, name, level) VALUES (5341, "building_trade_center", 8);
INSERT INTO building(id, name, level) VALUES (5361, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (5463, "building_rice_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5463, 0.19776, 0.029043516370150485, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 5463, 3.9552, 0.5808703274030098, 1);
INSERT INTO building(id, name, level) VALUES (5831, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5831, 21.08425, 18.67927082430641, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5831, 63.25275, 14.214840897778892, 1);
