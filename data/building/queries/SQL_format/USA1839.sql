
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 54.230150488161144, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 93.44478555105988, 5.800394089629669);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 119.43013740475136, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 108.40781670629057, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 29.480765445212718, 3540.1971368825198);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 25.111925769652366, 734.062467421852);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 8.815581073326653, 98.72884582947043);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 30.391334775115205, 236.33682502256198);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 43.46382331921002, 317.0178305193506);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 35.09281980051205, 886.1412535345753);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 40.79877051911631, 797.7157216911846);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 49.66898834491378, 65.30744045294597);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 35.75016491181814, 686.8191073647077);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 45.79066179420722, 229.58251666666652);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 77.64780506251216, 35.52024268329789);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 44.33169075019811, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 77.17361201684844, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 48.49870551155453, 392.08191654604303);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 66.8574354212345, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 61.53519323723371, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 66.5541528208537, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 57.17942175251663, 29.377785914451263);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 96.18507624959548, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 81.78874048823471, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 49.11238997202486, 40.40761541705621);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 48.66856578526014, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 64.24046794880391, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 76.7267436870413, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 103.39841634867452, 132.03971820598068);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 43.48090688116278, 190.2050666397459);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 47.01889642592856, 199.66748161419846);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 34.58669262028266, 480.4647695622632);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 45.20899898387344, 177.23916249394892);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 87.5, 0.624691591833299);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 87.5, 51.44670130523545);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 40.76119059194121, 35.45529207982958);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 36.15505306140504, 892.5121972033039);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 119.05154467528402);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 90.84915805571737, 295.27985491773865);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 323.32752583084147, 0.016551619424030183);
INSERT INTO building(id, name, level) VALUES (16777662, "building_conscription_center", 8);
INSERT INTO building(id, name, level) VALUES (1318, "building_government_administration", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1318, 40.0, 5.450279525249231, 2);
INSERT INTO building(id, name, level) VALUES (1319, "building_white_house", 1);
INSERT INTO building(id, name, level) VALUES (1320, "building_capitol_hill", 1);
INSERT INTO building(id, name, level) VALUES (1321, "building_port", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1321, 10.0, 6.277336717567289, 2);
INSERT INTO building(id, name, level) VALUES (1322, "building_sugar_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1322, 29.004, 29.004, 1);
INSERT INTO building(id, name, level) VALUES (1323, "building_cotton_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1323, 78.584, 99.01584, 2);
INSERT INTO building(id, name, level) VALUES (1324, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1324, 199.99999999999997, 27.251397626246153, 10);
INSERT INTO building(id, name, level) VALUES (1325, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1325, 39.9996, 157.47547218840407, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1325, 79.9992, 25.498573650595446, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1325, 99.999, 11.536416936193708, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1325, 19.9998, 4.13353056407102, 2);
INSERT INTO building(id, name, level) VALUES (1326, "building_university", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1326, 30.0, 4.087709643936924, 3);
INSERT INTO building(id, name, level) VALUES (1327, "building_iron_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1327, 40.0, 7.762041575548855, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1327, 40.0, 8.267143799580035, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1327, 160.0, 32.05837075025778, 4);
INSERT INTO building(id, name, level) VALUES (1328, "building_paper_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1328, 60.0, 19.124121479161378, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1328, 20.0, 6.065400689900013, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1328, 140.0, 43.540377473671654, 2);
INSERT INTO building(id, name, level) VALUES (1329, "building_textile_mills", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1329, 164.19, 646.4039085044366, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1329, 20.52375, 17.560332065520715, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1329, 246.28499999999997, 228.50449239312425, 5);
INSERT INTO building(id, name, level) VALUES (1330, "building_furniture_manufacturies", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1330, 20.0, 78.73852347943682, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1330, 30.0, 9.562060739580689, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1330, 30.0, 3.7600531887683784, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1330, 10.0, 2.0667859498950087, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1330, 80.0, 33.01498118535606, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1330, 50.0, 20.63436324084754, 2);
INSERT INTO building(id, name, level) VALUES (1331, "building_tooling_workshops", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1331, 150.0, 47.81030369790344, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 1331, 100.0, 16.978701173404488, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1331, 400.0, 97.70447394401356, 5);
INSERT INTO building(id, name, level) VALUES (1332, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1332, 10.0, 2.0667859498950087, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1332, 70.0, 14.46750164926506, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1332, 25.0, 5.166964874737522, 2);
INSERT INTO building(id, name, level) VALUES (1333, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1333, 15.0, 2.801010736584032, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1333, 3.0, 0.6200357849685026, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1333, 120.0, 23.60475864570618, 3);
INSERT INTO building(id, name, level) VALUES (1334, "building_vineyard_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1334, 60.0, 61.2, 3);
INSERT INTO building(id, name, level) VALUES (1335, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1335, 10.0, 6.277336717567289, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1335, 100.0, 62.77336717567289, 2);
INSERT INTO building(id, name, level) VALUES (1336, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1336, 15.0, 9.416005076350933, 3);
INSERT INTO building(id, name, level) VALUES (1337, "building_central_park", 1);
INSERT INTO building(id, name, level) VALUES (1338, "building_government_administration", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1338, 40.0, 5.450279525249231, 2);
INSERT INTO building(id, name, level) VALUES (1339, "building_arms_industry", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1339, 21.956098039215686, 2.532972342446798, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1339, 21.956098039215686, 2.7518698815088025, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 1339, 65.86829411764705, 7.927263335933401, 3);
INSERT INTO building(id, name, level) VALUES (1340, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1340, 20.0, 3.8810207877744274, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1340, 20.0, 4.1335718997900175, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1340, 80.0, 16.02918537512889, 2);
INSERT INTO building(id, name, level) VALUES (1341, "building_maize_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1341, 25.0, 4.668351227640053, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1341, 5.0, 1.0333929749475044, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1341, 200.0, 39.3412644095103, 5);
INSERT INTO building(id, name, level) VALUES (1342, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1342, 20.0, 7.578760856689806, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1342, 20.0, 4.1335718997900175, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1342, 40.0, 11.712332756479821, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1342, 10.0, 2.9280831891199552, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1342, 50.0, 14.640415945599777, 2);
INSERT INTO building(id, name, level) VALUES (1343, "building_tobacco_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1343, 100.0, 103.0, 4);
INSERT INTO building(id, name, level) VALUES (1344, "building_cotton_plantation", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1344, 280.0, 366.8, 7);
INSERT INTO building(id, name, level) VALUES (1345, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1345, 20.0, 78.73852347943682, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1345, 40.0, 12.749414319440916, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1345, 20.0, 2.5067021258455853, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 1345, 5.0, 1.1064107034925104, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1345, 70.0, 29.14367058709407, 1);
INSERT INTO building(id, name, level) VALUES (1346, "building_military_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1346, 7.3804, 29.05608993438177, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1346, 14.7608, 4.704788872160087, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1346, 7.3804, 0.8514422299811404, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1346, 14.7608, 1.850046436959076, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 1346, 3.690195744680851, 0.8165744139794817, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 1346, 33.21179574468085, 11.82816802067213, 1);
INSERT INTO building(id, name, level) VALUES (1347, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1347, 15.0, 9.416005076350933, 3);
INSERT INTO building(id, name, level) VALUES (1351, "building_cotton_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1351, 40.0, 50.0, 1);
INSERT INTO building(id, name, level) VALUES (1352, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1352, 10.0, 3.789380428344903, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1352, 5.0, 1.0333929749475044, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1352, 20.0, 5.8561663782399105, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1352, 5.0, 1.4640415945599776, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1352, 15.0, 4.392124783679933, 1);
INSERT INTO building(id, name, level) VALUES (1353, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1353, 20.0, 2.7251397626246154, 1);
INSERT INTO building(id, name, level) VALUES (1354, "building_lead_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1354, 5.5304, 1.0731798682353846, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1354, 5.5304, 1.1430153017299356, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 1354, 22.1216, 4.43239033993064, 1);
INSERT INTO building(id, name, level) VALUES (1355, "building_maize_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1355, 10.0, 1.8673404910560212, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1355, 2.0, 0.4133571899790017, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1355, 50.0, 9.835316102377575, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1355, 14.0, 2.753888508665721, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1355, 10.0, 1.9670632204755152, 2);
INSERT INTO building(id, name, level) VALUES (1356, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1356, 9.4098, 3.5657311954639868, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1356, 4.7049, 0.9724021215661027, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1356, 18.8196, 5.510535438596191, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1356, 4.7049, 1.3776338596490478, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1356, 14.1147, 4.132901578947143, 1);
INSERT INTO building(id, name, level) VALUES (1357, "building_government_administration", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1357, 40.0, 5.450279525249231, 2);
INSERT INTO building(id, name, level) VALUES (1358, "building_chemical_plants", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1358, 30.0, 9.098101034850018, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1358, 10.0, 1.1536532301516724, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1358, 90.0, 18.838591087957557, 1);
INSERT INTO building(id, name, level) VALUES (1359, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1359, 10.0, 2.0667859498950087, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1359, 120.0, 24.801431398740103, 2);
INSERT INTO building(id, name, level) VALUES (1360, "building_maize_farm", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1360, 39.99999999999999, 7.469361964224084, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1360, 8.0, 1.653428759916007, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1360, 319.99999999999994, 62.94602305521647, 8);
INSERT INTO building(id, name, level) VALUES (1361, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1361, 5.0, 1.0333929749475044, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1361, 60.0, 12.400715699370052, 1);
INSERT INTO building(id, name, level) VALUES (1362, "building_sulfur_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1362, 39.94759223300971, 7.751871793892346, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1362, 39.94759223300971, 8.256312235931944, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 1362, 159.79039805825244, 32.01637389551704, 4);
INSERT INTO building(id, name, level) VALUES (1363, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1363, 10.0, 3.789380428344903, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1363, 20.0, 7.578760856689806, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1363, 5.0, 1.8946902141724515, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1363, 5.0, 1.8946902141724515, 1);
INSERT INTO building(id, name, level) VALUES (1364, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1364, 20.0, 2.7251397626246154, 1);
INSERT INTO building(id, name, level) VALUES (1365, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1365, 19.9998, 78.73773609420203, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1365, 39.9996, 12.749286825297723, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1365, 49.9995, 5.768208468096854, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1365, 9.9999, 2.06676528203551, 1);
INSERT INTO building(id, name, level) VALUES (1366, "building_glassworks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1366, 20.0, 6.374707159720458, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1366, 5.0, 4.278051541633648, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1366, 10.0, 3.1074784367076633, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1366, 20.0, 9.900623399890126, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1366, 20.0, 9.900623399890126, 1);
INSERT INTO building(id, name, level) VALUES (1367, "building_furniture_manufacturies", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1367, 20.0, 78.73852347943682, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1367, 30.0, 9.562060739580689, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1367, 30.0, 3.7600531887683784, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1367, 10.0, 2.0667859498950087, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1367, 80.0, 33.01498118535606, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1367, 50.0, 20.63436324084754, 2);
INSERT INTO building(id, name, level) VALUES (1368, "building_chemical_plants", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1368, 30.0, 9.098101034850018, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1368, 10.0, 1.1536532301516724, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1368, 90.0, 18.838591087957557, 1);
INSERT INTO building(id, name, level) VALUES (1369, "building_coal_mine", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1369, 30.0, 6.200357849685027, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1369, 120.0, 24.801431398740107, 3);
INSERT INTO building(id, name, level) VALUES (1370, "building_maize_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1370, 20.0, 3.7346809821120424, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1370, 4.0, 0.8267143799580035, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1370, 100.0, 19.67063220475515, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1370, 28.0, 5.507777017331442, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1370, 20.0, 3.9341264409510304, 4);
INSERT INTO building(id, name, level) VALUES (1371, "building_vineyard_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1371, 60.0, 61.2, 3);
INSERT INTO building(id, name, level) VALUES (1372, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1372, 14.898, 5.645418962148236, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1372, 7.449, 1.539548854076792, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1372, 29.796, 8.72451667030182, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1372, 7.449, 2.181129167575455, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1372, 22.347, 6.543387502726366, 2);
INSERT INTO building(id, name, level) VALUES (1373, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1373, 20.0, 2.7251397626246154, 1);
INSERT INTO building(id, name, level) VALUES (1374, "building_university", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1374, 10.0, 1.3625698813123077, 1);
INSERT INTO building(id, name, level) VALUES (1375, "building_food_industry", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1375, 80.0, 30.315043426759225, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1375, 30.0, 16.10018835147721, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1375, 130.0, 59.51471421244249, 2);
INSERT INTO building(id, name, level) VALUES (1376, "building_coal_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1376, 19.999999999999996, 4.133571899790017, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1376, 79.99999999999999, 16.534287599160066, 2);
INSERT INTO building(id, name, level) VALUES (1377, "building_tobacco_plantation", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1377, 193.17, 206.6919, 8);
INSERT INTO building(id, name, level) VALUES (1378, "building_maize_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1378, 19.987592233009707, 3.7323640295415865, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1378, 3.997514563106796, 0.8262006933529811, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1378, 99.93799999999999, 19.658436412788202, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1378, 27.98263106796116, 5.5043604385921885, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1378, 19.987592233009707, 3.9316857547415465, 4);
INSERT INTO building(id, name, level) VALUES (1379, "building_vineyard_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1379, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (1380, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1380, 9.9352, 3.7648252431692284, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1380, 4.9676, 1.0266965884698445, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1380, 19.8704, 5.818218420108917, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1380, 4.9676, 1.4545546050272293, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1380, 14.9028, 4.363663815081688, 1);
INSERT INTO building(id, name, level) VALUES (1381, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1381, 20.0, 2.7251397626246154, 1);
INSERT INTO building(id, name, level) VALUES (1382, "building_food_industry", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1382, 79.96719801980198, 30.302613508581896, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1382, 79.96719801980198, 42.916231668622906, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1382, 69.97129702970297, 32.033244198436144, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1382, 119.95079207920791, 54.91413161653976, 2);
INSERT INTO building(id, name, level) VALUES (1383, "building_maize_farm", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1383, 49.895495412844035, 9.317187890570363, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1383, 9.979091743119264, 2.0624646607392187, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1383, 249.47749541284404, 49.07380055629545, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1383, 69.85369724770642, 13.740663867019501, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1383, 49.895495412844035, 9.814759389401027, 10);
INSERT INTO building(id, name, level) VALUES (1384, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1384, 9.4764, 3.5909684691167643, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1384, 4.7382, 0.9792845187792529, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1384, 18.9528, 5.549537506675271, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1384, 4.7382, 1.3873843766688176, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1384, 14.2146, 4.162153130006453, 1);
INSERT INTO building(id, name, level) VALUES (1385, "building_tobacco_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1385, 249.905, 272.39645, 10);
INSERT INTO building(id, name, level) VALUES (1386, "building_cotton_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1386, 119.53559842519685, 151.81021, 3);
INSERT INTO building(id, name, level) VALUES (1387, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1387, 20.0, 2.7251397626246154, 1);
INSERT INTO building(id, name, level) VALUES (1388, "building_cotton_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1388, 157.968, 202.19904, 4);
INSERT INTO building(id, name, level) VALUES (1389, "building_maize_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1389, 20.0, 3.7346809821120424, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1389, 4.0, 0.8267143799580035, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1389, 160.0, 31.473011527608243, 4);
INSERT INTO building(id, name, level) VALUES (1390, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1390, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (1391, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1391, 20.0, 2.7251397626246154, 1);
INSERT INTO building(id, name, level) VALUES (1392, "building_maize_farm", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1392, 28.89869523809524, 5.396370375678307, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1392, 5.779733333333333, 1.1945471647473178, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1392, 144.49349523809522, 28.42278400808111, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1392, 40.458171428571426, 7.958378098483617, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1392, 28.89869523809524, 5.684556052258805, 6);
INSERT INTO building(id, name, level) VALUES (1393, "building_sugar_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1393, 29.172, 29.172, 1);
INSERT INTO building(id, name, level) VALUES (1394, "building_cotton_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1394, 400.0, 536.0, 10);
INSERT INTO building(id, name, level) VALUES (1395, "building_tobacco_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1395, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (1396, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1396, 5.0, 3.1386683587836446, 1);
INSERT INTO building(id, name, level) VALUES (1397, "building_government_administration", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1397, 40.0, 5.450279525249231, 2);
INSERT INTO building(id, name, level) VALUES (1398, "building_maize_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1398, 23.700999999999997, 4.425783697851875, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1398, 4.740192307692308, 0.979696286133886, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1398, 189.60799999999998, 37.29709231079214, 5);
INSERT INTO building(id, name, level) VALUES (1399, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1399, 9.3019, 3.5248437806421453, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1399, 4.65095, 0.9612518113664191, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1399, 18.6038, 5.447347403374984, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1399, 4.65095, 1.361836850843746, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1399, 13.95285, 4.085510552531238, 1);
INSERT INTO building(id, name, level) VALUES (1400, "building_sugar_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1400, 29.538, 29.538, 1);
INSERT INTO building(id, name, level) VALUES (1401, "building_cotton_plantation", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1401, 319.62239393939393, 421.90156, 8);
INSERT INTO building(id, name, level) VALUES (1402, "building_tobacco_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1402, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (1403, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1403, 5.0, 3.1386683587836446, 1);
INSERT INTO building(id, name, level) VALUES (1404, "building_coal_mine", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1404, 30.0, 6.200357849685027, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1404, 120.0, 24.801431398740107, 3);
INSERT INTO building(id, name, level) VALUES (1405, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1405, 4.9891, 1.0311401782621188, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1405, 34.9237, 7.217981247834832, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1405, 12.47275, 2.5778504456552973, 1);
INSERT INTO building(id, name, level) VALUES (1406, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1406, 20.0, 2.7251397626246154, 1);
INSERT INTO building(id, name, level) VALUES (1407, "building_glassworks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1407, 20.0, 6.374707159720458, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1407, 5.0, 4.278051541633648, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1407, 10.0, 3.1074784367076633, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1407, 20.0, 9.900623399890126, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1407, 20.0, 9.900623399890126, 1);
INSERT INTO building(id, name, level) VALUES (1408, "building_fishing_wharf", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1408, 15.0, 9.416005076350933, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1408, 150.0, 94.16005076350933, 3);
INSERT INTO building(id, name, level) VALUES (1409, "building_tobacco_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1409, 100.0, 103.0, 4);
INSERT INTO building(id, name, level) VALUES (1410, "building_port", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1410, 10.0, 6.277336717567289, 2);
INSERT INTO building(id, name, level) VALUES (1412, "building_fishing_wharf", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1412, 5.0, 3.1386683587836446, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1412, 50.0, 31.386683587836444, 1);
INSERT INTO building(id, name, level) VALUES (1413, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1413, 9.9941, 3.7871446938921793, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1413, 9.9941, 2.065566546184571, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1413, 19.9882, 5.85271124007675, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1413, 4.99705, 1.4631778100191875, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1413, 24.98525, 7.315889050095938, 1);
INSERT INTO building(id, name, level) VALUES (1414, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1414, 60.0, 8.175419287873847, 3);
INSERT INTO building(id, name, level) VALUES (1415, "building_steel_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1415, 50.1, 9.72195707337494, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1415, 66.8, 7.706403577413172, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 1415, 108.55, 16.79357306947105, 2);
INSERT INTO building(id, name, level) VALUES (1416, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1416, 39.9996, 157.47547218840407, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1416, 79.9992, 25.498573650595446, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1416, 99.999, 11.536416936193708, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1416, 19.9998, 4.13353056407102, 2);
INSERT INTO building(id, name, level) VALUES (1417, "building_textile_mills", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1417, 80.0, 314.9540939177473, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1417, 10.0, 8.556103083267296, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1417, 120.0, 111.33661849960377, 2);
INSERT INTO building(id, name, level) VALUES (1418, "building_furniture_manufacturies", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1418, 40.0, 157.47704695887364, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1418, 59.99999999999999, 19.124121479161374, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1418, 59.99999999999999, 7.520106377536756, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1418, 20.0, 4.1335718997900175, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1418, 160.0, 66.02996237071211, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1418, 100.0, 41.268726481695076, 4);
INSERT INTO building(id, name, level) VALUES (1419, "building_munition_plants", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1419, 13.3606, 4.151777640147641, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 1419, 13.3606, 4.285491683894172, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 1419, 33.4015, 10.546586655052266, 1);
INSERT INTO building(id, name, level) VALUES (1420, "building_glassworks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1420, 20.0, 6.374707159720458, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1420, 5.0, 4.278051541633648, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1420, 10.0, 3.1074784367076633, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1420, 20.0, 9.900623399890126, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1420, 20.0, 9.900623399890126, 1);
INSERT INTO building(id, name, level) VALUES (1421, "building_coal_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1421, 40.00000000000001, 8.267143799580037, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1421, 160.00000000000003, 33.06857519832015, 4);
INSERT INTO building(id, name, level) VALUES (1422, "building_maize_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1422, 15.0, 2.801010736584032, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1422, 3.0, 0.6200357849685026, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1422, 120.0, 23.60475864570618, 3);
INSERT INTO building(id, name, level) VALUES (1423, "building_tooling_workshops", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1423, 60.0, 19.124121479161378, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 1423, 40.0, 6.791480469361794, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1423, 160.0, 39.08178957760542, 2);
INSERT INTO building(id, name, level) VALUES (1424, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1424, 20.0, 3.8810207877744274, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1424, 20.0, 4.1335718997900175, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1424, 80.0, 16.02918537512889, 2);
INSERT INTO building(id, name, level) VALUES (1425, "building_vineyard_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1425, 40.0, 40.4, 2);
INSERT INTO building(id, name, level) VALUES (1426, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1426, 30.0, 11.36814128503471, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1426, 15.0, 3.1001789248425133, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1426, 60.0, 17.568499134719737, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1426, 15.0, 4.392124783679934, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1426, 45.0, 13.176374351039803, 3);
INSERT INTO building(id, name, level) VALUES (1427, "building_logging_camp", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1427, 25.0, 5.166964874737522, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1427, 175.0, 36.16875412316265, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1427, 62.5, 12.917412186843803, 5);
INSERT INTO building(id, name, level) VALUES (1428, "building_explosives_factory", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1428, 6.2666, 1.900471998166371, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1428, 6.2666, 1.1701875921251663, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 1428, 15.6665, 3.8383244878644214, 1);
INSERT INTO building(id, name, level) VALUES (1429, "building_paper_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1429, 90.0, 28.686182218742065, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 1429, 30.0, 9.098101034850018, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 1429, 209.99999999999997, 65.31056621050747, 3);
INSERT INTO building(id, name, level) VALUES (1430, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1430, 20.0, 2.7251397626246154, 1);
INSERT INTO building(id, name, level) VALUES (1431, "building_fishing_wharf", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1431, 3.5239, 2.212070685903537, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1431, 35.239, 22.12070685903537, 1);
INSERT INTO building(id, name, level) VALUES (1432, "building_maize_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1432, 4.0344, 0.7533598477116412, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1432, 0.80688, 0.1667648247251285, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1432, 20.172, 3.967959928343209, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1432, 5.64816, 1.1110287799360985, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1432, 4.0344, 0.7935919856686418, 1);
INSERT INTO building(id, name, level) VALUES (1433, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1433, 10.0, 2.0667859498950087, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1433, 120.0, 24.801431398740103, 2);
INSERT INTO building(id, name, level) VALUES (1434, "building_lead_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1434, 16.25639603960396, 3.154570548199842, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1434, 16.25639603960396, 3.3598490930582328, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 1434, 65.02559405940593, 13.02884126632622, 2);
INSERT INTO building(id, name, level) VALUES (1435, "building_maize_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1435, 10.0, 1.8673404910560212, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1435, 2.0, 0.4133571899790017, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1435, 50.0, 9.835316102377575, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1435, 14.0, 2.753888508665721, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1435, 10.0, 1.9670632204755152, 2);
INSERT INTO building(id, name, level) VALUES (1437, "building_glassworks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1437, 17.047, 5.433481647587733, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1437, 4.26175, 3.6463972315114397, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1437, 8.5235, 2.648659245527777, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1437, 17.047, 8.438796354896349, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1437, 17.047, 8.438796354896349, 1);
INSERT INTO building(id, name, level) VALUES (1438, "building_fishing_wharf", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1438, 3.80865, 2.3908178489362655, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1438, 38.0865, 23.908178489362655, 1);
INSERT INTO building(id, name, level) VALUES (1439, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1439, 20.0, 2.7251397626246154, 1);
INSERT INTO building(id, name, level) VALUES (1440, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1440, 17.88259405940594, 3.4701359641942964, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1440, 17.88259405940594, 3.695949414965614, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1440, 71.53039603960396, 14.332174725939964, 2);
INSERT INTO building(id, name, level) VALUES (1441, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1441, 5.0, 3.1386683587836446, 1);
INSERT INTO building(id, name, level) VALUES (1442, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1442, 19.752, 2.6913480295680703, 1);
INSERT INTO building(id, name, level) VALUES (1443, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1443, 18.27981, 71.9662624442322, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1443, 36.55963, 11.652846755886543, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 1443, 45.69954, 5.272142193744556, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1443, 9.1399, 1.8890216903445394, 1);
INSERT INTO building(id, name, level) VALUES (1444, "building_university", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1444, 20.0, 2.7251397626246154, 2);
INSERT INTO building(id, name, level) VALUES (1445, "building_textile_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1445, 57.078, 224.71187215796476, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1445, 7.1347450980392155, 6.104561453165956, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1445, 85.617, 79.4358938840048, 3);
INSERT INTO building(id, name, level) VALUES (1446, "building_glassworks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1446, 20.0, 6.374707159720458, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1446, 5.0, 4.278051541633648, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 1446, 10.0, 3.1074784367076633, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 1446, 20.0, 9.900623399890126, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 1446, 20.0, 9.900623399890126, 1);
INSERT INTO building(id, name, level) VALUES (1447, "building_shipyards", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1447, 19.44, 76.53384482201258, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1447, 38.88, 12.392430718496572, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1447, 34.02, 22.43168843934225, 2);
INSERT INTO building(id, name, level) VALUES (1448, "building_fishing_wharf", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1448, 15.0, 9.416005076350933, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1448, 150.0, 94.16005076350933, 3);
INSERT INTO building(id, name, level) VALUES (1449, "building_whaling_station", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1449, 5.0, 3.1386683587836446, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (28, 1449, 20.0, 12.554673435134578, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1449, 10.0, 6.277336717567289, 1);
INSERT INTO building(id, name, level) VALUES (1450, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1450, 10.0, 1.8673404910560212, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1450, 2.0, 0.4133571899790017, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1450, 50.0, 9.835316102377575, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1450, 14.0, 2.753888508665721, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1450, 10.0, 1.9670632204755152, 2);
INSERT INTO building(id, name, level) VALUES (1451, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1451, 15.0, 9.416005076350933, 3);
INSERT INTO building(id, name, level) VALUES (1452, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 1452, 5.0, 1.584824210099637, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1452, 5.0, 1.0333929749475044, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1452, 60.0, 15.709303110282848, 1);
INSERT INTO building(id, name, level) VALUES (1453, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1453, 20.0, 2.7251397626246154, 1);
INSERT INTO building(id, name, level) VALUES (1454, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1454, 5.0, 1.0333929749475044, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1454, 60.0, 12.400715699370052, 1);
INSERT INTO building(id, name, level) VALUES (1455, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1455, 4.1705, 1.5803611076412416, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1455, 4.1705, 0.8619530804037133, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1455, 8.341, 2.442314188044955, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1455, 2.08525, 0.6105785470112387, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1455, 10.42625, 3.0528927350561936, 1);
INSERT INTO building(id, name, level) VALUES (1456, "building_food_industry", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1456, 49.29279207920792, 18.678914156342515, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1456, 49.29279207920792, 26.454107894848384, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1456, 43.131198019801985, 19.74569941375985, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1456, 73.93919801980198, 33.849771071120834, 2);
INSERT INTO building(id, name, level) VALUES (1457, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1457, 5.0, 1.0333929749475044, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1457, 60.0, 12.400715699370052, 1);
INSERT INTO building(id, name, level) VALUES (1458, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1458, 3.0137, 1.1420055796903035, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1458, 3.0137, 0.6228672817198588, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1458, 6.0274, 1.7648728614101623, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1458, 1.50685, 0.4412182153525406, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1458, 7.53425, 2.206091076762703, 1);
INSERT INTO building(id, name, level) VALUES (1459, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1459, 20.0, 2.7251397626246154, 1);
INSERT INTO building(id, name, level) VALUES (1460, "building_furniture_manufacturies", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1460, 10.0, 39.36926173971841, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1460, 15.0, 4.7810303697903445, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1460, 15.0, 1.8800265943841892, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1460, 5.0, 1.0333929749475044, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1460, 40.0, 16.50749059267803, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1460, 25.0, 10.31718162042377, 1);
INSERT INTO building(id, name, level) VALUES (1461, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1461, 14.047794117647058, 2.9033783509370688, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1461, 98.33459803921568, 20.323656561602423, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1461, 35.1195, 7.2584489167337765, 3);
INSERT INTO building(id, name, level) VALUES (1462, "building_shipyards", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1462, 20.212793248945147, 79.57627479085347, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1462, 40.42559493670886, 12.885066473949855, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1462, 20.212793248945147, 2.533372590320405, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 1462, 5.053198312236287, 1.1181825399057033, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 1462, 70.74479324894514, 29.453756431419055, 2);
INSERT INTO building(id, name, level) VALUES (1463, "building_fishing_wharf", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1463, 14.872794117647057, 9.336153660752466, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1463, 148.728, 93.36157353303479, 3);
INSERT INTO building(id, name, level) VALUES (1464, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1464, 4.946, 3.1047707405087808, 1);
INSERT INTO building(id, name, level) VALUES (1465, "building_government_administration", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1465, 40.0, 5.450279525249231, 2);
INSERT INTO building(id, name, level) VALUES (1466, "building_food_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1466, 40.0, 15.157521713379612, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1466, 40.0, 21.46691780196961, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1466, 35.0, 16.023192287965283, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1466, 60.0, 27.468329636511914, 1);
INSERT INTO building(id, name, level) VALUES (1467, "building_coal_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1467, 19.780396396396394, 4.088184535542593, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 1467, 79.12159459459458, 16.352740004139697, 2);
INSERT INTO building(id, name, level) VALUES (1468, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1468, 4.93475, 1.0199071966244395, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1468, 59.217, 12.238886359493273, 1);
INSERT INTO building(id, name, level) VALUES (1469, "building_maize_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1469, 23.8065, 4.445484140032517, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1469, 4.761298076923077, 0.9840583968646741, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1469, 119.0325, 23.414445279125175, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1469, 33.32909615384616, 6.556043921592273, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1469, 23.8065, 4.682889055825035, 5);
INSERT INTO building(id, name, level) VALUES (1470, "building_cotton_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1470, 399.8159999999999, 535.75344, 10);
INSERT INTO building(id, name, level) VALUES (1471, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1471, 5.0, 3.1386683587836446, 1);
INSERT INTO building(id, name, level) VALUES (1472, "building_government_administration", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1472, 40.0, 5.450279525249231, 2);
INSERT INTO building(id, name, level) VALUES (1473, "building_cotton_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1473, 390.6479999999999, 523.46832, 10);
INSERT INTO building(id, name, level) VALUES (1474, "building_tobacco_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1474, 122.085, 126.9684, 5);
INSERT INTO building(id, name, level) VALUES (1475, "building_dye_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 1475, 48.809999999999995, 49.2981, 2);
INSERT INTO building(id, name, level) VALUES (1476, "building_maize_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1476, 22.73425, 4.245258555879035, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1476, 4.546846153846154, 0.939735774710339, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1476, 181.874, 35.775765616076384, 5);
INSERT INTO building(id, name, level) VALUES (1477, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1477, 4.64, 2.912684236951222, 1);
INSERT INTO building(id, name, level) VALUES (16778945, "building_naval_base", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 16778945, 0.065, 0.018193581948468242, 2);
INSERT INTO building(id, name, level) VALUES (2690, "building_subsistence_farms", 117);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2690, 25.89912, 25.89912, 117);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2690, 6.47478, 6.47478, 117);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2690, 6.47478, 6.47478, 117);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2690, 6.47478, 6.47478, 117);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2690, 6.47478, 6.47478, 117);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2690, 6.47478, 6.47478, 117);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2690, 6.47478, 6.47478, 117);
INSERT INTO building(id, name, level) VALUES (2691, "building_subsistence_farms", 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2691, 113.27952, 113.27952, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2691, 28.31988, 28.31988, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2691, 28.31988, 28.31988, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2691, 28.31988, 28.31988, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2691, 28.31988, 28.31988, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2691, 28.31988, 28.31988, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2691, 28.31988, 28.31988, 114);
INSERT INTO building(id, name, level) VALUES (2692, "building_urban_center", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2692, 9.954192660550458, 3.1727531611223925, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2692, 9.954192660550458, 1.9316214320553977, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2692, 9.954192660550458, 7.175531035545295, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2692, 218.9923944954128, 90.05270541157263, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2692, 49.771, 20.466524471621618, 10);
INSERT INTO building(id, name, level) VALUES (2693, "building_subsistence_farms", 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2693, 56.35008, 56.35008, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2693, 14.08752, 14.08752, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2693, 14.08752, 14.08752, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2693, 14.08752, 14.08752, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2693, 14.08752, 14.08752, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2693, 14.08752, 14.08752, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2693, 14.08752, 14.08752, 162);
INSERT INTO building(id, name, level) VALUES (2694, "building_urban_center", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2694, 3.0, 0.9562060739580688, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2694, 3.0, 0.5821531181661641, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 2694, 3.0, 0.6638464220955063, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2694, 3.0, 2.162565447617676, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2694, 65.99999999999999, 24.00624084010578, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2694, 24.0, 8.72954212367483, 3);
INSERT INTO building(id, name, level) VALUES (2739, "building_subsistence_farms", 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2739, 0.04958, 0.04958, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2739, 0.01239, 0.01239, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2739, 0.01239, 0.01239, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2739, 0.01239, 0.01239, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2739, 0.01239, 0.01239, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2739, 0.01239, 0.01239, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2739, 0.01239, 0.01239, 37);
INSERT INTO building(id, name, level) VALUES (2740, "building_subsistence_farms", 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2740, 5.002, 5.002, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2740, 1.2505, 1.2505, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2740, 1.2505, 1.2505, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2740, 1.2505, 1.2505, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2740, 1.2505, 1.2505, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2740, 1.2505, 1.2505, 122);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2740, 1.2505, 1.2505, 122);
INSERT INTO building(id, name, level) VALUES (2743, "building_subsistence_farms", 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2743, 4.53832, 4.53832, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2743, 1.13458, 1.13458, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2743, 1.13458, 1.13458, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2743, 1.13458, 1.13458, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2743, 1.13458, 1.13458, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2743, 1.13458, 1.13458, 142);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2743, 1.13458, 1.13458, 142);
INSERT INTO building(id, name, level) VALUES (2747, "building_subsistence_farms", 160);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2747, 6.9536, 8.34432, 160);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2747, 1.7384, 2.08608, 160);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2747, 1.7384, 2.08608, 160);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2747, 1.7384, 2.08608, 160);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2747, 1.7384, 2.08608, 160);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2747, 1.7384, 2.08608, 160);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2747, 1.7384, 2.08608, 160);
INSERT INTO building(id, name, level) VALUES (2750, "building_subsistence_farms", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2750, 1.0090000000000001, 1.2108, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2750, 0.25225000000000003, 0.3027, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2750, 0.25225000000000003, 0.3027, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2750, 0.25225000000000003, 0.3027, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2750, 0.25225000000000003, 0.3027, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2750, 0.25225000000000003, 0.3027, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2750, 0.25225000000000003, 0.3027, 50);
INSERT INTO building(id, name, level) VALUES (2751, "building_subsistence_farms", 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2751, 1.0046333333333333, 1.20556, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2751, 0.2511583333333333, 0.30139, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2751, 0.2511583333333333, 0.30139, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2751, 0.2511583333333333, 0.30139, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2751, 0.2511583333333333, 0.30139, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2751, 0.2511583333333333, 0.30139, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2751, 0.2511583333333333, 0.30139, 69);
INSERT INTO building(id, name, level) VALUES (2754, "building_subsistence_farms", 183);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2754, 6.573358333333333, 7.88803, 183);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2754, 1.6433333333333333, 1.972, 183);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2754, 1.6433333333333333, 1.972, 183);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2754, 1.6433333333333333, 1.972, 183);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2754, 1.6433333333333333, 1.972, 183);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2754, 1.6433333333333333, 1.972, 183);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2754, 1.6433333333333333, 1.972, 183);
INSERT INTO building(id, name, level) VALUES (2759, "building_subsistence_farms", 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2759, 18.41944, 18.41944, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2759, 4.60486, 4.60486, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2759, 4.60486, 4.60486, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2759, 4.60486, 4.60486, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2759, 4.60486, 4.60486, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2759, 4.60486, 4.60486, 178);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2759, 4.60486, 4.60486, 178);
INSERT INTO building(id, name, level) VALUES (2760, "building_subsistence_farms", 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2760, 34.65382, 34.65382, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2760, 8.66345, 8.66345, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2760, 8.66345, 8.66345, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2760, 8.66345, 8.66345, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2760, 8.66345, 8.66345, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2760, 8.66345, 8.66345, 227);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2760, 8.66345, 8.66345, 227);
INSERT INTO building(id, name, level) VALUES (2761, "building_subsistence_farms", 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2761, 15.48, 15.48, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2761, 3.87, 3.87, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2761, 3.87, 3.87, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2761, 3.87, 3.87, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2761, 3.87, 3.87, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2761, 3.87, 3.87, 240);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2761, 3.87, 3.87, 240);
INSERT INTO building(id, name, level) VALUES (2762, "building_subsistence_farms", 133);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2762, 16.88568, 16.88568, 133);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2762, 4.22142, 4.22142, 133);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2762, 4.22142, 4.22142, 133);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2762, 4.22142, 4.22142, 133);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2762, 4.22142, 4.22142, 133);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2762, 4.22142, 4.22142, 133);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2762, 4.22142, 4.22142, 133);
INSERT INTO building(id, name, level) VALUES (2764, "building_subsistence_farms", 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2764, 13.8744, 13.8744, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2764, 3.4686, 3.4686, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2764, 3.4686, 3.4686, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2764, 3.4686, 3.4686, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2764, 3.4686, 3.4686, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2764, 3.4686, 3.4686, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2764, 3.4686, 3.4686, 180);
INSERT INTO building(id, name, level) VALUES (2765, "building_subsistence_farms", 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2765, 44.6896, 44.6896, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2765, 11.1724, 11.1724, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2765, 11.1724, 11.1724, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2765, 11.1724, 11.1724, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2765, 11.1724, 11.1724, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2765, 11.1724, 11.1724, 212);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2765, 11.1724, 11.1724, 212);
INSERT INTO building(id, name, level) VALUES (2766, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2766, 1.0, 0.3187353579860229, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2766, 1.0, 0.19405103938872134, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 2766, 1.0, 0.22128214069850208, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2766, 1.0, 0.720855149205892, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2766, 22.0, 8.002080280035262, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2766, 8.0, 2.909847374558277, 1);
INSERT INTO building(id, name, level) VALUES (2767, "building_subsistence_farms", 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2767, 21.73418, 21.73418, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2767, 5.43354, 5.43354, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2767, 5.43354, 5.43354, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2767, 5.43354, 5.43354, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2767, 5.43354, 5.43354, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2767, 5.43354, 5.43354, 179);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2767, 5.43354, 5.43354, 179);
INSERT INTO building(id, name, level) VALUES (2768, "building_subsistence_farms", 171);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2768, 100.74636, 100.74636, 171);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2768, 25.18659, 25.18659, 171);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2768, 25.18659, 25.18659, 171);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2768, 25.18659, 25.18659, 171);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2768, 25.18659, 25.18659, 171);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2768, 25.18659, 25.18659, 171);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2768, 25.18659, 25.18659, 171);
INSERT INTO building(id, name, level) VALUES (2769, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2769, 1.0, 0.3187353579860229, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2769, 1.0, 0.19405103938872134, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 2769, 1.0, 0.22128214069850208, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2769, 1.0, 0.720855149205892, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2769, 22.0, 8.002080280035262, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2769, 8.0, 2.909847374558277, 1);
INSERT INTO building(id, name, level) VALUES (2770, "building_subsistence_farms", 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2770, 48.5826, 48.5826, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2770, 12.14565, 12.14565, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2770, 12.14565, 12.14565, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2770, 12.14565, 12.14565, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2770, 12.14565, 12.14565, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2770, 12.14565, 12.14565, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2770, 12.14565, 12.14565, 165);
INSERT INTO building(id, name, level) VALUES (2771, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2771, 1.0, 0.3187353579860229, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2771, 1.0, 0.19405103938872134, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2771, 1.0, 0.720855149205892, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2771, 22.0, 9.046704674924667, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2771, 5.0, 2.0560692443010606, 1);
INSERT INTO building(id, name, level) VALUES (2772, "building_subsistence_farms", 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2772, 39.9516, 39.9516, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2772, 9.9879, 9.9879, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2772, 9.9879, 9.9879, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2772, 9.9879, 9.9879, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2772, 9.9879, 9.9879, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2772, 9.9879, 9.9879, 156);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2772, 9.9879, 9.9879, 156);
INSERT INTO building(id, name, level) VALUES (2773, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2773, 1.0, 0.3187353579860229, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2773, 1.0, 0.19405103938872134, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2773, 1.0, 0.720855149205892, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2773, 22.0, 9.046704674924667, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2773, 5.0, 2.0560692443010606, 1);
INSERT INTO building(id, name, level) VALUES (2774, "building_subsistence_farms", 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2774, 16.569, 16.569, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2774, 4.14225, 4.14225, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2774, 4.14225, 4.14225, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2774, 4.14225, 4.14225, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2774, 4.14225, 4.14225, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2774, 4.14225, 4.14225, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2774, 4.14225, 4.14225, 150);
INSERT INTO building(id, name, level) VALUES (2775, "building_subsistence_farms", 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2775, 40.296, 40.296, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2775, 10.074, 10.074, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2775, 10.074, 10.074, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2775, 10.074, 10.074, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2775, 10.074, 10.074, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2775, 10.074, 10.074, 150);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2775, 10.074, 10.074, 150);
INSERT INTO building(id, name, level) VALUES (2776, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2776, 1.0, 0.3187353579860229, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2776, 1.0, 0.19405103938872134, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2776, 1.0, 0.720855149205892, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2776, 22.0, 9.046704674924667, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2776, 5.0, 2.0560692443010606, 1);
INSERT INTO building(id, name, level) VALUES (2777, "building_subsistence_farms", 99);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2777, 14.69754, 14.69754, 99);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2777, 3.67438, 3.67438, 99);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2777, 3.67438, 3.67438, 99);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2777, 3.67438, 3.67438, 99);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2777, 3.67438, 3.67438, 99);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2777, 3.67438, 3.67438, 99);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2777, 3.67438, 3.67438, 99);
INSERT INTO building(id, name, level) VALUES (2778, "building_subsistence_farms", 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2778, 45.5058, 45.5058, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2778, 11.37645, 11.37645, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2778, 11.37645, 11.37645, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2778, 11.37645, 11.37645, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2778, 11.37645, 11.37645, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2778, 11.37645, 11.37645, 162);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2778, 11.37645, 11.37645, 162);
INSERT INTO building(id, name, level) VALUES (2779, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2779, 1.0, 0.3187353579860229, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2779, 1.0, 0.19405103938872134, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2779, 1.0, 0.720855149205892, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2779, 22.0, 9.046704674924667, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2779, 5.0, 2.0560692443010606, 1);
INSERT INTO building(id, name, level) VALUES (2780, "building_subsistence_farms", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2780, 2.0, 2.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2780, 0.5, 0.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2780, 0.5, 0.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2780, 0.5, 0.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2780, 0.5, 0.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2780, 0.5, 0.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2780, 0.5, 0.5, 1);
INSERT INTO building(id, name, level) VALUES (2782, "building_subsistence_farms", 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2782, 15.69, 15.69, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2782, 3.9225, 3.9225, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2782, 3.9225, 3.9225, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2782, 3.9225, 3.9225, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2782, 3.9225, 3.9225, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2782, 3.9225, 3.9225, 50);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2782, 3.9225, 3.9225, 50);
INSERT INTO building(id, name, level) VALUES (2783, "building_subsistence_farms", 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2783, 16.63326, 16.63326, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2783, 4.15831, 4.15831, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2783, 4.15831, 4.15831, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2783, 4.15831, 4.15831, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2783, 4.15831, 4.15831, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2783, 4.15831, 4.15831, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2783, 4.15831, 4.15831, 21);
INSERT INTO building(id, name, level) VALUES (2784, "building_subsistence_farms", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2784, 5.94552, 5.94552, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2784, 1.48638, 1.48638, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2784, 1.48638, 1.48638, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2784, 1.48638, 1.48638, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2784, 1.48638, 1.48638, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2784, 1.48638, 1.48638, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2784, 1.48638, 1.48638, 14);
INSERT INTO building(id, name, level) VALUES (2785, "building_subsistence_farms", 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2785, 75.56648, 75.56648, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2785, 18.89162, 18.89162, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2785, 18.89162, 18.89162, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2785, 18.89162, 18.89162, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2785, 18.89162, 18.89162, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2785, 18.89162, 18.89162, 172);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2785, 18.89162, 18.89162, 172);
INSERT INTO building(id, name, level) VALUES (2786, "building_urban_center", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2786, 4.0, 1.2749414319440917, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2786, 4.0, 0.7762041575548854, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 2786, 4.0, 0.8851285627940083, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2786, 4.0, 2.883420596823568, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2786, 88.0, 32.00832112014105, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2786, 32.0, 11.639389498233108, 4);
INSERT INTO building(id, name, level) VALUES (2787, "building_subsistence_farms", 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2787, 15.86286, 15.86286, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2787, 3.96571, 3.96571, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2787, 3.96571, 3.96571, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2787, 3.96571, 3.96571, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2787, 3.96571, 3.96571, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2787, 3.96571, 3.96571, 39);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2787, 3.96571, 3.96571, 39);
INSERT INTO building(id, name, level) VALUES (2788, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2788, 1.0, 0.3187353579860229, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2788, 1.0, 0.19405103938872134, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 2788, 1.0, 0.22128214069850208, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2788, 1.0, 0.720855149205892, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2788, 22.0, 8.002080280035262, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2788, 8.0, 2.909847374558277, 1);
INSERT INTO building(id, name, level) VALUES (2789, "building_subsistence_farms", 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2789, 43.16944, 43.16944, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2789, 10.79236, 10.79236, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2789, 10.79236, 10.79236, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2789, 10.79236, 10.79236, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2789, 10.79236, 10.79236, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2789, 10.79236, 10.79236, 268);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2789, 10.79236, 10.79236, 268);
INSERT INTO building(id, name, level) VALUES (2790, "building_subsistence_fishing_villages", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2790, 2.45085, 2.45085, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2790, 9.8034, 9.8034, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2790, 1.22542, 1.22542, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2790, 3.67627, 3.67627, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2790, 2.45085, 2.45085, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2790, 2.45085, 2.45085, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2790, 2.45085, 2.45085, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2790, 2.45085, 2.45085, 15);
INSERT INTO building(id, name, level) VALUES (2791, "building_subsistence_fishing_villages", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2791, 1.6042, 1.6042, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2791, 6.4168, 6.4168, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2791, 0.8021, 0.8021, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2791, 2.4063, 2.4063, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2791, 1.6042, 1.6042, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2791, 1.6042, 1.6042, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2791, 1.6042, 1.6042, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2791, 1.6042, 1.6042, 10);
INSERT INTO building(id, name, level) VALUES (2792, "building_subsistence_fishing_villages", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2792, 8.6859, 8.6859, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2792, 34.7436, 34.7436, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2792, 4.34295, 4.34295, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2792, 13.02885, 13.02885, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2792, 8.6859, 8.6859, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2792, 8.6859, 8.6859, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2792, 8.6859, 8.6859, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2792, 8.6859, 8.6859, 18);
INSERT INTO building(id, name, level) VALUES (2793, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2793, 1.9201782178217823, 0.6120286916543893, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2793, 1.9201782178217823, 0.37261257897989947, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 2793, 1.9201782178217823, 0.4249011465622386, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2793, 1.9201782178217823, 1.3841703557098246, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2793, 42.2439504950495, 15.365431054873703, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2793, 15.361435643564358, 5.58742914710896, 2);
INSERT INTO building(id, name, level) VALUES (2794, "building_subsistence_farms", 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2794, 20.3205, 20.3205, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2794, 5.08012, 5.08012, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2794, 5.08012, 5.08012, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2794, 5.08012, 5.08012, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2794, 5.08012, 5.08012, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2794, 5.08012, 5.08012, 23);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2794, 5.08012, 5.08012, 23);
INSERT INTO building(id, name, level) VALUES (2795, "building_subsistence_farms", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2795, 20.6262, 20.6262, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2795, 5.15655, 5.15655, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2795, 5.15655, 5.15655, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2795, 5.15655, 5.15655, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2795, 5.15655, 5.15655, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2795, 5.15655, 5.15655, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2795, 5.15655, 5.15655, 14);
INSERT INTO building(id, name, level) VALUES (2796, "building_subsistence_fishing_villages", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2796, 2.7658, 2.7658, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2796, 11.0632, 11.0632, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2796, 1.3829, 1.3829, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2796, 4.1487, 4.1487, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2796, 2.7658, 2.7658, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2796, 2.7658, 2.7658, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2796, 2.7658, 2.7658, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2796, 2.7658, 2.7658, 20);
INSERT INTO building(id, name, level) VALUES (2797, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2797, 1.1552376237623763, 0.3682150775688235, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2797, 1.1552376237623763, 0.22417506163204573, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 2797, 1.1552376237623763, 0.25563345440158936, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2797, 1.1552376237623763, 0.8327589896454878, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2797, 25.415277227722772, 9.244322214344983, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2797, 9.241910891089109, 3.361568767792148, 2);
INSERT INTO building(id, name, level) VALUES (2798, "building_subsistence_farms", 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2798, 53.4402, 53.4402, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2798, 13.36005, 13.36005, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2798, 13.36005, 13.36005, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2798, 13.36005, 13.36005, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2798, 13.36005, 13.36005, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2798, 13.36005, 13.36005, 165);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2798, 13.36005, 13.36005, 165);
INSERT INTO building(id, name, level) VALUES (2799, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2799, 1.0, 0.3187353579860229, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 2799, 1.0, 0.19405103938872134, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2799, 1.0, 0.720855149205892, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2799, 22.0, 9.046704674924667, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2799, 5.0, 2.0560692443010606, 1);
INSERT INTO building(id, name, level) VALUES (16780735, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 16780735, 0.4006, 0.4005174819495611, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 16780735, 0.8012, 0.22951515748524165, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16780735, 1.6024, 0.10452868951736147, 5);
INSERT INTO building(id, name, level) VALUES (3758, "building_subsistence_farms", 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3758, 38.01876, 38.01876, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3758, 9.50469, 9.50469, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3758, 9.50469, 9.50469, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3758, 9.50469, 9.50469, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3758, 9.50469, 9.50469, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3758, 9.50469, 9.50469, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3758, 9.50469, 9.50469, 78);
INSERT INTO building(id, name, level) VALUES (3759, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3759, 0.56247, 0.17927907680639832, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 3759, 0.56247, 0.10914788812497411, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3759, 0.56247, 0.4054593957738381, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3759, 12.37434, 5.088499978504878, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3759, 2.81235, 1.1564772678420174, 1);
INSERT INTO building(id, name, level) VALUES (3990, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3990, 2.42796, 2.4274598738748288, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3990, 4.85595, 1.3910560771223903, 3);
INSERT INTO building(id, name, level) VALUES (3991, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3991, 0.052, 0.05198928872036239, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3991, 0.104, 0.029792282050006398, 2);
INSERT INTO building(id, name, level) VALUES (3992, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3992, 0.143, 0.1429705439809966, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3992, 0.286, 0.08192877563751759, 2);
INSERT INTO building(id, name, level) VALUES (3993, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3993, 2.0, 1.999588027706246, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3993, 4.0, 1.1458570019233232, 2);
INSERT INTO building(id, name, level) VALUES (3994, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3994, 0.965, 0.9648012233682637, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3994, 1.93, 0.5528760034280034, 1);
INSERT INTO building(id, name, level) VALUES (3995, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3995, 1.0, 0.999794013853123, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3995, 2.0, 0.5729285009616616, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3995, 2.0, 0.13046516415047613, 2);
INSERT INTO building(id, name, level) VALUES (3996, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3996, 1.0, 0.999794013853123, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3996, 2.0, 0.5729285009616616, 1);
INSERT INTO building(id, name, level) VALUES (3997, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3997, 0.671, 0.6708617832954455, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3997, 1.342, 0.384435024145275, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3997, 1.342, 0.08754212514496948, 2);
INSERT INTO building(id, name, level) VALUES (3998, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3998, 0.99999, 0.9997840159129845, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3998, 1.99998, 0.5729227716766521, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3998, 3.99999, 0.2609296759751315, 3);
INSERT INTO building(id, name, level) VALUES (3999, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3999, 0.969, 0.9688003994236762, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3999, 1.938, 0.55516771743185, 1);
INSERT INTO building(id, name, level) VALUES (4000, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4000, 1.29598, 0.37125193933814704, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 4000, 1.29598, 0.08454012171786703, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4000, 1.29598, 0.4910961247526427, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4000, 1.29598, 0.14951115132119644, 3);
INSERT INTO building(id, name, level) VALUES (4001, "building_naval_base", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4001, 9.0, 2.5191113467109876, 5);
INSERT INTO building(id, name, level) VALUES (4002, "building_naval_base", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4002, 9.99996, 2.799001411406223, 6);
INSERT INTO building(id, name, level) VALUES (4003, "building_naval_base", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4003, 2.63898, 0.7386538290825957, 3);
INSERT INTO building(id, name, level) VALUES (4004, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4004, 0.896, 0.2507915296281161, 1);
INSERT INTO building(id, name, level) VALUES (4005, "building_naval_base", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4005, 14.99994, 4.198502117109334, 9);
INSERT INTO building(id, name, level) VALUES (16781347, "building_conscription_center", 20);
INSERT INTO building(id, name, level) VALUES (4133, "building_trade_center", 32);
INSERT INTO building(id, name, level) VALUES (4134, "building_trade_center", 65);
INSERT INTO building(id, name, level) VALUES (4135, "building_trade_center", 6);
INSERT INTO building(id, name, level) VALUES (16781442, "building_conscription_center", 12);
INSERT INTO building(id, name, level) VALUES (4239, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4239, 19.9608, 78.58419597341711, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4239, 39.9216, 12.724425467374813, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 4239, 19.9608, 2.5017889896789276, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 4239, 4.990195744680851, 1.1042411968875343, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 4239, 69.8628, 29.086548992743364, 1);
INSERT INTO building(id, name, level) VALUES (4492, "building_conscription_center", 8);
INSERT INTO building(id, name, level) VALUES (4601, "building_artillery_foundries", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4601, 3.5535, 0.40995067533439683, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 4601, 2.369, 0.2969188668064096, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 4601, 5.9225, 0.712774146286676, 1);
INSERT INTO building(id, name, level) VALUES (4676, "building_conscription_center", 16);
INSERT INTO building(id, name, level) VALUES (4831, "building_motor_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 4831, 0.34559999999999996, 0.05867839125528589, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 4831, 0.4608, 0.07823785500704786, 1);
INSERT INTO building(id, name, level) VALUES (4928, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 4928, 0.135, 0.13497219187017162, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4928, 0.27, 0.07734534762982433, 1);
INSERT INTO building(id, name, level) VALUES (4935, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4935, 39.9996, 157.47547218840407, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4935, 79.9992, 25.498573650595446, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4935, 99.999, 11.536416936193708, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4935, 19.9998, 4.13353056407102, 2);
INSERT INTO building(id, name, level) VALUES (4961, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4961, 17.35582, 68.32858202874395, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4961, 34.71165, 11.063830189035533, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4961, 43.38956, 5.005650604885981, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4961, 8.67791, 1.7935382462453398, 1);
INSERT INTO building(id, name, level) VALUES (4963, "building_conscription_center", 9);
INSERT INTO building(id, name, level) VALUES (4974, "building_conscription_center", 6);
INSERT INTO building(id, name, level) VALUES (5011, "building_conscription_center", 7);
INSERT INTO building(id, name, level) VALUES (5038, "building_conscription_center", 11);
INSERT INTO building(id, name, level) VALUES (5041, "building_conscription_center", 16);
INSERT INTO building(id, name, level) VALUES (5042, "building_conscription_center", 8);
INSERT INTO building(id, name, level) VALUES (5049, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5049, 19.9998, 78.73773609420203, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5049, 39.9996, 12.749286825297723, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5049, 49.9995, 5.768208468096854, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5049, 9.9999, 2.06676528203551, 1);
INSERT INTO building(id, name, level) VALUES (16782305, "building_trade_center", 5);
INSERT INTO building(id, name, level) VALUES (5133, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5133, 19.9998, 78.73773609420203, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5133, 39.9996, 12.749286825297723, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5133, 49.9995, 5.768208468096854, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5133, 9.9999, 2.06676528203551, 1);
INSERT INTO building(id, name, level) VALUES (5144, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5144, 19.9998, 78.73773609420203, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5144, 39.9996, 12.749286825297723, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5144, 49.9995, 5.768208468096854, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5144, 9.9999, 2.06676528203551, 1);
INSERT INTO building(id, name, level) VALUES (5149, "building_naval_base", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5149, 0.76131, 0.21309162881828247, 3);
INSERT INTO building(id, name, level) VALUES (5161, "building_naval_base", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5161, 0.08232, 0.02304147178458317, 3);
INSERT INTO building(id, name, level) VALUES (5207, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5207, 0.321, 0.08984830469935856, 1);
INSERT INTO building(id, name, level) VALUES (5209, "building_naval_base", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5209, 0.66798, 0.1869684441528895, 3);
INSERT INTO building(id, name, level) VALUES (5212, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5212, 1.194, 0.3342021053303243, 1);
INSERT INTO building(id, name, level) VALUES (5213, "building_naval_base", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5213, 0.777, 0.21748327959938193, 4);
INSERT INTO building(id, name, level) VALUES (5221, "building_trade_center", 9);
INSERT INTO building(id, name, level) VALUES (5224, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5224, 8.8216, 34.729987936309996, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5224, 17.6432, 5.623511668019, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 5224, 8.8216, 1.1056561736679709, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 5224, 2.205395744680851, 0.4880146914703459, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 5224, 30.8756, 12.854690222555453, 1);
INSERT INTO building(id, name, level) VALUES (5226, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5226, 0.396, 0.11084089925528345, 1);
INSERT INTO building(id, name, level) VALUES (5227, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5227, 0.333, 0.09320711982830654, 1);
INSERT INTO building(id, name, level) VALUES (5229, "building_naval_base", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5229, 0.43899, 0.12287385445473961, 3);
INSERT INTO building(id, name, level) VALUES (5252, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5252, 0.756, 0.21160535312372294, 1);
INSERT INTO building(id, name, level) VALUES (5317, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 5317, 0.738, 0.20656713043030098, 1);
INSERT INTO building(id, name, level) VALUES (5352, "building_barracks", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 5352, 0.868, 0.8678212040245108, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 5352, 1.736, 0.49730193883472223, 4);
INSERT INTO building(id, name, level) VALUES (5353, "building_tobacco_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 5353, 8.16, 8.16, 1);
INSERT INTO building(id, name, level) VALUES (5354, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 5354, 0.148, 0.009654422147135234, 2);
INSERT INTO building(id, name, level) VALUES (5356, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 5356, 1.72931, 0.11280735650852994, 3);
INSERT INTO building(id, name, level) VALUES (5364, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 5364, 0.072, 0.00469674590941714, 1);
INSERT INTO building(id, name, level) VALUES (5367, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 5367, 0.23, 0.2299526231862183, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 5367, 0.46, 0.13177355522118217, 1);
INSERT INTO building(id, name, level) VALUES (5392, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 5392, 0.026, 0.025994644360181195, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 5392, 0.052, 0.014896141025003199, 1);
INSERT INTO building(id, name, level) VALUES (5446, "building_arts_academy", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 5446, 0.054, 0.007357877359086462, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 5446, 0.0216, 0.002943150943634585, 1);
INSERT INTO building(id, name, level) VALUES (5507, "building_motor_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 5507, 29.0868, 4.938560852905816, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 5507, 38.782399999999996, 6.584747803874421, 1);
INSERT INTO building(id, name, level) VALUES (5621, "building_gold_fields", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 5621, 20.0, 20.0, 1);
INSERT INTO building(id, name, level) VALUES (5645, "building_steel_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 5645, 19.4151, 3.7675203348359636, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5645, 38.8302, 4.479658565743547, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 5645, 58.2453, 9.011024426561606, 1);
INSERT INTO building(id, name, level) VALUES (5690, "building_conscription_center", 7);
INSERT INTO building(id, name, level) VALUES (5711, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (5729, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (5776, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (16783025, "building_dye_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 16783025, 3.6815, 3.6815, 1);
