
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 30.138481258084944, 422.74906446013455);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 7.483523935459777, 23.346317084810938);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 29.30453783076426, 20.996493589031168);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 32.25480674931399, 55.30244141096876);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 52.5, 28.771062941814126);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 49.857891448297345, 164.90357662224946);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 48.93073897809612, 144.23834272763673);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 52.5, 8.163452585006597);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 42.794484391320275, 45.97319010901794);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 70.0, 1.308483315052971);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 20.46557367880829);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 52.5, 11.53283964763599);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 52.5, 7.292957472897206);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 37.17030978166165, 128.29484790549026);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 87.5, 13.296093106787637);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 87.5, 30.724651893212368);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 52.5, 3.264625745347869);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 70.0, 3.228219110421897);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 87.5, 10.359808968368332);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 6.309962731294907);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 93.30620867998815, 21.654197143428746);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 350.0, 0.022218733647305075);
INSERT INTO building(id, name, level) VALUES (70, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 70, 10.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (71, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 71, 10.0, 3.796974779490494, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 71, 10.0, 1.8301288337906714, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 71, 20.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 71, 10.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 71, 24.999999999999996, 3.5169397583007282, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 71, 40.0, 5.627103613281166, 1);
INSERT INTO building(id, name, level) VALUES (72, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 72, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (73, "building_rye_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 73, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 73, 15.0, 15.0, 1);
INSERT INTO building(id, name, level) VALUES (74, "building_logging_camplevel", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 74, 45.0, 0.0, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 74, 540.0, 0.0, 9);
INSERT INTO building(id, name, level) VALUES (75, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 75, 10.0, 0.0, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 75, 10.0, 0.0, 10);
INSERT INTO building(id, name, level) VALUES (76, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 76, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (77, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 77, 10.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (78, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 78, 25.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (79, "building_rye_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 79, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 79, 15.0, 15.0, 1);
INSERT INTO building(id, name, level) VALUES (80, "building_logging_camplevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 80, 25.0, 0.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 80, 300.0, 0.0, 5);
INSERT INTO building(id, name, level) VALUES (81, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 81, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (82, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 82, 50.0, 60.5, 2);
INSERT INTO building(id, name, level) VALUES (83, "building_logging_camplevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 83, 35.0, 0.0, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 83, 420.00000000000006, 0.0, 7);
INSERT INTO building(id, name, level) VALUES (84, "building_rye_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 84, 20.0, 20.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 84, 30.0, 30.3, 2);
INSERT INTO building(id, name, level) VALUES (3669, "building_subsistence_farmslevel", 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3669, 57.9405, 57.9405, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3669, 11.5881, 11.5881, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3669, 11.5881, 11.5881, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3669, 11.5881, 11.5881, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3669, 11.5881, 11.5881, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3669, 11.5881, 11.5881, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3669, 16.22334, 16.22334, 57);
INSERT INTO building(id, name, level) VALUES (3670, "building_subsistence_farmslevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3670, 3.033, 3.033, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3670, 0.6066, 0.6066, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3670, 0.6066, 0.6066, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3670, 0.6066, 0.6066, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3670, 0.6066, 0.6066, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3670, 0.6066, 0.6066, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3670, 0.84924, 0.84924, 4);
INSERT INTO building(id, name, level) VALUES (3671, "building_subsistence_farmslevel", 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3671, 9.5238, 9.5238, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3671, 1.90476, 1.90476, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3671, 1.90476, 1.90476, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3671, 1.90476, 1.90476, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3671, 1.90476, 1.90476, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3671, 1.90476, 1.90476, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3671, 2.66666, 2.66666, 33);
INSERT INTO building(id, name, level) VALUES (3672, "building_subsistence_farmslevel", 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3672, 26.3144, 26.3144, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3672, 5.26288, 5.26288, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3672, 5.26288, 5.26288, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3672, 5.26288, 5.26288, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3672, 5.26288, 5.26288, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3672, 5.26288, 5.26288, 28);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3672, 7.36803, 7.36803, 28);
INSERT INTO building(id, name, level) VALUES (553652116, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 553652116, 5.0, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 553652116, 20.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (4445, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4446, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4447, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4750, "building_iron_minelevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4750, 4.96, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 4750, 19.84, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (5486, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5486, 0.51, 0.09333657052332425, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5486, 2.55, 0.46668285261662124, 1);
INSERT INTO building(id, name, level) VALUES (50337366, "building_logging_camplevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 50337366, 10.0, 0.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 50337366, 120.0, 0.0, 2);
INSERT INTO building(id, name, level) VALUES (6043, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 6043, 20.0, 7.593949558980988, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 6043, 40.0, 7.3205153351626855, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 6043, 50.0, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 6043, 10.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (6462, "building_subsistence_fishing_villageslevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6462, 0.0018, 0.0018, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 6462, 0.0072, 0.0072, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6462, 0.0009, 0.0009, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6462, 0.0027, 0.0027, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6462, 0.0018, 0.0018, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6462, 0.0018, 0.0018, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6462, 0.0018, 0.0018, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6462, 0.00252, 0.00252, 4);
