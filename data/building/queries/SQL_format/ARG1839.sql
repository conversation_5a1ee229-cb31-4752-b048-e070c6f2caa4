
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 105.0, 0.021284207689543654);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 122.5, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 25.445670467689318, 115.44137562383862);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 24.56976929039684, 35.95318362946047);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 22.43137356121373, 6.184760135602363);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 15.534470061682287, 37.150746114397634);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 27.621560221251954, 46.58622008480517);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 21.814305428425303, 31.8193123373905);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 25.937059163802434, 15.652309359204684);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 36.32177640255656, 3.9428);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 105.0, 0.008237333812432624);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 16.695133884885685, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 38.49037840117345, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 122.5, 1.2693744897959183);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 30.015120703792366, 42.49752071626961);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 21.42774012080422, 4.719275462533301);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 20.63504738437662, 6.186070240548339);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 19.536288730839868, 3.958259534674811);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 8.541862446521538, 0.0018753524037387692);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 23.884433303439828, 45.553576069588765);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 105.0, 7.4046845238095225);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 105.0, 1.4809369047619048);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (1730, "building_gold_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1730, 3.543, 3.730733179578344, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 1730, 7.086, 7.086, 1);
INSERT INTO building(id, name, level) VALUES (1740, "building_government_administration", 3);
INSERT INTO building(id, name, level) VALUES (1741, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1741, 30.0, 42.71690909605546, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 1741, 30.0, 30.0, 1);
INSERT INTO building(id, name, level) VALUES (1742, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1742, 4.3539, 2.7732396900484986, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1742, 4.3539, 4.5846003924826855, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1742, 8.7078, 7.127139690048498, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1742, 17.4156, 14.254279380096996, 1);
INSERT INTO building(id, name, level) VALUES (1743, "building_fishing_wharf", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1743, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (1744, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1744, 18.344, 21.0956, 1);
INSERT INTO building(id, name, level) VALUES (1745, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1745, 5.0, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (1746, "building_government_administration", 1);
INSERT INTO building(id, name, level) VALUES (1747, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1747, 1.8457524752475247, 1.9435534859426462, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1747, 36.915198019801984, 36.915198019801984, 2);
INSERT INTO building(id, name, level) VALUES (1748, "building_textile_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1748, 30.104, 25.2243953542148, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 1748, 3.763, 16.8615, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 1748, 45.156, 41.49629651566109, 1);
INSERT INTO building(id, name, level) VALUES (1749, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1749, 4.3598, 2.7769977263312073, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1749, 8.7196, 5.553995452662415, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1749, 8.7196, 5.553995452662415, 1);
INSERT INTO building(id, name, level) VALUES (1750, "building_furniture_manufacturies", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1750, 6.4108, 5.371663358251403, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1750, 19.2324, 27.384956083299233, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1750, 3.2054, 3.375244745645053, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1750, 41.6702, 39.418737276211374, 1);
INSERT INTO building(id, name, level) VALUES (1751, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1751, 9.563, 10.069715324952782, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1751, 114.756, 114.756, 2);
INSERT INTO building(id, name, level) VALUES (1752, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1752, 4.48625, 2.857540724288586, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1752, 8.9725, 5.715081448577172, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1752, 8.9725, 5.715081448577172, 1);
INSERT INTO building(id, name, level) VALUES (1753, "building_maize_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1753, 0.9793259259259258, 1.0312175347087595, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1753, 19.586599999999997, 19.586599999999997, 1);
INSERT INTO building(id, name, level) VALUES (1754, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1754, 3.48035, 2.216827385851832, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1754, 6.9607, 4.433654771703664, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1754, 6.9607, 4.433654771703664, 1);
INSERT INTO building(id, name, level) VALUES (1755, "building_tobacco_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1755, 71.84474452554744, 98.4273, 3);
INSERT INTO building(id, name, level) VALUES (1756, "building_gold_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1756, 5.0, 5.264935336689732, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 1756, 10.0, 10.0, 1);
INSERT INTO building(id, name, level) VALUES (3003, "building_subsistence_pastures", 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3003, 0.8330000000000001, 0.95795, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3003, 1.2494956521739131, 1.43692, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3003, 0.4164956521739131, 0.47897, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3003, 0.8330000000000001, 0.95795, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3003, 0.8330000000000001, 0.95795, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3003, 0.8330000000000001, 0.95795, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3003, 2.2157739130434786, 2.54814, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3003, 0.8330000000000001, 0.95795, 98);
INSERT INTO building(id, name, level) VALUES (3004, "building_urban_center", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3004, 8.505, 8.505, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3004, 2.835, 2.835, 1);
INSERT INTO building(id, name, level) VALUES (3008, "building_subsistence_pastures", 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3008, 1.74144, 1.74144, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3008, 2.61216, 2.61216, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3008, 0.87072, 0.87072, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3008, 1.74144, 1.74144, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3008, 1.74144, 1.74144, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3008, 1.74144, 1.74144, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3008, 4.63223, 4.63223, 64);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3008, 1.74144, 1.74144, 64);
INSERT INTO building(id, name, level) VALUES (3010, "building_subsistence_farms", 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3010, 8.94642, 8.94642, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3010, 2.2366, 2.2366, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3010, 2.2366, 2.2366, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3010, 2.2366, 2.2366, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3010, 2.2366, 2.2366, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3010, 2.2366, 2.2366, 147);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3010, 2.2366, 2.2366, 147);
INSERT INTO building(id, name, level) VALUES (3012, "building_subsistence_farms", 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3012, 3.4245565217391305, 3.93824, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3012, 0.8561391304347826, 0.98456, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3012, 0.8561391304347826, 0.98456, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3012, 0.8561391304347826, 0.98456, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3012, 0.8561391304347826, 0.98456, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3012, 0.8561391304347826, 0.98456, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3012, 0.8561391304347826, 0.98456, 57);
INSERT INTO building(id, name, level) VALUES (3013, "building_subsistence_farms", 112);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3013, 4.932474074074073, 6.65884, 112);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3013, 1.2331185185185183, 1.66471, 112);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3013, 1.2331185185185183, 1.66471, 112);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3013, 1.2331185185185183, 1.66471, 112);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3013, 1.2331185185185183, 1.66471, 112);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3013, 1.2331185185185183, 1.66471, 112);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3013, 1.2331185185185183, 1.66471, 112);
INSERT INTO building(id, name, level) VALUES (3014, "building_subsistence_pastures", 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3014, 2.0043, 2.0043, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3014, 3.00645, 3.00645, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3014, 1.00215, 1.00215, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3014, 2.0043, 2.0043, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3014, 2.0043, 2.0043, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3014, 2.0043, 2.0043, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3014, 5.33143, 5.33143, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3014, 2.0043, 2.0043, 60);
INSERT INTO building(id, name, level) VALUES (3016, "building_subsistence_farms", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3016, 5.0348, 4.02784, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3016, 1.2587, 1.00696, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3016, 1.2587, 1.00696, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3016, 1.2587, 1.00696, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3016, 1.2587, 1.00696, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3016, 1.2587, 1.00696, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3016, 1.2587, 1.00696, 20);
INSERT INTO building(id, name, level) VALUES (3018, "building_subsistence_pastures", 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3018, 7.5e-05, 6e-05, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3018, 0.0001125, 9e-05, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3018, 3.75e-05, 3e-05, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3018, 7.5e-05, 6e-05, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3018, 7.5e-05, 6e-05, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3018, 7.5e-05, 6e-05, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3018, 0.00018749999999999998, 0.00015, 15);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3018, 7.5e-05, 6e-05, 15);
INSERT INTO building(id, name, level) VALUES (4026, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4026, 5.8524, 0.0, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 4026, 0.9754, 0.621286201721056, 5);
INSERT INTO building(id, name, level) VALUES (4027, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 4027, 0.977, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (4028, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4028, 0.782, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (4178, "building_trade_center", 15);
INSERT INTO building(id, name, level) VALUES (4318, "building_dye_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 4318, 12.489999999999998, 16.8615, 1);
INSERT INTO building(id, name, level) VALUES (4319, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (16781590, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 16781590, 0.208, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (4749, "building_artillery_foundries", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4749, 0.25905, 0.0, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 4749, 0.1727, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 4749, 0.43175, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (5057, "building_conscription_center", 1);
INSERT INTO building(id, name, level) VALUES (5191, "building_sugar_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 5191, 0.03, 0.0405, 1);
INSERT INTO building(id, name, level) VALUES (5514, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 5514, 5.513999999999999, 7.4439, 1);
INSERT INTO building(id, name, level) VALUES (5515, "building_barracks", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 5515, 0.182, 0.0, 1);
INSERT INTO building(id, name, level) VALUES (5595, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 5595, 0.18, 0.18, 1);
