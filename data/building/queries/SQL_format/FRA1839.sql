
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 85.51885158228767, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 102.74568425607899, 25.53715903366759);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 121.0938845459035, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 117.04545149774191, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 26.002747884476758, 4851.44140444324);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 31.692153116346926, 840.7841355299057);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 28.597375944731365, 106.89292125457223);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 32.34305216168679, 598.551418899853);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 41.7010467768231, 386.1045291302751);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 32.87270386524631, 1494.8846824519164);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 29.76979498162618, 1005.2677899875398);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 49.79755572400555, 155.2250007126205);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 27.459491039315438, 883.9334229949857);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 49.76013900641662, 478.49526666666634);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 95.49781341061488, 15.60545558294657);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 34.49616808740057, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 57.1071865714425, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 85.87380673581413, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 51.62088892522434, 431.4380250059374);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 69.62097144689443, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 68.71719788342985, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 68.78389594753101, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 70.0, 17.019106168334382);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 104.8766250508922, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 87.31071516094553, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 62.42581732178539, 25.73138066080244);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 50.446330727483556, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 68.16940969130447, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 84.91734350688954, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 112.47374742016201, 101.71435941681837);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 42.64279263843151, 472.36378816727364);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 43.3715238742003, 626.3232361026562);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 38.403963550450186, 1818.0631470319072);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 22.196573096706363, 339.82093958453106);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 24.984378266144695, 13.33143833775131);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 39.527805674054015, 84.44257986685065);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 65.6471905544617, 378.8012134760684);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 68.39053211794257, 593.3337632647733);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 93.1694307113155, 462.0030757489383);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 317.13545502298024, 0.23745924663371917);
INSERT INTO building(id, name, level) VALUES (186, "building_government_administration", 31);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 186, 620.0, 74.66780642817774, 31);
INSERT INTO building(id, name, level) VALUES (187, "building_construction_sector", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 187, 124.99875, 53.36063948483916, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 187, 374.99625, 66.4399791586197, 5);
INSERT INTO building(id, name, level) VALUES (188, "building_university", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 188, 50.0, 6.021597292594979, 5);
INSERT INTO building(id, name, level) VALUES (189, "building_arts_academy", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 189, 1.08, 0.13006650152005156, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 189, 0.432, 0.05202660060802062, 2);
INSERT INTO building(id, name, level) VALUES (190, "building_furniture_manufacturies", 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 190, 175.43519658119658, 74.89142313600031, 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 190, 263.1527948717949, 46.62410945926682, 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 190, 263.1527948717949, 10.69839760305411, 18);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 190, 87.71759829059829, 5.3587328128660765, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 190, 701.740794871795, 123.8238949780872, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 190, 438.588, 77.38993492685726, 18);
INSERT INTO building(id, name, level) VALUES (191, "building_wheat_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 191, 25.0, 2.283592374597105, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 191, 5.0, 0.3054536898692331, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 191, 200.0, 15.243443295773082, 5);
INSERT INTO building(id, name, level) VALUES (192, "building_livestock_ranch", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 192, 40.00000000000001, 23.995705642319365, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 192, 40.00000000000001, 2.4436295189538653, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 192, 80.00000000000001, 26.43933516127323, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 192, 20.000000000000004, 6.609833790318308, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 192, 100.00000000000001, 33.04916895159154, 4);
INSERT INTO building(id, name, level) VALUES (193, "building_paper_mills", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 193, 240.0, 42.52201188163543, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 193, 79.99999999999999, 3.4844861366366082, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 193, 560.0, 61.80471534013613, 8);
INSERT INTO building(id, name, level) VALUES (194, "building_railway", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 194, 7.95272, 1.4090235597138323, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 194, 1.98818, 0.07790730839196064, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 194, 4.97045, 0.01390528261666962, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 194, 29.8227, 2.1786265568354333, 1);
INSERT INTO building(id, name, level) VALUES (198, "building_textile_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 198, 75.0, 32.016703857942076, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 198, 44.99999999999999, 55.11066037689229, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 198, 15.0, 6.446406714278752, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 198, 89.99999999999999, 55.699494971734325, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 198, 89.99999999999999, 55.699494971734325, 3);
INSERT INTO building(id, name, level) VALUES (199, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 199, 10.0, 2.113658718780943, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 199, 100.0, 21.13658718780943, 2);
INSERT INTO building(id, name, level) VALUES (200, "building_silk_plantation", 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 200, 160.0, 187.2, 8);
INSERT INTO building(id, name, level) VALUES (201, "building_wheat_farm", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 201, 30.000000000000004, 2.740310849516526, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 201, 6.000000000000001, 0.3665444278430798, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 201, 150.0, 11.432582471829809, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 201, 42.0, 3.2011230921123466, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 201, 30.000000000000004, 2.286516494365962, 6);
INSERT INTO building(id, name, level) VALUES (202, "building_vineyard_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 202, 100.00000000000001, 114.0, 5);
INSERT INTO building(id, name, level) VALUES (203, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 203, 15.0, 3.170488078171414, 3);
INSERT INTO building(id, name, level) VALUES (204, "building_government_administration", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 204, 100.0, 12.043194585189958, 5);
INSERT INTO building(id, name, level) VALUES (205, "building_shipyards", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 205, 29.168999999999997, 12.451936464430831, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 205, 58.337999999999994, 10.336038038128532, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 205, 29.168999999999997, 1.185856908095991, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 205, 7.292245098039215, 0.020400714019507077, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 205, 102.0915, 16.52648838970835, 3);
INSERT INTO building(id, name, level) VALUES (206, "building_military_shipyards", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 206, 10.263, 4.381165755920794, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 206, 20.526, 3.6366950661768707, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 206, 10.263, 0.13023512422236638, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 206, 20.526, 0.8344783467235185, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 206, 5.1314950495049505, 0.014355820682112766, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 206, 45.629297029702975, 6.0249516500104745, 2);
INSERT INTO building(id, name, level) VALUES (207, "building_textile_mills", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 207, 200.00000000000003, 85.37787695451222, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 207, 120.00000000000001, 146.96176100504613, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 207, 40.00000000000001, 17.19041790474334, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 207, 240.00000000000003, 148.53198659129157, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 207, 240.00000000000003, 148.53198659129157, 8);
INSERT INTO building(id, name, level) VALUES (208, "building_fishing_wharf", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 208, 15.0, 3.170488078171414, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 208, 150.0, 31.70488078171414, 3);
INSERT INTO building(id, name, level) VALUES (209, "building_wheat_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 209, 20.000000000000004, 1.8268738996776843, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 209, 4.0, 0.24436295189538648, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 209, 100.00000000000001, 7.621721647886542, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 209, 28.000000000000004, 2.1340820614082316, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 209, 20.000000000000004, 1.5243443295773085, 4);
INSERT INTO building(id, name, level) VALUES (210, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 210, 19.999999999999996, 11.997852821159679, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 210, 19.999999999999996, 1.2218147594769322, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 210, 39.99999999999999, 13.219667580636612, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 210, 9.999999999999998, 3.304916895159153, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 210, 49.99999999999999, 16.524584475795766, 2);
INSERT INTO building(id, name, level) VALUES (211, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 211, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (212, "building_university", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 212, 20.0, 2.408638917037992, 2);
INSERT INTO building(id, name, level) VALUES (213, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 213, 9.998, 2.113235987037186, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 213, 99.97999999999999, 21.13235987037186, 2);
INSERT INTO building(id, name, level) VALUES (214, "building_glassworks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 214, 59.94479411764706, 10.620721865469909, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 214, 14.986196078431373, 6.440474334766527, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 214, 29.972392156862746, 1.2872641760465422, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 214, 59.94479411764706, 12.985717397009626, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 214, 59.94479411764706, 12.985717397009626, 3);
INSERT INTO building(id, name, level) VALUES (215, "building_wheat_farm", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 215, 35.00000000000001, 3.1970293244359476, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 215, 7.0, 0.42763516581692634, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 215, 175.0, 13.338012883801447, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 215, 49.00000000000001, 3.7346436074644056, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 215, 35.00000000000001, 2.66760257676029, 7);
INSERT INTO building(id, name, level) VALUES (216, "building_vineyard_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 216, 80.00000000000001, 90.4, 4);
INSERT INTO building(id, name, level) VALUES (217, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 217, 19.99, 11.9918538947491, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 217, 19.99, 1.2212038520971937, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 217, 39.98, 13.213057746846292, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 217, 9.995, 3.303264436711573, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 217, 49.974999999999994, 16.516322183557865, 2);
INSERT INTO building(id, name, level) VALUES (218, "building_construction_sector", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 218, 74.99925, 32.016383690903496, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 218, 224.99775, 39.86398749517183, 3);
INSERT INTO building(id, name, level) VALUES (219, "building_government_administration", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 219, 120.0, 14.45183350222795, 6);
INSERT INTO building(id, name, level) VALUES (220, "building_textile_mills", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 220, 175.00000000000003, 74.70564233519819, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 220, 105.00000000000001, 128.59154087941536, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 220, 35.0, 15.04161566665042, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 220, 210.00000000000003, 129.96548826738012, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 220, 210.00000000000003, 129.96548826738012, 7);
INSERT INTO building(id, name, level) VALUES (221, "building_paper_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 221, 90.0, 15.945754455613287, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 221, 30.0, 1.3066823012387283, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 221, 209.99999999999997, 23.176768252551046, 3);
INSERT INTO building(id, name, level) VALUES (222, "building_logging_camp", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 222, 20.0, 2.4398721875648466, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 222, 20.0, 1.2218147594769324, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 222, 239.99999999999997, 21.97012168225067, 4);
INSERT INTO building(id, name, level) VALUES (223, "building_coal_mine", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 223, 40.0, 2.443629518953865, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 223, 160.0, 9.77451807581546, 4);
INSERT INTO building(id, name, level) VALUES (224, "building_silk_plantation", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 224, 240.0, 290.4, 12);
INSERT INTO building(id, name, level) VALUES (225, "building_wheat_farm", 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 225, 45.00000000000001, 4.110466274274789, 9);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 225, 9.0, 0.5498166417646195, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 225, 225.0, 17.148873707744713, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 225, 63.00000000000001, 4.8016846381685205, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 225, 45.00000000000001, 3.4297747415489432, 9);
INSERT INTO building(id, name, level) VALUES (226, "building_vineyard_plantation", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 226, 120.00000000000001, 138.0, 6);
INSERT INTO building(id, name, level) VALUES (227, "building_railway", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 227, 8.0, 1.4174003960545145, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 227, 2.0, 0.07837047791644684, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 227, 5.0, 0.013987951409499764, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 227, 30.0, 2.191578787469377, 1);
INSERT INTO building(id, name, level) VALUES (228, "building_arms_industry", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 228, 60.0, 2.4392819255291394, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 228, 60.0, 0.305221896052876, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 228, 30.0, 1.8327221392153983, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 228, 300.0, 10.683246833354687, 6);
INSERT INTO building(id, name, level) VALUES (229, "building_artillery_foundries", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 229, 28.54889108910891, 0.36227890260166695, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 229, 19.032594059405938, 0.7737643780840362, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 229, 47.01051485148515, 1.2538755041111245, 2);
INSERT INTO building(id, name, level) VALUES (230, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 230, 10.0, 0.6109073797384662, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 230, 40.0, 2.443629518953865, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 230, 40.0, 2.443629518953865, 2);
INSERT INTO building(id, name, level) VALUES (231, "building_munition_plants", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 231, 48.96839215686275, 2.103110644363259, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (34, 231, 48.96839215686275, 3.3889275065844284, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (0, 231, 122.421, 6.865048788241056, 3);
INSERT INTO building(id, name, level) VALUES (232, "building_wheat_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 232, 25.0, 2.283592374597105, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 232, 5.0, 0.3054536898692331, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 232, 125.0, 9.527152059858176, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 232, 35.0, 2.6676025767602893, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 232, 25.0, 1.9054304119716352, 5);
INSERT INTO building(id, name, level) VALUES (233, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 233, 19.999999999999996, 11.997852821159679, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 233, 19.999999999999996, 1.2218147594769322, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 233, 39.99999999999999, 13.219667580636612, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 233, 9.999999999999998, 3.304916895159153, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 233, 49.99999999999999, 16.524584475795766, 2);
INSERT INTO building(id, name, level) VALUES (234, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 234, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (235, "building_coal_mine", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 235, 60.0, 3.6654442784307966, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 235, 240.0, 14.661777113723186, 6);
INSERT INTO building(id, name, level) VALUES (236, "building_explosives_factory", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 236, 20.0, 0.8711215341591522, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 236, 20.0, 1.8268738996776839, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (34, 236, 50.0, 3.3724942922960452, 1);
INSERT INTO building(id, name, level) VALUES (237, "building_textile_mills", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 237, 124.964995951417, 53.346230239805976, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 237, 74.97899595141699, 91.82537736175387, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 237, 24.992995951417, 10.741001127410415, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 237, 149.95799999999997, 92.80649852190373, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 237, 149.95799999999997, 92.80649852190373, 5);
INSERT INTO building(id, name, level) VALUES (238, "building_wheat_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 238, 25.0, 2.283592374597105, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 238, 5.0, 0.3054536898692331, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 238, 125.0, 9.527152059858176, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 238, 35.0, 2.6676025767602893, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 238, 25.0, 1.9054304119716352, 5);
INSERT INTO building(id, name, level) VALUES (239, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 239, 19.996, 11.99545325059545, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 239, 19.996, 1.221570396525037, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 239, 39.992, 13.217023647120488, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 239, 9.998, 3.304255911780122, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 239, 49.989999999999995, 16.52127955890061, 2);
INSERT INTO building(id, name, level) VALUES (240, "building_steel_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 240, 30.0, 1.1755571687467026, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 240, 40.0, 0.5075908573413871, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (30, 240, 64.22, 1.6657065836793525, 1);
INSERT INTO building(id, name, level) VALUES (241, "building_arms_industry", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 241, 40.0, 1.6261879503527596, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 241, 40.0, 0.20348126403525063, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 241, 20.0, 1.2218147594769324, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 241, 200.0, 7.122164555569792, 4);
INSERT INTO building(id, name, level) VALUES (242, "building_iron_mine", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 242, 50.0, 1.959261947911171, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 242, 50.0, 3.0545368986923314, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 242, 200.0, 10.027597693207005, 5);
INSERT INTO building(id, name, level) VALUES (243, "building_vineyard_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 243, 80.00000000000001, 90.4, 4);
INSERT INTO building(id, name, level) VALUES (244, "building_logging_camp", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 244, 25.0, 1.5272684493461657, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 244, 300.0, 18.327221392153987, 5);
INSERT INTO building(id, name, level) VALUES (245, "building_wheat_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 245, 24.963245614035092, 2.2802350931762065, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 245, 4.992649122807018, 0.3050046193567587, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 245, 199.70600000000002, 15.221035434128297, 5);
INSERT INTO building(id, name, level) VALUES (246, "building_paper_mills", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 246, 119.99999999999999, 21.261005940817714, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 246, 40.0, 1.7422430683183043, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 246, 279.99999999999994, 30.902357670068064, 4);
INSERT INTO building(id, name, level) VALUES (247, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 247, 9.999999999999998, 0.9134369498388418, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 247, 2.0, 0.12218147594769324, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 247, 49.99999999999999, 3.81086082394327, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 247, 13.999999999999998, 1.0670410307041156, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 247, 9.999999999999998, 0.762172164788654, 2);
INSERT INTO building(id, name, level) VALUES (248, "building_vineyard_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 248, 39.99999999999999, 44.4, 2);
INSERT INTO building(id, name, level) VALUES (249, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 249, 10.0, 2.113658718780943, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 249, 100.0, 21.13658718780943, 2);
INSERT INTO building(id, name, level) VALUES (250, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 250, 4.896, 1.0348473087151495, 1);
INSERT INTO building(id, name, level) VALUES (251, "building_textile_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 251, 75.0, 32.016703857942076, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 251, 44.99999999999999, 55.11066037689229, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 251, 15.0, 6.446406714278752, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 251, 89.99999999999999, 55.699494971734325, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 251, 89.99999999999999, 55.699494971734325, 3);
INSERT INTO building(id, name, level) VALUES (252, "building_wheat_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 252, 12.530000000000001, 1.144536498148069, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 252, 2.5060000000000002, 0.15309338936245964, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 252, 62.65000000000001, 4.775008612400918, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 252, 17.542, 1.337002411472257, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 252, 12.530000000000001, 0.9550017224801834, 5);
INSERT INTO building(id, name, level) VALUES (253, "building_vineyard_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 253, 99.99600000000001, 113.99544, 5);
INSERT INTO building(id, name, level) VALUES (254, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 254, 19.999999999999996, 11.997852821159679, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 254, 19.999999999999996, 1.2218147594769322, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 254, 39.99999999999999, 13.219667580636612, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 254, 9.999999999999998, 3.304916895159153, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 254, 49.99999999999999, 16.524584475795766, 2);
INSERT INTO building(id, name, level) VALUES (255, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 255, 60.0, 7.225916751113975, 3);
INSERT INTO building(id, name, level) VALUES (256, "building_paper_mills", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 256, 149.94, 26.565626923051738, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 256, 49.98, 2.176932713863721, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 256, 349.86, 38.612495908750056, 5);
INSERT INTO building(id, name, level) VALUES (257, "building_furniture_manufacturies", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 257, 19.98219801980198, 8.530188219076756, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 257, 29.97329702970297, 5.310520385120074, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 257, 29.97329702970297, 1.2185553615511782, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 257, 9.99109900990099, 0.6103636116846198, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 257, 79.92879207920792, 14.103632606893498, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 257, 49.95549504950495, 8.814770379308436, 2);
INSERT INTO building(id, name, level) VALUES (258, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 258, 14.993392156862745, 0.9159573915940289, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 258, 59.97359803921569, 3.6638313631625268, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 258, 59.97359803921569, 3.6638313631625268, 3);
INSERT INTO building(id, name, level) VALUES (259, "building_wheat_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 259, 24.99424561403509, 2.2830667477207065, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 259, 4.998842105263159, 0.30538295322526343, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 259, 124.9712456140351, 9.52496048059837, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 259, 34.99194736842105, 2.6669888275960116, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 259, 24.99424561403509, 1.9049918286908445, 5);
INSERT INTO building(id, name, level) VALUES (260, "building_government_administration", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 260, 100.0, 12.043194585189958, 5);
INSERT INTO building(id, name, level) VALUES (261, "building_shipyards", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 261, 40.0, 17.07557539090244, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 261, 80.0, 14.174003960545145, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 261, 40.0, 1.6261879503527596, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 261, 10.0, 0.02797590281899953, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 261, 140.0, 22.663085316203297, 2);
INSERT INTO building(id, name, level) VALUES (262, "building_military_shipyards", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 262, 20.0, 8.53778769545122, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 262, 40.0, 7.087001980272572, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 262, 20.0, 0.25379542867069355, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 262, 40.0, 1.6261879503527596, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 262, 10.0, 0.02797590281899953, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 262, 88.92, 11.741112302698536, 2);
INSERT INTO building(id, name, level) VALUES (263, "building_logging_camp", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 263, 20.0, 1.2218147594769324, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 263, 139.99999999999997, 8.552703316338524, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 263, 40.0, 2.443629518953865, 4);
INSERT INTO building(id, name, level) VALUES (264, "building_fishing_wharf", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 264, 15.0, 3.170488078171414, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 264, 150.0, 31.70488078171414, 3);
INSERT INTO building(id, name, level) VALUES (265, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 265, 9.999999999999998, 0.9134369498388418, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 265, 2.0, 0.12218147594769324, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 265, 79.99999999999999, 6.097377318309232, 2);
INSERT INTO building(id, name, level) VALUES (266, "building_vineyard_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 266, 39.99999999999999, 44.4, 2);
INSERT INTO building(id, name, level) VALUES (267, "building_livestock_ranch", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 267, 50.00000000000001, 29.994632052899206, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 267, 50.00000000000001, 3.0545368986923314, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 267, 100.00000000000001, 33.04916895159154, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 267, 25.000000000000004, 8.262292237897885, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 267, 125.00000000000001, 41.31146118948942, 5);
INSERT INTO building(id, name, level) VALUES (268, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 268, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (269, "building_tooling_workshops", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 269, 119.99999999999999, 21.261005940817714, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 269, 80.0, 0.40696252807050126, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 269, 320.0, 29.16193297723129, 4);
INSERT INTO building(id, name, level) VALUES (270, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 270, 10.0, 0.6109073797384662, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 270, 40.0, 2.443629518953865, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 270, 40.0, 2.443629518953865, 2);
INSERT INTO building(id, name, level) VALUES (271, "building_iron_mine", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 271, 30.0, 1.1755571687467026, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 271, 30.0, 1.8327221392153983, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 271, 120.0, 6.016558615924201, 3);
INSERT INTO building(id, name, level) VALUES (272, "building_wheat_farm", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 272, 37.20879487179487, 3.3987888094871455, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 272, 7.441752136752137, 0.45462212985263795, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 272, 186.044, 14.179755822594037, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 272, 52.09231623931624, 3.970331343697482, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 272, 37.20879487179487, 2.835950773661287, 8);
INSERT INTO building(id, name, level) VALUES (273, "building_vineyard_plantation", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 273, 200.0, 238.0, 10);
INSERT INTO building(id, name, level) VALUES (274, "building_food_industry", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 274, 240.0, 143.97423385391616, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 274, 240.0, 138.40627827033083, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 274, 210.0, 123.54147405435806, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 274, 360.0, 211.78538409318526, 6);
INSERT INTO building(id, name, level) VALUES (275, "building_logging_camp", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 275, 19.995592233009706, 1.2215454857386785, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 275, 79.98239805825243, 4.886183722296598, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 275, 79.98239805825243, 4.886183722296598, 4);
INSERT INTO building(id, name, level) VALUES (276, "building_vineyard_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 276, 60.00000000000001, 73.2, 3);
INSERT INTO building(id, name, level) VALUES (277, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 277, 15.000000000000002, 1.370155424758263, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 277, 3.0, 0.18327221392153986, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 277, 75.0, 5.7162912359149045, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 277, 21.0, 1.6005615460561733, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 277, 15.000000000000002, 1.143258247182981, 3);
INSERT INTO building(id, name, level) VALUES (278, "building_furniture_manufacturies", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 278, 79.92319626168224, 34.11836408120618, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 278, 119.88479439252335, 21.24059438160958, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 278, 119.88479439252335, 4.873880201790988, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 278, 39.96159813084112, 2.4412835204273735, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 278, 319.6927943925233, 56.410582493416456, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 278, 199.808, 35.256614676793475, 8);
INSERT INTO building(id, name, level) VALUES (279, "building_iron_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 279, 4.9986, 0.609797255838082, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 279, 9.9972, 0.3917426709131512, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 279, 9.9972, 0.6107363256721394, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 279, 39.9888, 2.962764677681939, 1);
INSERT INTO building(id, name, level) VALUES (280, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 280, 5.0, 0.45671847491942097, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 280, 1.0, 0.06109073797384662, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 280, 24.999999999999996, 1.905430411971635, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 280, 7.0, 0.5335205153520579, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 280, 5.0, 0.38108608239432706, 1);
INSERT INTO building(id, name, level) VALUES (281, "building_vineyard_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 281, 60.0, 67.2, 3);
INSERT INTO building(id, name, level) VALUES (282, "building_tooling_workshops", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 282, 119.99999999999999, 21.261005940817714, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 282, 80.0, 0.40696252807050126, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 282, 320.0, 29.16193297723129, 4);
INSERT INTO building(id, name, level) VALUES (283, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 283, 4.9544, 0.45255320242815583, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 283, 0.9908727272727272, 0.06053314614724896, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 283, 24.771999999999995, 1.8880528866144535, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 283, 6.936154545454545, 0.5286543925217754, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 283, 4.9544, 0.3776105773228908, 1);
INSERT INTO building(id, name, level) VALUES (284, "building_lead_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 284, 20.0, 0.7837047791644685, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 284, 20.0, 1.2218147594769324, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 284, 80.0, 4.011039077282802, 2);
INSERT INTO building(id, name, level) VALUES (285, "building_sulfur_mine", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 285, 50.0, 1.959261947911171, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 285, 50.0, 3.0545368986923314, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (22, 285, 200.0, 10.027597693207005, 5);
INSERT INTO building(id, name, level) VALUES (286, "building_logging_camp", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 286, 25.0, 1.5272684493461657, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 286, 100.0, 6.109073797384663, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 286, 100.0, 6.109073797384663, 5);
INSERT INTO building(id, name, level) VALUES (287, "building_food_industry", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 287, 120.0, 71.98711692695808, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 287, 120.0, 69.20313913516542, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 287, 104.99999999999999, 61.770737027179024, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 287, 180.0, 105.89269204659263, 3);
INSERT INTO building(id, name, level) VALUES (288, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 288, 15.0, 0.9163610696076991, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 288, 60.0, 3.6654442784307966, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 288, 60.0, 3.6654442784307966, 3);
INSERT INTO building(id, name, level) VALUES (289, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 289, 9.950999999999999, 2.103301791058916, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 289, 99.50999999999999, 21.03301791058916, 2);
INSERT INTO building(id, name, level) VALUES (290, "building_wheat_farm", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 290, 30.000000000000004, 2.740310849516526, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 290, 6.000000000000001, 0.3665444278430798, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 290, 150.0, 11.432582471829809, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 290, 42.0, 3.2011230921123466, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 290, 30.000000000000004, 2.286516494365962, 6);
INSERT INTO building(id, name, level) VALUES (291, "building_vineyard_plantation", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 291, 100.00000000000001, 114.0, 5);
INSERT INTO building(id, name, level) VALUES (292, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 292, 4.857, 1.026604039711904, 1);
INSERT INTO building(id, name, level) VALUES (293, "building_glassworks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 293, 29.72519801980198, 5.266563430758275, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 293, 7.431297029702971, 3.1936775378717983, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 293, 14.862594059405941, 0.6383235877759184, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 293, 29.72519801980198, 6.439308479360742, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 293, 29.72519801980198, 6.439308479360742, 2);
INSERT INTO building(id, name, level) VALUES (294, "building_wheat_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 294, 22.102, 2.0188783465338087, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 294, 4.420395161290323, 0.2700452025392466, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 294, 110.50999999999999, 8.422764593079416, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 294, 30.942798387096776, 2.3583739631312426, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 294, 22.102, 1.6845529186158834, 5);
INSERT INTO building(id, name, level) VALUES (295, "building_vineyard_plantation", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 295, 79.52000000000001, 97.8096, 4);
INSERT INTO building(id, name, level) VALUES (296, "building_livestock_ranch", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 296, 22.398000000000003, 13.436395374416728, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 296, 22.398000000000003, 1.368310349138217, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 296, 44.79600000000001, 14.804705723554946, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 296, 11.199000000000002, 3.7011764308887365, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 296, 55.995000000000005, 18.505882154443682, 4);
INSERT INTO building(id, name, level) VALUES (538, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 538, 15.0, 1.370155424758263, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 538, 2.9999999999999996, 0.18327221392153983, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 538, 74.99999999999999, 5.7162912359149045, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 538, 20.999999999999996, 1.6005615460561733, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 538, 15.0, 1.143258247182981, 3);
INSERT INTO building(id, name, level) VALUES (539, "building_vineyard_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 539, 39.99999999999999, 44.4, 2);
INSERT INTO building(id, name, level) VALUES (540, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 540, 10.0, 5.998926410579841, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 540, 10.0, 0.6109073797384662, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 540, 20.0, 6.609833790318307, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 540, 5.0, 1.6524584475795767, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 540, 24.999999999999996, 8.262292237897883, 1);
INSERT INTO building(id, name, level) VALUES (541, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 541, 5.0, 0.3054536898692331, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 541, 20.0, 1.2218147594769324, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 541, 20.0, 1.2218147594769324, 1);
INSERT INTO building(id, name, level) VALUES (574, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 574, 9.9843, 5.98950809611523, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 574, 4.992145454545454, 0.30497384989096576, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 574, 19.9686, 6.599456351267507, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 574, 4.992145454545454, 1.6498625855819244, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 574, 14.976445454545454, 4.949590761215678, 1);
INSERT INTO building(id, name, level) VALUES (575, "building_fishing_wharf", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 575, 4.999, 1.056617993518593, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 575, 49.99, 10.566179935185932, 1);
INSERT INTO building(id, name, level) VALUES (576, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 576, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (1096, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1096, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (1097, "building_government_administration", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1097, 20.0, 2.408638917037992, 1);
INSERT INTO building(id, name, level) VALUES (1098, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1098, 5.0, 0.45671847491942097, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1098, 1.0, 0.06109073797384662, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1098, 25.0, 1.9054304119716352, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1098, 7.0, 0.5335205153520579, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1098, 5.0, 0.38108608239432706, 1);
INSERT INTO building(id, name, level) VALUES (1099, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1099, 10.0, 5.998926410579841, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1099, 20.0, 11.997852821159682, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 1099, 5.0, 2.9994632052899206, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1099, 5.0, 2.9994632052899206, 1);
INSERT INTO building(id, name, level) VALUES (1100, "building_government_administration", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1100, 40.0, 4.817277834075984, 2);
INSERT INTO building(id, name, level) VALUES (1102, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1102, 20.0, 22.0, 1);
INSERT INTO building(id, name, level) VALUES (1103, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1103, 1.7382, 1.0427333886869878, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1103, 3.4764, 2.0854667773739757, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1103, 3.4764, 2.0854667773739757, 1);
INSERT INTO building(id, name, level) VALUES (1105, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1105, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (1106, "building_wheat_farm", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1106, 17.186592920353984, 1.5698869015289978, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1106, 3.437318584070797, 0.20998832895210254, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1106, 85.933, 6.549574063678341, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1106, 24.061238938053098, 1.8338806568912982, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1106, 17.186592920353984, 1.309914273144755, 4);
INSERT INTO building(id, name, level) VALUES (1117, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1117, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (1118, "building_fishing_wharf", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1118, 4.887, 1.0329450158682465, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 1118, 48.87, 10.329450158682466, 1);
INSERT INTO building(id, name, level) VALUES (1119, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (32, 1119, 4.89, 0.4466706684711937, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1119, 0.978, 0.059746741738421996, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1119, 24.45, 1.8635109429082592, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1119, 6.846, 0.5217830640143126, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1119, 4.89, 0.3727021885816518, 1);
INSERT INTO building(id, name, level) VALUES (1146, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1146, 2.69, 0.5685741953520735, 1);
INSERT INTO building(id, name, level) VALUES (1147, "building_sugar_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1147, 59.838, 60.43638, 2);
INSERT INTO building(id, name, level) VALUES (1148, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1148, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (16778787, "building_cotton_plantation", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (16, 16778787, 5.0, 0.6099680468912116, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16778787, 40.0, 4.879744375129693, 1);
INSERT INTO building(id, name, level) VALUES (16778819, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16778819, 19.9998, 8.537702317574267, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16778819, 39.9996, 7.0869311102527695, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16778819, 49.9995, 0.634482226791017, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16778819, 9.9999, 0.6109012706646688, 1);
INSERT INTO building(id, name, level) VALUES (1613, "building_sugar_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1613, 89.23769642857141, 99.94622, 3);
INSERT INTO building(id, name, level) VALUES (1614, "building_coffee_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (41, 1614, 36.07599999999999, 40.04436, 2);
INSERT INTO building(id, name, level) VALUES (1615, "building_tobacco_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 1615, 49.51499999999999, 54.96165, 2);
INSERT INTO building(id, name, level) VALUES (1616, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1616, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (1641, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 1641, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (16778877, "building_conscription_center", 7);
INSERT INTO building(id, name, level) VALUES (2195, "building_sugar_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2195, 59.99999999999999, 66.6, 2);
INSERT INTO building(id, name, level) VALUES (2196, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2196, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (2217, "building_dye_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2217, 50.0, 65.5, 2);
INSERT INTO building(id, name, level) VALUES (2218, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2218, 5.0, 1.0568293593904714, 1);
INSERT INTO building(id, name, level) VALUES (2702, "building_subsistence_farms", 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2702, 65.69177272727272, 72.26095, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2702, 16.422936363636364, 18.06523, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2702, 16.422936363636364, 18.06523, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2702, 16.422936363636364, 18.06523, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2702, 16.422936363636364, 18.06523, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2702, 16.422936363636364, 18.06523, 33);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2702, 16.422936363636364, 18.06523, 33);
INSERT INTO building(id, name, level) VALUES (2703, "building_urban_center", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2703, 3.0, 0.531525148520443, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 2703, 3.0, 0.008392770845699858, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2703, 3.0, 0.7581028546756335, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2703, 60.0, 8.653471826945175, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2703, 24.0, 3.4613887307780704, 3);
INSERT INTO building(id, name, level) VALUES (2704, "building_subsistence_farms", 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2704, 226.1212727272727, 248.7334, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2704, 56.530318181818174, 62.18335, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2704, 56.530318181818174, 62.18335, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2704, 56.530318181818174, 62.18335, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2704, 56.530318181818174, 62.18335, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2704, 56.530318181818174, 62.18335, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2704, 56.530318181818174, 62.18335, 114);
INSERT INTO building(id, name, level) VALUES (2705, "building_urban_center", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2705, 7.0, 1.2402253465477002, 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2705, 7.0, 1.7689066609098119, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2705, 140.0, 30.09132007457512, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2705, 35.0, 7.52283001864378, 7);
INSERT INTO building(id, name, level) VALUES (16780112, "building_conscription_center", 5);
INSERT INTO building(id, name, level) VALUES (2956, "building_subsistence_farms", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2956, 9.572, 10.5292, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2956, 2.393, 2.6323, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2956, 2.393, 2.6323, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2956, 2.393, 2.6323, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2956, 2.393, 2.6323, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2956, 2.393, 2.6323, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2956, 2.393, 2.6323, 5);
INSERT INTO building(id, name, level) VALUES (3041, "building_subsistence_orchards", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3041, 0.2986181818181818, 0.32848, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3041, 0.1493090909090909, 0.16424, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3041, 0.4479272727272727, 0.49272, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3041, 0.2986181818181818, 0.32848, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3041, 0.2986181818181818, 0.32848, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3041, 0.2986181818181818, 0.32848, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3041, 0.7943272727272727, 0.87376, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3041, 0.2986181818181818, 0.32848, 18);
INSERT INTO building(id, name, level) VALUES (3046, "building_subsistence_farms", 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3046, 13.182776, 16.47847, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3046, 3.2956879999999997, 4.11961, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3046, 3.2956879999999997, 4.11961, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3046, 3.2956879999999997, 4.11961, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3046, 3.2956879999999997, 4.11961, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3046, 3.2956879999999997, 4.11961, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3046, 3.2956879999999997, 4.11961, 13);
INSERT INTO building(id, name, level) VALUES (3048, "building_subsistence_orchards", 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3048, 35.64755454545455, 39.21231, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3048, 17.823772727272726, 19.60615, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3048, 53.471336363636354, 58.81847, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3048, 35.64755454545455, 39.21231, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3048, 35.64755454545455, 39.21231, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3048, 35.64755454545455, 39.21231, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3048, 94.82250909090908, 104.30476, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3048, 35.64755454545455, 39.21231, 78);
INSERT INTO building(id, name, level) VALUES (3072, "building_subsistence_farms", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3072, 9.5544, 10.50984, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3072, 2.3886, 2.62746, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3072, 2.3886, 2.62746, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3072, 2.3886, 2.62746, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3072, 2.3886, 2.62746, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3072, 2.3886, 2.62746, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3072, 2.3886, 2.62746, 5);
INSERT INTO building(id, name, level) VALUES (3104, "building_subsistence_farms", 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3104, 210.3662727272727, 231.4029, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3104, 52.59156363636363, 57.85072, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3104, 52.59156363636363, 57.85072, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3104, 52.59156363636363, 57.85072, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3104, 52.59156363636363, 57.85072, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3104, 52.59156363636363, 57.85072, 107);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3104, 52.59156363636363, 57.85072, 107);
INSERT INTO building(id, name, level) VALUES (3105, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3105, 2.0, 0.3543500990136286, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3105, 2.0, 0.005595180563799905, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3105, 2.0, 0.5054019031170891, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3105, 40.0, 5.76898121796345, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3105, 16.0, 2.30759248718538, 2);
INSERT INTO building(id, name, level) VALUES (3106, "building_subsistence_farms", 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3106, 165.22477272727272, 181.74725, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3106, 41.30619090909091, 45.43681, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3106, 41.30619090909091, 45.43681, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3106, 41.30619090909091, 45.43681, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3106, 41.30619090909091, 45.43681, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3106, 41.30619090909091, 45.43681, 83);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3106, 41.30619090909091, 45.43681, 83);
INSERT INTO building(id, name, level) VALUES (3107, "building_urban_center", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3107, 5.0, 0.8858752475340715, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3107, 5.0, 0.013987951409499764, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3107, 5.0, 1.2635047577927225, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3107, 100.0, 14.422453044908625, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3107, 40.0, 5.76898121796345, 5);
INSERT INTO building(id, name, level) VALUES (3108, "building_subsistence_farms", 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3108, 124.5825, 149.499, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3108, 31.145625, 37.37475, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3108, 31.145625, 37.37475, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3108, 31.145625, 37.37475, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3108, 31.145625, 37.37475, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3108, 31.145625, 37.37475, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3108, 31.145625, 37.37475, 63);
INSERT INTO building(id, name, level) VALUES (3109, "building_urban_center", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3109, 4.0, 0.7087001980272573, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3109, 4.0, 0.01119036112759981, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3109, 4.0, 1.0108038062341782, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3109, 80.0, 11.5379624359269, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3109, 32.0, 4.61518497437076, 4);
INSERT INTO building(id, name, level) VALUES (3110, "building_subsistence_farms", 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3110, 144.4816, 173.37792, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3110, 36.1204, 43.34448, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3110, 36.1204, 43.34448, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3110, 36.1204, 43.34448, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3110, 36.1204, 43.34448, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3110, 36.1204, 43.34448, 73);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3110, 36.1204, 43.34448, 73);
INSERT INTO building(id, name, level) VALUES (3111, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3111, 1.0, 0.1771750495068143, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3111, 1.0, 0.0027975902818999526, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3111, 1.0, 0.25270095155854455, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3111, 20.0, 2.884490608981725, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3111, 8.0, 1.15379624359269, 1);
INSERT INTO building(id, name, level) VALUES (3112, "building_subsistence_farms", 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3112, 106.93311818181817, 117.62643, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3112, 26.733272727272727, 29.4066, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3112, 26.733272727272727, 29.4066, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3112, 26.733272727272727, 29.4066, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3112, 26.733272727272727, 29.4066, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3112, 26.733272727272727, 29.4066, 56);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3112, 26.733272727272727, 29.4066, 56);
INSERT INTO building(id, name, level) VALUES (3113, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3113, 2.0, 0.3543500990136286, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3113, 2.0, 0.005595180563799905, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3113, 2.0, 0.5054019031170891, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3113, 40.0, 5.76898121796345, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3113, 16.0, 2.30759248718538, 2);
INSERT INTO building(id, name, level) VALUES (3114, "building_subsistence_farms", 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3114, 165.54139999999998, 182.09554, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3114, 41.38534545454545, 45.52388, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3114, 41.38534545454545, 45.52388, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3114, 41.38534545454545, 45.52388, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3114, 41.38534545454545, 45.52388, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3114, 41.38534545454545, 45.52388, 86);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3114, 41.38534545454545, 45.52388, 86);
INSERT INTO building(id, name, level) VALUES (3115, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3115, 1.0, 0.1771750495068143, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3115, 1.0, 0.0027975902818999526, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3115, 1.0, 0.25270095155854455, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3115, 20.0, 2.884490608981725, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3115, 8.0, 1.15379624359269, 1);
INSERT INTO building(id, name, level) VALUES (3116, "building_subsistence_farms", 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3116, 154.2652727272727, 169.6918, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3116, 38.566318181818176, 42.42295, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3116, 38.566318181818176, 42.42295, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3116, 38.566318181818176, 42.42295, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3116, 38.566318181818176, 42.42295, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3116, 38.566318181818176, 42.42295, 78);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3116, 38.566318181818176, 42.42295, 78);
INSERT INTO building(id, name, level) VALUES (3117, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3117, 1.0, 0.1771750495068143, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3117, 1.0, 0.25270095155854455, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3117, 20.0, 4.298760010653588, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3117, 5.0, 1.074690002663397, 1);
INSERT INTO building(id, name, level) VALUES (3118, "building_subsistence_farms", 104);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3118, 206.60223636363637, 227.26246, 104);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3118, 51.65055454545454, 56.81561, 104);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3118, 51.65055454545454, 56.81561, 104);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3118, 51.65055454545454, 56.81561, 104);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3118, 51.65055454545454, 56.81561, 104);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3118, 51.65055454545454, 56.81561, 104);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3118, 51.65055454545454, 56.81561, 104);
INSERT INTO building(id, name, level) VALUES (3119, "building_urban_center", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3119, 3.0, 0.531525148520443, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3119, 3.0, 0.008392770845699858, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3119, 3.0, 0.7581028546756335, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3119, 60.0, 8.653471826945175, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3119, 24.0, 3.4613887307780704, 3);
INSERT INTO building(id, name, level) VALUES (3120, "building_subsistence_farms", 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3120, 220.8567, 242.94237, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3120, 55.214172727272725, 60.73559, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3120, 55.214172727272725, 60.73559, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3120, 55.214172727272725, 60.73559, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3120, 55.214172727272725, 60.73559, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3120, 55.214172727272725, 60.73559, 111);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3120, 55.214172727272725, 60.73559, 111);
INSERT INTO building(id, name, level) VALUES (3121, "building_urban_center", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3121, 3.0, 0.531525148520443, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3121, 3.0, 0.008392770845699858, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3121, 3.0, 0.7581028546756335, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3121, 60.0, 8.653471826945175, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3121, 24.0, 3.4613887307780704, 3);
INSERT INTO building(id, name, level) VALUES (3122, "building_subsistence_farms", 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3122, 118.6146727272727, 130.47614, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3122, 29.653663636363635, 32.61903, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3122, 29.653663636363635, 32.61903, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3122, 29.653663636363635, 32.61903, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3122, 29.653663636363635, 32.61903, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3122, 29.653663636363635, 32.61903, 62);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3122, 29.653663636363635, 32.61903, 62);
INSERT INTO building(id, name, level) VALUES (3123, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3123, 2.0, 0.3543500990136286, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3123, 2.0, 0.005595180563799905, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3123, 2.0, 0.5054019031170891, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3123, 40.0, 5.76898121796345, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3123, 16.0, 2.30759248718538, 2);
INSERT INTO building(id, name, level) VALUES (3124, "building_subsistence_farms", 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3124, 97.21083333333334, 116.653, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3124, 24.302708333333335, 29.16325, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3124, 24.302708333333335, 29.16325, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3124, 24.302708333333335, 29.16325, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3124, 24.302708333333335, 29.16325, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3124, 24.302708333333335, 29.16325, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3124, 24.302708333333335, 29.16325, 74);
INSERT INTO building(id, name, level) VALUES (3125, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3125, 2.0, 0.3543500990136286, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3125, 2.0, 0.005595180563799905, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3125, 2.0, 0.5054019031170891, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3125, 40.0, 5.76898121796345, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3125, 16.0, 2.30759248718538, 2);
INSERT INTO building(id, name, level) VALUES (3126, "building_subsistence_farms", 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3126, 73.00751818181817, 80.30827, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3126, 18.251872727272726, 20.07706, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3126, 18.251872727272726, 20.07706, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3126, 18.251872727272726, 20.07706, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3126, 18.251872727272726, 20.07706, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3126, 18.251872727272726, 20.07706, 46);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3126, 18.251872727272726, 20.07706, 46);
INSERT INTO building(id, name, level) VALUES (3127, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3127, 1.8464356435643565, 0.32714232655966136, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3127, 1.8464356435643565, 0.005165570412589329, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3127, 1.8464356435643565, 0.46659604412032646, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3127, 36.928792079207916, 5.326037697675697, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3127, 14.771514851485149, 2.13041479347715, 2);
INSERT INTO building(id, name, level) VALUES (3128, "building_subsistence_farms", 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3128, 94.39415454545453, 103.83357, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3128, 23.598536363636363, 25.95839, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3128, 23.598536363636363, 25.95839, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3128, 23.598536363636363, 25.95839, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3128, 23.598536363636363, 25.95839, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3128, 23.598536363636363, 25.95839, 54);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3128, 23.598536363636363, 25.95839, 54);
INSERT INTO building(id, name, level) VALUES (3129, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3129, 1.9774950495049506, 0.35036278329551984, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (29, 3129, 1.9774950495049506, 0.005532220933000315, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3129, 1.9774950495049506, 0.49971488071221215, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3129, 39.550000000000004, 5.7040801792613625, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3129, 15.819999999999999, 2.281632071704544, 2);
INSERT INTO building(id, name, level) VALUES (3130, "building_subsistence_farms", 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3130, 154.90793636363634, 170.39873, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3130, 38.72698181818181, 42.59968, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3130, 38.72698181818181, 42.59968, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3130, 38.72698181818181, 42.59968, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3130, 38.72698181818181, 42.59968, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3130, 38.72698181818181, 42.59968, 79);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3130, 38.72698181818181, 42.59968, 79);
INSERT INTO building(id, name, level) VALUES (3131, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3131, 1.0, 0.1771750495068143, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3131, 1.0, 0.25270095155854455, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3131, 20.0, 4.298760010653588, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3131, 5.0, 1.074690002663397, 1);
INSERT INTO building(id, name, level) VALUES (3132, "building_subsistence_farms", 106);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3132, 182.50231818181817, 200.75255, 106);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3132, 45.625572727272726, 50.18813, 106);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3132, 45.625572727272726, 50.18813, 106);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3132, 45.625572727272726, 50.18813, 106);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3132, 45.625572727272726, 50.18813, 106);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3132, 45.625572727272726, 50.18813, 106);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3132, 45.625572727272726, 50.18813, 106);
INSERT INTO building(id, name, level) VALUES (3133, "building_subsistence_farms", 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3133, 112.3356, 134.80272, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3133, 28.0839, 33.70068, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3133, 28.0839, 33.70068, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3133, 28.0839, 33.70068, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3133, 28.0839, 33.70068, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3133, 28.0839, 33.70068, 57);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3133, 28.0839, 33.70068, 57);
INSERT INTO building(id, name, level) VALUES (3134, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3134, 1.0, 0.1771750495068143, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3134, 1.0, 0.25270095155854455, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3134, 20.0, 4.298760010653588, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3134, 5.0, 1.074690002663397, 1);
INSERT INTO building(id, name, level) VALUES (3135, "building_subsistence_farms", 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3135, 51.25601666666667, 61.50722, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3135, 12.814, 15.3768, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3135, 12.814, 15.3768, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3135, 12.814, 15.3768, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3135, 12.814, 15.3768, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3135, 12.814, 15.3768, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3135, 12.814, 15.3768, 31);
INSERT INTO building(id, name, level) VALUES (3136, "building_urban_center", 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3136, 9.383785714285715, 1.6625726984899085, 13);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3136, 9.383785714285715, 2.371291579221477, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3136, 187.6757946428571, 40.338660048917454, 13);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3136, 46.918946428571424, 10.084664532457042, 13);
INSERT INTO building(id, name, level) VALUES (3177, "building_subsistence_fishing_villages", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3177, 2.4871, 2.73581, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 3177, 9.9484, 10.94324, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3177, 1.2435454545454543, 1.3679, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3177, 3.7306454545454546, 4.10371, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3177, 2.4871, 2.73581, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3177, 2.4871, 2.73581, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3177, 2.4871, 2.73581, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3177, 2.4871, 2.73581, 5);
INSERT INTO building(id, name, level) VALUES (3203, "building_subsistence_rice_paddies", 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3203, 31.957199999999997, 41.54436, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3203, 5.3262, 6.92406, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3203, 5.3262, 6.92406, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3203, 7.1015999999999995, 9.23208, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3203, 7.1015999999999995, 9.23208, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3203, 7.1015999999999995, 9.23208, 12);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3203, 7.1015999999999995, 9.23208, 12);
INSERT INTO building(id, name, level) VALUES (3400, "building_subsistence_farms", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3400, 3.8395999999999995, 4.22356, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3400, 0.9598999999999999, 1.05589, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3400, 0.9598999999999999, 1.05589, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3400, 0.9598999999999999, 1.05589, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3400, 0.9598999999999999, 1.05589, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3400, 0.9598999999999999, 1.05589, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3400, 0.9598999999999999, 1.05589, 2);
INSERT INTO building(id, name, level) VALUES (3407, "building_subsistence_orchards", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3407, 5.5843, 5.5843, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3407, 2.79215, 2.79215, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3407, 8.37645, 8.37645, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3407, 5.5843, 5.5843, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3407, 5.5843, 5.5843, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3407, 5.5843, 5.5843, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3407, 14.85423, 14.85423, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3407, 5.5843, 5.5843, 20);
INSERT INTO building(id, name, level) VALUES (3753, "building_subsistence_pastures", 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3753, 4.5, 4.95, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3753, 6.749999999999999, 7.425, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3753, 2.25, 2.475, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3753, 4.5, 4.95, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3753, 4.5, 4.95, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3753, 4.5, 4.95, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 3753, 11.969999999999999, 13.167, 9);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3753, 4.5, 4.95, 9);
INSERT INTO building(id, name, level) VALUES (3798, "building_barracks", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3798, 20.0, 1.0690366660627424, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3798, 40.0, 2.0142489070176968, 20);
INSERT INTO building(id, name, level) VALUES (3799, "building_barracks", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3799, 15.0, 0.8017774995470568, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3799, 30.0, 1.5106866802632726, 15);
INSERT INTO building(id, name, level) VALUES (3800, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3800, 20.0, 1.0071244535088484, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3800, 20.0, 11.997852821159682, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3800, 20.0, 0.25379542867069355, 10);
INSERT INTO building(id, name, level) VALUES (3801, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3801, 10.0, 0.2699160593656063, 5);
INSERT INTO building(id, name, level) VALUES (3802, "building_barracks", 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3802, 25.0, 1.3362958325784282, 25);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3802, 50.0, 2.517811133772121, 25);
INSERT INTO building(id, name, level) VALUES (3803, "building_barracks", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3803, 14.99985, 0.8017694817720614, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3803, 29.99985, 1.5106791268298712, 15);
INSERT INTO building(id, name, level) VALUES (3804, "building_barracks", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3804, 29.99985, 1.5106791268298712, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3804, 29.99985, 17.99668924784336, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3804, 29.99985, 0.38069123954032524, 15);
INSERT INTO building(id, name, level) VALUES (3805, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3805, 10.0, 0.2699160593656063, 5);
INSERT INTO building(id, name, level) VALUES (3806, "building_barracks", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3806, 14.99985, 0.8017694817720614, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3806, 29.99985, 1.5106791268298712, 15);
INSERT INTO building(id, name, level) VALUES (3807, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3807, 10.0, 0.5345183330313712, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3807, 20.0, 1.0071244535088484, 10);
INSERT INTO building(id, name, level) VALUES (3808, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3808, 20.0, 1.0071244535088484, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3808, 20.0, 11.997852821159682, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3808, 20.0, 0.25379542867069355, 10);
INSERT INTO building(id, name, level) VALUES (3809, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3809, 20.0, 0.5398321187312126, 10);
INSERT INTO building(id, name, level) VALUES (3810, "building_barracks", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3810, 2.0, 0.10690366660627425, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3810, 4.0, 0.20142489070176967, 2);
INSERT INTO building(id, name, level) VALUES (3811, "building_barracks", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3811, 14.99985, 0.8017694817720614, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3811, 29.99985, 1.5106791268298712, 15);
INSERT INTO building(id, name, level) VALUES (3812, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 3812, 10.0, 0.5345183330313712, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3812, 20.0, 1.0071244535088484, 10);
INSERT INTO building(id, name, level) VALUES (3813, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3813, 10.0, 0.2699160593656063, 5);
INSERT INTO building(id, name, level) VALUES (3814, "building_naval_base", 17);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3814, 28.4862, 2.9630752195366235, 17);
INSERT INTO building(id, name, level) VALUES (3815, "building_naval_base", 24);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3815, 36.60646, 3.8077277594399614, 24);
INSERT INTO building(id, name, level) VALUES (3816, "building_naval_base", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3816, 8.2581, 0.8589903697388698, 7);
INSERT INTO building(id, name, level) VALUES (3817, "building_naval_base", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3817, 10.62396, 1.1050822015343678, 6);
INSERT INTO building(id, name, level) VALUES (3818, "building_naval_base", 27);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3818, 48.68355, 5.063961518351769, 27);
INSERT INTO building(id, name, level) VALUES (3819, "building_naval_base", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3819, 14.324, 1.4899526593453178, 8);
INSERT INTO building(id, name, level) VALUES (3820, "building_naval_base", 16);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3820, 24.0, 2.496430035205783, 16);
INSERT INTO building(id, name, level) VALUES (16781190, "building_artillery_foundries", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16781190, 1.94175, 0.024640363681065956, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 16781190, 1.2945, 0.05262750754329118, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 16781190, 3.19741, 0.08528207121886677, 1);
INSERT INTO building(id, name, level) VALUES (4138, "building_trade_center", 54);
INSERT INTO building(id, name, level) VALUES (4184, "building_trade_center", 16);
INSERT INTO building(id, name, level) VALUES (4238, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 4238, 29.999999999999996, 33.0, 1);
INSERT INTO building(id, name, level) VALUES (16781528, "building_motor_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16781528, 30.0, 0.152610948026438, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (29, 16781528, 40.0, 0.20348126403525066, 1);
INSERT INTO building(id, name, level) VALUES (4496, "building_conscription_center", 14);
INSERT INTO building(id, name, level) VALUES (16781780, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16781780, 30.0, 5.315251485204429, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 16781780, 20.0, 0.10174063201762532, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 16781780, 80.0, 7.290483244307822, 1);
INSERT INTO building(id, name, level) VALUES (4582, "building_trade_center", 24);
INSERT INTO building(id, name, level) VALUES (4624, "building_fishing_wharf", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 4624, 2.70865, 0.5725161688626, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 4624, 27.0865, 5.725161688626001, 1);
INSERT INTO building(id, name, level) VALUES (4678, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 4678, 24.648, 27.1128, 1);
INSERT INTO building(id, name, level) VALUES (4780, "building_conscription_center", 8);
INSERT INTO building(id, name, level) VALUES (4832, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (4870, "building_banana_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 4870, 29.999999999999996, 33.0, 1);
INSERT INTO building(id, name, level) VALUES (4871, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (4919, "building_conscription_center", 11);
INSERT INTO building(id, name, level) VALUES (4925, "building_construction_sector", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4925, 59.9994, 25.6131069527228, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4925, 119.9988, 21.260793330758307, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4925, 149.9985, 1.9034466803730512, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4925, 29.9997, 1.8327038119940064, 3);
INSERT INTO building(id, name, level) VALUES (4945, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4945, 49.9995, 21.344255793935666, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4945, 149.9985, 26.575991663447887, 2);
INSERT INTO building(id, name, level) VALUES (4964, "building_conscription_center", 9);
INSERT INTO building(id, name, level) VALUES (4982, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (4994, "building_conscription_center", 11);
INSERT INTO building(id, name, level) VALUES (5000, "building_construction_sector", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5000, 34.19965, 14.599467547936916, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5000, 102.59897, 18.177977589098155, 3);
INSERT INTO building(id, name, level) VALUES (5016, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5016, 12.11987, 5.173843847823419, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5016, 24.23975, 4.294678906282802, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5016, 30.29969, 0.3844961406069563, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5016, 6.05993, 0.3702055957698523, 2);
INSERT INTO building(id, name, level) VALUES (5032, "building_conscription_center", 7);
INSERT INTO building(id, name, level) VALUES (5052, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (5062, "building_conscription_center", 10);
INSERT INTO building(id, name, level) VALUES (5102, "building_conscription_center", 8);
INSERT INTO building(id, name, level) VALUES (5131, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5131, 49.9995, 21.344255793935666, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5131, 149.9985, 26.575991663447887, 2);
INSERT INTO building(id, name, level) VALUES (16782379, "building_trade_center", 4);
INSERT INTO building(id, name, level) VALUES (5173, "building_construction_sector", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5173, 99.999, 42.68851158787133, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5173, 299.997, 53.151983326895774, 4);
INSERT INTO building(id, name, level) VALUES (5235, "building_construction_sector", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5235, 59.9994, 25.6131069527228, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5235, 119.9988, 21.260793330758307, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5235, 149.9985, 1.9034466803730512, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5235, 29.9997, 1.8327038119940064, 3);
INSERT INTO building(id, name, level) VALUES (5369, "building_construction_sector", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5369, 99.999, 42.68851158787133, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5369, 299.997, 53.151983326895774, 4);
INSERT INTO building(id, name, level) VALUES (5460, "building_construction_sector", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5460, 59.9874, 25.607984280105526, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5460, 119.9748, 21.256541129570145, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5460, 149.9685, 1.903065987230045, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5460, 29.9937, 1.8323372675661633, 3);
INSERT INTO building(id, name, level) VALUES (5520, "building_tooling_workshops", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5520, 30.0, 5.315251485204429, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (30, 5520, 20.0, 0.10174063201762532, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 5520, 80.0, 7.290483244307822, 1);
INSERT INTO building(id, name, level) VALUES (5638, "building_barracks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (0, 5638, 2.944944444444445, 0.15741267953144422, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 5638, 5.889922222222222, 0.2965942349632589, 3);
INSERT INTO building(id, name, level) VALUES (5647, "building_construction_sector", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5647, 79.9992, 34.15080927029707, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5647, 159.9984, 28.347724441011078, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5647, 199.998, 2.537928907164068, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5647, 39.9996, 2.4436050826586753, 4);
INSERT INTO building(id, name, level) VALUES (5650, "building_conscription_center", 1);
INSERT INTO building(id, name, level) VALUES (5656, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5656, 24.98975, 10.667859003120109, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5656, 74.96925, 13.28268058023874, 1);
INSERT INTO building(id, name, level) VALUES (5662, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5662, 19.9998, 8.537702317574267, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5662, 39.9996, 7.0869311102527695, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5662, 49.9995, 0.634482226791017, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5662, 9.9999, 0.6109012706646688, 1);
INSERT INTO building(id, name, level) VALUES (5672, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5672, 49.9995, 21.344255793935666, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5672, 149.9985, 26.575991663447887, 2);
INSERT INTO building(id, name, level) VALUES (5683, "building_construction_sector", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5683, 99.931, 42.65948310970679, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5683, 199.862, 35.410559744530914, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5683, 249.8275, 3.170253872811384, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5683, 49.9655, 3.0524292682322334, 5);
INSERT INTO building(id, name, level) VALUES (5689, "building_chemical_plants", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (22, 5689, 11.2563, 0.4902802662477832, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5689, 3.7521, 0.04761329139576546, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (32, 5689, 33.36367, 0.938283958131431, 1);
INSERT INTO building(id, name, level) VALUES (5714, "building_construction_sector", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5714, 99.931, 42.65948310970679, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5714, 199.862, 35.410559744530914, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5714, 249.8275, 3.170253872811384, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5714, 49.9655, 3.0524292682322334, 5);
INSERT INTO building(id, name, level) VALUES (16782940, "building_naval_base", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 16782940, 0.016, 0.0016642866901371886, 1);
INSERT INTO building(id, name, level) VALUES (5736, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5736, 19.9878, 8.532579644956995, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5736, 39.9756, 7.082678909064605, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5736, 49.9695, 0.6341015336480109, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5736, 9.9939, 0.6105347262368257, 1);
INSERT INTO building(id, name, level) VALUES (5779, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 5779, 20.0, 22.0, 1);
