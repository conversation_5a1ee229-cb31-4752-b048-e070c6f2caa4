
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 64.22697732924055, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 74.93147355078064, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 71.87652234430054, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 22.051444677435367, 5000.628950106441);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 21.974079098289664, 383.4661700843082);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 18.781797071147125, 679.1854805338364);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 26.34386469636432, 415.6965708961683);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 45.72397583010389, 343.26834560744123);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 28.211561711413815, 1222.1850406370784);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 29.276221948347413, 1003.0188975963235);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 43.32644042120042, 51.84976821306489);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 16.091313253077267, 512.3956779343366);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 69.08964893677442, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 33.07692307692307, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 38.43409725333774, 35.83023404666434);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 46.84831055353129, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 48.537249336468605, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 44.664437364277674, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 57.729538010735844, 81.1582212556401);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 54.05156877486541, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 114.77948743858013, 245.55938479999105);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 39.65341380079986, 267.8824511537757);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 37.95433664736363, 307.6690207592952);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 34.58267201924185, 2046.7990883333332);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 64.25025629368145, 466.71482219612756);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 87.5, 44.57274780387275);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 43.72895896449816, 65.1534650133238);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 85.064159409215, 361.15858607577735);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 87.90501530594456, 272.75347332423325);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 24.99999999999999, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 297.73410619945946, 2.3441803098495764);
INSERT INTO building(id, name, level) VALUES (389, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 389, 99.99999999999999, 40.84022637463872, 10);
INSERT INTO building(id, name, level) VALUES (390, "building_arts_academylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 390, 5.0, 2.0420113187319364, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 390, 2.0, 0.8168045274927745, 2);
INSERT INTO building(id, name, level) VALUES (391, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 391, 47.339, 51.52339686430286, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 391, 142.017, 82.05474196492209, 2);
INSERT INTO building(id, name, level) VALUES (392, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 392, 10.0, 4.084022637463873, 2);
INSERT INTO building(id, name, level) VALUES (393, "building_furniture_manufacturieslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 393, 50.0, 54.41960842466345, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 393, 100.0, 57.7781124547921, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 393, 50.0, 42.305249942498435, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 393, 25.0, 13.414219164707118, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 393, 225.0, 166.52558756172232, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 393, 100.0, 74.01137224965436, 5);
INSERT INTO building(id, name, level) VALUES (394, "building_paper_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 394, 150.0, 86.66716868218815, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 394, 200.0, 115.55622490958419, 5);
INSERT INTO building(id, name, level) VALUES (395, "building_wheat_farmlevel", 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 395, 99.99999999999999, 109.0, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 395, 89.99999999999999, 98.1, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 395, 60.0, 65.4, 10);
INSERT INTO building(id, name, level) VALUES (396, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 396, 90.0, 91.8, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 396, 15.0, 15.3, 3);
INSERT INTO building(id, name, level) VALUES (397, "building_barrackslevel", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 397, 20.0, 18.275941536311972, 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 397, 20.0, 18.275941536311972, 20);
INSERT INTO building(id, name, level) VALUES (398, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 398, 99.99999999999999, 40.84022637463872, 10);
INSERT INTO building(id, name, level) VALUES (399, "building_arms_industrylevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 399, 100.0, 78.07889427783589, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 399, 50.0, 42.305249942498435, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 399, 75.0, 61.008522811062285, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 399, 75.0, 61.008522811062285, 5);
INSERT INTO building(id, name, level) VALUES (400, "building_construction_sectorlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 400, 47.339, 51.52339686430286, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 400, 142.017, 82.05474196492209, 2);
INSERT INTO building(id, name, level) VALUES (401, "building_universitylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 401, 10.0, 4.084022637463873, 2);
INSERT INTO building(id, name, level) VALUES (402, "building_food_industrylevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 402, 160.0, 138.11792344068945, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 402, 100.0, 38.982404602230375, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 402, 59.99999999999999, 37.591832025798375, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 402, 239.99999999999997, 150.3673281031935, 4);
INSERT INTO building(id, name, level) VALUES (403, "building_glassworkslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 403, 60.0, 34.66686747287526, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 403, 30.0, 21.69539983132875, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 403, 120.0, 78.05766713553275, 3);
INSERT INTO building(id, name, level) VALUES (404, "building_iron_minelevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 404, 50.0, 31.596383361155343, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 404, 50.0, 26.828438329414237, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 404, 200.0, 116.84964338113917, 5);
INSERT INTO building(id, name, level) VALUES (405, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 405, 15.0, 8.048531498824271, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 405, 120.0, 64.38825199059417, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 405, 30.0, 16.097062997648543, 3);
INSERT INTO building(id, name, level) VALUES (406, "building_rye_farmlevel", 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 406, 70.0, 74.2, 7);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 406, 104.99999999999999, 111.3, 7);
INSERT INTO building(id, name, level) VALUES (407, "building_barrackslevel", 30);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 407, 30.0, 27.413912304467956, 30);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 407, 30.0, 27.413912304467956, 30);
INSERT INTO building(id, name, level) VALUES (408, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 408, 10.0, 5.365687665882847, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 408, 50.0, 26.828438329414233, 2);
INSERT INTO building(id, name, level) VALUES (409, "building_textile_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 409, 120.0, 130.60706021919228, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 409, 135.0, 135.0, 3);
INSERT INTO building(id, name, level) VALUES (410, "building_food_industrylevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 410, 120.0, 103.58844258051708, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 410, 75.0, 29.23680345167278, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 410, 45.0, 28.193874019348787, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 410, 180.0, 112.77549607739515, 3);
INSERT INTO building(id, name, level) VALUES (411, "building_logging_camplevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 411, 15.0, 8.048531498824271, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 411, 120.0, 64.38825199059417, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 411, 30.0, 16.097062997648543, 3);
INSERT INTO building(id, name, level) VALUES (412, "building_wheat_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 412, 40.0, 41.2, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 412, 36.0, 37.08, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 412, 24.0, 24.72, 4);
INSERT INTO building(id, name, level) VALUES (413, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 413, 10.0, 9.137970768155986, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 413, 10.0, 9.137970768155986, 10);
INSERT INTO building(id, name, level) VALUES (414, "building_coal_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 414, 10.0, 5.365687665882847, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 414, 50.0, 26.828438329414233, 2);
INSERT INTO building(id, name, level) VALUES (415, "building_iron_minelevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 415, 15.0, 8.048531498824271, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 415, 60.0, 32.194125995297085, 3);
INSERT INTO building(id, name, level) VALUES (416, "building_lead_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 416, 10.0, 5.365687665882847, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 416, 40.0, 21.462750663531388, 2);
INSERT INTO building(id, name, level) VALUES (417, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 417, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 417, 8.0, 8.0, 1);
INSERT INTO building(id, name, level) VALUES (418, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 418, 60.0, 60.6, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 418, 10.0, 10.1, 2);
INSERT INTO building(id, name, level) VALUES (419, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 419, 10.0, 9.137970768155986, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 419, 10.0, 9.137970768155986, 10);
INSERT INTO building(id, name, level) VALUES (420, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 420, 10.0, 5.365687665882847, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 420, 40.0, 21.462750663531388, 2);
INSERT INTO building(id, name, level) VALUES (421, "building_gold_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 421, 10.0, 5.365687665882847, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 421, 20.0, 10.731375331765694, 2);
INSERT INTO building(id, name, level) VALUES (422, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 422, 30.0, 30.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 422, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (423, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 423, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 423, 8.0, 8.0, 1);
INSERT INTO building(id, name, level) VALUES (424, "building_barrackslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 424, 2.0, 1.8275941536311973, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 424, 2.0, 1.8275941536311973, 2);
INSERT INTO building(id, name, level) VALUES (600, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 600, 30.0, 12.252067912391617, 3);
INSERT INTO building(id, name, level) VALUES (601, "building_construction_sectorlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 601, 71.0085, 77.28509529645429, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 601, 213.0255, 123.08211294738315, 3);
INSERT INTO building(id, name, level) VALUES (602, "building_textile_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 602, 200.0, 217.6784336986538, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 602, 225.0, 225.0, 5);
INSERT INTO building(id, name, level) VALUES (603, "building_arms_industrylevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 603, 199.99999999999997, 156.15778855567174, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 603, 99.99999999999999, 84.61049988499686, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 603, 150.0, 122.01704562212457, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 603, 150.0, 122.01704562212457, 10);
INSERT INTO building(id, name, level) VALUES (604, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 604, 45.0, 45.9, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 604, 24.0, 24.48, 3);
INSERT INTO building(id, name, level) VALUES (605, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 605, 15.0, 13.706956152233978, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 605, 15.0, 13.706956152233978, 15);
INSERT INTO building(id, name, level) VALUES (708, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 708, 30.0, 12.252067912391617, 3);
INSERT INTO building(id, name, level) VALUES (709, "building_paper_millslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 709, 150.0, 86.66716868218815, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 709, 200.0, 115.55622490958419, 5);
INSERT INTO building(id, name, level) VALUES (710, "building_glassworkslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 710, 150.0, 86.66716868218815, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 710, 75.0, 0.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 710, 50.0, 14.444528113698023, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 710, 125.0, 36.11132028424506, 5);
INSERT INTO building(id, name, level) VALUES (711, "building_textile_millslevel", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 711, 240.0, 261.21412043838455, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 711, 79.99999999999999, 104.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 711, 200.0, 200.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 711, 159.99999999999997, 159.99999999999997, 8);
INSERT INTO building(id, name, level) VALUES (712, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 712, 10.0, 9.137970768155986, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 712, 10.0, 9.137970768155986, 10);
INSERT INTO building(id, name, level) VALUES (713, "building_fishing_wharflevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 713, 100.0, 103.0, 4);
INSERT INTO building(id, name, level) VALUES (714, "building_silk_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 714, 100.0, 104.0, 5);
INSERT INTO building(id, name, level) VALUES (715, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 715, 30.0, 30.3, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 715, 16.0, 16.16, 2);
INSERT INTO building(id, name, level) VALUES (716, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 716, 15.0, 11.977519318381201, 3);
INSERT INTO building(id, name, level) VALUES (873, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 873, 30.0, 30.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 873, 27.0, 27.54, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 873, 18.0, 18.36, 3);
INSERT INTO building(id, name, level) VALUES (874, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 874, 30.0, 30.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 874, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (875, "building_logging_camplevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 875, 20.0, 20.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 875, 20.0, 20.2, 2);
INSERT INTO building(id, name, level) VALUES (876, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 876, 5.0, 4.568985384077993, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 876, 5.0, 4.568985384077993, 5);
INSERT INTO building(id, name, level) VALUES (877, "building_fishing_wharflevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 877, 75.0, 76.5, 3);
INSERT INTO building(id, name, level) VALUES (878, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 878, 15.000000000000002, 17.25, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 878, 8.0, 9.2, 1);
INSERT INTO building(id, name, level) VALUES (879, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 879, 5.0, 4.568985384077993, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 879, 5.0, 4.568985384077993, 5);
INSERT INTO building(id, name, level) VALUES (880, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 880, 5.0, 3.9925064394604006, 1);
INSERT INTO building(id, name, level) VALUES (881, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 881, 30.0, 12.252067912391617, 3);
INSERT INTO building(id, name, level) VALUES (882, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 882, 30.0, 34.8, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 882, 16.0, 18.56, 2);
INSERT INTO building(id, name, level) VALUES (883, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 883, 30.000000000000004, 34.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 883, 5.0, 5.75, 1);
INSERT INTO building(id, name, level) VALUES (884, "building_logging_camplevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 884, 90.0, 91.8, 3);
INSERT INTO building(id, name, level) VALUES (885, "building_fishing_wharflevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 885, 50.0, 50.5, 2);
INSERT INTO building(id, name, level) VALUES (886, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 886, 5.0, 3.9925064394604006, 1);
INSERT INTO building(id, name, level) VALUES (887, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 887, 10.0, 9.137970768155986, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 887, 10.0, 9.137970768155986, 10);
INSERT INTO building(id, name, level) VALUES (888, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 888, 20.0, 8.168045274927746, 2);
INSERT INTO building(id, name, level) VALUES (889, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 889, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 889, 9.0, 9.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 889, 6.0, 6.0, 1);
INSERT INTO building(id, name, level) VALUES (890, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 890, 30.0, 30.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 890, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (891, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 891, 5.0, 4.568985384077993, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 891, 5.0, 4.568985384077993, 5);
INSERT INTO building(id, name, level) VALUES (892, "building_tooling_workshopslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 892, 90.0, 52.000301209312894, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 892, 60.0, 46.84733656670153, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 892, 180.0, 122.2713060593652, 3);
INSERT INTO building(id, name, level) VALUES (893, "building_shipyardslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 893, 40.0, 43.53568673973076, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 893, 80.0, 46.22248996383368, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 893, 30.0, 23.666716868218813, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 893, 40.0, 31.55562249095842, 2);
INSERT INTO building(id, name, level) VALUES (894, "building_fishing_wharflevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 894, 100.0, 103.0, 4);
INSERT INTO building(id, name, level) VALUES (895, "building_naval_baselevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 895, 20.0, 19.297060234955506, 10);
INSERT INTO building(id, name, level) VALUES (896, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 896, 10.0, 7.985012878920801, 2);
INSERT INTO building(id, name, level) VALUES (900, "building_naval_baselevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 900, 4.544, 4.38429208538189, 5);
INSERT INTO building(id, name, level) VALUES (901, "building_portlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 901, 4.543, 3.6275913508937205, 2);
INSERT INTO building(id, name, level) VALUES (1078, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1078, 50.0, 20.420113187319362, 5);
INSERT INTO building(id, name, level) VALUES (1079, "building_rye_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1079, 20.0, 20.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1079, 30.0, 30.3, 2);
INSERT INTO building(id, name, level) VALUES (1080, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1080, 30.0, 30.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1080, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (1081, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1081, 5.0, 4.568985384077993, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1081, 5.0, 4.568985384077993, 5);
INSERT INTO building(id, name, level) VALUES (1085, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1085, 30.0, 12.252067912391617, 3);
INSERT INTO building(id, name, level) VALUES (1086, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1086, 45.0, 45.9, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1086, 24.0, 24.48, 3);
INSERT INTO building(id, name, level) VALUES (1087, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1087, 60.0, 60.6, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1087, 10.0, 10.1, 2);
INSERT INTO building(id, name, level) VALUES (1088, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1088, 5.0, 4.568985384077993, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1088, 5.0, 4.568985384077993, 5);
INSERT INTO building(id, name, level) VALUES (1089, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1089, 50.0, 20.420113187319362, 5);
INSERT INTO building(id, name, level) VALUES (1090, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1090, 23.6695, 25.76169843215143, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1090, 71.0085, 41.02737098246104, 1);
INSERT INTO building(id, name, level) VALUES (1091, "building_iron_minelevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1091, 10.0, 5.365687665882847, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1091, 40.0, 21.462750663531388, 2);
INSERT INTO building(id, name, level) VALUES (1092, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1092, 80.0, 69.05896172034473, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1092, 50.0, 19.491202301115187, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1092, 30.0, 18.79591601289919, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1092, 120.0, 75.18366405159676, 2);
INSERT INTO building(id, name, level) VALUES (1093, "building_wheat_farmlevel", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1093, 90.0, 94.5, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1093, 48.0, 50.4, 6);
INSERT INTO building(id, name, level) VALUES (1094, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1094, 90.0, 91.8, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1094, 15.0, 15.3, 3);
INSERT INTO building(id, name, level) VALUES (1095, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1095, 15.0, 13.706956152233978, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1095, 15.0, 13.706956152233978, 15);
INSERT INTO building(id, name, level) VALUES (1096, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1096, 45.0, 45.9, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1096, 24.0, 24.48, 3);
INSERT INTO building(id, name, level) VALUES (1097, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1097, 60.0, 60.6, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1097, 10.0, 10.1, 2);
INSERT INTO building(id, name, level) VALUES (1098, "building_barrackslevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1098, 10.0, 9.137970768155986, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1098, 10.0, 9.137970768155986, 10);
INSERT INTO building(id, name, level) VALUES (1099, "building_food_industrylevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1099, 80.0, 69.05896172034473, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 1099, 50.0, 19.491202301115187, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 1099, 30.0, 18.79591601289919, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 1099, 120.0, 75.18366405159676, 2);
INSERT INTO building(id, name, level) VALUES (1100, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1100, 45.0, 45.9, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1100, 24.0, 24.48, 3);
INSERT INTO building(id, name, level) VALUES (1101, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1101, 90.0, 91.8, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1101, 15.0, 15.3, 3);
INSERT INTO building(id, name, level) VALUES (1102, "building_barrackslevel", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1102, 15.0, 13.706956152233978, 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1102, 15.0, 13.706956152233978, 15);
INSERT INTO building(id, name, level) VALUES (1103, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1103, 45.0, 45.9, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1103, 24.0, 24.48, 3);
INSERT INTO building(id, name, level) VALUES (1104, "building_livestock_ranchlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1104, 90.0, 91.8, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1104, 15.0, 15.3, 3);
INSERT INTO building(id, name, level) VALUES (1105, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1105, 5.0, 4.568985384077993, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1105, 5.0, 4.568985384077993, 5);
INSERT INTO building(id, name, level) VALUES (1106, "building_wheat_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1106, 45.0, 45.9, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1106, 24.0, 24.48, 3);
INSERT INTO building(id, name, level) VALUES (1107, "building_livestock_ranchlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1107, 119.99999999999999, 123.6, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1107, 20.0, 20.6, 4);
INSERT INTO building(id, name, level) VALUES (1108, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1108, 5.0, 4.568985384077993, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1108, 5.0, 4.568985384077993, 5);
INSERT INTO building(id, name, level) VALUES (1109, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1109, 30.0, 12.252067912391617, 3);
INSERT INTO building(id, name, level) VALUES (1110, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1110, 20.0, 21.76784336986538, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1110, 40.0, 23.11124498191684, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1110, 20.0, 16.922099976999373, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1110, 10.0, 5.365687665882847, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1110, 90.0, 66.61023502468892, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1110, 40.0, 29.604548899861744, 2);
INSERT INTO building(id, name, level) VALUES (1111, "building_iron_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1111, 20.0, 10.731375331765694, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1111, 80.0, 42.925501327062776, 4);
INSERT INTO building(id, name, level) VALUES (1112, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1112, 20.0, 20.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1112, 18.0, 18.18, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1112, 12.0, 12.12, 2);
INSERT INTO building(id, name, level) VALUES (1113, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1113, 5.0, 4.568985384077993, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1113, 5.0, 4.568985384077993, 5);
INSERT INTO building(id, name, level) VALUES (1114, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1114, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1114, 9.0, 9.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1114, 6.0, 6.0, 1);
INSERT INTO building(id, name, level) VALUES (1115, "building_logging_camplevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1115, 30.0, 30.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1115, 30.0, 30.6, 3);
INSERT INTO building(id, name, level) VALUES (1116, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1116, 5.0, 4.568985384077993, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1116, 5.0, 4.568985384077993, 5);
INSERT INTO building(id, name, level) VALUES (1117, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1117, 5.0, 4.568985384077993, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1117, 5.0, 4.568985384077993, 5);
INSERT INTO building(id, name, level) VALUES (1118, "building_wheat_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1118, 30.0, 30.3, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1118, 16.0, 16.16, 2);
INSERT INTO building(id, name, level) VALUES (1119, "building_logging_camplevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1119, 50.0, 52.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1119, 50.0, 52.0, 5);
INSERT INTO building(id, name, level) VALUES (1120, "building_iron_minelevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1120, 20.0, 10.731375331765694, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1120, 80.0, 42.925501327062776, 4);
INSERT INTO building(id, name, level) VALUES (1121, "building_government_administrationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1121, 20.0, 8.168045274927746, 2);
INSERT INTO building(id, name, level) VALUES (1122, "building_furniture_manufacturieslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1122, 10.0, 10.88392168493269, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1122, 30.0, 17.33343373643763, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1122, 45.0, 35.50007530232822, 1);
INSERT INTO building(id, name, level) VALUES (1123, "building_logging_camplevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1123, 30.0, 30.6, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1123, 30.0, 30.6, 3);
INSERT INTO building(id, name, level) VALUES (1124, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1124, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1124, 8.0, 8.0, 1);
INSERT INTO building(id, name, level) VALUES (1125, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1125, 30.0, 30.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1125, 5.0, 5.0, 1);
INSERT INTO building(id, name, level) VALUES (1126, "building_barrackslevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 1126, 5.0, 4.568985384077993, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 1126, 5.0, 4.568985384077993, 5);
INSERT INTO building(id, name, level) VALUES (1127, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1127, 15.0, 15.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1127, 8.0, 8.0, 1);
INSERT INTO building(id, name, level) VALUES (1128, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1128, 60.0, 60.6, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1128, 10.0, 10.1, 2);
INSERT INTO building(id, name, level) VALUES (1129, "building_logging_camplevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1129, 20.0, 20.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1129, 20.0, 20.2, 2);
INSERT INTO building(id, name, level) VALUES (2902, "building_subsistence_farmslevel", 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2902, 132.2085, 132.2085, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2902, 26.4417, 26.4417, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2902, 26.4417, 26.4417, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2902, 26.4417, 26.4417, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2902, 26.4417, 26.4417, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2902, 26.4417, 26.4417, 53);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2902, 37.01838, 37.01838, 53);
INSERT INTO building(id, name, level) VALUES (2903, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2903, 30.0, 17.33343373643763, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2903, 30.0, 12.36683155519613, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2903, 240.0, 118.80106116653505, 6);
INSERT INTO building(id, name, level) VALUES (3022, "building_subsistence_farmslevel", 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3022, 119.6112, 119.6112, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3022, 23.92224, 23.92224, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3022, 23.92224, 23.92224, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3022, 23.92224, 23.92224, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3022, 23.92224, 23.92224, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3022, 23.92224, 23.92224, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3022, 33.49113, 33.49113, 48);
INSERT INTO building(id, name, level) VALUES (3024, "building_subsistence_farmslevel", 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3024, 119.6868, 119.6868, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3024, 23.93736, 23.93736, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3024, 23.93736, 23.93736, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3024, 23.93736, 23.93736, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3024, 23.93736, 23.93736, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3024, 23.93736, 23.93736, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3024, 33.5123, 33.5123, 48);
INSERT INTO building(id, name, level) VALUES (3025, "building_subsistence_farmslevel", 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3025, 40.6068, 40.6068, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3025, 8.12136, 8.12136, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3025, 8.12136, 8.12136, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3025, 8.12136, 8.12136, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3025, 8.12136, 8.12136, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3025, 8.12136, 8.12136, 24);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3025, 11.3699, 11.3699, 24);
INSERT INTO building(id, name, level) VALUES (3638, "building_subsistence_farmslevel", 2);
INSERT INTO building(id, name, level) VALUES (3839, "building_subsistence_farmslevel", 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3839, 261.88837, 261.88837, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3839, 52.37767, 52.37767, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3839, 52.37767, 52.37767, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3839, 52.37767, 52.37767, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3839, 52.37767, 52.37767, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3839, 52.37767, 52.37767, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3839, 73.32874, 73.32874, 105);
INSERT INTO building(id, name, level) VALUES (3840, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3840, 5.0, 2.888905622739605, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3840, 5.0, 2.0611385925326884, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3840, 40.0, 19.800176861089174, 1);
INSERT INTO building(id, name, level) VALUES (3842, "building_subsistence_farmslevel", 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3842, 312.23437, 312.23437, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3842, 62.44687, 62.44687, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3842, 62.44687, 62.44687, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3842, 62.44687, 62.44687, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3842, 62.44687, 62.44687, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3842, 62.44687, 62.44687, 125);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3842, 87.42562, 87.42562, 125);
INSERT INTO building(id, name, level) VALUES (3843, "building_subsistence_farmslevel", 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3843, 244.58595, 244.58595, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3843, 48.91719, 48.91719, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3843, 48.91719, 48.91719, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3843, 48.91719, 48.91719, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3843, 48.91719, 48.91719, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3843, 48.91719, 48.91719, 98);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3843, 68.48406, 68.48406, 98);
INSERT INTO building(id, name, level) VALUES (3844, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3844, 5.0, 2.888905622739605, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3844, 5.0, 2.0611385925326884, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3844, 40.0, 19.800176861089174, 1);
INSERT INTO building(id, name, level) VALUES (3845, "building_subsistence_farmslevel", 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3845, 169.49677, 169.49677, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3845, 33.89935, 33.89935, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3845, 33.89935, 33.89935, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3845, 33.89935, 33.89935, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3845, 33.89935, 33.89935, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3845, 33.89935, 33.89935, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3845, 47.45909, 47.45909, 69);
INSERT INTO building(id, name, level) VALUES (3846, "building_subsistence_farmslevel", 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3846, 41.409547826086964, 47.62098, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3846, 8.281904347826089, 9.52419, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3846, 8.281904347826089, 9.52419, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3846, 8.281904347826089, 9.52419, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3846, 8.281904347826089, 9.52419, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3846, 8.281904347826089, 9.52419, 19);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3846, 11.59466956521739, 13.33387, 19);
INSERT INTO building(id, name, level) VALUES (3847, "building_subsistence_farmslevel", 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3847, 78.6944, 90.49856, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3847, 15.738878260869567, 18.09971, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3847, 15.738878260869567, 18.09971, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3847, 15.738878260869567, 18.09971, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3847, 15.738878260869567, 18.09971, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3847, 15.738878260869567, 18.09971, 32);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3847, 22.034426086956525, 25.33959, 32);
INSERT INTO building(id, name, level) VALUES (3848, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3848, 5.0, 2.888905622739605, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3848, 5.0, 2.0611385925326884, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3848, 40.0, 19.800176861089174, 1);
INSERT INTO building(id, name, level) VALUES (3849, "building_subsistence_farmslevel", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3849, 39.7998, 39.7998, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3849, 7.95996, 7.95996, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3849, 7.95996, 7.95996, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3849, 7.95996, 7.95996, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3849, 7.95996, 7.95996, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3849, 7.95996, 7.95996, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3849, 11.14394, 11.14394, 18);
INSERT INTO building(id, name, level) VALUES (3850, "building_subsistence_farmslevel", 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3850, 184.7558, 184.7558, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3850, 36.95116, 36.95116, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3850, 36.95116, 36.95116, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3850, 36.95116, 36.95116, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3850, 36.95116, 36.95116, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3850, 36.95116, 36.95116, 74);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3850, 51.73162, 51.73162, 74);
INSERT INTO building(id, name, level) VALUES (3851, "building_subsistence_farmslevel", 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3851, 284.57535, 284.57535, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3851, 56.91507, 56.91507, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3851, 56.91507, 56.91507, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3851, 56.91507, 56.91507, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3851, 56.91507, 56.91507, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3851, 56.91507, 56.91507, 114);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3851, 79.68109, 79.68109, 114);
INSERT INTO building(id, name, level) VALUES (3852, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3852, 5.0, 2.888905622739605, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3852, 5.0, 2.0611385925326884, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3852, 40.0, 19.800176861089174, 1);
INSERT INTO building(id, name, level) VALUES (3853, "building_subsistence_farmslevel", 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3853, 60.53061739130435, 69.61021, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3853, 12.106121739130437, 13.92204, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3853, 12.106121739130437, 13.92204, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3853, 12.106121739130437, 13.92204, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3853, 12.106121739130437, 13.92204, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3853, 12.106121739130437, 13.92204, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3853, 16.948573913043482, 19.49086, 25);
INSERT INTO building(id, name, level) VALUES (3854, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3854, 5.0, 2.888905622739605, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3854, 5.0, 2.0611385925326884, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3854, 40.0, 19.800176861089174, 1);
INSERT INTO building(id, name, level) VALUES (3855, "building_subsistence_farmslevel", 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3855, 241.93497, 241.93497, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3855, 48.38699, 48.38699, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3855, 48.38699, 48.38699, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3855, 48.38699, 48.38699, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3855, 48.38699, 48.38699, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3855, 48.38699, 48.38699, 97);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3855, 67.74179, 67.74179, 97);
INSERT INTO building(id, name, level) VALUES (3856, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3856, 20.0, 11.55562249095842, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3856, 20.0, 8.244554370130754, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3856, 160.0, 79.2007074443567, 4);
INSERT INTO building(id, name, level) VALUES (3857, "building_subsistence_farmslevel", 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3857, 431.45335, 431.45335, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3857, 86.29067, 86.29067, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3857, 86.29067, 86.29067, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3857, 86.29067, 86.29067, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3857, 86.29067, 86.29067, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3857, 86.29067, 86.29067, 173);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3857, 120.80693, 120.80693, 173);
INSERT INTO building(id, name, level) VALUES (3858, "building_urban_centerlevel", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3858, 30.0, 17.33343373643763, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3858, 30.0, 12.36683155519613, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3858, 240.0, 118.80106116653505, 6);
INSERT INTO building(id, name, level) VALUES (3859, "building_subsistence_farmslevel", 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3859, 189.5421, 189.5421, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3859, 37.90842, 37.90842, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3859, 37.90842, 37.90842, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3859, 37.90842, 37.90842, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3859, 37.90842, 37.90842, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3859, 37.90842, 37.90842, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3859, 53.07178, 53.07178, 76);
INSERT INTO building(id, name, level) VALUES (3860, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3860, 5.0, 2.888905622739605, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3860, 5.0, 2.0611385925326884, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3860, 40.0, 19.800176861089174, 1);
INSERT INTO building(id, name, level) VALUES (3861, "building_subsistence_farmslevel", 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3861, 227.16785, 227.16785, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3861, 45.43357, 45.43357, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3861, 45.43357, 45.43357, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3861, 45.43357, 45.43357, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3861, 45.43357, 45.43357, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3861, 45.43357, 45.43357, 91);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3861, 63.60699, 63.60699, 91);
INSERT INTO building(id, name, level) VALUES (3862, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3862, 10.0, 5.77781124547921, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3862, 10.0, 4.122277185065377, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3862, 80.0, 39.60035372217835, 2);
INSERT INTO building(id, name, level) VALUES (3863, "building_subsistence_farmslevel", 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3863, 80.12812, 80.12812, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3863, 16.02562, 16.02562, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3863, 16.02562, 16.02562, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3863, 16.02562, 16.02562, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3863, 16.02562, 16.02562, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3863, 16.02562, 16.02562, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3863, 22.43587, 22.43587, 37);
INSERT INTO building(id, name, level) VALUES (3864, "building_subsistence_farmslevel", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3864, 68.66547, 68.66547, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3864, 13.73309, 13.73309, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3864, 13.73309, 13.73309, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3864, 13.73309, 13.73309, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3864, 13.73309, 13.73309, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3864, 13.73309, 13.73309, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3864, 19.22633, 19.22633, 29);
INSERT INTO building(id, name, level) VALUES (3865, "building_subsistence_farmslevel", 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3865, 162.11487, 162.11487, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3865, 32.42297, 32.42297, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3865, 32.42297, 32.42297, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3865, 32.42297, 32.42297, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3865, 32.42297, 32.42297, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3865, 32.42297, 32.42297, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3865, 45.39216, 45.39216, 67);
INSERT INTO building(id, name, level) VALUES (3866, "building_urban_centerlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3866, 25.0, 14.444528113698025, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3866, 25.0, 10.305692962663441, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3866, 200.0, 99.00088430544587, 5);
INSERT INTO building(id, name, level) VALUES (3867, "building_subsistence_farmslevel", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3867, 22.6044, 22.6044, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3867, 4.52088, 4.52088, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3867, 4.52088, 4.52088, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3867, 4.52088, 4.52088, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3867, 4.52088, 4.52088, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3867, 4.52088, 4.52088, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3867, 6.32923, 6.32923, 14);
INSERT INTO building(id, name, level) VALUES (3868, "building_subsistence_farmslevel", 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3868, 51.69412, 51.69412, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3868, 10.33882, 10.33882, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3868, 10.33882, 10.33882, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3868, 10.33882, 10.33882, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3868, 10.33882, 10.33882, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3868, 10.33882, 10.33882, 21);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3868, 14.47435, 14.47435, 21);
INSERT INTO building(id, name, level) VALUES (3869, "building_subsistence_farmslevel", 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3869, 92.5441, 92.5441, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3869, 18.50882, 18.50882, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3869, 18.50882, 18.50882, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3869, 18.50882, 18.50882, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3869, 18.50882, 18.50882, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3869, 18.50882, 18.50882, 44);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3869, 25.91234, 25.91234, 44);
INSERT INTO building(id, name, level) VALUES (3940, "building_subsistence_farmslevel", 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3940, 153.57352, 153.57352, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3940, 30.7147, 30.7147, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3940, 30.7147, 30.7147, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3940, 30.7147, 30.7147, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3940, 30.7147, 30.7147, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3940, 30.7147, 30.7147, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3940, 43.00058, 43.00058, 63);
INSERT INTO building(id, name, level) VALUES (3967, "building_trade_centerlevel", 26);
INSERT INTO building(id, name, level) VALUES (4056, "building_conscription_centerlevel", 7);
INSERT INTO building(id, name, level) VALUES (4114, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4116, "building_conscription_centerlevel", 4);
INSERT INTO building(id, name, level) VALUES (4117, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4588, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4589, "building_conscription_centerlevel", 10);
INSERT INTO building(id, name, level) VALUES (4590, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4591, "building_conscription_centerlevel", 5);
INSERT INTO building(id, name, level) VALUES (4592, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4593, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4594, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4595, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4596, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4597, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4598, "building_conscription_centerlevel", 9);
INSERT INTO building(id, name, level) VALUES (4599, "building_conscription_centerlevel", 16);
INSERT INTO building(id, name, level) VALUES (4600, "building_conscription_centerlevel", 6);
INSERT INTO building(id, name, level) VALUES (4601, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4602, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4603, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4604, "building_conscription_centerlevel", 8);
INSERT INTO building(id, name, level) VALUES (4605, "building_conscription_centerlevel", 1);
INSERT INTO building(id, name, level) VALUES (4606, "building_conscription_centerlevel", 3);
INSERT INTO building(id, name, level) VALUES (4639, "building_conscription_centerlevel", 4);
