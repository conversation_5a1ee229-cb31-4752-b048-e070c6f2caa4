
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 15.182548646412524, 3511.041611888566);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 23.77924771467431, 462.3166052141664);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 13.776684808221313, 400.8660020659782);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 19.035832723964884, 277.63693176701014);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 33.88943488704233, 1108.1221324999594);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 33.58607876559046, 962.4672152531722);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 36.820260414778225, 56.79238728797359);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 14.455546566677441, 373.6752466666669);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 78.74978124817706, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 32.52084022252091, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 27.998213137399716, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 32.972972972972975, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 27.12419583839708, 13.31759808350544);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 46.67223875515295, 44.3010696249877);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 69.95487622560915, 124.89256244192657);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 32.38291342237582, 81.16115278419713);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 27.525056502390985, 78.5780013144782);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 22.455493144799494, 713.3413159361919);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 35.73787687033392, 276.6776725000002);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 13.636179298657435, 30.533628190119394);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 44.08318383389272, 443.2668436728551);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 67.62280337765114, 144.47311430443636);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 72.80976788388692, 47.52994617998254);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 200, 0);
INSERT INTO building(id, name, level) VALUES (33555019, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 33555019, 5.0, 2.9166909724247705, 1);
INSERT INTO building(id, name, level) VALUES (2550, "building_logging_camplevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2550, 59.99999999999999, 66.6, 2);
INSERT INTO building(id, name, level) VALUES (2551, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2551, 10.0, 5.833381944849541, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2551, 100.0, 58.333819448495404, 2);
INSERT INTO building(id, name, level) VALUES (2552, "building_wheat_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2552, 29.999999999999996, 33.0, 1);
INSERT INTO building(id, name, level) VALUES (2553, "building_barrackslevel", 5);
INSERT INTO building(id, name, level) VALUES (2554, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2554, 30.0, 20.906319446962375, 3);
INSERT INTO building(id, name, level) VALUES (2555, "building_logging_camplevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2555, 89.99999999999999, 100.8, 3);
INSERT INTO building(id, name, level) VALUES (2556, "building_rice_farmlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2556, 35.0, 38.5, 1);
INSERT INTO building(id, name, level) VALUES (2557, "building_tobacco_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2557, 88.431, 91.08393, 4);
INSERT INTO building(id, name, level) VALUES (2558, "building_barrackslevel", 15);
INSERT INTO building(id, name, level) VALUES (2559, "building_government_administrationlevel", 7);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2559, 70.0, 48.7814120429122, 7);
INSERT INTO building(id, name, level) VALUES (2560, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2560, 30.0, 32.060798162097406, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2560, 15.0, 25.002482, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2560, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2560, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (2561, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2561, 15.0, 8.750072917274311, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2561, 150.0, 87.5007291727431, 3);
INSERT INTO building(id, name, level) VALUES (2562, "building_whaling_stationlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2562, 9.999, 5.832798606655056, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (28, 2562, 39.996, 23.331194426620225, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2562, 19.998, 11.665597213310113, 2);
INSERT INTO building(id, name, level) VALUES (2563, "building_rice_farmlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2563, 104.99999999999999, 117.6, 3);
INSERT INTO building(id, name, level) VALUES (2564, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2564, 44.23, 44.6723, 2);
INSERT INTO building(id, name, level) VALUES (2565, "building_tea_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2565, 78.74479611650486, 81.10714, 4);
INSERT INTO building(id, name, level) VALUES (2566, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2566, 15.0, 8.750072917274311, 3);
INSERT INTO building(id, name, level) VALUES (2567, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2568, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2568, 50.0, 34.84386574493729, 5);
INSERT INTO building(id, name, level) VALUES (2569, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2569, 25.0, 42.72683914189663, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2569, 75.0, 80.1519954052435, 1);
INSERT INTO building(id, name, level) VALUES (2570, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2570, 60.0, 64.12159632419481, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2570, 80.0, 80.0, 2);
INSERT INTO building(id, name, level) VALUES (2571, "building_furniture_manufacturieslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2571, 17.0, 29.054250616489707, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2571, 34.0, 36.33557125037706, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 2571, 17.0, 22.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2571, 42.5, 42.5, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 2571, 34.0, 34.0, 2);
INSERT INTO building(id, name, level) VALUES (2572, "building_logging_camplevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2572, 19.999999999999996, 22.2, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 2572, 19.999999999999996, 22.2, 2);
INSERT INTO building(id, name, level) VALUES (2573, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2573, 59.99999999999999, 66.6, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2573, 9.999999999999998, 11.1, 2);
INSERT INTO building(id, name, level) VALUES (2574, "building_rice_farmlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2574, 80.00000000000001, 90.4, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2574, 24.000000000000004, 27.12, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2574, 36.0, 40.68, 4);
INSERT INTO building(id, name, level) VALUES (2575, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2575, 44.215495049504945, 44.65765, 2);
INSERT INTO building(id, name, level) VALUES (2576, "building_tea_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2576, 78.74479611650486, 81.10714, 4);
INSERT INTO building(id, name, level) VALUES (2577, "building_barrackslevel", 15);
INSERT INTO building(id, name, level) VALUES (2578, "building_government_administrationlevel", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2578, 99.99999999999999, 69.68773148987457, 10);
INSERT INTO building(id, name, level) VALUES (2579, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2579, 25.0, 42.72683914189663, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2579, 75.0, 80.1519954052435, 1);
INSERT INTO building(id, name, level) VALUES (2580, "building_textile_millslevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2580, 119.99999999999999, 205.08882788110378, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2580, 40.0, 53.28398000000001, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2580, 100.0, 100.0, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2580, 80.0, 80.0, 4);
INSERT INTO building(id, name, level) VALUES (2581, "building_paper_millslevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2581, 90.0, 96.18239448629221, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2581, 120.0, 120.0, 3);
INSERT INTO building(id, name, level) VALUES (2582, "building_fishing_wharflevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2582, 15.0, 8.750072917274311, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2582, 150.0, 87.5007291727431, 3);
INSERT INTO building(id, name, level) VALUES (2583, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2583, 59.99999999999999, 66.6, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2583, 9.999999999999998, 11.1, 2);
INSERT INTO building(id, name, level) VALUES (2584, "building_rice_farmlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2584, 100.00000000000001, 114.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2584, 30.000000000000004, 34.2, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2584, 45.0, 51.3, 5);
INSERT INTO building(id, name, level) VALUES (2585, "building_silk_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2585, 59.05859803921569, 60.23977, 3);
INSERT INTO building(id, name, level) VALUES (2586, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2586, 44.215495049504945, 44.65765, 2);
INSERT INTO building(id, name, level) VALUES (2587, "building_tea_plantationlevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2587, 98.431, 102.36824, 5);
INSERT INTO building(id, name, level) VALUES (2588, "building_barrackslevel", 20);
INSERT INTO building(id, name, level) VALUES (2589, "building_government_administrationlevel", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2589, 50.0, 34.84386574493729, 5);
INSERT INTO building(id, name, level) VALUES (2590, "building_shipyardslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2590, 40.0, 68.36294262703461, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2590, 80.0, 85.49546176559308, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 2590, 70.0, 70.0, 2);
INSERT INTO building(id, name, level) VALUES (2591, "building_textile_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 2591, 60.0, 102.5444139405519, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 2591, 20.0, 26.641990000000003, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2591, 50.0, 50.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 2591, 40.0, 40.0, 2);
INSERT INTO building(id, name, level) VALUES (2592, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2592, 30.0, 32.060798162097406, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2592, 15.0, 25.002482, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2592, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2592, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (2593, "building_fishing_wharflevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2593, 10.0, 5.833381944849541, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2593, 100.0, 58.333819448495404, 2);
INSERT INTO building(id, name, level) VALUES (2594, "building_rice_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2594, 70.0, 77.7, 2);
INSERT INTO building(id, name, level) VALUES (2595, "building_dye_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2595, 49.22049504950495, 49.7127, 2);
INSERT INTO building(id, name, level) VALUES (2596, "building_tobacco_plantationlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2596, 44.23, 44.6723, 2);
INSERT INTO building(id, name, level) VALUES (2597, "building_tea_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2597, 78.74879611650485, 81.11126, 4);
INSERT INTO building(id, name, level) VALUES (2598, "building_barrackslevel", 15);
INSERT INTO building(id, name, level) VALUES (2599, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2599, 5.0, 2.9166909724247705, 1);
INSERT INTO building(id, name, level) VALUES (2600, "building_fishing_wharflevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 2600, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (2601, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2601, 59.99999999999999, 66.6, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2601, 9.999999999999998, 11.1, 2);
INSERT INTO building(id, name, level) VALUES (2602, "building_portlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 2602, 15.0, 8.750072917274311, 3);
INSERT INTO building(id, name, level) VALUES (2603, "building_government_administrationlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2603, 30.0, 20.906319446962375, 3);
INSERT INTO building(id, name, level) VALUES (2604, "building_paper_millslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2604, 60.0, 64.12159632419481, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 2604, 80.0, 80.0, 2);
INSERT INTO building(id, name, level) VALUES (2605, "building_glassworkslevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2605, 30.0, 32.060798162097406, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2605, 15.0, 25.002482, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2605, 10.0, 10.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2605, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (2606, "building_logging_camplevel", 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2606, 150.0, 171.0, 5);
INSERT INTO building(id, name, level) VALUES (2607, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2607, 29.999999999999996, 33.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 2607, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (2608, "building_rice_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2608, 39.99999999999999, 44.4, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 2608, 12.0, 13.32, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 2608, 18.0, 19.98, 2);
INSERT INTO building(id, name, level) VALUES (2609, "building_silk_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 2609, 19.6862, 19.6862, 1);
INSERT INTO building(id, name, level) VALUES (2610, "building_tobacco_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2610, 88.431, 91.08393, 4);
INSERT INTO building(id, name, level) VALUES (2611, "building_tea_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2611, 19.6862, 19.6862, 1);
INSERT INTO building(id, name, level) VALUES (2612, "building_barrackslevel", 10);
INSERT INTO building(id, name, level) VALUES (2613, "building_government_administrationlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 2613, 10.0, 6.968773148987458, 1);
INSERT INTO building(id, name, level) VALUES (2614, "building_glassworkslevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2614, 60.0, 64.12159632419481, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 2614, 30.0, 50.004964, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 2614, 20.0, 20.0, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 2614, 50.0, 50.0, 2);
INSERT INTO building(id, name, level) VALUES (2615, "building_rice_farmlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2615, 70.0, 77.7, 2);
INSERT INTO building(id, name, level) VALUES (2616, "building_dye_plantationlevel", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (21, 2616, 73.82324509803922, 75.29971, 3);
INSERT INTO building(id, name, level) VALUES (2617, "building_tobacco_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (43, 2617, 22.10775, 22.10775, 1);
INSERT INTO building(id, name, level) VALUES (2618, "building_tea_plantationlevel", 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (40, 2618, 78.74479611650486, 81.10714, 4);
INSERT INTO building(id, name, level) VALUES (2619, "building_barrackslevel", 5);
INSERT INTO building(id, name, level) VALUES (3591, "building_subsistence_farmslevel", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3591, 3.0041090909090906, 3.30452, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3591, 0.5006818181818181, 0.55075, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3591, 0.5006818181818181, 0.55075, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3591, 0.5006818181818181, 0.55075, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3591, 0.5006818181818181, 0.55075, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3591, 0.5006818181818181, 0.55075, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3591, 0.7009545454545454, 0.77105, 29);
INSERT INTO building(id, name, level) VALUES (3592, "building_subsistence_farmslevel", 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3592, 460.0422, 506.04642, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3592, 76.6737, 84.34107, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3592, 76.6737, 84.34107, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3592, 76.6737, 84.34107, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3592, 76.6737, 84.34107, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3592, 76.6737, 84.34107, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3592, 107.34317272727272, 118.07749, 180);
INSERT INTO building(id, name, level) VALUES (3593, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3593, 5.0, 5.343466360349567, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3593, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (3594, "building_subsistence_farmslevel", 269);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3594, 702.4934999999999, 772.74285, 269);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3594, 117.08224545454544, 128.79047, 269);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3594, 117.08224545454544, 128.79047, 269);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3594, 117.08224545454544, 128.79047, 269);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3594, 117.08224545454544, 128.79047, 269);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3594, 117.08224545454544, 128.79047, 269);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3594, 163.91514545454544, 180.30666, 269);
INSERT INTO building(id, name, level) VALUES (3595, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3595, 10.0, 10.686932720699135, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3595, 50.0, 50.0, 2);
INSERT INTO building(id, name, level) VALUES (3596, "building_subsistence_farmslevel", 362);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3596, 817.4864999999999, 899.23515, 362);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3596, 136.24774545454545, 149.87252, 362);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3596, 136.24774545454545, 149.87252, 362);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3596, 136.24774545454545, 149.87252, 362);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3596, 136.24774545454545, 149.87252, 362);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3596, 136.24774545454545, 149.87252, 362);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3596, 190.74684545454542, 209.82153, 362);
INSERT INTO building(id, name, level) VALUES (3597, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3597, 15.0, 16.030399081048703, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3597, 75.0, 75.0, 3);
INSERT INTO building(id, name, level) VALUES (3598, "building_subsistence_farmslevel", 442);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3598, 1079.2711727272726, 1187.19829, 442);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3598, 179.87852727272724, 197.86638, 442);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3598, 179.87852727272724, 197.86638, 442);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3598, 179.87852727272724, 197.86638, 442);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3598, 179.87852727272724, 197.86638, 442);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3598, 179.87852727272724, 197.86638, 442);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3598, 251.82993636363634, 277.01293, 442);
INSERT INTO building(id, name, level) VALUES (3599, "building_urban_centerlevel", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3599, 20.0, 21.37386544139827, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3599, 20.0, 15.551840829898037, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3599, 160.0, 142.20736331959213, 4);
INSERT INTO building(id, name, level) VALUES (3600, "building_subsistence_farmslevel", 229);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3600, 559.2111272727273, 615.13224, 229);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3600, 93.20185454545454, 102.52204, 229);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3600, 93.20185454545454, 102.52204, 229);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3600, 93.20185454545454, 102.52204, 229);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3600, 93.20185454545454, 102.52204, 229);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3600, 93.20185454545454, 102.52204, 229);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3600, 130.48259090909087, 143.53085, 229);
INSERT INTO building(id, name, level) VALUES (3601, "building_urban_centerlevel", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3601, 15.0, 16.030399081048703, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3601, 75.0, 75.0, 3);
INSERT INTO building(id, name, level) VALUES (3602, "building_subsistence_orchardslevel", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3602, 3.7375727272727266, 4.11133, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3602, 1.868781818181818, 2.05566, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3602, 5.6063636363636355, 6.167, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3602, 3.7375727272727266, 4.11133, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3602, 3.7375727272727266, 4.11133, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3602, 3.7375727272727266, 4.11133, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 3602, 12.408763636363636, 13.64964, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3602, 5.23260909090909, 5.75587, 14);
INSERT INTO building(id, name, level) VALUES (3603, "building_subsistence_farmslevel", 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3603, 353.23280909090903, 388.55609, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3603, 58.87212727272726, 64.75934, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3603, 58.87212727272726, 64.75934, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3603, 58.87212727272726, 64.75934, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3603, 58.87212727272726, 64.75934, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3603, 58.87212727272726, 64.75934, 159);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3603, 82.4209818181818, 90.66308, 159);
INSERT INTO building(id, name, level) VALUES (3604, "building_urban_centerlevel", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3604, 10.0, 10.686932720699135, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3604, 50.0, 50.0, 2);
INSERT INTO building(id, name, level) VALUES (3605, "building_subsistence_farmslevel", 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3605, 184.0511636363636, 202.45628, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3605, 30.675190909090908, 33.74271, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3605, 30.675190909090908, 33.74271, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3605, 30.675190909090908, 33.74271, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3605, 30.675190909090908, 33.74271, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3605, 30.675190909090908, 33.74271, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3605, 42.94527272727272, 47.2398, 71);
INSERT INTO building(id, name, level) VALUES (3606, "building_urban_centerlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3606, 5.0, 5.343466360349567, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3606, 25.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (4400, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4401, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4402, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4403, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4404, "building_conscription_centerlevel", 25);
INSERT INTO building(id, name, level) VALUES (4405, "building_conscription_centerlevel", 2);
INSERT INTO building(id, name, level) VALUES (4406, "building_conscription_centerlevel", 22);
INSERT INTO building(id, name, level) VALUES (4407, "building_conscription_centerlevel", 12);
INSERT INTO building(id, name, level) VALUES (4684, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 4684, 5.0, 2.9166909724247705, 1);
INSERT INTO building(id, name, level) VALUES (67113664, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 67113664, 29.999999999999996, 33.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 67113664, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (16782058, "building_livestock_ranchlevel", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16782058, 59.99999999999999, 66.6, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 16782058, 9.999999999999998, 11.1, 2);
INSERT INTO building(id, name, level) VALUES (16782160, "building_construction_sectorlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16782160, 25.0, 42.72683914189663, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782160, 75.0, 80.1519954052435, 1);
INSERT INTO building(id, name, level) VALUES (16782175, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 16782175, 5.0, 2.9166909724247705, 1);
INSERT INTO building(id, name, level) VALUES (16782346, "building_portlevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 16782346, 5.0, 2.9166909724247705, 1);
INSERT INTO building(id, name, level) VALUES (117445789, "building_universitylevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 117445789, 5.0, 3.484386574493729, 1);
INSERT INTO building(id, name, level) VALUES (16782661, "building_livestock_ranchlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 16782661, 29.999999999999996, 33.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 16782661, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (6151, "building_fishing_wharflevel", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 6151, 5.0, 2.9166909724247705, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 6151, 50.0, 29.166909724247702, 1);
INSERT INTO building(id, name, level) VALUES (33560699, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 33560699, 35.3724, 44.2155, 1);
INSERT INTO building(id, name, level) VALUES (50337937, "building_cotton_plantationlevel", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 50337937, 35.3724, 44.2155, 1);
INSERT INTO building(id, name, level) VALUES (6451, "building_subsistence_rice_paddieslevel", 29);
INSERT INTO building(id, name, level) VALUES (6452, "building_subsistence_rice_paddieslevel", 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6452, 0.009899999999999999, 0.01089, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6452, 0.0013454545454545453, 0.00148, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6452, 0.0013454545454545453, 0.00148, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6452, 0.0018, 0.00198, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6452, 0.0018, 0.00198, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6452, 0.0018, 0.00198, 180);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6452, 0.0026999999999999997, 0.00297, 180);
INSERT INTO building(id, name, level) VALUES (6453, "building_subsistence_rice_paddieslevel", 269);
INSERT INTO building(id, name, level) VALUES (6454, "building_subsistence_rice_paddieslevel", 362);
INSERT INTO building(id, name, level) VALUES (6455, "building_subsistence_rice_paddieslevel", 442);
INSERT INTO building(id, name, level) VALUES (6456, "building_subsistence_rice_paddieslevel", 229);
INSERT INTO building(id, name, level) VALUES (6457, "building_subsistence_rice_paddieslevel", 159);
INSERT INTO building(id, name, level) VALUES (6458, "building_subsistence_rice_paddieslevel", 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 6458, 0.046854545454545454, 0.05154, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 6458, 0.006381818181818181, 0.00702, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 6458, 0.006381818181818181, 0.00702, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 6458, 0.008518181818181817, 0.00937, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 6458, 0.008518181818181817, 0.00937, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 6458, 0.008518181818181817, 0.00937, 71);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 6458, 0.012772727272727272, 0.01405, 71);
