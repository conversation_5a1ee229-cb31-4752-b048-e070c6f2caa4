
  
CREATE TABLE goods(    goods_name    VARCHAR(30),    code    INT,    base_price FLOAT,    current_price FLOAT,    pop_demand FLOAT, PRIMARY KEY (code));

CREATE TABLE building(    id INT,    name VARCHAR(80),    level INT, PRIMARY KEY (id));

CREATE TABLE supply(    goods_id INT,    building_id INT,    max_supply FLOAT,    current_output FLOAT,    level INT, PRIMARY KEY (goods_id, building_id));

CREATE TABLE demand(    goods_id INT,    building_id INT,    max_demand FLOAT,    current_input FLOAT,    level INT,   PRIMARY KEY (goods_id, building_id));
    
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ammunition", 0, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("small_arms", 1, 60, 100.83882713703517, 17.154524998398852);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("artillery", 2, 70, 118.51547248533429, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tanks", 3, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("aeroplanes", 4, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("manowars", 5, 70, 101.45045226783853, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("ironclads", 6, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("grain", 7, 20, 22.33696568336964, 3731.2663579171463);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fish", 8, 20, 23.935633028006293, 659.5664717670595);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fabric", 9, 20, 17.90938000416997, 105.24324070227173);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wood", 10, 20, 27.226822986534238, 631.4594442136317);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("groceries", 11, 30, 44.1595304157735, 558.3372402591729);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clothes", 12, 30, 33.18653200983196, 1391.9872748222099);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("furniture", 13, 30, 32.12600224678727, 1134.2121386221381);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("paper", 14, 30, 44.649275832613505, 44.81594463888053);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("services", 15, 30, 21.421702906322185, 734.3224114150086);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("transportation", 16, 30, 48.54904054379436, 372.9620500000001);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("electricity", 17, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("clippers", 18, 60, 71.33535460149018, 11.476715031073466);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steamers", 19, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("silk", 20, 40, 47.348381678873295, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("dye", 21, 40, 70.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sulfur", 22, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coal", 23, 30, 50.514077775822884, 251.00097755606419);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("iron", 24, 40, 68.40873775284707, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("lead", 25, 40, 63.274559299036795, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("hardwood", 26, 40, 66.15796539510151, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("rubber", 27, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("oil", 28, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("engines", 29, 60, 60, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("steel", 30, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("glass", 31, 40, 61.76964587525149, 68.50886608114503);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fertilizer", 32, 30, 30, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tools", 33, 40, 64.97023501172912, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("explosives", 34, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("porcelain", 35, 70, 111.98738451694061, 203.82796272458575);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("meat", 36, 30, 40.18078896224525, 465.86278088118735);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fruit", 37, 30, 47.80191845249058, 287.2736880667883);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("liquor", 38, 30, 37.463034383043194, 1952.4728641666702);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("wine", 39, 50, 44.69842620171515, 246.6510724812334);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tea", 40, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("coffee", 41, 50, 87.5, 46.38549529760017);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("sugar", 42, 30, 50.95793053622214, 107.07390407602162);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("tobacco", 43, 40, 40, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("opium", 44, 50, 50, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("automobiles", 45, 100, 100, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("telephones", 46, 70, 70, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("radios", 47, 80, 80, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_clothes", 48, 60, 82.84356068266527, 548.4067284445616);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("luxury_furniture", 49, 60, 86.23613915358642, 251.9736858767559);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("gold", 50, 100, 25.0, 0);
INSERT INTO goods(goods_name,code,base_price, current_price, pop_demand) VALUES ("fine_art", 51, 200, 283.32368615272554, 4.643963872899385);
INSERT INTO building(id, name, level) VALUES (352, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 352, 99.99999999999999, 34.94992919373827, 10);
INSERT INTO building(id, name, level) VALUES (353, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 353, 39.9996, 46.4816814836901, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 353, 79.9992, 41.49201922915288, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 353, 99.999, 5.343298909403064, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 353, 19.9998, 3.3626867232780926, 2);
INSERT INTO building(id, name, level) VALUES (354, "building_university", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 354, 25.0, 8.73748229843457, 5);
INSERT INTO building(id, name, level) VALUES (355, "building_arts_academy", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 355, 10.0, 3.4949929193738276, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 355, 4.0, 1.397997167749531, 1);
INSERT INTO building(id, name, level) VALUES (356, "building_furniture_manufacturies", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 356, 50.0, 58.102682881441446, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 356, 75.0, 38.899157018901015, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 356, 75.0, 9.658780818221166, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 356, 100.0, 54.91463903872097, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 356, 125.0, 68.64329879840122, 5);
INSERT INTO building(id, name, level) VALUES (357, "building_paper_mills", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 357, 150.0, 77.79831403780203, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 357, 200.0, 103.73108538373603, 5);
INSERT INTO building(id, name, level) VALUES (358, "building_wheat_farm", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 358, 10.0, 1.6813601752407987, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 358, 50.0, 8.406800876203993, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 358, 70.0, 11.76952122668559, 10);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 358, 50.0, 8.406800876203993, 10);
INSERT INTO building(id, name, level) VALUES (359, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 359, 13.939196428571428, 11.768303979625978, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 359, 13.939196428571428, 2.343680974985877, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 359, 27.878392857142856, 14.111984954611856, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 359, 55.75679464285714, 28.223974428848148, 3);
INSERT INTO building(id, name, level) VALUES (360, "building_government_administration", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 360, 99.99999999999999, 34.94992919373827, 10);
INSERT INTO building(id, name, level) VALUES (361, "building_arms_industry", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 361, 20.0, 1.0686704685852986, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 361, 20.0, 2.5756748848589774, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 361, 60.0, 5.466518030166414, 2);
INSERT INTO building(id, name, level) VALUES (362, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 362, 39.9996, 46.4816814836901, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 362, 79.9992, 41.49201922915288, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 362, 99.999, 5.343298909403064, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 362, 19.9998, 3.3626867232780926, 2);
INSERT INTO building(id, name, level) VALUES (363, "building_university", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 363, 10.0, 3.4949929193738276, 2);
INSERT INTO building(id, name, level) VALUES (364, "building_food_industry", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 364, 160.0, 135.08157707575472, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 364, 160.0, 11.019259308093707, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 364, 139.99999999999997, 63.919115917933674, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 364, 239.99999999999997, 109.5756272878863, 4);
INSERT INTO building(id, name, level) VALUES (365, "building_glassworks", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 365, 60.0, 31.11932561512081, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 365, 15.0, 0.0, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (25, 365, 30.0, 6.763037225508134, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 365, 60.0, 14.881800022045692, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 365, 60.0, 14.881800022045692, 3);
INSERT INTO building(id, name, level) VALUES (366, "building_iron_mine", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 366, 30.0, 2.6626985553824487, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 366, 30.0, 5.044080525722396, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 366, 120.0, 15.41355816220969, 3);
INSERT INTO building(id, name, level) VALUES (367, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 367, 15.0, 2.522040262861198, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 367, 180.0, 30.26448315433438, 3);
INSERT INTO building(id, name, level) VALUES (368, "building_rye_farm", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 368, 8.0, 1.345088140192639, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 368, 40.0, 6.725440700963194, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 368, 120.00000000000001, 20.176322102889586, 8);
INSERT INTO building(id, name, level) VALUES (369, "building_coal_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 369, 20.0, 3.3627203504815975, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 369, 80.0, 13.450881401926388, 2);
INSERT INTO building(id, name, level) VALUES (370, "building_tooling_workshops", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 370, 90.0, 46.678988422681215, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 370, 60.0, 3.206011405755896, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 370, 180.0, 51.48800553131505, 3);
INSERT INTO building(id, name, level) VALUES (371, "building_textile_mills", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 371, 75.0, 87.15402432216217, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 371, 45.0, 33.97742748169006, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 371, 15.0, 0.0, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 371, 90.0, 52.651618321126705, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 371, 90.0, 52.651618321126705, 3);
INSERT INTO building(id, name, level) VALUES (372, "building_food_industry", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 372, 120.0, 101.31118280681605, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 372, 120.0, 8.26444448107028, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 372, 104.99999999999999, 47.93933693845026, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 372, 180.0, 82.18172046591474, 3);
INSERT INTO building(id, name, level) VALUES (373, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 373, 15.0, 2.522040262861198, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 373, 60.0, 10.088161051444793, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 373, 60.0, 10.088161051444793, 3);
INSERT INTO building(id, name, level) VALUES (374, "building_wheat_farm", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 374, 5.000000000000001, 0.8406800876203996, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 374, 25.000000000000004, 4.203400438101998, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 374, 35.0, 5.884760613342796, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 374, 25.000000000000004, 4.203400438101998, 5);
INSERT INTO building(id, name, level) VALUES (375, "building_coal_mine", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 375, 30.0, 5.044080525722396, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (23, 375, 120.0, 20.176322102889586, 3);
INSERT INTO building(id, name, level) VALUES (376, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 376, 20.0, 1.7751323702549655, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 376, 20.0, 3.3627203504815975, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 376, 80.0, 10.275705441473125, 2);
INSERT INTO building(id, name, level) VALUES (377, "building_lead_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 377, 10.0, 1.6813601752407987, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (25, 377, 40.0, 6.725440700963194, 2);
INSERT INTO building(id, name, level) VALUES (378, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 378, 1.0, 0.16813601752407986, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 378, 5.0, 0.8406800876203993, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 378, 7.0, 1.176952122668559, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 378, 5.0, 0.8406800876203993, 1);
INSERT INTO building(id, name, level) VALUES (379, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 379, 9.419594594594594, 7.952585582825577, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 379, 9.419594594594594, 1.5837731218264848, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 379, 18.839198198198197, 9.536363264993838, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 379, 37.678396396396394, 19.072726529987676, 2);
INSERT INTO building(id, name, level) VALUES (380, "building_iron_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 380, 10.0, 0.8875661851274828, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 380, 10.0, 1.6813601752407987, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 380, 40.0, 5.1378527207365625, 1);
INSERT INTO building(id, name, level) VALUES (381, "building_gold_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 381, 20.0, 1.7751323702549655, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 381, 20.0, 3.3627203504815975, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (50, 381, 40.0, 5.1378527207365625, 2);
INSERT INTO building(id, name, level) VALUES (382, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 382, 3.0185999999999997, 2.5484828035054576, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 382, 3.0185999999999997, 0.5075353824981874, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 382, 6.0371999999999995, 3.056018186003645, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 382, 12.074399999999999, 6.11203637200729, 1);
INSERT INTO building(id, name, level) VALUES (383, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 383, 1.0, 0.16813601752407986, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 383, 5.0, 0.8406800876203993, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 383, 7.0, 1.176952122668559, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 383, 5.0, 0.8406800876203993, 1);
INSERT INTO building(id, name, level) VALUES (16777612, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 16777612, 19.9838, 23.22224788332299, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16777612, 39.9676, 20.72941264091504, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 16777612, 49.9595, 2.6695121137643616, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 16777612, 9.9919, 1.6799982734988537, 1);
INSERT INTO building(id, name, level) VALUES (542, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 542, 30.0, 10.484978758121482, 3);
INSERT INTO building(id, name, level) VALUES (543, "building_construction_sector", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 543, 79.9552, 92.91223260644455, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 543, 159.9104, 82.93839678073691, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 543, 199.888, 10.68072013122891, 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 543, 39.9776, 6.721674454170657, 4);
INSERT INTO building(id, name, level) VALUES (544, "building_textile_mills", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 544, 104.67625, 121.63941917936971, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 544, 62.80575, 47.421729245736785, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 544, 20.935249999999996, 0.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 544, 125.6115, 73.48498616382453, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 544, 125.6115, 73.48498616382453, 5);
INSERT INTO building(id, name, level) VALUES (545, "building_arms_industry", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 545, 49.98, 2.6706075009946613, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 545, 49.98, 6.436611537262585, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (1, 545, 149.94, 13.660828557385871, 5);
INSERT INTO building(id, name, level) VALUES (546, "building_artillery_foundries", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 546, 29.98889108910891, 1.6024121146275756, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 546, 19.99259405940594, 2.574721120099633, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (2, 546, 49.98149504950495, 4.553745731046536, 2);
INSERT INTO building(id, name, level) VALUES (547, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 547, 2.999098214285714, 0.5042564299135794, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 547, 14.995499999999998, 2.521283650782339, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 547, 20.993696428571425, 3.529796510609498, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 547, 14.995499999999998, 2.521283650782339, 3);
INSERT INTO building(id, name, level) VALUES (548, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 548, 19.996, 21.9956, 1);
INSERT INTO building(id, name, level) VALUES (645, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 645, 30.0, 10.484978758121482, 3);
INSERT INTO building(id, name, level) VALUES (646, "building_paper_mills", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 646, 150.0, 77.79831403780203, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (14, 646, 200.0, 103.73108538373603, 5);
INSERT INTO building(id, name, level) VALUES (647, "building_glassworks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 647, 150.0, 77.79831403780203, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 647, 25.0, 0.0, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (31, 647, 50.0, 12.966385672967004, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (35, 647, 100.0, 25.932771345934007, 5);
INSERT INTO building(id, name, level) VALUES (648, "building_textile_mills", 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 648, 200.0, 232.41073152576578, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 648, 120.0, 90.6064732845068, 8);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 648, 39.99999999999999, 0.0, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 648, 240.0, 140.40431552300453, 8);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 648, 240.0, 140.40431552300453, 8);
INSERT INTO building(id, name, level) VALUES (649, "building_fishing_wharf", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 649, 20.0, 14.970532516983349, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 649, 200.0, 149.7053251698335, 4);
INSERT INTO building(id, name, level) VALUES (650, "building_silk_plantation", 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 650, 119.8512, 137.82888, 6);
INSERT INTO building(id, name, level) VALUES (651, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 651, 2.0, 0.3362720350481597, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 651, 9.999999999999998, 1.6813601752407983, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 651, 13.999999999999998, 2.3539042453371177, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 651, 9.999999999999998, 1.6813601752407983, 2);
INSERT INTO building(id, name, level) VALUES (652, "building_vineyard_plantation", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 652, 39.99999999999999, 44.4, 2);
INSERT INTO building(id, name, level) VALUES (653, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 653, 15.0, 11.227899387737512, 3);
INSERT INTO building(id, name, level) VALUES (799, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 799, 3.0, 0.5044080525722396, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 799, 15.0, 2.522040262861198, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 799, 20.999999999999996, 3.530856368005677, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 799, 15.0, 2.522040262861198, 3);
INSERT INTO building(id, name, level) VALUES (800, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 800, 5.3587938931297705, 4.524214564424331, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 800, 5.3587938931297705, 0.9010062639231993, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 800, 10.717595419847328, 5.42522469245392, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 800, 21.435198473282444, 10.850453249014231, 2);
INSERT INTO building(id, name, level) VALUES (801, "building_logging_camp", 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 801, 60.0, 60.6, 2);
INSERT INTO building(id, name, level) VALUES (802, "building_fishing_wharf", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 802, 15.0, 11.227899387737512, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 802, 150.0, 112.27899387737513, 3);
INSERT INTO building(id, name, level) VALUES (803, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 803, 1.0, 0.16813601752407986, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 803, 5.0, 0.8406800876203993, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 803, 7.0, 1.176952122668559, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 803, 5.0, 0.8406800876203993, 1);
INSERT INTO building(id, name, level) VALUES (804, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 804, 20.0, 25.0, 1);
INSERT INTO building(id, name, level) VALUES (805, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 805, 5.0, 3.7426331292458372, 1);
INSERT INTO building(id, name, level) VALUES (806, "building_government_administration", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 806, 40.0, 13.97997167749531, 4);
INSERT INTO building(id, name, level) VALUES (807, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 807, 1.944, 0.32685641806681126, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 807, 9.719999999999999, 1.634282090334056, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 807, 13.608, 2.287994926467679, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 807, 9.719999999999999, 1.634282090334056, 2);
INSERT INTO building(id, name, level) VALUES (808, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 808, 19.856, 24.82, 1);
INSERT INTO building(id, name, level) VALUES (809, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 809, 4.2637, 3.599670751111846, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 809, 4.2637, 0.7168815379174194, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 809, 8.5274, 4.316552289029265, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 809, 17.0548, 8.63310457805853, 1);
INSERT INTO building(id, name, level) VALUES (810, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 810, 14.991294117647058, 2.520576490473341, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 810, 179.89559803921566, 30.24692942442639, 3);
INSERT INTO building(id, name, level) VALUES (811, "building_fishing_wharf", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 811, 9.996, 7.482272151988279, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 811, 99.96, 74.82272151988278, 2);
INSERT INTO building(id, name, level) VALUES (812, "building_port", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 812, 5.0, 3.7426331292458372, 1);
INSERT INTO building(id, name, level) VALUES (813, "building_government_administration", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 813, 20.0, 6.989985838747655, 2);
INSERT INTO building(id, name, level) VALUES (814, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 814, 1.0, 0.16813601752407986, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 814, 5.0, 0.8406800876203993, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 814, 7.0, 1.176952122668559, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 814, 5.0, 0.8406800876203993, 1);
INSERT INTO building(id, name, level) VALUES (815, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 815, 3.7166454545454544, 3.1378145589464705, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 815, 3.7166454545454544, 0.6249019652762463, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 815, 7.433299999999999, 3.762721126022145, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 815, 14.866599999999998, 7.52544225204429, 1);
INSERT INTO building(id, name, level) VALUES (816, "building_tooling_workshops", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 816, 90.0, 46.678988422681215, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 816, 60.0, 3.206011405755896, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (33, 816, 180.0, 51.48800553131505, 3);
INSERT INTO building(id, name, level) VALUES (817, "building_shipyards", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 817, 60.0, 69.72321945772974, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 817, 120.0, 62.23865123024162, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 817, 104.99999999999999, 79.7294099132307, 3);
INSERT INTO building(id, name, level) VALUES (818, "building_military_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 818, 7.196, 8.362138120297054, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 818, 14.392, 7.4644889042136455, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 818, 3.598, 0.19225381729849522, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (5, 818, 12.593, 6.599105383910557, 1);
INSERT INTO building(id, name, level) VALUES (819, "building_fishing_wharf", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 819, 20.0, 14.970532516983349, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (8, 819, 200.0, 149.7053251698335, 4);
INSERT INTO building(id, name, level) VALUES (820, "building_port", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (18, 820, 15.0, 11.227899387737512, 3);
INSERT INTO building(id, name, level) VALUES (823, "building_port", 2);
INSERT INTO building(id, name, level) VALUES (973, "building_government_administration", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 973, 50.0, 17.47496459686914, 5);
INSERT INTO building(id, name, level) VALUES (974, "building_rye_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 974, 2.0, 0.3362720350481597, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 974, 9.999999999999998, 1.6813601752407983, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 974, 29.999999999999996, 5.0440805257223955, 2);
INSERT INTO building(id, name, level) VALUES (975, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 975, 5.0, 4.221299283617335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 975, 5.0, 0.8406800876203994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 975, 10.0, 5.061979371237735, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 975, 20.0, 10.12395874247547, 1);
INSERT INTO building(id, name, level) VALUES (979, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 979, 30.0, 10.484978758121482, 3);
INSERT INTO building(id, name, level) VALUES (980, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 980, 2.0, 0.3362720350481597, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 980, 9.999999999999998, 1.6813601752407983, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 980, 13.999999999999998, 2.3539042453371177, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 980, 9.999999999999998, 1.6813601752407983, 2);
INSERT INTO building(id, name, level) VALUES (981, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 981, 9.999999999999998, 8.44259856723467, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 981, 19.999999999999996, 16.88519713446934, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 981, 19.999999999999996, 16.88519713446934, 2);
INSERT INTO building(id, name, level) VALUES (982, "building_government_administration", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 982, 50.0, 17.47496459686914, 5);
INSERT INTO building(id, name, level) VALUES (983, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 983, 19.8158, 23.027022868841346, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 983, 39.6316, 20.555144417470366, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 983, 49.5395, 2.64707003392407, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 983, 9.9079, 1.6658748480268308, 1);
INSERT INTO building(id, name, level) VALUES (984, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 984, 19.98679207920792, 1.7739600798678763, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 984, 19.98679207920792, 3.360499623279843, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 984, 79.94719801980197, 10.268923221532608, 2);
INSERT INTO building(id, name, level) VALUES (985, "building_food_industry", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 985, 120.0, 101.31118280681605, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 985, 120.0, 8.26444448107028, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 985, 104.99999999999999, 47.93933693845026, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 985, 180.0, 82.18172046591474, 3);
INSERT INTO building(id, name, level) VALUES (986, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 986, 2.9993032786885245, 0.504290908625604, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 986, 14.99654918032787, 2.5214600557843325, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 986, 20.99516393442623, 3.530043251199619, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 986, 14.99654918032787, 2.5214600557843325, 3);
INSERT INTO building(id, name, level) VALUES (987, "building_vineyard_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 987, 59.98800000000001, 73.18536, 3);
INSERT INTO building(id, name, level) VALUES (988, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 988, 13.17044262295082, 11.119276001837102, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 988, 13.17044262295082, 2.214425771652347, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 988, 26.340893442622953, 13.33370592265287, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 988, 52.68179508196721, 26.667415994469156, 3);
INSERT INTO building(id, name, level) VALUES (989, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 989, 1.2208760330578512, 0.205273234088944, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 989, 6.104396694214876, 1.0263689495524477, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 989, 8.546157024793388, 1.4369168072841996, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 989, 6.104396694214876, 1.0263689495524477, 2);
INSERT INTO building(id, name, level) VALUES (990, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 990, 8.294099173553718, 7.002374979914689, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 990, 8.294099173553718, 1.3945368039910844, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 990, 16.588198347107436, 8.396911783905773, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 990, 33.17639669421487, 16.793823567811547, 2);
INSERT INTO building(id, name, level) VALUES (991, "building_food_industry", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 991, 79.85439603960396, 67.41786095913503, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 991, 79.85439603960396, 5.4996018553225445, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 991, 69.87259405940594, 31.90138885121352, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 991, 119.78159405940595, 54.68809711084319, 2);
INSERT INTO building(id, name, level) VALUES (992, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 992, 0.9977666666666667, 0.1677605137516094, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 992, 4.98885, 0.8388053710250059, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 992, 6.984383333333333, 1.1743263985282246, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 992, 4.98885, 0.8388053710250059, 1);
INSERT INTO building(id, name, level) VALUES (993, "building_vineyard_plantation", 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 993, 50.28, 61.3416, 3);
INSERT INTO building(id, name, level) VALUES (994, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 994, 9.752844262295081, 8.233934899531532, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 994, 9.752844262295081, 1.6398043937948676, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 994, 19.505696721311477, 9.87374344248982, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 994, 39.01139344262295, 19.74748688497964, 3);
INSERT INTO building(id, name, level) VALUES (995, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 995, 1.0, 0.16813601752407986, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 995, 5.0, 0.8406800876203993, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 995, 7.000000000000001, 1.176952122668559, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 995, 5.0, 0.8406800876203993, 1);
INSERT INTO building(id, name, level) VALUES (996, "building_livestock_ranch", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 996, 15.000000000000002, 12.663897850852008, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 996, 30.000000000000004, 25.327795701704016, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 996, 30.000000000000004, 25.327795701704016, 3);
INSERT INTO building(id, name, level) VALUES (997, "building_wheat_farm", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 997, 2.973532786885246, 0.49995796076416377, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 997, 14.867696721311477, 2.4997953164771314, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 997, 20.81477868852459, 3.4997139943336144, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 997, 14.867696721311477, 2.4997953164771314, 3);
INSERT INTO building(id, name, level) VALUES (998, "building_livestock_ranch", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 998, 20.0, 16.88519713446934, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 998, 40.0, 33.77039426893868, 4);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 998, 40.0, 33.77039426893868, 4);
INSERT INTO building(id, name, level) VALUES (999, "building_government_administration", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 999, 30.0, 10.484978758121482, 3);
INSERT INTO building(id, name, level) VALUES (1000, "building_furniture_manufacturies", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1000, 20.0, 23.24107315257658, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1000, 30.0, 15.559662807560406, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1000, 30.0, 3.863512327288466, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1000, 10.0, 1.6813601752407987, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1000, 80.0, 36.31150377371418, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1000, 50.0, 22.694689858571362, 2);
INSERT INTO building(id, name, level) VALUES (1001, "building_iron_mine", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1001, 10.0, 0.8875661851274828, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1001, 10.0, 1.6813601752407987, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1001, 40.0, 5.1378527207365625, 1);
INSERT INTO building(id, name, level) VALUES (1002, "building_wheat_farm", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1002, 2.0, 0.3362720350481597, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1002, 9.999999999999998, 1.6813601752407983, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1002, 13.999999999999998, 2.3539042453371177, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1002, 9.999999999999998, 1.6813601752407983, 2);
INSERT INTO building(id, name, level) VALUES (1003, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1003, 1.0, 0.16813601752407986, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1003, 5.0, 0.8406800876203993, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1003, 7.0, 1.176952122668559, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1003, 5.0, 0.8406800876203993, 1);
INSERT INTO building(id, name, level) VALUES (1004, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1004, 15.0, 2.522040262861198, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1004, 180.0, 30.26448315433438, 3);
INSERT INTO building(id, name, level) VALUES (1005, "building_wheat_farm", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1005, 5.0, 5.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1005, 7.0, 7.7, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1005, 5.0, 5.5, 1);
INSERT INTO building(id, name, level) VALUES (1006, "building_silk_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 1006, 20.0, 22.0, 1);
INSERT INTO building(id, name, level) VALUES (1007, "building_logging_camp", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1007, 25.0, 4.203400438101997, 5);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1007, 300.0, 50.44080525722396, 5);
INSERT INTO building(id, name, level) VALUES (1008, "building_iron_mine", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (23, 1008, 20.0, 1.7751323702549655, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1008, 20.0, 3.3627203504815975, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (24, 1008, 80.0, 10.275705441473125, 2);
INSERT INTO building(id, name, level) VALUES (1009, "building_government_administration", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 1009, 20.0, 6.989985838747655, 2);
INSERT INTO building(id, name, level) VALUES (1010, "building_furniture_manufacturies", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 1010, 10.0, 11.62053657628829, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 1010, 15.0, 7.779831403780203, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (26, 1010, 15.0, 1.931756163644233, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 1010, 20.0, 10.982927807744193, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (49, 1010, 25.0, 13.728659759680243, 1);
INSERT INTO building(id, name, level) VALUES (1011, "building_logging_camp", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1011, 15.0, 2.522040262861198, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1011, 60.0, 10.088161051444793, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1011, 60.0, 10.088161051444793, 3);
INSERT INTO building(id, name, level) VALUES (1012, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1012, 1.0, 0.16813601752407986, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1012, 5.0, 0.8406800876203993, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1012, 7.0, 1.176952122668559, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1012, 5.0, 0.8406800876203993, 1);
INSERT INTO building(id, name, level) VALUES (1013, "building_livestock_ranch", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1013, 5.0, 4.221299283617335, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1013, 5.0, 0.8406800876203994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1013, 10.0, 5.061979371237735, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1013, 20.0, 10.12395874247547, 1);
INSERT INTO building(id, name, level) VALUES (1014, "building_wheat_farm", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1014, 0.28843846153846153, 0.04849689422384941, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 1014, 1.4422, 0.242485764473228, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (37, 1014, 2.019076923076923, 0.33947955292092685, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (42, 1014, 1.4422, 0.242485764473228, 1);
INSERT INTO building(id, name, level) VALUES (1015, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 1015, 20.0, 26.0, 1);
INSERT INTO building(id, name, level) VALUES (1016, "building_livestock_ranch", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 1016, 7.5126946564885495, 6.34266651429418, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1016, 7.5126946564885495, 1.26315456041642, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 1016, 15.025396946564886, 7.60582493881699, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (36, 1016, 30.05079389312977, 15.21164987763398, 2);
INSERT INTO building(id, name, level) VALUES (1017, "building_logging_camp", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 1017, 9.98359405940594, 1.6786017457255769, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 1017, 39.93439603960396, 6.714410312328398, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (26, 1017, 39.93439603960396, 6.714410312328398, 2);
INSERT INTO building(id, name, level) VALUES (2726, "building_subsistence_farms", 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2726, 118.46159999999999, 130.30776, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2726, 29.615399999999998, 32.57694, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2726, 29.615399999999998, 32.57694, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2726, 29.615399999999998, 32.57694, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2726, 29.615399999999998, 32.57694, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2726, 29.615399999999998, 32.57694, 60);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2726, 29.615399999999998, 32.57694, 60);
INSERT INTO building(id, name, level) VALUES (2727, "building_urban_center", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 2727, 6.0, 3.111932561512081, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 2727, 6.0, 1.651227334657179, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2727, 120.0, 47.6315989616926, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 2727, 30.0, 11.90789974042315, 6);
INSERT INTO building(id, name, level) VALUES (2848, "building_subsistence_farms", 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2848, 93.51683636363636, 102.86852, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2848, 23.37920909090909, 25.71713, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2848, 23.37920909090909, 25.71713, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2848, 23.37920909090909, 25.71713, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2848, 23.37920909090909, 25.71713, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2848, 23.37920909090909, 25.71713, 47);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2848, 23.37920909090909, 25.71713, 47);
INSERT INTO building(id, name, level) VALUES (2850, "building_subsistence_farms", 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2850, 95.05823636363635, 104.56406, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2850, 23.764554545454544, 26.14101, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2850, 23.764554545454544, 26.14101, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2850, 23.764554545454544, 26.14101, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2850, 23.764554545454544, 26.14101, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2850, 23.764554545454544, 26.14101, 48);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2850, 23.764554545454544, 26.14101, 48);
INSERT INTO building(id, name, level) VALUES (2851, "building_subsistence_farms", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 2851, 33.516, 43.5708, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 2851, 8.379, 10.8927, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 2851, 8.379, 10.8927, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 2851, 8.379, 10.8927, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 2851, 8.379, 10.8927, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 2851, 8.379, 10.8927, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 2851, 8.379, 10.8927, 20);
INSERT INTO building(id, name, level) VALUES (3459, "building_subsistence_farms", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3459, 2.0, 2.5, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3459, 0.5, 0.625, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3459, 0.5, 0.625, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3459, 0.5, 0.625, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3459, 0.5, 0.625, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3459, 0.5, 0.625, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3459, 0.5, 0.625, 1);
INSERT INTO building(id, name, level) VALUES (3656, "building_subsistence_farms", 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3656, 236.33919999999998, 259.97312, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3656, 59.084799999999994, 64.99328, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3656, 59.084799999999994, 64.99328, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3656, 59.084799999999994, 64.99328, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3656, 59.084799999999994, 64.99328, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3656, 59.084799999999994, 64.99328, 128);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3656, 59.084799999999994, 64.99328, 128);
INSERT INTO building(id, name, level) VALUES (3657, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3657, 1.0, 0.5186554269186802, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3657, 1.0, 0.2752045557761965, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3657, 20.0, 7.938599826948767, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3657, 5.0, 1.9846499567371918, 1);
INSERT INTO building(id, name, level) VALUES (3659, "building_subsistence_farms", 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3659, 281.2414181818182, 309.36556, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3659, 70.31035454545454, 77.34139, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3659, 70.31035454545454, 77.34139, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3659, 70.31035454545454, 77.34139, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3659, 70.31035454545454, 77.34139, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3659, 70.31035454545454, 77.34139, 141);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3659, 70.31035454545454, 77.34139, 141);
INSERT INTO building(id, name, level) VALUES (3660, "building_subsistence_farms", 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3660, 205.6456727272727, 226.21024, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3660, 51.41141818181818, 56.55256, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3660, 51.41141818181818, 56.55256, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3660, 51.41141818181818, 56.55256, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3660, 51.41141818181818, 56.55256, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3660, 51.41141818181818, 56.55256, 103);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3660, 51.41141818181818, 56.55256, 103);
INSERT INTO building(id, name, level) VALUES (3661, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3661, 1.0, 0.5186554269186802, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3661, 1.0, 0.2752045557761965, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3661, 20.0, 7.938599826948767, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3661, 5.0, 1.9846499567371918, 1);
INSERT INTO building(id, name, level) VALUES (3662, "building_subsistence_farms", 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3662, 137.91581818181817, 151.7074, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3662, 34.47895454545454, 37.92685, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3662, 34.47895454545454, 37.92685, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3662, 34.47895454545454, 37.92685, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3662, 34.47895454545454, 37.92685, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3662, 34.47895454545454, 37.92685, 69);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3662, 34.47895454545454, 37.92685, 69);
INSERT INTO building(id, name, level) VALUES (3663, "building_subsistence_farms", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3663, 34.17192, 42.7149, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3663, 8.542976, 10.67872, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3663, 8.542976, 10.67872, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3663, 8.542976, 10.67872, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3663, 8.542976, 10.67872, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3663, 8.542976, 10.67872, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3663, 8.542976, 10.67872, 18);
INSERT INTO building(id, name, level) VALUES (3664, "building_subsistence_farms", 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3664, 61.952256, 77.44032, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3664, 15.488064, 19.36008, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3664, 15.488064, 19.36008, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3664, 15.488064, 19.36008, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3664, 15.488064, 19.36008, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3664, 15.488064, 19.36008, 31);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3664, 15.488064, 19.36008, 31);
INSERT INTO building(id, name, level) VALUES (3665, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3665, 1.0, 0.5186554269186802, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3665, 1.0, 0.2752045557761965, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3665, 20.0, 7.938599826948767, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3665, 5.0, 1.9846499567371918, 1);
INSERT INTO building(id, name, level) VALUES (3666, "building_subsistence_farms", 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3666, 35.72567272727272, 39.29824, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3666, 8.93141818181818, 9.82456, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3666, 8.93141818181818, 9.82456, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3666, 8.93141818181818, 9.82456, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3666, 8.93141818181818, 9.82456, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3666, 8.93141818181818, 9.82456, 18);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3666, 8.93141818181818, 9.82456, 18);
INSERT INTO building(id, name, level) VALUES (3667, "building_subsistence_farms", 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3667, 149.2944, 179.15328, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3667, 37.3236, 44.78832, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3667, 37.3236, 44.78832, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3667, 37.3236, 44.78832, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3667, 37.3236, 44.78832, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3667, 37.3236, 44.78832, 76);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3667, 37.3236, 44.78832, 76);
INSERT INTO building(id, name, level) VALUES (3668, "building_subsistence_farms", 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3668, 235.546875, 282.65625, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3668, 58.88671666666667, 70.66406, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3668, 58.88671666666667, 70.66406, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3668, 58.88671666666667, 70.66406, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3668, 58.88671666666667, 70.66406, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3668, 58.88671666666667, 70.66406, 118);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3668, 58.88671666666667, 70.66406, 118);
INSERT INTO building(id, name, level) VALUES (3669, "building_subsistence_farms", 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3669, 50.0, 62.5, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3669, 12.5, 15.625, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3669, 12.5, 15.625, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3669, 12.5, 15.625, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3669, 12.5, 15.625, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3669, 12.5, 15.625, 25);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3669, 12.5, 15.625, 25);
INSERT INTO building(id, name, level) VALUES (3670, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3670, 2.0, 1.0373108538373603, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3670, 2.0, 0.550409111552393, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3670, 40.0, 15.877199653897534, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3670, 10.0, 3.9692999134743836, 2);
INSERT INTO building(id, name, level) VALUES (3671, "building_subsistence_farms", 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3671, 209.00879999999998, 229.90968, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3671, 52.252199999999995, 57.47742, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3671, 52.252199999999995, 57.47742, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3671, 52.252199999999995, 57.47742, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3671, 52.252199999999995, 57.47742, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3671, 52.252199999999995, 57.47742, 105);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3671, 52.252199999999995, 57.47742, 105);
INSERT INTO building(id, name, level) VALUES (3672, "building_urban_center", 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3672, 2.999392156862745, 1.5556510196141877, 3);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3672, 2.999392156862745, 0.8254463861280197, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3672, 59.988, 23.81103632095013, 3);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3672, 14.997, 5.9527590802375325, 3);
INSERT INTO building(id, name, level) VALUES (3673, "building_subsistence_farms", 187);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3673, 372.8331181818181, 410.11643, 187);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3673, 93.20827272727271, 102.5291, 187);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3673, 93.20827272727271, 102.5291, 187);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3673, 93.20827272727271, 102.5291, 187);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3673, 93.20827272727271, 102.5291, 187);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3673, 93.20827272727271, 102.5291, 187);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3673, 93.20827272727271, 102.5291, 187);
INSERT INTO building(id, name, level) VALUES (3674, "building_urban_center", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3674, 6.0, 3.111932561512081, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3674, 6.0, 1.651227334657179, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3674, 120.0, 47.6315989616926, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3674, 30.0, 11.90789974042315, 6);
INSERT INTO building(id, name, level) VALUES (3675, "building_subsistence_farms", 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3675, 147.49949999999998, 162.24945, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3675, 36.874872727272724, 40.56236, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3675, 36.874872727272724, 40.56236, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3675, 36.874872727272724, 40.56236, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3675, 36.874872727272724, 40.56236, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3675, 36.874872727272724, 40.56236, 75);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3675, 36.874872727272724, 40.56236, 75);
INSERT INTO building(id, name, level) VALUES (3676, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3676, 2.0, 1.0373108538373603, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3676, 2.0, 0.550409111552393, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3676, 40.0, 15.877199653897534, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3676, 10.0, 3.9692999134743836, 2);
INSERT INTO building(id, name, level) VALUES (3677, "building_subsistence_farms", 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3677, 200.4244, 240.50928, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3677, 50.1061, 60.12732, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3677, 50.1061, 60.12732, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3677, 50.1061, 60.12732, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3677, 50.1061, 60.12732, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3677, 50.1061, 60.12732, 101);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3677, 50.1061, 60.12732, 101);
INSERT INTO building(id, name, level) VALUES (3678, "building_urban_center", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3678, 1.9995940594059405, 1.0371003105452448, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3678, 1.9995940594059405, 0.5502973948515334, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3678, 39.992, 15.874024213966752, 2);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3678, 9.998, 3.968506053491688, 2);
INSERT INTO building(id, name, level) VALUES (3679, "building_subsistence_farms", 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3679, 74.0, 81.4, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3679, 18.5, 20.35, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3679, 18.5, 20.35, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3679, 18.5, 20.35, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3679, 18.5, 20.35, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3679, 18.5, 20.35, 37);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3679, 18.5, 20.35, 37);
INSERT INTO building(id, name, level) VALUES (3680, "building_subsistence_farms", 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3680, 57.99999999999999, 63.8, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3680, 14.499999999999998, 15.95, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3680, 14.499999999999998, 15.95, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3680, 14.499999999999998, 15.95, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3680, 14.499999999999998, 15.95, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3680, 14.499999999999998, 15.95, 29);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3680, 14.499999999999998, 15.95, 29);
INSERT INTO building(id, name, level) VALUES (3681, "building_subsistence_farms", 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3681, 132.59433636363636, 145.85377, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3681, 33.14858181818182, 36.46344, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3681, 33.14858181818182, 36.46344, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3681, 33.14858181818182, 36.46344, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3681, 33.14858181818182, 36.46344, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3681, 33.14858181818182, 36.46344, 67);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3681, 33.14858181818182, 36.46344, 67);
INSERT INTO building(id, name, level) VALUES (3682, "building_urban_center", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 3682, 6.0, 3.111932561512081, 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 3682, 6.0, 1.651227334657179, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3682, 120.0, 47.6315989616926, 6);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 3682, 30.0, 11.90789974042315, 6);
INSERT INTO building(id, name, level) VALUES (3683, "building_subsistence_farms", 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3683, 28.0, 30.8, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3683, 7.0, 7.7, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3683, 7.0, 7.7, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3683, 7.0, 7.7, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3683, 7.0, 7.7, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3683, 7.0, 7.7, 14);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3683, 7.0, 7.7, 14);
INSERT INTO building(id, name, level) VALUES (3684, "building_subsistence_farms", 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3684, 40.0, 52.0, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3684, 10.0, 13.0, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3684, 10.0, 13.0, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3684, 10.0, 13.0, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3684, 10.0, 13.0, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3684, 10.0, 13.0, 20);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3684, 10.0, 13.0, 20);
INSERT INTO building(id, name, level) VALUES (3685, "building_subsistence_farms", 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3685, 83.59830000000001, 117.03762, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3685, 20.89957142857143, 29.2594, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3685, 20.89957142857143, 29.2594, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3685, 20.89957142857143, 29.2594, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3685, 20.89957142857143, 29.2594, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3685, 20.89957142857143, 29.2594, 45);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3685, 20.89957142857143, 29.2594, 45);
INSERT INTO building(id, name, level) VALUES (3752, "building_subsistence_farms", 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (7, 3752, 125.68625833333334, 150.82351, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 3752, 31.421558333333333, 37.70587, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 3752, 31.421558333333333, 37.70587, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 3752, 31.421558333333333, 37.70587, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (13, 3752, 31.421558333333333, 37.70587, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 3752, 31.421558333333333, 37.70587, 63);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 3752, 31.421558333333333, 37.70587, 63);
INSERT INTO building(id, name, level) VALUES (3778, "building_barracks", 30);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3778, 29.9997, 2.8069078104541196, 30);
INSERT INTO building(id, name, level) VALUES (3779, "building_barracks", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3779, 20.0, 1.8712905865419454, 20);
INSERT INTO building(id, name, level) VALUES (3780, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3780, 19.964, 1.8679222634861696, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3780, 19.964, 16.854803779627296, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3780, 19.964, 1.0667468617418452, 10);
INSERT INTO building(id, name, level) VALUES (3781, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3781, 20.0, 1.5358737429273965, 10);
INSERT INTO building(id, name, level) VALUES (3782, "building_barracks", 20);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3782, 20.0, 1.8712905865419454, 20);
INSERT INTO building(id, name, level) VALUES (3783, "building_barracks", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3783, 15.0, 1.403467939906459, 15);
INSERT INTO building(id, name, level) VALUES (3784, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3784, 9.73, 0.9103828703526565, 10);
INSERT INTO building(id, name, level) VALUES (3785, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3785, 5.0, 0.46782264663548634, 5);
INSERT INTO building(id, name, level) VALUES (3786, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3786, 10.0, 0.9356452932709727, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3786, 10.0, 8.44259856723467, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3786, 10.0, 0.5343352342926493, 5);
INSERT INTO building(id, name, level) VALUES (3787, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3787, 10.0, 0.9356452932709727, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3787, 10.0, 8.44259856723467, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3787, 10.0, 0.5343352342926493, 5);
INSERT INTO building(id, name, level) VALUES (3788, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3788, 10.0, 0.7679368714636983, 5);
INSERT INTO building(id, name, level) VALUES (3789, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3789, 10.0, 0.7679368714636983, 5);
INSERT INTO building(id, name, level) VALUES (3790, "building_barracks", 15);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3790, 14.99985, 1.4034539052270598, 15);
INSERT INTO building(id, name, level) VALUES (3791, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3791, 10.0, 0.9356452932709727, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3791, 10.0, 0.7679368714636983, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3791, 10.0, 8.44259856723467, 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3791, 10.0, 0.5343352342926493, 10);
INSERT INTO building(id, name, level) VALUES (3792, "building_barracks", 10);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3792, 10.0, 0.9356452932709727, 10);
INSERT INTO building(id, name, level) VALUES (3793, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3793, 5.0, 0.46782264663548634, 5);
INSERT INTO building(id, name, level) VALUES (3794, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (1, 3794, 10.0, 0.9356452932709727, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 3794, 10.0, 8.44259856723467, 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 3794, 10.0, 0.5343352342926493, 5);
INSERT INTO building(id, name, level) VALUES (3795, "building_barracks", 5);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (2, 3795, 10.0, 0.7679368714636983, 5);
INSERT INTO building(id, name, level) VALUES (3796, "building_naval_base", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3796, 9.82496, 3.9433364711265226, 6);
INSERT INTO building(id, name, level) VALUES (3797, "building_naval_base", 6);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 3797, 5.44997, 2.1873947036471812, 6);
INSERT INTO building(id, name, level) VALUES (4142, "building_trade_center", 25);
INSERT INTO building(id, name, level) VALUES (4225, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4225, 1.0, 0.5186554269186802, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 4225, 1.0, 0.2752045557761965, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 4225, 20.0, 7.938599826948767, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 4225, 5.0, 1.9846499567371918, 1);
INSERT INTO building(id, name, level) VALUES (4260, "building_conscription_center", 7);
INSERT INTO building(id, name, level) VALUES (4504, "building_conscription_center", 8);
INSERT INTO building(id, name, level) VALUES (4608, "building_conscription_center", 6);
INSERT INTO building(id, name, level) VALUES (4820, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4820, 19.9998, 23.24084074184505, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4820, 39.9996, 20.74600961457644, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4820, 49.9995, 2.671649454701532, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4820, 9.9999, 1.6813433616390463, 1);
INSERT INTO building(id, name, level) VALUES (4837, "building_conscription_center", 15);
INSERT INTO building(id, name, level) VALUES (4901, "building_construction_sector", 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 4901, 39.9996, 46.4816814836901, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 4901, 79.9992, 41.49201922915288, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 4901, 99.999, 5.343298909403064, 2);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 4901, 19.9998, 3.3626867232780926, 2);
INSERT INTO building(id, name, level) VALUES (4920, "building_conscription_center", 2);
INSERT INTO building(id, name, level) VALUES (4930, "building_conscription_center", 5);
INSERT INTO building(id, name, level) VALUES (4983, "building_naval_base", 4);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (5, 4983, 1.184, 0.4752090982369192, 4);
INSERT INTO building(id, name, level) VALUES (4984, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (4989, "building_conscription_center", 6);
INSERT INTO building(id, name, level) VALUES (5012, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (5014, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5014, 12.98787, 15.092601838307736, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5014, 25.97574, 13.472458519228637, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5014, 32.46967, 1.7349688726855008, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5014, 6.49393, 1.091863528280148, 1);
INSERT INTO building(id, name, level) VALUES (5023, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5023, 19.9998, 23.24084074184505, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5023, 39.9996, 20.74600961457644, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5023, 49.9995, 2.671649454701532, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5023, 9.9999, 1.6813433616390463, 1);
INSERT INTO building(id, name, level) VALUES (5024, "building_conscription_center", 1);
INSERT INTO building(id, name, level) VALUES (5028, "building_construction_sector", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5028, 19.9958, 23.236192527214538, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5028, 39.9916, 20.74186037116109, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (24, 5028, 49.9895, 2.671115119467239, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5028, 9.9979, 1.6810070896039981, 1);
INSERT INTO building(id, name, level) VALUES (5059, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (5063, "building_conscription_center", 4);
INSERT INTO building(id, name, level) VALUES (5071, "building_conscription_center", 1);
INSERT INTO building(id, name, level) VALUES (5074, "building_conscription_center", 7);
INSERT INTO building(id, name, level) VALUES (5079, "building_conscription_center", 1);
INSERT INTO building(id, name, level) VALUES (5085, "building_conscription_center", 3);
INSERT INTO building(id, name, level) VALUES (5096, "building_conscription_center", 2);
INSERT INTO building(id, name, level) VALUES (5123, "building_trade_center", 10);
INSERT INTO building(id, name, level) VALUES (5251, "building_silk_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (20, 5251, 11.092, 14.4196, 1);
INSERT INTO building(id, name, level) VALUES (5266, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5266, 1.0, 0.5186554269186802, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 5266, 1.0, 0.2752045557761965, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 5266, 20.0, 7.938599826948767, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 5266, 5.0, 1.9846499567371918, 1);
INSERT INTO building(id, name, level) VALUES (5331, "building_logging_camp", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (33, 5331, 5.0, 0.8406800876203994, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (10, 5331, 60.0, 10.088161051444791, 1);
INSERT INTO building(id, name, level) VALUES (5360, "building_trade_center", 2);
INSERT INTO building(id, name, level) VALUES (5390, "building_arts_academy", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (14, 5390, 4.766, 1.6657136253735663, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (51, 5390, 1.9064, 0.6662854501494265, 1);
INSERT INTO building(id, name, level) VALUES (5422, "building_vineyard_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (39, 5422, 9.564, 10.5204, 1);
INSERT INTO building(id, name, level) VALUES (5475, "building_food_industry", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (7, 5475, 38.222, 32.26930024368436, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (42, 5475, 38.222, 2.632363307962235, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (11, 5475, 33.44425, 15.269477803845385, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (38, 5475, 57.333, 26.176247663734948, 1);
INSERT INTO building(id, name, level) VALUES (5477, "building_conscription_center", 2);
INSERT INTO building(id, name, level) VALUES (5547, "building_cotton_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 5547, 10.655999999999999, 16.5168, 1);
INSERT INTO building(id, name, level) VALUES (5677, "building_textile_mills", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5677, 4.95075, 5.7530371455059255, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (20, 5677, 2.97045, 2.242849988066361, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (21, 5677, 0.99015, 0.0, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (12, 5677, 5.9409, 3.4755333253775738, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (48, 5677, 5.9409, 3.4755333253775738, 1);
INSERT INTO building(id, name, level) VALUES (5764, "building_trade_center", 5);
INSERT INTO building(id, name, level) VALUES (16782993, "building_urban_center", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 16782993, 1.0, 0.5186554269186802, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (31, 16782993, 1.0, 0.2752045557761965, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (15, 16782993, 20.0, 7.938599826948767, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (16, 16782993, 5.0, 1.9846499567371918, 1);
INSERT INTO building(id, name, level) VALUES (5803, "building_shipyards", 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (9, 5803, 5.572, 6.474962980307835, 1);
INSERT INTO demand(goods_id, building_id, max_demand, current_input, level) VALUES (10, 5803, 11.144, 5.779896077581772, 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (18, 5803, 9.751, 7.404204533942025, 1);
INSERT INTO building(id, name, level) VALUES (5879, "building_cotton_plantation", 1);
INSERT INTO supply(goods_id, building_id, max_supply, current_output, level) VALUES (9, 5879, 2.384, 3.2184, 1);
