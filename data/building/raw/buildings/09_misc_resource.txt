﻿building_logging_camp = {
	building_group = bg_logging
	texture = "gfx/interface/icons/building_icons/logging_camp.dds"
	city_type = wood
	required_construction = construction_cost_low
	terrain_manipulator = forestry
	levels_per_mesh = 5

	production_method_groups = {
		pmg_base_building_logging_camp
		pmg_hardwood
		pmg_equipment
		pmg_transportation_building_logging_camp
		pmg_ownership_capital_building_logging_camp
	}
}

building_rubber_plantation = {
	building_group = bg_rubber
	texture = "gfx/interface/icons/building_icons/rubber_lodge.dds"
	required_construction = construction_cost_low
	
	terrain_manipulator = forestry

	unlocking_technologies = {
		rubber_mastication
	}

	city_type = wood
	levels_per_mesh = 5
	
	production_method_groups = {
		pmg_base_building_rubber_plantation
		pmg_train_automation_building_rubber_plantation
		pmg_ownership_land_building_rubber_plantation
	}
}

building_fishing_wharf = {
	building_group = bg_fishing
	texture = "gfx/interface/icons/building_icons/fishing_wharf.dds"
	city_type = port
	required_construction = construction_cost_low

	production_method_groups = {
		pmg_base_building_fishing_wharf
		pmg_refrigeration_building_fishing_wharf
		pmg_ownership_capital_building_fishing_wharf
	}
	
	possible = {
		error_check = {
			severity = fail
			is_sea_adjacent = yes
		}
	}
}

building_whaling_station = {
	building_group = bg_whaling
	texture = "gfx/interface/icons/building_icons/whaling_station.dds"
	city_type = port
	required_construction = construction_cost_low
	
	unlocking_technologies = {
		navigation
	}

	production_method_groups = {
		pmg_base_building_whaling_station
		pmg_refrigeration_building_whaling_station
		pmg_ownership_capital_building_whaling_station
	}
	
	possible = {
		error_check = {
			severity = fail
			is_sea_adjacent = yes
		}
	}
}


building_oil_rig = {
	building_group = bg_oil_extraction
	texture = "gfx/interface/icons/building_icons/oil_rig.dds"
	city_type = mine
	levels_per_mesh = 5
	required_construction = construction_cost_medium

	unlocking_technologies = {
		pumpjacks
	}
	
	production_method_groups = {
		pmg_base_building_oil_rig
		pmg_transportation_building_oil_rig
		pmg_ownership_capital_building_oil_rig
	}
}