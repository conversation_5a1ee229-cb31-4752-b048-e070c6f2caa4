﻿building_eiffel_tower = {
	building_group = bg_monuments
	texture = "gfx/interface/icons/building_icons/building_eiffel_tower.dds"
	expandable = no
	downsizeable = no
	unique = yes
	locator = "eiffel_tower_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { "building_construction_skyscraper_entity" }
	entity_constructed = { "wonder_eiffel_tower_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 3
	}
	
	production_method_groups = {
		pmg_base_building_eiffel_tower
	}

	unlocking_technologies = { 
		steel_frame_buildings 
	}
	
	required_construction = construction_cost_monument
	
	possible = {
		error_check = {
			severity = invalid
			this = {
				state_region = s:STATE_ILE_DE_FRANCE
			}
		}
	}
	
	ai_value = 10000
}

building_angkor_wat = {
	building_group = bg_monuments
	texture = "gfx/interface/icons/building_icons/angkor_wat.dds"
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "angkor_wat_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { "building_construction_3x3_entity" }
	entity_constructed = { "wonder_angkorwat_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 5
	}
	
	production_method_groups = {
		pmg_base_building_angkor_wat
	}
	
	possible = {
		error_check = {
			severity = invalid
			this = {
				state_region = s:STATE_CAMBODIA
			}
		}
	}
	
	required_construction = construction_cost_monument	
}

building_big_ben = {
	building_group = bg_monuments
	texture = "gfx/interface/icons/building_icons/big_ben.dds"
	expandable = no
	downsizeable = no
	unique = yes
	locator = "big_ben_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { "building_construction_2x2_entity" }
	entity_constructed = { "wonder_big_ben_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 2
	}
	
	production_method_groups = {
		pmg_base_building_big_ben
	}
	
	required_construction = construction_cost_monument
	
	possible = {
		error_check = {
			severity = invalid
			this = {
				state_region = s:STATE_HOME_COUNTIES
			}
		}
	}
	
	ai_value = 10000
}

building_forbidden_city = {
	building_group = bg_monuments
	texture = "gfx/interface/icons/building_icons/forbidden_city.dds"
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "forbidden_city_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { "building_construction_3x3_entity" }
	entity_constructed = { "wonder_forbidden_city_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 4
	}
	
	production_method_groups = {
		pmg_base_building_forbidden_city
	}
	
	possible = {
		error_check = {
			severity = invalid
			this = {
				state_region = s:STATE_BEIJING
			}
		}
	}
	
	required_construction = construction_cost_monument
}

building_hagia_sophia = {
	building_group = bg_monuments
	texture = "gfx/interface/icons/building_icons/hagia_sophia.dds"
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "hagia_sophia_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { "building_construction_3x3_entity" }
	entity_constructed = { "wonder_hagia_sophia_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 3
	}
	
	production_method_groups = {
		pmg_base_building_hagia_sophia
	}
	
	possible = {
		error_check = {
			severity = invalid
			this = {
				state_region = s:STATE_EASTERN_THRACE
			}
		}
	}
	
	required_construction = construction_cost_monument
}

building_mosque_of_djenne = {
	building_group = bg_monuments
	texture = "gfx/interface/icons/building_icons/mosque_of_djenne.dds"
	expandable = no
	downsizeable = no
	unique = yes
	locator = "mosque_of_djenne_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { "building_construction_3x3_entity" }
	entity_constructed = { "wonder_great_mosque_of_djenne_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 4
	}
	
	production_method_groups = {
		pmg_base_building_mosque_of_djenne
	}
	
	required_construction = construction_cost_monument
	
	possible = {
		error_check = {
			severity = invalid
			this = {
				state_region = s:STATE_EASTERN_MALI
			}
		}
	}
	
	ai_value = 10000
}

building_saint_basils_cathedral = {
	building_group = bg_monuments
	texture = "gfx/interface/icons/building_icons/saint_basils_cathedral.dds"
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "saint_basils_cathedral_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { "building_construction_3x3_entity" }
	entity_constructed = { "wonder_saint_basils_cathedral_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 4
	}
	
	production_method_groups = {
		pmg_base_building_saint_basils_cathedral
	}
	
	possible = {
		error_check = {
			severity = invalid
			this = {
				state_region = s:STATE_MOSCOW
			}
		}
	}
	
	required_construction = construction_cost_monument
}

building_statue_of_liberty = {
	building_group = bg_monuments
	texture = "gfx/interface/icons/building_icons/statue_of_liberty.dds"
	expandable = no
	downsizeable = no
	unique = yes
	locator = "statue_of_liberty_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { "building_construction_3x3_entity" }
	entity_constructed = { "wonder_statue_of_liberty_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 2
	}
	
	production_method_groups = {
		pmg_base_building_statue_of_liberty
	}

	unlocking_technologies = { 
		steel_frame_buildings 
	}
	
	required_construction = construction_cost_monument
	
	possible = {
		error_check = {
			severity = invalid
			this = {
				state_region = s:STATE_NEW_YORK
			}
		}
	}
	
	ai_value = 10000

	downsizeable = no
}

building_taj_mahal = {
	building_group = bg_monuments
	texture = "gfx/interface/icons/building_icons/taj_mahal.dds"
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "taj_mahal_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { "building_construction_3x3_entity" }
	entity_constructed = { "wonder_taj_mahal_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 3
	}
	
	production_method_groups = {
		pmg_base_building_taj_mahal
	}

	possible = {
		error_check = {
			severity = invalid
			this = {
				state_region = s:STATE_JAIPUR
			}
		}
	}
	
	required_construction = construction_cost_monument
}

building_vatican_city = {
	building_group = bg_monuments
	texture = "gfx/interface/icons/building_icons/vatican_city.dds"
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "vatican_city_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { "building_construction_3x3_entity" }
	entity_constructed = { "wonder_vatican_city_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 6
	
	}
	
	production_method_groups = {
		pmg_base_building_vatican_city
	}
	
	possible = {
		error_check = {
			severity = invalid
			this = {
				state_region = s:STATE_LAZIO
			}
		}
	}
	
	required_construction = construction_cost_monument
}

building_white_house = {
	building_group = bg_monuments
	texture = "gfx/interface/icons/building_icons/white_house.dds"
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "white_house_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { "building_construction_3x3_entity" }
	entity_constructed = { "wonder_white_house_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 4
	}
	
	production_method_groups = {
		pmg_base_building_white_house
	}
	
	possible = {
		error_check = {
			severity = invalid
			this = {
				state_region = s:STATE_DISTRICT_OF_COLUMBIA
			}
		}
	}
	
	required_construction = construction_cost_monument
}

# Decorative only

building_machu_picchu = {
	building_group = bg_monuments_hidden
	texture = "gfx/interface/icons/building_icons/machu_picchu.dds"
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "machu_picchu_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { }
	entity_constructed = { "wonder_machu_picchu_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 6
	}
	
	production_method_groups = {
		pmg_dummy
	}
}

building_argebam = {
	building_group = bg_monuments_hidden
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "argebam_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { }
	entity_constructed = { "wonder_argebam_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 8
	}
	
	production_method_groups = {
		pmg_dummy
	}
}

building_chichen_itza = {
	building_group = bg_monuments_hidden
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "chichen_itza_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { }
	entity_constructed = { "wonder_chichen_itza_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 4
	}
	
	production_method_groups = {
		pmg_dummy
	}
}

building_easter_island_heads = {
	building_group = bg_monuments_hidden
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "easter_island_heads_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { }
	entity_constructed = { "wonder_easter_island_heads_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 6
	}
	
	production_method_groups = {
		pmg_dummy
	}
}

building_eye_of_sahara = {
	building_group = bg_monuments_hidden
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "eye_of_sahara_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { }
	entity_constructed = { "wonder_eye_of_sahara_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 3
	}
	
	production_method_groups = {
		pmg_dummy
	}
}

building_giza_necropolis = {
	building_group = bg_monuments_hidden
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "giza_necropolis_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { }
	entity_constructed = { "wonder_giza_necropolis_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 10
	}
	
	production_method_groups = {
		pmg_dummy
	}
}

building_khaju_bridge = {
	building_group = bg_monuments_hidden
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "khaju_bridge_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { }
	entity_constructed = { "wonder_khaju_bridge_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 4
	}
	
	production_method_groups = {
		pmg_dummy
	}
}

building_petra = {
	building_group = bg_monuments_hidden
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "petra_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { }
	entity_constructed = { "wonder_petra_01_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 3
	}
	
	production_method_groups = {
		pmg_dummy
	}
}

### American Buildings DLC - models not present in basegame ###

building_capitol_hill = {
	building_group = bg_monuments_hidden
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "capitol_hill_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { }
	entity_constructed = { "wonder_capitol_building_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 5
	}
	
	production_method_groups = {
		pmg_dummy
	}
}

building_central_park = {
	building_group = bg_monuments_hidden
	expandable = no
	buildable = no
	downsizeable = no
	unique = yes
	locator = "central_park_locator"
	
	entity_not_constructed = { }
	entity_under_construction = { }
	entity_constructed = { "wonders_central_park_entity"}
	
	city_gfx_interactions = {
		clear_size_area = yes
		size = 4
	}
	
	production_method_groups = {
		pmg_dummy
	}
}
