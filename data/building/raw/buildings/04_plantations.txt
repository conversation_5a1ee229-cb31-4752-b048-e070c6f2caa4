﻿############# PLANTATIONS

building_coffee_plantation = {
	building_group = bg_coffee_plantations
	texture = "gfx/interface/icons/building_icons/coffee_plantation.dds"
	required_construction = construction_cost_low

	unlocking_technologies = {
		enclosure
	}

	city_type = farm
	levels_per_mesh = 5
	
	production_method_groups = {
		pmg_base_building_coffee_plantation
		pmg_train_automation_building_coffee_plantation
		pmg_ownership_land_building_coffee_plantation
	}
}

building_cotton_plantation = {
	building_group = bg_cotton_plantations
	texture = "gfx/interface/icons/building_icons/cotton_plantation.dds"
	required_construction = construction_cost_low

	unlocking_technologies = {
		enclosure
	}

	city_type = farm
	levels_per_mesh = 5

	production_method_groups = {
		pmg_base_building_cotton_plantation
		pmg_train_automation_building_cotton_plantation
		pmg_ownership_land_building_cotton_plantation
	}
}

building_dye_plantation = {
	building_group = bg_dye_plantations
	texture = "gfx/interface/icons/building_icons/dye_plantation.dds"
	required_construction = construction_cost_low

	unlocking_technologies = {
		enclosure
	}

	city_type = farm
	levels_per_mesh = 5
	
	production_method_groups = {
		pmg_base_building_dye_plantation
		pmg_train_automation_building_dye_plantation
		pmg_ownership_land_building_dye_plantation
	}
}

building_opium_plantation = {
	building_group = bg_opium_plantations
	texture = "gfx/interface/icons/building_icons/opium_plantation.dds"
	required_construction = construction_cost_low

	unlocking_technologies = {
		enclosure
	}

	city_type = farm
	levels_per_mesh = 5
	
	production_method_groups = {
		pmg_base_building_opium_plantation
		pmg_train_automation_building_opium_plantation
		pmg_ownership_land_building_opium_plantation
	}
	
	can_build_government = {
		error_check = {
			severity = fail
			owner = { NOT = { is_banning_goods = g:opium } }
		}
	}
	can_build_private = {
		error_check = {
			severity = fail
			owner = { NOT = { is_banning_goods = g:opium } }
		}
	}	
}

building_tea_plantation = {
	building_group = bg_tea_plantations
	texture = "gfx/interface/icons/building_icons/tea_plantation.dds"
	required_construction = construction_cost_low

	unlocking_technologies = {
		enclosure
	}

	city_type = farm
	levels_per_mesh = 5
	
	production_method_groups = {
		pmg_base_building_tea_plantation
		pmg_train_automation_building_tea_plantation
		pmg_ownership_land_building_tea_plantation
	}
}

building_tobacco_plantation = {
	building_group = bg_tobacco_plantations
	texture = "gfx/interface/icons/building_icons/tobacco_plantation.dds"
	required_construction = construction_cost_low

	unlocking_technologies = {
		enclosure
	}

	city_type = farm
	levels_per_mesh = 5
	
	production_method_groups = {
		pmg_base_building_tobacco_plantation
		pmg_train_automation_building_tobacco_plantation
		pmg_ownership_land_building_tobacco_plantation
	}
}

building_sugar_plantation = {
	building_group = bg_sugar_plantations
	texture = "gfx/interface/icons/building_icons/sugar_plantation.dds"
	required_construction = construction_cost_low

	unlocking_technologies = {
		enclosure
	}
	
	city_type = farm
	levels_per_mesh = 5

	production_method_groups = {
		pmg_base_building_sugar_plantation
		pmg_train_automation_building_sugar_plantation
		pmg_ownership_land_building_sugar_plantation
	}
}

building_banana_plantation = {
	building_group = bg_banana_plantations
	texture = "gfx/interface/icons/building_icons/banana_plantation.dds"
	required_construction = construction_cost_low

	unlocking_technologies = {
		enclosure
	}

	city_type = farm
	levels_per_mesh = 5
	
	production_method_groups = {
		pmg_base_building_banana_plantation
		pmg_train_automation_building_banana_plantation
		pmg_ownership_land_building_banana_plantation
	}
}

building_silk_plantation = {
	building_group = bg_silk_plantations
	texture = "gfx/interface/icons/building_icons/silk_plantation.dds"
	required_construction = construction_cost_low

	unlocking_technologies = {
		enclosure
	}

	city_type = farm
	levels_per_mesh = 5

	production_method_groups = {
		pmg_base_building_silk_plantation
		pmg_train_automation_building_silk_plantation
		pmg_ownership_land_building_silk_plantation
	}
}