﻿pm_simple_forestry = {
	texture = "gfx/interface/icons/production_method_icons/simple_forestry.dds"

	building_modifiers = {
		workforce_scaled = {
			building_output_wood_add = 30
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 4500
		}
	}
}

pm_saw_mills = {
	texture = "gfx/interface/icons/production_method_icons/saw_mills.dds"
	unlocking_technologies = {
		steelworking
	}				

	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			
			# output goods
			building_output_wood_add = 60
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 4000
			building_employment_machinists_add = 500
		}
	}
}	

pm_electric_saw_mills = {
	texture = "gfx/interface/icons/production_method_icons/electric_saw_mills.dds"
	unlocking_technologies = {
		electrical_generation
	}

	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 10
			building_input_electricity_add = 5
			
			# output goods
			building_output_wood_add = 100 #+800
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_no_hardwood = {
	texture = "gfx/interface/icons/production_method_icons/no_hardwood_selection.dds"
}

pm_hardwood = {
	texture = "gfx/interface/icons/production_method_icons/hardwood_selection.dds"
	building_modifiers = {
		workforce_scaled = {
			# output goods										
			building_output_wood_add = -20			
			building_output_hardwood_add = 10	
		}
	}
}

pm_no_equipment = {
	texture = "gfx/interface/icons/production_method_icons/no_automation.dds"
}

pm_steam_donkey_building_logging_camp = {
	texture = "gfx/interface/icons/production_method_icons/steam_donkey.dds"
	
	unlocking_technologies = {
		steam_donkey
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_engines_add = 1
			building_input_coal_add = 4
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = -1000
		}
	}
}

pm_chainsaws = {
	texture = "gfx/interface/icons/production_method_icons/chainsaws.dds"
	unlocking_technologies = {
		combustion_engine
	}
	disallowing_laws = {
		law_industry_banned
	}
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 10
			building_input_oil_add = 5
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = -1500
			building_employment_machinists_add = 200
			building_employment_engineers_add = 200
		}
	}
}

pm_rail_transport_building_logging_camp = {
	texture = "gfx/interface/icons/production_method_icons/rail_transport.dds"

	pollution_generation = 15

	unlocking_technologies = {
		railways
	}
	building_modifiers = {
		workforce_scaled = {
			building_input_transportation_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -1000
		}
	}
}

pm_log_carts = {
	texture = "gfx/interface/icons/production_method_icons/log_carts.dds"

	pollution_generation = 15

	unlocking_technologies = {
		electric_railway
	}
	building_modifiers = {
		workforce_scaled = {
			building_input_transportation_add = 8
		}

		level_scaled = {
			building_employment_laborers_add = -1500
		}
	}
}

pm_merchant_guilds_building_logging_camp = {
	texture = "gfx/interface/icons/production_method_icons/merchant_guilds.dds"

	unlocking_production_methods = {
		pm_simple_forestry
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}
		unscaled = {
			building_shopkeepers_shares_add = 10
		}
	}
}

pm_privately_owned_building_logging_camp = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	unlocking_production_methods = {
		pm_saw_mills
		pm_electric_saw_mills
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_logging_camp = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_saw_mills
		pm_electric_saw_mills
	}
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_government_run_building_logging_camp = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"

	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_building_logging_camp = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_production_methods = {
		pm_saw_mills
		pm_electric_saw_mills
	}

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

default_building_rubber_plantation = {
	texture = "gfx/interface/icons/production_method_icons/plantation_production.dds"

	building_modifiers = {
		workforce_scaled = {
			building_output_rubber_add = 30
		}

		level_scaled = {
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 100
		}
	}
}

automatic_irrigation_building_rubber_plantation = {
	texture = "gfx/interface/icons/production_method_icons/automatic_irrigation.dds"

	pollution_generation = 5
		
	unlocking_technologies = {
		pumpjacks
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_engines_add = 5
			building_output_rubber_add = 60
		}

		level_scaled = {
			building_employment_laborers_add = 3000
			building_employment_farmers_add = 1200
			building_employment_machinists_add = 500
			building_employment_clergymen_add = 100
		}
	}
}

pm_simple_fishing = {
	texture = "gfx/interface/icons/production_method_icons/simple_fishing.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# output
			building_output_fish_add = 25
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 4500
		}
	}
}

pm_fishing_trawlers = {
	texture = "gfx/interface/icons/production_method_icons/trawlers.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input
			building_input_clippers_add = 5
			
			# output
			building_output_fish_add = 50
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 4000
			building_employment_machinists_add = 500
		}
	}
}

pm_steam_trawlers = {
	texture = "gfx/interface/icons/production_method_icons/steam_trawlers.dds"

	pollution_generation = 10
	
	unlocking_technologies = {
		ironclad_tech
	}
	building_modifiers = {
		workforce_scaled = {
			# input
			building_input_steamers_add = 5
			building_input_coal_add = 15
			
			# output
			building_output_fish_add = 100
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_refrigerated_storage_building_fishing_wharf = {
	texture = "gfx/interface/icons/production_method_icons/refrigerated_storage.dds"
	
	unlocking_technologies = {
		pasteurization
	}
	building_modifiers = {
		workforce_scaled = {
			# input
			building_input_electricity_add = 5
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = -1000
		}
	}
}

pm_refrigerated_rail_cars_building_fishing_wharf = {
	texture = "gfx/interface/icons/production_method_icons/refrigerated_rail_cars.dds"

	pollution_generation = 15
	
	unlocking_technologies = {
		electric_railway
	}
	building_modifiers = {
		workforce_scaled = {
			# input
			building_input_electricity_add = 5
			building_input_transportation_add = 5
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = -2000
		}
	}
}

pm_flash_freezing_building_fishing_wharf = {
	texture = "gfx/interface/icons/production_method_icons/flash_refrigerated.dds"
	
	unlocking_technologies = {
		flash_freezing
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input
			building_input_electricity_add = 7
			building_input_transportation_add = 7
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = -3000
		}
	}
}

pm_merchant_guilds_building_fishing_wharf = {
	texture = "gfx/interface/icons/production_method_icons/merchant_guilds.dds"

	unlocking_production_methods = {
		pm_simple_fishing
		pm_fishing_trawlers
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}
		unscaled = {
			building_shopkeepers_shares_add = 10
		}
	}
}

pm_privately_owned_building_fishing_wharf = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	unlocking_production_methods = {				
		pm_fishing_trawlers
		pm_steam_trawlers
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_fishing_wharf = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {				
		pm_fishing_trawlers
		pm_steam_trawlers
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_government_run_building_fishing_wharf = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"

	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_building_fishing_wharf = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_production_methods = {
		pm_steam_trawlers
	}

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_simple_whaling = {
	texture = "gfx/interface/icons/production_method_icons/simple_whaling.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input 
			
			# output
			building_output_meat_add = 5
			building_output_oil_add = 10
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 4500
		}
	}
}

pm_wooden_whaling_ships = {
	texture = "gfx/interface/icons/production_method_icons/wooden_whaling_ships.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input
			building_input_clippers_add = 5 
			
			# output
			building_output_meat_add = 10
			building_output_oil_add = 20
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 4000
			building_employment_machinists_add = 500
		}
	}
}

pm_steam_whaling_ships = {
	texture = "gfx/interface/icons/production_method_icons/steam_whaling_ships.dds"

	pollution_generation = 10
	
	unlocking_technologies = {
		ironclad_tech
	}
	building_modifiers = {
		workforce_scaled = {
			# input
			building_input_steamers_add = 5
			building_input_coal_add = 20
			
			building_output_meat_add = 20
			building_output_oil_add = 40
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_refrigerated_storage_building_whaling_station = {
	texture = "gfx/interface/icons/production_method_icons/refrigerated_storage.dds"
	
	unlocking_technologies = {
		pasteurization
	}
	building_modifiers = {
		workforce_scaled = {
			# input
			building_input_electricity_add = 5
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = -1000
		}
	}
}

pm_refrigerated_rail_cars_building_whaling_station = {
	texture = "gfx/interface/icons/production_method_icons/refrigerated_rail_cars.dds"

	pollution_generation = 15
	
	unlocking_technologies = {
		electric_railway
	}
	building_modifiers = {
		workforce_scaled = {
			# input
			building_input_electricity_add = 5
			building_input_transportation_add = 5
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = -2000
		}
	}
}

pm_flash_freezing_building_whaling_station = {
	texture = "gfx/interface/icons/production_method_icons/flash_refrigerated.dds"
	
	unlocking_technologies = {
		flash_freezing
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input
			building_input_electricity_add = 7
			building_input_transportation_add = 7
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = -3000
		}
	}
}

pm_merchant_guilds_building_whaling_station = {
	texture = "gfx/interface/icons/production_method_icons/merchant_guilds.dds"

	unlocking_production_methods = {
		pm_simple_whaling
		pm_wooden_whaling_ships
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}
		unscaled = {
			building_shopkeepers_shares_add = 10
		}
	}
}		

pm_privately_owned_building_whaling_station = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"
	
	unlocking_production_methods = {
		pm_wooden_whaling_ships
		pm_steam_whaling_ships
	}				
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_whaling_station = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_wooden_whaling_ships
		pm_steam_whaling_ships
	}			
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_government_run_building_whaling_station = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"

	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_building_whaling_station = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_production_methods = {
		pm_steam_whaling_ships
	}

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_steam_derricks = {
	texture = "gfx/interface/icons/production_method_icons/simple_oil_extraction.dds"
	
	pollution_generation = 10
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_engines_add = 5
			building_input_coal_add = 10
			
			# output goods
			building_output_oil_add = 60
		}

		level_scaled = {
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_combustion_derricks = {
	texture = "gfx/interface/icons/production_method_icons/combustion_derricks.dds"
	
	pollution_generation = 15
	
	unlocking_technologies = {
		combustion_engine
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_engines_add = 10
			
			# output goods
			building_output_oil_add = 100
		}

		level_scaled = {
			building_employment_laborers_add = 2000
			building_employment_machinists_add = 2000
			building_employment_engineers_add = 900
		}
	}
}

pm_rail_transport_building_oil_rig = {
	texture = "gfx/interface/icons/production_method_icons/rail_transport.dds"

	pollution_generation = 5

	unlocking_technologies = {
		railways
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_transportation_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -1000
		}
	}
}

pm_tanker_cars = {
	texture = "gfx/interface/icons/production_method_icons/tanker_cars.dds"

	pollution_generation = 10

	unlocking_technologies = {
		steel_railway_cars
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_transportation_add = 10
		}

		level_scaled = {
			building_employment_laborers_add = -2000
		}
	}
}

pm_publicly_traded_building_oil_rig = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

