﻿pm_bakery = {
	texture = "gfx/interface/icons/production_method_icons/bakeries.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_grain_add = 40		# x20 =  800

			# output goods
			building_output_groceries_add = 45	# x30 = 1350
		}

		level_scaled = {
			# profit = 550
			building_employment_laborers_add = 4500
		}
	}
}		

pm_sweeteners = {
	texture = "gfx/interface/icons/production_method_icons/sweeteners.dds"
	
	unlocking_technologies = {
		distillation
	}				
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_grain_add = 40		# x20 = 800
			building_input_sugar_add = 15		# x30 = 450
 
			# output goods
			building_output_groceries_add = 65	# x30 = 1950
		}

		level_scaled = {
			# profit = 700
			building_employment_laborers_add = 4000
			building_employment_machinists_add = 500
		}
	}
}

pm_baking_powder = {
	texture = "gfx/interface/icons/production_method_icons/baking_powder.dds"
	
	unlocking_technologies = {
		baking_powder
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_grain_add = 80		# x20 = 1600
			building_input_sugar_add = 30		# x30 =  900

			# output goods
			building_output_groceries_add = 120	# x30 = 3600
		}

		level_scaled = {
			# profit = 1100
			building_employment_laborers_add = 3500
			building_employment_machinists_add = 750
			building_employment_engineers_add = 250
		}
	}
}

pm_disabled_canning = {
	texture = "gfx/interface/icons/production_method_icons/no_canning.dds"
}

pm_cannery = {
	texture = "gfx/interface/icons/production_method_icons/canneries.dds"
	
	unlocking_technologies = {
		canneries
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_grain_add = -20		# x20 = -400
			building_input_fish_add = 30		# x20 =  600
			building_input_iron_add = 10		# x40 =  400
			
			# output goods
			building_output_groceries_add = 30	# x30 =  900
		}

		level_scaled = {
			# profit = 300 (20.8)
			building_employment_machinists_add = 500	# x1.5 = 750 laborer wages
		}
	}
}

pm_vacuum_canning = {
	texture = "gfx/interface/icons/production_method_icons/vaccum_canning.dds"
	
	unlocking_technologies = {
		vacuum_canning
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_grain_add = -30		# x20 = -600
			building_input_fish_add = 60		# x20 = 1200
			building_input_iron_add = 10		# x40 =  400
			building_input_oil_add = 5 		# x40 =  200
			
			# output goods
			building_output_groceries_add = 60	# x30 = 1800
		}

		level_scaled = {
			# profit = 600 (23.1)
			building_employment_machinists_add = 500	# x1.5 = 750 laborer wages
			building_employment_engineers_add = 200		# x3.0 = 600 laborer wages
		}
	}
}

pm_disabled_distillery = {
	texture = "gfx/interface/icons/production_method_icons/no_distillery.dds"
}

pm_pot_stills = {
	texture = "gfx/interface/icons/production_method_icons/pot_stills.dds"

	unlocking_technologies = {
		distillation				
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_sugar_add = 25				# x30 =  750
			
			# output goods
			building_output_liquor_add = 60				# x30 = 1800
			building_output_groceries_add = -30 		# x30 = -900
		}

		level_scaled = {
			# profit = 150 (13.6)
			building_employment_shopkeepers_add = 100	# x2.0 = 200 laborer wages
			building_employment_machinists_add = 250	# x1.5 = 375 laborer wages
		}
	}
}

pm_patent_stills = {
	texture = "gfx/interface/icons/production_method_icons/patent_stills.dds"

	unlocking_technologies = {
		fractional_distillation
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_sugar_add = 25				# x30 =   750
			building_input_glass_add = 10				# x40 =   400
			
			# output goods
			building_output_liquor_add = 100			# x30 =  3000
			building_output_groceries_add = -40			# x30 = -1200
		}

		level_scaled = {
			# profit = 650 (19.3)
			building_employment_shopkeepers_add = 200	# x2.0 = 400 laborer wages
			building_employment_machinists_add = 500	# x1.5 = 750 laborer wages
			building_employment_engineers_add = 200		# x3.0 = 600 laborer wages
		}
	}
}

pm_manual_dough_processing = {
	texture = "gfx/interface/icons/production_method_icons/no_automation.dds"
}

pm_automated_bakery = {
	texture = "gfx/interface/icons/production_method_icons/automated_bakery.dds"
	
	disallowing_laws = {
		law_industry_banned
	}

	unlocking_technologies = {
		dough_rollers
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 10		# x40 = 400
		}

		level_scaled = {
			# profit = -400 (break-even when annual laborer wage > 8.32)
			building_employment_laborers_add = -2500
		}
	}
}

pm_merchant_guilds_building_food_industry = {
	texture = "gfx/interface/icons/production_method_icons/merchant_guilds.dds"

	unlocking_production_methods = {
		pm_bakery
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}
		unscaled = {
			building_shopkeepers_shares_add = 10
		}
	}
}

pm_privately_owned_building_food_industry = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	unlocking_production_methods = {
		pm_sweeteners
		pm_baking_powder
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_food_industry = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_sweeteners
		pm_baking_powder
	}

	unlocking_technologies = {
		mutual_funds
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_government_run_building_food_industry = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"
	
	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_building_food_industry = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_production_methods = {
		pm_baking_powder
	}

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_handsewn_clothes = {
	texture = "gfx/interface/icons/production_method_icons/handsewn_clothes.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_fabric_add = 40		# x20 =  800
			
			# output goods
			building_output_clothes_add = 45	# x30 = 1350
		}

		level_scaled = {
			# profit = 550 (6.3)
			building_employment_laborers_add = 4500
		}
	}
}

pm_dye_workshops = {
	texture = "gfx/interface/icons/production_method_icons/dye_workshops.dds"
	
	unlocking_technologies = { lathe }
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_fabric_add = 40		# x20 =  800
			building_input_dye_add = 5			# x40 =  200
			
			# output goods
			building_output_clothes_add = 60	# x30 = 1800
		}

		level_scaled = {
			# profit = 800 (8.8)
			building_employment_laborers_add = 4000
			building_employment_machinists_add = 500
		}
	}
}			

pm_sewing_machines = {
	texture = "gfx/interface/icons/production_method_icons/sewing_machines.dds"
	
	unlocking_technologies = {
		mechanized_workshops
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_fabric_add = 60		# x20 = 1200
			building_input_dye_add = 10			# x40 =  400
			building_input_tools_add = 5		# x40 =  200
			
			# output goods
			building_output_clothes_add = 100	# x30 = 3000
		}

		level_scaled = {
			# profit = 1000
			building_employment_laborers_add = 3500
			building_employment_machinists_add = 750
			building_employment_engineers_add = 250
		}
	}
}

pm_electric_sewing_machines = {
	texture = "gfx/interface/icons/production_method_icons/electric_sewing_machines.dds"
	
	unlocking_technologies = {
		electrical_capacitors
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_fabric_add = 60
			building_input_dye_add = 20
			building_input_tools_add = 10
			building_input_electricity_add = 10
			
			# output goods
			building_output_clothes_add = 140
		}

		level_scaled = {
			# profit = 1300
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_no_luxury_clothes = {
	texture = "gfx/interface/icons/production_method_icons/no_luxury_clothes.dds"
}

pm_craftsman_sewing = {
	texture = "gfx/interface/icons/production_method_icons/craftsman_sewing.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_silk_add = 10				# x40 =  400
			building_input_fabric_add = -10				# x20 =  200
			
			# output goods
			building_output_clothes_add = -20			# x30 = -600
			building_output_luxury_clothes_add = 20		# x60 = 1200
		}
		
		level_scaled = {
			# profit = 400 (20.8)
			building_employment_shopkeepers_add = 500
		}					
	}
}

pm_elastics = {
	texture = "gfx/interface/icons/production_method_icons/elastics.dds"

	unlocking_technologies = {
		vulcanization
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_rubber_add = 10
			building_input_silk_add = 10
			building_input_fabric_add = -10
			
			# output goods
			building_output_clothes_add = -20
			building_output_luxury_clothes_add = 35
		}
		
		level_scaled = {
			# profit = 900 (23.4)
			building_employment_shopkeepers_add = 1000
		}						
	}
}

pm_traditional_looms = {
	texture = "gfx/interface/icons/production_method_icons/no_automation.dds"
}

pm_mechanized_looms = {
	texture = "gfx/interface/icons/production_method_icons/mechanized_looms.dds"
	
	unlocking_technologies = {
		mechanized_workshops
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5				# x40 =  200
		}

		level_scaled = {
			# profit = -200 (break-even when annual laborer wage > 6.9)
			building_employment_laborers_add = -1500
		}
	}
}

pm_automatic_power_looms = {
	texture = "gfx/interface/icons/production_method_icons/automatic_power_looms.dds"
	
	unlocking_technologies = {
		electrical_capacitors
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5				# x40 =  200
			building_input_electricity_add = 10			# x30 =  300
		}

		level_scaled = {
			# profit = -500 (break-even when annual laborer wage > 10.4)
			building_employment_laborers_add = -2500
		}
	}
}

pm_merchant_guilds_building_textile_mills = {
	texture = "gfx/interface/icons/production_method_icons/merchant_guilds.dds"

	unlocking_production_methods = {
		pm_handsewn_clothes
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}
		unscaled = {
			building_shopkeepers_shares_add = 10
		}
	}
}

pm_privately_owned_building_textile_mills = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	unlocking_production_methods = {
		pm_dye_workshops
		pm_sewing_machines
		pm_electric_sewing_machines
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_textile_mills = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_dye_workshops
		pm_sewing_machines
		pm_electric_sewing_machines
	}
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_government_run_building_textile_mills = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"
	
	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_building_textile_mills = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_production_methods = {
		pm_dye_workshops
		pm_sewing_machines
		pm_electric_sewing_machines
	}

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_handcrafted_furniture = {
	texture = "gfx/interface/icons/production_method_icons/furniture_handicraft.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 30		# x20 =  600
			building_input_fabric_add = 10		# x20 =  200
			
			# output goods
			building_output_furniture_add = 45	# x30 = 1350
		}

		level_scaled = {
			# profit = 550 (6.4)
			building_employment_laborers_add = 4500
		}
	}
}		

pm_lathe = {
	texture = "gfx/interface/icons/production_method_icons/lathes.dds"
	
	unlocking_technologies = {
		lathe
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 30
			building_input_fabric_add = 10
			building_input_tools_add = 5
			
			# output goods
			building_output_furniture_add = 65
		}

		level_scaled = {
			# profit = 950 (8.8)
			building_employment_laborers_add = 3250
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 250
		}
	}
}

pm_mechanized_workshops = {
	texture = "gfx/interface/icons/production_method_icons/mechanized_workshops.dds"
	
	pollution_generation = 5
	
	unlocking_technologies = {
		mechanized_workshops
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 50
			building_input_fabric_add = 10
			building_input_tools_add = 10
			
			# output goods
			building_output_furniture_add = 110
		}

		level_scaled = {
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1250
			building_employment_engineers_add = 750
		}
	}
}

pm_no_luxuries = {
	texture = "gfx/interface/icons/production_method_icons/no_luxury_furniture.dds"
}

pm_luxury_furniture = {
	texture = "gfx/interface/icons/production_method_icons/luxury_furniture.dds"
		
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_hardwood_add = 10
			building_input_wood_add = -10
	
			# output goods
			building_output_furniture_add = -20
			building_output_luxury_furniture_add = 20
		}
		
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}						
	}
}

pm_precision_tools = {
	texture = "gfx/interface/icons/production_method_icons/precision_tools.dds"
		
	unlocking_technologies = {
		mechanical_tools
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_hardwood_add = 20
			building_input_tools_add = 10
			building_input_wood_add = -20
			
			# output goods
			building_output_furniture_add = -20
			building_output_luxury_furniture_add = 40
		}
		
		level_scaled = {
			building_employment_shopkeepers_add = 1000
		}						
	}
}

pm_automation_disabled = {
	texture = "gfx/interface/icons/production_method_icons/no_automation.dds"
}

pm_watertube_boiler_building_furniture_manufacturies = {
	texture = "gfx/interface/icons/production_method_icons/watertube_boiler.dds"
	
	pollution_generation = 5
	
	unlocking_technologies = {
		watertube_boiler
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			building_input_coal_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -1500
		}
	}
}

pm_rotary_valve_engine_building_furniture_manufacturies = {
	texture = "gfx/interface/icons/production_method_icons/rotary_valve_engine.dds"
	
	pollution_generation = 10
	
	unlocking_technologies = {
		rotary_valve_engine
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			building_input_coal_add = 10
		}

		level_scaled = {
			building_employment_laborers_add = -2000
		}
	}
}

pm_assembly_lines_building_furniture_manufacturies = {
	texture = "gfx/interface/icons/production_method_icons/assembly_lines.dds"
	
	pollution_generation = 10
	
	unlocking_technologies = {
		conveyors
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			building_input_oil_add = 5
			building_input_electricity_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -3000
		}
	}
}

pm_merchant_guilds_building_furniture_manufacturies = {
	texture = "gfx/interface/icons/production_method_icons/merchant_guilds.dds"

	unlocking_production_methods = {
		pm_handcrafted_furniture
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}
		unscaled = {
			building_shopkeepers_shares_add = 10
		}
	}
}

pm_privately_owned_building_furniture_manufacturies = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	unlocking_production_methods = {
		pm_lathe
		pm_mechanized_workshops
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_furniture_manufacturies = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_lathe
		pm_mechanized_workshops
	}
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_government_run_building_furniture_manufacturies = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"

	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_building_furniture_manufacturies = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_production_methods = {
		pm_lathe
		pm_mechanized_workshops
	}

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_forest_glass = {
	texture = "gfx/interface/icons/production_method_icons/forest_glass.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 30
			
			# output goods
			building_output_glass_add = 30
		}

		level_scaled = {
			building_employment_laborers_add = 4500
		}
	}
}		

pm_leaded_glass = {
	texture = "gfx/interface/icons/production_method_icons/leaded_glass.dds"
	pollution_generation = 5
	
	unlocking_technologies = { 
		lathe 
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 20
			building_input_lead_add = 10
			
			# output goods
			building_output_glass_add = 40
		}

		level_scaled = {
			building_employment_laborers_add = 4000
			building_employment_machinists_add = 500
		}
	}
}

pm_crystal_glass = {
	texture = "gfx/interface/icons/production_method_icons/crystal_glass.dds"
	pollution_generation = 10
	
	unlocking_technologies = {
		crystal_glass
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_lead_add = 35
			
			# output goods
			building_output_glass_add = 70
		}

		level_scaled = {
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_houseware_plastics = {
	texture = "gfx/interface/icons/production_method_icons/houseware_plastics.dds"
	pollution_generation = 15
	
	unlocking_technologies = {
		plastics
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_oil_add = 25
			building_input_lead_add = 35
			
			# output goods
			building_output_glass_add = 110
		}

		level_scaled = {
			building_employment_laborers_add = 2500
			building_employment_machinists_add = 1250
			building_employment_engineers_add = 750
		}
	}
}

pm_disabled_ceramics = {
	texture = "gfx/interface/icons/production_method_icons/no_ceramics.dds"
}

pm_ceramics = {
	texture = "gfx/interface/icons/production_method_icons/ceramics.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_dye_add = 15

			# output goods
			building_output_glass_add = -20
			building_output_porcelain_add = 25
		}
		
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}						
	}
}

pm_bone_china = {
	texture = "gfx/interface/icons/production_method_icons/bone_china.dds"
	
	unlocking_technologies = {
		chemical_bleaching
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_dye_add = 30
			
			# output goods
			building_output_glass_add = -20
			building_output_porcelain_add = 40
		}
		
		level_scaled = {
			building_employment_shopkeepers_add = 1000
		}					
	}														
}

pm_manual_glassblowing = {
	texture = "gfx/interface/icons/production_method_icons/glassworks_handicraft.dds"
}

pm_automatic_bottle_blowers = {
	texture = "gfx/interface/icons/production_method_icons/automated_bottle_blowers.dds"
	pollution_generation = 5
	
	unlocking_technologies = {
		automatic_bottle_blowers
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			building_input_oil_add = 10
		}

		level_scaled = {
			building_employment_laborers_add = -2500
		}
	}
}

pm_merchant_guilds_building_glassworks = {
	texture = "gfx/interface/icons/production_method_icons/merchant_guilds.dds"

	unlocking_production_methods = {
		pm_forest_glass
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}
		unscaled = {
			building_shopkeepers_shares_add = 10
		}
	}
}

pm_privately_owned_building_glassworks = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	unlocking_production_methods = {
		pm_leaded_glass
		pm_crystal_glass
		pm_houseware_plastics
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 1
		}
	}
}

pm_publicly_traded_building_glassworks = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_leaded_glass
		pm_crystal_glass
		pm_houseware_plastics
	}
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 1
		}
	}
}

pm_government_run_building_glassworks = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"
	
	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_building_glassworks = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_production_methods = {
		pm_leaded_glass
		pm_crystal_glass
		pm_houseware_plastics
	}

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_crude_tools = {
	texture = "gfx/interface/icons/production_method_icons/crude_tools.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 30
			
			# output goods
			building_output_tools_add = 30
		}

		level_scaled = {
			building_employment_laborers_add = 4500
		}
	}
}

pm_pig_iron = {
	texture = "gfx/interface/icons/production_method_icons/pig_iron_tools.dds"
	
	unlocking_technologies = {
		steelworking
	}	
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 30
			building_input_iron_add = 20
			
			# output goods
			building_output_tools_add = 60
		}

		level_scaled = {
			building_employment_laborers_add = 4000
			building_employment_machinists_add = 500
		}
	}
}

pm_steel = {
	texture = "gfx/interface/icons/production_method_icons/bessemer_process.dds"
	unlocking_technologies = {
		mechanical_tools
	}
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 30
			building_input_steel_add = 20
			
			# output goods
			building_output_tools_add = 80
		}

		level_scaled = {
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_watertube_boiler_building_tooling_workshops = {
	texture = "gfx/interface/icons/production_method_icons/watertube_boiler.dds"
	
	pollution_generation = 5
	
	unlocking_technologies = {
		watertube_boiler
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_coal_add = 10
		}

		level_scaled = {
			building_employment_laborers_add = -1500
		}
	}
}

pm_rotary_valve_engine_building_tooling_workshops = {
	texture = "gfx/interface/icons/production_method_icons/rotary_valve_engine.dds"
	
	pollution_generation = 10
	
	unlocking_technologies = {
		rotary_valve_engine
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_coal_add = 15
		}

		level_scaled = {
			building_employment_laborers_add = -2000
		}
	}
}

pm_assembly_lines_building_tooling_workshops = {
	texture = "gfx/interface/icons/production_method_icons/assembly_lines.dds"
	
	pollution_generation = 10
	
	unlocking_technologies = {
		conveyors
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_oil_add = 10
			building_input_electricity_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -3000
		}
	}
}

pm_merchant_guilds_building_tooling_workshops = {
	texture = "gfx/interface/icons/production_method_icons/merchant_guilds.dds"

	unlocking_production_methods = {
		pm_crude_tools
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}
		unscaled = {
			building_shopkeepers_shares_add = 10
		}
	}
}

pm_privately_owned_building_tooling_workshops = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	unlocking_production_methods = {
		pm_pig_iron
		pm_steel
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_tooling_workshops = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_pig_iron
		pm_steel
	}
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_government_run_building_tooling_workshops = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"

	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_building_tooling_workshops = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_production_methods = {
		pm_pig_iron
		pm_steel
	}

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_pulp_pressing = {
	texture = "gfx/interface/icons/production_method_icons/pulp_pressing.dds"			
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 30
			
			# output goods
			building_output_paper_add = 40
		}

		level_scaled = {
			building_employment_laborers_add = 4500
		}
	}
}

pm_sulfite_pulping = {
	texture = "gfx/interface/icons/production_method_icons/sulfite_pulping.dds"
	pollution_generation = 3
	
	unlocking_technologies = {
		mechanical_tools
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 30
			building_input_sulfur_add = 10
			
			# output goods
			building_output_paper_add = 70
		}

		level_scaled = {
			building_employment_laborers_add = 4000
			building_employment_machinists_add = 500				
		}
	}
}

pm_bleached_paper = {
	texture = "gfx/interface/icons/production_method_icons/bleached_paper.dds"
	pollution_generation = 5
	
	unlocking_technologies = {
		chemical_bleaching
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 30
			building_input_sulfur_add = 10
			building_input_dye_add = 10
			
			# output goods
			building_output_paper_add = 100
		}

		level_scaled = {
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_watertube_boiler_building_paper_mills = {
	texture = "gfx/interface/icons/production_method_icons/watertube_boiler.dds"
	
	pollution_generation = 5
	
	unlocking_technologies = {
		watertube_boiler
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			building_input_coal_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -1500
		}
	}
}

pm_rotary_valve_engine_building_paper_mills = {
	texture = "gfx/interface/icons/production_method_icons/rotary_valve_engine.dds"
	
	pollution_generation = 10
	
	unlocking_technologies = {
		rotary_valve_engine
	}

	disallowing_laws = {
		law_industry_banned
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			building_input_coal_add = 10
		}

		level_scaled = {
			building_employment_laborers_add = -2000
		}
	}
}

pm_merchant_guilds_building_paper_mills = {
	texture = "gfx/interface/icons/production_method_icons/merchant_guilds.dds"

	unlocking_production_methods = {
		pm_pulp_pressing
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}
		unscaled = {
			building_shopkeepers_shares_add = 10
		}
	}
}

pm_privately_owned_building_paper_mills = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	unlocking_production_methods = {				
		pm_sulfite_pulping
		pm_bleached_paper
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_paper_mills = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_sulfite_pulping
		pm_bleached_paper
	}
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_government_run_building_paper_mills = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"
	
	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_building_paper_mills = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_production_methods = {
		pm_sulfite_pulping
		pm_bleached_paper
	}

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_artificial_fertilizers = {
	texture = "gfx/interface/icons/production_method_icons/chemical_fertilizers.dds"
	pollution_generation = 5

	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_sulfur_add = 30
			building_input_iron_add = 10

			# output goods
			building_output_fertilizer_add = 90
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_improved_fertilizer = {
	texture = "gfx/interface/icons/production_method_icons/improved_fertilizer.dds"
	pollution_generation = 10

	unlocking_technologies = {
		improved_fertilizer
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_sulfur_add = 30
			building_input_iron_add = 30

			# output goods
			building_output_fertilizer_add = 140
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 2000
			building_employment_machinists_add = 1500
			building_employment_engineers_add = 1000
		}
	}
}

pm_nitrogen_fixation = {
	texture = "gfx/interface/icons/production_method_icons/nitrogen_fixation.dds"
	pollution_generation = 15

	unlocking_technologies = {
		nitrogen_fixation
	}

	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_sulfur_add = 40
			building_input_oil_add = 20
			building_input_iron_add = 30

			# output goods
			building_output_fertilizer_add = 200
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 1000
			building_employment_machinists_add = 2000
			building_employment_engineers_add = 1500
		}
	}
}

pm_no_explosives_production = {
	texture = "gfx/interface/icons/production_method_icons/no_explosives.dds"
}

pm_leblanc_process = {
	texture = "gfx/interface/icons/production_method_icons/leblanc_process.dds"
	pollution_generation = 10
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_coal_add = 15
			building_output_fertilizer_add = -15

			# output goods
			building_output_explosives_add = 20
		}
		
		level_scaled = {
			# employment
			building_employment_engineers_add = 250
		}
	}
}

pm_ammonia-soda_process = {
	texture = "gfx/interface/icons/production_method_icons/ammonia_soda_process.dds"
	pollution_generation = 15

	unlocking_technologies = {
		nitroglycerin
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_sulfur_add = 15
			building_input_coal_add = 15 
			building_output_fertilizer_add = -15 

			# output goods
			building_output_explosives_add = 40
		}

		level_scaled = {
			# employment
			building_employment_engineers_add = 500
		}
	}
}

pm_vacuum_evaporation = {
	texture = "gfx/interface/icons/production_method_icons/vaccum_evaporation.dds"
	pollution_generation = 20
	
	unlocking_technologies = {
		dynamite
	}

	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_sulfur_add = 30 
			building_input_coal_add = 15
			building_output_fertilizer_add = -30

			# output goods
			building_output_explosives_add = 70
		}

		level_scaled = {
			# employment
			building_employment_engineers_add = 750
		}
	}
}

pm_brine_electrolysis = {
	texture = "gfx/interface/icons/production_method_icons/vaccum_brine_electrolysis.dds"
	pollution_generation = 25

	unlocking_technologies = {
		electrical_capacitors
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_sulfur_add = 30
			building_input_electricity_add = 15
			building_input_coal_add = 15
			building_output_fertilizer_add = -50

			# output goods
			building_output_explosives_add = 100
		}

		level_scaled = {
			# employment
			building_employment_engineers_add = 1000
		}
	}
}

pm_privately_owned_industry = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_government_run_industry = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"

	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_industry = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_dye_production = {
	texture = "gfx/interface/icons/production_method_icons/synthetic_dyes.dds"
	
	pollution_generation = 5

	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_sulfur_add = 20
			building_input_fertilizer_add = 30

			# output goods
			building_output_dye_add = 80
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 1000
			building_employment_machinists_add = 1500
			building_employment_engineers_add = 2000
		}
	}
}

pm_no_artificial_fibers = {
	texture = "gfx/interface/icons/production_method_icons/no_artificial_silk.dds"
}

pm_rayon = {
	texture = "gfx/interface/icons/production_method_icons/rayon.dds"

	unlocking_technologies = {
		art_silk
	}

	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 40
			building_output_dye_add = -40

			# output goods
			building_output_silk_add = 40
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = -1000
			building_employment_machinists_add = 500
			building_employment_engineers_add = 500
		}
	}
}

pm_blister_steel_process = {
	texture = "gfx/interface/icons/production_method_icons/blister_steel_process.dds"
	
	pollution_generation = 10
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_iron_add = 40
			building_input_coal_add = 30
			
			# output goods
			building_output_steel_add = 65
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 3500
			building_employment_machinists_add = 750
			building_employment_engineers_add = 250
		}
	}
}

pm_bessemer_process = {
	texture = "gfx/interface/icons/production_method_icons/bessemer_process.dds"
	pollution_generation = 15
	
	unlocking_technologies = {
		bessemer_process
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_iron_add = 60
			building_input_coal_add = 30
			
			# output goods
			building_output_steel_add = 90
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_open_hearth_process = {
	texture = "gfx/interface/icons/production_method_icons/open_hearth_process.dds"
	
	pollution_generation = 20
	
	unlocking_technologies = {
		open_hearth_process
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_iron_add = 90
			building_input_coal_add = 30
			
			# output goods
			building_output_steel_add = 120
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 2500
			building_employment_machinists_add = 1250
			building_employment_engineers_add = 750
		}
	}
}

pm_electric_arc_process = {
	texture = "gfx/interface/icons/production_method_icons/electric_arc_process.dds"
	
	pollution_generation = 25
	
	unlocking_technologies = {
		electric_arc_process
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_iron_add = 100
			building_input_coal_add = 30 
			building_input_electricity_add = 30
			
			# output goods
			building_output_steel_add = 150
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 2000
			building_employment_machinists_add = 1500
			building_employment_engineers_add = 1000
		}
	}
}

pm_watertube_boiler_building_steel_mills = {
	texture = "gfx/interface/icons/production_method_icons/watertube_boiler.dds"
	
	pollution_generation = 5
	
	unlocking_technologies = {
		watertube_boiler
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			building_input_coal_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -1500
		}
	}
}

pm_rotary_valve_engine_building_steel_mills = {
	texture = "gfx/interface/icons/production_method_icons/rotary_valve_engine.dds"
	
	pollution_generation = 10
	
	unlocking_technologies = {
		rotary_valve_engine
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			building_input_coal_add = 10
		}

		level_scaled = {
			building_employment_laborers_add = -2000
		}
	}
}

pm_steam_engines = {
	texture = "gfx/interface/icons/production_method_icons/steamworks.dds"
	
	pollution_generation = 10
	
	building_modifiers = {
		workforce_scaled = {
			building_input_steel_add = 30
			
			building_output_engines_add = 40
		}

		level_scaled = {
			building_employment_laborers_add = 2500
			building_employment_machinists_add = 1500
			building_employment_engineers_add = 500
		}
	}
}

pm_electric_engines = {
	texture = "gfx/interface/icons/production_method_icons/electric_engines.dds"
	
	pollution_generation = 5
	
	unlocking_technologies = {
		electric_railway
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_steel_add = 40
			building_input_electricity_add = 30
			
			# output goods
			building_output_engines_add = 80
		}

		level_scaled = {
			building_employment_laborers_add = 2000
			building_employment_machinists_add = 1500
			building_employment_engineers_add = 1000
		}
	}
}

pm_diesel_engines = {
	texture = "gfx/interface/icons/production_method_icons/diesel_engines.dds"
	
	pollution_generation = 10
	
	unlocking_technologies = {
		compression_ignition
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_steel_add = 50
			building_input_oil_add = 50
			
			# output goods
			building_output_engines_add = 120
		}

		level_scaled = {
			building_employment_laborers_add = 1500
			building_employment_machinists_add = 1500
			building_employment_engineers_add = 1500
		}
	}
}

pm_automobiles_disabled = {
	texture = "gfx/interface/icons/production_method_icons/no_automobiles.dds"
}

pm_automobile_production = {
	texture = "gfx/interface/icons/production_method_icons/automobiles.dds"
	pollution_generation = 10
	
	unlocking_technologies = { 
		combustion_engine
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_output_engines_add = -20
			building_input_rubber_add = 10
			building_input_oil_add = 20

			# output goods
			building_output_automobiles_add = 25
		}

		level_scaled = {
			# employment
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_watertube_boiler_building_motor_industry = {
	texture = "gfx/interface/icons/production_method_icons/watertube_boiler.dds"
	
	pollution_generation = 5
	
	unlocking_technologies = {
		watertube_boiler
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			building_input_coal_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -500
			building_employment_machinists_add = -500
		}
	}
}

pm_rotary_valve_engine_building_motor_industry = {
	texture = "gfx/interface/icons/production_method_icons/rotary_valve_engine.dds"
	
	pollution_generation = 10
	
	unlocking_technologies = {
		rotary_valve_engine
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			building_input_coal_add = 10
		}

		level_scaled = {
			building_employment_laborers_add = -1000
			building_employment_machinists_add = -500
		}
	}
}

pm_assembly_lines_building_motor_industry = {
	texture = "gfx/interface/icons/production_method_icons/assembly_lines.dds"
	pollution_generation = 15
	
	unlocking_technologies = {
		conveyors
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 5
			building_input_oil_add = 5
			building_input_electricity_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -1500
			building_employment_machinists_add = -500
		}
	}
}

pm_basic_shipbuilding = {
	texture = "gfx/interface/icons/production_method_icons/wooden_ships.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 40
			building_input_fabric_add = 20
			
			# output goods
			building_output_clippers_add = 35 
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 3500
			building_employment_machinists_add = 750
			building_employment_engineers_add = 250
		}
	}
}

pm_complex_shipbuilding = {
	texture = "gfx/interface/icons/production_method_icons/reinforced_wooden_ships.dds"
	unlocking_technologies = {
		screw_frigate
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_wood_add = 40
			building_input_hardwood_add = 20
			building_input_fabric_add = 20
			building_input_engines_add = 10
			
			# output goods
			building_output_clippers_add = 70
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_metal_shipbuilding = {
	texture = "gfx/interface/icons/production_method_icons/reinforced_steam_ships.dds"
	
	pollution_generation = 10
	
	unlocking_technologies = {
		ironclad_tech
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_steel_add = 30
			building_input_coal_add = 10
			building_input_engines_add = 10
			
			# output goods
			building_output_steamers_add = 60
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 2000
			building_employment_machinists_add = 1500
			building_employment_engineers_add = 1000
		}
	}
}

pm_arc_welding_shipbuilding = {
	texture = "gfx/interface/icons/production_method_icons/arc_welded_steam_ships.dds"
	
	pollution_generation = 20
	
	unlocking_technologies = {
		arc_welding
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_steel_add = 30
			building_input_electricity_add = 30
			building_input_engines_add = 10
			
			# output goods
			building_output_steamers_add = 80
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 1000
			building_employment_machinists_add = 2000
			building_employment_engineers_add = 1500
		}
	}
}

pm_no_military_shipbuilding = {
	texture = "gfx/interface/icons/production_method_icons/no_military_shipbuilding.dds"
}

pm_military_shipbuilding_wooden = {
	texture = "gfx/interface/icons/production_method_icons/military_shipbuilding_wooden.dds"
	
	unlocking_production_methods = {
		pm_basic_shipbuilding
		pm_complex_shipbuilding
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_clippers_add = -15
			building_output_manowars_add = 15
		}
	}
}

pm_military_shipbuilding_wooden_2 = {
	texture = "gfx/interface/icons/production_method_icons/military_shipbuilding_wooden_2.dds"
	
	unlocking_production_methods = {
		pm_complex_shipbuilding
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_clippers_add = -25
			building_output_manowars_add = 25
		}
	}
}			

pm_military_shipbuilding_steam = {
	texture = "gfx/interface/icons/production_method_icons/military_shipbuilding_steam.dds"
	
	unlocking_production_methods = {
		pm_metal_shipbuilding
		pm_arc_welding_shipbuilding
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_steamers_add = -20
			building_output_ironclads_add = 20
		}
	}
}

pm_military_shipbuilding_steam_2 = {
	texture = "gfx/interface/icons/production_method_icons/military_shipbuilding_steam_2.dds"
	
	unlocking_production_methods = {
		pm_metal_shipbuilding
		pm_arc_welding_shipbuilding
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_steamers_add = -25
			building_output_ironclads_add = 25
		}
	}
}			

pm_merchant_guilds_building_shipyards = {
	texture = "gfx/interface/icons/production_method_icons/merchant_guilds.dds"

	unlocking_production_methods = {
		pm_basic_shipbuilding
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}
		unscaled = {
			building_shopkeepers_shares_add = 10
		}
	}
}		

pm_privately_owned_building_shipyards = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	unlocking_production_methods = {
		pm_complex_shipbuilding
		pm_metal_shipbuilding
		pm_arc_welding_shipbuilding
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_shipyards = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_complex_shipbuilding
		pm_metal_shipbuilding
		pm_arc_welding_shipbuilding
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_government_run_building_shipyards = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"

	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_building_shipyards = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_production_methods = {
		pm_complex_shipbuilding
		pm_metal_shipbuilding
		pm_arc_welding_shipbuilding
	}

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_aeroplane_production = {
	texture = "gfx/interface/icons/production_method_icons/aeroplanes.dds"

	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_engines_add = 10
			building_input_hardwood_add = 20
			building_input_fabric_add = 20
			building_input_oil_add = 20

			# output goods
			building_output_aeroplanes_add = 50
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 1500
			building_employment_machinists_add = 2000
			building_employment_engineers_add = 1000
		}
	}
}

pm_no_tank_production = {
	texture = "gfx/interface/icons/production_method_icons/no_tanks.dds"
}

pm_tank_production = {
	texture = "gfx/interface/icons/production_method_icons/tanks.dds"

	unlocking_technologies = {
		mobile_armor
	}

	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_engines_add = 10
			building_input_steel_add = 20
			building_input_oil_add = 10
			building_output_aeroplanes_add = -10

			# output goods
			building_output_tanks_add = 40
		}
		
		level_scaled = {
			# employment
			building_employment_engineers_add = 500
		}
	}
}

pm_telephones = {
	texture = "gfx/interface/icons/production_method_icons/telephones.dds"
	building_modifiers = {
		workforce_scaled = {
			building_input_iron_add = 20
			building_input_rubber_add = 20
			building_input_lead_add = 20
			building_input_tools_add = 10
			
			building_output_telephones_add = 60
		}

		level_scaled = {
			building_employment_laborers_add = 1500
			building_employment_machinists_add = 2000
			building_employment_engineers_add = 1000
		}
	}
}

pm_no_radios = {
	texture = "gfx/interface/icons/production_method_icons/no_radios.dds"
}

pm_radios = {
	texture = "gfx/interface/icons/production_method_icons/radios.dds"
	unlocking_technologies = {
		radio
	}	
	building_modifiers = {
		workforce_scaled = {
			building_input_electricity_add = 50
			building_output_telephones_add = -20
			building_output_radios_add = 40
		}
		
		level_scaled = {
			building_employment_engineers_add = 500
		}
	}
}

pm_muskets = {
	texture = "gfx/interface/icons/production_method_icons/muskets.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_iron_add = 10
			building_input_hardwood_add = 10
			
			# output goods
			building_output_small_arms_add = 25
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 4500
			building_employment_machinists_add = 500
		}
	}
}

pm_rifles = {
	texture = "gfx/interface/icons/production_method_icons/rifles.dds"
	
	unlocking_technologies = {
		rifling
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_steel_add = 10
			building_input_hardwood_add = 10
			building_input_tools_add = 5
			
			# output goods
			building_output_small_arms_add = 35
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 4000
			building_employment_machinists_add = 1000
		}
	}
}

pm_repeaters = {
	texture = "gfx/interface/icons/production_method_icons/repeaters.dds"
	
	unlocking_technologies = {
		repeaters
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_steel_add = 20
			building_input_hardwood_add = 10
			building_input_tools_add = 10
			
			# output goods
			building_output_small_arms_add = 50
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 3500
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_bolt_action_rifles = {
	texture = "gfx/interface/icons/production_method_icons/bolt_action_rifles.dds"
	
	unlocking_technologies = {
		bolt_action_rifles
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_steel_add = 20
			building_input_hardwood_add = 10
			building_input_tools_add = 20
			building_input_oil_add = 10
			
			# output goods
			building_output_small_arms_add = 75
		}

		level_scaled = {
			# employment
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1250
			building_employment_engineers_add = 750
		}
	}
}

pm_no_artillery_production = {
	texture = "gfx/interface/icons/production_method_icons/no_artillery_production.dds"
}

pm_cannons = {
	texture = "gfx/interface/icons/production_method_icons/artillery_production.dds"
	
	unlocking_technologies = {
		artillery
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_output_small_arms_add = -10
			building_input_iron_add = 10

			# output goods
			building_output_artillery_add = 15
		}

		level_scaled = {
			building_employment_machinists_add = 500
		}
	}
}

pm_smoothbores = {
	texture = "gfx/interface/icons/production_method_icons/smoothbores.dds"
	
	unlocking_technologies = {
		shell_gun
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_output_small_arms_add = -15
			building_input_steel_add = 15

			# output goods
			building_output_artillery_add = 25
		}

		level_scaled = {
			building_employment_machinists_add = 500
			building_employment_engineers_add = 250
		}
	}
}

pm_breech_loaders = {
	texture = "gfx/interface/icons/production_method_icons/artillery_production.dds"
	
	unlocking_technologies = {
		breech_loading_artillery
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_output_small_arms_add = -25
			building_input_steel_add = 25

			# output goods
			building_output_artillery_add = 40
		}

		level_scaled = {
			building_employment_machinists_add = 500
			building_employment_engineers_add = 500
		}
	}
}

pm_rotary_valve_engine_building_arms_industry = {
	texture = "gfx/interface/icons/production_method_icons/rotary_valve_engine.dds"
	
	pollution_generation = 10
	
	unlocking_technologies = {
		rotary_valve_engine
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_coal_add = 15
		}

		level_scaled = {
			building_employment_laborers_add = -2000
		}
	}
}

pm_assembly_lines_building_arms_industry = {
	texture = "gfx/interface/icons/production_method_icons/assembly_lines.dds"
	
	pollution_generation = 10

	unlocking_technologies = {
		conveyors
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_oil_add = 10
			building_input_electricity_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -3000
		}
	}
}

pm_merchant_guilds_building_arms_industry = {
	texture = "gfx/interface/icons/production_method_icons/merchant_guilds.dds"

	unlocking_production_methods = {
		pm_muskets
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 500
		}
		unscaled = {
			building_shopkeepers_shares_add = 10
		}
	}
}		

pm_privately_owned_building_arms_industry = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	unlocking_production_methods = {
		pm_rifles
		pm_repeaters
		pm_bolt_action_rifles
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 100
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_arms_industry = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_rifles
		pm_repeaters
		pm_bolt_action_rifles
	}

	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_government_run_building_arms_industry = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"

	unlocking_laws = {
		law_command_economy
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_building_arms_industry = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_production_methods = {
		pm_rifles
		pm_repeaters
		pm_bolt_action_rifles
	}

	unlocking_laws = {
		law_cooperative_ownership
	}

	building_modifiers = {
		level_scaled = {
			building_employment_shopkeepers_add = 250
		}
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_percussion_caps = {
	texture = "gfx/interface/icons/production_method_icons/percussion_caps.dds"
	
	pollution_generation = 10
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_explosives_add = 20
			building_input_lead_add = 20
			
			# output goods
			building_output_ammunition_add = 50
		}

		level_scaled = {
			building_employment_laborers_add = 3500
			building_employment_machinists_add = 1000
			building_employment_engineers_add = 500
		}
	}
}

pm_explosive_shells = {
	texture = "gfx/interface/icons/production_method_icons/explosive_shells.dds"
	
	unlocking_technologies = {
		dynamite
	}
	
	pollution_generation = 15
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_explosives_add = 40
			building_input_lead_add = 30
			
			# output goods
			building_output_ammunition_add = 90
		}

		level_scaled = {
			building_employment_laborers_add = 3000
			building_employment_machinists_add = 1250
			building_employment_engineers_add = 750
		}
	}
}

pm_rotary_valve_engine_building_munition_plants = {
	texture = "gfx/interface/icons/production_method_icons/rotary_valve_engine.dds"
	
	pollution_generation = 10

	unlocking_technologies = {
		rotary_valve_engine
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_coal_add = 15
		}

		level_scaled = {
			building_employment_laborers_add = -2000
		}
	}
}

pm_assembly_lines_building_munition_plants = {
	texture = "gfx/interface/icons/production_method_icons/assembly_lines.dds"
	
	pollution_generation = 10

	unlocking_technologies = {
		conveyors
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_oil_add = 10
			building_input_electricity_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -3000
		}
	}
}

pm_publicly_traded_building_chemical_plants = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_improved_fertilizer
		pm_nitrogen_fixation
	}
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_synthetic_plants = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_steel_mills = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_bessemer_process
		pm_open_hearth_process
		pm_electric_arc_process
	}
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_motor_industries = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_electric_engines
		pm_diesel_engines
	}
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_war_machines_induestries = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_electrics_industries = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}

pm_publicly_traded_building_munition_plants = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_production_methods = {
		pm_explosive_shells
	}
	
	disallowing_laws = {
		law_command_economy
		law_cooperative_ownership
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_capitalists_add = 150
		}
		unscaled = {
			building_capitalists_shares_add = 10
		}
	}
}
