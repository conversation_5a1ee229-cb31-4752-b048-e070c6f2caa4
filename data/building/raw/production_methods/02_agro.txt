﻿pm_simple_farming = {
	texture = "gfx/interface/icons/production_method_icons/simple_farming.dds"
	building_modifiers = {
		workforce_scaled = {
			# output goods													
			building_output_grain_add = 30
		}

		level_scaled = {
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 600
			building_employment_clergymen_add = 300
		}
	}
}

pm_soil_enriching_farming = {
	texture = "gfx/interface/icons/production_method_icons/soil_enriching_farming.dds"
	
	unlocking_technologies = {
		intensive_agriculture
	}

	building_modifiers = {
		workforce_scaled = {
			# input goods													
			building_input_fertilizer_add = 5				
			
			# output goods													
			building_output_grain_add = 50				
		}

		level_scaled = {
			# earnings														
			building_employment_laborers_add = 3800
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 300
		}
	}
}

pm_fertilization = {
	texture = "gfx/interface/icons/production_method_icons/fertilization.dds"
	
	unlocking_technologies = {
		improved_fertilizer
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods													
			building_input_fertilizer_add = 15				
			
			# output goods												   
			building_output_grain_add = 90				
		}

		level_scaled = {
			# earnings														
			building_employment_laborers_add = 3600
			building_employment_farmers_add = 1000
			building_employment_clergymen_add = 300
		}
	}
}

pm_chemical_fertilizer = {
	texture = "gfx/interface/icons/production_method_icons/chemical_fertilizers.dds"
	unlocking_technologies = {
		nitrogen_fixation
	}
	building_modifiers = {
		workforce_scaled = {
			# input goods												   
			building_input_fertilizer_add = 40				
			
			# output goods												   
			building_output_grain_add = 140				
		}

		level_scaled = {
			# earnings														
			building_employment_laborers_add = 3400
			building_employment_farmers_add = 1200
			building_employment_clergymen_add = 300
		}
	}
}			

pm_no_secondary = {
	texture = "gfx/interface/icons/production_method_icons/no_orchards.dds"
}

pm_potatoes = {
	texture = "gfx/interface/icons/production_method_icons/potatoes.dds"
	
	building_modifiers = {
		workforce_scaled = {
			building_output_grain_add = -20
			building_output_liquor_add = 15
		}
	}
}

pm_apple_orchards = {
	texture = "gfx/interface/icons/production_method_icons/orchards.dds"
	
	building_modifiers = {
		workforce_scaled = {
			building_output_grain_add = -20
			building_output_fruit_add = 10
			building_output_sugar_add = 5
		}
	}
}

pm_tools_disabled = {
	texture = "gfx/interface/icons/production_method_icons/no_tool_use.dds"
}

pm_tools = {
	texture = "gfx/interface/icons/production_method_icons/harvesting_tools.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_tools_add = 1
		}

		level_scaled = {
			building_employment_laborers_add = -500
		}
	}
}

pm_steam_threshers = {
	texture = "gfx/interface/icons/production_method_icons/steam_powered_threshers.dds"

	pollution_generation = 10

	unlocking_technologies = {
		threshing_machine
	}
	building_modifiers = {
		workforce_scaled = {
			# input goods													
			building_input_tools_add = 2	
			building_input_coal_add = 4
		}

		level_scaled = {
			building_employment_laborers_add = -1500
		}
	}
}

pm_tractors = {
	texture = "gfx/interface/icons/production_method_icons/tractors.dds"

	pollution_generation = 15

	unlocking_technologies = {
		mechanized_farming
	}
	building_modifiers = {
		workforce_scaled = {
			# input goods								
			building_input_coal_add = 5 
			building_input_engines_add = 2
		}

		level_scaled = {
			building_employment_laborers_add = -2500
		}
	}
}

pm_compression_ignition_tractors = {
	texture = "gfx/interface/icons/production_method_icons/tractors.dds"

	pollution_generation = 20

	unlocking_technologies = {
		compression_ignition
	}
	building_modifiers = {
		workforce_scaled = {
			# input goods								
			building_input_oil_add = 3
			building_input_engines_add = 2
		}

		level_scaled = {
			building_employment_laborers_add = -3000
		}
	}
}

pm_privately_owned = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	unlocking_laws = {
		law_serfdom
		law_tenant_farmers
	}

	building_modifiers = {
		level_scaled = {
			building_employment_aristocrats_add = 100
		}
		unscaled = {
			building_aristocrats_shares_add = 10 
			building_clergymen_shares_add = 2
			building_farmers_shares_add = 0.25
		}
	}
}

pm_publicly_traded = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	unlocking_laws = {
		law_commercialized_agriculture
	}

	building_modifiers = {
		level_scaled = {
			building_employment_aristocrats_add = 100
			building_employment_capitalists_add = 50
		}
		unscaled = {
			building_aristocrats_shares_add = 10 
			building_capitalists_shares_add = 5 
			building_clergymen_shares_add = 2
			building_farmers_shares_add = 0.25
		}
	}
}

pm_homesteading = {
	texture = "gfx/interface/icons/production_method_icons/homesteading.dds" 

	unlocking_laws = {
		law_homesteading
	}

	building_modifiers = {
		level_scaled = {
			building_employment_aristocrats_add = 50
			building_employment_farmers_add = 50
		}
		unscaled = {
			building_aristocrats_shares_add = 10
			building_clergymen_shares_add = 1
			building_farmers_shares_add = 2
		}
	}
}

pm_government_run = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"
	
	unlocking_laws = {
		law_collectivized_agriculture
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_government_shares_add = 1
		}
	}
}

pm_worker_cooperative_farm = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_laws = {
		law_collectivized_agriculture
	}

	building_modifiers = {
		level_scaled = {
			building_employment_farmers_add = 250
		}	
		unscaled = {
			building_workforce_shares_add = 1
		}
	}
}

pm_citrus_orchards = {
	texture = "gfx/interface/icons/production_method_icons/orchards.dds"
	
	building_modifiers = {
		workforce_scaled = {
			building_output_grain_add = -20
			building_output_fruit_add = 9
			building_output_sugar_add = 6
		}
	}
}

pm_vineyards = {
	texture = "gfx/interface/icons/production_method_icons/vineyards.dds"
	
	building_modifiers = {
		workforce_scaled = {
			building_output_grain_add = -15
			building_output_wine_add = 8
		}
		
		level_scaled = {
			building_employment_shopkeepers_add = 200
		}
	}
}

pm_simple_farming_building_rice_farm = {
	texture = "gfx/interface/icons/production_method_icons/simple_farming.dds"
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_grain_add = 35
		}

		level_scaled = {
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 600
			building_employment_clergymen_add = 300
		}
	}
}

pm_soil_enriching_farming_building_rice_farm = {
	texture = "gfx/interface/icons/production_method_icons/soil_enriching_farming.dds"
	
	unlocking_technologies = {
		intensive_agriculture
	}

	building_modifiers = {
		workforce_scaled = {
			# input goods
			building_input_fertilizer_add = 10		
			
			# output goods													
			building_output_grain_add = 65		
		}

		level_scaled = {
			# earnings												
			building_employment_laborers_add = 3800
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 300
		}
	}
}

pm_fertilization_building_rice_farm = {
	texture = "gfx/interface/icons/production_method_icons/fertilization.dds"
	
	unlocking_technologies = {
		improved_fertilizer
	}
	
	building_modifiers = {
		workforce_scaled = {
			# input goods													
			building_input_fertilizer_add = 20				
			
			# output goods												   
			building_output_grain_add = 100
		}

		level_scaled = {
			# earnings																									
			building_employment_laborers_add = 3700
			building_employment_farmers_add = 1000
			building_employment_clergymen_add = 300
		}
	}
}

pm_chemical_fertilizer_building_rice_farm = {
	texture = "gfx/interface/icons/production_method_icons/chemical_fertilizers.dds"
	unlocking_technologies = {
		nitrogen_fixation
	}
	building_modifiers = {
		workforce_scaled = {
			# input goods												   
			building_input_fertilizer_add = 40			
			
			# output goods												   
			building_output_grain_add = 150		
		}

		level_scaled = {
			# earnings														
			building_employment_laborers_add = 3500
			building_employment_farmers_add = 1200
			building_employment_clergymen_add = 300
		}
	}
}

pm_fig_orchards = {
	texture = "gfx/interface/icons/production_method_icons/orchards.dds"
	
	building_modifiers = {
		workforce_scaled = {
			building_output_grain_add = -15
			building_output_fruit_add = 6
			building_output_sugar_add = 9
		}
	}
}						

pm_vineyards_building_maize_farm = {
	texture = "gfx/interface/icons/production_method_icons/vineyards.dds"
	
	building_modifiers = {
		workforce_scaled = {
			building_output_grain_add = -12
			building_output_wine_add = 6
		}

		level_scaled = {
			building_employment_shopkeepers_add = 150
		}
	}
}

pm_simple_ranch = {
	texture = "gfx/interface/icons/production_method_icons/simple_ranching.dds"

	building_modifiers = {
		workforce_scaled = {
			building_output_fabric_add = 30
		}

		level_scaled = {
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 700
			building_employment_clergymen_add = 200
		}
	}
}

pm_intensive_grazing_ranch = {
	texture = "gfx/interface/icons/production_method_icons/intensive_grazing.dds"

	pollution_generation = 15
	
	unlocking_technologies = {
		intensive_agriculture
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_grain_add = 10		
			building_output_fabric_add = 40
			building_output_fertilizer_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = 3900
			building_employment_farmers_add = 900
			building_employment_clergymen_add = 200
		}
	}
}

pm_open_air_stockyards = {
	texture = "gfx/interface/icons/production_method_icons/open_air_stockyards.dds"		
	building_modifiers = {
		workforce_scaled = {
			building_output_meat_add = 5
		}
	}
}		

pm_butchering_tools = {
	texture = "gfx/interface/icons/production_method_icons/butchering_tools.dds"			
	building_modifiers = {				
		workforce_scaled = {
			building_input_tools_add = 5
			building_output_meat_add = 15
		}
	}							
}

pm_slaughterhouses = {
	texture = "gfx/interface/icons/production_method_icons/slaughterhouses.dds"	
	unlocking_technologies = {
		mechanical_tools
	}	
	building_modifiers = {
		workforce_scaled = {
			building_input_tools_add = 10
			building_output_meat_add = 25
		}
	}
}

pm_mechanized_slaughtering = {
	texture = "gfx/interface/icons/production_method_icons/mechanized_slaughtering.dds"	
	unlocking_technologies = {
		mechanized_farming
	}
	building_modifiers = {
		workforce_scaled = {
			building_input_tools_add = 10
			building_input_coal_add = 5
			building_output_meat_add = 35
		}
	}
}

pm_standard_fences = {
	texture = "gfx/interface/icons/production_method_icons/standard_fences.dds"
}

pm_barbed_wire_fences = {
	texture = "gfx/interface/icons/production_method_icons/barbed_wire_fencing.dds"
	unlocking_technologies = {
		trench_works	
	}	
	building_modifiers = {
		workforce_scaled = {
			building_input_iron_add = 3
		}

		level_scaled = {
			building_employment_laborers_add = -1000
		}
	}
}		

pm_electric_fencing = {
	texture = "gfx/interface/icons/production_method_icons/electric_fencing.dds"	
	unlocking_technologies = {
		electrical_generation	
	}
	building_modifiers = {
		workforce_scaled = {
			building_input_iron_add = 3
			building_input_electricity_add = 3
		}

		level_scaled = {
			building_employment_laborers_add = -1800
		}
	}
}

pm_unrefrigerated = {
	texture = "gfx/interface/icons/production_method_icons/no_refrigeration.dds"

}

pm_refrigerated_storage_building_livestock_ranch = {
	texture = "gfx/interface/icons/production_method_icons/refrigerated_storage.dds"
	unlocking_technologies = {
		pasteurization
	}
	building_modifiers = {
		workforce_scaled = {
			building_input_electricity_add = 4
		}

		level_scaled = {
			building_employment_laborers_add = -1000
		}
	}
}

pm_refrigerated_rail_cars_building_livestock_ranch = {
	texture = "gfx/interface/icons/production_method_icons/refrigerated_rail_cars.dds"
	
	unlocking_technologies = {
		flash_freezing
	}
	
	building_modifiers = {
		workforce_scaled = {
			building_input_electricity_add = 4
			building_input_transportation_add = 3
		}

		level_scaled = {
			building_employment_laborers_add = -1800
		}
	}
}
