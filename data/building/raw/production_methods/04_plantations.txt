﻿default_building_coffee_plantation = {
	texture = "gfx/interface/icons/production_method_icons/plantation_production.dds"

	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_coffee_add = 20
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 100
		}
	}
}

automatic_irrigation_building_coffee_plantation = {
	texture = "gfx/interface/icons/production_method_icons/automatic_irrigation.dds"

	pollution_generation = 5
		
	unlocking_technologies = {
		pumpjacks
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_input_engines_add = 5
			building_output_coffee_add = 40
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 3000
			building_employment_farmers_add = 1200
			building_employment_machinists_add = 500
			building_employment_clergymen_add = 100
		}
	}
}

pm_steam_rail_transport = {
	texture = "gfx/interface/icons/production_method_icons/rail_transport.dds"

	pollution_generation = 10
		
	unlocking_technologies = {
		railways
	}
		
	building_modifiers = {
		workforce_scaled = {
			# input goods					
			building_input_transportation_add = 5
		}

		level_scaled = {
			building_employment_laborers_add = -1000	
		}
	}
}

pm_privately_owned_plantation = {
	texture = "gfx/interface/icons/production_method_icons/privately_owned.dds"

	disallowing_laws = {
		law_collectivized_agriculture
	}

	building_modifiers = {
		level_scaled = {
			building_employment_aristocrats_add = 100
		}
		unscaled = {
			building_aristocrats_shares_add = 5 
			building_clergymen_shares_add = 2
			building_farmers_shares_add = 1
		}
	}
}

pm_publicly_traded_plantation = {
	texture = "gfx/interface/icons/production_method_icons/publicly_traded.dds"

	disallowing_laws = {
		law_collectivized_agriculture
	}

	unlocking_technologies = {
		mutual_funds
	}

	building_modifiers = {
		level_scaled = {
			building_employment_aristocrats_add = 100
			building_employment_capitalists_add = 50
		}
		unscaled = {
			building_aristocrats_shares_add = 5 
			building_capitalists_shares_add = 5 
			building_clergymen_shares_add = 2
			building_farmers_shares_add = 1
		}
	}
}

pm_government_run_plantation = {
	texture = "gfx/interface/icons/production_method_icons/government_run.dds"
	
	unlocking_laws = {
		law_collectivized_agriculture
	}
	
	building_modifiers = {
		level_scaled = {
			building_employment_bureaucrats_add = 150
		}
		unscaled = {
			building_bureaucrats_shares_add = 5
		}
	}
}

pm_worker_cooperative_plantation = {
	texture = "gfx/interface/icons/production_method_icons/worker_cooperative.dds"

	unlocking_laws = {
		law_collectivized_agriculture
	}

	building_modifiers = {
		level_scaled = {
			building_employment_farmers_add = 250
		}		
		unscaled = {
			building_workforce_shares_add = 1			
		}
	}
}

default_building_cotton_plantation = {
	texture = "gfx/interface/icons/production_method_icons/plantation_production.dds"

	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_fabric_add = 40
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 100
		}
	}
}

automatic_irrigation_building_cotton_plantation = {
	texture = "gfx/interface/icons/production_method_icons/automatic_irrigation.dds"

	pollution_generation = 5
		
	unlocking_technologies = {
		pumpjacks
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_input_engines_add = 5
			building_output_fabric_add = 100
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 3000
			building_employment_farmers_add = 1200
			building_employment_machinists_add = 500
			building_employment_clergymen_add = 100
		}
	}
}

default_building_dye_plantation = {
	texture = "gfx/interface/icons/production_method_icons/plantation_production.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_dye_add = 25
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 100
		}
	}
}

automatic_irrigation_building_dye_plantation = {
	texture = "gfx/interface/icons/production_method_icons/automatic_irrigation.dds"

	pollution_generation = 5
		
	unlocking_technologies = {
		pumpjacks
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_input_engines_add = 5
			building_output_dye_add = 50
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 3000
			building_employment_farmers_add = 1200
			building_employment_machinists_add = 500
			building_employment_clergymen_add = 100
		}
	}
}

default_building_opium_plantation = {
	texture = "gfx/interface/icons/production_method_icons/plantation_production.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# output goods	
			building_output_opium_add = 20
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 100
		}
	}
}

automatic_irrigation_building_opium_plantation = {
	texture = "gfx/interface/icons/production_method_icons/automatic_irrigation.dds"

	pollution_generation = 5
		
	unlocking_technologies = {
		pumpjacks
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_input_engines_add = 5
			building_output_opium_add = 50
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 3000
			building_employment_farmers_add = 1200
			building_employment_machinists_add = 500
			building_employment_clergymen_add = 100
		}
	}
}

default_building_tea_plantation = {
	texture = "gfx/interface/icons/production_method_icons/plantation_production.dds"

	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_tea_add = 20
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 100
		}
	}
}

automatic_irrigation_building_tea_plantation = {
	texture = "gfx/interface/icons/production_method_icons/automatic_irrigation.dds"

	pollution_generation = 5
		
	unlocking_technologies = {
		pumpjacks
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_input_engines_add = 5
			building_output_tea_add = 40
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 3000
			building_employment_farmers_add = 1200
			building_employment_machinists_add = 500
			building_employment_clergymen_add = 100
		}
	}
}

default_building_tobacco_plantation = {
	texture = "gfx/interface/icons/production_method_icons/plantation_production.dds"
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_tobacco_add = 25
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 100
		}
	}
}

automatic_irrigation_building_tobacco_plantation = {
	texture = "gfx/interface/icons/production_method_icons/automatic_irrigation.dds"

	pollution_generation = 5
		
	unlocking_technologies = {
		pumpjacks
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_input_engines_add = 5
			building_output_tobacco_add = 50
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 3000
			building_employment_farmers_add = 1200
			building_employment_machinists_add = 500
			building_employment_clergymen_add = 100
		}
	}
}

default_building_sugar_plantation = {
	texture = "gfx/interface/icons/production_method_icons/plantation_production.dds"

	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_sugar_add = 30
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 100
		}
	}
}

automatic_irrigation_building_sugar_plantation = {
	texture = "gfx/interface/icons/production_method_icons/automatic_irrigation.dds"

	pollution_generation = 5
		
	unlocking_technologies = {
		pumpjacks
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_input_engines_add = 5
			building_output_sugar_add = 60
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 3000
			building_employment_farmers_add = 1200
			building_employment_machinists_add = 500
			building_employment_clergymen_add = 100
		}
	}
}

default_building_banana_plantation = {
	texture = "gfx/interface/icons/production_method_icons/plantation_production.dds"

	building_modifiers = {
		workforce_scaled = {
			building_output_fruit_add = 30
		}

		level_scaled = {
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 100
		}
	}
}

automatic_irrigation_building_banana_plantation = {
	texture = "gfx/interface/icons/production_method_icons/automatic_irrigation.dds"

	pollution_generation = 5
		
	unlocking_technologies = {
		pumpjacks
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_input_engines_add = 5
			building_output_fruit_add = 60
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 3000
			building_employment_farmers_add = 1200
			building_employment_machinists_add = 500
			building_employment_clergymen_add = 100
		}
	}
}

default_building_silk_plantation = {
	texture = "gfx/interface/icons/production_method_icons/plantation_production.dds"

	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_output_silk_add = 20
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 4000
			building_employment_farmers_add = 800
			building_employment_clergymen_add = 100
		}
	}
}

automatic_irrigation_building_silk_plantation = {
	texture = "gfx/interface/icons/production_method_icons/automatic_irrigation.dds"

	pollution_generation = 5
		
	unlocking_technologies = {
		pumpjacks
	}
	
	building_modifiers = {
		workforce_scaled = {
			# output goods
			building_input_engines_add = 5
			building_output_silk_add = 40
		}

		level_scaled = {
			# profit
			building_employment_laborers_add = 3000
			building_employment_farmers_add = 1200
			building_employment_machinists_add = 500
			building_employment_clergymen_add = 100
		}
	}
}
