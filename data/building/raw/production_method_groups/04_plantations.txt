﻿pmg_base_building_coffee_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_coffee_plantation
		automatic_irrigation_building_coffee_plantation
	}
}

pmg_train_automation_building_coffee_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_steam_rail_transport
	}
}

pmg_ownership_land_building_coffee_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned_plantation
		pm_publicly_traded_plantation
		pm_government_run_plantation
		pm_worker_cooperative_plantation
	}
}

pmg_base_building_cotton_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_cotton_plantation
		automatic_irrigation_building_cotton_plantation
	}
}

pmg_train_automation_building_cotton_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_steam_rail_transport
	}
}

pmg_ownership_land_building_cotton_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned_plantation
		pm_publicly_traded_plantation
		pm_government_run_plantation
		pm_worker_cooperative_plantation
	}
}

pmg_base_building_dye_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_dye_plantation
		automatic_irrigation_building_dye_plantation
	}
}

pmg_train_automation_building_dye_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_steam_rail_transport
	}
}

pmg_ownership_land_building_dye_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned_plantation
		pm_publicly_traded_plantation
		pm_government_run_plantation
		pm_worker_cooperative_plantation
	}
}

pmg_base_building_opium_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_opium_plantation
		automatic_irrigation_building_opium_plantation
	}
}

pmg_train_automation_building_opium_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_steam_rail_transport
	}
}

pmg_ownership_land_building_opium_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned_plantation
		pm_publicly_traded_plantation
		pm_government_run_plantation
		pm_worker_cooperative_plantation
	}
}

pmg_base_building_tea_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_tea_plantation
		automatic_irrigation_building_tea_plantation
	}
}

pmg_train_automation_building_tea_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_steam_rail_transport
	}
}

pmg_ownership_land_building_tea_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned_plantation
		pm_publicly_traded_plantation
		pm_government_run_plantation
		pm_worker_cooperative_plantation
	}
}

pmg_base_building_tobacco_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_tobacco_plantation
		automatic_irrigation_building_tobacco_plantation
	}
}

pmg_train_automation_building_tobacco_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_steam_rail_transport
	}
}

pmg_ownership_land_building_tobacco_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned_plantation
		pm_publicly_traded_plantation
		pm_government_run_plantation
		pm_worker_cooperative_plantation
	}
}

pmg_base_building_sugar_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_sugar_plantation
		automatic_irrigation_building_sugar_plantation
	}
}

pmg_train_automation_building_sugar_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_steam_rail_transport
	}
}

pmg_ownership_land_building_sugar_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned_plantation
		pm_publicly_traded_plantation
		pm_government_run_plantation
		pm_worker_cooperative_plantation
	}
}

pmg_base_building_banana_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_banana_plantation
		automatic_irrigation_building_banana_plantation
	}
}

pmg_train_automation_building_banana_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_steam_rail_transport
	}
}

pmg_ownership_land_building_banana_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned_plantation
		pm_publicly_traded_plantation
		pm_government_run_plantation
		pm_worker_cooperative_plantation
	}
}

pmg_base_building_silk_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_silk_plantation
		automatic_irrigation_building_silk_plantation
	}
}

pmg_train_automation_building_silk_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_steam_rail_transport
	}
}

pmg_ownership_land_building_silk_plantation = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned_plantation
		pm_publicly_traded_plantation
		pm_government_run_plantation
		pm_worker_cooperative_plantation
	}
}
