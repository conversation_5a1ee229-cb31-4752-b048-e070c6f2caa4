﻿# To ensure the game rule 'monument_effects' works properly, new monument buildings added should be given the optional base methods
# 	pm_monument_prestige_only
#	pm_monument_no_effects
# 
# 'monument_effects' additionally needs to be amended with flags disabling the new monument's Base method
# PM Groups for Monuments should have the 'is_hidden_when_unavailable' flag to ensure only one alternative is visible at any time (unless the player actually has an option they can switch to)

pmg_base_building_eiffel_tower = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	ai_selection = most_productive
	is_hidden_when_unavailable = yes

	production_methods = {
		pm_default_building_eiffel_tower
		pm_monument_prestige_only
		pm_monument_no_effects
	}
}

pmg_base_building_angkor_wat = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	ai_selection = most_productive
	is_hidden_when_unavailable = yes

	production_methods = {
		pm_default_building_angkor_wat
		pm_monument_prestige_only
		pm_monument_no_effects
	}
}

pmg_base_building_big_ben = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	ai_selection = most_productive
	is_hidden_when_unavailable = yes

	production_methods = {
		pm_default_building_big_ben
		pm_monument_prestige_only
		pm_monument_no_effects
	}
}

pmg_base_building_forbidden_city = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	ai_selection = most_productive
	is_hidden_when_unavailable = yes

	production_methods = {
		pm_default_building_forbidden_city
		pm_monument_prestige_only
		pm_monument_no_effects
	}
}

pmg_base_building_hagia_sophia = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	ai_selection = most_productive
	is_hidden_when_unavailable = yes

	production_methods = {
		pm_default_building_hagia_sophia
		pm_monument_prestige_only
		pm_monument_no_effects
	}
}

pmg_base_building_mosque_of_djenne = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	ai_selection = most_productive
	is_hidden_when_unavailable = yes

	production_methods = {
		pm_default_building_mosque_of_djenne
		pm_monument_prestige_only
		pm_monument_no_effects
	}
}

pmg_base_building_saint_basils_cathedral = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	ai_selection = most_productive
	is_hidden_when_unavailable = yes

	production_methods = {
		pm_default_building_saint_basils_cathedral
		pm_monument_prestige_only
		pm_monument_no_effects
	}
}

pmg_base_building_statue_of_liberty = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	ai_selection = most_productive
	is_hidden_when_unavailable = yes

	production_methods = {
		pm_default_building_statue_of_liberty
		pm_monument_prestige_only
		pm_monument_no_effects
	}
}

pmg_base_building_taj_mahal = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	ai_selection = most_productive
	is_hidden_when_unavailable = yes

	production_methods = {
		pm_default_building_taj_mahal
		pm_monument_prestige_only
		pm_monument_no_effects
	}
}

pmg_base_building_vatican_city = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	ai_selection = most_productive
	is_hidden_when_unavailable = yes

	production_methods = {
		pm_default_building_vatican_city
		pm_monument_prestige_only_vatican_city
		pm_monument_prestige_only
		pm_monument_no_effects
	}
}

pmg_base_building_white_house = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	ai_selection = most_productive
	is_hidden_when_unavailable = yes

	production_methods = {
		pm_default_building_white_house
		pm_monument_prestige_only
		pm_monument_no_effects
	}
}