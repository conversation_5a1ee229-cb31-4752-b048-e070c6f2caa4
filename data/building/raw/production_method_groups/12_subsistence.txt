﻿pmg_base_building_subsistence_farms = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_subsistence_farms
	}
}

pmg_home_workshops_building_subsistence_farms = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_refining.dds"
	production_methods = {
		pm_home_workshops_no_building_subsistence_farms
		pm_home_workshops_building_subsistence_farms
	}
}

pmg_serfdom_building_subsistence_farms = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_serfdom_no
		pm_serfdom
	}
}

pmg_base_building_subsistence_orchards = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_subsistence_orchards
	}
}

pmg_home_workshops_building_subsistence_orchards = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_refining.dds"
	production_methods = {
		pm_home_workshops_no_building_subsistence_orchards
		pm_home_workshops_building_subsistence_orchards
	}
}

pmg_serfdom_building_subsistence_orchards = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_serfdom_no
		pm_serfdom_building_subsistence_orchards
	}
}

pmg_base_building_subsistence_pastures = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_subsistence_pastures
	}
}

pmg_home_workshops_building_subsistence_pastures = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_refining.dds"
	production_methods = {
		pm_home_workshops_no_building_subsistence_pastures
		pm_home_workshops_building_subsistence_pastures
	}
}

pmg_serfdom_building_subsistence_pastures = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_serfdom_no
		pm_serfdom_building_subsistence_pastures
	}
}

pmg_base_building_subsistence_fishing_villages = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_subsistence_fishing_villages
	}
}

pmg_home_workshops_building_subsistence_fishing_villages = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_refining.dds"
	production_methods = {
		pm_home_workshops_no_building_subsistence_fishing_villages
		pm_home_workshops_building_subsistence_fishing_villages
	}
}

pmg_serfdom_building_subsistence_fishing_villages = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_serfdom_no
		pm_serfdom_building_subsistence_fishing_villages
	}
}


pmg_base_building_subsistence_rice_paddies = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_subsistence_rice_paddies
	}
}

pmg_home_workshops_building_subsistence_rice_paddies = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_refining.dds"
	production_methods = {
		pm_home_workshops_no_building_subsistence_rice_paddies
		pm_home_workshops_building_subsistence_rice_paddies
	}
}

pmg_serfdom_building_subsistence_rice_paddies = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_serfdom_no
		pm_serfdom_building_subsistence_rice_paddies
	}
}

pmg_ownership_building_subsistence = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned_building_subsistence
		pm_homesteading_building_subsistence
		pm_worker_cooperative_building_subsistence
		pm_government_run_building_subsistence
	}
}
