﻿pmg_mining_equipment_building_coal_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_picks_and_shovels_building_coal_mine
		pm_atmospheric_engine_pump_building_coal_mine
		pm_condensing_engine_pump_building_coal_mine
		pm_diesel_pump_building_coal_mine
	}
}

pmg_explosives_building_coal_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_no_explosives
		pm_nitroglycerin_building_coal_mine
		pm_dynamite_building_coal_mine
	}
}

pmg_steam_automation_building_coal_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_no_steam_automation
		pm_steam_donkey_building_coal_mine
	}
}

pmg_train_automation_building_coal_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_rail_transport_mine
	}
}

pmg_ownership_capital_building_coal_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_merchant_guilds_building_coal_mine
		pm_privately_owned_building_coal_mine
		pm_publicly_traded_building_coal_mine
		pm_government_run_building_coal_mine
		pm_worker_cooperative_building_coal_mine
	}
}

pmg_mining_equipment_building_iron_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_picks_and_shovels_building_iron_mine
		pm_atmospheric_engine_pump_building_iron_mine
		pm_condensing_engine_pump_building_iron_mine
		pm_diesel_pump_building_iron_mine
	}
}

pmg_explosives_building_iron_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_no_explosives
		pm_nitroglycerin_building_iron_mine
		pm_dynamite_building_iron_mine
	}
}

pmg_steam_automation_building_iron_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_no_steam_automation
		pm_steam_donkey_mine
	}
}

pmg_train_automation_building_iron_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_rail_transport_mine
	}
}

pmg_ownership_capital_building_iron_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_merchant_guilds_building_iron_mine
		pm_privately_owned_building_iron_mine
		pm_publicly_traded_building_iron_mine
		pm_government_run_building_iron_mine
		pm_worker_cooperative_building_iron_mine
	}
}

pmg_mining_equipment_building_lead_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_picks_and_shovels_building_lead_mine
		pm_atmospheric_engine_pump_building_lead_mine
		pm_condensing_engine_pump_building_lead_mine
		pm_diesel_pump_building_lead_mine
	}
}

pmg_explosives_building_lead_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_no_explosives
		pm_nitroglycerin_building_lead_mine
		pm_dynamite_building_lead_mine
	}
}

pmg_steam_automation_building_lead_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_no_steam_automation
		pm_steam_donkey_mine
	}
}

pmg_train_automation_building_lead_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_rail_transport_mine
	}
}

pmg_ownership_capital_building_lead_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_merchant_guilds_building_lead_mine
		pm_privately_owned_building_lead_mine
		pm_publicly_traded_building_lead_mine
		pm_government_run_building_lead_mine
		pm_worker_cooperative_building_lead_mine
	}
}

pmg_mining_equipment_building_sulfur_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_picks_and_shovels_building_sulfur_mine
		pm_atmospheric_engine_pump_building_sulfur_mine
		pm_condensing_engine_pump_building_sulfur_mine
		pm_diesel_pump_building_sulfur_mine
	}
}

pmg_explosives_building_sulfur_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_no_explosives
		pm_nitroglycerin_building_sulfur_mine
		pm_dynamite_building_sulfur_mine
	}
}

pmg_steam_automation_building_sulfur_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_no_steam_automation
		pm_steam_donkey_mine
	}
}

pmg_train_automation_building_sulfur_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_rail_transport_mine
	}
}

pmg_ownership_capital_building_sulfur_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_merchant_guilds_building_sulfur_mine
		pm_privately_owned_building_sulfur_mine
		pm_publicly_traded_building_sulfur_mine
		pm_government_run_building_sulfur_mine
		pm_worker_cooperative_building_sulfur_mine
	}
}

pmg_mining_equipment_building_gold_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_picks_and_shovels_building_gold_mine
		pm_atmospheric_engine_pump_building_gold_mine
		pm_condensing_engine_pump_building_gold_mine
		pm_diesel_pump_building_gold_mine
	}
}

pmg_explosives_building_gold_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_no_explosives
		pm_nitroglycerin_building_gold_mine
		pm_dynamite_building_gold_mine
	}
}

pmg_steam_automation_building_gold_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_no_steam_automation
		pm_steam_donkey_mine
	}
}

pmg_train_automation_building_gold_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_road_carts
		pm_rail_transport_mine
	}
}

pmg_ownership_capital_building_gold_mine = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_merchant_guilds_building_gold_mine
		pm_privately_owned_building_gold_mine
		pm_publicly_traded_building_gold_mine
		pm_government_run_building_gold_mine
		pm_worker_cooperative_building_gold_mine
	}
}

pmg_base_building_gold_fields = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		default_building_gold_fields
	}
}
