﻿pmg_base_building_rye_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_simple_farming
		pm_soil_enriching_farming
		pm_fertilization
		pm_chemical_fertilizer
	}
}

pmg_secondary_building_rye_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_refining.dds"
	production_methods = {
		pm_no_secondary
		pm_potatoes
		pm_apple_orchards
	}
}

pmg_harvesting_process_building_rye_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_tools_disabled
		pm_tools
		pm_steam_threshers
		pm_tractors
		pm_compression_ignition_tractors
	}
}

pmg_ownership_land_building_rye_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned
		pm_publicly_traded
		pm_homesteading
		pm_government_run
		pm_worker_cooperative_farm
	}
}

pmg_base_building_wheat_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_simple_farming
		pm_soil_enriching_farming
		pm_fertilization
		pm_chemical_fertilizer
	}
}

pmg_secondary_building_wheat_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_refining.dds"
	production_methods = {
		pm_no_secondary
		pm_citrus_orchards
		pm_vineyards
	}
}

pmg_harvesting_process_building_wheat_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_tools_disabled
		pm_tools
		pm_steam_threshers
		pm_tractors
		pm_compression_ignition_tractors
	}
}

pmg_ownership_land_building_wheat_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned
		pm_publicly_traded
		pm_homesteading
		pm_government_run
		pm_worker_cooperative_farm
	}
}

pmg_base_building_rice_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_simple_farming_building_rice_farm
		pm_soil_enriching_farming_building_rice_farm
		pm_fertilization_building_rice_farm
		pm_chemical_fertilizer_building_rice_farm
	}
}

pmg_secondary_building_rice_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_refining.dds"
	production_methods = {
		pm_no_secondary
		pm_fig_orchards
	}
}

pmg_harvesting_process_building_rice_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_tools_disabled
		pm_tools
		pm_steam_threshers
	}
}

pmg_ownership_land_building_rice_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned
		pm_publicly_traded
		pm_homesteading
		pm_government_run
		pm_worker_cooperative_farm
	}
}

pmg_base_building_maize_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_simple_farming
		pm_soil_enriching_farming
		pm_fertilization
		pm_chemical_fertilizer
	}
}

pmg_secondary_building_maize_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_refining.dds"
	production_methods = {
		pm_no_secondary
		pm_citrus_orchards
		pm_vineyards_building_maize_farm
	}
}

pmg_harvesting_process_building_maize_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_tools_disabled
		pm_tools
		pm_steam_threshers
		pm_tractors
		pm_compression_ignition_tractors
	}
}

pmg_ownership_land_building_maize_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned
		pm_publicly_traded
		pm_homesteading
		pm_government_run
		pm_worker_cooperative_farm
	}
}

pmg_base_building_millet_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_simple_farming
		pm_soil_enriching_farming
		pm_fertilization
		pm_chemical_fertilizer
	}
}

pmg_secondary_building_millet_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_refining.dds"
	production_methods = {
		pm_no_secondary
		pm_fig_orchards
	}
}

pmg_harvesting_process_building_millet_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_tools_disabled
		pm_tools
		pm_steam_threshers
		pm_tractors
		pm_compression_ignition_tractors
	}
}

pmg_ownership_land_building_millet_farm = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned
		pm_publicly_traded
		pm_homesteading
		pm_government_run
		pm_worker_cooperative_farm
	}
}

pmg_base_building_livestock_ranch = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_base.dds"
	production_methods = {
		pm_simple_ranch
		pm_intensive_grazing_ranch
	}
}

pmg_slaughtering = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_refining.dds"
	production_methods = {
		pm_open_air_stockyards
		pm_butchering_tools
		pm_slaughterhouses
		pm_mechanized_slaughtering
	}
}

pmg_fencing = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_standard_fences
		pm_barbed_wire_fences
		pm_electric_fencing
	}
}

pmg_refrigeration_building_livestock_ranch = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_automation.dds"
	production_methods = {
		pm_unrefrigerated
		pm_refrigerated_storage_building_livestock_ranch
		pm_refrigerated_rail_cars_building_livestock_ranch
	}
}

pmg_ownership_land_building_livestock_ranch = {
	texture = "gfx/interface/icons/generic_icons/mixed_icon_ownership.dds"
	production_methods = {
		pm_privately_owned
		pm_publicly_traded
		pm_homesteading
		pm_government_run
		pm_worker_cooperative_farm
	}
}
