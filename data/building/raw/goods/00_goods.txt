﻿# goods types, organized by category

# prestige_factor							Base prestige for occupying the rank MIN_PRESTIGE_AWARD spot on the goods production leaderboard. x2 awarded for every rank above the minimum.

############
# MILITARY #
############

ammunition = {
	texture = "gfx/interface/icons/goods_icons/ammunition.dds"
	cost = 50
	category = military
	
	prestige_factor = 5
}

small_arms = {
	texture = "gfx/interface/icons/goods_icons/small_arms.dds"
	cost = 60
	category = military
	
	prestige_factor = 5
}

artillery = {
	texture = "gfx/interface/icons/goods_icons/artillery.dds"
	cost = 70
	category = military
	
	prestige_factor = 5
	traded_quantity = 3.5
	convoy_cost_multiplier = 1.5
}

tanks = {
	texture = "gfx/interface/icons/goods_icons/tanks.dds"
	cost = 80
	category = military
	
	prestige_factor = 10
	traded_quantity = 3
	convoy_cost_multiplier = 2.0
}

aeroplanes = {
	texture = "gfx/interface/icons/goods_icons/aeroplanes.dds"
	cost = 80
	category = military
	
	prestige_factor = 10
	traded_quantity = 3
	convoy_cost_multiplier = 2.0
}

manowars = {
	texture = "gfx/interface/icons/goods_icons/man_o_wars.dds"
	cost = 70
	category = military
	
	prestige_factor = 5
	traded_quantity = 2.5
	convoy_cost_multiplier = 0.5
}

ironclads = {
	texture = "gfx/interface/icons/goods_icons/ironclads.dds"
	cost = 80
	category = military
	
	prestige_factor = 10
	traded_quantity = 2.5
	convoy_cost_multiplier = 0.5
}

##########
# STAPLE #
##########

grain = {
	texture = "gfx/interface/icons/goods_icons/grain.dds"
	cost = 20
	category = staple
	
	prestige_factor = 3
	
	traded_quantity = 10
	convoy_cost_multiplier = 0.25
	
	consumption_tax_cost = 500
}

fish = {
	texture = "gfx/interface/icons/goods_icons/fish.dds"
	cost = 20
	category = staple
	
	prestige_factor = 3
	
	traded_quantity = 10
	convoy_cost_multiplier = 0.25
	
	consumption_tax_cost = 300
}

fabric = {
	texture = "gfx/interface/icons/goods_icons/fabric.dds"
	cost = 20
	category = staple
	
	prestige_factor = 3
	
	traded_quantity = 10
	convoy_cost_multiplier = 0.25
	
	consumption_tax_cost = 300
}

wood = {
	texture = "gfx/interface/icons/goods_icons/wood.dds"
	cost = 20
	category = staple
	
	prestige_factor = 3
	
	traded_quantity = 10
	convoy_cost_multiplier = 0.25
	
	consumption_tax_cost = 300
}

groceries = {
	texture = "gfx/interface/icons/goods_icons/groceries.dds"
	cost = 30
	category = staple
	
	prestige_factor = 4
	
	traded_quantity = 7.5
	convoy_cost_multiplier = 0.5
	
	consumption_tax_cost = 300
}

clothes = {
	texture = "gfx/interface/icons/goods_icons/clothes.dds"
	cost = 30
	category = staple
	
	prestige_factor = 4
	
	traded_quantity = 7.5
	convoy_cost_multiplier = 0.5
	
	consumption_tax_cost = 300
}

furniture = {
	texture = "gfx/interface/icons/goods_icons/furniture.dds"
	cost = 30
	category = staple
	
	prestige_factor = 4
	
	traded_quantity = 7.5
	convoy_cost_multiplier = 0.5
	
	consumption_tax_cost = 300
}

paper = {
	texture = "gfx/interface/icons/goods_icons/paper.dds"
	cost = 30
	category = staple
	
	prestige_factor = 4
	
	traded_quantity = 7.5
	convoy_cost_multiplier = 0.5
	
	consumption_tax_cost = 200
}

services = {
	texture = "gfx/interface/icons/goods_icons/services.dds"
	cost = 30
	category = staple
	tradeable = no
	
	prestige_factor = 3
	
	consumption_tax_cost = 200
}

transportation = {
	texture = "gfx/interface/icons/goods_icons/transportation.dds"
	cost = 30
	category = staple
	tradeable = no

	prestige_factor = 4
	
	consumption_tax_cost = 200
}

electricity = {
	texture = "gfx/interface/icons/goods_icons/electricity.dds"
	cost = 30
	category = staple
	tradeable = no
	
	prestige_factor = 5
	
	consumption_tax_cost = 200
}

##############
# INDUSTRIAL #
##############

clippers = {
	texture = "gfx/interface/icons/goods_icons/clippers.dds"
	cost = 60
	category = industrial
	
	prestige_factor = 5
	traded_quantity = 3.5
	convoy_cost_multiplier = 0.25
}

steamers = {
	texture = "gfx/interface/icons/goods_icons/steamers.dds"
	cost = 70
	category = industrial
	
	prestige_factor = 5
	traded_quantity = 3.5
	convoy_cost_multiplier = 0.25
}

silk = {
	texture = "gfx/interface/icons/goods_icons/silk.dds"
	cost = 40
	category = industrial
	
	prestige_factor = 5
}

dye = {
	texture = "gfx/interface/icons/goods_icons/dye.dds"
	cost = 40
	category = industrial
	
	prestige_factor = 5
}

sulfur = {
	texture = "gfx/interface/icons/goods_icons/sulfur.dds"
	cost = 50
	category = industrial
	
	prestige_factor = 5
}

coal = {
	texture = "gfx/interface/icons/goods_icons/coal.dds"
	cost = 30
	category = industrial
	
	prestige_factor = 5
	
	traded_quantity = 7.5
	convoy_cost_multiplier = 0.75
	
	consumption_tax_cost = 200
}

iron = {
	texture = "gfx/interface/icons/goods_icons/iron.dds"
	cost = 40
	category = industrial
	
	prestige_factor = 5
}

lead = {
	texture = "gfx/interface/icons/goods_icons/lead.dds"
	cost = 40
	category = industrial
	
	prestige_factor = 5
}

hardwood = {
	texture = "gfx/interface/icons/goods_icons/hardwood.dds"
	cost = 40
	category = industrial
	
	prestige_factor = 5
}

rubber = {
	texture = "gfx/interface/icons/goods_icons/rubber.dds"
	cost = 40
	category = industrial
	
	prestige_factor = 5
}

oil = {
	texture = "gfx/interface/icons/goods_icons/oil.dds"
	cost = 40
	category = industrial
	
	prestige_factor = 5
	
	consumption_tax_cost = 200
}

engines = {
	texture = "gfx/interface/icons/goods_icons/locomotives.dds"
	cost = 60
	category = industrial
	
	prestige_factor = 10
	
	traded_quantity = 4
	convoy_cost_multiplier = 1.5
}

steel = {
	texture = "gfx/interface/icons/goods_icons/steel.dds"
	cost = 50
	category = industrial
	
	prestige_factor = 5
}

glass = {
	texture = "gfx/interface/icons/goods_icons/glass.dds"
	cost = 40
	category = industrial
	
	prestige_factor = 5
}

fertilizer = {
	texture = "gfx/interface/icons/goods_icons/fertilizer.dds"
	cost = 30
	category = industrial
	
	prestige_factor = 5
	
	traded_quantity = 7.5
}

tools = {
	texture = "gfx/interface/icons/goods_icons/tools.dds"
	cost = 40
	category = industrial
	
	prestige_factor = 5
}

explosives = {
	texture = "gfx/interface/icons/goods_icons/explosives.dds"
	cost = 50
	category = industrial
	
	prestige_factor = 10
	
	traded_quantity = 4
	convoy_cost_multiplier = 1.5
}

##########
# LUXURY #
##########

porcelain = {
	texture = "gfx/interface/icons/goods_icons/porcelain.dds"
	cost = 70
	category = luxury
	
	obsession_chance = 1.0
	prestige_factor = 5
	convoy_cost_multiplier = 0.75
}

meat = {
	texture = "gfx/interface/icons/goods_icons/meat.dds"
	cost = 30
	category = luxury
	
	obsession_chance = 1.0
	prestige_factor = 4
	
	traded_quantity = 7.5
	convoy_cost_multiplier = 0.75
	
	consumption_tax_cost = 200
}

fruit = {
	texture = "gfx/interface/icons/goods_icons/fruit.dds"
	cost = 30
	category = luxury
	
	obsession_chance = 1.0
	prestige_factor = 4
	
	traded_quantity = 7.5
	convoy_cost_multiplier = 0.75
	
	consumption_tax_cost = 200
}

liquor = {
	texture = "gfx/interface/icons/goods_icons/liquor.dds"
	cost = 30
	category = luxury
	
	obsession_chance = 2.0
	prestige_factor = 4
	
	traded_quantity = 7.5
	convoy_cost_multiplier = 0.75
}

wine = {
	texture = "gfx/interface/icons/goods_icons/wine.dds"
	cost = 50
	category = luxury
	
	obsession_chance = 2.0
	prestige_factor = 5
	convoy_cost_multiplier = 0.75
}

tea = {
	texture = "gfx/interface/icons/goods_icons/tea.dds"
	cost = 50
	category = luxury
	
	obsession_chance = 1.5
	prestige_factor = 4
	convoy_cost_multiplier = 0.75
}

coffee = {
	texture = "gfx/interface/icons/goods_icons/coffee.dds"
	cost = 50
	category = luxury
	
	obsession_chance = 1.5
	prestige_factor = 4
	convoy_cost_multiplier = 0.75
}

sugar = {
	texture = "gfx/interface/icons/goods_icons/sugar.dds"
	cost = 30
	category = luxury
	
	obsession_chance = 1.5
	prestige_factor = 4
	
	traded_quantity = 7.5
	convoy_cost_multiplier = 0.75
}

tobacco = {	
	texture = "gfx/interface/icons/goods_icons/tobacco.dds"
	cost = 40
	category = luxury
	
	obsession_chance = 2.0
	prestige_factor = 4
	convoy_cost_multiplier = 0.75
}

opium = {
	texture = "gfx/interface/icons/goods_icons/opium.dds"
	cost = 50
	category = luxury
	
	obsession_chance = 2.0
	prestige_factor = 4
	convoy_cost_multiplier = 0.75
}

automobiles = {
	texture = "gfx/interface/icons/goods_icons/automobiles.dds"
	cost = 100
	category = luxury
	
	obsession_chance = 1.0
	prestige_factor = 10
	traded_quantity = 4
}

telephones = {
	texture = "gfx/interface/icons/goods_icons/telephones.dds"
	cost = 70
	category = luxury
	
	obsession_chance = 1.0
	prestige_factor = 10
}

radios = {
	texture = "gfx/interface/icons/goods_icons/radios.dds"
	cost = 80 
	category = luxury
	
	obsession_chance = 1.0
	prestige_factor = 10
}

luxury_clothes = {
	texture = "gfx/interface/icons/goods_icons/luxury_clothes.dds"
	cost = 60
	category = luxury
	
	obsession_chance = 1.0
	prestige_factor = 10
	convoy_cost_multiplier = 0.75
}

luxury_furniture = {
	texture = "gfx/interface/icons/goods_icons/luxury_furniture.dds"
	cost = 60
	category = luxury
	
	obsession_chance = 1.0
	prestige_factor = 10
	convoy_cost_multiplier = 0.75
}

gold = {
	texture = "gfx/interface/icons/goods_icons/gold.dds"
	cost = 100
	category = luxury
	tradeable = no
	fixed_price = yes
	prestige_factor = 5
}

fine_art = {
	texture = "gfx/interface/icons/goods_icons/fine_art.dds"
	cost = 200
	category = luxury
	
	obsession_chance = 1.0
	prestige_factor = 15
	
	traded_quantity = 2
}